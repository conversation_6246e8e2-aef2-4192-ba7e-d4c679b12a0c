import asyncio

from deepeval import evaluate
from deepeval.metrics import AnswerRelevancyMetric, GEval
from deepeval.test_case import LLMTestCase, LLMTestCaseParams
from langfuse import Lang<PERSON>

from salestech_be.core.ai.tasks.activities import generate_intel_tasks
from salestech_be.settings import settings

langfuse = Langfuse(
    public_key=settings.langfuse_public_key.get_secret_value(),
    secret_key=settings.langfuse_secret_key.get_secret_value(),
    host=settings.langfuse_host,
)


# There's prebuilt ones like Summarization, Prompt Alignment, etc
# What we really want for tomorrow is probably to have a good set of custom GEval metrics
# TODO <PERSON>: Talk to <PERSON> about his list of eval criteria but probably this format is superior
correctness_metric = GEval(
    name="Correctness",
    # criteria="Determine whether the actual output is factually correct based on the expected output.",
    # NOTE: you can only provide either criteria or evaluation_steps, and not both
    evaluation_steps=[
        "Check whether the facts in 'actual output' contradicts any facts in 'expected output'",
        "You should also heavily penalize omission of detail",
        "Vague language, or contradicting OPINIONS, are OK",
    ],
    evaluation_params=[
        LLMTestCaseParams.INPUT,
        LLMTestCaseParams.ACTUAL_OUTPUT,
        LLMTestCaseParams.EXPECTED_OUTPUT,
    ],
)
metrics = [correctness_metric, AnswerRelevancyMetric()]


async def run_evals() -> None:
    eval_metrics = metrics

    dataset = langfuse.get_dataset("tasks_raw")

    test_cases = []

    # Process each golden in your dataset
    for item in dataset.items:
        actual_output = await generate_intel_tasks.generate_intel_tasks(item.input)
        test_case = LLMTestCase(
            input=str(item.input),
            actual_output=str(actual_output),
            expected_output=str(item.expected_output),
        )
        test_cases.append(test_case)

    # Run an evaluation
    evaluate(
        test_cases=test_cases,
        metrics=eval_metrics,
        hyperparameters={
            # model and prompt template are required for logging for deepeval
            "feature": "task_tasks",
            "model": "claude-3.7",
            "agent_type": "zeroshot",
            "version": 1,
            "prompt template": "",
        },
    )


if __name__ == "__main__":
    asyncio.run(run_evals())
