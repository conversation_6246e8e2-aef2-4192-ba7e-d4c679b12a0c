# Task Ownership Autoeval Tests

This directory contains automated evaluation tests for the task ownership functionality in the SalesTech backend.

## Overview

The tests in `task_eval.py` use the `autoevals` library to measure how well the task ownership determination logic works across different scenarios. It tests the system's ability to correctly identify which user should be assigned ownership of a task based on the task content, context, and available users.

## Running the Tests

1. Make sure you have the `autoevals` package installed:

```bash
uv sync
```

2. Run the test script:

```bash
cd salestech-be
uv run python -m evals.task_eval
```

## Test Cases

The tests cover various scenarios:

1. **Simple single user assignment**: When only one user is available, they should be assigned the task.
2. **Multiple users - sales rep assignment**: When multiple users are available, the primary sales rep should be assigned.
3. **No users available**: Edge case where no users are available to assign the task to.
4. **Content specifically mentions user**: When the task content mentions a specific user by name, they should be assigned.
5. **Email follow-up task**: When the task is related to an email, the recipient should be assigned.

## Evaluation

Each test outputs:
- ✅ PASS or ❌ FAIL status
- The expected owner ID
- The actual owner ID returned by the system
- An explanation of the match/mismatch

An overall score from 0.0 to 1.0 is provided at the end, representing the accuracy of the task ownership functionality.

## Notes

- The tests use a mock implementation of `generate_task_ownership` to simulate the actual system logic
- To test against the real implementation, you need to have the main project installed and set up correctly
