# mypy: ignore-errors
import argparse
import asyncio
from collections.abc import Awaitable, Callable
from enum import Str<PERSON><PERSON>

from deepeval import evaluate
from deepeval.metrics import AnswerRelevancyMetric, GEval
from deepeval.test_case import LLMTestCase, LLMTestCaseParams
from langfuse import <PERSON><PERSON>

from salestech_be.core.ai.opportuntity_stages.llm_calls.criteria_update_with_llm import (
    make_criteria_context_update_llm_call,
    make_criteria_items_update_llm_call,
)
from salestech_be.settings import settings

langfuse = Langfuse(
    public_key=settings.langfuse_public_key.get_secret_value(),
    secret_key=settings.langfuse_secret_key.get_secret_value(),
    host=settings.langfuse_host,
)


class CriteriaEnum(StrEnum):
    PaperProcessItems = "stage_criteria_items_paper_process_raw"
    MetricItems = "stage_criteria_items_metric_raw"
    MetricContext = "stage_criteria_context_metric_raw"
    IdentifiedPainItems = "stage_criteria_items_identified_pain_raw"
    CompetitorItems = "stage_criteria_items_competitor_raw"
    CompetitionContext = "stage_criteria_context_competition_raw"
    DecisionProcessItems = "stage_criteria_items_decision_process_raw"
    DecisionProcessContext = "stage_criteria_context_decision_process_raw"
    DecisionCriteriaItems = "stage_criteria_items_decision_criteria_raw"
    DecisionCriteriaContext = "stage_criteria_context_decision_criteria_raw"


def create_context_update_closure(
    context_name: str,
) -> Callable[[dict], Awaitable[None]]:  # type: ignore[no-untyped-def]
    async def context_update_wrapper(input_data: dict) -> None:
        return await make_criteria_context_update_llm_call(
            messages=input_data["messages"],
            previous_context=input_data["previous_context"],
            seller_contacts=input_data["seller_contacts"],
            langfuse_session_id=input_data["langfuse_session_id"],
            organization_id=input_data["organization_id"],
            context_name=context_name,
        )

    return context_update_wrapper


def create_items_update_closure(items_name: str) -> Callable[[dict], Awaitable[None]]:  # type: ignore[no-untyped-def]
    async def items_update_wrapper(input_data: dict) -> None:
        return await make_criteria_items_update_llm_call(
            messages=input_data["messages"],
            previous_context=input_data["previous_context"],
            seller_contacts=input_data["seller_contacts"],
            langfuse_session_id=input_data["langfuse_session_id"],
            organization_id=input_data["organization_id"],
            items_name=items_name,
        )

    return items_update_wrapper


# Then you can map the criteria types like this:
CRITERIA_TYPE_FUNCTION_MAP = {
    CriteriaEnum.PaperProcessItems: create_items_update_closure("paper_process"),
    CriteriaEnum.MetricItems: create_items_update_closure("metric"),
    CriteriaEnum.MetricContext: create_context_update_closure("metric"),
    CriteriaEnum.IdentifiedPainItems: create_items_update_closure("identified_pain"),
    CriteriaEnum.CompetitorItems: create_items_update_closure("competitor"),
    CriteriaEnum.CompetitionContext: create_context_update_closure("competition"),
    CriteriaEnum.DecisionProcessItems: create_items_update_closure("decision_process"),
    CriteriaEnum.DecisionProcessContext: create_context_update_closure(
        "decision_process"
    ),
    CriteriaEnum.DecisionCriteriaItems: create_items_update_closure(
        "decision_criteria"
    ),
    CriteriaEnum.DecisionCriteriaContext: create_context_update_closure(
        "decision_criteria"
    ),
}

# Use this to map the eval metrics to the criteria type
# There's prebuilt ones like Summarization, Prompt Alignment, etc
# What we really want for tomorrow is probably to have a good set of custom GEval metrics
# TODO Ahmed: Talk to Clement about his list of eval criteria but probably this format is superior
correctness_metric = GEval(
    name="Correctness",
    # criteria="Determine whether the actual output is factually correct based on the expected output.",
    # NOTE: you can only provide either criteria or evaluation_steps, and not both
    evaluation_steps=[
        "Check whether the facts in 'actual output' contradicts any facts in 'expected output'",
        "You should also heavily penalize omission of detail",
        "Vague language, or contradicting OPINIONS, are OK",
    ],
    evaluation_params=[
        LLMTestCaseParams.INPUT,
        LLMTestCaseParams.ACTUAL_OUTPUT,
        LLMTestCaseParams.EXPECTED_OUTPUT,
    ],
)
# TODO Ahmed: Now map the metrics to the criteria type
# I added a relevancy metric as well to demonstrate usage but we should use a more useful one, probably GEval objects for now.
CRITERIA_EVAL_MAP = {
    CriteriaEnum.PaperProcessItems: [correctness_metric, AnswerRelevancyMetric()],
    # add the rest of the criteria types here
}


async def run_evals(criteria_type: CriteriaEnum) -> None:
    eval_metrics = CRITERIA_EVAL_MAP[criteria_type]
    dataset = langfuse.get_dataset(criteria_type.value)
    test_cases = []

    # Get the appropriate function for this criteria type
    criteria_function = CRITERIA_TYPE_FUNCTION_MAP[criteria_type]

    # Process each golden in your dataset
    for item in dataset.items:
        # Convert the input to the expected format
        input_data = {
            "messages": item.input["messages"],
            "previous_context": item.input["previous_context"],
            "seller_contacts": item.input["seller_contacts"],
            "langfuse_session_id": item.input["langfuse_session_id"],
            "organization_id": item.input["organization_id"],
        }

        # Run the appropriate function to get the actual output
        actual_output = await criteria_function(input_data)

        test_case = LLMTestCase(
            input=str(item.input),
            actual_output=str(actual_output),  # Use the result from our function
            expected_output="hoops🏀",  # TODO: replace with varun's expected input csv/json
        )
        test_cases.append(test_case)

    # Run an evaluation
    evaluate(
        test_cases=test_cases,
        metrics=eval_metrics,
        hyperparameters={
            "feature": "stage_criteria",
            "model": "claude-3.7",
            "agent_type": "zeroshot",
            "version": 1,
            "prompt template": "",
        },
    )


if __name__ == "__main__":
    # parse a command line flag to run a specific criteria type
    parser = argparse.ArgumentParser()
    parser.add_argument("--criteria_type", type=CriteriaEnum, required=True)
    args = parser.parse_args()
    asyncio.run(run_evals(args.criteria_type))
