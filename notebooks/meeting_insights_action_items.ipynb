{"cells": [{"cell_type": "markdown", "id": "fa696c030f58e37f", "metadata": {}, "source": ["# Meeting Insights"]}, {"cell_type": "markdown", "id": "1cf0659ed679ad2d", "metadata": {}, "source": ["## Set up IDs"]}, {"cell_type": "code", "execution_count": 1, "id": "22cab8818e348cea", "metadata": {}, "outputs": [], "source": ["from uuid import UUID\n", "\n", "meeting_id = UUID(\"5ebdc711-9ae5-4994-893e-d26cef314b37\")\n", "organization_id = UUID(\"1981d325-0cde-4a7c-93de-917c41077036\")"]}, {"cell_type": "markdown", "id": "286a2f92d3b198d7", "metadata": {}, "source": ["## Set up services"]}, {"cell_type": "code", "execution_count": null, "id": "46fbdfd2b8108a02", "metadata": {}, "outputs": [], "source": ["from fastapi import BackgroundTasks\n", "from salestech_be.core.meeting.meeting_service import meeting_service_factory_general\n", "from salestech_be.temporal.database import get_or_init_db_engine\n", "\n", "engine = await get_or_init_db_engine()\n", "\n", "meeting_service = meeting_service_factory_general(\n", "    db_engine=engine, background_tasks=BackgroundTasks()\n", ")"]}, {"cell_type": "markdown", "id": "d962b6c476e1fa68", "metadata": {}, "source": ["## Get Transcript"]}, {"cell_type": "code", "execution_count": 3, "id": "e0cd42add14b2e", "metadata": {}, "outputs": [], "source": ["_, transcript_container, meeting = await meeting_service.get_transcript_by_meeting_id(\n", "    meeting_id=meeting_id, organization_id=organization_id\n", ")"]}, {"cell_type": "markdown", "id": "0b659d80-9c9d-4bb7-b0e5-8e87dc755b5e", "metadata": {}, "source": ["# GPT 4o mini\n", "`MTok $0.15 / MTok $0.60`"]}, {"cell_type": "markdown", "id": "8d0b5023-84d8-4419-b0b0-bd85a435b5e8", "metadata": {}, "source": ["## Extract Action Items"]}, {"cell_type": "code", "execution_count": 4, "id": "54d06be9-918f-489f-8054-0c22907a78f2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/github/salestech-be/.venv/lib/python3.12/site-packages/langchain_core/_api/deprecation.py:139: LangChainDeprecationWarning: The class `LLMChain` was deprecated in LangChain 0.1.17 and will be removed in 1.0. Use RunnableSequence, e.g., `prompt | llm` instead.\n", "  warn_deprecated(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["- Action Item 1:\n", "title: Provide <PERSON> Access for Evaluation\n", "insight: Provide Demo Access and Assess SKU Management\n", "action_type: action\n", "priority: HIGH\n", "due date: None\n", "contacts: []\n", "tags: ['demo access', 'SKU management', 'product assessment']\n", "lines: [398, 403, 416, 469]\n", "- Action Item 2:\n", "title: Discuss Customization and Data Import Needs\n", "insight: Follow Up with Customization Needs\n", "action_type: email\n", "priority: MEDIUM\n", "due date: None\n", "contacts: ['<PERSON><PERSON>']\n", "tags: ['customization', 'data import', 'user feedback']\n", "lines: [388, 424, 427]\n"]}], "source": ["from salestech_be.core.transcript.llm import openai_4o_mini\n", "from salestech_be.core.transcript.transcript_extraction_service_v2 import (\n", "    transcript_extraction_factory_v2,\n", ")\n", "\n", "transcript_extraction_service_v2 = transcript_extraction_factory_v2(\n", "    engine=engine, llm=openai_4o_mini()\n", ")\n", "\n", "items = await transcript_extraction_service_v2.extract_action_items(\n", "    organization_id=organization_id,\n", "    compacted_transcript=transcript_container.transcript.compact(),\n", "    meeting=meeting,\n", ")\n", "\n", "for i, item in enumerate(items):\n", "    print(f\"- Action Item {i + 1}:\")\n", "    print(f\"{item}\")"]}, {"cell_type": "markdown", "id": "d1c13e30-1e92-449f-8d25-b5a1b75cb4d7", "metadata": {}, "source": ["## Extract Summary"]}, {"cell_type": "code", "execution_count": 5, "id": "8cd4719c-3342-4a0b-9007-3c8e6fd12b0e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- Item 1:\n", "  - Insight: <PERSON> is well-connected, leveraging personal relationships in outreach.\n", "  - Line Numbers: [13, 14, 36]\n", "  - Tags: ['networking', 'relationships']\n", "- Item 2:\n", "  - Insight: The customer is interested in automation for efficiency.\n", "  - Line Numbers: [376]\n", "  - Tags: ['automation', 'efficiency']\n", "- Item 3:\n", "  - Insight: <PERSON> emphasizes the unique AI features of Revo's platform.\n", "  - Line Numbers: [148, 213]\n", "  - Tags: ['AI', 'technology']\n", "- Item 4:\n", "  - Insight: Customer is looking for a trial to assess Rev<PERSON>'s effectiveness.\n", "  - Line Numbers: [370]\n", "  - Tags: ['trial', 'evaluation']\n", "- Item 5:\n", "  - Insight: <PERSON> positions Revo as a cost-effective alternative to existing tools.\n", "  - Line Numbers: [357, 358]\n", "  - Tags: ['cost-effectiveness', 'value']\n", "- Item 6:\n", "  - Insight: The conversation indicates potential for a follow-up demo session.\n", "  - Line Numbers: [399, 470]\n", "  - Tags: ['follow-up', 'demo']\n", "- Item 7:\n", "  - Insight: <PERSON> is the decision-maker for adopting new sales tools.\n", "  - Line Numbers: [367]\n", "  - Tags: ['decision-maker', 'sales']\n", "- Item 8:\n", "  - Insight: <PERSON> is open to customizing the product based on customer needs.\n", "  - Line Numbers: [388, 427]\n", "  - Tags: ['customization', 'client-focused']\n", "- Item 9:\n", "  - Insight: The customer expresses concerns about platform stability.\n", "  - Line Numbers: [440]\n", "  - Tags: ['stability', 'concerns']\n"]}], "source": ["insights = await transcript_extraction_service_v2.extract_summary(\n", "    organization_id=organization_id,\n", "    compacted_transcript=transcript_container.transcript.compact(),\n", "    meeting=meeting,\n", ")\n", "\n", "for i, item in enumerate(insights):\n", "    print(f\"- Item {i + 1}:\")\n", "    print(f\"  - Insight: {item.insight}\")\n", "    print(f\"  - Line Numbers: {item.line_numbers}\")\n", "    print(f\"  - Tags: {item.keyword_tags}\")"]}, {"cell_type": "markdown", "id": "90971999-a46d-469d-ad50-bd9c04a52e30", "metadata": {}, "source": ["## Extract Insights"]}, {"cell_type": "code", "execution_count": 6, "id": "9474f0e0-de19-4c03-ad25-f0bdf59c8aae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- Item 1:\n", "  - Insight: Customer expresses interest in a trial of the product.\n", "  - Type: champion\n", "  - Line Numbers: [369, 370]\n", "  - Tags: ['trial', 'interest']\n", "- Item 2:\n", "  - Insight: Customer shows a preference for automation to avoid wasted movements.\n", "  - Type: identify_pain\n", "  - Line Numbers: [376, 377]\n", "  - Tags: ['automation', 'efficiency']\n", "- Item 3:\n", "  - Insight: Customer needs to understand how to integrate product SKUs into the system.\n", "  - Type: decision_criteria\n", "  - Line Numbers: [416, 417]\n", "  - Tags: ['integration', 'SKUs']\n", "- Item 4:\n", "  - Insight: Decision-maker <PERSON> is open to assessing the product's fit.\n", "  - Type: economic_buyer\n", "  - Line Numbers: [367, 396]\n", "  - Tags: ['decision-maker', 'assessment']\n", "- Item 5:\n", "  - Insight: Customer values the potential of the product to scale uniquely for their needs.\n", "  - Type: identify_pain\n", "  - Line Numbers: [437, 438]\n", "  - Tags: ['scalability', 'unique needs']\n", "- Item 6:\n", "  - Insight: Discussion about the costly nature of current systems like Pipedrive.\n", "  - Type: objection\n", "  - Line Numbers: [450, 451]\n", "  - Tags: ['cost', 'current systems']\n", "- Item 7:\n", "  - Insight: Customer is interested in reducing costs associated with multiple software tools.\n", "  - Type: metrics\n", "  - Line Numbers: [358, 359]\n", "  - Tags: ['cost reduction', 'software tools']\n", "- Item 8:\n", "  - Insight: Customer is willing to explore product capabilities further after the demo.\n", "  - Type: decision_process\n", "  - Line Numbers: [431, 432]\n", "  - Tags: ['exploration', 'product capabilities']\n"]}], "source": ["insights = await transcript_extraction_service_v2.extract_insights(\n", "    organization_id=organization_id,\n", "    compacted_transcript=transcript_container.transcript.compact(),\n", "    meeting=meeting,\n", ")\n", "\n", "for i, item in enumerate(insights):\n", "    print(f\"- Item {i + 1}:\")\n", "    print(f\"  - Insight: {item.insight}\")\n", "    print(f\"  - Type: {item.insight_type}\")\n", "    print(f\"  - Line Numbers: {item.line_numbers}\")\n", "    print(f\"  - Tags: {item.keyword_tags}\")"]}, {"cell_type": "markdown", "id": "50140c54-f3fa-495a-8ae8-169f877afbd5", "metadata": {}, "source": ["# <PERSON>\n", "`MTok $3.00 / $15.00`"]}, {"cell_type": "markdown", "id": "7d7fb114-3917-4cc1-94da-fed6e5f2c26b", "metadata": {}, "source": ["## Extract Action Items"]}, {"cell_type": "code", "execution_count": 11, "id": "c1bcb3b6-0c12-4948-822d-e15ffd7341f4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["None of PyTorch, TensorFlow >= 2.0, or Flax have been found. Models won't be available and only tokenizers, configuration and file/data utilities can be used.\n", "Token indices sequence length is longer than the specified maximum sequence length for this model (1313 > 1024). Running this sequence through the model will result in indexing errors\n"]}, {"name": "stdout", "output_type": "stream", "text": ["- Action Item 1:\n", "title: Set up demo environment with SKU/product functionality\n", "insight: <PERSON> requested to see how SKUs and products can be added to the system, as this is critical for their SaaS platform sales process. Set up a demo environment that showcases this functionality.\n", "action_type: action\n", "priority: HIGH\n", "due date: None\n", "contacts: []\n", "tags: ['demo', 'SKUs', 'products', 'pricing', 'functionality']\n", "lines: [398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417]\n", "- Action Item 2:\n", "title: Follow up with demo access and next steps\n", "insight: <PERSON> requested demo access to further evaluate the system. Send follow-up email with demo access information and outline next steps, including gathering more information about their SKUs/products and current data setup in Pipedrive.\n", "action_type: email\n", "priority: HIGH\n", "due date: None\n", "contacts: ['<PERSON><PERSON>']\n", "tags: ['demo access', 'follow-up', 'data import', 'next steps']\n", "lines: [430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474]\n"]}], "source": ["from salestech_be.core.transcript.llm import anthropic_sonnet\n", "from salestech_be.core.transcript.transcript_extraction_service_v2 import (\n", "    transcript_extraction_factory_v2,\n", ")\n", "\n", "transcript_extraction_service_v2 = transcript_extraction_factory_v2(\n", "    engine=engine, llm=anthropic_sonnet()\n", ")\n", "\n", "items = await transcript_extraction_service_v2.extract_action_items(\n", "    organization_id=organization_id,\n", "    compacted_transcript=transcript_container.transcript.compact(),\n", "    meeting=meeting,\n", ")\n", "\n", "for i, item in enumerate(items):\n", "    print(f\"- Action Item {i + 1}:\")\n", "    print(f\"{item}\")"]}, {"cell_type": "markdown", "id": "bf72ac54-a9ba-4223-9269-da4699284a24", "metadata": {}, "source": ["## Extract Summary"]}, {"cell_type": "code", "execution_count": 12, "id": "68b2ff68-ecd0-43bd-9c8c-964c48376268", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- Item 1:\n", "  - Insight: Revo is a new, AI-powered sales platform aiming to combine multiple sales tools into one\n", "  - Line Numbers: [146, 147, 148, 149, 150, 151, 152]\n", "  - Tags: ['AI', 'sales platform', 'tool integration']\n", "- Item 2:\n", "  - Insight: The product includes features like prospecting, dialing, call recording, and meeting scheduling\n", "  - Line Numbers: [224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242]\n", "  - Tags: ['prospecting', 'dialing', 'call recording', 'meeting scheduling']\n", "- Item 3:\n", "  - Insight: Revo offers AI-powered features like call summaries, sentiment analysis, and automated follow-ups\n", "  - Line Numbers: [263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275]\n", "  - Tags: ['AI', 'call summaries', 'sentiment analysis', 'automated follow-ups']\n", "- Item 4:\n", "  - Insight: The platform is currently free, with potential pricing of $100/user/month in the future\n", "  - Line Numbers: [352, 353, 354, 355, 356, 357, 358]\n", "  - Tags: ['pricing', 'free trial', 'future cost']\n", "- Item 5:\n", "  - Insight: Prospect shows interest in trying Revo, particularly for specialized B2G sales approach\n", "  - Line Numbers: [368, 369, 370, 371, 372, 373, 374, 375, 376, 377]\n", "  - Tags: ['interest', 'trial', 'B2G sales', 'specialized approach']\n", "- Item 6:\n", "  - Insight: Prospect requests demo access to further evaluate the product, particularly SKU functionality\n", "  - Line Numbers: [398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416]\n", "  - Tags: ['demo access', 'product evaluation', 'SKU functionality']\n", "- Item 7:\n", "  - Insight: <PERSON><PERSON> offers to set up a tailored environment for the prospect to try out <PERSON><PERSON>\n", "  - Line Numbers: [420, 421, 422, 423, 424, 425, 426, 427, 428]\n", "  - Tags: ['tailored setup', 'trial offer', 'customization']\n"]}], "source": ["insights = await transcript_extraction_service_v2.extract_summary(\n", "    organization_id=organization_id,\n", "    compacted_transcript=transcript_container.transcript.compact(),\n", "    meeting=meeting,\n", ")\n", "\n", "for i, item in enumerate(insights):\n", "    print(f\"- Item {i + 1}:\")\n", "    print(f\"  - Insight: {item.insight}\")\n", "    print(f\"  - Line Numbers: {item.line_numbers}\")\n", "    print(f\"  - Tags: {item.keyword_tags}\")"]}, {"cell_type": "markdown", "id": "1c480775-4d10-45aa-a9a1-93feea916453", "metadata": {}, "source": ["## Extract Insights"]}, {"cell_type": "code", "execution_count": 13, "id": "f8d43a7a-cd34-4219-8caa-6847d053c085", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- Item 1:\n", "  - Insight: Rod emphasizes need for product/SKU management in CRM to handle pricing and deal values\n", "  - Type: decision_criteria\n", "  - Line Numbers: [401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416]\n", "  - Tags: ['product management', 'SKUs', 'pricing', 'deal value']\n", "- Item 2:\n", "  - Insight: <PERSON> requests demo access to assess product capabilities before making any decisions\n", "  - Type: decision_process\n", "  - Line Numbers: [398, 399, 400, 430, 431, 432, 433, 434, 435, 436, 437, 438]\n", "  - Tags: ['demo access', 'assessment', 'decision making']\n", "- Item 3:\n", "  - Insight: <PERSON> expresses concern about <PERSON><PERSON>'s stability due to being a young company\n", "  - Type: objection\n", "  - Line Numbers: [438, 439, 440]\n", "  - Tags: ['stability', 'young company', 'concern']\n", "- Item 4:\n", "  - Insight: <PERSON> highlights frustration with Pipedrive's pricing model, calling it 'death by 1000 cuts'\n", "  - Type: identify_pain\n", "  - Line Numbers: [450, 451, 452, 453, 454, 455]\n", "  - Tags: ['pricing frustration', 'additional costs', 'scalability']\n", "- Item 5:\n", "  - Insight: <PERSON> emphasizes the importance of scalability for their business needs\n", "  - Type: decision_criteria\n", "  - Line Numbers: [436, 437, 438, 451, 452, 453, 454, 455]\n", "  - Tags: ['scalability', 'business needs', 'growth']\n", "- Item 6:\n", "  - Insight: <PERSON><PERSON> shows enthusiasm for <PERSON><PERSON>, suggesting a trial and seeing potential benefits\n", "  - Type: champion\n", "  - Line Numbers: [366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377]\n", "  - Tags: ['trial', 'potential benefits', 'enthusiasm']\n"]}], "source": ["insights = await transcript_extraction_service_v2.extract_insights(\n", "    organization_id=organization_id,\n", "    compacted_transcript=transcript_container.transcript.compact(),\n", "    meeting=meeting,\n", ")\n", "\n", "for i, item in enumerate(insights):\n", "    print(f\"- Item {i + 1}:\")\n", "    print(f\"  - Insight: {item.insight}\")\n", "    print(f\"  - Type: {item.insight_type}\")\n", "    print(f\"  - Line Numbers: {item.line_numbers}\")\n", "    print(f\"  - Tags: {item.keyword_tags}\")"]}, {"cell_type": "markdown", "id": "18d53e66-6f40-4d51-9aab-63eaaf0065b7", "metadata": {}, "source": ["## Transcript lines (for reference)"]}, {"cell_type": "code", "execution_count": 9, "id": "48e452a4-cfa3-4482-b9df-d9d5148fb21f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON> [0]: Hey, how are you doing, <PERSON>?\n", "<PERSON><PERSON> [1]: How are you?\n", "<PERSON><PERSON> [2]: How are you doing?\n", "<PERSON> [3]: Well, I'm sitting here in Vancouver, just kind of getting my day going to some extent, but having a nice, sunny day.\n", "<PERSON><PERSON> [4]: It's, uh, it's rare that that happens these days because I don't know what's going on in Canada, particularly Toronto.\n", "<PERSON><PERSON> [5]: I mean, I know it rains quite a bit on the west coast, but, like, shit.\n", "<PERSON><PERSON> [6]: Shit's going down up there, man.\n", "<PERSON><PERSON> [7]: Shit's going down.\n", "<PERSON> [8]: I have, yeah, seen a lot of posts from Ontario.\n", "<PERSON> [9]: A lot of my friends sending me flooded golf courses.\n", "<PERSON> [10]: You see <PERSON>'s house flooding, and I'm flying out there next Thursday to head up to Muskoka.\n", "<PERSON> [11]: But, yeah, a lot of stuff going on.\n", "<PERSON><PERSON> [12]: Yeah, man.\n", "<PERSON><PERSON> [13]: So how do you know <PERSON>?\n", "<PERSON> [14]: Yeah, just grew up in the same hood, kind of.\n", "<PERSON> [15]: Yeah, I've been friends for a long time.\n", "<PERSON> [16]: I was just with him last weekend at the Calgary Stampede.\n", "<PERSON> [17]: He's just a good buddy that even though we live far apart, every time I'm in Toronto or he's here, we'll link up.\n", "<PERSON> [18]: So just kind of a childhood friend that we, you know, got to know each other more and more throughout the years, despite going to different high schools and then still hang out.\n", "<PERSON> [19]: And he's good friends with, like, my core group of friends from university.\n", "<PERSON><PERSON> [20]: Nice, nice that you had worked in a couple other.\n", "<PERSON><PERSON> [21]: Couple other big spots.\n", "<PERSON><PERSON> [22]: Right?\n", "<PERSON><PERSON> [23]: Exit, I think, did you not?\n", "<PERSON> [24]: I was at.\n", "<PERSON> [25]: I was at DoorDash for six years, and I was their first hire in Canada way back when and kind of built the team, the sales team out nationwide and spent some time there, but, yeah.\n", "<PERSON> [26]: How do you know?\n", "<PERSON><PERSON> [27]: His wife works for my company.\n", "<PERSON><PERSON> [28]: She was actually one of my first hires, and she was with me through the merger and acquisition as well.\n", "<PERSON><PERSON> [29]: So I've known <PERSON> for.\n", "<PERSON><PERSON> [30]: For.\n", "<PERSON><PERSON> [31]: Since 2012, so twelve years.\n", "<PERSON><PERSON> [32]: She actually introduced.\n", "<PERSON><PERSON> [33]: She didn't really introduce us, but she's a part of the whole me and my wife meeting thing.\n", "<PERSON><PERSON> [34]: Right.\n", "<PERSON><PERSON> [35]: She was friends with my wife, and so when we all worked together at the same restaurant in twenties, so then I met.\n", "<PERSON> [36]: I know <PERSON> decently, like, obviously, through all my trips and just seeing hass, she's become a good friend.\n", "<PERSON> [37]: <PERSON><PERSON> to their wedding last year, two years ago, and our kids are.\n", "<PERSON><PERSON> [38]: I was there, too.\n", "<PERSON> [39]: We did.\n", "<PERSON><PERSON> [40]: Yeah, we did a speech there.\n", "<PERSON><PERSON> [41]: It was me and.\n", "<PERSON><PERSON> [42]: Me and my wife.\n", "<PERSON> [43]: Oh, wow.\n", "<PERSON> [44]: Sorry, I don't recall exactly.\n", "<PERSON> [45]: Lots of weddings, lots of speeches.\n", "<PERSON><PERSON> [46]: Of course.\n", "<PERSON><PERSON> [47]: Of course.\n", "<PERSON><PERSON> [48]: Yeah, there's a lot of that one particularly do indian weddings.\n", "<PERSON><PERSON> [49]: You got to incorporate so many family members, etcetera.\n", "<PERSON><PERSON> [50]: Right?\n", "<PERSON> [51]: Nice.\n", "<PERSON> [52]: Yeah, yeah.\n", "<PERSON> [53]: It's always busy summers with the weddings, but I think I'm almost through it.\n", "<PERSON> [54]: I've got three, four, one more this year, three next year, and then hopefully my friend, friends just stop getting married because I've been to too many at this point.\n", "<PERSON> [55]: You know how the first few you're like super fun and then all sudden you're traveling all around the world and spending all your vacation time at weddings?\n", "<PERSON><PERSON> [56]: Yeah, it's a fun thing, but it ends up becoming quite costly over time and you're like, oh, yeah, you guys start saying no eventually.\n", "<PERSON><PERSON> [57]: Where did the year go?\n", "<PERSON> [58]: Yeah.\n", "<PERSON> [59]: So I appreciate you putting time on learning a bit about <PERSON><PERSON>.\n", "<PERSON> [60]: I'd love to just share some info on where we're at, what we're doing, the product today and the product three months ago, share a bit about our vision, but also understand, of course, what you're encountering, what your current flow looks like from a sales perspective, and ultimately your current tech stack and your goals from that perspective.\n", "<PERSON> [61]: That's really, it would rather keep it fairly light today, but give you a really good idea of what Google looks like and who's using it and how they're using it, and then tell you what's kind of on the near horizon.\n", "<PERSON> [62]: But we're really in our journey and you're kind of like part of the founding team, if not founder.\n", "<PERSON> [63]: <PERSON> Keel.\n", "<PERSON><PERSON> [64]: Yeah, I founded Keelmind, which was get ahead before sold to a larger company, Vector Health Laboratories, at the time, which turned into Keele Digital solutions, and we rebranded after the acquisition, the main product became the product that I sold to them.\n", "<PERSON><PERSON> [65]: That is like the main line of business, 99% of revenues generated by the platform I created.\n", "<PERSON><PERSON> [66]: And so I now sit on the executive board for this new healthcare company and one of the, one of the main people pushing it forward.\n", "<PERSON><PERSON> [67]: So that said, what I'm going to suggest is, let me see more about what, what you all do.\n", "<PERSON><PERSON> [68]: I could talk very lightly about the sales side of things and if I could use pipe driving that type of stuff.\n", "<PERSON> [69]: Right.\n", "<PERSON><PERSON> [70]: But ultimately, you know, if I understand what you do, it's going to be a better call with you and my director of operations and sales.\n", "<PERSON><PERSON> [71]: Right.\n", "<PERSON><PERSON> [72]: And, and so I'll make that introduction and, you know, and, you know, if he, if he signs off on it, then I'm sure we'll be able to move forward.\n", "<PERSON> [73]: Yeah, yeah, that sounds good.\n", "<PERSON> [74]: I mean, I'm not, I don't want to, you know, do heavy discovery if the product's not even going to connect and all that.\n", "<PERSON> [75]: So why don't I just give you my spiel, leave it in your hands to say, yeah, I think this would fit, if not all good, but I think interrupt me at any time, ask questions where you see fit.\n", "<PERSON> [76]: And, you know, because as I go through the demo, there's quite a bit of different things to show.\n", "<PERSON> [77]: And sometimes I can talk for ten minutes straight, which isn't that fun.\n", "<PERSON> [78]: So just interrupt me where you.\n", "<PERSON> [79]: Where you want, but I'll give you a quick intro.\n", "<PERSON> [80]: So I think basically, revo, fairly young, we were only founded four months ago, which I know is like, holy crap, that's scary.\n", "<PERSON> [81]: Yeah, right?\n", "<PERSON> [82]: You guys have any sort of like, decent product, but it is founded by the ex head engineering at Doordash.\n", "<PERSON> [83]: So he managed over a thousand thousand engineers and he picked his favorite twelve.\n", "<PERSON> [84]: And he kind of started revo.\n", "<PERSON> [85]: So I'm just leading the go to market side.\n", "<PERSON> [86]: And we have twelve pretty talented engineers.\n", "<PERSON> [87]: And our entire thesis here is we've been part of really early companies that have grown.\n", "<PERSON> [88]: And what we've seen is, as they.\n", "<PERSON><PERSON> [89]: Quick question, <PERSON>.\n", "<PERSON><PERSON> [90]: Is it okay with you?\n", "<PERSON><PERSON> [91]: I'm actually going to pull in my.\n", "<PERSON><PERSON> [92]: My sales guy and my director of operations for.\n", "<PERSON><PERSON> [93]: Because like, this is helpful for him to hear.\n", "<PERSON><PERSON> [94]: And instead of booking multiple meetings, like, we can probably figure out if this makes sense for us now after, as you described this.\n", "<PERSON> [95]: Yeah, yeah, go ahead.\n", "<PERSON> [96]: Of course.\n", "<PERSON><PERSON> [97]: I don't know that.\n", "<PERSON><PERSON> [98]: I'll just copy the joining info and put it this way.\n", "<PERSON><PERSON> [99]: Here we go.\n", "<PERSON> [100]: So put it in the chat if you need it.\n", "<PERSON><PERSON> [101]: Unless you're good.\n", "<PERSON><PERSON> [102]: Yeah, no, I just grabbed it from the meeting details and I just.\n", "<PERSON><PERSON> [103]: We use Google Meet.\n", "<PERSON> [104]: Okay.\n", "<PERSON> [105]: I don't think they'll have any issues.\n", "<PERSON> [106]: We'll see.\n", "<PERSON><PERSON> [107]: Yeah, you should be able to join.\n", "<PERSON><PERSON> [108]: Just a second.\n", "<PERSON><PERSON> [109]: He's right on his computer.\n", "<PERSON><PERSON> [110]: So I said, jump on now, buddy.\n", "<PERSON><PERSON> [111]: Let's go.\n", "<PERSON><PERSON> [112]: Sorry to interrupt you, buddy.\n", "<PERSON><PERSON> [113]: My apologies.\n", "<PERSON><PERSON> [114]: Come on, <PERSON><PERSON>, he might be trying to draw him out.\n", "<PERSON> [115]: Okay, I'll keep an eye out here.\n", "<PERSON><PERSON> [116]: I'll give you context.\n", "<PERSON><PERSON> [117]: As soon as you jump, I'll give.\n", "<PERSON><PERSON> [118]: Soon as you jump on, I'll give it.\n", "<PERSON><PERSON> [119]: Say it again.\n", "<PERSON><PERSON> [120]: Oh, there we go.\n", "<PERSON><PERSON> [121]: Hey, what's up?\n", "<PERSON><PERSON> [122]: Hey, buddy.\n", "<PERSON><PERSON> [123]: Nothing too much.\n", "<PERSON><PERSON> [124]: This is.\n", "<PERSON><PERSON> [125]: This is <PERSON>.\n", "<PERSON><PERSON> [126]: He.\n", "<PERSON><PERSON> [127]: I met him through a close friend of mine, <PERSON><PERSON>.\n", "<PERSON><PERSON> [128]: And essentially what he's going to describe to us right now is a system that can help us on the sales side.\n", "<PERSON><PERSON> [129]: Right.\n", "<PERSON><PERSON> [130]: A tech stack.\n", "<PERSON><PERSON> [131]: You know, I know we use Pipedrive right now, but what I thought is, you know, it's a short, just overview of what they're doing, you know, how it can help advance our mission.\n", "<PERSON><PERSON> [132]: And I thought it might be fruitful for you to be.\n", "<PERSON><PERSON> [133]: To be part of the conversation.\n", "<PERSON><PERSON> [134]: Just a quick background.\n", "<PERSON><PERSON> [135]: <PERSON> is one of the first hires on the us front for Door<PERSON>h, the engineer that he's partnered with, you know, they started about four months ago, right, which I know it seems relatively soon or new, but they made up a ton of ground because one of the main people that end up starting it with him was the head engineer at Doordash.\n", "<PERSON><PERSON> [136]: So they've got some wheels on the team itself, and he's just about to get into a bit of the background.\n", "<PERSON> [137]: Welcome.\n", "<PERSON> [138]: Thank you for bringing me in.\n", "<PERSON> [139]: Yeah, no problem.\n", "<PERSON> [140]: As I mentioned, I just want to.\n", "<PERSON> [141]: I'll run through <PERSON><PERSON>.\n", "<PERSON> [142]: I'm not going to do heavy discovery with you guys.\n", "<PERSON> [143]: I'll show you the product, and then if there is interest, can book, you know, a follow up meeting to go deeper or anything like that.\n", "<PERSON> [144]: But as he mentioned, you know, fairly nascent in our journey here, only four months in, but we have a talented team.\n", "<PERSON> [145]: We have 13 engineers today, but believe it or not, we're actually going up to 40 by the end of the year.\n", "<PERSON> [146]: And that's, that sounds crazy for something so early.\n", "<PERSON> [147]: But what we're trying to do is build a single platform where you have everything you need to sell, analyze data, manage current customers, and tie it all together through AI.\n", "<PERSON> [148]: And the AI component here is not replacing roles, but automating a lot of workflows.\n", "<PERSON> [149]: So, for example, at the end of this call, if I said, I'll send you a follow up, it's going to summarize this call for us.\n", "<PERSON> [150]: It's going to put into an email, it's going to help us write that super crafty follow up email, and then the rep can just tweak it from there, do 80% of the work.\n", "<PERSON> [151]: And so reps can maximize time selling and not as much, things like entering all your activity, staying up to date on every single deal, things like that.\n", "<PERSON> [152]: And that's our entire vision.\n", "<PERSON> [153]: So I'll show you.\n", "<PERSON> [154]: This just helps you.\n", "<PERSON> [155]: Sorry.\n", "<PERSON> [156]: AI as in your AI or AI as in chat?\n", "<PERSON> [157]: A little bit of both.\n", "<PERSON> [158]: We have some backend AI with and we're looking at chat GBT, but we've, you know, balanced, balance them out.\n", "<PERSON> [159]: And then we also have some AI, I don't know what you call them, but like ML writers that can incorporate our own code as well as part of our team.\n", "<PERSON> [160]: Okay, but the reason the thesis here is we've been part of a lot of small companies that have grown into bigger ones.\n", "<PERSON> [161]: And this slide is going to be messy as hell.\n", "<PERSON> [162]: It's not my slide.\n", "<PERSON> [163]: It's from a guy, <PERSON>.\n", "<PERSON> [164]: He's one of my advisors, and he wrote this book, founding sales.\n", "<PERSON> [165]: But this is the tech stack that begins to overwhelm companies as they begin to grow.\n", "<PERSON> [166]: And right now, if you look at a typical sales process from each different role, you're adding all these softwares to complement them.\n", "<PERSON> [167]: We're pretty confident in twelve months or less, it's gonna sound crazy, but we can build about 80% of this into a single platform.\n", "<PERSON> [168]: So today what we've done is we've built like an outreach and a sales loft into Revo.\n", "<PERSON> [169]: We have conversational intelligence and a lot of the gong components already, and we have prospecting and tilers and things like that in Revo.\n", "<PERSON> [170]: I'll show you in 1 second.\n", "<PERSON> [171]: But what we're tackling next is like actually executing contracts.\n", "<PERSON> [172]: Clarion forecasting is coming within a month.\n", "<PERSON> [173]: And then we're going to move up and think about how can we maximize this for management?\n", "<PERSON> [174]: And we think we're going to have a tremendous advantage, because everything that's going to occur from that sales side and outbound motion, from your very first touch all the way to the close, being able to have it live on one platform, instead of the data silos that exist today, there's like integrations, but they're never that perfect.\n", "<PERSON> [175]: And you're constantly trying to find the right data.\n", "<PERSON> [176]: But we think by having it live on one platform, we're going to be able to surface the very best insights.\n", "<PERSON> [177]: And from a leadership or a rev op perspective, ultimately our goal is to have you get every critical piece of data you need served up to you on a platter.\n", "<PERSON> [178]: So kind of like a Monday morning quarterback or a Friday afternoon recap of what happened with your team.\n", "<PERSON> [179]: Where the opportunities are, where the reps are lacking, and where we can push to hopefully get them closer to goal.\n", "<PERSON> [180]: So a rep can come in on Monday and see, okay, I'm pacing 7% behind goal.\n", "<PERSON> [181]: I need to make 63 calls, book 20, book five meetings to close two of them, something like that.\n", "<PERSON> [182]: But also prompt reports in natural language, and be able to say, show me the report for <PERSON>'s last 30 days activity and deals, and then have that report generate for you.\n", "<PERSON> [183]: But that's kind of the vision of <PERSON><PERSON>.\n", "<PERSON> [184]: Just to give you an idea of where.\n", "<PERSON> [185]: Where we're heading and why we think this may have an opportunity for success.\n", "<PERSON> [186]: So I'll run through this quick, but pause it or stop me if you have any questions.\n", "<PERSON> [187]: Yep.\n", "<PERSON> [188]: If you can go back to that slide for a sec, if you don't mind.\n", "<PERSON> [189]: I.\n", "<PERSON> [190]: Yeah, it feels more like a combination of, like, HubSpot and marketing automation more so than a CRM.\n", "<PERSON> [191]: So, yeah, we're not doing tons of, like, to compare us to HubSpot.\n", "<PERSON> [192]: I'd say very, very fair.\n", "<PERSON> [193]: But if are you guys.\n", "<PERSON> [194]: Oh, you have pipedrive, but yeah, the functionality that you're, you're comparing yourself to is more.\n", "<PERSON> [195]: More like marketing automation and HubSpot ish than it is.\n", "<PERSON> [196]: And <PERSON>b<PERSON>.\n", "<PERSON> [197]: HubSpot is kind of is a CRM, don't get me wrong, but they're not like creme de la crema CRM's.\n", "<PERSON> [198]: But it feels more like that.\n", "<PERSON> [199]: Yeah.\n", "<PERSON> [200]: Yeah.\n", "<PERSON> [201]: I would say from a HubSpot revo perspective, we're building a lot of the tools you need into a platform.\n", "<PERSON> [202]: But like, the marketing side and that marketing automation side, that's not in revo yet.\n", "<PERSON> [203]: What we're building is every single sales tool you need and you'd go pay for.\n", "<PERSON> [204]: So from prospecting to sales engagement to conversational intelligence to, like, task management, every one of those softwares actually, you know, over time and will start to kind of break the bank and.\n", "<PERSON> [205]: And they have diminishing value as you add them in.\n", "<PERSON> [206]: What we're saying is we can give you 80% of the tools for 20% of the cost and have it live in a space that's ultimately, I think, our big differentiator.\n", "<PERSON> [207]: Whereas, like, I, HubSpot will plug into gong and all these different tools and the data that comes from gong to HubSpot is so limited that you can't really get the insights you need.\n", "<PERSON> [208]: And then the thing that HubSpot doesn't do is take it to the next level.\n", "<PERSON> [209]: They have all the data, but how can they make it easier for you to find that, access it and leverage it for us?\n", "<PERSON> [210]: We want our CRM.\n", "<PERSON> [211]: We call this CRM today, but honestly, right now it's a conglomerate of really good point solutions for sales.\n", "<PERSON> [212]: And the CRM component will come together, but we don't want to rip and replace anyone's CRM tomorrow.\n", "<PERSON> [213]: We know that that's not feasible.\n", "<PERSON> [214]: So what we're going to do is give them all these tools for free.\n", "<PERSON> [215]: We're going to compare everything that's happening in Revo, giving you the right reports, everything you need and then eventually, hopefully, you say, hey, look, they're doing everything pipe drives doing and more.\n", "<PERSON> [216]: Plus we have all these tools and then eventually filter over.\n", "<PERSON> [217]: But we're not looking to change pipe drive today.\n", "<PERSON> [218]: We want to give you all the tools you need to sell and complement your current CRM.\n", "<PERSON> [219]: And then eventually, twelve to 18 months from now, hopefully provide such a strong use case that you actually do switch over and say, <PERSON><PERSON>'s got everything we need.\n", "<PERSON> [220]: We can cancel this pipe drive contract and we'll get five additional tools, plus AI automating some of our workflows.\n", "<PERSON> [221]: Okay, so I think from like a cooling perspective, your company and your team size and things like that, from what I can see on LinkedIn is exactly who we're selling into right now.\n", "<PERSON> [222]: And we have about, we only launched like invite only ten days ago, so we have ten companies right now using Revo, things like that, but on Monday, we're introducing it to about five more.\n", "<PERSON> [223]: So still, as I said, nascent in our journey, but completely functional.\n", "<PERSON> [224]: I use it every day, but from a prospecting standpoint, this is very similar to that of an Apollo.\n", "<PERSON> [225]: You guys are familiar with Apollo people, data, labs, zoom info, all that?\n", "<PERSON> [226]: I am, yeah.\n", "<PERSON> [227]: So you can build the exact search that you want.\n", "<PERSON> [228]: You can leverage the database is 240 million records.\n", "<PERSON> [229]: It's plugged into Apollo on the back end, so the exact same data you'd get there.\n", "<PERSON> [230]: But you can start to either add to an existing list, start from scratch, etcetera.\n", "<PERSON> [231]: Yeah, yeah.\n", "<PERSON> [232]: Just if you do, if you don't have a excellent tool today, it is built into Revo.\n", "<PERSON> [233]: The cooler aspect about this is once you do find your list and you select your people, you can import them directly into the platform and then.\n", "<PERSON> [234]: And then begin things like dialing.\n", "<PERSON> [235]: So if I go to one of my test phone numbers here, we have a dialer built in.\n", "<PERSON> [236]: Now, this can be used for cold calling or if you're hosting phone meetings.\n", "<PERSON> [237]: There's a few different benefits.\n", "<PERSON> [238]: If you're cold calling, you can call directly and use a revo provided number.\n", "<PERSON> [239]: You can use your own number.\n", "<PERSON> [240]: You can use an area code specified to the region you're calling to increase connect rates and meaningful touches because obviously local area code, more pickups, things like that.\n", "<PERSON> [241]: But if you're hosting a phone meeting as well as the cold call, but not as impactful, every single call you make is going to be if you want recorded and transcribed so you don't have to furiously take notes.\n", "<PERSON> [242]: You can jump into any given previous call you have had and it's going to give you a quick summary of everything that took place on that call.\n", "<PERSON> [243]: So if it's a phone meeting, we'll do a quick summary, some really key insights into what occurred, current challenges, any objections that you got, next steps that were identified on the call, possible buying motives and log and capture all that activity from that call so you're never really missing anything critical.\n", "<PERSON> [244]: And then if you do need, of course you can just edit it or something like that, you can jump to the portion of the call that that objection occurred, kind of relive it, and then if you're using any sales methodologies like medic ban will auto populate that if it is applicable within the call.\n", "<PERSON> [245]: So capturing every cold call, auto logging it, every phone meeting, capturing all the summaries, the insights you need, attaching it as it converts to a deal or an opportunity.\n", "<PERSON> [246]: However you use that today, it's going to be captured and the key things are going to be pinned to the account.\n", "<PERSON> [247]: So I can pin, you know, a key buying motive, I can action a lot of things in here.\n", "<PERSON> [248]: There's a note section.\n", "<PERSON> [249]: You can add some keynotes from that call.\n", "<PERSON> [250]: You can see here rapport, building opportunities, buying motives, potential blockers, pricing, next steps, and then also filter by speaker, find keywords, things like that.\n", "<PERSON> [251]: Very gong esque, but good for a rep because you're not having anything slip through the cracks and you're not having to furiously take notes while you're on every single call.\n", "<PERSON> [252]: So we got the dialer and that's once part of the sales engagement thing.\n", "<PERSON> [253]: You can also build and execute sequences.\n", "<PERSON> [254]: So you can see here, this is a classic sequence flow.\n", "<PERSON> [255]: If they respond, what will happen?\n", "<PERSON> [256]: A task is automatically created and notifying you and your CRM, you have a response.\n", "<PERSON> [257]: If no one responds, you can move to the next stage of that sequence or drip campaign.\n", "<PERSON> [258]: You can a, b and c tests, sorry, mark the marketing automation side.\n", "<PERSON> [259]: Yes.\n", "<PERSON> [260]: Yeah, yeah.\n", "<PERSON> [261]: So you can run a, b and c tests on messaging, find what's working, double down on the higher response rates, get all the metrics you need for your outbound campaigns by email.\n", "<PERSON> [262]: This is just a little demo account, so obviously not tons in here, but some basic info.\n", "<PERSON> [263]: The one thing I will share is that this is like, I think table stakes today, but where we're taking this is the AI components.\n", "<PERSON> [264]: So if they do respond, we're going to be able to gauge positive or negative sentiment in that email.\n", "<PERSON> [265]: Sorry, you're doing sentiment analysis?\n", "<PERSON> [266]: Yeah.\n", "<PERSON> [267]: So just be able to identify if it's positive, negative and then help auto prompt a response for you to minimize the amount of time between their response and yours to hopefully capitalize on booking that demo.\n", "<PERSON> [268]: The other aspect is it is synced up with like my entire email, calendar, things like that.\n", "<PERSON> [269]: And it helps us understand everything that's occurring.\n", "<PERSON> [270]: Part of that holistic picture that helps us tile the data points together and surface the right insights.\n", "<PERSON> [271]: But we do have a scheduler link, so if you're using calendly or another booking link system, this is part of Revo as well.\n", "<PERSON> [272]: You can set up 30 minutes, 45 minutes and onboarding calls.\n", "<PERSON> [273]: Copy the link, share it with prospects or current accounts to just book time on your calendar easier instead of all that back and forth and email.\n", "<PERSON> [274]: The cool thing I'd say about the scheduler is if a prospect books time with your sales team and they're not a current contact, we're automatically creating that contact for you based on the information we have in that booking link, the account they're associated with.\n", "<PERSON> [275]: So you don't really have to go do that manual entry of like, okay, I got this new prospect to book time.\n", "<PERSON> [276]: It's going to be automatically created.\n", "<PERSON> [277]: And then you can also toggle by sales events only.\n", "<PERSON> [278]: Like, are they a contact in my CRM?\n", "<PERSON> [279]: And if they are, they'll show up here versus all the internal meetings that sometimes clog up the noise on the calendar.\n", "<PERSON> [280]: It just, just a simple view like that.\n", "<PERSON> [281]: You can book new meetings, you can meet.\n", "<PERSON> [282]: Now it's basically Google calendar just kind of built into Revo.\n", "<PERSON> [283]: And you're tracking open rates and unsubscribes and all the other crap too, right?\n", "<PERSON> [284]: Yeah.\n", "<PERSON> [285]: Through the sequencing we can definitely crack all that.\n", "<PERSON> [286]: And then we can bucket unsubscribes.\n", "<PERSON> [287]: We can automatically create pass offs on unsubscribes.\n", "<PERSON> [288]: Similar to like a close loss funnel if you want to reengage in a different way.\n", "<PERSON> [289]: But you got the dialer, you got sequencing, you got the calendar link, and then also if you're hosting a meeting like this, you guys saw when you joined, I agree to be recorded.\n", "<PERSON> [290]: Every single meeting will be, you know, captured and logged, similar to the phone call I showed you earlier.\n", "<PERSON> [291]: So summary all that stuff, and then everything that's happening in this meeting, there's a few different things that are about to come.\n", "<PERSON> [292]: So say, have you ever heard of battle cards?\n", "<PERSON> [293]: No.\n", "<PERSON> [294]: So basically if we're in a meeting and I say a sentence that would trigger a battle card, which is a little card that's going to pop up to help me navigate any objections or give me that key piece of data to help me navigate a conversation, but are really good for ramping onboarding reps, or even tenured reps who just need a little.\n", "<PERSON> [295]: Sorry, it's like a cold note type thing.\n", "<PERSON> [296]: You said.\n", "<PERSON> [297]: Yeah, yeah.\n", "<PERSON> [298]: Like if you hit me with an objection right now saying, hey, how are you different from HubSpot?\n", "<PERSON> [299]: I'm gonna have a little card that triggers and pops up three key points to help me navigate that objection and get past those revenue critical moments within a call.\n", "<PERSON> [300]: That's really cool.\n", "<PERSON> [301]: Not ready today.\n", "<PERSON> [302]: Probably about 30 to 40 days they'll be ready.\n", "<PERSON> [303]: So it stops you from the type jumping between screens.\n", "<PERSON> [304]: It's just meant to be something that really helps every rep.\n", "<PERSON> [305]: And you can customize these by rep, you can customize these by trigger words, you can customize them by data points that are surface.\n", "<PERSON> [306]: But I'll have that data point pop up.\n", "<PERSON> [307]: Only I can see it, I can quickly reference it, respond to my objection, and keep pushing the call forward like that.\n", "<PERSON><PERSON> [308]: Particularly as an aside, <PERSON>, particularly with what we do, because it's so specialized.\n", "<PERSON><PERSON> [309]: Right.\n", "<PERSON><PERSON> [310]: This, that part could be quite fruitful.\n", "<PERSON> [311]: Yes.\n", "<PERSON><PERSON> [312]: Your typical sales approach, right?\n", "<PERSON> [313]: New salespeople for consultants, whatever.\n", "<PERSON> [314]: Yeah.\n", "<PERSON> [315]: You get.\n", "<PERSON> [316]: If you asked about <PERSON><PERSON>, what do you say?\n", "<PERSON> [317]: You don't kind of stumble.\n", "<PERSON> [318]: Yes, yeah, exactly.\n", "<PERSON> [319]: And like, over time, a rep after one year is going to have a bunch of really strong rebuttals in their back pocket.\n", "<PERSON> [320]: But what we can do is help, like, almost, almost train and guide them earlier.\n", "<PERSON> [321]: Not to mention, they're not saying, I'll follow up in a day, I'll follow up once I speak to my team.\n", "<PERSON> [322]: It's right there.\n", "<PERSON> [323]: Yeah, exactly.\n", "<PERSON> [324]: So battle cards are coming, and then also automating things like next steps from our call.\n", "<PERSON> [325]: If you said, shoot me a follow up email with everything we discussed, we're going to take that, we're going to put it into a nice email format for the rep, tweak it, send it off, and in the deal view.\n", "<PERSON> [326]: This is pretty standard stuff, just managing your deal pipeline.\n", "<PERSON> [327]: You can do a board view, you can do the list view, but the list view is good for, like, kind of pipeline reviews.\n", "<PERSON> [328]: What's the next step?\n", "<PERSON> [329]: Next step?\n", "<PERSON> [330]: Date expected, close date.\n", "<PERSON> [331]: Just giving you that quick snapshot.\n", "<PERSON> [332]: And then you can double click into anything at any time and get a holistic view of everything that's occurred.\n", "<PERSON> [333]: So every engagement, phone calls, the sequences, the meetings that we've had, and the keynotes that were captured across those calls right there in the deal for you.\n", "<PERSON> [334]: And then the action from the deals views slightly different.\n", "<PERSON> [335]: You can, you can fire off an email directly from in here to the decision maker.\n", "<PERSON> [336]: You can create that task that you need, assign it to a different team member like an internal stakeholder, your colleague, your manager can assign it to a rep.\n", "<PERSON> [337]: Just saying like where are the next steps?\n", "<PERSON> [338]: Please add with a due date.\n", "<PERSON> [339]: Pretty standard stuff, but also schedule a meeting and, and all that stuff.\n", "<PERSON> [340]: But you can hopefully get a good view of everything that's going on from tasks to engagements to deal notes.\n", "<PERSON> [341]: The only thing I'll add here before I pause is where we're taking this is kind of going to be helping reps in every situation.\n", "<PERSON> [342]: So say my deal pipeline gets really bulky and meeting held and we can tell that this compared to Rep BCD is getting too many days in the current stage, they're having trouble moving it to the next stage.\n", "<PERSON> [343]: Not only are we going to surface that as an insight to the rep, we're also going to try to pair it with sales collateral.\n", "<PERSON> [344]: We noticed that this company meta has just raised send a congratulatory follow up email to help push them to the next stage and hopefully reengage with a value added follow up.\n", "<PERSON> [345]: That's kind of how we're thinking about it.\n", "<PERSON> [346]: And same goes for prospecting within your entire lead list.\n", "<PERSON> [347]: Like all my contacts here, we can track email domains similar to that of targeted marketing.\n", "<PERSON> [348]: So if you go visit a page, you know you're going to get hit on like Instagram, things like that.\n", "<PERSON> [349]: But for sales, what we can do is if there's a recent spike in, for me, like companies searching CRMs, a recent spike that's an outlier in their regular activity.\n", "<PERSON> [350]: I know that's a high intent buying signal and I'm going to reach out to those top prospects and that's going to be ready by September.\n", "<PERSON> [351]: So a little bit of where we are today, you got dialers, meet, conversational intelligence, scheduler links, the meeting center summaries, the deal views, and you can use all that today, completely free.\n", "<PERSON> [352]: We've raised 30 million.\n", "<PERSON> [353]: And our entire philosophy here is we want customers, we want to be customer obsessed.\n", "<PERSON> [354]: We're closely alongside our customers, get them using it, understand what they want to see, what's great, what's bad, refine, tweak and make a better product.\n", "<PERSON> [355]: And we're handing it out for free.\n", "<PERSON> [356]: And I know there's like how long is that going to go for?\n", "<PERSON> [357]: We're thinking like end of year and then after that something around 9100 bucks per user per month, which if you add things like gone calendly sales off.\n", "<PERSON> [358]: Like it's, it's about 30, 20, 30% of that cost of adding all them independently.\n", "<PERSON> [359]: So that's kind of where we're at.\n", "<PERSON> [360]: A lot of stuff there.\n", "<PERSON> [361]: I've been chatting like crazy.\n", "<PERSON> [362]: Any like, questions, anything you see that's, you know, possible interest or didn't see product before.\n", "<PERSON><PERSON> [363]: Before I got a jump.\n", "<PERSON><PERSON> [364]: I got a meeting actually starting right now.\n", "<PERSON><PERSON> [365]: <PERSON>, first and foremost, thank you for the time and that demo.\n", "<PERSON><PERSON> [366]: A lot of cool shit that I see there.\n", "<PERSON><PERSON> [367]: <PERSON> would likely be the decision maker on this one for me.\n", "<PERSON><PERSON> [368]: Right.\n", "<PERSON><PERSON> [369]: But <PERSON>, you know, just to put a bug in your ear, particularly if there's no charge in the initial point, like, I'd love to try it out with some of our members, right.\n", "<PERSON><PERSON> [370]: To do a trial with it.\n", "<PERSON><PERSON> [371]: And, you know, if we're able to get some of these things he's talking about the building in.\n", "<PERSON><PERSON> [372]: Like, I definitely think that it will shift and improve our approach because we're not like, look, if this was a regular sales company, Ross, then I would say, you know, pipe drive or Salesforce or whatever the fuck, you know, insert company here would do the job, right.\n", "<PERSON><PERSON> [373]: But because we sell specifically business to government and sometimes organizations and to specialize, like, I can't just go hire a salesperson off the, off the street, right.\n", "<PERSON><PERSON> [374]: I think it would be helpful.\n", "<PERSON><PERSON> [375]: Particularly we can learn from data and how exactly we often get the comparison of our platform to Zoom, which we're vastly different from understanding how to navigate that will be helpful for them.\n", "<PERSON><PERSON> [376]: And then also I'm a big fan of automation.\n", "<PERSON><PERSON> [377]: No wasted movements.\n", "<PERSON> [378]: Especially we're going with.\n", "<PERSON><PERSON> [379]: But that's my two cent.\n", "<PERSON><PERSON> [380]: I'm going to jump.\n", "<PERSON> [381]: <PERSON>, again, thank you for the time.\n", "<PERSON> [382]: I'll leave you with <PERSON>.\n", "<PERSON><PERSON> [383]: Thanks for joining last minute.\n", "<PERSON> [384]: Thank you.\n", "<PERSON> [385]: Thank you.\n", "<PERSON> [386]: So I think from my perspective, a lot of companies, what they've been doing, it's very similar to what was just said.\n", "<PERSON> [387]: And that's not committing to anything, not making a major change.\n", "<PERSON> [388]: You tell us what data you would need from your current setup to live within revo to make that experience useful.\n", "<PERSON> [389]: We'll do that import, we'll put all the grunt work in to set it up, make it look good.\n", "<PERSON> [390]: We'll set you up some predetermined sequences based on what we think would be optimal.\n", "<PERSON> [391]: You can create your own, but you can get one rep in it.\n", "<PERSON> [392]: You can get multiple, you guys can play around, utilize it, and then hopefully be able to compare data or direct feedback from your team on what they love, if it's better.\n", "<PERSON> [393]: And then compared side by side to your current setup.\n", "<PERSON> [394]: And as I mentioned, there's no rush on like any sort of commitment payment, things like that.\n", "<PERSON> [395]: I don't even know if, like some companies say, well, we'd like to agree to a statement of work or like a term sheet, sure.\n", "<PERSON> [396]: But for us, we just want users, and we're happy to set you up an account completely free, start using it, tell us what you love, what you hate, and then we'll try to build it better.\n", "<PERSON> [397]: That's ultimately our goal.\n", "<PERSON> [398]: So here is, and I'm going to have to bail shortly, but here is, I need kind of a demo access so I can actually digest this.\n", "<PERSON> [399]: Yep.\n", "<PERSON> [400]: As you said, there's a lot going on in a good way.\n", "<PERSON> [401]: I didn't see anything on product.\n", "<PERSON> [402]: So how to add product to the system, I'm guessing it's there, but it's been compressed.\n", "<PERSON> [403]: So let's, let's get that sorted by add product to the system.\n", "<PERSON> [404]: Could you.\n", "<PERSON> [405]: We have, we have skews.\n", "<PERSON> [406]: Right?\n", "<PERSON> [407]: Right.\n", "<PERSON> [408]: So we're selling.\n", "<PERSON> [409]: We're SaaS software platform.\n", "<PERSON> [410]: We sell.\n", "<PERSON> [411]: We sell skus.\n", "<PERSON> [412]: And so we need to have the skus in the system so that the SKus dictate price point, which dictate the kind of the bundle.\n", "<PERSON> [413]: If somebody buys like 30 licenses of a supervisor and 40 of a trainee, that dictates the value of the deal.\n", "<PERSON> [414]: Right.\n", "<PERSON> [415]: So I didn't see that in your system.\n", "<PERSON> [416]: Doesn't mean it's not there.\n", "<PERSON> [417]: Yeah, but those types of things I just need to assess out and then we can figure out kind of next steps after that.\n", "<PERSON> [418]: Yeah.\n", "<PERSON> [419]: We have a lot of companies in similar positions.\n", "<PERSON> [420]: We've done a few different things with them.\n", "<PERSON> [421]: I think I will.\n", "<PERSON> [422]: What I'll do is just share that with my team.\n", "<PERSON> [423]: They'll have do a little bit of digging.\n", "<PERSON> [424]: We might ask you some questions about, you know, how would this be optimal to show up on your end and do further digging?\n", "<PERSON> [425]: But it sounds like, hey, there might be a bit of interest.\n", "<PERSON> [426]: Let's try it out.\n", "<PERSON> [427]: You guys just tell us exactly what you need and we'll work to build that environment for you.\n", "<PERSON> [428]: And then I think just get you.\n", "<PERSON> [429]: Get you some sort of access.\n", "<PERSON> [430]: Yeah, give me an environment.\n", "<PERSON> [431]: Give me some access, and then I'll commit to a timeline to kind of get back to you, and then we'll figure out what we can do.\n", "<PERSON> [432]: I know there's no kind of.\n", "<PERSON> [433]: We're not married to anything.\n", "<PERSON> [434]: Right.\n", "<PERSON> [435]: Yeah.\n", "<PERSON> [436]: At the end of the day, so it really is like what's best for the business and what's going to help us.\n", "<PERSON> [437]: Scale stuff is kind of interesting, especially as our product is so unique.\n", "<PERSON> [438]: Those types of things are interesting to look at.\n", "<PERSON> [439]: We just got to look at it and see one.\n", "<PERSON> [440]: You're young so stability is going to be key being and beyond that, that's it.\n", "<PERSON> [441]: Yep.\n", "<PERSON> [442]: 100%.\n", "<PERSON> [443]: I agree.\n", "<PERSON> [444]: Sounds good.\n", "<PERSON> [445]: I appreciate you guys being open to it and no need to marry us.\n", "<PERSON> [446]: Maybe just date us for if you want to push it forward.\n", "<PERSON> [447]: Love it, man.\n", "<PERSON> [448]: Yeah.\n", "<PERSON> [449]: Look, as I said, we're not, they're all, there's similarities in all of them.\n", "<PERSON> [450]: They all have their, which is pipe drive is decked by 1000 cuts.\n", "<PERSON> [451]: Everything costs additional and so at some point we'll probably outgrow them because they started out as like you look at Salesforce and what they charge, right?\n", "<PERSON> [452]: You look at pipe Drive and this introductory charge and then you want, oh, you want a bulk message.\n", "<PERSON> [453]: Well, it's going to cost x.\n", "<PERSON> [454]: Oh, you want to be able to do docusign.\n", "<PERSON> [455]: Well, it's going to cost y and it's just a constant death by 1000 cuts and that also doesn't scale so wide open to taking a look.\n", "<PERSON> [456]: Okay, sounds great.\n", "<PERSON> [457]: I appreciate you jumping on blindly, <PERSON>, but pleasure to meet you and some of the teams.\n", "<PERSON> [458]: So have a great day.\n", "<PERSON> [459]: We'll be.\n", "<PERSON> [460]: You got my email address or whatever?\n", "<PERSON> [461]: Yeah, yeah.\n", "<PERSON> [462]: Oh, yeah.\n", "<PERSON> [463]: You're not on the invite but mine's just Rossivo <PERSON>.\n", "<PERSON> [464]: But I, and I'll also take yours and I'll send a follow up to you and <PERSON><PERSON>.\n", "<PERSON> [465]: And what's yours?\n", "<PERSON> [466]: I'll put it in the chat.\n", "<PERSON> [467]: Yeah.\n", "<PERSON> [468]: And then we'll be in touch.\n", "<PERSON> [469]: I think I'd get you some sort of like grilled access demo account set up next week but I think it'd be important to understand a little bit more about skew's product and also the your data currently lives and what the import would look like if it's possible to get from pipedrive like what's critical to you and how can we set that up.\n", "<PERSON> [470]: But that's future calls and stuff.\n", "<PERSON> [471]: Let's not worry about that yet.\n", "<PERSON> [472]: Typically I just get my engineering lead to jump on and say, okay, I've seen this before.\n", "<PERSON> [473]: We'll import that, we'll get that set up and then go live at a date that you want and use it as you wish.\n", "<PERSON> [474]: And we're definitely going to stay close to every new user to make sure we can build the best thing possible for our customers.\n", "<PERSON> [475]: Thanks.\n", "<PERSON> [476]: <PERSON> meeting you, man.\n", "<PERSON> [477]: Yeah, you too.\n", "<PERSON> [478]: Have a great day.\n", "<PERSON> [479]: You as well.\n", "<PERSON> [480]: Be well.\n", "<PERSON> [481]: All right.\n", "<PERSON> [482]: Bye.\n"]}], "source": ["print(transcript_container.transcript.compact())"]}, {"cell_type": "code", "execution_count": null, "id": "baff3573-1b6d-4da7-a177-f02093cf3a7e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}