{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['frequency_penalty', 'logit_bias', 'logprobs', 'top_logprobs', 'max_tokens', 'max_completion_tokens', 'modalities', 'prediction', 'n', 'presence_penalty', 'seed', 'stop', 'stream', 'stream_options', 'temperature', 'top_p', 'tools', 'tool_choice', 'function_call', 'functions', 'extra_headers', 'parallel_tool_calls', 'response_format']\n", "Model gemini-2.0-flash isn't mapped yet. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json\n", "getting company news\n"]}], "source": ["#notebook for testing company news research prompts\n", "\n", "import time\n", "import uuid\n", "\n", "from salestech_be.core.research_agent.types import ResearchContext\n", "from salestech_be.temporal.activities.research_agent.company_news import get_company_news, extract_json_block\n", "from salestech_be.temporal.activities.research_agent.company_site import get_company_site_content\n", "\n", "from salestech_be.core.research_agent.types import ResearchContextType\n", "from salestech_be.core.research_agent.types import ResearchTime\n", "\n", "from litellm import get_supported_openai_params, get_max_tokens\n", "params = get_supported_openai_params(model=\"vertex_ai/gemini-2.0-flash\")\n", "print(params)\n", "try:\n", "    print(get_max_tokens(model=\"vertex_ai/gemini-2.0-flash\"))\n", "except Exception as e:\n", "    print(e)\n", "\n", "#set company name and domain\n", "# company_name = \"haven\"\n", "# company_domain = \"usehaven.com\"\n", "\n", "company_name = \"Craneway Pickleball Pavilion\"\n", "company_domain = \"pbdevgroup.com\"\n", "#create ResearchContext\n", "ctx = ResearchContext(\n", "    research_type=ResearchContextType.COMPANY,\n", "    research_time=ResearchTime(\n", "        cdc_triggered_at=time.time_ns(),\n", "        intel_created_at=time.time_ns(),\n", "        wf_started_at=time.time_ns(),\n", "    ),\n", "    session_id=str(uuid.uuid4()),\n", "    user_id=uuid.uuid4(),\n", "    tags=[\"company_news\"],\n", ")\n", "\n", "#get company news\n", "company_site_info = await get_company_site_content(ctx, company_name, company_domain)\n", "\n", "print(\"getting company news\")\n", "\n", "res = await get_company_news(ctx, company_name, company_domain, enforce_company_name=True, company_description=company_site_info)\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["title='Haven is building AI Agents to automate property management operations' url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AQXblrzpU8Mr1uFDXNCjRqhf8iik4JmW5zHwrvEWJS5LTIrzt9-2tXpEnt2OYlVDbXHXhLZ_Xeut83m8IsQxKfyQK-Gh_2UazCXBDpoWsDafJzGVz-l0j96k2Ww_ekbnY17-O97j30zO9ujs' published_date=None summary='Haven is building AI Agents to automate property management operations. Our first Agent is automating the maintenance coordination process. This means answering the phone when tenants have a maintenance issue, diagnosing the issue, dispatching a technician, and then following up with tenants to make sure the issue is resolved.'\n", "title='Haven: AI workforce for property management | Y Combinator' url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AQXblrzpU8Mr1uFDXNCjRqhf8iik4JmW5zHwrvEWJS5LTIrzt9-2tXpEnt2OYlVDbXHXhLZ_Xeut83m8IsQxKfyQK-Gh_2UazCXBDpoWsDafJzGVz-l0j96k2Ww_ekbnY17-O97j30zO9ujs' published_date=None summary='<PERSON>, Founder. Co-founder and CEO @ Haven, an AI-powered powered front desk for property managers. Prior to Haven, my cofounders and I built PeerPal (acquired), an admissions engagement platform for private K-12 schools.\\nS<PERSON><PERSON>, Founder. Co-founder at Haven. Former co-founder of PeerPal (acquired).'\n", "title=\"Haven's AI agents for property management\" url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AQXblryZLb0BOyKSB65t6qla9BSpTNkKJxcFmKJTaBvFPn1YDduYN3EygPVoZSPL3xos_LX7q5pwXbn0U-Xc2qTD1FMnp_nu6l4U0jfiXxz5nElwFc-YOkw=' published_date=None summary=\"Haven's AI agents, trained for property management, handle manual tasks like maintenance intake and coordination, so your team can focus on delivering excellent service to tenants and owners. 90% of tenant calls automated. Handles after hours & emergency maintenance calls. Triages and assigns work orders to preferred vendors. Follows up with vendors and tenants to ensure work order completion.\"\n", "title='Haven automates finances for startups' url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AQXblryCDq8aHlo0L73hXvW4AdaxeQA9H11Qna7V8W2fl52JfXLpCcXUaKN04i2qedriJC7iFB3ht1ugDgTO4lzdTA86vhFgaNClKciPFq3Hnq3rptCarMA5jcfQfARH8Q==' published_date=None summary=\"Haven takes care of your finances in order to free up your time, We'll find you more tax credits, help you optimize your treasury and most importantly free up your time to let you focus on what matters most - building your business! Haven will provide you with dedicated support to ensure you no longer have to sit around waiting for a response. We set up a dedicated slack channel for all clients.\"\n", "title=\"Testimonials about Haven's accounting services\" url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AQXblrysC5RMcLfIFwfiit5Uca83IG_OjOGhAmSwlpUlyO_OeCUXUur8CH1fc4vJFf08TWISUyLwxMyEYKzgW45qWT-M65wRTNtJe580vY9Izvnl3cY6dA==' published_date=None summary='Haven is the go to for startup financial back office operations — helping with way more than just bookkeeping and taxes. On average they save me and my co-founder 10+ hours a week. And their responsiveness + customer service is the best!\\nHaven made the R&D tax credit filing incredibly easy and fast. The team was responsive and did a great job not only putting together a rock solid proposal & working directly with our accounting firm, but also answered all of my questions thoroughly and helped me learn along the way.'\n", "title=\"Haven's efficient R&D tax credit process\" url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AQXblrwFBpGa-Kwoi0f9S9KMuYtFQuAhk3KyREy5L3xooIaezyOrhSK8DQ1k_Dt2UqxAbEyL8Vl0DaZC_bLUZnK-HzjwZrCHGTEBoOHrqLEdNiFoGOxczCL1wJnMMRPLNRMdjIQ=' published_date=None summary=\"Haven's team made claiming the R&D tax credit an incredibly efficient process – getting our R&D study ready in 3 days. Their top notch support and guidance made them seamless to work with and put all my questions to rest. Not to mention the $1000s their competitive pricing saved our company.\\nWorking with Haven has let me put my accounting and tax on autopilot, which is so valuable.\"\n"]}], "source": ["for item in res:\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["end_idx is -1\n", "None\n"]}], "source": ["def extract_json_block(content: str) -> str | None:\n", "    \"\"\"Extract content between ```json and ``` markers.\n", "\n", "    Args:\n", "        content: String that may contain a JSON code block\n", "\n", "    Returns:\n", "        Extracted content between markers, or None if no valid block found\n", "    \"\"\"\n", "    if not content:\n", "        return None\n", "\n", "\n", "    # Find start of JSON block\n", "    start_marker = \"```json\\n\"\n", "    end_marker = \"\\n```\"\n", "\n", "    start_idx = content.find(start_marker)\n", "    if start_idx == -1:\n", "        print(\"start_idx is -1\")\n", "        return None\n", "\n", "    # Adjust index to start of actual content\n", "    start_idx += len(start_marker)\n", "\n", "    # Find end of block from start position\n", "    end_idx = content.find(end_marker, start_idx)\n", "    if end_idx == -1:\n", "        print(\"end_idx is -1\")\n", "        return None\n", "\n", "    # Extract content between markers\n", "    return content[start_idx:end_idx].strip()\n", "\n", "    \n", "\n", "\n", "\n", "print(extract_json_block(\"```json\\n {\\n  \\\"news\\\": [\\n    {\\n      \\\"title\\\": \\\"Riot Games Announces New Game: League of Legends: Wild Rift\\\",\\n      \\\"url\\\": \\\"https://www.riotgames.com/news/league-of-legends-wild-rift\\\",\\n      \\\"published_date\\\": \\\"2021-01-01\\\",\\n      \\\"summary\\\": \\\"Riot Games announces a new game called League of Legends: Wild Rift.\\\"\\n    }\\n  ]\\n}\\n \\n```\"))"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Company News Results for tesla (tesla.com):\n", "================================================================================\n", "\n", "1. Four Key Takeaways From Tesla's Latest Earnings and <PERSON><PERSON>'s Conference Call\n", "--------------------------------------------------------------------------------\n", "Published Date: 2025-01-30\n", "URL: https://www.investopedia.com/news/four-key-takeaways-teslas-latest-earnings-elon-musks-conference-call/\n", "\n", "Summary:\n", "Tesla's earnings report and conference call revealed plans for new self-driving software and the Optimus robot.  CEO <PERSON><PERSON> discussed unsupervised self-driving software launching as a paid service around June in Texas and California, with other markets possible this year.  He also mentioned the potential for Tesla owners to rent their vehicles autonomously next year.  The call followed a disappointing quarterly report, with revenue and adjusted profits falling short of estimates after Tesla reported its first-ever annual decline in vehicle deliveries.\n", "================================================================================\n", "\n", "2. Tesla Stock Rises After Soft Earnings As Musk Predicts It Will Be World's Biggest Company\n", "--------------------------------------------------------------------------------\n", "Published Date: 2025-01-30\n", "URL: https://www.forbes.com/sites/dereksaul/2025/01/30/tesla-stock-rises-after-soft-earnings-as-musk-predicts-it-will-be-worlds-biggest-company/\n", "\n", "Summary:\n", "Tesla shares rallied despite a fourth-quarter earnings report that missed expectations and marked a second consecutive year of declining profits. Investors focused on CEO <PERSON><PERSON>'s bullish commentary and updated production timelines, including for its \"robotaxi\" driverless cab. <PERSON><PERSON> predicted Tesla could become the world's most valuable company, surpassing the top five companies combined.  He also highlighted a potential $10 trillion revenue opportunity for Tesla's \"Optimus\" humanoid robots.  Critics, however, pointed out the disconnect between Tesla's share price and its financial performance.\n", "================================================================================\n", "\n", "3. Tesla plans to launch a robotaxi service in Austin, Texas, this June\n", "--------------------------------------------------------------------------------\n", "Published Date: 2025-01-30\n", "URL: https://www.autonews.com/mobility-report/tesla-plans-launch-robotaxi-service-austin-texas-june\n", "\n", "Summary:\n", "Tesla plans to launch a robotaxi service in Austin, Texas, this June before expanding globally, according to CEO <PERSON><PERSON>'s statement on a January 29 earnings call.  The company also expects to return to volume growth this year after a sales decline in 2024.\n", "================================================================================\n", "\n", "4. Tesla Releases Fourth Quarter and Full Year 2024 Financial Results\n", "--------------------------------------------------------------------------------\n", "Published Date: 2025-01-29\n", "URL: https://ir.tesla.com/press-release/tesla-releases-fourth-quarter-and-full-year-2024-financial-results\n", "\n", "Summary:\n", "Tesla has released its financial results for the fourth quarter and full year ended December 31, 2024.  The information is available on Tesla's Investor Relations website.\n", "================================================================================\n"]}], "source": ["#display the LLM powered search results\n", "print(f\"\\nCompany News Results for {company_name} ({company_domain}):\")\n", "print(\"=\" * 80)\n", "\n", "if not res:\n", "    print(\"No news found.\")\n", "else:\n", "    for idx, news in enumerate(res, 1):\n", "        print(f\"\\n{idx}. {news.title}\")\n", "        print(\"-\" * 80)\n", "        print(f\"Published Date: {news.published_date}\")\n", "        print(f\"URL: {news.url}\")\n", "        print(\"\\nSummary:\")\n", "        print(news.summary)\n", "        print(\"=\" * 80)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["CompanySiteContent(products_and_services=\"Premier Football Consulting offers services including initial assessments of student-athletes' potential, written evaluations, film editing assistance, course selection guidance, and support for Name, Image, and Likeness (NIL) opportunities through their Premier NIL Consulting division.\", ideal_customer_profile='The ideal customer profile for Premier Football Consulting includes high school and college student-athletes seeking guidance in the college football recruitment process, as well as their families who are looking for personalized support and strategies to enhance their prospects and maximize NIL opportunities.', case_studies=[CaseStudy(company_name='Various Colleges', content=\"Premier Football Consulting has successfully mentored over 1000 student-athletes, helping them secure spots at a wide range of colleges, including top academic institutions and prominent football programs. Testimonials from parents highlight the personalized approach and invaluable guidance provided, emphasizing the company's strong reputation and connections with college coaches.\")])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["company_site_info"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}