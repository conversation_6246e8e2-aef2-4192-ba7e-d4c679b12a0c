{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Em<PERSON> sent <NAME_EMAIL>\n", "Message saved to Sent folder\n"]}], "source": ["from email.utils import formatdate\n", "import smtplib\n", "import imaplib\n", "from email.mime.text import MIMEText\n", "from email.mime.multipart import MIMEMultipart\n", "import socket\n", "import time\n", "import uuid\n", "\n", "\n", "smtp_host = 'iris.secured-shield.com'\n", "smtp_port = 587\n", "smtp_username = '<EMAIL>'\n", "smtp_password = 'LHr?tAUMRRLlKgUDsP'\n", "\n", "imap_host = 'iris.secured-shield.com'\n", "imap_username = '<EMAIL>'\n", "imap_password = 'LHr?tAUMRRLlKgUDsP'\n", "\n", "imap = imaplib.IMAP4_SSL(imap_host)\n", "imap.login(imap_username, imap_password)\n", "\n", "def generate_message_id(domain=None):\n", "    \"\"\"\n", "    Generate a unique Message ID compliant with RFC 5322.\n", "    \n", "    :param domain: The domain to use in the Message ID. If None, uses the local hostname.\n", "    :return: A string containing the generated Message ID.\n", "    \"\"\"\n", "    # Use UUID version 1 (time-based) for uniqueness\n", "    unique_id = uuid.uuid1().hex\n", "\n", "    # Use provided domain or fallback to hostname\n", "    if domain is None:\n", "        domain = socket.getfqdn()\n", "\n", "    # Ensure the local part doesn't exceed 64 characters\n", "    local_part = unique_id[:64]\n", "\n", "    return f\"<{local_part}@{domain}>\"\n", "\n", "def send_email(subject, body, to_email):\n", "    # Create the email message\n", "    msg = MIMEMultipart()\n", "    msg['From'] = smtp_username\n", "    msg['To'] = to_email\n", "    msg['Subject'] = subject\n", "    msg['Message-ID'] = generate_message_id('tryreevo.com')\n", "    msg['Date'] = formatdate()\n", "\n", "    # Attach the body of the email\n", "    msg.attach(MIMEText(body, 'plain'))\n", "\n", "    try:\n", "        # Create SMTP session\n", "        with smtplib.SMTP(smtp_host, smtp_port) as server:\n", "            server.starttls()  # Enable TLS\n", "            server.login(smtp_username, smtp_password)\n", "            \n", "            # Send the email\n", "            server.send_message(msg)\n", "        print(f\"Email sent successfully to {to_email}\")\n", "\n", "        # Save the sent message to the \"Sent\" folder\n", "        imap.select('INBOX')\n", "        imap.append('INBOX', None, imaplib.Time2Internaldate(time.time()), msg.as_string().encode())\n", "        print(\"Message saved to INBOX folder\")\n", "\n", "    except Exception as e:\n", "        print(f\"Error sending email or saving to Sent folder: {str(e)}\")\n", "\n", "send_email(subject=\"New Thread\",\n", "           body=\"This is a test email body.\",\n", "           to_email=\"<EMAIL>\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import email\n", "from email.header import decode_header\n", "import hashlib\n", "import imaplib\n", "from io import BytesIO\n", "\n", "imap_host = 'iris.secured-shield.com'\n", "imap_username = '<EMAIL>'\n", "imap_password = '<fill in>'\n", "\n", "\n", "# pull emails since last sync with some buffer\n", "start_date = datetime.now() - <PERSON><PERSON><PERSON>(days=7) \n", "date_string = start_date.strftime(\"%d-%b-%Y\") \n", "search_criteria = f'(SINCE \"{date_string}\")'\n", "\n", "# ignore emails that have already been processed\n", "def get_idempotency_key(email_message) -> str:\n", "    message_id = email_message['Message-ID']\n", "    if message_id:\n", "        return message_id\n", "    \n", "    buffer = BytesIO()\n", "    email_message.as_bytes(policy=email_message.policy)\n", "    email_bytes = buffer.getvalue()\n", "    \n", "    return hashlib.sha256(email_bytes).hexdigest()\n", "\n", "\n", "def print_message(email_message):\n", "    idempotency_key = get_idempotency_key(email_message)\n", "    print(f\"Idempotency Key: {idempotency_key}\")\n", "    \n", "    # Print email headers\n", "    print(\"From:\", email_message['From'])\n", "    print(\"To:\", email_message['To'])\n", "    print(\"Subject:\", decode_header(email_message['Subject'])[0][0])\n", "    print(\"Date:\", email_message['Date'])\n", "    print(\"\\nContent:\")\n", "    \n", "    # Print email body and handle attachments\n", "    if email_message.is_multipart():\n", "        for part in email_message.walk():\n", "            content_type = part.get_content_type()\n", "            content_disposition = str(part.get(\"Content-Disposition\"))\n", "\n", "            if content_type == \"text/html\" and \"attachment\" not in content_disposition:\n", "                body = part.get_payload(decode=True).decode()\n", "                print(body)\n", "                print(\"\\n\" + \"=\"*30 + \" END OF HTML CONTENT \" + \"=\"*30 + \"\\n\")\n", "            elif \"attachment\" in content_disposition:\n", "                filename = part.get_filename()\n", "                if filename:\n", "                    print(f\"Attachment found: {filename}\")\n", "                    print(f\"Content-Type: {content_type}\")\n", "                    print(f\"Size: {len(part.get_payload())} bytes\")\n", "                    print(\"\\n\" + \"-\"*30 + \"\\n\")\n", "    else:\n", "        if email_message.get_content_type() == \"text/html\":\n", "            body = email_message.get_payload(decode=True).decode()\n", "            print(body)\n", "            print(\"\\n\" + \"=\"*30 + \" END OF HTML CONTENT \" + \"=\"*30 + \"\\n\")\n", "        else:\n", "            print(\"No HTML content found in this email.\")\n", "    \n", "    print(\"\\n\" + \"=\"*50 + \"\\n\")\n", "\n", "def fetch_and_print_messages(imap, folder, search_criteria):\n", "    imap.select(folder, readonly=True)\n", "    print(\"\\n\" + \"=\"*20 + f\"FOLDER: {folder} \" + \"=\"*20 + \"\\n\")\n", "    _, data = imap.search(None, search_criteria)\n", "    for num in data[0].split():\n", "        _, data = imap.fetch(num, '(RFC822)')\n", "        email_message = email.message_from_bytes(data[0][1])\n", "        print_message(email_message)\n", "\n", "\n", "imap = imaplib.IMAP4_SSL(imap_host)\n", "imap.login(imap_username, imap_password)\n", "_, folder_list = imap.list()\n", "for folder_info in folder_list:\n", "    folder = folder_info.decode().split('\"/\"')[-1].strip('\"').strip()\n", "    fetch_and_print_messages(imap, folder, search_criteria)\n", "\n", "\n", "imap.close()\n", "from uuid import UUID\n", "from salestech_be.core.email.service.smtp_sending_service import SmtpSendingService\n", "from salestech_be.db.dao.attachment_repository import AttachmentRepository\n", "from salestech_be.db.dto.email_dto import EmailDto, MessageDto\n", "from salestech_be.db.models.message import Message\n", "from salestech_be.db.models.thread import Thread\n", "from salestech_be.temporal.database import get_or_init_db_engine\n", "from salestech_be.util.validation import not_none\n", "\n", "engine = await get_or_init_db_engine()\n", "repository = AttachmentRepository(engine=engine)\n", "\n", "smtp_sending_service = SmtpSendingService(\n", "    db_engine=engine,\n", ")\n", "\n", "message_id = UUID(\"789b02fd-dc99-472a-b970-1f6a3dd2bcf6\")\n", "message = not_none(await repository.find_by_primary_key(Message, id=message_id))\n", "thread_id = message.thread_id\n", "organization_id = message.organization_id\n", "thread = not_none(await repository.find_by_primary_key(Thread, id=thread_id))\n", "attachments = await repository.find_by_ids(\n", "    ids=message.attachment_ids,\n", "    organization_id=organization_id\n", ")\n", "\n", "email_dto = EmailDto(\n", "    thread=thread,\n", "    message_dtos=[\n", "        MessageDto(\n", "            message=message,\n", "            attachments=attachments,\n", "        ),\n", "    ],\n", ")\n", "await smtp_sending_service.send_message(email_dto=email_dto)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import imaplib\n", "from salestech_be.core.email.service.imap_syncing_service import ImapSyncingService\n", "from salestech_be.db.dao.thread_repository import ThreadRepository\n", "from salestech_be.db.models.email_account import EmailAccount\n", "from salestech_be.integrations.s3.s3_bucket_manager import S3BucketManager\n", "from salestech_be.temporal.database import get_or_init_db_engine\n", "from uuid import UUID\n", "\n", "engine = await get_db_engine()\n", "thread_repository = ThreadRepository(engine=engine)\n", "email_account = await thread_repository.find_by_primary_key(EmailAccount, id=UUID(\"0dbf22a0-8cdf-439b-aae6-adbe0c84d359\"))\n", "s3_bucket_manager = S3BucketManager(bucket_name=\"reevo-dev-email-attachments-bucket\")\n", "\n", "with imaplib.IMAP4_SSL(host=\"iris.secured-shield.com\", port=993) as imap_server:\n", "    imap_server.login(user=\"<EMAIL>\", password=\"LHr?tAUMRRLlKgUDsP\")\n", "    print(imap_server.capability())\n", "    await ImapSyncingService(imap_server=imap_server, s3_bucket_manager=s3_bucket_manager, db_engine=engine).sync(\n", "        email_account=email_account,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from uuid import UUID\n", "from salestech_be.core.email.service.email_sending_service import EmailSendingService\n", "from salestech_be.core.email.type.email import EmailHydratedParticipant\n", "from salestech_be.db.dao.thread_repository import ThreadRepository\n", "from salestech_be.db.models.attachment import Attachment\n", "from salestech_be.db.models.email_account import EmailAccount, EmailAccountType\n", "from salestech_be.db.models.message import Message\n", "from salestech_be.db.models.thread import Thread\n", "from salestech_be.db.dto.email_dto import EmailDto, MessageDto\n", "from salestech_be.util.time import zoned_utc_now\n", "from salestech_be.task.instantiate.database import get_db_engine\n", "from salestech_be.util.validation import not_none\n", "from salestech_be.core.email.service.smtp_sending_service import SmtpSendingService\n", "\n", "\n", "host=\"iris.secured-shield.com\"\n", "user=\"<EMAIL>\"\n", "password=\"LHr?tAUMRRLlKgUDsP\"\n", "\n", "email_account = EmailAccount(\n", "    id=UUID(\"0dbf22a0-8cdf-439b-aae6-adbe0c84d359\"),\n", "    owner_user_id=UUID(\"0dbf22a0-8cdf-439b-aae6-adbe0c84d359\"),\n", "    email=\"<EMAIL>\",\n", "    type=EmailAccountType.OUTBOUND,\n", "    active=True,\n", "    is_default=True,\n", "    seconds_delay_between_emails=1,\n", "    organization_id=UUID(\"0dbf22a0-8cdf-439b-aae6-adbe0c84d359\"),\n", "    created_at=zoned_utc_now(),\n", "    created_by_user_id=UUID(\"0dbf22a0-8cdf-439b-aae6-adbe0c84d359\"),\n", "    smtp_host=host,\n", "    smtp_port=587,\n", "    smtp_username=user,\n", "    smtp_password=password,\n", "    imap_host=host,\n", "    imap_port=993,\n", "    imap_username=user,\n", "    imap_password=password,\n", ")\n", "\n", "engine = await get_db_engine()\n", "repository = ThreadRepository(engine=engine)\n", "db_message = not_none(await repository.find_by_primary_key(Message, id=UUID(\"516dc469-69d0-4ae2-a2a1-0437af52adbe\")))\n", "message = db_message.model_copy(\n", "    update={\n", "        \"send_to\": [EmailHydratedParticipant(email=\"jim<PERSON>@reevo.ai\", name=\"<PERSON>\")],\n", "        \"send_from\": [EmailHydratedParticipant(email=\"<EMAIL>\")],\n", "    }\n", ")\n", "thread = not_none(await repository.find_by_primary_key(Thread, id=message.thread_id))\n", "attachments = []\n", "for attachment_id in message.attachment_ids:\n", "    attachments.append(not_none(await repository.find_by_primary_key(Attachment, id=attachment_id)))\n", "\n", "email_dto = EmailDto(\n", "    thread=thread,\n", "    message_dtos=[MessageDto(message=message, attachments=attachments)])\n", "smtp_sending_service = SmtpSendingService(db_engine=engine)\n", "\n", "email_sending_service = EmailSendingService(db_engine=engine)\n", "\n", "await email_sending_service.send_message_through_smtp(email_dto=email_dto, decrypted_email_account=email_account)\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}