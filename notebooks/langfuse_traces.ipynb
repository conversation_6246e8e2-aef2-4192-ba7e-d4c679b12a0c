{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from salestech_be.core.meeting.meeting_service import meeting_service_factory_general\n", "from salestech_be.core.transcript.llm import openai_4o_mini\n", "from salestech_be.core.transcript.transcript_extraction_service_v2 import transcript_extraction_factory_v2\n", "from salestech_be.db.dbengine.core import DatabaseEngine\n", "from salestech_be.settings import get_settings\n", "from fastapi import BackgroundTasks\n", "from uuid import UUID\n", "\n", "meeting_id = UUID(\"5ebdc711-9ae5-4994-893e-d26cef314b37\")\n", "organization_id = UUID(\"1981d325-0cde-4a7c-93de-917c41077036\")\n", "\n", "settings = get_settings()\n", "engine = DatabaseEngine(str(settings.db_url))\n", "meeting_service = meeting_service_factory_general(\n", "    db_engine=engine, background_tasks=BackgroundTasks()\n", ")\n", "_, transcript_container, meeting = await meeting_service.get_transcript_by_meeting_id(\n", "    meeting_id=meeting_id, organization_id=organization_id\n", ")\n", "transcript_extraction_service_v2 = transcript_extraction_factory_v2(\n", "    engine=engine, llm=openai_4o_mini()\n", ")\n", "items = await transcript_extraction_service_v2.extract_action_items(\n", "    organization_id=organization_id,\n", "    compacted_transcript=transcript_container.transcript.compact(),\n", "    meeting=meeting,\n", ")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}