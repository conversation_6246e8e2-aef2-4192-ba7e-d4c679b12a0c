import shutil
import subprocess
import typing
from collections.abc import Async<PERSON>enerator, <PERSON>wai<PERSON>, Callable, Coroutine
from random import randint
from unittest.mock import AsyncMock, Mock
from uuid import UUID, uuid4

import pytest
from _pytest.fixtures import fixture
from asgi_lifespan import LifespanManager
from faker import Faker
from fastapi import BackgroundTasks, Fast<PERSON><PERSON>
from httpx import ASGITransport, AsyncClient
from temporalio.client import Client
from temporalio.contrib.pydantic import pydantic_data_converter
from temporalio.testing import WorkflowEnvironment
from wiremock.constants import Config
from wiremock.testing.testcontainer import WireMockContainer, wiremock_container

from salestech_be.core.account.service.account_data_integrity_service import (
    AccountDataIntegrityService,
    get_account_data_integrity_service,
)
from salestech_be.core.account.service.account_query_service import (
    AccountQueryService,
    get_account_query_service,
)
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.activity.service.activity_query_service import (
    get_activity_query_service,
)
from salestech_be.core.activity.service.activity_service import ActivityService
from salestech_be.core.approval_request.service.approval_service import (
    ApprovalService,
    get_approval_service_with_engine,
)
from salestech_be.core.auth.service import (
    UserAuthService,
    get_user_auth_service_with_engine,
)
from salestech_be.core.calendar.account.calendar_account_service import (
    CalendarAccountService,
)
from salestech_be.core.calendar.user_calendar_data_integrity_service import (
    UserCalendarDataIntegrityService,
    get_user_calendar_data_integrity_service,
)
from salestech_be.core.calendar.user_calendar_schedule_service import (
    UserCalendarScheduleService,
    get_user_calendar_schedule_service_by_db_engine,
)
from salestech_be.core.calendar.user_calendar_sync_service import (
    UserCalendarSyncService,
    get_user_calendar_sync_service_by_db_engine,
)
from salestech_be.core.calendar.user_calendar_webhook_service import (
    UserCalendarWebhookService,
    get_user_calendar_webhook_service_by_db_engine,
)
from salestech_be.core.citation.service.citation_query_service import (
    CitationQueryService,
    get_citation_query_service,
)
from salestech_be.core.citation.service.citation_service import (
    CitationService,
    get_citation_service,
)
from salestech_be.core.comment.service.comment_query_service import (
    CommentQueryService,
    get_comment_query_service_from_engine,
)
from salestech_be.core.contact.service.contact_data_integrity_service import (
    ContactDataIntegrityService,
    get_contact_data_integrity_service,
)
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.contact.service.contact_resolve_service import (
    ContactResolveService,
    get_contact_resolve_service,
)
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.conversation.service.conversation_query_service import (
    ConversationQueryService,
    get_conversation_query_service_from_engine,
)
from salestech_be.core.crm_integrity.data_operation_tracker.atomic_operation_tracker_service import (
    AtomicOperationTrackerService,
)
from salestech_be.core.crm_integrity.data_operation_tracker.integrity_job_service import (
    IntegrityJobService,
)
from salestech_be.core.crm_integrity.integrity_operation_preview.merge_accounts_preview import (
    MergeAccountsPreviewService,
)
from salestech_be.core.crm_sync.hubspot.hubspot_service import (
    HubSpotService,
    get_hubspot_service_by_db_engine,
)
from salestech_be.core.custom_object.service.association_service import (
    AssociationService,
    get_association_service,
)
from salestech_be.core.custom_object.service.custom_object_query_service import (
    CustomObjectQueryService,
    get_custom_object_query_service,
)
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
)
from salestech_be.core.data.service.query_service import (
    DomainObjectQueryService,
    get_domain_object_query_service,
)
from salestech_be.core.data.service.resolvers.activity_resolver import (
    get_activity_resolver,
)
from salestech_be.core.domain_crm_association.domain_crm_association_service import (
    DomainCRMAssociationService,
    get_domain_crm_association_service,
)
from salestech_be.core.domain_object_list.service.domain_object_list_query_service import (
    DomainObjectListQueryService,
    get_domain_object_list_query_service_by_db_engine,
)
from salestech_be.core.domain_object_list.service.domain_object_list_service import (
    DomainObjectListService,
    get_domain_object_list_service,
)
from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.account.email_account_query_service import (
    EmailAccountQueryService,
    get_email_account_query_service_by_db_engine,
)
from salestech_be.core.email.account.service_v2 import (
    EmailAccountServiceV2,
    get_email_account_service_v2_by_db_engine,
)
from salestech_be.core.email.activity.email_activity_service import EmailActivityService
from salestech_be.core.email.attachment.email_attachment_query_service import (
    EmailAttachmentQueryService,
    get_email_attachment_query_service_by_db_engine,
)
from salestech_be.core.email.attachment.email_attachment_service import (
    EmailAttachmentService,
)
from salestech_be.core.email.copywriter.email_copy_writer_service import (
    EmailCopyWriterService,
    get_email_copy_writer_service_from_engine,
)
from salestech_be.core.email.deliver_window.email_deliver_window_service import (
    EmailDeliverWindowService,
)
from salestech_be.core.email.global_email.global_thread_query_service import (
    GlobalThreadQueryService,
    get_global_thread_query_service,
)
from salestech_be.core.email.global_email.global_thread_service import (
    GlobalThreadService,
    get_global_thread_service_by_db_engine,
)
from salestech_be.core.email.insight.email_insight_service import EmailInsightService
from salestech_be.core.email.outbound_domain.outbound_domain_query_service import (
    OutboundDomainQueryService,
    get_outbound_domain_query_service_by_db_engine,
)
from salestech_be.core.email.participant.email_participant_service import (
    EmailParticipantService,
)
from salestech_be.core.email.pool.email_account_pool_query_service import (
    EmailAccountPoolQueryService,
    get_email_account_pool_query_service_from_engine,
)
from salestech_be.core.email.pool.service import (
    EmailAccountPoolService,
    get_email_account_pool_service_general,
)
from salestech_be.core.email.render.email_rendering_service import EmailRenderingService
from salestech_be.core.email.service.email_data_integrity_service import (
    EmailDataIntegrityService,
    get_email_data_integrity_service,
)
from salestech_be.core.email.service.email_sending_service import EmailSendingService
from salestech_be.core.email.service.imap_syncing_service import (
    ImapSyncingService,
    get_imap_syncing_service_general,
)
from salestech_be.core.email.service.message_service import MessageService
from salestech_be.core.email.service.message_service_ext import MessageServiceExt
from salestech_be.core.email.template.email_template_query_service import (
    EmailTemplateQueryService,
    get_email_template_query_service_from_engine,
)
from salestech_be.core.email.template.email_template_service_ext import (
    EmailTemplateServiceExt,
)
from salestech_be.core.email.thread.thread_service_ext import ThreadServiceExt
from salestech_be.core.email.unsubscription_group.email_subscription_service import (
    EmailSubscriptionService,
)
from salestech_be.core.email.unsubscription_group.unsubscription_group_ext import (
    UnsuscriptionGroupServiceExt,
)
from salestech_be.core.extraction_prompt.service.config_extraction_service import (
    ExtractionConfigService,
)
from salestech_be.core.extraction_prompt.service.email_extraction_service import (
    EmailExtractionService,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.files.service.file_service import (
    FileService,
)
from salestech_be.core.goal.service.goal_query_service import (
    GoalQueryService,
    goal_query_service_by_db_engine,
)
from salestech_be.core.imports.repository.import_job_repository import (
    ImportJobRepository,
)
from salestech_be.core.imports.service.crm_sync_push_service import CrmSyncPushService
from salestech_be.core.imports.service.crm_sync_service import CrmSyncService
from salestech_be.core.imports.service.import_csv_job_review_service import (
    ImportCsvJobReviewService,
)
from salestech_be.core.imports.service.import_job_service import ImportJobService
from salestech_be.core.job.service.job_service import JobService
from salestech_be.core.logical_propagation.service.evaluation import (
    LogicalPropagationEvaluationService,
)
from salestech_be.core.logical_propagation.service.execution import (
    LogicalPropagationExecutionService,
)
from salestech_be.core.logical_propagation.service.trigger import (
    LogicalPropagationTriggerService,
)
from salestech_be.core.meeting.meeting_ai_rec_service import (
    MeetingAIRecService,
    get_meeting_ai_rec_service,
)
from salestech_be.core.meeting.meeting_bot_service import (
    MeetingBotService,
    meeting_bot_service_general,
)
from salestech_be.core.meeting.meeting_insight_service import (
    MeetingInsightService,
    meeting_insight_service_factory_general,
)
from salestech_be.core.meeting.meeting_service import (
    MeetingService,
    meeting_service_factory_general,
)
from salestech_be.core.meeting.service.meeting_query_service import (
    MeetingQueryService,
    get_meeting_query_service,
)
from salestech_be.core.metadata.repository.select_list_repository import (
    SelectListRepository,
)
from salestech_be.core.metadata.repository.stage_criteria_repository import (
    StageCriteriaRepository,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
    get_select_list_service,
)
from salestech_be.core.metadata.service.metadata_service import MetadataService
from salestech_be.core.metadata.service.pipeline_stage_service import (
    PipelineStageSelectListService,
)
from salestech_be.core.metadata.service.stage_criteria_service import (
    StageCriteriaService,
)
from salestech_be.core.note.service.note_data_integrity_service import (
    NoteDataIntegrityService,
    get_note_data_integrity_service,
)
from salestech_be.core.note.service.note_service import (
    NoteService,
    get_note_service_general,
)
from salestech_be.core.notification.service.notification_service import (
    NotificationService,
    get_notification_service_by_db_engine,
)
from salestech_be.core.organization.service.organization_service import (
    OrganizationService,
)
from salestech_be.core.organization.service.organization_service_v2 import (
    OrganizationServiceV2,
    get_organization_service_v2_from_engine,
)
from salestech_be.core.pipeline.service.pipeline_intel_service import (
    PipelineIntelService,
    get_pipeline_intel_service,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_service import (
    PipelineQualificationPropertyService,
    get_pipeline_qualification_property_service,
)
from salestech_be.core.pipeline.service.pipeline_query_service import (
    PipelineQueryService,
    get_pipeline_query_service,
)
from salestech_be.core.pipeline.service.pipeline_service import (
    PipelineService,
    get_pipeline_service,
)
from salestech_be.core.prospecting.prospecting_credit_query_service import (
    ProspectingCreditUsageQueryService,
    get_prospecting_credit_usage_query_service_by_db,
)
from salestech_be.core.prospecting.prospecting_enrichment_service import (
    ProspectingEnrichmentService,
    get_prospecting_enrichment_service_by_db_engine,
)
from salestech_be.core.prospecting.prospecting_filter_field_query_service import (
    ProspectingFilterFieldQueryService,
    get_prospecting_filter_field_query_service_by_db,
)
from salestech_be.core.prospecting.prospecting_quota_service import (
    ProspectingQuotaService,
    get_prospecting_quota_service_by_db_engine,
)
from salestech_be.core.prospecting.prospecting_run_query_service import (
    ProspectingRunQueryService,
    get_prospecting_run_query_service_by_db,
)
from salestech_be.core.prospecting.prospecting_run_result_query_service import (
    ProspectingRunResultQueryService,
    get_prospecting_run_result_query_service_by_db,
)
from salestech_be.core.prospecting.prospecting_saved_search_query_query_service import (
    ProspectingSavedSearchQueryQueryService,
    get_prospecting_saved_search_query_query_service_by_db,
)
from salestech_be.core.quota.service.quota_policy_service import QuotaPolicyService
from salestech_be.core.quota.service.quota_service import QuotaService
from salestech_be.core.research_agent.research_agent_client import (
    get_research_agent_client,
)
from salestech_be.core.research_agent.research_agent_service import ResearchAgentService
from salestech_be.core.schedule.event_schedule_query_service import (
    EventScheduleQueryService,
    get_event_schedule_query_service_from_engine,
)
from salestech_be.core.sequence.service.sequence_enrollment_query_service import (
    SequenceEnrollmentQueryService,
    get_sequence_enrollment_query_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    SequenceEnrollmentService,
    get_sequence_enrollment_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_execution_query_service import (
    SequenceExecutionQueryService,
    get_sequence_execution_query_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_execution_service import (
    SequenceExecutionService,
    get_sequence_execution_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_query_service import (
    SequenceQueryService,
    get_sequence_query_service_by_db,
)
from salestech_be.core.sequence.service.sequence_service import (
    SequenceService,
    get_sequence_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_step_execution_query_service import (
    SequenceStepExecutionQueryService,
    get_sequence_step_execution_query_service_by_db,
)
from salestech_be.core.sequence.service.sequence_step_query_service import (
    SequenceStepQueryService,
    get_sequence_step_query_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_step_variant_query_service import (
    SequenceStepVariantQueryService,
    get_sequence_step_variant_query_service_by_db_engine,
)
from salestech_be.core.task.service.task_query_service import (
    TaskQueryService,
    get_task_query_service_from_engine,
)
from salestech_be.core.task.service.task_v2_service import (
    TaskV2Service,
    get_task_v2_service_general,
)
from salestech_be.core.transcript.llm import anthropic_sonnet
from salestech_be.core.user.service.permission_service import (
    PermissionService,
    get_permission_service_by_db_engine,
)
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.core.user.signature.service import (
    SignatureService,
    get_signature_service,
)
from salestech_be.core.user.signature.signature_query_service import (
    SignatureQueryService,
    get_signature_query_service_by_db_engine,
)
from salestech_be.core.user.utils import default_roles
from salestech_be.core.user_feedback.service import UserFeedbackService
from salestech_be.core.view_management.service.view_management_service import (
    ViewManagementService,
)
from salestech_be.core.voice.v2.voice_call_service import (
    VoiceCallService,
    voice_call_service_from_engine,
)
from salestech_be.core.workflow.activities.if_else_condition_node_activity import (
    IfElseNodeActivity,
)
from salestech_be.core.workflow.activities.switch_condition_node_activity import (
    SwitchNodeActivity,
)
from salestech_be.core.workflow.service.workflow_execution_service import (
    WorkflowExecutionService,
)
from salestech_be.core.workflow.service.workflow_temporal_service import (
    WorkflowTemporalService,
)
from salestech_be.core.workflow.service.workflow_trigger_service import (
    WorkflowTriggerService,
)
from salestech_be.core.workflow.service.workflow_validation_service import (
    WorkflowValidationService,
    get_workflow_validation_service,
)
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.activity_repository import ActivityRepository
from salestech_be.db.dao.address_repository import AddressRepository
from salestech_be.db.dao.attachment_repository import AttachmentRepository
from salestech_be.db.dao.audience_repository import (
    AudienceListMembershipRepository,
    AudienceListRepository,
)
from salestech_be.db.dao.auth0_user_repository import Auth0UserRepository
from salestech_be.db.dao.calendar_account_repository import CalendarAccountRepository
from salestech_be.db.dao.citation_repository import CitationRepository
from salestech_be.db.dao.comment_repository import CommentRepository
from salestech_be.db.dao.company_repository import CompanyRepository
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.contact_sourcing_queue_repository import (
    ContactSourcingQueueRepository,
)
from salestech_be.db.dao.conversation_repository import ConversationRepository
from salestech_be.db.dao.crm_integrity_repository import CRMIntegrityJobRepository
from salestech_be.db.dao.crm_sync_repository import CRMSyncRepository
from salestech_be.db.dao.custom_object_repository import CustomObjectRepository
from salestech_be.db.dao.domain_crm_association_repository import (
    DomainCRMAssociationRepository,
)
from salestech_be.db.dao.domain_object_list_repository import DomainObjectListRepository
from salestech_be.db.dao.email_account import (
    EmailAccountHealthRepository,
    EmailAccountPoolRepository,
    EmailAccountRepository,
    EmailAccountSlotAllocationRepository,
)
from salestech_be.db.dao.email_account_warm_up_campaign_repository import (
    EmailAccountWarmUpCampaignRepository,
)
from salestech_be.db.dao.email_event_repository import EmailEventRepository
from salestech_be.db.dao.email_template_repository import EmailTemplateRepository
from salestech_be.db.dao.event_schedule_repository import EventScheduleRepository
from salestech_be.db.dao.event_tracking_repository import EventTrackingRepository
from salestech_be.db.dao.extraction_config_repository import ExtractionConfigRepository
from salestech_be.db.dao.form_repository import FormRepository
from salestech_be.db.dao.form_submission_repository import FormSubmissionRepository
from salestech_be.db.dao.import_csv_job_review_repository import (
    ImportCsvJobReviewRepository,
)
from salestech_be.db.dao.import_repository import ImportRepository
from salestech_be.db.dao.insight_repository import InsightRepository
from salestech_be.db.dao.intel_repository import IntelRepository
from salestech_be.db.dao.job_repository import JobRepository
from salestech_be.db.dao.job_role_user_association_repository import (
    JobRoleUserAssociationRepository,
)
from salestech_be.db.dao.meeting_repository import MeetingRepository
from salestech_be.db.dao.message_metadata_repository import MessageMetadataRepository
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dao.metadata_repository import MetadataRepository
from salestech_be.db.dao.note_repository import NoteReferenceRepository, NoteRepository
from salestech_be.db.dao.notification_repository import NotificationRepository
from salestech_be.db.dao.oauth_repository import OauthRepository
from salestech_be.db.dao.organization_info_repository import OrganizationInfoRepository
from salestech_be.db.dao.organization_repository import OrganizationRepository
from salestech_be.db.dao.outbound_repository import (
    DomainHealthRepository,
    OutboundDomainRepository,
    OutboundWorkspaceRepository,
)
from salestech_be.db.dao.pdl_cache_repository import PDLCacheRepository
from salestech_be.db.dao.pdl_company_repository import PDLCompanyRepository
from salestech_be.db.dao.pdl_person_repository import PDLPersonRepository
from salestech_be.db.dao.permission_set_group_permission_set_association_repository import (
    PermissionSetGroupPermissionSetAssociationRepository,
)
from salestech_be.db.dao.permission_set_group_repository import (
    PermissionSetGroupRepository,
)
from salestech_be.db.dao.permission_set_group_user_association_repository import (
    PermissionSetGroupUserAssociationRepository,
)
from salestech_be.db.dao.permission_set_repository import PermissionSetRepository
from salestech_be.db.dao.permission_set_user_association_repository import (
    PermissionSetUserAssociationRepository,
)
from salestech_be.db.dao.person_repository import PersonRepository
from salestech_be.db.dao.pipeline_repository import PipelineRepository
from salestech_be.db.dao.pipeline_stage_repository import PipelineStageRepository
from salestech_be.db.dao.prompt_repository import PromptRepository
from salestech_be.db.dao.propagation_rule_repository import PropagationRuleRepository
from salestech_be.db.dao.prospecting_run_repository import ProspectingRunRepository
from salestech_be.db.dao.prospecting_tag_repository import ProspectingTagRepository
from salestech_be.db.dao.quota_repository import (
    QuotaPolicyRepository,
    QuotaUsageRepository,
)
from salestech_be.db.dao.saved_domain_object_filter_repository import (
    SavedDomainObjectFilterRepository,
)
from salestech_be.db.dao.search_query_repository import SearchQueryRepository
from salestech_be.db.dao.sequence_enrollment_repo import SequenceEnrollmentRepository
from salestech_be.db.dao.sequence_repository import (
    SequenceRepository,
    SequenceStepRepository,
)
from salestech_be.db.dao.signature_repository import SignatureRepository
from salestech_be.db.dao.task_repository import TaskRepository, TaskTemplateRepository
from salestech_be.db.dao.template_repository import TemplateRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dao.tracker_repository import TrackerRepository
from salestech_be.db.dao.unsubscription_group_repository import (
    UnsubscriptionGroupRepository,
)
from salestech_be.db.dao.user_calendar_repository import UserCalendarRepository
from salestech_be.db.dao.user_feedback_repository import UserFeedbackRepository
from salestech_be.db.dao.user_goal_repository import UserGoalRepository
from salestech_be.db.dao.user_integration_connector_repository import (
    UserIntegrationConnectorRepository,
)
from salestech_be.db.dao.user_integration_repository import UserIntegrationRepository
from salestech_be.db.dao.user_invite_repository import UserInviteRepository
from salestech_be.db.dao.user_notification_repository import UserNotificationRepository
from salestech_be.db.dao.user_platform_credential_repository import (
    UserPlatformCredentialRepository,
)
from salestech_be.db.dao.user_preference_repository import UserPreferenceRepository
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dao.view_management_repository import ViewManagementRepository
from salestech_be.db.dao.voice_call_repository import VoiceCallRepository
from salestech_be.db.dao.workflow_repository import (
    WorkflowBlockRepository,
    WorkflowEdgeRepository,
    WorkflowNodeRepository,
    WorkflowRepository,
    WorkflowRunNodeRepository,
    WorkflowRunRepository,
    WorkflowSnapshotRepository,
    WorkflowTriggerEventRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.sequence import (
    Sequence,
    SequenceAction,
    SequenceExecutionStatus,
    SequenceStep,
)
from salestech_be.db.models.user import User, UserRole
from salestech_be.falkordb.falkordb_client import FalkorDBClient, get_falkordb_client
from salestech_be.falkordb.falkordb_factory import (
    FalkorDBConnectionManager,
    get_falkordb_connection_manager,
)
from salestech_be.falkordb.indexing_lib import (
    FalkorDBIndexingLib,
    get_falkordb_indexing_lib,
)
from salestech_be.integrations.brightdata.client import BrightdataClient
from salestech_be.integrations.crustdata.client import CrustdataClient
from salestech_be.integrations.linkedin.linkedin_client import LinkedInClient
from salestech_be.integrations.mailivery.async_mailivery_client import (
    AsyncMailiveryClient,
)
from salestech_be.integrations.pdl.pdl_client import PdlClient
from salestech_be.integrations.s3.s3_bucket_manager import (
    S3BucketManager,
    get_s3_bucket_manager_by_bucket_name,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.settings import settings
from salestech_be.web.api.audience.list.service import AudienceListService
from salestech_be.web.api.audience.list_membership.service import (
    AudienceListMembershipService,
)
from salestech_be.web.api.calendar.service import (
    UserCalendarService,
    get_user_calendar_service_by_db_engine,
)
from salestech_be.web.api.email.common.email_sync_schedule_service import (
    EmailSyncScheduleService,
    get_email_sync_schedule_service_by_db_engine,
)
from salestech_be.web.api.email.common.email_sync_service import (
    EmailSyncService,
    get_email_sync_service_by_db_engine,
)
from salestech_be.web.api.email.common.email_webhook_service import (
    EmailWebhookService,
    get_email_webhook_service_by_db_engine,
)
from salestech_be.web.api.propagation_rule.validation import (
    LogicalPropagationValidationService,
)
from salestech_be.web.api.prospecting.common.prospecting_common_service import (
    ProspectingCommonService,
    get_prospecting_common_service,
)
from salestech_be.web.api.prospecting.people.service import PeopleService
from salestech_be.web.api.prospecting.run.service import (
    ProspectingRunService,
    get_prospecting_run_service_from_engine,
)
from salestech_be.web.api.schedule.service import (
    EventScheduleService,
    get_event_schedule_service_by_db_engine,
)
from salestech_be.web.application import get_app
from tests.integration.sequence.repository_for_test import (
    SequenceExecutionRepositoryForTest,
)
from tests.integration.sequence_fixtures import (
    SequenceExecutionIds,
    SequenceExecutionReferences,
)
from tests.integration.test_utils import TEST_SERVER_BASE_URL
from tests.test_util import (
    RandomOrganization,
    random_unique_email,
)
from tests.util.factories import (
    ContactFactory,
    OrganizationFactory,
    SequenceActionFactory,
    SequenceExecutionFactory,
    SequenceFactory,
    SequenceScheduleFactory,
    SequenceStepFactory,
    SequenceTargetFactory,
    UserFactory,
)

Message = dict[str, typing.Any]
Receive = Callable[[], Awaitable[Message]]
Send = Callable[[dict[str, typing.Any]], Coroutine[None, None, None]]


################
#
# App Fixtures
#
#################


@fixture(scope="session")
async def _engine() -> AsyncGenerator[DatabaseEngine, None]:
    engine = DatabaseEngine(url=str(settings.db_url), pool_size=100, max_overflow=500)
    try:
        yield engine
    finally:
        await engine.close()


@fixture(scope="session")
async def _materialize_engine() -> AsyncGenerator[DatabaseEngine, None]:
    materialize_engine = DatabaseEngine(
        url=str(settings.materialize_url), pool_size=100, max_overflow=500
    )
    try:
        yield materialize_engine
    finally:
        await materialize_engine.close()


@fixture(scope="session")
async def _temporal_client() -> AsyncGenerator[Client]:
    yield await get_temporal_client()


@fixture(scope="session")
async def fastapi_app() -> FastAPI:
    return get_app()


@fixture(scope="session")
async def _session_api_client(
    fastapi_app: FastAPI,
) -> AsyncGenerator[AsyncClient, None]:
    """
    Fixture for creating API client for the entire test session.
    DO NOT directly use it in tests, use `api_client` fixture instead.
    """
    async with (
        LifespanManager(
            fastapi_app, startup_timeout=120, shutdown_timeout=60
        ) as manager,
        AsyncClient(
            transport=ASGITransport(app=manager.app),
            base_url=TEST_SERVER_BASE_URL,
        ) as client,
    ):
        yield client


class ApiClientFactory:
    def __init__(self, fastapi_app: FastAPI):
        self.fastapi_app = fastapi_app

    async def get(self) -> AsyncClient:
        manager = LifespanManager(
            self.fastapi_app, startup_timeout=120, shutdown_timeout=60
        )
        return AsyncClient(
            transport=ASGITransport(app=manager.app),
            base_url=TEST_SERVER_BASE_URL,
        )


@fixture(scope="session")
async def _session_api_client_factory(
    fastapi_app: FastAPI,
) -> ApiClientFactory:
    """
    Fixture for creating API client for the entire test session.
    DO NOT directly use it in tests, use `api_client` fixture instead.

    TODO: (colin) I do not know why the async context manager auto-closes the client when wrapped in a factory constructor.
          See `_session_api_client` fixture for what this code was based on. Since this is a session-scoped fixture,
          it should not be closing the client, but it is. I've unwrapped the context manager and left it to the Python interpreter's GC to close the client.
    """
    return ApiClientFactory(fastapi_app)


@fixture
async def api_client(_session_api_client: AsyncClient) -> AsyncClient:
    """
    Fixture that reuse the same api_client `_session_api_client` across the entire test session.
    So we don't have to instantiate new app context for every individual test case.
    This fixture also cleanup states between each test case.
    """
    _session_api_client.cookies.clear()
    return _session_api_client


@fixture(scope="session")
def wiremock() -> WireMockContainer:
    with wiremock_container(secure=False) as wm:
        Config.base_url = wm.get_url("__admin")
        yield wm


# Ensure Faker returns unique value
@fixture(autouse=True)
def faker_seed() -> None:
    Faker.seed(randint(1, *********))  # noqa: S311


################
#
# Repo Fixtures
#
#################


@fixture(scope="session")
def account_repository(
    _engine: DatabaseEngine,
) -> AccountRepository:
    return AccountRepository(engine=_engine)


@fixture(scope="session")
def account_repo(_engine: DatabaseEngine) -> AccountRepository:
    return AccountRepository(engine=_engine)


@fixture(scope="session")
def activity_repository(
    _engine: DatabaseEngine,
) -> ActivityRepository:
    return ActivityRepository(engine=_engine)


@fixture(scope="session")
def address_repo(_engine: DatabaseEngine) -> AddressRepository:
    return AddressRepository(engine=_engine)


@fixture(scope="session")
def pdl_person_repository(
    _engine: DatabaseEngine,
) -> PDLPersonRepository:
    return PDLPersonRepository(engine=_engine)


@fixture(scope="session")
def attachment_repository(
    _engine: DatabaseEngine,
) -> AttachmentRepository:
    return AttachmentRepository(engine=_engine)


@fixture(scope="session")
def audience_list_membership_repo(
    _engine: DatabaseEngine,
) -> AudienceListMembershipRepository:
    return AudienceListMembershipRepository(engine=_engine)


@fixture(scope="session")
def audience_list_repo(_engine: DatabaseEngine) -> AudienceListRepository:
    return AudienceListRepository(engine=_engine)


@fixture(scope="session")
def auth0_user_repository(
    _engine: DatabaseEngine,
) -> Auth0UserRepository:
    return Auth0UserRepository(engine=_engine)


@fixture(scope="session")
def calendar_account_repository(
    _engine: DatabaseEngine,
) -> CalendarAccountRepository:
    return CalendarAccountRepository(
        engine=_engine,
    )


@fixture(scope="session")
def conversation_repository(
    _engine: DatabaseEngine,
) -> ConversationRepository:
    return ConversationRepository(
        engine=_engine,
    )


@fixture(scope="session")
def event_tracking_repo(
    _engine: DatabaseEngine,
) -> EventTrackingRepository:
    return EventTrackingRepository(
        engine=_engine,
    )


@fixture(scope="session")
def event_schedule_repo(
    _engine: DatabaseEngine,
) -> EventScheduleRepository:
    return EventScheduleRepository(
        engine=_engine,
    )


@fixture(scope="session")
def comment_repository(_engine: DatabaseEngine) -> CommentRepository:
    return CommentRepository(engine=_engine)


@fixture(scope="session")
def company_repository(
    _engine: DatabaseEngine,
) -> CompanyRepository:
    return CompanyRepository(engine=_engine)


@fixture(scope="session")
def contact_repo(
    _engine: DatabaseEngine,
) -> ContactRepository:
    return ContactRepository(engine=_engine)


@fixture(scope="session")
def pipeline_repo(
    _engine: DatabaseEngine,
) -> PipelineRepository:
    return PipelineRepository(engine=_engine)


@fixture(scope="session")
def citation_repository(
    _engine: DatabaseEngine,
) -> CitationRepository:
    return CitationRepository(engine=_engine)


@fixture(scope="session")
def contact_sourcing_queue_repository(
    _engine: DatabaseEngine,
) -> ContactSourcingQueueRepository:
    return ContactSourcingQueueRepository(engine=_engine)


@fixture(scope="session")
def crm_sync_repository(_engine: DatabaseEngine) -> CRMSyncRepository:
    return CRMSyncRepository(engine=_engine)


@fixture(scope="session")
def custom_object_repo(_engine: DatabaseEngine) -> CustomObjectRepository:
    return CustomObjectRepository(engine=_engine)


@fixture(scope="session")
def email_account_repository(
    _engine: DatabaseEngine,
) -> EmailAccountRepository:
    return EmailAccountRepository(engine=_engine)


@fixture(scope="session")
def email_account_warm_up_campaign_repo(
    _engine: DatabaseEngine,
) -> EmailAccountWarmUpCampaignRepository:
    return EmailAccountWarmUpCampaignRepository(engine=_engine)


@fixture(scope="session")
def email_account_health_repo(
    _engine: DatabaseEngine,
) -> EmailAccountHealthRepository:
    return EmailAccountHealthRepository(engine=_engine)


@fixture(scope="session")
def extraction_config_repository(
    _engine: DatabaseEngine,
) -> ExtractionConfigRepository:
    return ExtractionConfigRepository(engine=_engine)


@fixture(scope="session")
def form_repository(
    _engine: DatabaseEngine,
) -> FormRepository:
    return FormRepository(engine=_engine)


@fixture(scope="session")
def form_submission_repository(
    _engine: DatabaseEngine,
) -> FormSubmissionRepository:
    return FormSubmissionRepository(engine=_engine)


@fixture(scope="session")
def import_repository(_engine: DatabaseEngine) -> ImportRepository:
    return ImportRepository(engine=_engine)


@fixture(scope="session")
def intel_repository(
    _engine: DatabaseEngine,
) -> IntelRepository:
    return IntelRepository(engine=_engine)


@fixture(scope="session")
def job_repository(_engine: DatabaseEngine) -> JobRepository:
    return JobRepository(engine=_engine)


@fixture(scope="session")
def list_repository(
    _engine: DatabaseEngine,
) -> AudienceListRepository:
    return AudienceListRepository(engine=_engine)


@fixture(scope="session")
def list_membership_repository(
    _engine: DatabaseEngine,
) -> AudienceListMembershipRepository:
    return AudienceListMembershipRepository(engine=_engine)


@fixture(scope="session")
def message_repository(
    _engine: DatabaseEngine,
) -> MessageRepository:
    return MessageRepository(engine=_engine)


@fixture(scope="session")
def message_metadata_repository(
    _engine: DatabaseEngine,
) -> MessageMetadataRepository:
    return MessageMetadataRepository(engine=_engine)


@fixture(scope="session")
def metadata_repository(
    _engine: DatabaseEngine,
) -> MetadataRepository:
    return MetadataRepository(engine=_engine)


@fixture(scope="session")
def meeting_repository(
    _engine: DatabaseEngine,
) -> MeetingRepository:
    return MeetingRepository(engine=_engine)


@fixture(scope="session")
def note_repository(_engine: DatabaseEngine) -> NoteRepository:
    return NoteRepository(engine=_engine)


@fixture(scope="session")
def note_reference_repository(_engine: DatabaseEngine) -> NoteReferenceRepository:
    return NoteReferenceRepository(engine=_engine)


@fixture(scope="session")
def notification_repository(
    _engine: DatabaseEngine,
) -> NotificationRepository:
    return NotificationRepository(engine=_engine)


@fixture(scope="session")
def oauth_repository(
    _engine: DatabaseEngine,
) -> OauthRepository:
    return OauthRepository(engine=_engine)


@fixture(scope="session")
def organization_repository(
    _engine: DatabaseEngine,
) -> OrganizationRepository:
    return OrganizationRepository(engine=_engine)


@fixture(scope="session")
def organization_info_repository(
    _engine: DatabaseEngine,
) -> OrganizationInfoRepository:
    return OrganizationInfoRepository(engine=_engine)


@pytest.fixture
def domain_object_list_repository(
    _engine: DatabaseEngine,
) -> DomainObjectListRepository:
    return DomainObjectListRepository(engine=_engine)


@pytest.fixture
def email_account_slot_allocation_repository(
    _engine: DatabaseEngine,
) -> EmailAccountSlotAllocationRepository:
    return EmailAccountSlotAllocationRepository(engine=_engine)


@pytest.fixture
def pdl_company_repository(
    _engine: DatabaseEngine,
) -> PDLCompanyRepository:
    return PDLCompanyRepository(engine=_engine)


@fixture(scope="session")
def outbound_domain_repository(_engine: DatabaseEngine) -> OutboundDomainRepository:
    return OutboundDomainRepository(engine=_engine)


@fixture(scope="session")
def quota_policy_repository(_engine: DatabaseEngine) -> QuotaPolicyRepository:
    return QuotaPolicyRepository(engine=_engine)


@fixture(scope="session")
def outbound_workspace_repository(
    _engine: DatabaseEngine,
) -> OutboundWorkspaceRepository:
    return OutboundWorkspaceRepository(engine=_engine)


@fixture(scope="session")
def domain_health_repository(_engine: DatabaseEngine) -> DomainHealthRepository:
    return DomainHealthRepository(engine=_engine)


@fixture(scope="session")
def sequence_repository(
    _engine: DatabaseEngine,
) -> SequenceRepository:
    return SequenceRepository(engine=_engine)


@fixture(scope="session")
def sequence_enrollment_repository(
    _engine: DatabaseEngine,
) -> SequenceEnrollmentRepository:
    return SequenceEnrollmentRepository(engine=_engine)


@fixture(scope="session")
def person_repository(
    _engine: DatabaseEngine,
) -> PersonRepository:
    return PersonRepository(engine=_engine)


@fixture(scope="session")
def pipeline_repository(_engine: DatabaseEngine) -> PipelineRepository:
    return PipelineRepository(engine=_engine)


@fixture(scope="session")
def pipeline_stage_repository(
    _engine: DatabaseEngine,
) -> PipelineStageRepository:
    return PipelineStageRepository(engine=_engine)


@fixture
def prompt_repo(_engine: DatabaseEngine) -> PromptRepository:
    return PromptRepository(engine=_engine)


@fixture(scope="session")
def prospecting_tag_repository(
    _engine: DatabaseEngine,
) -> ProspectingTagRepository:
    return ProspectingTagRepository(engine=_engine)


@fixture(scope="session")
def prospecting_run_repository(
    _engine: DatabaseEngine,
) -> ProspectingRunRepository:
    return ProspectingRunRepository(engine=_engine)


@fixture(scope="session")
def quota_policy_repo(_engine: DatabaseEngine) -> QuotaPolicyRepository:
    return QuotaPolicyRepository(engine=_engine)


@fixture(scope="session")
def quota_usage_repo(_engine: DatabaseEngine) -> QuotaUsageRepository:
    return QuotaUsageRepository(engine=_engine)


@fixture(scope="session")
def saved_domain_object_filter_repository(
    _engine: DatabaseEngine,
) -> SavedDomainObjectFilterRepository:
    return SavedDomainObjectFilterRepository(engine=_engine)


@fixture(scope="session")
def search_query_repository(
    _engine: DatabaseEngine,
) -> SearchQueryRepository:
    return SearchQueryRepository(engine=_engine)


@fixture(scope="session")
def select_list_repository(
    _engine: DatabaseEngine,
) -> SelectListRepository:
    return SelectListRepository(engine=_engine)


@fixture(scope="session")
def seq_exec_repo(
    _engine: DatabaseEngine,
) -> SequenceExecutionRepositoryForTest:
    return SequenceExecutionRepositoryForTest(engine=_engine)


@fixture(scope="session")
def seq_repo(
    _engine: DatabaseEngine,
) -> SequenceRepository:
    return SequenceRepository(engine=_engine)


@fixture(scope="session")
def seq_step_repo(
    _engine: DatabaseEngine,
) -> SequenceStepRepository:
    return SequenceStepRepository(engine=_engine)


@fixture(scope="session")
def signature_repository(_engine: DatabaseEngine) -> SignatureRepository:
    return SignatureRepository(engine=_engine)


@fixture(scope="session")
def stage_criteria_repository(
    _engine: DatabaseEngine,
) -> StageCriteriaRepository:
    return StageCriteriaRepository(engine=_engine)


@fixture(scope="session")
def task_repo(
    _engine: DatabaseEngine,
) -> TaskRepository:
    return TaskRepository(engine=_engine)


@fixture(scope="session")
def sequence_repo(
    _engine: DatabaseEngine,
) -> SequenceRepository:
    return SequenceRepository(engine=_engine)


@fixture(scope="session")
def sequence_step_repo(
    _engine: DatabaseEngine,
) -> SequenceStepRepository:
    return SequenceStepRepository(engine=_engine)


@fixture(scope="session")
def task_template_repository(
    _engine: DatabaseEngine,
) -> TaskTemplateRepository:
    return TaskTemplateRepository(engine=_engine)


@fixture(scope="session")
def template_repository(
    _engine: DatabaseEngine,
) -> TemplateRepository:
    return TemplateRepository(engine=_engine)


@fixture(scope="session")
def email_template_repository(
    _engine: DatabaseEngine,
) -> EmailTemplateRepository:
    return EmailTemplateRepository(engine=_engine)


@fixture(scope="session")
def thread_repository(
    _engine: DatabaseEngine,
) -> ThreadRepository:
    return ThreadRepository(engine=_engine)


@fixture(scope="session")
def unsubscription_group_repository(
    _engine: DatabaseEngine,
) -> UnsubscriptionGroupRepository:
    return UnsubscriptionGroupRepository(engine=_engine)


@fixture(scope="session")
def user_calendar_repository(
    _engine: DatabaseEngine,
) -> UserCalendarRepository:
    return UserCalendarRepository(
        engine=_engine,
    )


@fixture(scope="session")
def event_schedule_repository(
    _engine: DatabaseEngine,
) -> EventScheduleRepository:
    return EventScheduleRepository(
        engine=_engine,
    )


@fixture(scope="session")
def user_integration_connector_repository(
    _engine: DatabaseEngine,
) -> UserIntegrationConnectorRepository:
    return UserIntegrationConnectorRepository(engine=_engine)


@fixture(scope="session")
def user_integration_repository(
    _engine: DatabaseEngine,
) -> UserIntegrationRepository:
    return UserIntegrationRepository(engine=_engine)


@fixture(scope="session")
def user_goal_repository(
    _engine: DatabaseEngine,
) -> UserGoalRepository:
    return UserGoalRepository(engine=_engine)


@fixture(scope="session")
def user_notification_repository(_engine: DatabaseEngine) -> UserNotificationRepository:
    return UserNotificationRepository(engine=_engine)


@fixture(scope="session")
def user_platform_credential_repository(
    _engine: DatabaseEngine,
) -> UserPlatformCredentialRepository:
    return UserPlatformCredentialRepository(engine=_engine)


@fixture(scope="session")
def user_repository(
    _engine: DatabaseEngine,
) -> UserRepository:
    return UserRepository(engine=_engine)


@fixture(scope="session")
def user_preference_repository(_engine: DatabaseEngine) -> UserPreferenceRepository:
    return UserPreferenceRepository(engine=_engine)


@fixture(scope="session")
def user_invite_repository(_engine: DatabaseEngine) -> UserInviteRepository:
    return UserInviteRepository(engine=_engine)


@fixture(scope="session")
def permission_set_repository(
    _engine: DatabaseEngine,
) -> PermissionSetRepository:
    return PermissionSetRepository(engine=_engine)


@fixture(scope="session")
def permission_set_group_repository(
    _engine: DatabaseEngine,
) -> PermissionSetGroupRepository:
    return PermissionSetGroupRepository(engine=_engine)


@fixture(scope="session")
def permission_set_user_association_repository(
    _engine: DatabaseEngine,
) -> PermissionSetUserAssociationRepository:
    return PermissionSetUserAssociationRepository(engine=_engine)


@fixture(scope="session")
def permission_set_group_user_association_repository(
    _engine: DatabaseEngine,
) -> PermissionSetGroupUserAssociationRepository:
    return PermissionSetGroupUserAssociationRepository(engine=_engine)


@fixture(scope="session")
def permission_set_group_permission_set_association_repository(
    _engine: DatabaseEngine,
) -> PermissionSetGroupPermissionSetAssociationRepository:
    return PermissionSetGroupPermissionSetAssociationRepository(engine=_engine)


@fixture(scope="session")
def job_role_user_association_repository(
    _engine: DatabaseEngine,
) -> JobRoleUserAssociationRepository:
    return JobRoleUserAssociationRepository(engine=_engine)


@fixture(scope="session")
async def email_event_repository(_engine: DatabaseEngine) -> EmailEventRepository:
    return EmailEventRepository(engine=_engine)


@fixture(scope="session")
def permission_service(
    _engine: DatabaseEngine,
) -> PermissionService:
    return get_permission_service_by_db_engine(_engine)


#
# @fixture(scope="session")
# def vital_repository(_engine: DatabaseEngine) -> VitalRepository:
#     return VitalRepository(engine=_engine)


@fixture(scope="session")
def view_management_repository(
    _engine: DatabaseEngine,
) -> ViewManagementRepository:
    return ViewManagementRepository(engine=_engine)


@fixture(scope="session")
def workflow_block_repo(_engine: DatabaseEngine) -> WorkflowBlockRepository:
    return WorkflowBlockRepository(engine=_engine)


@fixture(scope="session")
def workflow_edge_repo(_engine: DatabaseEngine) -> WorkflowEdgeRepository:
    return WorkflowEdgeRepository(engine=_engine)


@fixture(scope="session")
def workflow_node_repo(_engine: DatabaseEngine) -> WorkflowNodeRepository:
    return WorkflowNodeRepository(engine=_engine)


@fixture(scope="session")
def workflow_repo(_engine: DatabaseEngine) -> WorkflowRepository:
    return WorkflowRepository(engine=_engine)


@fixture(scope="session")
def workflow_run_node_repo(_engine: DatabaseEngine) -> WorkflowRunNodeRepository:
    return WorkflowRunNodeRepository(engine=_engine)


@fixture(scope="session")
def workflow_run_repo(_engine: DatabaseEngine) -> WorkflowRunRepository:
    return WorkflowRunRepository(engine=_engine)


@fixture(scope="session")
def workflow_snapshot_repo(_engine: DatabaseEngine) -> WorkflowSnapshotRepository:
    return WorkflowSnapshotRepository(engine=_engine)


@fixture(scope="session")
def workflow_trigger_event_repo(
    _engine: DatabaseEngine,
) -> WorkflowTriggerEventRepository:
    return WorkflowTriggerEventRepository(engine=_engine)


@fixture(scope="session")
def propagation_rule_repo(_engine: DatabaseEngine) -> PropagationRuleRepository:
    return PropagationRuleRepository(engine=_engine)


@fixture(scope="session")
def contact_repository(_engine: DatabaseEngine) -> ContactRepository:
    return ContactRepository(engine=_engine)


@fixture(scope="session")
def crm_integrity_job_repository(_engine: DatabaseEngine) -> CRMIntegrityJobRepository:
    return CRMIntegrityJobRepository(engine=_engine)


@fixture(scope="session")
def tracker_repository(_engine: DatabaseEngine) -> TrackerRepository:
    return TrackerRepository(engine=_engine)


@fixture(scope="session")
def voice_call_repository(_engine: DatabaseEngine) -> VoiceCallRepository:
    return VoiceCallRepository(engine=_engine)


@fixture(scope="session")
def voice_call_service(_engine: DatabaseEngine) -> VoiceCallService:
    return voice_call_service_from_engine(engine=_engine)


##################
#
# Client Fixtures
#
###################


@fixture(scope="session")
def brightdata_client() -> BrightdataClient:
    return BrightdataClient()


@fixture(scope="session")
def crustdata_client() -> CrustdataClient:
    return CrustdataClient()


@fixture(scope="session")
def linkedin_client() -> LinkedInClient:
    return LinkedInClient()


@fixture(scope="session")
def s3_manager() -> S3BucketManager:
    return get_s3_bucket_manager_by_bucket_name(bucket_name="bucket")


@fixture(scope="session")
def mailivery_client() -> AsyncMailiveryClient:
    return AsyncMailiveryClient()


@fixture(scope="session")
def pdl_client() -> PdlClient:
    return PdlClient()


##################
#
# Service Fixtures
#
###################
@pytest.fixture(scope="session")
def feature_flag_service() -> FeatureFlagService:
    return get_feature_flag_service()


@fixture(scope="session")
def approval_service(_engine: DatabaseEngine) -> ApprovalService:
    return get_approval_service_with_engine(db_engine=_engine)


@fixture(scope="session")
def pipeline_query_service(
    _engine: DatabaseEngine,
) -> PipelineQueryService:
    return get_pipeline_query_service(db_engine=_engine)


@fixture(scope="session")
def pipeline_service(
    _engine: DatabaseEngine,
) -> PipelineService:
    return get_pipeline_service(db_engine=_engine)


@fixture(scope="session")
def account_service(
    account_repo: AccountRepository,
    contact_service: ContactService,
    contact_query_service: ContactQueryService,
    address_repo: AddressRepository,
    custom_object_service: CustomObjectService,
    account_query_service: AccountQueryService,
    approval_service: ApprovalService,
    stage_criteria_service: StageCriteriaService,
    feature_flag_service: FeatureFlagService,
    pipeline_repository: PipelineRepository,
    event_schedule_repository: EventScheduleRepository,
    crm_sync_push_service: CrmSyncPushService,
) -> AccountService:
    return AccountService(
        account_repository=account_repo,
        contact_service=contact_service,
        contact_query_service=contact_query_service,
        address_repository=address_repo,
        custom_object_service=custom_object_service,
        account_query_service=account_query_service,
        feature_flag_service=feature_flag_service,
        research_agent_service=AsyncMock(),
        approval_service=approval_service,
        stage_criteria_service=stage_criteria_service,
        notification_service=AsyncMock(),
        pipeline_repository=pipeline_repository,
        event_schedule_repository=event_schedule_repository,
        crm_sync_push_service=crm_sync_push_service,
    )


@fixture(scope="session")
def activity_service(
    activity_repository: ActivityRepository,
    contact_repository: ContactRepository,
    pipeline_repository: PipelineRepository,
    _engine: DatabaseEngine,
) -> ActivityService:
    return ActivityService(
        activity_repository=activity_repository,
        contact_repository=contact_repository,
        pipeline_repository=pipeline_repository,
        activity_query_service=get_activity_query_service(db_engine=_engine),
    )


@fixture(scope="session")
def audience_list_service(
    list_repository: AudienceListRepository,
    list_membership_repository: AudienceListMembershipRepository,
) -> AudienceListService:
    return AudienceListService(
        list_repository=list_repository,
        list_membership_repository=list_membership_repository,
    )


@fixture(scope="session")
def audience_list_membership_service(
    list_repository: AudienceListRepository,
    list_membership_repository: AudienceListMembershipRepository,
) -> AudienceListMembershipService:
    return AudienceListMembershipService(
        list_repository=list_repository,
        list_membership_repository=list_membership_repository,
    )


@fixture(scope="session")
def user_calendar_service(
    _engine: DatabaseEngine,
) -> UserCalendarService:
    return get_user_calendar_service_by_db_engine(
        db_engine=_engine, background_tasks=BackgroundTasks()
    )


@fixture(scope="session")
def calendar_account_service(
    _engine: DatabaseEngine,
) -> CalendarAccountService:
    return CalendarAccountService(
        engine=_engine,
    )


@fixture(scope="session")
def contact_service(
    _engine: DatabaseEngine,
    contact_repo: ContactRepository,
    account_repo: AccountRepository,
    address_repo: AddressRepository,
    custom_object_service: CustomObjectService,
    person_repository: PersonRepository,
    job_service: JobService,
    approval_service: ApprovalService,
    stage_criteria_service: StageCriteriaService,
    feature_flag_service: FeatureFlagService,
) -> ContactService:
    return ContactService(
        contact_repository=contact_repo,
        account_repository=account_repo,
        address_repository=address_repo,
        custom_object_service=custom_object_service,
        job_service=job_service,
        person_repository=person_repository,
        contact_query_service=get_contact_query_service(db_engine=_engine),
        select_list_service=get_select_list_service(engine=_engine),
        research_agent_service=AsyncMock(),
        approval_service=approval_service,
        stage_criteria_service=stage_criteria_service,
        notification_service=AsyncMock(),
        feature_flag_service=feature_flag_service,
        crm_sync_push_service=AsyncMock(),
        domain_object_list_query_service=AsyncMock(),
    )


@fixture(scope="session")
def custom_object_service(
    custom_object_repo: CustomObjectRepository,
    select_list_service: InternalSelectListService,
) -> CustomObjectService:
    return CustomObjectService(
        custom_object_repo=custom_object_repo,
        select_list_service=select_list_service,
    )


@fixture(scope="session")
def comment_query_service(_engine: DatabaseEngine) -> CommentQueryService:
    return get_comment_query_service_from_engine(db_engine=_engine)


@fixture(scope="session")
def pipeline_stage_select_list_service(
    pipeline_stage_repository: PipelineStageRepository,
    select_list_service: InternalSelectListService,
) -> PipelineStageSelectListService:
    return PipelineStageSelectListService(
        pipeline_stage_select_list_repository=pipeline_stage_repository,
        select_list_service=select_list_service,
    )


@fixture(scope="session")
def account_query_service(
    _engine: DatabaseEngine,
) -> AccountQueryService:
    return get_account_query_service(
        db_engine=_engine,
    )


@fixture(scope="session")
def contact_query_service(
    _engine: DatabaseEngine,
) -> ContactQueryService:
    return get_contact_query_service(db_engine=_engine)


@fixture(scope="session")
def conversation_query_service(
    _engine: DatabaseEngine,
) -> ConversationQueryService:
    return get_conversation_query_service_from_engine(db_engine=_engine)


@fixture(scope="session")
def domain_object_list_query_service(
    _engine: DatabaseEngine,
) -> DomainObjectListQueryService:
    return get_domain_object_list_query_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def meeting_query_service(
    _engine: DatabaseEngine,
) -> MeetingQueryService:
    return get_meeting_query_service(db_engine=_engine)


@fixture(scope="session")
def task_query_service(
    _engine: DatabaseEngine,
) -> TaskQueryService:
    return get_task_query_service_from_engine(db_engine=_engine)


@fixture(scope="session")
def pipeline_intel_service(
    _engine: DatabaseEngine,
) -> PipelineIntelService:
    return get_pipeline_intel_service(db_engine=_engine)


@fixture(scope="session")
def global_thread_query_service(
    _engine: DatabaseEngine,
) -> GlobalThreadQueryService:
    return get_global_thread_query_service(db_engine=_engine)


@fixture(scope="session")
def event_schedule_query_service(
    _engine: DatabaseEngine,
) -> EventScheduleQueryService:
    return get_event_schedule_query_service_from_engine(db_engine=_engine)


@fixture(scope="session")
def outbound_domain_query_service(
    _engine: DatabaseEngine,
) -> OutboundDomainQueryService:
    return get_outbound_domain_query_service_by_db_engine(db_engine=_engine)


@fixture(autouse=True, scope="session")
async def wf_env() -> WorkflowEnvironment:
    return await WorkflowEnvironment.start_time_skipping(
        data_converter=pydantic_data_converter,
    )


@fixture(scope="session")
def citation_query_service(
    _engine: DatabaseEngine,
) -> CitationQueryService:
    return get_citation_query_service(db_engine=_engine)


@fixture(scope="session")
def custom_object_query_service(
    _engine: DatabaseEngine,
) -> CustomObjectQueryService:
    return get_custom_object_query_service(db_engine=_engine)


@fixture(scope="session")
def email_attachment_query_service(
    _engine: DatabaseEngine,
) -> EmailAttachmentQueryService:
    return get_email_attachment_query_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def email_account_pool_service(
    _engine: DatabaseEngine,
) -> EmailAccountPoolService:
    return get_email_account_pool_service_general(engine=_engine)


@fixture(scope="session")
def email_account_service_v2(
    _engine: DatabaseEngine,
) -> EmailAccountServiceV2:
    return get_email_account_service_v2_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def email_account_pool_query_service(
    _engine: DatabaseEngine,
) -> EmailAccountPoolQueryService:
    return get_email_account_pool_query_service_from_engine(db_engine=_engine)


@fixture(scope="session")
def email_account_query_service(
    _engine: DatabaseEngine,
) -> EmailAccountQueryService:
    return get_email_account_query_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def prospecting_run_query_service(
    _engine: DatabaseEngine,
) -> ProspectingRunQueryService:
    return get_prospecting_run_query_service_by_db(db_engine=_engine)


@fixture(scope="session")
def prospecting_run_result_query_service(
    _engine: DatabaseEngine,
) -> ProspectingRunResultQueryService:
    return get_prospecting_run_result_query_service_by_db(db_engine=_engine)


@fixture(scope="session")
def prospecting_credit_usage_query_service(
    _engine: DatabaseEngine,
) -> ProspectingCreditUsageQueryService:
    return get_prospecting_credit_usage_query_service_by_db(db_engine=_engine)


@fixture(scope="session")
def prospecting_filter_field_query_service(
    _engine: DatabaseEngine,
) -> ProspectingFilterFieldQueryService:
    return get_prospecting_filter_field_query_service_by_db(db_engine=_engine)


@fixture(scope="session")
def prospecting_saved_search_query_query_service(
    _engine: DatabaseEngine,
) -> ProspectingSavedSearchQueryQueryService:
    return get_prospecting_saved_search_query_query_service_by_db(db_engine=_engine)


@fixture(scope="session")
def signature_query_service(
    _engine: DatabaseEngine,
) -> SignatureQueryService:
    return get_signature_query_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def sequence_execution_service(
    _engine: DatabaseEngine,
) -> SequenceExecutionService:
    return get_sequence_execution_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def signature_service(
    _engine: DatabaseEngine,
) -> SignatureService:
    return get_signature_service(db_engine=_engine)


@fixture(scope="session")
def sequence_step_query_service(
    _engine: DatabaseEngine,
) -> SequenceStepQueryService:
    return get_sequence_step_query_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def sequence_execution_query_service(
    _engine: DatabaseEngine,
) -> SequenceExecutionQueryService:
    return get_sequence_execution_query_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def sequence_step_variant_query_service(
    _engine: DatabaseEngine,
) -> SequenceStepVariantQueryService:
    return get_sequence_step_variant_query_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def sequence_step_execution_query_service(
    _engine: DatabaseEngine,
) -> SequenceStepExecutionQueryService:
    return get_sequence_step_execution_query_service_by_db(db_engine=_engine)


@fixture(scope="session")
def pipeline_qualification_property_service(
    _engine: DatabaseEngine,
) -> PipelineQualificationPropertyService:
    return get_pipeline_qualification_property_service(db_engine=_engine)


@fixture(scope="session")
def citation_service(
    _engine: DatabaseEngine,
) -> CitationService:
    return get_citation_service(engine=_engine)


@fixture(scope="session")
def domain_object_query_service(
    metadata_service: MetadataService,
    account_query_service: AccountQueryService,
    contact_query_service: ContactQueryService,
    saved_domain_object_filter_repository: SavedDomainObjectFilterRepository,
    user_service: UserService,
    goal_query_service: GoalQueryService,
    meeting_query_service: MeetingQueryService,
    comment_query_service: CommentQueryService,
    pipeline_query_service: PipelineQueryService,
    global_thread_query_service: GlobalThreadQueryService,
    task_query_service: TaskQueryService,
    event_schedule_query_service: EventScheduleQueryService,
    email_template_query_service: EmailTemplateQueryService,
    domain_object_list_query_service: DomainObjectListQueryService,
    citation_query_service: CitationQueryService,
    conversation_query_service: ConversationQueryService,
    custom_object_query_service: CustomObjectQueryService,
    association_service: AssociationService,
    email_attachment_query_service: EmailAttachmentQueryService,
    email_account_pool_query_service: EmailAccountPoolQueryService,
    email_account_query_service: EmailAccountQueryService,
    sequence_query_service: SequenceQueryService,
    prospecting_run_query_service: ProspectingRunQueryService,
    prospecting_run_result_query_service: ProspectingRunResultQueryService,
    prospecting_credit_usage_query_service: ProspectingCreditUsageQueryService,
    prospecting_filter_field_query_service: ProspectingFilterFieldQueryService,
    sequence_enrollment_query_service: SequenceEnrollmentQueryService,
    sequence_step_query_service: SequenceStepQueryService,
    sequence_execution_query_service: SequenceExecutionQueryService,
    sequence_step_variant_query_service: SequenceStepVariantQueryService,
    prospecting_saved_search_query_query_service: ProspectingSavedSearchQueryQueryService,
    outbound_domain_query_service: OutboundDomainQueryService,
    signature_query_service: SignatureQueryService,
    sequence_step_execution_query_service: SequenceStepExecutionQueryService,
    feature_flag_service: FeatureFlagService,
    falkordb_conn_mgr: FalkorDBConnectionManager,
    select_list_service: InternalSelectListService,
    _engine: DatabaseEngine,
) -> DomainObjectQueryService:
    return DomainObjectQueryService(
        metadata_service=metadata_service,
        feature_flag_service=get_feature_flag_service(),
        saved_domain_object_filter_repository=saved_domain_object_filter_repository,
        account_query_service=account_query_service,
        contact_query_service=contact_query_service,
        user_service=user_service,
        goal_query_service=goal_query_service,
        meeting_query_service=meeting_query_service,
        comment_query_service=comment_query_service,
        pipeline_query_service=pipeline_query_service,
        task_query_service=task_query_service,
        outbound_domain_query_service=outbound_domain_query_service,
        event_schedule_query_service=event_schedule_query_service,
        activity_resolver=get_activity_resolver(db_engine=_engine),
        global_thread_query_service=global_thread_query_service,
        email_template_query_service=email_template_query_service,
        domain_object_list_query_service=domain_object_list_query_service,
        citation_query_service=citation_query_service,
        conversation_query_service=conversation_query_service,
        custom_object_query_service=custom_object_query_service,
        association_service=association_service,
        email_account_query_service=email_account_query_service,
        email_attachment_query_service=email_attachment_query_service,
        email_account_pool_query_service=email_account_pool_query_service,
        sequence_query_service=sequence_query_service,
        sequence_step_query_service=sequence_step_query_service,
        prospecting_run_query_service=prospecting_run_query_service,
        prospecting_run_result_query_service=prospecting_run_result_query_service,
        prospecting_credit_usage_query_service=prospecting_credit_usage_query_service,
        prospecting_filter_field_query_service=prospecting_filter_field_query_service,
        sequence_enrollment_query_service=sequence_enrollment_query_service,
        sequence_execution_query_service=sequence_execution_query_service,
        sequence_step_variant_query_service=sequence_step_variant_query_service,
        prospecting_saved_search_query_query_service=prospecting_saved_search_query_query_service,
        signature_query_service=signature_query_service,
        sequence_step_execution_query_service=sequence_step_execution_query_service,
        falkordb_conn_mgr=falkordb_conn_mgr,
        select_list_service=select_list_service,
    )


@fixture(scope="session")
def falkordb_indexing_lib(
    _engine: DatabaseEngine,
    falkordb_client: FalkorDBClient,
) -> FalkorDBIndexingLib:
    return get_falkordb_indexing_lib(_engine, falkordb_client)


@fixture(scope="session")
def falkordb_client() -> FalkorDBClient:
    return get_falkordb_client()


@fixture(scope="session")
def falkordb_conn_mgr() -> FalkorDBConnectionManager:
    return get_falkordb_connection_manager()


@fixture(scope="session")
def domain_object_list_service(
    _engine: DatabaseEngine,
) -> DomainObjectListService:
    return get_domain_object_list_service(db_engine=_engine)


@fixture(scope="session")
def domain_crm_association_service(
    _engine: DatabaseEngine,
) -> DomainCRMAssociationService:
    return get_domain_crm_association_service(db_engine=_engine)


@fixture(scope="session")
def email_account_service_ext(
    _engine: DatabaseEngine,
) -> EmailAccountServiceExt:
    return EmailAccountServiceExt(engine=_engine)


@fixture(scope="session")
def email_activity_service(
    _engine: DatabaseEngine,
) -> EmailActivityService:
    return EmailActivityService(_engine)


@fixture(scope="session")
def email_attachment_service(
    _engine: DatabaseEngine,
) -> EmailAttachmentService:
    return EmailAttachmentService(_engine)


@fixture(scope="session")
def email_copy_writer_service(_engine: DatabaseEngine) -> EmailCopyWriterService:
    return get_email_copy_writer_service_from_engine(db_engine=_engine)


@fixture(scope="session")
def email_deliverable_window_service(
    _engine: DatabaseEngine,
) -> EmailDeliverWindowService:
    return EmailDeliverWindowService(
        db_engine=_engine,
    )


@fixture(scope="session")
def organization_service(
    _engine: DatabaseEngine,
) -> OrganizationService:
    organization_repo = OrganizationRepository(engine=_engine)
    organization_info_repo = OrganizationInfoRepository(engine=_engine)

    return OrganizationService(
        organization_repo=organization_repo,
        organization_info_repo=organization_info_repo,
        feature_flag_service=Mock(),  # just do a mock for now
    )


@fixture(scope="session")
def email_insight_service(
    message_repository: MessageRepository,
    thread_repository: ThreadRepository,
    meeting_repository: MeetingRepository,
    extraction_config_repository: ExtractionConfigRepository,
    user_service: UserService,
    organization_service: OrganizationService,
    user_repository: UserRepository,
    account_query_service: AccountQueryService,
    _engine: DatabaseEngine,
) -> EmailInsightService:
    return EmailInsightService(
        config_extraction_service=ExtractionConfigService(
            extraction_config_repository=extraction_config_repository
        ),
        email_extraction_service=EmailExtractionService(
            extraction_config_repository=extraction_config_repository,
            user_service=user_service,
            llm=Mock(),
        ),
        insight_repository=InsightRepository(engine=_engine),
        message_repository=message_repository,
        thread_repository=thread_repository,
        task_v2_service=get_task_v2_service_general(db_engine=_engine),
        contact_query_service=get_contact_query_service(db_engine=_engine),
        meeting_repository=meeting_repository,
        organization_service=organization_service,
        user_repository=user_repository,
        account_query_service=account_query_service,
        user_service=user_service,
    )


@fixture(scope="session")
def email_message_service_ext(
    _engine: DatabaseEngine,
) -> MessageServiceExt:
    return MessageServiceExt(db_engine=_engine)


@fixture(scope="session")
def email_participant_service(
    _engine: DatabaseEngine,
) -> EmailParticipantService:
    return EmailParticipantService(_engine)


@fixture(scope="session")
def email_rendering_service(
    _engine: DatabaseEngine,
) -> EmailRenderingService:
    return EmailRenderingService(
        db_engine=_engine,
    )


@fixture(scope="session")
def email_sending_service(
    _engine: DatabaseEngine,
) -> EmailSendingService:
    return EmailSendingService(_engine)


@fixture(scope="session")
def email_subscription_service(
    _engine: DatabaseEngine,
) -> EmailSubscriptionService:
    return EmailSubscriptionService(
        db_engine=_engine,
    )


@fixture(scope="session")
def email_template_service_ext(
    _engine: DatabaseEngine,
) -> EmailTemplateServiceExt:
    return EmailTemplateServiceExt(
        db_engine=_engine,
    )


@fixture(scope="session")
def email_account_pool_repository(
    _engine: DatabaseEngine,
) -> EmailAccountPoolRepository:
    return EmailAccountPoolRepository(engine=_engine)


@fixture(scope="session")
def email_account_warm_up_campaign_repository(
    _engine: DatabaseEngine,
) -> EmailAccountWarmUpCampaignRepository:
    return EmailAccountWarmUpCampaignRepository(engine=_engine)


@fixture(scope="session")
def email_sync_service(
    _engine: DatabaseEngine,
) -> EmailSyncService:
    return get_email_sync_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def email_data_integrity_service(
    _engine: DatabaseEngine,
) -> EmailDataIntegrityService:
    return get_email_data_integrity_service(db_engine=_engine)


@fixture(scope="session")
def contact_resolve_service(
    _engine: DatabaseEngine,
) -> ContactResolveService:
    return get_contact_resolve_service(db_engine=_engine)


@fixture(scope="session")
def global_thread_service(
    _engine: DatabaseEngine,
) -> GlobalThreadService:
    return get_global_thread_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def imap_syncing_service(
    _engine: DatabaseEngine,
) -> ImapSyncingService:
    return get_imap_syncing_service_general(engine=_engine)


@fixture(scope="session")
def file_service(
    attachment_repository: AttachmentRepository,
    s3_manager: S3BucketManager,
    email_account_service_ext: EmailAccountServiceExt,
) -> FileService:
    return FileService(
        attachment_repository=attachment_repository,
        email_attachment_s3_manager=s3_manager,
        meeting_video_s3_manager=s3_manager,
        import_csv_s3_manager=s3_manager,
        avatar_s3_manager=s3_manager,
        public_s3_manager=s3_manager,
        voice_mail_s3_manager=s3_manager,
        email_account_service_ext=email_account_service_ext,
    )


@fixture(scope="session")
def hubspot_service(_engine: DatabaseEngine) -> HubSpotService:
    return get_hubspot_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def meeting_service(_engine: DatabaseEngine) -> MeetingService:
    return meeting_service_factory_general(db_engine=_engine)


@fixture(scope="session")
def meeting_ai_rec_service(_engine: DatabaseEngine) -> MeetingAIRecService:
    return get_meeting_ai_rec_service(db_engine=_engine)


@fixture(scope="session")
def meeting_insight_service(_engine: DatabaseEngine) -> MeetingInsightService:
    return meeting_insight_service_factory_general(db_engine=_engine)


@fixture(scope="session")
def meeting_bot_service(_engine: DatabaseEngine) -> MeetingBotService:
    return meeting_bot_service_general(db_engine=_engine)


@fixture(scope="session")
def import_job_repository(_engine: DatabaseEngine) -> ImportJobRepository:
    return ImportJobRepository(engine=_engine)


@fixture(scope="session")
def crm_sync_service(
    import_repository: ImportRepository,
    contact_service: ContactService,
    contact_query_service: ContactQueryService,
    custom_object_service: CustomObjectService,
    association_service: AssociationService,
    account_service: AccountService,
    pipeline_service: PipelineService,
    pipeline_stage_select_list_service: PipelineStageSelectListService,
    user_auth_service: UserAuthService,
    task_v2_service: TaskV2Service,
    meeting_service: MeetingService,
    file_service: FileService,
    crm_sync_repository: CRMSyncRepository,
    import_job_repository: ImportJobRepository,
    hubspot_service: HubSpotService,
    feature_flag_service: FeatureFlagService,
    domain_object_list_service: DomainObjectListService,
    select_list_service: InternalSelectListService,
) -> CrmSyncService:
    return CrmSyncService(
        import_repository=import_repository,
        contact_service=contact_service,
        contact_query_service=contact_query_service,
        custom_object_service=custom_object_service,
        association_service=association_service,
        account_service=account_service,
        pipeline_service=pipeline_service,
        pipeline_stage_service=pipeline_stage_select_list_service,
        user_auth_service=user_auth_service,
        task_v2_service=task_v2_service,
        meeting_service=meeting_service,
        file_service=file_service,
        crm_sync_repository=crm_sync_repository,
        import_job_repository=import_job_repository,
        hubspot_service=hubspot_service,
        domain_object_list_service=domain_object_list_service,
        feature_flag_service=feature_flag_service,
        select_list_service=select_list_service,
    )


@fixture(scope="session")
def crm_sync_push_service(
    hubspot_service: HubSpotService,
    crm_sync_repository: CRMSyncRepository,
    import_repository: ImportRepository,
) -> CrmSyncPushService:
    return CrmSyncPushService(
        hubspot_service=hubspot_service,
        crm_sync_repository=crm_sync_repository,
        import_repository=import_repository,
    )


@fixture(scope="session")
def job_service(job_repository: JobRepository) -> JobService:
    return JobService(
        job_repository=job_repository,
    )


@fixture(scope="session")
def import_job_service(
    import_job_repository: ImportJobRepository,
    import_repository: ImportRepository,
    file_service: FileService,
    _temporal_client: Client,
) -> ImportJobService:
    return ImportJobService(
        repository=import_job_repository,
        import_repository=import_repository,
        file_service=file_service,
        temporal_client=_temporal_client,
    )


@fixture(scope="session")
def import_csv_job_review_repository(
    _engine: DatabaseEngine,
) -> ImportCsvJobReviewRepository:
    return ImportCsvJobReviewRepository(engine=_engine)


@fixture(scope="session")
def import_csv_job_review_service(
    import_csv_job_review_repository: ImportCsvJobReviewRepository,
    import_job_service: ImportJobService,
    organization_service: OrganizationService,
) -> ImportCsvJobReviewService:
    return ImportCsvJobReviewService(
        import_csv_job_review_repository=import_csv_job_review_repository,
        import_job_service=import_job_service,
        organization_service=organization_service,
    )


@fixture(scope="session")
def message_service(_engine: DatabaseEngine) -> MessageService:
    return MessageService(db_engine=_engine)


@fixture(scope="session")
def metadata_service(
    custom_object_service: CustomObjectService,
    crm_sync_repository: CRMSyncRepository,
    _engine: DatabaseEngine,
) -> MetadataService:
    return MetadataService(
        custom_object_service=custom_object_service,
        crm_sync_repository=crm_sync_repository,
        association_service=get_association_service(db_engine=_engine),
    )


@fixture(scope="session")
def people_service(
    person_repository: PersonRepository,
    prospecting_common_service: ProspectingCommonService,
) -> PeopleService:
    return PeopleService(
        person_repository=person_repository,
        prospecting_common_service=prospecting_common_service,
    )


@fixture(scope="session")
def prospecting_common_service(
    _engine: DatabaseEngine,
) -> ProspectingCommonService:
    return get_prospecting_common_service(db_engine=_engine)


@fixture(scope="session")
def prospecting_enrichment_service(
    _engine: DatabaseEngine,
) -> ProspectingEnrichmentService:
    return get_prospecting_enrichment_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def prospecting_quota_service(
    _engine: DatabaseEngine,
) -> ProspectingQuotaService:
    return get_prospecting_quota_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def quota_service(_engine: DatabaseEngine) -> QuotaService:
    return QuotaService(db_engine=_engine)


@fixture(scope="session")
def quota_policy_service(_engine: DatabaseEngine) -> QuotaPolicyService:
    return QuotaPolicyService(db_engine=_engine)


@fixture(scope="session")
def research_agent_service(
    intel_repository: IntelRepository,
    crustdata_client: CrustdataClient,
    user_feedback_repository: UserFeedbackRepository,
) -> ResearchAgentService:
    return ResearchAgentService(
        crustdata_client=crustdata_client,
        intel_repository=intel_repository,
        research_agent_client=get_research_agent_client(),
        user_feedback_repository=user_feedback_repository,
    )


@fixture(scope="session")
def select_list_service(
    select_list_repository: SelectListRepository,
) -> InternalSelectListService:
    return InternalSelectListService(
        select_list_repository=select_list_repository,
    )


@fixture(scope="session")
def email_extraction_service(
    _engine: DatabaseEngine,
    extraction_config_repository: ExtractionConfigRepository,
    user_service: UserService,
) -> EmailExtractionService:
    """
    This fixture may have some issues due to anthropic instantiation. ONLY use locally.
    Although likely no one will see this; we need to verify github runner has correct
    aws creds that have access to sonnet.
    """
    return EmailExtractionService(
        extraction_config_repository=extraction_config_repository,
        user_service=user_service,
        llm=anthropic_sonnet(),
    )


@fixture(scope="session")
def sequence_service(_engine: DatabaseEngine) -> SequenceService:
    return get_sequence_service_by_db_engine(_engine)


@fixture(scope="session")
def sequence_enrollment_service(_engine: DatabaseEngine) -> SequenceEnrollmentService:
    return get_sequence_enrollment_service_by_db_engine(_engine)


@fixture(scope="session")
def stage_criteria_service(
    domain_object_query_service: DomainObjectQueryService,
    pipeline_stage_select_list_service: PipelineStageSelectListService,
    stage_criteria_repository: StageCriteriaRepository,
) -> StageCriteriaService:
    return StageCriteriaService(
        domain_object_query_service=domain_object_query_service,
        pipeline_stage_select_list_service=pipeline_stage_select_list_service,
        stage_criteria_repository=stage_criteria_repository,
    )


@fixture(scope="session")
def goal_query_service(_engine: DatabaseEngine) -> GoalQueryService:
    return goal_query_service_by_db_engine(_engine)


@fixture(scope="session")
def task_v2_service(_engine: DatabaseEngine) -> TaskV2Service:
    return get_task_v2_service_general(_engine)


@fixture(scope="session")
def email_template_query_service(_engine: DatabaseEngine) -> EmailTemplateQueryService:
    return get_email_template_query_service_from_engine(
        db_engine=_engine,
    )


@fixture(scope="session")
def thread_service_ext(_engine: DatabaseEngine) -> ThreadServiceExt:
    return ThreadServiceExt(db_engine=_engine)


@fixture(scope="session")
def unsubscription_group_ext_service(
    _engine: DatabaseEngine,
) -> UnsuscriptionGroupServiceExt:
    return UnsuscriptionGroupServiceExt(engine=_engine)


@fixture(scope="session")
def user_auth_service(
    _engine: DatabaseEngine,
) -> UserAuthService:
    return get_user_auth_service_with_engine(_engine)


@fixture(scope="session")
def user_calendar_sync_service(
    _engine: DatabaseEngine,
) -> UserCalendarSyncService:
    return get_user_calendar_sync_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def user_calendar_schedule_service(
    _engine: DatabaseEngine,
) -> UserCalendarScheduleService:
    return get_user_calendar_schedule_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def email_sync_schedule_service(
    _engine: DatabaseEngine,
) -> EmailSyncScheduleService:
    return get_email_sync_schedule_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def user_calendar_data_integrity_service(
    _engine: DatabaseEngine,
) -> UserCalendarDataIntegrityService:
    return get_user_calendar_data_integrity_service(db_engine=_engine)


@fixture(scope="session")
def user_calendar_webhook_service(
    _engine: DatabaseEngine,
) -> UserCalendarWebhookService:
    return get_user_calendar_webhook_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def user_feedback_service(_engine: DatabaseEngine) -> UserFeedbackService:
    return UserFeedbackService(repository=UserFeedbackRepository(engine=_engine))


@fixture(scope="session")
def user_feedback_repository(_engine: DatabaseEngine) -> UserFeedbackRepository:
    return UserFeedbackRepository(engine=_engine)


@fixture(scope="session")
def user_service(
    _engine: DatabaseEngine,
) -> UserService:
    return get_user_service_general(db_engine=_engine)


@fixture(scope="session")
def view_management_service(
    view_management_repository: ViewManagementRepository,
    domain_object_query_service: DomainObjectQueryService,
    user_service: UserService,
) -> ViewManagementService:
    return ViewManagementService(
        view_management_repository=view_management_repository,
        domain_object_query_service=domain_object_query_service,
        user_service=user_service,
    )


@fixture(scope="session")
def workflow_execution_service(
    _engine: DatabaseEngine,
) -> WorkflowExecutionService:
    return WorkflowExecutionService(db_engine=_engine)


@fixture(scope="session")
def workflow_trigger_service(
    _engine: DatabaseEngine,
) -> WorkflowTriggerService:
    return WorkflowTriggerService(db_engine=_engine)


@fixture(scope="session")
def workflow_temporal_service(
    _engine: DatabaseEngine,
    _temporal_client: Client,
) -> WorkflowTemporalService:
    return WorkflowTemporalService(db_engine=_engine, client=_temporal_client)


@fixture(scope="session")
def workflow_if_else_node_activity(
    _engine: DatabaseEngine,
) -> IfElseNodeActivity:
    return IfElseNodeActivity(db_engine=_engine)


@fixture(scope="session")
def workflow_switch_node_activity(
    _engine: DatabaseEngine,
) -> SwitchNodeActivity:
    return SwitchNodeActivity(db_engine=_engine)


@fixture(scope="session")
def workflow_validation_service(
    _engine: DatabaseEngine,
) -> WorkflowValidationService:
    return get_workflow_validation_service(db_engine=_engine)


@fixture(scope="session")
def logical_propagation_trigger_service(
    _engine: DatabaseEngine,
) -> LogicalPropagationTriggerService:
    return LogicalPropagationTriggerService(db_engine=_engine)


@fixture(scope="session")
def logical_propagation_evaluation_service(
    _engine: DatabaseEngine,
) -> LogicalPropagationEvaluationService:
    return LogicalPropagationEvaluationService(db_engine=_engine)


@fixture(scope="session")
def logical_propagation_execution_service(
    _engine: DatabaseEngine,
) -> LogicalPropagationExecutionService:
    return LogicalPropagationExecutionService(db_engine=_engine)


@fixture(scope="session")
def logical_propagation_validation_service(
    propagation_rule_repo: PropagationRuleRepository,
    _engine: DatabaseEngine,
) -> LogicalPropagationValidationService:
    domain_object_query_service = get_domain_object_query_service(db_engine=_engine)
    return LogicalPropagationValidationService(
        db_engine=_engine,
        domain_object_query_service=domain_object_query_service,
        propagation_rule_repository=propagation_rule_repo,
    )


@fixture(scope="session")
def atomic_operation_tracker_service(
    _engine: DatabaseEngine,
) -> AtomicOperationTrackerService:
    return AtomicOperationTrackerService(db_engine=_engine)


@fixture(scope="session")
def integrity_job_service(
    _engine: DatabaseEngine,
) -> IntegrityJobService:
    return IntegrityJobService(db_engine=_engine)


@fixture(scope="session")
def contact_data_integrity_service(
    _engine: DatabaseEngine,
) -> ContactDataIntegrityService:
    return get_contact_data_integrity_service(db_engine=_engine)


@fixture(scope="session")
def account_data_integrity_service(
    _engine: DatabaseEngine,
) -> AccountDataIntegrityService:
    return get_account_data_integrity_service(db_engine=_engine)


@fixture(scope="session")
def note_service(_engine: DatabaseEngine) -> NoteService:
    return get_note_service_general(db_engine=_engine)


@fixture(scope="session")
def note_data_integrity_service(_engine: DatabaseEngine) -> NoteDataIntegrityService:
    return get_note_data_integrity_service(db_engine=_engine)


@fixture(scope="session")
def notification_service(
    _engine: DatabaseEngine,
) -> NotificationService:
    return get_notification_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def merge_accounts_preview_service(
    _engine: DatabaseEngine,
) -> MergeAccountsPreviewService:
    return MergeAccountsPreviewService(database_engine=_engine)


@fixture(scope="session")
def association_service(
    _engine: DatabaseEngine,
) -> AssociationService:
    return get_association_service(db_engine=_engine)


@fixture(scope="session")
def organization_service_v2(
    _engine: DatabaseEngine,
) -> OrganizationServiceV2:
    return get_organization_service_v2_from_engine(db_engine=_engine)


@fixture(scope="session")
def event_schedule_service(
    _engine: DatabaseEngine,
) -> EventScheduleService:
    return get_event_schedule_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def sequence_query_service(
    _engine: DatabaseEngine,
) -> SequenceQueryService:
    return get_sequence_query_service_by_db(db_engine=_engine)


@fixture(scope="session")
def prospecting_run_service(
    _engine: DatabaseEngine,
) -> ProspectingRunService:
    return get_prospecting_run_service_from_engine(db_engine=_engine)


@fixture(scope="session")
def sequence_enrollment_query_service(
    _engine: DatabaseEngine,
) -> SequenceEnrollmentQueryService:
    return get_sequence_enrollment_query_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def sequence_enrollment_repo(
    _engine: DatabaseEngine,
) -> SequenceEnrollmentRepository:
    return SequenceEnrollmentRepository(engine=_engine)


@fixture(scope="session")
def domain_crm_association_repository(
    _engine: DatabaseEngine,
) -> DomainCRMAssociationRepository:
    return DomainCRMAssociationRepository(engine=_engine)


#####################
#
# Other Fixtures
#
#####################


@fixture
async def make_sequence_execution(
    faker: Faker,
    contact_factory: ContactFactory,
    sequence_factory: SequenceFactory,
    sequence_schedule_factory: SequenceScheduleFactory,
    sequence_step_factory: SequenceStepFactory,
    sequence_target_factory: SequenceTargetFactory,
    sequence_execution_factory: SequenceExecutionFactory,
    user_factory: UserFactory,
    seq_exec_repo: SequenceExecutionRepositoryForTest,
    sequence_action_factory: SequenceActionFactory,
) -> AsyncGenerator[
    Callable[[SequenceExecutionReferences], Awaitable[SequenceExecutionIds]], None
]:
    created_sequence_ids = []
    created_user_ids = []

    async def _generate(
        references: SequenceExecutionReferences,
    ) -> SequenceExecutionIds:
        organization_id = references.organization_id
        user_id = references.user_id
        sequence_id = references.sequence_id
        step_id = references.step_id
        action_id = references.action_id

        if not user_id:
            user_id = (
                await seq_exec_repo.insert(
                    user_factory.build(email=random_unique_email())
                )
            ).id
            created_user_ids.append(user_id)
        elif not await seq_exec_repo.find_by_primary_key(User, id=user_id):
            await seq_exec_repo.insert(
                user_factory.build(id=user_id, email=random_unique_email())
            )

        if not sequence_id:
            sequence_id = (
                await seq_exec_repo.insert(
                    sequence_factory.build(organization_id=organization_id)
                )
            ).id
            created_sequence_ids.append(sequence_id)
        elif not await seq_exec_repo.find_by_primary_key(Sequence, id=sequence_id):
            await seq_exec_repo.insert(
                sequence_factory.build(id=sequence_id, organization_id=organization_id)
            )

        if not step_id:
            step_id = (
                await seq_exec_repo.insert(
                    sequence_step_factory.build(
                        organization_id=organization_id,
                        sequence_id=sequence_id,
                    )
                )
            ).id
        elif not await seq_exec_repo.find_by_primary_key(SequenceStep, id=step_id):
            await seq_exec_repo.insert(
                sequence_step_factory.build(
                    organization_id=organization_id,
                    id=step_id,
                    sequence_id=sequence_id,
                )
            )

        if not action_id:
            action_id = (
                await seq_exec_repo.insert(
                    sequence_action_factory.build(
                        organization_id=organization_id,
                        sequence_step_id=step_id,
                        sequence_id=sequence_id,
                    )
                )
            ).id
        elif not await seq_exec_repo.find_by_primary_key(SequenceAction, id=action_id):
            await seq_exec_repo.insert(
                sequence_action_factory.build(
                    organization_id=organization_id,
                    id=action_id,
                    sequence_step_id=step_id,
                    sequence_id=sequence_id,
                )
            )

        email_id = uuid4()
        contact = await seq_exec_repo.insert(
            contact_factory.build(
                primary_email=random_unique_email(), organization_id=organization_id
            )
        )
        target_id = (
            await seq_exec_repo.insert(
                sequence_target_factory.build(
                    organization_id=organization_id,
                    sequence_id=sequence_id,
                    contact_id=contact.id,
                )
            )
        ).id
        execution_id = (
            await seq_exec_repo.insert(
                sequence_execution_factory.build(
                    organization_id=organization_id,
                    sequence_id=sequence_id,
                    step_id=step_id,
                    target_id=target_id,
                    email_id=email_id,
                    action_id=action_id,
                    status=faker.enum(enum_cls=SequenceExecutionStatus),
                )
            )
        ).id
        return SequenceExecutionIds(
            execution_id=execution_id,
            email_id=email_id,
            target_id=target_id,
            step_id=step_id,
            sequence_id=sequence_id,
            user_id=user_id,
            action_id=action_id,
        )

    yield _generate


@fixture
async def random_organization(
    user_auth_service: UserAuthService,
    user_repository: UserRepository,
    faker: Faker,
) -> RandomOrganization:
    admin_user = await user_auth_service.get_or_create_user(
        email=f"{uuid4()}@example.com"
    )
    organization = await user_auth_service.create_organization(
        created_by_user_id=admin_user.id
    )
    regular_user = await user_auth_service.get_or_create_user(
        email=f"{uuid4()}@example.com"
    )
    await user_repository.associate_user_to_an_organization(
        user_id=regular_user.id,
        organization_id=organization.id,
        roles=[UserRole.USER],
    )
    await user_repository.associate_user_to_an_organization(
        user_id=admin_user.id,
        organization_id=organization.id,
        roles=[UserRole.ADMIN],
    )
    return RandomOrganization(
        organization=organization,
        admin_user=admin_user,
        regular_user=regular_user,
    )


@fixture
async def make_user(
    faker: Faker,
    user_factory: UserFactory,
    user_repository: UserRepository,
) -> AsyncGenerator[Callable[[], Awaitable[UUID]]]:
    async def _make_user() -> UUID:
        return (
            await user_repository.insert(
                user_factory.build(
                    first_name=faker.first_name(),
                    last_name=faker.last_name(),
                    email=random_unique_email(),
                    phone_number=faker.phone_number(),
                )
            )
        ).id

    yield _make_user


@fixture
async def make_user_org(
    faker: Faker,
    organization_factory: OrganizationFactory,
    user_factory: UserFactory,
    user_repository: UserRepository,
    select_list_service: InternalSelectListService,
    pipeline_stage_select_list_service: PipelineStageSelectListService,
) -> AsyncGenerator[Callable[[], Awaitable[tuple[UUID, UUID]]], None]:
    async def _make_user_org() -> tuple[UUID, UUID]:
        organization_id = (
            await user_repository.insert(
                organization_factory.build(display_name=faker.company())
            )
        ).id

        user_id = (
            await user_repository.insert(
                user_factory.build(
                    first_name=faker.first_name(),
                    last_name=faker.last_name(),
                    email=random_unique_email(),
                    phone_number=faker.phone_number(),
                )
            )
        ).id

        await user_repository.associate_user_to_an_organization(
            user_id=user_id,
            organization_id=organization_id,
            roles=default_roles(),
        )

        await select_list_service.bootstrap_all_direct_std_select_lists(
            organization_id=organization_id, user_id=user_id
        )

        await pipeline_stage_select_list_service.bootstrap_organization_default_pipeline_stage_select_list(
            organization_id=organization_id, actioning_user_id=user_id
        )

        return user_id, organization_id

    yield _make_user_org

    # TODO cleanup


@fixture(scope="session", autouse=True)
def install_playwright_browsers() -> None:
    playwright_executable = shutil.which("playwright")
    if not playwright_executable:
        raise FileNotFoundError(
            "Playwright executable not found. Please uv pip install"
        )

    args = ["install", "--with-deps", "chromium"]
    # Ensure Playwright browsers are installed before running the tests
    subprocess.run([playwright_executable, *args], check=True)  # noqa: S603


@fixture(scope="session")
def email_webhook_service(
    _engine: DatabaseEngine,
) -> EmailWebhookService:
    return get_email_webhook_service_by_db_engine(db_engine=_engine)


@fixture(scope="session")
def pdl_cache_repository(
    _engine: DatabaseEngine,
) -> PDLCacheRepository:
    return PDLCacheRepository(engine=_engine)
