from datetime import timed<PERSON><PERSON>
from unittest import mock
from unittest.mock import AsyncMock
from uuid import UUID, uuid4

import pytest

from salestech_be.core.sequence.service.sequence_execution_query_service import (
    SequenceExecutionQueryService,
)
from salestech_be.core.sequence.service.sequence_execution_service import (
    SequenceExecutionService,
)
from salestech_be.db.dao.sequence_repository import SequenceStepRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.models.global_thread import GlobalThread
from salestech_be.db.models.sequence import (
    EmailStepContent,
    SequenceEnrollment,
    SequenceEnrollmentStatus,
    SequenceEnrollmentStepVariantAssociation,
    SequenceErrorCode,
    SequenceStepExecution,
    SequenceStepExecutionResultEntityType,
    SequenceStepExecutionStatus,
    SequenceStepType,
    SequenceStepV2,
    SequenceStepVariant,
    SequenceStepVariantStatus,
)
from salestech_be.util.time import zoned_utc_now
from tests.integration.sequence.repository_for_test import (
    SequenceExecutionRepositoryForTest,
)
from tests.test_util import random_unique_email
from tests.util.factories import (
    DbSequenceStepVariantFactory,
    EmailAccountFactory,
    EmailTemplateFactory,
    SequenceEnrollmentFactory,
    SequenceStepExecutionFactory,
    UserFactory,
)


# Define fixtures for mock services
@pytest.fixture
def contact_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def email_account_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def template_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def email_quota_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def email_template_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def user_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def email_rendering_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def sequence_execution_service(
    contact_service: AsyncMock,
    email_account_service: AsyncMock,
    email_template_service: AsyncMock,
    user_service: AsyncMock,
    email_rendering_service: AsyncMock,
    seq_exec_repo: SequenceExecutionRepositoryForTest,
) -> SequenceExecutionService:
    # Initialize with db_engine instead of repository
    service = SequenceExecutionService(db_engine=seq_exec_repo.engine)

    # Replace service dependencies with mocks
    service._contact_service = contact_service
    service._email_account_service = email_account_service
    service._email_template_service = email_template_service
    service._user_service = user_service
    service._email_rendering_service = email_rendering_service

    return service


# Define fixtures for sequence execution query service
@pytest.fixture
def sequence_execution_query_service(
    seq_exec_repo: SequenceExecutionRepositoryForTest,
) -> SequenceExecutionQueryService:
    return SequenceExecutionQueryService(db_engine=seq_exec_repo.engine)


class TestSequenceExecutionService:
    async def test_create_scheduled_message_success(
        self,
        sequence_execution_service: SequenceExecutionService,
        contact_service: AsyncMock,
        email_account_service: AsyncMock,
        email_template_service: AsyncMock,
        email_quota_service: AsyncMock,
        user_service: AsyncMock,
        email_rendering_service: AsyncMock,
        user_factory: UserFactory,
        email_template_factory: EmailTemplateFactory,
        sequence_enrollment_factory: SequenceEnrollmentFactory,
        db_sequence_step_variant_factory: DbSequenceStepVariantFactory,
        sequence_step_execution_factory: SequenceStepExecutionFactory,
        email_account_factory: EmailAccountFactory,
        # quota_usage_factory: QuotaUsageFactory,
        seq_exec_repo: SequenceExecutionRepositoryForTest,
    ) -> None:
        organization_id = uuid4()

        # Create basic test data
        user = await seq_exec_repo.insert(
            user_factory.build(email=random_unique_email())
        )

        contact_email = random_unique_email()
        contact_id = uuid4()

        # Create a real email account in the database to satisfy the foreign key constraint
        email_account = await seq_exec_repo.insert(
            email_account_factory.build(
                organization_id=organization_id,
                owner_user_id=user.id,
                email=random_unique_email(),
                active=True,
            )
        )
        email_account_id = email_account.id

        # Create a template
        template = await seq_exec_repo.insert(
            email_template_factory.build(
                organization_id=organization_id,
                subject="Test Subject {{contact.first_name}}",
                body_html="<p>Hello {{contact.first_name}},</p><p>This is a test email.</p>",
                owner_user_id=user.id,
            )
        )

        # Create a sequence enrollment
        enrollment = await seq_exec_repo.insert(
            sequence_enrollment_factory.build(
                organization_id=organization_id,
                contact_id=contact_id,  # Just need the ID, not a real contact
                email_account_id=email_account_id,  # Now using a real email account ID
                enrolled_by_user_id=user.id,
                email=contact_email,
                status=SequenceEnrollmentStatus.ACTIVE,
            )
        )

        # Create a sequence step variant
        variant = await seq_exec_repo.insert(
            db_sequence_step_variant_factory.build(
                organization_id=organization_id,
                template_id=template.id,
                status=SequenceStepVariantStatus.ACTIVE,
            )
        )

        # Create a sequence step execution
        step_execution = await seq_exec_repo.insert(
            sequence_step_execution_factory.build(
                organization_id=organization_id,
                sequence_enrollment_id=enrollment.id,
                status=SequenceStepExecutionStatus.QUEUED,
            )
        )

        # Set the scheduled time for tomorrow
        scheduled_at = zoned_utc_now() + timedelta(days=1)

        # Setup mocks
        mock_contact = AsyncMock()
        mock_contact.first_name = "Test"
        mock_contact.id = contact_id
        mock_contact.primary_email = contact_email
        mock_contact.display_name = "Test User"
        contact_service.get_contact_v2.return_value = mock_contact

        # Create a proper email account mock with real values for database fields
        mock_email_account = AsyncMock()
        mock_email_account.id = email_account_id
        mock_email_account.email = "<EMAIL>"
        mock_email_account.owner_user_id = user.id  # Use the real user ID, not a mock
        mock_email_account.organization_id = (
            organization_id  # Set organization_id as well
        )
        mock_email_account.display_name = "Sender Name"  # Add display name
        email_account_service.get_email_account_by_id.return_value = mock_email_account

        # Mock the template service to return a template if needed
        mock_template = AsyncMock()
        mock_template.id = template.id
        mock_template.subject = template.subject
        mock_template.body_html = template.body_html
        mock_template.include_email_signature = True
        email_template_service.get_template.return_value = mock_template
        # Also mock get_email_template_by_id which is what the service actually calls
        email_template_service.get_email_template_by_id.return_value = mock_template

        # Mock user service to return the real user
        mock_user = AsyncMock()
        mock_user.id = user.id
        mock_user.email = user.email
        mock_user.first_name = "Test"
        mock_user.last_name = "User"
        mock_user.display_name = "Test User"  # Add display name for the user
        user_service.get_user_v2.return_value = mock_user

        # Mock email rendering service
        email_rendering_service.render_email_with_domain_models.return_value = (
            "Test Subject Test",
            "<p>Hello Test,</p><p>This is a test email.</p>",
            set(),  # Empty set for unresolved variables
        )
        with (
            mock.patch(
                "salestech_be.core.email.service.message_service.parse_main_body_text_and_persist",
                new_callable=mock.AsyncMock,
                return_value=None,
            ),
            mock.patch(
                "salestech_be.core.email.service.message_service.classify_message_and_update_metadata",
                new_callable=mock.AsyncMock,
                return_value=None,
            ),
        ):
            # Execute the method being tested
            (
                execution,
                updated_enrollment,
            ) = await sequence_execution_service.create_scheduled_message(
                organization_id=organization_id,
                step_execution_id=step_execution.id,
                enrollment=enrollment,
                variant=variant,
                scheduled_at=scheduled_at,
            )

            # Assertions for execution
            assert execution is not None
            assert execution.id == step_execution.id
            assert execution.global_message_id is not None
            assert execution.global_thread_id is not None

            # Assertions for enrollment
            assert updated_enrollment is not None
            assert updated_enrollment.id == enrollment.id

            # Verify the execution was updated in the database
            updated_execution = await seq_exec_repo.find_by_primary_key(
                SequenceStepExecution, id=step_execution.id
            )
            assert updated_execution is not None
            assert updated_execution.global_message_id is not None
            assert updated_execution.global_thread_id is not None

            # Also check that there's no error code on the execution
            assert updated_execution.error_code is None

            # Fetch and verify the global thread has the sequence_id
            thread_repository = ThreadRepository(engine=seq_exec_repo.engine)
            global_thread = await thread_repository.find_by_tenanted_primary_key(
                GlobalThread,
                id=updated_execution.global_thread_id,
                organization_id=organization_id,
            )

            # Verify the global thread has the correct sequence_id
            assert global_thread is not None
            assert global_thread.sequence_id is not None
            assert global_thread.sequence_id == enrollment.sequence_id

            # Verify the mocks were called
            contact_service.get_contact_v2.assert_called_once_with(
                organization_id=organization_id,
                contact_id=contact_id,
            )
            email_account_service.get_email_account_by_id.assert_called_once_with(
                organization_id=organization_id,
                email_account_id=email_account_id,
            )
            email_rendering_service.render_email_with_domain_models.assert_called_once_with(
                sender_email_account_id=email_account_id,
                recipient_contact_id=contact_id,
                pre_render_subject=template.subject,
                pre_render_body_html=template.body_html,
                include_email_signature=mock_template.include_email_signature,
                organization_id=organization_id,
                user_id=enrollment.enrolled_by_user_id,
            )

    async def test_create_scheduled_message_contact_not_found(
        self,
        sequence_execution_service: SequenceExecutionService,
        contact_service: AsyncMock,
        email_account_service: AsyncMock,
        email_template_service: AsyncMock,
        user_service: AsyncMock,
        email_rendering_service: AsyncMock,
        user_factory: UserFactory,
        email_template_factory: EmailTemplateFactory,
        sequence_enrollment_factory: SequenceEnrollmentFactory,
        db_sequence_step_variant_factory: DbSequenceStepVariantFactory,
        sequence_step_execution_factory: SequenceStepExecutionFactory,
        email_account_factory: EmailAccountFactory,
        seq_exec_repo: SequenceExecutionRepositoryForTest,
    ) -> None:
        organization_id = uuid4()

        # Create basic test data
        user = await seq_exec_repo.insert(
            user_factory.build(email=random_unique_email())
        )

        # Create a non-existent contact ID
        non_existent_contact_id = uuid4()

        # Create a real email account in the database to satisfy the foreign key constraint
        email_account = await seq_exec_repo.insert(
            email_account_factory.build(
                organization_id=organization_id,
                owner_user_id=user.id,
                email=random_unique_email(),
                active=True,
            )
        )
        email_account_id = email_account.id

        # Create a template
        template = await seq_exec_repo.insert(
            email_template_factory.build(
                organization_id=organization_id,
                subject="Test Subject",
                body_html="<p>Test Body</p>",
                owner_user_id=user.id,
            )
        )

        # Create a sequence enrollment with non-existent contact
        enrollment = await seq_exec_repo.insert(
            sequence_enrollment_factory.build(
                organization_id=organization_id,
                contact_id=non_existent_contact_id,  # This contact doesn't exist
                email_account_id=email_account_id,
                enrolled_by_user_id=user.id,
                email="<EMAIL>",
                status=SequenceEnrollmentStatus.ACTIVE,
            )
        )

        # Create a sequence step variant
        variant = await seq_exec_repo.insert(
            db_sequence_step_variant_factory.build(
                organization_id=organization_id,
                template_id=template.id,
                status=SequenceStepVariantStatus.ACTIVE,
            )
        )

        # Create a sequence step execution
        step_execution = await seq_exec_repo.insert(
            sequence_step_execution_factory.build(
                organization_id=organization_id,
                sequence_enrollment_id=enrollment.id,
                status=SequenceStepExecutionStatus.QUEUED,
            )
        )

        # Set the scheduled time for tomorrow
        scheduled_at = zoned_utc_now() + timedelta(days=1)

        # Setup mock to return None (contact not found)
        contact_service.get_contact_v2.return_value = None

        # Create a proper email account mock with real values
        mock_email_account = AsyncMock()
        mock_email_account.id = email_account_id
        mock_email_account.email = "<EMAIL>"
        mock_email_account.owner_user_id = user.id
        mock_email_account.organization_id = organization_id
        mock_email_account.display_name = "Sender Name"  # Add display name
        email_account_service.get_email_account_by_id.return_value = mock_email_account

        # Mock the template service
        mock_template = AsyncMock()
        mock_template.id = template.id
        mock_template.subject = template.subject
        mock_template.body_html = template.body_html
        mock_template.include_email_signature = True
        email_template_service.get_template.return_value = mock_template
        # Also mock get_email_template_by_id which is what the service actually calls
        email_template_service.get_email_template_by_id.return_value = mock_template

        # Mock user service
        mock_user = AsyncMock()
        mock_user.id = user.id
        mock_user.email = user.email
        mock_user.first_name = "Test"
        mock_user.last_name = "User"
        mock_user.display_name = "Test User"  # Add display name for the user
        user_service.get_user_v2.return_value = mock_user

        # Execute the method being tested - now handling the tuple return
        (
            execution,
            updated_enrollment,
        ) = await sequence_execution_service.create_scheduled_message(
            organization_id=organization_id,
            step_execution_id=step_execution.id,
            enrollment=enrollment,
            variant=variant,
            scheduled_at=scheduled_at,
        )

        # Assertions for both parts of the return value
        assert execution is not None
        assert execution.id == step_execution.id
        assert execution.error_code == SequenceErrorCode.CONTACT_NOT_EXIST
        assert execution.error_detail is not None
        assert str(non_existent_contact_id) in execution.error_detail

        assert updated_enrollment is not None
        assert updated_enrollment.id == enrollment.id

        # Verify the execution was updated with the error code in the database
        updated_execution = await seq_exec_repo.find_by_primary_key(
            SequenceStepExecution, id=step_execution.id
        )
        assert updated_execution is not None
        assert updated_execution.error_code == SequenceErrorCode.CONTACT_NOT_EXIST
        assert updated_execution.error_detail is not None
        assert str(non_existent_contact_id) in updated_execution.error_detail

        # Verify the mock was called
        contact_service.get_contact_v2.assert_called_once_with(
            organization_id=organization_id,
            contact_id=non_existent_contact_id,
        )

        # Since contact was not found, render_email_with_domain_models should not be called
        email_rendering_service.render_email_with_domain_models.assert_not_called()

    async def test_create_scheduled_message_template_not_found(
        self,
        sequence_execution_service: SequenceExecutionService,
        contact_service: AsyncMock,
        email_template_service: AsyncMock,
        email_account_service: AsyncMock,
        user_service: AsyncMock,
        email_rendering_service: AsyncMock,
        user_factory: UserFactory,
        sequence_enrollment_factory: SequenceEnrollmentFactory,
        db_sequence_step_variant_factory: DbSequenceStepVariantFactory,
        sequence_step_execution_factory: SequenceStepExecutionFactory,
        email_account_factory: EmailAccountFactory,
        seq_exec_repo: SequenceExecutionRepositoryForTest,
    ) -> None:
        # Create basic test data
        organization_id = uuid4()

        # Create basic test data
        user = await seq_exec_repo.insert(
            user_factory.build(email=random_unique_email())
        )

        contact_id = uuid4()

        # Create a real email account in the database to satisfy the foreign key constraint
        email_account = await seq_exec_repo.insert(
            email_account_factory.build(
                organization_id=organization_id,
                owner_user_id=user.id,
                email=random_unique_email(),
                active=True,
            )
        )
        email_account_id = email_account.id

        # Create a non-existent template ID
        non_existent_template_id = uuid4()

        # Create a sequence enrollment
        enrollment = await seq_exec_repo.insert(
            sequence_enrollment_factory.build(
                organization_id=organization_id,
                contact_id=contact_id,
                email_account_id=email_account_id,
                enrolled_by_user_id=user.id,
                email="<EMAIL>",
                status=SequenceEnrollmentStatus.ACTIVE,
            )
        )

        # Create a sequence step variant with non-existent template
        variant = await seq_exec_repo.insert(
            db_sequence_step_variant_factory.build(
                organization_id=organization_id,
                template_id=non_existent_template_id,  # This template doesn't exist
                status=SequenceStepVariantStatus.ACTIVE,
            )
        )

        # Create a sequence step execution
        step_execution = await seq_exec_repo.insert(
            sequence_step_execution_factory.build(
                organization_id=organization_id,
                sequence_enrollment_id=enrollment.id,
                status=SequenceStepExecutionStatus.QUEUED,
            )
        )

        # Set the scheduled time for tomorrow
        scheduled_at = zoned_utc_now() + timedelta(days=1)

        # Setup mocks
        mock_contact = AsyncMock()
        mock_contact.first_name = "Test"
        mock_contact.id = contact_id
        mock_contact.primary_email = "<EMAIL>"
        mock_contact.display_name = "Test User"
        contact_service.get_contact_v2.return_value = mock_contact

        # Create a proper email account mock with real values
        mock_email_account = AsyncMock()
        mock_email_account.id = email_account_id
        mock_email_account.email = "<EMAIL>"
        mock_email_account.owner_user_id = user.id
        mock_email_account.organization_id = organization_id
        mock_email_account.display_name = "Sender Name"  # Add display name
        email_account_service.get_email_account_by_id.return_value = mock_email_account

        # Mock template service to return None (template not found)
        email_template_service.get_template.return_value = None
        # Also mock get_email_template_by_id which is what the service actually calls
        email_template_service.get_email_template_by_id.return_value = None

        # Mock user service
        mock_user = AsyncMock()
        mock_user.id = user.id
        mock_user.email = user.email
        mock_user.first_name = "Test"
        mock_user.last_name = "User"
        mock_user.display_name = "Test User"  # Add display name for the user
        user_service.get_user_v2.return_value = mock_user

        # Execute the method being tested with tuple return type
        (
            execution,
            updated_enrollment,
        ) = await sequence_execution_service.create_scheduled_message(
            organization_id=organization_id,
            step_execution_id=step_execution.id,
            enrollment=enrollment,
            variant=variant,
            scheduled_at=scheduled_at,
        )

        # Assertions for both parts of the return value
        assert execution is not None
        assert execution.id == step_execution.id
        assert execution.error_code == SequenceErrorCode.TEMPLATE_RENDER_ERROR
        assert execution.error_detail is not None
        assert str(non_existent_template_id) in execution.error_detail

        assert updated_enrollment is not None
        assert updated_enrollment.id == enrollment.id

        # Verify the execution was updated with the error code in the database
        updated_execution = await seq_exec_repo.find_by_primary_key(
            SequenceStepExecution, id=step_execution.id
        )
        assert updated_execution is not None
        assert updated_execution.error_code == SequenceErrorCode.TEMPLATE_RENDER_ERROR
        assert updated_execution.error_detail is not None
        assert str(non_existent_template_id) in updated_execution.error_detail

        # Verify mocks were called
        contact_service.get_contact_v2.assert_called_once_with(
            organization_id=organization_id,
            contact_id=contact_id,
        )

    async def test_assign_step_to_active_variant_equal_counts(
        self,
        sequence_execution_service: SequenceExecutionService,
        seq_exec_repo: SequenceExecutionRepositoryForTest,
    ) -> None:
        """Test variant assignment when all variants have equal counts, demonstrating deterministic selection."""
        # Arrange
        organization_id = uuid4()
        sequence_step_id = uuid4()
        is_reassign = False

        # Mock sequence repository methods
        sequence_execution_service._sequence_step_repository = AsyncMock()
        sequence_execution_service._logger = AsyncMock()
        sequence_execution_service._logger.bind.return_value = (
            sequence_execution_service._logger
        )

        # Create three active variants, all with fixed IDs for deterministic test results
        variant_1_id = UUID("11111111-1111-1111-1111-111111111111")
        variant_2_id = UUID("*************-2222-2222-************")
        variant_3_id = UUID("*************-3333-3333-************")

        variant_1 = SequenceStepVariant(
            id=variant_1_id,
            sequence_step_id=sequence_step_id,
            name="Variant 1",
            organization_id=organization_id,
            status=SequenceStepVariantStatus.ACTIVE,
            created_by_user_id=uuid4(),
            created_at=zoned_utc_now(),
            content=EmailStepContent(
                type="email",
                subject="Test 1",
                body="Body 1",
                attachment_ids=[],
            ),
        )
        variant_2 = SequenceStepVariant(
            id=variant_2_id,
            sequence_step_id=sequence_step_id,
            name="Variant 2",
            organization_id=organization_id,
            status=SequenceStepVariantStatus.ACTIVE,
            created_by_user_id=uuid4(),
            created_at=zoned_utc_now(),
            content=EmailStepContent(
                type="email",
                subject="Test 2",
                body="Body 2",
                attachment_ids=[],
            ),
        )
        variant_3 = SequenceStepVariant(
            id=variant_3_id,
            sequence_step_id=sequence_step_id,
            name="Variant 3",
            organization_id=organization_id,
            status=SequenceStepVariantStatus.ACTIVE,
            created_by_user_id=uuid4(),
            created_at=zoned_utc_now(),
            content=EmailStepContent(
                type="email",
                subject="Test 3",
                body="Body 3",
                attachment_ids=[],
            ),
        )

        # Mock active variants
        sequence_execution_service._sequence_step_repository.list_active_sequence_step_variants.return_value = [
            variant_1,
            variant_2,
            variant_3,
        ]

        # Mock existing associations - all variants have exactly 2 associations
        sequence_execution_service._sequence_step_repository.find_enrollment_step_variant_associations_by_step_id.return_value = [
            SequenceEnrollmentStepVariantAssociation(
                id=uuid4(),
                sequence_enrollment_id=uuid4(),
                sequence_step_id=sequence_step_id,
                sequence_step_variant_id=variant_1_id,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
            ),
            SequenceEnrollmentStepVariantAssociation(
                id=uuid4(),
                sequence_enrollment_id=uuid4(),
                sequence_step_id=sequence_step_id,
                sequence_step_variant_id=variant_1_id,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
            ),
            SequenceEnrollmentStepVariantAssociation(
                id=uuid4(),
                sequence_enrollment_id=uuid4(),
                sequence_step_id=sequence_step_id,
                sequence_step_variant_id=variant_2_id,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
            ),
            SequenceEnrollmentStepVariantAssociation(
                id=uuid4(),
                sequence_enrollment_id=uuid4(),
                sequence_step_id=sequence_step_id,
                sequence_step_variant_id=variant_2_id,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
            ),
            SequenceEnrollmentStepVariantAssociation(
                id=uuid4(),
                sequence_enrollment_id=uuid4(),
                sequence_step_id=sequence_step_id,
                sequence_step_variant_id=variant_3_id,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
            ),
            SequenceEnrollmentStepVariantAssociation(
                id=uuid4(),
                sequence_enrollment_id=uuid4(),
                sequence_step_id=sequence_step_id,
                sequence_step_variant_id=variant_3_id,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
            ),
        ]

        # Use multiple enrollment IDs to demonstrate deterministic selection
        enrollment_id_1 = UUID("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa")
        enrollment_id_2 = UUID("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb")
        enrollment_id_3 = UUID("cccccccc-cccc-cccc-cccc-cccccccccccc")

        # Act - Run with first enrollment ID
        result_1 = await sequence_execution_service.assign_step_to_active_variant(
            organization_id=organization_id,
            sequence_enrollment_id=enrollment_id_1,
            sequence_step_id=sequence_step_id,
            is_reassign=is_reassign,
        )

        # Verify first insert call
        assert (
            sequence_execution_service._sequence_step_repository.insert.call_count == 1
        )
        sequence_execution_service._sequence_step_repository.insert.assert_called_once()

        # Reset mocks for second call
        sequence_execution_service._sequence_step_repository.insert.reset_mock()

        # Act - Run with second enrollment ID
        result_2 = await sequence_execution_service.assign_step_to_active_variant(
            organization_id=organization_id,
            sequence_enrollment_id=enrollment_id_2,
            sequence_step_id=sequence_step_id,
            is_reassign=is_reassign,
        )

        # Verify second insert call
        assert (
            sequence_execution_service._sequence_step_repository.insert.call_count == 1
        )
        sequence_execution_service._sequence_step_repository.insert.assert_called_once()

        # Reset mocks for third call
        sequence_execution_service._sequence_step_repository.insert.reset_mock()

        # Act - Run with third enrollment ID
        result_3 = await sequence_execution_service.assign_step_to_active_variant(
            organization_id=organization_id,
            sequence_enrollment_id=enrollment_id_3,
            sequence_step_id=sequence_step_id,
            is_reassign=is_reassign,
        )

        # Verify third insert call
        assert (
            sequence_execution_service._sequence_step_repository.insert.call_count == 1
        )
        sequence_execution_service._sequence_step_repository.insert.assert_called_once()

        # Assert - Each enrollment ID should deterministically map to a variant
        # The actual mapping depends on the hash implementation, but the key point
        # is that it's deterministic - running the test multiple times should yield
        # the same results for the same enrollment IDs

        # Verify that we got a result for each call
        assert result_1 is not None
        assert result_2 is not None
        assert result_3 is not None

        # Verify that the result IDs are valid variant IDs
        assert result_1.id in [variant_1_id, variant_2_id, variant_3_id]
        assert result_2.id in [variant_1_id, variant_2_id, variant_3_id]
        assert result_3.id in [variant_1_id, variant_2_id, variant_3_id]

        # We should have called insert exactly 3 times for the 3 different calls
        # Note: We're asserting after each call individually because we're resetting the mock in between
        # assert sequence_execution_service._sequence_step_repository.insert.call_count == 3

        # Check that each enrollment ID properly maps to its associated variant
        # and that the distribution is roughly even (over a large number of enrollments)
        # Note: This is a probabilistic test, but with fixed random seed and enrollment IDs
        # it should be deterministic

        # Finally, verify that multiple calls with the same enrollment ID return the same variant
        # Reset mocks
        sequence_execution_service._sequence_step_repository.insert.reset_mock()

        # Call again with first enrollment ID
        result_1_repeat = (
            await sequence_execution_service.assign_step_to_active_variant(
                organization_id=organization_id,
                sequence_enrollment_id=enrollment_id_1,
                sequence_step_id=sequence_step_id,
                is_reassign=is_reassign,
            )
        )

        # Verify we get the same variant as before
        assert result_1_repeat is not None
        assert result_1 is not None
        assert result_1_repeat.id == result_1.id

        # Verify insert was called again
        sequence_execution_service._sequence_step_repository.insert.assert_called_once()

    async def test_create_scheduled_message_with_reply_to_previous_thread(
        self,
        sequence_execution_service: SequenceExecutionService,
        contact_service: AsyncMock,
        email_account_service: AsyncMock,
        template_service: AsyncMock,
        user_service: AsyncMock,
        email_rendering_service: AsyncMock,
        user_factory: UserFactory,
        email_template_factory: EmailTemplateFactory,
        sequence_enrollment_factory: SequenceEnrollmentFactory,
        db_sequence_step_variant_factory: DbSequenceStepVariantFactory,
        sequence_step_execution_factory: SequenceStepExecutionFactory,
        email_account_factory: EmailAccountFactory,
        seq_exec_repo: SequenceExecutionRepositoryForTest,
    ) -> None:
        organization_id = uuid4()

        # create a user
        user = await seq_exec_repo.insert(
            user_factory.build(email=random_unique_email())
        )

        contact_email = random_unique_email()
        contact_id = uuid4()

        # create a email account
        email_account = await seq_exec_repo.insert(
            email_account_factory.build(
                organization_id=organization_id,
                owner_user_id=user.id,
                email=random_unique_email(),
                active=True,
            )
        )
        email_account_id = email_account.id

        # create a template
        template = await seq_exec_repo.insert(
            email_template_factory.build(
                organization_id=organization_id,
                subject="Test Subject {{contact.first_name}}",
                body_html="<p>Hello {{contact.first_name}},</p><p>This is a test email.</p>",
                owner_user_id=user.id,
            )
        )

        # create a sequence enrollment
        enrollment = await seq_exec_repo.insert(
            sequence_enrollment_factory.build(
                organization_id=organization_id,
                contact_id=contact_id,
                email_account_id=email_account_id,
                enrolled_by_user_id=user.id,
                email=contact_email,
                status=SequenceEnrollmentStatus.ACTIVE,
            )
        )

        # create a sequence step variant with reply_to_previous_thread=True
        variant = await seq_exec_repo.insert(
            db_sequence_step_variant_factory.build(
                organization_id=organization_id,
                template_id=template.id,
                status=SequenceStepVariantStatus.ACTIVE,
                reply_to_previous_thread=True,
            )
        )

        # create a sequence step execution
        step_execution = await seq_exec_repo.insert(
            sequence_step_execution_factory.build(
                organization_id=organization_id,
                sequence_enrollment_id=enrollment.id,
                status=SequenceStepExecutionStatus.QUEUED,
            )
        )

        # set the scheduled time for tomorrow
        scheduled_at = zoned_utc_now() + timedelta(days=1)

        # setup mock data and return values
        mock_contact = AsyncMock()
        mock_contact.first_name = "Test"
        mock_contact.id = contact_id
        mock_contact.primary_email = contact_email
        mock_contact.display_name = "Test User"
        contact_service.get_contact_v2.return_value = mock_contact

        mock_email_account = AsyncMock()
        mock_email_account.id = email_account_id
        mock_email_account.email = "<EMAIL>"
        mock_email_account.owner_user_id = user.id
        mock_email_account.organization_id = organization_id
        mock_email_account.display_name = "Sender Name"
        email_account_service.get_email_account_by_id.return_value = mock_email_account

        mock_template = AsyncMock()
        mock_template.id = template.id
        mock_template.subject = template.subject
        mock_template.body_html = template.body_html
        mock_template.attachment_ids = []
        template_service.get_email_template_by_id.return_value = mock_template

        mock_user = AsyncMock()
        mock_user.id = user.id
        mock_user.email = user.email
        mock_user.first_name = "Test"
        mock_user.last_name = "User"
        mock_user.display_name = "Test User"
        user_service.get_user_v2.return_value = mock_user

        email_rendering_service.render_email_with_domain_models.return_value = (
            "Test Subject Test",
            "<p>Hello Test,</p><p>This is a test email.</p>",
            set(),  # empty set means no unresolved variables
        )

        # mock the previous step execution record
        previous_global_message_id = uuid4()
        previous_step_execution = AsyncMock()
        previous_step_execution.global_message_id = previous_global_message_id

        # mock the message id
        expected_message_id = uuid4()

        # prepare the expected global message id and thread id
        global_message_id = uuid4()
        global_thread_id = uuid4()

        # create a clone of step_execution with updated fields for the return value
        updated_step_execution = SequenceStepExecution(
            id=step_execution.id,
            organization_id=organization_id,
            sequence_enrollment_id=enrollment.id,
            status=SequenceStepExecutionStatus.QUEUED,
            global_message_id=global_message_id,
            global_thread_id=global_thread_id,
            created_at=step_execution.created_at,
            # Add any other required fields from step_execution
            sequence_id=step_execution.sequence_id,
            sequence_step_id=step_execution.sequence_step_id,
            sequence_step_variant_id=step_execution.sequence_step_variant_id,
        )

        # create mock execution repository and thread service
        mock_execution_repo = AsyncMock()
        mock_execution_repo.find_sequence_step_executions_by_enrollment_id.return_value = [
            previous_step_execution
        ]
        # Set the return value for update_by_tenanted_primary_key
        mock_execution_repo.update_by_tenanted_primary_key.return_value = (
            updated_step_execution
        )

        mock_thread_service = AsyncMock()
        mock_thread_service.get_message_id_by_global_message_id.return_value = (
            expected_message_id
        )

        # replace services with mocks
        sequence_execution_service._execution_repository = mock_execution_repo
        sequence_execution_service._thread_service_ext = mock_thread_service

        # mock the send message request parameters
        message_dto = AsyncMock()
        message_dto.global_message_mapping = {global_message_id: "fake-message-id"}
        message_dto.global_thread_id = global_thread_id

        mock_message_service = AsyncMock()
        mock_message_service.create_message_and_thread.return_value = (
            message_dto,
            None,
        )
        sequence_execution_service._message_service = mock_message_service

        # execute the method being tested with tuple return type
        (
            execution,
            updated_enrollment,
        ) = await sequence_execution_service.create_scheduled_message(
            organization_id=organization_id,
            step_execution_id=step_execution.id,
            enrollment=enrollment,
            variant=variant,
            scheduled_at=scheduled_at,
        )

        # assert the execution result
        assert execution is not None
        assert execution.id == step_execution.id
        assert execution.global_message_id == global_message_id
        assert execution.global_thread_id == global_thread_id

        # assert the enrollment result
        assert updated_enrollment is not None
        assert updated_enrollment.id == enrollment.id

        # assert the call parameters
        mock_thread_service.get_message_id_by_global_message_id.assert_called_once_with(
            global_message_id=previous_global_message_id,
            organization_id=organization_id,
        )

        # assert the create message call parameters
        create_message_call_args = (
            mock_message_service.create_message_and_thread.call_args
        )
        assert create_message_call_args is not None

        send_message_request = create_message_call_args[1]["send_message_request"]
        assert send_message_request is not None
        assert send_message_request.reply_to_message_id == expected_message_id

    async def test_create_draft_message(
        self,
        sequence_execution_service: SequenceExecutionService,
        contact_service: AsyncMock,
        email_account_service: AsyncMock,
        template_service: AsyncMock,
        user_service: AsyncMock,
        email_rendering_service: AsyncMock,
        user_factory: UserFactory,
        email_template_factory: EmailTemplateFactory,
        sequence_enrollment_factory: SequenceEnrollmentFactory,
        db_sequence_step_variant_factory: DbSequenceStepVariantFactory,
        sequence_step_execution_factory: SequenceStepExecutionFactory,
        email_account_factory: EmailAccountFactory,
        seq_exec_repo: SequenceExecutionRepositoryForTest,
    ) -> None:
        # setup test data
        organization_id = uuid4()

        # create a user
        user = await seq_exec_repo.insert(
            user_factory.build(email=random_unique_email())
        )

        contact_email = random_unique_email()
        contact_id = uuid4()

        # create an email account
        email_account = await seq_exec_repo.insert(
            email_account_factory.build(
                organization_id=organization_id,
                owner_user_id=user.id,
                email=random_unique_email(),
                active=True,
            )
        )
        email_account_id = email_account.id

        # create a template
        template = await seq_exec_repo.insert(
            email_template_factory.build(
                organization_id=organization_id,
                subject="Test Subject {{contact.first_name}}",
                body_html="<p>Hello {{contact.first_name}},</p><p>This is a test email.</p>",
                owner_user_id=user.id,
            )
        )

        # create a sequence enrollment
        enrollment = await seq_exec_repo.insert(
            sequence_enrollment_factory.build(
                organization_id=organization_id,
                contact_id=contact_id,
                email_account_id=email_account_id,
                enrolled_by_user_id=user.id,
                email=contact_email,
                status=SequenceEnrollmentStatus.ACTIVE,
            )
        )

        # create a sequence step variant
        variant = await seq_exec_repo.insert(
            db_sequence_step_variant_factory.build(
                organization_id=organization_id,
                template_id=template.id,
                status=SequenceStepVariantStatus.ACTIVE,
            )
        )

        # create a sequence step execution
        step_execution = await seq_exec_repo.insert(
            sequence_step_execution_factory.build(
                organization_id=organization_id,
                sequence_enrollment_id=enrollment.id,
                status=SequenceStepExecutionStatus.QUEUED,
            )
        )

        # setup mock data and return values
        mock_contact = AsyncMock()
        mock_contact.first_name = "Test"
        mock_contact.id = contact_id
        mock_contact.primary_email = contact_email
        mock_contact.display_name = "Test User"
        contact_service.get_contact_v2.return_value = mock_contact

        mock_email_account = AsyncMock()
        mock_email_account.id = email_account_id
        mock_email_account.email = "<EMAIL>"
        mock_email_account.owner_user_id = user.id
        mock_email_account.organization_id = organization_id
        mock_email_account.display_name = "Sender Name"
        email_account_service.get_email_account_by_id.return_value = mock_email_account

        mock_template = AsyncMock()
        mock_template.id = template.id
        mock_template.subject = template.subject
        mock_template.body_html = template.body_html
        mock_template.attachment_ids = []
        mock_template.include_email_signature = True
        template_service.get_email_template_by_id.return_value = mock_template

        mock_user = AsyncMock()
        mock_user.id = user.id
        mock_user.email = user.email
        mock_user.first_name = "Test"
        mock_user.last_name = "User"
        mock_user.display_name = "Test User"
        user_service.get_user_v2.return_value = mock_user

        email_rendering_service.render_email_with_domain_models.return_value = (
            "Test Subject Test",
            "<p>Hello Test,</p><p>This is a test email.</p>",
            set(),  # empty set means no unresolved variables
        )

        # prepare the expected global message id and thread id
        global_message_id = uuid4()
        global_thread_id = uuid4()

        # create a clone of step_execution with updated fields for the return value
        updated_step_execution = SequenceStepExecution(
            id=step_execution.id,
            organization_id=organization_id,
            sequence_enrollment_id=enrollment.id,
            status=SequenceStepExecutionStatus.QUEUED,
            global_message_id=global_message_id,
            global_thread_id=global_thread_id,
            created_at=step_execution.created_at,
            # Add any other required fields from step_execution
            sequence_id=step_execution.sequence_id,
            sequence_step_id=step_execution.sequence_step_id,
            sequence_step_variant_id=step_execution.sequence_step_variant_id,
        )

        # Setup the mock message service
        mock_message_service = AsyncMock()
        message_dto = AsyncMock()
        message_dto.global_message_mapping = {global_message_id: "fake-message-id"}
        message_dto.global_thread_id = global_thread_id
        mock_message_service.create_message_and_thread.return_value = (
            message_dto,
            None,
        )
        sequence_execution_service._message_service = mock_message_service

        # Setup the mock execution repository
        mock_execution_repo = AsyncMock()
        mock_execution_repo.update_by_tenanted_primary_key.return_value = (
            updated_step_execution
        )
        sequence_execution_service._execution_repository = mock_execution_repo

        # Execute the create_draft_message function
        (
            execution,
            updated_enrollment,
        ) = await sequence_execution_service.create_draft_message(
            organization_id=organization_id,
            step_execution_id=step_execution.id,
            enrollment=enrollment,
            variant=variant,
        )

        # Assert the results
        assert execution is not None
        assert execution.id == step_execution.id
        assert execution.global_message_id == global_message_id
        assert execution.global_thread_id == global_thread_id

        # Assert the enrollment result
        assert updated_enrollment is not None
        assert updated_enrollment.id == enrollment.id

        # Verify create_message_and_thread was called with use_draft=True
        create_message_call_args = (
            mock_message_service.create_message_and_thread.call_args
        )
        assert create_message_call_args is not None

        send_message_request = create_message_call_args[1]["send_message_request"]
        assert send_message_request is not None
        assert send_message_request.use_draft is True

        assert send_message_request.send_at is None

        # Verify step execution was updated with global message and thread IDs
        update_call_args = mock_execution_repo.update_by_tenanted_primary_key.call_args
        assert update_call_args is not None

        update_kwargs = update_call_args[1]
        assert update_kwargs["organization_id"] == organization_id
        assert update_kwargs["primary_key_to_value"]["id"] == step_execution.id

        column_to_update = update_kwargs["column_to_update"]
        assert column_to_update["global_thread_id"] == global_thread_id
        assert column_to_update["global_message_id"] == global_message_id
        assert (
            column_to_update["result_entity_type"]
            == SequenceStepExecutionResultEntityType.GLOBAL_MESSAGE
        )
        assert column_to_update["result_entity_id"] == global_message_id

    async def test_move_enrollment_to_next_step(
        self,
        sequence_execution_service: SequenceExecutionService,
        seq_exec_repo: SequenceStepRepository,
    ) -> None:
        # Arrange
        organization_id = uuid4()
        sequence_id = uuid4()

        # Create a sequence enrollment
        user_id = uuid4()
        enrollment = await seq_exec_repo.insert(
            SequenceEnrollment(
                id=uuid4(),
                organization_id=organization_id,
                sequence_id=sequence_id,
                contact_id=uuid4(),
                email="<EMAIL>",
                status=SequenceEnrollmentStatus.ACTIVE,
                current_step_id=None,  # Will be set in tests
                enrolled_at=zoned_utc_now(),
                enrolled_by_user_id=user_id,
                updated_at=zoned_utc_now(),
            )
        )

        # Create a sequence with 3 steps in a sequence: step1 -> step2 -> step3
        step1_id = uuid4()
        step2_id = uuid4()
        step3_id = uuid4()

        step1 = await seq_exec_repo.insert(  # noqa: F841
            SequenceStepV2(
                id=step1_id,
                organization_id=organization_id,
                sequence_id=sequence_id,
                name="Step 1",
                is_first_step=True,
                next_step_id=step2_id,
                type=SequenceStepType.AUTO_EMAIL,
                delay_minutes=60,
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                support_ab_test=True,
            )
        )

        step2 = await seq_exec_repo.insert(  # noqa: F841
            SequenceStepV2(
                id=step2_id,
                organization_id=organization_id,
                sequence_id=sequence_id,
                name="Step 2",
                is_first_step=False,
                next_step_id=step3_id,
                type=SequenceStepType.AUTO_EMAIL,
                delay_minutes=60,
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                support_ab_test=True,
            )
        )

        step3 = await seq_exec_repo.insert(  # noqa: F841
            SequenceStepV2(
                id=step3_id,
                organization_id=organization_id,
                sequence_id=sequence_id,
                name="Step 3",
                is_first_step=False,
                next_step_id=None,
                type=SequenceStepType.AUTO_EMAIL,
                delay_minutes=60,
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                support_ab_test=True,
            )
        )

        # Create email content for variants
        email_content = EmailStepContent(
            type="email",
            subject="Test Subject",
            body="Test body content",
            attachment_ids=[],
        )

        # Create an active variant for step1
        variant1 = await seq_exec_repo.insert(  # noqa: F841
            SequenceStepVariant(
                id=uuid4(),
                organization_id=organization_id,
                sequence_step_id=step1_id,
                status=SequenceStepVariantStatus.ACTIVE,
                name="Variant 1",
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                content=email_content,
            )
        )

        # Create an inactive variant for step2 (for testing step with no active variants)
        variant2 = await seq_exec_repo.insert(
            SequenceStepVariant(
                id=uuid4(),
                organization_id=organization_id,
                sequence_step_id=step2_id,
                status=SequenceStepVariantStatus.INACTIVE,
                name="Variant 2",
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                content=email_content,
            )
        )

        # Create an active variant for step3
        variant3 = await seq_exec_repo.insert(  # noqa: F841
            SequenceStepVariant(
                id=uuid4(),
                organization_id=organization_id,
                sequence_step_id=step3_id,
                status=SequenceStepVariantStatus.ACTIVE,
                name="Variant 3",
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                content=email_content,
            )
        )

        # Test Case 1: Moving to a step with active variants
        # Set current step to null and move to step1 (which has active variants)
        updated_enrollment = (
            await sequence_execution_service.move_enrollment_to_next_step(
                enrollment_id=enrollment.id,
                next_step_id=step1_id,
                organization_id=organization_id,
            )
        )

        # Verify the enrollment was updated to step1
        assert updated_enrollment.current_step_id == step1_id

        # Test Case 2: Moving to a step without active variants but with a next step
        # It should skip step2 (no active variants) and move to step3
        updated_enrollment = (
            await sequence_execution_service.move_enrollment_to_next_step(
                enrollment_id=enrollment.id,
                next_step_id=step2_id,
                organization_id=organization_id,
            )
        )

        # Verify the enrollment skipped step2 and went to step3
        assert updated_enrollment.current_step_id == step3_id

        # Test Case 3: Moving to null (end of sequence)
        updated_enrollment = (
            await sequence_execution_service.move_enrollment_to_next_step(
                enrollment_id=enrollment.id,
                next_step_id=None,
                organization_id=organization_id,
            )
        )

        # Verify the enrollment was updated to null (end of sequence)
        assert updated_enrollment.current_step_id is None

        # Test Case 4: Activate the inactive variant and verify direct movement
        # First activate variant2
        await seq_exec_repo.update_by_tenanted_primary_key(
            SequenceStepVariant,
            primary_key_to_value={"id": variant2.id},
            organization_id=organization_id,
            column_to_update={
                "status": SequenceStepVariantStatus.ACTIVE,
            },
        )

        # Now move to step2 again - this time it should work
        updated_enrollment = (
            await sequence_execution_service.move_enrollment_to_next_step(
                enrollment_id=enrollment.id,
                next_step_id=step2_id,
                organization_id=organization_id,
            )
        )

        # Verify the enrollment now moved to step2 directly
        assert updated_enrollment.current_step_id == step2_id


class TestSequenceExecutionQueryService:
    async def test_map_sequence_step_executions_by_ids(
        self,
        sequence_execution_query_service: SequenceExecutionQueryService,
        sequence_step_execution_factory: SequenceStepExecutionFactory,
        seq_exec_repo: SequenceExecutionRepositoryForTest,
    ) -> None:
        # Create test data
        organization_id = uuid4()

        # Create three sequence step executions
        executions = []
        for _ in range(3):
            execution = await seq_exec_repo.insert(
                sequence_step_execution_factory.build(
                    organization_id=organization_id,
                    status=SequenceStepExecutionStatus.QUEUED,
                )
            )
            executions.append(execution)

        # Extract IDs
        execution_ids = [execution.id for execution in executions]
        assert len(execution_ids) == 3

        # Call the method being tested
        result = (
            await sequence_execution_query_service.map_sequence_step_executions_by_ids(
                organization_id=organization_id,
                sequence_step_execution_ids=execution_ids,
            )
        )

        # Assertions
        assert isinstance(result, dict)
        assert len(result) == 3

        # Verify each execution is in the result dict and correctly mapped by ID
        for execution in executions:
            assert execution.id in result
            mapped_execution = result[execution.id]
            assert mapped_execution.id == execution.id
            assert mapped_execution.organization_id == organization_id
            assert mapped_execution.status == execution.status
            assert (
                mapped_execution.sequence_enrollment_id
                == execution.sequence_enrollment_id
            )
            assert mapped_execution.sequence_step_id == execution.sequence_step_id
            assert (
                mapped_execution.sequence_step_variant_id
                == execution.sequence_step_variant_id
            )

    async def test_list_sequence_step_executions_by_ids(
        self,
        sequence_execution_query_service: SequenceExecutionQueryService,
        sequence_step_execution_factory: SequenceStepExecutionFactory,
        seq_exec_repo: SequenceExecutionRepositoryForTest,
    ) -> None:
        # Create test data
        organization_id = uuid4()

        # Create multiple sequence step executions
        executions = []
        for _ in range(3):
            execution = await seq_exec_repo.insert(
                sequence_step_execution_factory.build(
                    organization_id=organization_id,
                    status=SequenceStepExecutionStatus.QUEUED,
                )
            )
            executions.append(execution)

        # Extract IDs
        execution_ids = [execution.id for execution in executions]
        assert len(execution_ids) == 3

        # Create an execution that should not be returned (different organization)
        different_org_id = uuid4()
        different_org_execution = await seq_exec_repo.insert(
            sequence_step_execution_factory.build(
                organization_id=different_org_id,
                status=SequenceStepExecutionStatus.QUEUED,
            )
        )

        # Call the method being tested
        result = (
            await sequence_execution_query_service.list_sequence_step_executions_by_ids(
                organization_id=organization_id,
                sequence_step_execution_ids=execution_ids,
            )
        )

        # Assertions
        assert isinstance(result, list)
        assert len(result) == 3  # All 3 executions should be returned

        # Verify all expected executions are in the result
        result_ids = {execution.id for execution in result}
        expected_ids = {execution.id for execution in executions}
        assert result_ids == expected_ids

        # Verify the different organization execution is not in the result
        assert different_org_execution.id not in result_ids

        # Verify each returned execution has the correct type and data
        for ex in result:
            assert ex.organization_id == organization_id
            assert ex.status == SequenceStepExecutionStatus.QUEUED

        # Test with a mix of valid and invalid IDs
        non_existent_id = uuid4()
        mixed_ids = [*execution_ids, non_existent_id]

        mixed_result = (
            await sequence_execution_query_service.list_sequence_step_executions_by_ids(
                organization_id=organization_id,
                sequence_step_execution_ids=mixed_ids,
            )
        )

        # Should only return the valid executions
        assert len(mixed_result) == 3
        mixed_result_ids = {execution.id for execution in mixed_result}
        assert mixed_result_ids == expected_ids
        assert non_existent_id not in mixed_result_ids

        # Test with empty list
        empty_result = (
            await sequence_execution_query_service.list_sequence_step_executions_by_ids(
                organization_id=organization_id,
                sequence_step_execution_ids=[],
            )
        )

        assert isinstance(empty_result, list)
        assert len(empty_result) == 0
