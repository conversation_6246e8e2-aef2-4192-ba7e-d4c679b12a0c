import asyncio
from collections.abc import Awaitable, Callable
from uuid import UUID, uuid4

import pytest
from faker import Faker

from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.custom_object_repository import CustomObjectRepository
from salestech_be.falkordb.cdc_events.cdc_event_processor import CDCEventProcessor
from salestech_be.falkordb.cdc_events.domain_mapper import DomainMapper
from salestech_be.falkordb.cdc_events.index_event_processor import IndexEventProcessor
from salestech_be.falkordb.cdc_events.index_event_producer import IndexEventProducer
from salestech_be.falkordb.falkordb_client import FalkorDBClient
from salestech_be.falkordb.indexing_lib import FalkorDBIndexingLib
from salestech_be.integrations.kafka.kafka_consumer import KafkaConsumer
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


@pytest.fixture(scope="class")
async def user_id() -> UUID:
    return uuid4()


@pytest.fixture(scope="class")
async def organization_id() -> UUID:
    return uuid4()


@pytest.fixture(scope="class")
async def make_account(
    faker: Faker,
    account_service: AccountService,
) -> Callable[[UUID, UUID], Awaitable[AccountV2]]:
    async def _make_account(
        organization_id: UUID,
        user_id: UUID,
    ) -> AccountV2:
        """Create test account."""
        account = await account_service.create_account_v2(
            organization_id=organization_id,
            user_id=user_id,
            create_account_request=CreateAccountRequest(
                display_name=faker.name(),
                owner_user_id=user_id,
                official_website=faker.uri(),
            ),
        )

        list_result = await account_service.list_accounts_v2(
            organization_id=organization_id, only_include_account_ids={account.id}
        )

        return list_result[0]

    return _make_account


class TestCDCEventProcessorIntegration:
    """
    Integration test for the CDC event processor.

    This test requires:
    1. A running Kafka instance
    2. A running Postgres instance with appropriate tables
    3. A configured Debezium connector watching Postgres
    4. A running FalkorDB instance

    The test will:
    1. Insert test data into Postgres
    2. Let Debezium capture the changes and produce events to Kafka
    3. Verify that the CDCEventProcessor correctly processes these events
    """

    async def test_cdc_event_processor_with_account_insert(
        self,
        falkordb_client: FalkorDBClient,
        falkordb_indexing_lib: FalkorDBIndexingLib,
        account_repository: AccountRepository,
        contact_repository: ContactRepository,
        custom_object_repo: CustomObjectRepository,
        make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
        make_account: Callable[[UUID, UUID], Awaitable[AccountV2]],
    ) -> None:
        """
        Test that the CDC event processor correctly processes an account insert event.

        The test:
        1. Start the CDC event consumer task which processes changes and writes to falkor
        2. Insert a test account into the database
        3. Wait and assert that the account is written to falkor
        """

        cdc_topic = "debezium.salestech_be.change_events"
        index_topic = "testing_index_topic"
        group_id = "test_cdc_event_consumer"

        domain_mapper = DomainMapper(
            account_repository=account_repository,
            contact_repository=contact_repository,
            custom_object_repository=custom_object_repo,
        )

        cdc_processor = CDCEventProcessor(
            domain_mapper=domain_mapper,
            index_producer=IndexEventProducer(topic=index_topic),
        )

        cdc_consumer = KafkaConsumer(
            topic=cdc_topic,
            group_id=group_id,
            processor=cdc_processor,
        )

        index_processor = IndexEventProcessor(
            falkor_indexing_lib=falkordb_indexing_lib,
        )

        index_consumer = KafkaConsumer(
            topic=index_topic,
            group_id=group_id,
            processor=index_processor,
        )

        async with asyncio.TaskGroup() as tg:
            tg.create_task(cdc_consumer.run_until_stopped())
            tg.create_task(index_consumer.run_until_stopped())

            user_id, organization_id = await make_user_org()

            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)
            logger.info(f"graph: {graph}")

            test_account = await make_account(organization_id, user_id)

            time_between_checks = 1.0
            test_timeout = 60
            try:
                async with asyncio.timeout(test_timeout):
                    while True:
                        result = await graph.query(
                            "MATCH (n:account {id: $id}) RETURN n",
                            params={"id": str(test_account.id)},
                        )
                        result_size = len(result.result_set)
                        if result_size:
                            account_node = result.result_set[0][0]
                            assert account_node.properties["id"] == str(test_account.id)
                            assert (
                                account_node.properties["display_name"]
                                == test_account.display_name
                            )
                            assert account_node.properties["organization_id"] == str(
                                organization_id
                            )
                            assert account_node.properties["status"] is not None
                            assert account_node.properties["access_status"] is not None
                            break

                        await asyncio.sleep(time_between_checks)
            except TimeoutError:
                raise AssertionError("Account was not indexed in time")
