import uuid
from collections.abc import AsyncGenerator
from uuid import UUID, uuid4

import pytest
from sqlalchemy import text

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdSelectListIdentifier,
)
from salestech_be.common.type.metadata.common import ObjectAccessStatus
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.metadata.dto.select_list_dto import PipelineStageDto
from salestech_be.core.metadata.dto.service_api_schema import (
    PipelineStageSelectListCreateRequest,
    PipelineStageSelectListValueCreateRequest,
    SelectListValueCreateRequest,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.metadata.service.pipeline_stage_service import (
    PipelineStageSelectListService,
)
from salestech_be.core.pipeline.service.pipeline_service import PipelineService
from salestech_be.core.pipeline.service_api_schema import (
    ContactPipelineAssociationRequest,
    CreatePipelineRequest,
    FullContactPipelineAssociationRequests,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.user.utils import default_roles
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.contact import (
    CreateContactAccountRoleRequest,
    CreateContactRequest,
    CreateDbContactEmailRequest,
    CreateDbContactRequest,
)
from salestech_be.db.models.pipeline import PipelineStatus
from salestech_be.db.models.pipeline_stage_select_list_value_metadata import (
    PipelineOutcomeState,
)
from salestech_be.falkordb.falkordb_client import FalkorDBClient
from salestech_be.falkordb.indexing_lib import FalkorDBIndexingLib
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now

logger = get_logger()


@pytest.fixture(scope="class")
def organization_id() -> UUID:
    """Test organization ID."""
    return uuid4()


@pytest.fixture(scope="class")
def user_id() -> UUID:
    """Test user ID."""
    return uuid4()


@pytest.fixture(scope="class")
def user_id2() -> UUID:
    """Test user ID 2."""
    return uuid4()


@pytest.fixture(scope="class")
def user_id3() -> UUID:
    """Test user ID 3."""
    return uuid4()


@pytest.fixture(scope="class")
async def primary_email() -> str:
    """Test primary email."""
    return f"test-{uuid.uuid4()}@example.com"


@pytest.fixture(scope="class")
async def primary_email2() -> str:
    """Test primary email 2."""
    return f"test-{uuid.uuid4()}@example.com"


@pytest.fixture(scope="class")
async def primary_email3() -> str:
    """Test primary email 3."""
    return f"test-{uuid.uuid4()}@example.com"


@pytest.fixture(scope="class")
async def _organization(
    _engine: DatabaseEngine,
    organization_id: UUID,
    user_id: UUID,
) -> None:
    """Create test organization."""
    await _engine.execute(
        text("""
            INSERT INTO organization (
                id, display_name, created_at, created_by_user_id
            ) VALUES (
                :id, :display_name, :created_at, :created_by_user_id
            )
        """),
        {
            "id": organization_id,
            "display_name": "Test Organization",
            "created_at": zoned_utc_now(),
            "created_by_user_id": user_id,
        },
    )


@pytest.fixture(scope="class")
async def _user(
    _engine: DatabaseEngine,
    user_id: UUID,
    primary_email: str,
) -> None:
    """Create test user."""
    try:
        await _engine.execute(
            text("""
                INSERT INTO "user" (id, email, first_name, last_name, created_at)
                VALUES (:id, :email, :first_name, :last_name, :created_at)
                ON CONFLICT (id) DO NOTHING
            """),
            {
                "id": user_id,
                "email": primary_email,
                "first_name": "Test",
                "last_name": "User",
                "created_at": zoned_utc_now(),
            },
        )
    except Exception as e:
        logger.info(f"Error creating user: {e}")


@pytest.fixture(scope="class")
async def make_user_org(
    user_repository: UserRepository,
    select_list_service: InternalSelectListService,
    pipeline_stage_select_list_service: PipelineStageSelectListService,
    user_id: UUID,
    organization_id: UUID,
    _organization: None,
    _user: None,
) -> None:
    result = await user_repository.associate_user_to_an_organization(
        user_id=user_id,
        organization_id=organization_id,
        roles=default_roles(),
    )
    logger.info(f"result: {result}")

    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id, user_id=user_id
    )

    pipeline_stage_select_list = await pipeline_stage_select_list_service.bootstrap_organization_default_pipeline_stage_select_list(
        organization_id=organization_id, actioning_user_id=user_id
    )
    logger.info(f"pipeline_stage_select_list: {pipeline_stage_select_list}")


@pytest.fixture(scope="class")
async def account(
    account_service: AccountService,
    organization_id: UUID,
    user_id: UUID,
) -> AccountV2:
    """Create test account."""
    # Create the initial request data
    request_data = CreateAccountRequest(
        display_name="Test Account",
        owner_user_id=user_id,
        official_website="https://example.com",
    )

    # Create the account using the service
    created_account_ref = await account_service.create_account_v2(
        organization_id=organization_id,
        user_id=user_id,
        create_account_request=request_data,
    )

    # Fetch the created account data from the database or service
    # This ensures we get the final state including defaults set by DB/service
    fetched_account_list = await account_service.list_accounts_v2(
        organization_id=organization_id,
        only_include_account_ids={created_account_ref.id},
    )
    if not fetched_account_list:
        raise ValueError("Failed to fetch created account")
    fetched_account = fetched_account_list[0]

    # Prepare the data dictionary for re-instantiation, ensuring defaults
    account_data = fetched_account.model_dump()
    account_data["access_status"] = (
        account_data.get("access_status") or ObjectAccessStatus.ACTIVE
    )
    account_data["participant_user_id_list"] = (
        account_data.get("participant_user_id_list") or []
    )
    account_data["research_reference_urls"] = (
        account_data.get("research_reference_urls") or []
    )
    account_data["updated_at"] = (
        account_data.get("updated_at") or account_data["created_at"]
    )

    # Re-instantiate the model with the complete data
    return AccountV2(**account_data)


@pytest.fixture(scope="class")
async def account2(
    account_service: AccountService,
    organization_id: UUID,
    user_id2: UUID,
) -> AccountV2:
    """Create test account."""
    # Create the initial request data
    request_data = CreateAccountRequest(
        display_name="Test Account 2",
        owner_user_id=user_id2,
        official_website="https://example2.com",
    )

    # Create the account using the service
    created_account_ref = await account_service.create_account_v2(
        organization_id=organization_id,
        user_id=user_id2,
        create_account_request=request_data,
    )

    # Fetch the created account data from the database or service
    # This ensures we get the final state including defaults set by DB/service
    fetched_account_list = await account_service.list_accounts_v2(
        organization_id=organization_id,
        only_include_account_ids={created_account_ref.id},
    )
    if not fetched_account_list:
        raise ValueError("Failed to fetch created account")
    fetched_account = fetched_account_list[0]

    # Prepare the data dictionary for re-instantiation, ensuring defaults
    account_data = fetched_account.model_dump()
    account_data["access_status"] = (
        account_data.get("access_status") or ObjectAccessStatus.ACTIVE
    )
    account_data["participant_user_id_list"] = (
        account_data.get("participant_user_id_list") or []
    )
    account_data["research_reference_urls"] = (
        account_data.get("research_reference_urls") or []
    )
    account_data["updated_at"] = (
        account_data.get("updated_at") or account_data["created_at"]
    )

    # Re-instantiate the model with the complete data
    return AccountV2(**account_data)


@pytest.fixture(scope="class")
async def account3(
    account_service: AccountService,
    organization_id: UUID,
    user_id3: UUID,
) -> AccountV2:
    """Create test account."""
    # Create the initial request data
    request_data = CreateAccountRequest(
        display_name="Test Account 3",
        owner_user_id=user_id3,
        official_website="https://example3.com",
    )

    # Create the account using the service
    created_account_ref = await account_service.create_account_v2(
        organization_id=organization_id,
        user_id=user_id3,
        create_account_request=request_data,
    )

    # Fetch the created account data from the database or service
    # This ensures we get the final state including defaults set by DB/service
    fetched_account_list = await account_service.list_accounts_v2(
        organization_id=organization_id,
        only_include_account_ids={created_account_ref.id},
    )
    if not fetched_account_list:
        raise ValueError("Failed to fetch created account")
    fetched_account = fetched_account_list[0]

    # Prepare the data dictionary for re-instantiation, ensuring defaults
    account_data = fetched_account.model_dump()
    account_data["access_status"] = (
        account_data.get("access_status") or ObjectAccessStatus.ACTIVE
    )
    account_data["participant_user_id_list"] = (
        account_data.get("participant_user_id_list") or []
    )
    account_data["research_reference_urls"] = (
        account_data.get("research_reference_urls") or []
    )
    account_data["updated_at"] = (
        account_data.get("updated_at") or account_data["created_at"]
    )

    # Re-instantiate the model with the complete data
    return AccountV2(**account_data)


@pytest.fixture(scope="class")
async def contact(
    contact_service: ContactService,
    select_list_service: InternalSelectListService,
    organization_id: UUID,
    user_id: UUID,
    primary_email: str,
    account: AccountV2,
) -> ContactV2:
    """Create test contact."""
    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id,
        user_id=user_id,
    )
    default_stage_list = (
        await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
    )
    assert default_stage_list and default_stage_list.default_or_initial_active_value
    return await contact_service.create_contact_with_contact_channels(
        organization_id=organization_id,
        user_id=user_id,
        create_contact_with_contact_channel_request=CreateContactRequest(
            contact=CreateDbContactRequest(
                first_name="Test",
                last_name="Contact",
                display_name="Test Contact",
                owner_user_id=user_id,
                created_by_user_id=user_id,
                stage_id=default_stage_list.default_or_initial_active_value.id,
            ),
            contact_emails=[
                CreateDbContactEmailRequest(
                    email=primary_email,
                    is_contact_primary=True,
                )
            ],
            contact_account_roles=[
                CreateContactAccountRoleRequest(
                    account_id=account.id,
                    is_primary_account=True,
                )
            ],
        ),
    )

    logger.info(f"contact: {contact}")
    return None


@pytest.fixture(scope="class")
async def create_pipeline_stage_dto(
    organization_id: UUID,
    user_id: UUID,
    pipeline_stage_select_list_service: PipelineStageSelectListService,
) -> PipelineStageDto:
    return await pipeline_stage_select_list_service.create_pipeline_stage_select_list(
        organization_id=organization_id,
        actioning_user_id=user_id,
        req=PipelineStageSelectListCreateRequest(
            display_name="Test Pipeline Stage",
            description="Test Pipeline Stage Description",
            is_default=True,
            value_reqs=(
                PipelineStageSelectListValueCreateRequest(
                    pipeline_status=PipelineStatus.PROSPECT,
                    select_list_value_req=SelectListValueCreateRequest(
                        api_name="starting",
                        display_value="Starting",
                        is_default=True,
                    ),
                ),
                PipelineStageSelectListValueCreateRequest(
                    pipeline_status=PipelineStatus.PROSPECT,
                    select_list_value_req=SelectListValueCreateRequest(
                        api_name="prospect",
                        display_value="Prospect",
                        is_default=False,
                    ),
                ),
                PipelineStageSelectListValueCreateRequest(
                    pipeline_status=PipelineStatus.DEAL,
                    outcome_state=PipelineOutcomeState.CLOSED_LOST,
                    select_list_value_req=SelectListValueCreateRequest(
                        api_name="closed_lost",
                        display_value="Closed Lost",
                        is_default=False,
                    ),
                ),
                PipelineStageSelectListValueCreateRequest(
                    pipeline_status=PipelineStatus.DEAL,
                    outcome_state=PipelineOutcomeState.CLOSED_WON,
                    select_list_value_req=SelectListValueCreateRequest(
                        api_name="closed_won",
                        display_value="Closed Won",
                        is_default=False,
                    ),
                ),
            ),
        ),
    )


@pytest.fixture(scope="class")
async def read_pipeline_stage_dto(
    pipeline_stage_select_list_service: PipelineStageSelectListService,
    organization_id: UUID,
) -> PipelineStageDto:
    """Create test pipeline stage."""

    logger.info(
        f"pipeline_stage_select_list_service: {pipeline_stage_select_list_service}"
    )
    logger.info(
        f"vars(pipeline_stage_select_list_service): {vars(pipeline_stage_select_list_service)}"
    )

    # Get default pipeline stages
    pipeline_stages = (
        await pipeline_stage_select_list_service.list_pipeline_stage_select_list_dtos(
            organization_id=organization_id
        )
    )

    logger.info(f"pipeline_stages: {pipeline_stages}")

    # Return the first stage
    return pipeline_stages[0]


@pytest.fixture(scope="class")
async def pipeline(
    pipeline_service: PipelineService,
    account: AccountV2,
    contact: ContactV2,
    create_pipeline_stage_dto: PipelineStageDto,
    read_pipeline_stage_dto: PipelineStageDto,
    organization_id: UUID,
    user_id: UUID,
    make_user_org: None,
) -> PipelineV2:
    """Create test pipeline."""
    return await pipeline_service.create_pipeline(
        organization_id=organization_id,
        user_id=user_id,
        req=CreatePipelineRequest(
            display_name="Test Pipeline",
            account_id=account.id,
            owner_user_id=user_id,
            contact_pipeline_associations=FullContactPipelineAssociationRequests(
                primary=ContactPipelineAssociationRequest(
                    contact_id=contact.id,
                ),
            ),
            stage_id=read_pipeline_stage_dto.select_list_value_dtos[
                0
            ].select_list_value.id,
        ),
    )


@pytest.fixture
async def falkordb_client() -> AsyncGenerator[FalkorDBClient, None]:
    """Yield FalkorDB client instance."""
    client = FalkorDBClient()
    yield client
    if client.pool:
        await client.pool.disconnect()


@pytest.fixture
async def falkordb_indexing_lib(
    _engine: DatabaseEngine, falkordb_client: FalkorDBClient
) -> FalkorDBIndexingLib:
    """Yield FalkorDB indexing lib."""
    return FalkorDBIndexingLib(db_engine=_engine, falkordb_client=falkordb_client)


class TestFalkorIndexDeletion:
    async def test_index_archive_and_reindex_pipeline(
        self,
        falkordb_indexing_lib: FalkorDBIndexingLib,
        falkordb_client: FalkorDBClient,
        pipeline_service: PipelineService,
        pipeline: PipelineV2,
        organization_id: UUID,
        user_id: UUID,
    ) -> None:
        """
        Test scenario:
        1. Index a pipeline.
        2. Verify it exists in FalkorDB.
        3. Archive the pipeline.
        4. Re-index pipelines (which should now mark the archived one as deleted in FalkorDB).
        5. Verify the pipeline node is deleted from FalkorDB.
        """
        # 1. Index the initial pipeline
        await falkordb_indexing_lib.index_pipelines(
            organization_id=organization_id,
            pipeline_ids=[pipeline.id],
        )

        # 2. Verify it exists in FalkorDB
        graph_name = falkordb_client.get_graph_name(organization_id)
        graph = falkordb_client.client.select_graph(graph_name)

        node_in_db = await falkordb_client.get_node_by_id(
            graph=graph,
            node_type=PipelineV2.object_id.object_name,
            node_id=pipeline.id,
        )
        assert node_in_db is not None, (
            f"Pipeline {pipeline.id} should exist in FalkorDB after initial indexing."
        )
        assert node_in_db.get("id") == str(pipeline.id)

        # 3. Archive the pipeline
        # Assuming archive_pipeline takes organization_id, pipeline_id, and user_id
        # Adjust if the method signature is different for PipelineService
        await pipeline_service.archive_pipeline(
            organization_id=organization_id, pipeline_id=pipeline.id, user_id=user_id
        )
        logger.info(
            f"Archived pipeline {pipeline.id} for organization {organization_id}"
        )

        # 4. Re-index pipelines
        # The FalkorDBIndexingLib needs metadata_service to get schema for sync_pipelines
        # We need to ensure it has it or mock/provide schema_descriptor if necessary
        # For simplicity, let's assume falkordb_indexing_lib is correctly initialized with metadata_service
        # as it is in test_falkordb_indexing.py (via its own falkordb_indexing_lib fixture)

        # Forcing a sync with the explicit knowledge of deletion for this test:
        schema_descriptor = await falkordb_indexing_lib.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id
        )
        await falkordb_client.sync_pipelines(
            pipelines=[],  # Pass empty list to simulate it's no longer active
            organization_id=organization_id,
            schema=schema_descriptor,
            deleted_pipeline_ids={pipeline.id},  # Explicitly mark as deleted
        )

        logger.info(
            f"Re-indexed pipelines for organization {organization_id} after archival."
        )

        # 5. Verify the pipeline node is deleted from FalkorDB
        node_after_reindex = await falkordb_client.get_node_by_id(
            graph=graph,
            node_type=PipelineV2.object_id.object_name,
            node_id=pipeline.id,
        )
        assert node_after_reindex is None, (
            f"Pipeline {pipeline.id} should be deleted from FalkorDB after archival and re-indexing."
        )
        logger.info(
            f"Successfully verified pipeline {pipeline.id} deletion from FalkorDB."
        )
