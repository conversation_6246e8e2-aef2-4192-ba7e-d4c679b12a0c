import uuid
from collections.abc import AsyncGenerator
from datetime import UTC, datetime, timedelta
from uuid import UUID, uuid4

import pytest
from sqlalchemy import text

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdSelectListIdentifier,
)
from salestech_be.common.type.metadata.common import (
    CustomObjectIdentifier,
    ObjectKind,
    RelationshipType,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    CaseAwareUniqueIndexableConfig,
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    NumericFieldProperty,
    TextFieldProperty,
)
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.service.contact_query_service import ContactQueryService
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.custom_object.service.association_service import (
    AssociationService,
)
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.meeting.meeting_service import MeetingService
from salestech_be.core.meeting.service.meeting_query_service import MeetingQueryService
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
from salestech_be.core.metadata.dto.select_list_dto import PipelineStageDto
from salestech_be.core.metadata.dto.service_api_schema import (
    PipelineStageSelectListCreateRequest,
    PipelineStageSelectListValueCreateRequest,
    SelectListValueCreateRequest,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.metadata.service.metadata_service import (
    MetadataService,
)
from salestech_be.core.metadata.service.pipeline_stage_service import (
    PipelineStageSelectListService,
)
from salestech_be.core.metadata.types import ContactAccountRole, ContactPipelineRole
from salestech_be.core.pipeline.service.pipeline_query_service import (
    PipelineQueryService,
)
from salestech_be.core.pipeline.service.pipeline_service import PipelineService
from salestech_be.core.pipeline.service_api_schema import (
    ContactPipelineAssociationRequest,
    CreatePipelineRequest,
    FullContactPipelineAssociationRequests,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.user.service.user_service import UserService
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.core.user.utils import default_roles
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.contact import (
    CreateContactAccountRoleRequest,
    CreateContactRequest,
    CreateDbContactEmailRequest,
    CreateDbContactRequest,
)
from salestech_be.db.models.custom_field import CustomField
from salestech_be.db.models.custom_object import CustomObject
from salestech_be.db.models.custom_object_association import CustomObjectAssociation
from salestech_be.db.models.custom_object_data import CustomObjectData
from salestech_be.db.models.meeting import MeetingProvider, MeetingReferenceIdType
from salestech_be.db.models.pipeline import PipelineStatus
from salestech_be.db.models.pipeline_stage_select_list_value_metadata import (
    PipelineOutcomeState,
)
from salestech_be.falkordb.delete_complex_node_structure import (
    delete_complex_node_structure,
)
from salestech_be.falkordb.falkordb_client import FalkorDBClient
from salestech_be.falkordb.indexing_lib import FalkorDBIndexingLib
from salestech_be.falkordb.types import RelationshipDef
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.meeting.schema import CreateMeetingRequest, Invitee

logger = get_logger()


@pytest.fixture(scope="class")
def organization_id() -> UUID:
    """Test organization ID."""
    return uuid4()


@pytest.fixture(scope="class")
def user_id() -> UUID:
    """Test user ID."""
    return uuid4()


@pytest.fixture(scope="class")
async def primary_email() -> str:
    """Test primary email."""
    return f"test-{uuid.uuid4()}@example.com"


@pytest.fixture(scope="class")
async def primary_email2() -> str:
    """Test primary email 2."""
    return f"test-{uuid.uuid4()}@example.com"


@pytest.fixture(scope="class")
async def primary_email3() -> str:
    """Test primary email 3."""
    return f"test-{uuid.uuid4()}@example.com"


@pytest.fixture(scope="class")
async def _organization(
    _engine: DatabaseEngine,
    organization_id: UUID,
    user_id: UUID,
    _user: None,
    user_service: UserService,
) -> None:
    """Create test organization."""
    await _engine.execute(
        text("""
            INSERT INTO organization (
                id, display_name, created_at, created_by_user_id
            ) VALUES (
                :id, :display_name, :created_at, :created_by_user_id
            )
        """),
        {
            "id": organization_id,
            "display_name": "Test Organization",
            "created_at": zoned_utc_now(),
            "created_by_user_id": user_id,
        },
    )
    await user_service.create_hidden_user_and_organization_association_v2(
        email=f"test_{user_id}@example.com",
        organization_id=organization_id,
    )
    await user_service.active_user_and_organization_association(
        user_id=user_id,
        organization_id=organization_id,
    )


@pytest.fixture(scope="class")
async def _user(
    _engine: DatabaseEngine,
    user_id: UUID,
    primary_email: str,
) -> None:
    """Create test user."""
    # Create user
    try:
        await _engine.execute(
            text("""
                INSERT INTO "user" (id, email, first_name, last_name, created_at)
                VALUES (:id, :email, :first_name, :last_name, :created_at)
                ON CONFLICT (id) DO NOTHING
            """),
            {
                "id": user_id,
                "email": primary_email,
                "first_name": "Test",
                "last_name": "User",
                "created_at": datetime.now(UTC),
            },
        )

    except Exception as e:
        logger.info(f"Error creating user: {e}")


@pytest.fixture(scope="class")
async def make_user_org(
    user_repository: UserRepository,
    select_list_service: InternalSelectListService,
    pipeline_stage_select_list_service: PipelineStageSelectListService,
    user_id: UUID,
    organization_id: UUID,
    _organization: None,
    _user: None,
) -> None:
    result = await user_repository.associate_user_to_an_organization(
        user_id=user_id,
        organization_id=organization_id,
        roles=default_roles(),
    )
    logger.info(f"result: {result}")

    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id, user_id=user_id
    )

    pipeline_stage_select_list = await pipeline_stage_select_list_service.bootstrap_organization_default_pipeline_stage_select_list(
        organization_id=organization_id, actioning_user_id=user_id
    )
    logger.info(f"pipeline_stage_select_list: {pipeline_stage_select_list}")


@pytest.fixture(scope="class")
async def account(
    account_service: AccountService,
    association_service: AssociationService,
    organization_id: UUID,
    user_id: UUID,
) -> AccountV2:
    """Create test account."""
    account = await account_service.create_account_v2(
        organization_id=organization_id,
        user_id=user_id,
        create_account_request=CreateAccountRequest(
            display_name="Test Account",
            owner_user_id=user_id,
            official_website="https://example.com",
        ),
    )

    list_result = await account_service.list_accounts_v2(
        organization_id=organization_id, only_include_account_ids={account.id}
    )

    return list_result[0]


@pytest.fixture(scope="class")
async def account2(
    account_service: AccountService,
    organization_id: UUID,
    user_id: UUID,
) -> AccountV2:
    """Create a second test account without associations."""
    account = await account_service.create_account_v2(
        organization_id=organization_id,
        user_id=user_id,
        create_account_request=CreateAccountRequest(
            display_name="Test Account 2",
            owner_user_id=user_id,
            official_website="https://example2.com",
        ),
    )

    list_result = await account_service.list_accounts_v2(
        organization_id=organization_id, only_include_account_ids={account.id}
    )

    return list_result[0]


@pytest.fixture(scope="class")
async def account_with_custom_fields(
    account_service: AccountService,
    custom_object_service: CustomObjectService,
    organization_id: UUID,
    user_id: UUID,
) -> AccountV2:
    """Create test account with custom fields."""

    # Create extension custom object
    account_object_with_custom_fields = (
        await custom_object_service.enable_extension_custom_object(
            user_id=user_id,
            organization_id=organization_id,
            objects_to_enable=[ExtendableStandardObject.account],
        )
    )
    logger.info(
        f"account_object_with_custom_fields: {account_object_with_custom_fields}"
    )

    # Add null check for account object
    account_object = account_object_with_custom_fields.get(
        ExtendableStandardObject.account
    )
    logger.info(f"account_object: {account_object}")
    if not account_object:
        raise ValueError("Failed to create account custom object")

    """ Create custom fields """
    # Text (single line)
    account_custom_text_field = await custom_object_service.create_custom_field_v2(
        user_id=user_id,
        organization_id=organization_id,
        custom_object_id=account_object.id,
        custom_field_type_property_create=TextFieldProperty(
            field_display_name="Account Custom Field Test 1",
            is_required=False,
            index_config=CaseAwareUniqueIndexableConfig(is_indexed=True),
        ),
    )
    logger.info(f"account_custom_text_field: {account_custom_text_field}")

    # Create the account finally
    account = await account_service.create_account_v2(
        organization_id=organization_id,
        user_id=user_id,
        create_account_request=CreateAccountRequest(
            display_name="Test Account with custom fields",
            owner_user_id=user_id,
            official_website="https://example_with_custom_fields.com",
            custom_field_data={
                account_custom_text_field.id: "test_value",
            },
        ),
    )
    logger.info(f"account: {account}")

    # Return the account with custom fields
    list_result = await account_service.list_accounts_v2(
        organization_id=organization_id,
        only_include_account_ids={account.id},
        include_custom_object=True,
    )
    logger.info(f"list_result: {list_result}")

    return list_result[0]


@pytest.fixture(scope="class")
async def contact(
    contact_service: ContactService,
    select_list_service: InternalSelectListService,
    organization_id: UUID,
    user_id: UUID,
    primary_email: str,
    account: AccountV2,
) -> ContactV2:
    """Create test contact."""
    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id,
        user_id=user_id,
    )
    default_stage_list = (
        await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
    )
    assert default_stage_list and default_stage_list.default_or_initial_active_value
    return await contact_service.create_contact_with_contact_channels(
        organization_id=organization_id,
        user_id=user_id,
        create_contact_with_contact_channel_request=CreateContactRequest(
            contact=CreateDbContactRequest(
                first_name="Test",
                last_name="Contact",
                display_name="Test Contact",
                owner_user_id=user_id,
                created_by_user_id=user_id,
                stage_id=default_stage_list.default_or_initial_active_value.id,
            ),
            contact_emails=[
                CreateDbContactEmailRequest(
                    email=primary_email,
                    is_contact_primary=True,
                )
            ],
            contact_account_roles=[
                CreateContactAccountRoleRequest(
                    account_id=account.id,
                    is_primary_account=True,
                )
            ],
        ),
    )


@pytest.fixture(scope="class")
async def contact_with_custom_fields(
    contact_service: ContactService,
    custom_object_service: CustomObjectService,
    select_list_service: InternalSelectListService,
    organization_id: UUID,
    user_id: UUID,
    primary_email2: str,  # Use different primary email to avoid ConflictResourceError
    account: AccountV2,
) -> ContactV2:
    """Create test contact with custom fields."""

    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id,
        user_id=user_id,
    )
    default_stage_list = (
        await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
    )
    assert default_stage_list and default_stage_list.default_or_initial_active_value

    """ Create extension custom object for contact """
    # Create extension custom object
    contact_object_with_custom_fields = (
        await custom_object_service.enable_extension_custom_object(
            user_id=user_id,
            organization_id=organization_id,
            objects_to_enable=[ExtendableStandardObject.contact],
        )
    )
    logger.info(
        f"contact_object_with_custom_fields: {contact_object_with_custom_fields}"
    )

    # Add null check for contact object
    contact_object = contact_object_with_custom_fields.get(
        ExtendableStandardObject.contact
    )
    logger.info(f"contact_object: {contact_object}")
    if not contact_object:
        raise ValueError("Failed to create contact custom object")

    """ Create custom fields """
    # Text (single line)
    contact_custom_text_field = await custom_object_service.create_custom_field_v2(
        user_id=user_id,
        organization_id=organization_id,
        custom_object_id=contact_object.id,
        custom_field_type_property_create=TextFieldProperty(
            field_display_name="Contact Custom Field Test 1",
            is_required=False,
            index_config=CaseAwareUniqueIndexableConfig(is_indexed=True),
        ),
    )
    logger.info(f"contact_custom_text_field: {contact_custom_text_field}")

    return await contact_service.create_contact_with_contact_channels(
        organization_id=organization_id,
        user_id=user_id,
        create_contact_with_contact_channel_request=CreateContactRequest(
            contact=CreateDbContactRequest(
                first_name="Test",
                last_name="Contact with custom fields",
                display_name="Test Contact with custom fields",
                owner_user_id=user_id,
                created_by_user_id=user_id,
                stage_id=default_stage_list.default_or_initial_active_value.id,
                custom_field_data={
                    contact_custom_text_field.id: "Contact Test Value with custom fields",
                },
            ),
            contact_emails=[
                CreateDbContactEmailRequest(
                    email=primary_email2,
                    is_contact_primary=True,
                )
            ],
            contact_account_roles=[
                CreateContactAccountRoleRequest(
                    account_id=account.id,
                    is_primary_account=True,
                )
            ],
        ),
    )


@pytest.fixture(scope="class")
async def contact2(
    contact_service: ContactService,
    select_list_service: InternalSelectListService,
    organization_id: UUID,
    user_id: UUID,
    primary_email3: str,  # Use different primary email to avoid ConflictResourceError
    account: AccountV2,
) -> ContactV2:
    """Create a second test contact without associations."""
    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id,
        user_id=user_id,
    )
    default_stage_list = (
        await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
    )
    assert default_stage_list and default_stage_list.default_or_initial_active_value
    return await contact_service.create_contact_with_contact_channels(
        organization_id=organization_id,
        user_id=user_id,
        create_contact_with_contact_channel_request=CreateContactRequest(
            contact=CreateDbContactRequest(
                first_name="Test",
                last_name="Contact",
                display_name="Test Contact",
                owner_user_id=user_id,
                created_by_user_id=user_id,
                stage_id=default_stage_list.default_or_initial_active_value.id,
            ),
            contact_emails=[
                CreateDbContactEmailRequest(
                    email=primary_email3,
                    is_contact_primary=True,
                )
            ],
            contact_account_roles=[
                CreateContactAccountRoleRequest(
                    account_id=account.id,
                    is_primary_account=True,
                )
            ],
        ),
    )


@pytest.fixture(scope="class")
async def account_to_contact_association(
    association_service: AssociationService,
    organization_id: UUID,
    user_id: UUID,
    account: AccountV2,
    contact: ContactV2,
) -> CustomObjectAssociation:
    # Create association between account and contact
    return await association_service.create_association(
        organization_id=organization_id,
        user_id=user_id,
        source_object_identifier=StandardObjectIdentifier(
            object_kind=ObjectKind.STANDARD,
            object_name=ExtendableStandardObject.account,
        ),
        target_object_identifier=StandardObjectIdentifier(
            object_kind=ObjectKind.STANDARD,
            object_name=ExtendableStandardObject.contact,
        ),
        relationship_type=RelationshipType.LOOKUP,
        association_name="Account to Contact",
        inverse_name="Contact to Account",
        max_source_records=1,
        max_target_records=1,
    )


@pytest.fixture(scope="class")
async def account_to_pipeline_association(
    association_service: AssociationService,
    organization_id: UUID,
    user_id: UUID,
    account: AccountV2,
    pipeline: PipelineV2,
) -> CustomObjectAssociation:
    return await association_service.create_association(
        organization_id=organization_id,
        user_id=user_id,
        source_object_identifier=StandardObjectIdentifier(
            object_kind=ObjectKind.STANDARD,
            object_name=ExtendableStandardObject.account,
        ),
        target_object_identifier=StandardObjectIdentifier(
            object_kind=ObjectKind.STANDARD,
            object_name=ExtendableStandardObject.pipeline,
        ),
        relationship_type=RelationshipType.LOOKUP,
        association_name="Account to Pipeline",
        inverse_name="Pipeline to Account",
        max_source_records=1,
        max_target_records=1,
    )


@pytest.fixture(scope="class")
async def account_to_meeting_association(
    association_service: AssociationService,
    organization_id: UUID,
    user_id: UUID,
    account: AccountV2,
    meeting: MeetingV2,
) -> CustomObjectAssociation:
    # Create association between account and meeting
    return await association_service.create_association(
        organization_id=organization_id,
        user_id=user_id,
        source_object_identifier=StandardObjectIdentifier(
            object_kind=ObjectKind.STANDARD,
            object_name=ExtendableStandardObject.account,
        ),
        target_object_identifier=StandardObjectIdentifier(
            object_kind=ObjectKind.STANDARD,
            object_name=ExtendableStandardObject.meeting,
        ),
        relationship_type=RelationshipType.LOOKUP,
        association_name="Account to Meeting",
        inverse_name="Meeting to Account",
        max_source_records=1,
        max_target_records=1,
    )


@pytest.fixture(scope="class")
async def association_records(
    association_service: AssociationService,
    account_to_contact_association: CustomObjectAssociation,
    account_to_pipeline_association: CustomObjectAssociation,
    account_to_meeting_association: CustomObjectAssociation,
    organization_id: UUID,
    user_id: UUID,
    account: AccountV2,
    contact: ContactV2,
    pipeline: PipelineV2,
    meeting: MeetingV2,
) -> None:
    """Test custom associations (standard object <-> standard object)"""

    # Create association records
    await association_service.create_association_record(
        organization_id=organization_id,
        user_id=user_id,
        association=account_to_contact_association,
        source_record_id=account.id,
        target_record_id=contact.id,
    )

    await association_service.create_association_record(
        organization_id=organization_id,
        user_id=user_id,
        association=account_to_pipeline_association,
        source_record_id=account.id,
        target_record_id=pipeline.id,
    )

    await association_service.create_association_record(
        organization_id=organization_id,
        user_id=user_id,
        association=account_to_meeting_association,
        source_record_id=account.id,
        target_record_id=meeting.id,
    )


@pytest.fixture(scope="class")
async def create_pipeline_stage_dto(
    organization_id: UUID,
    user_id: UUID,
    pipeline_stage_select_list_service: PipelineStageSelectListService,
) -> PipelineStageDto:
    return await pipeline_stage_select_list_service.create_pipeline_stage_select_list(
        organization_id=organization_id,
        actioning_user_id=user_id,
        req=PipelineStageSelectListCreateRequest(
            display_name="Test Pipeline Stage",
            description="Test Pipeline Stage Description",
            is_default=True,
            value_reqs=(
                PipelineStageSelectListValueCreateRequest(
                    pipeline_status=PipelineStatus.PROSPECT,
                    select_list_value_req=SelectListValueCreateRequest(
                        api_name="starting",
                        display_value="Starting",
                        is_default=True,
                    ),
                ),
                PipelineStageSelectListValueCreateRequest(
                    pipeline_status=PipelineStatus.PROSPECT,
                    select_list_value_req=SelectListValueCreateRequest(
                        api_name="prospect",
                        display_value="Prospect",
                        is_default=False,
                    ),
                ),
                PipelineStageSelectListValueCreateRequest(
                    pipeline_status=PipelineStatus.DEAL,
                    outcome_state=PipelineOutcomeState.CLOSED_LOST,
                    select_list_value_req=SelectListValueCreateRequest(
                        api_name="closed_lost",
                        display_value="Closed Lost",
                        is_default=False,
                    ),
                ),
                PipelineStageSelectListValueCreateRequest(
                    pipeline_status=PipelineStatus.DEAL,
                    outcome_state=PipelineOutcomeState.CLOSED_WON,
                    select_list_value_req=SelectListValueCreateRequest(
                        api_name="closed_won",
                        display_value="Closed Won",
                        is_default=False,
                    ),
                ),
            ),
        ),
    )


@pytest.fixture(scope="class")
async def read_pipeline_stage_dto(
    pipeline_stage_select_list_service: PipelineStageSelectListService,
    organization_id: UUID,
) -> PipelineStageDto:
    """Create test pipeline stage."""

    logger.info(
        f"pipeline_stage_select_list_service: {pipeline_stage_select_list_service}"
    )
    logger.info(
        f"vars(pipeline_stage_select_list_service): {vars(pipeline_stage_select_list_service)}"
    )

    # Get default pipeline stages
    pipeline_stages = (
        await pipeline_stage_select_list_service.list_pipeline_stage_select_list_dtos(
            organization_id=organization_id
        )
    )

    logger.info(f"pipeline_stages: {pipeline_stages}")

    # Return the first stage
    return pipeline_stages[0]


@pytest.fixture(scope="class")
async def pipeline(
    pipeline_service: PipelineService,
    account: AccountV2,
    contact: ContactV2,
    create_pipeline_stage_dto: PipelineStageDto,
    read_pipeline_stage_dto: PipelineStageDto,
    organization_id: UUID,
    user_id: UUID,
    make_user_org: None,
) -> PipelineV2:
    """Create test pipeline."""
    return await pipeline_service.create_pipeline(
        organization_id=organization_id,
        user_id=user_id,
        req=CreatePipelineRequest(
            display_name="Test Pipeline",
            account_id=account.id,
            owner_user_id=user_id,
            contact_pipeline_associations=FullContactPipelineAssociationRequests(
                primary=ContactPipelineAssociationRequest(
                    contact_id=contact.id,
                ),
            ),
            stage_id=read_pipeline_stage_dto.select_list_value_dtos[
                0
            ].select_list_value.id,
        ),
    )


@pytest.fixture(scope="class")
async def pipeline2(
    pipeline_service: PipelineService,
    account: AccountV2,
    contact: ContactV2,
    create_pipeline_stage_dto: PipelineStageDto,
    read_pipeline_stage_dto: PipelineStageDto,
    organization_id: UUID,
    user_id: UUID,
    make_user_org: None,
) -> PipelineV2:
    """Create test pipeline."""
    return await pipeline_service.create_pipeline(
        organization_id=organization_id,
        user_id=user_id,
        req=CreatePipelineRequest(
            display_name="Test Pipeline 2",
            account_id=account.id,
            owner_user_id=user_id,
            contact_pipeline_associations=FullContactPipelineAssociationRequests(
                primary=ContactPipelineAssociationRequest(
                    contact_id=contact.id,
                ),
            ),
            stage_id=read_pipeline_stage_dto.select_list_value_dtos[
                0
            ].select_list_value.id,
        ),
    )


@pytest.fixture(scope="class")
async def pipeline_with_custom_fields(
    pipeline_service: PipelineService,
    custom_object_service: CustomObjectService,
    account: AccountV2,
    contact: ContactV2,
    create_pipeline_stage_dto: PipelineStageDto,
    read_pipeline_stage_dto: PipelineStageDto,
    organization_id: UUID,
    user_id: UUID,
    make_user_org: None,
) -> PipelineV2:
    """Create test pipeline with custom fields."""

    """ Create extension custom object for contact """
    # Create extension custom object
    pipeline_object_with_custom_fields = (
        await custom_object_service.enable_extension_custom_object(
            user_id=user_id,
            organization_id=organization_id,
            objects_to_enable=[ExtendableStandardObject.pipeline],
        )
    )
    logger.info(
        f"pipeline_object_with_custom_fields: {pipeline_object_with_custom_fields}"
    )

    # Add null check for contact object
    pipeline_object = pipeline_object_with_custom_fields.get(
        ExtendableStandardObject.pipeline
    )
    logger.info(f"pipeline_object: {pipeline_object}")
    if not pipeline_object:
        raise ValueError("Failed to create pipeline custom object")

    """ Create custom fields """
    # Text (single line)
    pipeline_custom_text_field = await custom_object_service.create_custom_field_v2(
        user_id=user_id,
        organization_id=organization_id,
        custom_object_id=pipeline_object.id,
        custom_field_type_property_create=TextFieldProperty(
            field_display_name="Pipeline Custom Field Test 1",
            is_required=False,
            index_config=CaseAwareUniqueIndexableConfig(is_indexed=True),
        ),
    )
    logger.info(f"pipeline_custom_text_field: {pipeline_custom_text_field}")

    return await pipeline_service.create_pipeline(
        organization_id=organization_id,
        user_id=user_id,
        req=CreatePipelineRequest(
            display_name="Test Pipeline with custom fields",
            account_id=account.id,
            owner_user_id=user_id,
            contact_pipeline_associations=FullContactPipelineAssociationRequests(
                primary=ContactPipelineAssociationRequest(
                    contact_id=contact.id,
                ),
            ),
            stage_id=read_pipeline_stage_dto.select_list_value_dtos[
                0
            ].select_list_value.id,
            custom_field_data={
                pipeline_custom_text_field.id: "Pipeline Test Value with custom fields",
            },
        ),
    )


@pytest.fixture(scope="class")
async def meeting(
    meeting_service: MeetingService,
    organization_id: UUID,
    user_id: UUID,
    account: AccountV2,
    contact: ContactV2,
    pipeline: PipelineV2,
) -> MeetingV2:
    """Create test meeting."""
    start_time = datetime.now(UTC)
    end_time = start_time + timedelta(hours=1)

    # Create meeting using the appropriate method
    meeting_dto = await meeting_service.create_meeting(
        organization_id=organization_id,
        user_id=user_id,
        request=CreateMeetingRequest(
            title="Test Meeting",
            starts_at=start_time,
            ends_at=end_time,
            account_id=account.id,
            invitees=[
                Invitee(
                    contact_id=contact.id,
                    is_organizer=True,
                    user_id=user_id,
                )
            ],
            reference_id=str(uuid.uuid4()),
            reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
            platform=MeetingProvider.ZOOM,
        ),
    )

    return await meeting_service.get_entity(
        entity_id=meeting_dto.meeting_id,
        organization_id=organization_id,
        user_id=user_id,
    )


@pytest.fixture(scope="class")
async def custom_object_name() -> str:
    """Create test custom object name."""
    return f"TestObject_{uuid.uuid4().hex[:8]}"


@pytest.fixture(scope="class")
async def custom_object_with_data(
    custom_object_service: CustomObjectService,
    association_service: AssociationService,
    organization_id: UUID,
    user_id: UUID,
    custom_object_name: str,
) -> tuple[CustomObject, CustomField, CustomObjectData]:
    """Create test custom object with data."""
    # Create custom object
    custom_object = await custom_object_service.create_standalone_custom_object(
        user_id=user_id,
        organization_id=organization_id,
        object_display_name=custom_object_name,
    )

    # Create text field
    text_field = await custom_object_service.create_custom_field_v2(
        user_id=user_id,
        organization_id=organization_id,
        custom_object_id=custom_object.id,
        custom_field_type_property_create=TextFieldProperty(
            field_display_name="Test Text Field",
            is_required=False,
            index_config=CaseAwareUniqueIndexableConfig(is_indexed=True),
            max_length=100,
        ),
    )

    # Create numeric field
    numeric_field = await custom_object_service.create_custom_field_v2(
        user_id=user_id,
        organization_id=organization_id,
        custom_object_id=custom_object.id,
        custom_field_type_property_create=NumericFieldProperty(
            field_display_name="Test Numeric Field",
            is_required=False,
            index_config=UniqueIndexableConfig(is_indexed=True),
            total_precision=10,
            decimal_precision=2,
        ),
    )

    data = await custom_object_service.create_custom_object_data_v2(
        organization_id=organization_id,
        user_id=user_id,
        custom_object_dto_or_id=custom_object.id,
        custom_field_data_by_field_id={
            text_field.id: "Test Text Value",
            numeric_field.id: 123.45,
        },
        display_name="Test Record 1",
    )

    # Create association between custom object and account (custom object <-> standard object)
    source_identifier1 = CustomObjectIdentifier(
        object_kind=ObjectKind.CUSTOM,
        organization_id=custom_object.organization_id,
        object_id=custom_object.id,
    )

    target_identifier1 = StandardObjectIdentifier(
        object_kind=ObjectKind.STANDARD,
        object_name=ExtendableStandardObject.account,
    )
    await association_service.create_association(
        organization_id=organization_id,
        user_id=user_id,
        source_object_identifier=source_identifier1,
        target_object_identifier=target_identifier1,
        relationship_type=RelationshipType.LOOKUP,
        association_name="Custom Object to Account",
        inverse_name="Account to Custom Object",
        max_source_records=1,
        max_target_records=1,
    )

    # Create association between contact and custom object (standard object <-> custom object)
    source_identifier2 = StandardObjectIdentifier(
        object_kind=ObjectKind.STANDARD,
        object_name=ExtendableStandardObject.contact,
    )

    target_identifier2 = CustomObjectIdentifier(
        object_kind=ObjectKind.CUSTOM,
        organization_id=custom_object.organization_id,
        object_id=custom_object.id,
    )
    await association_service.create_association(
        organization_id=organization_id,
        user_id=user_id,
        source_object_identifier=source_identifier2,
        target_object_identifier=target_identifier2,
        relationship_type=RelationshipType.LOOKUP,
        association_name="Contact to Custom Object",
        inverse_name="Custom Object to Contact",
        max_source_records=1,
        max_target_records=1,
    )

    return custom_object, text_field, data.custom_object_data


@pytest.fixture(scope="class")
async def two_same_custom_objects_with_data(
    custom_object_service: CustomObjectService,
    organization_id: UUID,
    user_id: UUID,
) -> list[tuple[CustomObject, CustomField, CustomObjectData]]:
    """Create two same test custom objects with different data records."""
    custom_object_tuples_list = []

    # Create custom object
    custom_object = await custom_object_service.create_standalone_custom_object(
        user_id=user_id,
        organization_id=organization_id,
        object_display_name="TestObject",
    )

    # Create text field
    text_field1 = await custom_object_service.create_custom_field_v2(
        user_id=user_id,
        organization_id=organization_id,
        custom_object_id=custom_object.id,
        custom_field_type_property_create=TextFieldProperty(
            field_display_name="Test Text Field 1",
            is_required=False,
            index_config=CaseAwareUniqueIndexableConfig(is_indexed=True),
            max_length=100,
        ),
    )

    # Create numeric field
    numeric_field1 = await custom_object_service.create_custom_field_v2(
        user_id=user_id,
        organization_id=organization_id,
        custom_object_id=custom_object.id,
        custom_field_type_property_create=NumericFieldProperty(
            field_display_name="Test Numeric Field 1",
            is_required=False,
            index_config=UniqueIndexableConfig(is_indexed=True),
            total_precision=10,
            decimal_precision=2,
        ),
    )

    # Create first data record
    data1 = await custom_object_service.create_custom_object_data_v2(
        organization_id=organization_id,
        user_id=user_id,
        custom_object_dto_or_id=custom_object.id,
        custom_field_data_by_field_id={
            text_field1.id: "Test Text Value 1",
            numeric_field1.id: 123.45,
        },
        display_name="Test Record 1",
    )
    custom_object_tuples_list.append(
        (custom_object, text_field1, data1.custom_object_data)
    )

    # Create second data record
    data2 = await custom_object_service.create_custom_object_data_v2(
        organization_id=organization_id,
        user_id=user_id,
        custom_object_dto_or_id=custom_object.id,
        custom_field_data_by_field_id={
            text_field1.id: "Test Text Value 2",
            numeric_field1.id: 456.78,
        },
        display_name="Test Record 2",
    )
    custom_object_tuples_list.append(
        (custom_object, text_field1, data2.custom_object_data)
    )

    # Note: You cannot create an association between the same custom object

    return custom_object_tuples_list


@pytest.fixture(scope="class")
async def two_different_custom_objects_with_data(
    custom_object_service: CustomObjectService,
    association_service: AssociationService,
    organization_id: UUID,
    user_id: UUID,
) -> list[tuple[CustomObject, CustomField, CustomObjectData]]:
    """Create two different test custom objects with data."""
    custom_object_tuples_list = []

    # Create first custom object
    custom_object1 = await custom_object_service.create_standalone_custom_object(
        user_id=user_id,
        organization_id=organization_id,
        object_display_name="TestObject_1",
    )

    # Create text field
    text_field1 = await custom_object_service.create_custom_field_v2(
        user_id=user_id,
        organization_id=organization_id,
        custom_object_id=custom_object1.id,
        custom_field_type_property_create=TextFieldProperty(
            field_display_name="Test Text Field 1",
            is_required=False,
            index_config=CaseAwareUniqueIndexableConfig(is_indexed=True),
            max_length=100,
        ),
    )

    # Create numeric field
    numeric_field1 = await custom_object_service.create_custom_field_v2(
        user_id=user_id,
        organization_id=organization_id,
        custom_object_id=custom_object1.id,
        custom_field_type_property_create=NumericFieldProperty(
            field_display_name="Test Numeric Field 1",
            is_required=False,
            index_config=UniqueIndexableConfig(is_indexed=True),
            total_precision=10,
            decimal_precision=2,
        ),
    )

    data1 = await custom_object_service.create_custom_object_data_v2(
        organization_id=organization_id,
        user_id=user_id,
        custom_object_dto_or_id=custom_object1.id,
        custom_field_data_by_field_id={
            text_field1.id: "Test Text Value 1",
            numeric_field1.id: 123.45,
        },
        display_name="Test Record 1",
    )
    custom_object_tuples_list.append(
        (custom_object1, text_field1, data1.custom_object_data)
    )

    # Create second custom object
    custom_object2 = await custom_object_service.create_standalone_custom_object(
        user_id=user_id,
        organization_id=organization_id,
        object_display_name="TestObject_2",
    )

    # Create text field
    text_field2 = await custom_object_service.create_custom_field_v2(
        user_id=user_id,
        organization_id=organization_id,
        custom_object_id=custom_object2.id,
        custom_field_type_property_create=TextFieldProperty(
            field_display_name="Test Text Field 2",
            is_required=False,
            index_config=CaseAwareUniqueIndexableConfig(is_indexed=True),
            max_length=100,
        ),
    )

    # Create numeric field
    numeric_field2 = await custom_object_service.create_custom_field_v2(
        user_id=user_id,
        organization_id=organization_id,
        custom_object_id=custom_object2.id,
        custom_field_type_property_create=NumericFieldProperty(
            field_display_name="Test Numeric Field 2",
            is_required=False,
            index_config=UniqueIndexableConfig(is_indexed=True),
            total_precision=10,
            decimal_precision=2,
        ),
    )

    data2 = await custom_object_service.create_custom_object_data_v2(
        organization_id=organization_id,
        user_id=user_id,
        custom_object_dto_or_id=custom_object2.id,
        custom_field_data_by_field_id={
            text_field2.id: "Test Text Value 2",
            numeric_field2.id: 456.78,
        },
        display_name="Test Record 2",
    )
    custom_object_tuples_list.append(
        (custom_object2, text_field2, data2.custom_object_data)
    )

    # Associate the custom objects (custom object <-> custom object)
    source_identifier = CustomObjectIdentifier(
        object_kind=ObjectKind.CUSTOM,
        organization_id=custom_object1.organization_id,
        object_id=custom_object1.id,
    )

    target_identifier = CustomObjectIdentifier(
        object_kind=ObjectKind.CUSTOM,
        organization_id=custom_object2.organization_id,
        object_id=custom_object2.id,
    )

    await association_service.create_association(
        organization_id=organization_id,
        user_id=user_id,
        source_object_identifier=source_identifier,
        target_object_identifier=target_identifier,
        relationship_type=RelationshipType.LOOKUP,
        association_name="Custom Object 1 to Custom Object 2",
        inverse_name="Custom Object 2 to Custom Object 1",
        max_source_records=1,
        max_target_records=1,
    )

    return custom_object_tuples_list


@pytest.fixture
async def falkordb_client() -> AsyncGenerator[FalkorDBClient, None]:
    """Create FalkorDB client."""
    client = FalkorDBClient()
    yield client


@pytest.fixture
async def falkordb_indexing_lib(
    _engine: DatabaseEngine, falkordb_client: FalkorDBClient
) -> FalkorDBIndexingLib:
    """Create FalkorDB indexing library."""
    return FalkorDBIndexingLib(db_engine=_engine, falkordb_client=falkordb_client)


class TestFalkorDBIndexing:
    """Test FalkorDB indexing functionality."""

    class TestIndexAccounts:
        async def test_index_account(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            account: AccountV2,
            organization_id: UUID,
        ) -> None:
            """Test indexing an account to FalkorDB."""
            # Index the account
            await falkordb_indexing_lib.index_accounts(
                organization_id=organization_id,
                account_ids=[account.id],
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the account
            result = await graph.query(
                f"MATCH (n:{AccountV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(account.id)},
            )

            # Verify the account was indexed
            assert len(result.result_set) == 1
            account_node = result.result_set[0][0]
            assert account_node.properties["id"] == str(account.id)
            assert account_node.properties["display_name"] == account.display_name
            assert account_node.properties["organization_id"] == str(organization_id)

            # Assert that the enum fields are not None
            assert account_node.properties["status"] is not None
            assert account_node.properties["access_status"] is not None

        # TODO: Rewrite this to call indexing function with update_edges set to True
        @pytest.mark.skip(
            reason="Skipping this test as the expected workflow is not implemented yet"
        )
        async def test_update_account(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            metadata_service: MetadataService,
            account: AccountV2,
            organization_id: UUID,
            execute_queries: bool = True,  # Set this to True or False as needed
        ) -> None:
            """Test updating an account in FalkorDB."""
            # Index the account
            await self.test_index_account(
                falkordb_indexing_lib=falkordb_indexing_lib,
                falkordb_client=falkordb_client,
                account=account,
                organization_id=organization_id,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)
            logger.info(f"graph: {graph}")

            # Get the original node
            original_node_query = await graph.query(
                f"MATCH (n:{AccountV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(account.id)},
            )
            logger.info(f"original_node_query: {original_node_query}")
            logger.info(
                f"original_node_query.result_set: {original_node_query.result_set}"
            )
            original_account_node = original_node_query.result_set[0][0]
            logger.info(f"original_account_node: {original_account_node}")
            logger.info(
                f"original_account_node.properties: {original_account_node.properties}"
            )

            # Convert accounts to dictionaries
            account_dict = falkordb_indexing_lib._convert_model_to_dict(account)
            account_props = falkordb_client.clean_props(account_dict)
            logger.info(f"OLD account_props: {account_props}")

            # Update account props
            account_props["display_name"] = "Test Account Updated"
            account_props["official_website"] = "www.mattyfresh.com"
            logger.info(f"NEW account_props: {account_props}")

            org_schema = await metadata_service.get_organization_domain_object_schema(
                organization_id=organization_id,
            )

            # Generate the update node query and execute it if execute_queries is True, else just return the query
            update_node_query = (
                await falkordb_indexing_lib.update_complex_node_structure(
                    graph=graph,
                    node_type=AccountV2.object_id.object_name,
                    node_id=str(account.id),
                    data=account_props,
                    schema=org_schema,
                    execute_queries=execute_queries,
                )
            )
            logger.info(f"update_node_query: {update_node_query}")

            # Verify the account was updated if execute_queries is True, else just return the query
            result = await graph.query(
                f"MATCH (n:{AccountV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(account.id)},
            )
            logger.info(f"result: {result}")
            logger.info(f"result.result_set: {result.result_set}")

            if execute_queries:
                logger.info(
                    "User requested to execute the update node query. Confirming that the account was updated."
                )

                assert len(result.result_set) == 1
                account_node = result.result_set[0][0]

                # Check that we didn't lose any properties
                logger.info(
                    f"len(account_node.properties): {len(account_node.properties)}"
                )
                logger.info(f"account_node.properties: {account_node.properties}")
                logger.info(
                    f"len(original_account_node.properties): {len(original_account_node.properties)}"
                )
                logger.info(
                    f"original_account_node.properties: {original_account_node.properties}"
                )
                assert len(account_node.properties) == len(
                    original_account_node.properties
                )

                # Check that the properties were updated
                assert account_node.properties["display_name"] == "Test Account Updated"
                assert (
                    account_node.properties["official_website"] == "www.mattyfresh.com"
                )
            else:
                logger.info(
                    "User did not request to execute the update node query. Verifying that the account was not updated."
                )

                # Verify that the account was not updated
                assert len(result.result_set) == 1
                account_node = result.result_set[0][0]
                assert account_node.properties["display_name"] == account.display_name
                assert (
                    account_node.properties["official_website"]
                    == account.official_website
                )

        # TODO: Add assertions once I figure out how to update fields via CDC. Maybe move this to integration tests?
        @pytest.mark.skip(
            reason="Skipping this test as the expected workflow is not implemented yet"
        )
        async def test_update_account_with_custom_fields(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            account_with_custom_fields: AccountV2,
            organization_id: UUID,
        ) -> None:
            """Test updating an account in FalkorDB."""
            logger.info(
                f"MATTY INDEXING TEST account_with_custom_fields: {account_with_custom_fields}"
            )

            # Index the account
            await self.test_index_account(
                falkordb_indexing_lib=falkordb_indexing_lib,
                falkordb_client=falkordb_client,
                account=account_with_custom_fields,
                organization_id=organization_id,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)
            logger.info(f"graph: {graph}")

            # Call index accounts again but with update_edges set to True
            await falkordb_indexing_lib.index_accounts(
                organization_id=organization_id,
                account_ids=[account_with_custom_fields.id],
                update_edges=True,
            )

        async def test_delete_account(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            account: AccountV2,
            organization_id: UUID,
            execute_queries: bool = True,  # Set this to True or False as needed
        ) -> None:
            """Test deleting an account from FalkorDB."""
            # Index the account
            await self.test_index_account(
                falkordb_indexing_lib=falkordb_indexing_lib,
                falkordb_client=falkordb_client,
                account=account,
                organization_id=organization_id,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)
            logger.info(f"graph: {graph}")

            # Generate the delete node query and execute it if execute_queries is True, else just return the query
            delete_node_query = await delete_complex_node_structure(
                graph=graph,
                node_type=AccountV2.object_id.object_name,
                node_id=str(account.id),
                execute_queries=execute_queries,
            )
            logger.info(f"delete_node_query: {delete_node_query}")

            # Verify the account was deleted if execute_queries is True, else just return the query
            result = await graph.query(
                f"MATCH (n:{AccountV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(account.id)},
            )
            logger.info(f"result: {result}")
            logger.info(f"result.result_set: {result.result_set}")

            if execute_queries:
                logger.info(
                    "User requested to execute the delete node query. Confirming that the account was deleted."
                )
                assert len(result.result_set) == 0
                logger.info(f"Account: {account.id}, was deleted as expected.")
            else:
                logger.info(
                    "User did not request to execute the delete node query. Verifying that the account was not deleted."
                )
                assert (
                    len(result.result_set) == 1
                )  # Expecting 1 node and no relationships

                # Extract the relationship types and verify the account ID for each
                for item in result.result_set:
                    # Ensure the source node is the correct account
                    assert item[0].properties["id"] == str(account.id)
                    logger.info(
                        f"Found account with id: {item[0].properties['id']}. Confirmed that it wasn't deleted."
                    )

        async def test_index_account_with_custom_fields(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            account_with_custom_fields: AccountV2,
            organization_id: UUID,
        ) -> None:
            """Test indexing an account with custom fields to FalkorDB."""
            logger.info(f"account_with_custom_fields: {account_with_custom_fields}")

            # Index the account, need to index all accounts for the organization
            await falkordb_indexing_lib.index_accounts(
                organization_id=organization_id,
                account_ids=[account_with_custom_fields.id],
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the account
            result = await graph.query(
                f"MATCH (n:{AccountV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(account_with_custom_fields.id)},
            )
            logger.info(f"result: {result}")
            logger.info(f"result.result_set: {result.result_set}")
            logger.info(f"result.result_set[0][0]: {result.result_set[0][0]}")

            # Verify the account was indexed AND has custom field data
            assert len(result.result_set) == 1
            account_node = result.result_set[0][0]

            # Verify basic properties
            assert account_node.properties["id"] == str(account_with_custom_fields.id)
            assert (
                account_node.properties["display_name"]
                == account_with_custom_fields.display_name
            )
            assert account_node.properties["organization_id"] == str(organization_id)

            # Assert that the enum fields are not None
            assert account_node.properties["status"] is not None
            assert account_node.properties["access_status"] is not None

            # Verify custom field data
            if account_with_custom_fields.custom_field_data:
                for (
                    field_id,
                    field_value,
                ) in account_with_custom_fields.custom_field_data.items():
                    logger.info(f"field_id: {field_id}, field_value: {field_value}")
                    assert account_node.properties[str(field_id)] == field_value

        async def test_index_two_accounts(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            account: AccountV2,
            account_with_custom_fields: AccountV2,
            organization_id: UUID,
        ) -> None:
            """Test indexing two accounts to FalkorDB."""

            # Index the account, need to index all accounts for the organization
            await falkordb_indexing_lib.index_accounts(
                organization_id=organization_id,
                account_ids=[account.id, account_with_custom_fields.id],
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)
            logger.info(f"graph: {graph}")

            # Query for the account
            result = await graph.query(
                f"MATCH (n:{AccountV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(account.id)},
            )
            logger.info(f"result.result_set: {result.result_set}")
            assert len(result.result_set) == 1
            account_node = result.result_set[0][0]
            assert account_node.properties["id"] == str(account.id)
            assert account_node.properties["display_name"] == account.display_name
            assert account_node.properties["organization_id"] == str(organization_id)
            # Assert that the enum fields are not None
            assert account_node.properties["status"] is not None
            assert account_node.properties["access_status"] is not None

            # Query for the account with custom fields
            result = await graph.query(
                f"MATCH (n:{AccountV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(account_with_custom_fields.id)},
            )
            logger.info(f"result.result_set: {result.result_set}")
            assert len(result.result_set) == 1
            account_node = result.result_set[0][0]
            assert account_node.properties["id"] == str(account_with_custom_fields.id)
            assert (
                account_node.properties["display_name"]
                == account_with_custom_fields.display_name
            )
            assert account_node.properties["organization_id"] == str(organization_id)
            # Assert that the enum fields are not None
            assert account_node.properties["status"] is not None
            assert account_node.properties["access_status"] is not None

            # Verify custom field data
            if account_with_custom_fields.custom_field_data:
                for (
                    field_id,
                    field_value,
                ) in account_with_custom_fields.custom_field_data.items():
                    logger.info(f"field_id: {field_id}, field_value: {field_value}")
                    assert account_node.properties[str(field_id)] == field_value

    class TestIndexContacts:
        async def test_index_contact(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            contact: ContactV2,
            organization_id: UUID,
        ) -> None:
            """Test indexing a contact to FalkorDB."""
            # Index the contact
            await falkordb_indexing_lib.index_contacts(
                organization_id=organization_id,
                contact_ids=[contact.id],
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the contact
            result = await graph.query(
                f"MATCH (n:{ContactV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(contact.id)},
            )

            # Verify the contact was indexed
            assert len(result.result_set) == 1
            contact_node = result.result_set[0][0]
            assert contact_node.properties["id"] == str(contact.id)
            assert contact_node.properties["first_name"] == contact.first_name
            assert contact_node.properties["organization_id"] == str(organization_id)

        # TODO: Rewrite this to call indexing function with update_edges set to True
        @pytest.mark.skip(
            reason="Skipping this test as the expected workflow is not implemented yet"
        )
        async def test_update_contact(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            metadata_service: MetadataService,
            contact: ContactV2,
            organization_id: UUID,
            execute_queries: bool = True,  # Set this to True or False as needed
        ) -> None:
            """Test the update of a contact in FalkorDB."""
            # Index the contact
            await self.test_index_contact(
                falkordb_indexing_lib=falkordb_indexing_lib,
                falkordb_client=falkordb_client,
                contact=contact,
                organization_id=organization_id,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)
            logger.info(f"graph: {graph}")

            logger.info(f"MATTY FRESH contact: {contact}")

            # Get the original nodes
            original_node_query = await graph.query(
                f"MATCH (n:{ContactV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(contact.id)},
            )
            logger.info(f"original_node_query: {original_node_query}")
            logger.info(
                f"original_node_query.result_set: {original_node_query.result_set}"
            )
            original_contact_node = original_node_query.result_set[0][0]

            # Get the original stage node
            original_stage_query = await graph.query(
                "MATCH (n:select_list_value {id: $id}) RETURN n",
                params={"id": str(contact.stage.id)},
            )
            logger.info(f"original_stage_query: {original_stage_query}")
            logger.info(
                f"original_stage_query.result_set: {original_stage_query.result_set}"
            )
            original_stage_node = original_stage_query.result_set[0][0]

            # Get the original contact email node
            original_contact_email_query = await graph.query(
                "MATCH (n:contact_email {id: $id}) RETURN n",
                params={"id": str(contact.contact_emails[0].id)},
            )
            logger.info(f"original_contact_email_query: {original_contact_email_query}")
            logger.info(
                f"original_contact_email_query.result_set: {original_contact_email_query.result_set}"
            )
            original_contact_email_node = original_contact_email_query.result_set[0][0]

            # Get the original contact account association node
            original_contact_account_association_query = await graph.query(
                "MATCH (n:contact_account_association) RETURN n",
            )
            logger.info(
                f"original_contact_account_association_query: {original_contact_account_association_query}"
            )
            logger.info(
                f"original_contact_account_association_query.result_set: {original_contact_account_association_query.result_set}"
            )
            original_contact_account_association_node = (
                original_contact_account_association_query.result_set[0][0]
            )

            # Convert contact to dictionary
            contact_dict = falkordb_indexing_lib._convert_model_to_dict(contact)
            contact_props = falkordb_client.clean_props(contact_dict)
            logger.info(f"OLD contact_props: {contact_props}")

            # Update contact props
            contact_props["display_name"] = "Matty Fresh TEST Contact"
            contact_props["stage"]["display_value"] = "Hot"
            contact_props["last_name"] = "Contact AY LMAO"
            contact_props["primary_email"] = "<EMAIL>"
            contact_props["contact_emails"][0]["email"] = "<EMAIL>"
            contact_props["contact_account_associations"][0]["account_id"] = (
                "MATTY FRESH ACCOUNT ID"
            )

            logger.info(f"NEW contact_props: {contact_props}")

            org_schema = await metadata_service.get_organization_domain_object_schema(
                organization_id=organization_id,
            )

            # Generate the update node query and execute it if execute_queries is True, else just return the query
            update_node_query = (
                await falkordb_indexing_lib.update_complex_node_structure(
                    graph=graph,
                    node_type=ContactV2.object_id.object_name,
                    node_id=str(contact.id),
                    data=contact_props,
                    schema=org_schema,
                    execute_queries=execute_queries,
                )
            )
            logger.info(f"update_node_query: {update_node_query}")

            # Verify the contact was updated if execute_queries is True, else just return the query
            contact_result = await graph.query(
                f"MATCH (n:{ContactV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(contact.id)},
            )
            logger.info(f"contact_result: {contact_result}")
            logger.info(f"contact_result.result_set: {contact_result.result_set}")
            contact_node = contact_result.result_set[0][0]

            stage_result = await graph.query(
                "MATCH (n:select_list_value {id: $id}) RETURN n",  # Single curly braces
                params={"id": str(contact.stage.id)},
            )
            logger.info(f"stage_result: {stage_result}")
            logger.info(f"stage_result.result_set: {stage_result.result_set}")
            stage_node = stage_result.result_set[0][0]

            contact_email_result = await graph.query(
                "MATCH (n:contact_email {id: $id}) RETURN n",  # Single curly braces
                params={"id": str(contact.contact_emails[0].id)},
            )
            logger.info(f"contact_email_result: {contact_email_result}")
            logger.info(
                f"contact_email_result.result_set: {contact_email_result.result_set}"
            )
            contact_email_node = contact_email_result.result_set[0][0]

            if execute_queries:
                logger.info(
                    "User requested to execute the update node query. Confirming that the contact was updated."
                )

                assert len(contact_result.result_set) == 1
                contact_node = contact_result.result_set[0][0]

                logger.info(f"contact: {contact}")
                logger.info(f"vars(contact): {vars(contact)}")
                contact_account_association_result = await graph.query(
                    "MATCH (n:contact_account_association) RETURN n",  # Note: contact_account_association does not have an ID we can grab from contact. It's autogenerated
                )
                logger.info(
                    f"contact_account_association_result: {contact_account_association_result}"
                )
                logger.info(
                    f"contact_account_association_result.result_set: {contact_account_association_result.result_set}"
                )
                contact_account_association_node = (
                    contact_account_association_result.result_set[0][0]
                )

                # Check that we didn't lose any properties
                # TODO: This part is failing, missing "contact_phone_numbers"
                logger.info(
                    f"len(contact_node.properties): {len(contact_node.properties)}"
                )
                logger.info(
                    f"len(original_contact_node.properties): {len(original_contact_node.properties)}"
                )
                # assert len(contact_node.properties) == len(original_contact_node.properties)

                logger.info(f"len(stage_node.properties): {len(stage_node.properties)}")
                logger.info(
                    f"len(original_stage_node.properties): {len(original_stage_node.properties)}"
                )
                assert len(stage_node.properties) == len(original_stage_node.properties)

                logger.info(
                    f"len(contact_email_node.properties): {len(contact_email_node.properties)}"
                )
                logger.info(
                    f"len(original_contact_email_node.properties): {len(original_contact_email_node.properties)}"
                )
                assert len(contact_email_node.properties) == len(
                    original_contact_email_node.properties
                )

                logger.info(
                    f"len(contact_account_association_node.properties): {len(contact_account_association_node.properties)}"
                )
                logger.info(
                    f"len(original_contact_account_association_node.properties): {len(original_contact_account_association_node.properties)}"
                )
                assert len(contact_account_association_node.properties) == len(
                    original_contact_account_association_node.properties
                )

                # Check that the properties were updated
                assert (
                    contact_node.properties["display_name"]
                    == "Matty Fresh TEST Contact"
                )
                assert contact_node.properties["last_name"] == "Contact AY LMAO"
                assert contact_node.properties["primary_email"] == "<EMAIL>"
                assert stage_node.properties["display_value"] == "Hot"
                assert (
                    contact_email_node.properties["email"]
                    == "<EMAIL>"
                )
                assert (
                    contact_account_association_node.properties["account_id"]
                    == "MATTY FRESH ACCOUNT ID"
                )
            else:
                logger.info(
                    "User did not request to execute the update node query. Verifying that the contact was not updated."
                )
                assert len(contact_result.result_set) == 1
                contact_node = contact_result.result_set[0][0]
                assert contact_node.properties["display_name"] == contact.display_name
                assert contact_node.properties["last_name"] == contact.last_name
                assert contact_node.properties["primary_email"] == contact.primary_email
                assert (
                    stage_node.properties["display_value"]
                    == contact.stage.display_value
                )
                assert (
                    contact_email_node.properties["email"]
                    == contact.contact_emails[0].email
                )

        async def test_delete_contact(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            contact: ContactV2,
            organization_id: UUID,
            execute_queries: bool = True,  # Set this to True or False as needed
        ) -> None:
            """Test the deletion of a contact from FalkorDB."""
            # Index the contact
            await self.test_index_contact(
                falkordb_indexing_lib=falkordb_indexing_lib,
                falkordb_client=falkordb_client,
                contact=contact,
                organization_id=organization_id,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)
            logger.info(f"graph: {graph}")

            # Generate the delete node query and execute it if execute_queries is True, else just return the query
            delete_node_query = await delete_complex_node_structure(
                graph=graph,
                node_type=ContactV2.object_id.object_name,
                node_id=str(contact.id),
                execute_queries=execute_queries,
            )
            logger.info(f"delete_node_query: {delete_node_query}")

            # Verify the contact and its relationships were deleted if execute_queries is True, else just return the query
            result = await graph.query(
                f"MATCH (n:{ContactV2.object_id.object_name} {{id: $id}})-[r]->(m) RETURN n, r, m",
                params={"id": str(contact.id)},
            )

            if execute_queries:
                logger.info(
                    "User requested to execute the delete node query. Confirming that the contact and its relationships were deleted."
                )
                assert len(result.result_set) == 0
                logger.info(f"Contact: {contact.id}, was deleted as expected.")
            else:
                logger.info(
                    "User did not request to execute the delete node query. Verifying that the contact and its relationships were not deleted."
                )
                assert len(result.result_set) == 3  # Expecting 3 relationships

                # Extract the relationship types and verify the contact ID for each
                returned_relations = set()
                for item in result.result_set:
                    # Ensure the source node is the correct contact
                    assert item[0].properties["id"] == str(contact.id)
                    # Add the relationship type to the set
                    returned_relations.add(item[1].relation)
                    # Log the details for debugging
                    logger.info(
                        f"Found relationship: {item[1].relation} from contact {item[0].properties['id']}"
                    )

                # Define the expected relationship types
                expected_relations = {
                    "stage",
                    "contact_account_associations",
                    "contact_emails",
                }

                # Verify that all expected relationships were found, regardless of order
                assert returned_relations == expected_relations
                logger.info(f"Verified expected relations: {expected_relations}")

        async def test_index_contact_with_custom_fields(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            contact_with_custom_fields: ContactV2,
            organization_id: UUID,
        ) -> None:
            """Test indexing a contact with custom fields to FalkorDB."""
            logger.info(f"contact_with_custom_fields: {contact_with_custom_fields}")

            # Index the contact
            await falkordb_indexing_lib.index_contacts(
                organization_id=organization_id,
                contact_ids=[contact_with_custom_fields.id],
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the contact
            result = await graph.query(
                f"MATCH (n:{ContactV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(contact_with_custom_fields.id)},
            )

            # Verify the contact was indexed AND has custom field data
            assert len(result.result_set) == 1
            contact_node = result.result_set[0][0]

            # Verify basic properties
            assert contact_node.properties["id"] == str(contact_with_custom_fields.id)
            assert (
                contact_node.properties["first_name"]
                == contact_with_custom_fields.first_name
            )
            assert contact_node.properties["organization_id"] == str(organization_id)

            # Verify custom field data
            if contact_with_custom_fields.custom_field_data:
                for (
                    field_id,
                    field_value,
                ) in contact_with_custom_fields.custom_field_data.items():
                    logger.info(f"field_id: {field_id}, field_value: {field_value}")
                    assert contact_node.properties[str(field_id)] == field_value

        async def test_index_two_contacts(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            contact: ContactV2,
            contact_with_custom_fields: ContactV2,
            organization_id: UUID,
        ) -> None:
            """Test indexing two contacts to FalkorDB."""
            # Index the contacts
            await falkordb_indexing_lib.index_contacts(
                organization_id=organization_id,
                contact_ids=[contact.id, contact_with_custom_fields.id],
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the contact
            result = await graph.query(
                f"MATCH (n:{ContactV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(contact.id)},
            )
            assert len(result.result_set) == 1
            contact_node = result.result_set[0][0]
            assert contact_node.properties["id"] == str(contact.id)

            # Query for the contact with custom fields
            result = await graph.query(
                f"MATCH (n:{ContactV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(contact_with_custom_fields.id)},
            )
            assert len(result.result_set) == 1
            contact_node = result.result_set[0][0]
            assert contact_node.properties["id"] == str(contact_with_custom_fields.id)
            assert (
                contact_node.properties["first_name"]
                == contact_with_custom_fields.first_name
            )
            assert contact_node.properties["organization_id"] == str(organization_id)

            # Verify custom field data
            if contact_with_custom_fields.custom_field_data:
                for (
                    field_id,
                    field_value,
                ) in contact_with_custom_fields.custom_field_data.items():
                    logger.info(f"field_id: {field_id}, field_value: {field_value}")
                    assert contact_node.properties[str(field_id)] == field_value

        async def test_index_contact_account_roles_for_organization(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            contact: ContactV2,
            organization_id: UUID,
        ) -> None:
            """Test indexing contact account roles to FalkorDB."""
            # Index the contact account roles
            contact_ids = {contact.id}
            await (
                falkordb_indexing_lib.index_all_contact_account_roles_for_organization(
                    organization_id=organization_id,
                    contact_ids=contact_ids,
                )
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the contact account roles
            result = await graph.query(
                f"MATCH (n:{ContactAccountRole.object_id.object_name}) RETURN n",
            )
            logger.info(f"result: {result}")
            logger.info(f"result.result_set: {result.result_set}")
            contact_account_role_nodes = result.result_set
            logger.info(f"contact_account_role_nodes: {contact_account_role_nodes}")
            logger.info(
                f"contact_account_role_nodes[0][0]: {contact_account_role_nodes[0][0]}"
            )
            logger.info(
                f"contact_account_role_nodes[0][0].properties: {contact_account_role_nodes[0][0].properties}"
            )

            assert len(contact_account_role_nodes) == 1
            contact_account_role_node = contact_account_role_nodes[0][0]
            assert contact_account_role_node.properties["contact_id"] == str(contact.id)
            assert contact_account_role_node.properties["account_id"] == str(
                contact.primary_account_id
            )

        async def test_delete_contact_account_roles(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            contact: ContactV2,
            organization_id: UUID,
            execute_queries: bool = True,  # Set this to True or False as needed
        ) -> None:
            """Test deleting contact account roles from FalkorDB."""
            # Index the contact account roles first
            await self.test_index_contact_account_roles_for_organization(
                falkordb_indexing_lib=falkordb_indexing_lib,
                falkordb_client=falkordb_client,
                contact=contact,
                organization_id=organization_id,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Grab the contact account role node to get its id
            result = await graph.query(
                f"MATCH (n:{ContactAccountRole.object_id.object_name}) RETURN n",
            )
            contact_account_role_nodes = result.result_set
            contact_account_role_node = contact_account_role_nodes[0][0]

            # Generate the delete node query and potentially execute it
            delete_node_query = await delete_complex_node_structure(
                graph=graph,
                node_type=ContactAccountRole.object_id.object_name,
                node_id=str(contact_account_role_node.properties["id"]),
                execute_queries=execute_queries,
            )
            logger.info(f"Generated delete_node_query: {delete_node_query}")

            # Re-query for the contact account role to verify it was deleted if execute_queries is True, else just return the query
            result = await graph.query(
                f"MATCH (n:{ContactAccountRole.object_id.object_name}) RETURN n",
            )
            contact_account_role_nodes = result.result_set

            if execute_queries:
                logger.info(
                    "Executed delete query. Verifying contact account role was deleted."
                )
                assert len(contact_account_role_nodes) == 0
                logger.info(f"Contact account role {contact.id} successfully deleted.")
            else:
                logger.info(
                    "Did not execute delete query. Verifying that the contact account role was not deleted."
                )
                assert (
                    len(contact_account_role_nodes) == 1
                )  # Expecting 1 node, 0 relationships
                assert contact_account_role_nodes[0][0].properties["id"] == str(
                    contact_account_role_node.properties["id"]
                )
                assert contact_account_role_nodes[0][0].properties["contact_id"] == str(
                    contact.id
                )
                assert contact_account_role_nodes[0][0].properties["account_id"] == str(
                    contact.primary_account_id
                )

        async def test_index_contact_account_roles_for_organization_in_batches(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            organization_id: UUID,
            contact2: ContactV2,
            account: AccountV2,
            offset: int = 0,
            batch_size: int = 1,
        ) -> None:
            logger.info(f"contact2.id: {contact2.id}")
            logger.info(f"account.id: {account.id}")

            # Index the contact account roles
            await falkordb_indexing_lib.index_all_contact_account_roles_for_organization_in_batches(
                organization_id=organization_id,
                offset=offset,
                batch_size=batch_size,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the contact account roles
            result = await graph.query(
                f"MATCH (n:{ContactAccountRole.object_id.object_name}) RETURN n",
            )
            logger.info(f"result: {result}")
            logger.info(f"result.result_set: {result.result_set}")
            contact_account_role_nodes = result.result_set
            logger.info(f"contact_account_role_nodes: {contact_account_role_nodes}")
            logger.info(
                f"contact_account_role_nodes[0][0]: {contact_account_role_nodes[0][0]}"
            )
            logger.info(
                f"contact_account_role_nodes[0][0].properties: {contact_account_role_nodes[0][0].properties}"
            )

            # Flatten to a list of nodes
            nodes = [row[0] for row in contact_account_role_nodes]
            assert len(nodes) == 1

            # Collect (contact_id, account_id) pairs from the nodes
            found_pairs = {
                (n.properties["contact_id"], n.properties["account_id"]) for n in nodes
            }
            expected_pairs = {
                (str(contact2.id), str(account.id)),
            }

            logger.info(f"Found pairs: {found_pairs}")
            logger.info(f"Expected pairs: {expected_pairs}")

            assert found_pairs == expected_pairs, (
                f"Expected pairs {expected_pairs}, but found {found_pairs}. "
                f"Node properties: {[n.properties for n in nodes]}"
            )

        async def test_index_contact_pipeline_roles_for_organization(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            organization_id: UUID,
            contact: ContactV2,
            pipeline: PipelineV2,
        ) -> None:
            """Test indexing contact pipeline roles to FalkorDB."""
            # Index the contact pipeline roles
            await (
                falkordb_indexing_lib.index_all_contact_pipeline_roles_for_organization(
                    organization_id=organization_id,
                    contact_id=contact.id,
                    pipeline_id=pipeline.id,
                )
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the contact pipeline roles
            result = await graph.query(
                f"MATCH (n:{ContactPipelineRole.object_id.object_name}) RETURN n",
            )
            contact_pipeline_role_nodes = result.result_set
            logger.info(f"contact_pipeline_role_nodes: {contact_pipeline_role_nodes}")
            logger.info(
                f"contact_pipeline_role_nodes[0][0]: {contact_pipeline_role_nodes[0][0]}"
            )
            logger.info(
                f"contact_pipeline_role_nodes[0][0].properties: {contact_pipeline_role_nodes[0][0].properties}"
            )

            assert len(contact_pipeline_role_nodes) == 1
            contact_pipeline_role_node = contact_pipeline_role_nodes[0][0]
            assert contact_pipeline_role_node.properties["contact_id"] == str(
                contact.id
            )
            assert contact_pipeline_role_node.properties["pipeline_id"] == str(
                pipeline.id
            )

        async def test_delete_contact_pipeline_roles(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            contact: ContactV2,
            pipeline: PipelineV2,
            organization_id: UUID,
            execute_queries: bool = True,  # Set this to True or False as needed
        ) -> None:
            """Test deleting contact pipeline roles from FalkorDB."""

            # Index the contact pipeline roles first
            await self.test_index_contact_pipeline_roles_for_organization(
                falkordb_indexing_lib=falkordb_indexing_lib,
                falkordb_client=falkordb_client,
                contact=contact,
                pipeline=pipeline,
                organization_id=organization_id,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Grab the contact pipeline role node to get its id
            result = await graph.query(
                f"MATCH (n:{ContactPipelineRole.object_id.object_name}) RETURN n",
            )
            contact_pipeline_role_nodes = result.result_set
            contact_pipeline_role_node = contact_pipeline_role_nodes[0][0]

            # Generate the delete node query and potentially execute it
            delete_node_query = await delete_complex_node_structure(
                graph=graph,
                node_type=ContactPipelineRole.object_id.object_name,
                node_id=str(contact_pipeline_role_node.properties["id"]),
                execute_queries=execute_queries,
            )
            logger.info(f"Generated delete_node_query: {delete_node_query}")

            # Re-query for the contact account role to verify it was deleted if execute_queries is True, else just return the query
            result = await graph.query(
                f"MATCH (n:{ContactPipelineRole.object_id.object_name}) RETURN n",
            )
            contact_pipeline_role_nodes = result.result_set

            if execute_queries:
                logger.info(
                    "Executed delete query. Verifying contact pipeline role was deleted."
                )
                assert len(result.result_set) == 0
                logger.info(f"Contact pipeline role {contact.id} successfully deleted.")
            else:
                logger.info(
                    "Did not execute delete query. Verifying that the contact pipeline role was not deleted."
                )
                assert (
                    len(contact_pipeline_role_nodes) == 1
                )  # Expecting 1 node, 0 relationships
                assert contact_pipeline_role_nodes[0][0].properties["id"] == str(
                    contact_pipeline_role_node.properties["id"]
                )
                assert contact_pipeline_role_nodes[0][0].properties[
                    "contact_id"
                ] == str(contact.id)
                assert contact_pipeline_role_nodes[0][0].properties[
                    "pipeline_id"
                ] == str(pipeline.id)

        async def test_index_all_contact_pipeline_roles_for_organization_in_batches(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            organization_id: UUID,
            contact: ContactV2,
            pipeline: PipelineV2,
            offset: int = 0,
            batch_size: int = 5,
        ) -> None:
            """Test indexing contact pipeline roles for an organization in batches."""
            logger.info(f"contact: {contact}")
            logger.info(f"pipeline: {pipeline}")

            # Index the contact pipeline roles
            await falkordb_indexing_lib.index_all_contact_pipeline_roles_for_organization_in_batches(
                organization_id=organization_id,
                offset=offset,
                batch_size=batch_size,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the contact account roles
            result = await graph.query(
                f"MATCH (n:{ContactPipelineRole.object_id.object_name}) RETURN n",
            )
            logger.info(f"result: {result}")
            logger.info(f"result.result_set: {result.result_set}")
            contact_pipeline_role_nodes = result.result_set
            logger.info(f"contact_pipeline_role_nodes: {contact_pipeline_role_nodes}")
            logger.info(
                f"contact_pipeline_role_nodes[0][0]: {contact_pipeline_role_nodes[0][0]}"
            )
            logger.info(
                f"contact_pipeline_role_nodes[0][0].properties: {contact_pipeline_role_nodes[0][0].properties}"
            )

            # Flatten to a list of nodes
            nodes = [row[0] for row in contact_pipeline_role_nodes]
            assert len(nodes) == 1

            # Collect (contact_id, pipeline_id) pairs from the nodes
            found_pairs = {
                (n.properties["contact_id"], n.properties["pipeline_id"]) for n in nodes
            }
            expected_pairs = {
                (str(contact.id), str(pipeline.id)),
            }

            logger.info(f"Found pairs: {found_pairs}")
            logger.info(f"Expected pairs: {expected_pairs}")

            assert found_pairs == expected_pairs, (
                f"Expected pairs {expected_pairs}, but found {found_pairs}. "
                f"Node properties: {[n.properties for n in nodes]}"
            )

    class TestIndexPipelines:
        async def test_index_pipeline(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            pipeline: PipelineV2,
            organization_id: UUID,
        ) -> None:
            """Test indexing a pipeline to FalkorDB."""
            # Index the pipeline
            await falkordb_indexing_lib.index_pipelines(
                organization_id=organization_id,
                pipeline_ids=[pipeline.id],
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the pipeline
            result = await graph.query(
                f"MATCH (n:{PipelineV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(pipeline.id)},
            )

            # Verify the pipeline was indexed
            assert len(result.result_set) == 1
            pipeline_node = result.result_set[0][0]
            assert pipeline_node.properties["id"] == str(pipeline.id)
            assert pipeline_node.properties["display_name"] == pipeline.display_name
            assert pipeline_node.properties["organization_id"] == str(organization_id)

        # TODO: Rewrite this to call indexing function with update_edges set to True
        @pytest.mark.skip(
            reason="Skipping this test as the expected workflow is not implemented yet"
        )
        async def test_update_pipeline(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            metadata_service: MetadataService,
            pipeline: PipelineV2,
            organization_id: UUID,
            execute_queries: bool = True,  # Set this to True or False as needed
        ) -> None:
            """Test updating a pipeline in FalkorDB."""
            # Index the pipeline first
            await self.test_index_pipeline(
                falkordb_indexing_lib=falkordb_indexing_lib,
                falkordb_client=falkordb_client,
                pipeline=pipeline,
                organization_id=organization_id,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Get the original node
            original_node_query = await graph.query(
                f"MATCH (n:{PipelineV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(pipeline.id)},
            )
            logger.info(f"original_node_query: {original_node_query}")
            logger.info(
                f"original_node_query.result_set: {original_node_query.result_set}"
            )
            original_pipeline_node = original_node_query.result_set[0][0]
            logger.info(f"original_pipeline_node: {original_pipeline_node}")
            logger.info(
                f"original_pipeline_node.properties: {original_pipeline_node.properties}"
            )

            # Get the original stage node
            original_stage_query = await graph.query(
                "MATCH (n:pipeline_stage_select_list_value {id: $id}) RETURN n",
                params={"id": str(pipeline.stage.id)},
            )
            logger.info(f"original_stage_query: {original_stage_query}")
            logger.info(
                f"original_stage_query.result_set: {original_stage_query.result_set}"
            )
            original_stage_node = original_stage_query.result_set[0][0]
            logger.info(f"original_stage_node: {original_stage_node}")
            logger.info(
                f"original_stage_node.properties: {original_stage_node.properties}"
            )

            # Convert pipeline to dictionary
            pipeline_dict = falkordb_indexing_lib._convert_model_to_dict(pipeline)
            pipeline_props = falkordb_client.clean_props(pipeline_dict)
            logger.info(f"OLD pipeline_props: {pipeline_props}")

            # Update pipeline props
            pipeline_props["display_name"] = "Matty Fresh TEST Pipeline"
            pipeline_props["status"] = "DEAL"
            pipeline_props["stage"]["display_value"] = "Hot"
            logger.info(f"NEW pipeline_props: {pipeline_props}")

            # Get the organization schema
            org_schema = await metadata_service.get_organization_domain_object_schema(
                organization_id=organization_id,
            )

            # Generate the update node query and execute it if execute_queries is True, else just return the query
            update_node_query = (
                await falkordb_indexing_lib.update_complex_node_structure(
                    graph=graph,
                    node_type=PipelineV2.object_id.object_name,
                    node_id=str(pipeline.id),
                    data=pipeline_props,
                    schema=org_schema,
                    execute_queries=execute_queries,
                )
            )
            logger.info(f"update_node_query: {update_node_query}")

            # Verify the pipeline was updated if execute_queries is True, else just return the query
            pipeline_result = await graph.query(
                f"MATCH (n:{PipelineV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(pipeline.id)},
            )
            logger.info(f"pipeline_result: {pipeline_result}")
            logger.info(f"pipeline_result.result_set: {pipeline_result.result_set}")
            pipeline_node = pipeline_result.result_set[0][0]

            stage_result = await graph.query(
                "MATCH (n:pipeline_stage_select_list_value {id: $id}) RETURN n",  # Single curly braces
                params={"id": str(pipeline.stage.id)},
            )
            logger.info(f"stage_result: {stage_result}")
            logger.info(f"stage_result.result_set: {stage_result.result_set}")
            stage_node = stage_result.result_set[0][0]

            if execute_queries:
                logger.info("Executed update query. Verifying pipeline was updated.")

                # Check that we didn't lose any properties
                logger.info(
                    f"len(pipeline_node.properties): {len(pipeline_node.properties)}"
                )
                logger.info(
                    f"len(original_pipeline_node.properties): {len(original_pipeline_node.properties)}"
                )
                assert len(pipeline_node.properties) == len(
                    original_pipeline_node.properties
                )

                logger.info(f"len(stage_node.properties): {len(stage_node.properties)}")
                logger.info(
                    f"len(original_stage_node.properties): {len(original_stage_node.properties)}"
                )
                assert len(stage_node.properties) == len(original_stage_node.properties)

                # Check that the properties were updated
                assert (
                    pipeline_node.properties["display_name"]
                    == "Matty Fresh TEST Pipeline"
                )
                assert pipeline_node.properties["status"] == "DEAL"
                assert stage_node.properties["display_value"] == "Hot"
            else:
                logger.info(
                    "Did not execute update query. Verifying that the pipeline was not updated."
                )

                assert pipeline_node.properties["display_name"] == pipeline.display_name
                assert pipeline_node.properties["status"] == pipeline.status
                assert (
                    stage_node.properties["display_value"]
                    == pipeline.stage.display_value
                )

        async def test_delete_pipeline(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            pipeline: PipelineV2,
            organization_id: UUID,
            execute_queries: bool = True,  # Set this to True or False as needed
        ) -> None:
            """Test deleting a pipeline from FalkorDB."""

            # Index the pipeline first
            await self.test_index_pipeline(
                falkordb_indexing_lib=falkordb_indexing_lib,
                falkordb_client=falkordb_client,
                pipeline=pipeline,
                organization_id=organization_id,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Generate the delete node query and potentially execute it
            delete_node_query = await delete_complex_node_structure(
                graph=graph,
                node_type=PipelineV2.object_id.object_name,
                node_id=str(pipeline.id),
                execute_queries=execute_queries,
            )
            logger.info(f"Generated delete_node_query: {delete_node_query}")

            # Verify the pipeline and its relationships based on execute_queries flag
            # Query for the pipeline and any outgoing relationships
            result = await graph.query(
                f"MATCH (n:{PipelineV2.object_id.object_name} {{id: $id}})-[r]->(m) RETURN n, r, m",
                params={"id": str(pipeline.id)},  # Added missing id parameter
            )

            if execute_queries:
                logger.info(
                    "Executed delete query. Verifying pipeline and relationships are gone."
                )
                assert len(result.result_set) == 0
                logger.info(
                    f"Pipeline {pipeline.id} and its relationships successfully deleted."
                )
            else:
                logger.info(
                    "Did not execute delete query. Verifying pipeline and 'stage' relationship still exist."
                )
                # Expecting 1 relationship: pipeline -> stage
                assert len(result.result_set) == 1
                pipeline_node, relationship, _ = result.result_set[0]

                # Verify the source node is the correct pipeline
                assert pipeline_node.properties["id"] == str(pipeline.id)
                # Verify the relationship type is 'stage'
                assert relationship.relation == "stage"

                logger.info(
                    f"Verified pipeline {pipeline.id} and its 'stage' relationship were not deleted."
                )

        async def test_index_pipeline_with_custom_fields(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            pipeline_with_custom_fields: PipelineV2,
            organization_id: UUID,
        ) -> None:
            """Test indexing a pipeline with custom fields to FalkorDB."""
            logger.info(f"pipeline_with_custom_fields: {pipeline_with_custom_fields}")

            # Index the pipeline
            await falkordb_indexing_lib.index_pipelines(
                organization_id=organization_id,
                pipeline_ids=[pipeline_with_custom_fields.id],
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the pipeline
            result = await graph.query(
                f"MATCH (n:{PipelineV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(pipeline_with_custom_fields.id)},
            )

            # Verify the pipeline was indexed AND has custom field data
            assert len(result.result_set) == 1
            pipeline_node = result.result_set[0][0]

            # Verify basic properties
            assert pipeline_node.properties["id"] == str(pipeline_with_custom_fields.id)
            assert (
                pipeline_node.properties["display_name"]
                == pipeline_with_custom_fields.display_name
            )
            assert pipeline_node.properties["organization_id"] == str(organization_id)

            # Verify custom field data
            if pipeline_with_custom_fields.custom_field_data:
                for (
                    field_id,
                    field_value,
                ) in pipeline_with_custom_fields.custom_field_data.items():
                    logger.info(f"field_id: {field_id}, field_value: {field_value}")
                    assert pipeline_node.properties[str(field_id)] == field_value

        async def test_index_two_pipelines(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            pipeline: PipelineV2,
            pipeline_with_custom_fields: PipelineV2,
            organization_id: UUID,
        ) -> None:
            """Test indexing two pipelines to FalkorDB."""
            # Index the pipelines
            await falkordb_indexing_lib.index_pipelines(
                organization_id=organization_id,
                pipeline_ids=[pipeline.id, pipeline_with_custom_fields.id],
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the pipeline
            result = await graph.query(
                f"MATCH (n:{PipelineV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(pipeline.id)},
            )
            assert len(result.result_set) == 1
            pipeline_node = result.result_set[0][0]
            assert pipeline_node.properties["id"] == str(pipeline.id)

            # Query for the pipeline with custom fields
            result = await graph.query(
                f"MATCH (n:{PipelineV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(pipeline_with_custom_fields.id)},
            )
            assert len(result.result_set) == 1
            pipeline_node = result.result_set[0][0]
            assert pipeline_node.properties["id"] == str(pipeline_with_custom_fields.id)
            assert (
                pipeline_node.properties["display_name"]
                == pipeline_with_custom_fields.display_name
            )

            # Verify custom field data
            if pipeline_with_custom_fields.custom_field_data:
                for (
                    field_id,
                    field_value,
                ) in pipeline_with_custom_fields.custom_field_data.items():
                    logger.info(f"field_id: {field_id}, field_value: {field_value}")

    class TestIndexMeetings:
        async def test_index_meeting(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            meeting: MeetingV2,
            organization_id: UUID,
        ) -> None:
            """Test indexing a meeting to FalkorDB."""
            # Index the meeting
            await falkordb_indexing_lib.index_meetings([meeting.id])

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the meeting
            logger.info(f"meeting.id: {meeting.id}")
            result = await graph.query(
                f"MATCH (n:{MeetingV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(meeting.id)},
            )

            # Verify the meeting was indexed
            assert len(result.result_set) == 1
            meeting_node = result.result_set[0][0]
            assert meeting_node.properties["id"] == str(meeting.id)
            assert meeting_node.properties["title"] == meeting.title
            assert meeting_node.properties["organization_id"] == str(organization_id)

            # Check if nested structure with relationships is created

            # Check for invitees relationships (list of dicts)
            invitees_query = f"""
                MATCH (m:{MeetingV2.object_id.object_name} {{id: $id}})-[r:invitees]->(i:invitees_item)
                RETURN i
            """
            invitees_result = await graph.query(
                invitees_query, params={"id": str(meeting.id)}
            )

            # There should be at least one invitee as per fixture
            assert len(invitees_result.result_set) > 0, "No invitee nodes found"

            # Check for any outgoing relationships from the meeting node
            # This is a more general approach that doesn't depend on specific field names
            relationships_query = f"""
                MATCH (m:{MeetingV2.object_id.object_name} {{id: $id}})-[r]->(n)
                RETURN type(r) as relation_type, count(n) as node_count
            """
            relationships_result = await graph.query(
                relationships_query, params={"id": str(meeting.id)}
            )

            # There should be at least one type of relationship
            assert len(relationships_result.result_set) > 0, (
                "No relationships found from meeting node"
            )

            # Log the relationships for debugging
            logger.info(f"Meeting relationships: {relationships_result.result_set}")

        async def test_delete_meeting(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            meeting: MeetingV2,
            organization_id: UUID,
            execute_queries: bool = True,  # Set this to True or False as needed
        ) -> None:
            """Test deleting a meeting from FalkorDB."""
            # Index the meeting
            await self.test_index_meeting(
                falkordb_indexing_lib=falkordb_indexing_lib,
                falkordb_client=falkordb_client,
                meeting=meeting,
                organization_id=organization_id,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Generate the delete node query and potentially execute it
            delete_node_query = await delete_complex_node_structure(
                graph=graph,
                node_type=MeetingV2.object_id.object_name,
                node_id=str(meeting.id),
                execute_queries=execute_queries,
            )
            logger.info(f"Generated delete_node_query: {delete_node_query}")

            # Verify the meeting and its relationships based on execute_queries flag
            # Query for the meeting and any outgoing relationships
            result = await graph.query(
                f"MATCH (n:{MeetingV2.object_id.object_name} {{id: $id}})-[r]->(m) RETURN n, r, m",
                params={"id": str(meeting.id)},
            )
            if execute_queries:
                logger.info(
                    "Executed delete query. Verifying meeting and relationships are gone."
                )
                assert len(result.result_set) == 0
                logger.info(
                    f"Meeting {meeting.id} and its relationships successfully deleted."
                )
            else:
                logger.info(
                    "Did not execute delete query. Verifying meeting and 'invitees' relationship still exist."
                )
                # Expecting 1 relationship: meeting -> invitees
                assert len(result.result_set) == 1
                meeting_node, relationship, invitees_node = result.result_set[0]

                # Verify the source node is the correct meeting
                assert meeting_node.properties["id"] == str(meeting.id)
                assert meeting_node.properties["title"] == meeting.title
                assert meeting_node.properties["organization_id"] == str(
                    organization_id
                )
                assert meeting_node.properties["created_by_user_id"] == str(
                    meeting.created_by_user_id
                )
                assert meeting_node.properties["account_id"] == str(meeting.account_id)
                assert meeting_node.properties["pipeline_id"] == str(
                    meeting.pipeline_id
                )

                # Verify the relationship type is 'invitees'
                assert relationship.relation == "invitees"

                # Verify the invitees node is the correct invitees
                assert invitees_node.properties["user_id"] == str(
                    meeting.created_by_user_id
                )

                logger.info(
                    f"Verified meeting {meeting.id} and its 'invitees' relationship were not deleted."
                )

    class TestIndexCustomObjects:
        async def test_index_custom_object_happy_path(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            custom_object_with_data: tuple[CustomObject, CustomField, CustomObjectData],
            organization_id: UUID,
        ) -> None:
            """Test indexing a custom object to FalkorDB."""
            # Extract custom object data from fixture
            _, _, custom_object_data = custom_object_with_data

            # Index the custom object
            await falkordb_indexing_lib.index_custom_objects(
                organization_id=organization_id,
                cobject_data_ids=[custom_object_data.id],
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the custom object
            result = await graph.query(
                f"MATCH (n:`{custom_object_data.cobject_metadata_id}` {{id: $id}}) RETURN n",
                params={"id": str(custom_object_data.id)},
            )

            # Verify the custom object was indexed
            assert len(result.result_set) == 1
            custom_object_node = result.result_set[0][0]
            assert custom_object_node.properties["id"] == str(custom_object_data.id)
            assert custom_object_node.properties["organization_id"] == str(
                organization_id
            )
            assert custom_object_node.properties["cobject_metadata_id"] == str(
                custom_object_data.cobject_metadata_id
            )
            assert (
                "object_display_name" in custom_object_node.properties
            )  # Note: There's no easy way to get this value, so just check for its presence.

            # Verify custom field data is correct
            # Note: This is really messy to verify. We should come up with better tests for validating custom field data on custom objects.
            # value_1 is a map of field_id to text: "Test Text Value"
            if isinstance(custom_object_data.value_1, TextFieldProperty) and isinstance(
                custom_object_data.value_1.value_by_field_id, dict
            ):
                value_1_map = custom_object_data.value_1.value_by_field_id
                for k in value_1_map:
                    assert custom_object_node.properties[str(k)] == value_1_map[k].text

            # value_2 is a map of field_id to number: 123.45
            if isinstance(
                custom_object_data.value_2, NumericFieldProperty
            ) and isinstance(custom_object_data.value_2.value_by_field_id, dict):
                value_2_map = custom_object_data.value_2.value_by_field_id
                for k in value_2_map:
                    assert str(custom_object_node.properties[str(k)]) == str(
                        value_2_map[k].number
                    )

        async def test_index_custom_object_using_index_all_function(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            custom_object_with_data: tuple[CustomObject, CustomField, CustomObjectData],
            organization_id: UUID,
        ) -> None:
            """Test indexing a custom object to FalkorDB."""
            # Extract custom object data from fixture
            _, _, custom_object_data = custom_object_with_data

            # Index the custom object using the all function. Only fundamental difference from previous test
            await falkordb_indexing_lib.index_all_custom_objects_for_organization(
                organization_id=organization_id,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the first custom object
            result = await graph.query(
                f"MATCH (n:`{custom_object_data.cobject_metadata_id}` {{id: $id}}) RETURN n",
                params={"id": str(custom_object_data.id)},
            )

            # Verify the first custom object was indexed
            assert len(result.result_set) == 1
            custom_object_node = result.result_set[0][0]
            assert custom_object_node.properties["id"] == str(custom_object_data.id)
            assert custom_object_node.properties["organization_id"] == str(
                organization_id
            )
            assert custom_object_node.properties["cobject_metadata_id"] == str(
                custom_object_data.cobject_metadata_id
            )
            assert (
                "object_display_name" in custom_object_node.properties
            )  # Note: There's no easy way to get this value, so just check for its presence.

        async def test_delete_custom_object(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            custom_object_with_data: tuple[CustomObject, CustomField, CustomObjectData],
            organization_id: UUID,
            execute_queries: bool = True,  # Set this to True or False as needed
        ) -> None:
            """Test deleting a custom object from FalkorDB."""
            # Extract custom object data from fixture
            _, _, custom_object_data = custom_object_with_data

            # Index the custom object
            await self.test_index_custom_object_happy_path(
                falkordb_indexing_lib=falkordb_indexing_lib,
                falkordb_client=falkordb_client,
                custom_object_with_data=custom_object_with_data,
                organization_id=organization_id,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Delete the custom object
            await delete_complex_node_structure(
                graph=graph,
                node_type=f"`{custom_object_data.cobject_metadata_id}`",
                node_id=str(custom_object_data.id),
                execute_queries=execute_queries,
            )

            # Verify the custom object was deleted
            result = await graph.query(
                f"MATCH (n:`{custom_object_data.cobject_metadata_id}` {{id: $id}}) RETURN n",
                params={"id": str(custom_object_data.id)},
            )
            if execute_queries:
                logger.info("Executed delete query. Verifying custom object is gone.")
                assert len(result.result_set) == 0
                logger.info(
                    f"Custom object: {custom_object_data.id} is successfully deleted."
                )
            else:
                logger.info(
                    "Did not execute delete query. Verifying custom object still exists."
                )
                assert (
                    len(result.result_set) == 1
                )  # Expecting 1 node and no relationships

                # Extract the relationship types and verify the account ID for each
                for item in result.result_set:
                    # Ensure the source node is the correct account
                    assert item[0].properties["id"] == str(custom_object_data.id)
                    logger.info(
                        f"Found custom object with id: {item[0].properties['id']}. Confirmed that it wasn't deleted."
                    )

        async def test_index_two_same_custom_objects(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            two_same_custom_objects_with_data: list[
                tuple[CustomObject, CustomField, CustomObjectData]
            ],
            organization_id: UUID,
        ) -> None:
            # Extract custom object data from fixture
            _, _, custom_object_data1 = two_same_custom_objects_with_data[0]
            _, _, custom_object_data2 = two_same_custom_objects_with_data[1]
            logger.info(f"custom_object_data1: {custom_object_data1}")
            logger.info(f"custom_object_data2: {custom_object_data2}")

            # Index the custom object
            await falkordb_indexing_lib.index_custom_objects(
                organization_id=organization_id,
                cobject_data_ids=[custom_object_data1.id, custom_object_data2.id],
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the first custom object
            result = await graph.query(
                f"MATCH (n:`{custom_object_data1.cobject_metadata_id}` {{id: $id}}) RETURN n",
                params={"id": str(custom_object_data1.id)},
            )

            # Verify the first custom object was indexed
            assert len(result.result_set) == 1
            custom_object_node = result.result_set[0][0]
            assert custom_object_node.properties["id"] == str(custom_object_data1.id)
            assert custom_object_node.properties["organization_id"] == str(
                organization_id
            )
            assert custom_object_node.properties["cobject_metadata_id"] == str(
                custom_object_data1.cobject_metadata_id
            )
            assert (
                "object_display_name" in custom_object_node.properties
            )  # Note: There's no easy way to get this value, so just check for its presence.

            # Query for the second custom object
            result = await graph.query(
                f"MATCH (n:`{custom_object_data2.cobject_metadata_id}` {{id: $id}}) RETURN n",
                params={"id": str(custom_object_data2.id)},
            )

            # Verify the second custom object was indexed
            assert len(result.result_set) == 1
            custom_object_node = result.result_set[0][0]
            assert custom_object_node.properties["id"] == str(custom_object_data2.id)
            assert custom_object_node.properties["organization_id"] == str(
                organization_id
            )
            assert custom_object_node.properties["cobject_metadata_id"] == str(
                custom_object_data2.cobject_metadata_id
            )
            assert (
                "object_display_name" in custom_object_node.properties
            )  # Note: There's no easy way to get this value, so just check for its presence.

        async def test_index_different_two_custom_objects(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            two_different_custom_objects_with_data: list[
                tuple[CustomObject, CustomField, CustomObjectData]
            ],
            organization_id: UUID,
        ) -> None:
            # Extract custom object data from fixture
            _, _, custom_object_data1 = two_different_custom_objects_with_data[0]
            _, _, custom_object_data2 = two_different_custom_objects_with_data[1]
            logger.info(f"custom_object_data1: {custom_object_data1}")
            logger.info(f"custom_object_data2: {custom_object_data2}")

            # Index the custom object
            await falkordb_indexing_lib.index_custom_objects(
                organization_id=organization_id,
                cobject_data_ids=[custom_object_data1.id, custom_object_data2.id],
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the first custom object
            result = await graph.query(
                f"MATCH (n:`{custom_object_data1.cobject_metadata_id}` {{id: $id}}) RETURN n",
                params={"id": str(custom_object_data1.id)},
            )

            # Verify the first custom object was indexed
            assert len(result.result_set) == 1
            custom_object_node = result.result_set[0][0]
            assert custom_object_node.properties["id"] == str(custom_object_data1.id)
            assert custom_object_node.properties["organization_id"] == str(
                organization_id
            )
            assert custom_object_node.properties["cobject_metadata_id"] == str(
                custom_object_data1.cobject_metadata_id
            )
            assert (
                "object_display_name" in custom_object_node.properties
            )  # Note: There's no easy way to get this value, so just check for its presence.

            # Query for the second custom object
            result = await graph.query(
                f"MATCH (n:`{custom_object_data2.cobject_metadata_id}` {{id: $id}}) RETURN n",
                params={"id": str(custom_object_data2.id)},
            )

            # Verify the second custom object was indexed
            assert len(result.result_set) == 1
            custom_object_node = result.result_set[0][0]
            assert custom_object_node.properties["id"] == str(custom_object_data2.id)
            assert custom_object_node.properties["organization_id"] == str(
                organization_id
            )
            assert custom_object_node.properties["cobject_metadata_id"] == str(
                custom_object_data2.cobject_metadata_id
            )
            assert (
                "object_display_name" in custom_object_node.properties
            )  # Note: There's no easy way to get this value, so just check for its presence.

    class TestIndexUsers:
        async def test_index_user(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            make_user_org: None,  # Needed to associate user to org
            # Temp args
            organization_id: UUID,
            user_id: UUID,
        ) -> None:
            """Test indexing a user to FalkorDB."""
            await falkordb_indexing_lib.index_users(
                user_ids=[user_id], organization_id=organization_id
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the user
            result = await graph.query(
                f"MATCH (n:{OrganizationUserV2.object_id.object_name} {{id: $id}}) RETURN n",
                params={"id": str(user_id)},
            )

            # Verify the user was indexed
            assert len(result.result_set) == 1
            user_node = result.result_set[0][0]
            assert user_node.properties["id"] == str(user_id)
            assert user_node.properties["organization_id"] == str(organization_id)

    class TestCreateRelationship:
        async def test_create_relationship(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            account: AccountV2,
            contact: ContactV2,
            organization_id: UUID,
        ) -> None:
            """Test creating a relationship between entities in FalkorDB."""
            # First, index the account and contact
            await falkordb_indexing_lib.index_accounts(
                organization_id=organization_id,
                account_ids=[account.id],
            )
            await falkordb_indexing_lib.index_contacts(
                organization_id=organization_id,
                contact_ids=[contact.id],
            )

            # Create a relationship between them
            await falkordb_indexing_lib.create_relationship(
                organization_id,
                AccountV2.object_id.object_name,
                account.id,
                ContactV2.object_id.object_name,
                contact.id,
                "HAS_CONTACT",
                from_id_field_name="id",
                to_id_field_name="id",
                properties={"created_at": int(datetime.now().timestamp() * 1000)},
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Query for the relationship
            query = f"""
                MATCH (a:{AccountV2.object_id.object_name} {{id: $account_id}})
                -[r:HAS_CONTACT]->
                (c:{ContactV2.object_id.object_name} {{id: $contact_id}})
                RETURN a, r, c
            """
            result = await graph.query(
                query,
                params={
                    "account_id": str(account.id),
                    "contact_id": str(contact.id),
                },
            )

            # Verify the relationship was created
            assert len(result.result_set) == 1
            account_node, relationship, contact_node = result.result_set[0]
            assert account_node.properties["id"] == str(account.id)
            assert contact_node.properties["id"] == str(contact.id)
            assert relationship.relation == "HAS_CONTACT"
            assert "created_at" in relationship.properties

        async def test_create_relationship_in_batches(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            _user: None,  # TODO: See if we can create the user in the function
            user_service: UserService,
            contact_query_service: ContactQueryService,
            pipeline_query_service: PipelineQueryService,
            user_id: UUID,
            account: AccountV2,
            contact: ContactV2,
            pipeline: PipelineV2,
            association_records: None,
            organization_id: UUID,
            account_to_contact_association: CustomObjectAssociation,
            account_to_pipeline_association: CustomObjectAssociation,
        ) -> None:
            """Test creating a relationship between entities in FalkorDB in batches."""

            # Index the user
            await falkordb_indexing_lib.index_users(
                user_ids=[user_id],
                organization_id=organization_id,
            )

            # Index the account, contact, and pipeline
            await falkordb_indexing_lib.index_accounts(
                organization_id=organization_id,
                account_ids=[account.id],
            )
            await falkordb_indexing_lib.index_contacts(
                organization_id=organization_id,
                contact_ids=[contact.id],
            )
            await falkordb_indexing_lib.index_pipelines(
                organization_id=organization_id,
                pipeline_ids=[pipeline.id],
            )

            # Index the contact account roles
            await (
                falkordb_indexing_lib.index_all_contact_account_roles_for_organization(
                    organization_id=organization_id,
                    contact_ids={contact.id},
                )
            )
            contact_account_roles = await contact_query_service.list_contact_account_roles_by_organization_id_in_batches(
                organization_id=organization_id
            )
            contact_account_role = contact_account_roles[0]
            logger.info(f"contact_account_role: {contact_account_role}")

            await (
                falkordb_indexing_lib.index_all_contact_pipeline_roles_for_organization(
                    organization_id=organization_id,
                    contact_id=contact.id,
                    pipeline_id=pipeline.id,
                )
            )
            contact_pipeline_roles = (
                await pipeline_query_service.list_contact_pipeline_roles_by_contact_id(
                    organization_id=organization_id,
                    contact_id=contact.id,
                    pipeline_id=pipeline.id,
                )
            )
            contact_pipeline_role = contact_pipeline_roles[0]

            """ Create User here """
            list_users = await user_service.list_users_v2(
                organization_id=organization_id,
                only_include_user_ids={user_id},
            )
            logger.info(f"list_users: {list_users}")
            user = list_users[0]

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Log the custom association records
            logger.info(f"association_records: {association_records}")

            # Create domain object level relationships and custom associations between them
            await falkordb_indexing_lib.index_relationships_for_organization_in_batches(
                organization_id,
                batch_size=1,
            )

            """ Check that domain object level relationships had been created """
            """ Verify the account relationships were created """
            """ Verify account__to__created_by_user relationship was created """
            account__to__created_by_user_result = await graph.query(
                f"MATCH (n:{AccountV2.object_id.object_name})-[r:account__to__created_by_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
            )
            logger.info(
                f"account__to__created_by_user_result: {account__to__created_by_user_result}"
            )
            logger.info(
                f"vars(account__to__created_by_user_result): {vars(account__to__created_by_user_result)}"
            )
            logger.info(
                f"account__to__created_by_user_result.result_set: {account__to__created_by_user_result.result_set}"
            )

            assert len(account__to__created_by_user_result.result_set) == 1, (
                f"Expected 1 result, got {len(account__to__created_by_user_result.result_set)} for org_id: {organization_id}"
            )

            # Check the account side of the relationship
            account__to__created_by_user_account_side = (
                account__to__created_by_user_result.result_set[0][0]
            )
            logger.info(
                f"account__to__created_by_user_account_side: {account__to__created_by_user_account_side}"
            )
            assert account__to__created_by_user_account_side.properties["id"] == str(
                account.id
            )
            assert account__to__created_by_user_account_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                account__to__created_by_user_account_side.properties["display_name"]
                == account.display_name
            )

            # Check the edge of the relationship
            account__to__created_by_user_edge = (
                account__to__created_by_user_result.result_set[0][1]
            )
            logger.info(
                f"account__to__created_by_user_edge: {account__to__created_by_user_edge}"
            )
            logger.info(
                f"vars(account__to__created_by_user_edge): {vars(account__to__created_by_user_edge)}"
            )
            assert (
                account__to__created_by_user_edge.relation
                == "account__to__created_by_user"
            )

            # Check the User side of the relationship
            account__to__created_by_user_user_side = (
                account__to__created_by_user_result.result_set[0][2]
            )
            logger.info(
                f"account__to__created_by_user_user_side: {account__to__created_by_user_user_side}"
            )
            logger.info(
                f"vars(account__to__created_by_user_user_side): {vars(account__to__created_by_user_user_side)}"
            )
            assert account__to__created_by_user_user_side.properties["id"] == str(
                user.id
            )
            assert account__to__created_by_user_user_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                account__to__created_by_user_user_side.properties["display_name"]
                == user.display_name
            )

            """ Verify the account__to__updated_by_user relationship was created """
            account__to__updated_by_user_result = await graph.query(
                f"MATCH (n:{AccountV2.object_id.object_name})-[r:account__to__updated_by_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
            )
            logger.info(
                f"account__to__updated_by_user_result: {account__to__updated_by_user_result}"
            )
            assert len(account__to__updated_by_user_result.result_set) == 1

            # Check the account side of the relationship
            account__to__updated_by_user_account_side = (
                account__to__updated_by_user_result.result_set[0][0]
            )
            logger.info(
                f"account__to__updated_by_user_account_side: {account__to__updated_by_user_account_side}"
            )
            assert account__to__updated_by_user_account_side.properties["id"] == str(
                account.id
            )
            assert account__to__updated_by_user_account_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                account__to__updated_by_user_account_side.properties["display_name"]
                == account.display_name
            )

            # Check the edge of the relationship
            account__to__updated_by_user_edge = (
                account__to__updated_by_user_result.result_set[0][1]
            )
            logger.info(
                f"account__to__updated_by_user_edge: {account__to__updated_by_user_edge}"
            )
            logger.info(
                f"vars(account__to__updated_by_user_edge): {vars(account__to__updated_by_user_edge)}"
            )
            assert (
                account__to__updated_by_user_edge.relation
                == "account__to__updated_by_user"
            )

            # Check the User side of the relationship
            account__to__updated_by_user_user_side = (
                account__to__updated_by_user_result.result_set[0][2]
            )
            logger.info(
                f"account__to__updated_by_user_user_side: {account__to__updated_by_user_user_side}"
            )
            logger.info(
                f"vars(account__to__updated_by_user_user_side): {vars(account__to__updated_by_user_user_side)}"
            )
            assert account__to__updated_by_user_user_side.properties["id"] == str(
                user.id
            )
            assert account__to__updated_by_user_user_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                account__to__updated_by_user_user_side.properties["display_name"]
                == user.display_name
            )

            """ Verify the account__to__owner_user relationship was created """
            account__to__owner_user_result = await graph.query(
                f"MATCH (n:{AccountV2.object_id.object_name})-[r:account__to__owner_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
            )
            logger.info(
                f"account__to__owner_user_result: {account__to__owner_user_result}"
            )
            logger.info(
                f"vars(account__to__owner_user_result): {vars(account__to__owner_user_result)}"
            )
            assert len(account__to__owner_user_result.result_set) == 1

            # Check the account side of the relationship
            account__to__owner_user_account_side = (
                account__to__owner_user_result.result_set[0][0]
            )
            logger.info(
                f"account__to__owner_user_account_side: {account__to__owner_user_account_side}"
            )
            assert account__to__owner_user_account_side.properties["id"] == str(
                account.id
            )
            assert account__to__owner_user_account_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                account__to__owner_user_account_side.properties["display_name"]
                == account.display_name
            )

            # Check the edge of the relationship
            account__to__owner_user_edge = account__to__owner_user_result.result_set[0][
                1
            ]
            logger.info(f"account__to__owner_user_edge: {account__to__owner_user_edge}")
            logger.info(
                f"vars(account__to__owner_user_edge): {vars(account__to__owner_user_edge)}"
            )
            assert account__to__owner_user_edge.relation == "account__to__owner_user"

            # Check the User side of the relationship
            account__to__owner_user_user_side = (
                account__to__owner_user_result.result_set[0][2]
            )
            logger.info(
                f"account__to__owner_user_user_side: {account__to__owner_user_user_side}"
            )
            logger.info(
                f"vars(account__to__owner_user_user_side): {vars(account__to__owner_user_user_side)}"
            )
            assert account__to__owner_user_user_side.properties["id"] == str(user.id)
            assert account__to__owner_user_user_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                account__to__owner_user_user_side.properties["display_name"]
                == user.display_name
            )

            """ Verify Contact domain relationships were created """
            """ Verify contact__to__owner_user relationship was created """
            contact_to_owner_user_relationship_result = await graph.query(
                f"MATCH (n:{ContactV2.object_id.object_name})-[r:contact__to__owner_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
            )
            logger.info(
                f"contact_to_owner_user_relationship_result: {contact_to_owner_user_relationship_result}"
            )
            logger.info(
                f"vars(contact_to_owner_user_relationship_result): {vars(contact_to_owner_user_relationship_result)}"
            )
            assert len(contact_to_owner_user_relationship_result.result_set) == 1

            # Check the contact side of the relationship
            contact_to_owner_user_relationship_contact_side = (
                contact_to_owner_user_relationship_result.result_set[0][0]
            )
            logger.info(
                f"contact_to_owner_user_relationship_contact_side: {contact_to_owner_user_relationship_contact_side}"
            )
            assert contact_to_owner_user_relationship_contact_side.properties[
                "id"
            ] == str(contact.id)
            assert contact_to_owner_user_relationship_contact_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                contact_to_owner_user_relationship_contact_side.properties[
                    "display_name"
                ]
                == contact.display_name
            )

            # Check the edge of the relationship
            contact_to_owner_user_relationship_edge = (
                contact_to_owner_user_relationship_result.result_set[0][1]
            )
            logger.info(
                f"contact_to_owner_user_relationship_edge: {contact_to_owner_user_relationship_edge}"
            )
            logger.info(
                f"vars(contact_to_owner_user_relationship_edge): {vars(contact_to_owner_user_relationship_edge)}"
            )
            assert (
                contact_to_owner_user_relationship_edge.relation
                == "contact__to__owner_user"
            )

            # Check the user side of the relationship
            contact_to_owner_user_relationship_user_side = (
                contact_to_owner_user_relationship_result.result_set[0][2]
            )
            logger.info(
                f"contact_to_owner_user_relationship_user_side: {contact_to_owner_user_relationship_user_side}"
            )
            logger.info(
                f"vars(contact_to_owner_user_relationship_user_side): {vars(contact_to_owner_user_relationship_user_side)}"
            )
            assert contact_to_owner_user_relationship_user_side.properties["id"] == str(
                user.id
            )
            assert contact_to_owner_user_relationship_user_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                contact_to_owner_user_relationship_user_side.properties["display_name"]
                == user.display_name
            )

            """ Verify the contact__to__updated_by_user relationship was created """
            contact_to_updated_by_user_relationship_result = await graph.query(
                f"MATCH (n:{ContactV2.object_id.object_name})-[r:contact__to__updated_by_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
            )
            logger.info(
                f"contact_to_updated_by_user_relationship_result: {contact_to_updated_by_user_relationship_result}"
            )
            logger.info(
                f"vars(contact_to_updated_by_user_relationship_result): {vars(contact_to_updated_by_user_relationship_result)}"
            )
            assert len(contact_to_updated_by_user_relationship_result.result_set) == 1

            # Check the contact side of the relationship
            contact_to_updated_by_user_relationship_contact_side = (
                contact_to_updated_by_user_relationship_result.result_set[0][0]
            )
            logger.info(
                f"contact_to_updated_by_user_relationship_contact_side: {contact_to_updated_by_user_relationship_contact_side}"
            )
            assert contact_to_updated_by_user_relationship_contact_side.properties[
                "id"
            ] == str(contact.id)
            assert contact_to_updated_by_user_relationship_contact_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                contact_to_updated_by_user_relationship_contact_side.properties[
                    "display_name"
                ]
                == contact.display_name
            )

            # Check the edge of the relationship
            contact_to_updated_by_user_relationship_edge = (
                contact_to_updated_by_user_relationship_result.result_set[0][1]
            )
            logger.info(
                f"contact_to_updated_by_user_relationship_edge: {contact_to_updated_by_user_relationship_edge}"
            )
            logger.info(
                f"vars(contact_to_updated_by_user_relationship_edge): {vars(contact_to_updated_by_user_relationship_edge)}"
            )
            assert (
                contact_to_updated_by_user_relationship_edge.relation
                == "contact__to__updated_by_user"
            )

            # Check the user side of the relationship
            contact_to_updated_by_user_relationship_user_side = (
                contact_to_updated_by_user_relationship_result.result_set[0][2]
            )
            logger.info(
                f"contact_to_updated_by_user_relationship_user_side: {contact_to_updated_by_user_relationship_user_side}"
            )
            logger.info(
                f"vars(contact_to_updated_by_user_relationship_user_side): {vars(contact_to_updated_by_user_relationship_user_side)}"
            )
            assert contact_to_updated_by_user_relationship_user_side.properties[
                "id"
            ] == str(user.id)
            assert contact_to_updated_by_user_relationship_user_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                contact_to_updated_by_user_relationship_user_side.properties[
                    "display_name"
                ]
                == user.display_name
            )

            """ Verify the contact__to__created_by_user relationship was created """
            contact_to_created_by_user_relationship_result = await graph.query(
                f"MATCH (n:{ContactV2.object_id.object_name})-[r:contact__to__created_by_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
            )
            logger.info(
                f"contact_to_created_by_user_relationship_result: {contact_to_created_by_user_relationship_result}"
            )
            logger.info(
                f"vars(contact_to_created_by_user_relationship_result): {vars(contact_to_created_by_user_relationship_result)}"
            )
            assert len(contact_to_created_by_user_relationship_result.result_set) == 1

            # Check the contact side of the relationship
            contact_to_created_by_user_relationship_contact_side = (
                contact_to_created_by_user_relationship_result.result_set[0][0]
            )
            logger.info(
                f"contact_to_created_by_user_relationship_contact_side: {contact_to_created_by_user_relationship_contact_side}"
            )
            assert contact_to_created_by_user_relationship_contact_side.properties[
                "id"
            ] == str(contact.id)
            assert contact_to_created_by_user_relationship_contact_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                contact_to_created_by_user_relationship_contact_side.properties[
                    "display_name"
                ]
                == contact.display_name
            )

            # Check the edge of the relationship
            contact_to_created_by_user_relationship_edge = (
                contact_to_created_by_user_relationship_result.result_set[0][1]
            )
            logger.info(
                f"contact_to_created_by_user_relationship_edge: {contact_to_created_by_user_relationship_edge}"
            )
            logger.info(
                f"vars(contact_to_created_by_user_relationship_edge): {vars(contact_to_created_by_user_relationship_edge)}"
            )
            assert (
                contact_to_created_by_user_relationship_edge.relation
                == "contact__to__created_by_user"
            )

            # Check the user side of the relationship
            contact_to_created_by_user_relationship_user_side = (
                contact_to_created_by_user_relationship_result.result_set[0][2]
            )
            logger.info(
                f"contact_to_created_by_user_relationship_user_side: {contact_to_created_by_user_relationship_user_side}"
            )
            logger.info(
                f"vars(contact_to_created_by_user_relationship_user_side): {vars(contact_to_created_by_user_relationship_user_side)}"
            )
            assert contact_to_created_by_user_relationship_user_side.properties[
                "id"
            ] == str(user.id)
            assert contact_to_created_by_user_relationship_user_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                contact_to_created_by_user_relationship_user_side.properties[
                    "display_name"
                ]
                == user.display_name
            )

            """ Verify the Primary Account (contact_to_primary_account) relationship was created """
            primary_account_relationship_result = await graph.query(
                f"MATCH (n:{ContactV2.object_id.object_name})-[r:contact__to__primary_account]->(m:{AccountV2.object_id.object_name}) RETURN n, r, m"
            )
            logger.info(
                f"primary_account_relationship_result: {primary_account_relationship_result}"
            )
            logger.info(
                f"vars(primary_account_relationship_result): {vars(primary_account_relationship_result)}"
            )
            assert len(primary_account_relationship_result.result_set) == 1

            # Check the contact side of the relationship
            primary_account_relationship_contact_side = (
                primary_account_relationship_result.result_set[0][0]
            )
            logger.info(
                f"primary_account_relationship_contact_side: {primary_account_relationship_contact_side}"
            )
            assert primary_account_relationship_contact_side.properties["id"] == str(
                contact.id
            )
            assert primary_account_relationship_contact_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                primary_account_relationship_contact_side.properties["display_name"]
                == contact.display_name
            )

            # Check the edge of the relationship
            primary_account_relationship_edge = (
                primary_account_relationship_result.result_set[0][1]
            )
            logger.info(
                f"primary_account_relationship_edge: {primary_account_relationship_edge}"
            )
            logger.info(
                f"vars(primary_account_relationship_edge): {vars(primary_account_relationship_edge)}"
            )
            assert (
                primary_account_relationship_edge.relation
                == "contact__to__primary_account"
            )

            # Check the account side of the relationship
            primary_account_relationship_account_side = (
                primary_account_relationship_result.result_set[0][2]
            )
            logger.info(
                f"primary_account_relationship_account_side: {primary_account_relationship_account_side}"
            )
            assert primary_account_relationship_account_side.properties["id"] == str(
                account.id
            )
            assert primary_account_relationship_account_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                primary_account_relationship_account_side.properties["display_name"]
                == account.display_name
            )

            """ Verify the contact_emails relationship was created """
            contact_emails_relationship_result = await graph.query(
                f"MATCH (n:{ContactV2.object_id.object_name})-[r:contact_emails]->(m) RETURN n, r, m"
            )
            logger.info(
                f"contact_emails_relationship_result: {contact_emails_relationship_result}"
            )
            logger.info(
                f"vars(contact_emails_relationship_result): {vars(contact_emails_relationship_result)}"
            )
            assert len(contact_emails_relationship_result.result_set) == 1

            # Check the contact side of the relationship
            contact_emails_relationship_contact_side = (
                contact_emails_relationship_result.result_set[0][0]
            )
            logger.info(
                f"contact_emails_relationship_contact_side: {contact_emails_relationship_contact_side}"
            )
            assert contact_emails_relationship_contact_side.properties["id"] == str(
                contact.id
            )
            assert contact_emails_relationship_contact_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                contact_emails_relationship_contact_side.properties["display_name"]
                == contact.display_name
            )

            # Check the edge of the relationship
            contact_emails_relationship_edge = (
                contact_emails_relationship_result.result_set[0][1]
            )
            logger.info(
                f"contact_emails_relationship_edge: {contact_emails_relationship_edge}"
            )
            logger.info(
                f"vars(contact_emails_relationship_edge): {vars(contact_emails_relationship_edge)}"
            )
            assert contact_emails_relationship_edge.relation == "contact_emails"

            # Check email side of the relationship
            contact_emails_relationship_email_side = (
                contact_emails_relationship_result.result_set[0][2]
            )
            logger.info(
                f"contact_emails_relationship_email_side: {contact_emails_relationship_email_side}"
            )
            logger.info(
                f"vars(contact_emails_relationship_email_side): {vars(contact_emails_relationship_email_side)}"
            )
            assert contact_emails_relationship_email_side.properties[
                "contact_id"
            ] == str(contact.id)
            assert (
                contact_emails_relationship_email_side.properties["email"]
                == contact.primary_email
            )

            """ Verify inbound relationships were created """
            """ account__from__contact_account_role """
            account_from_contact_account_role_result = await graph.query(
                f"MATCH (n:{ContactAccountRole.object_id.object_name})-[r:account__from__contact_account_role]->(m:{AccountV2.object_id.object_name}) RETURN n, r, m"
            )
            logger.info(
                f"account_from_contact_account_role_result: {account_from_contact_account_role_result}"
            )
            logger.info(
                f"vars(account_from_contact_account_role_result): {vars(account_from_contact_account_role_result)}"
            )
            assert len(account_from_contact_account_role_result.result_set) == 1

            # Check the contact account role side of the relationship
            account_from_contact_account_role_contact_account_role_side = (
                account_from_contact_account_role_result.result_set[0][0]
            )
            logger.info(
                f"account_from_contact_account_role_contact_account_role_side: {account_from_contact_account_role_contact_account_role_side}"
            )
            assert (
                account_from_contact_account_role_contact_account_role_side.properties[
                    "id"
                ]
                == str(contact_account_role.id)
            )

            # Check the edge of the relationship
            account_from_contact_account_role_edge = (
                account_from_contact_account_role_result.result_set[0][1]
            )
            logger.info(
                f"account_from_contact_account_role_edge: {account_from_contact_account_role_edge}"
            )
            logger.info(
                f"vars(account_from_contact_account_role_edge): {vars(account_from_contact_account_role_edge)}"
            )
            assert (
                account_from_contact_account_role_edge.relation
                == "account__from__contact_account_role"
            )

            # Check the account side of the relationship
            account_from_contact_account_role_account_side = (
                account_from_contact_account_role_result.result_set[0][2]
            )
            logger.info(
                f"account_from_contact_account_role_account_side: {account_from_contact_account_role_account_side}"
            )
            assert account_from_contact_account_role_account_side.properties[
                "id"
            ] == str(account.id)
            assert account_from_contact_account_role_account_side.properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                account_from_contact_account_role_account_side.properties[
                    "display_name"
                ]
                == account.display_name
            )

            """ contact__from__contact_account_role """
            contact_from_contact_account_role_result = await graph.query(
                f"MATCH (n:{ContactAccountRole.object_id.object_name})-[r:contact__from__contact_account_role]->(m:{ContactV2.object_id.object_name}) RETURN n, r, m"
            )
            logger.info(
                f"contact_from_contact_account_role_result: {contact_from_contact_account_role_result}"
            )
            logger.info(
                f"vars(contact_from_contact_account_role_result): {vars(contact_from_contact_account_role_result)}"
            )
            assert len(contact_from_contact_account_role_result.result_set) == 1

            # Check the contact account role side of the relationship
            contact_from_contact_account_role_contact_account_role_side = (
                contact_from_contact_account_role_result.result_set[0][0]
            )
            logger.info(
                f"contact_from_contact_account_role_contact_account_role_side: {contact_from_contact_account_role_contact_account_role_side}"
            )
            assert (
                contact_from_contact_account_role_contact_account_role_side.properties[
                    "id"
                ]
                == str(contact_account_role.id)
            )

            # Check the edge of the relationship
            contact_from_contact_account_role_edge = (
                contact_from_contact_account_role_result.result_set[0][1]
            )
            logger.info(
                f"contact_from_contact_account_role_edge: {contact_from_contact_account_role_edge}"
            )
            logger.info(
                f"vars(contact_from_contact_account_role_edge): {vars(contact_from_contact_account_role_edge)}"
            )
            assert (
                contact_from_contact_account_role_edge.relation
                == "contact__from__contact_account_role"
            )

            # Check the contact side of the relationship
            contact_from_contact_account_role_contact_side = (
                contact_from_contact_account_role_result.result_set[0][2]
            )
            logger.info(
                f"contact_from_contact_account_role_contact_side: {contact_from_contact_account_role_contact_side}"
            )
            assert contact_from_contact_account_role_contact_side.properties[
                "id"
            ] == str(contact.id)

            """ contact__from__contact_pipeline_role """
            contact_from_contact_pipeline_role_result = await graph.query(
                f"MATCH (n:{ContactPipelineRole.object_id.object_name})-[r:contact__from__contact_pipeline_role]->(m:{ContactV2.object_id.object_name}) RETURN n, r, m"
            )
            logger.info(
                f"contact_from_contact_pipeline_role_result: {contact_from_contact_pipeline_role_result}"
            )
            logger.info(
                f"vars(contact_from_contact_pipeline_role_result): {vars(contact_from_contact_pipeline_role_result)}"
            )
            assert len(contact_from_contact_pipeline_role_result.result_set) == 1

            # Check the contact pipeline role side of the relationship
            contact_from_contact_pipeline_role_contact_pipeline_role_side = (
                contact_from_contact_pipeline_role_result.result_set[0][0]
            )
            logger.info(
                f"contact_from_contact_pipeline_role_contact_pipeline_role_side: {contact_from_contact_pipeline_role_contact_pipeline_role_side}"
            )
            assert (
                contact_from_contact_pipeline_role_contact_pipeline_role_side.properties[
                    "id"
                ]
                == str(contact_pipeline_role.id)
            )

            # Check the edge of the relationship
            contact_from_contact_pipeline_role_edge = (
                contact_from_contact_pipeline_role_result.result_set[0][1]
            )
            logger.info(
                f"contact_from_contact_pipeline_role_edge: {contact_from_contact_pipeline_role_edge}"
            )
            logger.info(
                f"vars(contact_from_contact_pipeline_role_edge): {vars(contact_from_contact_pipeline_role_edge)}"
            )
            assert (
                contact_from_contact_pipeline_role_edge.relation
                == "contact__from__contact_pipeline_role"
            )

            # Check the contact side of the relationship
            contact_from_contact_pipeline_role_contact_side = (
                contact_from_contact_pipeline_role_result.result_set[0][2]
            )
            logger.info(
                f"contact_from_contact_pipeline_role_contact_side: {contact_from_contact_pipeline_role_contact_side}"
            )
            assert contact_from_contact_pipeline_role_contact_side.properties[
                "id"
            ] == str(contact.id)

            """ pipeline__from__contact_pipeline_role """
            pipeline_from_contact_pipeline_role_result = await graph.query(
                f"MATCH (n:{ContactPipelineRole.object_id.object_name})-[r:pipeline__from__contact_pipeline_role]->(m:{PipelineV2.object_id.object_name}) RETURN n, r, m"
            )
            logger.info(
                f"pipeline_from_contact_pipeline_role_result: {pipeline_from_contact_pipeline_role_result}"
            )
            logger.info(
                f"vars(pipeline_from_contact_pipeline_role_result): {vars(pipeline_from_contact_pipeline_role_result)}"
            )
            assert len(pipeline_from_contact_pipeline_role_result.result_set) == 1

            # Check the contact pipeline role side of the relationship
            pipeline_from_contact_pipeline_role_contact_pipeline_role_side = (
                pipeline_from_contact_pipeline_role_result.result_set[0][0]
            )
            logger.info(
                f"pipeline_from_contact_pipeline_role_contact_pipeline_role_side: {pipeline_from_contact_pipeline_role_contact_pipeline_role_side}"
            )
            assert (
                pipeline_from_contact_pipeline_role_contact_pipeline_role_side.properties[
                    "id"
                ]
                == str(contact_pipeline_role.id)
            )

            # Check the edge of the relationship
            pipeline_from_contact_pipeline_role_edge = (
                pipeline_from_contact_pipeline_role_result.result_set[0][1]
            )
            logger.info(
                f"pipeline_from_contact_pipeline_role_edge: {pipeline_from_contact_pipeline_role_edge}"
            )
            logger.info(
                f"vars(pipeline_from_contact_pipeline_role_edge): {vars(pipeline_from_contact_pipeline_role_edge)}"
            )
            assert (
                pipeline_from_contact_pipeline_role_edge.relation
                == "pipeline__from__contact_pipeline_role"
            )

            # Check the pipeline side of the relationship
            pipeline_from_contact_pipeline_role_pipeline_side = (
                pipeline_from_contact_pipeline_role_result.result_set[0][2]
            )
            logger.info(
                f"pipeline_from_contact_pipeline_role_pipeline_side: {pipeline_from_contact_pipeline_role_pipeline_side}"
            )
            assert pipeline_from_contact_pipeline_role_pipeline_side.properties[
                "id"
            ] == str(pipeline.id)
            assert (
                pipeline_from_contact_pipeline_role_pipeline_side.properties[
                    "display_name"
                ]
                == pipeline.display_name
            )

            """ Verify that contact_account_associations were created """
            contact_account_associations_result = await graph.query(
                f"MATCH (n:{ContactV2.object_id.object_name})-[r:contact_account_associations]->(m) RETURN n, r, m"
            )
            logger.info(
                f"contact_account_associations_result: {contact_account_associations_result}"
            )
            logger.info(
                f"vars(contact_account_associations_result): {vars(contact_account_associations_result)}"
            )
            assert len(contact_account_associations_result.result_set) == 1

            # Check the contact side of the relationship
            contact_account_associations_contact_side = (
                contact_account_associations_result.result_set[0][0]
            )
            logger.info(
                f"contact_account_associations_contact_side: {contact_account_associations_contact_side}"
            )
            assert contact_account_associations_contact_side.properties["id"] == str(
                contact.id
            )
            assert (
                contact_account_associations_contact_side.properties["display_name"]
                == contact.display_name
            )

            # Check the edge of the relationship
            contact_account_associations_edge = (
                contact_account_associations_result.result_set[0][1]
            )
            logger.info(
                f"contact_account_associations_edge: {contact_account_associations_edge}"
            )
            logger.info(
                f"vars(contact_account_associations_edge): {vars(contact_account_associations_edge)}"
            )
            assert (
                contact_account_associations_edge.relation
                == "contact_account_associations"
            )

            # Check the associations side of the relationship
            contact_account_associations_associations_side = (
                contact_account_associations_result.result_set[0][2]
            )
            logger.info(
                f"contact_account_associations_associations_side: {contact_account_associations_associations_side}"
            )
            assert contact_account_associations_associations_side.properties[
                "account_id"
            ] == str(account.id)
            assert contact_account_associations_associations_side.properties[
                "contact_id"
            ] == str(contact.id)

            """ Verify Custom Associations were created """
            """ Account to Contact association """
            account_to_contact_association_result = await graph.query(
                f"MATCH (n:{AccountV2.object_id.object_name})-[r:`{account_to_contact_association.id}`]->(m:{ContactV2.object_id.object_name}) RETURN n, r, m"
            )
            logger.info(
                f"account_to_contact_association_result: {account_to_contact_association_result}"
            )
            logger.info(
                f"vars(account_to_contact_association_result): {vars(account_to_contact_association_result)}"
            )

            # Get the account side of the relationship
            account_to_contact_association_account_side = (
                account_to_contact_association_result.result_set[0][0]
            )
            logger.info(
                f"account_to_contact_association_account_side: {account_to_contact_association_account_side}"
            )
            logger.info(
                f"vars(account_to_contact_association_account_side): {vars(account_to_contact_association_account_side)}"
            )

            assert account_to_contact_association_account_side.labels[0] == "account"
            assert account_to_contact_association_account_side.properties["id"] == str(
                account.id
            )
            assert account_to_contact_association_account_side.properties[
                "organization_id"
            ] == str(organization_id)

            # Get the edge of the relationship
            account_to_contact_association_edge = (
                account_to_contact_association_result.result_set[0][1]
            )
            logger.info(
                f"account_to_contact_association_edge: {account_to_contact_association_edge}"
            )
            logger.info(
                f"vars(account_to_contact_association_edge): {vars(account_to_contact_association_edge)}"
            )
            assert account_to_contact_association_edge.relation == str(
                account_to_contact_association.id
            )

            # Get the contact side of the relationship
            assert (
                account_to_contact_association_result.result_set[0][2].labels[0]
                == "contact"
            )
            assert account_to_contact_association_result.result_set[0][2].properties[
                "id"
            ] == str(contact.id)
            assert account_to_contact_association_result.result_set[0][2].properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                account_to_contact_association_result.result_set[0][2].properties[
                    "display_name"
                ]
                == contact.display_name
            )
            assert account_to_contact_association_result.result_set[0][2].properties[
                "stage_id"
            ] == str(contact.stage_id)
            assert account_to_contact_association_result.result_set[0][2].properties[
                "owner_user_id"
            ] == str(contact.owner_user_id)

            """ Account to Pipeline association """
            account_to_pipeline_association_result = await graph.query(
                f"MATCH (n:{AccountV2.object_id.object_name})-[r:`{account_to_pipeline_association.id}`]->(m:{PipelineV2.object_id.object_name}) RETURN n, r, m"
            )
            logger.info(
                f"account_to_pipeline_association_result: {account_to_pipeline_association_result}"
            )
            logger.info(
                f"vars(account_to_pipeline_association_result): {vars(account_to_pipeline_association_result)}"
            )

            # Get the account side of the relationship
            account_to_pipeline_association_account_side = (
                account_to_pipeline_association_result.result_set[0][0]
            )
            logger.info(
                f"account_to_pipeline_association_account_side: {account_to_pipeline_association_account_side}"
            )
            logger.info(
                f"vars(account_to_pipeline_association_account_side): {vars(account_to_pipeline_association_account_side)}"
            )
            assert account_to_pipeline_association_account_side.labels[0] == "account"

            # Get the edge of the relationship
            account_to_pipeline_association_edge = (
                account_to_pipeline_association_result.result_set[0][1]
            )
            logger.info(
                f"account_to_pipeline_association_edge: {account_to_pipeline_association_edge}"
            )
            logger.info(
                f"vars(account_to_pipeline_association_edge): {vars(account_to_pipeline_association_edge)}"
            )
            assert account_to_pipeline_association_edge.relation == str(
                account_to_pipeline_association.id
            )

            # Get the pipeline side of the relationship
            assert (
                account_to_pipeline_association_result.result_set[0][2].labels[0]
                == "pipeline"
            )
            assert account_to_pipeline_association_result.result_set[0][2].properties[
                "id"
            ] == str(pipeline.id)
            assert account_to_pipeline_association_result.result_set[0][2].properties[
                "organization_id"
            ] == str(organization_id)
            assert (
                account_to_pipeline_association_result.result_set[0][2].properties[
                    "display_name"
                ]
                == pipeline.display_name
            )
            assert account_to_pipeline_association_result.result_set[0][2].properties[
                "stage_id"
            ] == str(pipeline.stage_id)
            assert account_to_pipeline_association_result.result_set[0][2].properties[
                "owner_user_id"
            ] == str(pipeline.owner_user_id)

    class TestUpdateCustomAssociation:
        class TestValidCustomAssociationCases:
            async def _create_initial_relationship(
                self,
                falkordb_indexing_lib: FalkorDBIndexingLib,
                account: AccountV2,
                contact: ContactV2,
                organization_id: UUID,
            ) -> None:
                """Helper method to create initial relationship for tests."""
                # Index the account and contact
                await falkordb_indexing_lib.index_accounts(
                    organization_id=organization_id,
                    account_ids=[account.id],
                )
                await falkordb_indexing_lib.index_contacts(
                    organization_id=organization_id,
                    contact_ids=[contact.id],
                )

                # Create the relationship
                await falkordb_indexing_lib.create_relationship(
                    organization_id=organization_id,
                    from_type="account",
                    from_id=account.id,
                    to_type="contact",
                    to_id=contact.id,
                    relationship_type="HAS_CONTACT",
                )

            async def test_update_custom_association_remap_source_node(
                self,
                falkordb_indexing_lib: FalkorDBIndexingLib,
                falkordb_client: FalkorDBClient,
                account: AccountV2,
                account_with_custom_fields: AccountV2,
                contact: ContactV2,
                contact_with_custom_fields: ContactV2,
                organization_id: UUID,
            ) -> None:
                """Test updating a custom association between two entities in FalkorDB."""
                # Create initial relationship using helper method
                await self._create_initial_relationship(
                    falkordb_indexing_lib=falkordb_indexing_lib,
                    account=account,
                    contact=contact,
                    organization_id=organization_id,
                )

                # Then, index the account_with_custom_fields and contact_with_custom_fields
                await falkordb_indexing_lib.index_accounts(
                    organization_id=organization_id,
                    account_ids=[account_with_custom_fields.id],
                )
                await falkordb_indexing_lib.index_contacts(
                    organization_id=organization_id,
                    contact_ids=[contact_with_custom_fields.id],
                )

                old_relationship = RelationshipDef(
                    from_type="account",
                    from_id=account.id,
                    to_type="contact",
                    to_id=contact.id,
                )

                new_relationship = RelationshipDef(
                    from_type="account",
                    from_id=account_with_custom_fields.id,
                    to_type="contact",
                    to_id=contact.id,
                )

                # Now, update the relationship
                await falkordb_indexing_lib.update_relationship(
                    organization_id=organization_id,
                    relationship_type="HAS_CONTACT",
                    old_relationship=old_relationship,
                    new_relationship=new_relationship,
                    properties={  # These properties will get updated
                        "updated_custom_field_1": "updated_custom_value_1",
                        "updated_custom_field_2": "updated_custom_value_2",
                    },
                )

                # Ensure the graph exists
                await falkordb_client.ensure_graph_exists(organization_id)
                graph_name = falkordb_client._get_graph_name(organization_id)
                graph = falkordb_client.client.select_graph(graph_name)

                # Assert the relationship was updated by checking the new IDs
                result = await graph.query(
                    f"MATCH (a:{AccountV2.object_id.object_name} {{id: $account_id}})-[r:HAS_CONTACT]->(c:{ContactV2.object_id.object_name} {{id: $contact_id}}) RETURN a, r, c",
                    params={
                        "account_id": str(account_with_custom_fields.id),
                        "contact_id": str(contact.id),
                    },
                )
                assert len(result.result_set) == 1

                # Check the account side of the relationship
                account_side = result.result_set[0][0]
                logger.info(f"account_side: {account_side}")
                logger.info(f"vars(account_side): {vars(account_side)}")
                logger.info(f"account_side.properties: {account_side.properties}")
                assert account_side.properties["id"] == str(
                    account_with_custom_fields.id
                )

                # Check the edge of the relationship
                edge = result.result_set[0][1]
                assert edge.relation == "HAS_CONTACT"
                assert edge.properties == {
                    "updated_custom_field_1": "updated_custom_value_1",
                    "updated_custom_field_2": "updated_custom_value_2",
                }

                # Check the contact side of the relationship
                contact_side = result.result_set[0][2]
                assert contact_side.properties["id"] == str(contact.id)

            async def test_update_custom_association_remap_target_node(
                self,
                falkordb_indexing_lib: FalkorDBIndexingLib,
                falkordb_client: FalkorDBClient,
                account: AccountV2,
                account_with_custom_fields: AccountV2,
                contact: ContactV2,
                contact_with_custom_fields: ContactV2,
                organization_id: UUID,
            ) -> None:
                """Test updating a custom association between two entities in FalkorDB."""
                # Create initial relationship using helper method
                await self._create_initial_relationship(
                    falkordb_indexing_lib=falkordb_indexing_lib,
                    account=account,
                    contact=contact,
                    organization_id=organization_id,
                )

                # Then, index the account_with_custom_fields and contact_with_custom_fields
                await falkordb_indexing_lib.index_accounts(
                    organization_id=organization_id,
                    account_ids=[account_with_custom_fields.id],
                )
                await falkordb_indexing_lib.index_contacts(
                    organization_id=organization_id,
                    contact_ids=[contact_with_custom_fields.id],
                )

                old_relationship = RelationshipDef(
                    from_type="account",
                    from_id=account.id,
                    to_type="contact",
                    to_id=contact.id,
                )

                new_relationship = RelationshipDef(
                    from_type="account",
                    from_id=account.id,
                    to_type="contact",
                    to_id=contact_with_custom_fields.id,
                )

                # Now, update the relationship
                await falkordb_indexing_lib.update_relationship(
                    organization_id=organization_id,
                    relationship_type="HAS_CONTACT",
                    old_relationship=old_relationship,
                    new_relationship=new_relationship,
                    properties={  # These properties will get updated
                        "updated_custom_field_1": "updated_custom_value_1",
                        "updated_custom_field_2": "updated_custom_value_2",
                    },
                )

                # Ensure the graph exists
                await falkordb_client.ensure_graph_exists(organization_id)
                graph_name = falkordb_client._get_graph_name(organization_id)
                graph = falkordb_client.client.select_graph(graph_name)

                # Assert the relationship was updated by checking the new IDs
                result = await graph.query(
                    f"MATCH (a:{AccountV2.object_id.object_name} {{id: $account_id}})-[r:HAS_CONTACT]->(c:{ContactV2.object_id.object_name} {{id: $contact_id}}) RETURN a, r, c",
                    params={
                        "account_id": str(account.id),
                        "contact_id": str(contact_with_custom_fields.id),
                    },
                )
                assert len(result.result_set) == 1

                # Check the account side of the relationship
                account_side = result.result_set[0][0]
                logger.info(f"account_side: {account_side}")
                logger.info(f"vars(account_side): {vars(account_side)}")
                logger.info(f"account_side.properties: {account_side.properties}")
                assert account_side.properties["id"] == str(account.id)

                # Check the edge of the relationship
                edge = result.result_set[0][1]
                assert edge.relation == "HAS_CONTACT"
                assert edge.properties == {
                    "updated_custom_field_1": "updated_custom_value_1",
                    "updated_custom_field_2": "updated_custom_value_2",
                }

                # Check the contact side of the relationship
                contact_side = result.result_set[0][2]
                assert contact_side.properties["id"] == str(
                    contact_with_custom_fields.id
                )

            async def test_update_custom_association_remap_source_and_target_nodes(
                self,
                falkordb_indexing_lib: FalkorDBIndexingLib,
                falkordb_client: FalkorDBClient,
                account: AccountV2,
                account_with_custom_fields: AccountV2,
                contact: ContactV2,
                contact_with_custom_fields: ContactV2,
                organization_id: UUID,
            ) -> None:
                """Test updating a custom association between two entities in FalkorDB."""
                # Create initial relationship using helper method
                await self._create_initial_relationship(
                    falkordb_indexing_lib=falkordb_indexing_lib,
                    account=account,
                    contact=contact,
                    organization_id=organization_id,
                )

                # Then, index the account_with_custom_fields and contact_with_custom_fields
                await falkordb_indexing_lib.index_accounts(
                    organization_id=organization_id,
                    account_ids=[account_with_custom_fields.id],
                )
                await falkordb_indexing_lib.index_contacts(
                    organization_id=organization_id,
                    contact_ids=[contact_with_custom_fields.id],
                )

                old_relationship = RelationshipDef(
                    from_type="account",
                    from_id=account.id,
                    to_type="contact",
                    to_id=contact.id,
                )

                new_relationship = RelationshipDef(
                    from_type="account",
                    from_id=account_with_custom_fields.id,
                    to_type="contact",
                    to_id=contact_with_custom_fields.id,
                )

                # Now, update the relationship
                await falkordb_indexing_lib.update_relationship(
                    organization_id=organization_id,
                    relationship_type="HAS_CONTACT",
                    old_relationship=old_relationship,
                    new_relationship=new_relationship,
                    properties={  # These properties will get updated
                        "updated_custom_field_1": "updated_custom_value_1",
                        "updated_custom_field_2": "updated_custom_value_2",
                    },
                )

                # Ensure the graph exists
                await falkordb_client.ensure_graph_exists(organization_id)
                graph_name = falkordb_client._get_graph_name(organization_id)
                graph = falkordb_client.client.select_graph(graph_name)

                # Assert the relationship was updated by checking the new IDs
                result = await graph.query(
                    f"MATCH (a:{AccountV2.object_id.object_name} {{id: $account_id}})-[r:HAS_CONTACT]->(c:{ContactV2.object_id.object_name} {{id: $contact_id}}) RETURN a, r, c",
                    params={
                        "account_id": str(account_with_custom_fields.id),
                        "contact_id": str(contact_with_custom_fields.id),
                    },
                )
                assert len(result.result_set) == 1

                # Check the account side of the relationship
                account_side = result.result_set[0][0]
                logger.info(f"account_side: {account_side}")
                logger.info(f"vars(account_side): {vars(account_side)}")
                logger.info(f"account_side.properties: {account_side.properties}")
                assert account_side.properties["id"] == str(
                    account_with_custom_fields.id
                )

                # Check the edge of the relationship
                edge = result.result_set[0][1]
                assert edge.relation == "HAS_CONTACT"
                assert edge.properties == {
                    "updated_custom_field_1": "updated_custom_value_1",
                    "updated_custom_field_2": "updated_custom_value_2",
                }

                # Check the contact side of the relationship
                contact_side = result.result_set[0][2]
                assert contact_side.properties["id"] == str(
                    contact_with_custom_fields.id
                )

            async def test_update_custom_association_as_create(
                self,
                falkordb_indexing_lib: FalkorDBIndexingLib,
                falkordb_client: FalkorDBClient,
                account: AccountV2,
                contact: ContactV2,
                organization_id: UUID,
            ) -> None:
                """Test updating a custom association between two entities in FalkorDB as a create."""

                # Index the account and contact
                await falkordb_indexing_lib.index_accounts(
                    organization_id=organization_id,
                    account_ids=[account.id],
                )
                await falkordb_indexing_lib.index_contacts(
                    organization_id=organization_id,
                    contact_ids=[contact.id],
                )

                new_relationship = RelationshipDef(
                    from_type="account",
                    from_id=account.id,
                    to_type="contact",
                    to_id=contact.id,
                )

                # Create the relationship by passing in the new ids
                await falkordb_indexing_lib.update_relationship(
                    organization_id=organization_id,
                    relationship_type="HAS_CONTACT",
                    old_relationship=None,
                    new_relationship=new_relationship,
                    properties={  # These properties will get created
                        "created_custom_field_1": "created_custom_value_1",
                        "created_custom_field_2": "created_custom_value_2",
                    },
                )

                # Ensure the graph exists
                await falkordb_client.ensure_graph_exists(organization_id)
                graph_name = falkordb_client._get_graph_name(organization_id)
                graph = falkordb_client.client.select_graph(graph_name)

                # Assert the relationship was created
                result = await graph.query(
                    f"MATCH (a:{AccountV2.object_id.object_name} {{id: $account_id}})-[r:HAS_CONTACT]->(c:{ContactV2.object_id.object_name} {{id: $contact_id}}) RETURN a, r, c",
                    params={
                        "account_id": str(account.id),
                        "contact_id": str(contact.id),
                    },
                )
                assert len(result.result_set) == 1

                # Check the account side of the relationship
                account_side = result.result_set[0][0]
                logger.info(f"account_side: {account_side}")
                logger.info(f"vars(account_side): {vars(account_side)}")
                logger.info(f"account_side.properties: {account_side.properties}")
                assert account_side.properties["id"] == str(account.id)

                # Check the edge of the relationship
                edge = result.result_set[0][1]
                assert edge.relation == "HAS_CONTACT"
                assert edge.properties == {
                    "created_custom_field_1": "created_custom_value_1",
                    "created_custom_field_2": "created_custom_value_2",
                }

                # Check the contact side of the relationship
                contact_side = result.result_set[0][2]
                assert contact_side.properties["id"] == str(contact.id)

            async def test_update_custom_association_as_delete(
                self,
                falkordb_indexing_lib: FalkorDBIndexingLib,
                falkordb_client: FalkorDBClient,
                account: AccountV2,
                contact: ContactV2,
                organization_id: UUID,
            ) -> None:
                """Test updating a custom association between two entities in FalkorDB as a delete."""

                # Create initial relationship using helper method
                await self._create_initial_relationship(
                    falkordb_indexing_lib=falkordb_indexing_lib,
                    account=account,
                    contact=contact,
                    organization_id=organization_id,
                )

                old_relationship = RelationshipDef(
                    from_type="account",
                    from_id=account.id,
                    to_type="contact",
                    to_id=contact.id,
                )

                # Create the relationship by passing in the old ids
                await falkordb_indexing_lib.update_relationship(
                    organization_id=organization_id,
                    relationship_type="HAS_CONTACT",
                    old_relationship=old_relationship,
                    new_relationship=None,
                    properties={  # This doesn't matter because they will get deleted anyway
                        "deleted_custom_field_1": "deleted_custom_value_1",
                        "deleted_custom_field_2": "deleted_custom_value_2",
                    },
                )

                # Ensure the graph exists
                await falkordb_client.ensure_graph_exists(organization_id)
                graph_name = falkordb_client._get_graph_name(organization_id)
                graph = falkordb_client.client.select_graph(graph_name)

                # Assert the relationship was deleted by checking the new IDs
                node_and_relationship_query_result = await graph.query(
                    f"MATCH (a:{AccountV2.object_id.object_name} {{id: $account_id}})-[r:HAS_CONTACT]->(c:{ContactV2.object_id.object_name} {{id: $contact_id}}) RETURN a, r, c",
                    params={
                        "account_id": str(account.id),
                        "contact_id": str(contact.id),
                    },
                )
                assert (
                    len(node_and_relationship_query_result.result_set) == 0
                )  # This should not return a result because relationship is deleted

                account_node_query_result = await graph.query(
                    f"MATCH (a:{AccountV2.object_id.object_name} {{id: $account_id}}) RETURN a",
                    params={"account_id": str(account.id)},
                )
                assert len(account_node_query_result.result_set) == 1
                assert account_node_query_result.result_set[0][0].labels[0] == "account"
                assert account_node_query_result.result_set[0][0].properties[
                    "id"
                ] == str(account.id)

                contact_node_query_result = await graph.query(
                    f"MATCH (c:{ContactV2.object_id.object_name} {{id: $contact_id}}) RETURN c",
                    params={"contact_id": str(contact.id)},
                )
                assert len(contact_node_query_result.result_set) == 1
                assert contact_node_query_result.result_set[0][0].labels[0] == "contact"
                assert contact_node_query_result.result_set[0][0].properties[
                    "id"
                ] == str(contact.id)

        class TestInvalidCustomAssociationCases:
            async def test_invalid_custom_association_missing_old_and_new_relationships(
                self,
                falkordb_indexing_lib: FalkorDBIndexingLib,
                account: AccountV2,
                contact: ContactV2,
                organization_id: UUID,
            ) -> None:
                # Index the account and contact
                await falkordb_indexing_lib.index_accounts(
                    organization_id=organization_id,
                    account_ids=[account.id],
                )
                await falkordb_indexing_lib.index_contacts(
                    organization_id=organization_id,
                    contact_ids=[contact.id],
                )

                with pytest.raises(ValueError) as e:
                    await falkordb_indexing_lib.update_relationship(
                        organization_id=organization_id,
                        relationship_type="HAS_CONTACT",
                        old_relationship=None,
                        new_relationship=None,
                    )
                assert (
                    "Must provide either old_relationship or new_relationship" in str(e)
                )

    class TestDeleteRelationship:
        async def _create_initial_relationship(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            account: AccountV2,
            contact: ContactV2,
            organization_id: UUID,
        ) -> None:
            """Helper method to create initial relationship for tests."""
            # Index the account and contact
            await falkordb_indexing_lib.index_accounts(
                organization_id=organization_id,
                account_ids=[account.id],
            )
            await falkordb_indexing_lib.index_contacts(
                organization_id=organization_id,
                contact_ids=[contact.id],
            )

            # Create the relationship
            await falkordb_indexing_lib.create_relationship(
                organization_id=organization_id,
                from_type="account",
                from_id=account.id,
                to_type="contact",
                to_id=contact.id,
                relationship_type="HAS_CONTACT",
            )

        async def test_delete_relationship(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            account: AccountV2,
            contact: ContactV2,
            organization_id: UUID,
        ) -> None:
            """Test deleting a relationship between two entities in FalkorDB."""

            # First create the relationship using the existing test
            await self._create_initial_relationship(
                falkordb_indexing_lib=falkordb_indexing_lib,
                account=account,
                contact=contact,
                organization_id=organization_id,
            )

            # Ensure the graph exists
            await falkordb_client.ensure_graph_exists(organization_id)
            graph_name = falkordb_client._get_graph_name(organization_id)
            graph = falkordb_client.client.select_graph(graph_name)

            # Delete the relationship
            await falkordb_indexing_lib.delete_relationship(
                organization_id=organization_id,
                from_type="account",
                from_id=account.id,
                to_type="contact",
                to_id=contact.id,
                relationship_type="HAS_CONTACT",
            )

            result = await graph.query(
                f"MATCH (a:{AccountV2.object_id.object_name} {{id: $account_id}})-[r:HAS_CONTACT]->(c:{ContactV2.object_id.object_name} {{id: $contact_id}}) RETURN r",
                params={"account_id": str(account.id), "contact_id": str(contact.id)},
            )
            # Assert no relationships were found
            assert len(result.result_set) == 0

    class TestIndexAllEntities:
        async def test_index_all_entities(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            account: AccountV2,
            contact: ContactV2,
            pipeline: PipelineV2,
            meeting: MeetingV2,
            custom_object_with_data: tuple[CustomObject, CustomField, CustomObjectData],
            organization_id: UUID,
        ) -> None:
            """Test indexing all entity types to FalkorDB."""
            # Extract custom object data from fixture
            _, _, custom_object_data = custom_object_with_data

            # Index all entities
            await falkordb_indexing_lib.index_accounts(
                organization_id=organization_id,
                account_ids=[account.id],
            )
            await falkordb_indexing_lib.index_contacts(
                organization_id=organization_id,
                contact_ids=[contact.id],
            )
            await falkordb_indexing_lib.index_pipelines(
                organization_id=organization_id,
                pipeline_ids=[pipeline.id],
            )
            await falkordb_indexing_lib.index_meetings([meeting.id])
            await falkordb_indexing_lib.index_custom_objects(
                organization_id=organization_id,
                cobject_data_ids=[custom_object_data.id],
            )

    # TODO: Still a sandbox environment
    class TestBulkIndexing:
        # TODO: Add more and better assertions for the graphs
        """Note: This test assumes that the previous tests have run and that the fixtures have created the domain objects in Postgres.
        If you run this test without running the previous tests, there will be nothing to index!
        """

        async def test_index_all_organizations(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
        ) -> None:
            # Get list of all user ids and org ids in an instance.
            user_and_org_ids = await falkordb_indexing_lib.get_list_of_all_user_and_org_ids_in_instance()
            logger.info(f"user_and_org_ids: {user_and_org_ids}")

            # Group users by organization
            org_id_to_user_ids = await falkordb_indexing_lib.group_users_by_org(
                user_and_org_ids
            )
            logger.info(f"org_id_to_user_ids: {org_id_to_user_ids}")

            # Index all organizations
            graphs = await falkordb_indexing_lib.index_all_organizations()
            logger.info(f"graphs: {graphs}")

            # Verify the number of graphs created is equal to the number of organizations
            # TODO: Add more tests later
            assert len(graphs) == len(org_id_to_user_ids)

        # Function to test indexing nodes
        """Note: This test assumes that the previous tests have run and that the fixtures have created the domain objects in Postgres.
        If you run this test without running the previous tests, there will be nothing to index!
        """

        async def test_indexing_nodes(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            account_service: AccountService,
            contact_service: ContactService,
            pipeline_service: PipelineService,
            meeting_query_service: MeetingQueryService,
            custom_object_service: CustomObjectService,
        ) -> None:
            # Get list of all user ids and org ids in an instance.
            user_and_org_ids = await falkordb_indexing_lib.get_list_of_all_user_and_org_ids_in_instance()
            logger.info(f"user_and_org_ids: {user_and_org_ids}")

            # Group users by organization
            org_id_to_user_ids = await falkordb_indexing_lib.group_users_by_org(
                user_and_org_ids
            )
            logger.info(f"org_id_to_user_ids: {org_id_to_user_ids}")

            # For each org, create a graph and index users
            for org_id in org_id_to_user_ids:
                # Create a graph for the org
                graph = await falkordb_indexing_lib.index_organization(
                    org_id=org_id, org_id_to_user_ids=org_id_to_user_ids
                )
                logger.info(f"vars(graph): {vars(graph)}")

                # Verify the graph was created
                assert graph is not None
                assert graph._name is not None
                assert graph.client is not None

                # Verify the users were indexed
                for user_id in org_id_to_user_ids[org_id]:
                    user_result = await graph.query(
                        f"MATCH (n:{OrganizationUserV2.object_id.object_name} {{id: $id}}) RETURN n",
                        params={"id": str(user_id)},
                    )
                    assert len(user_result.result_set) == 1
                    user_node = user_result.result_set[0][0]
                    logger.info(f"user_node: {user_node}")
                    assert user_node.properties["id"] == str(user_id)
                    assert user_node.properties["organization_id"] == str(org_id)

                # Verify accounts were indexed
                accounts = await account_service.list_accounts_v2(
                    organization_id=org_id,
                )
                logger.info(f"accounts: {accounts}")

                for account in accounts:
                    account_result = await graph.query(
                        f"MATCH (n:{AccountV2.object_id.object_name} {{id: $id}}) RETURN n",
                        params={"id": str(account.id)},
                    )
                    logger.info(f"account_result: {account_result}")
                    logger.info(f"vars(account_result): {vars(account_result)}")

                    assert len(account_result.result_set) == 1
                    account_node = account_result.result_set[0][0]
                    assert account_node.properties["id"] == str(account.id)
                    assert (
                        account_node.properties["display_name"] == account.display_name
                    )
                    assert account_node.properties["organization_id"] == str(org_id)

                # Verify contacts were indexed
                contacts = await contact_service.list_contacts_v2(
                    organization_id=org_id,
                )
                logger.info(f"contacts: {contacts}")

                for contact in contacts:
                    contact_result = await graph.query(
                        f"MATCH (n:{ContactV2.object_id.object_name} {{id: $id}}) RETURN n",
                        params={"id": str(contact.id)},
                    )
                    logger.info(f"contact_result: {contact_result}")
                    logger.info(f"vars(contact_result): {vars(contact_result)}")

                    assert len(contact_result.result_set) == 1
                    contact_node = contact_result.result_set[0][0]
                    assert contact_node.properties["id"] == str(contact.id)
                    assert (
                        contact_node.properties["display_name"] == contact.display_name
                    )
                    assert contact_node.properties["organization_id"] == str(org_id)

                # Verify pipelines were indexed
                pipelines = await pipeline_service.list_pipelines(
                    organization_id=org_id,
                )
                logger.info(f"pipelines: {pipelines}")

                for pipeline in pipelines:
                    pipeline_result = await graph.query(
                        f"MATCH (n:{PipelineV2.object_id.object_name} {{id: $id}}) RETURN n",
                        params={"id": str(pipeline.id)},
                    )
                    logger.info(f"pipeline_result: {pipeline_result}")
                    logger.info(f"vars(pipeline_result): {vars(pipeline_result)}")

                    assert len(pipeline_result.result_set) == 1
                    pipeline_node = pipeline_result.result_set[0][0]
                    assert pipeline_node.properties["id"] == str(pipeline.id)
                    assert (
                        pipeline_node.properties["display_name"]
                        == pipeline.display_name
                    )
                    assert pipeline_node.properties["organization_id"] == str(org_id)

                # Verify meetings were indexed
                meetings = await meeting_query_service.list_meeting_v2(
                    organization_id=org_id,
                    user_id=None,
                    include_custom_object=False,
                )
                logger.info(f"meetings: {meetings}")

                for meeting in meetings:
                    meeting_result = await graph.query(
                        f"MATCH (n:{MeetingV2.object_id.object_name} {{id: $id}}) RETURN n",
                        params={"id": str(meeting.id)},
                    )
                    logger.info(f"meeting_result: {meeting_result}")

                    assert len(meeting_result.result_set) == 1
                    meeting_node = meeting_result.result_set[0][0]
                    assert meeting_node.properties["id"] == str(meeting.id)
                    assert meeting_node.properties["title"] == meeting.title
                    assert meeting_node.properties["organization_id"] == str(org_id)

                # Verify custom objects were indexed
                custom_object_data_dto_list = await custom_object_service.list_custom_object_data_dto_by_organization_id(
                    organization_id=org_id,
                )
                logger.info(
                    f"custom_object_data_dto_list: {custom_object_data_dto_list}"
                )

                for custom_object_data_dto in custom_object_data_dto_list:
                    custom_object_data_result = await graph.query(
                        f"MATCH (n:`{custom_object_data_dto.custom_object_data.cobject_metadata_id}` {{id: $id}}) RETURN n",
                        params={
                            "id": str(custom_object_data_dto.custom_object_data.id)
                        },
                    )
                    logger.info(
                        f"custom_object_data_result: {custom_object_data_result}"
                    )

                    assert len(custom_object_data_result.result_set) == 1
                    custom_object_data_node = custom_object_data_result.result_set[0][0]
                    logger.info(f"custom_object_data_node: {custom_object_data_node}")
                    assert custom_object_data_node.properties["id"] == str(
                        custom_object_data_dto.custom_object_data.id
                    )
                    assert custom_object_data_node.properties["organization_id"] == str(
                        org_id
                    )

        # TODO: Need to assert the following relationships. Find a way to make this cleaner.
        # expected_domain_relationships: list[str] = [
        #     "account__to__owner_user",
        #     "account__to__updated_by_user",
        #     "account__to__created_by_user",
        #     "contact__to__owner_user",
        #     "contact__to__updated_by_user",
        #     "contact__to__created_by_user",
        #     "contact__to__primary_account",
        #     "contact_emails",
        #     "stage",
        #     "contact_account_associations",
        #     "pipeline__to__updated_by_user",
        #     "pipeline__to__created_by_user",
        #     "pipeline__to__owner_user",
        #     "pipeline__to__account",
        #     "pipeline__to__primary_contact",
        #     # TODO: Need to map all the ones below
        #     "meeting__to__created_by_user",
        #     "meeting__to__organizer_user",
        #     "meeting__to__account",
        #     "meeting__to__pipeline",
        # ]

        async def test_domain_object_relationships_creation(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            user_service: UserService,
            organization_id: UUID,
            user_id: UUID,
            _organization: None,
            _user: None,  # TODO: See if we can create the user in the function
            account: AccountV2,
            contact: ContactV2,
            pipeline: PipelineV2,
            meeting: MeetingV2,
        ) -> None:
            # Pre-setup is the same as the test_indexing_nodes test
            # Get list of all user ids and org ids in an instance.
            user_and_org_ids = await falkordb_indexing_lib.get_list_of_all_user_and_org_ids_in_instance()
            logger.info(f"user_and_org_ids: {user_and_org_ids}")

            # Group users by organization
            org_id_to_user_ids = await falkordb_indexing_lib.group_users_by_org(
                user_and_org_ids
            )
            logger.info(f"org_id_to_user_ids: {org_id_to_user_ids}")

            # For each org, create a graph and index users
            for org_id in [organization_id]:
                # Create a graph for the org
                graph = await falkordb_indexing_lib.index_organization(
                    org_id=org_id, org_id_to_user_ids=org_id_to_user_ids
                )
                logger.info(f"vars(graph): {vars(graph)}")
                logger.info(f"graph.schema: {graph.schema}")
                logger.info(f"vars(graph.schema): {vars(graph.schema)}")

                # Verify the graph was created
                assert graph is not None
                assert graph._name is not None
                assert graph.client is not None

                """ Create User here """
                list_users = await user_service.list_users_v2(
                    organization_id=organization_id,
                    only_include_user_ids={user_id},
                )
                logger.info(f"list_users: {list_users}")
                user = list_users[0]

                """ Verify the account relationships were created """
                """ Verify account__to__created_by_user relationship was created """
                account__to__created_by_user_result = await graph.query(
                    f"MATCH (n:{AccountV2.object_id.object_name})-[r:account__to__created_by_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"account__to__created_by_user_result: {account__to__created_by_user_result}"
                )

                assert len(account__to__created_by_user_result.result_set) == 1, (
                    f"Expected 1 result, got {len(account__to__created_by_user_result.result_set)} for org_id: {org_id}"
                )

                # Check the account side of the relationship
                account__to__created_by_user_account_side = (
                    account__to__created_by_user_result.result_set[0][0]
                )
                logger.info(
                    f"account__to__created_by_user_account_side: {account__to__created_by_user_account_side}"
                )
                assert account__to__created_by_user_account_side.properties[
                    "id"
                ] == str(account.id)
                assert account__to__created_by_user_account_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    account__to__created_by_user_account_side.properties["display_name"]
                    == account.display_name
                )

                # Check the edge of the relationship
                account__to__created_by_user_edge = (
                    account__to__created_by_user_result.result_set[0][1]
                )
                logger.info(
                    f"account__to__created_by_user_edge: {account__to__created_by_user_edge}"
                )
                logger.info(
                    f"vars(account__to__created_by_user_edge): {vars(account__to__created_by_user_edge)}"
                )
                assert (
                    account__to__created_by_user_edge.relation
                    == "account__to__created_by_user"
                )

                # Check the User side of the relationship
                account__to__created_by_user_user_side = (
                    account__to__created_by_user_result.result_set[0][2]
                )
                logger.info(
                    f"account__to__created_by_user_user_side: {account__to__created_by_user_user_side}"
                )
                logger.info(
                    f"vars(account__to__created_by_user_user_side): {vars(account__to__created_by_user_user_side)}"
                )
                assert account__to__created_by_user_user_side.properties["id"] == str(
                    user.id
                )
                assert account__to__created_by_user_user_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    account__to__created_by_user_user_side.properties["display_name"]
                    == user.display_name
                )

                """ Verify the account__to__updated_by_user relationship was created """
                account__to__updated_by_user_result = await graph.query(
                    f"MATCH (n:{AccountV2.object_id.object_name})-[r:account__to__updated_by_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"account__to__updated_by_user_result: {account__to__updated_by_user_result}"
                )
                assert len(account__to__updated_by_user_result.result_set) == 1

                # Check the account side of the relationship
                account__to__updated_by_user_account_side = (
                    account__to__updated_by_user_result.result_set[0][0]
                )
                logger.info(
                    f"account__to__updated_by_user_account_side: {account__to__updated_by_user_account_side}"
                )
                assert account__to__updated_by_user_account_side.properties[
                    "id"
                ] == str(account.id)
                assert account__to__updated_by_user_account_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    account__to__updated_by_user_account_side.properties["display_name"]
                    == account.display_name
                )

                # Check the edge of the relationship
                account__to__updated_by_user_edge = (
                    account__to__updated_by_user_result.result_set[0][1]
                )
                logger.info(
                    f"account__to__updated_by_user_edge: {account__to__updated_by_user_edge}"
                )
                logger.info(
                    f"vars(account__to__updated_by_user_edge): {vars(account__to__updated_by_user_edge)}"
                )
                assert (
                    account__to__updated_by_user_edge.relation
                    == "account__to__updated_by_user"
                )

                # Check the User side of the relationship
                account__to__updated_by_user_user_side = (
                    account__to__updated_by_user_result.result_set[0][2]
                )
                logger.info(
                    f"account__to__updated_by_user_user_side: {account__to__updated_by_user_user_side}"
                )
                logger.info(
                    f"vars(account__to__updated_by_user_user_side): {vars(account__to__updated_by_user_user_side)}"
                )
                assert account__to__updated_by_user_user_side.properties["id"] == str(
                    user.id
                )
                assert account__to__updated_by_user_user_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    account__to__updated_by_user_user_side.properties["display_name"]
                    == user.display_name
                )

                """ Verify the account__to__owner_user relationship was created """
                account__to__owner_user_result = await graph.query(
                    f"MATCH (n:{AccountV2.object_id.object_name})-[r:account__to__owner_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"account__to__owner_user_result: {account__to__owner_user_result}"
                )
                logger.info(
                    f"vars(account__to__owner_user_result): {vars(account__to__owner_user_result)}"
                )
                assert len(account__to__owner_user_result.result_set) == 1

                # Check the account side of the relationship
                account__to__owner_user_account_side = (
                    account__to__owner_user_result.result_set[0][0]
                )
                logger.info(
                    f"account__to__owner_user_account_side: {account__to__owner_user_account_side}"
                )
                assert account__to__owner_user_account_side.properties["id"] == str(
                    account.id
                )
                assert account__to__owner_user_account_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    account__to__owner_user_account_side.properties["display_name"]
                    == account.display_name
                )

                # Check the edge of the relationship
                account__to__owner_user_edge = (
                    account__to__owner_user_result.result_set[0][1]
                )
                logger.info(
                    f"account__to__owner_user_edge: {account__to__owner_user_edge}"
                )
                logger.info(
                    f"vars(account__to__owner_user_edge): {vars(account__to__owner_user_edge)}"
                )
                assert (
                    account__to__owner_user_edge.relation == "account__to__owner_user"
                )

                # Check the User side of the relationship
                account__to__owner_user_user_side = (
                    account__to__owner_user_result.result_set[0][2]
                )
                logger.info(
                    f"account__to__owner_user_user_side: {account__to__owner_user_user_side}"
                )
                logger.info(
                    f"vars(account__to__owner_user_user_side): {vars(account__to__owner_user_user_side)}"
                )
                assert account__to__owner_user_user_side.properties["id"] == str(
                    user.id
                )
                assert account__to__owner_user_user_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    account__to__owner_user_user_side.properties["display_name"]
                    == user.display_name
                )

                """ Verify Contact domain relationships were created """
                """ Verify contact__to__owner_user relationship was created """
                contact_to_owner_user_relationship_result = await graph.query(
                    f"MATCH (n:{ContactV2.object_id.object_name})-[r:contact__to__owner_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"contact_to_owner_user_relationship_result: {contact_to_owner_user_relationship_result}"
                )
                logger.info(
                    f"vars(contact_to_owner_user_relationship_result): {vars(contact_to_owner_user_relationship_result)}"
                )
                assert len(contact_to_owner_user_relationship_result.result_set) == 1

                # Check the contact side of the relationship
                contact_to_owner_user_relationship_contact_side = (
                    contact_to_owner_user_relationship_result.result_set[0][0]
                )
                logger.info(
                    f"contact_to_owner_user_relationship_contact_side: {contact_to_owner_user_relationship_contact_side}"
                )
                assert contact_to_owner_user_relationship_contact_side.properties[
                    "id"
                ] == str(contact.id)
                assert contact_to_owner_user_relationship_contact_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    contact_to_owner_user_relationship_contact_side.properties[
                        "display_name"
                    ]
                    == contact.display_name
                )

                # Check the edge of the relationship
                contact_to_owner_user_relationship_edge = (
                    contact_to_owner_user_relationship_result.result_set[0][1]
                )
                logger.info(
                    f"contact_to_owner_user_relationship_edge: {contact_to_owner_user_relationship_edge}"
                )
                logger.info(
                    f"vars(contact_to_owner_user_relationship_edge): {vars(contact_to_owner_user_relationship_edge)}"
                )
                assert (
                    contact_to_owner_user_relationship_edge.relation
                    == "contact__to__owner_user"
                )

                # Check the user side of the relationship
                contact_to_owner_user_relationship_user_side = (
                    contact_to_owner_user_relationship_result.result_set[0][2]
                )
                logger.info(
                    f"contact_to_owner_user_relationship_user_side: {contact_to_owner_user_relationship_user_side}"
                )
                logger.info(
                    f"vars(contact_to_owner_user_relationship_user_side): {vars(contact_to_owner_user_relationship_user_side)}"
                )
                assert contact_to_owner_user_relationship_user_side.properties[
                    "id"
                ] == str(user.id)
                assert contact_to_owner_user_relationship_user_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    contact_to_owner_user_relationship_user_side.properties[
                        "display_name"
                    ]
                    == user.display_name
                )

                """ Verify the contact__to__updated_by_user relationship was created """
                contact_to_updated_by_user_relationship_result = await graph.query(
                    f"MATCH (n:{ContactV2.object_id.object_name})-[r:contact__to__updated_by_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"contact_to_updated_by_user_relationship_result: {contact_to_updated_by_user_relationship_result}"
                )
                logger.info(
                    f"vars(contact_to_updated_by_user_relationship_result): {vars(contact_to_updated_by_user_relationship_result)}"
                )
                assert (
                    len(contact_to_updated_by_user_relationship_result.result_set) == 1
                )

                # Check the contact side of the relationship
                contact_to_updated_by_user_relationship_contact_side = (
                    contact_to_updated_by_user_relationship_result.result_set[0][0]
                )
                logger.info(
                    f"contact_to_updated_by_user_relationship_contact_side: {contact_to_updated_by_user_relationship_contact_side}"
                )
                assert contact_to_updated_by_user_relationship_contact_side.properties[
                    "id"
                ] == str(contact.id)
                assert contact_to_updated_by_user_relationship_contact_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    contact_to_updated_by_user_relationship_contact_side.properties[
                        "display_name"
                    ]
                    == contact.display_name
                )

                # Check the edge of the relationship
                contact_to_updated_by_user_relationship_edge = (
                    contact_to_updated_by_user_relationship_result.result_set[0][1]
                )
                logger.info(
                    f"contact_to_updated_by_user_relationship_edge: {contact_to_updated_by_user_relationship_edge}"
                )
                logger.info(
                    f"vars(contact_to_updated_by_user_relationship_edge): {vars(contact_to_updated_by_user_relationship_edge)}"
                )
                assert (
                    contact_to_updated_by_user_relationship_edge.relation
                    == "contact__to__updated_by_user"
                )

                # Check the user side of the relationship
                contact_to_updated_by_user_relationship_user_side = (
                    contact_to_updated_by_user_relationship_result.result_set[0][2]
                )
                logger.info(
                    f"contact_to_updated_by_user_relationship_user_side: {contact_to_updated_by_user_relationship_user_side}"
                )
                logger.info(
                    f"vars(contact_to_updated_by_user_relationship_user_side): {vars(contact_to_updated_by_user_relationship_user_side)}"
                )
                assert contact_to_updated_by_user_relationship_user_side.properties[
                    "id"
                ] == str(user.id)
                assert contact_to_updated_by_user_relationship_user_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    contact_to_updated_by_user_relationship_user_side.properties[
                        "display_name"
                    ]
                    == user.display_name
                )

                """ Verify the contact__to__created_by_user relationship was created """
                contact_to_created_by_user_relationship_result = await graph.query(
                    f"MATCH (n:{ContactV2.object_id.object_name})-[r:contact__to__created_by_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"contact_to_created_by_user_relationship_result: {contact_to_created_by_user_relationship_result}"
                )
                logger.info(
                    f"vars(contact_to_created_by_user_relationship_result): {vars(contact_to_created_by_user_relationship_result)}"
                )
                assert (
                    len(contact_to_created_by_user_relationship_result.result_set) == 1
                )

                # Check the contact side of the relationship
                contact_to_created_by_user_relationship_contact_side = (
                    contact_to_created_by_user_relationship_result.result_set[0][0]
                )
                logger.info(
                    f"contact_to_created_by_user_relationship_contact_side: {contact_to_created_by_user_relationship_contact_side}"
                )
                assert contact_to_created_by_user_relationship_contact_side.properties[
                    "id"
                ] == str(contact.id)
                assert contact_to_created_by_user_relationship_contact_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    contact_to_created_by_user_relationship_contact_side.properties[
                        "display_name"
                    ]
                    == contact.display_name
                )

                # Check the edge of the relationship
                contact_to_created_by_user_relationship_edge = (
                    contact_to_created_by_user_relationship_result.result_set[0][1]
                )
                logger.info(
                    f"contact_to_created_by_user_relationship_edge: {contact_to_created_by_user_relationship_edge}"
                )
                logger.info(
                    f"vars(contact_to_created_by_user_relationship_edge): {vars(contact_to_created_by_user_relationship_edge)}"
                )
                assert (
                    contact_to_created_by_user_relationship_edge.relation
                    == "contact__to__created_by_user"
                )

                # Check the user side of the relationship
                contact_to_created_by_user_relationship_user_side = (
                    contact_to_created_by_user_relationship_result.result_set[0][2]
                )
                logger.info(
                    f"contact_to_created_by_user_relationship_user_side: {contact_to_created_by_user_relationship_user_side}"
                )
                logger.info(
                    f"vars(contact_to_created_by_user_relationship_user_side): {vars(contact_to_created_by_user_relationship_user_side)}"
                )
                assert contact_to_created_by_user_relationship_user_side.properties[
                    "id"
                ] == str(user.id)
                assert contact_to_created_by_user_relationship_user_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    contact_to_created_by_user_relationship_user_side.properties[
                        "display_name"
                    ]
                    == user.display_name
                )

                """ Verify the Primary Account (contact_to_primary_account) relationship was created """
                primary_account_relationship_result = await graph.query(
                    f"MATCH (n:{ContactV2.object_id.object_name})-[r:contact__to__primary_account]->(m:{AccountV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"primary_account_relationship_result: {primary_account_relationship_result}"
                )
                logger.info(
                    f"vars(primary_account_relationship_result): {vars(primary_account_relationship_result)}"
                )
                assert len(primary_account_relationship_result.result_set) == 1

                # Check the contact side of the relationship
                primary_account_relationship_contact_side = (
                    primary_account_relationship_result.result_set[0][0]
                )
                logger.info(
                    f"primary_account_relationship_contact_side: {primary_account_relationship_contact_side}"
                )
                assert primary_account_relationship_contact_side.properties[
                    "id"
                ] == str(contact.id)
                assert primary_account_relationship_contact_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    primary_account_relationship_contact_side.properties["display_name"]
                    == contact.display_name
                )

                # Check the edge of the relationship
                primary_account_relationship_edge = (
                    primary_account_relationship_result.result_set[0][1]
                )
                logger.info(
                    f"primary_account_relationship_edge: {primary_account_relationship_edge}"
                )
                logger.info(
                    f"vars(primary_account_relationship_edge): {vars(primary_account_relationship_edge)}"
                )
                assert (
                    primary_account_relationship_edge.relation
                    == "contact__to__primary_account"
                )

                # Check the account side of the relationship
                primary_account_relationship_account_side = (
                    primary_account_relationship_result.result_set[0][2]
                )
                logger.info(
                    f"primary_account_relationship_account_side: {primary_account_relationship_account_side}"
                )
                assert primary_account_relationship_account_side.properties[
                    "id"
                ] == str(account.id)
                assert primary_account_relationship_account_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    primary_account_relationship_account_side.properties["display_name"]
                    == account.display_name
                )

                """ Verify the contact_emails relationship was created """
                contact_emails_relationship_result = await graph.query(
                    f"MATCH (n:{ContactV2.object_id.object_name})-[r:contact_emails]->(m) RETURN n, r, m"
                )
                logger.info(
                    f"contact_emails_relationship_result: {contact_emails_relationship_result}"
                )
                logger.info(
                    f"vars(contact_emails_relationship_result): {vars(contact_emails_relationship_result)}"
                )
                assert len(contact_emails_relationship_result.result_set) == 1

                # Check the contact side of the relationship
                contact_emails_relationship_contact_side = (
                    contact_emails_relationship_result.result_set[0][0]
                )
                logger.info(
                    f"contact_emails_relationship_contact_side: {contact_emails_relationship_contact_side}"
                )
                assert contact_emails_relationship_contact_side.properties["id"] == str(
                    contact.id
                )
                assert contact_emails_relationship_contact_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    contact_emails_relationship_contact_side.properties["display_name"]
                    == contact.display_name
                )

                # Check the edge of the relationship
                contact_emails_relationship_edge = (
                    contact_emails_relationship_result.result_set[0][1]
                )
                logger.info(
                    f"contact_emails_relationship_edge: {contact_emails_relationship_edge}"
                )
                logger.info(
                    f"vars(contact_emails_relationship_edge): {vars(contact_emails_relationship_edge)}"
                )
                assert contact_emails_relationship_edge.relation == "contact_emails"

                # Check email side of the relationship
                contact_emails_relationship_email_side = (
                    contact_emails_relationship_result.result_set[0][2]
                )
                logger.info(
                    f"contact_emails_relationship_email_side: {contact_emails_relationship_email_side}"
                )
                logger.info(
                    f"vars(contact_emails_relationship_email_side): {vars(contact_emails_relationship_email_side)}"
                )
                assert contact_emails_relationship_email_side.properties[
                    "contact_id"
                ] == str(contact.id)
                assert (
                    contact_emails_relationship_email_side.properties["email"]
                    == contact.primary_email
                )

                """ Verify that contact_account_associations were created """
                contact_account_associations_result = await graph.query(
                    f"MATCH (n:{ContactV2.object_id.object_name})-[r:contact_account_associations]->(m) RETURN n, r, m"
                )
                logger.info(
                    f"contact_account_associations_result: {contact_account_associations_result}"
                )
                logger.info(
                    f"vars(contact_account_associations_result): {vars(contact_account_associations_result)}"
                )
                assert len(contact_account_associations_result.result_set) == 1

                # Check the contact side of the relationship
                contact_account_associations_contact_side = (
                    contact_account_associations_result.result_set[0][0]
                )
                logger.info(
                    f"contact_account_associations_contact_side: {contact_account_associations_contact_side}"
                )
                assert contact_account_associations_contact_side.properties[
                    "id"
                ] == str(contact.id)
                assert contact_account_associations_contact_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    contact_account_associations_contact_side.properties["display_name"]
                    == contact.display_name
                )

                # Check the edge of the relationship
                contact_account_associations_edge = (
                    contact_account_associations_result.result_set[0][1]
                )
                logger.info(
                    f"contact_account_associations_edge: {contact_account_associations_edge}"
                )
                logger.info(
                    f"vars(contact_account_associations_edge): {vars(contact_account_associations_edge)}"
                )
                assert (
                    contact_account_associations_edge.relation
                    == "contact_account_associations"
                )

                # Check the associations side of the relationship
                contact_account_associations_associations_side = (
                    contact_account_associations_result.result_set[0][2]
                )
                logger.info(
                    f"contact_account_associations_associations_side: {contact_account_associations_associations_side}"
                )
                assert contact_account_associations_associations_side.properties[
                    "account_id"
                ] == str(account.id)
                assert contact_account_associations_associations_side.properties[
                    "contact_id"
                ] == str(contact.id)

                """ Verify Contact Account Role domain relationships were created """
                """ Verify the contact_account_role__to__account relationship was created """
                contact_account_role_to_account_relationship_result = await graph.query(
                    f"MATCH (n:{ContactAccountRole.object_id.object_name})-[r:contact_account_role__to__account]->(m:{AccountV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"contact_account_role_to_account_relationship_result: {contact_account_role_to_account_relationship_result}"
                )
                logger.info(
                    f"vars(contact_account_role_to_account_relationship_result): {vars(contact_account_role_to_account_relationship_result)}"
                )
                assert (
                    len(contact_account_role_to_account_relationship_result.result_set)
                    == 1
                )

                # Check the contact account role side of the relationship
                contact_account_role_to_account_relationship_contact_account_role_side = contact_account_role_to_account_relationship_result.result_set[
                    0
                ][0]
                logger.info(
                    f"contact_account_role_to_account_relationship_contact_account_role_side: {contact_account_role_to_account_relationship_contact_account_role_side}"
                )
                logger.info(
                    f"vars(contact_account_role_to_account_relationship_contact_account_role_side): {vars(contact_account_role_to_account_relationship_contact_account_role_side)}"
                )
                assert (
                    contact_account_role_to_account_relationship_contact_account_role_side.labels[
                        0
                    ]
                    == "contact_account_role"
                )
                assert (
                    contact_account_role_to_account_relationship_contact_account_role_side.properties[
                        "account_id"
                    ]
                    == str(account.id)
                )
                assert (
                    contact_account_role_to_account_relationship_contact_account_role_side.properties[
                        "contact_id"
                    ]
                    == str(contact.id)
                )

                # Check the edge of the relationship
                contact_account_role_to_account_relationship_edge = (
                    contact_account_role_to_account_relationship_result.result_set[0][1]
                )
                logger.info(
                    f"contact_account_role_to_account_relationship_edge: {contact_account_role_to_account_relationship_edge}"
                )
                logger.info(
                    f"vars(contact_account_role_to_account_relationship_edge): {vars(contact_account_role_to_account_relationship_edge)}"
                )
                assert (
                    contact_account_role_to_account_relationship_edge.relation
                    == "contact_account_role__to__account"
                )

                # CHeck the account side of the relationship
                contact_account_role_to_account_relationship_account_side = (
                    contact_account_role_to_account_relationship_result.result_set[0][2]
                )
                logger.info(
                    f"contact_account_role_to_account_relationship_account_side: {contact_account_role_to_account_relationship_account_side}"
                )
                logger.info(
                    f"vars(contact_account_role_to_account_relationship_account_side): {vars(contact_account_role_to_account_relationship_account_side)}"
                )
                assert (
                    contact_account_role_to_account_relationship_account_side.properties[
                        "id"
                    ]
                    == str(account.id)
                )
                assert (
                    contact_account_role_to_account_relationship_account_side.properties[
                        "organization_id"
                    ]
                    == str(org_id)
                )
                assert (
                    contact_account_role_to_account_relationship_account_side.properties[
                        "display_name"
                    ]
                    == account.display_name
                )

                """ Verify contact_account_role__to__contact relationship was created """
                contact_account_role_to_contact_relationship_result = await graph.query(
                    f"MATCH (n:{ContactAccountRole.object_id.object_name})-[r:contact_account_role__to__contact]->(m:{ContactV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"contact_account_role_to_contact_relationship_result: {contact_account_role_to_contact_relationship_result}"
                )
                logger.info(
                    f"vars(contact_account_role_to_contact_relationship_result): {vars(contact_account_role_to_contact_relationship_result)}"
                )
                assert (
                    len(contact_account_role_to_contact_relationship_result.result_set)
                    == 1
                )

                # Check the contact account role side of the relationship
                contact_account_role_to_contact_relationship_contact_account_role_side = contact_account_role_to_contact_relationship_result.result_set[
                    0
                ][0]
                logger.info(
                    f"contact_account_role_to_contact_relationship_contact_account_role_side: {contact_account_role_to_contact_relationship_contact_account_role_side}"
                )
                logger.info(
                    f"vars(contact_account_role_to_contact_relationship_contact_account_role_side): {vars(contact_account_role_to_contact_relationship_contact_account_role_side)}"
                )
                assert (
                    contact_account_role_to_contact_relationship_contact_account_role_side.labels[
                        0
                    ]
                    == "contact_account_role"
                )
                assert (
                    contact_account_role_to_contact_relationship_contact_account_role_side.properties[
                        "account_id"
                    ]
                    == str(account.id)
                )
                assert (
                    contact_account_role_to_contact_relationship_contact_account_role_side.properties[
                        "contact_id"
                    ]
                    == str(contact.id)
                )

                # Check the edge of the relationship
                contact_account_role_to_contact_relationship_edge = (
                    contact_account_role_to_contact_relationship_result.result_set[0][1]
                )
                logger.info(
                    f"contact_account_role_to_contact_relationship_edge: {contact_account_role_to_contact_relationship_edge}"
                )
                logger.info(
                    f"vars(contact_account_role_to_contact_relationship_edge): {vars(contact_account_role_to_contact_relationship_edge)}"
                )
                assert (
                    contact_account_role_to_contact_relationship_edge.relation
                    == "contact_account_role__to__contact"
                )

                # Check the contact side of the relationship
                contact_account_role_to_contact_relationship_contact_side = (
                    contact_account_role_to_contact_relationship_result.result_set[0][2]
                )
                logger.info(
                    f"contact_account_role_to_contact_relationship_contact_side: {contact_account_role_to_contact_relationship_contact_side}"
                )
                logger.info(
                    f"vars(contact_account_role_to_contact_relationship_contact_side): {vars(contact_account_role_to_contact_relationship_contact_side)}"
                )
                assert (
                    contact_account_role_to_contact_relationship_contact_side.properties[
                        "id"
                    ]
                    == str(contact.id)
                )
                assert (
                    contact_account_role_to_contact_relationship_contact_side.properties[
                        "organization_id"
                    ]
                    == str(org_id)
                )
                assert (
                    contact_account_role_to_contact_relationship_contact_side.properties[
                        "display_name"
                    ]
                    == contact.display_name
                )

                """ Verify the contact_pipeline_role__to__contact relationship was created """
                contact_pipeline_role_to_contact_relationship_result = await graph.query(
                    f"MATCH (n:{ContactPipelineRole.object_id.object_name})-[r:contact_pipeline_role__to__contact]->(m:{ContactV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"contact_pipeline_role_to_contact_relationship_result: {contact_pipeline_role_to_contact_relationship_result}"
                )
                logger.info(
                    f"vars(contact_pipeline_role_to_contact_relationship_result): {vars(contact_pipeline_role_to_contact_relationship_result)}"
                )

                assert (
                    len(contact_pipeline_role_to_contact_relationship_result.result_set)
                    == 1
                )

                # Check the contact pipeline role side of the relationship
                contact_pipeline_role_to_contact_relationship_contact_pipeline_role_side = contact_pipeline_role_to_contact_relationship_result.result_set[
                    0
                ][0]
                logger.info(
                    f"contact_pipeline_role_to_contact_relationship_contact_pipeline_role_side: {contact_pipeline_role_to_contact_relationship_contact_pipeline_role_side}"
                )
                logger.info(
                    f"vars(contact_pipeline_role_to_contact_relationship_contact_pipeline_role_side): {vars(contact_pipeline_role_to_contact_relationship_contact_pipeline_role_side)}"
                )
                assert (
                    contact_pipeline_role_to_contact_relationship_contact_pipeline_role_side.labels[
                        0
                    ]
                    == "contact_pipeline_role"
                )
                assert (
                    contact_pipeline_role_to_contact_relationship_contact_pipeline_role_side.properties[
                        "contact_id"
                    ]
                    == str(contact.id)
                )
                assert (
                    contact_pipeline_role_to_contact_relationship_contact_pipeline_role_side.properties[
                        "pipeline_id"
                    ]
                    == str(pipeline.id)
                )

                # Check the edge of the relationship
                contact_pipeline_role_to_contact_relationship_edge = (
                    contact_pipeline_role_to_contact_relationship_result.result_set[0][
                        1
                    ]
                )
                logger.info(
                    f"contact_pipeline_role_to_contact_relationship_edge: {contact_pipeline_role_to_contact_relationship_edge}"
                )
                logger.info(
                    f"vars(contact_pipeline_role_to_contact_relationship_edge): {vars(contact_pipeline_role_to_contact_relationship_edge)}"
                )
                assert (
                    contact_pipeline_role_to_contact_relationship_edge.relation
                    == "contact_pipeline_role__to__contact"
                )

                # Check the contact side of the relationship
                contact_pipeline_role_to_contact_relationship_contact_side = (
                    contact_pipeline_role_to_contact_relationship_result.result_set[0][
                        2
                    ]
                )
                logger.info(
                    f"contact_pipeline_role_to_contact_relationship_contact_side: {contact_pipeline_role_to_contact_relationship_contact_side}"
                )
                logger.info(
                    f"vars(contact_pipeline_role_to_contact_relationship_contact_side): {vars(contact_pipeline_role_to_contact_relationship_contact_side)}"
                )
                assert (
                    contact_pipeline_role_to_contact_relationship_contact_side.properties[
                        "id"
                    ]
                    == str(contact.id)
                )
                assert (
                    contact_pipeline_role_to_contact_relationship_contact_side.properties[
                        "organization_id"
                    ]
                    == str(org_id)
                )

                """ Verify the contact_pipeline_role__to__pipeline relationship was created """
                contact_pipeline_role_to_pipeline_relationship_result = await graph.query(
                    f"MATCH (n:{ContactPipelineRole.object_id.object_name})-[r:contact_pipeline_role__to__pipeline]->(m:{PipelineV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"contact_pipeline_role_to_pipeline_relationship_result: {contact_pipeline_role_to_pipeline_relationship_result}"
                )
                logger.info(
                    f"vars(contact_pipeline_role_to_pipeline_relationship_result): {vars(contact_pipeline_role_to_pipeline_relationship_result)}"
                )
                assert (
                    len(
                        contact_pipeline_role_to_pipeline_relationship_result.result_set
                    )
                    == 1
                )

                # Check the contact pipeline role side of the relationship
                contact_pipeline_role_to_pipeline_relationship_contact_pipeline_role_side = contact_pipeline_role_to_pipeline_relationship_result.result_set[
                    0
                ][0]
                logger.info(
                    f"contact_pipeline_role_to_pipeline_relationship_contact_pipeline_role_side: {contact_pipeline_role_to_pipeline_relationship_contact_pipeline_role_side}"
                )
                logger.info(
                    f"vars(contact_pipeline_role_to_pipeline_relationship_contact_pipeline_role_side): {vars(contact_pipeline_role_to_pipeline_relationship_contact_pipeline_role_side)}"
                )
                assert (
                    contact_pipeline_role_to_pipeline_relationship_contact_pipeline_role_side.labels[
                        0
                    ]
                    == "contact_pipeline_role"
                )
                assert (
                    contact_pipeline_role_to_pipeline_relationship_contact_pipeline_role_side.properties[
                        "contact_id"
                    ]
                    == str(contact.id)
                )
                assert (
                    contact_pipeline_role_to_pipeline_relationship_contact_pipeline_role_side.properties[
                        "pipeline_id"
                    ]
                    == str(pipeline.id)
                )

                # Check the edge of the relationship
                contact_pipeline_role_to_pipeline_relationship_edge = (
                    contact_pipeline_role_to_pipeline_relationship_result.result_set[0][
                        1
                    ]
                )
                logger.info(
                    f"contact_pipeline_role_to_pipeline_relationship_edge: {contact_pipeline_role_to_pipeline_relationship_edge}"
                )
                logger.info(
                    f"vars(contact_pipeline_role_to_pipeline_relationship_edge): {vars(contact_pipeline_role_to_pipeline_relationship_edge)}"
                )
                assert (
                    contact_pipeline_role_to_pipeline_relationship_edge.relation
                    == "contact_pipeline_role__to__pipeline"
                )

                # Check the pipeline side of the relationship
                contact_pipeline_role_to_pipeline_relationship_pipeline_side = (
                    contact_pipeline_role_to_pipeline_relationship_result.result_set[0][
                        2
                    ]
                )
                logger.info(
                    f"contact_pipeline_role_to_pipeline_relationship_pipeline_side: {contact_pipeline_role_to_pipeline_relationship_pipeline_side}"
                )
                logger.info(
                    f"vars(contact_pipeline_role_to_pipeline_relationship_pipeline_side): {vars(contact_pipeline_role_to_pipeline_relationship_pipeline_side)}"
                )
                assert (
                    contact_pipeline_role_to_pipeline_relationship_pipeline_side.properties[
                        "id"
                    ]
                    == str(pipeline.id)
                )
                assert (
                    contact_pipeline_role_to_pipeline_relationship_pipeline_side.properties[
                        "organization_id"
                    ]
                    == str(org_id)
                )
                assert (
                    contact_pipeline_role_to_pipeline_relationship_pipeline_side.properties[
                        "primary_contact_id"
                    ]
                    == str(contact.id)
                )

                """ Verify Pipeline domain relationships were created """
                """ Verify the stage relationship was created """
                stage_relationship_result = await graph.query(
                    f"MATCH (n:{PipelineV2.object_id.object_name})-[r:stage]->(m) RETURN n, r, m"
                )
                logger.info(f"stage_relationship_result: {stage_relationship_result}")
                logger.info(
                    f"vars(stage_relationship_result): {vars(stage_relationship_result)}"
                )
                assert len(stage_relationship_result.result_set) == 1

                # Check the pipeline side of the relationship
                stage_relationship_pipeline_side = stage_relationship_result.result_set[
                    0
                ][0]
                logger.info(
                    f"stage_relationship_pipeline_side: {stage_relationship_pipeline_side}"
                )
                assert stage_relationship_pipeline_side.properties["id"] == str(
                    pipeline.id
                )
                assert stage_relationship_pipeline_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    stage_relationship_pipeline_side.properties["display_name"]
                    == pipeline.display_name
                )

                # Check the edge of the relationship
                stage_relationship_edge = stage_relationship_result.result_set[0][1]
                logger.info(f"stage_relationship_edge: {stage_relationship_edge}")
                logger.info(
                    f"vars(stage_relationship_edge): {vars(stage_relationship_edge)}"
                )
                assert stage_relationship_edge.relation == "stage"

                # Check the stage side of the relationship
                stage_relationship_stage_side = stage_relationship_result.result_set[0][
                    2
                ]
                logger.info(
                    f"stage_relationship_stage_side: {stage_relationship_stage_side}"
                )
                assert stage_relationship_stage_side.properties["id"] == str(
                    pipeline.stage_id
                )

                """ Verify the pipeline__to__owner_user relationship was created """
                pipeline_to_owner_user_relationship_result = await graph.query(
                    f"MATCH (n:{PipelineV2.object_id.object_name})-[r:pipeline__to__owner_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"pipeline_to_owner_user_relationship_result: {pipeline_to_owner_user_relationship_result}"
                )
                logger.info(
                    f"vars(pipeline_to_owner_user_relationship_result): {vars(pipeline_to_owner_user_relationship_result)}"
                )
                assert len(pipeline_to_owner_user_relationship_result.result_set) == 1

                # Check the pipeline side of the relationship
                pipeline_to_owner_user_relationship_pipeline_side = (
                    pipeline_to_owner_user_relationship_result.result_set[0][0]
                )
                logger.info(
                    f"pipeline_to_owner_user_relationship_pipeline_side: {pipeline_to_owner_user_relationship_pipeline_side}"
                )
                assert pipeline_to_owner_user_relationship_pipeline_side.properties[
                    "id"
                ] == str(pipeline.id)
                assert pipeline_to_owner_user_relationship_pipeline_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    pipeline_to_owner_user_relationship_pipeline_side.properties[
                        "display_name"
                    ]
                    == pipeline.display_name
                )

                # Check the edge of the relationship
                pipeline_to_owner_user_relationship_edge = (
                    pipeline_to_owner_user_relationship_result.result_set[0][1]
                )
                logger.info(
                    f"pipeline_to_owner_user_relationship_edge: {pipeline_to_owner_user_relationship_edge}"
                )
                logger.info(
                    f"vars(pipeline_to_owner_user_relationship_edge): {vars(pipeline_to_owner_user_relationship_edge)}"
                )
                assert (
                    pipeline_to_owner_user_relationship_edge.relation
                    == "pipeline__to__owner_user"
                )

                # Check the user side of the relationship
                pipeline_to_owner_user_relationship_user_side = (
                    pipeline_to_owner_user_relationship_result.result_set[0][2]
                )
                logger.info(
                    f"pipeline_to_owner_user_relationship_user_side: {pipeline_to_owner_user_relationship_user_side}"
                )
                assert pipeline_to_owner_user_relationship_user_side.properties[
                    "id"
                ] == str(user.id)
                assert pipeline_to_owner_user_relationship_user_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    pipeline_to_owner_user_relationship_user_side.properties[
                        "display_name"
                    ]
                    == user.display_name
                )

                """ Verify pipeline__to__updated_by_user relationship was created """
                pipeline_to_updated_by_user_relationship_result = await graph.query(
                    f"MATCH (n:{PipelineV2.object_id.object_name})-[r:pipeline__to__updated_by_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"pipeline_to_updated_by_user_relationship_result: {pipeline_to_updated_by_user_relationship_result}"
                )
                logger.info(
                    f"vars(pipeline_to_updated_by_user_relationship_result): {vars(pipeline_to_updated_by_user_relationship_result)}"
                )
                assert (
                    len(pipeline_to_updated_by_user_relationship_result.result_set) == 1
                )

                # Check the pipeline side of the relationship
                pipeline_to_updated_by_user_relationship_pipeline_side = (
                    pipeline_to_updated_by_user_relationship_result.result_set[0][0]
                )
                logger.info(
                    f"pipeline_to_updated_by_user_relationship_pipeline_side: {pipeline_to_updated_by_user_relationship_pipeline_side}"
                )
                assert (
                    pipeline_to_updated_by_user_relationship_pipeline_side.properties[
                        "id"
                    ]
                    == str(pipeline.id)
                )
                assert (
                    pipeline_to_updated_by_user_relationship_pipeline_side.properties[
                        "organization_id"
                    ]
                    == str(org_id)
                )
                assert (
                    pipeline_to_updated_by_user_relationship_pipeline_side.properties[
                        "display_name"
                    ]
                    == pipeline.display_name
                )

                # Check the edge of the relationship
                pipeline_to_updated_by_user_relationship_edge = (
                    pipeline_to_updated_by_user_relationship_result.result_set[0][1]
                )
                logger.info(
                    f"pipeline_to_updated_by_user_relationship_edge: {pipeline_to_updated_by_user_relationship_edge}"
                )
                logger.info(
                    f"vars(pipeline_to_updated_by_user_relationship_edge): {vars(pipeline_to_updated_by_user_relationship_edge)}"
                )
                assert (
                    pipeline_to_updated_by_user_relationship_edge.relation
                    == "pipeline__to__updated_by_user"
                )

                # Check the user side of the relationship
                pipeline_to_updated_by_user_relationship_user_side = (
                    pipeline_to_updated_by_user_relationship_result.result_set[0][2]
                )
                logger.info(
                    f"pipeline_to_updated_by_user_relationship_user_side: {pipeline_to_updated_by_user_relationship_user_side}"
                )
                assert pipeline_to_updated_by_user_relationship_user_side.properties[
                    "id"
                ] == str(user.id)
                assert pipeline_to_updated_by_user_relationship_user_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    pipeline_to_updated_by_user_relationship_user_side.properties[
                        "display_name"
                    ]
                    == user.display_name
                )

                """ Verify the pipeline__to__created_by_user relationship was created """
                pipeline_to_created_by_user_relationship_result = await graph.query(
                    f"MATCH (n:{PipelineV2.object_id.object_name})-[r:pipeline__to__created_by_user]->(m:{OrganizationUserV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"pipeline_to_created_by_user_relationship_result: {pipeline_to_created_by_user_relationship_result}"
                )
                logger.info(
                    f"vars(pipeline_to_created_by_user_relationship_result): {vars(pipeline_to_created_by_user_relationship_result)}"
                )
                assert (
                    len(pipeline_to_created_by_user_relationship_result.result_set) == 1
                )

                # Check the pipeline side of the relationship
                pipeline_to_created_by_user_relationship_pipeline_side = (
                    pipeline_to_created_by_user_relationship_result.result_set[0][0]
                )
                logger.info(
                    f"pipeline_to_created_by_user_relationship_pipeline_side: {pipeline_to_created_by_user_relationship_pipeline_side}"
                )
                assert (
                    pipeline_to_created_by_user_relationship_pipeline_side.properties[
                        "id"
                    ]
                    == str(pipeline.id)
                )
                assert (
                    pipeline_to_created_by_user_relationship_pipeline_side.properties[
                        "organization_id"
                    ]
                    == str(org_id)
                )
                assert (
                    pipeline_to_created_by_user_relationship_pipeline_side.properties[
                        "display_name"
                    ]
                    == pipeline.display_name
                )

                # Check the edge of the relationship
                pipeline_to_created_by_user_relationship_edge = (
                    pipeline_to_created_by_user_relationship_result.result_set[0][1]
                )
                logger.info(
                    f"pipeline_to_created_by_user_relationship_edge: {pipeline_to_created_by_user_relationship_edge}"
                )
                logger.info(
                    f"vars(pipeline_to_created_by_user_relationship_edge): {vars(pipeline_to_created_by_user_relationship_edge)}"
                )
                assert (
                    pipeline_to_created_by_user_relationship_edge.relation
                    == "pipeline__to__created_by_user"
                )

                # Check the user side of the relationship
                pipeline_to_created_by_user_relationship_user_side = (
                    pipeline_to_created_by_user_relationship_result.result_set[0][2]
                )
                logger.info(
                    f"pipeline_to_created_by_user_relationship_user_side: {pipeline_to_created_by_user_relationship_user_side}"
                )
                assert pipeline_to_created_by_user_relationship_user_side.properties[
                    "id"
                ] == str(user.id)
                assert pipeline_to_created_by_user_relationship_user_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    pipeline_to_created_by_user_relationship_user_side.properties[
                        "display_name"
                    ]
                    == user.display_name
                )

                """ Verify the pipeline__to__account relationship was created """
                pipeline_to_account_relationship_result = await graph.query(
                    f"MATCH (n:{PipelineV2.object_id.object_name})-[r:pipeline__to__account]->(m:{AccountV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"pipeline_to_account_relationship_result: {pipeline_to_account_relationship_result}"
                )
                logger.info(
                    f"vars(pipeline_to_account_relationship_result): {vars(pipeline_to_account_relationship_result)}"
                )
                assert len(pipeline_to_account_relationship_result.result_set) == 1

                # Check the pipeline side of the relationship
                pipeline_to_account_relationship_pipeline_side = (
                    pipeline_to_account_relationship_result.result_set[0][0]
                )
                logger.info(
                    f"pipeline_to_account_relationship_pipeline_side: {pipeline_to_account_relationship_pipeline_side}"
                )
                assert pipeline_to_account_relationship_pipeline_side.properties[
                    "id"
                ] == str(pipeline.id)
                assert pipeline_to_account_relationship_pipeline_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    pipeline_to_account_relationship_pipeline_side.properties[
                        "display_name"
                    ]
                    == pipeline.display_name
                )

                # Check the edge of the relationship
                pipeline_to_account_relationship_edge = (
                    pipeline_to_account_relationship_result.result_set[0][1]
                )
                logger.info(
                    f"pipeline_to_account_relationship_edge: {pipeline_to_account_relationship_edge}"
                )
                logger.info(
                    f"vars(pipeline_to_account_relationship_edge): {vars(pipeline_to_account_relationship_edge)}"
                )
                assert (
                    pipeline_to_account_relationship_edge.relation
                    == "pipeline__to__account"
                )

                # Check the account side of the relationship
                pipeline_to_account_relationship_account_side = (
                    pipeline_to_account_relationship_result.result_set[0][2]
                )
                logger.info(
                    f"pipeline_to_account_relationship_account_side: {pipeline_to_account_relationship_account_side}"
                )
                assert pipeline_to_account_relationship_account_side.properties[
                    "id"
                ] == str(account.id)
                assert pipeline_to_account_relationship_account_side.properties[
                    "organization_id"
                ] == str(org_id)

                """ Verify the pipeline__to__primary_contact was created """
                pipeline_to_primary_contact_relationship_result = await graph.query(
                    f"MATCH (n:{PipelineV2.object_id.object_name})-[r:pipeline__to__primary_contact]->(m:{ContactV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"pipeline_to_primary_contact_relationship_result: {pipeline_to_primary_contact_relationship_result}"
                )
                logger.info(
                    f"vars(pipeline_to_primary_contact_relationship_result): {vars(pipeline_to_primary_contact_relationship_result)}"
                )
                assert (
                    len(pipeline_to_primary_contact_relationship_result.result_set) == 1
                )

                # Check the pipeline side of the relationship
                pipeline_to_primary_contact_relationship_pipeline_side = (
                    pipeline_to_primary_contact_relationship_result.result_set[0][0]
                )
                logger.info(
                    f"pipeline_to_primary_contact_relationship_pipeline_side: {pipeline_to_primary_contact_relationship_pipeline_side}"
                )
                assert (
                    pipeline_to_primary_contact_relationship_pipeline_side.properties[
                        "id"
                    ]
                    == str(pipeline.id)
                )
                assert (
                    pipeline_to_primary_contact_relationship_pipeline_side.properties[
                        "organization_id"
                    ]
                    == str(org_id)
                )
                assert (
                    pipeline_to_primary_contact_relationship_pipeline_side.properties[
                        "display_name"
                    ]
                    == pipeline.display_name
                )
                assert (
                    pipeline_to_primary_contact_relationship_pipeline_side.properties[
                        "stage_id"
                    ]
                    == str(pipeline.stage_id)
                )
                assert (
                    pipeline_to_primary_contact_relationship_pipeline_side.properties[
                        "owner_user_id"
                    ]
                    == str(pipeline.owner_user_id)
                )

                # Check the edge of the relationship
                pipeline_to_primary_contact_relationship_edge = (
                    pipeline_to_primary_contact_relationship_result.result_set[0][1]
                )
                logger.info(
                    f"pipeline_to_primary_contact_relationship_edge: {pipeline_to_primary_contact_relationship_edge}"
                )
                logger.info(
                    f"vars(pipeline_to_primary_contact_relationship_edge): {vars(pipeline_to_primary_contact_relationship_edge)}"
                )
                assert (
                    pipeline_to_primary_contact_relationship_edge.relation
                    == "pipeline__to__primary_contact"
                )

                # Check the contact side of the relationship
                pipeline_to_primary_contact_relationship_contact_side = (
                    pipeline_to_primary_contact_relationship_result.result_set[0][2]
                )
                logger.info(
                    f"pipeline_to_primary_contact_relationship_contact_side: {pipeline_to_primary_contact_relationship_contact_side}"
                )
                assert pipeline_to_primary_contact_relationship_contact_side.properties[
                    "id"
                ] == str(contact.id)
                assert pipeline_to_primary_contact_relationship_contact_side.properties[
                    "organization_id"
                ] == str(org_id)
                assert (
                    pipeline_to_primary_contact_relationship_contact_side.properties[
                        "display_name"
                    ]
                    == contact.display_name
                )
                assert pipeline_to_primary_contact_relationship_contact_side.properties[
                    "stage_id"
                ] == str(contact.stage_id)
                assert pipeline_to_primary_contact_relationship_contact_side.properties[
                    "owner_user_id"
                ] == str(contact.owner_user_id)
                assert pipeline_to_primary_contact_relationship_contact_side.properties[
                    "primary_account_id"
                ] == str(contact.primary_account_id)

            # TODO: Need to add meeting domain object level relationships

        async def test_domain_object_relationships_updates(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            falkordb_client: FalkorDBClient,
            metadata_service: MetadataService,
            user_service: UserService,
            organization_id: UUID,
            user_id: UUID,
            _organization: None,
            _user: None,  # TODO: See if we can create the user in the function
            account: AccountV2,
            contact: ContactV2,
            pipeline: PipelineV2,
            meeting: MeetingV2,
            execute_queries: bool = True,  # Set this to True or False as needed
        ) -> None:
            """
            Test the updates of domain object relationships
            """

            # Pre-setup is the same as the test_indexing_nodes test
            # Get list of all user ids and org ids in an instance.
            user_and_org_ids = await falkordb_indexing_lib.get_list_of_all_user_and_org_ids_in_instance()
            logger.info(f"user_and_org_ids: {user_and_org_ids}")

            # Group users by organization
            org_id_to_user_ids = await falkordb_indexing_lib.group_users_by_org(
                user_and_org_ids
            )
            logger.info(f"org_id_to_user_ids: {org_id_to_user_ids}")

            # For each org, create a graph and index users
            for org_id in [organization_id]:
                # Create a graph for the org
                graph = await falkordb_indexing_lib.index_organization(
                    org_id=org_id, org_id_to_user_ids=org_id_to_user_ids
                )
                logger.info(f"vars(graph): {vars(graph)}")
                logger.info(f"graph.schema: {graph.schema}")
                logger.info(f"vars(graph.schema): {vars(graph.schema)}")

                # Verify the graph was created
                assert graph is not None
                assert graph._name is not None
                assert graph.client is not None

                # Get the old contact's domain relationships
                old_contact_domain_relationships_query = f"MATCH(n:{ContactV2.object_id.object_name})-[r]->(m) RETURN distinct(type(r))"
                old_contact_domain_relationships_result = await graph.query(
                    old_contact_domain_relationships_query
                )
                logger.info(
                    f"old_contact_domain_relationships_result.result_set: {old_contact_domain_relationships_result.result_set}"
                )

                # Convert contact to dictionary
                contact_dict = falkordb_indexing_lib._convert_model_to_dict(contact)
                contact_props = falkordb_client.clean_props(contact_dict)
                logger.info(f"contact_props: {contact_props}")

                org_schema = (
                    await metadata_service.get_organization_domain_object_schema(
                        organization_id=organization_id
                    )
                )

                # Update the graph with the new data
                await falkordb_indexing_lib.update_complex_node_structure(
                    graph=graph,
                    node_type=ContactV2.object_id.object_name,
                    node_id=str(contact.id),
                    data=contact_props,
                    schema=org_schema,
                    execute_queries=execute_queries,
                )

                # Assert that the new contact's domain relationships are the same as the old ones
                if execute_queries:
                    new_contact_domain_relationships_query = f"MATCH(n:{ContactV2.object_id.object_name})-[r]->(m) RETURN distinct(type(r))"
                    new_contact_domain_relationships_result = await graph.query(
                        new_contact_domain_relationships_query
                    )
                    logger.info(
                        f"new_contact_domain_relationships_result.result_set: {new_contact_domain_relationships_result.result_set}"
                    )
                    assert (
                        old_contact_domain_relationships_result.result_set
                        == new_contact_domain_relationships_result.result_set
                    )

        async def test_custom_association_creation(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            user_service: UserService,
            organization_id: UUID,
            user_id: UUID,
            _organization: None,
            _user: None,  # TODO: See if we can create the user in the function
            account: AccountV2,
            contact: ContactV2,
            pipeline: PipelineV2,
            meeting: MeetingV2,
            account_to_contact_association: CustomObjectAssociation,
            account_to_pipeline_association: CustomObjectAssociation,
            account_to_meeting_association: CustomObjectAssociation,
            association_records: None,
        ) -> None:
            """
            Test the creation of custom associations between domain objects
            """

            # Pre-setup is the same as the test_indexing_nodes test
            # Get list of all user ids and org ids in an instance.
            user_and_org_ids = await falkordb_indexing_lib.get_list_of_all_user_and_org_ids_in_instance()
            logger.info(f"user_and_org_ids: {user_and_org_ids}")

            # Group users by organization
            org_id_to_user_ids = await falkordb_indexing_lib.group_users_by_org(
                user_and_org_ids
            )
            logger.info(f"org_id_to_user_ids: {org_id_to_user_ids}")

            # For each org, create a graph and index users
            for org_id in [organization_id]:
                # Create a graph for the org
                graph = await falkordb_indexing_lib.index_organization(
                    org_id=org_id, org_id_to_user_ids=org_id_to_user_ids
                )
                logger.info(f"vars(graph): {vars(graph)}")
                logger.info(f"graph.schema: {graph.schema}")
                logger.info(f"vars(graph.schema): {vars(graph.schema)}")

                # Verify the graph was created
                assert graph is not None
                assert graph._name is not None
                assert graph.client is not None

                """ Verify Account custom associations were created """
                """ Account to Contact association """
                account_to_contact_association_result = await graph.query(
                    f"MATCH (n:{AccountV2.object_id.object_name})-[r:`{account_to_contact_association.id}`]->(m:{ContactV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"account_to_contact_association_result: {account_to_contact_association_result}"
                )
                logger.info(
                    f"vars(account_to_contact_association_result): {vars(account_to_contact_association_result)}"
                )

                # Get the account side of the relationship
                account_to_contact_association_account_side = (
                    account_to_contact_association_result.result_set[0][0]
                )
                logger.info(
                    f"account_to_contact_association_account_side: {account_to_contact_association_account_side}"
                )
                logger.info(
                    f"vars(account_to_contact_association_account_side): {vars(account_to_contact_association_account_side)}"
                )

                assert (
                    account_to_contact_association_account_side.labels[0] == "account"
                )
                assert account_to_contact_association_account_side.properties[
                    "id"
                ] == str(account.id)
                assert account_to_contact_association_account_side.properties[
                    "organization_id"
                ] == str(org_id)

                # Get the edge of the relationship
                account_to_contact_association_edge = (
                    account_to_contact_association_result.result_set[0][1]
                )
                logger.info(
                    f"account_to_contact_association_edge: {account_to_contact_association_edge}"
                )
                logger.info(
                    f"vars(account_to_contact_association_edge): {vars(account_to_contact_association_edge)}"
                )
                assert account_to_contact_association_edge.relation == str(
                    account_to_contact_association.id
                )

                # Get the contact side of the relationship
                assert (
                    account_to_contact_association_result.result_set[0][2].labels[0]
                    == "contact"
                )
                assert account_to_contact_association_result.result_set[0][
                    2
                ].properties["id"] == str(contact.id)
                assert account_to_contact_association_result.result_set[0][
                    2
                ].properties["organization_id"] == str(org_id)
                assert (
                    account_to_contact_association_result.result_set[0][2].properties[
                        "display_name"
                    ]
                    == contact.display_name
                )
                assert account_to_contact_association_result.result_set[0][
                    2
                ].properties["stage_id"] == str(contact.stage_id)
                assert account_to_contact_association_result.result_set[0][
                    2
                ].properties["owner_user_id"] == str(contact.owner_user_id)

                """ Account to Pipeline association """
                account_to_pipeline_association_result = await graph.query(
                    f"MATCH (n:{AccountV2.object_id.object_name})-[r:`{account_to_pipeline_association.id}`]->(m:{PipelineV2.object_id.object_name}) RETURN n, r, m"
                )
                logger.info(
                    f"account_to_pipeline_association_result: {account_to_pipeline_association_result}"
                )
                logger.info(
                    f"vars(account_to_pipeline_association_result): {vars(account_to_pipeline_association_result)}"
                )

                # Get the account side of the relationship
                account_to_pipeline_association_account_side = (
                    account_to_pipeline_association_result.result_set[0][0]
                )
                logger.info(
                    f"account_to_pipeline_association_account_side: {account_to_pipeline_association_account_side}"
                )
                logger.info(
                    f"vars(account_to_pipeline_association_account_side): {vars(account_to_pipeline_association_account_side)}"
                )
                assert (
                    account_to_pipeline_association_account_side.labels[0] == "account"
                )

                # Get the edge of the relationship
                account_to_pipeline_association_edge = (
                    account_to_pipeline_association_result.result_set[0][1]
                )
                logger.info(
                    f"account_to_pipeline_association_edge: {account_to_pipeline_association_edge}"
                )
                logger.info(
                    f"vars(account_to_pipeline_association_edge): {vars(account_to_pipeline_association_edge)}"
                )
                assert account_to_pipeline_association_edge.relation == str(
                    account_to_pipeline_association.id
                )

                # Get the pipeline side of the relationship
                assert (
                    account_to_pipeline_association_result.result_set[0][2].labels[0]
                    == "pipeline"
                )
                assert account_to_pipeline_association_result.result_set[0][
                    2
                ].properties["id"] == str(pipeline.id)
                assert account_to_pipeline_association_result.result_set[0][
                    2
                ].properties["organization_id"] == str(org_id)
                assert (
                    account_to_pipeline_association_result.result_set[0][2].properties[
                        "display_name"
                    ]
                    == pipeline.display_name
                )
                assert account_to_pipeline_association_result.result_set[0][
                    2
                ].properties["stage_id"] == str(pipeline.stage_id)
                assert account_to_pipeline_association_result.result_set[0][
                    2
                ].properties["owner_user_id"] == str(pipeline.owner_user_id)

                # Account to Meeting association
                # TODO: This isn't using the latest 'create_complex_node_structure' function. Fix this.
                # account_to_meeting_association_result = await graph.query(
                #     f"MATCH (n:{AccountV2.object_id.object_name})-[r:Account_to_Meeting]->(m:{MeetingV2.object_id.object_name}) RETURN n, r, m"
                # )
                # logger.info(
                #     f"account_to_meeting_association_result: {account_to_meeting_association_result}"
                # )
                # logger.info(
                #     f"vars(account_to_meeting_association_result): {vars(account_to_meeting_association_result)}"
                # )

                # assert (
                #     account_to_meeting_association_result.result_set[0][0].labels[0]
                #     == "account"
                # )
                # assert (
                #     account_to_meeting_association_result.result_set[0][1].relation
                #     == "Account_to_Meeting"
                # )
                # assert (
                #     account_to_meeting_association_result.result_set[0][2].labels[0]
                #     == "meeting"
                # )

                # )

    class TestDomainObjectCounts:
        async def test_get_accounts_count_falkor(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            organization_id: UUID,
            account: AccountV2,
            account2: AccountV2,
            account_with_custom_fields: AccountV2,
        ) -> None:
            """Test getting the count of accounts for an organization from Falkor."""

            await falkordb_indexing_lib.index_accounts(
                organization_id=organization_id,
                account_ids=[
                    account.id,
                    account2.id,
                    account_with_custom_fields.id,
                ],
            )

            # Get the count of accounts for the organization from Falkor
            falkor_count = await falkordb_indexing_lib.get_domain_object_count_falkor(
                organization_id, AccountV2
            )
            logger.info(f"falkor_count: {falkor_count}")

            # Get the count of accounts for the organization from Postgres
            postgres_count = await falkordb_indexing_lib.get_accounts_count(
                organization_id
            )
            logger.info(f"postgres_count: {postgres_count}")

            logger.info(f"falkor_count: {falkor_count}")
            logger.info(f"postgres_count: {postgres_count}")
            logger.info(
                f"falkor_count == postgres_count: {falkor_count == postgres_count}"
            )
            assert falkor_count == postgres_count

        async def test_get_contacts_count_falkor(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            organization_id: UUID,
            contact: ContactV2,
            contact2: ContactV2,
            contact_with_custom_fields: ContactV2,
        ) -> None:
            """Test getting the count of contacts for an organization from Falkor."""

            await falkordb_indexing_lib.index_contacts(
                organization_id=organization_id,
                contact_ids=[
                    contact.id,
                    contact2.id,
                    contact_with_custom_fields.id,
                ],
            )

            # Get the count of contacts for the organization from Falkor
            falkor_count = await falkordb_indexing_lib.get_domain_object_count_falkor(
                organization_id, ContactV2
            )
            logger.info(f"falkor_count: {falkor_count}")

            # Get the count of contacts for the organization from Postgres
            postgres_count = await falkordb_indexing_lib.get_contacts_count(
                organization_id
            )
            logger.info(f"postgres_count: {postgres_count}")

            logger.info(f"falkor_count: {falkor_count}")
            logger.info(f"postgres_count: {postgres_count}")
            logger.info(
                f"falkor_count == postgres_count: {falkor_count == postgres_count}"
            )
            assert falkor_count == postgres_count

        async def test_get_pipelines_count_falkor(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            organization_id: UUID,
            pipeline: PipelineV2,
            pipeline2: PipelineV2,
        ) -> None:
            """Test getting the count of pipelines for an organization from Falkor."""

            await falkordb_indexing_lib.index_pipelines(
                organization_id=organization_id,
                pipeline_ids=[pipeline.id, pipeline2.id],
            )

            # Get the count of pipelines for the organization from Falkor
            falkor_count = await falkordb_indexing_lib.get_domain_object_count_falkor(
                organization_id, PipelineV2
            )
            logger.info(f"falkor_count: {falkor_count}")

            # Get the count of pipelines for the organization from Postgres
            postgres_count = await falkordb_indexing_lib.get_pipelines_count(
                organization_id
            )
            logger.info(f"postgres_count: {postgres_count}")

            logger.info(f"falkor_count: {falkor_count}")
            logger.info(f"postgres_count: {postgres_count}")
            logger.info(
                f"falkor_count == postgres_count: {falkor_count == postgres_count}"
            )
            assert falkor_count == postgres_count

        async def test_get_contact_pipeline_roles_count_falkor(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            organization_id: UUID,
            contact: ContactV2,
            contact2: ContactV2,
            pipeline: PipelineV2,
            pipeline2: PipelineV2,
        ) -> None:
            """Test getting the count of contact pipeline roles for an organization from Falkor."""

            logger.info(f"contact.id: {contact.id}")
            logger.info(f"contact2.id: {contact2.id}")
            logger.info(f"pipeline.id: {pipeline.id}")
            logger.info(f"pipeline2.id: {pipeline2.id}")

            # Index the contact pipeline roles
            await falkordb_indexing_lib.index_all_contact_pipeline_roles_for_organization_in_batches(
                organization_id=organization_id,
                offset=0,
                batch_size=5,
            )

            # Get the count of contact pipeline roles for the organization from Falkor
            falkor_count = await falkordb_indexing_lib.get_domain_object_count_falkor(
                organization_id, ContactPipelineRole
            )
            logger.info(f"falkor_count: {falkor_count}")

            # Get the count of contact pipeline roles for the organization from Postgres
            postgres_count = (
                await falkordb_indexing_lib.get_contact_pipeline_roles_count(
                    organization_id
                )
            )
            logger.info(f"postgres_count: {postgres_count}")

            logger.info(f"falkor_count: {falkor_count}")
            logger.info(f"postgres_count: {postgres_count}")
            logger.info(
                f"falkor_count == postgres_count: {falkor_count == postgres_count}"
            )
            assert falkor_count == postgres_count

        async def test_get_contact_account_roles_count_falkor(
            self,
            falkordb_indexing_lib: FalkorDBIndexingLib,
            organization_id: UUID,
            contact: ContactV2,
            contact2: ContactV2,
            account: AccountV2,
            account2: AccountV2,
        ) -> None:
            """Test getting the count of contact account roles for an organization from Falkor."""

            logger.info(f"contact.id: {contact.id}")
            logger.info(f"contact2.id: {contact2.id}")
            logger.info(f"account.id: {account.id}")
            logger.info(f"account2.id: {account2.id}")

            # Index the contact pipeline roles
            await falkordb_indexing_lib.index_all_contact_account_roles_for_organization_in_batches(
                organization_id=organization_id,
                offset=0,
                batch_size=5,
            )

            # Get the count of contact account roles for the organization from Falkor
            falkor_count = await falkordb_indexing_lib.get_domain_object_count_falkor(
                organization_id, ContactAccountRole
            )
            logger.info(f"falkor_count: {falkor_count}")

            # Get the count of contact account roles for the organization from Postgres
            postgres_count = (
                await falkordb_indexing_lib.get_contact_account_roles_count(
                    organization_id
                )
            )
            logger.info(f"postgres_count: {postgres_count}")

            logger.info(f"falkor_count: {falkor_count}")
            logger.info(f"postgres_count: {postgres_count}")
            logger.info(
                f"falkor_count == postgres_count: {falkor_count == postgres_count}"
            )
            assert falkor_count == postgres_count
