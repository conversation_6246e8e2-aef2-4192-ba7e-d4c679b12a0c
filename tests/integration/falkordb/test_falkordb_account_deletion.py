import uuid
from collections.abc import AsyncGenerator
from datetime import UTC, datetime
from uuid import UUID, uuid4

import pytest
from sqlalchemy import text

from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2

# Assuming AssociationService might be needed if account fixture has complex dependencies
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.metadata.service.pipeline_stage_service import (
    PipelineStageSelectListService,
)
from salestech_be.core.user.service.user_service import UserService
from salestech_be.core.user.utils import default_roles
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.falkordb.falkordb_client import FalkorDBClient
from salestech_be.falkordb.indexing_lib import FalkorDBIndexingLib
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now

logger = get_logger()


@pytest.fixture(scope="class")
def organization_id() -> UUID:
    """Test organization ID."""
    return uuid4()


@pytest.fixture(scope="class")
def user_id() -> UUID:
    """Test user ID."""
    return uuid4()


@pytest.fixture(scope="class")
async def primary_email() -> str:
    """Test primary email."""
    return f"test-deletion-{uuid.uuid4()}@example.com"


@pytest.fixture(scope="class")
async def _user(
    _engine: DatabaseEngine,
    user_id: UUID,
    primary_email: str,
) -> None:
    """Create test user."""
    try:
        await _engine.execute(
            text("""
                INSERT INTO "user" (id, email, first_name, last_name, created_at)
                VALUES (:id, :email, :first_name, :last_name, :created_at)
                ON CONFLICT (id) DO NOTHING
            """),
            {
                "id": user_id,
                "email": primary_email,
                "first_name": "TestDeletion",
                "last_name": "User",
                "created_at": datetime.now(UTC),
            },
        )
    except Exception as e:
        logger.info(f"Error creating user for deletion test: {e}")


@pytest.fixture(scope="class")
async def _organization(
    _engine: DatabaseEngine,
    organization_id: UUID,
    user_id: UUID,
    _user: None,
    user_service: UserService,
) -> None:
    """Create test organization."""
    await _engine.execute(
        text("""
            INSERT INTO organization (
                id, display_name, created_at, created_by_user_id
            ) VALUES (
                :id, :display_name, :created_at, :created_by_user_id
            )
            ON CONFLICT (id) DO NOTHING
        """),
        {
            "id": organization_id,
            "display_name": "Test Deletion Organization",
            "created_at": zoned_utc_now(),
            "created_by_user_id": user_id,
        },
    )
    # Ensure user organization association, handling potential conflicts if already exists
    existing_association_row = await _engine.execute(
        text(
            "SELECT user_id FROM user_organization_association WHERE user_id = :user_id AND organization_id = :organization_id"
        ),
        {"user_id": user_id, "organization_id": organization_id},
    )
    existing_association = (
        existing_association_row.first()
    )  # Get the first row from the result

    if not existing_association:
        await user_service.create_hidden_user_and_organization_association_v2(
            email=f"test-deletion-assoc-{user_id}@example.com",  # Unique email for association
            organization_id=organization_id,
        )

    # Activate association if it's not active
    # This part might need adjustment based on how user_service.active_user_and_organization_association behaves
    # For simplicity, we assume it can be called even if already active or correctly handles it.
    try:
        await user_service.active_user_and_organization_association(
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.info(
            f"Could not activate user/org association (might be okay if already active): {e}"
        )


@pytest.fixture(scope="class")
async def make_user_org(
    user_repository: UserRepository,
    select_list_service: InternalSelectListService,
    pipeline_stage_select_list_service: PipelineStageSelectListService,
    user_id: UUID,
    organization_id: UUID,
    _organization: None,
    _user: None,
) -> None:
    """Ensure user is associated with the organization and bootstrap select lists."""
    # Check if association already exists
    association = await user_repository.find_association_by_user_id_and_organization_id(
        user_id=user_id, organization_id=organization_id
    )
    if not association:
        await user_repository.associate_user_to_an_organization(
            user_id=user_id,
            organization_id=organization_id,
            roles=default_roles(),
        )
        logger.info(
            f"User {user_id} associated with organization {organization_id} for deletion test."
        )
    else:
        logger.info(
            f"User {user_id} already associated with organization {organization_id}."
        )

    # Bootstrap select lists (idempotent or checks internally)
    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id, user_id=user_id
    )
    await pipeline_stage_select_list_service.bootstrap_organization_default_pipeline_stage_select_list(
        organization_id=organization_id, actioning_user_id=user_id
    )


@pytest.fixture(scope="class")
async def account(
    account_service: AccountService,
    # association_service: AssociationService, # Not directly used by create_account_v2
    organization_id: UUID,
    user_id: UUID,
    make_user_org: None,  # Ensure user and org setup is complete
) -> AccountV2:
    """Create test account for deletion tests."""
    account_name = f"Test Account for Deletion {uuid.uuid4()}"
    created_account = await account_service.create_account_v2(
        organization_id=organization_id,
        user_id=user_id,
        create_account_request=CreateAccountRequest(
            display_name=account_name,
            owner_user_id=user_id,
            official_website="https://example-deletion.com",
        ),
    )
    # Fetch the full account object to ensure all fields are populated as expected by tests
    list_result = await account_service.list_accounts_v2(
        organization_id=organization_id, only_include_account_ids={created_account.id}
    )
    assert list_result, f"Failed to retrieve created account {created_account.id}"
    return list_result[0]


@pytest.fixture
async def falkordb_client() -> AsyncGenerator[FalkorDBClient, None]:
    """Yield FalkorDB client instance."""
    client = FalkorDBClient()
    yield client
    # Optional: Add cleanup logic here if needed, e.g., closing connections if not managed by pool
    if client.pool:  # If pool was created by this client instance
        await client.pool.disconnect()


@pytest.fixture
async def falkordb_indexing_lib(
    _engine: DatabaseEngine, falkordb_client: FalkorDBClient
) -> FalkorDBIndexingLib:
    """Yield FalkorDB indexing lib."""
    # Ensure _engine fixture is resolved if FalkorDBIndexingLib requires it directly
    # or through services it initializes.
    # The provided snippet for FalkorDBIndexingLib in original file doesn't show direct _engine use in __init__
    # but its methods might use services that need it.
    return FalkorDBIndexingLib(db_engine=_engine, falkordb_client=falkordb_client)


class TestFalkorIndexDeletion:
    async def test_index_archive_and_reindex_account(
        self,
        falkordb_indexing_lib: FalkorDBIndexingLib,
        falkordb_client: FalkorDBClient,
        account_service: AccountService,
        account: AccountV2,
        organization_id: UUID,
        user_id: UUID,  # Required for archive_by_id
    ) -> None:
        """
        Test scenario:
        1. Index an account.
        2. Verify it exists in FalkorDB.
        3. Archive the account.
        4. Re-index accounts (which should now mark the archived one as deleted in FalkorDB).
        5. Verify the account node is deleted from FalkorDB.
        """
        # 1. Index the initial account
        # schema = await falkordb_indexing_lib.metadata_service.get_organization_schema(organization_id) # Schema is fetched internally
        await falkordb_indexing_lib.index_accounts(
            organization_id=organization_id,
            account_ids=[account.id],
            # schema=schema, # Removed as it's fetched internally by index_accounts
        )

        # 2. Verify it exists in FalkorDB
        graph_name = falkordb_client.get_graph_name(organization_id)
        graph = falkordb_client.client.select_graph(graph_name)

        node_in_db = await falkordb_client.get_node_by_id(
            graph=graph,
            node_type=AccountV2.object_id.object_name,
            node_id=account.id,
        )
        assert node_in_db is not None, (
            f"Account {account.id} should exist in FalkorDB after initial indexing."
        )
        assert node_in_db.get("id") == str(account.id)

        # 3. Archive the account
        await account_service.archive_by_id(
            account_id=account.id,
            organization_id=organization_id,
            user_id=user_id,  # Assuming archive needs user_id
        )
        logger.info(f"Account {account.id} archived.")

        # 4. Re-index accounts (simulating a subsequent sync)
        # This call should identify that 'account.id' is now archived and delete it from FalkorDB.
        await falkordb_indexing_lib.index_accounts(
            organization_id=organization_id,
            account_ids=[account.id],  # Pass the ID of the (now archived) account
            # schema=schema, # Removed as it's fetched internally by index_accounts
        )
        logger.info(f"Re-indexed accounts including the archived one: {account.id}.")

        # 5. Verify the account node is now deleted from FalkorDB
        node_after_reindex = await falkordb_client.get_node_by_id(
            graph=graph,
            node_type=AccountV2.object_id.object_name,
            node_id=account.id,
        )
        assert node_after_reindex is None, (
            f"Account {account.id} should be deleted from FalkorDB after re-indexing an archived account."
        )
        logger.info(
            f"Successfully verified account {account.id} is deleted from FalkorDB."
        )
