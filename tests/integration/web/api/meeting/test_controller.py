import datetime
import json
from collections.abc import Awaitable, Callable
from http import HTTPStatus
from uuid import UUID, uuid4

import pytest
from asyncpg.pgproto.pgproto import timedelta
from fastapi import FastAPI
from fastapi.encoders import jsonable_encoder
from httpx import AsyncClient
from pydantic import EmailStr
from starlette import status

from salestech_be.common.exception.exception import (
    IllegalStateError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.type.patch_request import UNSET
from salestech_be.core.extraction_prompt.type.shared_type import ExtractionType
from salestech_be.core.meeting.dto.meeting_insight_dto import InsightDTO
from salestech_be.core.meeting.service_type import RecallStatusCode
from salestech_be.core.tracker.dto.tracker_dto import TrackerStatsDTO
from salestech_be.core.tracker.types import (
    MeetingTrackedMetadata,
    MeetingTrackedTranscriptSentence,
    MeetingTrackerFilter,
    TrackerReferenceIdType,
    TrackerScope,
    TrackUserType,
)
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.insight_repository import InsightRepository
from salestech_be.db.dao.meeting_repository import MeetingRepository
from salestech_be.db.dao.tracker_repository import TrackerRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.insight import (
    Insight as DbInsight,
)
from salestech_be.db.models.insight import (
    InsightAuthorType,
    InsightReferenceIdType,
    InsightSection,
    InsightSourceType,
    InsightUserFeedback,
)
from salestech_be.db.models.meeting import (
    BotStatusEvent as DbBotStatusEvent,
)
from salestech_be.db.models.meeting import (
    BotStatusHistory,
    Meeting,
    MeetingAnnotationType,
    MeetingAttendeeMonologue,
    MeetingAttendeeTalkRatio,
    MeetingBot,
    MeetingBotStatus,
    MeetingShare,
    MeetingShareAccessType,
    MeetingStats,
    MeetingUpdate,
)
from salestech_be.db.models.tracker import Tracker, TrackerStats
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.common.container import (
    DeleteEntityResponse,
    PaginatedListResponse,
)
from salestech_be.web.api.meeting.schema import (
    CreateInsightsFieldRequest,
    CreateMeetingAnnotationRequest,
    CreateMeetingClipRequest,
    CreateMeetingShareRequest,
    ListMeetingClipRequest,
    ListMeetingShareRequest,
    ListMeetingTrackerStatsRequest,
    LiveTranscriptSession,
    MeetingAnnotation,
    MeetingClipResponse,
    MeetingConsentResponse,
    MeetingInsight,
    MeetingInsightField,
    MeetingShareMeetingInfoResponse,
    MeetingShareResponse,
    PatchMeetingClipRequest,
    PatchMeetingShareRequest,
    RecallBotEventData,
    RecallBotEventStatus,
    RecallBotEventTypes,
    RecallBotStatusChangeEvent,
    TranscriptRange,
)
from tests.integration.web.api.common.test_utils import (
    create_meeting,
    create_meeting_bot,
    get_headers,
)
from tests.integration.web.api.util.api_client import (
    APITestClient,
    APITestError,
)
from tests.integration.web.api.util.common_api_client import (
    CommonAPIClient,
)


@pytest.mark.skip(reason="Failing because of connection pool closed")
async def test_insight_lifecycle(
    multi_api_client: dict[str, CommonAPIClient],
    api_client: AsyncClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
    insight_repository: InsightRepository,
) -> None:
    admin_api_client = multi_api_client["admin"]
    user_id = admin_api_client.default_user_id
    organization_id = admin_api_client.default_organization_id

    meeting = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        organization_id=organization_id,
        user_id=user_id,
    )

    # List existing - None yet
    headers = get_headers(organization_id=organization_id, user_id=user_id)
    list_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/meetings/insights/_list",
        headers=headers,
        json={"meeting_id": str(meeting.id)},
    )
    assert list_response.status_code == status.HTTP_200_OK
    insights_data = list_response.json()
    assert (
        len(insights_data["map_data"][ExtractionType.XSCRIPT_SUMMARY_EXTRACTION.value])
        == 0
    )

    # Create in DB
    persisted_insights = await create_meeting_insight_section_with_fields(
        insight_repository=insight_repository,
        meeting_id=meeting.id,
        organization_id=organization_id,
        user_id=user_id,
    )

    # List existing
    list_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/meetings/insights/_list",
        headers=headers,
        json={"meeting_id": str(meeting.id)},
    )
    assert list_response.status_code == status.HTTP_200_OK
    insights_data = list_response.json()
    assert (
        len(insights_data["map_data"][ExtractionType.XSCRIPT_SUMMARY_EXTRACTION.value])
        == 1
    )
    result_meeting_insight = MeetingInsight.model_validate(
        insights_data["map_data"][ExtractionType.XSCRIPT_SUMMARY_EXTRACTION.value][0]
    )
    expected_meeting_insight = MeetingInsight(
        id=persisted_insights.insight_section.id,
        insight_type=persisted_insights.insight_section.insight_type,
        name=persisted_insights.insight_section.name,
        source_type=persisted_insights.insight_section.source_type,
        organization_id=organization_id,
        extraction_section_id=persisted_insights.insight_section.extraction_section_id,
        extraction_section_version=persisted_insights.insight_section.extraction_section_version,
        meeting_id=meeting.id,
        created_at=persisted_insights.insight_section.created_at,
        created_by_user_id=persisted_insights.insight_section.created_by_user_id,
        updated_at=persisted_insights.insight_section.updated_at,
        updated_by_user_id=persisted_insights.insight_section.updated_by_user_id,
        deleted_at=None,
        deleted_by_user_id=None,
        user_feedback=None,
        insight_fields=[],
    )
    for persisted_insight_field in persisted_insights.insights:
        expected_meeting_insight.insight_fields.append(
            MeetingInsightField(
                id=persisted_insight_field.id,
                name=persisted_insight_field.insight_name,
                description=not_none(persisted_insight_field.insight_description),
                contact_id=None,
                tags=["tag1", "tag2"],
                field_values=["value1", "value2"],
                detailed_explanation="detailed explanation",
                transcript_offset_locations=[1, 100],
                transcript_range_locations=[
                    TranscriptRange(start=1, end=1),
                    TranscriptRange(start=100, end=100),
                ],
                author_type=InsightAuthorType.SYSTEM,
                created_at=persisted_insight_field.created_at,
                created_by_user_id=persisted_insight_field.created_by_user_id,
                updated_at=persisted_insight_field.updated_at,
                updated_by_user_id=persisted_insight_field.updated_by_user_id,
                deleted_at=None,
                deleted_by_user_id=None,
                user_feedback=None,
                approved_at=None,
                approved_by_user_id=None,
                metadata={},
            )
        )
    assert result_meeting_insight == expected_meeting_insight

    # Update one
    approved_at = zoned_utc_now()
    patch_response = await admin_api_client.api_test_client.client.patch(
        url=f"/api/v1/meetings/insights/{persisted_insights.insights[0].id}",
        headers=headers,
        json=json.loads(
            json.dumps(
                {
                    "field_values": ["value3", "value2"],
                    "user_feedback": "starred",
                    "approved_at": approved_at.isoformat(),
                }
            )
        ),
    )
    assert patch_response.status_code == status.HTTP_200_OK
    updated_insights_data = patch_response.json()
    result_meeting_insight = MeetingInsight.model_validate(updated_insights_data)
    expected_meeting_insight.insight_fields[0].field_values = ["value3", "value2"]
    expected_meeting_insight.insight_fields[
        0
    ].user_feedback = InsightUserFeedback.STARRED
    expected_meeting_insight.insight_fields[
        0
    ].updated_at = result_meeting_insight.insight_fields[0].updated_at
    expected_meeting_insight.insight_fields[0].updated_by_user_id = user_id
    expected_meeting_insight.insight_fields[
        0
    ].author_type = InsightAuthorType.SYSTEM_USER_UPDATED
    expected_meeting_insight.insight_fields[0].approved_at = approved_at
    expected_meeting_insight.insight_fields[0].approved_by_user_id = user_id
    assert result_meeting_insight == expected_meeting_insight

    # Remove one
    id_to_remove = persisted_insights.insights[0].id
    delete_response = await admin_api_client.api_test_client.client.delete(
        url=f"/api/v1/meetings/insights/{id_to_remove}",
        headers=headers,
    )
    assert delete_response.status_code == status.HTTP_200_OK
    delete_insights_data = delete_response.json()
    assert (delete_insights_data["id"]) == str(id_to_remove)
    list_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/meetings/insights/_list",
        headers=headers,
        json={"meeting_id": str(meeting.id)},
    )
    assert list_response.status_code == status.HTTP_200_OK
    insights_data = list_response.json()
    assert (
        len(insights_data["map_data"][ExtractionType.XSCRIPT_SUMMARY_EXTRACTION.value])
        == 1
    )
    result_meeting_insight = MeetingInsight.model_validate(
        insights_data["map_data"][ExtractionType.XSCRIPT_SUMMARY_EXTRACTION.value][0]
    )
    expected_meeting_insight.insight_fields = [
        expected_meeting_insight.insight_fields[1]
    ]
    assert result_meeting_insight == expected_meeting_insight

    # Add custom
    create_request = CreateInsightsFieldRequest(
        meeting_insight_id=expected_meeting_insight.id,
        name="My own thing",
        rank=0,
        field_values=["Thing 1", "Thing 2"],
    )
    create_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/meetings/insights/fields",
        headers=headers,
        json=jsonable_encoder(create_request),
    )
    assert create_response.status_code == status.HTTP_201_CREATED
    insights_data = create_response.json()
    result_meeting_insight = MeetingInsight.model_validate(insights_data)
    expected_meeting_insight.insight_fields.append(
        MeetingInsightField(
            id=result_meeting_insight.insight_fields[-1].id,
            name="My own thing",
            description="",
            contact_id=None,
            tags=[],
            field_values=["Thing 1", "Thing 2"],
            detailed_explanation="",
            transcript_offset_locations=[],
            transcript_range_locations=[],
            author_type=InsightAuthorType.USER,
            created_at=result_meeting_insight.insight_fields[-1].created_at,
            created_by_user_id=user_id,
            updated_at=result_meeting_insight.insight_fields[-1].updated_at,
            updated_by_user_id=user_id,
            deleted_at=None,
            deleted_by_user_id=None,
            user_feedback=None,
            approved_at=result_meeting_insight.insight_fields[-1].approved_at,
            approved_by_user_id=user_id,
            metadata={},
        ),
    )
    assert result_meeting_insight == expected_meeting_insight

    # Remove entire section
    id_to_remove = persisted_insights.insight_section.id
    delete_response = await admin_api_client.api_test_client.client.delete(
        url=f"/api/v1/meetings/insight_sections/{id_to_remove}",
        headers=headers,
    )
    assert delete_response.status_code == status.HTTP_200_OK
    delete_insights_data = delete_response.json()
    assert (delete_insights_data["id"]) == str(id_to_remove)

    # List after deleting all - none should be found now
    list_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/meetings/insights/_list",
        headers=headers,
        json={"meeting_id": str(meeting.id)},
    )
    assert list_response.status_code == status.HTTP_200_OK
    insights_data = list_response.json()
    assert len(insights_data["configured_sections"]) == 0
    assert (
        len(insights_data["map_data"][ExtractionType.XSCRIPT_SUMMARY_EXTRACTION.value])
        == 0
    )


async def test_meeting_bot_status(
    multi_api_client: dict[str, CommonAPIClient],
    api_client: AsyncClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
) -> None:
    admin_api_client = multi_api_client["admin"]
    user_id = admin_api_client.default_user_id
    organization_id = admin_api_client.default_organization_id

    meeting = await create_meeting(
        meeting_repository=meeting_repository,
        organization_id=organization_id,
        user_id=user_id,
        account_repository=account_repo,
    )
    meeting_bot, _ = await create_meeting_bot(
        meeting_repository=meeting_repository,
        organization_id=organization_id,
        meeting_id=meeting.id,
    )

    webhook_headers = {
        "accept": "application/json",
        "Content-Type": "application/json",
    }

    event = RecallBotStatusChangeEvent(
        event=RecallBotEventTypes.BOT_STATUS_CHANGE,
        data=RecallBotEventData(
            bot_id=meeting_bot.external_meeting_bot_id,
            status=RecallBotEventStatus(
                code=RecallStatusCode.JOINING_CALL,
                created_at="2024-04-26T07:54:32.789587+00:00",
                sub_code="sub-code-1",
                message="message-1",
                recording_id=None,
            ),
        ),
    )
    webhook_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/webhook/recallai/bot/event",
        headers=webhook_headers,
        json=event.model_dump(),
    )
    assert webhook_response.status_code == status.HTTP_200_OK

    persisted_record = not_none(
        await meeting_repository._find_unique_by_column_values(
            table_model=MeetingBot,
            external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
        )
    )
    expected_record = meeting_bot.model_copy(
        update={
            "updated_at": persisted_record.updated_at,
            "status": MeetingBotStatus.JOINING,
            "status_history": BotStatusHistory(
                status_history=[
                    DbBotStatusEvent(
                        status=RecallStatusCode.JOINING_CALL,
                        sub_code="sub-code-1",
                        status_at=datetime.datetime.fromisoformat(
                            "2024-04-26T07:54:32.789587+00:00"
                        ),
                    )
                ]
            ),
        }
    )
    assert persisted_record == expected_record

    # Second event
    event.data.status = RecallBotEventStatus(
        code=RecallStatusCode.RECORDING_PERMISSION_ALLOWED,
        created_at="2024-04-27T07:55:32.789587+00:00",
        sub_code="sub-code-2",
        message="message-2",
        recording_id=None,
    )
    webhook_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/webhook/recallai/bot/event",
        headers=webhook_headers,
        json=event.model_dump(),
    )
    assert webhook_response.status_code == status.HTTP_200_OK
    persisted_record = not_none(
        await meeting_repository._find_unique_by_column_values(
            table_model=MeetingBot,
            external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
        )
    )
    expected_record = expected_record.model_copy(
        update={
            "updated_at": persisted_record.updated_at,
        }
    )
    not_none(expected_record.status_history).status_history.append(
        DbBotStatusEvent(
            status=RecallStatusCode.RECORDING_PERMISSION_ALLOWED,
            sub_code="sub-code-2",
            status_at=datetime.datetime.fromisoformat(
                "2024-04-27T07:55:32.789587+00:00"
            ),
        )
    )
    assert persisted_record == expected_record

    # Jump ahead to in call recording - we skip over waiting
    recording_id = str(uuid4())
    event.data.status = RecallBotEventStatus(
        code=RecallStatusCode.IN_CALL_RECORDING,
        created_at="2024-04-27T07:56:32.789587+00:00",
        sub_code="sub-code-2",
        message="message-2",
        recording_id=recording_id,
    )
    webhook_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/webhook/recallai/bot/event",
        headers=webhook_headers,
        json=event.model_dump(),
    )
    assert webhook_response.status_code == status.HTTP_200_OK
    persisted_record = not_none(
        await meeting_repository._find_unique_by_column_values(
            table_model=MeetingBot,
            external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
        )
    )
    expected_record = expected_record.model_copy(
        update={
            "updated_at": persisted_record.updated_at,
            "external_recording_id": recording_id,
            "status": MeetingBotStatus.IN_CALL_RECORDING,
        }
    )
    not_none(expected_record.status_history).status_history.append(
        DbBotStatusEvent(
            status=RecallStatusCode.IN_CALL_RECORDING,
            sub_code="sub-code-2",
            status_at=datetime.datetime.fromisoformat(
                "2024-04-27T07:56:32.789587+00:00"
            ),
        )
    )
    assert persisted_record == expected_record

    # Now get the out of order waiting event
    event.data.status = RecallBotEventStatus(
        code=RecallStatusCode.IN_WAITING_ROOM,
        created_at="2024-04-27T07:57:32.789587+00:00",
        sub_code="sub-code-2",
        message="message-2",
        recording_id=None,
    )
    webhook_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/webhook/recallai/bot/event",
        headers=webhook_headers,
        json=event.model_dump(),
    )
    assert webhook_response.status_code == status.HTTP_200_OK
    persisted_record = not_none(
        await meeting_repository._find_unique_by_column_values(
            table_model=MeetingBot,
            external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
        )
    )
    expected_record = expected_record.model_copy(
        update={
            "updated_at": persisted_record.updated_at,
        }
    )
    not_none(expected_record.status_history).status_history.append(
        DbBotStatusEvent(
            status=RecallStatusCode.IN_WAITING_ROOM,
            sub_code="sub-code-2",
            status_at=datetime.datetime.fromisoformat(
                "2024-04-27T07:57:32.789587+00:00"
            ),
        )
    )
    assert persisted_record == expected_record


async def test_meeting_consent(
    multi_api_client: dict[str, CommonAPIClient],
    fastapi_app: FastAPI,
    api_client: AsyncClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
) -> None:
    admin_api_client = multi_api_client["admin"]
    user_id = admin_api_client.default_user_id
    organization_id = admin_api_client.default_organization_id

    meeting = await create_meeting(
        meeting_repository=meeting_repository,
        organization_id=organization_id,
        user_id=user_id,
        account_repository=account_repo,
    )
    headers = get_headers(organization_id=organization_id, user_id=user_id)
    consent_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/meetings/consent",
        headers=headers,
        json={
            "consent_id": str(meeting.consent_id),
            "is_recording_consent_given": False,
        },
    )
    assert consent_response.status_code == status.HTTP_200_OK
    result = MeetingConsentResponse.model_validate(consent_response.json())
    expected = MeetingConsentResponse(meeting_url=meeting.meeting_url)
    assert result == expected


async def test_live_transcript_session_lifecycle(
    multi_api_client: dict[str, CommonAPIClient],
    api_client: AsyncClient,
    fastapi_app: FastAPI,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    admin_api_client = multi_api_client["admin"]
    user_id = admin_api_client.default_user_id
    organization_id = admin_api_client.default_organization_id

    meeting = await create_meeting(
        meeting_repository=meeting_repository,
        organization_id=organization_id,
        user_id=user_id,
        account_repository=account_repo,
    )

    headers = get_headers(organization_id=organization_id, user_id=user_id)
    create_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/meetings/live_transcript_sessions",
        headers=headers,
        json={"meeting_id": str(meeting.id)},
    )

    # Initial create
    assert create_response.status_code == status.HTTP_201_CREATED
    created_session = LiveTranscriptSession.model_validate(create_response.json())
    expected_session = LiveTranscriptSession(
        id=created_session.id,
        organization_id=organization_id,
        user_id=user_id,
        meeting_id=meeting.id,
        created_at=created_session.created_at,
        updated_at=created_session.updated_at,
        last_connected_at=created_session.last_connected_at,
    )
    assert created_session == expected_session
    assert created_session.created_at == created_session.updated_at
    assert created_session.created_at == created_session.last_connected_at

    # Retry attempt
    recreate_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/meetings/live_transcript_sessions",
        headers=headers,
        json={"meeting_id": str(meeting.id)},
    )
    assert recreate_response.status_code == status.HTTP_200_OK
    recreated_session = LiveTranscriptSession.model_validate(recreate_response.json())
    expected_session = expected_session.model_copy(
        update={
            "last_connected_at": recreated_session.last_connected_at,
            "updated_at": recreated_session.updated_at,
        }
    )
    assert recreated_session == expected_session
    assert recreated_session.last_connected_at == recreated_session.updated_at
    assert recreated_session.last_connected_at > created_session.last_connected_at


async def test_meeting_annotation_lifecycle(
    multi_api_client: dict[str, CommonAPIClient],
    api_client: AsyncClient,
    fastapi_app: FastAPI,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    admin_api_client = multi_api_client["admin"]
    user_id = admin_api_client.default_user_id
    organization_id = admin_api_client.default_organization_id

    meeting = await create_meeting(
        meeting_repository=meeting_repository,
        organization_id=organization_id,
        user_id=user_id,
        account_repository=account_repo,
    )

    # List existing - None yet
    headers = get_headers(organization_id=organization_id, user_id=user_id)
    list_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/meetings/meeting_annotations/_list",
        headers=headers,
        json={"meeting_id": str(meeting.id)},
    )
    assert list_response.status_code == status.HTTP_200_OK
    annotations_data = list_response.json()
    assert len(annotations_data["list_data"]) == 0

    # Create
    create_request = CreateMeetingAnnotationRequest(
        meeting_id=meeting.id,
        annotation_type=MeetingAnnotationType.OBJECTION,
        start_offset=0,
        transcript_text="I need coffee beans though",
    )
    create_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/meetings/meeting_annotations",
        headers=headers,
        json=jsonable_encoder(create_request),
    )
    assert create_response.status_code == status.HTTP_201_CREATED
    result_annotation = MeetingAnnotation.model_validate(create_response.json())
    expected_annotation = MeetingAnnotation(
        id=result_annotation.id,
        organization_id=organization_id,
        annotation_type=MeetingAnnotationType.OBJECTION,
        start_offset=0,
        contact_id=None,
        transcript_text="I need coffee beans though",
        description=None,
        created_by_user_id=user_id,
        created_at=result_annotation.created_at,
        updated_by_user_id=None,
        updated_at=result_annotation.updated_at,
        deleted_by_user_id=None,
        deleted_at=None,
    )
    assert result_annotation == expected_annotation

    # List existing
    list_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/meetings/meeting_annotations/_list",
        headers=headers,
        json={"meeting_id": str(meeting.id)},
    )
    assert list_response.status_code == status.HTTP_200_OK
    annotations_data = list_response.json()
    assert len(annotations_data["list_data"]) == 1
    result_annotation = MeetingAnnotation.model_validate(
        annotations_data["list_data"][0]
    )
    assert result_annotation == expected_annotation

    # Update one
    patch_response = await admin_api_client.api_test_client.client.patch(
        url=f"/api/v1/meetings/meeting_annotations/{result_annotation.id}",
        headers=headers,
        json={"description": "This guy really does"},
    )
    assert patch_response.status_code == status.HTTP_200_OK
    updated_annotation_data = patch_response.json()
    result_annotation = MeetingAnnotation.model_validate(updated_annotation_data)
    expected_annotation.description = "This guy really does"
    expected_annotation.updated_by_user_id = user_id
    expected_annotation.updated_at = result_annotation.updated_at
    assert result_annotation == expected_annotation

    # Remove
    delete_response = await admin_api_client.api_test_client.client.delete(
        url=f"/api/v1/meetings/meeting_annotations/{result_annotation.id}",
        headers=headers,
    )
    assert delete_response.status_code == status.HTTP_200_OK
    delete_annotation_data = delete_response.json()
    assert (delete_annotation_data["id"]) == str(result_annotation.id)
    list_response = await admin_api_client.api_test_client.client.post(
        url="/api/v1/meetings/meeting_annotations/_list",
        headers=headers,
        json={"meeting_id": str(meeting.id)},
    )
    assert list_response.status_code == status.HTTP_200_OK
    annotations_data = list_response.json()
    assert len(annotations_data["list_data"]) == 0


async def create_meeting_insight_section_with_fields(
    insight_repository: InsightRepository,
    meeting_id: UUID,
    organization_id: UUID,
    user_id: UUID,
) -> InsightDTO:
    time_now = zoned_utc_now()
    insight_section = await insight_repository.insert(
        InsightSection(
            id=uuid4(),
            source_type=InsightSourceType.TRANSCRIPTION,
            reference_type=InsightReferenceIdType.MEETING,
            reference_id=meeting_id,
            name=ExtractionType.XSCRIPT_SUMMARY_EXTRACTION,
            insight_type=ExtractionType.XSCRIPT_SUMMARY_EXTRACTION,
            description="test description",
            extraction_section_version=2,
            extraction_section_id=uuid4(),
            created_by_user_id=user_id,
            created_at=time_now,
            updated_by_user_id=user_id,
            updated_at=time_now,
            deleted_by_user_id=None,
            deleted_at=None,
            organization_id=organization_id,
            user_feedback=None,
        )
    )

    insights = []
    field_names = ["1 - First", "2 - Second"]
    for i in range(len(field_names)):
        field_name = field_names[i]
        # Order of insights list is consistent with rank
        rank = i
        insights.append(
            await insight_repository.insert(
                DbInsight(
                    id=uuid4(),
                    insight_section_id=insight_section.id,
                    organization_id=organization_id,
                    reference_id=meeting_id,
                    reference_type=InsightReferenceIdType.MEETING,
                    insight_name=field_name,
                    insight_description=f"{field_name} description",
                    contact_id=None,
                    created_by_user_id=user_id,
                    updated_by_user_id=user_id,
                    deleted_by_user_id=None,
                    source_type=InsightSourceType.TRANSCRIPTION,
                    tags=["tag1", "tag2"],
                    brief_values=["value1", "value2"],
                    detailed_explanation="detailed explanation",
                    transcript_locations=[1, 100],
                    source_locations=[],
                    created_at=time_now,
                    updated_at=time_now,
                    deleted_at=None,
                    author_type=InsightAuthorType.SYSTEM,
                    user_feedback=None,
                    approved_at=None,
                    approved_by_user_id=None,
                    metadata={},
                    rank=rank,
                    version=0,
                )
            )
        )

    # API returns in created_at reversed order
    return InsightDTO(
        insight_section=insight_section,
        insights=list(insights),
    )


async def test_get_meeting_stats(
    multi_api_client: dict[str, CommonAPIClient],
    api_client: AsyncClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    admin_api_client = multi_api_client["admin"]
    user_id = admin_api_client.default_user_id
    organization_id = admin_api_client.default_organization_id

    # Test: should return ResourceNotFound if no meeting record
    headers = get_headers(organization_id=organization_id, user_id=user_id)
    meeting_id = uuid4()
    get_response = await admin_api_client.api_test_client.client.get(
        url=f"/api/v1/meetings/{meeting_id}/stats", headers=headers
    )
    assert get_response.status_code == 404
    assert get_response.json().get("error") == "ResourceNotFoundError"
    assert (
        get_response.json().get("message") == f"No meeting found with id {meeting_id}"
    )

    # Test: should return ResourceNotFound if no meeting stats record
    meeting = await create_meeting(
        meeting_repository=meeting_repository,
        organization_id=organization_id,
        user_id=user_id,
        account_repository=account_repo,
    )
    get_response = await admin_api_client.api_test_client.client.get(
        url=f"/api/v1/meetings/{meeting.id}/stats", headers=headers
    )
    assert get_response.status_code == 404
    assert get_response.json().get("error") == "ResourceNotFoundError"
    assert (
        get_response.json().get("message")
        == f"No meeting stats found for meeting {meeting.id}"
    )

    # Test: Return meeting stats success
    # insert meeting stats and attach to meeting record
    meeting_stats = await meeting_repository.insert(
        MeetingStats(
            id=uuid4(),
            organization_id=organization_id,
            talk_ratios=[
                MeetingAttendeeTalkRatio(
                    user_id=uuid4(), is_organizer=True, total_time=38, ratio=0.34
                ),
                MeetingAttendeeTalkRatio(
                    contact_id=uuid4(), is_organizer=True, total_time=68, ratio=0.66
                ),
            ],
            longest_user_monologue=MeetingAttendeeMonologue(
                user_id=uuid4(), is_organizer=True, total_time=11, start_offset=2
            ),
            longest_customer_story=None,
            interactivity=1.0,
            patience=2.1,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )
    updated_meeting = await meeting_repository.update_by_tenanted_primary_key(
        Meeting,
        organization_id=organization_id,
        primary_key_to_value={"id": meeting.id},
        column_to_update=MeetingUpdate(meeting_stats_id=meeting_stats.id),
    )
    assert updated_meeting is not None

    get_response = await admin_api_client.api_test_client.client.get(
        url=f"/api/v1/meetings/{meeting.id}/stats", headers=headers
    )
    assert get_response.status_code == 200
    data = get_response.json()
    meeting_stats_dict = json.loads(meeting_stats.model_dump_json())
    assert data["talk_ratios"] == meeting_stats_dict["talk_ratios"]
    assert (
        data["longest_user_monologue"] == meeting_stats_dict["longest_user_monologue"]
    )
    assert data["longest_customer_story"] is None
    assert data["interactivity"] == 1.0
    assert data["patience"] == 2.1
    assert data["organization_id"] == str(organization_id)


@pytest.fixture
def meeting_repository(_engine: DatabaseEngine) -> MeetingRepository:
    return MeetingRepository(engine=_engine)


@pytest.fixture
def insight_repository(_engine: DatabaseEngine) -> InsightRepository:
    return InsightRepository(engine=_engine)


async def test_meeting_clips(
    authed_api_test_client: APITestClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    user_id = authed_api_test_client.default_user_id
    organization_id = authed_api_test_client.default_organization_id
    meeting = await create_meeting(
        meeting_repository=meeting_repository,
        organization_id=organization_id,
        user_id=user_id,
        account_repository=account_repo,
    )

    # List existing - None yet
    list_response = await authed_api_test_client.post(
        endpoint_name="list_meeting_clips",
        path_params={"meeting_id": meeting.id},
        request_body=ListMeetingClipRequest(
            include_archived=False,
        ),
        response_type=PaginatedListResponse[MeetingClipResponse],
    )
    assert len(list_response.list_data) == 0

    # Create clip
    create_request = CreateMeetingClipRequest(
        name="Test Clip",
        start_offset=10,
        end_offset=20,
    )
    result_clip = await authed_api_test_client.post(
        endpoint_name="create_meeting_clip",
        path_params={"meeting_id": meeting.id},
        request_body=create_request,
        response_type=MeetingClipResponse,
    )
    expected_clip = MeetingClipResponse(
        id=result_clip.id,
        name="Test Clip",
        meeting_id=meeting.id,
        organization_id=organization_id,
        start_offset=10,
        end_offset=20,
        created_by_user_id=user_id,
        updated_by_user_id=user_id,
        deleted_by_user_id=None,
        created_at=result_clip.created_at,
        updated_at=result_clip.updated_at,
        deleted_at=None,
    )
    assert result_clip == expected_clip

    # List existing clips
    list_response = await authed_api_test_client.post(
        endpoint_name="list_meeting_clips",
        path_params={"meeting_id": meeting.id},
        request_body=ListMeetingClipRequest(
            include_archived=False,
        ),
        response_type=PaginatedListResponse[MeetingClipResponse],
    )
    assert len(list_response.list_data) == 1
    assert list_response.list_data[0] == expected_clip

    # Get specific clip
    result_clip = await authed_api_test_client.get(
        endpoint_name="get_meeting_clip",
        path_params={"meeting_id": meeting.id, "clip_id": result_clip.id},
        response_type=MeetingClipResponse,
    )
    assert result_clip == expected_clip

    # Update clip
    patch_request = PatchMeetingClipRequest(
        name="Updated Clip Name",
        start_offset=15,
        end_offset=25,
    )
    result_clip = await authed_api_test_client.patch(
        endpoint_name="patch_meeting_clip",
        path_params={"meeting_id": meeting.id, "clip_id": result_clip.id},
        request_body=patch_request,
        response_type=MeetingClipResponse,
    )
    expected_clip = expected_clip.model_copy(
        update={
            "name": "Updated Clip Name",
            "start_offset": 15,
            "end_offset": 25,
            "updated_by_user_id": user_id,
            "updated_at": result_clip.updated_at,
        }
    )
    assert result_clip == expected_clip

    # Test invalid update (start >= end)
    invalid_patch_request = PatchMeetingClipRequest(
        name="Updated Clip Name",
        start_offset=30,
        end_offset=25,
    )
    with pytest.raises(APITestError) as e:
        await authed_api_test_client.patch(
            endpoint_name="patch_meeting_clip",
            path_params={"meeting_id": meeting.id, "clip_id": result_clip.id},
            request_body=invalid_patch_request,
            response_type=MeetingClipResponse,
        )
    assert e.value.status == HTTPStatus.BAD_REQUEST
    assert e.value.extra.get("error") == InvalidArgumentError.__name__

    # Delete clip
    delete_response = await authed_api_test_client.delete(
        endpoint_name="delete_meeting_clip",
        path_params={"meeting_id": meeting.id, "clip_id": result_clip.id},
        response_type=DeleteEntityResponse,
    )
    assert str(delete_response.id) == str(result_clip.id)

    # Test not found scenarios
    not_found_id = uuid4()
    with pytest.raises(APITestError) as e:
        await authed_api_test_client.get(
            endpoint_name="get_meeting_clip",
            path_params={"meeting_id": meeting.id, "clip_id": not_found_id},
            response_type=MeetingClipResponse,
        )
    assert e.value.status == HTTPStatus.NOT_FOUND
    assert e.value.extra.get("error") == ResourceNotFoundError.__name__


async def create_test_meeting(
    meeting_repository: MeetingRepository,
    organization_id: UUID,
    user_id: UUID,
    account_repo: AccountRepository,
    recording: bool = True,
) -> Meeting:
    # Create a test meeting first
    meeting = await create_meeting(
        meeting_repository=meeting_repository,
        organization_id=organization_id,
        user_id=user_id,
        account_repository=account_repo,
    )
    if not recording:
        return meeting
    # Create a meeting bot and set its status to recording
    meeting_bot, _ = await create_meeting_bot(
        meeting_repository=meeting_repository,
        organization_id=organization_id,
        meeting_id=meeting.id,
    )

    # Update bot status to recording using model_copy
    updated_bot = meeting_bot.model_copy(
        update={
            "status": MeetingBotStatus.IN_CALL_RECORDING,
            "status_history": BotStatusHistory(
                status_history=[
                    DbBotStatusEvent(
                        status="in_call_recording",
                        sub_code="",
                        status_at=zoned_utc_now(),
                    )
                ]
            ),
        }
    )
    await meeting_repository.update_instance(updated_bot)
    return meeting


@pytest.mark.parametrize(
    "authorized_emails",
    [
        ["<EMAIL>"],
        ["<EMAIL>"],
        ["<EMAIL>", "<EMAIL>"],
    ],
)
async def test_create_meeting_share(
    authed_api_test_client: APITestClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
    authorized_emails: list[EmailStr],
) -> None:
    # Arrange
    user_id = authed_api_test_client.default_user_id
    organization_id = authed_api_test_client.default_organization_id
    lowered_authorized_emails = [str(email).lower() for email in authorized_emails]
    # Create a test meeting first
    meeting = await create_test_meeting(
        meeting_repository=meeting_repository,
        organization_id=organization_id,
        user_id=user_id,
        account_repo=account_repo,
    )

    # Test successful creation
    create_request = CreateMeetingShareRequest(
        meeting_id=meeting.id,
        access_type=MeetingShareAccessType.PRIVATE_WITH_VERIFY,
        authorized_emails=authorized_emails,
        start_offset=0,
        end_offset=100,
        include_ai_content=True,
        expires_at=zoned_utc_now() + datetime.timedelta(days=1),
    )

    result_share = await authed_api_test_client.post(
        endpoint_name="create_meeting_share",
        request_body=create_request,
        response_type=MeetingShareResponse,
    )

    # Assert
    assert result_share.meeting_id == meeting.id
    assert result_share.access_type == create_request.access_type
    assert result_share.authorized_emails == lowered_authorized_emails
    assert result_share.start_offset == create_request.start_offset
    assert result_share.end_offset == create_request.end_offset
    assert result_share.include_ai_content == create_request.include_ai_content

    # Test invalid meeting id
    invalid_meeting_id = uuid4()
    invalid_request = create_request.model_copy(
        update={"meeting_id": invalid_meeting_id}
    )
    with pytest.raises(APITestError) as e:
        await authed_api_test_client.post(
            endpoint_name="create_meeting_share",
            request_body=invalid_request,
            response_type=MeetingShareResponse,
        )
    assert e.value.status == HTTPStatus.NOT_FOUND
    assert e.value.extra.get("error") == ResourceNotFoundError.__name__

    # Test invalid start/end offset
    invalid_request = create_request.model_copy(
        update={"start_offset": 100, "end_offset": 0}
    )
    with pytest.raises(APITestError) as e:
        await authed_api_test_client.post(
            endpoint_name="create_meeting_share",
            request_body=invalid_request,
            response_type=MeetingShareResponse,
        )
    assert e.value.status == HTTPStatus.UNPROCESSABLE_ENTITY
    assert "Start offset must be less than end offset" in str(
        e.value.extra.get("detail", [])
    )

    # Test negative offset
    invalid_request = create_request.model_copy(
        update={"start_offset": -1, "end_offset": 100}
    )
    with pytest.raises(APITestError) as e:
        await authed_api_test_client.post(
            endpoint_name="create_meeting_share",
            request_body=invalid_request,
            response_type=MeetingShareResponse,
        )
    assert e.value.status == HTTPStatus.UNPROCESSABLE_ENTITY
    assert "Start and end offsets must be non-negative" in str(
        e.value.extra.get("detail", [])
    )

    # Test expired date
    invalid_request = create_request.model_copy(
        update={"expires_at": zoned_utc_now() - datetime.timedelta(days=1)}
    )
    with pytest.raises(APITestError) as e:
        await authed_api_test_client.post(
            endpoint_name="create_meeting_share",
            request_body=invalid_request,
            response_type=MeetingShareResponse,
        )
    assert e.value.status == HTTPStatus.UNPROCESSABLE_ENTITY
    assert "Expires at must be in the future" in str(e.value.extra.get("detail", []))

    # Test max expiration time
    invalid_request = create_request.model_copy(
        update={"expires_at": zoned_utc_now() + datetime.timedelta(days=181)}
    )
    with pytest.raises(APITestError) as e:
        await authed_api_test_client.post(
            endpoint_name="create_meeting_share",
            request_body=invalid_request,
            response_type=MeetingShareResponse,
        )
    assert e.value.status == HTTPStatus.UNPROCESSABLE_ENTITY
    assert "Expires at must be within 180 days" in str(e.value.extra.get("detail", []))

    # Test private share without authorized emails
    invalid_request = create_request.model_copy(update={"authorized_emails": None})
    with pytest.raises(APITestError) as e:
        await authed_api_test_client.post(
            endpoint_name="create_meeting_share",
            request_body=invalid_request,
            response_type=MeetingShareResponse,
        )
    assert e.value.status == HTTPStatus.UNPROCESSABLE_ENTITY
    assert "Authorized emails are required for private shares with verification" in str(
        e.value.extra.get("detail", [])
    )


async def test_create_meeting_share_without_recording(
    authed_api_test_client: APITestClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    user_id = authed_api_test_client.default_user_id
    organization_id = authed_api_test_client.default_organization_id
    meeting = await create_test_meeting(
        meeting_repository=meeting_repository,
        organization_id=organization_id,
        user_id=user_id,
        account_repo=account_repo,
        recording=False,
    )
    create_request = CreateMeetingShareRequest(
        meeting_id=meeting.id,
        access_type=MeetingShareAccessType.PRIVATE_WITH_VERIFY,
        authorized_emails=["<EMAIL>"],
        start_offset=0,
        end_offset=100,
        include_ai_content=True,
        expires_at=zoned_utc_now() + datetime.timedelta(days=1),
    )
    with pytest.raises(APITestError) as e:
        await authed_api_test_client.post(
            endpoint_name="create_meeting_share",
            request_body=create_request,
            response_type=MeetingShareResponse,
        )
    assert e.value.status == HTTPStatus.INTERNAL_SERVER_ERROR
    assert e.value.extra.get("error") == IllegalStateError.__name__
    assert e.value.extra.get("message") == "Meeting is not recorded"


async def test_meeting_share_lifecycle(
    authed_api_test_client: APITestClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    # Arrange
    user_id = authed_api_test_client.default_user_id
    organization_id = authed_api_test_client.default_organization_id

    # Create a test meeting first
    meeting = await create_test_meeting(
        meeting_repository=meeting_repository,
        organization_id=organization_id,
        user_id=user_id,
        account_repo=account_repo,
    )

    # Create a meeting share
    create_request = CreateMeetingShareRequest(
        meeting_id=meeting.id,
        access_type=MeetingShareAccessType.PRIVATE_WITH_VERIFY,
        authorized_emails=["<EMAIL>"],
        start_offset=0,
        end_offset=100,
        include_ai_content=True,
        expires_at=zoned_utc_now() + datetime.timedelta(days=1),
    )
    created_share = await authed_api_test_client.post(
        endpoint_name="create_meeting_share",
        request_body=create_request,
        response_type=MeetingShareResponse,
    )

    # List meeting shares with pagination and sorting
    list_response = await authed_api_test_client.post(
        endpoint_name="list_meeting_shares",
        request_body=ListMeetingShareRequest(
            meeting_id=meeting.id,
        ),
        response_type=PaginatedListResponse[MeetingShareResponse],
    )
    assert any(share.id == created_share.id for share in list_response.list_data)

    # Get meeting share by ID
    get_response = await authed_api_test_client.get(
        endpoint_name="get_meeting_share_by_id",
        path_params={"share_id": created_share.id},
        response_type=MeetingShareResponse,
    )
    assert get_response.id == created_share.id

    # Update meeting share
    update_request = PatchMeetingShareRequest(
        start_offset=None,
        end_offset=None,
        authorized_emails=UNSET,
        include_ai_content=UNSET,
        expires_at=UNSET,
    )
    updated_share = await authed_api_test_client.patch(
        endpoint_name="update_meeting_share",
        path_params={"share_id": created_share.id},
        request_body=update_request,
        response_type=MeetingShareResponse,
    )

    # Verify offsets are cleared
    assert updated_share.start_offset is None
    assert updated_share.end_offset is None
    assert updated_share.authorized_emails == created_share.authorized_emails
    assert updated_share.include_ai_content == created_share.include_ai_content

    # Delete meeting share
    delete_response = await authed_api_test_client.delete(
        endpoint_name="delete_meeting_share",
        path_params={"share_id": created_share.id},
        response_type=DeleteEntityResponse,
    )
    assert delete_response.id == created_share.id

    # Test case 1: Create share with optional fields omitted
    create_request = CreateMeetingShareRequest(
        meeting_id=meeting.id,
        access_type=MeetingShareAccessType.PRIVATE_WITH_VERIFY,
        authorized_emails=["<EMAIL>"],
        include_ai_content=True,
        expires_at=zoned_utc_now() + datetime.timedelta(days=1),
    )

    created_share = await authed_api_test_client.post(
        endpoint_name="create_meeting_share",
        request_body=create_request,
        response_type=MeetingShareResponse,
    )

    # Verify optional fields are None
    assert created_share.start_offset is None
    assert created_share.end_offset is None
    assert created_share.authorized_emails == ["<EMAIL>"]
    assert created_share.include_ai_content is True

    # Test case 2: Create share with optional fields provided
    create_request_with_offsets = CreateMeetingShareRequest(
        meeting_id=meeting.id,
        access_type=MeetingShareAccessType.PRIVATE_WITH_VERIFY,
        start_offset=0,
        end_offset=100,
        authorized_emails=["<EMAIL>"],
        include_ai_content=True,
        expires_at=zoned_utc_now() + datetime.timedelta(days=1),
    )

    created_share_with_offsets = await authed_api_test_client.post(
        endpoint_name="create_meeting_share",
        request_body=create_request_with_offsets,
        response_type=MeetingShareResponse,
    )

    # Verify optional fields are set
    assert created_share_with_offsets.start_offset == 0
    assert created_share_with_offsets.end_offset == 100
    assert created_share_with_offsets.authorized_emails == ["<EMAIL>"]
    assert created_share_with_offsets.include_ai_content is True
    assert created_share_with_offsets.expires_at is not None

    # Test case: Update share to clear offsets
    update_request = PatchMeetingShareRequest(
        start_offset=None,
        end_offset=None,
        authorized_emails=UNSET,
        include_ai_content=UNSET,
        expires_at=UNSET,
    )
    updated_share = await authed_api_test_client.patch(
        endpoint_name="update_meeting_share",
        path_params={"share_id": created_share_with_offsets.id},
        request_body=update_request,
        response_type=MeetingShareResponse,
    )

    # Verify offsets are cleared
    assert updated_share.start_offset is None
    assert updated_share.end_offset is None
    assert (
        updated_share.authorized_emails == created_share_with_offsets.authorized_emails
    )
    assert (
        updated_share.include_ai_content
        == created_share_with_offsets.include_ai_content
    )


async def test_meeting_share_info_errors(
    authed_api_test_client: APITestClient,
    meeting_repository: MeetingRepository,
) -> None:
    expired_meeting_share = await meeting_repository.insert(
        MeetingShare(
            id=uuid4(),
            meeting_id=uuid4(),
            organization_id=authed_api_test_client.default_organization_id,
            access_type=MeetingShareAccessType.PRIVATE_WITH_VERIFY,
            authorized_emails=["<EMAIL>"],
            start_offset=None,
            end_offset=None,
            include_ai_content=True,
            expires_at=zoned_utc_now() + timedelta(days=-1),
            created_by_user_id=authed_api_test_client.default_user_id,
            updated_by_user_id=authed_api_test_client.default_user_id,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )
    with pytest.raises(APITestError) as e:
        await authed_api_test_client.get(
            endpoint_name="get_meeting_info_for_share",
            path_params={"share_id": expired_meeting_share.id},
            query_params="code=<EMAIL>",
            response_type=MeetingShareMeetingInfoResponse,
        )

    assert e.value.status == HTTPStatus.BAD_REQUEST
    assert e.value.extra.get("error") == InvalidArgumentError.__name__
    assert e.value.extra.get("message") == "The share has expired."

    # Delete the share
    await meeting_repository._update_unique_by_column_values(
        MeetingShare,
        column_value_to_query={"id": expired_meeting_share.id},
        column_to_update={"deleted_at": zoned_utc_now()},
    )

    with pytest.raises(APITestError) as e:
        await authed_api_test_client.get(
            endpoint_name="get_meeting_info_for_share",
            path_params={"share_id": expired_meeting_share.id},
            query_params="code=<EMAIL>",
            response_type=MeetingShareMeetingInfoResponse,
        )

    assert e.value.status == HTTPStatus.BAD_REQUEST
    assert e.value.extra.get("error") == InvalidArgumentError.__name__
    assert e.value.extra.get("message") == "The share has been deleted."

    with pytest.raises(APITestError) as e:
        await authed_api_test_client.get(
            endpoint_name="get_meeting_info_for_share",
            path_params={"share_id": expired_meeting_share.id},
            query_params="code=<EMAIL>",
            response_type=MeetingShareMeetingInfoResponse,
        )

    assert e.value.status == HTTPStatus.BAD_REQUEST
    assert e.value.extra.get("error") == InvalidArgumentError.__name__
    assert e.value.extra.get("message") == "Email is not authorized"


async def create_test_tracker_stats_by_meeting_id(
    tracker_repository: TrackerRepository,
    meeting_id: UUID,
    organization_id: UUID,
) -> tuple[Tracker, list[TrackerStats]]:
    """Create test tracker and tracker stats data for testing."""
    # Create a tracker first
    tracker = await tracker_repository.create_tracker(
        Tracker(
            id=uuid4(),
            organization_id=organization_id,
            name="Test Tracker",
            description="Test Description",
            phrases=["test phrase 1", "test phrase 2"],
            scope=[TrackerScope.MEETINGS],
            include_related_words=True,
            filter=MeetingTrackerFilter(track_user_type=TrackUserType.ANYONE),
            created_at=zoned_utc_now(),
            created_by_user_id=uuid4(),
            updated_at=zoned_utc_now(),
            updated_by_user_id=uuid4(),
            deleted_at=None,
            deleted_by_user_id=None,
        )
    )

    # Create tracker stats list
    tracker_stats_list = []

    # Create first tracker stats with two transcript sentences
    tracker_stats1 = await tracker_repository.create_tracker_stats(
        TrackerStats(
            id=uuid4(),
            organization_id=organization_id,
            tracker_id=tracker.id,
            reference_id_type=TrackerReferenceIdType.MEETING,
            reference_id=meeting_id,
            matched_phrase="test phrase 1",
            tracked_metadata=MeetingTrackedMetadata(
                scope=TrackerScope.MEETINGS,
                transcript_sentences=[
                    MeetingTrackedTranscriptSentence(
                        speaker="Speaker 1",
                        transcript_sentence="First match",
                        start_timestamp=0,
                        end_timestamp=1000,
                        related_phrases=["related 1"],
                        tracked_user_id=uuid4(),
                        tracked_contact_id=None,
                    ),
                    MeetingTrackedTranscriptSentence(
                        speaker="Speaker 2",
                        transcript_sentence="Second match",
                        start_timestamp=1000,
                        end_timestamp=2000,
                        related_phrases=["related 2"],
                        tracked_user_id=uuid4(),
                        tracked_contact_id=None,
                    ),
                ],
            ),
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )
    tracker_stats_list.append(tracker_stats1)

    # Create second tracker stats with different phrase
    tracker_stats2 = await tracker_repository.create_tracker_stats(
        TrackerStats(
            id=uuid4(),
            organization_id=organization_id,
            tracker_id=tracker.id,
            reference_id_type=TrackerReferenceIdType.MEETING,
            reference_id=meeting_id,
            matched_phrase="test phrase 2",  # Different phrase
            tracked_metadata=MeetingTrackedMetadata(
                scope=TrackerScope.MEETINGS,
                transcript_sentences=[
                    MeetingTrackedTranscriptSentence(
                        speaker="Speaker 3",
                        transcript_sentence="Third match",
                        start_timestamp=2000,
                        end_timestamp=3000,
                        related_phrases=["related 3"],
                        tracked_user_id=uuid4(),
                        tracked_contact_id=None,
                    ),
                ],
            ),
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )
    tracker_stats_list.append(tracker_stats2)

    return tracker, tracker_stats_list


async def test_get_tracker_stats_by_meeting_id(
    fastapi_app: FastAPI,
    authed_api_test_client: APITestClient,
    meeting_repository: MeetingRepository,
    tracker_repository: TrackerRepository,
    account_repo: AccountRepository,
) -> None:
    """Test get_tracker_stats_by_meeting_id endpoint."""
    # Create test user and organization
    user_id = authed_api_test_client.default_user_id
    organization_id = authed_api_test_client.default_organization_id

    # Create test meeting
    meeting = await create_meeting(
        meeting_repository=meeting_repository,
        organization_id=organization_id,
        user_id=user_id,
        account_repository=account_repo,
    )

    # Create test tracker and tracker stats
    tracker, tracker_stats_list = await create_test_tracker_stats_by_meeting_id(
        tracker_repository=tracker_repository,
        meeting_id=meeting.id,
        organization_id=organization_id,
    )

    # Test list tracker stats
    list_response = await authed_api_test_client.post(
        endpoint_name="get_tracker_stats_by_meeting_id",
        path_params={"meeting_id": meeting.id},
        request_body=ListMeetingTrackerStatsRequest(),
        response_type=PaginatedListResponse[TrackerStatsDTO],
    )

    # Verify response
    assert list_response.list_data is not None
    assert len(list_response.list_data) == len(tracker_stats_list)

    # Verify each tracker stat
    for tracker_stat_dto in list_response.list_data:
        # Find corresponding test data
        original_stat = next(
            (
                stat
                for stat in tracker_stats_list
                if stat.matched_phrase == tracker_stat_dto.matched_phrase
            ),
            None,
        )
        assert original_stat is not None

        # Verify DTO fields
        assert tracker_stat_dto.tracker_name == tracker.name
        assert tracker_stat_dto.matched_phrase == original_stat.matched_phrase

        assert isinstance(original_stat.tracked_metadata, MeetingTrackedMetadata), (
            "Expected MeetingTrackedMetadata"
        )
        # Verify matched count based on number of transcript sentences
        expected_count = len(original_stat.tracked_metadata.transcript_sentences)
        assert tracker_stat_dto.matched_count == expected_count

    # Verify ordering (by matched_count DESC)
    assert (
        list_response.list_data[0].matched_count
        >= list_response.list_data[1].matched_count
    )
