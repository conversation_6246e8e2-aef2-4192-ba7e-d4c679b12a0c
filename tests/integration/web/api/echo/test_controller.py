import os
from collections.abc import AsyncGenerator
from unittest.mock import patch

import pytest
from asgi_lifespan import LifespanManager
from httpx import ASGITransport, AsyncClient


@pytest.fixture()
async def client_all_app() -> AsyncGenerator[AsyncClient, None]:
    os.environ["SALESTECH_BE_SENTRY_DSN"] = (
        "https://<EMAIL>/1"
    )
    os.environ["SALESTECH_BE_SENTRY_TRACES_SAMPLE_RATE"] = "1.0"

    # Import only after env created
    from salestech_be.web.application import get_app

    fastapi_app = get_app()
    async with (
        LifespanManager(
            fastapi_app, startup_timeout=120, shutdown_timeout=60
        ) as manager,
        AsyncClient(
            transport=ASGITransport(app=manager.app),
            base_url="http://testserver",
        ) as client,
    ):
        yield client

    # Make sure you reset the env after the test
    del os.environ["SALESTECH_BE_SENTRY_DSN"]
    del os.environ["SALESTECH_BE_SENTRY_TRACES_SAMPLE_RATE"]


async def test_sentry_behavior(
    client_all_app: AsyncClient,
) -> None:
    # Loguru error level should be captured by sentry
    with patch(
        "salestech_be.integrations.sentry.transport.DummyTransport.capture_envelope"
    ) as mock_capture_envelope:
        get_response = await client_all_app.get(
            "/api/echo/test",
            headers={
                "accept": "application/json",
                "Content-Type": "application/json",
            },
        )
        assert get_response.status_code == 200

        # Ensure that Sentry capture was called
        assert mock_capture_envelope.called
        assert mock_capture_envelope.call_count == 2

        #
        # Ensure that the correct message is captured, by unpacking the call args
        #

        # actual error
        assert (
            mock_capture_envelope.call_args_list[0][0][0].items[0].payload.json["level"]
            == "error"
        )
        assert (
            mock_capture_envelope.call_args_list[0][0][0]
            .items[0]
            .payload.json["logentry"]["message"]
            .find("test loguru error level")
        )

        # actual transaction
        assert (
            mock_capture_envelope.call_args_list[1][0][0].items[0].payload.json["type"]
            == "transaction"
        )
