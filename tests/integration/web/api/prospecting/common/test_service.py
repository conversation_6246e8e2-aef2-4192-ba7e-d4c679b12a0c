import uuid
from unittest.mock import patch

import pytest

from salestech_be.common.query_util.filter_schema import (
    CompositeFilter,
    FilterSpec,
    ValueFilter,
)
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.schema import <PERSON>Field
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.prospecting.type.company_type_v2 import ProspectingCompany
from salestech_be.core.prospecting.type.person_type_v2 import ProspectingPerson
from salestech_be.core.quota.type.quota_policy_type import QuotaPolicy
from salestech_be.db.dao.company_repository import CompanyRepository
from salestech_be.db.dao.pdl_company_repository import PDLCompanyRepository
from salestech_be.db.dao.pdl_person_repository import PDLPersonRepository
from salestech_be.db.dao.person_repository import PersonRepository
from salestech_be.db.dao.quota_repository import QuotaUsageRepository
from salestech_be.db.dto.person_dto import PersonDto
from salestech_be.db.dto.prospecting_dto import (
    EMAIL_ENRICH_CREDITS_PER_ENRICHMENT,
    MOBILE_ENRICH_CREDITS_PER_ENRICHMENT,
    PDL_SEARCH_CREDITS_PER_RECORD,
)
from salestech_be.db.models.company import Company
from salestech_be.db.models.pdl_company import PDLCompany
from salestech_be.db.models.pdl_person import PDLPerson
from salestech_be.db.models.person import (
    Person,
    ProspectingEnrichStatus,
)
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
    QuotaPeriod,
)
from salestech_be.db.models.search_query import ProspectingSearchQueryType
from salestech_be.integrations.pdl.model import (
    PeopleDataLabsBulkEnrichResponse,
    PeopleDataLabsPersonResponse,
    PeopleDataLabsPreviewSearchPeopleResponse,
    PeopleDataLabsSearchCompanyResponse,
)
from salestech_be.util.asyncio_util.adapter import run_in_pool
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.common.container import (
    ListEntityRequestV2,
)
from salestech_be.web.api.prospecting.common.prospecting_common_service import (
    ProspectingCommonService,
)
from salestech_be.web.api.prospecting.company.schema import BulkImportCompanyRequest
from salestech_be.web.api.prospecting.people.schema import (
    BulkImportPeopleRequest,
)
from tests.integration.web.api.util.api_client import APITestClient
from tests.test_util import read_response_file, read_response_file_list


@pytest.mark.parametrize(
    "prospecting_quota_policy",
    [
        [
            [
                QuotaConsumerEntityType.ORGANIZATION,
                QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH,
                QuotaPeriod.MONTHLY,
                1000,
            ],
            [
                QuotaConsumerEntityType.ORGANIZATION,
                QuotaConsumingResource.PROSPECTING_ENRICHMENT,
                QuotaPeriod.MONTHLY,
                100,
            ],
        ]
    ],
    indirect=True,
)
async def test_bulk_import_people_with_existing_people(
    authed_api_test_client: APITestClient,
    prospecting_common_service: ProspectingCommonService,
    person_repository: PersonRepository,
    prospecting_quota_policy: list[QuotaPolicy],
) -> None:
    """
    Test bulk_import_people with existing people in the database (no search needed).
    Focus on quota credit calculation.
    """
    # Get organization_id and user_id from authed_api_test_client
    organization_id = authed_api_test_client.default_organization_id
    user_id = authed_api_test_client.default_user_id

    # Create existing people in the database that need enrichment
    existing_people = []
    for i in range(3):
        person = Person(
            id=uuid.uuid4(),
            organization_id=organization_id,
            user_id=user_id,
            full_name=f"Test Person {i}",
            current_company="Test Company",
            job_title="Test Title",
            work_email_enrich_status=ProspectingEnrichStatus.NOT_REQUESTED,
            phone_number_enrich_status=ProspectingEnrichStatus.NOT_REQUESTED,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
        await person_repository.insert(person)
        existing_people.append(person)

    # Create a request for bulk_import_people with only existing people
    request = BulkImportPeopleRequest(
        person_ids=[person.id for person in existing_people],
        total_number=len(existing_people),
        enrich_phone_numbers=True,
    )

    # Execute the bulk_import_people method
    prospecting_run = await prospecting_common_service.bulk_import_people(
        user_id=user_id,
        organization_id=organization_id,
        bulk_import_people_request=request,
    )

    # Verify results
    assert prospecting_run is not None
    assert prospecting_run.organization_id == organization_id
    assert prospecting_run.user_id == user_id

    # Verify that the estimated credits are calculated correctly
    assert prospecting_run.estimated_credits is not None
    assert len(prospecting_run.estimated_credits) == 2  # One for email, one for phone

    # Calculate expected credits
    expected_email_credits = EMAIL_ENRICH_CREDITS_PER_ENRICHMENT * len(existing_people)
    expected_phone_credits = MOBILE_ENRICH_CREDITS_PER_ENRICHMENT * len(existing_people)

    # Check that the estimated credits match what we expect
    assert prospecting_run.estimated_credits is not None
    email_credit = next(
        (
            c
            for c in prospecting_run.estimated_credits
            if c.resource == QuotaConsumingResource.PROSPECTING_EMAIL_ENRICHMENT
        ),
        None,
    )
    phone_credit = next(
        (
            c
            for c in prospecting_run.estimated_credits
            if c.resource == QuotaConsumingResource.PROSPECTING_PHONE_NUMBER_ENRICHMENT
        ),
        None,
    )

    assert email_credit is not None
    assert phone_credit is not None
    assert email_credit.credit == expected_email_credits
    assert phone_credit.credit == expected_phone_credits


@pytest.mark.parametrize(
    "prospecting_quota_policy",
    [
        [
            [
                QuotaConsumerEntityType.ORGANIZATION,
                QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH,
                QuotaPeriod.MONTHLY,
                1000,
            ],
            [
                QuotaConsumerEntityType.ORGANIZATION,
                QuotaConsumingResource.PROSPECTING_ENRICHMENT,
                QuotaPeriod.MONTHLY,
                100,
            ],
        ]
    ],
    indirect=True,
)
async def test_enrich_people_by_ids_with_single_person(
    authed_api_test_client: APITestClient,
    prospecting_common_service: ProspectingCommonService,
    person_repository: PersonRepository,
    pdl_person_repository: PDLPersonRepository,
    prospecting_quota_policy: list[QuotaPolicy],
) -> None:
    """
    Test enrich_people_by_ids with a single person requiring enrichment.
    Focus on quota credit calculation and enrichment status.
    """
    # Get organization_id and user_id from authed_api_test_client
    organization_id = authed_api_test_client.default_organization_id
    user_id = authed_api_test_client.default_user_id

    # Create one test person that needs enrichment
    person = Person(
        id=uuid.uuid4(),
        organization_id=organization_id,
        user_id=user_id,
        full_name="Santhosh Ayanikkat",
        current_company="Cognizant",
        job_title="Senior Project Manager",
        work_email_enrich_status=ProspectingEnrichStatus.NOT_REQUESTED,
        phone_number_enrich_status=ProspectingEnrichStatus.NOT_REQUESTED,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await person_repository.insert(person)

    # Create PDL Person record with the exact ID from the JSON response
    pdl_person = PDLPerson(
        id=uuid.uuid4(),
        organization_id=organization_id,
        person_id=person.id,
        ext_id="NekJsRebP-0rM6A3BOlDlw_0000",  # This is the ID from the JSON response
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
        data={"k": "v"},
    )
    await pdl_person_repository.insert(pdl_person)

    # Load mock PDL search response
    json_data = await run_in_pool(
        read_response_file,
        file_name="integration/integrations/pdl/data/pdl_enrich_person_response.json",
    )

    # Enrich the single person
    with (
        patch(
            "salestech_be.integrations.pdl.pdl_client.PdlClient.enrich_person"
        ) as mock_enrich_person,
        patch.object(
            prospecting_common_service,
            "_process_people_workflow",
            return_value=None,
        ) as mock_process_workflow,
    ):
        # Configure mock to return a fixed PDL response
        mock_enrich_person.return_value = PeopleDataLabsPersonResponse.model_validate(
            json_data
        )

        # Call enrich_people_by_ids with the single person
        enriched_people = await prospecting_common_service.enrich_people_by_ids(
            person_ids=[person.id],
            user_id=user_id,
            organization_id=organization_id,
            enrich_phone_numbers=True,
        )

        # Verify search_people was called with the correct SQL query containing the PDL ext_id
        mock_enrich_person.assert_called_once()

        # Verify _process_people_workflow was called
        mock_process_workflow.assert_called_once()

        # Check the result - should have one enriched person
        assert len(enriched_people) == 1

        # Get the enriched person
        enriched_person = enriched_people[0].db_person

        # Verify email was enriched with the correct value from the PDL response
        assert enriched_person.work_email is not None
        assert enriched_person.work_email == "<EMAIL>"
        assert (
            enriched_person.work_email_enrich_status == ProspectingEnrichStatus.ENRICHED
        )

        # Verify phone was enriched with the correct value from the PDL response
        assert enriched_person.phone_numbers is not None
        assert len(enriched_person.phone_numbers) > 0
        assert enriched_person.phone_numbers[0].number == "+18609672854"
        assert (
            enriched_person.phone_number_enrich_status
            == ProspectingEnrichStatus.ENRICHED
        )


@pytest.mark.parametrize(
    "prospecting_quota_policy",
    [
        [
            [
                QuotaConsumerEntityType.ORGANIZATION,
                QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH,
                QuotaPeriod.MONTHLY,
                1000,
            ],
            [
                QuotaConsumerEntityType.ORGANIZATION,
                QuotaConsumingResource.PROSPECTING_ENRICHMENT,
                QuotaPeriod.MONTHLY,
                100,
            ],
        ]
    ],
    indirect=True,
)
async def test_enrich_people_by_ids_with_multi_person(
    authed_api_test_client: APITestClient,
    prospecting_common_service: ProspectingCommonService,
    person_repository: PersonRepository,
    pdl_person_repository: PDLPersonRepository,
    prospecting_quota_policy: list[QuotaPolicy],
) -> None:
    # Get organization_id and user_id from authed_api_test_client
    organization_id = authed_api_test_client.default_organization_id
    user_id = authed_api_test_client.default_user_id

    # Test data for two people to enrich
    test_person_data = [
        {
            "name": "Matt Rum",
            "company": "Easy Money App",
            "title": "Co-Founder",
            "pdl_id": "jF2XkFMiNu3An7i-CIGRFg_0000",
            "expected_email": "<EMAIL>",
            "expected_phone": "+19198122806",
        },
        {
            "name": "Joshua Rahm",
            "company": "Google",
            "title": "Software Engineer",
            "pdl_id": "WQSbVAfWtL6Rkxdy3zYkSA_0000",
            "expected_email": "<EMAIL>",
            "expected_phone": "+17202432230",
        },
    ]

    # Create prospect records and store their IDs
    person_ids = []
    for person_data in test_person_data:
        # Create person record
        inserted_person = Person(
            id=uuid.uuid4(),
            organization_id=organization_id,
            user_id=user_id,
            full_name=person_data["name"],
            current_company=person_data["company"],
            job_title=person_data["title"],
            work_email_enrich_status=ProspectingEnrichStatus.NOT_REQUESTED,
            phone_number_enrich_status=ProspectingEnrichStatus.NOT_REQUESTED,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
        await person_repository.insert(inserted_person)
        person_ids.append(inserted_person.id)

        # Create PDL mapping record
        pdl_person = PDLPerson(
            id=uuid.uuid4(),
            organization_id=organization_id,
            person_id=inserted_person.id,
            ext_id=person_data["pdl_id"],
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            data={"k": "v"},
        )
        await pdl_person_repository.insert(pdl_person)

    # Load mock PDL search response
    json_data = await run_in_pool(
        read_response_file_list,
        file_name="integration/integrations/pdl/data/pdl_enrich_multi_person_response.json",
    )

    # Enrich the single person
    with (
        patch(
            "salestech_be.integrations.pdl.pdl_client.PdlClient.bulk_enrich_person"
        ) as mock_bulk_enrich_person,
        patch.object(
            prospecting_common_service,
            "_process_people_workflow",
            return_value=None,
        ) as mock_process_workflow,
    ):
        # Configure mock to return a fixed PDL response
        mock_bulk_enrich_person.return_value = (
            PeopleDataLabsBulkEnrichResponse.from_response_list(json_data)
        )

        # Call enrich_people_by_ids with the single person
        enriched_people = await prospecting_common_service.enrich_people_by_ids(
            person_ids=person_ids,
            user_id=user_id,
            organization_id=organization_id,
            enrich_phone_numbers=True,
        )

        # Verify search_people was called with the correct SQL query containing the PDL ext_id
        mock_bulk_enrich_person.assert_called_once()

        # Verify _process_people_workflow was called
        mock_process_workflow.assert_called_once()

        # Check the result - should have one enriched person
        assert len(enriched_people) == 2

        # Get the enriched person
        enriched_person_1 = enriched_people[0].db_person
        enriched_person_2 = enriched_people[1].db_person

        # Verify email was enriched with the correct value from the PDL response
        assert enriched_person_1.work_email is not None
        assert enriched_person_1.work_email == "<EMAIL>"
        assert (
            enriched_person_1.work_email_enrich_status
            == ProspectingEnrichStatus.ENRICHED
        )

        # Verify phone was enriched with the correct value from the PDL response
        assert enriched_person_1.phone_numbers is not None
        assert len(enriched_person_1.phone_numbers) > 0
        assert enriched_person_1.phone_numbers[0].number == "+19198122806"
        assert (
            enriched_person_1.phone_number_enrich_status
            == ProspectingEnrichStatus.ENRICHED
        )

        assert enriched_person_2.work_email is not None
        assert enriched_person_2.work_email == "<EMAIL>"
        assert (
            enriched_person_2.work_email_enrich_status
            == ProspectingEnrichStatus.ENRICHED
        )

        assert enriched_person_2.phone_numbers is not None
        assert len(enriched_person_2.phone_numbers) > 0
        assert enriched_person_2.phone_numbers[0].number == "+17202432230"
        assert (
            enriched_person_2.phone_number_enrich_status
            == ProspectingEnrichStatus.ENRICHED
        )


@pytest.mark.parametrize(
    "prospecting_quota_policy",
    [
        [
            [
                QuotaConsumerEntityType.ORGANIZATION,
                QuotaConsumingResource.PROSPECTING_COMPANY_SEARCH,
                QuotaPeriod.MONTHLY,
                1000,
            ],
        ]
    ],
    indirect=True,
)
async def test_bulk_import_company_with_existing_companies(
    authed_api_test_client: APITestClient,
    prospecting_common_service: ProspectingCommonService,
    company_repository: CompanyRepository,
    prospecting_quota_policy: list[QuotaPolicy],
) -> None:
    """
    Test bulk_import_company with existing companies in the database (no search needed).
    Focus on quota credit calculation for search.
    """
    # Get organization_id and user_id from authed_api_test_client
    organization_id = authed_api_test_client.default_organization_id
    user_id = authed_api_test_client.default_user_id

    # Create existing companies in the database
    existing_companies = []
    for i in range(3):
        company = Company(
            id=uuid.uuid4(),
            organization_id=organization_id,
            user_id=user_id,
            name=f"Test Company {i}",
            website_url=f"https://testcompany{i}.com",
            primary_domain=f"testcompany{i}.com",
            industry="Technology",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
        await company_repository.insert(company)
        existing_companies.append(company)

    # Create a request for bulk_import_company with only existing companies
    request = BulkImportCompanyRequest(
        company_ids=[company.id for company in existing_companies],
        total_number=len(existing_companies),
    )

    # Execute the bulk_import_company method
    company_ids, prospecting_run = await prospecting_common_service.bulk_import_company(
        user_id=user_id,
        organization_id=organization_id,
        bulk_import_company_request=request,
    )

    # Verify results
    assert len(company_ids) == len(existing_companies)
    assert prospecting_run is not None
    assert prospecting_run.organization_id == organization_id
    assert prospecting_run.user_id == user_id

    # Verify that the company IDs returned match the ones we created
    for company_id in company_ids:
        assert company_id in [company.id for company in existing_companies]


@pytest.mark.parametrize(
    "prospecting_quota_policy",
    [
        [
            [
                QuotaConsumerEntityType.ORGANIZATION,
                QuotaConsumingResource.PROSPECTING_COMPANY_SEARCH,
                QuotaPeriod.MONTHLY,
                1000,
            ],
        ]
    ],
    indirect=True,
)
async def test_bulk_import_company_with_search(
    authed_api_test_client: APITestClient,
    prospecting_common_service: ProspectingCommonService,
    company_repository: CompanyRepository,
    prospecting_quota_policy: list[QuotaPolicy],
    quota_usage_repo: QuotaUsageRepository,
) -> None:
    """
    Test bulk_import_company when additional companies need to be searched.
    Focus on quota credit calculation for search.
    """
    # Get organization_id and user_id from authed_api_test_client
    organization_id = authed_api_test_client.default_organization_id
    user_id = authed_api_test_client.default_user_id

    # Create some existing companies in the database
    existing_companies = []
    for i in range(2):
        company = Company(
            id=uuid.uuid4(),
            organization_id=organization_id,
            user_id=user_id,
            name=f"Test Company {i}",
            website_url=f"https://testcompany{i}.com",
            primary_domain=f"testcompany{i}.com",
            industry="Technology",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
        await company_repository.insert(company)
        existing_companies.append(company)

    # We'll need 3 more companies from search
    need_more_count = 3
    total_count = len(existing_companies) + need_more_count

    # calculate the expected search credits - using the same logic as the implementation
    expected_search_credits = PDL_SEARCH_CREDITS_PER_RECORD * need_more_count

    # Mock PDL search response
    json_data = await run_in_pool(
        read_response_file,
        file_name="integration/integrations/pdl/data/pdl_search_company_response.json",
    )

    # get the usage before the test
    period_start = zoned_utc_now().replace(hour=0, minute=0, second=0, microsecond=0)
    period_end = zoned_utc_now().replace(
        hour=23, minute=59, second=59, microsecond=999999
    )

    before_usage = await quota_usage_repo.get_aggregate_user_usage_in_period(
        organization_id=organization_id,
        entity_id=organization_id,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        resource=QuotaConsumingResource.PROSPECTING_COMPANY_SEARCH,
        period_start=period_start,
        period_end=period_end,
    )

    with patch(
        "salestech_be.integrations.pdl.pdl_client.PdlClient.search_company"
    ) as mock_search_company:
        # Configure mock to return a fixed PDL response
        mock_search_company.return_value = (
            PeopleDataLabsSearchCompanyResponse.model_validate(json_data)
        )

        # Create a request for bulk_import_company with filter_spec to trigger search
        filter_spec = FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.company.identifier,
            filter=CompositeFilter(
                all_of=[
                    ValueFilter(
                        field=QualifiedField(
                            path=("linkedin_industry",),
                        ),
                        value={"computer software"},
                        operator=MatchOperator.IN,
                    ),
                ]
            ),
        )

        request = BulkImportCompanyRequest(
            company_ids=[company.id for company in existing_companies],
            filter_spec=filter_spec,
            total_number=total_count,
        )

        # Execute the bulk_import_company method
        (
            company_ids,
            prospecting_run,
        ) = await prospecting_common_service.bulk_import_company(
            user_id=user_id,
            organization_id=organization_id,
            bulk_import_company_request=request,
        )

        # Verify search was called
        mock_search_company.assert_called()

    # get the usage after the test
    after_usage = await quota_usage_repo.get_aggregate_user_usage_in_period(
        organization_id=organization_id,
        entity_id=organization_id,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        resource=QuotaConsumingResource.PROSPECTING_COMPANY_SEARCH,
        period_start=period_start,
        period_end=period_end,
    )

    # verify the usage difference is as expected
    usage_difference = after_usage - before_usage
    assert usage_difference == expected_search_credits

    # Verify results
    assert len(company_ids) == 5  # 2 existing + 3 from search
    assert prospecting_run is not None
    assert prospecting_run.organization_id == organization_id
    assert prospecting_run.user_id == user_id

    # Now we should have search credits in the prospecting run's estimated_credits
    assert prospecting_run.estimated_credits is not None
    search_credit = next(
        (
            c
            for c in prospecting_run.estimated_credits
            if c.resource == QuotaConsumingResource.PROSPECTING_COMPANY_SEARCH
        ),
        None,
    )

    # Search credits SHOULD now be included in the run
    assert search_credit is not None
    assert search_credit.credit == expected_search_credits


async def test_convert_people_to_contacts(
    authed_api_test_client: APITestClient,
    prospecting_common_service: ProspectingCommonService,
    person_repository: PersonRepository,
    company_repository: CompanyRepository,
    select_list_service: InternalSelectListService,
    contact_service: ContactService,
) -> None:
    # Get organization_id and user_id from authed_api_test_client
    organization_id = authed_api_test_client.default_organization_id
    user_id = authed_api_test_client.default_user_id
    # Bootstrap select lists
    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id,
        user_id=user_id,
    )

    # ===== Test Case 1: Person WITH email =====
    # Create person with email
    person_with_email = Person(
        id=uuid.uuid4(),
        organization_id=organization_id,
        user_id=user_id,
        full_name="Test Person With Email",
        first_name="Test",
        last_name="Person-Email",
        current_company="Test Company",
        job_title="Software Engineer",
        work_email="<EMAIL>",
        work_email_enrich_status=ProspectingEnrichStatus.ENRICHED,
        phone_number_enrich_status=ProspectingEnrichStatus.ENRICHED,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    company1 = Company(
        id=uuid.uuid4(),
        organization_id=organization_id,
        user_id=user_id,
        name="Test Company 1",
        website_url="https://testcompany1.com",
        primary_domain="testcompany1.com",
        industry="Technology",
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await company_repository.insert(company1)
    await person_repository.insert(person_with_email)
    person_dto_list_with_email = [
        PersonDto(db_person=person_with_email, db_company=company1)
    ]

    # First conversion attempt should create a new contact
    contacts_with_email_first = (
        await prospecting_common_service.convert_people_to_contacts(
            person_dto_list=person_dto_list_with_email,
            organization_id=organization_id,
            user_id=user_id,
        )
    )

    # Verify results of first conversion
    assert len(contacts_with_email_first) == 1
    contact_with_email_first = contacts_with_email_first[0]
    assert contact_with_email_first.person_id == person_with_email.id

    # Second conversion attempt should recognize existing contact
    contacts_with_email_second = (
        await prospecting_common_service.convert_people_to_contacts(
            person_dto_list=person_dto_list_with_email,
            organization_id=organization_id,
            user_id=user_id,
        )
    )

    # Verify second conversion doesn't create new contacts
    assert len(contacts_with_email_second) == 1
    contact_with_email_second = contacts_with_email_second[0]
    assert contact_with_email_second.person_id == person_with_email.id

    # Verify the first contact is the same as the second contact
    assert contact_with_email_first.id == contact_with_email_second.id

    # Verify the contact details in db
    contacts_with_email = await contact_service.list_by_person_ids(
        person_ids=[person_with_email.id],
        organization_id=organization_id,
    )

    assert len(contacts_with_email) == 1
    contact_with_email = contacts_with_email[0]
    assert contact_with_email.person_id == person_with_email.id
    assert contact_with_email.display_name == person_with_email.full_name
    assert contact_with_email.primary_email == person_with_email.work_email

    # ===== Test Case 2: Person WITHOUT email =====
    # Create person without email
    person_without_email = Person(
        id=uuid.uuid4(),
        organization_id=organization_id,
        user_id=user_id,
        full_name="Test Person Without Email",
        first_name="Test",
        last_name="Person-NoEmail",
        current_company="Test Company",
        job_title="Product Manager",
        work_email=None,
        work_email_enrich_status=ProspectingEnrichStatus.ENRICHED,
        phone_number_enrich_status=ProspectingEnrichStatus.ENRICHED,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    company2 = Company(
        id=uuid.uuid4(),
        organization_id=organization_id,
        user_id=user_id,
        name="Test Company 2",
        website_url="https://testcompany2.com",
        primary_domain="testcompany2.com",
        industry="Technology",
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await company_repository.insert(company2)
    await person_repository.insert(person_without_email)
    person_dto_list_without_email = [
        PersonDto(db_person=person_without_email, db_company=company2)
    ]

    # First conversion attempt should create a new contact
    contacts_without_email_first = (
        await prospecting_common_service.convert_people_to_contacts(
            person_dto_list=person_dto_list_without_email,
            organization_id=organization_id,
            user_id=user_id,
        )
    )

    # Verify results of first conversion
    assert len(contacts_without_email_first) == 1
    contact_without_email_first = contacts_without_email_first[0]
    assert contact_without_email_first.person_id == person_without_email.id

    # Second conversion attempt should recognize existing contact
    contacts_without_email_second = (
        await prospecting_common_service.convert_people_to_contacts(
            person_dto_list=person_dto_list_without_email,
            organization_id=organization_id,
            user_id=user_id,
        )
    )

    # Verify second conversion doesn't create new contacts
    assert len(contacts_without_email_second) == 1
    contact_without_email_second = contacts_without_email_second[0]
    assert contact_without_email_second.person_id == person_without_email.id

    # Verify the first contact is the same as the second contact
    assert contact_without_email_first.id == contact_without_email_second.id

    # Verify the contact details in database
    contacts_without_email = await contact_service.list_by_person_ids(
        person_ids=[person_without_email.id],
        organization_id=organization_id,
    )

    assert len(contacts_without_email) == 1
    contact_without_email = contacts_without_email[0]
    assert contact_without_email.person_id == person_without_email.id
    assert contact_without_email.display_name == person_without_email.full_name
    assert contact_without_email.primary_email is None  # No email


@pytest.mark.parametrize(
    "prospecting_quota_policy",
    [
        [
            [
                QuotaConsumerEntityType.ORGANIZATION,
                QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH,
                QuotaPeriod.MONTHLY,
                1000,
            ],
        ]
    ],
    indirect=True,
)
async def test_search_additional_people(
    authed_api_test_client: APITestClient,
    prospecting_common_service: ProspectingCommonService,
    quota_usage_repo: QuotaUsageRepository,
    prospecting_quota_policy: list[QuotaPolicy],
) -> None:
    # Get organization_id and user_id from authed_api_test_client
    organization_id = authed_api_test_client.default_organization_id
    user_id = authed_api_test_client.default_user_id

    # We'll need 3 more people from search
    need_more_count = 3

    # calculate the expected search credits - using the same logic as the implementation
    expected_search_credits = PDL_SEARCH_CREDITS_PER_RECORD * need_more_count

    # Mock PDL preview search response
    json_data = await run_in_pool(
        read_response_file,
        file_name="integration/integrations/pdl/data/pdl_preview_search_person_response.json",
    )

    # Get the before search usage
    period_start = zoned_utc_now().replace(hour=0, minute=0, second=0, microsecond=0)
    period_end = zoned_utc_now().replace(
        hour=23, minute=59, second=59, microsecond=999999
    )
    before_search_usage = await quota_usage_repo.get_aggregate_user_usage_in_period(
        organization_id=organization_id,
        entity_id=organization_id,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        resource=QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH,
        period_start=period_start,
        period_end=period_end,
    )

    with patch(
        "salestech_be.integrations.pdl.pdl_client.PdlClient.preview_search_people"
    ) as mock_search_people:
        # Configure mock to return a fixed PDL response
        mock_search_people.return_value = (
            PeopleDataLabsPreviewSearchPeopleResponse.model_validate(json_data)
        )

        # Execute the search_additional_people method
        await prospecting_common_service.search_additional_people(
            user_id=user_id,
            organization_id=organization_id,
            filter_spec=FilterSpec(
                primary_object_identifier=ProspectingPerson.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=("person_title",),
                            ),
                            value={"senior software engineer"},
                            operator=MatchOperator.IN,
                        ),
                    ]
                ),
            ),
            count_needed=need_more_count,
        )

        # Verify search was called
        mock_search_people.assert_called()

    # Verify the usage after the test
    after_search_usage = await quota_usage_repo.get_aggregate_user_usage_in_period(
        organization_id=organization_id,
        entity_id=organization_id,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        resource=QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH,
        period_start=period_start,
        period_end=period_end,
    )
    usage_difference = after_search_usage - before_search_usage
    assert usage_difference == expected_search_credits


async def test_build_pdl_search_request_exclude_person_ids(
    authed_api_test_client: APITestClient,
    prospecting_common_service: ProspectingCommonService,
    pdl_person_repository: PDLPersonRepository,
) -> None:
    # Get organization_id and user_id from authed_api_test_client
    organization_id = authed_api_test_client.default_organization_id
    user_id = authed_api_test_client.default_user_id

    # Create PDL person records to exclude
    exclude_person_ids = [uuid.uuid4(), uuid.uuid4(), uuid.uuid4()]
    pdl_ext_ids = [f"pdl_ext_id_{i}" for i in range(len(exclude_person_ids))]

    # Insert PDL person records
    for idx, person_id in enumerate(exclude_person_ids):
        pdl_person = PDLPerson(
            id=uuid.uuid4(),
            organization_id=organization_id,
            person_id=person_id,
            ext_id=pdl_ext_ids[idx],
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            data={"k": "v"},
        )
        await pdl_person_repository.insert(pdl_person)

    # Create a basic search request
    list_request = ListEntityRequestV2(
        filter_spec=FilterSpec(
            primary_object_identifier=ProspectingPerson.object_id,
            filter=CompositeFilter(
                all_of=[
                    ValueFilter(
                        field=QualifiedField(path=("person_title",)),
                        value={"Software Engineer"},
                        operator=MatchOperator.IN,
                    ),
                ],
            ),
        ),
    )

    # Directly call the method and get the return value
    _, pdl_request = await prospecting_common_service.build_pdl_common_search_request(
        user_id=user_id,
        organization_id=organization_id,
        list_request=list_request,
        search_query_type=ProspectingSearchQueryType.PEOPLE,
        exclude_person_ids=exclude_person_ids,
    )

    # Convert query to string to check for presence of IDs
    query_str = pdl_request.query

    assert query_str is not None
    # Check that all excluded PDL IDs are in the query
    for pdl_ext_id in pdl_ext_ids:
        assert pdl_ext_id in query_str, f"PDL ext ID {pdl_ext_id} not found in query"

    # Also check that the query contains must_not and terms/id structure
    assert "must_not" in query_str, "Query does not contain must_not"
    assert '"terms"' in query_str and '"id"' in query_str, (
        "Query does not contain terms/id structure"
    )


async def test_build_pdl_search_request_exclude_company_ids(
    authed_api_test_client: APITestClient,
    prospecting_common_service: ProspectingCommonService,
    pdl_company_repository: PDLCompanyRepository,
) -> None:
    # Get organization_id and user_id from authed_api_test_client
    organization_id = authed_api_test_client.default_organization_id
    user_id = authed_api_test_client.default_user_id

    # Create PDL company records to exclude
    exclude_company_ids = [uuid.uuid4(), uuid.uuid4(), uuid.uuid4()]
    pdl_ext_ids = [f"company_pdl_ext_id_{i}" for i in range(len(exclude_company_ids))]

    # Insert PDL company records
    for idx, company_id in enumerate(exclude_company_ids):
        pdl_company = PDLCompany(
            id=uuid.uuid4(),
            organization_id=organization_id,
            company_id=company_id,
            ext_id=pdl_ext_ids[idx],
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            data={"k": "v"},
        )
        await pdl_company_repository.insert(pdl_company)

    # Create a basic search request
    list_request = ListEntityRequestV2(
        filter_spec=FilterSpec(
            primary_object_identifier=ProspectingCompany.object_id,
            filter=CompositeFilter(
                all_of=[
                    ValueFilter(
                        field=QualifiedField(path=("company_name",)),
                        value={"Google"},
                        operator=MatchOperator.IN,
                    ),
                ],
            ),
        ),
    )

    # Directly call the method and get the return value
    _, pdl_request = await prospecting_common_service.build_pdl_common_search_request(
        user_id=user_id,
        organization_id=organization_id,
        list_request=list_request,
        search_query_type=ProspectingSearchQueryType.COMPANY,
        exclude_company_ids=exclude_company_ids,
    )

    # Convert query to string to check for presence of company IDs
    query_str = pdl_request.query

    assert query_str is not None

    # Check that all excluded PDL company IDs are in the query
    for pdl_ext_id in pdl_ext_ids:
        assert pdl_ext_id in query_str, (
            f"Company PDL ext ID {pdl_ext_id} not found in query"
        )

    # Also check that the query contains must_not and terms/company_id structure
    assert "must_not" in query_str, "Query does not contain must_not"
    assert '"terms"' in query_str and '"id"' in query_str, (
        "Query does not contain terms/company_id structure"
    )
