from datetime import time
from unittest.mock import patch
from uuid import uuid4

import pytest
from fastapi import FastAPI
from httpx import AsyncClient
from pydantic_extra_types.timezone_name import TimeZoneName
from starlette import status

from salestech_be.common.exception.exception import ApplicationError
from salestech_be.common.query_util.filter_schema import (
    CompositeFilter,
    FilterSpec,
    ValueFilter,
)
from salestech_be.common.query_util.legacy.sort_schema import OrderEnum
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.query_util.sort_schema import Sorter, SortingSpec
from salestech_be.common.schema_manager.std_object_field_identifier import (
    SequenceEnrollmentField,
    StdSelectListIdentifier,
)
from salestech_be.common.type.metadata.schema import QualifiedField
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.email.pool.schema import EmailAccountPoolResponse
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.metadata.types import (
    SelectListDetails,
)
from salestech_be.core.sequence.service.sequence_enrollment_query_service import (
    SequenceEnrollmentQueryService,
)
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    SequenceEnrollmentService,
)
from salestech_be.core.sequence.type.sequence_enrollment_types import (
    SequenceEnrollment,
    SequenceEnrollmentEligibility,
)
from salestech_be.db.dao.email_account import (
    EmailAccountPoolRepository,
    EmailAccountRepository,
)
from salestech_be.db.dao.sequence_enrollment_repo import SequenceEnrollmentRepository
from salestech_be.db.dao.sequence_repository import (
    SequenceRepository,
)
from salestech_be.db.models.contact import (
    CreateContactAccountRoleRequest,
    CreateDbContactEmailAccountAssociationRequest,
    CreateDbContactEmailRequest,
)
from salestech_be.db.models.email_account import (
    EmailAccount,
    EmailAccountPool,
    EmailAccountPoolMembership,
    EmailAccountType,
    EmailAccountUseOverride,
)
from salestech_be.db.models.sequence import (
    DayOfWeek,
    SequenceEnrollmentContactStatus,
    SequenceEnrollmentDisplayStatus,
    SequenceEnrollmentRunStatus,
    SequenceEnrollmentStatus,
    SequenceFailureReason,
    SequenceScheduleTime,
    SequenceStatus,
    SequenceStepType,
    SequenceStepV2,
    SequenceV2Schedule,
    SequenceVisibility,
)
from salestech_be.db.models.sequence import (
    SequenceEnrollment as DbSequenceEnrollment,
)
from salestech_be.db.models.sequence import (
    SequenceV2 as SequenceV2Db,
)
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.common.container import ListEntityRequestV2
from salestech_be.web.api.contact.schema import (
    CreateContactInfoRequest,
    CreateContactWithContactChannelRequest,
)
from salestech_be.web.api.sequence.enrollment.schema import (
    BulkChangeSequenceEnrollmentRequest,
    ContactForSequenceEnrollment,
    CreateSequenceEnrollmentRequest,
    PatchSequenceEnrollmentRequest,
    PreviewSequenceEnrollmentRequest,
)
from tests.integration.web.api.util.common_api_client import (
    CommonAPIClient,
)
from tests.util.factories import (
    SequenceFactory,
)


@patch.object(settings, "enable_sequence_perms", True)
@pytest.mark.parametrize(
    "sequence_visibility, creating_user, accessing_user, expected_accessing_create_status, expected_accessing_read_status, expected_accessing_update_status",
    [
        (
            SequenceVisibility.PRIVATE,
            "user1",
            "user1",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.PRIVATE,
            "user1",
            "user2",
            status.HTTP_403_FORBIDDEN,
            status.HTTP_403_FORBIDDEN,
            status.HTTP_403_FORBIDDEN,
        ),
        (
            SequenceVisibility.PRIVATE,
            "user1",
            "admin",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.PRIVATE,
            "user2",
            "user1",
            status.HTTP_403_FORBIDDEN,
            status.HTTP_403_FORBIDDEN,
            status.HTTP_403_FORBIDDEN,
        ),
        (
            SequenceVisibility.PRIVATE,
            "user2",
            "user2",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.PRIVATE,
            "user2",
            "admin",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.PRIVATE,
            "admin",
            "user1",
            status.HTTP_403_FORBIDDEN,
            status.HTTP_403_FORBIDDEN,
            status.HTTP_403_FORBIDDEN,
        ),
        (
            SequenceVisibility.PRIVATE,
            "admin",
            "user2",
            status.HTTP_403_FORBIDDEN,
            status.HTTP_403_FORBIDDEN,
            status.HTTP_403_FORBIDDEN,
        ),
        (
            SequenceVisibility.PRIVATE,
            "admin",
            "admin",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_VIEWABLE,
            "user1",
            "user1",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_VIEWABLE,
            "user1",
            "user2",
            status.HTTP_403_FORBIDDEN,
            status.HTTP_200_OK,
            status.HTTP_403_FORBIDDEN,
        ),
        (
            SequenceVisibility.TEAM_VIEWABLE,
            "user1",
            "admin",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_VIEWABLE,
            "user2",
            "user1",
            status.HTTP_403_FORBIDDEN,
            status.HTTP_200_OK,
            status.HTTP_403_FORBIDDEN,
        ),
        (
            SequenceVisibility.TEAM_VIEWABLE,
            "user2",
            "user2",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_VIEWABLE,
            "user2",
            "admin",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_VIEWABLE,
            "admin",
            "user1",
            status.HTTP_403_FORBIDDEN,
            status.HTTP_200_OK,
            status.HTTP_403_FORBIDDEN,
        ),
        (
            SequenceVisibility.TEAM_VIEWABLE,
            "admin",
            "user2",
            status.HTTP_403_FORBIDDEN,
            status.HTTP_200_OK,
            status.HTTP_403_FORBIDDEN,
        ),
        (
            SequenceVisibility.TEAM_VIEWABLE,
            "admin",
            "admin",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_EDITABLE,
            "user1",
            "user1",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_EDITABLE,
            "user1",
            "user2",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_EDITABLE,
            "user1",
            "admin",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_EDITABLE,
            "user2",
            "user1",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_EDITABLE,
            "user2",
            "user2",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_EDITABLE,
            "user2",
            "admin",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_EDITABLE,
            "admin",
            "user1",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_EDITABLE,
            "admin",
            "user2",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
        (
            SequenceVisibility.TEAM_EDITABLE,
            "admin",
            "admin",
            status.HTTP_200_OK,
            status.HTTP_200_OK,
            status.HTTP_200_OK,
        ),
    ],
)
async def test_sequence_enrollment_crud_perms(
    multi_api_client: dict[str, CommonAPIClient],
    fastapi_app: FastAPI,
    api_client: AsyncClient,
    seq_repo: SequenceRepository,
    sequence_visibility: SequenceVisibility,
    creating_user: str,
    accessing_user: str,
    expected_accessing_create_status: int,
    expected_accessing_read_status: int,
    expected_accessing_update_status: int,
) -> None:
    """
    This only tests permissions. Functionality is tested separately.

    This purposely excludes the _list endpoint, which is tested separately.
    """

    # this represents the user that created the resource.
    creating_user_client = multi_api_client[creating_user]

    # this represents the user that is accessing the resource.
    accessing_user_client = multi_api_client[accessing_user]

    # admin can do anything, so only add claims to non-admin users.
    if creating_user != "admin":
        await creating_user_client.set_permissions(
            ["create:sequence", "read:sequence", "update:sequence", "delete:sequence"]
        )
    if accessing_user != "admin":
        await accessing_user_client.set_permissions(
            ["create:sequence", "read:sequence", "update:sequence", "delete:sequence"]
        )

    # Creates a sequence belonging to the creating user.
    sequence_id = uuid4()
    await seq_repo.insert(
        SequenceV2Db(
            id=sequence_id,
            name=f"Test Sequence {sequence_id}",
            description=f"Description for test sequence {sequence_id}",
            owner_user_id=creating_user_client.default_user_id,
            participants=[],
            visibility=sequence_visibility,
            status=SequenceStatus.ACTIVE,
            cloned_from_sequence_id=None,
            organization_id=creating_user_client.default_organization_id,
            created_at=zoned_utc_now(),
            created_by_user_id=creating_user_client.default_user_id,
            schedule=SequenceV2Schedule(
                timezone=TimeZoneName("America/New_York"),
                skip_holidays=False,
                schedule_times=[
                    SequenceScheduleTime(
                        day_of_the_week=DayOfWeek.MON,
                        start_time=time(hour=9, minute=0),
                        end_time=time(hour=17, minute=0),
                    ),
                ],
            ),
        )
    )

    preview_sequence_enrollment_request = PreviewSequenceEnrollmentRequest(
        sequence_id=sequence_id,
        contacts=[
            ContactForSequenceEnrollment(
                contact_id=uuid4(),
            )
        ],
    ).model_dump(mode="json")

    create_sequence_enrollment_request = CreateSequenceEnrollmentRequest(
        sequence_id=sequence_id,
        contacts=[
            ContactForSequenceEnrollment(
                contact_id=uuid4(),
            )
        ],
    ).model_dump(mode="json")

    patch_sequence_enrollment_request = PatchSequenceEnrollmentRequest(
        status=SequenceEnrollmentStatus.INACTIVE,
    ).model_dump(mode="json")

    mock_sequence_enrollment = DbSequenceEnrollment(
        id=uuid4(),
        sequence_id=sequence_id,
        contact_id=uuid4(),
        status=SequenceEnrollmentStatus.ACTIVE,
        organization_id=creating_user_client.default_organization_id,
        enrolled_at=zoned_utc_now(),
        enrolled_by_user_id=creating_user_client.default_user_id,
        updated_at=zoned_utc_now(),
    )

    class ForcedReturn(ApplicationError):
        def http_code(self) -> int:
            return 200

    with (
        patch.object(
            SequenceEnrollmentService,
            "preview_sequence_enrollments_from_contacts",
            side_effect=ForcedReturn("permissions check only - stop here"),
        ),
        patch.object(
            SequenceEnrollmentService,
            "preview_sequence_enrollments_from_domain_object_list",
            side_effect=ForcedReturn("permissions check only - stop here"),
        ),
        patch.object(
            SequenceEnrollmentService,
            "create_sequence_enrollment_from_contacts",
            side_effect=ForcedReturn("permissions check only - stop here"),
        ),
        patch.object(
            SequenceEnrollmentService,
            "create_sequence_enrollment_from_domain_object_list",
            side_effect=ForcedReturn("permissions check only - stop here"),
        ),
        patch.object(
            SequenceEnrollmentRepository,
            "find_sequence_enrollments_by_ids",
            return_value=[mock_sequence_enrollment],
        ),
        patch.object(
            SequenceEnrollmentRepository,
            "find_by_tenanted_primary_key",
            side_effect=ForcedReturn("permissions check only - stop here"),
        ),
        patch.object(
            SequenceEnrollmentRepository,
            "bulk_update_sequence_enrollment_status",
            side_effect=ForcedReturn("permissions check only - stop here"),
        ),
    ):
        # List enrollments.
        # This is tested directly against SequenceEnrollmentQueryService.filter_viewable_records().

        # Preview enrollment.
        creating_user_preview_response = (
            await creating_user_client.api_test_client.client.post(
                "/api/v1/sequence_enrollments/_preview",
                json=preview_sequence_enrollment_request,
            )
        )
        assert creating_user_preview_response.status_code == status.HTTP_200_OK

        accessing_user_preview_response = (
            await accessing_user_client.api_test_client.client.post(
                "/api/v1/sequence_enrollments/_preview",
                json=preview_sequence_enrollment_request,
            )
        )
        assert (
            accessing_user_preview_response.status_code
            == expected_accessing_read_status
        )

        # Create enrollment.
        create_sequence_enrollment_response = (
            await creating_user_client.api_test_client.client.post(
                "/api/v1/sequence_enrollments",
                json=create_sequence_enrollment_request,
            )
        )
        assert create_sequence_enrollment_response.status_code == status.HTTP_200_OK

        accessing_user_create_sequence_enrollment_response = (
            await accessing_user_client.api_test_client.client.post(
                "/api/v1/sequence_enrollments",
                json=create_sequence_enrollment_request,
            )
        )
        assert (
            accessing_user_create_sequence_enrollment_response.status_code
            == expected_accessing_create_status
        )

        # Delete enrollment.
        delete_sequence_enrollment_response = (
            await creating_user_client.api_test_client.client.delete(
                f"/api/v1/sequence_enrollments/{uuid4()}",
            )
        )
        assert delete_sequence_enrollment_response.status_code == status.HTTP_200_OK

        accessing_user_delete_sequence_enrollment_response = (
            await accessing_user_client.api_test_client.client.delete(
                f"/api/v1/sequence_enrollments/{uuid4()}",
            )
        )
        assert (
            accessing_user_delete_sequence_enrollment_response.status_code
            == expected_accessing_update_status
        )

        # Patch enrollment.
        patch_sequence_enrollment_response = (
            await creating_user_client.api_test_client.client.patch(
                f"/api/v1/sequence_enrollments/{mock_sequence_enrollment.id}",
                json=patch_sequence_enrollment_request,
            )
        )
        assert patch_sequence_enrollment_response.status_code == status.HTTP_200_OK

        accessing_user_patch_sequence_enrollment_response = (
            await accessing_user_client.api_test_client.client.patch(
                f"/api/v1/sequence_enrollments/{uuid4()}",
                json=PatchSequenceEnrollmentRequest(
                    status=SequenceEnrollmentStatus.INACTIVE,
                ).model_dump(mode="json"),
            )
        )
        assert (
            accessing_user_patch_sequence_enrollment_response.status_code
            == expected_accessing_update_status
        )

        # Bulk change enrollment.
        bulk_change_sequence_enrollment_response = (
            await creating_user_client.api_test_client.client.post(
                "/api/v1/sequence_enrollments/_bulk_change",
                json=BulkChangeSequenceEnrollmentRequest(
                    enrollment_ids=[uuid4()],
                    status=SequenceEnrollmentStatus.INACTIVE,
                ).model_dump(mode="json"),
            )
        )
        assert (
            bulk_change_sequence_enrollment_response.status_code == status.HTTP_200_OK
        )

        accessing_user_bulk_change_sequence_enrollment_response = (
            await accessing_user_client.api_test_client.client.post(
                "/api/v1/sequence_enrollments/_bulk_change",
                json=BulkChangeSequenceEnrollmentRequest(
                    enrollment_ids=[uuid4()],
                    status=SequenceEnrollmentStatus.INACTIVE,
                ).model_dump(mode="json"),
            )
        )
        assert (
            accessing_user_bulk_change_sequence_enrollment_response.status_code
            == expected_accessing_update_status
        )

        # Proceed enrollment.
        proceed_sequence_enrollment_response = (
            await creating_user_client.api_test_client.client.post(
                f"/api/v1/sequence_enrollments/{uuid4()}/_proceed",
                json=None,
            )
        )
        assert proceed_sequence_enrollment_response.status_code == status.HTTP_200_OK

        accessing_user_proceed_sequence_enrollment_response = (
            await accessing_user_client.api_test_client.client.post(
                f"/api/v1/sequence_enrollments/{uuid4()}/_proceed",
                json=None,
            )
        )
        assert (
            accessing_user_proceed_sequence_enrollment_response.status_code
            == expected_accessing_update_status
        )


@patch.object(settings, "enable_sequence_perms", True)
async def test_filter_viewable_records_for_sequence_enrollment(
    sequence_enrollment_query_service: SequenceEnrollmentQueryService,
) -> None:
    """
    This test is not expected to be exhaustive since we already have test coverage across all permission permutations.

    This test is only to verify that filtering based on user auth context is working as expected.
    """

    user_1_id = uuid4()
    user_2_id = uuid4()
    sequence_id_1 = uuid4()
    sequence_id_2 = uuid4()
    organization_id = uuid4()
    with (
        # return the sequences for the organization, in the expected order they will be queried.
        patch.object(
            SequenceRepository,
            "find_sequences_by_ids_and_organization_id",
            return_value=[
                SequenceV2Db(
                    id=sequence_id_1,
                    name="Test Sequence 1",
                    organization_id=organization_id,
                    visibility=SequenceVisibility.PRIVATE,
                    status=SequenceStatus.ACTIVE,
                    owner_user_id=user_1_id,
                    created_at=zoned_utc_now(),
                    created_by_user_id=user_1_id,
                    schedule=SequenceV2Schedule(
                        timezone=TimeZoneName("America/New_York"),
                        skip_holidays=False,
                        schedule_times=[
                            SequenceScheduleTime(
                                day_of_the_week=DayOfWeek.MON,
                                start_time=time(hour=9, minute=0),
                                end_time=time(hour=17, minute=0),
                            ),
                        ],
                    ),
                ),
                SequenceV2Db(
                    id=sequence_id_2,
                    name="Test Sequence 2",
                    organization_id=organization_id,
                    visibility=SequenceVisibility.PRIVATE,
                    status=SequenceStatus.ACTIVE,
                    owner_user_id=user_2_id,
                    created_at=zoned_utc_now(),
                    created_by_user_id=user_2_id,
                    schedule=SequenceV2Schedule(
                        timezone=TimeZoneName("America/New_York"),
                        skip_holidays=False,
                        schedule_times=[
                            SequenceScheduleTime(
                                day_of_the_week=DayOfWeek.MON,
                                start_time=time(hour=9, minute=0),
                                end_time=time(hour=17, minute=0),
                            ),
                        ],
                    ),
                ),
            ],
        ),
    ):
        # Since both Sequences are private, we are only expecting user1 to see their own enrollment.
        filtered_records = (
            await sequence_enrollment_query_service.filter_viewable_records(
                user_auth_context=UserAuthContext(
                    organization_id=organization_id,
                    user_id=user_1_id,
                ),
                records=[
                    SequenceEnrollment(
                        id=uuid4(),
                        sequence_id=sequence_id_1,
                        contact_id=uuid4(),
                        status=SequenceEnrollmentStatus.ACTIVE,
                        organization_id=organization_id,
                        display_status=SequenceEnrollmentDisplayStatus.ENROLLED,
                        enrolled_at=zoned_utc_now(),
                        enrolled_by_user_id=user_1_id,
                        updated_at=zoned_utc_now(),
                    ),
                    SequenceEnrollment(
                        id=uuid4(),
                        sequence_id=sequence_id_2,
                        contact_id=uuid4(),
                        status=SequenceEnrollmentStatus.ACTIVE,
                        organization_id=organization_id,
                        display_status=SequenceEnrollmentDisplayStatus.ENROLLED,
                        enrolled_at=zoned_utc_now(),
                        enrolled_by_user_id=user_2_id,
                        updated_at=zoned_utc_now(),
                    ),
                ],
            )
        )
    assert len(filtered_records) == 1
    assert filtered_records[0].sequence_id == sequence_id_1


async def test_bulk_change_sequence_enrollments(
    common_api_client: CommonAPIClient,
    sequence_enrollment_repo: SequenceEnrollmentRepository,
    sequence_factory: SequenceFactory,
    contact_stage_select_list: SelectListDetails,
    email_account_pool_repository: EmailAccountPoolRepository,
) -> None:
    # Setup test data
    user_id, organization_id = (
        common_api_client.default_user_id,
        common_api_client.default_organization_id,
    )

    # Create a real email account pool in the database
    email_pool_id = uuid4()
    email_account_pool = EmailAccountPool(
        id=email_pool_id,
        organization_id=organization_id,
        name="Test Email Account Pool for Bulk Change",
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
        is_default=True,
        owner_user_id=user_id,
    )
    await email_account_pool_repository.insert(email_account_pool)

    # Create an email account pool response with the ID of the pool we just created
    email_pool_response = EmailAccountPoolResponse(
        id=email_pool_id,
        name="Default Test Pool",
        description="Mock default email account pool for testing",
        organization_id=organization_id,
        created_at=str(zoned_utc_now()),
        created_by_user_id=user_id,
    )

    # Use unittest.mock.patch to mock the EmailAccountPoolService dependency at the correct location
    with patch(
        "salestech_be.core.sequence.service.sequence_enrollment_service.EmailAccountPoolService.get_default_email_account_pool",
        return_value=email_pool_response,
    ):
        # Get first stage for contacts
        first_stage = contact_stage_select_list.select_list_values[0]

        # Create test accounts
        account_1 = await common_api_client.create_account(
            CreateAccountRequest(
                display_name="Test Account 1",
                owner_user_id=user_id,
            )
        )
        account_2 = await common_api_client.create_account(
            CreateAccountRequest(
                display_name="Test Account 2",
                owner_user_id=user_id,
            )
        )

        # Create test contacts
        contact_1 = await common_api_client.create_contact_with_contact_channel(
            organization_id=organization_id,
            user_id=user_id,
            create_request=CreateContactWithContactChannelRequest(
                contact=CreateContactInfoRequest(
                    first_name="Test Contact 1",
                    last_name="Contact",
                    owner_user_id=user_id,
                    display_name="Test Contact 1",
                    created_by_user_id=user_id,
                    stage_id=first_stage.id,
                ),
                contact_emails=[
                    CreateDbContactEmailRequest(
                        email="<EMAIL>",
                        is_contact_primary=True,
                        email_account_associations=[
                            CreateDbContactEmailAccountAssociationRequest(
                                account_id=account_1.id,
                                is_contact_account_primary=True,
                            )
                        ],
                    ),
                ],
                account_roles=[
                    CreateContactAccountRoleRequest(
                        account_id=account_1.id,
                        is_primary_account=True,
                    )
                ],
            ),
        )
        contact_2 = await common_api_client.create_contact_with_contact_channel(
            organization_id=organization_id,
            user_id=user_id,
            create_request=CreateContactWithContactChannelRequest(
                contact=CreateContactInfoRequest(
                    first_name="Test Contact 2",
                    last_name="Contact",
                    owner_user_id=user_id,
                    display_name="Test Contact 2",
                    created_by_user_id=user_id,
                    stage_id=first_stage.id,
                ),
                contact_emails=[
                    CreateDbContactEmailRequest(
                        email="<EMAIL>",
                        is_contact_primary=True,
                        email_account_associations=[
                            CreateDbContactEmailAccountAssociationRequest(
                                account_id=account_2.id,
                                is_contact_account_primary=True,
                            )
                        ],
                    ),
                ],
                account_roles=[
                    CreateContactAccountRoleRequest(
                        account_id=account_2.id,
                        is_primary_account=True,
                    )
                ],
            ),
        )

        # Create test sequence
        sequence = sequence_factory.build(
            organization_id=organization_id,
            created_by_user_id=user_id,
        )

        # Create initial sequence enrollments
        create_request = CreateSequenceEnrollmentRequest(
            sequence_id=sequence.id,
            contacts=[
                ContactForSequenceEnrollment(
                    contact_id=contact_1.id,
                    account_id=not_none(contact_1.primary_account_id),
                    email=not_none(contact_1.primary_email),
                ),
                ContactForSequenceEnrollment(
                    contact_id=contact_2.id,
                    account_id=not_none(contact_2.primary_account_id),
                    email=not_none(contact_2.primary_email),
                ),
            ],
        )
        create_response = await common_api_client.create_sequence_enrollment(
            create_request
        )

        # Verify enrollments were created successfully
        assert len(create_response.enrolled_contacts) == 2, (
            "Expected two contacts to be enrolled"
        )
        assert len(create_response.failed_enrollments) == 0, (
            "Expected no failed enrollments"
        )

        enrollment_ids = [contact.id for contact in create_response.enrolled_contacts]

        # Verify enrollments exist in database
        enrollments_before_update = await sequence_enrollment_repo.find_all_sequence_enrollments_by_sequence_id_and_organization_id(
            sequence_id=sequence.id,
            organization_id=organization_id,
        )
        assert len(enrollments_before_update) == 2, (
            "Expected two enrollments in database"
        )

        # Test bulk change to INACTIVE status
        bulk_change_request = BulkChangeSequenceEnrollmentRequest(
            enrollment_ids=enrollment_ids,
            status=SequenceEnrollmentStatus.INACTIVE,
        )
        bulk_change_response = await common_api_client.bulk_change_sequence_enrollments(
            bulk_change_request
        )

        # Verify response
        assert (
            bulk_change_response.message
            == "Sequence enrollment status updated successfully"
        )
        assert len(bulk_change_response.updated_enrollments) == 2
        assert all(
            e.status == SequenceEnrollmentStatus.INACTIVE
            for e in bulk_change_response.updated_enrollments
        )

        # Verify database state
        updated_enrollments = await sequence_enrollment_repo.find_all_sequence_enrollments_by_sequence_id_and_organization_id(
            sequence_id=sequence.id,
            organization_id=organization_id,
        )
        assert len(updated_enrollments) == 2
        assert all(
            e.status == SequenceEnrollmentStatus.INACTIVE for e in updated_enrollments
        )


async def test_list_sequence_enrollments_with_complex_filters(
    common_api_client: CommonAPIClient,
    sequence_enrollment_repo: SequenceEnrollmentRepository,
    sequence_factory: SequenceFactory,
    contact_stage_select_list: SelectListDetails,
    email_account_pool_repository: EmailAccountPoolRepository,
) -> None:
    """Test complex filtering and sorting scenarios for the list sequence enrollments endpoint."""
    # Setup test data
    user_id, organization_id = (
        common_api_client.default_user_id,
        common_api_client.default_organization_id,
    )

    # Create a real email account pool in the database
    email_pool_id = uuid4()
    email_account_pool = EmailAccountPool(
        id=email_pool_id,
        organization_id=organization_id,
        name="Test Email Account Pool for List Filters",
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
        is_default=True,
        owner_user_id=user_id,
    )
    await email_account_pool_repository.insert(email_account_pool)

    # Create an email account pool response with the ID of the pool we just created
    email_pool_response = EmailAccountPoolResponse(
        id=email_pool_id,
        name="Default Test Pool",
        description="Mock default email account pool for testing",
        organization_id=organization_id,
        created_at=str(zoned_utc_now()),
        created_by_user_id=user_id,
    )

    # Use unittest.mock.patch to mock the EmailAccountPoolService dependency at the correct location
    with patch(
        "salestech_be.core.sequence.service.sequence_enrollment_service.EmailAccountPoolService.get_default_email_account_pool",
        return_value=email_pool_response,
    ):
        # Create test sequence
        sequence = sequence_factory.build(
            organization_id=organization_id,
            created_by_user_id=user_id,
        )

        # Create another sequence for testing filters
        another_sequence = sequence_factory.build(
            organization_id=organization_id,
            created_by_user_id=user_id,
        )

        # Get first stage for contacts
        first_stage = contact_stage_select_list.select_list_values[0]

        # Create contacts with different properties
        contacts = []
        for i in range(4):  # Create 4 test contacts with different properties
            # Create account first
            account = await common_api_client.create_account(
                CreateAccountRequest(
                    display_name=f"Filter Test Account {i}",
                    owner_user_id=user_id,
                )
            )

            # Create contact with email and account association
            email = f"filter_test{i}@example.com"
            contact = await common_api_client.create_contact_with_contact_channel(
                organization_id=organization_id,
                user_id=user_id,
                create_request=CreateContactWithContactChannelRequest(
                    contact=CreateContactInfoRequest(
                        first_name=f"Filter Test Contact {i}",
                        last_name="Contact",
                        owner_user_id=user_id,
                        display_name=f"Filter Test Contact {i}",
                        created_by_user_id=user_id,
                        stage_id=first_stage.id,
                    ),
                    contact_emails=[
                        CreateDbContactEmailRequest(
                            email=email,
                            is_contact_primary=True,
                            email_account_associations=[
                                CreateDbContactEmailAccountAssociationRequest(
                                    account_id=account.id,
                                    is_contact_account_primary=True,
                                )
                            ],
                        ),
                    ],
                    account_roles=[
                        CreateContactAccountRoleRequest(
                            account_id=account.id,
                            is_primary_account=True,
                        )
                    ],
                ),
            )
            contacts.append((contact, account, email))

        # Create sequence enrollments with different properties
        # First enrollment: Active status, first sequence
        create_request_1 = CreateSequenceEnrollmentRequest(
            sequence_id=sequence.id,
            contacts=[
                ContactForSequenceEnrollment(
                    contact_id=contacts[0][0].id,
                    account_id=contacts[0][1].id,
                    email=contacts[0][2],
                ),
            ],
        )
        response_1 = await common_api_client.create_sequence_enrollment(
            create_request_1
        )
        enrollment_1 = response_1.enrolled_contacts[0]

        # Second enrollment: Inactive status, first sequence
        create_request_2 = CreateSequenceEnrollmentRequest(
            sequence_id=sequence.id,
            contacts=[
                ContactForSequenceEnrollment(
                    contact_id=contacts[1][0].id,
                    account_id=contacts[1][1].id,
                    email=contacts[1][2],
                ),
            ],
        )
        response_2 = await common_api_client.create_sequence_enrollment(
            create_request_2
        )
        enrollment_2 = response_2.enrolled_contacts[0]

        # Update status to INACTIVE
        await common_api_client.patch_sequence_enrollment(
            enrollment_id=enrollment_2.id,
            request=PatchSequenceEnrollmentRequest(
                status=SequenceEnrollmentStatus.INACTIVE,
            ),
        )

        # Third enrollment: Active status, second sequence
        create_request_3 = CreateSequenceEnrollmentRequest(
            sequence_id=another_sequence.id,
            contacts=[
                ContactForSequenceEnrollment(
                    contact_id=contacts[2][0].id,
                    account_id=contacts[2][1].id,
                    email=contacts[2][2],
                ),
            ],
        )
        response_3 = await common_api_client.create_sequence_enrollment(
            create_request_3
        )
        enrollment_3 = response_3.enrolled_contacts[0]

        # Fourth enrollment: Failed status, first sequence
        create_request_4 = CreateSequenceEnrollmentRequest(
            sequence_id=sequence.id,
            contacts=[
                ContactForSequenceEnrollment(
                    contact_id=contacts[3][0].id,
                    account_id=contacts[3][1].id,
                    email=contacts[3][2],
                ),
            ],
        )
        response_4 = await common_api_client.create_sequence_enrollment(
            create_request_4
        )
        enrollment_4 = response_4.enrolled_contacts[0]

        # Update status to FAILED
        await common_api_client.patch_sequence_enrollment(
            enrollment_id=enrollment_4.id,
            request=PatchSequenceEnrollmentRequest(
                status=SequenceEnrollmentStatus.FAILED,
            ),
        )

        # Test 1: Filter by sequence ID
        sequence_id_filter_response = await common_api_client.list_sequence_enrollments(
            ListEntityRequestV2(
                filter_spec=FilterSpec(
                    primary_object_identifier=SequenceEnrollment.object_id,
                    filter=CompositeFilter(
                        all_of=[
                            ValueFilter(
                                field=QualifiedField(
                                    path=(SequenceEnrollmentField.sequence_id,),
                                    fetch_relationship_ids=(),
                                ),
                                operator=MatchOperator.EQ,
                                value=sequence.id,
                            ),
                        ]
                    ),
                ),
                sorting_spec=SortingSpec(
                    primary_object_identifier=SequenceEnrollment.object_id,
                    ordered_sorters=(
                        Sorter(
                            field=QualifiedField(
                                path=(SequenceEnrollmentField.enrolled_at,)
                            ),
                            order=OrderEnum.DESC,
                        ),
                    ),
                ),
            )
        )

        # Should return 3 enrollments for the first sequence
        assert sequence_id_filter_response.cursor.total_number == 3
        enrollment_ids = {
            item.data.id for item in sequence_id_filter_response.list_data
        }
        assert enrollment_1.id in enrollment_ids
        assert enrollment_2.id in enrollment_ids
        assert enrollment_3.id not in enrollment_ids
        assert enrollment_4.id in enrollment_ids

        # Test 2: Filter by enrollment status
        status_filter_response = await common_api_client.list_sequence_enrollments(
            ListEntityRequestV2(
                filter_spec=FilterSpec(
                    primary_object_identifier=SequenceEnrollment.object_id,
                    filter=CompositeFilter(
                        all_of=[
                            ValueFilter(
                                field=QualifiedField(
                                    path=(SequenceEnrollmentField.status,),
                                    fetch_relationship_ids=(),
                                ),
                                operator=MatchOperator.EQ,
                                value=SequenceEnrollmentStatus.ACTIVE,
                            ),
                        ]
                    ),
                ),
                sorting_spec=SortingSpec(
                    primary_object_identifier=SequenceEnrollment.object_id,
                    ordered_sorters=(
                        Sorter(
                            field=QualifiedField(
                                path=(SequenceEnrollmentField.enrolled_at,)
                            ),
                            order=OrderEnum.DESC,
                        ),
                    ),
                ),
            )
        )

        # Should return 2 active enrollments
        assert status_filter_response.cursor.total_number == 2
        status_enrollment_ids = {
            item.data.id for item in status_filter_response.list_data
        }
        assert enrollment_1.id in status_enrollment_ids
        assert enrollment_2.id not in status_enrollment_ids
        assert enrollment_3.id in status_enrollment_ids
        assert enrollment_4.id not in status_enrollment_ids

        # Test 3: Complex filter combining sequence ID and status
        complex_filter_response = await common_api_client.list_sequence_enrollments(
            ListEntityRequestV2(
                filter_spec=FilterSpec(
                    primary_object_identifier=SequenceEnrollment.object_id,
                    filter=CompositeFilter(
                        all_of=[
                            ValueFilter(
                                field=QualifiedField(
                                    path=(SequenceEnrollmentField.sequence_id,),
                                    fetch_relationship_ids=(),
                                ),
                                operator=MatchOperator.EQ,
                                value=sequence.id,
                            ),
                            ValueFilter(
                                field=QualifiedField(
                                    path=(SequenceEnrollmentField.status,),
                                    fetch_relationship_ids=(),
                                ),
                                operator=MatchOperator.NE,
                                value=SequenceEnrollmentStatus.INACTIVE,
                            ),
                        ]
                    ),
                ),
                sorting_spec=SortingSpec(
                    primary_object_identifier=SequenceEnrollment.object_id,
                    ordered_sorters=(
                        Sorter(
                            field=QualifiedField(
                                path=(SequenceEnrollmentField.enrolled_at,)
                            ),
                            order=OrderEnum.DESC,
                        ),
                    ),
                ),
            )
        )

        # Should return 2 enrollments (active + failed, but not inactive)
        assert complex_filter_response.cursor.total_number == 2
        complex_enrollment_ids = {
            item.data.id for item in complex_filter_response.list_data
        }
        assert enrollment_1.id in complex_enrollment_ids
        assert enrollment_2.id not in complex_enrollment_ids
        assert enrollment_3.id not in complex_enrollment_ids
        assert enrollment_4.id in complex_enrollment_ids

        # Test 4: Filter with "any_of" composite filter
        any_of_filter_response = await common_api_client.list_sequence_enrollments(
            ListEntityRequestV2(
                filter_spec=FilterSpec(
                    primary_object_identifier=SequenceEnrollment.object_id,
                    filter=CompositeFilter(
                        all_of=[
                            CompositeFilter(
                                any_of=[
                                    ValueFilter(
                                        field=QualifiedField(
                                            path=(SequenceEnrollmentField.status,),
                                            fetch_relationship_ids=(),
                                        ),
                                        operator=MatchOperator.EQ,
                                        value=SequenceEnrollmentStatus.INACTIVE,
                                    ),
                                    ValueFilter(
                                        field=QualifiedField(
                                            path=(SequenceEnrollmentField.status,),
                                            fetch_relationship_ids=(),
                                        ),
                                        operator=MatchOperator.EQ,
                                        value=SequenceEnrollmentStatus.FAILED,
                                    ),
                                ]
                            ),
                        ]
                    ),
                ),
                sorting_spec=SortingSpec(
                    primary_object_identifier=SequenceEnrollment.object_id,
                    ordered_sorters=(
                        Sorter(
                            field=QualifiedField(
                                path=(SequenceEnrollmentField.enrolled_at,)
                            ),
                            order=OrderEnum.DESC,
                        ),
                    ),
                ),
            )
        )

        # Should return 2 enrollments (inactive + failed)
        assert any_of_filter_response.cursor.total_number == 2
        any_of_enrollment_ids = {
            item.data.id for item in any_of_filter_response.list_data
        }
        assert enrollment_1.id not in any_of_enrollment_ids
        assert enrollment_2.id in any_of_enrollment_ids
        assert enrollment_3.id not in any_of_enrollment_ids
        assert enrollment_4.id in any_of_enrollment_ids

        # Test 5: Sort by status in ascending order
        status_sort_response = await common_api_client.list_sequence_enrollments(
            ListEntityRequestV2(
                filter_spec=FilterSpec(
                    primary_object_identifier=SequenceEnrollment.object_id,
                    filter=CompositeFilter(
                        all_of=[
                            ValueFilter(
                                field=QualifiedField(
                                    path=(SequenceEnrollmentField.sequence_id,),
                                    fetch_relationship_ids=(),
                                ),
                                operator=MatchOperator.EQ,
                                value=sequence.id,
                            ),
                        ]
                    ),
                ),
                sorting_spec=SortingSpec(
                    primary_object_identifier=SequenceEnrollment.object_id,
                    ordered_sorters=(
                        Sorter(
                            field=QualifiedField(
                                path=(SequenceEnrollmentField.status,)
                            ),
                            order=OrderEnum.ASC,
                        ),
                    ),
                ),
            )
        )

        # Check that sorting works correctly - active should come first, then failed, then inactive
        assert status_sort_response.cursor.total_number == 3
        assert len(status_sort_response.list_data) == 3

        # Should be in order: ACTIVE, FAILED, INACTIVE (alphabetical)
        sorted_statuses = [item.data.status for item in status_sort_response.list_data]
        assert sorted_statuses[0] == SequenceEnrollmentStatus.ACTIVE
        assert sorted_statuses[1] == SequenceEnrollmentStatus.FAILED
        assert sorted_statuses[2] == SequenceEnrollmentStatus.INACTIVE


async def test_resolve_and_fill_enrollment(
    sequence_enrollment_service: SequenceEnrollmentService,
    common_api_client: CommonAPIClient,
    contact_stage_select_list: SelectListDetails,
) -> None:
    # Setup test data
    user_id, organization_id = (
        common_api_client.default_user_id,
        common_api_client.default_organization_id,
    )

    # Create accounts for testing
    account1 = await common_api_client.create_account(
        CreateAccountRequest(
            display_name="Test Account 1",
            owner_user_id=user_id,
        )
    )

    account2 = await common_api_client.create_account(
        CreateAccountRequest(
            display_name="Test Account 2",
            owner_user_id=user_id,
        )
    )

    # Create 4 contacts with different combinations of email and account:

    # 1. Contact with both email and account association
    contact_with_both = await common_api_client.create_contact_with_contact_channel(
        organization_id=organization_id,
        user_id=user_id,
        create_request=CreateContactWithContactChannelRequest(
            contact=CreateContactInfoRequest(
                first_name="Contact",
                last_name="WithBoth",
                owner_user_id=user_id,
                display_name="Contact WithBoth",
                created_by_user_id=user_id,
                stage_id=contact_stage_select_list.select_list_values[0].id,
            ),
            contact_emails=[
                CreateDbContactEmailRequest(
                    email="<EMAIL>",
                    is_contact_primary=True,
                    email_account_associations=[
                        CreateDbContactEmailAccountAssociationRequest(
                            account_id=account1.id,
                            is_contact_account_primary=True,
                        )
                    ],
                ),
            ],
            account_roles=[
                CreateContactAccountRoleRequest(
                    account_id=account1.id,
                    is_primary_account=True,
                )
            ],
        ),
    )

    # 2. Contact with email but without account
    contact_with_email_only = (
        await common_api_client.create_contact_with_contact_channel(
            organization_id=organization_id,
            user_id=user_id,
            create_request=CreateContactWithContactChannelRequest(
                contact=CreateContactInfoRequest(
                    first_name="Contact",
                    last_name="WithEmailOnly",
                    owner_user_id=user_id,
                    display_name="Contact WithEmailOnly",
                    created_by_user_id=user_id,
                    stage_id=contact_stage_select_list.select_list_values[0].id,
                ),
                contact_emails=[
                    CreateDbContactEmailRequest(
                        email="<EMAIL>",
                        is_contact_primary=True,
                    ),
                ],
            ),
        )
    )

    # 3. Contact with account but without email
    contact_with_account_only = (
        await common_api_client.create_contact_with_contact_channel(
            organization_id=organization_id,
            user_id=user_id,
            create_request=CreateContactWithContactChannelRequest(
                contact=CreateContactInfoRequest(
                    first_name="Contact",
                    last_name="WithAccountOnly",
                    owner_user_id=user_id,
                    display_name="Contact WithAccountOnly",
                    created_by_user_id=user_id,
                    stage_id=contact_stage_select_list.select_list_values[0].id,
                ),
                account_roles=[
                    CreateContactAccountRoleRequest(
                        account_id=account2.id,
                        is_primary_account=True,
                    )
                ],
            ),
        )
    )

    # 4. Contact with neither email nor account
    contact_with_neither = await common_api_client.create_contact_with_contact_channel(
        organization_id=organization_id,
        user_id=user_id,
        create_request=CreateContactWithContactChannelRequest(
            contact=CreateContactInfoRequest(
                first_name="Contact",
                last_name="WithNeither",
                owner_user_id=user_id,
                display_name="Contact WithNeither",
                created_by_user_id=user_id,
                stage_id=contact_stage_select_list.select_list_values[0].id,
            ),
            contact_emails=[
                CreateDbContactEmailRequest(
                    email="<EMAIL>",
                    is_contact_primary=True,
                    email_account_associations=[
                        CreateDbContactEmailAccountAssociationRequest(
                            account_id=account2.id,
                            is_contact_account_primary=True,
                        )
                    ],
                ),
            ],
            account_roles=[
                CreateContactAccountRoleRequest(
                    account_id=account2.id,
                    is_primary_account=True,
                )
            ],
        ),
    )

    # Now create test cases for sequence enrollment with different missing fields
    test_contacts = [
        # Case 1: Both fields provided (no resolution needed)
        ContactForSequenceEnrollment(
            contact_id=contact_with_both.id,
            account_id=account1.id,
            email="<EMAIL>",
        ),
        # Case 2: Only email provided (needs account resolution)
        ContactForSequenceEnrollment(
            contact_id=contact_with_email_only.id,
            email="<EMAIL>",
        ),
        # Case 3: Only account provided (needs email resolution)
        ContactForSequenceEnrollment(
            contact_id=contact_with_account_only.id,
            account_id=account2.id,
        ),
        # Case 4: Neither provided (needs both resolved)
        ContactForSequenceEnrollment(
            contact_id=contact_with_neither.id,
        ),
    ]

    # Call the method to resolve and fill
    await sequence_enrollment_service.resolve_and_fill_sequence_enrollment_v2(
        organization_id=organization_id,
        contacts=test_contacts,
    )

    # Verify the results

    # Case 1: Contact with both email and account - all fields should remain unchanged
    assert test_contacts[0].contact_id == contact_with_both.id
    assert test_contacts[0].account_id == account1.id
    assert test_contacts[0].email == "<EMAIL>"

    # Case 2: Contact with email only - account_id should remain None as no association exists
    assert test_contacts[1].contact_id == contact_with_email_only.id
    assert test_contacts[1].email == "<EMAIL>"
    assert test_contacts[1].account_id is None

    # Case 3: Contact with account only - email should remain None as none was set up
    assert test_contacts[2].contact_id == contact_with_account_only.id
    assert test_contacts[2].account_id == account2.id
    assert test_contacts[2].email is None

    # Case 4: Contact with neither initially - both should be resolved
    assert test_contacts[3].contact_id == contact_with_neither.id
    assert test_contacts[3].email == "<EMAIL>"
    assert test_contacts[3].account_id == account2.id


@pytest.fixture
async def contact_stage_select_list(
    select_list_service: InternalSelectListService,
    common_api_client: CommonAPIClient,
) -> SelectListDetails:
    # todo(xw): remove this once we put bootstrap in the organization put api (post-fork)
    await select_list_service.bootstrap_direct_std_select_list(
        organization_id=common_api_client.api_test_client.default_organization_id,
        user_id=common_api_client.api_test_client.default_user_id,
        direct_std_select_list_name=StdSelectListIdentifier.contact_stage,
    )
    select_list_summaries = await common_api_client.list_select_lists()
    return next(
        select_list_summary
        for select_list_summary in select_list_summaries.list_data
        if select_list_summary.application_code_name
        == StdSelectListIdentifier.contact_stage
    )


async def test_check_contact_enrollment_status_for_sequence(  # noqa: C901
    common_api_client: CommonAPIClient,
    sequence_factory: SequenceFactory,
    sequence_enrollment_repo: SequenceEnrollmentRepository,
    contact_stage_select_list: SelectListDetails,
    email_account_pool_repository: EmailAccountPoolRepository,
    email_account_repository: EmailAccountRepository,
) -> None:
    """Test checking enrollment status for contacts in a particular sequence."""
    # Setup test data
    user_id, organization_id = (
        common_api_client.default_user_id,
        common_api_client.default_organization_id,
    )

    # Create a real email account pool in the database
    email_pool_id = uuid4()
    email_account_pool = EmailAccountPool(
        id=email_pool_id,
        organization_id=organization_id,
        name="Test Email Account Pool for Status Check",
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
        is_default=True,
        owner_user_id=user_id,
    )
    await email_account_pool_repository.insert(email_account_pool)

    # Create real email accounts with warmup override
    email_account = EmailAccount(
        id=uuid4(),
        owner_user_id=user_id,
        email="<EMAIL>",
        type=EmailAccountType.OUTBOUND,
        active=True,
        is_default=True,
        seconds_delay_between_emails=60,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
        use_override=EmailAccountUseOverride.USE_DESPITE_WARMUP_STATUS,
    )
    await email_account_repository.insert(email_account)

    # Add the email account to the pool
    email_account_pool_membership = EmailAccountPoolMembership(
        id=uuid4(),
        email_account_pool_id=email_pool_id,
        email_account_id=email_account.id,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
    )
    await email_account_repository.insert(email_account_pool_membership)

    # Create an email account pool response with the ID of the pool we just created
    email_pool_response = EmailAccountPoolResponse(
        id=email_pool_id,
        name="Default Test Pool",
        description="Mock default email account pool for testing",
        organization_id=organization_id,
        created_at=str(zoned_utc_now()),
        created_by_user_id=user_id,
    )

    # Use unittest.mock.patch to mock the EmailAccountPoolService dependency at the correct location
    with patch(
        "salestech_be.core.sequence.service.sequence_enrollment_service.EmailAccountPoolService.get_default_email_account_pool",
        return_value=email_pool_response,
    ):
        # Get first stage for contacts
        first_stage = contact_stage_select_list.select_list_values[0]

        # Create test sequence
        sequence = sequence_factory.build(
            organization_id=organization_id,
            created_by_user_id=user_id,
        )

        # Create another sequence to test warnings about contacts enrolled in other sequences
        other_sequence = sequence_factory.build(
            organization_id=organization_id,
            created_by_user_id=user_id,
        )

        # Add steps to both sequences - required for enrollment to work
        main_sequence_step = SequenceStepV2(
            id=uuid4(),
            organization_id=organization_id,
            sequence_id=sequence.id,
            name="Test Step 1",
            type=SequenceStepType.AUTO_EMAIL,
            delay_minutes=0,
            is_first_step=True,
            support_ab_test=False,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )
        await sequence_enrollment_repo.insert(main_sequence_step)

        # Create a step for the other sequence
        other_sequence_step = SequenceStepV2(
            id=uuid4(),
            organization_id=organization_id,
            sequence_id=other_sequence.id,
            name="Test Step 1",
            type=SequenceStepType.AUTO_EMAIL,
            delay_minutes=0,
            is_first_step=True,
            support_ab_test=False,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )
        await sequence_enrollment_repo.insert(other_sequence_step)

        # Create contacts with different statuses
        contacts = []
        accounts = []
        for i in range(4):  # Create 4 test contacts with different statuses
            # Create account
            account = await common_api_client.create_account(
                CreateAccountRequest(
                    display_name=f"Status Test Account {i}",
                    owner_user_id=user_id,
                )
            )
            accounts.append(account)

            # Create contact with email and account association
            contact = await common_api_client.create_contact_with_contact_channel(
                organization_id=organization_id,
                user_id=user_id,
                create_request=CreateContactWithContactChannelRequest(
                    contact=CreateContactInfoRequest(
                        first_name=f"Status Test Contact {i}",
                        last_name="Contact",
                        owner_user_id=user_id,
                        display_name=f"Status Test Contact {i}",
                        created_by_user_id=user_id,
                        stage_id=first_stage.id,
                    ),
                    contact_emails=[
                        CreateDbContactEmailRequest(
                            email=f"status_test{i}@example.com",
                            is_contact_primary=True,
                            email_account_associations=[
                                CreateDbContactEmailAccountAssociationRequest(
                                    account_id=account.id,
                                    is_contact_account_primary=True,
                                )
                            ],
                        ),
                    ],
                    account_roles=[
                        CreateContactAccountRoleRequest(
                            account_id=account.id,
                            is_primary_account=True,
                        )
                    ],
                ),
            )
            contacts.append(contact)

        # Create enrollments with different statuses
        enrollment_ids = []

        # Contact 0: ACTIVE enrollment
        active_request = CreateSequenceEnrollmentRequest(
            sequence_id=sequence.id,
            contacts=[
                ContactForSequenceEnrollment(
                    contact_id=contacts[0].id,
                    account_id=accounts[0].id,
                    email="<EMAIL>",
                ),
            ],
        )
        active_response = await common_api_client.create_sequence_enrollment(
            active_request
        )
        assert len(active_response.enrolled_contacts) > 0, (
            "Failed to create ACTIVE enrollment"
        )
        active_enrollment_id = active_response.enrolled_contacts[0].id
        enrollment_ids.append(active_enrollment_id)

        # Contact 1: INACTIVE enrollment
        inactive_request = CreateSequenceEnrollmentRequest(
            sequence_id=sequence.id,
            contacts=[
                ContactForSequenceEnrollment(
                    contact_id=contacts[1].id,
                    account_id=accounts[1].id,
                    email="<EMAIL>",
                ),
            ],
        )
        inactive_response = await common_api_client.create_sequence_enrollment(
            inactive_request
        )
        assert len(inactive_response.enrolled_contacts) > 0, (
            "Failed to create enrollment for INACTIVE status"
        )
        inactive_enrollment_id = inactive_response.enrolled_contacts[0].id
        enrollment_ids.append(inactive_enrollment_id)

        # Update to INACTIVE
        await common_api_client.patch_sequence_enrollment(
            enrollment_id=inactive_enrollment_id,
            request=PatchSequenceEnrollmentRequest(
                status=SequenceEnrollmentStatus.INACTIVE,
            ),
        )

        # Contact 2: FAILED enrollment
        failed_request = CreateSequenceEnrollmentRequest(
            sequence_id=sequence.id,
            contacts=[
                ContactForSequenceEnrollment(
                    contact_id=contacts[2].id,
                    account_id=accounts[2].id,
                    email="<EMAIL>",
                ),
            ],
        )
        failed_response = await common_api_client.create_sequence_enrollment(
            failed_request
        )
        assert len(failed_response.enrolled_contacts) > 0, (
            "Failed to create enrollment for FAILED status"
        )
        failed_enrollment_id = failed_response.enrolled_contacts[0].id
        enrollment_ids.append(failed_enrollment_id)

        # Update to FAILED
        await common_api_client.patch_sequence_enrollment(
            enrollment_id=failed_enrollment_id,
            request=PatchSequenceEnrollmentRequest(
                status=SequenceEnrollmentStatus.FAILED,
            ),
        )

        # Contact 3: EXITED enrollment
        exited_request = CreateSequenceEnrollmentRequest(
            sequence_id=sequence.id,
            contacts=[
                ContactForSequenceEnrollment(
                    contact_id=contacts[3].id,
                    account_id=accounts[3].id,
                    email="<EMAIL>",
                ),
            ],
        )
        exited_response = await common_api_client.create_sequence_enrollment(
            exited_request
        )
        assert len(exited_response.enrolled_contacts) > 0, (
            "Failed to create enrollment for EXITED status"
        )
        exited_enrollment_id = exited_response.enrolled_contacts[0].id
        enrollment_ids.append(exited_enrollment_id)

        # Update to EXITED
        await common_api_client.patch_sequence_enrollment(
            enrollment_id=exited_enrollment_id,
            request=PatchSequenceEnrollmentRequest(
                status=SequenceEnrollmentStatus.EXITED,
            ),
        )

        # Create a preview request to test the status checking functionality
        preview_request = PreviewSequenceEnrollmentRequest(
            sequence_id=sequence.id,
            contacts=[
                ContactForSequenceEnrollment(
                    contact_id=contacts[0].id,
                    account_id=accounts[0].id,
                    email="<EMAIL>",
                ),
                ContactForSequenceEnrollment(
                    contact_id=contacts[1].id,
                    account_id=accounts[1].id,
                    email="<EMAIL>",
                ),
                ContactForSequenceEnrollment(
                    contact_id=contacts[2].id,
                    account_id=accounts[2].id,
                    email="<EMAIL>",
                ),
                ContactForSequenceEnrollment(
                    contact_id=contacts[3].id,
                    account_id=accounts[3].id,
                    email="<EMAIL>",
                ),
            ],
        )

        preview_response = await common_api_client.preview_sequence_enrollments(
            preview_request
        )

        # Verify the preview response contains the correct eligibility for each contact
        assert len(preview_response.preview) == 4

        for preview_item in preview_response.preview:
            if preview_item.contact_id == contacts[0].id:
                # ACTIVE enrollment should be ineligible
                assert (
                    preview_item.eligibility == SequenceEnrollmentEligibility.INELIGIBLE
                )
                assert preview_item.can_enroll is False
                assert (
                    preview_item.reason
                    == SequenceFailureReason.CONTACT_ALREADY_ENROLLED
                )

            elif preview_item.contact_id == contacts[1].id:
                # INACTIVE enrollment should be eligible
                assert (
                    preview_item.eligibility == SequenceEnrollmentEligibility.ELIGIBLE
                )
                assert preview_item.can_enroll is True
                assert preview_item.reason == ""

            elif preview_item.contact_id == contacts[2].id:
                # FAILED enrollment should be ineligible
                assert (
                    preview_item.eligibility == SequenceEnrollmentEligibility.INELIGIBLE
                )
                assert preview_item.can_enroll is False
                assert (
                    preview_item.reason == SequenceFailureReason.CONTACT_ALREADY_FAILED
                )

            elif preview_item.contact_id == contacts[3].id:
                # EXITED enrollment should be ineligible
                assert (
                    preview_item.eligibility == SequenceEnrollmentEligibility.INELIGIBLE
                )
                assert preview_item.can_enroll is False
                assert (
                    preview_item.reason == SequenceFailureReason.CONTACT_ALREADY_EXITED
                )

        # Try to create enrollments for the same contacts to verify the behavior matches the preview
        create_request = CreateSequenceEnrollmentRequest(
            sequence_id=sequence.id,
            contacts=[
                ContactForSequenceEnrollment(
                    contact_id=contacts[0].id,
                    account_id=accounts[0].id,
                    email="<EMAIL>",
                ),
                ContactForSequenceEnrollment(
                    contact_id=contacts[1].id,
                    account_id=accounts[1].id,
                    email="<EMAIL>",
                ),
                ContactForSequenceEnrollment(
                    contact_id=contacts[2].id,
                    account_id=accounts[2].id,
                    email="<EMAIL>",
                ),
                ContactForSequenceEnrollment(
                    contact_id=contacts[3].id,
                    account_id=accounts[3].id,
                    email="<EMAIL>",
                ),
            ],
        )

        create_response = await common_api_client.create_sequence_enrollment(
            create_request
        )

        # Only the INACTIVE contact should be enrolled successfully
        assert len(create_response.enrolled_contacts) == 1
        assert create_response.enrolled_contacts[0].contact_id == contacts[1].id

        # The other 3 contacts should fail with appropriate failure reasons
        assert len(create_response.failed_enrollments) == 3

        for failed in create_response.failed_enrollments:
            if failed.contact_id == contacts[0].id:
                assert (
                    SequenceFailureReason.CONTACT_ALREADY_ENROLLED
                    in failed.failure_reasons
                )
            elif failed.contact_id == contacts[2].id:
                assert (
                    SequenceFailureReason.CONTACT_ALREADY_FAILED
                    in failed.failure_reasons
                )
            elif failed.contact_id == contacts[3].id:
                assert (
                    SequenceFailureReason.CONTACT_ALREADY_EXITED
                    in failed.failure_reasons
                )


async def test_create_sequence_enrollment_run_and_enrollments_from_contacts(
    common_api_client: CommonAPIClient,
    sequence_factory: SequenceFactory,
    sequence_enrollment_service: SequenceEnrollmentService,
    sequence_enrollment_repo: SequenceEnrollmentRepository,
    contact_stage_select_list: SelectListDetails,
    email_account_pool_repository: EmailAccountPoolRepository,
    email_account_repository: EmailAccountRepository,
) -> None:
    """Test creating sequence enrollment run and enrollments from contacts."""
    # Setup test data
    user_id, organization_id = (
        common_api_client.default_user_id,
        common_api_client.default_organization_id,
    )

    # Create a real email account pool in the database
    email_pool_id = uuid4()
    email_account_pool = EmailAccountPool(
        id=email_pool_id,
        organization_id=organization_id,
        name="Test Email Account Pool for Enrollment Run",
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
        is_default=True,
        owner_user_id=user_id,
    )
    await email_account_pool_repository.insert(email_account_pool)

    # Create real email account with warmup override
    email_account = EmailAccount(
        id=uuid4(),
        owner_user_id=user_id,
        email="<EMAIL>",
        type=EmailAccountType.OUTBOUND,
        active=True,
        is_default=True,
        seconds_delay_between_emails=60,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
        use_override=EmailAccountUseOverride.USE_DESPITE_WARMUP_STATUS,
    )
    await email_account_repository.insert(email_account)

    # Add the email account to the pool
    email_account_pool_membership = EmailAccountPoolMembership(
        id=uuid4(),
        email_account_pool_id=email_pool_id,
        email_account_id=email_account.id,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
    )
    await email_account_repository.insert(email_account_pool_membership)

    # Create an email account pool response with the ID of the pool we just created
    email_pool_response = EmailAccountPoolResponse(
        id=email_pool_id,
        name="Default Test Pool",
        description="Mock default email account pool for testing",
        organization_id=organization_id,
        created_at=str(zoned_utc_now()),
        created_by_user_id=user_id,
    )

    # Use unittest.mock.patch to mock the EmailAccountPoolService dependency at the correct location
    with patch(
        "salestech_be.core.sequence.service.sequence_enrollment_service.EmailAccountPoolService.get_default_email_account_pool",
        return_value=email_pool_response,
    ):
        # Get first stage for contacts
        first_stage = contact_stage_select_list.select_list_values[0]

        # Create test sequence
        sequence = sequence_factory.build(
            organization_id=organization_id,
            created_by_user_id=user_id,
        )

        # Create another sequence to test warnings about contacts enrolled in other sequences
        other_sequence = sequence_factory.build(
            organization_id=organization_id,
            created_by_user_id=user_id,
        )

        # Add steps to both sequences - required for enrollment to work
        main_sequence_step = SequenceStepV2(
            id=uuid4(),
            organization_id=organization_id,
            sequence_id=sequence.id,
            name="Test Step 1",
            type=SequenceStepType.AUTO_EMAIL,
            delay_minutes=0,
            is_first_step=True,
            support_ab_test=False,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )
        await sequence_enrollment_repo.insert(main_sequence_step)

        # Create a step for the other sequence
        other_sequence_step = SequenceStepV2(
            id=uuid4(),
            organization_id=organization_id,
            sequence_id=other_sequence.id,
            name="Test Step 1",
            type=SequenceStepType.AUTO_EMAIL,
            delay_minutes=0,
            is_first_step=True,
            support_ab_test=False,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )
        await sequence_enrollment_repo.insert(other_sequence_step)

        # Create test accounts and contacts with different scenarios
        # 1. Regular contact that should enroll successfully
        account1 = await common_api_client.create_account(
            CreateAccountRequest(
                display_name="Test Account 1",
                owner_user_id=user_id,
            )
        )
        contact1 = await common_api_client.create_contact_with_contact_channel(
            organization_id=organization_id,
            user_id=user_id,
            create_request=CreateContactWithContactChannelRequest(
                contact=CreateContactInfoRequest(
                    first_name="Enrollment Run Test 1",
                    last_name="Contact",
                    owner_user_id=user_id,
                    display_name="Enrollment Run Test 1",
                    created_by_user_id=user_id,
                    stage_id=first_stage.id,
                ),
                contact_emails=[
                    CreateDbContactEmailRequest(
                        email="<EMAIL>",
                        is_contact_primary=True,
                        email_account_associations=[
                            CreateDbContactEmailAccountAssociationRequest(
                                account_id=account1.id,
                                is_contact_account_primary=True,
                            )
                        ],
                    ),
                ],
                account_roles=[
                    CreateContactAccountRoleRequest(
                        account_id=account1.id,
                        is_primary_account=True,
                    )
                ],
            ),
        )

        # 2. Contact already enrolled in another sequence (should generate warning)
        account2 = await common_api_client.create_account(
            CreateAccountRequest(
                display_name="Test Account 2",
                owner_user_id=user_id,
            )
        )
        contact2 = await common_api_client.create_contact_with_contact_channel(
            organization_id=organization_id,
            user_id=user_id,
            create_request=CreateContactWithContactChannelRequest(
                contact=CreateContactInfoRequest(
                    first_name="Enrollment Run Test 2",
                    last_name="Contact",
                    owner_user_id=user_id,
                    display_name="Enrollment Run Test 2",
                    created_by_user_id=user_id,
                    stage_id=first_stage.id,
                ),
                contact_emails=[
                    CreateDbContactEmailRequest(
                        email="<EMAIL>",
                        is_contact_primary=True,
                        email_account_associations=[
                            CreateDbContactEmailAccountAssociationRequest(
                                account_id=account2.id,
                                is_contact_account_primary=True,
                            )
                        ],
                    ),
                ],
                account_roles=[
                    CreateContactAccountRoleRequest(
                        account_id=account2.id,
                        is_primary_account=True,
                    )
                ],
            ),
        )

        # Enroll contact2 in the other sequence
        await common_api_client.create_sequence_enrollment(
            CreateSequenceEnrollmentRequest(
                sequence_id=other_sequence.id,
                contacts=[
                    ContactForSequenceEnrollment(
                        contact_id=contact2.id,
                        account_id=not_none(contact2.primary_account_id),
                        email=not_none(contact2.primary_email),
                    ),
                ],
            )
        )

        # 3. Contact without account (should be ineligible)
        contact3 = await common_api_client.create_contact_with_contact_channel(
            organization_id=organization_id,
            user_id=user_id,
            create_request=CreateContactWithContactChannelRequest(
                contact=CreateContactInfoRequest(
                    first_name="Enrollment Run Test 3",
                    last_name="Contact",
                    owner_user_id=user_id,
                    display_name="Enrollment Run Test 3",
                    created_by_user_id=user_id,
                    stage_id=first_stage.id,
                ),
                contact_emails=[
                    CreateDbContactEmailRequest(
                        email="<EMAIL>",
                        is_contact_primary=True,
                    ),
                ],
            ),
        )

        # Create a sequence enrollment run with a predefined ID
        custom_enrollment_run_id = uuid4()

        # Test Case 1: Without bypass_warnings
        contacts_for_enrollment = [
            ContactForSequenceEnrollment(
                contact_id=contact1.id,
                account_id=not_none(contact1.primary_account_id),
                email=not_none(contact1.primary_email),
            ),
            ContactForSequenceEnrollment(
                contact_id=contact2.id,
                account_id=not_none(contact2.primary_account_id),
                email=not_none(contact2.primary_email),
            ),
            ContactForSequenceEnrollment(
                contact_id=contact3.id,
                email=not_none(contact3.primary_email),
            ),
        ]

        response = await sequence_enrollment_service.create_sequence_enrollment_run_and_enrollments_from_contacts(
            user_id=user_id,
            organization_id=organization_id,
            contacts=contacts_for_enrollment,
            sequence_id=sequence.id,
            bypass_warnings=False,
            enrollment_run_id=custom_enrollment_run_id,
        )

        # Verify response
        assert response.enrollment_run_id == custom_enrollment_run_id
        assert len(response.enrollment_contacts) == 3

        # Count by status
        enrolled_count = 0
        warning_count = 0
        failed_count = 0

        contact_status_map = {}
        for contact in response.enrollment_contacts:
            contact_status_map[contact.contact_id] = contact.status
            if contact.status == SequenceEnrollmentContactStatus.ENROLLED:
                enrolled_count += 1
            elif contact.status == SequenceEnrollmentContactStatus.WARNING:
                warning_count += 1
            elif contact.status == SequenceEnrollmentContactStatus.FAILED:
                failed_count += 1

        # Verify status counts
        assert enrolled_count == 1  # Only contact1 should be enrolled without warnings
        assert warning_count == 1  # contact2 should have warning but not be enrolled
        assert failed_count == 1  # contact3 should fail due to missing account

        # Verify specific statuses
        assert (
            contact_status_map[contact1.id] == SequenceEnrollmentContactStatus.ENROLLED
        )
        assert (
            contact_status_map[contact2.id] == SequenceEnrollmentContactStatus.WARNING
        )
        assert contact_status_map[contact3.id] == SequenceEnrollmentContactStatus.FAILED

        # Verify database state - should only have one enrollment
        db_enrollments = await sequence_enrollment_repo.find_all_sequence_enrollments_by_sequence_id_and_organization_id(
            sequence_id=sequence.id,
            organization_id=organization_id,
        )
        assert len(db_enrollments) == 1

        # Test Case 2: With bypass_warnings
        custom_enrollment_run_id_2 = uuid4()
        _ = await sequence_enrollment_service.create_sequence_enrollment_run_and_enrollments_from_contacts(
            user_id=user_id,
            organization_id=organization_id,
            contacts=contacts_for_enrollment,
            sequence_id=sequence.id,
            bypass_warnings=True,
            enrollment_run_id=custom_enrollment_run_id_2,
        )

        # Verify database state - should now have more enrollments
        db_enrollments_2 = await sequence_enrollment_repo.find_all_sequence_enrollments_by_sequence_id_and_organization_id(
            sequence_id=sequence.id,
            organization_id=organization_id,
        )
        # contact1 and contact2 enrolled now (with bypass_warnings=True), contact3 still fails due to ineligibility
        assert len(db_enrollments_2) == 2
        # Check that the right contacts were enrolled
        assert contact3.id not in [e.contact_id for e in db_enrollments_2]
        assert contact1.id in [e.contact_id for e in db_enrollments_2]
        assert contact2.id in [e.contact_id for e in db_enrollments_2]

        # verify enrollment run in database
        from salestech_be.db.models.sequence import SequenceEnrollmentRun

        db_enrollment_run = await sequence_enrollment_repo.find_by_tenanted_primary_key(
            table_model=SequenceEnrollmentRun,
            organization_id=organization_id,
            id=custom_enrollment_run_id_2,
        )
        assert db_enrollment_run is not None
        assert db_enrollment_run.status == SequenceEnrollmentRunStatus.COMPLETED
