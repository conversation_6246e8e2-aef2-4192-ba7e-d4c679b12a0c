from uuid import UUID, uuid4

import pytest
from faker import Faker
from fastapi import FastAPI
from httpx import AsyncClient
from pytest_mock import Mocker<PERSON>ixture
from starlette import status
from temporalio.testing import WorkflowEnvironment

from salestech_be.common.type.metadata.common import ObjectAccessStatus
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.crm_integrity.data_operation_tracker.integrity_job_service import (
    IntegrityJobService,
)
from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
    get_affected_sub_domains_by_integrity_job_name,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    CreateCRMIntegrityJobResponse,
    IntegrityJobPreviewResponse,
    RetryCRMIntegrityJobResponse,
)
from salestech_be.core.domain_object_list.types import (
    DomainObjectList,
)
from salestech_be.core.meeting.dto.meeting_dto import MeetingDto
from salestech_be.core.meeting.dto.meeting_insight_dto import InsightDTO
from salestech_be.core.metadata.dto.select_list_dto import PipelineStageDto
from salestech_be.core.pipeline.service.pipeline_service import PipelineService
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.sequence.type.sequence_v2 import SequenceV2
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.crm_integrity_repository import CRMIntegrityJobRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.user_calendar_event_dto import HydratedUserCalendarEventDto
from salestech_be.db.models.account import Account
from salestech_be.db.models.contact import Contact, ContactEmail
from salestech_be.db.models.contact_email import (
    ContactEmailAccountAssociation,
)
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityAssociatedEntityOperation,
    CRMIntegrityJob,
    CRMIntegrityOperation,
    CRMIntegritySubDomainJob,
    CRMSubDomain,
    EntityType,
    IntegrityJobName,
    IntegrityJobType,
    JobStatus,
    MoveContactToAccountContextualParam,
    MoveContactToAccountUserOption,
)
from salestech_be.db.models.global_thread import GlobalMessage, GlobalThread
from salestech_be.db.models.intel_company_association import IntelCompanyAssociation
from salestech_be.db.models.intel_person_association import IntelPersonAssociation
from salestech_be.db.models.meeting import Meeting
from salestech_be.db.models.message import Message
from salestech_be.db.models.note import Note
from salestech_be.db.models.thread import Thread
from salestech_be.db.models.voice_v2 import Call
from salestech_be.services.auth.tokens import create_access_token
from salestech_be.services.auth.types import ReevoJWTClaims
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.common.container import (
    PaginatedListResponse,
)
from salestech_be.web.api.crm_integrity.schema import (
    IntegrityJobDetails,
)
from tests.integration.core.crm_integrity.test_add_account_to_pipeline_workflow import (
    AddAccountToPipelineWorkflowBaseFixtures,
)
from tests.integration.core.crm_integrity.test_archive_account_workflow import (
    ArchiveAccountWorkflowBaseFixtures,
)
from tests.integration.core.crm_integrity.test_archive_contact_workflow import (
    ArchiveContactWorkflowBaseFixtures,
)
from tests.integration.core.crm_integrity.test_merge_accounts_workflow import (
    MergeAccountsWorkflowBaseFixtures,
)
from tests.integration.core.crm_integrity.test_merge_contacts_workflow import (
    MergeContactsWorkflowBaseFixtures,
)
from tests.integration.core.crm_integrity.test_move_contact_email_to_account_workflow import (
    MoveContactEmailToAccountWorkflowBaseFixtures,
)
from tests.integration.core.crm_integrity.test_move_contact_email_to_contact_workflow import (
    MoveContactEmailToContactWorkflowBaseFixtures,
)
from tests.integration.core.crm_integrity.test_move_contact_to_account_workflow import (
    MoveContactToAccountWorkflowBaseFixtures,
)
from tests.integration.core.crm_integrity.test_newly_move_contact_to_account_workflow import (
    NewlyMoveContactToAccountWorkflowBaseFixtures,
)
from tests.integration.core.crm_integrity.test_remove_contact_email_from_account_workflow import (
    RemoveContactEmailFromAccountWorkflowBaseFixtures,
)
from tests.integration.core.crm_integrity.test_remove_contact_email_from_contact_workflow import (
    RemoveContactEmailFromContactWorkflowBaseFixtures,
)
from tests.integration.core.crm_integrity.test_remove_contact_from_account_workflow import (
    RemoveContactFromAccountWorkflowBaseFixtures,
)
from tests.integration.web.api.util.common_api_client import CommonAPIClient


def get_request_headers(user_id: UUID, organization_id: UUID) -> dict[str, str]:
    return {
        "x-reevo-user-id": str(user_id),
        "x-reevo-org-id": str(organization_id),
        "accept": "application/json",
        "Content-Type": "application/json",
        "authorization": "Bearer "
        + create_access_token(
            claims=ReevoJWTClaims(
                sub=user_id,
                org=organization_id,
            )
        ),
    }


class TestCRMIntegrityJobControllerLifecycle(MoveContactToAccountWorkflowBaseFixtures):
    async def test_create_integrity_job(
        self,
        mocker: MockerFixture,
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        multi_api_client: dict[str, CommonAPIClient],
        account_1: AccountV2,
        account_2: AccountV2,
        contact_1: ContactV2,
        contact_2: ContactV2,
        pipeline_1: PipelineV2,
        meeting: Meeting,
        task: TaskV2,
        note: Note,
        email_thread: Thread,
        email_message: Message,
        global_thread: GlobalThread,
        global_message: GlobalMessage,
        sequence: SequenceV2,
        voice_call: Call,
        calendar_event: HydratedUserCalendarEventDto,
    ) -> None:
        mocker.patch("temporalio.client.Client.start_workflow", return_value=None)

        admin_api_client = multi_api_client["admin"]
        # test list user options
        list_user_options_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("list_integrity_job_user_options"),
            headers=get_request_headers(self.user_id, self.organization_id),
        )
        assert list_user_options_response.status_code == status.HTTP_200_OK

        # create job
        create_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_2.id),
                "contextual_param": {
                    "src_account_id": str(account_1.id),
                },
                "user_choices": dict.fromkeys(
                    get_affected_sub_domains_by_integrity_job_name(
                        integrity_job_name=IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT
                    ),
                    MoveContactToAccountUserOption.RETAIN,
                ),
                "start_immediately": False,
            },
        )
        assert create_response.status_code == status.HTTP_200_OK
        create_job_response = CreateCRMIntegrityJobResponse.model_validate_json(
            create_response.text
        )
        assert create_job_response.job_id is not None

        # test list job
        list_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("list_integrity_jobs"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={},
        )
        assert list_response.status_code == status.HTTP_200_OK
        list_job_response = PaginatedListResponse[
            IntegrityJobDetails
        ].model_validate_json(list_response.text)
        assert len(list_job_response.list_data) == 1
        assert list_job_response.list_data[0].job_status == JobStatus.NEW
        assert list_job_response.list_data[0].retry_count == 0
        assert list_job_response.list_data[0].error_details is None
        assert list_job_response.list_data[0].created_by_user_display_name is not None
        assert list_job_response.list_data[0].deleted_at is None
        assert len(list_job_response.list_data[0].contacts) == 1
        assert list_job_response.list_data[0].contacts[0].id == contact_1.id
        assert len(list_job_response.list_data[0].accounts) == 2
        assert {
            list_job_response.list_data[0].accounts[0].id,
            list_job_response.list_data[0].accounts[1].id,
        } == {account_1.id, account_2.id}

        # test patch job
        patch_response = await admin_api_client.api_test_client.client.patch(
            fastapi_app.url_path_for(
                "patch_integrity_job", job_id=create_job_response.job_id
            ),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "user_choices": dict.fromkeys(
                    get_affected_sub_domains_by_integrity_job_name(
                        integrity_job_name=IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT
                    ),
                    MoveContactToAccountUserOption.MOVE,
                ),
            },
        )
        assert patch_response.status_code == status.HTTP_200_OK

        # test get job
        get_response = await admin_api_client.api_test_client.client.get(
            fastapi_app.url_path_for(
                "get_integrity_job_details", job_id=create_job_response.job_id
            ),
            headers=get_request_headers(self.user_id, self.organization_id),
        )
        assert get_response.status_code == status.HTTP_200_OK
        get_job_details_response = IntegrityJobDetails.model_validate_json(
            get_response.text
        )
        assert get_job_details_response.job_status == JobStatus.NEW
        assert get_job_details_response.retry_count == 0
        assert get_job_details_response.error_details is None
        assert get_job_details_response.created_by_user_display_name is not None
        assert get_job_details_response.deleted_at is None
        assert get_job_details_response.user_choices == dict.fromkeys(
            get_affected_sub_domains_by_integrity_job_name(
                integrity_job_name=IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT
            ),
            MoveContactToAccountUserOption.MOVE,
        )
        assert len(get_job_details_response.contacts) == 1
        assert get_job_details_response.contacts[0].id == contact_1.id
        assert len(get_job_details_response.accounts) == 2
        assert {
            get_job_details_response.accounts[0].id,
            get_job_details_response.accounts[1].id,
        } == {account_1.id, account_2.id}

        # test delete job
        delete_response = await admin_api_client.api_test_client.client.delete(
            fastapi_app.url_path_for(
                "delete_integrity_job", job_id=create_job_response.job_id
            ),
            headers=get_request_headers(self.user_id, self.organization_id),
        )
        assert delete_response.status_code == status.HTTP_204_NO_CONTENT

        list_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("list_integrity_jobs"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={},
        )
        assert list_response.status_code == status.HTTP_200_OK
        list_job_response = PaginatedListResponse[
            IntegrityJobDetails
        ].model_validate_json(list_response.text)
        assert len(list_job_response.list_data) == 0


class TestCRMIntegrityGetJobDetailsController(MergeAccountsWorkflowBaseFixtures):
    @pytest.fixture(scope="function")
    async def integrity_job(
        self,
        integrity_job_service: IntegrityJobService,
        account_1: AccountV2,
        account_2: AccountV2,
        integrity_job_subdomains: list[CRMSubDomain],
    ) -> CRMIntegrityJob:
        integrity_job, _ = await integrity_job_service.create_integrity_job(
            user_id=self.user_id,
            organization_id=self.organization_id,
            job_type=IntegrityJobType.MERGE,
            src_entity_type=EntityType.ACCOUNT,
            src_entity_id=account_1.id,
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=account_2.id,
            user_choices=dict.fromkeys(integrity_job_subdomains),
        )

        return integrity_job

    async def test_get_integrity_job_details(
        self,
        mocker: MockerFixture,
        _engine: DatabaseEngine,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        account_2: AccountV2,
        account_3: AccountV2,
        contact_1: ContactV2,
        contact_2: ContactV2,
        contact_3: ContactV2,
        contact_4: ContactV2,
        pipeline_1: PipelineV2,
        intel_company_association_account_1: IntelCompanyAssociation,
        intel_company_association_account_2: IntelCompanyAssociation,
        task: TaskV2,
        note: Note,
        voice_call: Call,
        email_thread: Thread,
        email_message: Message,
        global_message: GlobalMessage,
        global_thread: GlobalThread,
        meeting: Meeting,
        integrity_job: CRMIntegrityJob,
        integrity_job_subdomains: list[CRMSubDomain],
        crm_integrity_job_repository: CRMIntegrityJobRepository,
        wf_env: WorkflowEnvironment,
    ) -> None:
        job_id = await self.execute_merge_accounts_workflow(
            _engine=_engine,
            integrity_job=integrity_job,
            wf_env=wf_env,
        )

        admin_api_client = multi_api_client["admin"]

        get_response = await admin_api_client.api_test_client.client.get(
            fastapi_app.url_path_for("get_integrity_job_details", job_id=job_id),
            headers=get_request_headers(self.user_id, self.organization_id),
        )

        assert get_response.status_code == status.HTTP_200_OK
        get_job_details_response = IntegrityJobDetails.model_validate_json(
            get_response.text
        )
        assert get_job_details_response.job_id == job_id
        assert get_job_details_response.job_status == JobStatus.SUCCESS
        assert get_job_details_response.retry_count == 0
        assert get_job_details_response.error_details is None
        assert get_job_details_response.src_entity_type == EntityType.ACCOUNT
        assert get_job_details_response.src_entity_id == account_1.id
        assert get_job_details_response.dest_entity_type == EntityType.ACCOUNT
        assert get_job_details_response.dest_entity_id == account_2.id
        assert get_job_details_response.organization_id == self.organization_id
        assert get_job_details_response.created_by_user_display_name is not None
        assert get_job_details_response.deleted_at is None
        assert get_job_details_response.deleted_by_user_id is None
        assert get_job_details_response.started_at
        assert get_job_details_response.ended_at

        assert get_job_details_response.operations

        db_operations = await crm_integrity_job_repository._find_by_column_values(
            table_model=CRMIntegrityOperation,
            integrity_job_id=job_id,
            organization_id=self.organization_id,
        )

        db_operations_ids = {operation.id for operation in db_operations}
        assert db_operations_ids == {
            operation.id for operation in get_job_details_response.operations
        }

        for sub_domain_job in get_job_details_response.sub_domain_jobs:
            if sub_domain_job.associated_data_operations:
                db_associated_entity_operations = (
                    await crm_integrity_job_repository._find_by_column_values(
                        table_model=CRMIntegrityAssociatedEntityOperation,
                        integrity_job_id=job_id,
                        integrity_subdomain_job_id=sub_domain_job.id,
                        organization_id=self.organization_id,
                    )
                )

                db_associated_entity_operations_ids = {
                    associated_entity_operation.id
                    for associated_entity_operation in db_associated_entity_operations
                }
                assert db_associated_entity_operations_ids == {
                    associated_entity_operation.id
                    for associated_entity_operation in sub_domain_job.associated_data_operations
                }

        assert get_job_details_response.contacts == []
        assert {account.id for account in get_job_details_response.accounts} == {
            account_1.id,
            account_2.id,
        }
        assert get_job_details_response.contact_emails_lite == []


class TestCRMIntegrityConcurrencyConflicts(MoveContactToAccountWorkflowBaseFixtures):
    async def test_conflicts_integrity_job(
        self,
        mocker: MockerFixture,
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        multi_api_client: dict[str, CommonAPIClient],
        account_1: AccountV2,
        account_2: AccountV2,
        contact_1: ContactV2,
        contact_repo: ContactRepository,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        mocker.patch("temporalio.client.Client.start_workflow", return_value=None)

        admin_api_client = multi_api_client["admin"]

        created_new_job_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_2.id),
                "contextual_param": {
                    "src_account_id": str(account_1.id),
                },
                "start_immediately": False,
            },
        )

        assert created_new_job_response.status_code == status.HTTP_200_OK
        created_new_job = CreateCRMIntegrityJobResponse.model_validate_json(
            created_new_job_response.text
        )

        db_created_new_job = (
            await crm_integrity_job_repository.find_by_primary_key_or_fail(
                table_model=CRMIntegrityJob,
                id=created_new_job.job_id,
            )
        )
        # should be NEW since the job is not actually started in mock
        assert db_created_new_job.status == JobStatus.NEW

        # mock the contact is in running integrity job
        assert await contact_repo.update_by_primary_key(
            table_model=Contact,
            primary_key_to_value={"id": contact_1.id},
            column_to_update={"integrity_job_started_at": zoned_utc_now()},
        )

        preview_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("preview_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_2.id),
                "contextual_param": {
                    "src_account_id": str(account_1.id),
                },
            },
        )

        assert preview_response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            f"contact {contact_1.id} has running integrity job" in preview_response.text
        )

        created_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_2.id),
                "contextual_param": {
                    "src_account_id": str(account_1.id),
                },
            },
        )

        assert created_response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            f"contact {contact_1.id} has running integrity job" in created_response.text
        )

        start_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for(
                "start_integrity_job", job_id=created_new_job.job_id
            ),
            headers=get_request_headers(self.user_id, self.organization_id),
        )
        assert start_response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            f"contact {contact_1.id} has running integrity job" in start_response.text
        )


class TestCRMIntegrityRetryJobController(ArchiveContactWorkflowBaseFixtures):
    async def test_retry_job(
        self,
        _engine: DatabaseEngine,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        contact_1: ContactV2,
        email_message: Message,
        email_thread: Thread,
        global_thread: GlobalThread,
        global_message: GlobalMessage,
        sequence: SequenceV2,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
        integrity_job: CRMIntegrityJob,
    ) -> None:
        job_id = await self.execute_archive_contact_workflow(
            _engine=_engine,
            integrity_job=integrity_job,
        )
        admin_api_client = multi_api_client["admin"]

        # manually set contact back and update job to FAIL status
        updated_status_job = await crm_integrity_job_repository.update_by_primary_key(
            table_model=CRMIntegrityJob,
            primary_key_to_value={"id": job_id},
            column_to_update={"status": JobStatus.FAIL},
        )
        assert updated_status_job and updated_status_job.status == JobStatus.FAIL

        updated_contact = await crm_integrity_job_repository.update_by_primary_key(
            table_model=Contact,
            exclude_deleted_or_archived=False,
            primary_key_to_value={"id": contact_1.id},
            column_to_update={"archived_at": None, "archived_by_user_id": None},
        )
        assert updated_contact and updated_contact.archived_at is None

        # retry job
        retry_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("retry_integrity_job", job_id=job_id),
            headers=get_request_headers(self.user_id, self.organization_id),
        )

        assert retry_response.status_code == status.HTTP_200_OK
        retry_job_response = RetryCRMIntegrityJobResponse.model_validate_json(
            retry_response.text
        )
        assert retry_job_response.job_id == job_id

        updated_retry_job = (
            await crm_integrity_job_repository.find_by_primary_key_or_fail(
                table_model=CRMIntegrityJob,
                id=job_id,
            )
        )

        assert updated_retry_job.status == JobStatus.QUEUED
        assert updated_retry_job.retry_count == 1


class TestCRMIntegrityConflictMoveContactToAccountCase(
    NewlyMoveContactToAccountWorkflowBaseFixtures
):
    @pytest.fixture(scope="function")
    async def integrity_job(
        self,
        contact_1: ContactV2,
        account_1: AccountV2,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
        account_repository: AccountRepository,
    ) -> CRMIntegrityJob:
        created_integrity_job = await crm_integrity_job_repository.insert(
            CRMIntegrityJob(
                id=uuid4(),
                type=IntegrityJobType.MOVE,
                status=JobStatus.RUNNING,
                src_entity_type=EntityType.CONTACT,
                src_entity_id=uuid4(),
                dest_entity_type=EntityType.ACCOUNT,
                dest_entity_id=account_1.id,
                contextual_param=MoveContactToAccountContextualParam(
                    src_account_id=None,
                ),
                created_at=zoned_utc_now(),
                created_by_user_id=self.user_id,
                organization_id=self.organization_id,
            )
        )

        patched_account = await account_repository.update_by_primary_key(
            table_model=Account,
            primary_key_to_value={"id": account_1.id},
            column_to_update={
                "integrity_job_started_at": zoned_utc_now(),
                "integrity_job_started_by_user_id": self.user_id,
                "integrity_job_started_by_job_ids": [created_integrity_job.id],
            },
        )
        assert patched_account
        assert patched_account.access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING
        assert patched_account and patched_account.integrity_job_started_at
        assert patched_account.integrity_job_started_by_user_id == self.user_id
        assert patched_account.integrity_job_started_by_job_ids == [
            created_integrity_job.id
        ]

        return created_integrity_job

    async def test_conflict_move_contact_to_account_preview(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        contact_1: ContactV2,
        integrity_job: CRMIntegrityJob,
        account_service: AccountService,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        # confirm the state of account before preview
        account_1 = await account_service.get_account_v2(
            account_id=account_1.id,
            organization_id=self.organization_id,
        )
        assert account_1.access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING
        assert account_1.integrity_job_started_at
        assert account_1.integrity_job_started_by_user_id == self.user_id
        assert account_1.integrity_job_started_by_job_ids == [integrity_job.id]

        preview_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("preview_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_1.id),
                "contextual_param": {
                    "src_account_id": None,
                },
            },
        )

        # expect to be OK although the account has running move contact integrity job
        assert preview_response_raw.status_code == status.HTTP_200_OK
        preview_response = IntegrityJobPreviewResponse.model_validate_json(
            preview_response_raw.text
        )
        assert len(preview_response.associated_data_changes_identifiers) == 0

    async def test_conflict_move_contact_to_account_creation(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        contact_1: ContactV2,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
        account_service: AccountService,
        integrity_job: CRMIntegrityJob,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        # confirm the state of account before preview
        account_1 = await account_service.get_account_v2(
            account_id=account_1.id,
            organization_id=self.organization_id,
        )
        assert account_1.access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING
        assert account_1.integrity_job_started_at
        assert account_1.integrity_job_started_by_user_id == self.user_id
        assert account_1.integrity_job_started_by_job_ids == [integrity_job.id]

        affected_sub_domains = get_affected_sub_domains_by_integrity_job_name(
            integrity_job_name=IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT
        )

        user_choices = dict.fromkeys(
            affected_sub_domains, MoveContactToAccountUserOption.RETAIN
        )

        # create case with user choices
        create_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_1.id),
                "contextual_param": {
                    "src_account_id": None,
                },
                "user_choices": user_choices,
                "start_immediately": False,
            },
        )

        # expect to be OK although the account has running move contact integrity job
        assert create_response.status_code == status.HTTP_200_OK
        create_job_response = CreateCRMIntegrityJobResponse.model_validate_json(
            create_response.text
        )

        sub_domain_jobs = await crm_integrity_job_repository._find_by_column_values(
            table_model=CRMIntegritySubDomainJob,
            id=create_job_response.subdomain_job_ids,
            organization_id=self.organization_id,
        )
        assert len(sub_domain_jobs) == len(affected_sub_domains)
        for sub_domain_job in sub_domain_jobs:
            assert sub_domain_job.user_choice == user_choices[sub_domain_job.subdomain]


class TestCRMIntegrityArchiveContactCreationAndPreview(
    ArchiveContactWorkflowBaseFixtures
):
    async def test_archive_contact_preview(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        contact_1: ContactV2,
        email_message: Message,
        email_thread: Thread,
        global_thread: GlobalThread,
        global_message: GlobalMessage,
        sequence: SequenceV2,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        preview_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("preview_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.ARCHIVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
            },
        )

        assert preview_response_raw.status_code == status.HTTP_200_OK

        preview_response = IntegrityJobPreviewResponse.model_validate_json(
            preview_response_raw.text
        )
        assert len(preview_response.associated_data_changes_identifiers) > 0

    async def test_archive_contact_creation(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        contact_1: ContactV2,
        email_message: Message,
        email_thread: Thread,
        global_thread: GlobalThread,
        global_message: GlobalMessage,
        sequence: SequenceV2,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        create_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.ARCHIVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "start_immediately": False,
            },
        )
        assert create_response_raw.status_code == status.HTTP_200_OK
        create_response = CreateCRMIntegrityJobResponse.model_validate_json(
            create_response_raw.text
        )
        assert create_response.job_id is not None

        db_crm_integrity_job = (
            await crm_integrity_job_repository.find_by_primary_key_or_fail(
                table_model=CRMIntegrityJob,
                id=create_response.job_id,
            )
        )
        assert db_crm_integrity_job.status == JobStatus.NEW


class TestCRMIntegrityArchiveAccountCreationAndPreview(
    ArchiveAccountWorkflowBaseFixtures
):
    async def test_archive_account_preview(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        preview_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("preview_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.ARCHIVE,
                "src_entity_type": EntityType.ACCOUNT,
                "src_entity_id": str(account_1.id),
            },
        )

        assert preview_response_raw.status_code == status.HTTP_200_OK

        preview_response = IntegrityJobPreviewResponse.model_validate_json(
            preview_response_raw.text
        )
        assert len(preview_response.associated_data_changes_identifiers) == 0

    async def test_archive_account_creation(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        create_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.ARCHIVE,
                "src_entity_type": EntityType.ACCOUNT,
                "src_entity_id": str(account_1.id),
                "start_immediately": False,
            },
        )
        assert create_response.status_code == status.HTTP_200_OK
        create_job_response = CreateCRMIntegrityJobResponse.model_validate_json(
            create_response.text
        )
        assert create_job_response.job_id is not None

        db_crm_integrity_job = (
            await crm_integrity_job_repository.find_by_primary_key_or_fail(
                table_model=CRMIntegrityJob,
                id=create_job_response.job_id,
            )
        )
        assert db_crm_integrity_job.status == JobStatus.NEW


class TestCRMIntegrityMergeContactsCreationAndPreview(
    MergeContactsWorkflowBaseFixtures
):
    async def test_merge_contacts_preview(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        account_2: AccountV2,
        account_3: AccountV2,
        contact_1: ContactV2,
        contact_2: ContactV2,
        pipeline_1: PipelineV2,
        task_1: TaskV2,
        email_message: Message,
        email_thread: Thread,
        meeting: MeetingDto,
        meeting_insight: InsightDTO,
        note: Note,
        voice_call: Call,
        global_message: GlobalMessage,
        global_thread: GlobalThread,
        intel_person_association_contact_1: IntelPersonAssociation,
        intel_person_association_contact_2: IntelPersonAssociation,
        sequence_1_with_contact_1: SequenceV2,
        sequence_2_with_contact_1_and_contact_2: SequenceV2,
        calendar_event: HydratedUserCalendarEventDto,
        domain_object_list_1_with_contact_1: DomainObjectList,
        domain_object_list_2_with_contact_1_and_contact_2: DomainObjectList,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        preview_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("preview_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MERGE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.CONTACT,
                "dest_entity_id": str(contact_2.id),
            },
        )
        assert preview_response_raw.status_code == status.HTTP_200_OK
        preview_response = IntegrityJobPreviewResponse.model_validate_json(
            preview_response_raw.text
        )
        assert len(preview_response.associated_data_changes_identifiers) > 0

    async def test_merge_contacts_creation(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        account_2: AccountV2,
        account_3: AccountV2,
        contact_1: ContactV2,
        contact_2: ContactV2,
        pipeline_1: PipelineV2,
        task_1: TaskV2,
        email_message: Message,
        email_thread: Thread,
        meeting: MeetingDto,
        meeting_insight: InsightDTO,
        note: Note,
        voice_call: Call,
        global_message: GlobalMessage,
        global_thread: GlobalThread,
        intel_person_association_contact_1: IntelPersonAssociation,
        intel_person_association_contact_2: IntelPersonAssociation,
        sequence_1_with_contact_1: SequenceV2,
        sequence_2_with_contact_1_and_contact_2: SequenceV2,
        calendar_event: HydratedUserCalendarEventDto,
        domain_object_list_1_with_contact_1: DomainObjectList,
        domain_object_list_2_with_contact_1_and_contact_2: DomainObjectList,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        create_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MERGE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.CONTACT,
                "dest_entity_id": str(contact_2.id),
                "start_immediately": False,
            },
        )
        assert create_response_raw.status_code == status.HTTP_200_OK
        create_response = CreateCRMIntegrityJobResponse.model_validate_json(
            create_response_raw.text
        )
        assert create_response.job_id is not None

        db_crm_integrity_job = (
            await crm_integrity_job_repository.find_by_primary_key_or_fail(
                table_model=CRMIntegrityJob,
                id=create_response.job_id,
            )
        )
        assert db_crm_integrity_job.status == JobStatus.NEW


class TestCRMIntegrityMergeAccountsCreationAndPreview(
    MergeAccountsWorkflowBaseFixtures
):
    async def test_merge_accounts_preview(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        account_2: AccountV2,
        account_3: AccountV2,
        contact_1: ContactV2,
        contact_2: ContactV2,
        contact_3: ContactV2,
        contact_4: ContactV2,
        pipeline_1: PipelineV2,
        intel_company_association_account_1: IntelCompanyAssociation,
        intel_company_association_account_2: IntelCompanyAssociation,
        task: TaskV2,
        note: Note,
        voice_call: Call,
        email_thread: Thread,
        email_message: Message,
        global_message: GlobalMessage,
        global_thread: GlobalThread,
        meeting: Meeting,
        domain_object_list_1_with_account_1: DomainObjectList,
        domain_object_list_2_with_account_1_and_account_2: DomainObjectList,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        preview_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("preview_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MERGE,
                "src_entity_type": EntityType.ACCOUNT,
                "src_entity_id": str(account_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_2.id),
            },
        )
        assert preview_response_raw.status_code == status.HTTP_200_OK
        preview_response = IntegrityJobPreviewResponse.model_validate_json(
            preview_response_raw.text
        )
        assert len(preview_response.associated_data_changes_identifiers) > 0

    async def test_merge_accounts_creation(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        account_2: AccountV2,
        account_3: AccountV2,
        contact_1: ContactV2,
        contact_2: ContactV2,
        contact_3: ContactV2,
        contact_4: ContactV2,
        pipeline_1: PipelineV2,
        intel_company_association_account_1: IntelCompanyAssociation,
        intel_company_association_account_2: IntelCompanyAssociation,
        task: TaskV2,
        note: Note,
        voice_call: Call,
        email_thread: Thread,
        email_message: Message,
        global_message: GlobalMessage,
        global_thread: GlobalThread,
        meeting: Meeting,
        domain_object_list_1_with_account_1: DomainObjectList,
        domain_object_list_2_with_account_1_and_account_2: DomainObjectList,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        create_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MERGE,
                "src_entity_type": EntityType.ACCOUNT,
                "src_entity_id": str(account_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_2.id),
                "start_immediately": False,
            },
        )
        assert create_response_raw.status_code == status.HTTP_200_OK
        create_response = CreateCRMIntegrityJobResponse.model_validate_json(
            create_response_raw.text
        )
        assert create_response.job_id is not None

        db_crm_integrity_job = (
            await crm_integrity_job_repository.find_by_primary_key_or_fail(
                table_model=CRMIntegrityJob,
                id=create_response.job_id,
            )
        )
        assert db_crm_integrity_job.status == JobStatus.NEW


class TestCRMIntegrityMoveContactToAccountCreationAndPreview(
    MoveContactToAccountWorkflowBaseFixtures
):
    async def test_move_contact_to_account_preview(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        account_2: AccountV2,
        contact_1: ContactV2,
        contact_2: ContactV2,
        pipeline_1: PipelineV2,
        meeting: Meeting,
        task: TaskV2,
        note: Note,
        email_thread: Thread,
        email_message: Message,
        global_thread: GlobalThread,
        global_message: GlobalMessage,
        sequence: SequenceV2,
        voice_call: Call,
        calendar_event: HydratedUserCalendarEventDto,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        preview_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("preview_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_2.id),
                "contextual_param": {
                    "src_account_id": str(account_1.id),
                },
            },
        )
        assert preview_response_raw.status_code == status.HTTP_200_OK
        preview_response = IntegrityJobPreviewResponse.model_validate_json(
            preview_response_raw.text
        )
        assert len(preview_response.associated_data_changes_identifiers) > 0

    async def test_move_contact_to_account_creation(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        account_2: AccountV2,
        contact_1: ContactV2,
        contact_2: ContactV2,
        pipeline_1: PipelineV2,
        meeting: Meeting,
        task: TaskV2,
        note: Note,
        email_thread: Thread,
        email_message: Message,
        global_thread: GlobalThread,
        global_message: GlobalMessage,
        sequence: SequenceV2,
        voice_call: Call,
        calendar_event: HydratedUserCalendarEventDto,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        affected_sub_domains = get_affected_sub_domains_by_integrity_job_name(
            integrity_job_name=IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT
        )

        user_choices = dict.fromkeys(
            affected_sub_domains, MoveContactToAccountUserOption.RETAIN
        )

        # create case with user choices
        create_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_2.id),
                "contextual_param": {
                    "src_account_id": str(account_1.id),
                },
                "user_choices": user_choices,
                "start_immediately": False,
            },
        )
        assert create_response.status_code == status.HTTP_200_OK
        create_job_response = CreateCRMIntegrityJobResponse.model_validate_json(
            create_response.text
        )

        sub_domain_jobs = await crm_integrity_job_repository._find_by_column_values(
            table_model=CRMIntegritySubDomainJob,
            id=create_job_response.subdomain_job_ids,
            organization_id=self.organization_id,
        )
        assert len(sub_domain_jobs) == len(affected_sub_domains)
        for sub_domain_job in sub_domain_jobs:
            assert sub_domain_job.user_choice == user_choices[sub_domain_job.subdomain]

        # create with unaffected domain
        create_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_2.id),
                "contextual_param": {
                    "src_account_id": str(account_1.id),
                },
                "user_choices": {
                    CRMSubDomain.INTELLIGENCE: MoveContactToAccountUserOption.RETAIN,
                },
                "start_immediately": False,
            },
        )
        assert create_response.status_code == status.HTTP_400_BAD_REQUEST

        # create with null user choices
        create_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_2.id),
                "contextual_param": {
                    "src_account_id": str(account_1.id),
                },
                "user_choices": {
                    CRMSubDomain.SEQUENCE: None,
                },
                "start_immediately": False,
            },
        )
        assert create_response.status_code == status.HTTP_400_BAD_REQUEST

        # create with incomplete user choices
        create_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_2.id),
                "contextual_param": {
                    "src_account_id": str(account_1.id),
                },
                "user_choices": {},
                "start_immediately": False,
            },
        )
        assert create_response.status_code == status.HTTP_200_OK
        create_job_response = CreateCRMIntegrityJobResponse.model_validate_json(
            create_response.text
        )

        sub_domain_jobs = await crm_integrity_job_repository._find_by_column_values(
            table_model=CRMIntegritySubDomainJob,
            id=create_job_response.subdomain_job_ids,
            organization_id=self.organization_id,
        )
        affected_sub_domains = get_affected_sub_domains_by_integrity_job_name(
            integrity_job_name=IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT
        )
        assert len(sub_domain_jobs) == len(affected_sub_domains)
        assert {sub_domain_job.user_choice for sub_domain_job in sub_domain_jobs} == {
            MoveContactToAccountUserOption.MOVE,
        }
        assert {sub_domain_job.subdomain for sub_domain_job in sub_domain_jobs} == set(
            affected_sub_domains
        )


class TestCRMIntegrityNewlyMoveContactToAccountCreationAndPreview(
    NewlyMoveContactToAccountWorkflowBaseFixtures
):
    async def test_newly_move_contact_to_account_preview(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        contact_1: ContactV2,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        preview_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("preview_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_1.id),
                "contextual_param": {
                    "src_account_id": None,
                },
            },
        )
        assert preview_response_raw.status_code == status.HTTP_200_OK
        preview_response = IntegrityJobPreviewResponse.model_validate_json(
            preview_response_raw.text
        )
        assert len(preview_response.associated_data_changes_identifiers) == 0

    async def test_newly_move_contact_to_account_creation(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        contact_1: ContactV2,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        affected_sub_domains = get_affected_sub_domains_by_integrity_job_name(
            integrity_job_name=IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT
        )

        user_choices = dict.fromkeys(
            affected_sub_domains, MoveContactToAccountUserOption.RETAIN
        )

        # create case with user choices
        create_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_1.id),
                "contextual_param": {
                    "src_account_id": None,
                },
                "user_choices": user_choices,
                "start_immediately": False,
            },
        )
        assert create_response.status_code == status.HTTP_200_OK
        create_job_response = CreateCRMIntegrityJobResponse.model_validate_json(
            create_response.text
        )

        sub_domain_jobs = await crm_integrity_job_repository._find_by_column_values(
            table_model=CRMIntegritySubDomainJob,
            id=create_job_response.subdomain_job_ids,
            organization_id=self.organization_id,
        )
        assert len(sub_domain_jobs) == len(affected_sub_domains)
        for sub_domain_job in sub_domain_jobs:
            assert sub_domain_job.user_choice == user_choices[sub_domain_job.subdomain]


class TestCRMIntegrityRemoveContactFromAccountCreationAndPreview(
    RemoveContactFromAccountWorkflowBaseFixtures
):
    async def test_remove_contact_from_account_preview(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        account_2: AccountV2,
        contact_1: ContactV2,
        contact_2: ContactV2,
        pipeline_1: PipelineV2,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        preview_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("preview_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.REMOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_1.id),
            },
        )
        assert preview_response_raw.status_code == status.HTTP_200_OK
        preview_response = IntegrityJobPreviewResponse.model_validate_json(
            preview_response_raw.text
        )
        assert len(preview_response.associated_data_changes_identifiers) == 0

    async def test_remove_contact_from_account_creation(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        account_1: AccountV2,
        account_2: AccountV2,
        contact_1: ContactV2,
        contact_2: ContactV2,
        pipeline_1: PipelineV2,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        create_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.REMOVE,
                "src_entity_type": EntityType.CONTACT,
                "src_entity_id": str(contact_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_1.id),
                "start_immediately": False,
            },
        )
        assert create_response.status_code == status.HTTP_200_OK
        create_job_response = CreateCRMIntegrityJobResponse.model_validate_json(
            create_response.text
        )

        db_crm_integrity_job = (
            await crm_integrity_job_repository.find_by_primary_key_or_fail(
                table_model=CRMIntegrityJob,
                id=create_job_response.job_id,
            )
        )
        assert db_crm_integrity_job.status == JobStatus.NEW


class TestCRMIntegrityMoveContactEmailToContactCreationAndPreview(
    MoveContactEmailToContactWorkflowBaseFixtures
):
    async def test_move_contact_email_to_contact_preview(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        contact_1: ContactV2,
        contact_2: ContactV2,
        email_1: str,
        account_1: AccountV2,
        account_2: AccountV2,
        contact_email_1: ContactEmail,
        pipeline_1: PipelineV2,
        email_message: Message,
        sequence: SequenceV2,
        email_thread: Thread,
        global_thread: GlobalThread,
        meeting: MeetingDto,
        calendar_event: HydratedUserCalendarEventDto,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        for option1, option2 in [
            (True, True),
            (True, False),
            (False, True),
            (False, False),
        ]:
            preview_response_raw = await admin_api_client.api_test_client.client.post(
                fastapi_app.url_path_for("preview_integrity_job"),
                headers=get_request_headers(self.user_id, self.organization_id),
                json={
                    "job_type": IntegrityJobType.MOVE,
                    "src_entity_type": EntityType.CONTACT_EMAIL,
                    "src_entity_id": str(contact_email_1.id),
                    "dest_entity_type": EntityType.CONTACT,
                    "dest_entity_id": str(contact_2.id),
                    "contextual_param": {
                        "src_contact_id": str(contact_1.id),
                        "remove_contact_account_association_if_last": option1,
                        "create_contact_account_association_if_missing": option2,
                    },
                },
            )

            assert preview_response_raw.status_code == status.HTTP_200_OK

            preview_response = IntegrityJobPreviewResponse.model_validate_json(
                preview_response_raw.text
            )

            assert len(preview_response.associated_data_changes_identifiers) > 0

    async def test_move_contact_email_to_contact_creation(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        contact_1: ContactV2,
        contact_2: ContactV2,
        email_1: str,
        account_1: AccountV2,
        account_2: AccountV2,
        contact_email_1: ContactEmail,
        pipeline_1: PipelineV2,
        email_message: Message,
        sequence: SequenceV2,
        email_thread: Thread,
        global_thread: GlobalThread,
        meeting: MeetingDto,
        calendar_event: HydratedUserCalendarEventDto,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        for option1, option2 in [
            (True, True),
            (True, False),
            (False, True),
            (False, False),
        ]:
            created_job_response_raw = (
                await admin_api_client.api_test_client.client.post(
                    fastapi_app.url_path_for("create_integrity_job"),
                    headers=get_request_headers(self.user_id, self.organization_id),
                    json={
                        "job_type": IntegrityJobType.MOVE,
                        "src_entity_type": EntityType.CONTACT_EMAIL,
                        "src_entity_id": str(contact_email_1.id),
                        "dest_entity_type": EntityType.CONTACT,
                        "dest_entity_id": str(contact_2.id),
                        "contextual_param": {
                            "src_contact_id": str(contact_1.id),
                            "remove_contact_account_association_if_last": option1,
                            "create_contact_account_association_if_missing": option2,
                        },
                        "start_immediately": False,
                    },
                )
            )

            assert created_job_response_raw.status_code == status.HTTP_200_OK

            create_job_response = CreateCRMIntegrityJobResponse.model_validate_json(
                created_job_response_raw.text
            )

            db_crm_integrity_job = (
                await crm_integrity_job_repository.find_by_primary_key_or_fail(
                    table_model=CRMIntegrityJob,
                    id=create_job_response.job_id,
                )
            )
            assert db_crm_integrity_job.status == JobStatus.NEW


class TestCRMIntegrityRemoveContactEmailFromContactCreationAndPreview(
    RemoveContactEmailFromContactWorkflowBaseFixtures
):
    async def test_remove_contact_email_from_contact_preview(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        contact_1: ContactV2,
        contact_email_1: ContactEmail,
        account_1: AccountV2,
        contact_email_account_association_1: ContactEmailAccountAssociation,
        email_thread: Thread,
        email_message: Message,
        global_thread: GlobalThread,
        global_message: GlobalMessage,
        sequence: SequenceV2,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        for option in [True, False]:
            preview_response_raw = await admin_api_client.api_test_client.client.post(
                fastapi_app.url_path_for("preview_integrity_job"),
                headers=get_request_headers(self.user_id, self.organization_id),
                json={
                    "job_type": IntegrityJobType.REMOVE,
                    "src_entity_type": EntityType.CONTACT_EMAIL,
                    "src_entity_id": str(contact_email_1.id),
                    "dest_entity_type": EntityType.CONTACT,
                    "dest_entity_id": str(contact_1.id),
                    "contextual_param": {
                        "remove_contact_account_association_if_last": option,
                    },
                },
            )

            assert preview_response_raw.status_code == status.HTTP_200_OK

            preview_response = IntegrityJobPreviewResponse.model_validate_json(
                preview_response_raw.text
            )

            assert len(preview_response.associated_data_changes_identifiers) > 0

    async def test_remove_contact_email_from_contact_creation(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        contact_1: ContactV2,
        contact_email_1: ContactEmail,
        account_1: AccountV2,
        contact_email_account_association_1: ContactEmailAccountAssociation,
        email_thread: Thread,
        email_message: Message,
        global_thread: GlobalThread,
        global_message: GlobalMessage,
        sequence: SequenceV2,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        for option in [True, False]:
            created_job_response_raw = (
                await admin_api_client.api_test_client.client.post(
                    fastapi_app.url_path_for("create_integrity_job"),
                    headers=get_request_headers(self.user_id, self.organization_id),
                    json={
                        "job_type": IntegrityJobType.REMOVE,
                        "src_entity_type": EntityType.CONTACT_EMAIL,
                        "src_entity_id": str(contact_email_1.id),
                        "dest_entity_type": EntityType.CONTACT,
                        "dest_entity_id": str(contact_1.id),
                        "contextual_param": {
                            "remove_contact_account_association_if_last": option,
                        },
                        "start_immediately": False,
                    },
                )
            )

            assert created_job_response_raw.status_code == status.HTTP_200_OK

            create_job_response = CreateCRMIntegrityJobResponse.model_validate_json(
                created_job_response_raw.text
            )

            db_crm_integrity_job = (
                await crm_integrity_job_repository.find_by_primary_key_or_fail(
                    table_model=CRMIntegrityJob,
                    id=create_job_response.job_id,
                )
            )
            assert db_crm_integrity_job.status == JobStatus.NEW


class TestCRMIntegrityMoveContactEmailToAccountCreationAndPreview(
    MoveContactEmailToAccountWorkflowBaseFixtures
):
    async def test_move_contact_email_to_account_preview(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        contact_1: ContactV2,
        email_1: str,
        email_2: str,
        account_1: AccountV2,
        account_2: AccountV2,
        pipeline_1: PipelineV2,
        contact_email_1: ContactEmail,
        contact_email_2: ContactEmail,
        email_message: Message,
        sequence: SequenceV2,
        email_thread: Thread,
        global_thread: GlobalThread,
        meeting: MeetingDto,
        calendar_event: HydratedUserCalendarEventDto,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        preview_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("preview_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT_EMAIL,
                "src_entity_id": str(contact_email_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_2.id),
                "contextual_param": {
                    "src_account_id": str(account_1.id),
                    "contact_id": str(contact_1.id),
                },
            },
        )

        assert preview_response_raw.status_code == status.HTTP_200_OK

    async def test_move_contact_email_to_account_creation(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        contact_1: ContactV2,
        email_1: str,
        email_2: str,
        account_1: AccountV2,
        account_2: AccountV2,
        pipeline_1: PipelineV2,
        contact_email_1: ContactEmail,
        contact_email_2: ContactEmail,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        created_new_job_response = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.MOVE,
                "src_entity_type": EntityType.CONTACT_EMAIL,
                "src_entity_id": str(contact_email_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_2.id),
                "contextual_param": {
                    "src_account_id": str(account_1.id),
                    "contact_id": str(contact_1.id),
                },
                "start_immediately": False,
            },
        )

        assert created_new_job_response.status_code == status.HTTP_200_OK

        create_job_response = CreateCRMIntegrityJobResponse.model_validate_json(
            created_new_job_response.text
        )

        db_crm_integrity_job = (
            await crm_integrity_job_repository.find_by_primary_key_or_fail(
                table_model=CRMIntegrityJob,
                id=create_job_response.job_id,
            )
        )
        assert db_crm_integrity_job.status == JobStatus.NEW


class TestCRMIntegrityRemoveContactEmailFromAccountCreationAndPreview(
    RemoveContactEmailFromAccountWorkflowBaseFixtures
):
    async def test_remove_contact_email_from_account_preview(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        contact_1: ContactV2,
        email_1: str,
        email_2: str,
        account_1: AccountV2,
        account_2: AccountV2,
        pipeline_1: PipelineV2,
        contact_email_1: ContactEmail,
        contact_email_2: ContactEmail,
        sequence: SequenceV2,
        global_message: GlobalMessage,
        global_thread: GlobalThread,
        email_message: Message,
        email_thread: Thread,
        meeting: MeetingDto,
        calendar_event: HydratedUserCalendarEventDto,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        preview_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("preview_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.REMOVE,
                "src_entity_type": EntityType.CONTACT_EMAIL,
                "src_entity_id": str(contact_email_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_1.id),
                "contextual_param": {
                    "contact_id": str(contact_1.id),
                },
                "start_immediately": False,
            },
        )

        assert preview_response_raw.status_code == status.HTTP_200_OK

        preview_response = IntegrityJobPreviewResponse.model_validate_json(
            preview_response_raw.text
        )

        assert len(preview_response.associated_data_changes_identifiers) > 0

    async def test_remove_contact_email_from_account_creation(
        self,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        contact_1: ContactV2,
        email_1: str,
        email_2: str,
        account_1: AccountV2,
        account_2: AccountV2,
        pipeline_1: PipelineV2,
        contact_email_1: ContactEmail,
        contact_email_2: ContactEmail,
        sequence: SequenceV2,
        global_message: GlobalMessage,
        global_thread: GlobalThread,
        email_message: Message,
        email_thread: Thread,
        meeting: MeetingDto,
        calendar_event: HydratedUserCalendarEventDto,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        admin_api_client = multi_api_client["admin"]

        created_job_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.REMOVE,
                "src_entity_type": EntityType.CONTACT_EMAIL,
                "src_entity_id": str(contact_email_1.id),
                "dest_entity_type": EntityType.ACCOUNT,
                "dest_entity_id": str(account_1.id),
                "contextual_param": {
                    "contact_id": str(contact_1.id),
                },
                "start_immediately": False,
            },
        )

        assert created_job_response_raw.status_code == status.HTTP_200_OK

        create_job_response = CreateCRMIntegrityJobResponse.model_validate_json(
            created_job_response_raw.text
        )

        db_crm_integrity_job = (
            await crm_integrity_job_repository.find_by_primary_key_or_fail(
                table_model=CRMIntegrityJob,
                id=create_job_response.job_id,
            )
        )
        assert db_crm_integrity_job.status == JobStatus.NEW


@pytest.mark.skip("Temporary skip due to global tests failure")
class TestCRMIntegrityAddAccountToPipelineCreationAndPreview(
    AddAccountToPipelineWorkflowBaseFixtures
):
    async def test_add_account_to_pipeline_preview_and_creation(
        self,
        faker: Faker,
        multi_api_client: dict[str, CommonAPIClient],
        fastapi_app: FastAPI,
        api_client: AsyncClient,
        email_1: str,
        account_1: AccountV2,
        contact_1: ContactV2,
        integrity_job_subdomains: list[CRMSubDomain],
        task: TaskV2,
        email_thread: Thread,
        email_message: Message,
        global_thread: GlobalThread,
        global_message: GlobalMessage,
        voice_call: Call,
        meeting: Meeting,
        calendar_event: HydratedUserCalendarEventDto,
        pipeline_service: PipelineService,
        pipeline_stage_dto: PipelineStageDto,
        integrity_job_service: IntegrityJobService,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        pipeline_1 = await self.create_pipeline_1(
            faker=faker,
            pipeline_service=pipeline_service,
            account_1=account_1,
            contact_1=contact_1,
            pipeline_stage_dto=pipeline_stage_dto,
        )

        admin_api_client = multi_api_client["admin"]

        preview_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("preview_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.ADD,
                "src_entity_type": EntityType.ACCOUNT,
                "src_entity_id": str(account_1.id),
                "dest_entity_type": EntityType.PIPELINE,
                "dest_entity_id": str(pipeline_1.id),
            },
        )

        assert preview_response_raw.status_code == status.HTTP_200_OK

        preview_response = IntegrityJobPreviewResponse.model_validate_json(
            preview_response_raw.text
        )
        assert len(preview_response.associated_data_changes_identifiers) > 0

        created_job_response_raw = await admin_api_client.api_test_client.client.post(
            fastapi_app.url_path_for("create_integrity_job"),
            headers=get_request_headers(self.user_id, self.organization_id),
            json={
                "job_type": IntegrityJobType.ADD,
                "src_entity_type": EntityType.ACCOUNT,
                "src_entity_id": str(account_1.id),
                "dest_entity_type": EntityType.PIPELINE,
                "dest_entity_id": str(pipeline_1.id),
                "start_immediately": False,
            },
        )

        assert created_job_response_raw.status_code == status.HTTP_200_OK

        create_job_response = CreateCRMIntegrityJobResponse.model_validate_json(
            created_job_response_raw.text
        )

        db_crm_integrity_job = (
            await crm_integrity_job_repository.find_by_primary_key_or_fail(
                table_model=CRMIntegrityJob,
                id=create_job_response.job_id,
            )
        )
        assert db_crm_integrity_job.status == JobStatus.NEW
