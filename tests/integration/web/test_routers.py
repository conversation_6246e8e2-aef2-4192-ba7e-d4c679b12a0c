import os
from collections.abc import AsyncGenerator

import pytest
from asgi_lifespan import LifespanManager
from httpx import ASGITransport, AsyncClient


@pytest.fixture()
async def client_public_app() -> AsyncGenerator[AsyncClient, None]:
    os.environ["SALESTECH_BE_PUBLIC_FACING_ROUTES_ONLY"] = "True"

    # Import only after env created
    from salestech_be.web.application import get_app

    fastapi_app = get_app()
    async with (
        LifespanManager(
            fastapi_app, startup_timeout=120, shutdown_timeout=60
        ) as manager,
        AsyncClient(
            transport=ASGITransport(app=manager.app),
            base_url="http://testserver",
        ) as client,
    ):
        yield client

    # Make sure you reset the env after the test
    del os.environ["SALESTECH_BE_PUBLIC_FACING_ROUTES_ONLY"]


@pytest.fixture()
async def client_all_app() -> AsyncGenerator[AsyncClient, None]:
    os.environ["SALESTECH_BE_PUBLIC_FACING_ROUTES_ONLY"] = "False"

    # Import only after env created
    from salestech_be.web.application import get_app

    fastapi_app = get_app()
    async with (
        LifespanManager(
            fastapi_app, startup_timeout=120, shutdown_timeout=60
        ) as manager,
        AsyncClient(
            transport=ASGITransport(app=manager.app),
            base_url="http://testserver",
        ) as client,
    ):
        yield client

    # Make sure you reset the env after the test
    del os.environ["SALESTECH_BE_PUBLIC_FACING_ROUTES_ONLY"]


async def test_public_routes_available(client_public_app: AsyncClient) -> None:
    response = await client_public_app.get("/api/openapi.json")
    assert response.status_code == 200
    assert response.json()["paths"].keys() == {
        "/api/v1/monitoring/health",
        "/api/v1/webhook/twilio/voice",
        "/api/v1/webhook/twilio/log",
        "/api/v1/webhook/twilio/amd",
        "/api/v1/webhook/twilio/call-status",
        "/api/v1/webhook/twilio/recording-status",
        "/api/v1/webhook/twilio/call-redirect",
        "/api/v1/webhook/assembly_ai_transcript",
        "/api/v1/webhook/nylas/v3/grant",
        "/api/v1/webhook/nylas/v3/calendar",
        "/api/v1/webhook/nylas/v3",
        "/api/v1/webhook/recallai/bot/event",
        "/api/v1/webhook/zoom/deauth",
        "/api/v1/webhook/brightdata/scraper/notify",
        "/api/v1/webhook/recall_zoom",
        "/api/v1/webhook/account_and_contact_creation",
        "/e/{encrypted_str}",
    }


async def test_all_routes_available(client_all_app: AsyncClient) -> None:
    response = await client_all_app.get("/api/openapi.json")
    assert response.status_code == 200
    existing_path_by_method = {
        "get": {
            "/api/echo/test",
            "/api/v1/accounts/{account_id}",
            "/api/v1/accounts/{account_id}/research",
            "/api/v1/activities/calendars",
            "/api/v1/activities/calendars/schedule/scan_calendar_event",
            "/api/v1/activities/calendars/{calendar_id}",
            "/api/v1/activities/{activity_id}",
            "/api/v1/analytics/dashboards",
            "/api/v1/api_key/{scope}",
            "/api/v1/approval_request/{approval_request_id}",
            "/api/v1/audience/list/{audience_list_id}",
            "/api/v1/auth/admin/me",
            "/api/v1/auth/callback",
            "/api/v1/auth/echo/deal",
            "/api/v1/auth/echo_header",
            "/api/v1/auth/me",
            "/api/v1/voice/call",
            "/api/v1/auth/signon",
            "/api/v1/auth/signout",
            "/api/v1/business_process/organization_sales_methodology",
            "/api/v1/cloud_files/{attachment_id}",
            "/api/v1/connect/callback/google",
            "/api/v1/connect/callback/hubspot",
            "/api/v1/connect/callback/microsoft",
            "/api/v1/connect/callback/recall/zoom",
            "/api/v1/connect/callback/zoom",
            "/api/v1/connect/google",
            "/api/v1/connect/hubspot",
            "/api/v1/connect/integration",
            "/api/v1/connect/microsoft",
            "/api/v1/connect/organization/integration",
            "/api/v1/emails/outbound/domains/{domain_id}",
            "/api/v1/connect/organization/list_integration",
            "/api/v1/connect/sync_task",
            "/api/v1/connect/test_only/auth/google",
            "/api/v1/connect/test_only/auth/microsoft",
            "/api/v1/connect/test_only/home",
            "/api/v1/connect/test_only/login/google",
            "/api/v1/connect/test_only/login/microsoft",
            "/api/v1/connect/test_only/logout/google",
            "/api/v1/connect/test_only/logout/microsoft",
            "/api/v1/connect/test_only/zoom/create_meeting",
            "/api/v1/connect/test_only/zoom/home",
            "/api/v1/connect/test_only/zoom/login",
            "/api/v1/connect/test_only/zoom/logout",
            "/api/v1/connect/test_only/zoom/update_meeting",
            "/api/v1/connect/zoom",
            "/api/v1/contacts/{contact_id}",
            "/api/v1/contacts/{contact_id}/research",
            "/api/v1/crm_sync/sync_instance",
            "/api/v1/crm_sync/records",
            "/api/v1/custom_object/data/{custom_object_id}",
            "/api/v1/custom_object/data/{custom_object_id}/{custom_object_data_id}",
            "/api/v1/emails/accounts/{email_account_id}",
            "/api/v2/emails/accounts/{email_account_id}",
            "/api/v1/emails/insights/{insight_id}",
            "/api/v1/emails/pools/{email_account_pool_id}/memberships",
            "/api/v1/emails/global/messages/{global_message_id}",
            "/api/v1/emails/unsubscription_groups/{unsubscription_group_id}",
            "/api/v1/event_schedule/booking/{event_schedule_booking_id}",
            "/api/v1/event_schedule/{event_schedule_id}",
            "/api/v1/event_schedule/variables/_list",
            "/api/v1/event_schedule/{user_org_identifier}/{event_title_slug}/id",
            "/api/v1/extraction_section/{extraction_section_id}",
            "/api/v1/forms/{form_id}",
            "/api/v1/goals/templates",
            "/api/v1/goals/{user_goal_id}",
            "/api/v1/imports/csv/job_reviews/{job_review_id}",
            "/api/v1/imports/csv/jobs/{job_id}",
            "/api/v1/imports/csv/jobs/{job_id}/details",
            "/api/v1/imports/csv/jobs/{job_id}/summary",
            "/api/v1/jobs/filter",
            "/api/v1/imports/csv/jobs",
            "/api/v1/jobs/{job_id}",
            "/api/v1/llm/stream",
            "/api/v1/llm_config/config/extraction/templates",
            "/api/v1/llm_config/config/extraction/{extraction_config_section_id}",
            "/api/v1/meetings/external/{external_event_id}",
            "/api/v1/meetings/shares/{share_id}",
            "/api/v1/meetings/shares/{share_id}/meeting_info",
            "/api/v1/meetings/shares/{share_id}/meeting_info/preview",
            "/api/v1/meetings/{meeting_id}/clips/{clip_id}",
            "/api/v1/meetings/{meeting_id}/research",
            "/api/v1/meetings/{meeting_id}/stats",
            "/api/v1/meetings/{meeting_id}/transcripts",
            "/api/v1/metadata/custom/standard_object/enablement",
            "/api/v1/metadata/field_type_description",
            "/api/v1/metadata/organization_schema",
            "/api/v1/metadata/tenant_schema",
            "/api/v1/monitoring/health",
            "/api/v1/notes",
            "/api/v1/stage_criteria/list_value/{stage_list_value_id}",
            "/api/v1/notes/{note_id}",
            "/api/v1/onboarding/state/{onboarding_type}/org/{of_organization_id}",
            "/api/v1/onboarding/state/{onboarding_type}/org/{of_organization_id}/user/{of_user_id}",
            "/api/v1/organization/{organization_id}",
            "/api/v1/organization/organization_preference/{key}",
            "/api/v1/permission/permission_claims/user/{target_user_id}",
            "/api/v1/permission/permission_set/{permission_set_id}",
            "/api/v1/permission/permission_set_group/{permission_set_group_id}",
            "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}",
            "/api/v1/pipelines/{pipeline_id}",
            "/api/v1/pipeline_intel/{pipeline_id}",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process",
            "/api/v1/prompts/context/{context_id}",
            "/api/v1/prompts/prompt/generate_email_template_customized_opener/theme",
            "/api/v1/prompts/prompt/{prompt_id}",
            "/api/v1/prompts/template/{template_id}",
            "/api/v1/propagation_rule/{rule_id}",
            "/api/v1/prospecting/queries/{query_id}",
            "/api/v1/prospecting/credits",
            "/api/v1/quotas/policies/{policy_id}",
            "/api/v1/quotas/usages/summary",
            "/api/v1/quotas/check_quota",
            "/api/v1/select_lists/{select_list_id}",
            "/api/v1/tasks/templates/{task_template_id}",
            "/api/v1/tasks/{task_id}",
            "/api/v1/users",
            "/api/v1/users/v2",
            "/api/v1/users/{target_user_id}",
            "/api/v1/users/user_preference/{key}",
            "/api/v1/users/test/authed_echo",
            "/api/v1/users/signatures/{signature_id}",
            "/api/v1/user_invite/verify",
            "/api/v1/user_invite/done",
            "/api/v1/user_invite/is_invited/{email}",
            "/api/v1/variables/_list",
            "/api/v1/view_management/object_list_view_schema/{view_id}",
            "/api/v1/view_management/org_view_schema_preference_config",
            "/api/v1/view_management/user_view_schema_preference_config",
            "/api/v1/voice/admin",
            "/api/v1/voice/admin/provider-accounts",
            "/api/v1/voice/admin/provider-accounts/{provider_account_id}",
            "/api/v1/voice/admin/verified_phone_numbers/{phone_number}",
            "/api/v1/voice/phone-numbers",
            "/api/v1/voice/usage",
            "/api/v1/webhook/nylas/v3",
            "/api/v1/webhook/nylas/v3/calendar",
            "/api/v1/webhook/nylas/v3/grant",
            "/api/v1/webhook/recall_zoom",
            "/api/v1/workflow_nodes/{node_id}",
            "/api/v1/workflow_runs/{run_id}",
            "/api/v1/workflow_snapshots/{snapshot_id}",
            "/api/v1/workflows/{workflow_id}",
            "/api/v1/crm_integrity/{job_id}",
            "/api/v1/trackers/{tracker_id}",
            "/api/v1/metadata/association_records",
            "/api/v1/stage_criteria_v2/stage_exit_criteria/{stage_value_id}",
            "/api/v1/stage_criteria_v2/stage_list_entrance_criteria/{stage_list_id}",
            "/e/{encrypted_str}",
        },
        "post": {
            "/api/echo",
            "/api/v1/stage_criteria/list",
            "/api/echo/openai",
            "/api/v1/accounts",
            "/api/v1/accounts/_list",
            # "/api/v1/voice/call", #temp disabled
            "/api/v1/accounts/bulk_patch",
            "/api/v1/accounts/{account_id}/_shift_status",
            "/api/v1/activities/_list",
            "/api/v1/activities/_list_records",
            "/api/v1/activities/calendars/_get_event_by_meeting",
            "/api/v1/activities/calendars/_list_events",
            "/api/v1/activities/calendars/events",
            "/api/v1/activities/calendars/events/{calendar_event_id}/_rsvp",
            "/api/v1/activities/calendars/events/test/create_sync_calendar_schedule",
            "/api/v1/analytics/guest_token",
            "/api/v1/api_key/{scope}",
            "/api/v1/approval_request/_list",
            "/api/v1/approval_request/{approval_request_id}/_approve",
            "/api/v1/approval_request/{approval_request_id}/_deny",
            "/api/v1/audience/list",
            "/api/v1/audience/list/_list",
            "/api/v1/audience/list/{audience_list_id}/_clone",
            "/api/v1/audience/list_membership/_add",
            "/api/v1/audience/list_membership/_list",
            "/api/v1/audience/list_membership/_remove",
            "/api/v1/auth/refresh",
            "/api/v1/auth/token",
            "/api/v1/business_process/sales_methodologies_templates/_list",
            "/api/v1/chat",
            "/api/v1/chat/_list",
            "/api/v1/chat/{chat_id}/messages/_list",
            "/api/v1/cloud_files/_upload",
            "/api/v1/cloud_files/multipart_uploads/_complete",
            "/api/v1/cloud_files/multipart_uploads",
            "/api/v1/cloud_files/presigned_urls",
            "/api/v1/comments",
            "/api/v1/comments/_list",
            "/api/v1/contacts",
            "/api/v1/contacts/_search_by_display_name_or_email",
            "/api/v1/contacts/_search_by_display_name_or_email_v2",
            "/api/v1/conversations/_list",
            "/api/v1/crm_sync/update_instance",
            "/api/v1/stage_criteria/list_value/{stage_list_value_id}",
            "/api/v1/citations/_list_by_ids",
            "/api/v1/contacts/{contact_id}/accounts",
            "/api/v1/contacts/{contact_id}/account_roles/_list",
            "/api/v1/contacts/{contact_id}/account_roles",
            "/api/v1/contacts/_list",
            "/api/v1/contacts/_create",
            "/api/v1/contacts/enrich_contact_by_ids",
            "/api/v1/contacts/enrich_contacts_preview",
            "/api/v1/contacts/enrich_contacts",
            "/api/v1/contacts/bulk_patch",
            "/api/v1/contacts/{contact_id}/_shift_stage",
            "/api/v1/contacts/{contact_id}/_unarchive",
            "/api/v1/contacts/{contact_id}/emails/_list",
            "/api/v1/contacts/{contact_id}/phone_numbers/_list",
            "/api/v1/custom_object/data/_query_indexed_v2",
            "/api/v1/custom_object/data_v2/{custom_object_id}",
            "/api/v1/custom_object/data/{custom_object_id}/_list_records/",
            "/api/v1/domain_object_lists",
            "/api/v1/domain_object_lists/_list",
            "/api/v1/domain_object_lists/{list_id}/_add_and_remove_items",
            "/api/v1/domain_object_lists/{list_id}/items/_list",
            "/api/v2/emails/accounts",
            "/api/v1/emails/accounts/_list",
            "/api/v2/emails/accounts/_list",
            "/api/v2/emails/accounts/{email_account_id}/_archive",
            "/api/v2/emails/accounts/{email_account_id}/_unarchive",
            "/api/v1/emails/insights",
            "/api/v1/emails/insights/_generate",
            "/api/v1/emails/insights/_list",
            "/api/v1/emails/messages",
            "/api/v1/emails/outbound/domains/_buy",
            "/api/v1/emails/outbound/domains/_list",
            "/api/v1/emails/outbound/domains/_find_domain",
            "/api/v1/emails/outbound/domains/{domain_id}/_archive",
            "/api/v1/emails/outbound/domains/{domain_id}/_unarchive",
            "/api/v1/emails/pools",
            "/api/v1/emails/pools/_list",
            "/api/v1/emails/pools/memberships",
            "/api/v1/emails/templates",
            "/api/v1/emails/templates/_list",
            "/api/v1/emails/templates/_preview",
            "/api/v1/emails/templates/{template_id}/_preview",
            "/api/v1/emails/templates/_list_history",
            "/api/v1/emails/unsubscription_groups",
            "/api/v1/emails/unsubscription_groups/_list",
            "/api/v1/emails/unsubscription_groups/_unsubscribe",
            "/api/v1/emails/global/threads/_list",
            "/api/v1/emails/global/threads/_list_for_sales_action_tagging",
            "/api/v1/emails/global/messages/{global_message_id}/cancel",
            "/api/v1/emails/global/messages/{global_message_id}/_temp_ai_rec",
            "/api/v1/event_schedule",
            "/api/v1/event_schedule/_list",
            "/api/v1/event_schedule/_list_v2",
            "/api/v1/event_schedule/_list_shared_schedules",
            "/api/v1/event_schedule/{event_schedule_id}/availability",
            "/api/v1/event_schedule/{event_schedule_id}/book",
            "/api/v1/event_schedule/{user_org_identifier}/{event_title_slug}/availability",
            "/api/v1/extraction_section",
            "/api/v1/extraction_section/_list",
            "/api/v1/extraction_section/{extraction_section_id}/_remove",
            "/api/v1/extraction_section/{extraction_section_id}/field",
            "/api/v1/extraction_section/{extraction_section_id}/field/{extraction_field_id}/_remove",
            "/api/v1/user_feedback",
            "/api/v1/forms/_list",
            "/api/v1/goals",
            "/api/v1/goals/_list",
            "/api/v1/imports/",
            "/api/v1/imports/csv/job_reviews",
            "/api/v1/imports/csv/job_reviews/_list",
            "/api/v1/imports/csv/job_reviews/{job_review_id}/_approve",
            "/api/v1/imports/csv/job_reviews/{job_review_id}/_archive",
            "/api/v1/imports/csv/job_reviews/{job_review_id}/_pending",
            "/api/v1/imports/csv/job_reviews/{job_review_id}/_reject",
            "/api/v1/imports/csv/jobs",
            "/api/v1/imports/csv/jobs/{job_id}/signal",
            "/api/v1/imports/csv/samples",
            "/api/v1/jobs",
            "/api/v1/jobs/_list",
            "/api/v1/llm_config/config/extraction",
            "/api/v1/llm_config/config/extraction/_list",
            "/api/v1/llm_config/config/extraction/_onboard",
            "/api/v1/llm_config/config/extraction/{extraction_config_section_id}/_remove",
            "/api/v1/llm_config/config/extraction/{extraction_config_section_id}/field",
            "/api/v1/llm_config/config/extraction/{extraction_config_section_id}/field/{extraction_field_id}/_remove",
            "/api/v1/meetings/_list",
            "/api/v1/meetings/_list_history",
            "/api/v1/meetings/{meeting_id}/_temp_ai_rec",
            "/api/v1/meetings/bots/_clear_future",
            "/api/v1/meetings/bots",
            "/api/v1/meetings/bots/_import",
            "/api/v1/meetings/bots/{meeting_bot_id}/sync_external_settings",
            "/api/v1/meetings/consent",
            "/api/v1/meetings/insights/_list",
            "/api/v1/meetings/insights/fields",
            "/api/v1/meetings/insights/{insight_id}/_re_rank",
            "/api/v1/meetings/live_recordings",
            "/api/v1/meetings/live_transcript_sessions",
            "/api/v1/meetings/meeting_annotations",
            "/api/v1/meetings/meeting_annotations/_list",
            "/api/v1/meetings/shares",
            "/api/v1/meetings/shares/_list",
            "/api/v1/meetings/{meeting_id}/_analyze",
            "/api/v1/meetings/{meeting_id}/_end",
            "/api/v1/meetings/{meeting_id}/clips",
            "/api/v1/meetings/{meeting_id}/clips/_list",
            "/api/v1/meetings/{meeting_id}/refresh_info",
            "/api/v1/meetings/{meeting_id}/transcripts/_process",
            "/api/v1/metadata/association",
            "/api/v1/metadata/custom/object",
            "/api/v1/metadata/custom/standard_object/enablement",
            "/api/v1/notes",
            "/api/v1/notes/_list",
            "/api/v1/object_metadata/ai_creation_recs/_reject",
            "/api/v1/object_metadata/ai_creation_recs/_list",
            "/api/v1/object_metadata/ai_recs/_list_active_by_source",
            "/api/v1/onboarding/state/{onboarding_type}/org/{of_organization_id}",
            "/api/v1/onboarding/state/{onboarding_type}/org/{of_organization_id}/user/{of_user_id}",
            "/api/v1/organization/_list",
            "/api/v1/organization/backfill_permissions_for_all",
            "/api/v1/organization/{organization_id}/backfill_permissions",
            "/api/v1/organization/{organization_id}/patch_public_domain_accounts",
            "/api/v1/organization/organization_preference",
            "/api/v1/permission/permission_set",
            "/api/v1/permission/permission_set/user/_assign",
            "/api/v1/permission/permission_set_group",
            "/api/v1/permission/permission_set_group/permission_set/_assign",
            "/api/v1/permission/permission_set_group/user/_assign",
            "/api/v1/pipeline_stage_select_lists",
            "/api/v1/pipeline_stage_select_lists/_list_summary",
            "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}/_activate",
            "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}/_deactivate",
            "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}/values/{pipeline_stage_select_list_value_id}/_activate",
            "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}/values/{pipeline_stage_select_list_value_id}/_deactivate",
            "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}/values/{pipeline_stage_select_list_value_id}/_remove_and_remap",
            "/api/v1/pipelines",
            "/api/v1/pipelines/_list",
            "/api/v1/pipelines/{pipeline_id}/archive",
            "/api/v1/pipelines/{pipeline_id}/contacts",
            "/api/v1/pipelines/{pipeline_id}/_shift_opportunity_pipeline",
            "/api/v1/pipelines/{pipeline_id}/shift_stage",
            "/api/v1/pipelines/{pipeline_id}/_shift_stage_v2",
            "/api/v1/pipelines/bulk_patch",
            "/api/v1/pipelines/{pipeline_id}/_evaluate_current_stage_exit",
            "/api/v1/pipelines/{pipeline_id}/contact_pipeline_roles",
            "/api/v1/pipelines/{pipeline_id}/contact_pipeline_roles/_list",
            "/api/v1/pipelines/{pipeline_id}/contact_pipeline_roles/{contact_id}/_temp_ai_rec",
            "/api/v1/pipelines/{pipeline_id}/contact_pipeline_roles/_by_source",
            "/api/v1/pipelines/{pipeline_id}/contact_pipeline_roles/_temp_ai_object_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain/items",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/items",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/items",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/items",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/items",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process/items",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/items/{item_id}/_temp_ai_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/items/_temp_ai_object_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/items/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain/items/{item_id}/_temp_ai_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain/items/_temp_ai_object_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain/items/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/items/{item_id}/_temp_ai_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/items/_temp_ai_object_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/items/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/items/{item_id}/_temp_ai_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/items/_temp_ai_object_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/items/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/items/{item_id}/_temp_ai_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/items/_temp_ai_object_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/items/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process/items/{item_id}/_temp_ai_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process/items/_temp_ai_object_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process/items/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/_temp_ai_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/_temp_ai_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/_temp_ai_rec",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/_temp_ai_rec",
            "/api/v1/prompts/context",
            "/api/v1/prompts/context/_list",
            "/api/v1/prompts/prompt/_list",
            "/api/v1/prompts/prompt/evaluate_email_template_content",
            "/api/v1/prompts/prompt/generate_email_template",
            "/api/v1/prompts/prompt/generate_email_template_customized_opener",
            "/api/v1/prompts/prompt/rephrase_email_template_body",
            "/api/v1/prompts/prompt/rephrase_email_template_subject",
            "/api/v1/prompts/template",
            "/api/v1/prompts/template/_list",
            "/api/v1/propagation_rule",
            "/api/v1/propagation_rule/_list",
            "/api/v1/prospecting/people/_enrich",
            "/api/v1/prospecting/people/_bulk_import",
            "/api/v1/prospecting/people/_list",
            "/api/v1/prospecting/company/_list",
            "/api/v1/prospecting/company/_bulk_import",
            "/api/v1/prospecting/queries",
            "/api/v1/prospecting/queries/_list",
            "/api/v1/prospecting/filter_fields/_list",
            "/api/v1/prospecting/filter_field_options/_search",
            "/api/v1/prospecting/run/_list",
            "/api/v1/prospecting/run/{prospecting_run_id}/results/_list",
            "/api/v1/prospecting/credits/usage/_list",
            "/api/v1/quotas/policies/_list",
            "/api/v1/quotas/policies/{policy_id}/_reset",
            "/api/v1/search/_search",
            "/api/v1/select_lists",
            "/api/v1/select_lists/_list_details",
            "/api/v1/select_lists/{select_list_id}/_activate",
            "/api/v1/select_lists/{select_list_id}/_deactivate",
            "/api/v1/select_lists/{select_list_id}/list_value/{select_list_value_id}/_activate",
            "/api/v1/select_lists/{select_list_id}/list_value/{select_list_value_id}/_deactivate",
            "/api/v1/select_lists/{select_list_id}/list_value/{select_list_value_id}/_remove_and_remap",
            "/api/v1/sequence_steps",
            "/api/v1/sequence_steps/_list",
            "/api/v1/sequence_steps/{sequence_step_id}/_clone",
            "/api/v1/sequence_steps/{sequence_step_id}/_move",
            "/api/v1/sequences",
            "/api/v1/sequences/_list",
            "/api/v1/sequences/_list_blueprints",
            "/api/v1/sequences/{sequence_id}/step_executions/_list",
            "/api/v1/sequences/{sequence_id}/step_executions/_list_v2",
            "/api/v1/sequences/{sequence_id}/_activate",
            "/api/v1/sequences/{sequence_id}/_clone",
            "/api/v1/sequences/{sequence_id}/_deactivate",
            "/api/v1/sse/chat",
            "/api/v1/giant_tasks/_list",
            "/api/v1/tasks",
            "/api/v1/tasks/_list",
            "/api/v1/tasks/summary/_list",
            "/api/v1/tasks/templates",
            "/api/v1/users",
            "/api/v1/users/_invite",
            "/api/v1/users/_list_legacy",  # keep it here just in case FE needs it before fully migrate
            "/api/v1/users/_list",
            "/api/v1/users/_reinvite",
            "/api/v1/users/user_preference",
            "/api/v1/users/organization_user/_deactivate",
            "/api/v1/users/platform_credentials",
            "/api/v1/users/platform_credentials/_list",
            "/api/v1/users/signatures",
            "/api/v1/users/signatures/_list",
            "/api/v1/users/v2",
            "/api/v1/user_invite",
            "/api/v1/user_invite/all",
            "/api/v1/view_management/object_list_view_schema",
            "/api/v1/view_management/object_list_view_schema/_list",
            "/api/v1/view_management/object_list_view_schema/{view_id}/_clone",
            "/api/v1/view_management/object_list_view_schema/{view_id}/_promote",
            "/api/v1/voice/admin/phone_numbers",
            "/api/v1/voice/admin/phone_numbers/_list",
            "/api/v1/voice/admin/provider-accounts",
            "/api/v1/voice/admin/process_transcript/{call_sid}",
            "/api/v1/voice/admin/start_transcription/{call_sid}",
            "/api/v1/voice/admin/verified_phone_numbers",
            "/api/v1/voice/phone-numbers/purchase",
            "/api/v1/voice/available-numbers/search",
            "/api/v1/voice/call/initiate",
            "/api/v1/voice/twilio_token",
            "/api/v1/voice/twilio_update_recording_status/{call_sid}",
            "/api/v1/voice/token",
            "/api/v1/voice/call/recording/update",
            "/api/v1/webhook/assembly_ai_transcript",
            "/api/v1/webhook/brightdata/scraper/notify",
            "/api/v1/webhook/nylas/v3",
            "/api/v1/webhook/nylas/v3/calendar",
            "/api/v1/webhook/nylas/v3/grant",
            "/api/v1/webhook/recallai/bot/event",
            "/api/v1/webhook/twilio/amd",
            "/api/v1/webhook/twilio/call-status",
            "/api/v1/webhook/twilio/log",
            "/api/v1/webhook/twilio/recording-status",
            "/api/v1/webhook/twilio/voice",
            "/api/v1/webhook/twilio/call-redirect",
            "/api/v1/webhook/zoom/deauth",
            "/api/v1/workflow_blocks",
            "/api/v1/workflow_blocks/_list",
            "/api/v1/workflow_edges",
            "/api/v1/workflow_edges/_list",
            "/api/v1/workflow_nodes",
            "/api/v1/workflow_nodes/_list",
            "/api/v1/workflow_run_nodes/_list",
            "/api/v1/workflow_runs/_list",
            "/api/v1/workflow_snapshots/_list",
            "/api/v1/workflow_snapshots/_list_templates",
            "/api/v1/workflow_snapshots/{snapshot_id}/_create_workflow",
            "/api/v1/workflow_snapshots/{snapshot_id}/_manually_run",
            "/api/v1/workflow_snapshots/{snapshot_id}/_publish",
            "/api/v1/workflow_snapshots/{snapshot_id}/_validate",
            "/api/v1/workflows",
            "/api/v1/workflows/_list",
            "/api/v1/workflows/test/produce_resource_change_trigger_event",
            "/api/v1/workflows/test/produce_trigger_event/{form_id}",
            "/api/v1/workflows/{workflow_id}/webhook",
            "/api/v1/crm_integrity",
            "/api/v1/crm_integrity/{job_id}/_start",
            "/api/v1/crm_integrity/_preview",
            "/api/v1/crm_integrity/_list",
            "/api/v1/crm_integrity/user_options/_list",
            "/api/v1/crm_integrity/{job_id}/_retry",
            "/api/v1/imports/import_meeting",
            "/api/v1/imports/jobs",
            "/api/v1/trackers",
            "/api/v1/trackers/_list",
            "/api/v1/meetings/{meeting_id}/tracker_stats/_list",
            "/api/v1/meetings/{meeting_id}/tracker_details/_list",
            "/api/v1/superadmin/organizations/login",
            "/api/v1/superadmin/organizations/_list",
            "/api/v1/superadmin/user_invite",
            "/api/v1/superadmin/user_invite/_list",
            "/api/v1/ai/email/reply",
            "/api/v1/ai/email/rephrase",
            "/api/v1/ai/email/compose",
            "/api/v1/ai/tracker/generate_phrases",
            "/api/v1/ai/meeting/generate",
            "/api/v1/emails/templates/categories",
            "/api/v1/metadata/custom/object/{custom_object_id}/field_v2",
            "/api/v1/emails/templates/categories/_list",
            "/api/v1/metadata/association_records",
            "/api/v1/metadata/association/get",
            "/api/v1/metadata/association/object/get",
            "/api/v1/metadata/association/delete",
            "/api/v1/sequence_step_variants",
            "/api/v1/sequence_step_variants/{variant_id}/_clone",
            "/api/v1/sequence_step_variants/{variant_id}/_activate",
            "/api/v1/sequence_step_variants/{variant_id}/_deactivate",
            "/api/v1/sequence_enrollments/_list",
            "/api/v1/sequence_enrollments/_preview",
            "/api/v1/sequence_enrollments",
            "/api/v1/sequence_enrollments/sync",
            "/api/v1/sequence_enrollments/async",
            "/api/v1/sequence_enrollments/reenroll",
            "/api/v1/sequence_enrollments/_reenroll_contacts",
            "/api/v1/sequence_enrollments/_bulk_change",
            "/api/v1/sequence_enrollments/sequence_enrollment_runs/{run_id}/contacts/_list",
            "/api/v1/sequence_enrollments/{enrollment_id}/_proceed",
            "/api/v1/sequence_enrollments/sequence_enrollment_runs/_list",
            "/api/v1/stage_criteria_v2/_list_stage_criteria",
            "/api/v1/stage_criteria_v2/_list_stage_criteria_item_template",
            "/api/v1/webhook/account_and_contact_creation",
        },
        "patch": {
            "/api/v1/accounts/{account_id}",
            "/api/v1/activities/calendars/events/{calendar_event_id}",
            "/api/v1/activities/calendars/{calendar_id}",
            "/api/v1/api_key/{scope}",
            "/api/v1/audience/list/{audience_list_id}",
            "/api/v1/chat/{chat_id}",
            "/api/v1/comments/{comment_id}",
            "/api/v1/voice/call/{call_id}/disposition",
            "/api/v1/contacts/{contact_id}",
            "/api/v1/contacts/{contact_id}/account_role",
            "/api/v1/contacts/{contact_id}/email",
            "/api/v1/contacts/{contact_id}/email_account_association",
            "/api/v1/custom_object/data_v2/{custom_object_id}/{custom_object_data_id}",
            "/api/v1/domain_object_lists/{list_id}",
            "/api/v1/emails/accounts/{email_account_id}",
            "/api/v2/emails/accounts/{email_account_id}",
            "/api/v1/emails/insights/{insight_id}",
            "/api/v1/emails/templates/{template_id}",
            "/api/v1/emails/unsubscription_groups/{unsubscription_group_id}",
            "/api/v1/emails/global/messages/{global_message_id}",
            "/api/v1/emails/global/messages/{global_message_id}/_by_source",
            "/api/v1/emails/global/threads/{global_thread_id}",
            "/api/v1/event_schedule/booking/{event_schedule_booking_id}",
            "/api/v1/event_schedule/{event_schedule_id}",
            "/api/v1/extraction_section/{extraction_section_id}",
            "/api/v1/extraction_section/{extraction_section_id}/field/{extraction_field_id}",
            "/api/v1/goals/{user_goal_id}",
            "/api/v1/llm_config/config/extraction/{extraction_config_section_id}",
            "/api/v1/llm_config/config/extraction/{extraction_config_section_id}/field/{extraction_field_id}",
            "/api/v1/meetings/insights/{insight_id}",
            "/api/v1/meetings/meeting_annotations/{meeting_annotation_id}",
            "/api/v1/meetings/shares/{share_id}",
            "/api/v1/meetings/{meeting_id}",
            "/api/v1/meetings/{meeting_id}/_by_source",
            "/api/v1/meetings/{meeting_id}/clips/{clip_id}",
            "/api/v1/metadata/association",
            "/api/v1/metadata/custom/object/{custom_object_id}",
            "/api/v1/metadata/custom/object/{custom_object_id}/field_v2/{custom_field_id}",
            "/api/v1/notes/{note_id}",
            "/api/v1/organization/{organization_id}",
            "/api/v1/organization/organization_preference",
            "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}",
            "/api/v1/pipelines/{pipeline_id}",
            "/api/v1/pipelines/{pipeline_id}/contact_pipeline_role",
            "/api/v1/pipelines/{pipeline_id}/contact_pipeline_roles/{contact_id}/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/items/{item_id}",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain/items/{item_id}",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/items/{item_id}",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/items/{item_id}",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/items/{item_id}",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process/items/{item_id}",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/items/{item_id}/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain/items/{item_id}/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/items/{item_id}/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/items/{item_id}/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/items/{item_id}/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/_by_source",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process/items/{item_id}/_by_source",
            "/api/v1/prompts/context/{context_id}",
            "/api/v1/prompts/prompt/{prompt_id}",
            "/api/v1/prompts/template/{template_id}",
            "/api/v1/propagation_rule/{rule_id}",
            "/api/v1/prospecting/queries/{query_id}",
            "/api/v1/quotas/policies/{policy_id}",
            "/api/v1/select_lists/{select_list_id}",
            "/api/v1/sequence_steps/{sequence_step_id}",
            "/api/v1/sequences/{sequence_id}",
            "/api/v1/crm_integrity/{job_id}",
            "/api/v1/tasks/templates/{task_template_id}",
            "/api/v1/tasks/{task_id}",
            "/api/v1/users/signatures/{signature_id}",
            "/api/v1/users/{target_user_id}",
            "/api/v1/users/{target_user_id}/v2",
            "/api/v1/users/user_preference",
            "/api/v1/view_management/object_list_view_schema/{view_id}",
            "/api/v1/view_management/org_view_schema_preference_config",
            "/api/v1/view_management/user_view_schema_preference_config",
            "/api/v1/workflow_blocks/{block_id}",
            "/api/v1/workflow_edges/{edge_id}",
            "/api/v1/workflow_nodes/{node_id}",
            "/api/v1/workflow_snapshots/{snapshot_id}",
            "/api/v1/workflows/{workflow_id}",
            "/api/v1/trackers/{tracker_id}",
            "/api/v1/emails/templates/categories/{category_id}",
            "/api/v1/metadata/association_records",
            "/api/v1/sequence_step_variants/{variant_id}",
            "/api/v1/sequence_enrollments/{enrollment_id}",
        },
        "delete": {
            "/api/v1/accounts/{account_id}/_archive",
            "/api/v1/activities/calendars/events/{calendar_event_id}",
            "/api/v1/audience/list/{audience_list_id}",
            "/api/v1/chat/{chat_id}",
            "/api/v1/comments/{comment_id}",
            "/api/v1/connect/google/integration",
            "/api/v1/connect/integration/{user_integration_id}",
            "/api/v1/contacts/{contact_id}/_archive",
            "/api/v1/contacts/{contact_id}/accounts/{account_id}",
            "/api/v1/contacts/{contact_id}/account_roles/{account_id}",
            "/api/v1/contacts/{contact_id}/emails",
            "/api/v1/contacts/{contact_id}/email_account_associations/{account_id}",
            "/api/v1/custom_object/data_v2/{custom_object_id}/{custom_object_data_id}",
            "/api/v1/domain_object_lists/{list_id}",
            "/api/v1/emails/insights/{insight_id}",
            "/api/v1/emails/pools/{email_account_pool_id}/memberships",
            "/api/v1/emails/templates/{template_id}",
            "/api/v1/emails/outbound/domains/{domain_id}",
            "/api/v1/emails/unsubscription_groups/{unsubscription_group_id}",
            "/api/v1/event_schedule/booking/{event_schedule_booking_id}",
            "/api/v1/event_schedule/{event_schedule_id}",
            "/api/v1/goals/{user_goal_id}",
            "/api/v1/meetings/insight_sections/{insight_section_id}",
            "/api/v1/meetings/insights/{insight_id}",
            "/api/v1/meetings/meeting_annotations/{meeting_annotation_id}",
            "/api/v1/meetings/shares/{share_id}",
            "/api/v1/meetings/{meeting_id}/bots",
            "/api/v1/meetings/{meeting_id}/clips/{clip_id}",
            "/api/v1/notes/{note_id}",
            "/api/v1/pipelines/{pipeline_id}/contact_pipeline_roles/{contact_id}",
            "/api/v1/pipelines/{pipeline_id}/contacts/{contact_id}",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/items/{item_id}",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain/items/{item_id}",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/items/{item_id}",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/items/{item_id}",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/items/{item_id}",
            "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process/items/{item_id}",
            "/api/v1/propagation_rule/{rule_id}",
            "/api/v1/prospecting/queries/{query_id}",
            "/api/v1/sequence_steps/{sequence_step_id}",
            "/api/v1/sequences/{sequence_id}",
            "/api/v1/tasks/templates/{task_template_id}",
            "/api/v1/tasks/{task_id}/_archive",
            "/api/v1/users/signatures/{signature_id}",
            "/api/v1/user_feedback",
            "/api/v1/view_management/object_list_view_schema/{view_id}",
            "/api/v1/voice/admin/phone_numbers/{phone_number}",
            "/api/v1/workflow_blocks/{block_id}",
            "/api/v1/workflow_edges/{edge_id}",
            "/api/v1/workflow_nodes/{node_id}",
            "/api/v1/workflows/{workflow_id}",
            "/api/v1/trackers/{tracker_id}",
            "/api/v1/stage_criteria/list_value/{stage_list_value_id}",
            "/api/v1/emails/templates/categories/{category_id}",
            "/api/v1/metadata/custom/object/{custom_object_id}/field_v2/{custom_field_id}",
            "/api/v1/metadata/association_records",
            "/api/v1/sequence_step_variants/{variant_id}",
            "/api/v1/sequence_enrollments/{enrollment_id}",
            "/api/v1/crm_integrity/{job_id}",
            "/api/v1/stage_criteria_v2/stage_exit_criteria/{stage_exit_criteria_id}",
            "/api/v1/stage_criteria_v2/stage_list_entrance_criteria/{stage_list_entrance_criteria_id}",
        },
        "put": {
            "/api/v1/activities/calendars/events/{calendar_event_id}",
            "/api/v1/business_process/organization_sales_methodology",
            "/api/v1/contacts/{contact_id}/emails",
            "/api/v1/contacts/{contact_id}/phone_numbers",
            "/api/v1/metadata/custom/object/{custom_object_id}",
            "/api/v1/organization/{organization_id}",
            "/api/v1/organization/{organization_id}/reset_extraction_configs",
            "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}/values",
            "/api/v1/pipelines/{pipeline_id}/contacts/_all",
            "/api/v1/select_lists/{select_list_id}/value",
            "/api/v1/stage_criteria/list_value/{stage_list_value_id}",
            "/api/v1/voice/admin",
            "/api/v1/metadata/associations_records",
            "/api/v1/pipelines/{pipeline_id}/contact_pipeline_roles",
            "/api/v1/stage_criteria_v2/stage_exit_criteria/{stage_value_id}",
            "/api/v1/stage_criteria_v2/stage_list_entrance_criteria/{stage_list_id}",
        },
    }

    path_by_http_method: dict[str, set[str]] = {}
    for path, methods in response.json()["paths"].items():
        for method in methods:
            path_by_http_method.setdefault(method, set()).add(path)
    assert path_by_http_method == existing_path_by_method

    # except for test-only endpoints, please don't add any new endpoints that contain hyphens
    # let's use underscores for consistency
    excluded_paths_for_hyphen_check = {
        "/api/v1/connect/test-only/auth/google",
        "/api/v1/connect/test-only/auth/microsoft",
        "/api/v1/connect/test-only/home",
        "/api/v1/connect/test-only/login/google",
        "/api/v1/connect/test-only/login/microsoft",
        "/api/v1/connect/test-only/logout/google",
        "/api/v1/connect/test-only/logout/microsoft",
        "/api/v1/connect/test-only/zoom/create_meeting",
        "/api/v1/connect/test-only/zoom/home",
        "/api/v1/connect/test-only/zoom/login",
        "/api/v1/connect/test-only/zoom/logout",
        "/api/v1/connect/test-only/zoom/update_meeting",
        "/api/v1/webhook/twilio/call-status",
        "/api/v1/webhook/twilio/call-redirect",
        "/api/v1/webhook/twilio/recording-status",
        "/api/v1/voice/admin/provider-accounts",
        "/api/v1/voice/admin/provider-accounts/{provider_account_id}",
        "/api/v1/voice/phone-numbers",
        "/api/v1/voice/phone-numbers/purchase",
        "/api/v1/voice/available-numbers/search",
    }

    for method, paths in existing_path_by_method.items():
        for path in paths:
            if path in excluded_paths_for_hyphen_check:
                continue
            assert "-" not in path, (
                f"{method} - {path} contains a hyphen, it's not wrong, but by repo convention, we should always use underscores"
            )
