from uuid import uuid4

import pytest
from faker import Faker

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.type.metadata.common import ObjectAccessStatus
from salestech_be.core.account.service.account_data_integrity_service import (
    AccountDataIntegrityService,
)
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.crm_integrity_repository import CRMIntegrityJobRepository
from salestech_be.db.models.account import Account
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityJob,
    EntityType,
    IntegrityJobType,
    JobStatus,
    MoveContactToAccountContextualParam,
)
from salestech_be.util.time import zoned_utc_now


async def test_acquire_lock_for_integrity_job_regular_case(
    faker: <PERSON>aker,
    account_service: AccountService,
    account_data_integrity_service: AccountDataIntegrityService,
    account_repository: AccountRepository,
    crm_integrity_job_repository: CRMIntegrityJobRepository,
) -> None:
    organization_id = uuid4()
    locking_job_id = uuid4()
    user_id = uuid4()

    created_account = await account_service.create_account_v2(
        create_account_request=CreateAccountRequest(
            display_name=faker.company(),
            owner_user_id=user_id,
        ),
        organization_id=organization_id,
        user_id=user_id,
    )

    # create a running integrity job but not move contact to account job
    await crm_integrity_job_repository.insert(
        CRMIntegrityJob(
            id=locking_job_id,
            type=IntegrityJobType.MERGE,
            status=JobStatus.RUNNING,
            src_entity_type=EntityType.ACCOUNT,
            src_entity_id=created_account.id,
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=uuid4(),
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            organization_id=organization_id,
        )
    )

    # success case
    updated_account = (
        await account_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=locking_job_id,
            account_id=created_account.id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )

    assert updated_account.id == created_account.id
    assert updated_account.integrity_job_started_at is not None
    assert updated_account.integrity_job_started_by_user_id == user_id
    assert updated_account.integrity_job_started_by_job_ids == [locking_job_id]
    assert updated_account.access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING

    # already locked case
    with pytest.raises(ResourceNotFoundError):
        await account_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=uuid4(),
            account_id=created_account.id,
            user_id=user_id,
            organization_id=organization_id,
        )

    # contact not found case
    with pytest.raises(ResourceNotFoundError):
        await account_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=uuid4(),
            account_id=uuid4(),
            user_id=user_id,
            organization_id=organization_id,
        )

    # edge case: account is locked but without job_ids
    patched_account = await account_repository.update_by_primary_key(
        table_model=Account,
        exclude_locked_by_integrity_jobs=False,
        exclude_deleted_or_archived=False,
        primary_key_to_value={"id": created_account.id},
        column_to_update={"integrity_job_started_by_job_ids": []},
    )

    assert patched_account
    assert patched_account.integrity_job_started_by_job_ids == []

    with pytest.raises(ResourceNotFoundError):
        await account_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=uuid4(),
            account_id=created_account.id,
            user_id=user_id,
            organization_id=organization_id,
        )


async def test_acquire_lock_for_integrity_job_special_case(
    faker: Faker,
    account_service: AccountService,
    account_data_integrity_service: AccountDataIntegrityService,
    account_repository: AccountRepository,
    crm_integrity_job_repository: CRMIntegrityJobRepository,
) -> None:
    organization_id = uuid4()
    locking_job_id = uuid4()
    job_id_1 = uuid4()
    job_id_2 = uuid4()
    user_id = uuid4()

    created_account = await account_service.create_account_v2(
        create_account_request=CreateAccountRequest(
            display_name=faker.company(),
            owner_user_id=user_id,
        ),
        organization_id=organization_id,
        user_id=user_id,
    )

    # create a running integrity job, must be not move contact to account job
    await crm_integrity_job_repository.insert(
        CRMIntegrityJob(
            id=job_id_1,
            type=IntegrityJobType.MOVE,
            status=JobStatus.RUNNING,
            src_entity_type=EntityType.CONTACT,
            src_entity_id=uuid4(),
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=created_account.id,
            contextual_param=MoveContactToAccountContextualParam(
                src_account_id=uuid4(),
            ),
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            organization_id=organization_id,
        )
    )

    await crm_integrity_job_repository.insert(
        CRMIntegrityJob(
            id=job_id_2,
            type=IntegrityJobType.MOVE,
            status=JobStatus.RUNNING,
            src_entity_type=EntityType.CONTACT,
            src_entity_id=uuid4(),
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=created_account.id,
            contextual_param=MoveContactToAccountContextualParam(
                src_account_id=None,
            ),
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            organization_id=organization_id,
        )
    )

    await crm_integrity_job_repository.insert(
        CRMIntegrityJob(
            id=locking_job_id,
            type=IntegrityJobType.MOVE,
            status=JobStatus.RUNNING,
            src_entity_type=EntityType.CONTACT,
            src_entity_id=uuid4(),
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=created_account.id,
            contextual_param=MoveContactToAccountContextualParam(
                src_account_id=None,
            ),
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            organization_id=organization_id,
        )
    )

    patched_account = await account_repository.update_by_primary_key(
        table_model=Account,
        exclude_locked_by_integrity_jobs=False,
        exclude_deleted_or_archived=False,
        primary_key_to_value={"id": created_account.id},
        column_to_update={
            "integrity_job_started_by_job_ids": [job_id_1, job_id_2],
            "integrity_job_started_at": zoned_utc_now(),
            "integrity_job_started_by_user_id": user_id,
        },
    )

    assert patched_account
    assert set(patched_account.integrity_job_started_by_job_ids) == {job_id_1, job_id_2}

    # success case
    with pytest.raises(ResourceNotFoundError):
        await account_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=locking_job_id,
            account_id=created_account.id,
            user_id=user_id,
            organization_id=organization_id,
            is_dest_account_in_move_contact_to_account_job=False,
        )

    updated_account = (
        await account_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=locking_job_id,
            account_id=created_account.id,
            user_id=user_id,
            organization_id=organization_id,
            is_dest_account_in_move_contact_to_account_job=True,
        )
    )

    assert updated_account.id == created_account.id
    assert updated_account.integrity_job_started_at is not None
    assert updated_account.integrity_job_started_by_user_id == user_id
    assert set(updated_account.integrity_job_started_by_job_ids) == {
        locking_job_id,
        job_id_1,
        job_id_2,
    }
    assert updated_account.access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING

    # already locked case
    with pytest.raises(ResourceNotFoundError):
        await account_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=uuid4(),
            account_id=created_account.id,
            user_id=user_id,
            organization_id=organization_id,
            is_dest_account_in_move_contact_to_account_job=False,
        )

    new_job_id = uuid4()
    updated_account = (
        await account_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=new_job_id,
            account_id=created_account.id,
            user_id=user_id,
            organization_id=organization_id,
            is_dest_account_in_move_contact_to_account_job=True,
        )
    )
    assert updated_account.id == created_account.id
    assert updated_account.integrity_job_started_at is not None
    assert updated_account.integrity_job_started_by_user_id == user_id
    assert set(updated_account.integrity_job_started_by_job_ids) == {
        locking_job_id,
        job_id_1,
        job_id_2,
        new_job_id,
    }
    assert updated_account.access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING

    # edge case: account is locked but without job_ids
    patched_account = await account_repository.update_by_primary_key(
        table_model=Account,
        exclude_locked_by_integrity_jobs=False,
        exclude_deleted_or_archived=False,
        primary_key_to_value={"id": created_account.id},
        column_to_update={"integrity_job_started_by_job_ids": []},
    )

    assert patched_account
    assert patched_account.integrity_job_started_by_job_ids == []

    with pytest.raises(ResourceNotFoundError):
        await account_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=locking_job_id,
            account_id=created_account.id,
            user_id=user_id,
            organization_id=organization_id,
            is_dest_account_in_move_contact_to_account_job=True,
        )
    with pytest.raises(ResourceNotFoundError):
        await account_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=locking_job_id,
            account_id=created_account.id,
            user_id=user_id,
            organization_id=organization_id,
            is_dest_account_in_move_contact_to_account_job=False,
        )


async def test_release_lock_for_integrity_job_regular_case(
    faker: Faker,
    account_service: AccountService,
    account_data_integrity_service: AccountDataIntegrityService,
    account_repository: AccountRepository,
    crm_integrity_job_repository: CRMIntegrityJobRepository,
) -> None:
    organization_id = uuid4()
    locking_job_id = uuid4()
    user_id = uuid4()

    created_account = await account_service.create_account_v2(
        create_account_request=CreateAccountRequest(
            display_name=faker.company(),
            owner_user_id=user_id,
        ),
        organization_id=organization_id,
        user_id=user_id,
    )

    # create a running integrity job but not move contact to account job
    await crm_integrity_job_repository.insert(
        CRMIntegrityJob(
            id=locking_job_id,
            type=IntegrityJobType.MERGE,
            status=JobStatus.RUNNING,
            src_entity_type=EntityType.ACCOUNT,
            src_entity_id=created_account.id,
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=uuid4(),
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            organization_id=organization_id,
        )
    )

    # success case (lock)
    updated_account = (
        await account_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=locking_job_id,
            account_id=created_account.id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )

    assert updated_account.id == created_account.id
    assert updated_account.integrity_job_started_at is not None
    assert updated_account.integrity_job_started_by_user_id == user_id
    assert updated_account.integrity_job_started_by_job_ids == [locking_job_id]
    assert updated_account.access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING

    # success case (release)
    updated_account = await account_data_integrity_service.free_lock_for_integrity_job(
        job_id=locking_job_id,
        account_id=created_account.id,
        user_id=user_id,
        organization_id=organization_id,
    )

    assert updated_account.id == created_account.id
    assert updated_account.integrity_job_started_at is None
    assert updated_account.integrity_job_started_by_user_id is None
    assert updated_account.integrity_job_started_by_job_ids == []
    assert updated_account.access_status == ObjectAccessStatus.ACTIVE

    # edge case: free a job_id not in list
    updated_account = await account_data_integrity_service.free_lock_for_integrity_job(
        job_id=uuid4(),
        account_id=created_account.id,
        user_id=user_id,
        organization_id=organization_id,
    )

    assert updated_account.id == created_account.id
    assert updated_account.integrity_job_started_at is None
    assert updated_account.integrity_job_started_by_user_id is None
    assert updated_account.integrity_job_started_by_job_ids == []
    assert updated_account.access_status == ObjectAccessStatus.ACTIVE


async def test_release_lock_for_integrity_job_special_case(
    faker: Faker,
    account_service: AccountService,
    account_data_integrity_service: AccountDataIntegrityService,
    account_repository: AccountRepository,
    crm_integrity_job_repository: CRMIntegrityJobRepository,
) -> None:
    organization_id = uuid4()
    job_id_1 = uuid4()
    job_id_2 = uuid4()
    user_id = uuid4()

    created_account = await account_service.create_account_v2(
        create_account_request=CreateAccountRequest(
            display_name=faker.company(),
            owner_user_id=user_id,
        ),
        organization_id=organization_id,
        user_id=user_id,
    )

    # create a running integrity job, must be not move contact to account job
    await crm_integrity_job_repository.insert(
        CRMIntegrityJob(
            id=job_id_1,
            type=IntegrityJobType.MOVE,
            status=JobStatus.RUNNING,
            src_entity_type=EntityType.CONTACT,
            src_entity_id=uuid4(),
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=created_account.id,
            contextual_param=MoveContactToAccountContextualParam(
                src_account_id=uuid4(),
            ),
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            organization_id=organization_id,
        )
    )

    await crm_integrity_job_repository.insert(
        CRMIntegrityJob(
            id=job_id_2,
            type=IntegrityJobType.MOVE,
            status=JobStatus.RUNNING,
            src_entity_type=EntityType.CONTACT,
            src_entity_id=uuid4(),
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=created_account.id,
            contextual_param=MoveContactToAccountContextualParam(
                src_account_id=None,
            ),
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            organization_id=organization_id,
        )
    )

    # success case (lock)
    updated_account = (
        await account_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=job_id_1,
            account_id=created_account.id,
            user_id=user_id,
            organization_id=organization_id,
            is_dest_account_in_move_contact_to_account_job=True,
        )
    )

    assert updated_account.id == created_account.id
    assert updated_account.integrity_job_started_at is not None
    assert updated_account.integrity_job_started_by_user_id == user_id
    assert set(updated_account.integrity_job_started_by_job_ids) == {job_id_1}
    assert updated_account.access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING

    updated_account = (
        await account_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=job_id_2,
            account_id=created_account.id,
            user_id=user_id,
            organization_id=organization_id,
            is_dest_account_in_move_contact_to_account_job=True,
        )
    )

    assert updated_account.id == created_account.id
    assert updated_account.integrity_job_started_at is not None
    assert updated_account.integrity_job_started_by_user_id == user_id
    assert set(updated_account.integrity_job_started_by_job_ids) == {job_id_1, job_id_2}
    assert updated_account.access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING

    # success case (release)
    updated_account = await account_data_integrity_service.free_lock_for_integrity_job(
        job_id=job_id_1,
        account_id=created_account.id,
        user_id=user_id,
        organization_id=organization_id,
    )

    assert updated_account.id == created_account.id
    assert updated_account.integrity_job_started_at is not None
    assert updated_account.integrity_job_started_by_user_id == user_id
    assert set(updated_account.integrity_job_started_by_job_ids) == {job_id_2}
    assert updated_account.access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING

    updated_account = await account_data_integrity_service.free_lock_for_integrity_job(
        job_id=job_id_2,
        account_id=created_account.id,
        user_id=user_id,
        organization_id=organization_id,
    )

    assert updated_account.id == created_account.id
    assert updated_account.integrity_job_started_at is None
    assert updated_account.integrity_job_started_by_user_id is None
    assert updated_account.integrity_job_started_by_job_ids == []
    assert updated_account.access_status == ObjectAccessStatus.ACTIVE
