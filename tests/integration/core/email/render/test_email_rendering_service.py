import re
import uuid
from collections.abc import Awaitable, Callable
from unittest.mock import patch
from uuid import UUID

import pytest
from pydantic import BaseModel

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdSelectListIdentifier,
)
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.email.account.schema import (
    CreateEmailAccountRequest,
    MockEmailAccountConfigParams,
)
from salestech_be.core.email.account.service_v2 import EmailAccountServiceV2
from salestech_be.core.email.render.email_rendering_service import EmailRenderingService
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.user.signature.service import SignatureService
from salestech_be.core.user.signature.types_v2 import CreateSignatureRequest
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.core.variable.types import EmailSenderUserBinding
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.organization_repository import OrganizationRepository
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.models.account import Account, AccountStatus
from salestech_be.db.models.contact import (
    CreateContactAccountRoleRequest,
    CreateContactRequest,
    CreateDbContactEmailRequest,
    CreateDbContactRequest,
)
from salestech_be.db.models.organization import Organization
from salestech_be.db.models.user_organization_association import (
    UserOrganizationAssociationStatus,
)
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.organization.schema import (
    OrganizationPreferenceRequest,
    OrganizationSequenceSettingsRequest,
)


# Helper model for sequence settings
class SequenceSettings(BaseModel):
    opt_out_message_template: str
    append_opt_out_after_signature: bool = True


async def setup_organization_preference(
    email_rendering_service: EmailRenderingService,
    organization_id: UUID,
    opt_out_template: str,
    append_opt_out: bool = True,
) -> None:
    """Set up organization preference with sequence settings."""
    # Only set opt_out_message_template if it's not empty and contains the placeholder pattern
    template_value = (
        opt_out_template
        if opt_out_template and re.search(r"<%(.*?)%>", opt_out_template)
        else None
    )

    # Create sequence settings with template value
    sequence_settings = OrganizationSequenceSettingsRequest(
        append_opt_out_after_signature=append_opt_out,
        opt_out_message_template=template_value,
    )

    # Create the preference request
    preference_request = OrganizationPreferenceRequest(
        sequence_settings=sequence_settings
    )

    # Create or update the organization preference
    preference_service = email_rendering_service.organization_preference_service
    await preference_service.upsert_organization_preference(
        user_id=UUID(
            "11111111-1111-1111-1111-111111111111"
        ),  # Mock user ID for testing
        organization_id=organization_id,
        data=preference_request,
    )


def test_convert_http_to_https_with_http_content() -> None:
    content = "http://example.com"
    expected_content = "https://example.com"
    assert EmailRenderingService._convert_http_to_https(content) == expected_content


def test_convert_http_to_https_with_https_content() -> None:
    content = "https://example.com"
    expected_content = "https://example.com"
    assert EmailRenderingService._convert_http_to_https(content) == expected_content


def test_convert_http_to_https_with_mixed_content() -> None:
    content = "http://example.com and https://example.com"
    expected_content = "https://example.com and https://example.com"
    assert EmailRenderingService._convert_http_to_https(content) == expected_content


def test_convert_http_to_https_with_no_http_content() -> None:
    content = "example.com"
    expected_content = "example.com"
    assert EmailRenderingService._convert_http_to_https(content) == expected_content


def test_convert_http_to_https_with_empty_or_none_content() -> None:
    content = ""
    expected_content = ""
    assert EmailRenderingService._convert_http_to_https(content) == expected_content


@pytest.mark.parametrize(
    "use_default",
    [
        False,
        True,
    ],
)
async def test_render_email_with_domain_models(
    email_rendering_service: EmailRenderingService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_v2: EmailAccountServiceV2,
    user_repository: UserRepository,
    contact_repo: ContactRepository,
    account_repo: AccountRepository,
    organization_repository: OrganizationRepository,
    select_list_service: InternalSelectListService,
    use_default: bool,
) -> None:
    user_id, organization_id = await make_user_org()
    user_auth_context = UserAuthContext(
        user_id=user_id, organization_id=organization_id
    )

    org = await organization_repository.find_by_primary_key(
        Organization, id=organization_id
    )
    assert org is not None
    assert org.display_name is not None

    # Set up contact stage select list
    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id,
        user_id=user_id,
    )

    default_stage_list = (
        await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
    )
    assert default_stage_list and default_stage_list.default_or_initial_active_value

    # prepare: create email account for the user
    sender_email = "<EMAIL>"
    email_account = await email_account_service_v2.create_and_warmup_email_account(
        create_email_account_request=CreateEmailAccountRequest(
            email=sender_email,
            owner_user_id=user_id,
            outbound_domain_id=uuid.uuid4(),
            warmup_limit=10,
            daily_quota=40,
            first_name="John",
            last_name="Doe",
            email_account_pool_ids=[],
            is_in_default_pool=True,
            mock_config_params=MockEmailAccountConfigParams(
                mock_purchase=True,
                mock_domain_name="reevo.ai",
                ignore_quota=True,
            ),
        ),
        user_auth_context=user_auth_context,
    )

    assert email_account is not None

    # create account
    account = await account_repo.insert(
        Account(
            id=uuid.uuid4(),
            display_name="pre-engagement account",
            status=AccountStatus.TARGET,
            owner_user_id=user_id,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            organization_id=organization_id,
        )
    )
    assert account.display_name == "pre-engagement account"

    # Prepare: create a contact
    contact_email = "<EMAIL>"
    req = CreateContactRequest(
        contact=CreateDbContactRequest(
            created_by_user_id=user_id,
            first_name="First name",
            display_name="Test Display Name",
            owner_user_id=user_id,
            stage_id=default_stage_list.default_or_initial_active_value.id,
            title="BDR",
        ),
        contact_emails=[
            CreateDbContactEmailRequest(
                email=contact_email,
                is_contact_primary=True,
            )
        ],
        contact_account_roles=[
            CreateContactAccountRoleRequest(
                account_id=account.id,
                is_primary_account=True,
            )
        ],
    )
    contact_dto = await contact_repo.create_contact(
        req=req, organization_id=organization_id, user_id=user_id
    )
    db_contact = contact_dto.contact
    assert db_contact is not None
    assert db_contact.primary_phone_number is None

    subject = "Test subject for {{contact.display_name}}"
    body_html = """
    <div>
        Contact Information:
        - Name: {{contact.display_name}}
        - First Name: {{contact.first_name}}
        - Email: {{contact.primary_email}}
        - Phone: {{contact.primary_phone_number}}
        - Title: {{contact.title}}

        Account Information:
        - Company: {{account.display_name}}

        Sender Information:
        - Name: {{sender.display_name}}
        - First Name: {{sender.first_name}}
        - Email: {{sender.email}}
        - Company: {{sender.company}}

        Current Date/Time:
        - Date: {{current.date}}
        - Time: {{current.time}}
        - Day: {{current.day}}
        - Month: {{current.month}}
        - Year: {{current.year}}

        Others:
        {{ contact.display_name}}
        {{contact.primary_email }}
        {{ contact.primary_phone_number }}
        {{some.unknown}}
    </div>
    """

    (
        rendered_subject,
        rendered_body_html,
        unresolved_vars,
    ) = await email_rendering_service.render_email_with_domain_models(
        sender_email_account_id=email_account.id,
        recipient_contact_id=db_contact.id,
        pre_render_subject=subject,
        pre_render_body_html=body_html,
        organization_id=organization_id,
        use_default=use_default,
        user_id=user_id,
    )
    assert rendered_subject == "Test subject for Test Display Name"
    assert "{{contact.display_name}}" not in rendered_body_html
    assert "{{contact.first_name}}" not in rendered_body_html
    assert "{{contact.primary_email}}" not in rendered_body_html
    assert "{{contact.title}}" not in rendered_body_html
    if not use_default:
        assert "{{contact.primary_phone_number}}" in rendered_body_html
    else:
        assert "+****************" in rendered_body_html

    assert "{{account.display_name}}" not in rendered_body_html

    assert "{{sender.display_name}}" not in rendered_body_html
    assert "{{sender.first_name}}" not in rendered_body_html
    assert "{{sender.email}}" not in rendered_body_html
    assert "{{sender.company}}" not in rendered_body_html
    assert org.display_name in rendered_body_html

    assert "{{current.date}}" not in rendered_body_html
    assert "{{current.time}}" not in rendered_body_html
    assert "{{current.day}}" not in rendered_body_html
    assert "{{current.month}}" not in rendered_body_html
    assert "{{current.year}}" not in rendered_body_html

    assert "{{ contact.display_name}}" in rendered_body_html
    assert "{{contact.primary_email }}" in rendered_body_html
    assert "{{ contact.primary_phone_number }}" in rendered_body_html
    assert "{{some.unknown}}" in rendered_body_html

    if not use_default:
        assert unresolved_vars == {
            "{{ contact.primary_phone_number }}",
            "{{ contact.display_name}}",
            "{{contact.primary_email }}",
            "{{contact.primary_phone_number}}",
            "{{some.unknown}}",
        }
    else:
        assert unresolved_vars == {
            "{{ contact.primary_phone_number }}",
            "{{ contact.display_name}}",
            "{{contact.primary_email }}",
            "{{some.unknown}}",
        }


async def test_render_email_with_domain_models_and_phone_number(
    email_rendering_service: EmailRenderingService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_v2: EmailAccountServiceV2,
    user_repository: UserRepository,
    contact_repo: ContactRepository,
    account_repo: AccountRepository,
    organization_repository: OrganizationRepository,
    select_list_service: InternalSelectListService,
) -> None:
    """Test render_email_with_domain_models with phone number variables."""
    user_id, organization_id = await make_user_org()
    user_auth_context = UserAuthContext(
        user_id=user_id, organization_id=organization_id
    )

    # Set up organization
    org = await organization_repository.find_by_primary_key(
        Organization, id=organization_id
    )
    assert org is not None
    assert org.display_name is not None

    # Set up contact stage select list
    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id,
        user_id=user_id,
    )

    default_stage_list = (
        await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
    )
    assert default_stage_list and default_stage_list.default_or_initial_active_value

    # Create email account
    sender_email = "<EMAIL>"
    email_account = await email_account_service_v2.create_and_warmup_email_account(
        create_email_account_request=CreateEmailAccountRequest(
            email=sender_email,
            owner_user_id=user_id,
            outbound_domain_id=uuid.uuid4(),
            warmup_limit=10,
            daily_quota=40,
            first_name="John",
            last_name="Doe",
            email_account_pool_ids=[],
            is_in_default_pool=True,
            mock_config_params=MockEmailAccountConfigParams(
                mock_purchase=True,
                mock_domain_name="reevo.ai",
                ignore_quota=True,
            ),
        ),
        user_auth_context=user_auth_context,
    )

    assert email_account is not None

    # Create account
    account = await account_repo.insert(
        Account(
            id=uuid.uuid4(),
            display_name="Test Account",
            status=AccountStatus.TARGET,
            owner_user_id=user_id,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            organization_id=organization_id,
        )
    )

    # Create contact
    contact_email = "<EMAIL>"
    req = CreateContactRequest(
        contact=CreateDbContactRequest(
            created_by_user_id=user_id,
            first_name="First name",
            display_name="Test Display Name",
            owner_user_id=user_id,
            stage_id=default_stage_list.default_or_initial_active_value.id,
            title="BDR",
        ),
        contact_emails=[
            CreateDbContactEmailRequest(
                email=contact_email,
                is_contact_primary=True,
            )
        ],
        contact_account_roles=[
            CreateContactAccountRoleRequest(
                account_id=account.id,
                is_primary_account=True,
            )
        ],
    )
    contact_dto = await contact_repo.create_contact(
        req=req, organization_id=organization_id, user_id=user_id
    )
    db_contact = contact_dto.contact
    assert db_contact is not None

    # Create email template with phone number variable
    subject = "Test subject"
    body_html = """
    <div>
        Sender Phone Information:
        - Phone Number: {{sender.phone_number}}
        - ReeVo Phone Number: {{sender.reevo_phone_number}}
        - Phone Number RFC 3966: {{sender.phone_number_rfc_3966}}
        - ReeVo Phone Number RFC 3966: {{sender.reevo_phone_number_rfc_3966}}
    </div>
    """

    # Mock the user service to return a user with phone number
    with patch.object(
        email_rendering_service.user_service,
        "get_user_v2",
        return_value=OrganizationUserV2(
            id=user_id,
            organization_id=organization_id,
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            display_name="Test User",
            phone_number="+***********",  # Add phone number to user
            phone_number_rfc_3966="tel:******-111-1111",  # Add RFC 3966 format with hyphens
            reevo_phone_number="+***********",  # Add reevo phone number to user
            reevo_phone_number_rfc_3966="tel:******-222-2222",  # Add RFC 3966 format with hyphens
            organization_roles=[],  # Add required field
            organization_association_status=UserOrganizationAssociationStatus.ACTIVE,  # Add required field
            created_at=zoned_utc_now(),  # Add required field
        ),
    ):
        # Test with user_id provided and use_default=False
        (
            rendered_subject,
            rendered_body_html,
            unresolved_vars,
        ) = await email_rendering_service.render_email_with_domain_models(
            sender_email_account_id=email_account.id,
            recipient_contact_id=db_contact.id,
            pre_render_subject=subject,
            pre_render_body_html=body_html,
            organization_id=organization_id,
            use_default=False,
            user_id=user_id,
        )

        # Verify rendering
        assert rendered_subject == "Test subject"
        assert "{{sender.phone_number}}" not in rendered_body_html
        assert "+***********" in rendered_body_html  # Original phone number
        assert (
            "tel:******-111-1111" in rendered_body_html
        )  # RFC 3966 format with hyphens
        assert "{{sender.reevo_phone_number}}" not in rendered_body_html
        assert "+***********" in rendered_body_html  # Original reevo phone number
        assert (
            "tel:******-222-2222" in rendered_body_html
        )  # RFC 3966 format with hyphens
        assert "{{sender.phone_number_rfc_3966}}" not in rendered_body_html
        assert "{{sender.reevo_phone_number_rfc_3966}}" not in rendered_body_html

    # Test with non-existent user_id but use_default=True
    with patch.object(
        EmailSenderUserBinding,
        "from_field_examples",
        return_value=EmailSenderUserBinding(
            phone_number="+***********",
            phone_number_rfc_3966="tel:******-111-1111",
            reevo_phone_number="+***********",
            reevo_phone_number_rfc_3966="tel:******-222-2222",
        ),
    ):
        (
            rendered_subject,
            rendered_body_html,
            unresolved_vars,
        ) = await email_rendering_service.render_email_with_domain_models(
            sender_email_account_id=email_account.id,
            recipient_contact_id=db_contact.id,
            pre_render_subject=subject,
            pre_render_body_html=body_html,
            organization_id=organization_id,
            use_default=True,
            user_id=uuid.uuid4(),  # Non-existent user
        )

        # Verify rendering - default phone number should be used
        assert rendered_subject == "Test subject"
        assert "{{sender.phone_number}}" not in rendered_body_html
        assert "+***********" in rendered_body_html  # Original phone number
        assert (
            "tel:******-111-1111" in rendered_body_html
        )  # RFC 3966 format with hyphens
        assert "{{sender.reevo_phone_number}}" not in rendered_body_html
        assert "+***********" in rendered_body_html  # Original reevo phone number
        assert (
            "tel:******-222-2222" in rendered_body_html
        )  # RFC 3966 format with hyphens
        assert "{{sender.phone_number_rfc_3966}}" not in rendered_body_html
        assert "{{sender.reevo_phone_number_rfc_3966}}" not in rendered_body_html


async def test_render_email_with_signature_div_and_variables(
    email_rendering_service: EmailRenderingService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_v2: EmailAccountServiceV2,
    signature_service: SignatureService,
    user_repository: UserRepository,
    contact_repo: ContactRepository,
    account_repo: AccountRepository,
    organization_repository: OrganizationRepository,
    select_list_service: InternalSelectListService,
) -> None:
    """Test email rendering with variable substitution and signature placement."""
    user_id, organization_id = await make_user_org()
    user_auth_context = UserAuthContext(
        user_id=user_id, organization_id=organization_id
    )

    # Set up organization
    org = await organization_repository.find_by_primary_key(
        Organization, id=organization_id
    )
    assert org is not None
    assert org.display_name is not None

    # Set up contact stage select list
    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id,
        user_id=user_id,
    )

    default_stage_list = (
        await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
    )
    assert default_stage_list and default_stage_list.default_or_initial_active_value

    # Create email account with signature
    signature = await signature_service.create_signature(
        user_auth_context=user_auth_context,
        create_signature_request=CreateSignatureRequest(
            signature_html="<div>This is my signature</div>",
            name="My signature",
        ),
    )
    db_signatures = await signature_service.find_signature_by_ids(
        signature_ids=[signature.id],
        organization_id=organization_id,
    )

    assert db_signatures is not None
    db_signature = db_signatures[0]
    assert db_signature is not None
    signature_id = db_signature.id

    sender_email = "<EMAIL>"
    email_account = await email_account_service_v2.create_and_warmup_email_account(
        create_email_account_request=CreateEmailAccountRequest(
            email=sender_email,
            owner_user_id=user_id,
            signature_id=signature_id,
            outbound_domain_id=uuid.uuid4(),
            warmup_limit=10,
            daily_quota=40,
            first_name="John",
            last_name="Doe",
            email_account_pool_ids=[],
            is_in_default_pool=True,
            mock_config_params=MockEmailAccountConfigParams(
                mock_purchase=True,
                mock_domain_name="reevo.ai",
                ignore_quota=True,
            ),
        ),
        user_auth_context=user_auth_context,
    )

    assert email_account is not None

    # Create account
    account = await account_repo.insert(
        Account(
            id=uuid.uuid4(),
            display_name="Test Account",
            status=AccountStatus.TARGET,
            owner_user_id=user_id,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            organization_id=organization_id,
        )
    )

    # Create contact
    contact_email = "<EMAIL>"
    req = CreateContactRequest(
        contact=CreateDbContactRequest(
            created_by_user_id=user_id,
            first_name="First name",
            display_name="Test Display Name",
            owner_user_id=user_id,
            stage_id=default_stage_list.default_or_initial_active_value.id,
            title="BDR",
        ),
        contact_emails=[
            CreateDbContactEmailRequest(
                email=contact_email,
                is_contact_primary=True,
            )
        ],
        contact_account_roles=[
            CreateContactAccountRoleRequest(
                account_id=account.id,
                is_primary_account=True,
            )
        ],
    )
    contact_dto = await contact_repo.create_contact(
        req=req, organization_id=organization_id, user_id=user_id
    )
    db_contact = contact_dto.contact
    assert db_contact is not None

    # Simple email body content (no outer div)
    email_body = """
    <p>Dear {{contact.display_name}},</p>
    <p>I hope this email finds you well at {{account.display_name}}.</p>
    <p>We're writing from {{sender.company}} about our services.</p>
    <p>Current date: {{current.date}}</p>
    """

    # Test with render_email_with_domain_models (no <br/> in signature)
    (
        _,
        rendered_domain,
        _,
    ) = await email_rendering_service.render_email_with_domain_models(
        sender_email_account_id=email_account.id,
        recipient_contact_id=db_contact.id,
        pre_render_body_html=email_body,
        organization_id=organization_id,
        include_email_signature=True,
        use_default=True,
        user_id=user_id,
    )

    # Test without signature
    (
        _,
        rendered_without_signature,
        _,
    ) = await email_rendering_service.render_email_with_domain_models(
        sender_email_account_id=email_account.id,
        recipient_contact_id=db_contact.id,
        pre_render_body_html=email_body,
        organization_id=organization_id,
        include_email_signature=False,
        use_default=True,
        user_id=user_id,
    )

    # Assert variable substitution
    for rendered_html in [rendered_domain, rendered_without_signature]:
        assert "{{contact.display_name}}" not in rendered_html
        assert "Test Display Name" in rendered_html
        assert "{{account.display_name}}" not in rendered_html
        assert "Test Account" in rendered_html
        assert "{{current.date}}" not in rendered_html
        assert "{{sender.company}}" not in rendered_html
        assert org.display_name in rendered_html

    # Check signature containers
    signature_container = '<div class="reevo-signature-container">'

    # 1. Check for the signature container and content in render_email_with_domain_models
    assert signature_container in rendered_domain
    assert "This is my signature" in rendered_domain

    # 2. Verify no signature when include_email_signature=False
    assert signature_container not in rendered_without_signature
    assert "This is my signature" not in rendered_without_signature


async def test_get_sequence_unsubscription_signature_html(
    email_rendering_service: EmailRenderingService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
) -> None:
    """test get_sequence_unsubscription_signature_html function to replace placeholders."""
    user_id, organization_id = await make_user_org()

    # Test scenario 1: use default link to replace placeholder
    template1 = "I want to unsubscribe, please <%tell me%>"
    await setup_organization_preference(
        email_rendering_service=email_rendering_service,
        organization_id=organization_id,
        opt_out_template=template1,
    )

    result = await email_rendering_service.get_sequence_unsubscription_signature_html(
        organization_id=organization_id
    )

    # Verify placeholder is replaced with default link
    assert result is not None
    assert "I want to unsubscribe, please" in result
    assert (
        f'<a id="{settings.unsubscribe_link_html_id}" href="{email_rendering_service.FAKE_GLOBAL_MESSAGE_UNSUBSCRIBE_LINK}">tell me</a>'
        in result
    )
    assert '<div class="reevo-unsubscribe-signature-container">' in result

    # Test scenario 2: use custom link to replace placeholder
    custom_link = "https://example.com/unsubscribe?id=12345"
    template2 = "To stop receiving emails, please <%click here%>"
    await setup_organization_preference(
        email_rendering_service=email_rendering_service,
        organization_id=organization_id,
        opt_out_template=template2,
    )

    result = await email_rendering_service.get_sequence_unsubscription_signature_html(
        organization_id=organization_id,
        unsubscribe_link=custom_link,
    )

    # Verify placeholder is replaced with custom link
    assert result is not None
    assert "To stop receiving emails, please" in result
    assert (
        f'<a id="{settings.unsubscribe_link_html_id}" href="{custom_link}">click here</a>'
        in result
    )

    # Check for the correct format with text before link
    expected_partial = f'To stop receiving emails, please <a id="{settings.unsubscribe_link_html_id}" href="{custom_link}">click here</a>'
    assert expected_partial in result

    # Test scenario 3: template with no placeholder - should return None
    template3 = "This is a message with no placeholders"
    await setup_organization_preference(
        email_rendering_service=email_rendering_service,
        organization_id=organization_id,
        opt_out_template=template3,
    )

    result = await email_rendering_service.get_sequence_unsubscription_signature_html(
        organization_id=organization_id
    )

    # Verify return None since the template doesn't contain placeholders
    assert result is None

    # Test scenario 4: template with no signature
    await setup_organization_preference(
        email_rendering_service=email_rendering_service,
        organization_id=organization_id,
        opt_out_template="",
        append_opt_out=False,
    )

    result = await email_rendering_service.get_sequence_unsubscription_signature_html(
        organization_id=organization_id
    )

    # Verify return None
    assert result is None


async def test_render_email_with_domain_models_unsubscription(
    email_rendering_service: EmailRenderingService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_v2: EmailAccountServiceV2,
    user_repository: UserRepository,
    contact_repo: ContactRepository,
    select_list_service: InternalSelectListService,
) -> None:
    """test add unsubscription info in render_email_with_domain_models."""
    user_id, organization_id = await make_user_org()
    user_auth_context = UserAuthContext(
        user_id=user_id, organization_id=organization_id
    )

    # set contact stage select list
    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id,
        user_id=user_id,
    )

    default_stage_list = (
        await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
    )
    assert default_stage_list and default_stage_list.default_or_initial_active_value

    # create email account
    sender_email = "<EMAIL>"
    email_account = await email_account_service_v2.create_and_warmup_email_account(
        create_email_account_request=CreateEmailAccountRequest(
            email=sender_email,
            owner_user_id=user_id,
            outbound_domain_id=uuid.uuid4(),
            warmup_limit=10,
            daily_quota=40,
            first_name="John",
            last_name="Doe",
            email_account_pool_ids=[],
            is_in_default_pool=True,
            mock_config_params=MockEmailAccountConfigParams(
                mock_purchase=True,
                mock_domain_name="reevo.ai",
                ignore_quota=True,
            ),
        ),
        user_auth_context=user_auth_context,
    )
    assert email_account is not None

    # create contact
    contact_email = "<EMAIL>"
    req = CreateContactRequest(
        contact=CreateDbContactRequest(
            created_by_user_id=user_id,
            first_name="First name",
            display_name="Test Display Name",
            owner_user_id=user_id,
            stage_id=default_stage_list.default_or_initial_active_value.id,
            title="BDR",
        ),
        contact_emails=[
            CreateDbContactEmailRequest(
                email=contact_email,
                is_contact_primary=True,
            )
        ],
    )
    contact_dto = await contact_repo.create_contact(
        req=req, organization_id=organization_id, user_id=user_id
    )
    db_contact = contact_dto.contact
    assert db_contact is not None

    # prepare email template
    subject = "Test subject"
    body_html = "<div>This is a test email</div>"

    # test scenario 1: template with unsubscribe info, use default link
    opt_out_template = "I want to unsubscribe, please <%tell me%>"
    await setup_organization_preference(
        email_rendering_service=email_rendering_service,
        organization_id=organization_id,
        opt_out_template=opt_out_template,
    )

    (
        rendered_subject,
        rendered_body_html,
        unresolved_vars,
    ) = await email_rendering_service.render_email_with_domain_models(
        sender_email_account_id=email_account.id,
        recipient_contact_id=db_contact.id,
        pre_render_subject=subject,
        pre_render_body_html=body_html,
        organization_id=organization_id,
        user_id=user_id,
    )

    # verify unsubscribe info is added to email bottom
    assert "<div>This is a test email</div>" in rendered_body_html
    assert "I want to unsubscribe, please" in rendered_body_html
    assert (
        f'<a id="{settings.unsubscribe_link_html_id}" href="{email_rendering_service.FAKE_GLOBAL_MESSAGE_UNSUBSCRIBE_LINK}">tell me</a>'
        in rendered_body_html
    )
    assert '<div class="reevo-unsubscribe-signature-container">' in rendered_body_html

    # test scenario 2: template with unsubscribe info, use custom link
    custom_link = "https://example.com/unsubscribe?id=12345"
    await setup_organization_preference(
        email_rendering_service=email_rendering_service,
        organization_id=organization_id,
        opt_out_template=opt_out_template,
    )

    (
        rendered_subject,
        rendered_body_html,
        unresolved_vars,
    ) = await email_rendering_service.render_email_with_domain_models(
        sender_email_account_id=email_account.id,
        recipient_contact_id=db_contact.id,
        pre_render_subject=subject,
        pre_render_body_html=body_html,
        organization_id=organization_id,
        unsubscribe_link=custom_link,
        user_id=user_id,
    )

    # verify unsubscribe info is added to email bottom, and use custom link
    assert "<div>This is a test email</div>" in rendered_body_html
    assert "I want to unsubscribe, please" in rendered_body_html
    assert (
        f'<a id="{settings.unsubscribe_link_html_id}" href="{custom_link}">tell me</a>'
        in rendered_body_html
    )
    assert '<div class="reevo-unsubscribe-signature-container">' in rendered_body_html

    # test scenario 3: template with no unsubscribe info
    await setup_organization_preference(
        email_rendering_service=email_rendering_service,
        organization_id=organization_id,
        opt_out_template="",
        append_opt_out=False,
    )

    (
        rendered_subject,
        rendered_body_html,
        unresolved_vars,
    ) = await email_rendering_service.render_email_with_domain_models(
        sender_email_account_id=email_account.id,
        recipient_contact_id=db_contact.id,
        pre_render_subject=subject,
        pre_render_body_html=body_html,
        organization_id=organization_id,
        user_id=user_id,
    )

    # verify unsubscribe info is not added to email.
    assert "<div>This is a test email</div>" in rendered_body_html
    assert "I want to unsubscribe, please" not in rendered_body_html
    assert "reevo-unsubscribe-signature-container" not in rendered_body_html
