import uuid
from uuid import UUID, uuid4

import pytest
from faker import Faker
from pytest_mock import Mo<PERSON><PERSON><PERSON><PERSON>

from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.service.email_data_integrity_service import (
    EmailDataIntegrityService,
)
from salestech_be.core.email.type.email import EmailHydratedParticipant, EmailTag
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dao.template_repository import TemplateRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.models.contact_account_association import ContactAccountAssociation
from salestech_be.db.models.crm_integrity import (
    AssociatedEntityField,
)
from salestech_be.db.models.email_account import EmailAccount
from salestech_be.db.models.global_thread import (
    GlobalMessage,
    GlobalMessageAssociation,
    GlobalThread,
)
from salestech_be.db.models.message import Message, MessageSource, MessageStatus
from salestech_be.db.models.template import Template, TemplateStatus, TemplateType
from salestech_be.db.models.thread import Thread
from salestech_be.util.time import zoned_utc_now


@pytest.fixture()
async def email_account(
    email_account_service_ext: EmailAccountServiceExt,
) -> EmailAccount:
    return await email_account_service_ext.get_or_create_email_account(
        owner_user_id=uuid4(),
        email="<EMAIL>",
        signature_html="test",
        organization_id=uuid4(),
        user_id=uuid4(),
    )


async def test_merge_contact_ids_in_email_by_field_name(
    email_account: EmailAccount,
    email_data_integrity_service: EmailDataIntegrityService,
    message_repository: MessageRepository,
) -> None:
    # Setup test data
    contact_id = uuid4()
    replaced_contact_id = uuid4()
    other_contact_id = uuid4()
    organization_id = uuid4()
    account_id = uuid4()
    message_id = uuid4()
    email = "<EMAIL>"
    other_email = "<EMAIL>"

    # Create message with duplicated and non-duplicated participants
    message = Message(
        id=message_id,
        thread_id=uuid4(),
        email_account_id=email_account.id,
        organization_id=organization_id,
        subject="test",
        send_from=[
            EmailHydratedParticipant(
                contact_id=contact_id,
                email=email,
                account_id=account_id,
            ),
            EmailHydratedParticipant(  # Duplicate
                contact_id=contact_id,
                email=email,
                account_id=account_id,
            ),
            EmailHydratedParticipant(  # Should not be affected
                contact_id=other_contact_id,
                email=other_email,
                account_id=account_id,
            ),
        ],
        send_to=[],
        snippet="test",
        body_text="test",
        body_html="test",
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await message_repository.insert(message)

    # Test merging contacts
    (
        original,
        updated,
    ) = await email_data_integrity_service.merge_contact_ids_in_email_by_field_name(
        message_id=message_id,
        contact_id=contact_id,
        replaced_contact_id=replaced_contact_id,
        organization_id=organization_id,
        field_name=AssociatedEntityField.SEND_FROM.lower(),
    )

    # Verify original message state
    assert original.id == message_id
    assert {
        (p.contact_id, p.email, p.account_id) for p in original.send_from or []
    } == {
        (contact_id, email, account_id),
        (contact_id, email, account_id),
        (other_contact_id, other_email, account_id),
    }

    # Verify updated message state
    assert updated.id == message_id
    assert {(p.contact_id, p.email, p.account_id) for p in updated.send_from or []} == {
        (replaced_contact_id, email, account_id),
        (replaced_contact_id, email, account_id),
        (other_contact_id, other_email, account_id),
    }


async def test_merge_account_ids_in_email_by_field_name(
    email_account: EmailAccount,
    email_data_integrity_service: EmailDataIntegrityService,
    message_repository: MessageRepository,
) -> None:
    # Setup test data
    contact_id = uuid4()
    organization_id = uuid4()
    account_id = uuid4()
    replaced_account_id = uuid4()
    other_account_id = uuid4()
    message_id = uuid4()
    email = "<EMAIL>"

    # Create message with duplicated and non-duplicated participants
    message = Message(
        id=message_id,
        thread_id=uuid4(),
        email_account_id=email_account.id,
        organization_id=organization_id,
        subject="test",
        send_from=[],
        send_to=[
            EmailHydratedParticipant(
                contact_id=contact_id,
                email=email,
                account_id=account_id,
            ),
            EmailHydratedParticipant(  # Duplicate account
                contact_id=contact_id,
                email=email,
                account_id=account_id,
            ),
            EmailHydratedParticipant(  # Different account
                contact_id=contact_id,
                email=email,
                account_id=other_account_id,
            ),
        ],
        snippet="test",
        body_text="test",
        body_html="test",
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await message_repository.insert(message)

    # Test merging accounts
    (
        original,
        updated,
    ) = await email_data_integrity_service.merge_account_ids_in_email_by_field_name(
        message_id=message_id,
        account_id=account_id,
        replaced_account_id=replaced_account_id,
        organization_id=organization_id,
        field_name=AssociatedEntityField.SEND_TO.lower(),
    )

    # Verify original message state
    assert original.id == message_id
    assert {(p.contact_id, p.email, p.account_id) for p in original.send_to or []} == {
        (contact_id, email, account_id),
        (contact_id, email, account_id),
        (contact_id, email, other_account_id),
    }

    # Verify updated message state
    assert updated.id == message_id
    assert {(p.contact_id, p.email, p.account_id) for p in updated.send_to or []} == {
        (contact_id, email, replaced_account_id),
        (contact_id, email, other_account_id),
    }


async def test_replace_contact_by_email_in_email(
    email_account: EmailAccount,
    mocker: MockerFixture,
    email_data_integrity_service: EmailDataIntegrityService,
    message_repository: MessageRepository,
) -> None:
    # Setup test data
    contact_id = uuid4()
    replaced_contact_id = uuid4()
    organization_id = uuid4()
    account_id = uuid4()
    message_id = uuid4()
    email = "<EMAIL>"
    other_email = "<EMAIL>"

    # Create message with various participant combinations
    message = Message(
        id=message_id,
        thread_id=uuid4(),
        email_account_id=email_account.id,
        organization_id=organization_id,
        subject="test",
        send_from=[
            EmailHydratedParticipant(
                contact_id=contact_id,
                email=email,
                account_id=account_id,
            ),
            EmailHydratedParticipant(  # Same contact, different email
                contact_id=contact_id,
                email=other_email,
                account_id=account_id,
            ),
        ],
        send_to=[],
        snippet="test",
        body_text="test",
        body_html="test",
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await message_repository.insert(message)

    # Mock contact account association check
    mocker.patch(
        "salestech_be.core.contact.service.contact_service.ContactService.list_active_contact_account_associations",
        return_value=[
            ContactAccountAssociation(
                id=uuid4(),
                organization_id=organization_id,
                contact_id=replaced_contact_id,
                account_id=account_id,
                is_primary=True,
                created_by_user_id=uuid4(),
                updated_by_user_id=uuid4(),
            )
        ],
    )

    # Test replacing contact
    (
        original,
        updated,
    ) = await email_data_integrity_service.replace_contact_id_in_email_by_field_name(
        message_id=message_id,
        contact_id=contact_id,
        email=email,
        replaced_contact_id=replaced_contact_id,
        organization_id=organization_id,
        field_name=AssociatedEntityField.SEND_FROM.lower(),
    )

    # Verify original message state
    assert original.id == message_id
    assert {
        (p.contact_id, p.email, p.account_id) for p in original.send_from or []
    } == {
        (contact_id, email, account_id),
        (contact_id, other_email, account_id),
    }

    # Verify updated message state
    assert updated.id == message_id
    assert {(p.contact_id, p.email, p.account_id) for p in updated.send_from or []} == {
        (replaced_contact_id, email, account_id),
        (contact_id, other_email, account_id),
    }


async def create_email_objects(
    faker: Faker,
    email_account: EmailAccount,
    thread_repository: ThreadRepository,
    template_repository: TemplateRepository,
    message_repository: MessageRepository,
    contact_id: UUID,
    organization_id: UUID,
    email: str,
    account_id: UUID | None = None,
) -> tuple[UUID, UUID, UUID]:
    template = Template(
        id=uuid.uuid4(),
        name=faker.sentence(),
        template_type=TemplateType.EMAIL,
        status=TemplateStatus.ACTIVE,
        body_text="",
        body_html="",
        is_shared=False,
        version=1,
        include_email_signature=True,
        unsubscription_group_id=None,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    created_template = await template_repository.insert(template)

    # thread
    thread = await thread_repository.insert(
        Thread(
            id=uuid.uuid4(),
            email_account_id=email_account.id,
            organization_id=organization_id,
            subject=created_template.subject or "",
            snippet=created_template.body_text[:100],
            unread=None,
            starred=None,
            has_attachments=bool(created_template.attachment_ids),
            has_drafts=None,
            earliest_message_date=zoned_utc_now(),
            latest_message_sent_date=None,
            latest_message_received_date=None,
            participants=[
                EmailHydratedParticipant(
                    email=email_account.email,
                    name=faker.name(),
                    email_account_id=email_account.id,
                ),
                EmailHydratedParticipant(
                    email=email,
                    name=faker.name(),
                    contact_id=contact_id,
                    account_id=account_id,
                ),
            ],
            tags=[EmailTag.OUTBOUND],
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=None,
        )
    )

    thread_id = thread.id

    message = await message_repository.insert(
        Message(
            id=uuid4(),
            thread_id=thread_id,
            idempotency_key=str(uuid4()),
            source=MessageSource.SEQUENCE,
            email_account_id=email_account.id,
            organization_id=organization_id,
            template_id=created_template.id,
            email_template_version=created_template.version,
            subject=created_template.subject or "",
            send_from=[
                EmailHydratedParticipant(
                    email=email_account.email,
                    name=faker.name(),
                    email_account_id=email_account.id,
                )
            ],
            send_to=[
                EmailHydratedParticipant(
                    email=email,
                    name=faker.name(),
                    contact_id=contact_id,
                    account_id=account_id,
                )
            ],
            cc=created_template.cc,
            bcc=created_template.bcc,
            reply_to=created_template.reply_to,
            reply_to_message_id=None,
            status=MessageStatus.SCHEDULED,
            snippet=created_template.body_text[:100],
            body_text=created_template.body_text,
            body_html=created_template.body_html,
            attachment_ids=created_template.attachment_ids,
            folders=None,
            starred=None,
            unread=None,
            use_draft=None,
            send_at=zoned_utc_now(),
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )

    message_id = message.id

    created_global_thread = await thread_repository.insert(
        GlobalThread(
            id=uuid4(),
            subject=faker.sentence(),
            snippet=faker.text(),
            thread_ids=[thread_id],
            contact_ids=[contact_id],
            account_ids=[account_id] if account_id else None,
            pipeline_id=None,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )

    created_global_message = await thread_repository.insert(
        GlobalMessage(
            id=uuid4(),
            original_message_id=f"test_original_message_id_{uuid.uuid4()}",
            organization_id=organization_id,
            global_thread_id=created_global_thread.id,
            message_received_at=zoned_utc_now(),
            created_at=zoned_utc_now(),
        )
    )

    await thread_repository.insert(
        GlobalMessageAssociation(
            id=uuid4(),
            global_message_id=created_global_message.id,
            message_id=message_id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
        )
    )

    return (created_global_thread.id, thread_id, message_id)


async def test_find_email_objects_by_contact_id(
    mocker: MockerFixture,
    faker: Faker,
    email_account: EmailAccount,
    email_data_integrity_service: EmailDataIntegrityService,
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    template_repository: TemplateRepository,
) -> None:
    contact_id = uuid4()
    organization_id = uuid4()
    account_id = uuid4()

    email = "<EMAIL>"
    email_2 = "<EMAIL>"

    global_thread_id, thread_id, message_id = await create_email_objects(
        faker=faker,
        email_account=email_account,
        thread_repository=thread_repository,
        template_repository=template_repository,
        message_repository=message_repository,
        contact_id=contact_id,
        organization_id=organization_id,
        email=email,
        account_id=account_id,
    )

    global_thread_id_2, thread_id_2, message_id_2 = await create_email_objects(
        faker=faker,
        email_account=email_account,
        thread_repository=thread_repository,
        template_repository=template_repository,
        message_repository=message_repository,
        contact_id=contact_id,
        organization_id=organization_id,
        email=email,
        account_id=None,
    )

    global_thread_id_3, thread_id_3, message_id_3 = await create_email_objects(
        faker=faker,
        email_account=email_account,
        thread_repository=thread_repository,
        template_repository=template_repository,
        message_repository=message_repository,
        contact_id=contact_id,
        organization_id=organization_id,
        email=email_2,
        account_id=None,
    )

    # Test 1: Find by contact_id only
    result = await email_data_integrity_service.find_email_objects_by_contact_id(
        contact_id=contact_id,
        organization_id=organization_id,
    )
    assert {t.id for t in result.global_threads} == {
        global_thread_id,
        global_thread_id_2,
        global_thread_id_3,
    }
    assert {t.id for t in result.messages} == {message_id, message_id_2, message_id_3}
    assert {t.id for t in result.threads} == {thread_id, thread_id_2, thread_id_3}

    # Test 2: Find by contact_id and email
    result = await email_data_integrity_service.find_email_objects_by_contact_id(
        contact_id=contact_id,
        organization_id=organization_id,
        email=email,
    )
    assert {t.id for t in result.global_threads} == {
        global_thread_id,
        global_thread_id_2,
    }
    assert {t.id for t in result.messages} == {message_id, message_id_2}
    assert {t.id for t in result.threads} == {thread_id, thread_id_2}

    # Test 3: Find by contact_id and account_id
    result = await email_data_integrity_service.find_email_objects_by_contact_id(
        contact_id=contact_id,
        organization_id=organization_id,
        account_id=account_id,
    )
    assert {t.id for t in result.global_threads} == {global_thread_id}
    assert {t.id for t in result.messages} == {message_id}
    assert {t.id for t in result.threads} == {thread_id}

    # Test 4: Find with non-existent contact
    result = await email_data_integrity_service.find_email_objects_by_contact_id(
        contact_id=uuid4(),
        organization_id=organization_id,
    )
    assert len(result.global_threads) == 0
    assert len(result.messages) == 0
    assert len(result.threads) == 0


async def test_find_email_objects_by_account_id(
    mocker: MockerFixture,
    faker: Faker,
    email_account: EmailAccount,
    email_data_integrity_service: EmailDataIntegrityService,
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    template_repository: TemplateRepository,
) -> None:
    # Setup test data
    contact_id = uuid4()
    email = "<EMAIL>"
    account_id = uuid4()
    account_id_2 = uuid4()
    organization_id = uuid4()

    global_thread_id, thread_id, message_id = await create_email_objects(
        faker=faker,
        email_account=email_account,
        thread_repository=thread_repository,
        template_repository=template_repository,
        message_repository=message_repository,
        contact_id=contact_id,
        organization_id=organization_id,
        email=email,
        account_id=account_id,
    )

    await create_email_objects(
        faker=faker,
        email_account=email_account,
        thread_repository=thread_repository,
        template_repository=template_repository,
        message_repository=message_repository,
        contact_id=contact_id,
        organization_id=organization_id,
        email=email,
        account_id=account_id_2,
    )

    await create_email_objects(
        faker=faker,
        email_account=email_account,
        thread_repository=thread_repository,
        template_repository=template_repository,
        message_repository=message_repository,
        contact_id=contact_id,
        organization_id=organization_id,
        email=email,
        account_id=None,
    )

    # Test finding by account_id
    result = await email_data_integrity_service.find_email_objects_by_account_id(
        account_id=account_id,
        organization_id=organization_id,
    )
    assert {t.id for t in result.global_threads} == {global_thread_id}
    assert {t.id for t in result.messages} == {message_id}
    assert {t.id for t in result.threads} == {thread_id}

    # Test with non-existent account
    result = await email_data_integrity_service.find_email_objects_by_account_id(
        account_id=uuid4(),
        organization_id=organization_id,
    )
    assert len(result.global_threads) == 0
    assert len(result.messages) == 0
    assert len(result.threads) == 0
