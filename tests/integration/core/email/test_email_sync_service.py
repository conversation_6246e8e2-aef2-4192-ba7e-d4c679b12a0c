from collections.abc import Awaitable, Callable
from datetime import <PERSON><PERSON><PERSON>
from typing import Any
from unittest import mock
from uuid import UUID, uuid4

import pytest
from pytest_mock import MockerFixture

from salestech_be.core.contact.service.contact_resolve_service import (
    ContactResolveService,
)
from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.global_email.global_thread_service import (
    GlobalThreadService,
)
from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.oauth_repository import OauthRepository
from salestech_be.db.dao.user_integration_repository import UserIntegrationRepository
from salestech_be.db.dto.email_attchment_dto import AttachmentDto
from salestech_be.db.models.attachment import Attachment
from salestech_be.db.models.message import EmailProvider, _compress_body_html
from salestech_be.db.models.oauth import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuthUserAuth
from salestech_be.db.models.thread import Thread
from salestech_be.db.models.user_integration import (
    IntegrationProvider,
    IntegrationType,
    UserIntegration,
)
from salestech_be.integrations.google.schema import (
    GoogleMessage,
    MessageBody,
    MessagePart,
    MessagePayload,
)
from salestech_be.integrations.nylas.model import (
    NylasEmailName,
    NylasMessage,
    NylasThread,
)
from salestech_be.services.auth.encryptions import fernet_encryption_manager
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.email.common.email_sync_service import EmailSyncService


@pytest.fixture
async def db_user_auth(
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    oauth_repository: OauthRepository,
    user_integration_repository: UserIntegrationRepository,
) -> OAuthUserAuth:
    user_id, organization_id = await make_user_org()
    raw_access_token = str(uuid4())
    raw_refresh_token = str(uuid4())
    db_user_auth = OAuthUserAuth(
        id=uuid4(),
        organization_id=organization_id,
        user_id=user_id,
        provider_id=uuid4(),
        access_token=fernet_encryption_manager.encrypt(raw_access_token),
        refresh_token=fernet_encryption_manager.encrypt(raw_refresh_token),
        expires_at=zoned_utc_now() + timedelta(hours=1),
        created_at=zoned_utc_now(),
        token_type="bearer",  # noqa: S106
        id_token=None,
        scopes="email profile",
        status=OAuthStatus.ACTIVE,
    )

    # Insert auth record first
    db_user_auth = await oauth_repository.insert(db_user_auth)

    # Create user integration in DB
    await user_integration_repository.insert(
        UserIntegration(
            id=uuid4(),
            user_id=user_id,
            organization_id=organization_id,
            user_auth_id=db_user_auth.id,
            integration_provider=IntegrationProvider.GOOGLE,
            integration_type=IntegrationType.EMAIL,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )

    return db_user_auth


@pytest.fixture
def parse_main_body_text_and_persist() -> None:
    pass


async def test_sync_messages_with_global_thread_creation(
    mocker: MockerFixture,
    email_sync_service: EmailSyncService,
    contact_resolve_service: ContactResolveService,
    email_account_service_ext: EmailAccountServiceExt,
    global_thread_service: GlobalThreadService,
    contact_repository: ContactRepository,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    db_user_auth: OAuthUserAuth,
    parse_main_body_text_and_persist: Callable[[], Awaitable[None]],
) -> None:
    # Setup test data
    user_id = db_user_auth.user_id
    organization_id = db_user_auth.organization_id
    email = "<EMAIL>"

    # Mock Google message response
    google_message = GoogleMessage(
        id="msg_1",
        threadId="test_thread_123",
        payload=MessagePayload(
            parts=[
                MessagePart(
                    mimeType="text/plain",
                    body=MessageBody(
                        # Base64 encoded: "Original plaintext content"
                        data="T3JpZ2luYWwgcGxhaW50ZXh0IGNvbnRlbnQ="
                    ),
                )
            ]
        ),
    )

    # Update mock to target UserIntegrationService instead of EmailSyncService
    mocker.patch.object(
        email_sync_service.user_integration_service,
        "get_or_refresh_google_access_token",
        return_value="fake_token",
    )

    mocker.patch.object(
        email_sync_service.google_client,
        "get_message",
        return_value=google_message,
    )

    # Create email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email,
        signature_html="test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    nylas_thread = NylasThread(
        id="thread_id",
        grant_id="test_grant",
        has_drafts=False,
        starred=False,
        unread=False,
        earliest_message_date=int(zoned_utc_now().timestamp()),
        message_ids=["msg_1", "msg_2", "msg_3"],
        folders=[],
        object="thread",
        participants=[
            NylasEmailName(
                name="Test User",
                email=email,
            ),
            NylasEmailName(
                name="Recipient",
                email="<EMAIL>",
            ),
        ],
    )
    mock_messages = [
        NylasMessage.model_validate(
            {
                "id": "msg_1",
                "grant_id": "test_grant",
                "thread_id": "test_thread_123",
                "subject": "Test Email",
                "snippet": "Original content",
                "body": "Original body",
                "from": [{"name": "Test User", "email": email}],
                "to": [{"name": "Recipient", "email": "<EMAIL>"}],
                "headers": [{"name": "Message-ID", "value": "<<EMAIL>>"}],
                "date": int(zoned_utc_now().timestamp()),
            }
        ),
        NylasMessage.model_validate(
            {
                "id": "msg_2",
                "grant_id": "test_grant",
                "thread_id": "test_thread_123",
                "subject": "Re: Test Email",
                "snippet": "First reply",
                "body": "First reply body",
                "from": [{"name": "Recipient", "email": "<EMAIL>"}],
                "to": [{"name": "Test User", "email": email}],
                "headers": [
                    {"name": "Message-ID", "value": "<<EMAIL>>"},
                    {"name": "In-Reply-To", "value": "<<EMAIL>>"},
                    {"name": "References", "value": "<<EMAIL>>"},
                ],
                "date": int(zoned_utc_now().timestamp()) + 1,
            }
        ),
        NylasMessage.model_validate(
            {
                "id": "msg_3",
                "grant_id": "test_grant",
                "thread_id": "test_thread_123",
                "subject": "Re: Re: Test Email",
                "snippet": "Second reply",
                "body": "Second reply body",
                "from": [{"name": "Test User", "email": email}],
                "to": [{"name": "Recipient", "email": "<EMAIL>"}],
                "headers": [
                    {"name": "Message-ID", "value": "<<EMAIL>>"},
                    {"name": "In-Reply-To", "value": "<<EMAIL>>"},
                    {
                        "name": "References",
                        "value": "<<EMAIL>> <<EMAIL>>",
                    },
                ],
                "date": int(zoned_utc_now().timestamp()) + 2,
            }
        ),
    ]

    mocker.patch.object(
        email_sync_service.async_nylas_client,
        "list_messages",
        return_value=MockResponse(data=mock_messages),
    )

    with (
        mock.patch.object(
            email_sync_service, "_has_valid_contact_participant", return_value=True
        ),
        mock.patch(
            "salestech_be.web.api.email.common.email_sync_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.web.api.email.common.email_sync_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        email_dto_list = await email_sync_service.insert_thread_and_sync_messages(
            organization_id=organization_id,
            grant_id="test_grant",
            integration_provider=IntegrationProvider.GOOGLE,
            nylas_thread_list=[nylas_thread],
            email_account_id=email_account.id,
        )

    # Verify thread
    assert len(email_dto_list) == 1
    thread = email_dto_list[0].thread

    assert len(email_dto_list[0].message_dtos) == 3

    # Construct global thread
    await global_thread_service.construct_global_thread_and_messages(
        organization_id=organization_id,
        email_dto=email_dto_list[0],
    )

    # Verify global thread creation
    global_thread = (
        await email_sync_service.thread_repository.find_global_thread_by_thread_id(
            thread_id=thread.id, organization_id=organization_id
        )
    )
    assert global_thread is not None
    assert global_thread.subject == thread.subject
    assert thread.id in global_thread.thread_ids

    # Verify original message
    original_global_message = (
        await email_sync_service.thread_repository.find_by_original_message_id(
            original_message_id="<<EMAIL>>",
            organization_id=organization_id,
        )
    )
    assert original_global_message is not None
    assert original_global_message.parent_global_message_id is None
    assert original_global_message.global_thread_id == global_thread.id

    # Verify first reply
    reply1_global_message = (
        await email_sync_service.thread_repository.find_by_original_message_id(
            original_message_id="<<EMAIL>>", organization_id=organization_id
        )
    )
    assert reply1_global_message is not None
    assert reply1_global_message.parent_global_message_id == original_global_message.id
    assert reply1_global_message.global_thread_id == global_thread.id

    # Verify second reply
    reply2_global_message = (
        await email_sync_service.thread_repository.find_by_original_message_id(
            original_message_id="<<EMAIL>>", organization_id=organization_id
        )
    )
    assert reply2_global_message is not None
    assert reply2_global_message.parent_global_message_id == reply1_global_message.id
    assert reply2_global_message.global_thread_id == global_thread.id

    # Create a new thread for a different user with the same message
    another_nylas_thread = NylasThread(
        id="test_thread_456",
        grant_id="test_grant",
        has_drafts=False,
        starred=False,
        unread=False,
        earliest_message_date=int(zoned_utc_now().timestamp()),
        message_ids=["msg_4", "msg_5", "msg_6"],
        folders=[],
        object="thread",
        participants=[
            NylasEmailName(
                name="Test User",
                email=email,
            ),
            NylasEmailName(
                name="Recipient",
                email="<EMAIL>",
            ),
        ],
    )

    # Execute sync for the new thread
    mock_messages = [
        NylasMessage.model_validate(
            {
                "id": "msg_4",
                "grant_id": "test_grant",
                "thread_id": "test_thread_456",
                "subject": "Original Email",
                "snippet": "Original content",
                "body": "Original body",
                "from": [{"name": "Test User", "email": email}],
                "to": [{"name": "Recipient", "email": "<EMAIL>"}],
                "headers": [{"name": "Message-ID", "value": "<<EMAIL>>"}],
                "date": int(zoned_utc_now().timestamp()),
            }
        ),
        NylasMessage.model_validate(
            {
                "id": "msg_5",
                "grant_id": "test_grant",
                "thread_id": "test_thread_456",
                "subject": "Re: Original Email",
                "snippet": "First reply",
                "body": "First reply body",
                "from": [{"name": "Recipient", "email": "<EMAIL>"}],
                "to": [{"name": "Test User", "email": email}],
                "headers": [
                    {"name": "Message-ID", "value": "<<EMAIL>>"},
                    {"name": "In-Reply-To", "value": "<<EMAIL>>"},
                    {"name": "References", "value": "<<EMAIL>>"},
                ],
                "date": int(zoned_utc_now().timestamp()) + 1,
            }
        ),
        NylasMessage.model_validate(
            {
                "id": "msg_6",
                "grant_id": "test_grant",
                "thread_id": "test_thread_456",
                "subject": "Re: Re: Original Email",
                "snippet": "Second reply",
                "body": "Second reply body",
                "from": [{"name": "Test User", "email": email}],
                "to": [{"name": "Recipient", "email": "<EMAIL>"}],
                "headers": [
                    {"name": "Message-ID", "value": "<<EMAIL>>"},
                    {"name": "In-Reply-To", "value": "<<EMAIL>>"},
                    {
                        "name": "References",
                        "value": "<<EMAIL>> <<EMAIL>>",
                    },
                ],
                "date": int(zoned_utc_now().timestamp()) + 2,
            }
        ),
    ]

    mocker.patch.object(
        email_sync_service.async_nylas_client,
        "list_messages",
        return_value=MockResponse(data=mock_messages),
    )
    with (
        mock.patch.object(
            email_sync_service, "_has_valid_contact_participant", return_value=True
        ),
        mock.patch(
            "salestech_be.web.api.email.common.email_sync_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.web.api.email.common.email_sync_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        result = await email_sync_service.insert_thread_and_sync_messages(
            organization_id=organization_id,
            grant_id="test_grant",
            integration_provider=IntegrationProvider.GOOGLE,
            nylas_thread_list=[another_nylas_thread],
            email_account_id=email_account.id,
        )
        # Construct global thread
        await global_thread_service.construct_global_thread_and_messages(
            organization_id=organization_id,
            email_dto=result[0],
        )

        another_thread = result[0].thread
        assert another_thread.id != thread.id

        # Verify that msg_3 maps to the same global message
        another_reply2_global_message = (
            await email_sync_service.thread_repository.find_by_original_message_id(
                original_message_id="<<EMAIL>>",
                organization_id=organization_id,
            )
        )
        assert another_reply2_global_message is not None
        assert another_reply2_global_message.id == reply2_global_message.id
        assert another_reply2_global_message.global_thread_id == global_thread.id

        # Verify that both threads are linked to the same global thread
        updated_global_thread = (
            await email_sync_service.thread_repository.find_global_thread_by_thread_id(
                thread_id=another_thread.id,
                organization_id=organization_id,
            )
        )
        assert updated_global_thread is not None
        assert updated_global_thread.id == global_thread.id
        assert thread.id in updated_global_thread.thread_ids
        assert another_thread.id in updated_global_thread.thread_ids

        # Mock email participant service
        mocker.patch.object(
            email_sync_service.email_participant_service,
            "find_contact_or_email_account_by_emails",
            return_value={},
        )

        with (
            mock.patch.object(
                email_sync_service, "_has_valid_contact_participant", return_value=True
            ),
            mock.patch(
                "salestech_be.web.api.email.common.email_sync_service.parse_main_body_text_and_persist",
                new_callable=mock.AsyncMock,
                return_value=None,
            ),
            mock.patch(
                "salestech_be.web.api.email.common.email_sync_service.classify_message_and_update_metadata",
                new_callable=mock.AsyncMock,
                return_value=None,
            ),
        ):
            # Act
            email_dto, _ = await email_sync_service.sync_messages_and_attachments(
                organization_id=organization_id,
                grant_id="test_grant",
                thread=thread,
                email_account_id=email_account.id,
            )

            # Assert
            assert email_dto.thread.id == thread.id
            assert len(email_dto.message_dtos) == 6

            first_message = next(
                m for m in email_dto.message_dtos if m.message.provider_id == "msg_1"
            )
            assert first_message.message.subject == "Test Email"

            # Verify the message was persisted
            db_messages = await email_sync_service.message_repository.list_by_thread_id(
                thread_id=thread.id,
                organization_id=organization_id,
            )
            assert len(db_messages) == 3
            assert db_messages[0].provider_id == "msg_1"
            assert db_messages[1].provider_id == "msg_2"
            assert db_messages[2].provider_id == "msg_3"
            # Verify message headers and reply chain
            assert db_messages[1].headers is not None
            assert db_messages[2].headers is not None
            assert (
                db_messages[1].headers.in_reply_to == "<<EMAIL>>"
            )  # First reply references original
            assert (
                db_messages[2].headers.in_reply_to == "<<EMAIL>>"
            )  # Second reply references first reply


def test_compress_body_html_with_github_notification() -> None:
    """Test HTML compression with a real GitHub notification email."""
    github_html = """<p></p>
<p dir="auto">Preparing PR description...</p>

<p style="font-size:small;-webkit-text-size-adjust:none;color:#666;">&mdash;<br />Reply to this email directly, <a href="https://github.com/ReevoAI/salestech-be/pull/3910#issuecomment-**********">view it on GitHub</a>, or <a href="https://github.com/notifications/unsubscribe-auth/BKV7LHU2K3WDEXGRKQZRO3L2DZZTXAVCNFSM6AAAAABS4RQYESVHI2DSMVQWIX3LMV43OSLTON2WKQ3PNVWWK3TUHMZDKMJWGA4TKNBYGE">unsubscribe</a>.<br />You are receiving this because your review was requested.<img src="https://github.com/notifications/beacon/BKV7LHUXGT7XJE4AE3FTWET2DZZTXA5CNFSM6AAAAABS4RQYESWGG33NNVSW45C7OR4XAZNMJFZXG5LFINXW23LFNZ2KUY3PNVWWK3TUL5UWJTUV7CI7S.gif" height="1" width="1" alt="" /><span style="color: transparent; font-size: 0; display: none; visibility: hidden; overflow: hidden; opacity: 0; width: 0; height: 0; max-width: 0; max-height: 0; mso-hide: all">Message ID: <span>&lt;ReevoAI/salestech-be/pull/3910/c**********</span><span>@</span><span>github</span><span>.</span><span>com&gt;</span></span></p>
<script type="application/ld+json">[
{
"@context": "http://schema.org",
"@type": "EmailMessage",
"potentialAction": {
"@type": "ViewAction",
"target": "https://github.com/ReevoAI/salestech-be/pull/3910#issuecomment-**********",
"url": "https://github.com/ReevoAI/salestech-be/pull/3910#issuecomment-**********",
"name": "View Pull Request"
},
"description": "View this Pull Request on GitHub",
"publisher": {
"@type": "Organization",
"name": "GitHub",
"url": "https://github.com"
}
}
]</script>"""

    # Compress the HTML
    compressed_html = _compress_body_html(github_html)

    # Assert exact match for compressed output
    expected_html = '<p></p><p dir="auto">Preparing PR description...</p><p style="font-size:small;-webkit-text-size-adjust:none;color:#666;">—<br/>Reply to this email directly, <a href="https://github.com/ReevoAI/salestech-be/pull/3910#issuecomment-**********">view it on GitHub</a>, or <a href="https://github.com/notifications/unsubscribe-auth/BKV7LHU2K3WDEXGRKQZRO3L2DZZTXAVCNFSM6AAAAABS4RQYESVHI2DSMVQWIX3LMV43OSLTON2WKQ3PNVWWK3TUHMZDKMJWGA4TKNBYGE">unsubscribe</a>.<br/>You are receiving this because your review was requested.<img alt="" height="1" src="https://github.com/notifications/beacon/BKV7LHUXGT7XJE4AE3FTWET2DZZTXA5CNFSM6AAAAABS4RQYESWGG33NNVSW45C7OR4XAZNMJFZXG5LFINXW23LFNZ2KUY3PNVWWK3TUL5UWJTUV7CI7S.gif" width="1"/><span style="color: transparent; font-size: 0; display: none; visibility: hidden; overflow: hidden; opacity: 0; width: 0; height: 0; max-width: 0; max-height: 0; mso-hide: all">Message ID: <span>&lt;ReevoAI/salestech-be/pull/3910/c**********</span><span>@</span><span>github</span><span>.</span><span>com&gt;</span></span></p><script type="application/ld+json">[{"@context": "http://schema.org","@type": "EmailMessage","potentialAction": {"@type": "ViewAction","target": "https://github.com/ReevoAI/salestech-be/pull/3910#issuecomment-**********","url": "https://github.com/ReevoAI/salestech-be/pull/3910#issuecomment-**********","name": "View Pull Request"},"description": "View this Pull Request on GitHub","publisher": {"@type": "Organization","name": "GitHub","url": "https://github.com"}}]</script>'
    assert compressed_html == expected_html

    # Basic content checks
    assert "<p></p>" in compressed_html
    assert '<p dir="auto">Preparing PR description...</p>' in compressed_html
    assert "Reply to this email directly" in compressed_html
    assert "view it on GitHub" in compressed_html
    assert "unsubscribe" in compressed_html
    assert "You are receiving this because your review was requested" in compressed_html

    # Verify script content is preserved
    assert '<script type="application/ld+json">' in compressed_html
    assert '"@type": "EmailMessage"' in compressed_html
    assert '"name": "GitHub"' in compressed_html

    # Verify tracking elements are preserved
    assert all(
        attr in compressed_html
        for attr in [
            'src="https://github.com/notifications/beacon/',
            'height="1"',
            'width="1"',
            'alt=""',
        ]
    )

    # Verify hidden message ID is preserved
    assert "Message ID:" in compressed_html
    assert "ReevoAI/salestech-be/pull/3910/c**********" in compressed_html


async def test_sync_messages_and_attachments(
    mocker: MockerFixture,
    email_sync_service: EmailSyncService,
    email_account_service_ext: EmailAccountServiceExt,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    db_user_auth: OAuthUserAuth,
) -> None:
    # Arrange
    user_id, organization_id = await make_user_org()
    email = "<EMAIL>"

    # Mock Google message response
    google_message = GoogleMessage(
        id="msg_1",
        threadId="test_thread_123",
        payload=MessagePayload(
            parts=[
                MessagePart(
                    mimeType="text/plain",
                    body=MessageBody(
                        # Base64url encoded: "Hello,\n\nThis is the plaintext content..."
                        data="SGVsbG8sCgpUaGlzIGlzIHRoZSBwbGFpbnRleHQgY29udGVudCBvZiB0aGUgZW1haWwuCgpCZXN0IHJlZ2FyZHMsClNlbmRlcg=="
                    ),
                ),
            ],
        ),
    )
    mocker.patch.object(
        email_sync_service.google_client,
        "get_message",
        return_value=google_message,
    )

    # Create email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email,
        signature_html="test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create a thread
    thread = await email_sync_service.thread_repository.insert(
        Thread(
            id=uuid4(),
            email_account_id=email_account.id,
            organization_id=organization_id,
            provider=EmailProvider.NYLAS,
            provider_id="test_thread_123",
            subject="Test Thread",
            snippet="Test snippet",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            latest_message_received_date=zoned_utc_now(),
            participants=[
                EmailHydratedParticipant(
                    name="Test User",
                    email=email,
                ),
                EmailHydratedParticipant(
                    name="Recipient",
                    email="<EMAIL>",
                ),
            ],
        )
    )

    # Mock Nylas message response
    mock_messages = [
        NylasMessage.model_validate(
            {
                "id": "msg_1",
                "grant_id": "test_grant",
                "thread_id": "test_thread_123",
                "subject": "Test Email",
                "snippet": "Test content",
                "body": "Test body",
                "from": [{"name": "Test User", "email": email}],
                "to": [{"name": "Recipient", "email": "<EMAIL>"}],
                "headers": [{"name": "Message-ID", "value": "<<EMAIL>>"}],
                "date": int(zoned_utc_now().timestamp()),
                "attachments": [
                    {
                        "id": "attachment1",
                        "content_type": "image/jpeg",
                        "filename": "test.jpg",
                        "size": 1000,
                    }
                ],
            }
        )
    ]

    mocker.patch.object(
        email_sync_service.async_nylas_client,
        "list_messages",
        return_value=MockResponse(data=mock_messages),
    )

    # Mock email participant service
    mocker.patch.object(
        email_sync_service.email_participant_service,
        "find_contact_or_email_account_by_emails",
        return_value={},
    )

    # Mock file service attachment handling
    mock_attachment_dto = AttachmentDto.from_attachment(
        attachment=Attachment(
            id=uuid4(),
            file_name="test.jpg",
            content_type="image/jpeg",
            size=1000,
            provider="NYLAS",
            provider_id="attachment1",
            s3_key="test/key",
            user_id=user_id,
            email_account_id=email_account.id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        ),
        public_url="https://test-url.com",
        expire_at=zoned_utc_now() + timedelta(hours=1),
    )

    mocker.patch.object(
        email_sync_service.email_attachment_service,
        "upload_nylas_attachments",
        return_value=[mock_attachment_dto],
    )

    # Mock user integration repository for auth
    mocker.patch.object(
        email_sync_service.user_integration_service,
        "get_or_refresh_google_access_token",
        return_value=("fake_token"),
    )

    with (
        mock.patch.object(
            email_sync_service, "_has_valid_contact_participant", return_value=True
        ),
        mock.patch(
            "salestech_be.web.api.email.common.email_sync_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.web.api.email.common.email_sync_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        # Act
        email_dto, _ = await email_sync_service.sync_messages_and_attachments(
            organization_id=organization_id,
            grant_id="test_grant",
            thread=thread,
            email_account_id=email_account.id,
        )

        # Assert
        assert email_dto.thread.id == thread.id
        assert len(email_dto.message_dtos) == 1

        message_dto = email_dto.message_dtos[0]
        assert message_dto.message.thread_id == thread.id
        assert message_dto.message.subject == "Test Email"
        assert message_dto.message.provider == "NYLAS"
        assert message_dto.message.provider_id == "msg_1"

        # Verify attachments
        assert len(message_dto.attachments) == 1
        attachment = message_dto.attachments[0]
        assert attachment.attachment.file_name == "test.jpg"
        assert attachment.attachment.content_type == "image/jpeg"
        assert attachment.attachment.provider == "NYLAS"
        assert attachment.attachment.provider_id == "attachment1"
        assert attachment.public_url == "https://test-url.com"

        # Verify the message was persisted
        db_messages = await email_sync_service.message_repository.list_by_thread_id(
            thread_id=thread.id,
            organization_id=organization_id,
        )
        assert len(db_messages) == 1
        assert db_messages[0].provider_id == "msg_1"
        assert db_messages[0].attachment_ids == [mock_attachment_dto.attachment.id]


class MockResponse:
    def __init__(self, data: Any):
        self.data = data


async def test_message_content_handling(
    mocker: MockerFixture,
    email_sync_service: EmailSyncService,
    email_account_service_ext: EmailAccountServiceExt,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
) -> None:
    # Setup test data
    user_id, organization_id = await make_user_org()
    email = "<EMAIL>"

    # Create email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email,
        signature_html="test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create a thread
    thread = await email_sync_service.thread_repository.insert(
        Thread(
            id=uuid4(),
            email_account_id=email_account.id,
            organization_id=organization_id,
            provider=EmailProvider.NYLAS,
            provider_id="test_thread_123",
            subject="Test Thread",
            snippet="Test snippet",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            latest_message_received_date=zoned_utc_now(),
            participants=[
                EmailHydratedParticipant(name="Test User", email=email),
            ],
        )
    )

    # Mock Nylas message with HTML body
    html_body = "<div>Test HTML content</div>"
    mock_messages = [
        NylasMessage.model_validate(
            {
                "id": "msg_1",
                "grant_id": "test_grant",
                "thread_id": "test_thread_123",
                "subject": "Test Email",
                "snippet": "Test content",
                "body": html_body,
                "from": [{"name": "Test User", "email": email}],
                "to": [{"name": "Recipient", "email": "<EMAIL>"}],
                "date": int(zoned_utc_now().timestamp()),
            }
        )
    ]

    mocker.patch.object(
        email_sync_service.async_nylas_client,
        "list_messages",
        return_value=MockResponse(data=mock_messages),
    )

    # Mock Google message response
    google_message = GoogleMessage(
        id="msg_1",
        threadId="test_thread_123",
        payload=MessagePayload(
            parts=[
                MessagePart(
                    mimeType="text/plain",
                    body=MessageBody(
                        # Base64 encoded: "Hello,\n\nThis is the plaintext content of the email.\n\nBest regards,\nSender"
                        data="SGVsbG8sCgpUaGlzIGlzIHRoZSBwbGFpbnRleHQgY29udGVudCBvZiB0aGUgZW1haWwuCgpCZXN0IHJlZ2FyZHMsClNlbmRlcg=="
                    ),
                ),
            ],
        ),
    )
    mocker.patch.object(
        email_sync_service.google_client,
        "get_message",
        return_value=google_message,
    )

    # Mock user integration repository for auth
    mocker.patch.object(
        email_sync_service.user_integration_service,
        "get_or_refresh_google_access_token",
        return_value="fake_token",
    )

    with (
        mock.patch.object(
            email_sync_service, "_has_valid_contact_participant", return_value=True
        ),
        mock.patch(
            "salestech_be.web.api.email.common.email_sync_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.web.api.email.common.email_sync_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        # Act
        email_dto, _ = await email_sync_service.sync_messages_and_attachments(
            organization_id=organization_id,
            grant_id="test_grant",
            thread=thread,
            email_account_id=email_account.id,
        )

        # Assert
        message = email_dto.message_dtos[0].message
        assert message.body_html == _compress_body_html(html_body)  # HTML from Nylas
        assert (
            message.body_text
            == "Hello,\n\nThis is the plaintext content of the email.\n\nBest regards,\nSender"
        )  # Plaintext from Google


async def test_message_body_no_parts(
    mocker: MockerFixture,
    email_sync_service: EmailSyncService,
    email_account_service_ext: EmailAccountServiceExt,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
) -> None:
    # Setup test data
    user_id, organization_id = await make_user_org()
    email = "<EMAIL>"

    # Create email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email,
        signature_html="test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create a thread
    thread = await email_sync_service.thread_repository.insert(
        Thread(
            id=uuid4(),
            email_account_id=email_account.id,
            organization_id=organization_id,
            provider=EmailProvider.NYLAS,
            provider_id="test_thread_123",
            subject="Test Thread",
            snippet="Test snippet",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            latest_message_received_date=zoned_utc_now(),
            participants=[
                EmailHydratedParticipant(name="Test User", email=email),
            ],
        )
    )

    # Mock Nylas message with HTML body
    html_body = "<div>Test HTML content</div>"
    mock_messages = [
        NylasMessage.model_validate(
            {
                "id": "msg_1",
                "grant_id": "test_grant",
                "thread_id": "test_thread_123",
                "subject": "Test Email",
                "snippet": "Test content",
                "body": html_body,
                "from": [{"name": "Test User", "email": email}],
                "to": [{"name": "Recipient", "email": "<EMAIL>"}],
                "date": int(zoned_utc_now().timestamp()),
            }
        )
    ]

    mocker.patch.object(
        email_sync_service.async_nylas_client,
        "list_messages",
        return_value=MockResponse(data=mock_messages),
    )

    google_message = GoogleMessage(
        id="msg_1",
        threadId="test_thread_123",
        payload=MessagePayload(body=MessageBody(data="SGVsbG8gd29ybGQ==")),
    )
    mocker.patch.object(
        email_sync_service.google_client,
        "get_message",
        return_value=google_message,
    )
    # Mock user integration repository for auth
    mocker.patch.object(
        email_sync_service.user_integration_service,
        "get_or_refresh_google_access_token",
        return_value="fake_token",
    )

    with (
        mock.patch.object(
            email_sync_service, "_has_valid_contact_participant", return_value=True
        ),
        mock.patch(
            "salestech_be.web.api.email.common.email_sync_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.web.api.email.common.email_sync_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        # Act
        email_dto, _ = await email_sync_service.sync_messages_and_attachments(
            organization_id=organization_id,
            grant_id="test_grant",
            thread=thread,
            email_account_id=email_account.id,
        )

        # Assert
        message = email_dto.message_dtos[0].message
        assert message.body_html == _compress_body_html(html_body)  # HTML from Nylas
        assert message.body_text == "Hello world"  # Plaintext from Google


async def test_message_multipart_plaintext(
    mocker: MockerFixture,
    email_sync_service: EmailSyncService,
    email_account_service_ext: EmailAccountServiceExt,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
) -> None:
    # Setup test data
    user_id, organization_id = await make_user_org()
    email = "<EMAIL>"

    # Create email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email,
        signature_html="test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create a thread
    thread = await email_sync_service.thread_repository.insert(
        Thread(
            id=uuid4(),
            email_account_id=email_account.id,
            organization_id=organization_id,
            provider=EmailProvider.NYLAS,
            provider_id="test_thread_123",
            subject="Test Thread",
            snippet="Test snippet",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            latest_message_received_date=zoned_utc_now(),
            participants=[
                EmailHydratedParticipant(name="Test User", email=email),
            ],
        )
    )

    # Mock Nylas message with HTML body
    html_body = "<div>Test HTML content</div>"
    mock_messages = [
        NylasMessage.model_validate(
            {
                "id": "msg_1",
                "grant_id": "test_grant",
                "thread_id": "test_thread_123",
                "subject": "Test Email",
                "snippet": "Test content",
                "body": html_body,
                "from": [{"name": "Test User", "email": email}],
                "to": [{"name": "Recipient", "email": "<EMAIL>"}],
                "date": int(zoned_utc_now().timestamp()),
            }
        )
    ]

    mocker.patch.object(
        email_sync_service.async_nylas_client,
        "list_messages",
        return_value=MockResponse(data=mock_messages),
    )

    # Mock Google message response with multipart/related structure
    google_message = GoogleMessage(
        id="19572005a05ae630",
        threadId="19572005a05ae630",
        payload=MessagePayload(
            mimeType="multipart/alternative",
            body=MessageBody(data=None),
            parts=[
                MessagePart(
                    mimeType="text/plain",
                    body=MessageBody(
                        # Base64 encoded: "Great, just sent you a calendar invite."
                        data="R3JlYXQsIGp1c3Qgc2VudCB5b3UgYSBjYWxlbmRhciBpbnZpdGUu"
                    ),
                ),
                MessagePart(
                    mimeType="text/html",
                    body=MessageBody(
                        data="PGRpdj5HcmVhdCwganVzdCBzZW50IHlvdSBhIGNhbGVuZGFyIGludml0ZS48L2Rpdj4="
                    ),
                ),
            ],
        ),
    )
    mocker.patch.object(
        email_sync_service.google_client,
        "get_message",
        return_value=google_message,
    )

    # Mock user integration repository for auth
    mocker.patch.object(
        email_sync_service.user_integration_service,
        "get_or_refresh_google_access_token",
        return_value="fake_token",
    )

    with (
        mock.patch.object(
            email_sync_service, "_has_valid_contact_participant", return_value=True
        ),
        mock.patch(
            "salestech_be.web.api.email.common.email_sync_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.web.api.email.common.email_sync_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        # Act
        email_dto, _ = await email_sync_service.sync_messages_and_attachments(
            organization_id=organization_id,
            grant_id="test_grant",
            thread=thread,
            email_account_id=email_account.id,
        )

        # Assert
        message = email_dto.message_dtos[0].message
        assert message.body_html == _compress_body_html(html_body)  # HTML from Nylas
        assert (
            message.body_text == "Great, just sent you a calendar invite."
        )  # Plaintext from Google nested part
