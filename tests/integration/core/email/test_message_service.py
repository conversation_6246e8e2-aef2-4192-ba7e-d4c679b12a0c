from collections.abc import Awaitable, Callable
from datetime import timedelta
from unittest import mock
from unittest.mock import AsyncMock
from uuid import UUID, uuid4

import pytest
from pytest_mock import MockerFixture

from salestech_be.common.exception import (
    ServiceError,
)
from salestech_be.common.exception.exception import (
    IllegalStateError,
    ResourceNotFoundError,
)
from salestech_be.core.contact.service.contact_resolve_service import (
    ContactResolveService,
    ResolveEmailResult,
)
from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.message.schema import EmailSendingCancelRequest
from salestech_be.core.email.service.message_service import MessageService
from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.db.dao.activity_repository import ActivityRepository
from salestech_be.db.dao.attachment_repository import AttachmentRepository
from salestech_be.db.dao.email_event_repository import EmailEventRepository
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dto.email_attchment_dto import AttachmentDto
from salestech_be.db.dto.email_dto import EmailDto, GlobalEmailDto, MessageDto
from salestech_be.db.models.activity import (
    ActivitySubType,
    ActivityType,
    ActivityV2,
)
from salestech_be.db.models.attachment import Attachment
from salestech_be.db.models.global_thread import (
    GlobalMessage,
    GlobalMessageAssociation,
    GlobalThread,
    GlobalThreadMessagesAssociation,
)
from salestech_be.db.models.message import (
    EmailProvider,
    Message,
    MessageSource,
    MessageStatus,
)
from salestech_be.db.models.sequence import (
    EmailEvent,
    EmailEventType,
    EmailProcessingStatus,
)
from salestech_be.db.models.thread import Thread
from salestech_be.integrations.nylas.model import MessageHeaders
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.email.message.schema import (
    SendMessageRequest,
)
from tests.test_util import random_unique_email


async def create_test_email_dto(
    email_account_id: UUID,
    message_id: UUID,
    thread_id: UUID,
    attachment_id: UUID,
    user_id: UUID,
    organization_id: UUID,
    email_send_from: str,
    email_send_to: str,
    email_send_to_contact_id: UUID,
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    attachment_repository: AttachmentRepository,
) -> tuple[Thread, Message, AttachmentDto]:
    utc_now = zoned_utc_now()

    db_attachment = await attachment_repository.insert(
        Attachment(
            id=attachment_id,
            file_name="test_attachment",
            content_type="image/jpeg",
            size=100,
            is_inline=False,
            s3_key="s3-key",
            user_id=user_id,
            email_account_id=email_account_id,
            organization_id=organization_id,
            created_at=utc_now,
            updated_at=utc_now,
        )
    )

    # Convert to AttachmentDto
    attachment_dto = AttachmentDto.from_attachment(
        db_attachment,
        public_url="https://app.reevo.ai",
        expire_at=utc_now + timedelta(days=1),
    )

    message = await message_repository.insert(
        Message(
            id=message_id,
            organization_id=organization_id,
            email_account_id=email_account_id,
            thread_id=thread_id,
            subject="Test Subject",
            status=MessageStatus.SENT,
            created_at=utc_now,
            updated_at=utc_now,
            send_from=[
                EmailHydratedParticipant(
                    email=email_send_from, email_account_id=email_account_id
                )
            ],
            send_to=[
                EmailHydratedParticipant(
                    email=email_send_to, contact_id=email_send_to_contact_id
                )
            ],
            snippet="Test Snippet",
            body_text="Test Body Text",
            body_html="Test Body HTML",
            send_at=None,
            attachment_ids=[attachment_id],
            headers=MessageHeaders(
                message_id=str(message_id),
                in_reply_to=None,
                references=None,
            ),
        )
    )

    thread = await thread_repository.insert(
        Thread(
            id=thread_id,
            email_account_id=email_account_id,
            organization_id=organization_id,
            subject="test subject",
            snippet="test snippet",
            participants=[
                EmailHydratedParticipant(
                    email=email_send_from, email_account_id=email_account_id
                ),
                EmailHydratedParticipant(
                    email=email_send_to, contact_id=email_send_to_contact_id
                ),
            ],
            earliest_message_date=utc_now,
            created_at=utc_now,
            updated_at=utc_now,
        )
    )

    return thread, message, attachment_dto


async def test_send_email_dto(
    mocker: MockerFixture,
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    attachment_repository: AttachmentRepository,
    message_service: MessageService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_ext: EmailAccountServiceExt,
    email_event_repository: EmailEventRepository,
) -> None:
    user_id, organization_id = await make_user_org()
    contact_id = uuid4()

    thread_id = uuid4()
    message_id = uuid4()
    attachment_id = uuid4()

    email_send_from = random_unique_email()
    email_send_to = random_unique_email()

    utc_now = zoned_utc_now()

    # Prepare email account and signature
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email_send_from,
        signature_html="this is my signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    thread, message, attachment_dto = await create_test_email_dto(
        email_account_id=email_account.id,
        message_id=message_id,
        thread_id=thread_id,
        attachment_id=attachment_id,
        user_id=user_id,
        organization_id=organization_id,
        email_send_from=email_send_from,
        email_send_to=email_send_to,
        email_send_to_contact_id=contact_id,
        thread_repository=thread_repository,
        message_repository=message_repository,
        attachment_repository=attachment_repository,
    )

    email_dto = EmailDto(
        thread=thread,
        message_dtos=[
            MessageDto(
                message=message,
                attachments=[attachment_dto],
            )
        ],
    )
    provider_id = str(uuid4())
    global_thread_id = uuid4()
    global_message_id = uuid4()

    # Create a message with specific ID for error case
    error_message = message.model_copy(
        update={
            "status": MessageStatus.CREATED,
            "provider": EmailProvider.NYLAS,
            "provider_id": provider_id,
            "send_at": utc_now + timedelta(days=1),
        }
    )

    # Mock for error case
    mocker.patch(
        "salestech_be.core.email.service.email_sending_service.EmailSendingService.send_messages_to_provider",
        side_effect=ServiceError("mock error"),
    )

    # Test error case
    with pytest.raises(ServiceError):
        await message_service.send_email_dto(
            organization_id=organization_id,
            global_email_dto=GlobalEmailDto.from_email_dto(
                email_dto=email_dto,
                global_thread_id=global_thread_id,
                global_message_mapping={global_message_id: (error_message, True)},
            ),
            reply_to_db_message=None,
        )

        updated_thread = await thread_repository.find_by_primary_key(
            Thread,
            id=thread_id,
        )
        assert updated_thread
        assert updated_thread.deleted_at is not None

        updated_message = await message_repository.find_by_primary_key(
            Message,
            id=message_id,
        )
        assert updated_message
        assert updated_message.deleted_at is not None
        assert updated_message.status == MessageStatus.FAILED

        # verify the email event is created
        email_event = (
            await email_event_repository.get_unique_email_event_by_external_event_id(
                external_event_id=str(message_id)
            )
        )

        assert email_event is not None
        assert email_event.organization_id == organization_id
        assert email_event.type == EmailEventType.SEND_ATTEMPTED_FAILED

    # Create updated message for success case - with the SAME ID
    success_message_id = uuid4()
    success_message = message.model_copy(
        update={
            "id": success_message_id,  # Use this same ID throughout the test
            "status": MessageStatus.CREATED,
            "provider": EmailProvider.NYLAS,
            "provider_id": provider_id,
            "send_at": utc_now + timedelta(days=1),
        }
    )

    # Create an updated EmailDto for the success case
    updated_email_dto = email_dto.model_copy(
        update={
            "message_dtos": [
                MessageDto(
                    message=success_message,
                    attachments=[attachment_dto],
                )
            ]
        }
    )

    # Create GlobalEmailDto for success case with consistent IDs
    mock_sent_email_dto = GlobalEmailDto.from_email_dto(
        email_dto=updated_email_dto,
        global_thread_id=global_thread_id,
        global_message_mapping={
            global_message_id: (success_message, True)
        },  # Use the SAME message object
    )

    # Mock for success case
    mocker.patch(
        "salestech_be.core.email.service.email_sending_service.EmailSendingService.send_messages_to_provider",
        return_value=mock_sent_email_dto,
    )

    # Test success case
    send_email_dto = await message_service.send_email_dto(
        organization_id=organization_id,
        global_email_dto=GlobalEmailDto.from_email_dto(
            email_dto=email_dto,
            global_thread_id=global_thread_id,
            global_message_mapping={
                global_message_id: (
                    message.model_copy(update={"id": success_message_id}),
                    True,
                )
            },
        ),
        reply_to_db_message=None,
    )

    # Verify email event
    email_event = (
        await email_event_repository.get_unique_email_event_by_external_event_id(
            external_event_id=provider_id
        )
    )

    assert email_event is not None
    assert email_event.organization_id == organization_id
    assert email_event.type == EmailEventType.SEND_ATTEMPTED
    assert email_event.global_thread_id == send_email_dto.global_thread_id
    assert email_event.global_message_id == next(
        iter(send_email_dto.global_message_mapping.keys())
    )


async def test_get_global_email_dto_from_draft_message(
    mocker: MockerFixture,
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    attachment_repository: AttachmentRepository,
    message_service: MessageService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_ext: EmailAccountServiceExt,
) -> None:
    """Test retrieving a GlobalEmailDto from a draft message and updating it to be sent."""
    # Step 1: Setup test data
    user_id, organization_id = await make_user_org()
    email_send_from = random_unique_email()
    email_send_to = random_unique_email()
    contact_id = uuid4()
    attachment_id = uuid4()

    # Prepare email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email_send_from,
        signature_html="Test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Mock S3 operations to prevent NoCredentialsError
    mocker.patch(
        "salestech_be.core.files.service.file_service.S3BucketManager.generate_presigned_url",
        return_value=("https://presigned-url.com", zoned_utc_now()),
    )

    # Prepare attachment
    await attachment_repository.insert(
        Attachment(
            id=attachment_id,
            file_name="test_attachment.pdf",
            content_type="application/pdf",
            size=1024,
            is_inline=False,
            s3_key="test-s3-key",
            user_id=user_id,
            email_account_id=email_account.id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )

    # Create a mock for send_messages_to_provider to avoid actually sending email
    mock_send = mocker.patch(
        "salestech_be.core.email.service.email_sending_service.EmailSendingService.send_messages_to_provider"
    )
    mock_send.return_value = None  # We'll mock a proper return later

    # Step 2: Create a draft message using create_message_and_thread
    draft_send_request = SendMessageRequest(
        subject="Draft Message Subject",
        send_from=[
            EmailHydratedParticipant(
                email=email_send_from,
                email_account_id=email_account.id,
            )
        ],
        to=[
            EmailHydratedParticipant(
                email=email_send_to,
                contact_id=contact_id,
            )
        ],
        cc=None,
        bcc=None,
        reply_to=None,
        body_text="This is a draft message body.",
        body_html="<p>This is a draft message body.</p>",
        reply_to_message_id=None,
        send_at=None,
        use_draft=True,
        attachment_ids=[attachment_id],
        source=MessageSource.SEQUENCE,
        snippet="Draft message snippet",
    )

    with (
        mock.patch(
            "salestech_be.core.email.service.message_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.core.email.service.message_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        global_email_dto, _ = await message_service.create_message_and_thread(
            organization_id=organization_id,
            send_message_request=draft_send_request,
        )

        # Verify the message was created with DRAFT status
        draft_message = global_email_dto.email_dto.message_dtos[0].message
        assert draft_message.status == MessageStatus.DRAFT
        assert draft_message.source == MessageSource.SEQUENCE

        global_message_id = next(iter(global_email_dto.global_message_mapping.keys()))

        # Step 3: Create an updated send request to modify and send the draft
        updated_send_request = SendMessageRequest(
            subject="Updated Subject",
            send_from=[
                EmailHydratedParticipant(
                    email=email_send_from,
                    email_account_id=email_account.id,
                )
            ],
            to=[
                EmailHydratedParticipant(
                    email=email_send_to,
                    contact_id=contact_id,
                )
            ],
            cc=None,
            bcc=None,
            reply_to=None,
            body_text="This is the updated message body.",
            body_html="<p>This is the updated message body.</p>",
            reply_to_message_id=None,
            send_at=None,
            use_draft=False,
            attachment_ids=[attachment_id],
            global_message_id=global_message_id,
            source=MessageSource.SEQUENCE,
        )

    # Mock the send_messages_to_provider to return a proper EmailDto
    def mock_send_side_effect(
        organization_id: UUID,
        global_email_dto: GlobalEmailDto,
        reply_to_provider_message_id: str | None,
    ) -> GlobalEmailDto:
        # Update the message status to SENT
        message = global_email_dto.email_dto.message_dtos[0].message
        updated_message = message.model_copy(
            update={
                "status": MessageStatus.SENT,
                "provider_id": str(uuid4()),
            }
        )

        # Create a new EmailDto with the updated message
        message_dto = MessageDto(
            message=updated_message,
            attachments=global_email_dto.email_dto.message_dtos[0].attachments,
        )

        return GlobalEmailDto.from_email_dto(
            email_dto=EmailDto(
                thread=global_email_dto.email_dto.thread,
                message_dtos=[message_dto],
            ),
            global_thread_id=global_email_dto.global_thread_id,
            global_message_mapping={global_message_id: (updated_message, True)},
        )

    mock_send.side_effect = mock_send_side_effect

    # Step 4: Test get_global_email_dto_from_draft_message and update the draft
    (
        updated_global_email_dto,
        reply_to_db_message,
    ) = await message_service.get_global_email_dto_from_draft_or_scheduled_message(
        organization_id=organization_id,
        send_message_request=updated_send_request,
    )

    # Verify the draft message was updated with the new content
    updated_message = updated_global_email_dto.email_dto.message_dtos[0].message
    assert updated_message.id == draft_message.id
    assert updated_message.subject == "Updated Subject"
    assert updated_message.body_text == "This is the updated message body."
    assert updated_message.body_html == "<p>This is the updated message body.</p>"
    assert (
        updated_message.status == MessageStatus.SCHEDULED
    )  # Changed from DRAFT to SCHEDULED

    # Step 5: Verify sending the message works
    # Now we'll test sending the updated draft message
    sent_global_email_dto = await message_service.send_email_dto(
        organization_id=organization_id,
        global_email_dto=updated_global_email_dto,
        reply_to_db_message=reply_to_db_message,
    )

    # Verify the message was sent
    sent_message = sent_global_email_dto.email_dto.message_dtos[0].message
    assert sent_message.status == MessageStatus.SENT
    assert sent_message.provider_id is not None


async def test_get_global_email_dto_from_draft_message_error_not_draft_or_scheduled(
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    attachment_repository: AttachmentRepository,
    message_service: MessageService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_ext: EmailAccountServiceExt,
) -> None:
    """Test that get_global_email_dto_from_draft_message raises an error when the message is not a draft."""
    # Step 1: Setup test data
    user_id, organization_id = await make_user_org()
    email_send_from = random_unique_email()
    email_send_to = random_unique_email()
    contact_id = uuid4()

    # Prepare email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email_send_from,
        signature_html="Test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Step 2: Create a message with Created status
    scheduled_send_request = SendMessageRequest(
        subject="Scheduled Message Subject",
        send_from=[
            EmailHydratedParticipant(
                email=email_send_from,
                email_account_id=email_account.id,
            )
        ],
        to=[
            EmailHydratedParticipant(
                email=email_send_to,
                contact_id=contact_id,
            )
        ],
        cc=None,
        bcc=None,
        reply_to=None,
        body_text="This is a scheduled message body.",
        body_html="<p>This is a scheduled message body.</p>",
        reply_to_message_id=None,
        send_at=None,
        use_draft=False,
        attachment_ids=None,
        source=MessageSource.INBOX,
        snippet="Scheduled message snippet",
    )

    with (
        mock.patch(
            "salestech_be.core.email.service.message_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.core.email.service.message_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        global_email_dto, _ = await message_service.create_message_and_thread(
            organization_id=organization_id,
            send_message_request=scheduled_send_request,
        )

        # Verify the message was created with CREATED status
        created_message = global_email_dto.email_dto.message_dtos[0].message
        assert created_message.status == MessageStatus.CREATED

        global_message_id = next(iter(global_email_dto.global_message_mapping.keys()))

        # Step 3: Create an updated send request with the global_message_id
        updated_send_request = SendMessageRequest(
            subject="Updated Subject",
            send_from=[
                EmailHydratedParticipant(
                    email=email_send_from,
                    email_account_id=email_account.id,
                )
            ],
            to=[
                EmailHydratedParticipant(
                    email=email_send_to,
                    contact_id=contact_id,
                )
            ],
            cc=None,
            bcc=None,
            reply_to=None,
            body_text="This is the updated message body.",
            body_html="<p>This is the updated message body.</p>",
            reply_to_message_id=None,
            send_at=None,
            use_draft=False,
            attachment_ids=None,
            global_message_id=global_message_id,
            source=MessageSource.SEQUENCE,
        )

        # Step 4: Test that get_global_email_dto_from_draft_message raises IllegalStateError
        with pytest.raises(IllegalStateError) as exc_info:
            await message_service.get_global_email_dto_from_draft_or_scheduled_message(
                organization_id=organization_id,
                send_message_request=updated_send_request,
            )

        # Verify the error message
        assert "Expected message" in str(exc_info.value)
        assert "to be in DRAFT/SCHEDULED state" in str(exc_info.value)
        assert str(created_message.status) in str(exc_info.value)


@mock.patch.multiple(
    "salestech_be.core.files.service.file_service.S3BucketManager",
    generate_presigned_url=mock.AsyncMock(
        return_value=("https://presigned-url.com", zoned_utc_now())
    ),
)
async def test_send_message_send_now_with_reply(
    mocker: MockerFixture,
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    attachment_repository: AttachmentRepository,
    message_service: MessageService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_ext: EmailAccountServiceExt,
    email_event_repository: EmailEventRepository,
) -> None:
    user_id, organization_id = await make_user_org()
    contact_id = uuid4()

    thread_id = uuid4()
    message_id = uuid4()
    attachment_id = uuid4()

    email_send_from = random_unique_email()
    email_send_to = random_unique_email()

    utc_now = zoned_utc_now()

    # Prepare email account and signature
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email_send_from,
        signature_html="this is my signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    thread, _message, _attachment_dto = await create_test_email_dto(
        email_account_id=email_account.id,
        message_id=message_id,
        thread_id=thread_id,
        attachment_id=attachment_id,
        user_id=user_id,
        organization_id=organization_id,
        email_send_from=email_send_from,
        email_send_to=email_send_to,
        email_send_to_contact_id=contact_id,
        thread_repository=thread_repository,
        message_repository=message_repository,
        attachment_repository=attachment_repository,
    )

    db_attachment = await attachment_repository.insert(
        Attachment(
            id=uuid4(),
            file_name="test_attachment",
            content_type="image/jpeg",
            size=100,
            is_inline=False,
            s3_key="s3-key",
            user_id=user_id,
            email_account_id=email_account.id,
            organization_id=organization_id,
            created_at=utc_now,
            updated_at=utc_now,
        )
    )

    # Convert to AttachmentDto
    new_attachment_dto = AttachmentDto.from_attachment(
        db_attachment,
        public_url="https://app.reevo.ai",
        expire_at=utc_now + timedelta(days=1),
    )

    send_request = SendMessageRequest(
        subject="Test Subject",
        send_from=[
            EmailHydratedParticipant(
                email_account_id=email_account.id, email=email_send_from
            )
        ],
        to=[EmailHydratedParticipant(contact_id=contact_id, email=email_send_to)],
        cc=None,
        bcc=None,
        reply_to=None,
        body_text="Test Body Text",
        body_html="Test Body HTML",
        send_at=None,
        attachment_ids=[db_attachment.id],
        reply_to_message_id=message_id,
        use_draft=False,
    )

    # Fix by storing the message ID and global message ID
    response_message_id = uuid4()
    global_thread_id = uuid4()
    global_message_id = uuid4()
    provider_id = f"test_provider_{uuid4()}"

    # Create the message that will be in both places
    mocked_message = Message(
        id=response_message_id,
        thread_id=thread_id,
        email_account_id=email_account.id,
        organization_id=organization_id,
        subject=send_request.subject,
        send_from=[EmailHydratedParticipant(email=email_send_from)],
        send_to=[EmailHydratedParticipant(email=email_send_to)],
        body_text=send_request.body_text,
        body_html=send_request.body_html,
        snippet="Test Snippet",
        created_at=utc_now,
        updated_at=utc_now,
        status=MessageStatus.SENT,
        provider=EmailProvider.NYLAS,
        provider_id=provider_id,
        send_at=utc_now + timedelta(days=1),
        headers=MessageHeaders(message_id=str(uuid4())),
    )

    # Create the GlobalEmailDto with consistent IDs
    mock_global_email_dto = GlobalEmailDto.from_email_dto(
        email_dto=EmailDto(
            thread=thread,
            message_dtos=[
                MessageDto(
                    message=mocked_message,  # Use the same message object
                    attachments=[new_attachment_dto],
                )
            ],
        ),
        global_thread_id=global_thread_id,
        global_message_mapping={
            global_message_id: (mocked_message, True)
        },  # Use the same message object
    )

    # Mock email sending service with our prepared response
    mocker.patch(
        "salestech_be.core.email.service.email_sending_service.EmailSendingService.send_messages_to_provider",
        return_value=mock_global_email_dto,
    )

    # Mock the email event service to ensure consistent organization_id
    mocker.patch(
        "salestech_be.core.email.event.email_event_service.EmailEventService.process_global_email_event",
        return_value=None,
    )

    # Also mock get_unique_email_event_by_external_event_id to return a controlled event
    mock_email_event = AsyncMock(
        return_value=EmailEvent(
            id=uuid4(),
            email_account_id=email_account.id,
            organization_id=organization_id,  # Use the same organization_id as in the test
            global_thread_id=global_thread_id,
            global_message_id=global_message_id,
            event_time=utc_now,
            type=EmailEventType.SEND_ATTEMPTED,
            details=None,
            processing_status=EmailProcessingStatus.PROCESSED,
            external_event_id=provider_id,
        )
    )
    mocker.patch.object(
        email_event_repository,
        "get_unique_email_event_by_external_event_id",
        mock_email_event,
    )

    with (
        mock.patch(
            "salestech_be.core.email.service.message_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.core.email.service.message_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        created_email_dto = await message_service.send_message(
            organization_id=organization_id,
            send_message_request=send_request,
        )

        # Verify the email DTO
        assert created_email_dto.thread == thread
        assert created_email_dto.message_dtos
        assert created_email_dto.message_dtos[0].message.subject == send_request.subject
        assert (
            created_email_dto.message_dtos[0].message.body_html
            == send_request.body_html
        )
        assert created_email_dto.message_dtos[0].attachments == [new_attachment_dto]
        assert created_email_dto.global_thread_id == global_thread_id

        # Verify email event
        email_event = (
            await email_event_repository.get_unique_email_event_by_external_event_id(
                external_event_id=provider_id
            )
        )
        assert email_event is not None
        assert email_event.organization_id == organization_id
        assert email_event.type == EmailEventType.SEND_ATTEMPTED
        assert email_event.global_thread_id == global_thread_id
        assert email_event.global_message_id == global_message_id


async def test_send_message_scheduled_with_reply(
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    attachment_repository: AttachmentRepository,
    message_service: MessageService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_ext: EmailAccountServiceExt,
) -> None:
    user_id, organization_id = await make_user_org()
    contact_id = uuid4()

    thread_id = uuid4()
    message_id = uuid4()
    attachment_id = uuid4()

    email_send_from = random_unique_email()
    email_send_to = random_unique_email()

    utc_now = zoned_utc_now()

    # Prepare email account and signature
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email_send_from,
        signature_html="this is my signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    thread, _message, _attachment_dto = await create_test_email_dto(
        email_account_id=email_account.id,
        message_id=message_id,
        thread_id=thread_id,
        attachment_id=attachment_id,
        user_id=user_id,
        organization_id=organization_id,
        email_send_from=email_send_from,
        email_send_to=email_send_to,
        email_send_to_contact_id=contact_id,
        thread_repository=thread_repository,
        message_repository=message_repository,
        attachment_repository=attachment_repository,
    )

    new_attachment = await attachment_repository.insert(
        Attachment(
            id=uuid4(),
            file_name="test_attachment",
            content_type="image/jpeg",
            size=100,
            is_inline=False,
            s3_key="s3-key",
            user_id=user_id,
            email_account_id=email_account.id,
            organization_id=organization_id,
            created_at=utc_now,
            updated_at=utc_now,
        )
    )

    send_request = SendMessageRequest(
        subject="Test Subject",
        send_from=[
            EmailHydratedParticipant(
                email_account_id=email_account.id, email=email_send_from
            )
        ],
        to=[EmailHydratedParticipant(contact_id=contact_id, email=email_send_to)],
        cc=None,
        bcc=None,
        reply_to=None,
        body_text="Test Body Text",
        body_html="Test Body HTML",
        reply_to_message_id=message_id,
        send_at=zoned_utc_now() + timedelta(days=1),
        use_draft=False,
        attachment_ids=[new_attachment.id],
    )

    with (
        mock.patch.multiple(
            "salestech_be.core.files.service.file_service.S3BucketManager",
            generate_presigned_url=AsyncMock(
                return_value=("https://presigned-url.com", zoned_utc_now())
            ),
        ),
        mock.patch(
            "salestech_be.core.email.service.message_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.core.email.service.message_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        created_email_dto = await message_service.send_message(
            organization_id=organization_id,
            send_message_request=send_request,
        )

        assert created_email_dto.thread == thread
        assert created_email_dto.message_dtos
        assert created_email_dto.message_dtos[0].message.subject == send_request.subject
        assert created_email_dto.message_dtos[0].attachments[0].attachment.model_dump(
            exclude={"public_url", "expire_at", "updated_at"}
        ) == new_attachment.model_dump(
            exclude={"public_url", "expire_at", "updated_at"}
        )
        assert (
            created_email_dto.message_dtos[0].attachments[0].public_url
            == "https://presigned-url.com"
        )


async def test_send_message_scheduled_with_no_reply(
    mocker: MockerFixture,
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    attachment_repository: AttachmentRepository,
    message_service: MessageService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_ext: EmailAccountServiceExt,
) -> None:
    user_id, organization_id = await make_user_org()
    contact_id = uuid4()

    thread_id = uuid4()
    message_id = uuid4()
    attachment_id = uuid4()

    email_send_from = random_unique_email()
    email_send_to = random_unique_email()

    utc_now = zoned_utc_now()

    # Prepare email account and signature
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email_send_from,
        signature_html="this is my signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    thread, _message, _attachment_dto = await create_test_email_dto(
        email_account_id=email_account.id,
        message_id=message_id,
        thread_id=thread_id,
        attachment_id=attachment_id,
        user_id=user_id,
        organization_id=organization_id,
        email_send_from=email_send_from,
        email_send_to=email_send_to,
        email_send_to_contact_id=contact_id,
        thread_repository=thread_repository,
        message_repository=message_repository,
        attachment_repository=attachment_repository,
    )

    new_attachment = await attachment_repository.insert(
        Attachment(
            id=uuid4(),
            file_name="test_attachment",
            content_type="image/jpeg",
            size=100,
            is_inline=False,
            s3_key="s3-key",
            user_id=user_id,
            email_account_id=email_account.id,
            organization_id=organization_id,
            created_at=utc_now,
            updated_at=utc_now,
        )
    )

    send_request = SendMessageRequest(
        subject="Test Subject",
        send_from=[
            EmailHydratedParticipant(
                email_account_id=email_account.id, email=email_send_from
            )
        ],
        to=[EmailHydratedParticipant(contact_id=contact_id, email=email_send_to)],
        cc=None,
        bcc=None,
        reply_to=None,
        body_text="Test Body Text",
        body_html="Test Body HTML",
        reply_to_message_id=None,
        send_at=zoned_utc_now() + timedelta(days=1),
        use_draft=False,
        attachment_ids=[new_attachment.id],
    )

    with (
        mock.patch.multiple(
            "salestech_be.core.files.service.file_service.S3BucketManager",
            generate_presigned_url=AsyncMock(
                return_value=("https://presigned-url.com", zoned_utc_now())
            ),
        ),
        mock.patch(
            "salestech_be.core.email.service.message_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.core.email.service.message_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        created_email_dto = await message_service.send_message(
            organization_id=organization_id,
            send_message_request=send_request,
        )

        assert created_email_dto.thread != thread
        assert created_email_dto.message_dtos
        assert created_email_dto.message_dtos[0].message.subject == send_request.subject
        assert created_email_dto.message_dtos[0].attachments[0].attachment.model_dump(
            exclude={"public_url", "expire_at", "updated_at"}
        ) == new_attachment.model_dump(
            exclude={"public_url", "expire_at", "updated_at"}
        )
        assert (
            created_email_dto.message_dtos[0].attachments[0].public_url
            == "https://presigned-url.com"
        )


async def test_send_message_with_global_thread_creation(
    mocker: MockerFixture,
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    attachment_repository: AttachmentRepository,
    message_service: MessageService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_ext: EmailAccountServiceExt,
    email_event_repository: EmailEventRepository,
) -> None:
    user_id, organization_id = await make_user_org()
    contact_id = uuid4()
    email_send_from = random_unique_email()
    email_send_to = random_unique_email()
    utc_now = zoned_utc_now()

    # Prepare email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email_send_from,
        signature_html="this is my signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create send request
    send_request = SendMessageRequest(
        subject="Test Subject",
        send_from=[
            EmailHydratedParticipant(
                email_account_id=email_account.id, email=email_send_from
            )
        ],
        to=[EmailHydratedParticipant(contact_id=contact_id, email=email_send_to)],
        cc=None,
        bcc=None,
        reply_to=None,
        body_text="Test Body Text",
        body_html="Test Body HTML",
        reply_to_message_id=None,
        send_at=None,
        use_draft=False,
        attachment_ids=None,
    )

    # Mock email sending service response
    message_id = uuid4()
    thread_id = uuid4()
    global_thread_id = uuid4()
    global_message_id = uuid4()
    provider_id = f"test_provider_{uuid4()}"

    # Create a fully specified message with all required fields
    mocked_message = Message(
        id=message_id,
        thread_id=thread_id,
        email_account_id=email_account.id,
        organization_id=organization_id,
        subject=send_request.subject,
        send_from=send_request.send_from,
        send_to=send_request.to,
        body_text=send_request.body_text,
        body_html=send_request.body_html,
        snippet="Test Snippet",
        created_at=utc_now,
        updated_at=utc_now,
        status=MessageStatus.SENT,
        provider=EmailProvider.NYLAS,
        provider_id=provider_id,
        headers=MessageHeaders(
            message_id="test-message-id",
            in_reply_to=None,
            references=None,
        ),
    )

    # Create a thread to use in the mock response
    mock_thread = Thread(
        id=thread_id,
        email_account_id=email_account.id,
        organization_id=organization_id,
        subject=send_request.subject,
        snippet="Test Snippet",
        participants=send_request.send_from + send_request.to,
        earliest_message_date=utc_now,
        created_at=utc_now,
        updated_at=utc_now,
    )

    # Create the mock response with consistent message ID in global_message_mapping
    mock_global_email_dto = GlobalEmailDto.from_email_dto(
        email_dto=EmailDto(
            thread=mock_thread,
            message_dtos=[
                MessageDto(
                    message=mocked_message,  # Use the same message object
                    attachments=[],
                )
            ],
        ),
        global_thread_id=global_thread_id,
        global_message_mapping={
            global_message_id: (mocked_message, True)
        },  # Use the SAME message object
    )

    mocker.patch(
        "salestech_be.core.email.service.email_sending_service.EmailSendingService.send_messages_to_provider",
        return_value=mock_global_email_dto,
    )

    # Mock pipeline and account resolution
    mocker.patch(
        "salestech_be.core.contact.service.contact_resolve_service.ContactResolveService.get_pipeline_ids_by_contact_ids",
        return_value=uuid4(),
    )
    mocker.patch(
        "salestech_be.core.contact.service.contact_resolve_service.ContactResolveService.resolve_account_by_contacts",
        return_value={uuid4(): uuid4()},
    )

    # Mock the email event service to ensure consistent organization_id
    mocker.patch(
        "salestech_be.core.email.event.email_event_service.EmailEventService.process_global_email_event",
        return_value=None,
    )

    # Also mock get_unique_email_event_by_external_event_id to return a controlled event
    mock_email_event = AsyncMock(
        return_value=EmailEvent(
            id=uuid4(),
            email_account_id=email_account.id,
            organization_id=organization_id,  # Use the same organization_id as in the test
            global_thread_id=global_thread_id,
            global_message_id=global_message_id,
            event_time=utc_now,
            type=EmailEventType.SEND_ATTEMPTED,
            details=None,
            processing_status=EmailProcessingStatus.PROCESSED,
            external_event_id=provider_id,
        )
    )
    mocker.patch.object(
        email_event_repository,
        "get_unique_email_event_by_external_event_id",
        mock_email_event,
    )

    with (
        mock.patch(
            "salestech_be.core.email.service.message_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.core.email.service.message_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        # Execute send_message
        created_email_dto = await message_service.send_message(
            organization_id=organization_id,
            send_message_request=send_request,
        )

        # Verify created DTO
        assert created_email_dto is not None
        assert created_email_dto.global_thread_id == global_thread_id
        assert created_email_dto.message_dtos[0].message.status == MessageStatus.SENT

        # Verify email event was created correctly
        email_event = (
            await email_event_repository.get_unique_email_event_by_external_event_id(
                external_event_id=provider_id
            )
        )
        assert email_event is not None
        assert email_event.organization_id == organization_id
        assert email_event.type == EmailEventType.SEND_ATTEMPTED
        assert email_event.global_thread_id == global_thread_id
        assert email_event.global_message_id == global_message_id


async def test_resolve_fields_for_participants(
    mocker: MockerFixture,
    contact_resolve_service: ContactResolveService,
    message_service: MessageService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
) -> None:
    user_id, organization_id = await make_user_org()
    # Create test participants
    participant1 = EmailHydratedParticipant(
        email="<EMAIL>",
        name="Test 1",
        contact_id=uuid4(),
        account_id=None,
        email_account_id=None,
    )

    participant2 = EmailHydratedParticipant(
        email="<EMAIL>",
        name="Test 2",
        contact_id=None,
        account_id=None,
        email_account_id=None,
    )

    participant3 = EmailHydratedParticipant(
        email="<EMAIL>",
        name="Test 3",
        contact_id=None,
        account_id=None,
        email_account_id=uuid4(),  # Has email_account_id set
    )

    # Create send message request
    send_request = SendMessageRequest(
        subject="Test Subject",
        send_from=[participant1],
        to=[participant2],
        cc=[participant3],  # Include participant with email_account_id
        bcc=None,
        reply_to=None,
        body_text="Test body",
        body_html="<p>Test body</p>",
        reply_to_message_id=None,
        send_at=None,
        use_draft=False,
        attachment_ids=None,
    )

    # Mock contact resolve service responses
    mocker.patch(
        "salestech_be.core.contact.service.contact_resolve_service.ContactResolveService.resolve_relevant_account_by_contact_and_email_pairs",
        return_value={participant1.contact_id: uuid4()},
    )
    mocker.patch(
        "salestech_be.core.contact.service.contact_resolve_service.ContactResolveService.batch_resolve_relevant_contact_info_by_email",
        return_value={
            "<EMAIL>": ResolveEmailResult(
                contact_id=uuid4(), account_id=uuid4()
            )
        },
    )

    # Call resolve_and_fill_fields_for_participants
    send_request = (
        await message_service.resolve_and_fill_fields_for_send_message_request(
            organization_id=organization_id, send_message_request=send_request
        )
    )

    # Verify participants were updated with resolved IDs
    assert send_request.send_from[0].account_id is not None
    assert send_request.to[0].contact_id is not None
    assert send_request.to[0].account_id is not None

    # Verify participant with email_account_id was not modified
    assert send_request.cc
    assert send_request.cc[0].email_account_id is not None
    assert send_request.cc[0].contact_id is None
    assert send_request.cc[0].account_id is None


def test_compress_body_html() -> None:
    """Test HTML compression with a real GitHub notification email."""
    # Create test input
    github_html = """<p></p>
<p dir="auto">Preparing PR description...</p>

<p style="font-size:small;-webkit-text-size-adjust:none;color:#666;">&mdash;<br />Reply to this email directly, <a href="https://github.com/ReevoAI/salestech-be/pull/3910#issuecomment-**********">view it on GitHub</a>, or <a href="https://github.com/notifications/unsubscribe-auth/BKV7LHU2K3WDEXGRKQZRO3L2DZZTXAVCNFSM6AAAAABS4RQYESVHI2DSMVQWIX3LMV43OSLTON2WKQ3PNVWWK3TUHMZDKMJWGA4TKNBYGE">unsubscribe</a>.<br />You are receiving this because your review was requested.<img src="https://github.com/notifications/beacon/BKV7LHUXGT7XJE4AE3FTWET2DZZTXA5CNFSM6AAAAABS4RQYESWGG33NNVSW45C7OR4XAZNMJFZXG5LFINXW23LFNZ2KUY3PNVWWK3TUL5UWJTUV7CI7S.gif" height="1" width="1" alt="" /><span style="color: transparent; font-size: 0; display: none; visibility: hidden; overflow: hidden; opacity: 0; width: 0; height: 0; max-width: 0; max-height: 0; mso-hide: all">Message ID: <span>&lt;ReevoAI/salestech-be/pull/3910/c**********</span><span>@</span><span>github</span><span>.</span><span>com&gt;</span></span></p>
<script type="application/ld+json">[
{
"@context": "http://schema.org",
"@type": "EmailMessage",
"potentialAction": {
"@type": "ViewAction",
"target": "https://github.com/ReevoAI/salestech-be/pull/3910#issuecomment-**********",
"url": "https://github.com/ReevoAI/salestech-be/pull/3910#issuecomment-**********",
"name": "View Pull Request"
},
"description": "View this Pull Request on GitHub",
"publisher": {
"@type": "Organization",
"name": "GitHub",
"url": "https://github.com"
}
}
]</script>"""

    # Create a message with the test HTML
    message = Message(
        id=uuid4(),
        thread_id=uuid4(),
        email_account_id=uuid4(),
        organization_id=uuid4(),
        subject="Test",
        send_from=[],
        send_to=[],
        snippet="Test",
        body_text="",
        body_html=github_html,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
        status=MessageStatus.SENT,
    )

    # Assert body_text is empty when compression is enabled
    assert message.body_text == ""
    # And body_html is compressed
    assert "ReevoAI/salestech-be/pull/3910/c**********" in message.body_html


async def test_scheduled_message_and_send_later(
    mocker: MockerFixture,
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    attachment_repository: AttachmentRepository,
    message_service: MessageService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_ext: EmailAccountServiceExt,
    email_event_repository: EmailEventRepository,
) -> None:
    """
    Test that scheduling a message and later sending it maintains
    the same global thread ID and global message mappings.
    """
    # prepare test data
    user_id, organization_id = await make_user_org()
    contact_id = uuid4()
    email_send_from = random_unique_email()
    email_send_to = random_unique_email()
    utc_now = zoned_utc_now()

    # prepare email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email_send_from,
        signature_html="this is my signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # create attachment
    new_attachment = await attachment_repository.insert(
        Attachment(
            id=uuid4(),
            file_name="test_attachment",
            content_type="image/jpeg",
            size=100,
            is_inline=False,
            s3_key="s3-key",
            user_id=user_id,
            email_account_id=email_account.id,
            organization_id=organization_id,
            created_at=utc_now,
            updated_at=utc_now,
        )
    )

    # convert to AttachmentDto
    attachment_dto = AttachmentDto.from_attachment(
        new_attachment,
        public_url="https://presigned-url.com",
        expire_at=utc_now + timedelta(days=1),
    )

    # create scheduled message request
    schedule_time = zoned_utc_now() + timedelta(days=1)
    send_request = SendMessageRequest(
        subject="Test Scheduled Message",
        send_from=[
            EmailHydratedParticipant(
                email_account_id=email_account.id, email=email_send_from
            )
        ],
        to=[EmailHydratedParticipant(contact_id=contact_id, email=email_send_to)],
        cc=None,
        bcc=None,
        reply_to=None,
        body_text="Test Body Text",
        body_html="Test Body HTML",
        reply_to_message_id=None,
        send_at=schedule_time,  # set to future time
        use_draft=False,
        attachment_ids=[new_attachment.id],
    )

    # Mock S3 generating presigned URL
    with (
        mock.patch.multiple(
            "salestech_be.core.files.service.file_service.S3BucketManager",
            generate_presigned_url=AsyncMock(
                return_value=("https://presigned-url.com", zoned_utc_now())
            ),
        ),
        mock.patch(
            "salestech_be.core.email.service.message_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.core.email.service.message_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        # step 1: create scheduled message
        scheduled_email_dto = await message_service.send_message(
            organization_id=organization_id,
            send_message_request=send_request,
        )

        # save the global thread id and global message mapping created in step 1
        first_global_thread_id = scheduled_email_dto.global_thread_id
        first_global_message_mapping = scheduled_email_dto.global_message_mapping
        scheduled_message_id = scheduled_email_dto.message_dtos[0].message.id

        # verify scheduled message created successfully
        assert (
            scheduled_email_dto.message_dtos[0].message.status
            == MessageStatus.SCHEDULED
        )
        assert scheduled_email_dto.message_dtos[0].message.send_at == schedule_time
        assert scheduled_email_dto.global_thread_id is not None
        assert scheduled_email_dto.global_message_mapping is not None

        # create a mock response for the sent email
        # Ensure mock_sent_email_dto is GlobalEmailDto type
        provider_id = f"test_provider_{uuid4()}"
        sent_message = scheduled_email_dto.message_dtos[0].message.model_copy(
            update={
                "status": MessageStatus.SENT,
                "provider": EmailProvider.NYLAS,
                "provider_id": provider_id,
            }
        )

        mock_sent_email_dto = GlobalEmailDto(
            email_dto=EmailDto(
                thread=scheduled_email_dto.thread,
                message_dtos=[
                    MessageDto(
                        message=sent_message,
                        attachments=[attachment_dto],
                    )
                ],
            ),
            global_thread_id=scheduled_email_dto.global_thread_id,
            global_message_mapping=scheduled_email_dto.global_message_mapping,
        )

        # Mock the return value of the send_messages_to_provider method
        mocker.patch(
            "salestech_be.core.email.service.email_sending_service.EmailSendingService.send_messages_to_provider",
            return_value=mock_sent_email_dto,
        )

        # step 2: call send_email_dto to simulate the actual sending of the message
        sent_email_dto = await message_service.send_email_dto(
            organization_id=organization_id,
            global_email_dto=scheduled_email_dto,
            reply_to_db_message=None,
        )

        # save the global thread id and global message mapping created in step 2
        second_global_thread_id = sent_email_dto.global_thread_id
        second_global_message_mapping = sent_email_dto.global_message_mapping

        # verify the global thread id is the same
        assert first_global_thread_id == second_global_thread_id

        # verify the global message mapping contains the message object
        # note: the structure of global_message_mapping is dict[UUID, tuple[Message, bool]]
        # where the key is the global message ID, and the value is a tuple of (original message, whether it's a new message)
        assert first_global_message_mapping is not None
        assert second_global_message_mapping is not None

        # extract the original message ID from the mapping
        message_ids_in_first_mapping = [
            message.id for _, (message, _) in first_global_message_mapping.items()
        ]
        message_ids_in_second_mapping = [
            message.id for _, (message, _) in second_global_message_mapping.items()
        ]

        # verify the original message ID exists in both mappings
        assert scheduled_message_id in message_ids_in_first_mapping
        assert scheduled_message_id in message_ids_in_second_mapping

        # get the list of global message IDs involved in the mapping, verify they are the same in both calls
        global_message_ids_first = set(first_global_message_mapping.keys())
        global_message_ids_second = set(second_global_message_mapping.keys())
        assert global_message_ids_first == global_message_ids_second

        # verify the sent status is updated
        assert sent_email_dto.message_dtos[0].message.status == MessageStatus.SENT

        # verify the event
        if email_event_repository:
            email_event = await email_event_repository.get_unique_email_event_by_external_event_id(
                external_event_id=provider_id
            )
            assert email_event is not None
            assert email_event.organization_id == organization_id
            assert email_event.type == EmailEventType.SEND_ATTEMPTED
            assert email_event.global_thread_id == scheduled_email_dto.global_thread_id

        # get the global thread from the database, confirm it is the same as the DTO
        global_thread = await thread_repository.find_global_thread_by_thread_id(
            thread_id=sent_email_dto.thread.id,
            organization_id=organization_id,
        )
        assert global_thread is not None
        assert global_thread.id == first_global_thread_id


async def test_cancel_scheduled_message(
    mocker: MockerFixture,
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    message_service: MessageService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_ext: EmailAccountServiceExt,
) -> None:
    """Test cancelling scheduled messages.

    Tests:
    1. Attempting to cancel a non-existent global message ID (should raise IllegalStateError)
    2. Cancelling an already cancelled message (should return without changes)
    3. Successfully cancelling a scheduled message (should update status and set cancelled_at on global entities)
    """
    # ===== Setup common test data =====
    user_id, organization_id = await make_user_org()

    # Create email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email="<EMAIL>",
        signature_html="test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create a global thread with a thread ID
    real_thread_id = uuid4()
    global_thread_id = uuid4()
    await thread_repository.insert(
        GlobalThread(
            id=global_thread_id,
            subject="Test Scheduled Message Thread",
            snippet="Test snippet",
            thread_ids=[real_thread_id],
            contact_ids=[],
            account_ids=None,
            pipeline_id=None,
            organization_id=organization_id,
            earliest_message_date=None,
            latest_message_received_date=None,
            latest_message_date=None,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )

    # Create the actual thread that's referenced in global_thread.thread_ids
    await thread_repository.insert(
        Thread(
            id=real_thread_id,
            email_account_id=email_account.id,
            organization_id=organization_id,
            subject="Test Thread",
            snippet="Test snippet",
            participants=[
                EmailHydratedParticipant(
                    email="<EMAIL>",
                    name="Test User",
                    email_account_id=email_account.id,
                )
            ],
            earliest_message_date=zoned_utc_now(),
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )

    # ===== Test Case 1: Non-existent global message =====
    non_existent_global_message_id = uuid4()

    # Verify ResourceNotFoundError is raised for non-existent global message association
    with pytest.raises(ResourceNotFoundError):
        await message_service.cancel_scheduled_message_by_global_message_id(
            global_message_id=non_existent_global_message_id,
            organization_id=organization_id,
        )

    # ===== Test Case 2: Already cancelled message =====
    already_cancelled_message_id = uuid4()
    already_cancelled_global_message_id = uuid4()

    # Create the already cancelled message
    cancelled_message = await message_repository.insert(
        Message(
            id=already_cancelled_message_id,
            thread_id=real_thread_id,
            organization_id=organization_id,
            email_account_id=email_account.id,
            subject="Already Cancelled Message",
            status=MessageStatus.CANCELLED,  # Already cancelled
            send_from=[
                EmailHydratedParticipant(
                    email="<EMAIL>",
                    name="Test User",
                    email_account_id=email_account.id,
                )
            ],
            send_to=[
                EmailHydratedParticipant(
                    email="<EMAIL>",
                    name="Recipient",
                )
            ],
            headers=MessageHeaders(
                message_id="<<EMAIL>>",
                in_reply_to=None,
                references=None,
            ),
            snippet="Cancelled message",
            body_text="Cancelled message body",
            body_html="<p>Cancelled message body</p>",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )

    # Create a global message for the cancelled message
    await thread_repository.insert(
        GlobalMessage(
            id=already_cancelled_global_message_id,
            global_thread_id=global_thread_id,
            original_message_id="<<EMAIL>>",
            organization_id=organization_id,
            message_received_at=zoned_utc_now(),
            created_at=zoned_utc_now(),
        )
    )

    # Create association between the message and global message
    await thread_repository.insert(
        GlobalMessageAssociation(
            id=uuid4(),
            global_message_id=already_cancelled_global_message_id,
            message_id=already_cancelled_message_id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
        )
    )

    # Create thread messages association for already_cancelled_message
    await thread_repository.insert(
        GlobalThreadMessagesAssociation(
            id=uuid4(),
            original_message_id=str(already_cancelled_message_id),
            global_thread_id=global_thread_id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
        )
    )

    # Save message status before calling cancel
    message_status_before = cancelled_message.status

    # Call cancel_scheduled_message_by_global_message_id for an already cancelled message
    result_dto = await message_service.cancel_scheduled_message_by_global_message_id(
        global_message_id=already_cancelled_global_message_id,
        organization_id=organization_id,
    )

    # Verify the message status didn't change (still cancelled)
    message_after = await message_repository.find_by_primary_key(
        Message,
        id=already_cancelled_message_id,
    )
    assert message_after is not None
    assert message_after.status == message_status_before
    assert result_dto.message_dtos[0].message.id == already_cancelled_message_id

    # ===== Test Case 3: Successfully cancel a scheduled message =====
    scheduled_message_id = uuid4()
    scheduled_global_message_id = uuid4()

    # Create a scheduled message using the same thread
    await message_repository.insert(
        Message(
            id=scheduled_message_id,
            thread_id=real_thread_id,
            organization_id=organization_id,
            email_account_id=email_account.id,
            subject="Scheduled Message",
            status=MessageStatus.SCHEDULED,
            send_from=[
                EmailHydratedParticipant(
                    email="<EMAIL>",
                    name="Test User",
                    email_account_id=email_account.id,
                )
            ],
            send_to=[
                EmailHydratedParticipant(
                    email="<EMAIL>",
                    name="Recipient",
                )
            ],
            headers=MessageHeaders(
                message_id="<<EMAIL>>",
                in_reply_to=None,
                references=None,
            ),
            snippet="Scheduled message",
            body_text="Scheduled message body",
            body_html="<p>Scheduled message body</p>",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            send_at=zoned_utc_now() + timedelta(hours=1),  # Schedule for future
        )
    )

    # Create a global message for the scheduled message
    await thread_repository.insert(
        GlobalMessage(
            id=scheduled_global_message_id,
            global_thread_id=global_thread_id,
            original_message_id="<<EMAIL>>",
            organization_id=organization_id,
            message_received_at=zoned_utc_now(),
            created_at=zoned_utc_now(),
        )
    )

    # Create association between the message and global message
    scheduled_association = await thread_repository.insert(
        GlobalMessageAssociation(
            id=uuid4(),
            global_message_id=scheduled_global_message_id,
            message_id=scheduled_message_id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
        )
    )

    # Create thread messages association for first scheduled message
    await thread_repository.insert(
        GlobalThreadMessagesAssociation(
            id=uuid4(),
            original_message_id=str(scheduled_message_id),
            global_thread_id=global_thread_id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
        )
    )

    # Call cancel_scheduled_message_by_global_message_id for a scheduled message
    cancelled_email_dto = (
        await message_service.cancel_scheduled_message_by_global_message_id(
            global_message_id=scheduled_global_message_id,
            organization_id=organization_id,
        )
    )

    # Verify results
    assert cancelled_email_dto is not None
    assert cancelled_email_dto.message_dtos[0].message.id == scheduled_message_id
    assert cancelled_email_dto.message_dtos[0].message.status == MessageStatus.CANCELLED

    # Verify message status was updated to CANCELLED
    updated_message = await message_repository.find_by_primary_key(
        Message,
        id=scheduled_message_id,
    )
    assert updated_message is not None
    assert updated_message.status == MessageStatus.CANCELLED

    # Verify global message was updated with cancelled_at
    global_message_after = await thread_repository.find_by_tenanted_primary_key(
        GlobalMessage,
        id=scheduled_global_message_id,
        organization_id=organization_id,
    )
    assert global_message_after is not None
    assert global_message_after.cancelled_at is not None

    # Verify association was updated with cancelled_at
    association_after = await thread_repository.find_by_tenanted_primary_key(
        GlobalMessageAssociation,
        id=scheduled_association.id,
        organization_id=organization_id,
    )
    assert association_after is not None
    assert association_after.cancelled_at is not None

    # Verify thread messages association was updated with cancelled_at
    thread_associations = await thread_repository._find_by_column_values(
        GlobalThreadMessagesAssociation,
        original_message_id=str(scheduled_message_id),
        organization_id=organization_id,
    )
    assert len(thread_associations) == 1
    thread_association_after = thread_associations[0]
    assert thread_association_after.cancelled_at is not None

    # Call the method again to see if email_activity_service is called
    await message_service.cancel_scheduled_message_by_global_message_id(
        global_message_id=scheduled_global_message_id,
        organization_id=organization_id,
    )

    # Verify activities were created for the cancelled messages
    # Get the activity service and repository to check activities
    activity_repository = ActivityRepository(
        engine=message_service.message_repository.engine
    )

    # Check that activities were created for the thread
    # The activity is created with the thread_id as reference_id, not the message ID
    activities = await activity_repository._find_by_column_values(
        ActivityV2,
        reference_id=str(real_thread_id),
        type=ActivityType.EMAIL_THREAD.name,
        organization_id=organization_id,
    )

    # Should have activities for the cancelled messages
    assert len(activities) > 0

    # Find the EMAIL_SCHEDULED_CANCEL activity for the thread
    cancel_activities = [
        a
        for a in activities
        if a.sub_type == ActivitySubType.EMAIL_SCHEDULED_CANCEL.name
    ]
    assert len(cancel_activities) > 0

    # Check that activities were created for the global thread
    all_activities = await activity_repository._find_by_column_values(
        ActivityV2,
        organization_id=organization_id,
    )

    # Let's modify the assertion to check reference_id only
    global_thread_activities = [
        a for a in all_activities if a.reference_id == str(global_thread_id)
    ]
    assert len(global_thread_activities) > 0


async def test_cancel_email_sending(
    mocker: MockerFixture,
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    message_service: MessageService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_ext: EmailAccountServiceExt,
) -> None:
    """Test cancelling multiple scheduled messages using the cancel_email_sending method.

    Tests:
    1. Successfully cancelling multiple scheduled messages
    2. Handling a mix of cancellable and already cancelled messages
    """
    # ===== Setup common test data =====
    user_id, organization_id = await make_user_org()

    # Create email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email="<EMAIL>",
        signature_html="test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create a global thread with a thread ID
    real_thread_id = uuid4()
    global_thread_id = uuid4()
    await thread_repository.insert(
        GlobalThread(
            id=global_thread_id,
            subject="Test Scheduled Message Thread",
            snippet="Test snippet",
            thread_ids=[real_thread_id],
            contact_ids=[],
            account_ids=None,
            pipeline_id=None,
            organization_id=organization_id,
            earliest_message_date=None,
            latest_message_received_date=None,
            latest_message_date=None,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )

    # Create the actual thread that's referenced in global_thread.thread_ids
    await thread_repository.insert(
        Thread(
            id=real_thread_id,
            email_account_id=email_account.id,
            organization_id=organization_id,
            subject="Test Thread",
            snippet="Test snippet",
            participants=[
                EmailHydratedParticipant(
                    email="<EMAIL>",
                    name="Test User",
                    email_account_id=email_account.id,
                )
            ],
            earliest_message_date=zoned_utc_now(),
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )

    # ===== Create scheduled messages =====
    scheduled_message_id1 = uuid4()
    scheduled_global_message_id1 = uuid4()

    # Create first scheduled message
    scheduled_message1 = await message_repository.insert(
        Message(
            id=scheduled_message_id1,
            thread_id=real_thread_id,
            organization_id=organization_id,
            email_account_id=email_account.id,
            subject="Scheduled Message 1",
            status=MessageStatus.SCHEDULED,
            send_from=[
                EmailHydratedParticipant(
                    email="<EMAIL>",
                    name="Test User",
                    email_account_id=email_account.id,
                )
            ],
            send_to=[
                EmailHydratedParticipant(
                    email="<EMAIL>",
                    name="Recipient",
                )
            ],
            headers=MessageHeaders(
                message_id="<<EMAIL>>",
                in_reply_to=None,
                references=None,
            ),
            snippet="Scheduled message 1",
            body_text="Scheduled message body 1",
            body_html="<p>Scheduled message body 1</p>",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            send_at=zoned_utc_now() + timedelta(hours=1),
        )
    )

    # Create global message for first scheduled message
    await thread_repository.insert(
        GlobalMessage(
            id=scheduled_global_message_id1,
            global_thread_id=global_thread_id,
            original_message_id="<<EMAIL>>",
            organization_id=organization_id,
            message_received_at=zoned_utc_now(),
            created_at=zoned_utc_now(),
        )
    )

    # Create association for first scheduled message
    await thread_repository.insert(
        GlobalMessageAssociation(
            id=uuid4(),
            global_message_id=scheduled_global_message_id1,
            message_id=scheduled_message_id1,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
        )
    )

    # Create second scheduled message
    scheduled_message_id2 = uuid4()
    scheduled_global_message_id2 = uuid4()

    scheduled_message2 = await message_repository.insert(
        Message(
            id=scheduled_message_id2,
            thread_id=real_thread_id,
            organization_id=organization_id,
            email_account_id=email_account.id,
            subject="Scheduled Message 2",
            status=MessageStatus.SCHEDULED,
            send_from=[
                EmailHydratedParticipant(
                    email="<EMAIL>",
                    name="Test User",
                    email_account_id=email_account.id,
                )
            ],
            send_to=[
                EmailHydratedParticipant(
                    email="<EMAIL>",
                    name="Recipient 2",
                )
            ],
            headers=MessageHeaders(
                message_id="<<EMAIL>>",
                in_reply_to=None,
                references=None,
            ),
            snippet="Scheduled message 2",
            body_text="Scheduled message body 2",
            body_html="<p>Scheduled message body 2</p>",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            send_at=zoned_utc_now() + timedelta(hours=2),
        )
    )

    # Create global message for second scheduled message
    await thread_repository.insert(
        GlobalMessage(
            id=scheduled_global_message_id2,
            global_thread_id=global_thread_id,
            original_message_id="<<EMAIL>>",
            organization_id=organization_id,
            message_received_at=zoned_utc_now(),
            created_at=zoned_utc_now(),
        )
    )

    # Create association for second scheduled message
    await thread_repository.insert(
        GlobalMessageAssociation(
            id=uuid4(),
            global_message_id=scheduled_global_message_id2,
            message_id=scheduled_message_id2,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
        )
    )

    # Create an already cancelled message
    cancelled_message_id = uuid4()
    cancelled_global_message_id = uuid4()

    cancelled_message = await message_repository.insert(
        Message(
            id=cancelled_message_id,
            thread_id=real_thread_id,
            organization_id=organization_id,
            email_account_id=email_account.id,
            subject="Already Cancelled Message",
            status=MessageStatus.CANCELLED,
            send_from=[
                EmailHydratedParticipant(
                    email="<EMAIL>",
                    name="Test User",
                    email_account_id=email_account.id,
                )
            ],
            send_to=[
                EmailHydratedParticipant(
                    email="<EMAIL>",
                    name="Recipient",
                )
            ],
            headers=MessageHeaders(
                message_id="<<EMAIL>>",
                in_reply_to=None,
                references=None,
            ),
            snippet="Cancelled message",
            body_text="Cancelled message body",
            body_html="<p>Cancelled message body</p>",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )

    # Create global message for cancelled message
    await thread_repository.insert(
        GlobalMessage(
            id=cancelled_global_message_id,
            global_thread_id=global_thread_id,
            original_message_id="<<EMAIL>>",
            organization_id=organization_id,
            message_received_at=zoned_utc_now(),
            created_at=zoned_utc_now(),
        )
    )

    # Create association for cancelled message
    await thread_repository.insert(
        GlobalMessageAssociation(
            id=uuid4(),
            global_message_id=cancelled_global_message_id,
            message_id=cancelled_message_id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
        )
    )

    # Create thread messages association for first scheduled message
    await thread_repository.insert(
        GlobalThreadMessagesAssociation(
            id=uuid4(),
            original_message_id=str(scheduled_message_id1),
            global_thread_id=global_thread_id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
        )
    )

    # Create thread messages association for second scheduled message
    await thread_repository.insert(
        GlobalThreadMessagesAssociation(
            id=uuid4(),
            original_message_id=str(scheduled_message_id2),
            global_thread_id=global_thread_id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
        )
    )

    # Create thread messages association for already cancelled message
    await thread_repository.insert(
        GlobalThreadMessagesAssociation(
            id=uuid4(),
            original_message_id=str(cancelled_message_id),
            global_thread_id=global_thread_id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
        )
    )

    # Create the cancel request with all three messages
    cancel_request = EmailSendingCancelRequest(
        scheduled_messages=[scheduled_message1, scheduled_message2, cancelled_message],
        organization_id=organization_id,
    )

    # Call cancel_email_sending
    response = await message_service.cancel_email_sending(
        email_sending_cancel_request=cancel_request
    )

    # Verify the response contains all messages
    assert len(response.data) == 3

    # Check that the two previously scheduled messages are now cancelled
    message1_after = await message_repository.find_by_primary_key(
        Message,
        id=scheduled_message_id1,
    )
    assert message1_after is not None
    assert message1_after.status == MessageStatus.CANCELLED

    message2_after = await message_repository.find_by_primary_key(
        Message,
        id=scheduled_message_id2,
    )
    assert message2_after is not None
    assert message2_after.status == MessageStatus.CANCELLED

    # The already cancelled message should still be cancelled
    message3_after = await message_repository.find_by_primary_key(
        Message,
        id=cancelled_message_id,
    )
    assert message3_after is not None
    assert message3_after.status == MessageStatus.CANCELLED

    # Verify activities were created for the cancelled messages
    # Get the activity service and repository to check activities

    activity_repository = ActivityRepository(
        engine=message_service.message_repository.engine
    )

    # Check that activities were created for the thread
    # The activity is created with the thread_id as reference_id, not the message ID
    activities = await activity_repository._find_by_column_values(
        ActivityV2,
        reference_id=str(real_thread_id),
        type=ActivityType.EMAIL_THREAD.name,
        organization_id=organization_id,
    )

    # Should have activities for the cancelled messages
    assert len(activities) > 0

    # Find the EMAIL_SCHEDULED_CANCEL activity for the thread
    cancel_activities = [
        a for a in activities if a.sub_type == ActivitySubType.EMAIL_SCHEDULED_CANCEL
    ]
    assert len(cancel_activities) > 0

    # Verify that we have at least one cancel activity
    assert any(a.sub_type == ActivitySubType.EMAIL_SCHEDULED_CANCEL for a in activities)

    # Check that activities were created for the global thread
    all_activities = await activity_repository._find_by_column_values(
        ActivityV2,
        organization_id=organization_id,
    )

    global_thread_activities = [
        a for a in all_activities if a.reference_id == str(global_thread_id)
    ]
    assert len(global_thread_activities) > 0


async def test_get_global_email_dto_from_draft_message_error_no_association(
    message_service: MessageService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    email_account_service_ext: EmailAccountServiceExt,
) -> None:
    """Test that get_global_email_dto_from_draft_message raises an error when the global message association can't be found."""
    # Step 1: Setup test data
    user_id, organization_id = await make_user_org()
    email_send_from = random_unique_email()
    email_send_to = random_unique_email()
    contact_id = uuid4()
    non_existent_global_message_id = uuid4()

    # Prepare email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email=email_send_from,
        signature_html="Test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create a send request with a non-existent global_message_id
    send_request = SendMessageRequest(
        subject="Subject",
        send_from=[
            EmailHydratedParticipant(
                email=email_send_from,
                email_account_id=email_account.id,
            )
        ],
        to=[
            EmailHydratedParticipant(
                email=email_send_to,
                contact_id=contact_id,
            )
        ],
        cc=None,
        bcc=None,
        reply_to=None,
        body_text="Message body",
        body_html="<p>Message body</p>",
        reply_to_message_id=None,
        send_at=None,
        use_draft=False,
        attachment_ids=None,
        global_message_id=non_existent_global_message_id,
        source=MessageSource.SEQUENCE,
    )

    # Test that get_global_email_dto_from_draft_message raises ResourceNotFoundError
    with pytest.raises(ResourceNotFoundError) as exc_info:
        await message_service.get_global_email_dto_from_draft_or_scheduled_message(
            organization_id=organization_id,
            send_message_request=send_request,
        )

    # Verify the error message
    assert "Expected exactly one global message association" in str(exc_info.value)
    assert str(non_existent_global_message_id) in str(exc_info.value)
