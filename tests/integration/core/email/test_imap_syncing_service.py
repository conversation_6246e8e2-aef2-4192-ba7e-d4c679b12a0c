from collections.abc import Awaitable, Callable
from datetime import datetime, timedelta
from email import message_from_string
from email.message import Message as EmailMessage
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from email.mime.text import MIMEText
from unittest import mock
from uuid import UUID, uuid4

import pytest
from pytest_mock import Mo<PERSON><PERSON>ixture

from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.service.imap_syncing_service import (
    CombinedMessage,
    DsnStatus,
    ImapMessage,
    ImapSyncingService,
    ImapUid,
)
from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.db.dao.email_event_repository import EmailEventRepository
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dto.email_dto import ShortDBMessage
from salestech_be.db.models.attachment import Attachment
from salestech_be.db.models.email_account import EmailAccount
from salestech_be.db.models.global_thread import (
    GlobalThread,
)
from salestech_be.db.models.message import Message
from salestech_be.db.models.sequence import EmailEvent, EmailEventType
from salestech_be.db.models.thread import Thread
from salestech_be.integrations.nylas.model import MessageHeaders
from salestech_be.util.time import zoned_utc_now
from tests.util.factories import (
    EmailAccountFactory,
)


@pytest.fixture
def email_account(email_account_factory: EmailAccountFactory) -> EmailAccount:
    return email_account_factory.build(
        imap_last_synced_at=zoned_utc_now() - timedelta(days=7)
    )


@pytest.fixture
def organization_id() -> UUID:
    return uuid4()


def create_mime_email_message(
    message_id: str, in_reply_to: str | None = None
) -> EmailMessage:
    msg = MIMEMultipart()
    msg["Message-ID"] = message_id
    msg["Subject"] = "Test Subject"
    msg["Date"] = "Thu, 21 Oct 2021 12:00:00 +0000"

    if in_reply_to:
        if not in_reply_to.startswith("<"):
            in_reply_to = f"<{in_reply_to}>"
        msg["In-Reply-To"] = in_reply_to
        msg["References"] = in_reply_to

    msg.attach(MIMEText("This is the body of the test email.", "plain"))

    return message_from_string(msg.as_string())


def create_mock_imap_message(
    imap_uid: ImapUid,
    date: datetime | None = None,
    thread_imap_uid: ImapUid | None = None,
    parent_imap_uid: ImapUid | None = None,
) -> ImapMessage:
    in_reply_to = parent_imap_uid if parent_imap_uid else None
    email_message = create_mime_email_message(imap_uid, in_reply_to)

    if date:
        email_message.replace_header(
            "Date", date.strftime("%a, %d %b %Y %H:%M:%S +0000")
        )
    return ImapMessage(
        imap_uid=imap_uid,
        email_message=email_message,
        is_unread=True,
        folder="INBOX",
        preview="preview",
        internal_date=date or zoned_utc_now(),
        thread_imap_uid=thread_imap_uid or imap_uid,
        parent_imap_uid=parent_imap_uid,
        visible_text="visible_text",
        html_body="html_body",
    )


def create_mock_imap_bounce_message(
    imap_uid: ImapUid,
    original_message_id: str,
    original_date: datetime,
    bounce_date: datetime,
    thread_imap_uid: ImapUid,
) -> ImapMessage:
    """
    Create a mock IMAP bounce message that correctly references the original message.

    This creates a DSN (Delivery Status Notification) that will be properly detected
    by the ImapMessage.get_dsn_status() and ImapMessage.get_original_message_id() methods.

    Args:
        imap_uid: The IMAP UID for the bounce message
        original_message_id: The Message-ID of the original message that bounced
        original_date: The date of the original message
        bounce_date: The date of the bounce message
        thread_imap_uid: The IMAP UID of the thread this bounce belongs to

    Returns:
        A properly formatted ImapMessage representing a bounce
    """
    # Make sure original_message_id has angle brackets
    if not original_message_id.startswith("<"):
        original_message_id = f"<{original_message_id}>"

    # Create a DSN message in raw format to ensure proper parsing
    bounce_raw_email = f"""From: <EMAIL>
To: <EMAIL>
Subject: Undelivered Mail Returned to Sender
Message-ID: <bounce-{imap_uid}>
Date: {bounce_date.strftime("%a, %d %b %Y %H:%M:%S +0000")}
MIME-Version: 1.0
Content-Type: multipart/report; report-type=delivery-status;
    boundary="12345.67890.12345"

This is a MIME-encapsulated message.

--12345.67890.12345
Content-Description: Notification
Content-Type: text/plain; charset=utf-8

This is the mail system at host example.com.

I'm sorry to have to inform you that your message could not
be delivered to one or more recipients. It's attached below.

--12345.67890.12345
Content-Description: Delivery report
Content-Type: message/delivery-status

Reporting-MTA: dns; example.com
Original-Recipient: rfc822;<EMAIL>
Final-Recipient: rfc822;<EMAIL>
Action: failed
Status: 5.1.1
Diagnostic-Code: smtp; 550 mailbox does not exist

--12345.67890.12345
Content-Description: Undelivered Message
Content-Type: message/rfc822

Return-Path: <<EMAIL>>
Received: from localhost (localhost)
    by example.com (Example Mail) with ESMTP id 12345
    for <<EMAIL>>; {original_date.strftime("%a, %d %b %Y %H:%M:%S +0000")}
From: <EMAIL>
To: <EMAIL>
Subject: Test Subject
Message-ID: {original_message_id}
Date: {original_date.strftime("%a, %d %b %Y %H:%M:%S +0000")}
Content-Type: text/plain; charset=utf-8

This is a test email body

--12345.67890.12345--
"""

    return ImapMessage(
        imap_uid=imap_uid,
        email_message=message_from_string(bounce_raw_email),
        is_unread=True,
        folder="INBOX",
        preview="preview",
        internal_date=bounce_date,
        thread_imap_uid=thread_imap_uid,
        parent_imap_uid=None,
        visible_text="visible_text",
        html_body="html_body",
    )


async def test_thread_merging_with_thread_id(
    imap_syncing_service: ImapSyncingService,
) -> None:
    imap_messages = {
        "1": create_mock_imap_message("msg1"),
        "2": create_mock_imap_message(
            imap_uid="msg2", thread_imap_uid="msg1", parent_imap_uid="msg1"
        ),
        "3": create_mock_imap_message(
            imap_uid="msg3", thread_imap_uid="msg1", parent_imap_uid="msg2"
        ),
    }
    all_messages = {}
    for key, message in imap_messages.items():
        all_messages[key] = CombinedMessage(message)

    threads = await imap_syncing_service._build_message_threads(
        imap_messages,
        all_messages,
        {},
        {
            "msg1": "1",
            "msg2": "2",
            "msg3": "3",
        },
        {},
    )
    assert len(threads) == 1
    assert set(threads["1"]) == {"1", "2", "3"}


async def test_very_long_chain(imap_syncing_service: ImapSyncingService) -> None:
    imap_messages = {}
    for i in range(1000):
        imap_uid = f"msg{i}"
        parent_imap_uid = f"msg{i - 1}" if i > 0 else None
        imap_messages[imap_uid] = create_mock_imap_message(
            imap_uid=imap_uid, thread_imap_uid="msg0", parent_imap_uid=parent_imap_uid
        )

    all_messages = {
        key: CombinedMessage(message) for key, message in imap_messages.items()
    }

    imap_uid_to_message_id = {imap_uid: imap_uid for imap_uid in imap_messages}

    threads = await imap_syncing_service._build_message_threads(
        imap_messages, all_messages, {}, imap_uid_to_message_id, {}
    )
    assert len(threads) == 1
    assert len(threads["msg0"]) == 1000


async def test_parse_thread_structure(imap_syncing_service: ImapSyncingService) -> None:
    thread_data = b"(3 6 (4 23)(44 7 96))"
    trees = imap_syncing_service._build_thread_trees(thread_data)

    assert len(trees) == 1, f"Expected 1 root, got {len(trees)}"
    root = trees[0]
    assert root.imap_uid == "3", f"Expected root ID '3', got '{root.imap_uid}'"
    assert len(root.children) == 1, f"Expected 1 child of '3', got {len(root.children)}"

    child_6 = root.children[0]
    assert child_6.imap_uid == "6", f"Expected child ID '6', got '{child_6.imap_uid}'"
    assert len(child_6.children) == 2, (
        f"Expected 2 children of '6', got {len(child_6.children)}"
    )

    child_4 = child_6.children[0]
    child_44 = child_6.children[1]
    assert child_4.imap_uid == "4", f"Expected child ID '4', got '{child_4.imap_uid}'"
    assert len(child_4.children) == 1, (
        f"Expected 1 child of '4', got {len(child_4.children)}"
    )
    assert child_4.children[0].imap_uid == "23", (
        f"Expected child ID '23', got '{child_4.children[0].imap_uid}'"
    )

    assert child_44.imap_uid == "44", (
        f"Expected child ID '44', got '{child_44.imap_uid}'"
    )
    assert len(child_44.children) == 1, (
        f"Expected 2 children of '44', got {len(child_44.children)}"
    )
    child_7 = child_44.children[0]
    child_96 = child_7.children[0]
    child_23 = child_4.children[0]
    assert len(child_7.children) == 1, (
        f"Expected 1 children of '77', got {len(child_7.children)}"
    )
    assert child_7.imap_uid == "7", f"Expected child ID '7', got '{child_7.imap_uid}'"
    assert child_96.imap_uid == "96", (
        f"Expected child ID '96', got '{child_96.imap_uid}'"
    )
    assert child_23.imap_uid == "23", (
        f"Expected child ID '23', got '{child_23.imap_uid}'"
    )
    assert len(child_23.children) == 0, (
        f"Expected 0 children of '23', got {len(child_7.children)}"
    )
    assert len(child_96.children) == 0, (
        f"Expected 0 children of '96', got {len(child_7.children)}"
    )


def test_get_message_id() -> None:
    imap_message = create_mock_imap_message(
        "\n <<EMAIL>>"
    )
    message_id = imap_message.get_message_id()
    assert (
        message_id
        == "<<EMAIL>>"
    )


def test_google_message_id_format() -> None:
    """Test handling of Google's internal email server message ID format (geopod-ismtpd)."""
    # Create message with Google's geopod-ismtpd format
    google_message = create_mock_imap_message(
        "<ku3qzR4LTi2vhXHaPxJmtw@geopod-ismtpd-33>"
    )
    message_id = google_message.get_message_id()

    # Verify the message ID is correctly processed
    assert message_id == "<ku3qzR4LTi2vhXHaPxJmtw@geopod-ismtpd-33>"

    # Test another example of internal server format without dots in domain
    internal_message = create_mock_imap_message("<abc123@internal-server>")
    internal_id = internal_message.get_message_id()
    assert internal_id == "<abc123@internal-server>"


async def test_update_thread_with_messages(
    imap_syncing_service: ImapSyncingService,
    email_account_service_ext: EmailAccountServiceExt,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
) -> None:
    # Setup test data
    user_id, organization_id = await make_user_org()

    # Create email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email="<EMAIL>",
        signature_html="test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create a thread
    thread_id = uuid4()
    thread = Thread(
        id=thread_id,
        email_account_id=email_account.id,
        organization_id=organization_id,
        subject="Original Subject",
        snippet="Original Snippet",
        unread=False,
        starred=False,
        has_attachments=False,
        folders=["INBOX"],
        participants=[],
        earliest_message_date=None,
        latest_message_received_date=None,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repository.insert(thread)

    # Create an attachment
    attachment_id = uuid4()
    attachment = Attachment(
        id=attachment_id,
        file_name="test.txt",
        content_type="text/plain",
        size=100,
        is_inline=False,
        s3_key=f"test/{attachment_id}/test.txt",
        organization_id=organization_id,
        email_account_id=email_account.id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await message_repository.insert(attachment)

    # Create messages
    message1 = Message(
        id=uuid4(),
        thread_id=thread_id,
        email_account_id=email_account.id,
        organization_id=organization_id,
        subject="Test Subject",
        send_from=[
            EmailHydratedParticipant(
                email="<EMAIL>",
                name="Sender",
                email_account_id=email_account.id,
            )
        ],
        send_to=[
            EmailHydratedParticipant(
                email="<EMAIL>",
                name="Recipient",
            )
        ],
        snippet="Test Snippet 1",
        body_text="Test Body 1",
        body_html="<p>Test Body 1</p>",
        unread=True,
        starred=False,
        folders=["INBOX"],
        attachment_ids=[attachment_id],
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
        received_at=zoned_utc_now(),
        headers=MessageHeaders(
            message_id="<<EMAIL>>",
            in_reply_to=None,
            references=None,
        ),
    )

    message2 = Message(
        id=uuid4(),
        thread_id=thread_id,
        email_account_id=email_account.id,
        organization_id=organization_id,
        subject="Re: Test Subject",
        send_from=[
            EmailHydratedParticipant(
                email="<EMAIL>",
                name="Recipient",
            )
        ],
        send_to=[
            EmailHydratedParticipant(
                email="<EMAIL>",
                name="Sender",
                email_account_id=email_account.id,
            )
        ],
        snippet="Test Snippet 2",
        body_text="Test Body 2",
        body_html="<p>Test Body 2</p>",
        unread=False,
        starred=True,
        folders=["INBOX", "Sent"],
        attachment_ids=None,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
        received_at=zoned_utc_now() + timedelta(hours=1),
        headers=MessageHeaders(
            message_id="<<EMAIL>>",
            in_reply_to="<<EMAIL>>",
            references=["<<EMAIL>>"],
        ),
    )

    # Call _update_thread_with_messages with real implementation
    email_dto = await imap_syncing_service._update_thread_with_messages(
        thread=thread,
        messages=[message1, message2],
    )
    updated_thread = email_dto.thread

    # Verify thread updates
    assert updated_thread.subject == "Test Subject"
    assert updated_thread.snippet == "Test Snippet 1"
    assert updated_thread.unread is True  # At least one message is unread
    assert updated_thread.starred is True  # At least one message is starred
    assert updated_thread.has_attachments is True
    assert updated_thread.folders is not None
    assert set(updated_thread.folders) == {"INBOX", "Sent"}
    assert len(updated_thread.participants) == 2
    assert updated_thread.latest_message_received_date == message2.received_at

    # Verify EmailDto
    assert email_dto.thread == updated_thread
    assert len(email_dto.message_dtos) == 2

    # Verify first message
    first_message_dto = email_dto.message_dtos[0]
    assert first_message_dto.message == message1

    # Verify second message
    second_message_dto = email_dto.message_dtos[1]
    assert second_message_dto.message == message2

    # Verify global thread data
    assert email_dto.global_thread_id is not None

    # Verify the global thread and global messages were created
    # Fetch the global thread
    global_thread = await thread_repository.find_by_tenanted_primary_key(
        GlobalThread,
        id=email_dto.global_thread_id,
        organization_id=organization_id,
    )

    # Verify global thread properties
    assert global_thread is not None
    assert global_thread.subject == updated_thread.subject
    assert global_thread.snippet == updated_thread.snippet
    assert thread.id in global_thread.thread_ids

    # Verify global messages
    # Find global messages by original message IDs from headers
    original_global_message = await thread_repository.find_by_original_message_id(
        original_message_id="<<EMAIL>>",
        organization_id=organization_id,
    )
    reply_global_message = await thread_repository.find_by_original_message_id(
        original_message_id="<<EMAIL>>",
        organization_id=organization_id,
    )

    # Verify original message
    assert original_global_message is not None
    assert original_global_message.parent_global_message_id is None
    assert original_global_message.global_thread_id == global_thread.id

    # Verify reply message
    assert reply_global_message is not None
    assert reply_global_message.parent_global_message_id == original_global_message.id
    assert reply_global_message.global_thread_id == global_thread.id

    # Verify global message associations
    message1_association = (
        await thread_repository.find_global_message_association_by_message_id(
            message_id=message1.id,
            organization_id=organization_id,
        )
    )
    message2_association = (
        await thread_repository.find_global_message_association_by_message_id(
            message_id=message2.id,
            organization_id=organization_id,
        )
    )

    assert message1_association is not None
    assert message1_association.global_message_id == original_global_message.id
    assert message1_association.message_id == message1.id

    assert message2_association is not None
    assert message2_association.global_message_id == reply_global_message.id
    assert message2_association.message_id == message2.id


async def test_sync(
    imap_syncing_service: ImapSyncingService,
    email_account_service_ext: EmailAccountServiceExt,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    email_event_repository: EmailEventRepository,
    mocker: MockerFixture,
) -> None:
    """
    Test the entire sync flow by mocking IMAP responses and verifying
    that email events are correctly created.
    """
    # Setup test data
    user_id, organization_id = await make_user_org()

    # Create email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email="<EMAIL>",
        signature_html="test signature",
        organization_id=organization_id,
        user_id=user_id,
        imap_host="imap.example.com",
        imap_port=993,
        imap_username="<EMAIL>",
        imap_password="password",  # noqa: S106
    )

    # Create mock IMAP messages
    original_provider_id = "original_message_123"
    reply_provider_id = "reply_message_456"
    bounce_provider_id = "bounce_message_789"

    original_date = zoned_utc_now() - timedelta(hours=2)
    reply_date = zoned_utc_now() - timedelta(hours=1)
    bounce_date = zoned_utc_now()

    # Create mock IMAP messages with proper relationships
    original_message = create_mock_imap_message(
        imap_uid=original_provider_id,
        date=original_date,
        thread_imap_uid=original_provider_id,
    )

    reply_message = create_mock_imap_message(
        imap_uid=reply_provider_id,
        date=reply_date,
        thread_imap_uid=original_provider_id,
        parent_imap_uid=original_provider_id,
    )

    bounce_message = create_mock_imap_message(
        imap_uid=bounce_provider_id,
        date=bounce_date,
        thread_imap_uid=original_provider_id,
    )
    # Set DSN status for bounce message
    bounce_message.dsn_status = DsnStatus.UNDELIVERED
    bounce_message.original_message_id = original_provider_id

    # Mock the _process_all_folders method to return our mock messages
    mock_imap_messages = {
        original_provider_id: original_message,
        reply_provider_id: reply_message,
        bounce_provider_id: bounce_message,
    }

    mock_imap_uid_to_message_id = {
        original_provider_id: original_provider_id,
        reply_provider_id: reply_provider_id,
        bounce_provider_id: bounce_provider_id,
    }

    # Mock get_decrypted_email_account_by_id to avoid encryption issues
    mocker.patch.object(
        imap_syncing_service.email_account_service_ext,
        "get_decrypted_email_account_by_id",
        return_value=email_account,
    )

    mocker.patch.object(
        imap_syncing_service,
        "_process_all_folders",
        return_value=(mock_imap_messages, mock_imap_uid_to_message_id),
    )

    with (
        mock.patch(
            "salestech_be.core.email.service.imap_syncing_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.core.email.service.imap_syncing_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        # Execute the sync function
        await imap_syncing_service.sync(email_account_id=email_account.id)

        # Verify threads and messages were created
        # Get all threads for this email account
        threads = await thread_repository.get_by_email_account_id(
            email_account_id=email_account.id,
            organization_id=organization_id,
        )

        # There should be one thread created
        assert len(threads) == 1
        thread = threads[0]

        # Find the global thread
        global_thread = await thread_repository.find_global_thread_by_thread_id(
            thread_id=thread.id,
            organization_id=organization_id,
        )
        assert global_thread is not None

        # Find the global messages
        global_messages = await thread_repository.list_messages_by_global_thread_id(
            global_thread_id=global_thread.id,
            organization_id=organization_id,
        )

        # Should have 2 global messages
        assert len(global_messages) == 2

        # Find the original global message
        original_global_message = await thread_repository.find_by_original_message_id(
            original_message_id=original_provider_id,
            organization_id=organization_id,
        )
        assert original_global_message is not None

        # Verify email events were created
        # For reply
        reply_events = await email_event_repository._find_by_column_values(
            EmailEvent,
            global_message_id=original_global_message.id,
            type=EmailEventType.REPLIED,
        )
        assert len(reply_events) > 0
        assert reply_events[0].global_message_id == original_global_message.id
        assert reply_events[0].global_thread_id == global_thread.id
        assert reply_events[0].type == EmailEventType.REPLIED

        # For bounce
        # bounce_events = await email_event_repository._find_by_column_values(
        #     EmailEvent,
        #     global_message_id=original_global_message.id,
        #     type=EmailEventType.BOUNCE_DETECTED,
        # )
        # assert len(bounce_events) > 0
        # assert bounce_events[0].global_message_id == original_global_message.id
        # assert bounce_events[0].global_thread_id == global_thread.id
        # assert bounce_events[0].type == EmailEventType.BOUNCE_DETECTED


async def test_is_self_reply(
    imap_syncing_service: ImapSyncingService,
) -> None:
    """Test the _is_self_reply method under different scenarios."""

    # Define common required fields for all test messages
    def create_test_message(
        sender_list: list[EmailHydratedParticipant], thread_id: UUID | None = None
    ) -> Message:
        return Message(
            id=uuid4(),
            thread_id=thread_id or uuid4(),
            organization_id=uuid4(),
            email_account_id=uuid4(),
            subject="Test Subject",
            send_from=sender_list,
            send_to=[
                EmailHydratedParticipant(
                    email="<EMAIL>",
                    name="Recipient",
                )
            ],
            snippet="Test snippet",
            body_text="Test body text",
            body_html="<p>Test body html</p>",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )

    # Case 1: Same sender in both messages (self-reply)
    same_sender = [
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="Same Sender",
        )
    ]
    thread_id1 = uuid4()
    same_sender_parent = create_test_message(same_sender, thread_id1)
    same_sender_reply = create_test_message(same_sender, thread_id1)

    # Case 2: Different senders (not a self-reply)
    sender1 = [
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="Sender One",
        )
    ]
    sender2 = [
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="Sender Two",
        )
    ]
    thread_id2 = uuid4()
    diff_sender_parent = create_test_message(sender1, thread_id2)
    diff_sender_reply = create_test_message(sender2, thread_id2)

    # Case 3: Multiple senders with overlap (self-reply)
    multi_sender1 = [
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="Sender One",
        ),
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="Sender Two",
        ),
    ]
    multi_sender2 = [
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="Sender Two",
        ),
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="Sender Three",
        ),
    ]
    thread_id3 = uuid4()
    multi_sender_parent = create_test_message(multi_sender1, thread_id3)
    multi_sender_reply = create_test_message(multi_sender2, thread_id3)

    # Case 4: Empty sender list (not a self-reply)
    thread_id4 = uuid4()
    empty_sender_parent = create_test_message([], thread_id4)
    empty_sender_reply = create_test_message(sender1, thread_id4)

    # Test cases
    assert (
        imap_syncing_service._is_self_reply(same_sender_parent, same_sender_reply)
        is True
    )
    assert (
        imap_syncing_service._is_self_reply(diff_sender_parent, diff_sender_reply)
        is False
    )
    assert (
        imap_syncing_service._is_self_reply(multi_sender_parent, multi_sender_reply)
        is True
    )
    assert (
        imap_syncing_service._is_self_reply(empty_sender_parent, empty_sender_reply)
        is False
    )
    assert (
        imap_syncing_service._is_self_reply(empty_sender_parent, empty_sender_parent)
        is False
    )


async def test_upsert_message(
    imap_syncing_service: ImapSyncingService,
    email_account_service_ext: EmailAccountServiceExt,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    thread_repository: ThreadRepository,
    message_repository: MessageRepository,
    mocker: MockerFixture,
) -> None:
    """
    Test the _upsert_message method in ImapSyncingService to ensure that:
    1. New messages are properly inserted
    2. Existing messages with changes are properly updated
    3. Existing messages without changes don't trigger unnecessary updates
    """
    # Setup test data
    user_id, organization_id = await make_user_org()
    thread_id = uuid4()

    # Create email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email="<EMAIL>",
        signature_html="test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create thread
    thread = Thread(
        id=thread_id,
        email_account_id=email_account.id,
        organization_id=organization_id,
        subject="Test Thread",
        snippet="Test Snippet",
        unread=False,
        starred=False,
        has_attachments=False,
        folders=["INBOX"],
        participants=[],
        earliest_message_date=None,
        latest_message_received_date=None,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repository.insert(thread)

    # Create message headers
    headers = MessageHeaders(
        message_id="<<EMAIL>>",
        in_reply_to=None,
        references=None,
    )

    # Create an IMAP message for testing
    provider_id = "test_provider_id_123"
    now = zoned_utc_now()
    imap_message = create_mock_imap_message(
        imap_uid=provider_id,
        date=now,
    )

    # Mock the extract_message_headers method to return our test headers
    mocker.patch.object(
        imap_syncing_service,
        "_extract_message_headers",
        return_value=headers,
    )

    # Mock the _upload_attachments method to return an empty list (no attachments)
    mocker.patch.object(
        imap_syncing_service,
        "_upload_attachments",
        return_value=[],
    )

    # Test Case 1: Insert a new message (not in messages_by_provider_id)
    messages_by_provider_id: dict[str, ShortDBMessage] = {}

    with (
        mock.patch(
            "salestech_be.core.email.service.imap_syncing_service.parse_main_body_text_and_persist",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
        mock.patch(
            "salestech_be.core.email.service.imap_syncing_service.classify_message_and_update_metadata",
            new_callable=mock.AsyncMock,
            return_value=None,
        ),
    ):
        new_message = await imap_syncing_service._upsert_message(
            provider_id=provider_id,
            imap_message=imap_message,
            thread_id=thread_id,
            email_account_id=email_account.id,
            organization_id=organization_id,
            messages_by_provider_id=messages_by_provider_id,
        )

        # Verify the new message was inserted
        assert new_message is not None
        assert new_message.provider_id == provider_id
        assert new_message.thread_id == thread_id
        assert new_message.email_account_id == email_account.id
        assert new_message.organization_id == organization_id
        assert new_message.folders == ["INBOX"]
        assert new_message.unread is True
        assert new_message.headers == headers

        # Test Case 2: Update an existing message with changes
        # Create a short database message with different values
        existing_message = Message(
            id=uuid4(),
            thread_id=thread_id,
            email_account_id=email_account.id,
            organization_id=organization_id,
            subject="Test Subject",
            send_from=[],
            send_to=[],
            snippet="Snippet",
            body_text="Body Text",
            body_html="<p>Body HTML</p>",
            folders=["SENT"],  # Different folder
            unread=False,  # Different unread status
            headers=MessageHeaders(  # Different headers
                message_id="<<EMAIL>>",
                in_reply_to=None,
                references=None,
            ),
            received_at=now - timedelta(hours=1),  # Different received date
            provider=None,
            provider_id=provider_id,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )

        # Insert the existing message
        existing_message = await message_repository.insert(existing_message)

        # Create a ShortDBMessage for the messages_by_provider_id dict
        short_message = ShortDBMessage(
            id=existing_message.id,
            provider_id=provider_id,
            thread_id=thread_id,
            folders=existing_message.folders,
            unread=existing_message.unread,
            headers=existing_message.headers,
            received_at=existing_message.received_at,
        )

        # Add it to messages_by_provider_id
        messages_by_provider_id = {provider_id: short_message}

        # Update IMAP message to have a different date than the existing message
        imap_message.internal_date = now

        # Mock get_message_date to return exactly the same now object
        mocker.patch.object(imap_message, "get_message_date", return_value=now)

        # Call _upsert_message to update
        updated_message = await imap_syncing_service._upsert_message(
            provider_id=provider_id,
            imap_message=imap_message,
            thread_id=thread_id,
            email_account_id=email_account.id,
            organization_id=organization_id,
            messages_by_provider_id=messages_by_provider_id,
        )

        # Verify the existing message was updated
        assert updated_message is not None
        assert updated_message.id == existing_message.id
        assert updated_message.folders == ["INBOX"]  # Updated
        assert updated_message.unread is True  # Updated
        assert updated_message.headers == headers  # Updated
        assert updated_message.received_at == now

        # Test Case 3: No update for an existing message with no changes
        # Create a new message instance that matches the IMAP message
        matched_message = Message(
            id=updated_message.id,
            thread_id=thread_id,
            email_account_id=email_account.id,
            organization_id=organization_id,
            subject="Test Subject",
            send_from=[],
            send_to=[],
            snippet="Snippet",
            body_text="Body Text",
            body_html="<p>Body HTML</p>",
            folders=["INBOX"],  # Matching folder
            unread=True,  # Matching unread status
            headers=headers,  # Matching headers
            received_at=updated_message.received_at,  # Matching received date
            provider=None,
            provider_id=provider_id,
            created_at=updated_message.created_at,
            updated_at=updated_message.updated_at,
        )

        # Mock the update_by_primary_key and find_by_primary_key methods to track calls
        update_spy = mocker.spy(message_repository, "update_by_primary_key")

        # Create a ShortDBMessage for the no-changes test
        matched_short_message = ShortDBMessage(
            id=matched_message.id,
            provider_id=provider_id,
            thread_id=thread_id,
            folders=matched_message.folders,
            unread=matched_message.unread,
            headers=matched_message.headers,
            received_at=matched_message.received_at,
        )

        # Create a new messages_by_provider_id with our matched message
        messages_by_provider_id = {provider_id: matched_short_message}

        # Call _upsert_message again
        result_message = await imap_syncing_service._upsert_message(
            provider_id=provider_id,
            imap_message=imap_message,
            thread_id=thread_id,
            email_account_id=email_account.id,
            organization_id=organization_id,
            messages_by_provider_id=messages_by_provider_id,
        )

        # Verify that no update was performed - this is the key assertion
        # When message properties match IMAP message, no database update should occur
        update_spy.assert_not_called()

        # Verify we still get the correct message back
        assert result_message.id == matched_message.id
        assert result_message.folders == ["INBOX"]
        assert result_message.unread is True
        assert result_message.headers == headers


def test_get_dsn_status_with_various_formats() -> None:
    """Test the DSN status detection with various email formats."""
    # Test case 1: Standard bounce message with MAILER-DAEMON
    bounce_email = MIMEMultipart()
    bounce_email["From"] = "<EMAIL>"
    bounce_email["Subject"] = "Test"
    bounce_email.attach(MIMEText("Test content", "plain"))

    imap_message = ImapMessage(
        imap_uid="1",
        email_message=message_from_string(bounce_email.as_string()),
        is_unread=True,
        folder="INBOX",
        preview="",
        internal_date=zoned_utc_now(),
        thread_imap_uid="1",
        parent_imap_uid=None,
        visible_text=None,
        html_body=None,
    )
    assert imap_message.get_dsn_status() == DsnStatus.UNDELIVERED

    # Test case 2: Bounce message with display name format
    bounce_email_with_name = MIMEMultipart()
    bounce_email_with_name["From"] = "Mail Delivery System <<EMAIL>>"
    bounce_email_with_name["Subject"] = "Test"
    bounce_email_with_name.attach(MIMEText("Test content", "plain"))

    imap_message = ImapMessage(
        imap_uid="2",
        email_message=message_from_string(bounce_email_with_name.as_string()),
        is_unread=True,
        folder="INBOX",
        preview="",
        internal_date=zoned_utc_now(),
        thread_imap_uid="2",
        parent_imap_uid=None,
        visible_text=None,
        html_body=None,
    )
    assert imap_message.get_dsn_status() == DsnStatus.UNDELIVERED

    # Test case 3: Bounce message with mixed case
    bounce_email_mixed_case = MIMEMultipart()
    bounce_email_mixed_case["From"] = "<EMAIL>"
    bounce_email_mixed_case["Subject"] = "Test"
    bounce_email_mixed_case.attach(MIMEText("Test content", "plain"))

    imap_message = ImapMessage(
        imap_uid="3",
        email_message=message_from_string(bounce_email_mixed_case.as_string()),
        is_unread=True,
        folder="INBOX",
        preview="",
        internal_date=zoned_utc_now(),
        thread_imap_uid="3",
        parent_imap_uid=None,
        visible_text=None,
        html_body=None,
    )
    assert imap_message.get_dsn_status() == DsnStatus.UNDELIVERED

    # Test case 4: Normal message (not a bounce)
    normal_email = MIMEMultipart()
    normal_email["From"] = "<EMAIL>"
    normal_email["Subject"] = "Test"
    normal_email.attach(MIMEText("Test content", "plain"))

    imap_message = ImapMessage(
        imap_uid="4",
        email_message=message_from_string(normal_email.as_string()),
        is_unread=True,
        folder="INBOX",
        preview="",
        internal_date=zoned_utc_now(),
        thread_imap_uid="4",
        parent_imap_uid=None,
        visible_text=None,
        html_body=None,
    )
    assert imap_message.get_dsn_status() is None

    # Test case 5: Postmaster message
    postmaster_email = MIMEMultipart()
    postmaster_email["From"] = "<EMAIL>"
    postmaster_email["Subject"] = "Test"
    postmaster_email.attach(MIMEText("Test content", "plain"))

    imap_message = ImapMessage(
        imap_uid="5",
        email_message=message_from_string(postmaster_email.as_string()),
        is_unread=True,
        folder="INBOX",
        preview="",
        internal_date=zoned_utc_now(),
        thread_imap_uid="5",
        parent_imap_uid=None,
        visible_text=None,
        html_body=None,
    )
    assert imap_message.get_dsn_status() == DsnStatus.UNDELIVERED

    # Test case 6: Complex display name with postmaster
    complex_postmaster = MIMEMultipart()
    complex_postmaster["From"] = '"Mail Server Administrator" <<EMAIL>>'
    complex_postmaster["Subject"] = "Test"
    complex_postmaster.attach(MIMEText("Test content", "plain"))

    imap_message = ImapMessage(
        imap_uid="6",
        email_message=message_from_string(complex_postmaster.as_string()),
        is_unread=True,
        folder="INBOX",
        preview="",
        internal_date=zoned_utc_now(),
        thread_imap_uid="6",
        parent_imap_uid=None,
        visible_text=None,
        html_body=None,
    )
    assert imap_message.get_dsn_status() == DsnStatus.UNDELIVERED

    # Test case 7: Exchange NDR header
    exchange_ndr = MIMEMultipart()
    exchange_ndr["From"] = "<EMAIL>"
    exchange_ndr["Subject"] = "Test"
    exchange_ndr["X-MS-Exchange-Message-Is-Ndr"] = "True"
    exchange_ndr.attach(MIMEText("Test content", "plain"))

    imap_message = ImapMessage(
        imap_uid="7",
        email_message=message_from_string(exchange_ndr.as_string()),
        is_unread=True,
        folder="INBOX",
        preview="",
        internal_date=zoned_utc_now(),
        thread_imap_uid="7",
        parent_imap_uid=None,
        visible_text=None,
        html_body=None,
    )
    assert imap_message.get_dsn_status() == DsnStatus.UNDELIVERED

    # Test case 8: Email containing but not matching system accounts
    non_match_email = MIMEMultipart()
    non_match_email["From"] = "<EMAIL>"
    non_match_email["Subject"] = "Test"
    non_match_email.attach(MIMEText("Test content", "plain"))

    imap_message = ImapMessage(
        imap_uid="8",
        email_message=message_from_string(non_match_email.as_string()),
        is_unread=True,
        folder="INBOX",
        preview="",
        internal_date=zoned_utc_now(),
        thread_imap_uid="8",
        parent_imap_uid=None,
        visible_text=None,
        html_body=None,
    )
    assert imap_message.get_dsn_status() is None


async def test_get_or_update_thread(
    imap_syncing_service: ImapSyncingService,
    email_account_service_ext: EmailAccountServiceExt,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    thread_repository: ThreadRepository,
) -> None:
    """Test the _get_or_update_thread method with different update scenarios."""
    # Setup test data
    user_id, organization_id = await make_user_org()

    # Create email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email="<EMAIL>",
        signature_html="test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create initial thread with participants
    thread_id = uuid4()
    initial_participants = [
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="Sender",
            email_account_id=email_account.id,
        ),
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="Recipient",
        ),
    ]

    thread = Thread(
        id=thread_id,
        email_account_id=email_account.id,
        organization_id=organization_id,
        subject="Original Subject",
        snippet="Original Snippet",
        unread=False,
        starred=False,
        has_attachments=False,
        folders=["INBOX"],
        participants=initial_participants,
        earliest_message_date=None,
        latest_message_received_date=None,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )

    await thread_repository.insert(thread)

    # Test Case 1: No changes - the method should not update the thread
    updated_thread = await imap_syncing_service._get_or_update_thread(
        thread=thread,
        organization_id=organization_id,
        subject="Original Subject",
        snippet="Original Snippet",
        unread=False,
        starred=False,
        has_attachments=False,
        folders=["INBOX"],
        earliest_message_date=None,
        latest_message_received_date=None,
        participants=initial_participants,
    )

    # Verify no updates were made
    assert updated_thread.subject == "Original Subject"
    assert updated_thread.snippet == "Original Snippet"
    assert updated_thread.unread is False
    assert updated_thread.starred is False
    assert updated_thread.has_attachments is False
    assert updated_thread.folders == ["INBOX"]
    assert len(updated_thread.participants) == 2
    assert updated_thread.participants[0].email == "<EMAIL>"
    assert updated_thread.participants[1].email == "<EMAIL>"

    # Test Case 2: Update basic fields
    new_date = zoned_utc_now()
    updated_thread = await imap_syncing_service._get_or_update_thread(
        thread=thread,
        organization_id=organization_id,
        subject="Updated Subject",
        snippet="Updated Snippet",
        unread=True,
        starred=True,
        has_attachments=True,
        folders=["INBOX", "Important"],
        earliest_message_date=new_date,
        latest_message_received_date=new_date,
        participants=initial_participants,
    )

    # Verify updates were made
    assert updated_thread.subject == "Updated Subject"
    assert updated_thread.snippet == "Updated Snippet"
    assert updated_thread.unread is True
    assert updated_thread.starred is True
    assert updated_thread.has_attachments is True
    assert updated_thread.folders is not None
    assert set(updated_thread.folders) == {"INBOX", "Important"}
    assert updated_thread.earliest_message_date == new_date
    assert updated_thread.latest_message_received_date == new_date

    # Test Case 3: Update participants - same number but different attributes
    modified_participants = [
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="Updated Sender Name",  # Name changed
            email_account_id=email_account.id,
        ),
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="Recipient",
            contact_id=uuid4(),  # Added contact_id
        ),
    ]

    updated_thread = await imap_syncing_service._get_or_update_thread(
        thread=updated_thread,
        organization_id=organization_id,
        subject="Updated Subject",
        snippet="Updated Snippet",
        unread=True,
        starred=True,
        has_attachments=True,
        folders=["INBOX", "Important"],
        earliest_message_date=new_date,
        latest_message_received_date=new_date,
        participants=modified_participants,
    )

    # Verify participants were updated
    assert len(updated_thread.participants) == 2
    assert updated_thread.participants[0].email == "<EMAIL>"
    assert updated_thread.participants[0].name == "Updated Sender Name"  # Name updated
    assert updated_thread.participants[1].email == "<EMAIL>"
    assert updated_thread.participants[1].contact_id is not None  # Contact ID added

    # Test Case 4: Different number of participants
    new_participants = [
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="Sender",
            email_account_id=email_account.id,
        ),
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="Recipient",
        ),
        EmailHydratedParticipant(
            email="<EMAIL>",
            name="New Person",
        ),
    ]

    updated_thread = await imap_syncing_service._get_or_update_thread(
        thread=updated_thread,
        organization_id=organization_id,
        subject="Updated Subject",
        snippet="Updated Snippet",
        unread=True,
        starred=True,
        has_attachments=True,
        folders=["INBOX", "Important"],
        earliest_message_date=new_date,
        latest_message_received_date=new_date,
        participants=new_participants,
    )

    # Verify participants were updated with new count
    assert len(updated_thread.participants) == 3
    emails = [p.email for p in updated_thread.participants]
    assert "<EMAIL>" in emails
    assert "<EMAIL>" in emails
    assert "<EMAIL>" in emails
