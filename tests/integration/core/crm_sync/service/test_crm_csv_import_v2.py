import csv
import io
import uuid
from collections.abc import Awaitable, Callable
from datetime import datetime
from pathlib import Path
from uuid import UUID, uuid4

import pytz
from pydantic_extra_types.timezone_name import TimeZoneName

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.common import (
    CustomObjectIdentifier,
    RelationshipType,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    CaseAwareUniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    TextAreaFieldProperty,
    TextFieldProperty,
)
from salestech_be.common.type.metadata.field.field_type_property_create import (
    SingleSelectFieldPropertyCreate,
)
from salestech_be.common.type.metadata.field.field_value import (
    SingleSelectFieldValue,
    TextAreaFieldValue,
    TextFieldValue,
)
from salestech_be.common.type.metadata.value import NativeValueType
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.service.contact_query_service import ContactQueryService
from salestech_be.core.custom_object.service.association_service import (
    AssociationService,
)
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.data.service.query_service import DomainObjectQueryService
from salestech_be.core.imports.models.import_job import (
    AssociationLabelMapping,
    ColumnMapping,
    FileDupeResolution,
    FileImportType,
    FileMetadata,
    ImportConfiguration,
    ImportCsvJobStatus,
    ImportJob,
    ObjectImportMode,
    ObjectMapping,
    QualifiedImportField,
)
from salestech_be.core.imports.service.crm_sync_service import (
    CrmSyncService,
)
from salestech_be.core.imports.types import ImportRecord
from salestech_be.core.metadata.dto.service_api_schema import (
    SelectListCreateRequest,
    SelectListValueCreateRequest,
)
from salestech_be.db.models.custom_field import CustomField
from salestech_be.db.models.custom_object_association import CustomObjectAssociation
from salestech_be.db.models.custom_object_data import CustomObjectData
from salestech_be.db.models.import_record import ImportEntityType, ImportRecordStatus
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from tests.integration.core.crm_sync.service.test_utils import (
    _setup_import_repository_clean_slate_from_org_id,
)

logger = get_logger()


class TestCrmCsvImport:
    class TestAccountObjectImport:
        async def test_reported20250328_csv_import_creates_account_objects_sanity(
            self,
            crm_sync_service: CrmSyncService,
            domain_object_query_service: DomainObjectQueryService,
            make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
        ) -> None:
            """Test that CSV import creates account objects in the database."""
            # Unpack user_id and org_id from the tuple
            user_id, organization_id = await make_user_org()

            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            # Read the CSV file
            csv_file_path = (
                Path(__file__).parent / "data" / "reported20250328_account_import_1.csv"
            )
            with open(csv_file_path, "rb") as f:  # noqa: ASYNC230
                csv_content = f.read()

            # For reference, the CSV header:
            #   official_website_url,description,industry,technologies,estimated_annual_revenue,estimated_employee_count,
            #   linkedin_url,facebook_url,x_url,owner_email,created_at,status,contact_name,contact_email,contact_title,
            #   contact_department
            file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    # Account mapping
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="display_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="official_website_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_official_website"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="description",
                                qualified_field=QualifiedImportField(
                                    path=["company_description"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="industry",
                                qualified_field=QualifiedImportField(
                                    path=["company_industry"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="technologies",
                                qualified_field=QualifiedImportField(
                                    path=["company_technologies"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="estimated_annual_revenue",
                                qualified_field=QualifiedImportField(
                                    path=["company_estimated_annual_revenue"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="estimated_employee_count",
                                qualified_field=QualifiedImportField(
                                    path=["company_estimated_employee_count"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="linkedin_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_linkedin_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="facebook_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_facebook_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="x_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_x_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="owner_email",
                                qualified_field=QualifiedImportField(
                                    path=["company_owner_email"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="created_at",
                                qualified_field=QualifiedImportField(
                                    path=["company_created_at"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="status",
                                qualified_field=QualifiedImportField(
                                    path=["company_status"]
                                ),
                            ),
                        ],
                    ),
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="contact_name",
                                qualified_field=QualifiedImportField(
                                    path=["contact_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="contact_email",
                                qualified_field=QualifiedImportField(
                                    path=["contact_primary_email"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="contact_title",
                                qualified_field=QualifiedImportField(
                                    path=["contact_job_title"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="contact_department",
                                qualified_field=QualifiedImportField(
                                    path=["contact_department"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="official_website_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_official_website"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="display_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_display_name"]
                                ),
                            ),
                        ],
                    ),
                ],
                association_label_mapping=[],
            )

            csv_import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
            )
            await crm_sync_service._process_import_job_file_v2(
                file_binary=csv_content,
                csv_import_job=csv_import_job,
                heartbeat_resume=None,
            )

            accounts = await domain_object_query_service.list_account_records(
                organization_id=organization_id,
                include_custom_object=False,
            )
            # for index, account in enumerate(accounts):
            #     logger.info(f"Debug Test account({index + 1}): ({account})")

            assert len(accounts) == 39  # There are 39 accounts in the CSV file
            has_industry_insurance = False
            for account in accounts:
                if (
                    account.data.category_list
                    and "Insurance" in account.data.category_list
                ):
                    has_industry_insurance = True
            assert (
                has_industry_insurance
            )  # Sanity check that category_list (industry is being populated)

        async def test_import_account_only_jana(
            self,
            crm_sync_service: CrmSyncService,
            domain_object_query_service: DomainObjectQueryService,
            make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
        ) -> None:
            """Test importing an account only with from the Jana project."""
            # Unpack user_id and org_id from the tuple
            user_id, organization_id = await make_user_org()

            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            # Read the CSV file
            csv_file_path = (
                Path(__file__).parent
                / "data"
                / "reported20250401_account_import_jana.csv"
            )
            with open(csv_file_path, "rb") as f:  # noqa: ASYNC230
                csv_content = f.read()

            file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    # Account mapping
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="display_name",
                                qualified_field=QualifiedImportField(
                                    path=["display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="official_website_url",
                                qualified_field=QualifiedImportField(
                                    path=["official_website"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="street_one",
                                qualified_field=QualifiedImportField(
                                    path=["street_one"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="city",
                                qualified_field=QualifiedImportField(path=["city"]),
                            ),
                            ColumnMapping(
                                column_name="state",
                                qualified_field=QualifiedImportField(path=["state"]),
                            ),
                            ColumnMapping(
                                column_name="linkedin_url",
                                qualified_field=QualifiedImportField(
                                    path=["linkedin_url"]
                                ),
                            ),
                        ],
                    ),
                ],
                association_label_mapping=[],
            )

            csv_import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                    ],
                    timezone=TimeZoneName("UTC"),
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
            )
            await crm_sync_service._process_import_job_file_v2(
                file_binary=csv_content,
                csv_import_job=csv_import_job,
                heartbeat_resume=None,
            )

            accounts = await domain_object_query_service.list_account_records(
                organization_id=organization_id,
                include_custom_object=False,
            )
            # for index, account in enumerate(accounts):
            #     logger.info(f"Debug Test account({index + 1}): ({account})")

            assert len(accounts) == 29

        async def test_import_account_with_custom_fields_v2(
            self,
            crm_sync_service: CrmSyncService,
            custom_object_service: CustomObjectService,
            domain_object_query_service: DomainObjectQueryService,
            make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
        ) -> None:
            user_id, organization_id = await make_user_org()
            # Get the custom object dto for Account extension
            account_custom_object_dto = await crm_sync_service.custom_object_service.enable_extension_custom_object(
                user_id=user_id,
                organization_id=organization_id,
                objects_to_enable=[ExtendableStandardObject.account],
            )

            # Retrieve the specific CustomObject ID for Account
            account_cobj = account_custom_object_dto.get(
                ExtendableStandardObject.account
            )
            assert account_cobj is not None
            account_cobj_id = account_cobj.id

            # Create a text custom field
            account_custom_text_field = (
                await crm_sync_service.custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=account_cobj_id,
                    custom_field_type_property_create=TextFieldProperty(
                        field_display_name="AccountCustomText",
                        field_description="A custom text field for accounts",
                        is_required=False,
                    ),
                )
            )

            # Create a select list
            select_list = (
                await custom_object_service.select_list_service.create_select_list(
                    select_list_create_request=SelectListCreateRequest(
                        organization_id=organization_id,
                        display_name="AccountStatusListTest",
                        data_type=NativeValueType.STRING,
                        actioning_user_id=user_id,
                    )
                )
            )
            await custom_object_service.select_list_service.add_select_list_value(
                select_list_id=select_list.id,
                actioning_user_id=user_id,
                organization_id=organization_id,
                slvcr=SelectListValueCreateRequest(
                    display_value="ProspectTest1",
                    is_default=False,
                ),
            )
            await custom_object_service.select_list_service.add_select_list_value(
                select_list_id=select_list.id,
                actioning_user_id=user_id,
                organization_id=organization_id,
                slvcr=SelectListValueCreateRequest(
                    display_value="CustomerTest2",
                    is_default=False,
                ),
            )
            account_custom_select_field = (
                await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=account_cobj_id,  # Now safe to access .id
                    custom_field_type_property_create=SingleSelectFieldPropertyCreate(
                        field_display_name="CustomAccountStatusListTest",
                        is_required=False,
                        select_list_id=select_list.id,
                    ),
                )
            )

            # Prepare CSV data
            csv_content = (
                "Account Name,Website,Description,Custom Text,Custom Status\n"
                '"TestCustomFieldsAccount_1",test1.com,"Desc 1","CustomTextValue_1","ProspectTest1"\n'
                '"TestCustomFieldsAccount_2",test2.com,"Desc 2","CustomTextValue_2","CustomerTest2"'
            )
            file_id = uuid.uuid4()

            # Define Import Job configuration
            import_config = ImportConfiguration(
                object_identifiers=[StandardObjectIdentifier(object_name="account")],
                file_import_type=FileImportType.SINGLE_FILE,
                object_import_mode=ObjectImportMode.CREATE_ONLY,
                file_dupe_resolution=FileDupeResolution.USE_FIRST,
                timezone=TimeZoneName("UTC"),
            )

            # Define Mappings
            column_mappings = [
                ColumnMapping(
                    column_name="Account Name",
                    qualified_field=QualifiedImportField(path=["display_name"]),
                ),
                ColumnMapping(
                    column_name="Website",
                    qualified_field=QualifiedImportField(path=["domain_name"]),
                ),
                ColumnMapping(
                    column_name="Description",
                    qualified_field=QualifiedImportField(path=["description"]),
                ),
                ColumnMapping(
                    column_name="Custom Text",
                    qualified_field=QualifiedImportField(
                        path=[str(account_custom_text_field.id)]
                    ),
                ),
                ColumnMapping(
                    column_name="Custom Status",
                    qualified_field=QualifiedImportField(
                        path=[str(account_custom_select_field.id)]
                    ),
                ),
            ]
            object_mapping = ObjectMapping(
                object_identifier=StandardObjectIdentifier(object_name="account"),
                column_mappings=column_mappings,
            )
            file_metadata = FileMetadata(
                file_id=file_id,
                object_mappings=[object_mapping],
                association_label_mapping=[],
            )

            # Create Import Job
            import_job = await crm_sync_service.import_job_repository.create(
                ImportJob(
                    id=uuid.uuid4(),
                    workflow_id=str(uuid.uuid4()),
                    organization_id=organization_id,
                    created_by_user_id=user_id,
                    display_name="Test Account Import with Custom Fields",
                    configuration=import_config,
                    metadata=file_metadata,
                    status=ImportCsvJobStatus.STARTED,
                    created_at=zoned_utc_now(),
                    updated_at=zoned_utc_now(),
                )
            )

            # Process the import
            import_records = await crm_sync_service._process_import_job_file_v2(
                file_binary=csv_content.encode("utf-8"),
                csv_import_job=import_job,
                heartbeat_resume=None,
            )

            # Verify results
            assert len(import_records) == 2
            assert import_records[0].status == ImportRecordStatus.SUCCESS
            assert import_records[1].status == ImportRecordStatus.SUCCESS
            assert import_records[0].entity_id is not None
            assert import_records[1].entity_id is not None

            # Fetch both accounts first
            potential_account_1 = await crm_sync_service.account_service.get_account_v2(
                account_id=import_records[0].entity_id, organization_id=organization_id
            )

            potential_account_2 = await crm_sync_service.account_service.get_account_v2(
                account_id=import_records[1].entity_id, organization_id=organization_id
            )

            # Identify accounts based on a unique property, e.g., display_name
            if potential_account_1.display_name == "TestCustomFieldsAccount_1":
                account_1 = potential_account_1
                account_2 = potential_account_2
            elif potential_account_1.display_name == "TestCustomFieldsAccount_2":
                account_1 = potential_account_2
                account_2 = potential_account_1
            else:
                # Fail test if neither potential account matches expected names
                raise AssertionError(
                    "Could not identify accounts based on display_name. "
                    f"Account 1 display_name: {potential_account_1.display_name}, "
                    f"Account 2 display_name: {potential_account_2.display_name}"
                )

            # Verify Account 1
            assert account_1.display_name == "TestCustomFieldsAccount_1"
            assert account_1.domain_name == "test1.com"
            assert account_1.description == "Desc 1"

            account_1_ext_data = await crm_sync_service.custom_object_service.get_custom_object_data_by_extension_id(
                organization_id=organization_id,
                parent_object_name=ExtendableStandardObject.account,
                custom_object_data_extension_id=account_1.id,
            )
            assert account_1_ext_data.custom_object_data.value_1 is not None
            val1a = account_1_ext_data.custom_object_data.value_1.value_by_field_id.get(
                account_custom_text_field.id
            )
            assert isinstance(val1a, TextFieldValue)
            assert val1a.text == "CustomTextValue_1"

            assert account_1_ext_data.custom_object_data.value_2 is not None
            val1b = account_1_ext_data.custom_object_data.value_2.value_by_field_id.get(
                account_custom_select_field.id
            )
            assert isinstance(val1b, SingleSelectFieldValue)
            assert val1b.value_id is not None
            select_list_value_by_id_1 = (
                await custom_object_service.select_list_service.get_select_list_value(
                    organization_id=organization_id,
                    select_list_value_id=val1b.value_id,
                )
            )
            assert select_list_value_by_id_1.display_value == "ProspectTest1"

            # Verify Account 2
            assert account_2.display_name == "TestCustomFieldsAccount_2"
            assert account_2.domain_name == "test2.com"
            assert account_2.description == "Desc 2"

            account_2_ext_data = await crm_sync_service.custom_object_service.get_custom_object_data_by_extension_id(
                organization_id=organization_id,
                parent_object_name=ExtendableStandardObject.account,
                custom_object_data_extension_id=account_2.id,
            )
            assert account_2_ext_data.custom_object_data.value_1 is not None
            val2a = account_2_ext_data.custom_object_data.value_1.value_by_field_id.get(
                account_custom_text_field.id
            )
            assert isinstance(val2a, TextFieldValue)
            assert val2a.text == "CustomTextValue_2"

            assert account_2_ext_data.custom_object_data.value_2 is not None
            val2b = account_2_ext_data.custom_object_data.value_2.value_by_field_id.get(
                account_custom_select_field.id
            )
            assert isinstance(val2b, SingleSelectFieldValue)
            assert val2b.value_id is not None
            select_list_value_by_id_2 = (
                await custom_object_service.select_list_service.get_select_list_value(
                    organization_id=organization_id,
                    select_list_value_id=val2b.value_id,
                )
            )
            assert select_list_value_by_id_2.display_value == "CustomerTest2"

        async def test_reported20250328_csv_import_creates_account_objects_with_std_relationships(
            self,
            crm_sync_service: CrmSyncService,
            domain_object_query_service: DomainObjectQueryService,
            make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
        ) -> None:
            """Test that CSV import creates account objects in the database."""
            # Unpack user_id and org_id from the tuple
            user_id, organization_id = await make_user_org()

            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            # Read the CSV file
            csv_file_path = (
                Path(__file__).parent / "data" / "reported20250328_account_import_1.csv"
            )
            with open(csv_file_path, "rb") as f:  # noqa: ASYNC230
                csv_content = f.read()

            # For reference, the CSV header:
            #   official_website_url,description,industry,technologies,estimated_annual_revenue,estimated_employee_count,
            #   linkedin_url,facebook_url,x_url,owner_email,created_at,status,contact_name,contact_email,contact_title,
            #   contact_department
            # For the original config, see
            #   https://reevoai.slack.com/archives/C08FFPP1YTS/p1743208744154319?thread_ts=**********.043599&cid=C08FFPP1YTS
            original_file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    # Account mapping
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="display_name",
                                qualified_field=QualifiedImportField(
                                    path=["display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="official_website_url",
                                qualified_field=QualifiedImportField(
                                    path=["official_website"]
                                ),
                            ),
                        ],
                    ),
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="contact_name",
                                qualified_field=QualifiedImportField(
                                    path=["display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="contact_email",
                                qualified_field=QualifiedImportField(
                                    path=["primary_email"]
                                ),
                            ),
                        ],
                    ),
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact_account_role
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="contact_title",
                                qualified_field=QualifiedImportField(path=["title"]),
                            ),
                        ],
                    ),
                ],
                association_label_mapping=[
                    AssociationLabelMapping(
                        relationship_id="contact_account_role__to__contact",
                        column_mapping={},
                    ),
                    AssociationLabelMapping(
                        relationship_id="contact_account_role__to__account",
                        column_mapping={},
                    ),
                ],
            )

            csv_import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=original_file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
            )
            await crm_sync_service._process_import_job_file_v2(
                file_binary=csv_content,
                csv_import_job=csv_import_job,
                heartbeat_resume=None,
            )

            accounts = await domain_object_query_service.list_account_records(
                organization_id=organization_id,
                include_custom_object=False,
            )
            # for index, account in enumerate(accounts):
            #    logger.info(f"Debug Test account({index + 1}): ({account})")

            # There are 39 accounts in the CSV file, test that even with relationship UUID error, all accounts created.
            assert len(accounts) == 39

            # Query import records to verify errors were recorded
            import_records = await crm_sync_service.import_repository.list_by_job_id(
                job_id=csv_import_job.id, organization_id=csv_import_job.organization_id
            )

            # Verify 0 records have errors
            failed_records = [
                record
                for record in import_records
                if record.status == ImportRecordStatus.FAILED
            ]
            assert len(failed_records) == 0

    async def test_all_account_entries_are_conflict(
        self,
        crm_sync_service: CrmSyncService,
        make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    ) -> None:
        user_id, organization_id = await make_user_org()
        await _setup_import_repository_clean_slate_from_org_id(
            crm_sync_service=crm_sync_service,
            organization_id=organization_id,
        )
        csv_content_orig = (
            "AccountName,AccountDomain,AccountDesc\n"
            "aaa,aaa.com,origAaaDesc\n"
            "bbb,bbb.com,origBbbDesc\n"
            "ccc,ccc.com,origCccDesc"
        )
        file_metadata = FileMetadata(
            file_id=uuid4(),
            object_mappings=[
                ObjectMapping(
                    object_identifier=StandardObjectIdentifier(object_name="account"),
                    column_mappings=[
                        ColumnMapping(
                            column_name="AccountName",
                            qualified_field=QualifiedImportField(
                                path=["company_display_name"]
                            ),
                        ),
                        ColumnMapping(
                            column_name="AccountDomain",
                            qualified_field=QualifiedImportField(
                                path=["company_domain_name"]
                            ),
                        ),
                        ColumnMapping(
                            column_name="AccountDesc",
                            qualified_field=QualifiedImportField(
                                path=["company_description"]
                            ),
                        ),
                    ],
                )
            ],
            association_label_mapping=[],
        )
        csv_import_job = ImportJob(
            id=uuid4(),
            organization_id=organization_id,
            created_by_user_id=user_id,
            created_at=datetime.now(pytz.UTC),
            updated_at=datetime.now(pytz.UTC),
            metadata=file_metadata,
            configuration=ImportConfiguration(
                object_identifiers=[
                    StandardObjectIdentifier(
                        object_name=StdObjectIdentifiers.account,
                    ),
                ],
                file_import_type=FileImportType.SINGLE_FILE,
                object_import_mode=ObjectImportMode.CREATE_ONLY,
                file_dupe_resolution=FileDupeResolution.USE_FIRST,
            ),
        )
        processed_records = await crm_sync_service._process_import_job_file_v2(
            file_binary=csv_content_orig.encode("utf-8"),
            csv_import_job=csv_import_job,
            heartbeat_resume=None,
        )
        for record in processed_records:
            assert record.status == ImportRecordStatus.SUCCESS
        assert len(processed_records) == 3

        csv_content_update = (
            "AccountName,AccountDomain,AccountDesc\n"
            "aaa,aaa.com,origAaaDesc__UPPPPDATEDDDDDDDDDD\n"
            "bbb,bbb.com,origBbbDesc__UPPPPDATEDDDDDDDDDD\n"
            "ccc,ccc.com,origDddDesc__UPPPPDATEDDDDDDDDDD"
        )
        csv_import_job_conflict = ImportJob(
            id=uuid4(),
            organization_id=organization_id,
            created_by_user_id=user_id,
            created_at=datetime.now(pytz.UTC),
            updated_at=datetime.now(pytz.UTC),
            metadata=file_metadata,
            configuration=ImportConfiguration(
                object_identifiers=[
                    StandardObjectIdentifier(
                        object_name=StdObjectIdentifiers.account,
                    ),
                ],
                file_import_type=FileImportType.SINGLE_FILE,
                object_import_mode=ObjectImportMode.CREATE_ONLY,
                file_dupe_resolution=FileDupeResolution.USE_FIRST,
            ),
        )

        # These entries all exist, expecting every single entry to be a conflict
        processed_conflict_records = await crm_sync_service._process_import_job_file_v2(
            file_binary=csv_content_update.encode("utf-8"),
            csv_import_job=csv_import_job_conflict,
            heartbeat_resume=None,
        )

        for conflict_record in processed_conflict_records:
            assert conflict_record.status == ImportRecordStatus.CONFLICT
        assert len(processed_conflict_records) == 3

        output_file_content = await crm_sync_service._create_output_file_content(
            file_binary=csv_content_update.encode("utf-8"),
            processed_records=processed_conflict_records,
            heartbeat_resume=None,
        )

        # parse the output file content as a csv and verify the results.
        csv_reader = csv.DictReader(io.StringIO(output_file_content))
        rows = list(csv_reader)

        # Verify we have all rows plus header
        assert len(rows) == 3

        # Verify each row has the expected content and error details
        expected_rows = [
            {
                "AccountName": "aaa",
                "AccountDomain": "aaa.com",
                "AccountDesc": "origAaaDesc__UPPPPDATEDDDDDDDDDD",
                "error_details": "Status: CONFLICT - Account already exists, please resolve conflicts.",
            },
            {
                "AccountName": "bbb",
                "AccountDomain": "bbb.com",
                "AccountDesc": "origBbbDesc__UPPPPDATEDDDDDDDDDD",
                "error_details": "Status: CONFLICT - Account already exists, please resolve conflicts.",
            },
            {
                "AccountName": "ccc",
                "AccountDomain": "ccc.com",
                "AccountDesc": "origDddDesc__UPPPPDATEDDDDDDDDDD",
                "error_details": "Status: CONFLICT - Account already exists, please resolve conflicts.",
            },
        ]

        for actual_row, expected_row in zip(rows, expected_rows, strict=False):
            assert actual_row["AccountName"] == expected_row["AccountName"]
            assert actual_row["AccountDomain"] == expected_row["AccountDomain"]
            assert actual_row["AccountDesc"] == expected_row["AccountDesc"]
            assert actual_row["error_details"] == expected_row["error_details"]

    class TestCustomObjectImport:
        async def test_import_custom_object_record_happy_path_v2(  # noqa: C901
            self,
            crm_sync_service: CrmSyncService,
            custom_object_service: CustomObjectService,
        ) -> None:
            """Test importing a custom object record via CSV."""

            test_record_1 = "Test Record 1"
            test_record_2 = "Test Record 2"
            example_csv_content: str = (
                "custom_object_display_name,col1,col2,col3,col4\n"  # Important: "custom_object_display_name" must be exact!
                f"{test_record_1},x,1,2,xyz\n"
                f"{test_record_2},5,4,8,abc\n"
            )

            user_id = uuid.uuid4()
            organization_id = uuid.uuid4()
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            # Create a test custom object first
            custom_object = await custom_object_service.create_standalone_custom_object(
                user_id=user_id,
                organization_id=organization_id,
                object_display_name="Test Import Object",
            )

            number_of_custom_fields = (
                len(example_csv_content.split("\n")[0].split(",")) - 1
            )  # -1 for display name
            custom_fields_map: dict[uuid.UUID, str] = {}
            custom_fields: list[CustomField] = []
            for i in range(number_of_custom_fields):
                custom_field = await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_field_type_property_create=TextFieldProperty(
                        field_display_name=f"col{i + 1}",
                        is_required=False,
                        index_config=CaseAwareUniqueIndexableConfig(is_indexed=True),
                    ),
                )
                custom_fields_map[custom_field.id] = custom_field.field_display_name
                custom_fields.append(custom_field)
            logger.info(f"custom_fields_map: {custom_fields_map}")

            file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="custom_object_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col1",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[0].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col2",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[1].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col3",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[2].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col4",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[3].id)]
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            )

            # Create a job for custom object import
            csv_import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                        # Since we're also importing custom fields for the account, we need to include the custom object
                        CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
            )

            logger.info(f"example_csv_content: {example_csv_content}")

            # Process the import
            await crm_sync_service._process_import_job_file_v2(
                file_binary=example_csv_content.encode(),
                csv_import_job=csv_import_job,
                heartbeat_resume=None,
            )

            # 2025-03-26: Job list will be migrated to new table.  Previous
            #   job is deprecated but still being used.
            # records = await crm_sync_service.import_repository.list_by_job_id(
            #     organization_id=organization_id,
            #     job_id=csv_import_job.id,
            # )
            # logger.info(f"records: {records}")
            # assert len(records) == 2
            # assert records[0].status == ImportRecordStatus.SUCCESS
            # assert records[1].status == ImportRecordStatus.SUCCESS

            # # Verify records were created successfully
            # assert records[0].entity_id is not None
            # assert records[1].entity_id is not None

            logger.info(
                f"TestGet customObjectData record 1: organization_id:({organization_id}) "
                f"custom_object_id:({custom_object.id}) display_name:({test_record_1})"
            )
            co_test_record_1 = (
                await custom_object_service.get_custom_object_data_by_display_name(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    display_name=test_record_1,
                )
            )
            logger.info(
                f"TestGet customObjectData record 2: organization_id:({organization_id}) "
                f"custom_object_id:({custom_object.id}) display_name:({test_record_2})"
            )
            co_test_record_2 = (
                await custom_object_service.get_custom_object_data_by_display_name(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    display_name=test_record_2,
                )
            )

            logger.info(
                f"co_test_record_1:({co_test_record_1.model_dump_json(exclude_none=True)})"
            )
            logger.info(
                f"co_test_record_2:({co_test_record_2.model_dump_json(exclude_none=True)})"
            )
            assert co_test_record_1 is not None
            assert co_test_record_2 is not None

            assert co_test_record_1.custom_object_data.display_name == "Test Record 1"

            # Get the TextFieldValue objects and verify their text values
            for field_id, field_name in custom_fields_map.items():
                match field_name:
                    case "col1":
                        assert co_test_record_1.custom_object_data.value_1 is not None
                        value = co_test_record_1.custom_object_data.value_1.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "x"
                    case "col2":
                        assert co_test_record_1.custom_object_data.value_2 is not None
                        value = co_test_record_1.custom_object_data.value_2.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "1"
                    case "col3":
                        assert co_test_record_1.custom_object_data.value_3 is not None
                        value = co_test_record_1.custom_object_data.value_3.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "2"
                    case "col4":
                        assert co_test_record_1.custom_object_data.value_4 is not None
                        value = co_test_record_1.custom_object_data.value_4.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "xyz"

            assert co_test_record_2.custom_object_data.display_name == "Test Record 2"

            # Get the TextFieldValue objects and verify their text values
            for field_id, field_name in custom_fields_map.items():
                match field_name:
                    case "col1":
                        assert co_test_record_2.custom_object_data.value_1 is not None
                        value = co_test_record_2.custom_object_data.value_1.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "5"
                    case "col2":
                        assert co_test_record_2.custom_object_data.value_2 is not None
                        value = co_test_record_2.custom_object_data.value_2.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "4"
                    case "col3":
                        assert co_test_record_2.custom_object_data.value_3 is not None
                        value = co_test_record_2.custom_object_data.value_3.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "8"
                    case "col4":
                        assert co_test_record_2.custom_object_data.value_4 is not None
                        value = co_test_record_2.custom_object_data.value_4.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "abc"

        async def test_import_custom_object_with_object_import_mode_upsert(  # noqa: C901, PLR0912
            self,
            crm_sync_service: CrmSyncService,
            custom_object_service: CustomObjectService,
        ) -> None:
            """Test importing a custom object record via CSV."""

            test_record_1 = "Test Record 1"
            test_record_2 = "Test Record 2"
            example_csv_content: str = (
                "custom_object_display_name,col1,col2,col3,col4\n"  # Important: "custom_object_display_name" must be exact!
                f"{test_record_1},x,1,2,xyz\n"
                f"{test_record_2},5,4,8,abc\n"
            )

            user_id = uuid.uuid4()
            organization_id = uuid.uuid4()
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            # Create a test custom object first
            custom_object = await custom_object_service.create_standalone_custom_object(
                user_id=user_id,
                organization_id=organization_id,
                object_display_name="Test Import Object",
            )

            number_of_custom_fields = (
                len(example_csv_content.split("\n")[0].split(",")) - 1
            )  # -1 for display name
            custom_fields_map: dict[uuid.UUID, str] = {}
            custom_fields: list[CustomField] = []
            for i in range(number_of_custom_fields):
                custom_field = await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_field_type_property_create=TextFieldProperty(
                        field_display_name=f"col{i + 1}",
                        is_required=False,
                        index_config=CaseAwareUniqueIndexableConfig(is_indexed=True),
                    ),
                )
                custom_fields_map[custom_field.id] = custom_field.field_display_name
                custom_fields.append(custom_field)
            logger.info(f"custom_fields_map: {custom_fields_map}")

            file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="custom_object_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col1",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[0].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col2",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[1].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col3",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[2].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col4",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[3].id)]
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            )

            # Create a job for custom object import
            csv_import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                        # Since we're also importing custom fields for the account, we need to include the custom object
                        CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,  # This is ignored.
                ),
            )

            logger.info(f"example_csv_content: {example_csv_content}")

            # Still Setup Process the import
            await crm_sync_service._process_import_job_file_v2(
                file_binary=example_csv_content.encode(),
                csv_import_job=csv_import_job,
                heartbeat_resume=None,
            )

            # Note, missing col3 purposely
            test_record_3 = "Test Record 3"
            upsert_example_csv_content: str = (
                "custom_object_display_name,col1,col2,col4\n"  # Important: "custom_object_display_name" must be exact!
                f"{test_record_1},k,777,jjjjxyz\n"  #   Change from previous above f"{test_record_1},x,1,2,xyz\n"
                f"{test_record_2},8888,7777,abc\n"  #   Change from previous above f"{test_record_2},5,4,8,abc\n"
                f"{test_record_3},33,333,3rdTestRecord\n"
            )
            logger.info(f"example_csv_content: {upsert_example_csv_content}")

            upsert_file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="custom_object_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col1",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[0].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col2",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[1].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col4",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[3].id)]
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            )

            # Still test setup...
            upsert_csv_import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=upsert_file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                        # Since we're also importing custom fields for the account, we need to include the custom object
                        CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.UPSERT,  # This is The TEST SETUP!!!!
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,  # This is ignored.
                ),
            )

            # The TEST!!!!!
            upsert_import_records = await crm_sync_service._process_import_job_file_v2(
                file_binary=upsert_example_csv_content.encode(),
                csv_import_job=upsert_csv_import_job,
                heartbeat_resume=None,
            )

            assert len(upsert_import_records) == 3
            assert upsert_import_records[0].status == ImportRecordStatus.SUCCESS
            assert upsert_import_records[1].status == ImportRecordStatus.SUCCESS
            assert upsert_import_records[2].status == ImportRecordStatus.SUCCESS

            logger.info(
                f"TestGet customObjectData record 1: organization_id:({organization_id}) "
                f"custom_object_id:({custom_object.id}) display_name:({test_record_1})"
            )
            co_test_record_1 = (
                await custom_object_service.get_custom_object_data_by_display_name(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    display_name=test_record_1,
                )
            )
            logger.info(
                f"TestGet customObjectData record 2: organization_id:({organization_id}) "
                f"custom_object_id:({custom_object.id}) display_name:({test_record_2})"
            )
            co_test_record_2 = (
                await custom_object_service.get_custom_object_data_by_display_name(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    display_name=test_record_2,
                )
            )

            logger.info(
                f"TestGet customObjectData record 2: organization_id:({organization_id}) "
                f"custom_object_id:({custom_object.id}) display_name:({test_record_3})"
            )
            co_test_record_3 = (
                await custom_object_service.get_custom_object_data_by_display_name(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    display_name=test_record_3,
                )
            )

            logger.info(
                f"co_test_record_1:({co_test_record_1}) co_test_record_2:({co_test_record_2})"
            )
            assert co_test_record_1 is not None
            assert co_test_record_2 is not None

            assert co_test_record_1.custom_object_data.display_name == "Test Record 1"
            # Get the TextFieldValue objects and verify their text values
            for field_id, field_name in custom_fields_map.items():
                match field_name:
                    case "col1":
                        assert co_test_record_1.custom_object_data.value_1 is not None
                        value = co_test_record_1.custom_object_data.value_1.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "k"
                    case "col2":
                        assert co_test_record_1.custom_object_data.value_2 is not None
                        value = co_test_record_1.custom_object_data.value_2.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "777"
                    case "col3":
                        assert co_test_record_1.custom_object_data.value_3 is not None
                        value = co_test_record_1.custom_object_data.value_3.value_by_field_id[
                            field_id
                        ]
                        assert (
                            isinstance(value, TextFieldValue) and value.text == "2"
                        )  # remains unchanged
                    case "col4":
                        assert co_test_record_1.custom_object_data.value_4 is not None
                        value = co_test_record_1.custom_object_data.value_4.value_by_field_id[
                            field_id
                        ]
                        assert (
                            isinstance(value, TextFieldValue)
                            and value.text == "jjjjxyz"
                        )

            assert co_test_record_2.custom_object_data.display_name == "Test Record 2"

            # Get the TextFieldValue objects and verify their text values
            for field_id, field_name in custom_fields_map.items():
                match field_name:
                    case "col1":
                        assert co_test_record_2.custom_object_data.value_1 is not None
                        value = co_test_record_2.custom_object_data.value_1.value_by_field_id[
                            field_id
                        ]
                        assert (
                            isinstance(value, TextFieldValue) and value.text == "8888"
                        )
                    case "col2":
                        assert co_test_record_2.custom_object_data.value_2 is not None
                        value = co_test_record_2.custom_object_data.value_2.value_by_field_id[
                            field_id
                        ]
                        assert (
                            isinstance(value, TextFieldValue) and value.text == "7777"
                        )
                    case "col3":
                        assert co_test_record_2.custom_object_data.value_3 is not None
                        value = co_test_record_2.custom_object_data.value_3.value_by_field_id[
                            field_id
                        ]
                        assert (
                            isinstance(value, TextFieldValue) and value.text == "8"
                        )  # remains unchanged
                    case "col4":
                        assert co_test_record_2.custom_object_data.value_4 is not None
                        value = co_test_record_2.custom_object_data.value_4.value_by_field_id[
                            field_id
                        ]
                        assert (
                            isinstance(value, TextFieldValue) and value.text == "abc"
                        )  # remains unchanged

            assert co_test_record_3.custom_object_data.display_name == "Test Record 3"

            # Get the TextFieldValue objects and verify their text values
            #                 f"{test_record_3}, 33, 333, 3rdTestRecord\n"
            for field_id, field_name in custom_fields_map.items():
                match field_name:
                    case "col1":
                        assert co_test_record_3.custom_object_data.value_1 is not None
                        value = co_test_record_3.custom_object_data.value_1.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "33"
                    case "col2":
                        assert co_test_record_3.custom_object_data.value_2 is not None
                        value = co_test_record_3.custom_object_data.value_2.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "333"
                    case "col3":
                        # Note, unspecified column does get returned, and the value is None
                        assert co_test_record_3.custom_object_data.value_3 is None
                    case "col4":
                        assert co_test_record_3.custom_object_data.value_4 is not None
                        value = co_test_record_3.custom_object_data.value_4.value_by_field_id[
                            field_id
                        ]
                        assert (
                            isinstance(value, TextFieldValue)
                            and value.text == "3rdTestRecord"
                        )
            # Bonus test: Ensure using CREATE_ONLY will result in error
            create_only_error_csv_import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=upsert_file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                        # Since we're also importing custom fields for the account, we need to include the custom object
                        CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,  # This should fail for all rows since they exist already
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,  # This is ignored.
                ),
            )

            # Bonus TEST!!!!!
            create_only_error_import_records = (
                await crm_sync_service._process_import_job_file_v2(
                    file_binary=upsert_example_csv_content.encode(),
                    csv_import_job=create_only_error_csv_import_job,
                    heartbeat_resume=None,
                )
            )
            assert len(create_only_error_import_records) == 3
            assert all(
                record.status == ImportRecordStatus.FAILED
                for record in create_only_error_import_records
            )

            # Create a set of expected test records
            test_records = {test_record_1, test_record_2, test_record_3}

            # Verify each record has appropriate error details
            found_records = set()
            for record in create_only_error_import_records:
                assert record.status_detail is not None
                assert "already exists" in record.status_detail
                # Extract the record name from the error message and add to found set
                matching_record = next(
                    (name for name in test_records if name in record.status_detail),
                    None,
                )
                assert matching_record is not None, (
                    f"Could not find any test record name in error detail: {record.status_detail}"
                )
                found_records.add(matching_record)

            # Verify we found all expected records
            assert found_records == test_records

        async def test_import_custom_object_with_account_association(
            self,
            crm_sync_service: CrmSyncService,
            custom_object_service: CustomObjectService,
            association_service: AssociationService,
            domain_object_query_service: DomainObjectQueryService,
            make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
        ) -> None:
            """Test importing a custom object record with account association via CSV."""

            test_association_active = "ACTIVE"
            test_association_past_active = "PAST-ACTIVE"
            test_association_future = "FUTURE"
            test_associations = [
                test_association_active,
                test_association_past_active,
                test_association_future,
            ]
            # Create test data with one account having multiple products
            test_data = [
                (
                    "Test Account 1",
                    "testaccount1.com",
                    "Test Product A",
                    test_association_active,
                ),
                (
                    "Test Account 1",
                    "testaccount1.com",
                    "Test Product B",
                    test_association_past_active,
                ),
                (
                    "Test Account 2",
                    "testaccount2.com",
                    "Test Product C",
                    test_association_future,
                ),
            ]

            # Create CSV content with header and data rows
            csv_rows = [
                "company_display_name,company_domain_name,custom_object_display_name,product"
            ]
            csv_rows.extend([",".join(row) for row in test_data])
            example_csv_content = "\n".join(csv_rows)

            # association_record has an foreign key to org, need real org
            user_id, organization_id = await make_user_org()

            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            # Create a test custom object first
            custom_object = await custom_object_service.create_standalone_custom_object(
                user_id=user_id,
                organization_id=organization_id,
                object_display_name="Test Import Object",
            )

            # Create an associations between custom object and account
            associations: list[CustomObjectAssociation] = []
            for product_association in test_associations:
                association = await association_service.create_association(
                    organization_id=organization_id,
                    user_id=user_id,
                    source_object_identifier=StandardObjectIdentifier(
                        object_name=StdObjectIdentifiers.account,
                    ),
                    target_object_identifier=CustomObjectIdentifier(
                        organization_id=organization_id,
                        object_id=custom_object.id,
                    ),
                    relationship_type=RelationshipType.LOOKUP,
                    association_name=product_association,
                    inverse_name=f"Company For {product_association}",
                    max_source_records=-1,
                    max_target_records=-1,
                )
                assert association.association_name == product_association
                associations.append(association)

            # Create import job configuration
            file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    # Account mapping
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="company_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["domain_name"]
                                ),
                            ),
                        ],
                    ),
                    # Custom object mapping
                    ObjectMapping(
                        object_identifier=CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="custom_object_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["custom_object_display_name"]
                                ),
                            ),
                        ],
                    ),
                ],
                association_label_mapping=[
                    AssociationLabelMapping(
                        relationship_id=str(associations[0].id),
                        column_mapping={
                            "product": [associations[0].association_name],
                        },
                    ),
                    AssociationLabelMapping(
                        relationship_id=str(associations[1].id),
                        column_mapping={
                            "product": [associations[1].association_name],
                        },
                    ),
                    AssociationLabelMapping(
                        relationship_id=str(associations[2].id),
                        column_mapping={
                            "product": [associations[2].association_name],
                        },
                    ),
                    # Throw in a couple standard relationships.  These should be ignored by
                    #   backend as they are handled in the normal course of object creation.
                    AssociationLabelMapping(
                        # Expected to be ignored
                        relationship_id="contact_account_role__to__contact",
                        column_mapping={},
                    ),
                    AssociationLabelMapping(
                        # Expected to be ignored
                        relationship_id="contact_account_role__to__account",
                        column_mapping={},
                    ),
                ],
            )

            csv_import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                        CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
            )

            # Process the import
            await crm_sync_service._process_import_job_file_v2(
                file_binary=example_csv_content.encode(),
                csv_import_job=csv_import_job,
                heartbeat_resume=None,
            )

            # Get all accounts once
            accounts = await domain_object_query_service.list_account_records(
                organization_id=organization_id,
                include_custom_object=False,
            )

            # Verify accounts were created correctly (should only be 2 unique accounts)
            unique_accounts = {
                domain_name: account_name
                for account_name, domain_name, _, _ in test_data
            }
            for domain_name, account_name in unique_accounts.items():
                account = next(
                    (a for a in accounts if a.data.domain_name == domain_name),
                    None,
                )
                assert account is not None
                assert account.data.display_name == account_name

            # Verify all products and their associations were created
            total_associations = 0
            for _, domain_name, product_name, test_data_association_name in test_data:
                account = next(
                    (a for a in accounts if a.data.domain_name == domain_name),
                    None,
                )
                assert account is not None

                # Verify custom object (product) was created
                custom_object_data = (
                    await custom_object_service.get_custom_object_data_by_display_name(
                        organization_id=organization_id,
                        custom_object_id=custom_object.id,
                        display_name=product_name,
                    )
                )
                assert custom_object_data is not None
                assert (
                    custom_object_data.custom_object_data.display_name == product_name
                )

                num_associations_handled = 0
                for my_association in associations:
                    if my_association.association_name != test_data_association_name:
                        continue

                    association_records = await association_service.get_records(
                        association=my_association
                    )
                    assert len(association_records) == 1
                    num_associations_handled += 1
                    association_record = association_records[0]
                    assert association_record.source_record_id == account.data.id
                    assert (
                        association_record.target_record_id
                        == custom_object_data.custom_object_data.id
                    )

                assert num_associations_handled == 1
                total_associations += 1

            assert total_associations == 3

    class TestOpportunityImport:
        """Need Account and Contact objects to test Opportunity import."""

        async def test_import_opportunity_record_3_simple_csv_std_obj_import(
            self,
            crm_sync_service: CrmSyncService,
            custom_object_service: CustomObjectService,
            association_service: AssociationService,
            domain_object_query_service: DomainObjectQueryService,
            make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
        ) -> None:
            """Test importing opportunities with related accounts and contacts."""
            # Setup test environment
            user_id, organization_id = await make_user_org()
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            # Step 1: Import Accounts
            account_csv_path = (
                Path(__file__).parent
                / "data"
                / "actual_conversion_coach_hubspot_accounts_just3.csv"
            )
            with open(account_csv_path, "rb") as f:  # noqa: ASYNC230
                account_csv_content = f.read()

            account_file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="display_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="official_website_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_official_website"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="linkedin_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_linkedin_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="category_list",
                                qualified_field=QualifiedImportField(
                                    path=["company_industry"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="technology_list",
                                qualified_field=QualifiedImportField(
                                    path=["company_technologies"]
                                ),
                            ),
                        ],
                    ),
                ],
                association_label_mapping=[],
            )

            account_import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=account_file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
            )
            await crm_sync_service._process_import_job_file_v2(
                file_binary=account_csv_content,
                csv_import_job=account_import_job,
                heartbeat_resume=None,
            )

            # Step 2: Import Contacts
            contact_csv_path = (
                Path(__file__).parent
                / "data"
                / "actual_conversion_coach_hubspot_contacts_just3.csv"
            )
            with open(contact_csv_path, "rb") as f:  # noqa: ASYNC230
                contact_csv_content = f.read()

            contact_file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="first_name",
                                qualified_field=QualifiedImportField(
                                    path=["contact_first_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="last_name",
                                qualified_field=QualifiedImportField(
                                    path=["contact_last_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="primary_email",
                                qualified_field=QualifiedImportField(
                                    path=["contact_primary_email"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="contact_job_title",
                                qualified_field=QualifiedImportField(
                                    path=["contact_job_title"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="linkedin_url",
                                qualified_field=QualifiedImportField(
                                    path=["contact_linkedin_url"]
                                ),
                            ),
                        ],
                    ),
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="company_domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),
                        ],
                    ),
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact_account_role
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="contact_job_title",
                                qualified_field=QualifiedImportField(
                                    path=["contact_job_title"]
                                ),
                            ),
                        ],
                    ),
                ],
                association_label_mapping=[
                    AssociationLabelMapping(
                        relationship_id="contact_account_role__to__contact",
                        column_mapping={},
                    ),
                    AssociationLabelMapping(
                        relationship_id="contact_account_role__to__account",
                        column_mapping={},
                    ),
                ],
            )

            contact_import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=contact_file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact,
                        ),
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact_account_role,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
            )
            await crm_sync_service._process_import_job_file_v2(
                file_binary=contact_csv_content,
                csv_import_job=contact_import_job,
                heartbeat_resume=None,
            )

            # Step 3: Import Opportunities
            opportunity_csv_path = (
                Path(__file__).parent
                / "data"
                / "actual_conversion_coach_hubspot_opportunities_just3.csv"
            )
            with open(opportunity_csv_path, "rb") as f:  # noqa: ASYNC230
                opportunity_csv_content = f.read()

            opportunity_file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),
                        ],
                    ),
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.pipeline
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="opportunity_name",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="amount",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_deal_amount"]
                                ),
                            ),
                        ],
                    ),
                ],
                association_label_mapping=[],
            )

            opportunity_import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=opportunity_file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.pipeline,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
            )
            await crm_sync_service._process_import_job_file_v2(
                file_binary=opportunity_csv_content,
                csv_import_job=opportunity_import_job,
                heartbeat_resume=None,
            )

            # Step 4: Verify the imports
            # Verify accounts were created
            accounts = await domain_object_query_service.list_account_records(
                organization_id=organization_id,
                include_custom_object=False,
            )
            assert len(accounts) == 3  # Built In, Neo4j, Muscogee Elementary

            # Verify contacts were created and associated with accounts
            contacts = await domain_object_query_service.list_contact_records(
                organization_id=organization_id,
                include_custom_object=False,
            )
            assert len(contacts) == 8  # Total number of contacts in the CSV

            # Verify opportunities were created and associated with accounts
            opportunities = await domain_object_query_service.list_pipeline_records(
                organization_id=organization_id,
                include_custom_object=False,
            )
            assert len(opportunities) == 3  # Three opportunities from CSV

            # Verify specific opportunity details
            opportunity_by_name = {opp.data.display_name: opp for opp in opportunities}

            # Check the opportunities
            muscogee_opportunity = opportunity_by_name["OneTestOpportunityMuscogee"]
            assert muscogee_opportunity.data.amount == ********
            muscogee_account = next(
                acc for acc in accounts if acc.data.domain_name == "muscogee.k12.ga.us"
            )
            assert muscogee_opportunity.data.account_id == muscogee_account.data.id

            builtin_opportunity = opportunity_by_name["TwoTestOpportunityBuiltin"]
            assert builtin_opportunity.data.amount == ********
            builtin_account = next(
                acc for acc in accounts if acc.data.domain_name == "builtin.com"
            )
            assert builtin_opportunity.data.account_id == builtin_account.data.id

            neo4j_opportunity = opportunity_by_name["ThreeTestOpportunityNeo4j"]
            assert neo4j_opportunity.data.amount == ********
            neo4j_account = next(
                acc for acc in accounts if acc.data.domain_name == "neo4j.com"
            )
            assert neo4j_opportunity.data.account_id == neo4j_account.data.id

    class TestLargerComboImports:
        def _get_custom_field_str_value(
            self, custom_object_data: CustomObjectData, custom_field: CustomField
        ) -> str:
            match custom_field.slot_number:
                case 1:
                    assert custom_object_data.value_1 is not None
                    value1 = custom_object_data.value_1.value_by_field_id[
                        custom_field.id
                    ]
                    if isinstance(value1, (TextFieldValue, TextAreaFieldValue)):
                        return str(value1.to_generic_value())
                    raise ValueError(f"Invalid value type: {type(value1)}")
                case 2:
                    assert custom_object_data.value_2 is not None
                    value2 = custom_object_data.value_2.value_by_field_id[
                        custom_field.id
                    ]
                    if isinstance(value2, (TextFieldValue, TextAreaFieldValue)):
                        return str(value2.to_generic_value())
                    raise ValueError(f"Invalid value type: {type(value2)}")
                case 3:
                    assert custom_object_data.value_3 is not None
                    value3 = custom_object_data.value_3.value_by_field_id[
                        custom_field.id
                    ]
                    if isinstance(value3, (TextFieldValue, TextAreaFieldValue)):
                        return str(value3.to_generic_value())
                    raise ValueError(f"Invalid value type: {type(value3)}")
                case _:
                    raise ValueError(f"Invalid slot number: {custom_field.slot_number}")

        async def test_import_account_with_custom_fields_and_a_few_associated_contacts(
            self,
            crm_sync_service: CrmSyncService,
            custom_object_service: CustomObjectService,
            domain_object_query_service: DomainObjectQueryService,
            contact_query_service: ContactQueryService,
            make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
        ) -> None:
            user_id, organization_id = await make_user_org()
            # Get the custom object dto for Account extension
            account_custom_object_dto = await crm_sync_service.custom_object_service.enable_extension_custom_object(
                user_id=user_id,
                organization_id=organization_id,
                objects_to_enable=[ExtendableStandardObject.account],
            )
            # Retrieve the specific CustomObject ID for Account
            account_cobj = account_custom_object_dto.get(
                ExtendableStandardObject.account
            )
            assert account_cobj is not None
            account_cobj_id = account_cobj.id

            # Create a text custom field
            account_custom_text_field_state_placeholder = (
                await crm_sync_service.custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=account_cobj_id,
                    custom_field_type_property_create=TextFieldProperty(
                        field_display_name="state_placeholder",
                        field_description="state placeholder",
                        is_required=False,
                    ),
                )
            )

            account_custom_text_area_field_address_placeholder = (
                await crm_sync_service.custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=account_cobj_id,
                    custom_field_type_property_create=TextAreaFieldProperty(
                        field_display_name="address_placeholder",
                        field_description="address placeholder",
                        is_required=False,
                    ),
                )
            )

            account_custom_text_field_city_placeholder = (
                await crm_sync_service.custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=account_cobj_id,
                    custom_field_type_property_create=TextFieldProperty(
                        field_display_name="city_placeholder",
                        field_description="city placeholder",
                        is_required=False,
                    ),
                )
            )

            # This was taken from the actual request from the FE
            account_file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="display_name",
                                qualified_field=QualifiedImportField(
                                    path=["display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="owner_email",
                                qualified_field=QualifiedImportField(
                                    path=["owner_user_id"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["domain_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="official_website_url",
                                qualified_field=QualifiedImportField(
                                    path=["official_website"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="estimated_annual_revenue",
                                qualified_field=QualifiedImportField(
                                    path=["estimated_annual_revenue"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="estimated_employee_count",
                                qualified_field=QualifiedImportField(
                                    path=["estimated_employee_count"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="technology_list",
                                qualified_field=QualifiedImportField(
                                    path=["technology_list"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="linkedin_url",
                                qualified_field=QualifiedImportField(
                                    path=["linkedin_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="facebook_url",
                                qualified_field=QualifiedImportField(
                                    path=["facebook_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="x_url",
                                qualified_field=QualifiedImportField(path=["x_url"]),
                            ),
                            ColumnMapping(
                                column_name="address_placeholder",
                                qualified_field=QualifiedImportField(
                                    path=[
                                        str(
                                            account_custom_text_area_field_address_placeholder.id
                                        )
                                    ]
                                ),
                            ),
                            ColumnMapping(
                                column_name="city_placeholder",
                                qualified_field=QualifiedImportField(
                                    path=[
                                        str(
                                            account_custom_text_field_city_placeholder.id
                                        )
                                    ]
                                ),
                            ),
                            ColumnMapping(
                                column_name="state_placeholder",
                                qualified_field=QualifiedImportField(
                                    path=[
                                        str(
                                            account_custom_text_field_state_placeholder.id
                                        )
                                    ]
                                ),
                            ),
                            ColumnMapping(
                                column_name="category_list",
                                qualified_field=QualifiedImportField(
                                    path=["category_list"]
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            )

            csv_account_import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=account_file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.UPSERT,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                    timezone=TimeZoneName("America/Los_Angeles"),
                ),
            )

            account_csv_path = (
                Path(__file__).parent / "data" / "quotekong_single_account.csv"
            )
            with open(account_csv_path, "rb") as f:  # noqa: ASYNC230
                account_csv_content = f.read()

            await crm_sync_service._process_import_job_file_v2(
                file_binary=account_csv_content,
                csv_import_job=csv_account_import_job,
                heartbeat_resume=None,
            )
            # verify the import.

            # verify the import
            accounts = await domain_object_query_service.list_account_records(
                organization_id=organization_id,
                include_custom_object=False,
            )
            assert len(accounts) == 1
            account_record = accounts[0]
            assert account_record is not None
            account = account_record.data
            self._verify_vectorinstituteai_account(account)

            # Verify custom fields
            account_extension = (
                await custom_object_service.get_custom_object_data_by_extension_id(
                    organization_id=organization_id,
                    parent_object_name=ExtendableStandardObject.account,
                    custom_object_data_extension_id=account.id,
                )
            )
            assert account_extension is not None

            # Check address placeholder
            address_value = self._get_custom_field_str_value(
                account_extension.custom_object_data,
                account_custom_text_area_field_address_placeholder,
            )
            assert address_value == "108 College St, Toronto, Ontario, Canada, M5G"

            # Check city placeholder
            city_value = self._get_custom_field_str_value(
                account_extension.custom_object_data,
                account_custom_text_field_city_placeholder,
            )
            assert city_value == "Toronto"

            # Check state placeholder
            state_value = self._get_custom_field_str_value(
                account_extension.custom_object_data,
                account_custom_text_field_state_placeholder,
            )
            assert state_value == "Ontario"

            # Now that the account is created, try and create a few contacts associated with this account.
            contact_file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="first_name",
                                qualified_field=QualifiedImportField(
                                    path=["first_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="last_name",
                                qualified_field=QualifiedImportField(
                                    path=["last_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="primary_email",
                                qualified_field=QualifiedImportField(
                                    path=["primary_email"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="stage_list_value",
                                qualified_field=QualifiedImportField(path=["stage"]),
                            ),
                            ColumnMapping(
                                column_name="primary_phone_number",
                                qualified_field=QualifiedImportField(
                                    path=["primary_phone_number"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="linkedin_url",
                                qualified_field=QualifiedImportField(
                                    path=["linkedin_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="owner_email",
                                qualified_field=QualifiedImportField(
                                    path=["owner_user_id"]
                                ),
                            ),
                        ],
                    ),
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="company_name",
                                qualified_field=QualifiedImportField(
                                    path=["display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["domain_name"]
                                ),
                            ),
                        ],
                    ),
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact_account_role
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="contact_job_title",
                                qualified_field=QualifiedImportField(path=["title"]),
                            ),
                        ],
                    ),
                ],
                association_label_mapping=[
                    AssociationLabelMapping(
                        relationship_id="contact_account_role__to__contact",
                        column_mapping={},
                    ),
                    AssociationLabelMapping(
                        relationship_id="contact_account_role__to__account",
                        column_mapping={},
                    ),
                ],
            )

            contact_csv_path = (
                Path(__file__).parent
                / "data"
                / "quotekong_a_few_associated_contacts.csv"
            )
            with open(contact_csv_path, "rb") as f:  # noqa: ASYNC230
                contact_csv_content = f.read()

            csv_contact_import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=contact_file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact,
                        ),
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact_account_role,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.UPSERT,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                    timezone=TimeZoneName("America/Los_Angeles"),
                ),
            )

            await crm_sync_service._process_import_job_file_v2(
                file_binary=contact_csv_content,
                csv_import_job=csv_contact_import_job,
                heartbeat_resume=None,
            )
            # verify the import
            contacts = await domain_object_query_service.list_contact_records(
                organization_id=organization_id,
                include_custom_object=False,
            )
            assert len(contacts) == 3

            # Sort contacts by email to ensure consistent order for verification
            contacts_by_email = {
                contact.data.primary_email: contact for contact in contacts
            }

            # Verify Flora Wan's details
            flora = contacts_by_email["<EMAIL>"]
            assert flora.data.first_name == "Flora"
            assert flora.data.last_name == "Wan"
            assert flora.data.display_name == "Flora Wan"
            assert flora.data.stage.display_value == "Reached Out"
            assert flora.data.primary_phone_number is None
            assert flora.data.linkedin_url is None

            # Verify Bob Zhou's details
            bob = contacts_by_email["<EMAIL>"]
            assert bob.data.first_name == "Bob"
            assert bob.data.last_name == "Zhou"
            assert bob.data.display_name == "Bob Zhou"
            assert bob.data.stage.display_value == "Reached Out"
            assert (
                bob.data.primary_phone_number == "+16472673621"
            )  # E164 format of '(*************'
            assert (
                bob.data.linkedin_url == "http://www.linkedin.com/in/bob-zhou-59b87b29a"
            )

            # Verify generic fastlane contact
            fastlane = contacts_by_email["<EMAIL>"]
            # Case where display_name, first_name, and last_name are all None.  first_name becomes primary_email
            # https://github.com/ReevoAI/salestech-be/blob/62b4a423a4087022c58cb8db09e45a4dae9f048c/salestech_be/core/imports/service/crm_sync_service.py#L6004-L6006
            assert fastlane.data.first_name == "<EMAIL>"
            assert fastlane.data.last_name is None
            assert fastlane.data.stage.display_value == "Reached Out"
            assert fastlane.data.primary_phone_number is None
            assert fastlane.data.linkedin_url is None

            # Verify all contacts are associated with Vector Institute
            for contact in contacts:
                contact_account_associations = await contact_query_service.list_active_contact_associations_for_contact(
                    organization_id=organization_id,
                    contact_id=contact.data.id,
                )
                # Should have exactly one association to Vector Institute
                assert len(contact_account_associations) == 1
                assert contact_account_associations[0].account_id == account.id

            # verify accounts again, make sure no account fields have changed after contact import.
            again_accounts = await domain_object_query_service.list_account_records(
                organization_id=organization_id,
                include_custom_object=False,
            )
            assert len(again_accounts) == 1
            again_account_record = again_accounts[0]
            assert again_account_record is not None
            again_account = again_account_record.data
            self._verify_vectorinstituteai_account(again_account)

        def _verify_vectorinstituteai_account(self, account: AccountV2) -> None:
            assert account.display_name == "Vector Institute"
            assert account.domain_name == "vectorinstitute.ai"
            assert account.official_website == "http://www.vectorinstitute.ai"
            assert account.estimated_employee_count == 540
            assert (
                account.linkedin_url
                == "http://www.linkedin.com/company/vector-institute"
            )
            assert account.x_url == "https://twitter.com/@VectorInst"
            assert account.technology_list is not None
            assert set(account.technology_list) == {
                "Salesforce",
                "Route 53",
                "Sendgrid",
                "Gmail",
                "Google Apps",
                "Microsoft Office 365",
                "CloudFlare Hosting",
                "Lever",
                "WordPress.org",
                "DoubleClick Conversion",
                "DoubleClick",
                "Ruby On Rails",
                "reCAPTCHA",
                "Mixpanel",
                "Google Analytics Ecommerce Tracking",
                "Gravity Forms",
                "Google Analytics",
                "YouTube",
                "Vimeo",
                "Mobile Friendly",
                "Google Tag Manager",
                "Google Dynamic Remarketing",
                "Remote",
                "AI",
            }
            assert account.category_list is not None
            assert set(account.category_list) == {"research"}

    async def test_import_contact_with_associated_account_but_no_account_identifier(
        self,
        crm_sync_service: CrmSyncService,
        domain_object_query_service: DomainObjectQueryService,
        make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
    ) -> None:
        # Setup test environment
        user_id, organization_id = await make_user_org()
        await _setup_import_repository_clean_slate_from_org_id(
            crm_sync_service=crm_sync_service,
            organization_id=organization_id,
        )

        # Prepare CSV data with contact info but empty account fields
        csv_content = (
            "first_name,last_name,contact_job_title,company_name,company_domain_name,primary_email,stage_list_value\n"
            "John,Doe,Software Engineer,,,<EMAIL>,Reached Out"
        )

        # Define file metadata with mappings for both contact and account
        file_metadata = FileMetadata(
            file_id=uuid4(),
            object_mappings=[
                ObjectMapping(
                    object_identifier=StandardObjectIdentifier(
                        object_name=StdObjectIdentifiers.contact
                    ),
                    column_mappings=[
                        ColumnMapping(
                            column_name="first_name",
                            qualified_field=QualifiedImportField(path=["first_name"]),
                        ),
                        ColumnMapping(
                            column_name="last_name",
                            qualified_field=QualifiedImportField(path=["last_name"]),
                        ),
                        ColumnMapping(
                            column_name="primary_email",
                            qualified_field=QualifiedImportField(
                                path=["primary_email"]
                            ),
                        ),
                        ColumnMapping(
                            column_name="stage_list_value",
                            qualified_field=QualifiedImportField(path=["stage"]),
                        ),
                    ],
                ),
                ObjectMapping(
                    object_identifier=StandardObjectIdentifier(
                        object_name=StdObjectIdentifiers.account
                    ),
                    column_mappings=[
                        ColumnMapping(
                            column_name="company_name",
                            qualified_field=QualifiedImportField(path=["display_name"]),
                        ),
                        ColumnMapping(
                            column_name="company_domain_name",
                            qualified_field=QualifiedImportField(path=["domain_name"]),
                        ),
                    ],
                ),
                ObjectMapping(
                    object_identifier=StandardObjectIdentifier(
                        object_name=StdObjectIdentifiers.contact_account_role
                    ),
                    column_mappings=[
                        ColumnMapping(
                            column_name="contact_job_title",
                            qualified_field=QualifiedImportField(path=["title"]),
                        ),
                    ],
                ),
            ],
            association_label_mapping=[
                AssociationLabelMapping(
                    relationship_id="contact_account_role__to__contact",
                    column_mapping={},
                ),
                AssociationLabelMapping(
                    relationship_id="contact_account_role__to__account",
                    column_mapping={},
                ),
            ],
        )

        # Create import job
        import_job = ImportJob(
            id=uuid4(),
            organization_id=organization_id,
            created_by_user_id=user_id,
            created_at=datetime.now(pytz.UTC),
            updated_at=datetime.now(pytz.UTC),
            metadata=file_metadata,
            configuration=ImportConfiguration(
                object_identifiers=[
                    StandardObjectIdentifier(
                        object_name=StdObjectIdentifiers.contact,
                    ),
                    StandardObjectIdentifier(
                        object_name=StdObjectIdentifiers.account,
                    ),
                    StandardObjectIdentifier(
                        object_name=StdObjectIdentifiers.contact_account_role,
                    ),
                ],
                file_import_type=FileImportType.SINGLE_FILE,
                object_import_mode=ObjectImportMode.CREATE_ONLY,
                file_dupe_resolution=FileDupeResolution.USE_FIRST,
                timezone=TimeZoneName("UTC"),
            ),
        )

        # Process the import
        import_records = await crm_sync_service._process_import_job_file_v2(
            file_binary=csv_content.encode("utf-8"),
            csv_import_job=import_job,
            heartbeat_resume=None,
        )

        # Sanity Verify contact was created with correct data
        contacts = await domain_object_query_service.list_contact_records(
            organization_id=organization_id,
            include_custom_object=False,
        )
        assert len(contacts) == 1
        contact = contacts[0].data
        assert contact.first_name == "John"
        assert contact.last_name == "Doe"
        assert contact.display_name == "John Doe"
        assert contact.primary_email == "<EMAIL>"
        assert contact.stage.display_value == "Reached Out"

        # Sanity Verify no accounts were created
        accounts = await domain_object_query_service.list_account_records(
            organization_id=organization_id,
            include_custom_object=False,
        )
        assert len(accounts) == 0

        # Verify import recordresults
        assert len(import_records) == 3  # One for contact and one skipped for account

        contact_import_record: ImportRecord | None = None
        account_import_record: ImportRecord | None = None
        contact_account_role_import_record: ImportRecord | None = None
        for import_record in import_records:
            match import_record.import_entity_type:
                case ImportEntityType.CONTACT:
                    contact_import_record = import_record
                case ImportEntityType.ACCOUNT:
                    account_import_record = import_record
                case ImportEntityType.CONTACT_ACCOUNT_ROLE:
                    contact_account_role_import_record = import_record
                case _:
                    raise AssertionError(
                        f"Unexpected import entity type: {import_record.import_entity_type}"
                    )

        # Verify contact import record
        assert contact_import_record is not None
        logger.info(f"contact_import_record: {contact_import_record.status_detail}")
        assert contact_import_record.status == ImportRecordStatus.SUCCESS
        assert contact_import_record.entity_id == contact.id

        # Verify account record (should be skipped)
        assert account_import_record is not None
        logger.info(f"account_import_record: {account_import_record.status_detail}")
        assert account_import_record.status == ImportRecordStatus.SKIPPED
        assert account_import_record.status_detail is not None
        assert (
            account_import_record.entity_id is None
        )  # Since account is processed first, no way to have contact_id.

        # Verify contact account role record (should be skipped)
        assert contact_account_role_import_record is not None
        logger.info(
            f"contact_account_role_import_record: {contact_account_role_import_record.status_detail}"
        )
        assert contact_account_role_import_record.status == ImportRecordStatus.SKIPPED
        assert contact_account_role_import_record.status_detail is not None
        # Code does its best to uniquely identify issues
        assert contact_account_role_import_record.entity_id == contact.id
