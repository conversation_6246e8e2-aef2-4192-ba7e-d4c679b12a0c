import uuid
from collections.abc import Awaitable, Callable
from datetime import datetime
from pathlib import Path

import pytest
import pytz

from salestech_be.common.type.metadata.common import StandardObjectIdentifier
from salestech_be.core.imports.models.import_job import (
    ColumnMapping,
    FileDupeResolution,
    FileImportType,
    FileMetadata,
    ImportConfiguration,
    ImportCsvJobStatus,
    ImportJob,
    ObjectImportMode,
    ObjectMapping,
    QualifiedImportField,
)
from salestech_be.core.imports.service.crm_sync_service import (
    CrmSyncService,
    get_crm_sync_service_from_db_engine,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.import_record import ImportRecordStatus
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


# For convenience to run this test:
#   uv run pytest -v -s tests/integration/core/crm_sync/service/test_csv_import_creates_objects.py


@pytest.fixture
def crm_sync_service(_engine: DatabaseEngine) -> CrmSyncService:
    return get_crm_sync_service_from_db_engine(_engine)


# Also see test_crm_sync_service.py:TestAccountImport:test_import_account_record_happy_path
#   for additional basic tests.  This is to focus more on csv edge cases that need to be resolved.
class TestCsvImportCreatesObjects:
    """Test that CSV import creates objects in the database."""

    async def test_csv_import_creates_account_objects(
        self,
        crm_sync_service: CrmSyncService,
        make_user_org: Callable[
            [], Awaitable[tuple[uuid.UUID, uuid.UUID]]
        ],  # Changed from common_api_client
    ) -> None:
        """Test that CSV import creates account objects in the database."""
        # Unpack user_id and org_id from the tuple
        user_id, organization_id = await make_user_org()

        # Read the CSV file
        csv_file_path = (
            Path(__file__).parent / "data" / "actual_accounts_test.partial.csv"
        )
        with open(csv_file_path, "rb") as f:  # noqa: ASYNC230
            csv_content = f.read()

        # Create an ImportJob for account import
        import_job_id = uuid.uuid4()
        csv_import_job = ImportJob(
            id=import_job_id,
            organization_id=organization_id,
            created_by_user_id=user_id,
            display_name="Test Account CSV Import",
            status=ImportCsvJobStatus.STARTED,
            created_at=datetime.now(pytz.UTC),
            updated_at=datetime.now(pytz.UTC),
            metadata=FileMetadata(
                file_id=uuid.uuid4(),
                original_filename="actual_accounts_test.partial.csv",
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name="account"
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="display_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_display_name"],
                                ),
                            ),
                            ColumnMapping(
                                column_name="domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"],
                                ),
                            ),
                            ColumnMapping(
                                column_name="official_website_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_official_website"],
                                ),
                            ),
                            ColumnMapping(
                                column_name="description",
                                qualified_field=QualifiedImportField(
                                    path=["company_description"],
                                ),
                            ),
                            ColumnMapping(
                                column_name="industry",
                                qualified_field=QualifiedImportField(
                                    path=["company_industry"],
                                ),
                            ),
                            ColumnMapping(
                                column_name="linkedin_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_linkedin_url"],
                                ),
                            ),
                            ColumnMapping(
                                column_name="facebook_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_facebook_url"],
                                ),
                            ),
                            ColumnMapping(
                                column_name="x_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_x_url"],
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            ),
            configuration=ImportConfiguration(
                object_identifiers=[StandardObjectIdentifier(object_name="account")],
                file_import_type=FileImportType.SINGLE_FILE,
                object_import_mode=ObjectImportMode.UPSERT,
                file_dupe_resolution=FileDupeResolution.USE_FIRST,
            ),
            workflow_id=None,
            completed_at=None,
            result_metadata=None,
            object_identifier=None,
        )
        # await crm_sync_service.import_job_repository.create_import_job(csv_import_job)

        # Process CSV data to get row_num_to_csv_row_map
        row_num_to_csv_row_map = await crm_sync_service._process_csv_data_v2(
            file_binary=csv_content,
            csv_import_job=csv_import_job,
            heartbeat_resume=None,
        )
        assert row_num_to_csv_row_map is not None

        # Call the _process_import_rows_v2 method
        processed_records = await crm_sync_service._process_import_rows_v2(
            csv_import_job=csv_import_job,
            row_num_to_csv_row_map=row_num_to_csv_row_map,
            heartbeat_resume=None,
        )

        # Check that the import produced records
        assert len(processed_records) > 0, "No records were processed"

        # Get the import records from the database
        # Note: _process_import_rows_v2 now directly returns ImportRecord objects,
        # but these might not be the final persisted state if further updates happen.
        # It's safer to fetch from DB for final state verification.
        import_records = await crm_sync_service.import_repository.list_by_job_id(
            organization_id=organization_id,
            job_id=csv_import_job.id,
        )

        # Check that we have import records
        assert len(import_records) > 0, "No import records were created"

        # Check that the import records were successful
        successful_records = [
            r for r in import_records if r.status == ImportRecordStatus.SUCCESS
        ]
        assert len(successful_records) > 0, "No successful import records"

        # Verify that accounts were created
        assert len(successful_records) == 4
        has_acct_build_robotics = False
        has_acct_black_veatch = False
        has_acct_uc_berkeley = False
        has_acct_crazy_commas = False
        for record in successful_records:
            if record.entity_id:
                account = await crm_sync_service.account_service.get_account_v2(
                    account_id=record.entity_id,
                    organization_id=organization_id,
                )
                # Verify account properties based on the CSV data
                if account.display_name == "Built Robotics":
                    has_acct_build_robotics = True
                    assert account.domain_name == "builtrobotics.com"
                    assert (
                        account.official_website
                        == "https://www.builtrobotics.com/about/company"
                    )
                    assert account.description is not None
                    assert "AI-Powered Tools" in account.description
                    assert account.technology_list is None
                    assert account.category_list is not None
                    assert (
                        "mechanical or industrial engineering" in account.category_list
                    )
                    # Assert social media URLs for Built Robotics
                    assert (
                        account.linkedin_url
                        == "http://www.linkedin.com/company/builtrobotics"
                    )
                    assert account.facebook_url is None
                    assert account.x_url == "https://twitter.com/builtrobotics"
                elif account.display_name == "Black & Veatch":
                    has_acct_black_veatch = True
                    assert account.domain_name == "bv.com"
                    assert account.official_website == "https://www.bv.com/"
                    assert account.description is not None
                    assert "engineering, procurement" in account.description
                    assert account.technology_list is None
                    assert account.category_list is not None
                    assert "information technology & services" in account.category_list
                    # Assert social media URLs for Black & Veatch
                    assert (
                        account.linkedin_url
                        == "http://www.linkedin.com/company/black-and-veatch"
                    )
                    assert (
                        account.facebook_url == "https://www.facebook.com/BlackVeatch/"
                    )
                    assert account.x_url == "https://twitter.com/Black_Veatch"
                elif account.display_name == "UC Berkeley School of Law":
                    has_acct_uc_berkeley = True
                    assert account.domain_name == "law.berkeley.edu"
                    assert account.official_website == "www.law.berkeley.edu"
                    assert account.description is None
                    assert account.technology_list is None
                    assert account.category_list is None
                    # Assert UC Berkeley has no social media URLs since they're empty in the CSV
                    assert account.linkedin_url is None
                    assert account.facebook_url is None
                    assert account.x_url is None
                    # Assert no description and industry
                    assert account.description is None
                    assert account.technology_list is None
                elif account.display_name == "cRaZy wRoNg tooManyCommas":
                    # This line used to be an error, but with the new CSV import style, it is still OK
                    #    Due to the explicit column mapping.
                    has_acct_crazy_commas = True
                    assert account.domain_name == "crazywrong.com"
                    assert (
                        account.official_website == "https://crazywrong.fake.com/about"
                    )
                    assert (
                        account.description
                        == "There are way too many commas in this field."
                    )
                    assert account.technology_list is None
                    assert account.category_list is None
                    assert account.linkedin_url is None
                    assert account.facebook_url is None
                else:
                    raise ValueError(f"Unexpected account: {account.display_name}")

        assert has_acct_build_robotics, "Built Robotics account was not created"
        assert has_acct_black_veatch, "Black & Veatch account was not created"
        assert has_acct_uc_berkeley, "UC Berkeley account was not created"
        assert has_acct_crazy_commas, (
            "cRaZy wRoNg tooManyCommas account was not created"
        )
