import pytest
from uuid import UUID

from salestech_be.core.reporting.type.dataset_query_type import (
    QueryConfig, DatasetConfig, ColumnConfig, FieldColumnConfig, DatasetFieldConfig,
    FieldConfig, JoinType, <PERSON>in<PERSON><PERSON>ator, JoinCondition, JoinConfig,
    FilterOperator, FilterConfig, FilterLogic, FilterGroup, OrderByConfig
)
from salestech_be.core.reporting.service.query_builder_service import QueryBuilderService
from salestech_be.db.dbengine.core import DatabaseEngine


@pytest.fixture
def query_builder_service():
    """Create a QueryBuilderService instance for testing."""
    return QueryBuilderService()


def test_build_simple_query(query_builder_service):
    """Test building a simple query with just a primary dataset and columns."""
    # Create a simple dataset configuration
    dataset_config = DatasetConfig(
        dataset_id="123",
        dataset_name="contacts",
        alias="c"
    )

    # Create field configurations
    field_configs = [
        FieldColumnConfig(
            type="field",
            dataset_field=DatasetFieldConfig(
                dataset=dataset_config,
                field=FieldConfig(
                    field_id="id",
                    field_name="id"
                )
            )
        ),
        FieldColumnConfig(
            type="field",
            dataset_field=DatasetFieldConfig(
                dataset=dataset_config,
                field=FieldConfig(
                    field_id="name",
                    field_name="name"
                )
            )
        ),
        FieldColumnConfig(
            type="field",
            dataset_field=DatasetFieldConfig(
                dataset=dataset_config,
                field=FieldConfig(
                    field_id="email",
                    field_name="email"
                )
            )
        )
    ]

    # Create the query config
    config = QueryConfig(
        primary_dataset=dataset_config,
        columns=field_configs,
        limit=100
    )

    # Build the query
    query = query_builder_service.build_query(config)

    # Assertions
    assert "SELECT" in query
    assert "c.id" in query
    assert "c.name" in query
    assert "c.email" in query
    assert "FROM contacts AS c" in query
    assert "LIMIT 100" in query


def test_build_query_with_join(query_builder_service):
    """Test building a query with a join between two datasets."""
    # Create primary dataset configuration
    contacts_dataset = DatasetConfig(
        dataset_id="123",
        dataset_name="contacts",
        alias="c"
    )

    # Create secondary dataset for joining
    accounts_dataset = DatasetConfig(
        dataset_id="456",
        dataset_name="accounts",
        alias="a"
    )

    # Create join condition
    join_condition = JoinCondition(
        left_dataset_field=DatasetFieldConfig(
            dataset=contacts_dataset,
            field=FieldConfig(
                field_id="account_id",
                field_name="account_id"
            )
        ),
        operator=JoinOperator.EQUALS,
        right_dataset_field=DatasetFieldConfig(
            dataset=accounts_dataset,
            field=FieldConfig(
                field_id="id",
                field_name="id"
            )
        )
    )

    # Create join configuration
    join_config = JoinConfig(
        dataset=accounts_dataset,
        join_type=JoinType.INNER,
        condition=join_condition
    )

    # Create field configurations
    field_configs = [
        FieldColumnConfig(
            type="field",
            dataset_field=DatasetFieldConfig(
                dataset=contacts_dataset,
                field=FieldConfig(
                    field_id="name",
                    field_name="name"
                )
            )
        ),
        FieldColumnConfig(
            type="field",
            dataset_field=DatasetFieldConfig(
                dataset=accounts_dataset,
                field=FieldConfig(
                    field_id="name",
                    field_name="name"
                )
            ),
            alias="account_name"
        )
    ]

    # Create the query config
    config = QueryConfig(
        primary_dataset=contacts_dataset,
        additional_datasets=[join_config],
        columns=field_configs
    )

    # Build the query
    query = query_builder_service.build_query(config)

    # Assertions
    assert "SELECT" in query
    assert "c.name" in query
    assert "a.name AS account_name" in query
    assert "FROM contacts AS c" in query
    assert "INNER JOIN accounts AS a" in query
    assert "c.account_id = a.id" in query


def test_build_query_with_filters(query_builder_service):
    """Test building a query with filter conditions."""
    # Create dataset configuration
    dataset_config = DatasetConfig(
        dataset_id="123",
        dataset_name="contacts",
        alias="c"
    )

    # Create field configurations
    field_configs = [
        FieldColumnConfig(
            type="field",
            dataset_field=DatasetFieldConfig(
                dataset=dataset_config,
                field=FieldConfig(
                    field_id="name",
                    field_name="name"
                )
            )
        ),
        FieldColumnConfig(
            type="field",
            dataset_field=DatasetFieldConfig(
                dataset=dataset_config,
                field=FieldConfig(
                    field_id="email",
                    field_name="email"
                )
            )
        )
    ]

    # Create filter conditions
    filter_group = FilterGroup(
        logic=FilterLogic.AND,
        filters=[
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=dataset_config,
                    field=FieldConfig(
                        field_id="status",
                        field_name="status"
                    )
                ),
                operator=FilterOperator.EQUALS,
                value="active"
            ),
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=dataset_config,
                    field=FieldConfig(
                        field_id="created_at",
                        field_name="created_at"
                    )
                ),
                operator=FilterOperator.GREATER_THAN,
                value="2023-01-01"
            )
        ]
    )

    # Create the query config
    config = QueryConfig(
        primary_dataset=dataset_config,
        columns=field_configs,
        filter_group=filter_group
    )

    # Build the query
    query = query_builder_service.build_query(config)

    # Assertions
    assert "SELECT" in query
    assert "c.name" in query
    assert "c.email" in query
    assert "FROM contacts AS c" in query
    assert "WHERE" in query
    assert "c.status = 'active'" in query
    assert "c.created_at > '2023-01-01'" in query


def test_build_query_with_order_by(query_builder_service):
    """Test building a query with ORDER BY clause."""
    # Create dataset configuration
    dataset_config = DatasetConfig(
        dataset_id="123",
        dataset_name="contacts",
        alias="c"
    )

    # Create field configurations
    field_configs = [
        FieldColumnConfig(
            type="field",
            dataset_field=DatasetFieldConfig(
                dataset=dataset_config,
                field=FieldConfig(
                    field_id="name",
                    field_name="name"
                )
            )
        )
    ]

    # Create order by configurations
    order_by_configs = [
        OrderByConfig(
            dataset_field=DatasetFieldConfig(
                dataset=dataset_config,
                field=FieldConfig(
                    field_id="name",
                    field_name="name"
                )
            ),
            direction="ASC"
        )
    ]

    # Create the query config
    config = QueryConfig(
        primary_dataset=dataset_config,
        columns=field_configs,
        order_bys=order_by_configs
    )

    # Build the query
    query = query_builder_service.build_query(config)

    # Assertions
    assert "SELECT" in query
    assert "c.name" in query
    assert "FROM contacts AS c" in query
    assert "ORDER BY" in query
    assert "c.name ASC" in query
