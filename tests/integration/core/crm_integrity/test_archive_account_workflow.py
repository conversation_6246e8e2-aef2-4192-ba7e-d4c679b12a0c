from collections.abc import Awaitable, Callable
from uuid import UUID

import pytest
from faker import Faker
from temporalio.contrib.pydantic import pydantic_data_converter
from temporalio.testing import WorkflowEnvironment
from temporalio.worker import Worker

from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.crm_integrity.atomic_operation_activities.account_related_activity import (
    AccountRelatedActivities,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.integrity_job_activity import (
    IntegrityJobActivity,
)
from salestech_be.core.crm_integrity.data_operation_tracker.integrity_job_service import (
    IntegrityJobService,
)
from salestech_be.core.crm_integrity.event_processors.processor_activity import (
    data_operation_event_processor_activity,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.archive_account_workflow import (
    ArchiveAccountWorkflow,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.integrity_job_dispatch_workflow import (
    get_workflow_entrypoint_and_params,
)
from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
    get_affected_sub_domains_by_integrity_job_name,
)
from salestech_be.db.dao.crm_integrity_repository import CRMIntegrityJobRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityJob,
    CRMSubDomain,
    EntityType,
    IntegrityJobName,
    IntegrityJobType,
)
from salestech_be.integrations.temporal.config import INTEGRITY_JOB_TASK_QUEUE
from salestech_be.temporal.worker import new_sandbox_runner
from tests.integration.core.crm_integrity.mocked_activity import (
    crm_data_integrity_job_send_notification_activity,
)


class ArchiveAccountWorkflowBaseFixtures:
    @pytest.fixture(autouse=True, scope="function")
    async def setup(
        self, make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]]
    ) -> None:
        self.user_id, self.organization_id = await make_user_org()

    @pytest.fixture(scope="function")
    async def account_1(
        self, faker: Faker, account_service: AccountService
    ) -> AccountV2:
        return await account_service.create_account_v2(
            create_account_request=CreateAccountRequest(
                display_name="account_1",
                owner_user_id=self.user_id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    def integrity_job_subdomains(
        self,
    ) -> list[CRMSubDomain]:
        return get_affected_sub_domains_by_integrity_job_name(
            integrity_job_name=IntegrityJobName.ARCHIVE_ACCOUNT,
        )

    @pytest.fixture(scope="function")
    async def integrity_job(
        self,
        integrity_job_service: IntegrityJobService,
        account_1: AccountV2,
        integrity_job_subdomains: list[CRMSubDomain],
    ) -> CRMIntegrityJob:
        integrity_job, _ = await integrity_job_service.create_integrity_job(
            user_id=self.user_id,
            organization_id=self.organization_id,
            job_type=IntegrityJobType.ARCHIVE,
            src_entity_type=EntityType.ACCOUNT,
            src_entity_id=account_1.id,
            dest_entity_type=None,
            dest_entity_id=None,
            user_choices=dict.fromkeys(integrity_job_subdomains),
        )

        return integrity_job

    async def execute_archive_account_workflow(
        self,
        _engine: DatabaseEngine,
        integrity_job: CRMIntegrityJob,
    ) -> UUID:
        integrity_job_activity = IntegrityJobActivity(db_engine=_engine)

        archive_account_activity = AccountRelatedActivities(db_engine=_engine)

        workflow_entrypoint, workflow_param = get_workflow_entrypoint_and_params(
            integrity_job_name=IntegrityJobName.ARCHIVE_ACCOUNT,
            job_id=integrity_job.id,
            retry_count=integrity_job.retry_count,
            src_entity_id=integrity_job.src_entity_id,
            dest_entity_id=None,
            user_id=self.user_id,
            organization_id=self.organization_id,
            user_choices={},
            contextual_param=integrity_job.contextual_param,
        )
        async with (
            await WorkflowEnvironment.start_time_skipping(
                data_converter=pydantic_data_converter
            ) as env,
            Worker(
                env.client,
                task_queue=INTEGRITY_JOB_TASK_QUEUE,
                workflows=[ArchiveAccountWorkflow],
                activities=[
                    integrity_job_activity.create_integrity_job,
                    integrity_job_activity.update_integrity_job,
                    integrity_job_activity.acquire_locks_for_entities,
                    integrity_job_activity.free_locks_for_entities,
                    archive_account_activity.archive_account,
                    # use faked notification activity to avoid sending actual msg and overlap event loop issue
                    crm_data_integrity_job_send_notification_activity,
                    data_operation_event_processor_activity,
                ],
                workflow_runner=new_sandbox_runner(),
            ),
        ):
            await env.client.execute_workflow(
                workflow_entrypoint,
                workflow_param,
                id=f"test_archive_account_workflow_{integrity_job.id}",
                task_queue=INTEGRITY_JOB_TASK_QUEUE,
            )

        return integrity_job.id


class TestArchiveAccountWorkflow(ArchiveAccountWorkflowBaseFixtures):
    async def test_archive_account_workflow(
        self,
        _engine: DatabaseEngine,
        account_1: AccountV2,
        integrity_job: CRMIntegrityJob,
        account_service: AccountService,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
    ) -> None:
        await self.execute_archive_account_workflow(
            _engine=_engine,
            integrity_job=integrity_job,
        )

        updated_integrity_job = await crm_integrity_job_repository.find_by_primary_key(
            table_model=CRMIntegrityJob,
            id=integrity_job.id,
        )

        assert updated_integrity_job
        assert updated_integrity_job.started_at
        assert updated_integrity_job.ended_at

        updated_account = await account_service.get_account_v2(
            organization_id=self.organization_id,
            account_id=account_1.id,
        )
        assert updated_account.archived_at
