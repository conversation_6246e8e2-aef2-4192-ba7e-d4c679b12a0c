"""
Test Case 1: Merge Contact 1 to Contact 2
They are in different accounts that pipeline 1 is associated with

    |---------|          |---------|          |---------|
|-> |Account 1|          |Account 2|          |Account 3|
|   |---------|          |---------|          |---------|
|         ^                    ^                 ^   ^
|         |                    |                 |   |
|         |                    |          |------|   |
|         |              |---------|      |   |---------|
|         |--------------|Contact 1|------|   |Contact 2|
|                        |---------|          |---------|
|                              ^
|                              |
|                              |
|   |---------|                |
|-- |Pipeline1|----------------|
    |---------|
"""

import uuid
from collections.abc import AsyncGenerator, Awaitable, Callable
from datetime import time, timedelta
from random import randint
from unittest.mock import AsyncMock, patch
from uuid import UUID, uuid4

import pytest
from faker import Faker
from pytest_mock import MockerFixture
from temporalio.testing import WorkflowEnvironment
from temporalio.worker import Worker

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdSelectListIdentifier,
)
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.activity.service.activity_service import (
    ActivityService,
)
from salestech_be.core.ai.activities.meeting_pipeline_intel_activities import (
    generate_meeting_activity_summary,
    generate_meeting_how_to_win,
    generate_meeting_objections,
    generate_meeting_risks,
    upsert_pipeline_intel,
)
from salestech_be.core.ai.tasks.activities.generate_intel_tasks import (
    generate_intel_tasks,
)
from salestech_be.core.ai.workflows.trigger_pipeline_intel import (
    MeetingTriggerPipelineIntelWorkflow,
)
from salestech_be.core.calendar.account.calendar_account_service import (
    CalendarAccountService,
)
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.crm_integrity.atomic_operation_activities.associate_contact_with_account_activity import (
    AssociateContactWithAccountActivity,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.associate_contact_with_pipeline_activity import (
    AssociateContactWithPipelineActivity,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.contact_email_related_activity import (
    ContactEmailRelatedActivity,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.contact_related_activity import (
    ContactRelatedActivities,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.integrity_job_activity import (
    IntegrityJobActivity,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.intelligence_related_activity import (
    IntelligenceRelatedActivity,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.phone_number_related_activity import (
    PhoneNumberRelatedActivity,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.unassociate_contact_with_account_activity import (
    UnassociateContactWithAccountActivity,
)
from salestech_be.core.crm_integrity.data_operation_tracker.integrity_job_service import (
    IntegrityJobService,
)
from salestech_be.core.crm_integrity.event_processors.processor_activity import (
    data_operation_event_processor_activity,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.integrity_job_dispatch_workflow import (
    get_workflow_entrypoint_and_params,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.merge_contacts_workflow import (
    MergeContactsWorkflow,
)
from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
    get_affected_sub_domains_by_integrity_job_name,
)
from salestech_be.core.domain_object_list.service.domain_object_list_query_service import (
    DomainObjectListQueryService,
)
from salestech_be.core.domain_object_list.service.domain_object_list_service import (
    DomainObjectListService,
)
from salestech_be.core.domain_object_list.types import (
    DomainObjectList,
)
from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.pool.schema import EmailAccountPoolResponse
from salestech_be.core.email.pool.service import EmailAccountPoolService
from salestech_be.core.email.service.message_service_ext import MessageServiceExt
from salestech_be.core.email.thread.thread_service_ext import ThreadServiceExt
from salestech_be.core.email.type.email import EmailHydratedParticipant, EmailTag
from salestech_be.core.extraction_prompt.type.shared_type import ExtractionType
from salestech_be.core.meeting.dto.meeting_dto import MeetingDto
from salestech_be.core.meeting.dto.meeting_insight_dto import InsightDTO
from salestech_be.core.meeting.meeting_insight_service import (
    MeetingInsightService,
)
from salestech_be.core.meeting.meeting_service import MeetingService
from salestech_be.core.metadata.dto.select_list_dto import PipelineStageDto
from salestech_be.core.metadata.dto.service_api_schema import (
    PipelineStageSelectListCreateRequest,
    PipelineStageSelectListValueCreateRequest,
    SelectListValueCreateRequest,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.metadata.service.pipeline_stage_service import (
    PipelineStageSelectListService,
)
from salestech_be.core.note.service.note_service import NoteService
from salestech_be.core.note.types import Note
from salestech_be.core.pipeline.service.pipeline_intel_service import (
    PipelineIntelService,
)
from salestech_be.core.pipeline.service.pipeline_service import PipelineService
from salestech_be.core.pipeline.service_api_schema import (
    ContactPipelineAssociationRequest,
    CreatePipelineRequest,
    FullContactPipelineAssociationRequests,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.research_agent.research_agent_service import ResearchAgentService
from salestech_be.core.sequence.service.sequence_enrollment_query_service import (
    SequenceEnrollmentQueryService,
)
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    SequenceEnrollmentService,
)
from salestech_be.core.sequence.service.sequence_service import SequenceService
from salestech_be.core.sequence.type.sequence_enrollment_types import (
    SequenceEnrollmentStatus,
)
from salestech_be.core.sequence.type.sequence_v2 import SequenceV2
from salestech_be.core.task.service.task_query_service import (
    TaskQueryService,
)
from salestech_be.core.task.service.task_v2_service import TaskV2Service
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.core.voice.v2.voice_call_service import VoiceCallService
from salestech_be.db.dao.calendar_account_repository import CalendarAccountRepository
from salestech_be.db.dao.crm_integrity_repository import CRMIntegrityJobRepository
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dao.sequence_repository import SequenceRepository
from salestech_be.db.dao.template_repository import TemplateRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dao.user_calendar_repository import UserCalendarRepository
from salestech_be.db.dao.user_integration_repository import UserIntegrationRepository
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dao.voice_call_repository import VoiceCallRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.user_calendar_event_dto import HydratedUserCalendarEventDto
from salestech_be.db.models.contact import (
    CreateContactAccountRoleRequest,
    CreateContactRequest,
    CreateDbContactEmailAccountAssociationRequest,
    CreateDbContactEmailRequest,
    CreateDbContactPhoneNumberRequest,
    CreateDbContactRequest,
)
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityAssociatedEntityOperation,
    CRMIntegrityJob,
    CRMIntegrityOperation,
    CRMSubDomain,
    EntityType,
    IntegrityJobName,
    IntegrityJobType,
)
from salestech_be.db.models.domain_object_list import (
    DomainObjectListItemType,
    DomainObjectListOrigin,
)
from salestech_be.db.models.email_account import EmailAccount
from salestech_be.db.models.global_thread import (
    GlobalMessage,
    GlobalMessageAssociation,
    GlobalThread,
)
from salestech_be.db.models.insight import (
    InsightReferenceIdType,
    InsightSection,
    InsightSourceType,
)
from salestech_be.db.models.intel_person_association import IntelPersonAssociation
from salestech_be.db.models.meeting import MeetingProvider, MeetingReferenceIdType
from salestech_be.db.models.message import Message, MessageSource, MessageStatus
from salestech_be.db.models.pipeline import PipelineStatus
from salestech_be.db.models.pipeline_stage_select_list_value_metadata import (
    PipelineOutcomeState,
)
from salestech_be.db.models.sequence import (
    DayOfWeek,
    EmailStepContent,
    SequenceEnrollment,
    SequenceEnrollmentStepVariantAssociation,
    SequenceStepExecution,
    SequenceStepExecutionStatus,
    SequenceStepType,
    SequenceStepV2,
    SequenceStepVariant,
    SequenceStepVariantStatus,
    SequenceVisibility,
)
from salestech_be.db.models.task import (
    TaskPriority,
    TaskSourceType,
    TaskStatus,
    TaskType,
)
from salestech_be.db.models.template import Template, TemplateStatus, TemplateType
from salestech_be.db.models.thread import Thread
from salestech_be.db.models.user_calendar import UserCalendar
from salestech_be.db.models.voice_v2 import Call, CallDirection, CallStatus, CallType
from salestech_be.integrations.nylas.model import (
    NylasEmailName,
    NylasEvent,
    NylasEventParticipant,
    NylasTimespan,
    Visibility,
)
from salestech_be.integrations.temporal.config import (
    AI_TASK_QUEUE,
    INTEGRITY_JOB_TASK_QUEUE,
)
from salestech_be.temporal.worker import new_sandbox_runner
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.calendar.schema import (
    ContactParticipantRequest,
    CreateCalendarEventRequest,
    EventConferencingRequest,
    ListCalendarEventRequest,
)
from salestech_be.web.api.calendar.service import UserCalendarService
from salestech_be.web.api.domain_object_list.schema import (
    AddItemsRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CreateDomainObjectListRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.meeting.schema import (
    CreateInsightsFieldRequest,
    CreateMeetingRequest,
    Invitee,
)
from salestech_be.web.api.note.schema import CreateNoteRequest
from salestech_be.web.api.sequence.enrollment.schema import (
    ContactForSequenceEnrollment,
    CreateSequenceEnrollmentRequest,
)
from salestech_be.web.api.sequence.schema import (
    CreateSequenceRequest,
    ScheduleConfig,
    ScheduleTime,
)
from salestech_be.web.api.task.schema import (
    CreateTaskRequest,
)
from tests.integration.core.calendar.test_user_calendar_webhook_service import (
    create_calendar_account_and_user_integration,
)
from tests.integration.core.crm_integrity.mocked_activity import (
    crm_data_integrity_job_send_notification_activity,
)


class MergeContactsWorkflowBaseFixtures:
    @pytest.fixture(autouse=True, scope="function")
    async def setup(
        self, make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]]
    ) -> None:
        self.user_id, self.organization_id = await make_user_org()

    @pytest.fixture(autouse=True, scope="class")
    async def _mock_temporal_client(
        self,
        wf_env: WorkflowEnvironment,
    ) -> AsyncGenerator[tuple[AsyncMock, AsyncMock, AsyncMock]]:
        """Globally mocks get_temporal_client for the entire test class."""

        mock_client = wf_env.client

        with (
            patch(
                "salestech_be.core.task.service.task_v2_service.get_temporal_client",
                new_callable=AsyncMock,
                return_value=mock_client,
            ) as mock_get_client_task,
            patch(
                "salestech_be.core.meeting.meeting_service.get_temporal_client",
                new_callable=AsyncMock,
                return_value=mock_client,
            ) as mock_get_client_meeting,
            patch(
                "salestech_be.core.ai.event_handlers.pipeline_intel.get_temporal_client",
                new_callable=AsyncMock,
                return_value=mock_client,
            ) as mock_get_client_ai,
        ):
            yield mock_get_client_task, mock_get_client_meeting, mock_get_client_ai

    @pytest.fixture(scope="function")
    async def pipeline_stage_dto(
        self,
        pipeline_stage_select_list_service: PipelineStageSelectListService,
        faker: Faker,
    ) -> PipelineStageDto:
        return (
            await pipeline_stage_select_list_service.create_pipeline_stage_select_list(
                organization_id=self.organization_id,
                actioning_user_id=self.user_id,
                req=PipelineStageSelectListCreateRequest(
                    display_name=faker.company(),
                    description=faker.text(),
                    is_default=True,
                    value_reqs=(
                        PipelineStageSelectListValueCreateRequest(
                            pipeline_status=PipelineStatus.PROSPECT,
                            select_list_value_req=SelectListValueCreateRequest(
                                api_name="starting",
                                display_value="Starting",
                                is_default=True,
                            ),
                        ),
                        PipelineStageSelectListValueCreateRequest(
                            pipeline_status=PipelineStatus.PROSPECT,
                            select_list_value_req=SelectListValueCreateRequest(
                                api_name="prospect",
                                display_value="Prospect",
                                is_default=False,
                            ),
                        ),
                        PipelineStageSelectListValueCreateRequest(
                            pipeline_status=PipelineStatus.DEAL,
                            outcome_state=PipelineOutcomeState.CLOSED_LOST,
                            select_list_value_req=SelectListValueCreateRequest(
                                api_name="closed_lost",
                                display_value="Closed Lost",
                                is_default=False,
                            ),
                        ),
                        PipelineStageSelectListValueCreateRequest(
                            pipeline_status=PipelineStatus.DEAL,
                            outcome_state=PipelineOutcomeState.CLOSED_WON,
                            select_list_value_req=SelectListValueCreateRequest(
                                api_name="closed_won",
                                display_value="Closed Won",
                                is_default=False,
                            ),
                        ),
                    ),
                ),
            )
        )

    @pytest.fixture(scope="function")
    async def email_1(
        self,
        faker: Faker,
    ) -> str:
        return faker.email()

    @pytest.fixture(scope="function")
    async def email_2(
        self,
        faker: Faker,
    ) -> str:
        return faker.email()

    @pytest.fixture(scope="function")
    async def phone_number_1(
        self,
    ) -> str:
        return f"+***********,{randint(999, 9999)}"  # noqa: S311

    @pytest.fixture(scope="function")
    async def phone_number_2(
        self,
    ) -> str:
        return f"+***********,{randint(999, 9999)}"  # noqa: S311

    @pytest.fixture(scope="function")
    async def account_1(
        self, faker: Faker, account_service: AccountService
    ) -> AccountV2:
        return await account_service.create_account_v2(
            create_account_request=CreateAccountRequest(
                display_name="account_1",
                owner_user_id=self.user_id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def account_2(
        self, faker: Faker, account_service: AccountService
    ) -> AccountV2:
        return await account_service.create_account_v2(
            create_account_request=CreateAccountRequest(
                display_name="account_2",
                owner_user_id=self.user_id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def account_3(
        self, faker: Faker, account_service: AccountService
    ) -> AccountV2:
        return await account_service.create_account_v2(
            create_account_request=CreateAccountRequest(
                display_name="account_3",
                owner_user_id=self.user_id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def contact_1(
        self,
        account_1: AccountV2,
        account_2: AccountV2,
        account_3: AccountV2,
        faker: Faker,
        email_1: str,
        phone_number_1: str,
        phone_number_2: str,
        contact_service: ContactService,
        select_list_service: InternalSelectListService,
    ) -> ContactV2:
        default_stage_list = await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=self.organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        assert default_stage_list and default_stage_list.default_or_initial_active_value
        created_contact = await contact_service.create_contact_with_contact_channels(
            organization_id=self.organization_id,
            user_id=self.user_id,
            create_contact_with_contact_channel_request=CreateContactRequest(
                contact=CreateDbContactRequest(
                    first_name=faker.first_name(),
                    last_name=faker.last_name(),
                    display_name="contact_1",
                    owner_user_id=self.user_id,
                    created_by_user_id=self.user_id,
                    stage_id=default_stage_list.default_or_initial_active_value.id,
                ),
                contact_emails=[
                    CreateDbContactEmailRequest(
                        email=email_1,
                        is_contact_primary=True,
                        email_account_associations=[
                            CreateDbContactEmailAccountAssociationRequest(
                                account_id=account_1.id,
                                is_contact_account_primary=True,
                            ),
                            CreateDbContactEmailAccountAssociationRequest(
                                account_id=account_3.id,
                                is_contact_account_primary=True,
                            ),
                        ],
                    )
                ],
                contact_phone_numbers=[
                    CreateDbContactPhoneNumberRequest(
                        phone_number=phone_number_1,
                        is_contact_primary=True,
                    ),
                    CreateDbContactPhoneNumberRequest(
                        phone_number=phone_number_2,
                        is_contact_primary=False,
                    ),
                ],
                contact_account_roles=[
                    CreateContactAccountRoleRequest(
                        account_id=account_1.id,
                        is_primary_account=True,
                    ),
                    CreateContactAccountRoleRequest(
                        account_id=account_2.id,
                        is_primary_account=False,
                    ),
                    CreateContactAccountRoleRequest(
                        account_id=account_3.id,
                        is_primary_account=False,
                    ),
                ],
            ),
        )

        # check contact account associations
        contact_account_1_associations = (
            await contact_service.list_active_contact_account_associations(
                organization_id=self.organization_id,
                contact_id=created_contact.id,
                account_id=account_1.id,
            )
        )
        assert len(contact_account_1_associations) == 1
        assert contact_account_1_associations[0].is_primary

        contact_account_2_associations = (
            await contact_service.list_active_contact_account_associations(
                organization_id=self.organization_id,
                contact_id=created_contact.id,
                account_id=account_2.id,
            )
        )
        assert len(contact_account_2_associations) == 1
        assert not contact_account_2_associations[0].is_primary

        contact_account_3_associations = (
            await contact_service.list_active_contact_account_associations(
                organization_id=self.organization_id,
                contact_id=created_contact.id,
                account_id=account_3.id,
            )
        )
        assert len(contact_account_3_associations) == 1
        assert not contact_account_3_associations[0].is_primary

        # check contact phone number
        contact_phone_number_dtos = await contact_service.contact_query_service.list_contact_phone_number_dtos_by_contact_id(
            organization_id=self.organization_id,
            contact_id=created_contact.id,
        )

        assert len(contact_phone_number_dtos) == 2
        assert {
            dto.contact_phone_number.phone_number for dto in contact_phone_number_dtos
        } == {phone_number_1, phone_number_2}
        assert (
            contact_phone_number_dtos[0].contact_phone_number_account_associations == []
        )
        assert (
            contact_phone_number_dtos[1].contact_phone_number_account_associations == []
        )

        # check contact email
        contact_contact_email = await contact_service.find_contact_email_by_email(
            organization_id=self.organization_id,
            email=email_1,
        )
        assert contact_contact_email
        assert contact_contact_email.is_contact_primary

        # check contact email account association
        contact_email_account_associations = await contact_service.list_contact_email_account_associations_by_contact_email_id(
            organization_id=self.organization_id,
            contact_email_id=contact_contact_email.id,
        )
        assert len(contact_email_account_associations) == 2
        assert {
            (
                cea.contact_id,
                cea.contact_email_id,
                cea.account_id,
                cea.is_contact_account_primary,
            )
            for cea in contact_email_account_associations
        } == {
            (created_contact.id, contact_contact_email.id, account_1.id, True),
            (created_contact.id, contact_contact_email.id, account_3.id, True),
        }

        return created_contact

    @pytest.fixture(scope="function")
    async def contact_2(
        self,
        account_1: AccountV2,
        account_2: AccountV2,
        account_3: AccountV2,
        faker: Faker,
        email_2: str,
        phone_number_2: str,
        contact_service: ContactService,
        select_list_service: InternalSelectListService,
    ) -> ContactV2:
        default_stage_list = await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=self.organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        assert default_stage_list and default_stage_list.default_or_initial_active_value
        created_contact = await contact_service.create_contact_with_contact_channels(
            organization_id=self.organization_id,
            user_id=self.user_id,
            create_contact_with_contact_channel_request=CreateContactRequest(
                contact=CreateDbContactRequest(
                    first_name=faker.first_name(),
                    last_name=faker.last_name(),
                    display_name="contact_2",
                    owner_user_id=self.user_id,
                    created_by_user_id=self.user_id,
                    stage_id=default_stage_list.default_or_initial_active_value.id,
                ),
                contact_emails=[
                    CreateDbContactEmailRequest(
                        email=email_2,
                        is_contact_primary=True,
                        email_account_associations=[
                            CreateDbContactEmailAccountAssociationRequest(
                                account_id=account_3.id,
                                is_contact_account_primary=True,
                            ),
                        ],
                    )
                ],
                contact_phone_numbers=[
                    CreateDbContactPhoneNumberRequest(
                        phone_number=phone_number_2,
                        is_contact_primary=True,
                    ),
                ],
                contact_account_roles=[
                    CreateContactAccountRoleRequest(
                        account_id=account_3.id,
                        is_primary_account=True,
                    ),
                ],
            ),
        )

        # check contact account associations
        contact_account_1_associations = (
            await contact_service.list_active_contact_account_associations(
                organization_id=self.organization_id,
                contact_id=created_contact.id,
                account_id=account_1.id,
            )
        )
        assert len(contact_account_1_associations) == 0

        contact_account_2_associations = (
            await contact_service.list_active_contact_account_associations(
                organization_id=self.organization_id,
                contact_id=created_contact.id,
                account_id=account_2.id,
            )
        )
        assert len(contact_account_2_associations) == 0

        contact_account_3_associations = (
            await contact_service.list_active_contact_account_associations(
                organization_id=self.organization_id,
                contact_id=created_contact.id,
                account_id=account_3.id,
            )
        )
        assert len(contact_account_3_associations) == 1
        assert contact_account_3_associations[0].is_primary

        # check contact phone number
        contact_phone_number_dtos = await contact_service.contact_query_service.list_contact_phone_number_dtos_by_contact_id(
            organization_id=self.organization_id,
            contact_id=created_contact.id,
        )

        assert len(contact_phone_number_dtos) == 1
        assert (
            contact_phone_number_dtos[0].contact_phone_number.phone_number
            == phone_number_2
        )
        assert (
            contact_phone_number_dtos[0].contact_phone_number_account_associations == []
        )

        # check contact email
        contact_contact_email = await contact_service.find_contact_email_by_email(
            organization_id=self.organization_id,
            email=email_2,
        )
        assert contact_contact_email
        assert contact_contact_email.is_contact_primary

        # check contact email account association
        contact_email_1_account_associations = await contact_service.list_contact_email_account_associations_by_contact_email_id(
            organization_id=self.organization_id,
            contact_email_id=contact_contact_email.id,
        )
        assert len(contact_email_1_account_associations) == 1
        assert contact_email_1_account_associations[0].contact_id == created_contact.id
        assert (
            contact_email_1_account_associations[0].contact_email_id
            == contact_contact_email.id
        )
        assert contact_email_1_account_associations[0].account_id == account_3.id
        assert contact_email_1_account_associations[0].is_contact_account_primary

        return created_contact

    @pytest.fixture(scope="function")
    async def pipeline_1(
        self,
        faker: Faker,
        pipeline_service: PipelineService,
        account_1: AccountV2,
        contact_1: ContactV2,
        pipeline_stage_dto: PipelineStageDto,
    ) -> PipelineV2:
        return await pipeline_service.create_pipeline(
            req=CreatePipelineRequest(
                display_name=faker.company(),
                account_id=account_1.id,
                owner_user_id=self.user_id,
                contact_pipeline_associations=FullContactPipelineAssociationRequests(
                    primary=ContactPipelineAssociationRequest(
                        contact_id=contact_1.id,
                    ),
                ),
                stage_id=pipeline_stage_dto.select_list_value_dtos[
                    0
                ].select_list_value.id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    def integrity_job_subdomains(
        self,
    ) -> list[CRMSubDomain]:
        return get_affected_sub_domains_by_integrity_job_name(
            integrity_job_name=IntegrityJobName.MERGE_CONTACTS,
        )

    @pytest.fixture(scope="function")
    async def integrity_job(
        self,
        integrity_job_service: IntegrityJobService,
        contact_1: ContactV2,
        contact_2: ContactV2,
        integrity_job_subdomains: list[CRMSubDomain],
    ) -> CRMIntegrityJob:
        integrity_job, _ = await integrity_job_service.create_integrity_job(
            user_id=self.user_id,
            organization_id=self.organization_id,
            job_type=IntegrityJobType.MERGE,
            src_entity_type=EntityType.CONTACT,
            src_entity_id=contact_1.id,
            dest_entity_type=EntityType.CONTACT,
            dest_entity_id=contact_2.id,
            user_choices=dict.fromkeys(integrity_job_subdomains),
        )

        return integrity_job

    @pytest.fixture(scope="function")
    async def email_account(
        self,
        faker: Faker,
        email_account_service_ext: EmailAccountServiceExt,
    ) -> EmailAccount:
        return await email_account_service_ext.get_or_create_email_account(
            owner_user_id=self.user_id,
            email=faker.email(),
            signature_html=faker.text(),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def email_template(
        self,
        faker: Faker,
        template_repository: TemplateRepository,
    ) -> Template:
        template = Template(
            id=uuid.uuid4(),
            name=faker.sentence(),
            template_type=TemplateType.EMAIL,
            status=TemplateStatus.ACTIVE,
            body_text="",
            body_html="",
            is_shared=False,
            version=1,
            include_email_signature=True,
            unsubscription_group_id=None,
            organization_id=self.organization_id,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
        return await template_repository.insert(template)

    @pytest.fixture(scope="function")
    async def email_thread(
        self,
        contact_1: ContactV2,
        contact_2: ContactV2,
        email_account: EmailAccount,
        thread_repository: ThreadRepository,
        email_template: Template,
    ) -> Thread:
        recipient_email = "<EMAIL>"
        recipient_name = "Test"
        sender_email = "<EMAIL>"
        sender_name = "Sender"
        email_recipient = EmailHydratedParticipant(
            email=recipient_email,
            name=recipient_name,
            contact_id=contact_1.id,
        )
        email_sender = EmailHydratedParticipant(
            email=sender_email,
            name=sender_name,
            email_account_id=email_account.id,
        )

        return await thread_repository.insert(
            Thread(
                id=uuid.uuid4(),
                email_account_id=not_none(email_sender.email_account_id),
                organization_id=self.organization_id,
                subject=email_template.subject or "",
                snippet=email_template.body_text[:100],
                unread=None,
                starred=None,
                has_attachments=bool(email_template.attachment_ids),
                has_drafts=None,
                earliest_message_date=zoned_utc_now(),
                latest_message_sent_date=None,
                latest_message_received_date=None,
                participants=[email_sender, email_recipient],
                tags=[EmailTag.OUTBOUND],
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
                deleted_at=None,
            )
        )

    @pytest.fixture(scope="function")
    async def email_message(
        self,
        contact_1: ContactV2,
        contact_2: ContactV2,
        email_thread: Thread,
        message_repository: MessageRepository,
        email_account: EmailAccount,
        email_template: Template,
    ) -> Message:
        recipient_email = "<EMAIL>"
        recipient_name = "Test"
        sender_email = "<EMAIL>"
        sender_name = "Sender"
        email_recipient = EmailHydratedParticipant(
            email=recipient_email,
            name=recipient_name,
            contact_id=contact_1.id,
            email_account_id=email_account.id,
        )
        email_sender = EmailHydratedParticipant(
            email=sender_email,
            name=sender_name,
            contact_id=contact_1.id,
            email_account_id=email_account.id,
        )

        return await message_repository.insert(
            Message(
                id=uuid4(),
                thread_id=email_thread.id,
                idempotency_key=str(uuid4()),
                source=MessageSource.SEQUENCE,
                email_account_id=not_none(email_sender.email_account_id),
                organization_id=self.organization_id,
                template_id=email_template.id,
                email_template_version=email_template.version,
                subject=email_template.subject or "",
                send_from=[email_sender],
                send_to=[email_recipient],
                cc=email_template.cc,
                bcc=email_template.bcc,
                reply_to=email_template.reply_to,
                reply_to_message_id=None,
                status=MessageStatus.SCHEDULED,
                snippet=email_template.body_text[:100],
                body_text=email_template.body_text,
                body_html=email_template.body_html,
                attachment_ids=email_template.attachment_ids,
                folders=None,
                starred=None,
                unread=None,
                use_draft=None,
                send_at=zoned_utc_now(),
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )

    @pytest.fixture(scope="function")
    async def global_thread(
        self,
        faker: Faker,
        contact_1: ContactV2,
        account_1: AccountV2,
        pipeline_1: PipelineV2,
        email_thread: Thread,
        thread_repository: ThreadRepository,
    ) -> GlobalThread:
        return await thread_repository.insert(
            GlobalThread(
                id=uuid4(),
                subject=faker.sentence(),
                snippet=faker.text(),
                thread_ids=[email_thread.id],
                contact_ids=[contact_1.id],
                account_ids=[account_1.id],
                pipeline_id=pipeline_1.id,
                organization_id=self.organization_id,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )

    @pytest.fixture(scope="function")
    async def global_message(
        self,
        global_thread: GlobalThread,
        email_message: Message,
        thread_repository: ThreadRepository,
    ) -> GlobalMessage:
        created_global_message = await thread_repository.insert(
            GlobalMessage(
                id=uuid4(),
                original_message_id="test_original_message_id",
                organization_id=self.organization_id,
                global_thread_id=global_thread.id,
                message_received_at=zoned_utc_now(),
                created_at=zoned_utc_now(),
            )
        )

        await thread_repository.insert(
            GlobalMessageAssociation(
                id=uuid4(),
                global_message_id=created_global_message.id,
                message_id=email_message.id,
                organization_id=self.organization_id,
                created_at=zoned_utc_now(),
            )
        )

        return created_global_message

    @pytest.fixture(scope="function")
    async def meeting_insight(
        self,
        contact_1: ContactV2,
        faker: Faker,
        _engine: DatabaseEngine,
        meeting_insight_service: MeetingInsightService,
    ) -> InsightDTO:
        insight_section = await meeting_insight_service.insight_repository.insert(
            InsightSection(
                id=uuid4(),
                organization_id=self.organization_id,
                reference_id=uuid4(),
                reference_type=InsightReferenceIdType.MEETING,  # Use a valid enum value
                name="Test Section",
                source_type=InsightSourceType.TRANSCRIPTION,
                insight_type=ExtractionType.XSCRIPT_SUMMARY_EXTRACTION,  # Use the correct enum type
                description="Test Description",
                extraction_section_version=1,
                extraction_section_id=uuid4(),
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
                deleted_by_user_id=None,
                deleted_at=None,
                user_feedback=None,
            )
        )
        return await meeting_insight_service.create_insight_field(
            organization_id=self.organization_id,
            user_id=self.user_id,
            request=CreateInsightsFieldRequest(
                meeting_insight_id=insight_section.id,
                name=faker.sentence(),
                description=faker.text(),
                contact_id=contact_1.id,
                field_values=[faker.text()],
            ),
        )

    @pytest.fixture(scope="function")
    async def mock_nylas_event(self) -> NylasEvent:
        return NylasEvent(
            id="test_nylas_event_id",
            grant_id="test_grant_id",
            calendar_id="test_calendar_id",
            when=NylasTimespan(
                start_timezone="America/Los_Angeles",
                end_timezone="America/Los_Angeles",
                object="timespan",
                start_time=int(zoned_utc_now().timestamp()),
                end_time=int(zoned_utc_now().timestamp()),
            ),
            html_link="http://test.test",
            busy=False,
            participants=[
                NylasEventParticipant(email="<EMAIL>", status="noreply")
            ],
            conferencing={
                "provider": "Google Meet",
                "details": {"url": "http://test.test"},
            },
            object="event",
            visibility=Visibility.private,
            description="test description",
            ical_uid="ical_uid",
            title="test title",
            organizer=NylasEmailName(
                email="<EMAIL>",
                name="test user",
            ),
            status="confirmed",
            created_at=int(zoned_utc_now().timestamp()),
            updated_at=int(zoned_utc_now().timestamp()),
        )

    @pytest.fixture(scope="function")
    async def calendar_event(
        self,
        faker: Faker,
        contact_1: ContactV2,
        user_calendar_service: UserCalendarService,
        user_repository: UserRepository,
        user_calendar_repository: UserCalendarRepository,
        calendar_account_service: CalendarAccountService,
        calendar_account_repository: CalendarAccountRepository,
        user_integration_repository: UserIntegrationRepository,
        mock_nylas_event: NylasEvent,
    ) -> HydratedUserCalendarEventDto:
        (
            calendar_account,
            user_integration_dto,
        ) = await create_calendar_account_and_user_integration(
            user_id=self.user_id,
            organization_id=self.organization_id,
            calendar_account_repository=calendar_account_repository,
            user_integration_repository=user_integration_repository,
        )
        await user_calendar_repository.insert(
            UserCalendar(
                id=uuid4(),
                user_id=self.user_id,
                organization_id=self.organization_id,
                user_integration_id=user_integration_dto.user_integration.id,
                calendar_account_id=calendar_account.id,
                name="test_calendar",
                timezone="America/Los_Angeles",
                external_id="test_calendar_id",
                is_primary=True,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )
        user = await user_repository.find_user_by_id_and_organization_id(
            user_id=self.user_id,
            organization_id=self.organization_id,
        )
        mock_nylas_event.participants = [
            NylasEventParticipant(
                email=not_none(contact_1.primary_email), status="noreply"
            )
        ]
        with patch(
            "salestech_be.integrations.nylas.async_nylas_client.AsyncNylasClient.create_event",
            new_callable=AsyncMock,
            return_value=mock_nylas_event,
        ):
            return await user_calendar_service.create_calendar_event(
                user=not_none(user),
                organization_id=self.organization_id,
                request=CreateCalendarEventRequest(
                    starts_at=zoned_utc_now(),
                    ends_at=zoned_utc_now() + timedelta(hours=1),
                    title=faker.sentence(),
                    description=faker.text(),
                    conferencing=EventConferencingRequest(
                        conferencing_provider="Google Meet"
                    ),
                    participants=[
                        ContactParticipantRequest(
                            participant_type="contact", contact_id=contact_1.id
                        )
                    ],
                    is_busy=True,
                    notify_all_participants=True,
                    enable_recording=True,
                    use_consent_link=True,
                    visibility=Visibility.private,
                ),
            )

    @pytest.fixture(scope="function")
    async def intel_person_association_contact_1(
        self,
        faker: Faker,
        contact_1: ContactV2,
        research_agent_service: ResearchAgentService,
    ) -> IntelPersonAssociation:
        await research_agent_service.get_or_create_intel_person_id(
            contact_id=contact_1.id,
            contact_linkedin_url=faker.url(),
        )
        intel_person_association = (
            await research_agent_service.get_intel_person_association_by_contact_id(
                contact_id=contact_1.id,
            )
        )
        return not_none(intel_person_association)

    @pytest.fixture(scope="function")
    async def intel_person_association_contact_2(
        self,
        faker: Faker,
        contact_2: ContactV2,
        research_agent_service: ResearchAgentService,
    ) -> IntelPersonAssociation:
        await research_agent_service.get_or_create_intel_person_id(
            contact_id=contact_2.id,
            contact_linkedin_url=faker.url(),
        )
        intel_person_association = (
            await research_agent_service.get_intel_person_association_by_contact_id(
                contact_id=contact_2.id,
            )
        )
        return not_none(intel_person_association)

    @pytest.fixture(scope="function")
    async def meeting(
        self,
        contact_1: ContactV2,
        calendar_event: HydratedUserCalendarEventDto,
        meeting_service: MeetingService,
        faker: Faker,
    ) -> MeetingDto:
        user_invitee = Invitee(user_id=self.user_id, is_organizer=True)
        contact_invitee = Invitee(contact_id=contact_1.id, is_organizer=False)
        create_meeting_request = CreateMeetingRequest(
            platform=MeetingProvider.ZOOM,
            reference_id=calendar_event.user_calendar_event.id,
            reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
            starts_at=zoned_utc_now(),
            title=faker.sentence(),
            event_conferencing=None,
            invitees=[user_invitee, contact_invitee],
        )
        return await meeting_service.create_meeting(
            organization_id=self.organization_id,
            user_id=self.user_id,
            request=create_meeting_request,
        )

    @pytest.fixture(scope="function")
    async def note(
        self,
        contact_1: ContactV2,
        note_service: NoteService,
        faker: Faker,
    ) -> Note:
        return await note_service.insert_note(
            user_id=self.user_id,
            organization_id=self.organization_id,
            create_note_request=CreateNoteRequest(
                note_html=faker.text(),
                contact_ids=[contact_1.id],
            ),
        )

    @pytest.fixture(scope="function")
    async def task_1(
        self,
        account_1: AccountV2,
        contact_1: ContactV2,
        pipeline_1: PipelineV2,
        task_v2_service: TaskV2Service,
    ) -> TaskV2:
        return await task_v2_service.insert_task_v2(
            created_by_user_id=self.user_id,
            organization_id=self.organization_id,
            request=CreateTaskRequest(
                title=str(uuid4()),
                status=TaskStatus.OPEN,
                priority=TaskPriority.MEDIUM,
                type=TaskType.ACTION_ITEM,
                account_id=account_1.id,
                contact_ids=[contact_1.id],
                pipeline_id=pipeline_1.id,
                owner_user_id=self.user_id,
                source_type=TaskSourceType.USER,
            ),
        )

    @pytest.fixture(scope="function")
    async def voice_call(
        self,
        faker: Faker,
        contact_1: ContactV2,
        account_1: AccountV2,
        pipeline_1: PipelineV2,
        voice_call_repository: VoiceCallRepository,
        voice_call_service: VoiceCallService,
    ) -> Call:
        created_call = await voice_call_repository.insert(
            Call(
                id=uuid4(),
                organization_id=self.organization_id,
                organization_phone_number_id=uuid4(),
                voice_provider_account_id=uuid4(),
                call_type=CallType.WEBRTC,
                caller_number=faker.phone_number(),
                status=CallStatus.INITIATED,
                direction=CallDirection.OUTBOUND,
                created_by_user_id=self.user_id,
                caller_id=self.user_id,
                contact_id=contact_1.id,
                pipeline_id=pipeline_1.id,
                account_id=account_1.id,
                recipient_number=faker.phone_number(),
                recipient_id=contact_1.id,
                metadata=None,
                external_id=None,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )

        created_meeting_dto = await voice_call_service._create_meeting_from_call(
            created_call
        )
        created_voice_meeting = created_meeting_dto.meeting

        # check voice meeting created
        assert created_voice_meeting.reference_id == str(created_call.id)
        assert (
            created_voice_meeting.reference_id_type == MeetingReferenceIdType.VOICE_V2
        )
        assert created_voice_meeting.meeting_platform == MeetingProvider.VOICE
        assert created_voice_meeting.account_id == account_1.id
        assert created_voice_meeting.pipeline_id == pipeline_1.id

        contact_invitee = [
            invitee
            for invitee in created_voice_meeting.invitees or []
            if invitee.contact_id == contact_1.id
        ]
        assert len(contact_invitee) == 1
        assert contact_invitee[0].is_organizer is False
        assert contact_invitee[0].account_id is None
        assert contact_invitee[0].contact_email is None

        return created_call

    @pytest.fixture(scope="function")
    async def email_account_pool(
        self,
        email_account_pool_service: EmailAccountPoolService,
    ) -> EmailAccountPoolResponse:
        return (
            await email_account_pool_service.get_or_create_default_email_account_pool(
                organization_id=self.organization_id,
                user_id=self.user_id,
            )
        )

    @pytest.fixture(scope="function")
    async def sequence_1_with_contact_1(
        self,
        faker: Faker,
        contact_1: ContactV2,
        email_account_pool: EmailAccountPoolResponse,
        sequence_service: SequenceService,
        sequence_enrollment_service: SequenceEnrollmentService,
    ) -> SequenceV2:
        created_sequence = await sequence_service._create_sequence(
            organization_id=self.organization_id,
            user_id=self.user_id,
            request=CreateSequenceRequest(
                name=faker.name(),
                description=faker.text(),
                visibility=SequenceVisibility.TEAM_EDITABLE,
                schedule=ScheduleConfig(
                    timezone="America/New_York",
                    skip_holidays=False,
                    schedule_times=[
                        ScheduleTime(
                            day_of_week=DayOfWeek.MON,
                            start_time=time(hour=9, minute=0),
                            end_time=time(hour=17, minute=0),
                        ),
                    ],
                ),
            ),
            is_blueprint=False,
        )

        created_sequence_enrollment_response = (
            await sequence_enrollment_service.create_sequence_enrollment(
                user_auth_context=UserAuthContext(
                    user_id=self.user_id,
                    organization_id=self.organization_id,
                ),
                request=CreateSequenceEnrollmentRequest(
                    sequence_id=created_sequence.id,
                    contacts=[
                        ContactForSequenceEnrollment(
                            contact_id=contact_1.id,
                        )
                    ],
                ),
            )
        )

        assert len(created_sequence_enrollment_response.failed_enrollments) == 0

        return created_sequence

    @pytest.fixture(scope="function")
    async def sequence_2_with_contact_1_and_contact_2(
        self,
        faker: Faker,
        contact_1: ContactV2,
        contact_2: ContactV2,
        sequence_service: SequenceService,
        email_account_pool: EmailAccountPoolResponse,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_query_service: SequenceEnrollmentQueryService,
        sequence_repository: SequenceRepository,
        email_template: Template,
        global_thread: GlobalThread,
        global_message: GlobalMessage,
    ) -> SequenceV2:
        created_sequence = await sequence_service._create_sequence(
            organization_id=self.organization_id,
            user_id=self.user_id,
            request=CreateSequenceRequest(
                name=faker.name(),
                description=faker.text(),
                visibility=SequenceVisibility.TEAM_EDITABLE,
                schedule=ScheduleConfig(
                    timezone="America/New_York",
                    skip_holidays=False,
                    schedule_times=[
                        ScheduleTime(
                            day_of_week=DayOfWeek.MON,
                            start_time=time(hour=9, minute=0),
                            end_time=time(hour=17, minute=0),
                        ),
                    ],
                ),
            ),
            is_blueprint=False,
        )

        created_sequence_enrollment_response = (
            await sequence_enrollment_service.create_sequence_enrollment(
                user_auth_context=UserAuthContext(
                    user_id=self.user_id,
                    organization_id=self.organization_id,
                ),
                request=CreateSequenceEnrollmentRequest(
                    sequence_id=created_sequence.id,
                    contacts=[
                        ContactForSequenceEnrollment(
                            contact_id=contact_1.id,
                        ),
                        ContactForSequenceEnrollment(
                            contact_id=contact_2.id,
                        ),
                    ],
                ),
            )
        )

        assert len(created_sequence_enrollment_response.failed_enrollments) == 0

        created_enrollment_contact_1 = await sequence_enrollment_query_service.find_sequence_enrollments_by_contact_id(
            contact_id=contact_1.id,
            organization_id=self.organization_id,
            sequence_id=created_sequence.id,
        )

        assert len(created_enrollment_contact_1) == 1

        created_step = await sequence_repository.insert(
            SequenceStepV2(
                id=uuid4(),
                name=faker.name(),
                sequence_id=created_sequence.id,
                is_first_step=True,
                next_step_id=None,
                delay_minutes=0,
                type=SequenceStepType.AUTO_EMAIL,
                support_ab_test=True,
                organization_id=self.organization_id,
                created_by_user_id=self.user_id,
                created_at=zoned_utc_now(),
            )
        )

        updated_created_enrollment_contact_1 = (
            await sequence_repository.update_by_tenanted_primary_key(
                table_model=SequenceEnrollment,
                primary_key_to_value={"id": created_enrollment_contact_1[0].id},
                organization_id=self.organization_id,
                column_to_update={"current_step_id": created_step.id},
            )
        )

        assert updated_created_enrollment_contact_1

        created_variant = await sequence_repository.insert(
            SequenceStepVariant(
                id=uuid4(),
                name=faker.name(),
                sequence_step_id=created_step.id,
                status=SequenceStepVariantStatus.ACTIVE,
                created_by_user_id=uuid4(),
                created_at=zoned_utc_now(),
                content=EmailStepContent(
                    type="email",
                    subject="Test 1",
                    body="Body 1",
                    attachment_ids=[],
                ),
                organization_id=self.organization_id,
            )
        )

        await sequence_repository.insert(
            SequenceEnrollmentStepVariantAssociation(
                id=uuid4(),
                sequence_enrollment_id=updated_created_enrollment_contact_1.id,
                sequence_step_id=created_step.id,
                sequence_step_variant_id=created_variant.id,
                organization_id=self.organization_id,
                created_at=zoned_utc_now(),
            ),
        )

        await sequence_repository.insert(
            SequenceStepExecution(
                id=uuid4(),
                organization_id=self.organization_id,
                sequence_id=created_sequence.id,
                sequence_step_id=created_step.id,
                sequence_step_variant_id=created_variant.id,
                sequence_enrollment_id=updated_created_enrollment_contact_1.id,
                contact_id=contact_1.id,
                global_message_id=global_message.id,
                global_thread_id=global_thread.id,
                status=SequenceStepExecutionStatus.QUEUED,
                created_at=zoned_utc_now(),
            )
        )

        return created_sequence

    @pytest.fixture(scope="function")
    async def domain_object_list_1_with_contact_1(
        self,
        faker: Faker,
        contact_1: ContactV2,
        domain_object_list_service: DomainObjectListService,
    ) -> DomainObjectList:
        return await domain_object_list_service.create_domain_object_list(
            organization_id=self.organization_id,
            user_id=self.user_id,
            request=CreateDomainObjectListRequest(
                name=faker.name(),
                description=faker.text(),
                item_type=DomainObjectListItemType.CONTACT,
                origin=DomainObjectListOrigin.USER,
                initial_items=AddItemsRequest(
                    item_ids_to_add={contact_1.id},
                ),
            ),
        )

    @pytest.fixture(scope="function")
    async def domain_object_list_2_with_contact_1_and_contact_2(
        self,
        faker: Faker,
        contact_1: ContactV2,
        contact_2: ContactV2,
        domain_object_list_service: DomainObjectListService,
    ) -> DomainObjectList:
        return await domain_object_list_service.create_domain_object_list(
            organization_id=self.organization_id,
            user_id=self.user_id,
            request=CreateDomainObjectListRequest(
                name=faker.name(),
                description=faker.text(),
                item_type=DomainObjectListItemType.CONTACT,
                origin=DomainObjectListOrigin.USER,
                initial_items=AddItemsRequest(
                    item_ids_to_add={contact_1.id, contact_2.id},
                ),
            ),
        )

    async def execute_merge_contacts_workflow(
        self,
        _engine: DatabaseEngine,
        integrity_job: CRMIntegrityJob,
        wf_env: WorkflowEnvironment,
    ) -> UUID:
        integrity_job_activity = IntegrityJobActivity(db_engine=_engine)
        intelligence_activity = IntelligenceRelatedActivity(db_engine=_engine)
        associate_contact_with_account_activity = AssociateContactWithAccountActivity(
            db_engine=_engine
        )
        unassociate_contact_with_account_activity = (
            UnassociateContactWithAccountActivity(db_engine=_engine)
        )
        contact_related_activities = ContactRelatedActivities(db_engine=_engine)
        associate_contact_with_pipeline_activity = AssociateContactWithPipelineActivity(
            db_engine=_engine
        )
        contact_email_related_activities = ContactEmailRelatedActivity(
            db_engine=_engine
        )
        phone_number_related_activities = PhoneNumberRelatedActivity(db_engine=_engine)

        workflow_entrypoint, workflow_param = get_workflow_entrypoint_and_params(
            integrity_job_name=IntegrityJobName.MERGE_CONTACTS,
            job_id=integrity_job.id,
            retry_count=integrity_job.retry_count,
            src_entity_id=integrity_job.src_entity_id,
            dest_entity_id=integrity_job.dest_entity_id,
            user_id=self.user_id,
            organization_id=self.organization_id,
            user_choices={},
            contextual_param=integrity_job.contextual_param,
        )

        async with (
            Worker(
                wf_env.client,
                task_queue=INTEGRITY_JOB_TASK_QUEUE,
                workflows=[MergeContactsWorkflow],
                activities=[
                    integrity_job_activity.create_integrity_job,
                    integrity_job_activity.update_integrity_job,
                    integrity_job_activity.acquire_locks_for_entities,
                    integrity_job_activity.free_locks_for_entities,
                    intelligence_activity.person_intel_activity_job,
                    intelligence_activity.pipeline_intel_activity_job,
                    contact_related_activities.archive_contact,
                    associate_contact_with_account_activity.copy_contact_associations_from_contact_to_contact,
                    unassociate_contact_with_account_activity.unassociate_single_contact_with_all_accounts,
                    associate_contact_with_pipeline_activity.copy_pipelines_associations_from_contact_to_contact,
                    contact_email_related_activities.move_contact_emails_from_contact_to_contact,
                    phone_number_related_activities.move_contact_phone_numbers_from_contact_to_contact,
                    # use faked notification activity to avoid sending actual msg and overlap event loop issue
                    crm_data_integrity_job_send_notification_activity,
                    data_operation_event_processor_activity,
                ],
                workflow_runner=new_sandbox_runner(),
            ),
            Worker(
                wf_env.client,
                task_queue=AI_TASK_QUEUE,
                debug_mode=True,
                workflows=[
                    MeetingTriggerPipelineIntelWorkflow,
                ],
                activities=[
                    generate_meeting_activity_summary,
                    generate_meeting_how_to_win,
                    generate_meeting_risks,
                    generate_meeting_objections,
                    generate_intel_tasks,
                    upsert_pipeline_intel,
                ],
                workflow_runner=new_sandbox_runner(),
            ),
        ):
            await wf_env.client.execute_workflow(
                workflow_entrypoint,
                workflow_param,
                id=f"test_merge_contacts_workflow_{integrity_job.id}",
                task_queue=INTEGRITY_JOB_TASK_QUEUE,
            )

        return integrity_job.id


class TestMergeContactsWorkflow(MergeContactsWorkflowBaseFixtures):
    async def test_merge_contacts(
        self,
        mocker: MockerFixture,
        _engine: DatabaseEngine,
        contact_service: ContactService,
        pipeline_service: PipelineService,
        email_message_service_ext: MessageServiceExt,
        thread_service_ext: ThreadServiceExt,
        meeting_service: MeetingService,
        meeting_insight_service: MeetingInsightService,
        note_service: NoteService,
        research_agent_service: ResearchAgentService,
        user_calendar_service: UserCalendarService,
        activity_service: ActivityService,
        # integrity job
        integrity_job: CRMIntegrityJob,
        # below are core models
        email_1: str,
        email_2: str,
        phone_number_1: str,
        phone_number_2: str,
        account_1: AccountV2,
        account_2: AccountV2,
        account_3: AccountV2,
        contact_1: ContactV2,
        contact_2: ContactV2,
        pipeline_1: PipelineV2,
        task_1: TaskV2,
        # below are associated models without activities
        # activities are generated in the meeting, email_message, email_thread
        email_message: Message,
        email_thread: Thread,
        meeting: MeetingDto,
        meeting_insight: InsightDTO,
        note: Note,
        voice_call: Call,
        global_message: GlobalMessage,
        global_thread: GlobalThread,
        intel_person_association_contact_1: IntelPersonAssociation,
        intel_person_association_contact_2: IntelPersonAssociation,
        sequence_1_with_contact_1: SequenceV2,
        sequence_2_with_contact_1_and_contact_2: SequenceV2,
        calendar_event: HydratedUserCalendarEventDto,
        task_query_service: TaskQueryService,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
        voice_call_repository: VoiceCallRepository,
        thread_repository: ThreadRepository,
        sequence_enrollment_query_service: SequenceEnrollmentQueryService,
        sequence_repository: SequenceRepository,
        domain_object_list_1_with_contact_1: DomainObjectList,
        domain_object_list_2_with_contact_1_and_contact_2: DomainObjectList,
        domain_object_list_service: DomainObjectListService,
        domain_object_list_query_service: DomainObjectListQueryService,
        pipeline_intel_service: PipelineIntelService,
        wf_env: WorkflowEnvironment,
        integrity_job_subdomains: list[CRMSubDomain],
    ) -> None:
        mock_cancel_scheduled_message_method = mocker.patch(
            "salestech_be.core.email.service.message_service.MessageService.cancel_scheduled_message_by_global_message_id",
            return_value=None,
        )

        await self.execute_merge_contacts_workflow(
            _engine=_engine,
            integrity_job=integrity_job,
            wf_env=wf_env,
        )

        updated_integrity_job = await crm_integrity_job_repository.find_by_primary_key(
            table_model=CRMIntegrityJob,
            id=integrity_job.id,
        )
        assert updated_integrity_job
        assert updated_integrity_job.started_at
        assert updated_integrity_job.ended_at

        assert await crm_integrity_job_repository._find_by_column_values(
            table_model=CRMIntegrityOperation,
            integrity_job_id=integrity_job.id,
            organization_id=self.organization_id,
        )

        assert await crm_integrity_job_repository._find_by_column_values(
            table_model=CRMIntegrityAssociatedEntityOperation,
            integrity_job_id=integrity_job.id,
            organization_id=self.organization_id,
        )

        updated_contact_1, updated_contact_2 = [
            await contact_service.get_contact_v2(
                contact_id=contact_id,
                organization_id=self.organization_id,
            )
            for contact_id in [contact_1.id, contact_2.id]
        ]

        # get_contact_v2 returns archived contacts, so we need to check archived_at here
        assert updated_contact_1.archived_at
        assert updated_contact_2.archived_at is None

        updated_pipeline_1 = await pipeline_service.get_pipeline_by_id(
            pipeline_id=pipeline_1.id,
            organization_id=self.organization_id,
        )

        assert updated_pipeline_1
        assert updated_pipeline_1.account_id == account_1.id

        # check pipeline <> contact association
        contact_to_pipeline_1_associations = (
            await pipeline_service.list_contact_pipeline_associations_by_pipeline_id(
                organization_id=self.organization_id,
                pipeline_id=pipeline_1.id,
            )
        )

        assert len(contact_to_pipeline_1_associations) == 1
        assert {
            association.contact_id for association in contact_to_pipeline_1_associations
        } == {contact_2.id}

        # check account <> contact association
        contact_to_account_1_associations = (
            await contact_service.list_active_contact_associations_for_account(
                organization_id=self.organization_id,
                account_id=account_1.id,
            )
        )

        assert len(contact_to_account_1_associations) == 1
        assert contact_to_account_1_associations[0].contact_id == contact_2.id
        assert contact_to_account_1_associations[0].is_primary

        contact_to_account_2_associations = (
            await contact_service.list_active_contact_associations_for_account(
                organization_id=self.organization_id,
                account_id=account_2.id,
            )
        )
        assert len(contact_to_account_2_associations) == 1
        assert contact_to_account_2_associations[0].contact_id == contact_2.id
        assert not contact_to_account_2_associations[0].is_primary

        contact_to_account_3_associations = (
            await contact_service.list_active_contact_associations_for_account(
                organization_id=self.organization_id,
                account_id=account_3.id,
            )
        )

        assert len(contact_to_account_3_associations) == 1
        assert contact_to_account_3_associations[0].contact_id == contact_2.id
        assert not contact_to_account_3_associations[0].is_primary

        # check contact phone number
        contact_1_contact_phone_numbers = await contact_service.contact_query_service.list_contact_phone_number_dtos_by_contact_id(
            organization_id=self.organization_id, contact_id=contact_1.id
        )
        assert len(contact_1_contact_phone_numbers) == 0

        contact_2_contact_phone_numbers = await contact_service.contact_query_service.list_contact_phone_number_dtos_by_contact_id(
            organization_id=self.organization_id, contact_id=contact_2.id
        )
        assert len(contact_2_contact_phone_numbers) == 2
        assert {
            cpn.contact_phone_number.phone_number
            for cpn in contact_2_contact_phone_numbers
        } == {
            phone_number_1,
            phone_number_2,
        }
        assert (
            contact_2_contact_phone_numbers[0].contact_phone_number_account_associations
            == []
        )
        assert (
            contact_2_contact_phone_numbers[1].contact_phone_number_account_associations
            == []
        )

        # check contact email
        contact_1_contact_emails = (
            await contact_service.list_contact_emails_by_contact_id(
                organization_id=self.organization_id, contact_id=contact_1.id
            )
        )
        assert len(contact_1_contact_emails) == 0
        contact_2_contact_emails = (
            await contact_service.list_contact_emails_by_contact_id(
                organization_id=self.organization_id, contact_id=contact_2.id
            )
        )
        assert len(contact_2_contact_emails) == 2
        assert {
            (ce.contact_id, ce.email, ce.is_contact_primary)
            for ce in contact_2_contact_emails
        } == {
            (contact_2.id, email_1, False),
            (contact_2.id, email_2, True),
        }

        contact_email_1 = next(
            (ce for ce in contact_2_contact_emails if ce.email == email_1), None
        )
        contact_email_2 = next(
            (ce for ce in contact_2_contact_emails if ce.email == email_2), None
        )
        assert contact_email_1
        assert contact_email_2

        # check contact email account associations
        contact_email_1_account_associations = await contact_service.list_contact_email_account_associations_by_contact_email_id(
            organization_id=self.organization_id,
            contact_email_id=contact_email_1.id,
        )
        assert len(contact_email_1_account_associations) == 2
        assert {
            (
                cea.contact_id,
                cea.contact_email_id,
                cea.account_id,
                cea.is_contact_account_primary,
            )
            for cea in contact_email_1_account_associations
        } == {
            (contact_2.id, contact_email_1.id, account_1.id, True),
            (contact_2.id, contact_email_1.id, account_3.id, False),
        }

        contact_email_2_account_associations = await contact_service.list_contact_email_account_associations_by_contact_email_id(
            organization_id=self.organization_id,
            contact_email_id=contact_email_2.id,
        )
        assert len(contact_email_2_account_associations) == 1
        assert {
            (
                cea.contact_id,
                cea.contact_email_id,
                cea.account_id,
                cea.is_contact_account_primary,
            )
            for cea in contact_email_2_account_associations
        } == {
            (contact_2.id, contact_email_2.id, account_3.id, True),
        }

        # check email message
        email_message_by_id = await email_message_service_ext.get_email_message_by_id(
            message_id=email_message.id,
            organization_id=self.organization_id,
        )
        assert email_message_by_id

        assert email_message_by_id.status == MessageStatus.CANCELLED

        send_to_contact_ids = {
            participant.contact_id for participant in email_message_by_id.send_to
        }
        send_from_contact_ids = {
            participant.contact_id for participant in email_message_by_id.send_from
        }
        assert contact_2.id in send_to_contact_ids
        assert contact_1.id not in send_to_contact_ids
        assert contact_2.id in send_from_contact_ids
        assert contact_1.id not in send_from_contact_ids

        # check thread
        thread_by_id = await thread_service_ext.find_thread_by_tenanted_primary_key(
            organization_id=self.organization_id,
            thread_id=email_thread.id,
        )
        assert thread_by_id
        participants_contact_ids = {
            participant.contact_id for participant in thread_by_id.participants
        }
        assert contact_2.id in participants_contact_ids
        assert contact_1.id not in participants_contact_ids

        # check meeting
        get_meeting_by_id = await meeting_service.get_meeting(
            meeting_id=meeting.meeting.id,
            organization_id=self.organization_id,
        )
        assert get_meeting_by_id
        assert get_meeting_by_id.meeting.invitees
        invitees_contact_ids = {
            participant.contact_id for participant in get_meeting_by_id.meeting.invitees
        }
        assert contact_2.id in invitees_contact_ids
        assert contact_1.id not in invitees_contact_ids
        assert get_meeting_by_id.meeting.account_id == account_1.id
        assert get_meeting_by_id.meeting.pipeline_id == pipeline_1.id

        # check insight
        get_insight_section_by_id = (
            await meeting_insight_service.get_insight_section_by_id(
                insight_section_id=meeting_insight.insight_section.id,
                organization_id=self.organization_id,
            )
        )
        assert get_insight_section_by_id
        insights = get_insight_section_by_id.insights
        assert len(insights) == 1
        assert insights[0].contact_id == contact_2.id

        # check note
        get_note_by_id = await note_service.get_by_id(
            note_id=note.id,
            organization_id=self.organization_id,
        )
        assert get_note_by_id
        assert get_note_by_id.contact_ids == [contact_2.id]

        # check intel person association
        contact_1_intel_person_association = (
            await research_agent_service.get_intel_person_association_by_contact_id(
                contact_id=contact_1.id,
            )
        )
        assert contact_1_intel_person_association is None
        contact_2_intel_person_association = (
            await research_agent_service.get_intel_person_association_by_contact_id(
                contact_id=contact_2.id,
            )
        )
        assert contact_2_intel_person_association

        # check calendar event
        list_user_calendar_events_by_calendar = (
            await user_calendar_service.list_user_calendar_events_by_calendar(
                user_id=self.user_id,
                organization_id=self.organization_id,
                request=ListCalendarEventRequest(
                    starts_at_inclusive=calendar_event.user_calendar_event.starts_at,
                    ends_at_exclusive=calendar_event.user_calendar_event.ends_at
                    + timedelta(minutes=1),
                ),
            )
        )
        assert len(list_user_calendar_events_by_calendar) == 1
        calendar_event_participant_contact_ids = {
            participant.contact_id
            for participant in list_user_calendar_events_by_calendar[0].participants
        }
        assert contact_2.id in calendar_event_participant_contact_ids
        assert contact_1.id not in calendar_event_participant_contact_ids

        # check task
        updated_task_1 = await task_query_service.get_by_id_v2(
            task_id=task_1.id,
            organization_id=self.organization_id,
        )

        assert contact_2.id in (updated_task_1.contact_ids or [])
        assert contact_1.id not in (updated_task_1.contact_ids or [])

        # check voice call and voice meeting
        voice_call_by_id = await voice_call_repository.find_by_id(
            call_id=voice_call.id,
        )
        assert voice_call_by_id
        assert voice_call_by_id.contact_id == contact_2.id

        voice_call_meeting_dto = await meeting_service.get_meeting_by_reference_id(
            reference_id=voice_call.id,
            reference_id_type=MeetingReferenceIdType.VOICE_V2,
            organization_id=self.organization_id,
        )
        assert voice_call_meeting_dto.meeting
        assert voice_call_meeting_dto.meeting.account_id == account_1.id
        assert voice_call_meeting_dto.meeting.pipeline_id == pipeline_1.id

        assert voice_call_meeting_dto.meeting.invitees
        contact_invitee = [
            invitee
            for invitee in voice_call_meeting_dto.meeting.invitees
            if invitee.contact_id
        ]
        assert len(contact_invitee) == 1
        assert contact_invitee[0].is_organizer is False
        assert contact_invitee[0].contact_id == contact_2.id
        assert contact_invitee[0].account_id is None
        assert contact_invitee[0].contact_email is None

        # check global thread
        global_thread_by_id = await thread_repository.get_global_thread_by_id(
            global_thread_id=global_thread.id,
            organization_id=self.organization_id,
        )
        assert global_thread_by_id
        assert global_thread_by_id.contact_ids == [contact_2.id]
        assert not global_thread_by_id.account_ids
        assert global_thread_by_id.pipeline_id == pipeline_1.id

        # check sequence enrollment
        sequence_enrollments_by_contact_id_1 = await sequence_enrollment_query_service.find_sequence_enrollments_by_contact_id(
            contact_id=contact_1.id,
            organization_id=self.organization_id,
        )
        assert len(sequence_enrollments_by_contact_id_1) == 1
        assert (
            sequence_enrollments_by_contact_id_1[0].status
            == SequenceEnrollmentStatus.FAILED
        )
        assert (
            sequence_enrollments_by_contact_id_1[0].sequence_id
            == sequence_2_with_contact_1_and_contact_2.id
        )

        sequence_enrollments_by_contact_id_2 = await sequence_enrollment_query_service.find_sequence_enrollments_by_contact_id(
            contact_id=contact_2.id,
            organization_id=self.organization_id,
        )
        assert len(sequence_enrollments_by_contact_id_2) == 2
        assert {
            enrollment.status for enrollment in sequence_enrollments_by_contact_id_2
        } == {SequenceEnrollmentStatus.ACTIVE}
        assert {
            enrollment.sequence_id
            for enrollment in sequence_enrollments_by_contact_id_2
        } == {sequence_1_with_contact_1.id, sequence_2_with_contact_1_and_contact_2.id}

        execution = await sequence_repository._find_unique_by_column_values(
            SequenceStepExecution,
            organization_id=self.organization_id,
            sequence_enrollment_id=sequence_enrollments_by_contact_id_1[0].id,
        )
        assert execution
        assert execution.status == SequenceStepExecutionStatus.TERMINATED

        mock_cancel_scheduled_message_method.assert_awaited_once_with(
            organization_id=self.organization_id,
            global_message_id=global_message.id,
        )

        # check domain object list
        domain_object_list_1_items = (
            await domain_object_list_query_service.list_domain_object_list_items(
                organization_id=self.organization_id,
                list_id=domain_object_list_1_with_contact_1.id,
            )
        )
        assert len(domain_object_list_1_items) == 1
        assert domain_object_list_1_items[0].data.reference_id == contact_2.id

        domain_object_list_2_items = (
            await domain_object_list_query_service.list_domain_object_list_items(
                organization_id=self.organization_id,
                list_id=domain_object_list_2_with_contact_1_and_contact_2.id,
            )
        )
        assert len(domain_object_list_2_items) == 1
        assert domain_object_list_2_items[0].data.reference_id == contact_2.id

        # check activity and activity_sub_reference
        list_activities_by_contact_1 = (
            await activity_service.list_activities_by_contact_ids(
                organization_id=self.organization_id,
                contact_ids=[contact_1.id],
            )
        )
        assert len(list_activities_by_contact_1) == 0
        list_activities_by_contact_2 = (
            await activity_service.list_activities_by_contact_ids(
                organization_id=self.organization_id,
                contact_ids=[contact_2.id],
            )
        )
        # we do not check the exact number of activities here because the activities are
        # generated in the meeting, email_message, email_thread might be changed in the future
        # and more activities on other entities might be added in the future too
        assert len(list_activities_by_contact_2) > 0

        pipeline_intel = await pipeline_intel_service.get_pipeline_intel_by_pipeline_id(
            organization_id=self.organization_id,
            pipeline_id=pipeline_1.id,
        )

        assert pipeline_intel is not None
