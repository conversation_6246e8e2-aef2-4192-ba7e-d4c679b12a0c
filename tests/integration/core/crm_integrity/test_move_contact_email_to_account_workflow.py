import uuid
from collections.abc import Awaitable, Callable
from datetime import time, timedelta
from unittest.mock import AsyncMock, patch
from uuid import UUID, uuid4

import pytest
from faker import Faker
from temporalio.contrib.pydantic import pydantic_data_converter
from temporalio.testing import WorkflowEnvironment
from temporalio.worker import Worker

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdSelectListIdentifier,
)
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.calendar.account.calendar_account_service import (
    CalendarAccountService,
)
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.crm_integrity.atomic_operation_activities.contact_email_related_activity import (
    ContactEmailRelatedActivity,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.integrity_job_activity import (
    IntegrityJobActivity,
)
from salestech_be.core.crm_integrity.data_operation_tracker.integrity_job_service import (
    IntegrityJobService,
)
from salestech_be.core.crm_integrity.event_processors.processor_activity import (
    data_operation_event_processor_activity,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.integrity_job_dispatch_workflow import (
    get_workflow_entrypoint_and_params,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.move_contact_email_to_account_workflow import (
    MoveContactEmailToAccountWorkflow,
)
from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
    get_affected_sub_domains_by_integrity_job_name,
)
from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.global_email.global_thread_query_service import (
    GlobalThreadQueryService,
)
from salestech_be.core.email.pool.schema import EmailAccountPoolResponse
from salestech_be.core.email.pool.service import EmailAccountPoolService
from salestech_be.core.email.service.message_service_ext import MessageServiceExt
from salestech_be.core.email.thread.thread_service_ext import ThreadServiceExt
from salestech_be.core.email.type.email import EmailHydratedParticipant, EmailTag
from salestech_be.core.meeting.dto.meeting_dto import MeetingDto
from salestech_be.core.meeting.meeting_service import MeetingService
from salestech_be.core.metadata.dto.select_list_dto import PipelineStageDto
from salestech_be.core.metadata.dto.service_api_schema import (
    PipelineStageSelectListCreateRequest,
    PipelineStageSelectListValueCreateRequest,
    SelectListValueCreateRequest,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.metadata.service.pipeline_stage_service import (
    PipelineStageSelectListService,
)
from salestech_be.core.pipeline.service.pipeline_service import PipelineService
from salestech_be.core.pipeline.service_api_schema import (
    ContactPipelineAssociationRequest,
    CreatePipelineRequest,
    FullContactPipelineAssociationRequests,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.sequence.service.sequence_enrollment_query_service import (
    SequenceEnrollmentQueryService,
)
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    SequenceEnrollmentService,
)
from salestech_be.core.sequence.service.sequence_service import SequenceService
from salestech_be.core.sequence.type.sequence_enrollment_types import (
    SequenceEnrollmentStatus,
)
from salestech_be.core.sequence.type.sequence_v2 import SequenceV2
from salestech_be.db.dao.calendar_account_repository import CalendarAccountRepository
from salestech_be.db.dao.crm_integrity_repository import CRMIntegrityJobRepository
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dao.sequence_repository import SequenceRepository
from salestech_be.db.dao.template_repository import TemplateRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dao.user_calendar_repository import UserCalendarRepository
from salestech_be.db.dao.user_integration_repository import UserIntegrationRepository
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.user_calendar_event_dto import HydratedUserCalendarEventDto
from salestech_be.db.models.contact import (
    ContactEmail,
    CreateContactAccountRoleRequest,
    CreateContactRequest,
    CreateDbContactEmailAccountAssociationRequest,
    CreateDbContactEmailRequest,
    CreateDbContactRequest,
)
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityJob,
    CRMIntegritySubDomainJob,
    CRMSubDomain,
    EntityType,
    IntegrityJobName,
    IntegrityJobType,
    MoveContactEmailToAccountContextualParam,
)
from salestech_be.db.models.email_account import EmailAccount
from salestech_be.db.models.global_thread import (
    GlobalMessage,
    GlobalMessageAssociation,
    GlobalThread,
)
from salestech_be.db.models.meeting import MeetingProvider, MeetingReferenceIdType
from salestech_be.db.models.message import Message, MessageSource, MessageStatus
from salestech_be.db.models.pipeline import PipelineStatus
from salestech_be.db.models.pipeline_stage_select_list_value_metadata import (
    PipelineOutcomeState,
)
from salestech_be.db.models.sequence import (
    DayOfWeek,
    EmailStepContent,
    SequenceEnrollment,
    SequenceEnrollmentStepVariantAssociation,
    SequenceStepExecution,
    SequenceStepExecutionStatus,
    SequenceStepType,
    SequenceStepV2,
    SequenceStepVariant,
    SequenceStepVariantStatus,
    SequenceVisibility,
)
from salestech_be.db.models.template import Template, TemplateStatus, TemplateType
from salestech_be.db.models.thread import Thread
from salestech_be.db.models.user_calendar import UserCalendar
from salestech_be.integrations.nylas.model import (
    NylasEmailName,
    NylasEvent,
    NylasEventParticipant,
    NylasTimespan,
    Visibility,
)
from salestech_be.integrations.temporal.config import INTEGRITY_JOB_TASK_QUEUE
from salestech_be.temporal.worker import new_sandbox_runner
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.calendar.schema import (
    ContactParticipantRequest,
    CreateCalendarEventRequest,
    EventConferencingRequest,
    ListCalendarEventRequest,
)
from salestech_be.web.api.calendar.service import UserCalendarService
from salestech_be.web.api.meeting.schema import (
    CreateMeetingRequest,
    Invitee,
)
from salestech_be.web.api.sequence.enrollment.schema import (
    ContactForSequenceEnrollment,
    CreateSequenceEnrollmentRequest,
)
from salestech_be.web.api.sequence.schema import (
    CreateSequenceRequest,
    ScheduleConfig,
    ScheduleTime,
)
from tests.integration.core.calendar.test_user_calendar_webhook_service import (
    create_calendar_account_and_user_integration,
)
from tests.integration.core.crm_integrity.mocked_activity import (
    crm_data_integrity_job_send_notification_activity,
)


class MoveContactEmailToAccountWorkflowBaseFixtures:
    @pytest.fixture(autouse=True, scope="function")
    async def setup(
        self, make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]]
    ) -> None:
        self.user_id, self.organization_id = await make_user_org()

    @pytest.fixture(scope="function")
    async def pipeline_stage_dto(
        self,
        pipeline_stage_select_list_service: PipelineStageSelectListService,
        faker: Faker,
    ) -> PipelineStageDto:
        return (
            await pipeline_stage_select_list_service.create_pipeline_stage_select_list(
                organization_id=self.organization_id,
                actioning_user_id=self.user_id,
                req=PipelineStageSelectListCreateRequest(
                    display_name=faker.company(),
                    description=faker.text(),
                    is_default=True,
                    value_reqs=(
                        PipelineStageSelectListValueCreateRequest(
                            pipeline_status=PipelineStatus.PROSPECT,
                            select_list_value_req=SelectListValueCreateRequest(
                                api_name="starting",
                                display_value="Starting",
                                is_default=True,
                            ),
                        ),
                        PipelineStageSelectListValueCreateRequest(
                            pipeline_status=PipelineStatus.PROSPECT,
                            select_list_value_req=SelectListValueCreateRequest(
                                api_name="prospect",
                                display_value="Prospect",
                                is_default=False,
                            ),
                        ),
                        PipelineStageSelectListValueCreateRequest(
                            pipeline_status=PipelineStatus.DEAL,
                            outcome_state=PipelineOutcomeState.CLOSED_LOST,
                            select_list_value_req=SelectListValueCreateRequest(
                                api_name="closed_lost",
                                display_value="Closed Lost",
                                is_default=False,
                            ),
                        ),
                        PipelineStageSelectListValueCreateRequest(
                            pipeline_status=PipelineStatus.DEAL,
                            outcome_state=PipelineOutcomeState.CLOSED_WON,
                            select_list_value_req=SelectListValueCreateRequest(
                                api_name="closed_won",
                                display_value="Closed Won",
                                is_default=False,
                            ),
                        ),
                    ),
                ),
            )
        )

    @pytest.fixture(scope="function")
    async def email_1(
        self,
        faker: Faker,
    ) -> str:
        return faker.email()

    @pytest.fixture(scope="function")
    async def email_2(
        self,
        faker: Faker,
    ) -> str:
        return faker.email()

    @pytest.fixture(scope="function")
    async def account_1(
        self, faker: Faker, account_service: AccountService
    ) -> AccountV2:
        return await account_service.create_account_v2(
            create_account_request=CreateAccountRequest(
                display_name="account_1",
                owner_user_id=self.user_id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def account_2(
        self, faker: Faker, account_service: AccountService
    ) -> AccountV2:
        return await account_service.create_account_v2(
            create_account_request=CreateAccountRequest(
                display_name="account_2",
                owner_user_id=self.user_id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def contact_1(
        self,
        faker: Faker,
        account_1: AccountV2,
        account_2: AccountV2,
        email_1: str,
        email_2: str,
        contact_service: ContactService,
        select_list_service: InternalSelectListService,
    ) -> ContactV2:
        default_stage_list = await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=self.organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        assert default_stage_list and default_stage_list.default_or_initial_active_value
        created_contact = await contact_service.create_contact_with_contact_channels(
            organization_id=self.organization_id,
            user_id=self.user_id,
            create_contact_with_contact_channel_request=CreateContactRequest(
                contact=CreateDbContactRequest(
                    first_name=faker.first_name(),
                    last_name=faker.last_name(),
                    display_name="contact_1",
                    owner_user_id=self.user_id,
                    created_by_user_id=self.user_id,
                    stage_id=default_stage_list.default_or_initial_active_value.id,
                ),
                contact_emails=[
                    CreateDbContactEmailRequest(
                        email=email_1,
                        is_contact_primary=True,
                        email_account_associations=[
                            CreateDbContactEmailAccountAssociationRequest(
                                account_id=account_1.id,
                                is_contact_account_primary=True,
                            )
                        ],
                    ),
                    CreateDbContactEmailRequest(
                        email=email_2,
                        is_contact_primary=False,
                        email_account_associations=[
                            CreateDbContactEmailAccountAssociationRequest(
                                account_id=account_2.id,
                                is_contact_account_primary=False,
                            )
                        ],
                    ),
                ],
                contact_account_roles=[
                    CreateContactAccountRoleRequest(
                        account_id=account_1.id, is_primary_account=True
                    ),
                    CreateContactAccountRoleRequest(
                        account_id=account_2.id, is_primary_account=False
                    ),
                ],
            ),
        )

        contact_account_1_associations = (
            await contact_service.list_active_contact_account_associations(
                organization_id=self.organization_id,
                contact_id=created_contact.id,
                account_id=account_1.id,
            )
        )
        assert len(contact_account_1_associations) == 1
        assert contact_account_1_associations[0].is_primary

        contact_account_2_associations = (
            await contact_service.list_active_contact_account_associations(
                organization_id=self.organization_id,
                contact_id=created_contact.id,
                account_id=account_2.id,
            )
        )
        assert len(contact_account_2_associations) == 1
        assert not contact_account_2_associations[0].is_primary

        created_contact_email_1 = await contact_service.find_contact_email_by_email(
            organization_id=self.organization_id,
            email=email_1,
        )
        assert created_contact_email_1
        assert created_contact_email_1.is_contact_primary

        created_contact_email_2 = await contact_service.find_contact_email_by_email(
            organization_id=self.organization_id,
            email=email_2,
        )
        assert created_contact_email_2
        assert not created_contact_email_2.is_contact_primary

        contact_email_1_account_1_association = await contact_service.list_contact_email_account_associations_by_contact_email_id(
            organization_id=self.organization_id,
            contact_email_id=created_contact_email_1.id,
        )
        assert len(contact_email_1_account_1_association) == 1
        assert contact_email_1_account_1_association[0].is_contact_account_primary

        contact_email_2_account_2_association = await contact_service.list_contact_email_account_associations_by_contact_email_id(
            organization_id=self.organization_id,
            contact_email_id=created_contact_email_2.id,
        )
        assert len(contact_email_2_account_2_association) == 1
        assert contact_email_2_account_2_association[0].is_contact_account_primary

        return created_contact

    @pytest.fixture(scope="function")
    async def contact_email_1(
        self,
        contact_1: ContactV2,
        email_1: str,
        contact_service: ContactService,
    ) -> ContactEmail:
        contact_email_1 = await contact_service.find_contact_email_by_email(
            organization_id=self.organization_id,
            email=email_1,
        )
        assert contact_email_1
        return contact_email_1

    @pytest.fixture(scope="function")
    async def contact_email_2(
        self,
        contact_1: ContactV2,
        email_2: str,
        contact_service: ContactService,
    ) -> ContactEmail:
        contact_email_2 = await contact_service.find_contact_email_by_email(
            organization_id=self.organization_id,
            email=email_2,
        )
        assert contact_email_2
        return contact_email_2

    @pytest.fixture(scope="function")
    async def pipeline_1(
        self,
        faker: Faker,
        pipeline_service: PipelineService,
        account_1: AccountV2,
        contact_1: ContactV2,
        pipeline_stage_dto: PipelineStageDto,
    ) -> PipelineV2:
        return await pipeline_service.create_pipeline(
            req=CreatePipelineRequest(
                display_name=faker.company(),
                account_id=account_1.id,
                owner_user_id=self.user_id,
                contact_pipeline_associations=FullContactPipelineAssociationRequests(
                    primary=ContactPipelineAssociationRequest(
                        contact_id=contact_1.id,
                    )
                ),
                stage_id=pipeline_stage_dto.select_list_value_dtos[
                    0
                ].select_list_value.id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def mock_nylas_event(self) -> NylasEvent:
        return NylasEvent(
            id="test_nylas_event_id",
            grant_id="test_grant_id",
            calendar_id="test_calendar_id",
            when=NylasTimespan(
                start_timezone="America/Los_Angeles",
                end_timezone="America/Los_Angeles",
                object="timespan",
                start_time=int(zoned_utc_now().timestamp()),
                end_time=int(zoned_utc_now().timestamp()),
            ),
            html_link="http://test.test",
            busy=False,
            participants=[
                NylasEventParticipant(email="<EMAIL>", status="noreply")
            ],
            conferencing={
                "provider": "Google Meet",
                "details": {"url": "http://test.test"},
            },
            object="event",
            visibility=Visibility.private,
            description="test description",
            ical_uid="ical_uid",
            title="test title",
            organizer=NylasEmailName(
                email="<EMAIL>",
                name="test user",
            ),
            status="confirmed",
            created_at=int(zoned_utc_now().timestamp()),
            updated_at=int(zoned_utc_now().timestamp()),
        )

    @pytest.fixture(scope="function")
    async def calendar_event(
        self,
        faker: Faker,
        contact_1: ContactV2,
        account_1: AccountV2,
        contact_email_1: ContactEmail,
        user_calendar_service: UserCalendarService,
        user_repository: UserRepository,
        user_calendar_repository: UserCalendarRepository,
        calendar_account_service: CalendarAccountService,
        calendar_account_repository: CalendarAccountRepository,
        user_integration_repository: UserIntegrationRepository,
        mock_nylas_event: NylasEvent,
    ) -> HydratedUserCalendarEventDto:
        (
            calendar_account,
            user_integration_dto,
        ) = await create_calendar_account_and_user_integration(
            user_id=self.user_id,
            organization_id=self.organization_id,
            calendar_account_repository=calendar_account_repository,
            user_integration_repository=user_integration_repository,
        )
        await user_calendar_repository.insert(
            UserCalendar(
                id=uuid4(),
                user_id=self.user_id,
                organization_id=self.organization_id,
                user_integration_id=user_integration_dto.user_integration.id,
                calendar_account_id=calendar_account.id,
                name="test_calendar",
                timezone="America/Los_Angeles",
                external_id="test_calendar_id",
                is_primary=True,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )
        user = await user_repository.find_user_by_id_and_organization_id(
            user_id=self.user_id,
            organization_id=self.organization_id,
        )
        mock_nylas_event.participants = [
            NylasEventParticipant(
                email=not_none(contact_1.primary_email), status="noreply"
            )
        ]
        with patch(
            "salestech_be.integrations.nylas.async_nylas_client.AsyncNylasClient.create_event",
            new_callable=AsyncMock,
            return_value=mock_nylas_event,
        ):
            return await user_calendar_service.create_calendar_event(
                user=not_none(user),
                organization_id=self.organization_id,
                request=CreateCalendarEventRequest(
                    starts_at=zoned_utc_now(),
                    ends_at=zoned_utc_now() + timedelta(hours=1),
                    title=faker.sentence(),
                    description=faker.text(),
                    conferencing=EventConferencingRequest(
                        conferencing_provider="Google Meet"
                    ),
                    participants=[
                        ContactParticipantRequest(
                            participant_type="contact",
                            contact_id=contact_1.id,
                            account_id=account_1.id,
                            contact_email=contact_email_1.email,
                        )
                    ],
                    is_busy=True,
                    notify_all_participants=True,
                    enable_recording=True,
                    use_consent_link=True,
                    visibility=Visibility.private,
                ),
            )

    @pytest.fixture(scope="function")
    async def meeting(
        self,
        contact_1: ContactV2,
        account_1: AccountV2,
        contact_email_1: ContactEmail,
        calendar_event: HydratedUserCalendarEventDto,
        meeting_service: MeetingService,
        faker: Faker,
    ) -> MeetingDto:
        user_invitee = Invitee(user_id=self.user_id, is_organizer=True)
        contact_invitee = Invitee(
            contact_id=contact_1.id,
            is_organizer=False,
            email=contact_email_1.email,
            account_id=account_1.id,
        )
        create_meeting_request = CreateMeetingRequest(
            platform=MeetingProvider.ZOOM,
            reference_id=calendar_event.user_calendar_event.id,
            reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
            starts_at=zoned_utc_now(),
            title=faker.sentence(),
            event_conferencing=None,
            invitees=[user_invitee, contact_invitee],
        )
        return await meeting_service.create_meeting(
            organization_id=self.organization_id,
            user_id=self.user_id,
            request=create_meeting_request,
        )

    @pytest.fixture(scope="function")
    async def email_account(
        self,
        faker: Faker,
        email_account_service_ext: EmailAccountServiceExt,
    ) -> EmailAccount:
        return await email_account_service_ext.get_or_create_email_account(
            owner_user_id=self.user_id,
            email=faker.email(),
            signature_html=faker.text(),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def email_template(
        self,
        faker: Faker,
        template_repository: TemplateRepository,
    ) -> Template:
        template = Template(
            id=uuid.uuid4(),
            name=faker.sentence(),
            template_type=TemplateType.EMAIL,
            status=TemplateStatus.ACTIVE,
            body_text="",
            body_html="",
            is_shared=False,
            version=1,
            include_email_signature=True,
            unsubscription_group_id=None,
            organization_id=self.organization_id,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
        return await template_repository.insert(template)

    @pytest.fixture(scope="function")
    async def email_thread(
        self,
        contact_1: ContactV2,
        account_1: AccountV2,
        contact_email_1: ContactEmail,
        email_account: EmailAccount,
        thread_repository: ThreadRepository,
        email_template: Template,
    ) -> Thread:
        email_recipient = EmailHydratedParticipant(
            email=contact_email_1.email,
            name="test recipient",
            contact_id=contact_1.id,
            account_id=account_1.id,
        )
        email_sender = EmailHydratedParticipant(
            email=email_account.email,
            name="test sender",
            email_account_id=email_account.id,
        )

        return await thread_repository.insert(
            Thread(
                id=uuid.uuid4(),
                email_account_id=not_none(email_sender.email_account_id),
                organization_id=self.organization_id,
                subject=email_template.subject or "",
                snippet=email_template.body_text[:100],
                unread=None,
                starred=None,
                has_attachments=bool(email_template.attachment_ids),
                has_drafts=None,
                earliest_message_date=zoned_utc_now(),
                latest_message_sent_date=None,
                latest_message_received_date=None,
                participants=[email_sender, email_recipient],
                tags=[EmailTag.OUTBOUND],
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
                deleted_at=None,
            )
        )

    @pytest.fixture(scope="function")
    async def email_message(
        self,
        contact_1: ContactV2,
        contact_email_1: ContactEmail,
        account_1: AccountV2,
        email_thread: Thread,
        message_repository: MessageRepository,
        email_account: EmailAccount,
        email_template: Template,
    ) -> Message:
        email_recipient = EmailHydratedParticipant(
            email=contact_email_1.email,
            name="test recipient",
            contact_id=contact_1.id,
            account_id=account_1.id,
        )
        email_sender = EmailHydratedParticipant(
            email=email_account.email,
            name="test sender",
            contact_id=contact_1.id,
            email_account_id=email_account.id,
        )

        return await message_repository.insert(
            Message(
                id=uuid4(),
                thread_id=email_thread.id,
                idempotency_key=str(uuid4()),
                source=MessageSource.SEQUENCE,
                email_account_id=not_none(email_sender.email_account_id),
                organization_id=self.organization_id,
                template_id=email_template.id,
                email_template_version=email_template.version,
                subject=email_template.subject or "",
                send_from=[email_sender],
                send_to=[email_recipient],
                cc=email_template.cc,
                bcc=email_template.bcc,
                reply_to=email_template.reply_to,
                reply_to_message_id=None,
                status=MessageStatus.SCHEDULED,
                snippet=email_template.body_text[:100],
                body_text=email_template.body_text,
                body_html=email_template.body_html,
                attachment_ids=email_template.attachment_ids,
                folders=None,
                starred=None,
                unread=None,
                use_draft=None,
                send_at=zoned_utc_now(),
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )

    @pytest.fixture(scope="function")
    async def global_thread(
        self,
        faker: Faker,
        contact_1: ContactV2,
        account_1: AccountV2,
        pipeline_1: PipelineV2,
        email_thread: Thread,
        thread_repository: ThreadRepository,
    ) -> GlobalThread:
        return await thread_repository.insert(
            GlobalThread(
                id=uuid4(),
                subject=faker.sentence(),
                snippet=faker.text(),
                thread_ids=[email_thread.id],
                contact_ids=[contact_1.id],
                account_ids=[account_1.id],
                pipeline_id=pipeline_1.id,
                organization_id=self.organization_id,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )

    @pytest.fixture(scope="function")
    async def global_message(
        self,
        global_thread: GlobalThread,
        email_message: Message,
        thread_repository: ThreadRepository,
    ) -> GlobalMessage:
        created_global_message = await thread_repository.insert(
            GlobalMessage(
                id=uuid4(),
                original_message_id="test_original_message_id",
                organization_id=self.organization_id,
                global_thread_id=global_thread.id,
                message_received_at=zoned_utc_now(),
                created_at=zoned_utc_now(),
            )
        )

        await thread_repository.insert(
            GlobalMessageAssociation(
                id=uuid4(),
                global_message_id=created_global_message.id,
                message_id=email_message.id,
                organization_id=self.organization_id,
                created_at=zoned_utc_now(),
            )
        )

        return created_global_message

    @pytest.fixture(scope="function")
    async def email_account_pool(
        self,
        email_account_pool_service: EmailAccountPoolService,
    ) -> EmailAccountPoolResponse:
        return (
            await email_account_pool_service.get_or_create_default_email_account_pool(
                organization_id=self.organization_id,
                user_id=self.user_id,
            )
        )

    @pytest.fixture(scope="function")
    async def sequence(
        self,
        faker: Faker,
        email_1: str,
        contact_1: ContactV2,
        account_1: AccountV2,
        global_message: GlobalMessage,
        global_thread: GlobalThread,
        email_account_pool: EmailAccountPoolResponse,
        sequence_service: SequenceService,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_query_service: SequenceEnrollmentQueryService,
        sequence_repository: SequenceRepository,
    ) -> SequenceV2:
        created_sequence = await sequence_service._create_sequence(
            organization_id=self.organization_id,
            user_id=self.user_id,
            request=CreateSequenceRequest(
                name=faker.name(),
                description=faker.text(),
                visibility=SequenceVisibility.TEAM_EDITABLE,
                schedule=ScheduleConfig(
                    timezone="America/New_York",
                    skip_holidays=False,
                    schedule_times=[
                        ScheduleTime(
                            day_of_week=DayOfWeek.MON,
                            start_time=time(hour=9, minute=0),
                            end_time=time(hour=17, minute=0),
                        ),
                    ],
                ),
            ),
            is_blueprint=False,
        )

        created_enrollment_contact_response = (
            await sequence_enrollment_service.create_sequence_enrollment(
                user_auth_context=UserAuthContext(
                    user_id=self.user_id,
                    organization_id=self.organization_id,
                ),
                request=CreateSequenceEnrollmentRequest(
                    sequence_id=created_sequence.id,
                    contacts=[
                        ContactForSequenceEnrollment(
                            contact_id=contact_1.id,
                            account_id=account_1.id,
                            email=email_1,
                        )
                    ],
                ),
            )
        )

        assert len(created_enrollment_contact_response.failed_enrollments) == 0

        created_enrollment_contact_1 = await sequence_enrollment_query_service.find_sequence_enrollments_by_contact_id(
            contact_id=contact_1.id,
            organization_id=self.organization_id,
            sequence_id=created_sequence.id,
        )

        assert len(created_enrollment_contact_1) == 1

        created_step = await sequence_repository.insert(
            SequenceStepV2(
                id=uuid4(),
                name=faker.name(),
                sequence_id=created_sequence.id,
                is_first_step=True,
                next_step_id=None,
                delay_minutes=0,
                type=SequenceStepType.AUTO_EMAIL,
                support_ab_test=True,
                organization_id=self.organization_id,
                created_by_user_id=self.user_id,
                created_at=zoned_utc_now(),
            )
        )

        updated_created_enrollment_contact_1 = (
            await sequence_repository.update_by_tenanted_primary_key(
                table_model=SequenceEnrollment,
                primary_key_to_value={"id": created_enrollment_contact_1[0].id},
                organization_id=self.organization_id,
                column_to_update={"current_step_id": created_step.id},
            )
        )

        assert updated_created_enrollment_contact_1

        created_variant = await sequence_repository.insert(
            SequenceStepVariant(
                id=uuid4(),
                name=faker.name(),
                sequence_step_id=created_step.id,
                status=SequenceStepVariantStatus.ACTIVE,
                created_by_user_id=uuid4(),
                created_at=zoned_utc_now(),
                content=EmailStepContent(
                    type="email",
                    subject="Test 1",
                    body="Body 1",
                    attachment_ids=[],
                ),
                organization_id=self.organization_id,
            )
        )

        await sequence_repository.insert(
            SequenceEnrollmentStepVariantAssociation(
                id=uuid4(),
                sequence_enrollment_id=updated_created_enrollment_contact_1.id,
                sequence_step_id=created_step.id,
                sequence_step_variant_id=created_variant.id,
                organization_id=self.organization_id,
                created_at=zoned_utc_now(),
            ),
        )

        await sequence_repository.insert(
            SequenceStepExecution(
                id=uuid4(),
                organization_id=self.organization_id,
                sequence_id=created_sequence.id,
                sequence_step_id=created_step.id,
                sequence_step_variant_id=created_variant.id,
                sequence_enrollment_id=updated_created_enrollment_contact_1.id,
                contact_id=contact_1.id,
                global_message_id=global_message.id,
                global_thread_id=global_thread.id,
                status=SequenceStepExecutionStatus.QUEUED,
                created_at=zoned_utc_now(),
            )
        )

        return created_sequence

    @pytest.fixture(scope="function")
    def integrity_job_subdomains(
        self,
    ) -> list[CRMSubDomain]:
        return get_affected_sub_domains_by_integrity_job_name(
            integrity_job_name=IntegrityJobName.MOVE_CONTACT_EMAIL_TO_ACCOUNT,
        )

    @pytest.fixture(scope="function")
    async def integrity_job(
        self,
        integrity_job_service: IntegrityJobService,
        contact_1: ContactV2,
        email_1: str,
        email_2: str,
        account_1: AccountV2,
        account_2: AccountV2,
        contact_email_1: ContactEmail,
        contact_email_2: ContactEmail,
        contact_service: ContactService,
        integrity_job_subdomains: list[CRMSubDomain],
    ) -> CRMIntegrityJob:
        integrity_job, _ = await integrity_job_service.create_integrity_job(
            user_id=self.user_id,
            organization_id=self.organization_id,
            job_type=IntegrityJobType.MOVE,
            src_entity_type=EntityType.CONTACT_EMAIL,
            src_entity_id=contact_email_1.id,
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=account_2.id,
            contextual_param=MoveContactEmailToAccountContextualParam(
                contact_id=contact_1.id,
                src_account_id=account_1.id,
            ),
            user_choices=dict.fromkeys(integrity_job_subdomains),
        )

        return integrity_job

    async def execute_move_contact_email_to_account_workflow(
        self,
        _engine: DatabaseEngine,
        integrity_job: CRMIntegrityJob,
    ) -> UUID:
        integrity_job_activity = IntegrityJobActivity(db_engine=_engine)
        contact_email_related_activity = ContactEmailRelatedActivity(db_engine=_engine)

        workflow_entrypoint, workflow_param = get_workflow_entrypoint_and_params(
            integrity_job_name=IntegrityJobName.MOVE_CONTACT_EMAIL_TO_ACCOUNT,
            job_id=integrity_job.id,
            retry_count=integrity_job.retry_count,
            src_entity_id=integrity_job.src_entity_id,
            dest_entity_id=integrity_job.dest_entity_id,
            user_id=self.user_id,
            organization_id=self.organization_id,
            user_choices={},
            contextual_param=integrity_job.contextual_param,
        )

        async with (
            await WorkflowEnvironment.start_time_skipping(
                data_converter=pydantic_data_converter
            ) as env,
            Worker(
                env.client,
                task_queue=INTEGRITY_JOB_TASK_QUEUE,
                workflows=[MoveContactEmailToAccountWorkflow],
                activities=[
                    integrity_job_activity.create_integrity_job,
                    integrity_job_activity.update_integrity_job,
                    integrity_job_activity.acquire_locks_for_entities,
                    integrity_job_activity.free_locks_for_entities,
                    contact_email_related_activity.move_contact_email_to_account,
                    # use faked notification activity to avoid sending actual msg and overlap event loop issue
                    crm_data_integrity_job_send_notification_activity,
                    data_operation_event_processor_activity,
                ],
                workflow_runner=new_sandbox_runner(),
            ),
        ):
            await env.client.execute_workflow(
                workflow_entrypoint,
                workflow_param,
                id=f"test_move_contact_email_to_account_workflow_{integrity_job.id}",
                task_queue=INTEGRITY_JOB_TASK_QUEUE,
            )

        return integrity_job.id


class TestMoveContactEmailToAccountWorkflow(
    MoveContactEmailToAccountWorkflowBaseFixtures
):
    async def test_move_contact_email_to_account_workflow(
        self,
        _engine: DatabaseEngine,
        contact_1: ContactV2,
        email_1: str,
        email_2: str,
        account_1: AccountV2,
        account_2: AccountV2,
        pipeline_1: PipelineV2,
        contact_email_1: ContactEmail,
        contact_email_2: ContactEmail,
        integrity_job: CRMIntegrityJob,
        contact_service: ContactService,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
        sequence_enrollment_query_service: SequenceEnrollmentQueryService,
        sequence_repository: SequenceRepository,
        email_message_service_ext: MessageServiceExt,
        email_message: Message,
        sequence: SequenceV2,
        global_thread_query_service: GlobalThreadQueryService,
        thread_service_ext: ThreadServiceExt,
        email_thread: Thread,
        global_thread: GlobalThread,
        meeting: MeetingDto,
        meeting_service: MeetingService,
        calendar_event: HydratedUserCalendarEventDto,
        user_calendar_service: UserCalendarService,
        integrity_job_subdomains: list[CRMSubDomain],
    ) -> None:
        await self.execute_move_contact_email_to_account_workflow(
            _engine=_engine,
            integrity_job=integrity_job,
        )

        updated_integrity_job = await crm_integrity_job_repository.find_by_primary_key(
            table_model=CRMIntegrityJob,
            id=integrity_job.id,
        )

        subdomain_jobs = await crm_integrity_job_repository._find_by_column_values(
            table_model=CRMIntegritySubDomainJob,
            integrity_job_id=integrity_job.id,
            organization_id=self.organization_id,
        )

        assert len(subdomain_jobs) == len(
            get_affected_sub_domains_by_integrity_job_name(
                integrity_job_name=IntegrityJobName.MOVE_CONTACT_EMAIL_TO_ACCOUNT,
            )
        )

        for subdomain_job in subdomain_jobs:
            assert subdomain_job.user_choice is None
        assert updated_integrity_job
        assert updated_integrity_job.started_at
        assert updated_integrity_job.ended_at

        contact_account_1_associations = (
            await contact_service.list_active_contact_account_associations(
                organization_id=self.organization_id,
                contact_id=contact_1.id,
                account_id=account_1.id,
            )
        )
        assert len(contact_account_1_associations) == 1
        assert contact_account_1_associations[0].is_primary

        contact_account_2_associations = (
            await contact_service.list_active_contact_account_associations(
                organization_id=self.organization_id,
                contact_id=contact_1.id,
                account_id=account_2.id,
            )
        )
        assert len(contact_account_2_associations) == 1
        assert not contact_account_2_associations[0].is_primary

        # expect no changes on contact emails
        created_contact_email_1 = await contact_service.find_contact_email_by_email(
            organization_id=self.organization_id,
            email=email_1,
        )
        assert created_contact_email_1
        assert created_contact_email_1.is_contact_primary

        created_contact_email_2 = await contact_service.find_contact_email_by_email(
            organization_id=self.organization_id,
            email=email_2,
        )
        assert created_contact_email_2
        assert not created_contact_email_2.is_contact_primary

        contact_email_1_account_1_association = await contact_service.list_contact_email_account_associations_by_contact_email_id(
            organization_id=self.organization_id,
            contact_email_id=created_contact_email_1.id,
        )
        assert len(contact_email_1_account_1_association) == 1
        assert contact_email_1_account_1_association[0].contact_id == contact_1.id
        assert contact_email_1_account_1_association[0].account_id == account_2.id
        assert not contact_email_1_account_1_association[0].is_contact_account_primary

        contact_email_2_account_2_association = await contact_service.list_contact_email_account_associations_by_contact_email_id(
            organization_id=self.organization_id,
            contact_email_id=created_contact_email_2.id,
        )
        assert len(contact_email_2_account_2_association) == 1
        assert contact_email_2_account_2_association[0].contact_id == contact_1.id
        assert contact_email_2_account_2_association[0].account_id == account_2.id
        assert contact_email_2_account_2_association[0].is_contact_account_primary

        # check sequence enrollment
        sequence_enrollment = await sequence_enrollment_query_service.find_sequence_enrollments_by_contact_id(
            contact_id=contact_1.id,
            organization_id=self.organization_id,
            account_id=account_1.id,
            email=email_1,
        )
        assert len(sequence_enrollment) == 1
        assert sequence_enrollment[0].status == SequenceEnrollmentStatus.FAILED
        assert sequence_enrollment[0].sequence_id == sequence.id

        seqeunce_step_execution = (
            await sequence_repository._find_unique_by_column_values(
                SequenceStepExecution,
                organization_id=self.organization_id,
                sequence_enrollment_id=sequence_enrollment[0].id,
            )
        )

        assert seqeunce_step_execution
        assert seqeunce_step_execution.status == SequenceStepExecutionStatus.TERMINATED

        updated_email_message = await email_message_service_ext.get_email_message_by_id(
            message_id=email_message.id,
            organization_id=self.organization_id,
        )
        assert updated_email_message
        assert updated_email_message.status == MessageStatus.CANCELLED

        # check message, thread, global_thread
        assert len(updated_email_message.send_to) == 1
        assert updated_email_message.send_to[0].account_id == account_2.id
        assert updated_email_message.send_to[0].contact_id == contact_1.id
        assert updated_email_message.send_to[0].email == contact_email_1.email

        updated_email_thread = (
            await thread_service_ext.find_thread_by_tenanted_primary_key(
                organization_id=self.organization_id,
                thread_id=email_thread.id,
            )
        )
        assert updated_email_thread
        assert len(updated_email_thread.participants) == 2

        contact_1_participant = next(
            (
                p
                for p in updated_email_thread.participants
                if p.contact_id == contact_1.id
            ),
            None,
        )
        assert contact_1_participant
        assert contact_1_participant.account_id == account_2.id
        assert contact_1_participant.email == contact_email_1.email

        # check global thread
        global_thread_list = await global_thread_query_service.list_global_threads(
            organization_id=self.organization_id,
            user_id=self.user_id,
            only_include_thread_for_contacts={contact_1.id},
        )

        assert len(global_thread_list) == 1
        assert global_thread_list[0].id == global_thread.id
        assert global_thread_list[0].account_ids == [account_2.id]
        assert global_thread_list[0].pipeline_id == pipeline_1.id

        # check meeting
        get_meeting_by_id = await meeting_service.get_meeting(
            meeting_id=meeting.meeting.id,
            organization_id=self.organization_id,
        )
        assert get_meeting_by_id
        assert get_meeting_by_id.meeting.invitees
        contact_invitee = next(
            (
                participant
                for participant in get_meeting_by_id.meeting.invitees
                if participant.contact_id == contact_1.id
            ),
            None,
        )
        assert contact_invitee
        assert contact_invitee.account_id == account_2.id
        assert contact_invitee.contact_email == contact_email_1.email
        assert get_meeting_by_id.meeting.account_id == account_2.id
        assert get_meeting_by_id.meeting.pipeline_id == pipeline_1.id

        # check calendar event
        list_user_calendar_events_by_calendar = (
            await user_calendar_service.list_user_calendar_events_by_calendar(
                user_id=self.user_id,
                organization_id=self.organization_id,
                request=ListCalendarEventRequest(
                    starts_at_inclusive=calendar_event.user_calendar_event.starts_at,
                    ends_at_exclusive=calendar_event.user_calendar_event.ends_at
                    + timedelta(minutes=1),
                ),
            )
        )
        assert len(list_user_calendar_events_by_calendar) == 1
        contact_participant = next(
            (
                participant
                for participant in list_user_calendar_events_by_calendar[0].participants
                if participant.contact_id == contact_1.id
            ),
            None,
        )
        assert contact_participant
        assert contact_participant.account_id == account_2.id
        assert contact_participant.email == contact_email_1.email
