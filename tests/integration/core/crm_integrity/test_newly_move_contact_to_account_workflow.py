import uuid
from collections.abc import AsyncGenerator, Awaitable, Callable
from datetime import time, timedelta
from unittest.mock import AsyncMock, patch
from uuid import UUID, uuid4

import pytest
from faker import Faker
from temporalio.testing import WorkflowEnvironment
from temporalio.worker import Worker

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdSelectListIdentifier,
)
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.activity.service.activity_service import (
    ActivityService,
)
from salestech_be.core.ai.activities.meeting_pipeline_intel_activities import (
    generate_meeting_activity_summary,
    generate_meeting_how_to_win,
    generate_meeting_objections,
    generate_meeting_risks,
    upsert_pipeline_intel,
)
from salestech_be.core.ai.tasks.activities.generate_intel_tasks import (
    generate_intel_tasks,
)
from salestech_be.core.ai.workflows.trigger_pipeline_intel import (
    MeetingTriggerPipelineIntelWorkflow,
)
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.crm_integrity.atomic_operation_activities.associate_contact_with_account_activity import (
    AssociateContactWithAccountActivity,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.contact_email_related_activity import (
    ContactEmailRelatedActivity,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.integrity_job_activity import (
    IntegrityJobActivity,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.intelligence_related_activity import (
    IntelligenceRelatedActivity,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.move_pipeline_to_alternative_contact_activity import (
    MovePipelineToAlternativeContactActivity,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.unassociate_contact_with_account_activity import (
    UnassociateContactWithAccountActivity,
)
from salestech_be.core.crm_integrity.data_operation_tracker.integrity_job_service import (
    IntegrityJobService,
)
from salestech_be.core.crm_integrity.event_processors.processor_activity import (
    data_operation_event_processor_activity,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.integrity_job_dispatch_workflow import (
    get_workflow_entrypoint_and_params,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.move_contact_to_account_workflow import (
    MoveContactToAccountWorkflow,
)
from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
    get_affected_sub_domains_by_integrity_job_name,
)
from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.global_email.global_thread_query_service import (
    GlobalThreadQueryService,
)
from salestech_be.core.email.pool.service import EmailAccountPoolService
from salestech_be.core.email.service.message_service_ext import MessageServiceExt
from salestech_be.core.email.thread.thread_service_ext import ThreadServiceExt
from salestech_be.core.email.type.email import EmailHydratedParticipant, EmailTag
from salestech_be.core.meeting.meeting_service import MeetingService
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.note.service.note_service import NoteService
from salestech_be.core.note.types import Note
from salestech_be.core.pipeline.service.pipeline_service import PipelineService
from salestech_be.core.sequence.service.sequence_enrollment_query_service import (
    SequenceEnrollmentQueryService,
)
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    SequenceEnrollmentService,
)
from salestech_be.core.sequence.service.sequence_service import SequenceService
from salestech_be.core.sequence.type.sequence_v2 import SequenceV2
from salestech_be.core.task.service.task_v2_service import TaskV2Service
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.core.voice.v2.voice_call_service import VoiceCallService
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.calendar_account_repository import CalendarAccountRepository
from salestech_be.db.dao.crm_integrity_repository import CRMIntegrityJobRepository
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dao.sequence_repository import SequenceRepository
from salestech_be.db.dao.template_repository import TemplateRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dao.user_calendar_repository import UserCalendarRepository
from salestech_be.db.dao.user_integration_repository import UserIntegrationRepository
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dao.voice_call_repository import VoiceCallRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.user_calendar_event_dto import HydratedUserCalendarEventDto
from salestech_be.db.models.activity import (
    ActivityType,
)
from salestech_be.db.models.contact import (
    CreateContactRequest,
    CreateDbContactEmailRequest,
    CreateDbContactRequest,
)
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityJob,
    CRMSubDomain,
    EntityType,
    IntegrityJobName,
    IntegrityJobType,
    MoveContactToAccountContextualParam,
    MoveContactToAccountUserOption,
)
from salestech_be.db.models.email_account import EmailAccount
from salestech_be.db.models.global_thread import (
    GlobalMessage,
    GlobalMessageAssociation,
    GlobalThread,
)
from salestech_be.db.models.meeting import (
    Meeting,
    MeetingProvider,
    MeetingReferenceIdType,
)
from salestech_be.db.models.message import Message, MessageSource, MessageStatus
from salestech_be.db.models.sequence import (
    DayOfWeek,
    SequenceVisibility,
)
from salestech_be.db.models.task import (
    TaskPriority,
    TaskSourceType,
    TaskStatus,
    TaskType,
)
from salestech_be.db.models.template import Template, TemplateStatus, TemplateType
from salestech_be.db.models.thread import Thread
from salestech_be.db.models.user_calendar import UserCalendar
from salestech_be.db.models.voice_v2 import Call, CallDirection, CallStatus, CallType
from salestech_be.integrations.nylas.model import (
    NylasEmailName,
    NylasEvent,
    NylasEventParticipant,
    NylasTimespan,
    Visibility,
)
from salestech_be.integrations.temporal.config import (
    AI_TASK_QUEUE,
    INTEGRITY_JOB_TASK_QUEUE,
)
from salestech_be.temporal.worker import new_sandbox_runner
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.calendar.schema import (
    ContactParticipantRequest,
    CreateCalendarEventRequest,
    EventConferencingRequest,
    ListCalendarEventRequest,
)
from salestech_be.web.api.calendar.service import UserCalendarService
from salestech_be.web.api.meeting.schema import (
    CreateMeetingRequest,
    Invitee,
)
from salestech_be.web.api.note.schema import CreateNoteRequest
from salestech_be.web.api.sequence.enrollment.schema import (
    ContactForSequenceEnrollment,
    CreateSequenceEnrollmentRequest,
)
from salestech_be.web.api.sequence.schema import (
    CreateSequenceRequest,
    ScheduleConfig,
    ScheduleTime,
)
from salestech_be.web.api.task.schema import (
    CreateTaskRequest,
)
from tests.integration.core.calendar.test_user_calendar_webhook_service import (
    create_calendar_account_and_user_integration,
)
from tests.integration.core.crm_integrity.mocked_activity import (
    crm_data_integrity_job_send_notification_activity,
)


class NewlyMoveContactToAccountWorkflowBaseFixtures:
    @pytest.fixture(autouse=True, scope="function")
    async def setup(
        self, make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]]
    ) -> None:
        self.user_id, self.organization_id = await make_user_org()

    @pytest.fixture(autouse=True, scope="class")
    async def _mock_temporal_client(
        self,
        wf_env: WorkflowEnvironment,
    ) -> AsyncGenerator[tuple[AsyncMock, AsyncMock, AsyncMock]]:
        """Globally mocks get_temporal_client for the entire test class."""

        mock_client = wf_env.client

        with (
            patch(
                "salestech_be.core.task.service.task_v2_service.get_temporal_client",
                new_callable=AsyncMock,
                return_value=mock_client,
            ) as mock_get_client_task,
            patch(
                "salestech_be.core.meeting.meeting_service.get_temporal_client",
                new_callable=AsyncMock,
                return_value=mock_client,
            ) as mock_get_client_meeting,
            patch(
                "salestech_be.core.ai.event_handlers.pipeline_intel.get_temporal_client",
                new_callable=AsyncMock,
                return_value=mock_client,
            ) as mock_get_client_ai,
        ):
            yield mock_get_client_task, mock_get_client_meeting, mock_get_client_ai

    @pytest.fixture(scope="function")
    async def title(
        self,
        faker: Faker,
    ) -> str:
        return faker.name()

    @pytest.fixture(scope="function")
    async def department(
        self,
        faker: Faker,
    ) -> str:
        return faker.name()

    @pytest.fixture(scope="function")
    async def email_1(
        self,
        faker: Faker,
    ) -> str:
        return faker.email()

    @pytest.fixture(scope="function")
    async def account_1(
        self, faker: Faker, account_service: AccountService
    ) -> AccountV2:
        return await account_service.create_account_v2(
            create_account_request=CreateAccountRequest(
                display_name="account_1",
                owner_user_id=self.user_id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def contact_1(
        self,
        email_1: str,
        account_1: AccountV2,
        faker: Faker,
        contact_service: ContactService,
        select_list_service: InternalSelectListService,
    ) -> ContactV2:
        default_stage_list = await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=self.organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        assert default_stage_list and default_stage_list.default_or_initial_active_value
        created_contact = await contact_service.create_contact_with_contact_channels(
            organization_id=self.organization_id,
            user_id=self.user_id,
            create_contact_with_contact_channel_request=CreateContactRequest(
                contact=CreateDbContactRequest(
                    first_name=faker.first_name(),
                    last_name=faker.last_name(),
                    display_name="contact_1",
                    owner_user_id=self.user_id,
                    created_by_user_id=self.user_id,
                    stage_id=default_stage_list.default_or_initial_active_value.id,
                ),
                contact_emails=[
                    CreateDbContactEmailRequest(
                        email=email_1,
                        is_contact_primary=True,
                    )
                ],
                contact_account_roles=[],
            ),
        )

        # check contact account associations
        contact_account_1_associations = (
            await contact_service.list_active_contact_account_associations(
                organization_id=self.organization_id,
                contact_id=created_contact.id,
                account_id=account_1.id,
            )
        )
        assert len(contact_account_1_associations) == 0

        # check contact email
        contact_contact_email = await contact_service.find_contact_email_by_email(
            organization_id=self.organization_id,
            email=email_1,
        )
        assert contact_contact_email
        assert contact_contact_email.is_contact_primary

        # check contact email account association
        contact_email_1_account_associations = await contact_service.list_contact_email_account_associations_by_contact_email_id(
            organization_id=self.organization_id,
            contact_email_id=contact_contact_email.id,
        )
        assert len(contact_email_1_account_associations) == 0

        return created_contact

    @pytest.fixture(scope="function")
    async def mock_nylas_event(self) -> NylasEvent:
        return NylasEvent(
            id="test_nylas_event_id",
            grant_id="test_grant_id",
            calendar_id="test_calendar_id",
            when=NylasTimespan(
                start_timezone="America/Los_Angeles",
                end_timezone="America/Los_Angeles",
                object="timespan",
                start_time=int(zoned_utc_now().timestamp()),
                end_time=int(zoned_utc_now().timestamp()),
            ),
            html_link="http://test.test",
            busy=False,
            participants=[
                NylasEventParticipant(email="<EMAIL>", status="noreply")
            ],
            conferencing={
                "provider": "Google Meet",
                "details": {"url": "http://test.test"},
            },
            object="event",
            visibility=Visibility.private,
            description="test description",
            ical_uid="ical_uid",
            title="test title",
            organizer=NylasEmailName(
                email="<EMAIL>",
                name="test user",
            ),
            status="confirmed",
            created_at=int(zoned_utc_now().timestamp()),
            updated_at=int(zoned_utc_now().timestamp()),
        )

    @pytest.fixture(scope="function")
    async def calendar_event(
        self,
        faker: Faker,
        contact_1: ContactV2,
        account_1: AccountV2,
        user_calendar_service: UserCalendarService,
        user_repository: UserRepository,
        user_calendar_repository: UserCalendarRepository,
        calendar_account_repository: CalendarAccountRepository,
        user_integration_repository: UserIntegrationRepository,
        mock_nylas_event: NylasEvent,
    ) -> HydratedUserCalendarEventDto:
        (
            calendar_account,
            user_integration_dto,
        ) = await create_calendar_account_and_user_integration(
            user_id=self.user_id,
            organization_id=self.organization_id,
            calendar_account_repository=calendar_account_repository,
            user_integration_repository=user_integration_repository,
        )
        await user_calendar_repository.insert(
            UserCalendar(
                id=uuid4(),
                user_id=self.user_id,
                organization_id=self.organization_id,
                user_integration_id=user_integration_dto.user_integration.id,
                calendar_account_id=calendar_account.id,
                name="test_calendar",
                timezone="America/Los_Angeles",
                external_id="test_calendar_id",
                is_primary=True,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )
        user = await user_repository.find_user_by_id_and_organization_id(
            user_id=self.user_id,
            organization_id=self.organization_id,
        )
        mock_nylas_event.participants = [
            NylasEventParticipant(
                email=not_none(contact_1.primary_email), status="noreply"
            )
        ]
        with patch(
            "salestech_be.integrations.nylas.async_nylas_client.AsyncNylasClient.create_event",
            new_callable=AsyncMock,
            return_value=mock_nylas_event,
        ):
            return await user_calendar_service.create_calendar_event(
                user=not_none(user),
                organization_id=self.organization_id,
                request=CreateCalendarEventRequest(
                    starts_at=zoned_utc_now(),
                    ends_at=zoned_utc_now() + timedelta(hours=1),
                    title=faker.sentence(),
                    description=faker.text(),
                    conferencing=EventConferencingRequest(
                        conferencing_provider="Google Meet"
                    ),
                    participants=[
                        ContactParticipantRequest(
                            participant_type="contact",
                            contact_id=contact_1.id,
                        )
                    ],
                    is_busy=True,
                    notify_all_participants=True,
                    enable_recording=True,
                    use_consent_link=True,
                    visibility=Visibility.private,
                ),
            )

    @pytest.fixture(scope="function")
    async def meeting(
        self,
        contact_1: ContactV2,
        account_1: AccountV2,
        meeting_service: MeetingService,
        activity_service: ActivityService,
        faker: Faker,
    ) -> Meeting:
        meeting = (
            await meeting_service.create_meeting(
                organization_id=self.organization_id,
                user_id=self.user_id,
                request=CreateMeetingRequest(
                    platform=MeetingProvider.ZOOM,
                    reference_id=uuid4(),
                    reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                    starts_at=zoned_utc_now(),
                    title=faker.sentence(),
                    event_conferencing=None,
                    invitees=[
                        Invitee(user_id=self.user_id, is_organizer=True),
                        Invitee(
                            contact_id=contact_1.id,
                            is_organizer=False,
                        ),
                    ],
                ),
            )
        ).meeting

        for invitee in meeting.invitees or []:
            assert invitee.account_id is None
        assert meeting.account_id is None

        return meeting

    @pytest.fixture(scope="function")
    async def task(
        self,
        contact_1: ContactV2,
        task_v2_service: TaskV2Service,
    ) -> TaskV2:
        return await task_v2_service.insert_task_v2(
            created_by_user_id=self.user_id,
            organization_id=self.organization_id,
            request=CreateTaskRequest(
                title=str(uuid4()),
                status=TaskStatus.OPEN,
                priority=TaskPriority.MEDIUM,
                type=TaskType.ACTION_ITEM,
                account_id=None,
                contact_ids=[contact_1.id],
                pipeline_id=None,
                owner_user_id=self.user_id,
                source_type=TaskSourceType.USER,
            ),
        )

    @pytest.fixture(scope="function")
    async def note(
        self,
        faker: Faker,
        note_service: NoteService,
        contact_1: ContactV2,
    ) -> Note:
        return await note_service.insert_note(
            organization_id=self.organization_id,
            user_id=self.user_id,
            create_note_request=CreateNoteRequest(
                title=faker.sentence(),
                note_html=faker.text(),
                account_id=None,
                contact_ids=[contact_1.id],
            ),
        )

    @pytest.fixture(scope="function")
    async def email_account(
        self,
        faker: Faker,
        email_account_service_ext: EmailAccountServiceExt,
    ) -> EmailAccount:
        return await email_account_service_ext.get_or_create_email_account(
            owner_user_id=self.user_id,
            email=faker.email(),
            signature_html=faker.text(),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def email_template(
        self,
        faker: Faker,
        template_repository: TemplateRepository,
    ) -> Template:
        template = Template(
            id=uuid.uuid4(),
            name=faker.sentence(),
            template_type=TemplateType.EMAIL,
            status=TemplateStatus.ACTIVE,
            body_text="",
            body_html="",
            is_shared=False,
            version=1,
            include_email_signature=True,
            unsubscription_group_id=None,
            organization_id=self.organization_id,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
        return await template_repository.insert(template)

    @pytest.fixture(scope="function")
    async def email_thread(
        self,
        contact_1: ContactV2,
        email_account: EmailAccount,
        thread_repository: ThreadRepository,
        email_template: Template,
    ) -> Thread:
        recipient_email = "<EMAIL>"
        recipient_name = "Test"
        sender_email = "<EMAIL>"
        sender_name = "Sender"
        email_recipient = EmailHydratedParticipant(
            email=recipient_email,
            name=recipient_name,
            contact_id=contact_1.id,
        )
        email_sender = EmailHydratedParticipant(
            email=sender_email,
            name=sender_name,
            email_account_id=email_account.id,
        )

        return await thread_repository.insert(
            Thread(
                id=uuid.uuid4(),
                email_account_id=not_none(email_sender.email_account_id),
                organization_id=self.organization_id,
                subject=email_template.subject or "",
                snippet=email_template.body_text[:100],
                unread=None,
                starred=None,
                has_attachments=bool(email_template.attachment_ids),
                has_drafts=None,
                earliest_message_date=zoned_utc_now(),
                latest_message_sent_date=None,
                latest_message_received_date=None,
                participants=[email_sender, email_recipient],
                tags=[EmailTag.OUTBOUND],
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
                deleted_at=None,
            )
        )

    @pytest.fixture(scope="function")
    async def email_message(
        self,
        contact_1: ContactV2,
        email_thread: Thread,
        message_repository: MessageRepository,
        email_account: EmailAccount,
        email_template: Template,
    ) -> Message:
        recipient_email = "<EMAIL>"
        recipient_name = "Test"
        sender_email = "<EMAIL>"
        sender_name = "Sender"
        email_recipient = EmailHydratedParticipant(
            email=recipient_email,
            name=recipient_name,
            contact_id=contact_1.id,
        )
        email_sender = EmailHydratedParticipant(
            email=sender_email,
            name=sender_name,
            email_account_id=email_account.id,
        )

        return await message_repository.insert(
            Message(
                id=uuid4(),
                thread_id=email_thread.id,
                idempotency_key=str(uuid4()),
                source=MessageSource.SEQUENCE,
                email_account_id=not_none(email_sender.email_account_id),
                organization_id=self.organization_id,
                template_id=email_template.id,
                email_template_version=email_template.version,
                subject=email_template.subject or "",
                send_from=[email_sender],
                send_to=[email_recipient],
                cc=email_template.cc,
                bcc=email_template.bcc,
                reply_to=email_template.reply_to,
                reply_to_message_id=None,
                status=MessageStatus.SCHEDULED,
                snippet=email_template.body_text[:100],
                body_text=email_template.body_text,
                body_html=email_template.body_html,
                attachment_ids=email_template.attachment_ids,
                folders=None,
                starred=None,
                unread=None,
                use_draft=None,
                send_at=zoned_utc_now(),
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )

    @pytest.fixture(scope="function")
    async def global_thread(
        self,
        faker: Faker,
        contact_1: ContactV2,
        email_thread: Thread,
        thread_repository: ThreadRepository,
    ) -> GlobalThread:
        return await thread_repository.insert(
            GlobalThread(
                id=uuid4(),
                subject=faker.sentence(),
                snippet=faker.text(),
                thread_ids=[email_thread.id],
                contact_ids=[contact_1.id],
                account_ids=[],
                pipeline_id=None,
                organization_id=self.organization_id,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )

    @pytest.fixture(scope="function")
    async def global_message(
        self,
        global_thread: GlobalThread,
        email_message: Message,
        thread_repository: ThreadRepository,
    ) -> GlobalMessage:
        created_global_message = await thread_repository.insert(
            GlobalMessage(
                id=uuid4(),
                original_message_id="test_original_message_id",
                organization_id=self.organization_id,
                global_thread_id=global_thread.id,
                message_received_at=zoned_utc_now(),
                created_at=zoned_utc_now(),
            )
        )

        await thread_repository.insert(
            GlobalMessageAssociation(
                id=uuid4(),
                global_message_id=created_global_message.id,
                message_id=email_message.id,
                organization_id=self.organization_id,
                created_at=zoned_utc_now(),
            )
        )

        return created_global_message

    @pytest.fixture(scope="function")
    async def voice_call(
        self,
        faker: Faker,
        contact_1: ContactV2,
        voice_call_repository: VoiceCallRepository,
        voice_call_service: VoiceCallService,
    ) -> Call:
        created_call = await voice_call_repository.insert(
            Call(
                id=uuid4(),
                organization_id=self.organization_id,
                organization_phone_number_id=uuid4(),
                voice_provider_account_id=uuid4(),
                call_type=CallType.WEBRTC,
                caller_number=faker.phone_number(),
                status=CallStatus.INITIATED,
                direction=CallDirection.OUTBOUND,
                created_by_user_id=self.user_id,  # always use the phone number owner as the creator
                caller_id=self.user_id,
                contact_id=contact_1.id,
                pipeline_id=None,
                account_id=None,
                recipient_number=faker.phone_number(),
                recipient_id=contact_1.id,
                metadata=None,
                external_id=None,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )

        created_meeting_dto = await voice_call_service._create_meeting_from_call(
            created_call
        )
        created_voice_meeting = created_meeting_dto.meeting

        # check voice meeting created
        assert created_voice_meeting.reference_id == str(created_call.id)
        assert (
            created_voice_meeting.reference_id_type == MeetingReferenceIdType.VOICE_V2
        )
        assert created_voice_meeting.meeting_platform == MeetingProvider.VOICE
        assert created_voice_meeting.account_id is None
        assert created_voice_meeting.pipeline_id is None

        contact_invitee = [
            invitee
            for invitee in created_voice_meeting.invitees or []
            if invitee.contact_id == contact_1.id
        ]
        assert len(contact_invitee) == 1
        assert contact_invitee[0].is_organizer is False
        assert contact_invitee[0].account_id is None
        assert contact_invitee[0].contact_email is None

        return created_call

    @pytest.fixture(scope="function")
    async def sequence(
        self,
        faker: Faker,
        contact_1: ContactV2,
        sequence_service: SequenceService,
        email_account_pool_service: EmailAccountPoolService,
        sequence_enrollment_service: SequenceEnrollmentService,
    ) -> SequenceV2:
        await email_account_pool_service.get_or_create_default_email_account_pool(
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

        created_sequence = await sequence_service._create_sequence(
            organization_id=self.organization_id,
            user_id=self.user_id,
            request=CreateSequenceRequest(
                name=faker.name(),
                description=faker.text(),
                visibility=SequenceVisibility.TEAM_EDITABLE,
                schedule=ScheduleConfig(
                    timezone="America/New_York",
                    skip_holidays=False,
                    schedule_times=[
                        ScheduleTime(
                            day_of_week=DayOfWeek.MON,
                            start_time=time(hour=9, minute=0),
                            end_time=time(hour=17, minute=0),
                        ),
                    ],
                ),
            ),
            is_blueprint=False,
        )

        created_sequence_enrollment_response = (
            await sequence_enrollment_service.create_sequence_enrollment(
                user_auth_context=UserAuthContext(
                    user_id=self.user_id,
                    organization_id=self.organization_id,
                ),
                request=CreateSequenceEnrollmentRequest(
                    sequence_id=created_sequence.id,
                    contacts=[
                        ContactForSequenceEnrollment(
                            contact_id=contact_1.id,
                        )
                    ],
                ),
            )
        )

        # do not allow craete seqquence enrollment for contact that has no account associated
        assert len(created_sequence_enrollment_response.failed_enrollments) == 1

        return created_sequence

    @pytest.fixture(scope="function")
    def integrity_job_subdomains(
        self,
    ) -> list[CRMSubDomain]:
        return get_affected_sub_domains_by_integrity_job_name(
            integrity_job_name=IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT,
        )

    async def execute_move_contact_to_account_workflow(
        self,
        _engine: DatabaseEngine,
        integrity_job: CRMIntegrityJob,
        user_choices: dict[CRMSubDomain, MoveContactToAccountUserOption],
        wf_env: WorkflowEnvironment,
    ) -> UUID:
        integrity_job_activity = IntegrityJobActivity(db_engine=_engine)
        intelligence_related_activity = IntelligenceRelatedActivity(db_engine=_engine)

        move_pipeline_to_alternative_contact_activity = (
            MovePipelineToAlternativeContactActivity(db_engine=_engine)
        )

        associate_contact_with_account_activity = AssociateContactWithAccountActivity(
            db_engine=_engine
        )

        unassociate_contact_with_account_activity = (
            UnassociateContactWithAccountActivity(db_engine=_engine)
        )

        contact_email_related_activity = ContactEmailRelatedActivity(db_engine=_engine)

        workflow_entrypoint, workflow_param = get_workflow_entrypoint_and_params(
            integrity_job_name=IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT,
            job_id=integrity_job.id,
            retry_count=integrity_job.retry_count,
            src_entity_id=integrity_job.src_entity_id,
            dest_entity_id=integrity_job.dest_entity_id,
            user_id=self.user_id,
            organization_id=self.organization_id,
            user_choices=user_choices,
            contextual_param=integrity_job.contextual_param,
        )

        async with (
            Worker(
                wf_env.client,
                task_queue=INTEGRITY_JOB_TASK_QUEUE,
                workflows=[MoveContactToAccountWorkflow],
                activities=[
                    integrity_job_activity.create_integrity_job,
                    integrity_job_activity.update_integrity_job,
                    integrity_job_activity.acquire_locks_for_entities,
                    integrity_job_activity.free_locks_for_entities,
                    intelligence_related_activity.pipeline_intel_activity_job,
                    move_pipeline_to_alternative_contact_activity.move_pipeline_to_alternative_contact,
                    associate_contact_with_account_activity.copy_single_contact_association_from_account_to_account,
                    unassociate_contact_with_account_activity.unassociate_single_contact_with_single_account,
                    contact_email_related_activity.move_contact_emails_from_contact_to_account,
                    # use faked notification activity to avoid sending actual msg and overlap event loop issue
                    crm_data_integrity_job_send_notification_activity,
                    data_operation_event_processor_activity,
                ],
                workflow_runner=new_sandbox_runner(),
            ),
            Worker(
                wf_env.client,
                task_queue=AI_TASK_QUEUE,
                debug_mode=True,
                workflows=[
                    MeetingTriggerPipelineIntelWorkflow,
                ],
                activities=[
                    generate_meeting_activity_summary,
                    generate_meeting_how_to_win,
                    generate_meeting_risks,
                    generate_meeting_objections,
                    generate_intel_tasks,
                    upsert_pipeline_intel,
                ],
                workflow_runner=new_sandbox_runner(),
            ),
        ):
            await wf_env.client.execute_workflow(
                workflow_entrypoint,
                workflow_param,
                id=f"test_move_contact_to_account_workflow_{integrity_job.id}",
                task_queue=INTEGRITY_JOB_TASK_QUEUE,
            )

        return integrity_job.id

    async def validate_core_objects_and_associations(
        self,
        email_1: str,
        contact_1_id: UUID,
        account_1_id: UUID,
        contact_service: ContactService,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
        integrity_job: CRMIntegrityJob,
        title: str,
        department: str,
    ) -> None:
        updated_integrity_job = await crm_integrity_job_repository.find_by_primary_key(
            table_model=CRMIntegrityJob,
            id=integrity_job.id,
        )

        assert updated_integrity_job
        assert updated_integrity_job.started_at
        assert updated_integrity_job.ended_at

        # check account <> contact association
        contact_1_to_account_1_associations = (
            await contact_service.list_active_contact_account_associations(
                organization_id=self.organization_id,
                contact_id=contact_1_id,
                account_id=account_1_id,
            )
        )

        assert len(contact_1_to_account_1_associations) == 1
        assert contact_1_to_account_1_associations[0].is_primary is True
        assert contact_1_to_account_1_associations[0].title == title
        assert contact_1_to_account_1_associations[0].department == department

        # check contact email
        contact_1_contact_emails = (
            await contact_service.list_contact_emails_by_contact_id(
                organization_id=self.organization_id, contact_id=contact_1_id
            )
        )
        assert len(contact_1_contact_emails) == 1
        assert {
            (ce.contact_id, ce.email, ce.is_contact_primary)
            for ce in contact_1_contact_emails
        } == {
            (contact_1_id, email_1, True),
        }

        contact_email_1 = next(
            (ce for ce in contact_1_contact_emails if ce.email == email_1), None
        )
        assert contact_email_1

        # check contact email account associations
        contact_email_1_account_associations = await contact_service.list_contact_email_account_associations_by_contact_email_id(
            organization_id=self.organization_id,
            contact_email_id=contact_email_1.id,
        )
        assert len(contact_email_1_account_associations) == 0


class TestNewlyMoveContactToAccountWorkflowWithRetainOption(
    NewlyMoveContactToAccountWorkflowBaseFixtures
):
    @pytest.fixture(scope="function")
    async def integrity_job(
        self,
        integrity_job_service: IntegrityJobService,
        contact_1: ContactV2,
        account_1: AccountV2,
        integrity_job_subdomains: list[CRMSubDomain],
        title: str,
        department: str,
    ) -> CRMIntegrityJob:
        integrity_job, _ = await integrity_job_service.create_integrity_job(
            user_id=self.user_id,
            organization_id=self.organization_id,
            job_type=IntegrityJobType.MOVE,
            src_entity_type=EntityType.CONTACT,
            src_entity_id=contact_1.id,
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=account_1.id,
            contextual_param=MoveContactToAccountContextualParam(
                src_account_id=None,
                title=title,
                department=department,
            ),
            user_choices=dict.fromkeys(
                integrity_job_subdomains,
                MoveContactToAccountUserOption.RETAIN,
            ),
        )

        return integrity_job

    async def test_move_contact_to_account_workflow(
        self,
        _engine: DatabaseEngine,
        email_1: str,
        account_1: AccountV2,
        contact_1: ContactV2,
        meeting: Meeting,
        task: TaskV2,
        note: Note,
        email_thread: Thread,
        email_message: Message,
        global_thread: GlobalThread,
        global_message: GlobalMessage,
        sequence: SequenceV2,
        voice_call: Call,
        calendar_event: HydratedUserCalendarEventDto,
        integrity_job: CRMIntegrityJob,
        account_service: AccountService,
        contact_service: ContactService,
        pipeline_service: PipelineService,
        account_repository: AccountRepository,
        meeting_service: MeetingService,
        task_v2_service: TaskV2Service,
        note_service: NoteService,
        activity_service: ActivityService,
        global_thread_query_service: GlobalThreadQueryService,
        voice_call_repository: VoiceCallRepository,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
        sequence_enrollment_query_service: SequenceEnrollmentQueryService,
        sequence_repository: SequenceRepository,
        email_message_service_ext: MessageServiceExt,
        user_calendar_service: UserCalendarService,
        thread_service_ext: ThreadServiceExt,
        integrity_job_subdomains: list[CRMSubDomain],
        wf_env: WorkflowEnvironment,
        title: str,
        department: str,
    ) -> None:
        await self.execute_move_contact_to_account_workflow(
            _engine=_engine,
            integrity_job=integrity_job,
            user_choices=dict.fromkeys(
                integrity_job_subdomains,
                MoveContactToAccountUserOption.RETAIN,
            ),
            wf_env=wf_env,
        )

        await self.validate_core_objects_and_associations(
            email_1=email_1,
            contact_1_id=contact_1.id,
            account_1_id=account_1.id,
            contact_service=contact_service,
            crm_integrity_job_repository=crm_integrity_job_repository,
            integrity_job=integrity_job,
            title=title,
            department=department,
        )

        # check meeting and activity
        updated_meeting = await meeting_service.get_meeting(
            organization_id=self.organization_id,
            meeting_id=meeting.id,
        )
        assert updated_meeting.meeting.account_id is None
        contact_participants = [
            participant
            for participant in updated_meeting.meeting.invitees or []
            if participant.contact_id
        ]
        assert len(contact_participants) == 1

        assert {
            (participant.contact_id, participant.account_id)
            for participant in contact_participants
        } == {
            (contact_1.id, None),
        }

        meeting_activity_list = (
            await activity_service.list_activities_by_reference_ids_and_type(
                organization_id=self.organization_id,
                reference_ids=[str(meeting.id)],
                activity_type=ActivityType.MEETING,
            )
        )

        assert len(meeting_activity_list) == 1
        assert meeting_activity_list[0].account_id is None

        # check calendar event
        list_user_calendar_events_by_calendar = (
            await user_calendar_service.list_user_calendar_events_by_calendar(
                user_id=self.user_id,
                organization_id=self.organization_id,
                request=ListCalendarEventRequest(
                    starts_at_inclusive=calendar_event.user_calendar_event.starts_at,
                    ends_at_exclusive=calendar_event.user_calendar_event.ends_at
                    + timedelta(minutes=1),
                ),
            )
        )
        assert len(list_user_calendar_events_by_calendar) == 1
        assert (
            list_user_calendar_events_by_calendar[0].id
            == calendar_event.user_calendar_event.id
        )
        calendar_event_participants = [
            participant
            for participant in list_user_calendar_events_by_calendar[0].participants
            if participant.contact_id
        ]
        assert len(calendar_event_participants) == 1
        assert {
            (participant.contact_id, participant.account_id)
            for participant in calendar_event_participants
        } == {
            (contact_1.id, None),
        }

        # check task and activity
        updated_task = await task_v2_service.task_query_service.get_by_id_v2(
            organization_id=self.organization_id,
            task_id=task.id,
        )
        assert updated_task.account_id is None

        task_activity_list = (
            await activity_service.list_activities_by_reference_ids_and_type(
                organization_id=self.organization_id,
                reference_ids=[str(task.id)],
                activity_type=ActivityType.TASK,
            )
        )

        assert len(task_activity_list) == 1
        assert task_activity_list[0].account_id is None

        # check note and activity
        updated_note = await note_service.get_by_id(
            organization_id=self.organization_id,
            note_id=note.id,
        )
        assert updated_note.account_id is None

        note_activity_list = (
            await activity_service.list_activities_by_reference_ids_and_type(
                organization_id=self.organization_id,
                reference_ids=[str(note.id)],
                activity_type=ActivityType.NOTE,
            )
        )

        assert len(note_activity_list) == 1
        assert note_activity_list[0].account_id is None

        # check email
        updated_email_message = await email_message_service_ext.get_email_message_by_id(
            organization_id=self.organization_id,
            message_id=email_message.id,
        )

        assert updated_email_message
        assert len(updated_email_message.send_to) == 1
        assert updated_email_message.send_to[0].account_id is None
        assert updated_email_message.send_to[0].contact_id == contact_1.id

        # check thread
        updated_email_thread = (
            await thread_service_ext.find_thread_by_tenanted_primary_key(
                organization_id=self.organization_id,
                thread_id=email_thread.id,
            )
        )
        assert updated_email_thread
        assert len(updated_email_thread.participants) == 2

        contact_1_participant = next(
            (
                p
                for p in updated_email_thread.participants
                if p.contact_id == contact_1.id
            ),
            None,
        )
        assert contact_1_participant
        assert contact_1_participant.account_id is None

        # check global thread
        global_thread_list = await global_thread_query_service.list_global_threads(
            organization_id=self.organization_id,
            user_id=self.user_id,
            only_include_thread_for_contacts={contact_1.id},
        )

        assert len(global_thread_list) == 1
        assert global_thread_list[0].id == global_thread.id
        assert global_thread_list[0].account_ids == []
        assert global_thread_list[0].pipeline_id is None

        # check voice call
        updated_voice_call = await voice_call_repository.find_by_id(
            call_id=voice_call.id,
        )
        assert updated_voice_call and updated_voice_call.account_id is None

        voice_call_meeting_dto = await meeting_service.get_meeting_by_reference_id(
            reference_id=voice_call.id,
            reference_id_type=MeetingReferenceIdType.VOICE_V2,
            organization_id=self.organization_id,
        )
        assert voice_call_meeting_dto.meeting
        assert voice_call_meeting_dto.meeting.account_id is None
        assert voice_call_meeting_dto.meeting.pipeline_id is None

        assert voice_call_meeting_dto.meeting.invitees
        contact_invitee = [
            invitee
            for invitee in voice_call_meeting_dto.meeting.invitees
            if invitee.contact_id
        ]
        assert len(contact_invitee) == 1
        assert contact_invitee[0].is_organizer is False
        assert contact_invitee[0].contact_id == contact_1.id
        assert contact_invitee[0].account_id is None
        assert contact_invitee[0].contact_email is None


class TestNewlyMoveContactToAccountWorkflowWithMoveOption(
    NewlyMoveContactToAccountWorkflowBaseFixtures
):
    @pytest.fixture(scope="function")
    async def integrity_job(
        self,
        integrity_job_service: IntegrityJobService,
        contact_1: ContactV2,
        account_1: AccountV2,
        integrity_job_subdomains: list[CRMSubDomain],
        title: str,
        department: str,
    ) -> CRMIntegrityJob:
        integrity_job, _ = await integrity_job_service.create_integrity_job(
            user_id=self.user_id,
            organization_id=self.organization_id,
            job_type=IntegrityJobType.MOVE,
            src_entity_type=EntityType.CONTACT,
            src_entity_id=contact_1.id,
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=account_1.id,
            contextual_param=MoveContactToAccountContextualParam(
                src_account_id=None,
                title=title,
                department=department,
            ),
            user_choices=dict.fromkeys(
                integrity_job_subdomains,
                MoveContactToAccountUserOption.MOVE,
            ),
        )

        return integrity_job

    async def test_move_contact_to_account_workflow(
        self,
        _engine: DatabaseEngine,
        email_1: str,
        account_1: AccountV2,
        contact_1: ContactV2,
        meeting: Meeting,
        task: TaskV2,
        note: Note,
        email_thread: Thread,
        email_message: Message,
        global_thread: GlobalThread,
        global_message: GlobalMessage,
        sequence: SequenceV2,
        voice_call: Call,
        calendar_event: HydratedUserCalendarEventDto,
        integrity_job: CRMIntegrityJob,
        account_service: AccountService,
        contact_service: ContactService,
        pipeline_service: PipelineService,
        account_repository: AccountRepository,
        meeting_service: MeetingService,
        task_v2_service: TaskV2Service,
        note_service: NoteService,
        activity_service: ActivityService,
        global_thread_query_service: GlobalThreadQueryService,
        voice_call_repository: VoiceCallRepository,
        crm_integrity_job_repository: CRMIntegrityJobRepository,
        sequence_enrollment_query_service: SequenceEnrollmentQueryService,
        sequence_repository: SequenceRepository,
        email_message_service_ext: MessageServiceExt,
        user_calendar_service: UserCalendarService,
        thread_service_ext: ThreadServiceExt,
        wf_env: WorkflowEnvironment,
        integrity_job_subdomains: list[CRMSubDomain],
        title: str,
        department: str,
    ) -> None:
        await self.execute_move_contact_to_account_workflow(
            _engine=_engine,
            integrity_job=integrity_job,
            user_choices=dict.fromkeys(
                integrity_job_subdomains,
                MoveContactToAccountUserOption.MOVE,
            ),
            wf_env=wf_env,
        )

        await self.validate_core_objects_and_associations(
            email_1=email_1,
            contact_1_id=contact_1.id,
            account_1_id=account_1.id,
            contact_service=contact_service,
            crm_integrity_job_repository=crm_integrity_job_repository,
            integrity_job=integrity_job,
            title=title,
            department=department,
        )

        # check meeting and activity
        updated_meeting = await meeting_service.get_meeting(
            organization_id=self.organization_id,
            meeting_id=meeting.id,
        )
        assert updated_meeting.meeting.account_id == account_1.id
        contact_participants = [
            participant
            for participant in updated_meeting.meeting.invitees or []
            if participant.contact_id
        ]
        assert len(contact_participants) == 1

        assert {
            (participant.contact_id, participant.account_id)
            for participant in contact_participants
        } == {
            (contact_1.id, None),
        }

        meeting_activity_list = (
            await activity_service.list_activities_by_reference_ids_and_type(
                organization_id=self.organization_id,
                reference_ids=[str(meeting.id)],
                activity_type=ActivityType.MEETING,
            )
        )

        assert len(meeting_activity_list) == 1
        assert meeting_activity_list[0].account_id == account_1.id

        # check calendar event
        list_user_calendar_events_by_calendar = (
            await user_calendar_service.list_user_calendar_events_by_calendar(
                user_id=self.user_id,
                organization_id=self.organization_id,
                request=ListCalendarEventRequest(
                    starts_at_inclusive=calendar_event.user_calendar_event.starts_at,
                    ends_at_exclusive=calendar_event.user_calendar_event.ends_at
                    + timedelta(minutes=1),
                ),
            )
        )
        assert len(list_user_calendar_events_by_calendar) == 1
        assert (
            list_user_calendar_events_by_calendar[0].id
            == calendar_event.user_calendar_event.id
        )
        calendar_event_participants = [
            participant
            for participant in list_user_calendar_events_by_calendar[0].participants
            if participant.contact_id
        ]
        assert len(calendar_event_participants) == 1
        assert {
            (participant.contact_id, participant.account_id)
            for participant in calendar_event_participants
        } == {
            (contact_1.id, None),
        }

        # check task and activity
        updated_task = await task_v2_service.task_query_service.get_by_id_v2(
            organization_id=self.organization_id,
            task_id=task.id,
        )
        assert updated_task.account_id == account_1.id

        task_activity_list = (
            await activity_service.list_activities_by_reference_ids_and_type(
                organization_id=self.organization_id,
                reference_ids=[str(task.id)],
                activity_type=ActivityType.TASK,
            )
        )

        assert len(task_activity_list) == 1
        assert task_activity_list[0].account_id == account_1.id

        # check note and activity
        updated_note = await note_service.get_by_id(
            organization_id=self.organization_id,
            note_id=note.id,
        )
        assert updated_note.account_id == account_1.id

        note_activity_list = (
            await activity_service.list_activities_by_reference_ids_and_type(
                organization_id=self.organization_id,
                reference_ids=[str(note.id)],
                activity_type=ActivityType.NOTE,
            )
        )

        assert len(note_activity_list) == 1
        assert note_activity_list[0].account_id == account_1.id

        # check email
        updated_email_message = await email_message_service_ext.get_email_message_by_id(
            organization_id=self.organization_id,
            message_id=email_message.id,
        )

        assert updated_email_message
        assert len(updated_email_message.send_to) == 1
        assert updated_email_message.send_to[0].account_id is None
        assert updated_email_message.send_to[0].contact_id == contact_1.id

        # check thread
        updated_email_thread = (
            await thread_service_ext.find_thread_by_tenanted_primary_key(
                organization_id=self.organization_id,
                thread_id=email_thread.id,
            )
        )
        assert updated_email_thread
        assert len(updated_email_thread.participants) == 2

        contact_1_participant = next(
            (
                p
                for p in updated_email_thread.participants
                if p.contact_id == contact_1.id
            ),
            None,
        )
        assert contact_1_participant
        assert contact_1_participant.account_id is None

        # check global thread
        global_thread_list = await global_thread_query_service.list_global_threads(
            organization_id=self.organization_id,
            user_id=self.user_id,
            only_include_thread_for_contacts={contact_1.id},
        )

        assert len(global_thread_list) == 1
        assert global_thread_list[0].id == global_thread.id
        assert global_thread_list[0].account_ids == [account_1.id]
        assert global_thread_list[0].pipeline_id is None

        # check voice call
        updated_voice_call = await voice_call_repository.find_by_id(
            call_id=voice_call.id,
        )
        assert updated_voice_call and updated_voice_call.account_id == account_1.id

        voice_call_meeting_dto = await meeting_service.get_meeting_by_reference_id(
            reference_id=voice_call.id,
            reference_id_type=MeetingReferenceIdType.VOICE_V2,
            organization_id=self.organization_id,
        )
        assert voice_call_meeting_dto.meeting
        assert voice_call_meeting_dto.meeting.account_id == account_1.id
        assert voice_call_meeting_dto.meeting.pipeline_id is None

        assert voice_call_meeting_dto.meeting.invitees
        contact_invitee = [
            invitee
            for invitee in voice_call_meeting_dto.meeting.invitees
            if invitee.contact_id
        ]
        assert len(contact_invitee) == 1
        assert contact_invitee[0].is_organizer is False
        assert contact_invitee[0].contact_id == contact_1.id
        assert contact_invitee[0].account_id is None
        assert contact_invitee[0].contact_email is None
