from uuid import uuid4

import pytest

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.core.domain_crm_association.domain_crm_association_service import (
    DomainCRMAssociationService,
    DomainCRMAssociationValue,
)
from salestech_be.core.domain_crm_association.types import (
    CreateEmailCrmAssociation,
    CreateMeetingCrmAssociation,
    CreateTaskCrmAssociation,
    CreateVoiceCallCrmAssociation,
    DeleteMeetingCrmAssociation,
    UpdateDomainCrmAssociation,
)
from salestech_be.db.models.domain_crm_association import (
    AttributionState,
    DomainType,
)


class TestDomainCRMAssociationService:
    async def test_create_email_association_success_with_all_crm_context_fields(
        self,
        domain_crm_association_service: DomainCRMAssociationService,
    ) -> None:
        # Arrange
        message_id = uuid4()
        thread_id = uuid4()
        created_by_user_id = uuid4()
        organization_id = uuid4()
        contact_id = uuid4()
        account_id = uuid4()
        pipeline_id = uuid4()
        email = "<EMAIL>"

        create_data = CreateEmailCrmAssociation(
            organization_id=organization_id,
            email=email,
            message_id=message_id,
            thread_id=thread_id,
            account_id=account_id,
            contact_id=contact_id,
            pipeline_id=pipeline_id,
            created_by_user_id=created_by_user_id,
        )

        # Act
        result = await domain_crm_association_service.create_domain_crm_association(
            create_data,
        )

        # Assert
        assert result.domain_type == DomainType.EMAIL
        assert result.message_id == message_id
        assert result.thread_id == thread_id
        assert result.account_id == account_id
        assert result.contact_id == contact_id
        assert result.pipeline_id == pipeline_id
        assert result.contact_attribution_state == AttributionState.NOT_PROCESSED
        assert result.contact_attribution_info is None
        assert result.account_attribution_state == AttributionState.NOT_PROCESSED
        assert result.account_attribution_info is None
        assert result.pipeline_attribution_state == AttributionState.NOT_PROCESSED
        assert result.pipeline_attribution_info is None

    async def test_create_email_association_success_with_partial_crm_context_fields(
        self,
        domain_crm_association_service: DomainCRMAssociationService,
    ) -> None:
        # Arrange
        message_id = uuid4()
        thread_id = uuid4()
        created_by_user_id = uuid4()
        organization_id = uuid4()
        contact_id = uuid4()
        account_id = uuid4()
        email = "<EMAIL>"
        email_account_id = uuid4()
        user_id = uuid4()

        create_data = CreateEmailCrmAssociation(
            organization_id=organization_id,
            email=email,
            message_id=message_id,
            thread_id=thread_id,
            account_id=account_id,
            contact_id=contact_id,
            user_id=user_id,
            email_account_id=email_account_id,
            created_by_user_id=created_by_user_id,
        )

        # Act
        result = await domain_crm_association_service.create_domain_crm_association(
            create_data,
        )

        # Assert
        assert result.domain_type == DomainType.EMAIL
        assert result.message_id == message_id
        assert result.thread_id == thread_id
        assert result.account_id == account_id
        assert result.contact_id == contact_id
        assert result.pipeline_id is None
        assert result.email_account_id == email_account_id
        assert result.contact_attribution_state == AttributionState.NOT_PROCESSED
        assert result.contact_attribution_info is None
        assert result.account_attribution_state == AttributionState.NOT_PROCESSED
        assert result.account_attribution_info is None
        assert result.pipeline_attribution_state == AttributionState.NOT_PROCESSED
        assert result.pipeline_attribution_info is None

    async def test_get_domain_crm_association_by_id(
        self,
        domain_crm_association_service: DomainCRMAssociationService,
    ) -> None:
        # Arrange
        message_id = uuid4()
        thread_id = uuid4()
        organization_id = uuid4()
        contact_id = uuid4()
        account_id = uuid4()
        email = "<EMAIL>"
        created_by_user_id = uuid4()

        create_data = CreateEmailCrmAssociation(
            organization_id=organization_id,
            email=email,
            message_id=message_id,
            thread_id=thread_id,
            account_id=account_id,
            contact_id=contact_id,
            created_by_user_id=created_by_user_id,
        )

        created_association = (
            await domain_crm_association_service.create_domain_crm_association(
                create_data,
            )
        )

        # Act
        result = await domain_crm_association_service.get_domain_crm_association_by_id(
            created_association.id
        )

        # Assert
        assert result.id == created_association.id
        assert result.domain_type == DomainType.EMAIL
        assert result.message_id == message_id
        assert result.thread_id == thread_id
        assert result.account_id == account_id
        assert result.contact_id == contact_id

    async def test_get_domain_crm_association_by_id_not_found(
        self,
        domain_crm_association_service: DomainCRMAssociationService,
    ) -> None:
        # Arrange
        non_existent_id = uuid4()

        # Act & Assert
        with pytest.raises(ResourceNotFoundError):
            await domain_crm_association_service.get_domain_crm_association_by_id(
                non_existent_id
            )

    async def test_get_domain_crm_associations_by_related_id(
        self,
        domain_crm_association_service: DomainCRMAssociationService,
    ) -> None:
        # Arrange
        call_id = uuid4()
        organization_id = uuid4()
        created_by_user_id = uuid4()
        meeting_id = uuid4()
        phone_number = "+**********"

        create_data = CreateVoiceCallCrmAssociation(
            organization_id=organization_id,
            call_id=call_id,
            created_by_user_id=created_by_user_id,
            meeting_id=meeting_id,
            phone_number=phone_number,
        )

        await domain_crm_association_service.create_domain_crm_association(
            create_data,
        )

        results = await domain_crm_association_service.get_domain_crm_associations_by_related_id(
            domain_id=call_id,
            domain_type=DomainType.VOICE_CALL,
        )

        # Assert
        assert len(results) == 1
        assert results[0].call_id == call_id
        assert results[0].meeting_id == meeting_id
        assert results[0].organization_id == organization_id
        assert results[0].created_by_user_id == created_by_user_id

    async def test_update_domain_crm_association(
        self,
        domain_crm_association_service: DomainCRMAssociationService,
    ) -> None:
        # Arrange
        message_id = uuid4()
        thread_id = uuid4()
        organization_id = uuid4()
        contact_id = uuid4()
        account_id = uuid4()
        email = "<EMAIL>"
        created_by_user_id = uuid4()

        create_data = CreateEmailCrmAssociation(
            organization_id=organization_id,
            email=email,
            message_id=message_id,
            thread_id=thread_id,
            account_id=account_id,
            contact_id=contact_id,
            created_by_user_id=created_by_user_id,
        )

        created_association = (
            await domain_crm_association_service.create_domain_crm_association(
                create_data,
            )
        )

        # Create update data with pipeline_id to
        pipeline_id = uuid4()

        update_data = UpdateDomainCrmAssociation(
            pipeline_id=pipeline_id,
            updated_by_user_id=created_by_user_id,
        )

        # Act
        updated_association = (
            await domain_crm_association_service.update_domain_crm_association(
                created_association.id,
                update_data,
            )
        )

        # Assert
        assert updated_association.id == created_association.id
        assert updated_association.pipeline_id == pipeline_id
        assert updated_association.updated_by_user_id == created_by_user_id
        assert (
            updated_association.contact_attribution_state
            == AttributionState.NOT_PROCESSED
        )
        assert updated_association.contact_attribution_info is None
        assert (
            updated_association.account_attribution_state
            == AttributionState.NOT_PROCESSED
        )
        assert updated_association.account_attribution_info is None
        assert (
            updated_association.pipeline_attribution_state
            == AttributionState.NOT_PROCESSED
        )
        assert updated_association.pipeline_attribution_info is None

    async def test_clone_domain_crm_association_with_updates(
        self,
        domain_crm_association_service: DomainCRMAssociationService,
    ) -> None:
        # Arrange
        # Create the original association
        message_id = uuid4()
        thread_id = uuid4()
        organization_id = uuid4()
        contact_id = uuid4()
        account_id = uuid4()
        email = "<EMAIL>"
        created_by_user_id = uuid4()

        create_data = CreateEmailCrmAssociation(
            organization_id=organization_id,
            email=email,
            message_id=message_id,
            thread_id=thread_id,
            account_id=account_id,
            contact_id=contact_id,
            created_by_user_id=created_by_user_id,
        )

        original_association = (
            await domain_crm_association_service.create_domain_crm_association(
                create_data,
            )
        )

        # Define updates for the clone
        new_pipeline_id = uuid4()
        new_email = "<EMAIL>"
        cloning_user_id = uuid4()

        updates: dict[str, DomainCRMAssociationValue] = {
            "pipeline_id": new_pipeline_id,
            "email": new_email,
            "updated_by_user_id": cloning_user_id,
        }

        # Act
        cloned_association = await domain_crm_association_service.clone_domain_crm_association_with_updates(
            original_association.id,
            updates,
        )

        # Assert
        # New association has a different ID
        assert cloned_association.id != original_association.id

        # Verify updated fields
        assert cloned_association.pipeline_id == new_pipeline_id
        assert cloned_association.email == new_email
        assert cloned_association.updated_by_user_id == cloning_user_id

        # Verify fields that should remain the same
        assert cloned_association.message_id == message_id
        assert cloned_association.thread_id == thread_id
        assert cloned_association.organization_id == organization_id
        assert cloned_association.contact_id == contact_id
        assert cloned_association.account_id == account_id
        assert cloned_association.domain_type == DomainType.EMAIL

        # Verify timestamps
        assert cloned_association.created_at == original_association.created_at
        assert cloned_association.updated_at is not None
        assert cloned_association.updated_at != original_association.updated_at

    async def test_delete_domain_crm_association(
        self,
        domain_crm_association_service: DomainCRMAssociationService,
    ) -> None:
        # Arrange
        message_id = uuid4()
        thread_id = uuid4()
        organization_id = uuid4()
        contact_id = uuid4()
        account_id = uuid4()
        email = "<EMAIL>"
        created_by_user_id = uuid4()

        create_data = CreateEmailCrmAssociation(
            organization_id=organization_id,
            email=email,
            message_id=message_id,
            thread_id=thread_id,
            account_id=account_id,
            contact_id=contact_id,
            created_by_user_id=created_by_user_id,
        )

        created_association = (
            await domain_crm_association_service.create_domain_crm_association(
                create_data,
            )
        )

        user_id = uuid4()

        # Act
        await domain_crm_association_service.delete_domain_crm_association(
            created_association.id,
            user_id=user_id,
        )

        # Assert - Verify the association is not accessible after deletion
        with pytest.raises(ResourceNotFoundError):
            await domain_crm_association_service.get_domain_crm_association_by_id(
                created_association.id
            )

    async def test_bulk_create_email_associations_success_with_all_crm_context_fields(
        self,
        domain_crm_association_service: DomainCRMAssociationService,
    ) -> None:
        # Arrange
        created_by_user_id = uuid4()
        organization_id = uuid4()
        contact_id = uuid4()
        account_id = uuid4()
        pipeline_id = uuid4()

        associations = []
        for i in range(3):
            associations.append(
                CreateEmailCrmAssociation(
                    organization_id=organization_id,
                    email=f"test{i}@test.com",
                    message_id=uuid4(),
                    thread_id=uuid4(),
                    account_id=account_id,
                    contact_id=contact_id,
                    pipeline_id=pipeline_id,
                    created_by_user_id=created_by_user_id,
                )
            )

        # Act
        results = (
            await domain_crm_association_service.bulk_create_domain_crm_associations(
                associations,
            )
        )

        # Assert
        assert len(results) == 3
        for i, result in enumerate(results):
            assert result.domain_type == DomainType.EMAIL
            assert result.email == f"test{i}@test.com"
            assert result.account_id == account_id
            assert result.contact_id == contact_id
            assert result.pipeline_id == pipeline_id
            assert result.contact_attribution_state == AttributionState.NOT_PROCESSED
            assert result.contact_attribution_info is None
            assert result.account_attribution_state == AttributionState.NOT_PROCESSED
            assert result.account_attribution_info is None
            assert result.pipeline_attribution_state == AttributionState.NOT_PROCESSED
            assert result.pipeline_attribution_info is None

    async def test_bulk_create_email_associations_success_with_mixed_crm_context_fields(
        self,
        domain_crm_association_service: DomainCRMAssociationService,
    ) -> None:
        # Arrange
        created_by_user_id = uuid4()
        organization_id = uuid4()
        contact_id = uuid4()
        account_id = uuid4()
        pipeline_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()

        # Association with all CRM fields
        association1 = CreateEmailCrmAssociation(
            organization_id=organization_id,
            email="<EMAIL>",
            message_id=uuid4(),
            thread_id=uuid4(),
            account_id=account_id,
            contact_id=contact_id,
            pipeline_id=pipeline_id,
            created_by_user_id=created_by_user_id,
        )

        # Association with partial CRM fields
        association2 = CreateEmailCrmAssociation(
            organization_id=organization_id,
            email="<EMAIL>",
            message_id=uuid4(),
            thread_id=uuid4(),
            account_id=account_id,
            contact_id=contact_id,
            # No pipeline_id
            user_id=user_id,
            email_account_id=email_account_id,
            created_by_user_id=created_by_user_id,
        )

        associations = [association1, association2]

        # Act
        results = (
            await domain_crm_association_service.bulk_create_domain_crm_associations(
                associations,
            )
        )

        # Assert
        assert len(results) == 2

        # Check first association (with all CRM fields)
        assert results[0].domain_type == DomainType.EMAIL
        assert results[0].email == "<EMAIL>"
        assert results[0].account_id == account_id
        assert results[0].contact_id == contact_id
        assert results[0].pipeline_id == pipeline_id
        assert results[0].contact_attribution_state == AttributionState.NOT_PROCESSED
        assert results[0].contact_attribution_info is None
        assert results[0].account_attribution_state == AttributionState.NOT_PROCESSED
        assert results[0].account_attribution_info is None
        assert results[0].pipeline_attribution_state == AttributionState.NOT_PROCESSED
        assert results[0].pipeline_attribution_info is None

        # Check second association (with partial CRM fields)
        assert results[1].domain_type == DomainType.EMAIL
        assert results[1].email == "<EMAIL>"
        assert results[1].account_id == account_id
        assert results[1].contact_id == contact_id
        assert results[1].pipeline_id is None
        assert results[1].email_account_id == email_account_id
        assert results[1].contact_attribution_state == AttributionState.NOT_PROCESSED
        assert results[1].contact_attribution_info is None
        assert results[1].account_attribution_state == AttributionState.NOT_PROCESSED
        assert results[1].account_attribution_info is None
        assert results[1].pipeline_attribution_state == AttributionState.NOT_PROCESSED
        assert results[1].pipeline_attribution_info is None

    async def test_bulk_delete_meeting_domain_crm_associations(
        self,
        domain_crm_association_service: DomainCRMAssociationService,
    ) -> None:
        # Arrange
        organization_id = uuid4()
        created_by_user_id = uuid4()
        meeting_id = uuid4()
        email1 = "<EMAIL>"
        user_id = uuid4()

        # Create multiple meeting domain CRM associations
        association1 = CreateMeetingCrmAssociation(
            organization_id=organization_id,
            meeting_id=meeting_id,
            email=email1,
            user_id=None,
            created_by_user_id=created_by_user_id,
        )

        association2 = CreateMeetingCrmAssociation(
            organization_id=organization_id,
            meeting_id=meeting_id,
            email=None,
            user_id=user_id,
            created_by_user_id=created_by_user_id,
        )

        await domain_crm_association_service.create_domain_crm_association(association1)
        await domain_crm_association_service.create_domain_crm_association(association2)

        # Verify associations are created
        initial_associations = await domain_crm_association_service.get_domain_crm_associations_by_related_id(
            domain_id=meeting_id,
            domain_type=DomainType.MEETING,
        )
        assert len(initial_associations) == 2

        # Create delete requests
        delete_request1 = DeleteMeetingCrmAssociation(
            meeting_id=meeting_id,
            email=email1,
            user_id=None,
            organization_id=organization_id,
            deleted_by_user_id=created_by_user_id,
        )

        delete_request2 = DeleteMeetingCrmAssociation(
            meeting_id=meeting_id,
            email=None,
            user_id=user_id,
            organization_id=organization_id,
            deleted_by_user_id=created_by_user_id,
        )

        # Act
        await (
            domain_crm_association_service.bulk_delete_meeting_domain_crm_associations(
                [delete_request1, delete_request2]
            )
        )

        # Assert - Verify the associations are no longer returned (soft-deleted)
        remaining_associations = await domain_crm_association_service.get_domain_crm_associations_by_related_id(
            domain_id=meeting_id,
            domain_type=DomainType.MEETING,
        )
        assert len(remaining_associations) == 0

    async def test_create_task_association_success_with_all_fields(
        self,
        domain_crm_association_service: DomainCRMAssociationService,
    ) -> None:
        # Arrange
        task_id = uuid4()
        organization_id = uuid4()
        created_by_user_id = uuid4()
        contact_id = uuid4()
        account_id = uuid4()
        pipeline_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()
        sequence_id = uuid4()
        sequence_enrollment_id = uuid4()
        sequence_step_id = uuid4()
        sequence_step_variant_id = uuid4()
        sequence_step_execution_id = uuid4()
        global_thread_id = uuid4()
        meeting_id = uuid4()
        call_id = uuid4()

        create_data = CreateTaskCrmAssociation(
            organization_id=organization_id,
            task_id=task_id,
            contact_id=contact_id,
            account_id=account_id,
            pipeline_id=pipeline_id,
            user_id=user_id,
            email_account_id=email_account_id,
            created_by_user_id=created_by_user_id,
            sequence_id=sequence_id,
            sequence_enrollment_id=sequence_enrollment_id,
            sequence_step_id=sequence_step_id,
            sequence_step_variant_id=sequence_step_variant_id,
            sequence_step_execution_id=sequence_step_execution_id,
            global_thread_id=global_thread_id,
            meeting_id=meeting_id,
            call_id=call_id,
        )

        # Act
        result = await domain_crm_association_service.create_domain_crm_association(
            create_data,
        )

        # Assert
        assert result.domain_type == DomainType.TASK
        assert result.task_id == task_id
        assert result.organization_id == organization_id
        assert result.contact_id == contact_id
        assert result.account_id == account_id
        assert result.pipeline_id == pipeline_id
        assert result.user_id == user_id
        assert result.email_account_id == email_account_id
        assert result.sequence_id == sequence_id
        assert result.sequence_enrollment_id == sequence_enrollment_id
        assert result.sequence_step_id == sequence_step_id
        assert result.sequence_step_variant_id == sequence_step_variant_id
        assert result.sequence_step_execution_id == sequence_step_execution_id
        assert result.global_thread_id == global_thread_id
        assert result.meeting_id == meeting_id
        assert result.call_id == call_id
        assert result.contact_attribution_state == AttributionState.NOT_PROCESSED
        assert result.contact_attribution_info is None
        assert result.account_attribution_state == AttributionState.NOT_PROCESSED
        assert result.account_attribution_info is None
        assert result.pipeline_attribution_state == AttributionState.NOT_PROCESSED
        assert result.pipeline_attribution_info is None

    async def test_bulk_create_task_associations_success_with_mixed_fields(
        self,
        domain_crm_association_service: DomainCRMAssociationService,
    ) -> None:
        # Arrange
        created_by_user_id = uuid4()
        organization_id = uuid4()
        contact_id = uuid4()
        account_id = uuid4()

        # Task association with all fields
        task_id1 = uuid4()
        pipeline_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()
        sequence_id = uuid4()
        sequence_enrollment_id = uuid4()
        sequence_step_id = uuid4()
        sequence_step_variant_id = uuid4()
        sequence_step_execution_id = uuid4()
        global_thread_id = uuid4()
        meeting_id = uuid4()
        call_id = uuid4()

        task_association1 = CreateTaskCrmAssociation(
            organization_id=organization_id,
            task_id=task_id1,
            contact_id=contact_id,
            account_id=account_id,
            pipeline_id=pipeline_id,
            user_id=user_id,
            email_account_id=email_account_id,
            created_by_user_id=created_by_user_id,
            sequence_id=sequence_id,
            sequence_enrollment_id=sequence_enrollment_id,
            sequence_step_id=sequence_step_id,
            sequence_step_variant_id=sequence_step_variant_id,
            sequence_step_execution_id=sequence_step_execution_id,
            global_thread_id=global_thread_id,
            meeting_id=meeting_id,
            call_id=call_id,
        )

        # Task association with minimal fields
        task_id2 = uuid4()
        task_association2 = CreateTaskCrmAssociation(
            organization_id=organization_id,
            task_id=task_id2,
            contact_id=contact_id,
            created_by_user_id=created_by_user_id,
        )

        associations = [task_association1, task_association2]

        # Act
        results = (
            await domain_crm_association_service.bulk_create_domain_crm_associations(
                associations,
            )
        )

        # Assert
        assert len(results) == 2

        # Check first association (with all fields)
        assert results[0].domain_type == DomainType.TASK
        assert results[0].task_id == task_id1
        assert results[0].organization_id == organization_id
        assert results[0].contact_id == contact_id
        assert results[0].account_id == account_id
        assert results[0].pipeline_id == pipeline_id
        assert results[0].user_id == user_id
        assert results[0].email_account_id == email_account_id
        assert results[0].sequence_id == sequence_id
        assert results[0].sequence_enrollment_id == sequence_enrollment_id
        assert results[0].sequence_step_id == sequence_step_id
        assert results[0].sequence_step_variant_id == sequence_step_variant_id
        assert results[0].sequence_step_execution_id == sequence_step_execution_id
        assert results[0].global_thread_id == global_thread_id
        assert results[0].meeting_id == meeting_id
        assert results[0].call_id == call_id
        assert results[0].contact_attribution_state == AttributionState.NOT_PROCESSED
        assert results[0].contact_attribution_info is None
        assert results[0].account_attribution_state == AttributionState.NOT_PROCESSED
        assert results[0].account_attribution_info is None
        assert results[0].pipeline_attribution_state == AttributionState.NOT_PROCESSED
        assert results[0].pipeline_attribution_info is None

        # Check second association (with minimal fields)
        assert results[1].domain_type == DomainType.TASK
        assert results[1].task_id == task_id2
        assert results[1].organization_id == organization_id
        assert results[1].contact_id == contact_id
        assert results[1].account_id is None
        assert results[1].pipeline_id is None
        assert results[1].user_id is None
        assert results[1].sequence_id is None
        assert results[1].meeting_id is None
        assert results[1].call_id is None
        assert results[1].contact_attribution_state == AttributionState.NOT_PROCESSED
        assert results[1].contact_attribution_info is None
