from uuid import uuid4

import pytest
from faker import Faker

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdSelectListIdentifier,
)
from salestech_be.common.type.metadata.common import ObjectAccessStatus
from salestech_be.core.contact.service.contact_data_integrity_service import (
    ContactDataIntegrityService,
)
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.db.models.contact import (
    CreateContactRequest,
    CreateDbContactRequest,
)


async def test_acquire_lock_for_integrity_job(
    faker: Faker,
    contact_service: ContactService,
    select_list_service: InternalSelectListService,
    contact_data_integrity_service: ContactDataIntegrityService,
) -> None:
    organization_id = uuid4()
    locking_job_id = uuid4()
    user_id = uuid4()

    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id,
        user_id=user_id,
    )

    default_stage_list = (
        await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
    )
    assert default_stage_list and default_stage_list.default_or_initial_active_value

    created_contact = await contact_service.create_contact_with_contact_channels(
        organization_id=organization_id,
        user_id=user_id,
        create_contact_with_contact_channel_request=CreateContactRequest(
            contact=CreateDbContactRequest(
                first_name=faker.first_name(),
                last_name=faker.last_name(),
                display_name=faker.name(),
                owner_user_id=user_id,
                created_by_user_id=user_id,
                stage_id=default_stage_list.default_or_initial_active_value.id,
            ),
            contact_account_roles=[],
            contact_emails=[],
        ),
    )

    # success case
    updated_contact = (
        await contact_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=locking_job_id,
            contact_id=created_contact.id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )

    assert updated_contact.id == created_contact.id
    assert updated_contact.integrity_job_started_at is not None
    assert updated_contact.integrity_job_started_by_user_id == user_id
    assert updated_contact.integrity_job_started_by_job_ids == [locking_job_id]
    assert updated_contact.access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING

    # already locked case
    with pytest.raises(ResourceNotFoundError):
        await contact_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=locking_job_id,
            contact_id=created_contact.id,
            user_id=user_id,
            organization_id=organization_id,
        )

    # contact not found case
    with pytest.raises(ResourceNotFoundError):
        await contact_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=locking_job_id,
            contact_id=uuid4(),
            user_id=user_id,
            organization_id=organization_id,
        )


async def test_free_lock_for_integrity_job(
    faker: Faker,
    contact_service: ContactService,
    select_list_service: InternalSelectListService,
    contact_data_integrity_service: ContactDataIntegrityService,
) -> None:
    organization_id = uuid4()
    locking_job_id = uuid4()
    user_id = uuid4()

    await select_list_service.bootstrap_all_direct_std_select_lists(
        organization_id=organization_id,
        user_id=user_id,
    )

    default_stage_list = (
        await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
    )
    assert default_stage_list and default_stage_list.default_or_initial_active_value

    created_contact = await contact_service.create_contact_with_contact_channels(
        organization_id=organization_id,
        user_id=user_id,
        create_contact_with_contact_channel_request=CreateContactRequest(
            contact=CreateDbContactRequest(
                first_name=faker.first_name(),
                last_name=faker.last_name(),
                display_name=faker.name(),
                owner_user_id=user_id,
                created_by_user_id=user_id,
                stage_id=default_stage_list.default_or_initial_active_value.id,
            ),
            contact_account_roles=[],
            contact_emails=[],
        ),
    )

    # success case (lock acquired)
    updated_contact = (
        await contact_data_integrity_service.acquire_lock_for_integrity_job(
            job_id=locking_job_id,
            contact_id=created_contact.id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )

    assert updated_contact.id == created_contact.id
    assert updated_contact.integrity_job_started_at is not None
    assert updated_contact.integrity_job_started_by_user_id == user_id
    assert updated_contact.integrity_job_started_by_job_ids == [locking_job_id]
    assert updated_contact.access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING

    # success case (lock free)
    updated_contact = await contact_data_integrity_service.free_lock_for_integrity_job(
        job_id=locking_job_id,
        contact_id=created_contact.id,
        user_id=user_id,
        organization_id=organization_id,
    )

    assert updated_contact.id == created_contact.id
    assert updated_contact.integrity_job_started_at is None
    assert updated_contact.integrity_job_started_by_user_id is None
    assert updated_contact.integrity_job_started_by_job_ids == []
    assert updated_contact.access_status == ObjectAccessStatus.ACTIVE

    # contact not found case
    with pytest.raises(ResourceNotFoundError):
        await contact_data_integrity_service.free_lock_for_integrity_job(
            job_id=locking_job_id,
            contact_id=uuid4(),
            user_id=user_id,
            organization_id=organization_id,
        )
