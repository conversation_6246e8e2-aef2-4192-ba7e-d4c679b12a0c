from uuid import uuid4

import pytest
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from salestech_be.core.meeting.meeting_data_integrity_service import (
    MeetingDataIntegrityService,
    get_meeting_data_integrity_service,
)
from salestech_be.db.dao.meeting_repository import MeetingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.contact_account_association import (
    ContactAccountAssociation,
)
from salestech_be.db.models.meeting import (
    MeetingAttendee,
    MeetingInvitee,
    MeetingReferenceIdType,
)
from tests.util.factories import MeetingFactory


class TestMeetingDataIntegrityService:
    @pytest.fixture(scope="class")
    def meeting_data_integrity_service(
        self,
        _engine: DatabaseEngine,
    ) -> MeetingDataIntegrityService:
        return get_meeting_data_integrity_service(db_engine=_engine)

    @pytest.fixture(scope="class")
    def meeting_repository(
        self,
        _engine: DatabaseEngine,
    ) -> MeetingRepository:
        return MeetingRepository(engine=_engine)

    async def test_find_meetings_by_contact_id(
        self,
        meeting_data_integrity_service: MeetingDataIntegrityService,
        meeting_repository: MeetingRepository,
        meeting_factory: MeetingFactory,
    ) -> None:
        contact_id = uuid4()
        contact_id_2 = uuid4()
        organization_id = uuid4()
        account_id = uuid4()
        account_id_2 = uuid4()
        email = "<EMAIL>"
        email_2 = "<EMAIL>"

        meeting_id = uuid4()
        meeting_id_2 = uuid4()
        meeting_id_3 = uuid4()
        meeting_id_4 = uuid4()

        # Meeting with contact in invitees with email and account
        await meeting_repository.insert(
            meeting_factory.build(
                id=meeting_id,
                organization_id=organization_id,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                invitees=[
                    MeetingInvitee(
                        contact_id=contact_id,
                        contact_email=email,
                        account_id=account_id,
                    )
                ],
                attendees=[
                    MeetingAttendee(
                        contact_id=contact_id_2,
                        contact_email=email_2,
                        account_id=account_id_2,
                    )
                ],
            )
        )

        # Meeting with contact in attendees with different email
        await meeting_repository.insert(
            meeting_factory.build(
                id=meeting_id_2,
                organization_id=organization_id,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                invitees=None,
                attendees=[
                    MeetingAttendee(
                        contact_id=contact_id,
                        contact_email=email_2,
                        account_id=account_id,
                    )
                ],
            )
        )

        # Meeting with contact in invitees with same email but different account
        await meeting_repository.insert(
            meeting_factory.build(
                id=meeting_id_3,
                organization_id=organization_id,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                invitees=[
                    MeetingInvitee(
                        contact_id=contact_id,
                        contact_email=email,
                        account_id=account_id_2,
                    )
                ],
                attendees=None,
            )
        )

        # Meeting with contact in both invitees and attendees with same email and account
        await meeting_repository.insert(
            meeting_factory.build(
                id=meeting_id_4,
                organization_id=organization_id,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                invitees=[
                    MeetingInvitee(
                        contact_id=contact_id,
                        contact_email=email,
                        account_id=account_id,
                    )
                ],
                attendees=[
                    MeetingAttendee(
                        contact_id=contact_id,
                        contact_email=email,
                        account_id=account_id,
                    )
                ],
            )
        )

        # Test 1: Find all meetings by contact_id only
        result = await meeting_data_integrity_service.find_meetings_by_contact_id(
            contact_id=contact_id,
            organization_id=organization_id,
        )
        assert len(result) == 4
        assert {m.id for m in result} == {
            meeting_id,
            meeting_id_2,
            meeting_id_3,
            meeting_id_4,
        }

        # Test 2: Find meetings by contact_id and specific email
        result = await meeting_data_integrity_service.find_meetings_by_contact_id(
            contact_id=contact_id,
            organization_id=organization_id,
            email=email,
        )
        assert len(result) == 3
        assert {m.id for m in result} == {meeting_id, meeting_id_3, meeting_id_4}

        # Test 3: Find meetings by contact_id and specific account_id
        result = await meeting_data_integrity_service.find_meetings_by_contact_id(
            contact_id=contact_id,
            organization_id=organization_id,
            account_id=account_id,
        )
        assert len(result) == 3
        assert {m.id for m in result} == {meeting_id, meeting_id_2, meeting_id_4}

        # Test 4: Find meetings by contact_id, email, and account_id
        result = await meeting_data_integrity_service.find_meetings_by_contact_id(
            contact_id=contact_id,
            organization_id=organization_id,
            email=email,
            account_id=account_id,
        )
        assert len(result) == 2
        assert {m.id for m in result} == {meeting_id, meeting_id_4}

        # Test 5: Find meetings with non-existent contact
        result = await meeting_data_integrity_service.find_meetings_by_contact_id(
            contact_id=uuid4(),
            organization_id=organization_id,
        )
        assert len(result) == 0

        # Test 6: Find meetings with existing contact but non-matching email
        result = await meeting_data_integrity_service.find_meetings_by_contact_id(
            contact_id=contact_id,
            organization_id=organization_id,
            email="<EMAIL>",
        )
        assert len(result) == 0

        # Test 7: Find meetings with existing contact but non-matching account
        result = await meeting_data_integrity_service.find_meetings_by_contact_id(
            contact_id=contact_id,
            organization_id=organization_id,
            account_id=uuid4(),
        )
        assert len(result) == 0

    async def test_find_meetings_by_account_id(
        self,
        meeting_data_integrity_service: MeetingDataIntegrityService,
        meeting_repository: MeetingRepository,
        meeting_factory: MeetingFactory,
    ) -> None:
        account_id = uuid4()
        organization_id = uuid4()
        meeting_id_1 = uuid4()

        # Create meetings with the account in invitees and attendees
        await meeting_repository.insert(
            meeting_factory.build(
                id=meeting_id_1,
                organization_id=organization_id,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                invitees=[MeetingInvitee(account_id=account_id)],
                attendees=[MeetingAttendee(account_id=account_id)],
                account_id=account_id,  # Also set as main account
            )
        )

        # Test finding meetings
        result = await meeting_data_integrity_service.find_meetings_by_account_id(
            account_id=account_id,
            organization_id=organization_id,
        )

        # Should find both meetings without duplicates
        assert len(result) == 1
        assert {m.id for m in result} == {meeting_id_1}

        # Test with non-existent account
        result = await meeting_data_integrity_service.find_meetings_by_account_id(
            account_id=uuid4(),
            organization_id=organization_id,
        )
        assert len(result) == 0

    async def test_merge_contacts_in_meeting_participants(
        self,
        meeting_data_integrity_service: MeetingDataIntegrityService,
        meeting_repository: MeetingRepository,
        meeting_factory: MeetingFactory,
    ) -> None:
        contact_id = uuid4()
        replaced_contact_id = uuid4()
        other_contact_id = uuid4()
        organization_id = uuid4()
        account_id = uuid4()
        other_account_id = uuid4()
        meeting_id = uuid4()
        contact_email = "<EMAIL>"
        other_contact_email = "<EMAIL>"

        await meeting_repository.insert(
            meeting_factory.build(
                id=meeting_id,
                organization_id=organization_id,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                invitees=[
                    MeetingInvitee(
                        contact_id=contact_id,
                        account_id=account_id,
                        contact_email=contact_email,
                    ),
                    MeetingInvitee(
                        contact_id=replaced_contact_id,  # Duplicate after merge
                        account_id=account_id,
                        contact_email=contact_email,
                    ),
                    MeetingInvitee(
                        contact_id=replaced_contact_id,  # should not affected
                        account_id=other_account_id,
                        contact_email=contact_email,
                    ),
                    MeetingInvitee(
                        contact_id=other_contact_id,  # should not affected
                        account_id=account_id,
                        contact_email=other_contact_email,
                    ),
                ],
                attendees=[],
            )
        )

        # Test merging contacts
        (
            original,
            updated,
        ) = await meeting_data_integrity_service.merge_contacts_in_meeting_participants(
            meeting_id=meeting_id,
            contact_id=contact_id,
            replaced_contact_id=replaced_contact_id,
            organization_id=organization_id,
        )

        # Verify original meeting state
        assert original.id == meeting_id
        assert original.invitees
        assert len(original.invitees) == 4
        assert {
            (i.contact_id, i.account_id, i.contact_email) for i in original.invitees
        } == {
            (contact_id, account_id, contact_email),
            (replaced_contact_id, account_id, contact_email),
            (replaced_contact_id, other_account_id, contact_email),
            (other_contact_id, account_id, other_contact_email),
        }

        # Verify updated meeting state
        assert updated.id == meeting_id
        assert updated.invitees
        assert len(updated.invitees) == 3
        assert {
            (i.contact_id, i.account_id, i.contact_email) for i in updated.invitees
        } == {
            (replaced_contact_id, account_id, contact_email),
            (replaced_contact_id, other_account_id, contact_email),
            (other_contact_id, account_id, other_contact_email),
        }

    async def test_merge_accounts_in_meeting_participants(
        self,
        meeting_data_integrity_service: MeetingDataIntegrityService,
        meeting_repository: MeetingRepository,
        meeting_factory: MeetingFactory,
    ) -> None:
        contact_id = uuid4()
        account_id = uuid4()
        replaced_account_id = uuid4()
        organization_id = uuid4()
        meeting_id = uuid4()

        # Create meeting with duplicate account references
        await meeting_repository.insert(
            meeting_factory.build(
                id=meeting_id,
                organization_id=organization_id,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                invitees=[
                    MeetingInvitee(account_id=account_id),
                    MeetingInvitee(account_id=account_id),  # Duplicate
                ],
                attendees=[
                    MeetingAttendee(contact_id=contact_id, account_id=account_id),
                    MeetingAttendee(account_id=account_id),  # not duplicate
                ],
            )
        )

        (
            original,
            updated,
        ) = await meeting_data_integrity_service.merge_accounts_in_meeting_participants(
            meeting_id=meeting_id,
            account_id=account_id,
            replaced_account_id=replaced_account_id,
            organization_id=organization_id,
        )

        assert original.id == meeting_id
        assert updated.id == meeting_id
        assert updated.invitees and updated.attendees
        assert len(updated.invitees) == 1
        assert len(updated.attendees) == 2
        assert updated.invitees[0].account_id == replaced_account_id
        assert updated.attendees[0].account_id == replaced_account_id
        assert updated.attendees[1].account_id == replaced_account_id

    async def test_move_contact_to_another_account(
        self,
        meeting_data_integrity_service: MeetingDataIntegrityService,
        meeting_repository: MeetingRepository,
        meeting_factory: MeetingFactory,
    ) -> None:
        contact_id = uuid4()
        old_account_id = uuid4()
        new_account_id = uuid4()
        organization_id = uuid4()
        meeting_id = uuid4()
        email = "<EMAIL>"
        other_email = "<EMAIL>"

        await meeting_repository.insert(
            meeting_factory.build(
                id=meeting_id,
                organization_id=organization_id,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                invitees=[
                    MeetingInvitee(
                        contact_id=contact_id,
                        account_id=old_account_id,
                    ),
                    MeetingInvitee(
                        contact_id=contact_id,
                        account_id=old_account_id,
                    ),
                ],
                attendees=[
                    MeetingAttendee(
                        contact_id=contact_id,
                        account_id=old_account_id,
                        contact_email=email,
                    ),
                    MeetingAttendee(
                        contact_id=contact_id,
                        account_id=old_account_id,
                        contact_email=None,
                    ),
                    MeetingAttendee(
                        contact_id=contact_id,
                        account_id=old_account_id,
                        contact_email=other_email,
                    ),
                ],
            )
        )

        # test move with email filter
        (
            original,
            updated,
        ) = await meeting_data_integrity_service.move_contact_to_another_account_in_meeting_participants(
            meeting_id=meeting_id,
            contact_id=contact_id,
            account_id=old_account_id,
            replaced_account_id=new_account_id,
            organization_id=organization_id,
            email=email,
        )

        assert original.id == meeting_id
        assert updated.id == meeting_id
        assert updated.invitees and updated.attendees
        assert len(updated.invitees) == 1
        assert len(updated.attendees) == 3
        assert {
            (i.contact_id, i.account_id, i.contact_email) for i in updated.invitees
        } == {
            (contact_id, old_account_id, None),
        }

        assert {
            (i.contact_id, i.account_id, i.contact_email) for i in updated.attendees
        } == {
            (contact_id, new_account_id, email),
            (contact_id, old_account_id, None),
            (contact_id, old_account_id, other_email),
        }

        # test futher move without email filter, based on result above
        (
            another_original,
            another_updated,
        ) = await meeting_data_integrity_service.move_contact_to_another_account_in_meeting_participants(
            meeting_id=meeting_id,
            contact_id=contact_id,
            account_id=old_account_id,
            replaced_account_id=new_account_id,
            organization_id=organization_id,
        )

        assert another_original.id == meeting_id
        assert another_updated.id == meeting_id
        assert another_updated.invitees and another_updated.attendees
        assert len(another_updated.invitees) == 1
        assert len(another_updated.attendees) == 3
        assert {
            (i.contact_id, i.account_id, i.contact_email)
            for i in another_updated.invitees
        } == {
            (contact_id, new_account_id, None),
        }

        assert {
            (i.contact_id, i.account_id, i.contact_email)
            for i in another_updated.attendees
        } == {
            (contact_id, new_account_id, email),
            (contact_id, new_account_id, None),
            (contact_id, new_account_id, other_email),
        }

    async def test_replace_contact_by_email(
        self,
        mocker: MockerFixture,
        meeting_data_integrity_service: MeetingDataIntegrityService,
        meeting_repository: MeetingRepository,
        meeting_factory: MeetingFactory,
    ) -> None:
        contact_id = uuid4()
        replaced_contact_id = uuid4()
        account_id = uuid4()
        organization_id = uuid4()
        meeting_id = uuid4()
        email = "<EMAIL>"
        another_email = "<EMAIL>"

        # Create meeting with duplicate contact-email pairs
        await meeting_repository.insert(
            meeting_factory.build(
                id=meeting_id,
                organization_id=organization_id,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                invitees=[
                    MeetingInvitee(
                        contact_id=contact_id,
                        contact_email=email,
                        account_id=account_id,
                    ),
                    MeetingInvitee(
                        contact_id=contact_id,
                        contact_email=email,
                        account_id=account_id,
                    ),
                ],
                attendees=[
                    MeetingAttendee(
                        contact_id=contact_id,
                        contact_email=email,
                        account_id=account_id,
                    ),
                    MeetingAttendee(
                        contact_id=contact_id,
                        contact_email=another_email,
                        account_id=account_id,
                    ),
                ],
            )
        )

        (
            original,
            updated,
        ) = await meeting_data_integrity_service.replace_contact_by_email_in_meeting_participants(
            meeting_id=meeting_id,
            contact_id=contact_id,
            email=email,
            replaced_contact_id=replaced_contact_id,
            organization_id=organization_id,
        )

        assert original.id == meeting_id
        assert updated.id == meeting_id
        assert updated.invitees and updated.attendees
        assert len(updated.invitees) == 1
        assert len(updated.attendees) == 2
        assert {
            (i.contact_id, i.account_id, i.contact_email) for i in updated.invitees
        } == {
            (replaced_contact_id, None, email),
        }

        assert {
            (i.contact_id, i.account_id, i.contact_email) for i in updated.attendees
        } == {
            (replaced_contact_id, None, email),
            (contact_id, account_id, another_email),
        }

        mocker.patch(
            "salestech_be.core.contact.service.contact_service.ContactService.list_active_contact_account_associations",
            return_value=[
                ContactAccountAssociation(
                    id=uuid4(),
                    organization_id=organization_id,
                    contact_id=contact_id,
                    account_id=account_id,
                    is_primary=True,
                    created_by_user_id=uuid4(),
                    updated_by_user_id=uuid4(),
                )
            ],
        )

        # test futher replace with existed association, based on result above
        (
            another_original,
            another_updated,
        ) = await meeting_data_integrity_service.replace_contact_by_email_in_meeting_participants(
            meeting_id=meeting_id,
            contact_id=contact_id,
            email=another_email,
            replaced_contact_id=replaced_contact_id,
            organization_id=organization_id,
        )

        assert another_original.id == meeting_id
        assert another_updated.id == meeting_id
        assert another_updated.invitees and another_updated.attendees
        assert len(another_updated.invitees) == 1
        assert len(another_updated.attendees) == 2
        assert {
            (i.contact_id, i.account_id, i.contact_email)
            for i in another_updated.invitees
        } == {
            (replaced_contact_id, None, email),
        }

        assert {
            (i.contact_id, i.account_id, i.contact_email)
            for i in another_updated.attendees
        } == {
            (replaced_contact_id, None, email),
            (replaced_contact_id, account_id, another_email),
        }

    async def test_replace_account_id_in_meeting(
        self,
        meeting_data_integrity_service: MeetingDataIntegrityService,
        meeting_repository: MeetingRepository,
        meeting_factory: MeetingFactory,
    ) -> None:
        old_account_id = uuid4()
        new_account_id = uuid4()
        meeting_id = uuid4()
        organization_id = uuid4()

        # Create meeting with account_id
        await meeting_repository.insert(
            meeting_factory.build(
                id=meeting_id,
                organization_id=organization_id,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                account_id=old_account_id,
            )
        )

        updated = await meeting_data_integrity_service.replace_account_id_in_meeting(
            meeting_id=meeting_id,
            account_id=old_account_id,
            new_account_id=new_account_id,
        )

        assert updated.id == meeting_id
        assert updated.account_id == new_account_id

        # Test replacing with None
        updated = await meeting_data_integrity_service.replace_account_id_in_meeting(
            meeting_id=meeting_id,
            account_id=new_account_id,
            new_account_id=None,
        )

        assert updated.id == meeting_id
        assert updated.account_id is None

    async def test_replace_pipeline_id_in_meeting(
        self,
        meeting_data_integrity_service: MeetingDataIntegrityService,
        meeting_repository: MeetingRepository,
        meeting_factory: MeetingFactory,
    ) -> None:
        old_pipeline_id = uuid4()
        new_pipeline_id = uuid4()
        new_pipeline_select_list_value_id = uuid4()
        meeting_id = uuid4()
        organization_id = uuid4()

        # Create meeting with pipeline_id
        await meeting_repository.insert(
            meeting_factory.build(
                id=meeting_id,
                organization_id=organization_id,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                pipeline_id=old_pipeline_id,
            )
        )

        updated = await meeting_data_integrity_service.replace_pipeline_id_in_meeting(
            meeting_id=meeting_id,
            pipeline_id=old_pipeline_id,
            new_pipeline_id=new_pipeline_id,
            new_pipeline_select_list_value_id=new_pipeline_select_list_value_id,
        )

        assert updated.id == meeting_id
        assert updated.pipeline_id == new_pipeline_id
        assert (
            updated.pipeline_select_list_value_id == new_pipeline_select_list_value_id
        )

        # Test replacing with None
        updated = await meeting_data_integrity_service.replace_pipeline_id_in_meeting(
            meeting_id=meeting_id,
            pipeline_id=new_pipeline_id,
            new_pipeline_id=None,
            new_pipeline_select_list_value_id=None,
        )

        assert updated.id == meeting_id
        assert updated.pipeline_id is None
        assert updated.pipeline_select_list_value_id is None
