import uuid
from collections.abc import Awaitable, Callable
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

import pytest
from sqlalchemy import text
from temporalio.client import (
    WorkflowExecutionDescription,
    WorkflowExecutionStatus,
    WorkflowHandle,
)

from salestech_be.common.exception.exception import InvalidArgumentError
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.common import StandardObjectIdentifier
from salestech_be.core.imports.models.import_job import (
    FileDupeResolution,
    FileImportType,
    FileMetadata,
    ImportConfiguration,
    ImportCsvJobStatus,
    ImportJob,
    ImportMetadata,
    ObjectImportMode,
    ObjectMapping,
)
from salestech_be.core.imports.repository.import_job_repository import (
    ImportJobRepository,
)
from salestech_be.core.imports.service.import_csv_job_review_service import (
    ImportCsvJobReviewService,
)
from salestech_be.core.imports.types import ImportCsvJobDetail
from salestech_be.db.models.core.constants import TableName
from salestech_be.db.models.import_csv_job_review_db import (
    ImportCsvJobReviewDb,
    ImportCsvJobReviewStatusDb,
)
from salestech_be.ree_logging import get_logger
from salestech_be.web.api.organization.schema import OrganizationResponse

logger = get_logger()


async def _delete_all_import_jobs(
    import_job_repository: ImportJobRepository,
) -> None:
    """
    Test-only delete for test setup. Make sure this is a TEST environment.  (CI or local)
    Be careful with this, delete should not happen in production, only archive.
    """
    stmt = text(
        f"""
        delete from {TableName.import_csv_job}
        returning *
        """  # noqa: S608
    )
    rows_deleted = await import_job_repository.engine.all(stmt)

    # Be loud about the deletion
    for deleted in rows_deleted:
        logger.info(f"TEST-ONLY-DELETE of import_job:({deleted})")


async def test_create_list_get_job_review(
    import_csv_job_review_service: ImportCsvJobReviewService,
    make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
) -> None:
    """
    Tests the flow of creating, listing, and getting details for an ImportCsvJobReview,
    including fetching the original filename.
    """
    await _delete_all_import_jobs(
        import_job_repository=import_csv_job_review_service.import_job_service._repository,
    )
    user_id, organization_id = await make_user_org()
    organization_ref = (
        await import_csv_job_review_service.organization_service.get_organization_by_id(
            organization_id
        )
    )

    # 1. Prepare test data
    test_display_name = f"Test Job Review - {uuid4()}"
    file_id = uuid4()
    csv_filename = "superDuperUberTest.csv"

    # Minimal valid ImportMetadata
    import_metadata = ImportMetadata(
        display_name=test_display_name,
        file_ids=[],
        file_metadata=[
            FileMetadata(
                file_id=file_id,
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                        column_mappings=[],  # No column mappings needed for draft creation
                    )
                ],
                association_label_mapping=[],
            )
        ],
        import_configuration=ImportConfiguration(
            object_identifiers=[
                StandardObjectIdentifier(object_name=StdObjectIdentifiers.account)
            ],
            file_import_type=FileImportType.SINGLE_FILE,
            object_import_mode=ObjectImportMode.CREATE_ONLY,
            file_dupe_resolution=FileDupeResolution.USE_FIRST,
        ),
    )

    # 2. Create the job review
    created_review = await import_csv_job_review_service.create_job_review(
        organization_id=organization_id,
        submitter_user_id=user_id,
        import_metadata=import_metadata,
    )

    # Assert creation results
    assert created_review is not None
    assert created_review.organization_id == organization_id
    assert created_review.submitter_user_id == user_id
    assert created_review.display_name == test_display_name
    assert created_review.status == ImportCsvJobReviewStatusDb.PENDING_REVIEW
    assert created_review.import_csv_job_id is not None
    assert created_review.reviewer_user_id is None
    assert created_review.reviewer_notes is None
    assert created_review.archived_at is None

    # 3. List job reviews
    job_reviews = await import_csv_job_review_service.list_job_reviews(
        organization_id=organization_id,
        statuses=[
            ImportCsvJobReviewStatusDb.PENDING_REVIEW,
            ImportCsvJobReviewStatusDb.REJECTED,
        ],
        limit=10,
        offset=0,
    )

    # Assert the created review is in the list
    assert any(review.id == created_review.id for review in job_reviews)

    # 4. Get job review detail - mock file_service interaction here
    with patch.object(
        import_csv_job_review_service.import_job_service.file_service,
        "get_original_filename",
        new_callable=AsyncMock,
        return_value=csv_filename,
    ) as mock_get_filename:
        (
            review_detail,
            import_job_detail,
            organization,
        ) = await import_csv_job_review_service.get_job_review_detail(
            created_review.id, None
        )
        # Assert mock was called correctly
        mock_get_filename.assert_awaited_once_with(
            file_id=file_id, organization_id=organization_id
        )

    # Assert the details
    def assert_the_details(
        review_detail: ImportCsvJobReviewDb,
        import_job_detail: ImportJob | None,
        organization: OrganizationResponse | None,
    ) -> None:
        assert review_detail is not None
        assert review_detail.id == created_review.id
        assert review_detail.organization_id == organization_id

        assert import_job_detail is not None
        assert import_job_detail.id == created_review.import_csv_job_id
        assert import_job_detail.organization_id == organization_id
        assert import_job_detail.created_by_user_id == user_id
        assert import_job_detail.display_name == test_display_name
        assert import_job_detail.status == ImportCsvJobStatus.DRAFT
        assert import_job_detail.configuration is not None
        assert import_job_detail.metadata is not None
        assert (
            import_job_detail.metadata.file_id == file_id
        )  # Check if file metadata was saved
        # Assert original filename was populated
        assert import_job_detail.metadata.original_filename == csv_filename

        # 4b. Verify the response pydantic model will work.
        import_job_detail_response = ImportCsvJobDetail.from_db(import_job_detail)
        assert import_job_detail_response is not None
        assert import_job_detail_response.id == import_job_detail.id
        assert (
            import_job_detail_response.organization_id
            == import_job_detail.organization_id
        )
        assert (
            import_job_detail_response.created_by_user_id
            == import_job_detail.created_by_user_id
        )
        assert import_job_detail_response.display_name == import_job_detail.display_name
        assert import_job_detail_response.status == import_job_detail.status
        assert import_job_detail_response.workflow_id == ""

        assert organization is not None
        assert organization_ref is not None
        assert organization.display_name == organization_ref.display_name

    assert_the_details(review_detail, import_job_detail, organization)

    # 4b. Get job review detail with organization_id_to_enforce
    with patch.object(
        import_csv_job_review_service.import_job_service.file_service,
        "get_original_filename",
        new_callable=AsyncMock,
        return_value=csv_filename,
    ) as mock_get_filename:
        (
            review_detail_2,
            import_job_detail_2,
            organization_2,
        ) = await import_csv_job_review_service.get_job_review_detail(
            created_review.id, organization_id
        )
        # Assert mock was called correctly
        mock_get_filename.assert_awaited_once_with(
            file_id=file_id, organization_id=organization_id
        )

    assert_the_details(review_detail_2, import_job_detail_2, organization_2)


async def test_reject_job_review(
    import_csv_job_review_service: ImportCsvJobReviewService,
    make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
) -> None:
    """
    Tests the flow of rejecting an ImportCsvJobReview.
    """
    await _delete_all_import_jobs(
        import_job_repository=import_csv_job_review_service.import_job_service._repository,
    )

    submitter_user_id, organization_id = await make_user_org()
    reviewer_user_id = uuid4()  # Simulate a different user reviewing

    # 1. Create a job review first (similar setup to test_create_list_get_job_review)
    test_display_name = f"Test Job Review for Rejection - {uuid4()}"
    file_id = uuid4()
    import_metadata = ImportMetadata(
        display_name=test_display_name,
        file_ids=[],
        file_metadata=[
            FileMetadata(
                file_id=file_id,
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                        column_mappings=[],
                    )
                ],
                association_label_mapping=[],
            )
        ],
        import_configuration=ImportConfiguration(
            object_identifiers=[
                StandardObjectIdentifier(object_name=StdObjectIdentifiers.account)
            ],
            file_import_type=FileImportType.SINGLE_FILE,
            object_import_mode=ObjectImportMode.CREATE_ONLY,
            file_dupe_resolution=FileDupeResolution.USE_FIRST,
        ),
    )

    created_review = await import_csv_job_review_service.create_job_review(
        organization_id=organization_id,
        submitter_user_id=submitter_user_id,
        import_metadata=import_metadata,
    )
    assert created_review.status == ImportCsvJobReviewStatusDb.PENDING_REVIEW
    # 2a. Reject the job review without notes. This should fail.
    with pytest.raises(InvalidArgumentError) as exc_info:
        await import_csv_job_review_service.set_reject_job_review(
            organization_id=organization_id,
            job_review_id=created_review.id,
            reviewer_user_id=reviewer_user_id,
            reviewer_notes=None,
        )
    assert "no reviewer notes" in str(exc_info.value)

    # 2b. Actually reject the job review
    rejection_notes = "Missing required columns in the uploaded file."
    rejected_review = await import_csv_job_review_service.set_reject_job_review(
        organization_id=organization_id,
        job_review_id=created_review.id,
        reviewer_user_id=reviewer_user_id,
        reviewer_notes=rejection_notes,
    )

    # 3. Assert the rejection results
    assert rejected_review is not None
    assert rejected_review.id == created_review.id
    assert rejected_review.status == ImportCsvJobReviewStatusDb.REJECTED
    assert rejected_review.reviewer_user_id == reviewer_user_id
    assert rejected_review.reviewer_notes == rejection_notes
    assert created_review.status_changed_at is not None
    assert rejected_review.status_changed_at is not None
    assert rejected_review.status_changed_at > created_review.status_changed_at

    # 4. Verify the associated import job is still DRAFT
    (
        _,  # review detail already checked
        import_job_detail,
        organization,
    ) = await import_csv_job_review_service.get_job_review_detail(
        created_review.id, None
    )

    assert import_job_detail is not None
    assert import_job_detail.id == created_review.import_csv_job_id
    assert import_job_detail.status == ImportCsvJobStatus.DRAFT  # Crucial check


async def test_approve_job_review(
    import_csv_job_review_service: ImportCsvJobReviewService,
    make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
) -> None:
    """
    Tests the flow of approving an ImportCsvJobReview.
    """
    await _delete_all_import_jobs(
        import_job_repository=import_csv_job_review_service.import_job_service._repository,
    )

    submitter_user_id, organization_id = await make_user_org()
    reviewer_user_id = uuid4()  # Simulate a different user reviewing

    # 1. Create a job review first (similar setup)
    test_display_name = f"Test Job Review for Approval - {uuid4()}"
    file_id = uuid4()
    import_metadata = ImportMetadata(
        display_name=test_display_name,
        file_ids=[],
        file_metadata=[
            FileMetadata(
                file_id=file_id,
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account,
                        ),
                        column_mappings=[],
                    )
                ],
                association_label_mapping=[],
            )
        ],
        import_configuration=ImportConfiguration(
            object_identifiers=[
                StandardObjectIdentifier(object_name=StdObjectIdentifiers.account)
            ],
            file_import_type=FileImportType.SINGLE_FILE,
            object_import_mode=ObjectImportMode.CREATE_ONLY,
            file_dupe_resolution=FileDupeResolution.USE_FIRST,
        ),
    )

    created_review = await import_csv_job_review_service.create_job_review(
        organization_id=organization_id,
        submitter_user_id=submitter_user_id,
        import_metadata=import_metadata,
    )
    assert created_review.status == ImportCsvJobReviewStatusDb.PENDING_REVIEW
    # Ensure the initial job is DRAFT
    _, initial_job, _ = await import_csv_job_review_service.get_job_review_detail(
        created_review.id, None
    )
    assert initial_job is not None
    assert initial_job.status == ImportCsvJobStatus.DRAFT

    # 2. Approve the job review (mock temporal interaction)
    # Target the specific instance's attribute
    target_instance = import_csv_job_review_service.import_job_service
    with patch.object(
        target_instance, "_temporal_client", new_callable=AsyncMock
    ) as mock_temporal_client:
        # --- Mock setup for get_workflow_handle().describe() ---
        # 1. Mock the WorkflowExecutionDescription object that describe() returns
        mock_wf_exec_desc = MagicMock(spec=WorkflowExecutionDescription)
        mock_wf_exec_desc.status = WorkflowExecutionStatus.RUNNING  # Example status

        # 2. Mock the WorkflowHandle object that get_workflow_handle() returns
        mock_handle_for_get = MagicMock(spec=WorkflowHandle)
        # This handle's describe() method must be an AsyncMock
        mock_handle_for_get.describe = AsyncMock(return_value=mock_wf_exec_desc)

        # 3. Configure mock_temporal_client.get_workflow_handle
        # It should be a synchronous MagicMock that returns our mock_handle_for_get
        mock_temporal_client.get_workflow_handle = MagicMock(
            return_value=mock_handle_for_get
        )
        # --- End mock setup for get_workflow_handle().describe() ---

        # --- Mock setup for start_workflow() ---
        # Client.start_workflow is async, so mock_temporal_client.start_workflow
        # will be an AsyncMock. Its return_value (when awaited) needs .id and .run_id
        mock_handle_for_start = MagicMock(spec=WorkflowHandle)
        mock_handle_for_start.id = "mocked_workflow_id_on_start"
        mock_handle_for_start.run_id = "mocked_run_id_on_start"
        mock_temporal_client.start_workflow = AsyncMock(
            return_value=mock_handle_for_start
        )
        # --- End mock setup for start_workflow() ---

        approved_review = await import_csv_job_review_service.set_approve_job_review(
            organization_id=organization_id,
            job_review_id=created_review.id,
            reviewer_user_id=reviewer_user_id,
        )
        # Verify temporal was called
        mock_temporal_client.start_workflow.assert_called_once()

    # 3. Assert the approval results on the review
    assert approved_review is not None
    assert approved_review.id == created_review.id
    assert approved_review.status == ImportCsvJobReviewStatusDb.APPROVED
    assert approved_review.reviewer_user_id == reviewer_user_id
    # Notes should remain None on approval
    assert approved_review.reviewer_notes is None
    assert approved_review.status_changed_at is not None
    assert created_review.status_changed_at is not None
    assert approved_review.status_changed_at > created_review.status_changed_at

    # 4. Verify the associated import job status is now STARTED
    (
        _,  # review detail already checked
        import_job_detail,
        organization,
    ) = await import_csv_job_review_service.get_job_review_detail(
        created_review.id, None
    )

    assert import_job_detail is not None
    assert import_job_detail.id == created_review.import_csv_job_id
    assert import_job_detail.status == ImportCsvJobStatus.STARTED  # Status updated
    assert import_job_detail.workflow_id is not None  # Workflow ID should be set
