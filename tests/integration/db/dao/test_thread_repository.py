import uuid
from collections.abc import Awaitable, Callable
from datetime import timedelta

import pytest
from _pytest.fixtures import fixture

from salestech_be.common.exception.exception import ServiceError
from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.email_dto import EmailDto, GlobalEmailDto, MessageDto
from salestech_be.db.models.global_thread import (
    GlobalMessage,
    GlobalMessageAssociation,
    GlobalThread,
    GlobalThreadMessagesAssociation,
)
from salestech_be.db.models.message import (
    EmailProvider,
    Message,
    MessageStatus,
    MessageUpdate,
)
from salestech_be.db.models.sequence import (
    EmailEvent,
    EmailEventType,
    EmailProcessingStatus,
)
from salestech_be.db.models.thread import Thread, ThreadUpdate
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none


@fixture
def thread_repo(
    _engine: DatabaseEngine,
) -> ThreadRepository:
    return ThreadRepository(engine=_engine)


async def prepare_thread_to_insert(
    organization_id: uuid.UUID,
    email_account_id: uuid.UUID,
    provider_id: str | None = None,
    provider: EmailProvider | None = None,
) -> Thread:
    return Thread(
        id=uuid.uuid4(),
        organization_id=organization_id,
        email_account_id=email_account_id,
        provider=provider,
        provider_id=provider_id,
        subject="Email Subject",
        snippet="email snippet",
        earliest_message_date=zoned_utc_now(),
        participants=[EmailHydratedParticipant(email="<EMAIL>", name="User")],
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )


async def test_get_by_provider_id(
    thread_repo: ThreadRepository,
    email_account_service_ext: EmailAccountServiceExt,
) -> None:
    # prepare email account
    organization_id = uuid.uuid4()
    sender_user_id = uuid.uuid4()
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=sender_user_id,
        email="<EMAIL>",
        signature_html="this is my signature",
        organization_id=organization_id,
        user_id=sender_user_id,
    )

    thread_to_insert = await prepare_thread_to_insert(
        organization_id=organization_id,
        email_account_id=email_account.id,
        provider_id=str(uuid.uuid4()),
        provider=EmailProvider.NYLAS,
    )
    thread = await thread_repo.insert(instance=thread_to_insert)
    assert thread is not None

    assert await thread_repo.get_by_provider_id(
        provider_id=not_none(thread.provider_id), email_provider=EmailProvider.NYLAS
    ) == [thread]

    assert (
        await thread_repo.get_by_provider_id(
            provider_id=str(uuid.uuid4()), email_provider=EmailProvider.NYLAS
        )
        == []
    )


async def test_list_global_threads_by_contact_ids(
    thread_repo: ThreadRepository,
) -> None:
    organization_id = uuid.uuid4()
    contact_ids = [uuid.uuid4(), uuid.uuid4()]
    ids = [uuid.uuid4(), uuid.uuid4(), uuid.uuid4()]

    global_threads_to_insert = [
        GlobalThread(
            id=ids[0],
            organization_id=organization_id,
            contact_ids=contact_ids,
            subject="Global Thread 1",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=None,
            thread_ids=[uuid.uuid4()],
            snippet="Snippet 1",
        ),
        GlobalThread(
            id=ids[1],
            organization_id=organization_id,
            contact_ids=[contact_ids[0]],
            subject="Global Thread 2",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=None,
            thread_ids=[uuid.uuid4()],
            snippet="Snippet 2",
        ),
        # deleted one
        GlobalThread(
            id=ids[2],
            organization_id=organization_id,
            contact_ids=contact_ids,
            subject="Global Thread 3",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=zoned_utc_now(),
            thread_ids=[uuid.uuid4()],
            snippet="Snippet 3",
        ),
    ]

    # Insert global threads
    inserted_global_threads = []
    for global_thread in global_threads_to_insert:
        inserted_global_thread = await thread_repo.insert(global_thread)
        inserted_global_threads.append(inserted_global_thread)
    assert len(inserted_global_threads) == 3

    # Verify: Should return 2 global threads
    fetched_threads = await thread_repo.list_global_threads_by_contact_ids(
        contact_ids=contact_ids,
        organization_id=organization_id,
    )

    assert len(fetched_threads) == 2
    fetched_thread_by_id_map = {thread.id: thread for thread in fetched_threads}
    assert fetched_thread_by_id_map.get(ids[0]) == inserted_global_threads[0]
    assert fetched_thread_by_id_map.get(ids[1]) == inserted_global_threads[1]

    # Verify: Should return 1 global thread
    fetched_threads = await thread_repo.list_global_threads_by_contact_ids(
        contact_ids=[contact_ids[1]],
        organization_id=organization_id,
    )

    assert len(fetched_threads) == 1
    assert fetched_threads[0] == inserted_global_threads[0]


async def test_list_global_threads_by_account_ids(
    thread_repo: ThreadRepository,
) -> None:
    organization_id = uuid.uuid4()
    account_ids = [uuid.uuid4(), uuid.uuid4()]
    ids = [uuid.uuid4(), uuid.uuid4(), uuid.uuid4()]

    global_threads_to_insert = [
        GlobalThread(
            id=ids[0],
            organization_id=organization_id,
            account_ids=account_ids,
            subject="Global Thread 1",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=None,
            thread_ids=[uuid.uuid4()],
            snippet="Snippet 1",
        ),
        GlobalThread(
            id=ids[1],
            organization_id=organization_id,
            account_ids=[account_ids[0]],
            subject="Global Thread 2",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=None,
            thread_ids=[uuid.uuid4()],
            snippet="Snippet 2",
        ),
        # deleted one
        GlobalThread(
            id=ids[2],
            organization_id=organization_id,
            account_ids=account_ids,
            subject="Global Thread 3",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=zoned_utc_now(),
            thread_ids=[uuid.uuid4()],
            snippet="Snippet 3",
        ),
    ]

    # Insert global threads
    inserted_global_threads = []
    for global_thread in global_threads_to_insert:
        inserted_global_thread = await thread_repo.insert(global_thread)
        inserted_global_threads.append(inserted_global_thread)
    assert len(inserted_global_threads) == 3

    # Verify: Should return 2 global threads
    fetched_threads = await thread_repo.list_global_threads_by_account_ids(
        account_ids=account_ids,
        organization_id=organization_id,
    )

    assert len(fetched_threads) == 2

    fetched_thread_by_id_map = {thread.id: thread for thread in fetched_threads}
    assert fetched_thread_by_id_map.get(ids[0]) == inserted_global_threads[0]
    assert fetched_thread_by_id_map.get(ids[1]) == inserted_global_threads[1]

    # Verify: Should return 1 global thread
    fetched_threads = await thread_repo.list_global_threads_by_account_ids(
        account_ids=[account_ids[1]],
        organization_id=organization_id,
    )

    assert len(fetched_threads) == 1
    assert fetched_threads[0] == inserted_global_threads[0]


async def test_list_global_threads_by_ids(
    thread_repo: ThreadRepository,
) -> None:
    organization_id = uuid.uuid4()
    ids = [uuid.uuid4(), uuid.uuid4(), uuid.uuid4()]

    global_threads_to_insert = [
        GlobalThread(
            id=ids[0],
            organization_id=organization_id,
            subject="Global Thread 1",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=None,
            thread_ids=[uuid.uuid4()],
            snippet="Snippet 1",
        ),
        GlobalThread(
            id=ids[1],
            organization_id=organization_id,
            subject="Global Thread 2",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=None,
            thread_ids=[uuid.uuid4()],
            snippet="Snippet 2",
        ),
        # deleted one
        GlobalThread(
            id=ids[2],
            organization_id=organization_id,
            subject="Global Thread 3",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=zoned_utc_now(),
            thread_ids=[uuid.uuid4()],
            snippet="Snippet 3",
        ),
    ]

    # Insert global threads
    inserted_global_threads = []
    for global_thread in global_threads_to_insert:
        inserted_global_thread = await thread_repo.insert(global_thread)
        inserted_global_threads.append(inserted_global_thread)
    assert len(inserted_global_threads) == 3

    # Verify: Should return 2 global threads
    fetched_threads = await thread_repo._find_by_column_values(
        GlobalThread,
        id=ids,
        organization_id=organization_id,
    )
    fetched_threads = sorted(fetched_threads, key=lambda x: x.subject)
    inserted_global_threads = sorted(inserted_global_threads, key=lambda x: x.subject)

    assert len(fetched_threads) == 2

    fetched_thread_by_id_map = {thread.id: thread for thread in fetched_threads}

    assert fetched_thread_by_id_map.get(ids[0]) == inserted_global_threads[0]
    assert fetched_thread_by_id_map.get(ids[1]) == inserted_global_threads[1]
    assert fetched_thread_by_id_map.get(ids[2]) is None


async def test_count_global_messages_by_thread_ids(
    thread_repo: ThreadRepository,
    email_account_service_ext: EmailAccountServiceExt,
) -> None:
    organization_id = uuid.uuid4()
    assert (
        await thread_repo.count_global_messages_by_thread_ids(
            global_thread_ids=[], organization_id=organization_id
        )
        == {}
    )

    ids = [uuid.uuid4(), uuid.uuid4(), uuid.uuid4()]

    global_threads_to_insert = [
        GlobalThread(
            id=ids[0],
            organization_id=organization_id,
            subject="Global Thread 1",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=None,
            thread_ids=[uuid.uuid4()],
            snippet="Snippet 1",
        ),
        GlobalThread(
            id=ids[1],
            organization_id=organization_id,
            subject="Global Thread 2",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=None,
            thread_ids=[uuid.uuid4()],
            snippet="Snippet 2",
        ),
        # deleted one
        GlobalThread(
            id=ids[2],
            organization_id=organization_id,
            subject="Global Thread 3",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=zoned_utc_now(),
            thread_ids=[uuid.uuid4()],
            snippet="Snippet 3",
        ),
    ]

    # Insert global threads
    inserted_global_threads = []
    for global_thread in global_threads_to_insert:
        inserted_global_thread = await thread_repo.insert(global_thread)
        inserted_global_threads.append(inserted_global_thread)

    global_message_to_insert = [
        GlobalMessage(
            id=uuid.uuid4(),
            global_thread_id=ids[0],
            organization_id=organization_id,
            original_message_id=str(uuid.uuid4()),
            created_at=zoned_utc_now(),
            message_received_at=zoned_utc_now(),
            deleted_at=None,
        ),
        GlobalMessage(
            id=uuid.uuid4(),
            global_thread_id=ids[0],
            organization_id=organization_id,
            original_message_id=str(uuid.uuid4()),
            created_at=zoned_utc_now(),
            message_received_at=zoned_utc_now(),
            deleted_at=None,
        ),
        GlobalMessage(
            id=uuid.uuid4(),
            global_thread_id=ids[1],
            organization_id=organization_id,
            original_message_id=str(uuid.uuid4()),
            created_at=zoned_utc_now(),
            message_received_at=zoned_utc_now(),
            deleted_at=None,
        ),
    ]
    for global_message in global_message_to_insert:
        await thread_repo.insert(global_message)

    global_thread_message_count_map: dict[
        uuid.UUID, int
    ] = await thread_repo.count_global_messages_by_thread_ids(
        global_thread_ids=ids, organization_id=organization_id
    )

    assert global_thread_message_count_map.get(ids[0]) == 2
    assert global_thread_message_count_map.get(ids[1]) == 1
    assert global_thread_message_count_map.get(ids[2]) is None


async def test_update_thread_and_message(
    thread_repo: ThreadRepository,
    email_account_service_ext: EmailAccountServiceExt,
) -> None:
    # Generate a unique organization ID and sender user ID
    organization_id = uuid.uuid4()
    sender_user_id = uuid.uuid4()
    sender_email_address = "<EMAIL>"

    # Create or retrieve an email account for the sender
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=sender_user_id,
        email=sender_email_address,
        signature_html="this is my signature",
        organization_id=organization_id,
        user_id=sender_user_id,
    )

    # Prepare a new thread to insert
    thread = await prepare_thread_to_insert(
        organization_id=organization_id,
        email_account_id=email_account.id,
        provider_id=str(uuid.uuid4()),
        provider=EmailProvider.NYLAS,
    )

    # Prepare a new message to associate with the thread
    message = Message(
        id=uuid.uuid4(),
        idempotency_key=str(uuid.uuid4()),
        email_account_id=email_account.id,
        thread_id=thread.id,
        subject="Email Subject",
        status=MessageStatus.CREATED,
        send_from=[
            EmailHydratedParticipant(
                email=sender_email_address,
                name="Test",
                email_account_id=email_account.id,
            )
        ],
        send_to=[
            EmailHydratedParticipant(
                email="<EMAIL>", name="Recipient", contact_id=uuid.uuid4()
            )
        ],
        organization_id=organization_id,
        snippet="email snippet",
        body_text="email body",
        body_html="email body",
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )

    # Insert the thread and message into the repository
    inserted_email_dto, _ = await thread_repo.insert_thread_and_message(
        thread=thread,
        message=message,
    )

    # Assert that the inserted email DTO is not None
    assert inserted_email_dto is not None

    # Extract the inserted thread and message from the DTO
    inserted_thread = inserted_email_dto.thread
    inserted_message_dto = inserted_email_dto.message_dtos[0]
    assert inserted_message_dto is not None
    inserted_message = inserted_message_dto.message

    # Verify that the inserted thread and message match the original
    assert inserted_thread.id == thread.id
    assert inserted_message.id == message.id

    # Update the thread and message with new values
    updated_email_dto = await thread_repo.update_thread_and_message(
        thread_id=thread.id,
        message_id=message.id,
        thread_update_fields=ThreadUpdate(
            subject="Updated Subject",  # New subject for the thread
        ),
        message_update_fields=MessageUpdate(
            subject="Updated Subject",  # New subject for the message
        ),
    )

    # Assert that the updated email DTO is not None
    assert updated_email_dto is not None

    # Verify that the updated thread has the new subject
    updated_thread = updated_email_dto.thread
    assert updated_thread.id == thread.id
    assert updated_thread.subject == "Updated Subject"

    # Verify that the updated message has the new subject
    updated_message_dto = updated_email_dto.message_dtos[0]
    assert updated_message_dto is not None
    updated_message = updated_message_dto.message
    assert updated_message.id == message.id
    assert updated_message.subject == "Updated Subject"


async def test_upsert_message(
    thread_repo: ThreadRepository,
    email_account_service_ext: EmailAccountServiceExt,
) -> None:
    # Generate test data
    organization_id = uuid.uuid4()
    sender_user_id = uuid.uuid4()
    provider_id = str(uuid.uuid4())

    # Create email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=sender_user_id,
        email="<EMAIL>",
        signature_html="test signature",
        organization_id=organization_id,
        user_id=sender_user_id,
    )

    # Create a thread first
    thread = await prepare_thread_to_insert(
        organization_id=organization_id,
        email_account_id=email_account.id,
        provider_id=provider_id,
        provider=EmailProvider.NYLAS,
    )
    inserted_thread = await thread_repo.insert(thread)

    # Prepare message for upsert
    message = Message(
        id=uuid.uuid4(),
        idempotency_key=str(uuid.uuid4()),
        email_account_id=email_account.id,
        thread_id=inserted_thread.id,
        provider=EmailProvider.NYLAS,
        provider_id=provider_id,
        subject="Original Subject",
        status=MessageStatus.CREATED,
        send_from=[
            EmailHydratedParticipant(
                email="<EMAIL>",
                name="Test Sender",
                email_account_id=email_account.id,
            )
        ],
        send_to=[
            EmailHydratedParticipant(
                email="<EMAIL>",
                name="Test Recipient",
            )
        ],
        organization_id=organization_id,
        snippet="original snippet",
        body_text="original body",
        body_html="<p>original body</p>",
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )

    # Test inserting new message
    inserted_message = await thread_repo.upsert_message(message)
    assert inserted_message is not None
    assert inserted_message.id == message.id
    assert inserted_message.subject == "Original Subject"
    assert inserted_message.snippet == "original snippet"

    # Prepare updated message with same unique constraints but different content
    updated_message = Message(
        id=uuid.uuid4(),  # Different ID
        idempotency_key=str(uuid.uuid4()),
        email_account_id=email_account.id,
        thread_id=inserted_thread.id,
        provider=EmailProvider.NYLAS,
        provider_id=provider_id,  # Same provider_id
        subject="Updated Subject",
        status=MessageStatus.CREATED,
        send_from=[
            EmailHydratedParticipant(
                email="<EMAIL>",
                name="Test Sender",
                email_account_id=email_account.id,
            )
        ],
        send_to=[
            EmailHydratedParticipant(
                email="<EMAIL>",
                name="Updated Recipient",
            )
        ],
        organization_id=organization_id,
        snippet="updated snippet",
        body_text="updated body",
        body_html="<p>updated body</p>",
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )

    # Test updating existing message
    upserted_message = await thread_repo.upsert_message(updated_message)
    assert upserted_message is not None
    assert upserted_message.id == inserted_message.id  # Should keep original ID
    assert upserted_message.subject == "Updated Subject"
    assert upserted_message.snippet == "updated snippet"
    assert upserted_message.provider_id == provider_id
    assert upserted_message.email_account_id == email_account.id
    assert upserted_message.provider == EmailProvider.NYLAS

    # Verify excluded fields remain unchanged
    assert upserted_message.thread_id == inserted_thread.id
    assert upserted_message.organization_id == organization_id
    assert upserted_message.status == MessageStatus.CREATED
    assert upserted_message.created_at == inserted_message.created_at


async def test_get_global_thread_by_original_message_ids(
    thread_repo: ThreadRepository,
    make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
) -> None:
    """Test getting global thread by original message IDs."""
    # Setup test data
    _, organization_id = await make_user_org()
    global_thread_id = uuid.uuid4()
    another_global_thread_id = uuid.uuid4()

    # Create test global threads
    global_thread = GlobalThread(
        id=global_thread_id,
        subject="Test Thread",
        snippet="Test snippet",
        thread_ids=[uuid.uuid4()],
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repo.insert(global_thread)

    another_global_thread = GlobalThread(
        id=another_global_thread_id,
        subject="Another Test Thread",
        snippet="Another Test snippet",
        thread_ids=[uuid.uuid4()],
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repo.insert(another_global_thread)

    # Create test global thread messages associations
    message_ids = [f"<message{i}@example.com>" for i in range(3)]
    for msg_id in message_ids:
        global_thread_messages_association = GlobalThreadMessagesAssociation(
            id=uuid.uuid4(),
            original_message_id=msg_id,
            global_thread_id=global_thread_id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
        )
        await thread_repo.insert(global_thread_messages_association)

    # Create another set of associations for testing multiple messages to same thread
    another_message_ids = [f"<another{i}@example.com>" for i in range(2)]
    for msg_id in another_message_ids:
        global_thread_messages_association = GlobalThreadMessagesAssociation(
            id=uuid.uuid4(),
            original_message_id=msg_id,
            global_thread_id=another_global_thread_id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
        )
        await thread_repo.insert(global_thread_messages_association)

    # Test cases for successful scenarios
    test_cases = [
        # Case 1: Single existing message ID
        ([message_ids[0]], True, global_thread_id),
        # Case 2: Multiple message IDs where one exists
        (["<<EMAIL>>", message_ids[1]], True, global_thread_id),
        # Case 3: All nonexistent message IDs
        (["<<EMAIL>>", "<<EMAIL>>"], False, None),
        # Case 4: Empty list
        ([], False, None),
        # Case 5: Multiple message IDs from same thread
        ([message_ids[0], message_ids[1]], True, global_thread_id),
    ]

    for message_ids_to_test, should_find, expected_thread_id in test_cases:
        result = await thread_repo.get_global_thread_by_original_message_ids(
            message_ids_to_test, organization_id
        )
        if should_find:
            assert result is not None
            assert result.id == expected_thread_id
        else:
            assert result is None

    # Test case 6: Multiple message IDs from different threads should raise error
    test_message_ids = [message_ids[0], another_message_ids[0]]
    with pytest.raises(ServiceError) as exc_info:
        await thread_repo.get_global_thread_by_original_message_ids(
            test_message_ids, organization_id
        )
    error = exc_info.value
    assert error.additional_error_details is not None
    assert (
        error.additional_error_details.details
        == f"Multiple global threads found for original message ids: {test_message_ids}"
    )

    # Test with deleted global thread
    await thread_repo.update_by_primary_key(
        GlobalThread,
        primary_key_to_value={"id": global_thread_id},
        column_to_update={"deleted_at": zoned_utc_now()},
    )
    result = await thread_repo.get_global_thread_by_original_message_ids(
        [message_ids[0]], organization_id
    )
    assert result is None


async def test_upsert_global_message_association(
    thread_repo: ThreadRepository,
) -> None:
    # Generate test data
    organization_id = uuid.uuid4()
    global_message_id = uuid.uuid4()
    message_id = uuid.uuid4()

    # Create initial association
    association = GlobalMessageAssociation(
        id=uuid.uuid4(),
        global_message_id=global_message_id,
        message_id=message_id,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
    )

    # Test inserting new association
    inserted_association = await thread_repo.insert(association)
    assert inserted_association is not None
    assert inserted_association.global_message_id == global_message_id
    assert inserted_association.message_id == message_id
    assert inserted_association.organization_id == organization_id

    # Create updated association with same unique constraints but different ID
    updated_association = GlobalMessageAssociation(
        id=uuid.uuid4(),  # Different ID
        global_message_id=global_message_id,  # Same global_message_id
        message_id=message_id,  # Same message_id
        organization_id=organization_id,
        created_at=zoned_utc_now(),
    )

    # Test upserting existing association
    upserted_association = await thread_repo.upsert_global_message_association(
        updated_association
    )
    assert upserted_association is not None
    assert upserted_association.id == inserted_association.id  # Should keep original ID
    assert upserted_association.global_message_id == global_message_id
    assert upserted_association.message_id == message_id
    assert upserted_association.organization_id == organization_id

    # Test inserting association with new unique constraints
    new_association = GlobalMessageAssociation(
        id=uuid.uuid4(),
        global_message_id=uuid.uuid4(),  # Different global_message_id
        message_id=uuid.uuid4(),  # Different message_id
        organization_id=organization_id,
        created_at=zoned_utc_now(),
    )

    # Should create new record
    new_inserted_association = await thread_repo.upsert_global_message_association(
        new_association
    )
    assert new_inserted_association is not None
    assert new_inserted_association.id == new_association.id
    assert (
        new_inserted_association.global_message_id == new_association.global_message_id
    )
    assert new_inserted_association.message_id == new_association.message_id
    assert new_inserted_association.organization_id == organization_id


async def test_upsert_global_thread_messages_association(
    thread_repo: ThreadRepository,
    make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
) -> None:
    """Test upserting global thread messages associations.
    Tests:
    1. Creating new associations
    2. Attempting to update existing associations (should not update)
    3. Batch processing with mix of new and existing message IDs
    """
    # Setup test data
    _, organization_id = await make_user_org()
    global_thread_id = uuid.uuid4()

    # Create a test global thread
    global_thread = GlobalThread(
        id=global_thread_id,
        subject="Test Thread",
        snippet="Test snippet",
        thread_ids=[uuid.uuid4()],
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repo.insert(global_thread)

    # Test Case 1: Create new associations
    original_message_ids = [
        "<<EMAIL>>",
        "<<EMAIL>>",
        "<<EMAIL>>",
    ]

    # First upsert - should create new associations
    await thread_repo.upsert_global_thread_messages_association(
        organization_id=organization_id,
        global_thread_id=global_thread_id,
        original_message_ids=original_message_ids,
    )

    # Verify associations were created
    for msg_id in original_message_ids:
        association = await thread_repo._find_unique_by_column_values(
            GlobalThreadMessagesAssociation,
            original_message_id=msg_id,
            organization_id=organization_id,
        )
        assert association is not None
        assert association.global_thread_id == global_thread_id
        assert association.original_message_id == msg_id
        assert association.organization_id == organization_id

    # Test Case 2: Attempt to update existing associations (should not update)
    new_global_thread_id = uuid.uuid4()
    new_global_thread = GlobalThread(
        id=new_global_thread_id,
        subject="New Test Thread",
        snippet="New test snippet",
        thread_ids=[uuid.uuid4()],
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repo.insert(new_global_thread)

    # Try to update existing associations with new global thread ID
    await thread_repo.upsert_global_thread_messages_association(
        organization_id=organization_id,
        global_thread_id=new_global_thread_id,
        original_message_ids=original_message_ids[
            :2
        ],  # Try to update first two messages
    )

    # Verify that existing associations were not updated
    for msg_id in original_message_ids[:2]:
        association = await thread_repo._find_unique_by_column_values(
            GlobalThreadMessagesAssociation,
            original_message_id=msg_id,
            organization_id=organization_id,
        )
        assert association is not None
        assert (
            association.global_thread_id == global_thread_id
        )  # Should still have original global_thread_id
        assert association.original_message_id == msg_id
        assert association.organization_id == organization_id

    # Test Case 3: Batch process with mix of new and existing message IDs
    mixed_message_ids = [
        "<<EMAIL>>",  # New message
        "<<EMAIL>>",  # New message
        original_message_ids[0],  # Existing message
    ]

    await thread_repo.upsert_global_thread_messages_association(
        organization_id=organization_id,
        global_thread_id=new_global_thread_id,
        original_message_ids=mixed_message_ids,
    )

    # Verify results:
    # New messages should be created with new_global_thread_id
    for msg_id in mixed_message_ids[:2]:  # Check new messages
        association = await thread_repo._find_unique_by_column_values(
            GlobalThreadMessagesAssociation,
            original_message_id=msg_id,
            organization_id=organization_id,
        )
        assert association is not None
        assert association.global_thread_id == new_global_thread_id
        assert association.original_message_id == msg_id
        assert association.organization_id == organization_id

    # Existing message should retain its original global_thread_id
    association = await thread_repo._find_unique_by_column_values(
        GlobalThreadMessagesAssociation,
        original_message_id=original_message_ids[0],
        organization_id=organization_id,
    )
    assert association is not None
    assert (
        association.global_thread_id == global_thread_id
    )  # Should still have original global_thread_id


async def test_get_global_message_by_provider_id(
    thread_repo: ThreadRepository,
    make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
    email_account_service_ext: EmailAccountServiceExt,
) -> None:
    # 1. Generate test data
    user_id, organization_id = await make_user_org()

    # Create a real email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email="<EMAIL>",
        signature_html="test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create a message with a provider ID
    provider_id = f"test_provider_message_id_{uuid.uuid4()}"
    thread_id = uuid.uuid4()

    message = Message(
        id=uuid.uuid4(),
        thread_id=thread_id,
        subject="Test Message",
        snippet="Test snippet",
        provider=EmailProvider.NYLAS,
        provider_id=provider_id,
        organization_id=organization_id,
        email_account_id=email_account.id,
        send_from=[
            EmailHydratedParticipant(
                email="<EMAIL>",
                name="Sender",
                email_account_id=email_account.id,
            )
        ],
        send_to=[
            EmailHydratedParticipant(
                email="<EMAIL>",
                name="Recipient",
                contact_id=uuid.uuid4(),
            )
        ],
        body_text="Test email body text",
        body_html="<p>Test email body html</p>",
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repo.insert(message)

    # Create a global thread
    global_thread = GlobalThread(
        id=uuid.uuid4(),
        subject="Test Global Thread",
        snippet="Test global thread snippet",
        thread_ids=[thread_id],
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repo.insert(global_thread)

    # Create a global message
    global_message = GlobalMessage(
        id=uuid.uuid4(),
        original_message_id=provider_id,
        organization_id=organization_id,
        global_thread_id=global_thread.id,
        message_received_at=zoned_utc_now(),
        created_at=zoned_utc_now(),
    )
    await thread_repo.insert(global_message)

    # Create a global message association
    global_message_association = GlobalMessageAssociation(
        id=uuid.uuid4(),
        global_message_id=global_message.id,
        message_id=message.id,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
    )
    await thread_repo.insert(global_message_association)

    # 2. Test successful lookup
    result = await thread_repo.get_global_message_by_provider_id_and_email_account_ids(
        provider_id=provider_id,
        email_provider=EmailProvider.NYLAS,
        email_account_ids=[email_account.id],
    )

    # 3. Verify results
    assert result is not None
    global_message_result, email_account_id = result
    assert global_message_result.id == global_message.id
    assert global_message_result.global_thread_id == global_thread.id
    assert global_message_result.organization_id == organization_id
    assert global_message_result.original_message_id == provider_id
    assert email_account_id == email_account.id

    # 4. Test not found scenario
    non_existent_provider_id = "non_existent_provider_id"
    result = await thread_repo.get_global_message_by_provider_id_and_email_account_ids(
        provider_id=non_existent_provider_id,
        email_provider=EmailProvider.NYLAS,
        email_account_ids=[email_account.id],
    )

    # 5. Verify not found results
    assert result is None


async def test_delete_failed_message_and_thread(
    thread_repo: ThreadRepository,
    make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
    email_account_service_ext: EmailAccountServiceExt,
) -> None:
    """Test deleting a failed message and its thread.

    Tests the following scenarios:
    1. When is_new_thread=True - delete message, thread and global thread
    2. When is_new_thread=False - delete only the failed message, keep thread and global thread
    """
    user_id, organization_id = await make_user_org()
    thread_id = uuid.uuid4()

    # Create email account
    email_account = await email_account_service_ext.get_or_create_email_account(
        owner_user_id=user_id,
        email="<EMAIL>",
        signature_html="test signature",
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create a thread
    thread = Thread(
        id=thread_id,
        organization_id=organization_id,
        email_account_id=email_account.id,
        subject="Test Thread",
        snippet="Test snippet",
        participants=[
            EmailHydratedParticipant(email="<EMAIL>", name="Test User")
        ],
        earliest_message_date=zoned_utc_now(),
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repo.insert(thread)

    # Create global thread with only this thread
    global_thread_1 = GlobalThread(
        id=uuid.uuid4(),
        subject="Test Global Thread",
        snippet="Test snippet",
        thread_ids=[thread_id],
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repo.insert(global_thread_1)

    # Test Case 1: Delete message and thread (is_new_thread=True)
    failed_message = Message(
        id=uuid.uuid4(),
        thread_id=thread_id,
        organization_id=organization_id,
        email_account_id=email_account.id,
        subject="Failed Message",
        status=MessageStatus.CREATED,  # Failed message
        send_from=[
            EmailHydratedParticipant(email="<EMAIL>", name="Test User")
        ],
        send_to=[
            EmailHydratedParticipant(email="<EMAIL>", name="Recipient")
        ],
        snippet="Test snippet",
        body_text="Test body",
        body_html="<p>Test body</p>",
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repo.insert(failed_message)

    # Create global message and associations
    global_message_1 = GlobalMessage(
        id=uuid.uuid4(),
        original_message_id=str(failed_message.id),
        organization_id=organization_id,
        global_thread_id=global_thread_1.id,
        message_received_at=zoned_utc_now(),
        created_at=zoned_utc_now(),
    )
    await thread_repo.insert(global_message_1)

    global_message_assoc_1 = GlobalMessageAssociation(
        id=uuid.uuid4(),
        global_message_id=global_message_1.id,
        message_id=failed_message.id,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
    )
    await thread_repo.insert(global_message_assoc_1)

    thread_messages_assoc_1 = GlobalThreadMessagesAssociation(
        id=uuid.uuid4(),
        original_message_id=str(failed_message.id),
        global_thread_id=global_thread_1.id,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
    )
    await thread_repo.insert(thread_messages_assoc_1)

    # Delete failed message with is_new_thread=True
    global_message_id, global_thread_id = not_none(
        await thread_repo.delete_failed_message_and_global_message(
            organization_id=organization_id,
            global_email_dto=GlobalEmailDto.from_email_dto(
                email_dto=EmailDto(
                    thread=thread,
                    message_dtos=[MessageDto(message=failed_message, attachments=[])],
                ),
                global_thread_id=global_thread_1.id,
                global_message_mapping={global_message_1.id: (failed_message, True)},
            ),
            is_new_thread=True,
        )
    )
    assert global_message_id == global_message_1.id
    assert global_thread_id == global_thread_1.id

    # Verify message, thread, and global entities are deleted
    deleted_message = await thread_repo._find_by_column_values(
        Message,
        id=failed_message.id,
        organization_id=organization_id,
        exclude_deleted_or_archived=False,
    )
    assert len(deleted_message) == 1
    assert deleted_message[0].deleted_at is not None
    assert deleted_message[0].status == MessageStatus.FAILED

    deleted_thread = await thread_repo._find_by_column_values(
        Thread,
        id=thread_id,
        organization_id=organization_id,
        exclude_deleted_or_archived=False,
    )
    assert len(deleted_thread) == 1
    assert deleted_thread[0].deleted_at is not None

    # Verify global entities are deleted since this was the only thread
    deleted_global_thread = await thread_repo._find_by_column_values(
        GlobalThread,
        id=global_thread_1.id,
        organization_id=organization_id,
        exclude_deleted_or_archived=False,
    )
    assert len(deleted_global_thread) == 1
    assert deleted_global_thread[0].deleted_at is not None

    # Test Case 2: Delete only message (is_new_thread=False)
    thread_2_id = uuid.uuid4()
    thread_2 = Thread(
        id=thread_2_id,
        organization_id=organization_id,
        email_account_id=email_account.id,
        subject="Test Thread 2",
        snippet="Test snippet 2",
        participants=[
            EmailHydratedParticipant(email="<EMAIL>", name="Test User")
        ],
        earliest_message_date=zoned_utc_now(),
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repo.insert(thread_2)

    # Create a failed message
    failed_message_2 = Message(
        id=uuid.uuid4(),
        thread_id=thread_2_id,
        organization_id=organization_id,
        email_account_id=email_account.id,
        subject="Failed Message 2",
        status=MessageStatus.CREATED,
        send_from=[
            EmailHydratedParticipant(email="<EMAIL>", name="Test User")
        ],
        send_to=[
            EmailHydratedParticipant(email="<EMAIL>", name="Recipient")
        ],
        snippet="Test snippet",
        body_text="Test body",
        body_html="<p>Test body</p>",
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repo.insert(failed_message_2)

    # Create global thread with only this thread
    global_thread_2 = GlobalThread(
        id=uuid.uuid4(),
        subject="Test Global Thread 2",
        snippet="Test snippet",
        thread_ids=[thread_2_id],  # Only one thread
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await thread_repo.insert(global_thread_2)

    # Create global message and associations for failed_message_2
    global_message_2 = GlobalMessage(
        id=uuid.uuid4(),
        original_message_id=str(failed_message_2.id),
        organization_id=organization_id,
        global_thread_id=global_thread_2.id,
        message_received_at=zoned_utc_now(),
        created_at=zoned_utc_now(),
    )
    await thread_repo.insert(global_message_2)

    global_message_assoc_2 = GlobalMessageAssociation(
        id=uuid.uuid4(),
        global_message_id=global_message_2.id,
        message_id=failed_message_2.id,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
    )
    await thread_repo.insert(global_message_assoc_2)

    thread_messages_assoc_2 = GlobalThreadMessagesAssociation(
        id=uuid.uuid4(),
        original_message_id=str(failed_message_2.id),
        global_thread_id=global_thread_2.id,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
    )
    await thread_repo.insert(thread_messages_assoc_2)

    # Delete failed message with is_new_thread=False
    global_message_id, global_thread_id = not_none(
        await thread_repo.delete_failed_message_and_global_message(
            organization_id=organization_id,
            global_email_dto=GlobalEmailDto.from_email_dto(
                email_dto=EmailDto(
                    thread=thread_2,
                    message_dtos=[MessageDto(message=failed_message_2, attachments=[])],
                ),
                global_thread_id=global_thread_2.id,
                global_message_mapping={global_message_2.id: (failed_message_2, True)},
            ),
            is_new_thread=False,
        )
    )
    assert global_message_id == global_message_2.id
    assert global_thread_id == global_thread_2.id

    # Verify only message is deleted, thread and global thread remain
    deleted_message_2 = await thread_repo._find_by_column_values(
        Message,
        id=failed_message_2.id,
        organization_id=organization_id,
        exclude_deleted_or_archived=False,
    )
    assert len(deleted_message_2) == 1
    assert deleted_message_2[0].deleted_at is not None
    assert deleted_message_2[0].status == MessageStatus.FAILED

    # Verify thread is not deleted
    active_thread = await thread_repo._find_by_column_values(
        Thread,
        id=thread_2_id,
        organization_id=organization_id,
        exclude_deleted_or_archived=False,
    )
    assert len(active_thread) == 1
    assert active_thread[0].deleted_at is None

    # Verify global thread is not deleted
    active_global_thread = await thread_repo._find_by_column_values(
        GlobalThread,
        id=global_thread_2.id,
        organization_id=organization_id,
        exclude_deleted_or_archived=False,
    )
    assert len(active_global_thread) == 1
    assert active_global_thread[0].deleted_at is None


async def test_get_global_message_associations_by_global_message_id(
    thread_repo: ThreadRepository,
    make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
) -> None:
    """Test getting global message association by global message ID.

    Tests:
    1. Successfully retrieving an existing association
    2. Returns None for non-existent association
    3. Returns None for deleted association
    """
    # Setup test data
    user_id, organization_id = await make_user_org()
    global_message_id = uuid.uuid4()
    message_id = uuid.uuid4()

    # Create test association
    association = GlobalMessageAssociation(
        id=uuid.uuid4(),
        global_message_id=global_message_id,
        message_id=message_id,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
    )
    await thread_repo.insert(association)

    # Test Case 1: Successfully retrieve existing association
    results = await thread_repo.get_global_message_associations_by_global_message_id(
        global_message_id=global_message_id,
        organization_id=organization_id,
    )
    assert len(results) == 1
    assert results[0].id == association.id
    assert results[0].global_message_id == global_message_id
    assert results[0].message_id == message_id
    assert results[0].organization_id == organization_id

    # Test Case 2: Return None for non-existent association
    non_existent_id = uuid.uuid4()
    results = await thread_repo.get_global_message_associations_by_global_message_id(
        global_message_id=non_existent_id,
        organization_id=organization_id,
    )
    assert len(results) == 0

    # Test Case 3: Return None for deleted association
    # Mark the association as deleted
    await thread_repo.update_by_primary_key(
        GlobalMessageAssociation,
        primary_key_to_value={"id": association.id},
        column_to_update={"deleted_at": zoned_utc_now()},
    )

    results = await thread_repo.get_global_message_associations_by_global_message_id(
        global_message_id=global_message_id,
        organization_id=organization_id,
    )
    assert len(results) == 0


async def test_find_email_events_summaries_by_global_message_id(
    thread_repo: ThreadRepository,
    make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
) -> None:
    """Test retrieving email event summaries for a global message."""
    # Setup test data
    _, organization_id = await make_user_org()
    global_message_id = uuid.uuid4()
    global_thread_id = uuid.uuid4()
    email_account_id = uuid.uuid4()

    # Create test email events

    # Create events with different types and times
    events_data = [
        # OPENED event
        EmailEvent(
            id=uuid.uuid4(),
            email_account_id=email_account_id,
            organization_id=organization_id,
            global_message_id=global_message_id,
            global_thread_id=global_thread_id,
            event_time=zoned_utc_now() - timedelta(days=3),  # Older event
            type=EmailEventType.OPENED,
            details={"device": "desktop"},
            processing_status=EmailProcessingStatus.PROCESSED,
            external_event_id=f"test_open_{uuid.uuid4()}",
        ),
        # Another OPENED event (newer, should be the one returned)
        EmailEvent(
            id=uuid.uuid4(),
            email_account_id=email_account_id,
            organization_id=organization_id,
            global_message_id=global_message_id,
            global_thread_id=global_thread_id,
            event_time=zoned_utc_now() - timedelta(days=1),  # Newer event
            type=EmailEventType.OPENED,
            details={"device": "mobile"},
            processing_status=EmailProcessingStatus.PROCESSED,
            external_event_id=f"test_open_{uuid.uuid4()}",
        ),
        # CLICKED event
        EmailEvent(
            id=uuid.uuid4(),
            email_account_id=email_account_id,
            organization_id=organization_id,
            global_message_id=global_message_id,
            global_thread_id=global_thread_id,
            event_time=zoned_utc_now() - timedelta(days=2),
            type=EmailEventType.LINK_CLICKED,
            details={"url": "https://example.com"},
            processing_status=EmailProcessingStatus.PROCESSED,
            external_event_id=f"test_click_{uuid.uuid4()}",
        ),
    ]

    # Insert the events
    for event in events_data:
        await thread_repo.insert(event)

    # Test fetching email event summaries
    event_summaries_dict = (
        await thread_repo.find_email_events_summaries_by_global_message_ids(
            global_message_ids=[global_message_id],
            organization_id=organization_id,
        )
    )

    # Verify results
    assert global_message_id in event_summaries_dict
    event_summaries = event_summaries_dict[global_message_id]
    assert (
        len(event_summaries) == 2
    )  # Should have summaries for OPENED and LINK_CLICKED

    # Verify event types
    event_types = {summary.event_type for summary in event_summaries}
    assert event_types == {
        EmailEventType.OPENED.value,
        EmailEventType.LINK_CLICKED.value,
    }

    # Find the newer OPENED event to verify it has the most recent timestamp
    opened_event = events_data[1]  # This is the newer OPENED event
    opened_summary = next(
        summary
        for summary in event_summaries
        if summary.event_type == EmailEventType.OPENED.value
    )
    assert opened_summary.latest_event_time == opened_event.event_time

    # Find the LINK_CLICKED event
    clicked_event = events_data[2]
    clicked_summary = next(
        summary
        for summary in event_summaries
        if summary.event_type == EmailEventType.LINK_CLICKED.value
    )
    assert clicked_summary.latest_event_time == clicked_event.event_time

    # Test for non-existent global message ID
    non_existent_id = uuid.uuid4()
    empty_summaries_dict = (
        await thread_repo.find_email_events_summaries_by_global_message_ids(
            global_message_ids=[non_existent_id],
            organization_id=organization_id,
        )
    )
    assert len(empty_summaries_dict) == 0


async def test_find_email_events_summaries_by_global_message_ids(
    thread_repo: ThreadRepository,
    make_user_org: Callable[[], Awaitable[tuple[uuid.UUID, uuid.UUID]]],
) -> None:
    """Test retrieving email event summaries for multiple global messages."""
    # Setup test data
    _, organization_id = await make_user_org()
    global_message_id_1 = uuid.uuid4()
    global_message_id_2 = uuid.uuid4()
    global_thread_id = uuid.uuid4()
    email_account_id = uuid.uuid4()

    # Create test email events for first global message
    events_data_1 = [
        # OPENED event for global_message_id_1
        EmailEvent(
            id=uuid.uuid4(),
            email_account_id=email_account_id,
            organization_id=organization_id,
            global_message_id=global_message_id_1,
            global_thread_id=global_thread_id,
            event_time=zoned_utc_now() - timedelta(days=3),
            type=EmailEventType.OPENED,
            details={"device": "desktop"},
            processing_status=EmailProcessingStatus.PROCESSED,
            external_event_id=f"test_open_1_{uuid.uuid4()}",
        ),
        # CLICKED event for global_message_id_1
        EmailEvent(
            id=uuid.uuid4(),
            email_account_id=email_account_id,
            organization_id=organization_id,
            global_message_id=global_message_id_1,
            global_thread_id=global_thread_id,
            event_time=zoned_utc_now() - timedelta(days=2),
            type=EmailEventType.LINK_CLICKED,
            details={"url": "https://example.com"},
            processing_status=EmailProcessingStatus.PROCESSED,
            external_event_id=f"test_click_1_{uuid.uuid4()}",
        ),
    ]

    # Create test email events for second global message
    events_data_2 = [
        # OPENED event for global_message_id_2
        EmailEvent(
            id=uuid.uuid4(),
            email_account_id=email_account_id,
            organization_id=organization_id,
            global_message_id=global_message_id_2,
            global_thread_id=global_thread_id,
            event_time=zoned_utc_now() - timedelta(days=1),
            type=EmailEventType.OPENED,
            details={"device": "mobile"},
            processing_status=EmailProcessingStatus.PROCESSED,
            external_event_id=f"test_open_2_{uuid.uuid4()}",
        ),
        # Another OPENED event for global_message_id_2 (newer timestamp)
        EmailEvent(
            id=uuid.uuid4(),
            email_account_id=email_account_id,
            organization_id=organization_id,
            global_message_id=global_message_id_2,
            global_thread_id=global_thread_id,
            event_time=zoned_utc_now(),  # Most recent event
            type=EmailEventType.OPENED,
            details={"device": "tablet"},
            processing_status=EmailProcessingStatus.PROCESSED,
            external_event_id=f"test_open_3_{uuid.uuid4()}",
        ),
        # SEND_ATTEMPTED event for global_message_id_2
        EmailEvent(
            id=uuid.uuid4(),
            email_account_id=email_account_id,
            organization_id=organization_id,
            global_message_id=global_message_id_2,
            global_thread_id=global_thread_id,
            event_time=zoned_utc_now() - timedelta(days=4),  # Oldest event
            type=EmailEventType.SEND_ATTEMPTED,
            details={},
            processing_status=EmailProcessingStatus.PROCESSED,
            external_event_id=f"test_send_attempted_{uuid.uuid4()}",
        ),
    ]

    # Insert all events
    for event in events_data_1 + events_data_2:
        await thread_repo.insert(event)

    # Test fetching email event summaries for both global message IDs
    event_summaries_dict = (
        await thread_repo.find_email_events_summaries_by_global_message_ids(
            global_message_ids=[global_message_id_1, global_message_id_2],
            organization_id=organization_id,
        )
    )

    # Verify result structure
    assert isinstance(event_summaries_dict, dict)
    assert len(event_summaries_dict) == 2
    assert global_message_id_1 in event_summaries_dict
    assert global_message_id_2 in event_summaries_dict

    # Verify summaries for global_message_id_1
    summaries_1 = event_summaries_dict[global_message_id_1]
    assert len(summaries_1) == 2  # OPENED and LINK_CLICKED

    event_types_1 = {summary.event_type for summary in summaries_1}
    assert event_types_1 == {
        EmailEventType.OPENED.value,
        EmailEventType.LINK_CLICKED.value,
    }

    # Verify summaries for global_message_id_2
    summaries_2 = event_summaries_dict[global_message_id_2]
    assert len(summaries_2) == 2  # OPENED and SEND_ATTEMPTED

    event_types_2 = {summary.event_type for summary in summaries_2}
    assert event_types_2 == {
        EmailEventType.OPENED.value,
        EmailEventType.SEND_ATTEMPTED.value,
    }

    # Verify the timestamps match - for message 2, the OPENED event should have the most recent timestamp
    opened_summary_2 = next(
        summary
        for summary in summaries_2
        if summary.event_type == EmailEventType.OPENED.value
    )
    # Should match the timestamp of the most recent OPENED event
    assert opened_summary_2.latest_event_time == events_data_2[1].event_time

    # Test with non-existent global message IDs
    non_existent_id = uuid.uuid4()
    empty_summaries_dict = (
        await thread_repo.find_email_events_summaries_by_global_message_ids(
            global_message_ids=[non_existent_id],
            organization_id=organization_id,
        )
    )
    assert len(empty_summaries_dict) == 0
