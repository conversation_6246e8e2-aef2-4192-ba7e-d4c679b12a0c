import uuid
from datetime import datetime, timedelta

from salestech_be.db.dao.pdl_cache_repository import PDLCacheRepository
from salestech_be.db.models.pdl_cache import PDLCache
from salestech_be.integrations.pdl.model import (
    PeopleDataLabsCompany,
    PeopleDataLabsSearchCompanyRequest,
    PeopleDataLabsSearchCompanyResponse,
)
from salestech_be.util.time import zoned_utc_now


def prepare_pdl_cache(
    operation_name: str = "search_company",
    query_hash: str = "test_hash",
    request: PeopleDataLabsSearchCompanyRequest | None = None,
    response: PeopleDataLabsSearchCompanyResponse | None = None,
    expires_at: datetime | None = None,
) -> PDLCache:
    if expires_at is None:
        expires_at = zoned_utc_now() + timedelta(days=1)

    if request is None:
        request = PeopleDataLabsSearchCompanyRequest(query="test")

    if response is None:
        # Create a valid PeopleDataLabsSearchCompanyResponse object
        response = PeopleDataLabsSearchCompanyResponse(
            status=200,
            data=[
                PeopleDataLabsCompany(
                    name="Test Company", display_name="Test Company Inc."
                )
            ],
            total=1,
            dataset_version="v1",
        )

    return PDLCache(
        id=uuid.uuid4(),
        operation_name=operation_name,
        query_hash=query_hash,
        request=request,
        response=response,
        expires_at=expires_at,
        created_at=zoned_utc_now(),
        deleted_at=None,
    )


class TestProspectingCacheRepository:
    async def test_find_by_query_hash(
        self, pdl_cache_repository: PDLCacheRepository
    ) -> None:
        # Test with non-existent hash
        result = await pdl_cache_repository.find_by_query_hash(
            operation_name="search_company", query_hash="non_existent_hash"
        )
        assert result is None

        # Insert test data
        cache_entry = prepare_pdl_cache()
        inserted_cache = await pdl_cache_repository.upsert_cache(cache_entry)

        # Test finding by query hash
        result = await pdl_cache_repository.find_by_query_hash(
            operation_name=inserted_cache.operation_name,
            query_hash=inserted_cache.query_hash,
        )

        # Verify the found result matches what was inserted
        assert result is not None
        assert result.id == inserted_cache.id
        assert result.operation_name == inserted_cache.operation_name
        assert result.query_hash == inserted_cache.query_hash
        assert result.request == inserted_cache.request
        assert result.response == inserted_cache.response

        # Test finding with wrong api name
        result = await pdl_cache_repository.find_by_query_hash(
            operation_name="wrong_api_name", query_hash=inserted_cache.query_hash
        )
        assert result is None

    async def test_upsert_cache(self, pdl_cache_repository: PDLCacheRepository) -> None:
        # Prepare test data
        cache_entry = prepare_pdl_cache()

        # Insert new cache entry
        inserted_cache = await pdl_cache_repository.upsert_cache(cache_entry)
        assert inserted_cache is not None
        assert inserted_cache.operation_name == cache_entry.operation_name
        assert inserted_cache.query_hash == cache_entry.query_hash
        assert inserted_cache.request == cache_entry.request
        assert inserted_cache.response == cache_entry.response

        # Verify it can be found
        found_cache = await pdl_cache_repository.find_by_query_hash(
            operation_name=cache_entry.operation_name, query_hash=cache_entry.query_hash
        )
        assert found_cache is not None
        assert found_cache.id == inserted_cache.id

        # Update existing cache entry with new response and expires_at
        updated_response = PeopleDataLabsSearchCompanyResponse(
            status=200,
            data=[
                PeopleDataLabsCompany(
                    name="Updated Company", display_name="Updated Company Inc."
                )
            ],
            total=1,
            dataset_version="v1",
        )
        updated_expires_at = zoned_utc_now() + timedelta(days=2)

        updated_cache_entry = prepare_pdl_cache(
            operation_name=cache_entry.operation_name,
            query_hash=cache_entry.query_hash,
            request=cache_entry.request,
            response=updated_response,
            expires_at=updated_expires_at,
        )

        # Perform update
        updated_cache = await pdl_cache_repository.upsert_cache(updated_cache_entry)
        assert updated_cache is not None

        # Verify update
        found_updated_cache = await pdl_cache_repository.find_by_query_hash(
            operation_name=cache_entry.operation_name, query_hash=cache_entry.query_hash
        )
        assert found_updated_cache is not None
        assert found_updated_cache.response == updated_response

        # Ensure only response and expires_at were updated (according to columns_to_update)
        assert found_updated_cache.id == inserted_cache.id  # ID should remain the same
        assert (
            found_updated_cache.request == inserted_cache.request
        )  # request should not change
