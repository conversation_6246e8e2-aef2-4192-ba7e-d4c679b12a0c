from datetime import datetime, timed<PERSON>ta
from typing import cast
from unittest.mock import AsyncMock, patch
from uuid import UUID, uuid4

import pytest

from salestech_be.common.events import (
    DomainEnrichedCDCEvent,
)
from salestech_be.common.exception import ResourceNotFoundError
from salestech_be.common.exception.exception import InvalidArgumentError
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.activity.types import (
    Activity,
    ActivityRequest,
    ActivitySubReferenceRequest,
)
from salestech_be.core.comment.types import Comment
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.domain_crm_association.types import CreateTaskCrmAssociation
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.task.converter import (
    task_v2_from_db,
)
from salestech_be.core.task.service.task_v2_service import TaskV2Service
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.core.user.types import UserDTO
from salestech_be.db.models.activity import (
    ActivityPriority,
    ActivityReferenceIdType,
    ActivityStatus,
    ActivitySubReferenceType,
    ActivitySubType,
    ActivityType,
)
from salestech_be.db.models.comment import CommentReferenceIdType
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.contact_account_association import ContactAccountAssociation
from salestech_be.db.models.core.base import TableModel
from salestech_be.db.models.core.types import EntityParticipant
from salestech_be.db.models.domain_crm_association import DomainCRMAssociationRole
from salestech_be.db.models.pipeline import Pipeline
from salestech_be.db.models.task import (
    TaskPriority,
    TaskReference,
    TaskReferenceIdType,
    TaskSourceType,
    TaskStatus,
    TaskType,
)
from salestech_be.integrations.knock.model import KnockWorkflowResponse
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.common.container import DeleteEntityResponse
from salestech_be.web.api.task.schema import CreateTaskRequest, PatchTaskRequest
from tests.util.factories import (
    AccountV2Factory,
    ActivityFactory,
    ContactAccountAssociationFactory,
    ContactFactory,
    ContactV2Factory,
    DbTaskFactory,
    TaskReferenceFactory,
    TaskV2Factory,
)
from tests.util.service_test import ServiceTest


class TestTaskV2Service(ServiceTest):
    async def test_insert_task_v2(
        self,
        task_v2_service: TaskV2Service,
        task_repository: AsyncMock,
        task_query_service: AsyncMock,
        db_task_factory: type[DbTaskFactory],
        task_reference_factory: type[TaskReferenceFactory],
        user_service: AsyncMock,
        knock_client: AsyncMock,
        account_query_service: AsyncMock,
        contact_query_service: AsyncMock,
        account_v2_factory: type[AccountV2Factory],
        contact_v2_factory: type[ContactV2Factory],
        contact_account_association_factory: type[ContactAccountAssociationFactory],
        activity_factory: type[ActivityFactory],
        activity_service: AsyncMock,
    ) -> None:
        organization_id = uuid4()
        user_id = uuid4()
        now = zoned_utc_now()
        account_id = uuid4()
        contact_id = uuid4()

        request = CreateTaskRequest(
            title="Test Task",
            status=TaskStatus.IN_PROGRESS,
            priority=TaskPriority.HIGH,
            type=TaskType.EMAIL,
            owner_user_id=user_id,
            due_at=now + timedelta(days=1),
            note="Test note",
            pipeline_id=None,
            contact_ids=[contact_id],
            account_id=account_id,
            sequence_id=None,
            email_thread_ids=[],
            email_ids=[],
            meeting_id=None,
            insight_id=None,
            is_time_specified=True,
            source_type=TaskSourceType.USER,
        )

        # Mock account validation
        self.mock_account_query_service_get_account_v2(
            account_query_service=account_query_service,
            input_organization_id=organization_id,
            input_account_id=account_id,
            result=account_v2_factory.build(
                id=account_id,
                organization_id=organization_id,
            ),
        )

        # Mock contact validation
        self.mock_contact_query_service_list_contacts_v2(
            contact_query_service=contact_query_service,
            input_organization_id=organization_id,
            input_contact_ids={contact_id},
            result=[
                contact_v2_factory.build(
                    id=contact_id,
                    organization_id=organization_id,
                )
            ],
        )

        # Mock contact associations validation
        self.mock_contact_query_service_list_active_contact_associations_for_account(
            contact_query_service=contact_query_service,
            input_organization_id=organization_id,
            input_account_id=account_id,
            result=[
                contact_account_association_factory.build(
                    id=uuid4(),
                    contact_id=contact_id,
                    account_id=account_id,
                    organization_id=organization_id,
                    created_at=now,
                    created_by_user_id=user_id,
                )
            ],
        )

        insert_sequence: list[TableModel] = []

        db_task = db_task_factory.build(
            title=request.title,
            organization_id=organization_id,
            type=request.type,
            status=request.status,
            priority=request.priority,
            created_at=now,
            created_by_user_id=request.owner_user_id,
            updated_at=now,
            owner_user_id=request.owner_user_id,
            due_at=request.due_at,
            note=request.note,
            is_time_specified=request.is_time_specified,
            source_type=request.source_type,
        )
        insert_sequence.append(db_task)

        task_references: list[TaskReference] = []
        if request.contact_ids:
            for contact_id in request.contact_ids:
                task_reference = task_reference_factory.build(
                    task_id=db_task.id,
                    reference_id=str(contact_id),
                    reference_id_type=TaskReferenceIdType.CONTACT_ID,
                    organization_id=organization_id,
                    created_by_user_id=user_id,
                )
                task_references.append(task_reference)
                insert_sequence.append(task_reference)
        if request.account_id:
            task_reference = task_reference_factory.build(
                task_id=db_task.id,
                reference_id=str(request.account_id),
                reference_id_type=TaskReferenceIdType.ACCOUNT_ID,
                organization_id=organization_id,
                created_by_user_id=user_id,
            )
            task_references.append(task_reference)
            insert_sequence.append(task_reference)

        self.mock_repo_insert_sequence(task_repository, insert_sequence)

        # Mock activity service for task creation with contact
        self.mock_activity_service_insert_activity(
            activity_service=activity_service,
            input_organization_id=organization_id,
            input_activity_request=ActivityRequest(
                type=ActivityType.TASK,
                sub_type=ActivitySubType.TASK_CREATED,
                priority=ActivityPriority.MEDIUM,
                status=ActivityStatus.NEW,
                owner_user_id=user_id,
                account_id=request.account_id,
                reference_id_type=ActivityReferenceIdType.TASK_ID,
                reference_id=str(db_task.id),
                display_name="Task Created.",
                created_at=now,
                created_by_user_id=user_id,
                sub_references=[
                    ActivitySubReferenceRequest(
                        type=ActivitySubReferenceType.TASK_ASSIGNED_TO,
                        value=str(request.owner_user_id),
                        contact_id=contact_id,
                    )
                ],
            ),
            result=activity_factory.build(
                id=uuid4(),
                organization_id=organization_id,
                created_at=now,
                created_by_user_id=user_id,
            ),
        )

        expected_task = task_v2_from_db(
            db_task=db_task,
            db_task_references=task_references,
            comments=[],
        )

        # Mock comment query service
        self.mock_comment_query_service_find_all_comments_by_reference_id(
            comment_query_service=task_query_service.comment_query_service,
            input_organization_id=organization_id,
            input_reference_id=db_task.id,
            result=[],
        )

        self.mock_task_query_service_get_by_id_v2(
            task_query_service=task_query_service,
            input_organization_id=organization_id,
            input_task_id=db_task.id,
            result=expected_task,
        )
        self.mock_user_service_get_by_id_and_organization_id(
            user_service=user_service,
            input_organization_id=organization_id,
            input_user_id=user_id,
            result=UserDTO(
                id=user_id,
                first_name="test",
                last_name="test",
                email="<EMAIL>",
                phone_number=None,
                linkedin_url=None,
                avatar_s3_key=None,
                avatar_url=None,
                display_name="test test",
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                updated_at=zoned_utc_now(),
                updated_by_user_id=user_id,
                deactivated_at=None,
                deactivated_by_user_id=None,
                user_organization=None,
            ),
        )

        self.mock_knock_client_trigger_workflow(
            knock_client=knock_client,
            result=KnockWorkflowResponse(workflow_run_id="workflow_run_id"),
        )

        result = await task_v2_service.insert_task_v2(
            created_by_user_id=user_id,
            organization_id=organization_id,
            request=request,
        )

        assert result == expected_task

    async def test_patch_task_v2(
        self,
        task_v2_service: TaskV2Service,
        task_query_service: AsyncMock,
        task_v2_factory: type[TaskV2Factory],
        user_service: AsyncMock,
        knock_client: AsyncMock,
    ) -> None:
        organization_id = uuid4()
        user_id = uuid4()
        task_id = uuid4()
        created_at = zoned_utc_now()
        updated_at = created_at + timedelta(minutes=5)
        completed_at = created_at + timedelta(minutes=5)

        request = PatchTaskRequest(
            title="Updated Task",
            status=TaskStatus.COMPLETED,
            priority=TaskPriority.LOW,
            owner_user_id=user_id,
            contact_ids=None,
            due_at=None,
            note=None,
        )

        existing_task = task_v2_factory.build(
            id=task_id,
            organization_id=organization_id,
            title="Original Task",
            status=TaskStatus.IN_PROGRESS,
            priority=TaskPriority.HIGH,
            type=TaskType.EMAIL,
            owner_user_id=uuid4(),
            created_at=created_at,
            created_by_user_id=user_id,
            account_id=None,
            contact_ids=None,
        )

        self.mock_task_query_service_get_by_id_v2(
            task_query_service=task_query_service,
            input_organization_id=organization_id,
            input_task_id=task_id,
            result=existing_task,
        )

        updated_task = task_v2_factory.build(
            id=task_id,
            organization_id=organization_id,
            title=request.title if request.title else existing_task.title,
            status=request.status if request.status else existing_task.status,
            priority=request.priority if request.priority else existing_task.priority,
            type=existing_task.type,
            owner_user_id=request.owner_user_id
            if request.owner_user_id
            else existing_task.owner_user_id,
            created_at=created_at,
            created_by_user_id=existing_task.created_by_user_id,
            updated_at=updated_at,
            updated_by_user_id=user_id,
            account_id=existing_task.account_id,
            contact_ids=existing_task.contact_ids,
            completed_at=completed_at,
            completed_by_user_id=user_id,
        )
        self.mock_user_service_get_by_id_and_organization_id(
            user_service=user_service,
            input_organization_id=organization_id,
            input_user_id=user_id,
            result=UserDTO(
                id=user_id,
                first_name="test",
                last_name="test",
                email="<EMAIL>",
                phone_number=None,
                linkedin_url=None,
                avatar_s3_key=None,
                avatar_url=None,
                display_name="test test",
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                updated_at=zoned_utc_now(),
                updated_by_user_id=user_id,
                deactivated_at=None,
                deactivated_by_user_id=None,
                user_organization=None,
            ),
        )

        self.mock_knock_client_trigger_workflow(
            knock_client=knock_client,
            result=KnockWorkflowResponse(workflow_run_id="workflow_run_id"),
        )
        task_query_service.get_by_id_v2.side_effect = [existing_task, updated_task]

        result = await task_v2_service.patch_by_id_v2(
            task_id=task_id,
            organization_id=organization_id,
            user_id=user_id,
            request=request,
        )

        assert result == updated_task

    async def test_patch_task_v2_participant_users_not_modified(
        self,
        task_v2_factory: type[TaskV2Factory],
        task_query_service: AsyncMock,
        task_v2_service: TaskV2Service,
    ) -> None:
        organization_id = uuid4()
        user_id = uuid4()
        participant_user_id = uuid4()
        task_id = uuid4()

        existing_task = task_v2_factory.build(
            id=task_id,
            organization_id=organization_id,
            title="Original Task",
            status=TaskStatus.IN_PROGRESS,
            priority=TaskPriority.HIGH,
            type=TaskType.EMAIL,
            participants=[EntityParticipant(user_id=participant_user_id)],
        )

        self.mock_task_query_service_get_by_id_v2(
            task_query_service=task_query_service,
            input_organization_id=organization_id,
            input_task_id=task_id,
            result=existing_task,
        )

        self.mock_task_query_service_get_by_id_v2_sequence(
            task_query_service=task_query_service,
            input_organization_id=organization_id,
            input_task_id_list=[task_id, task_id],
            result_list=[existing_task, existing_task],
        )

        request = PatchTaskRequest(
            participant_user_id_list=[participant_user_id],
        )

        result = await task_v2_service.patch_by_id_v2(
            task_id=task_id,
            organization_id=organization_id,
            user_id=user_id,
            request=request,
        )
        assert result == existing_task

    @pytest.mark.parametrize("source_type", [TaskSourceType.USER])
    async def test_patch_task_v2_participant_users_modified(
        self,
        task_v2_factory: type[TaskV2Factory],
        task_repository: AsyncMock,
        task_query_service: AsyncMock,
        task_v2_service: TaskV2Service,
        db_task_factory: type[DbTaskFactory],
        source_type: TaskSourceType,
    ) -> None:
        """Test successful removal of task references when pipeline's account is removed."""
        organization_id = uuid4()
        user_id = uuid4()
        task_id = uuid4()
        now = zoned_utc_now()

        existing_task = task_v2_factory.build(
            id=task_id,
            organization_id=organization_id,
            created_by_user_id=user_id,
            created_at=now,
            source_type=source_type,
        )

        self.mock_task_query_service_get_by_id_v2(
            task_query_service=task_query_service,
            input_organization_id=organization_id,
            input_task_id=task_id,
            result=existing_task,
        )

        new_participant_user_id = uuid4()
        updated_task = existing_task.model_copy(
            update={
                "participants": [EntityParticipant(user_id=new_participant_user_id)],
            }
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=task_repository,
            input_organization_id=organization_id,
            where_columns={"id": task_id},
            update_columns={
                "participants": [EntityParticipant(user_id=new_participant_user_id)],
                "updated_by_user_id": user_id,
            },
            update_date_fields=["updated_at"],
            result=db_task_factory.build(
                id=task_id,
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=now,
                source_type=source_type,
                updated_by_user_id=user_id,
                updated_at=now,
                participants=[EntityParticipant(user_id=new_participant_user_id)],
            ),
        )

        self.mock_task_query_service_get_by_id_v2_sequence(
            task_query_service=task_query_service,
            input_organization_id=organization_id,
            input_task_id_list=[task_id, task_id],
            result_list=[existing_task, updated_task],
        )

        request = PatchTaskRequest(
            participant_user_id_list=[new_participant_user_id],
        )

        result = await task_v2_service.patch_by_id_v2(
            task_id=task_id,
            organization_id=organization_id,
            user_id=user_id,
            request=request,
        )
        assert result == updated_task

    async def test_patch_task_v2_not_found(
        self,
        task_v2_service: TaskV2Service,
        task_query_service: AsyncMock,
    ) -> None:
        task_id = uuid4()
        organization_id = uuid4()
        user_id = uuid4()

        task_query_service.get_by_id_v2.side_effect = ResourceNotFoundError()

        request = PatchTaskRequest(
            title="Updated Task",
            status=TaskStatus.COMPLETED,
            priority=TaskPriority.LOW,
            owner_user_id=user_id,
            contact_ids=None,
            due_at=None,
            note=None,
        )

        with pytest.raises(ResourceNotFoundError):
            await task_v2_service.patch_by_id_v2(
                task_id=task_id,
                organization_id=organization_id,
                user_id=user_id,
                request=request,
            )

    @pytest.mark.parametrize(
        "source_type", [TaskSourceType.SYSTEM, TaskSourceType.USER]
    )
    async def test_remove_entity(
        self,
        task_v2_service: TaskV2Service,
        task_repository: AsyncMock,
        db_task_factory: type[DbTaskFactory],
        source_type: TaskSourceType,
    ) -> None:
        task_id = uuid4()
        organization_id = uuid4()
        user_id = uuid4()
        now = zoned_utc_now()

        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=task_repository,
            input_organization_id=organization_id,
            where_columns={"id": task_id},
            update_columns={
                "archived_by_user_id": user_id,
            },
            update_date_fields=["archived_at"],
            result=db_task_factory.build(
                id=task_id,
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=now,
                archived_at=now,
                archived_by_user_id=user_id,
                source_type=source_type,
            ),
        )

        remove_entity_response = await task_v2_service.remove_entity(
            user_id=user_id,
            organization_id=organization_id,
            entity_id=task_id,
        )
        expected = DeleteEntityResponse(
            id=task_id,
            deleted_at=now,
            deleted_by_user_id=user_id,
        )
        assert remove_entity_response == expected

    async def test_insert_task_v2_account_not_found(
        self,
        task_v2_service: TaskV2Service,
        account_query_service: AsyncMock,
    ) -> None:
        """Test that task creation fails when account doesn't exist."""
        organization_id = uuid4()
        user_id = uuid4()
        account_id = uuid4()
        now = zoned_utc_now()

        request = CreateTaskRequest(
            title="Test Task",
            status=TaskStatus.IN_PROGRESS,
            priority=TaskPriority.HIGH,
            type=TaskType.EMAIL,
            owner_user_id=user_id,
            due_at=now + timedelta(days=1),
            note="Test note",
            pipeline_id=None,
            contact_ids=[],
            account_id=account_id,
            sequence_id=None,
            email_thread_ids=[],
            email_ids=[],
            meeting_id=None,
            insight_id=None,
            is_time_specified=True,
            source_type=TaskSourceType.USER,
        )

        # Mock account validation to fail
        account_query_service.get_account_v2.side_effect = ResourceNotFoundError(
            "Account not found"
        )

        with pytest.raises(ResourceNotFoundError) as e:
            await task_v2_service.insert_task_v2(
                created_by_user_id=user_id,
                organization_id=organization_id,
                request=request,
            )
        assert str(e.value) == "Account not found"

    async def test_insert_task_v2_contact_not_found(
        self,
        task_v2_service: TaskV2Service,
        account_query_service: AsyncMock,
        contact_query_service: AsyncMock,
        account_v2_factory: type[AccountV2Factory],
    ) -> None:
        """Test that task creation fails when contact doesn't exist."""
        organization_id = uuid4()
        user_id = uuid4()
        account_id = uuid4()
        contact_id = uuid4()
        now = zoned_utc_now()

        request = CreateTaskRequest(
            title="Test Task",
            status=TaskStatus.IN_PROGRESS,
            priority=TaskPriority.HIGH,
            type=TaskType.EMAIL,
            owner_user_id=user_id,
            due_at=now + timedelta(days=1),
            note="Test note",
            pipeline_id=None,
            contact_ids=[contact_id],
            account_id=account_id,
            sequence_id=None,
            email_thread_ids=[],
            email_ids=[],
            meeting_id=None,
            insight_id=None,
            is_time_specified=True,
            source_type=TaskSourceType.USER,
        )

        # Mock account validation
        self.mock_account_query_service_get_account_v2(
            account_query_service=account_query_service,
            input_organization_id=organization_id,
            input_account_id=account_id,
            result=account_v2_factory.build(
                id=account_id,
                organization_id=organization_id,
            ),
        )

        # Mock contact validation to return empty list (simulating no contacts found)
        self.mock_contact_query_service_list_contacts_v2(
            contact_query_service=contact_query_service,
            input_organization_id=organization_id,
            input_contact_ids={contact_id},
            result=[],
        )

        with pytest.raises(ResourceNotFoundError) as e:
            await task_v2_service.insert_task_v2(
                created_by_user_id=user_id,
                organization_id=organization_id,
                request=request,
            )
        assert str(e.value) == f"Contacts with ids {[contact_id]} not found"

    async def test_insert_task_v2_contact_not_associated_with_account(
        self,
        task_v2_service: TaskV2Service,
        account_query_service: AsyncMock,
        contact_query_service: AsyncMock,
        account_v2_factory: type[AccountV2Factory],
        contact_v2_factory: type[ContactV2Factory],
    ) -> None:
        """Test that task creation fails when contact is not associated with account."""
        organization_id = uuid4()
        user_id = uuid4()
        account_id = uuid4()
        contact_id = uuid4()
        now = zoned_utc_now()

        request = CreateTaskRequest(
            title="Test Task",
            status=TaskStatus.IN_PROGRESS,
            priority=TaskPriority.HIGH,
            type=TaskType.EMAIL,
            owner_user_id=user_id,
            due_at=now + timedelta(days=1),
            note="Test note",
            pipeline_id=None,
            contact_ids=[contact_id],
            account_id=account_id,
            sequence_id=None,
            email_thread_ids=[],
            email_ids=[],
            meeting_id=None,
            insight_id=None,
            is_time_specified=True,
            source_type=TaskSourceType.USER,
        )

        # Mock account validation
        self.mock_account_query_service_get_account_v2(
            account_query_service=account_query_service,
            input_organization_id=organization_id,
            input_account_id=account_id,
            result=account_v2_factory.build(
                id=account_id,
                organization_id=organization_id,
            ),
        )

        other_account_id = uuid4()
        # Mock contact validation
        self.mock_contact_query_service_list_contacts_v2(
            contact_query_service=contact_query_service,
            input_organization_id=organization_id,
            input_contact_ids={contact_id},
            result=[
                contact_v2_factory.build(
                    id=contact_id,
                    organization_id=organization_id,
                    first_name="Test",
                    last_name="Contact",
                    primary_account_id=other_account_id,
                )
            ],
        )

        # Mock contact associations validation to return empty list (no association)
        self.mock_contact_query_service_list_active_contact_associations_for_account(
            contact_query_service=contact_query_service,
            input_organization_id=organization_id,
            input_account_id=account_id,
            result=[],
        )

        with pytest.raises(InvalidArgumentError) as e:
            await task_v2_service.insert_task_v2(
                created_by_user_id=user_id,
                organization_id=organization_id,
                request=request,
            )
        assert (
            str(e.value)
            == f"None of the contacts {[contact_id]} are associated with account {account_id}. At least one contact must be associated."
        )

    async def test_insert_task_v2_with_only_account(
        self,
        task_v2_service: TaskV2Service,
        task_repository: AsyncMock,
        task_query_service: AsyncMock,
        db_task_factory: type[DbTaskFactory],
        task_reference_factory: type[TaskReferenceFactory],
        user_service: AsyncMock,
        knock_client: AsyncMock,
        account_query_service: AsyncMock,
        account_v2_factory: type[AccountV2Factory],
        activity_factory: type[ActivityFactory],
        activity_service: AsyncMock,
    ) -> None:
        """Test task creation with only account_id (no contacts)."""
        organization_id = uuid4()
        user_id = uuid4()
        now = zoned_utc_now()
        account_id = uuid4()

        request = CreateTaskRequest(
            title="Test Task",
            status=TaskStatus.IN_PROGRESS,
            priority=TaskPriority.HIGH,
            type=TaskType.EMAIL,
            owner_user_id=user_id,
            due_at=now + timedelta(days=1),
            note="Test note",
            pipeline_id=None,
            contact_ids=None,
            account_id=account_id,
            sequence_id=None,
            email_thread_ids=[],
            email_ids=[],
            meeting_id=None,
            insight_id=None,
            is_time_specified=True,
            source_type=TaskSourceType.USER,
        )

        # Mock account validation
        self.mock_account_query_service_get_account_v2(
            account_query_service=account_query_service,
            input_organization_id=organization_id,
            input_account_id=account_id,
            result=account_v2_factory.build(
                id=account_id,
                organization_id=organization_id,
            ),
        )

        insert_sequence: list[TableModel] = []

        db_task = db_task_factory.build(
            title=request.title,
            organization_id=organization_id,
            type=request.type,
            status=request.status,
            priority=request.priority,
            created_at=now,
            created_by_user_id=request.owner_user_id,
            updated_at=now,
            owner_user_id=request.owner_user_id,
            due_at=request.due_at,
            note=request.note,
            is_time_specified=request.is_time_specified,
            source_type=request.source_type,
        )
        insert_sequence.append(db_task)

        task_references: list[TaskReference] = []
        if request.account_id:
            task_reference = task_reference_factory.build(
                task_id=db_task.id,
                reference_id=str(request.account_id),
                reference_id_type=TaskReferenceIdType.ACCOUNT_ID,
                organization_id=organization_id,
                created_by_user_id=user_id,
            )
            task_references.append(task_reference)
            insert_sequence.append(task_reference)

        self.mock_repo_insert_sequence(task_repository, insert_sequence)

        # Mock activity service for task creation with only account
        self.mock_activity_service_insert_activity(
            activity_service=activity_service,
            input_organization_id=organization_id,
            input_activity_request=ActivityRequest(
                type=ActivityType.TASK,
                sub_type=ActivitySubType.TASK_CREATED,
                priority=ActivityPriority.MEDIUM,
                status=ActivityStatus.NEW,
                owner_user_id=user_id,
                account_id=request.account_id,
                reference_id_type=ActivityReferenceIdType.TASK_ID,
                reference_id=str(db_task.id),
                display_name="Task Created.",
                created_at=now,
                created_by_user_id=user_id,
                sub_references=[],
            ),
            result=activity_factory.build(
                id=uuid4(),
                organization_id=organization_id,
                created_at=now,
                created_by_user_id=user_id,
            ),
        )

        expected_task = task_v2_from_db(
            db_task=db_task,
            db_task_references=task_references,
            comments=[],
        )

        # Mock comment query service
        self.mock_comment_query_service_find_all_comments_by_reference_id(
            comment_query_service=task_query_service.comment_query_service,
            input_organization_id=organization_id,
            input_reference_id=db_task.id,
            result=[],
        )

        self.mock_task_query_service_get_by_id_v2(
            task_query_service=task_query_service,
            input_organization_id=organization_id,
            input_task_id=db_task.id,
            result=expected_task,
        )
        self.mock_user_service_get_by_id_and_organization_id(
            user_service=user_service,
            input_organization_id=organization_id,
            input_user_id=user_id,
            result=UserDTO(
                id=user_id,
                first_name="test",
                last_name="test",
                email="<EMAIL>",
                phone_number=None,
                linkedin_url=None,
                avatar_s3_key=None,
                avatar_url=None,
                display_name="test test",
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                updated_at=zoned_utc_now(),
                updated_by_user_id=user_id,
                deactivated_at=None,
                deactivated_by_user_id=None,
                user_organization=None,
            ),
        )

        self.mock_knock_client_trigger_workflow(
            knock_client=knock_client,
            result=KnockWorkflowResponse(workflow_run_id="workflow_run_id"),
        )

        result = await task_v2_service.insert_task_v2(
            created_by_user_id=user_id,
            organization_id=organization_id,
            request=request,
        )

        assert result == expected_task

    async def test_insert_task_v2_without_account_or_contacts(
        self,
        task_v2_service: TaskV2Service,
        task_repository: AsyncMock,
        task_query_service: AsyncMock,
        custom_object_service: AsyncMock,
        db_task_factory: type[DbTaskFactory],
        task_v2_factory: type[TaskV2Factory],
        user_service: AsyncMock,
        knock_client: AsyncMock,
        activity_factory: type[ActivityFactory],
        activity_service: AsyncMock,
    ) -> None:
        """Test task creation without account_id or contacts."""
        organization_id = uuid4()
        user_id = uuid4()
        now = zoned_utc_now()

        request = CreateTaskRequest(
            title="Test Task",
            status=TaskStatus.IN_PROGRESS,
            priority=TaskPriority.HIGH,
            type=TaskType.EMAIL,
            owner_user_id=user_id,
            due_at=now + timedelta(days=1),
            note="Test note",
            pipeline_id=None,
            contact_ids=None,
            account_id=None,
            sequence_id=None,
            email_thread_ids=[],
            email_ids=[],
            meeting_id=None,
            insight_id=None,
            is_time_specified=True,
            source_type=TaskSourceType.USER,
        )

        insert_sequence: list[TableModel] = []

        db_task = db_task_factory.build(
            title=request.title,
            organization_id=organization_id,
            type=request.type,
            status=request.status,
            priority=request.priority,
            created_at=now,
            created_by_user_id=request.owner_user_id,
            updated_at=now,
            owner_user_id=request.owner_user_id,
            due_at=request.due_at,
            note=request.note,
            is_time_specified=request.is_time_specified,
            source_type=request.source_type,
        )
        insert_sequence.append(db_task)

        self.mock_repo_insert_sequence(task_repository, insert_sequence)

        # Mock activity service for task creation without account or contacts
        self.mock_activity_service_insert_activity(
            activity_service=activity_service,
            input_organization_id=organization_id,
            input_activity_request=ActivityRequest(
                type=ActivityType.TASK,
                sub_type=ActivitySubType.TASK_CREATED,
                priority=ActivityPriority.MEDIUM,
                status=ActivityStatus.NEW,
                owner_user_id=user_id,
                account_id=None,
                reference_id_type=ActivityReferenceIdType.TASK_ID,
                reference_id=str(db_task.id),
                display_name="Task Created.",
                created_at=now,
                created_by_user_id=user_id,
                sub_references=[],
            ),
            result=activity_factory.build(
                id=uuid4(),
                organization_id=organization_id,
                created_at=now,
                created_by_user_id=user_id,
            ),
        )

        expected_task = task_v2_from_db(
            db_task=db_task,
            db_task_references=[],
            comments=[],
        )

        # Mock comment query service
        self.mock_comment_query_service_find_all_comments_by_reference_id(
            comment_query_service=task_query_service.comment_query_service,
            input_organization_id=organization_id,
            input_reference_id=db_task.id,
            result=[],
        )

        self.mock_task_query_service_get_by_id_v2(
            task_query_service=task_query_service,
            input_organization_id=organization_id,
            input_task_id=db_task.id,
            result=expected_task,
        )
        self.mock_user_service_get_by_id_and_organization_id(
            user_service=user_service,
            input_organization_id=organization_id,
            input_user_id=user_id,
            result=UserDTO(
                id=user_id,
                first_name="test",
                last_name="test",
                email="<EMAIL>",
                phone_number=None,
                linkedin_url=None,
                avatar_s3_key=None,
                avatar_url=None,
                display_name="test test",
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                updated_at=zoned_utc_now(),
                updated_by_user_id=user_id,
                deactivated_at=None,
                deactivated_by_user_id=None,
                user_organization=None,
            ),
        )

        self.mock_knock_client_trigger_workflow(
            knock_client=knock_client,
            result=KnockWorkflowResponse(workflow_run_id="workflow_run_id"),
        )

        result = await task_v2_service.insert_task_v2(
            created_by_user_id=user_id,
            organization_id=organization_id,
            request=request,
        )

        assert result == expected_task

    async def test_process_contact_account_change_upon_creation(
        self,
        task_v2_service: TaskV2Service,
        contact_v2_factory: type[ContactV2Factory],
        contact_factory: type[ContactFactory],
    ) -> None:
        """Test that no task updates occur when a contact is created."""
        organization_id = uuid4()
        entity_id = uuid4()
        new_account_id = uuid4()
        user_id = uuid4()

        result = await task_v2_service.process_task_account_change(
            previous_account_id=new_account_id,
            new_account_id=new_account_id,
            task_reference_type=TaskReferenceIdType.CONTACT_ID,
            organization_id=organization_id,
            updated_by_user_id=user_id,
            reference_id=entity_id,
        )
        assert result == {}

    async def test_process_pipeline_account_change_upon_creation(
        self,
        task_v2_service: TaskV2Service,
    ) -> None:
        """Test that no task updates occur when a pipeline is created."""
        organization_id = uuid4()
        entity_id = uuid4()
        new_account_id = uuid4()
        user_id = uuid4()

        result = await task_v2_service.process_task_account_change(
            previous_account_id=new_account_id,
            new_account_id=new_account_id,
            task_reference_type=TaskReferenceIdType.PIPELINE_ID,
            organization_id=organization_id,
            updated_by_user_id=user_id,
            reference_id=entity_id,
        )
        assert result == {}

    async def test_process_contact_account_change_same_account(
        self,
        task_v2_service: TaskV2Service,
    ) -> None:
        """Test that no task updates occur when contact's account hasn't changed."""
        organization_id = uuid4()
        entity_id = uuid4()
        account_id = uuid4()
        user_id = uuid4()

        result = await task_v2_service.process_task_account_change(
            previous_account_id=account_id,
            new_account_id=account_id,
            task_reference_type=TaskReferenceIdType.CONTACT_ID,
            organization_id=organization_id,
            updated_by_user_id=user_id,
            reference_id=entity_id,
        )
        assert result == {}

    async def test_process_pipeline_account_change_same_account(
        self,
        task_v2_service: TaskV2Service,
    ) -> None:
        """Test that no task updates occur when pipeline's account hasn't changed."""
        organization_id = uuid4()
        entity_id = uuid4()
        account_id = uuid4()
        user_id = uuid4()

        result = await task_v2_service.process_task_account_change(
            previous_account_id=account_id,
            new_account_id=account_id,
            task_reference_type=TaskReferenceIdType.PIPELINE_ID,
            organization_id=organization_id,
            updated_by_user_id=user_id,
            reference_id=entity_id,
        )
        assert result == {}

    async def test_process_contact_account_change_no_tasks_found(
        self,
        task_v2_service: TaskV2Service,
        task_query_service: AsyncMock,
    ) -> None:
        """Test that no updates occur when no tasks are found for the contact."""
        organization_id = uuid4()
        entity_id = uuid4()
        old_account_id = uuid4()
        new_account_id = uuid4()
        user_id = uuid4()

        self.mock_task_query_service_get_tasks_by_reference_ids_and_type(
            task_query_service=task_query_service,
            input_reference_ids=[entity_id],
            input_reference_id_type=TaskReferenceIdType.CONTACT_ID,
            input_organization_id=organization_id,
            result={},
        )

        result = await task_v2_service.process_task_account_change(
            previous_account_id=old_account_id,
            new_account_id=new_account_id,
            task_reference_type=TaskReferenceIdType.CONTACT_ID,
            organization_id=organization_id,
            updated_by_user_id=user_id,
            reference_id=entity_id,
        )
        assert result == {}

    async def test_process_pipeline_account_change_no_tasks_found(
        self,
        task_v2_service: TaskV2Service,
        task_query_service: AsyncMock,
    ) -> None:
        """Test that no updates occur when no tasks are found for the pipeline."""
        organization_id = uuid4()
        entity_id = uuid4()
        old_account_id = uuid4()
        new_account_id = uuid4()
        user_id = uuid4()

        self.mock_task_query_service_get_tasks_by_reference_ids_and_type(
            task_query_service=task_query_service,
            input_reference_ids=[entity_id],
            input_reference_id_type=TaskReferenceIdType.PIPELINE_ID,
            input_organization_id=organization_id,
            result={},
        )

        result = await task_v2_service.process_task_account_change(
            previous_account_id=old_account_id,
            new_account_id=new_account_id,
            task_reference_type=TaskReferenceIdType.PIPELINE_ID,
            organization_id=organization_id,
            updated_by_user_id=user_id,
            reference_id=entity_id,
        )
        assert result == {}

    async def test_process_contact_account_change_update_references(
        self,
        task_v2_service: TaskV2Service,
        task_repository: AsyncMock,
        task_query_service: AsyncMock,
        task_v2_factory: type[TaskV2Factory],
    ) -> None:
        """Test successful update of task references when contact's account changes."""
        organization_id = uuid4()
        entity_id = uuid4()
        old_account_id = uuid4()
        new_account_id = uuid4()
        user_id = uuid4()
        task_id = uuid4()
        now = zoned_utc_now()

        task = task_v2_factory.build(
            id=task_id,
            organization_id=organization_id,
            created_by_user_id=user_id,
            created_at=now,
        )

        self.mock_task_query_service_get_tasks_by_reference_ids_and_type(
            task_query_service=task_query_service,
            input_reference_ids=[entity_id],
            input_reference_id_type=TaskReferenceIdType.CONTACT_ID,
            input_organization_id=organization_id,
            result={task_id: [task]},
        )

        # Mock deleting old account reference
        self.mock_repo_delete_task_references(
            mock_repository=task_repository,
            input_task_id=task_id,
            input_organization_id=organization_id,
            input_reference_id=str(old_account_id),
            input_reference_id_type=TaskReferenceIdType.ACCOUNT_ID.name,
            input_deleted_by_user_id=user_id,
            result=1,
        )

        # Mock inserting new account reference
        task_reference = TaskReference(
            id=uuid4(),
            task_id=task_id,
            reference_id=str(new_account_id),
            reference_id_type=TaskReferenceIdType.ACCOUNT_ID.name,
            created_at=now,
            created_by_user_id=user_id,
            organization_id=organization_id,
        )
        self.mock_repo_insert(task_repository, task_reference)

        result = await task_v2_service.process_task_account_change(
            previous_account_id=old_account_id,
            new_account_id=new_account_id,
            task_reference_type=TaskReferenceIdType.CONTACT_ID,
            organization_id=organization_id,
            updated_by_user_id=user_id,
            reference_id=entity_id,
        )
        assert result == {task_id: [task]}

        # Verify task reference deletion and insertion
        task_repository.delete_task_references.assert_called_once()
        task_repository.insert.assert_called_once()

        # Verify the inserted task reference
        inserted_reference = task_repository.insert.call_args[0][0]
        assert inserted_reference.task_id == task_reference.task_id
        assert inserted_reference.reference_id == task_reference.reference_id
        assert inserted_reference.reference_id_type == task_reference.reference_id_type
        assert inserted_reference.organization_id == task_reference.organization_id
        assert (
            inserted_reference.created_by_user_id == task_reference.created_by_user_id
        )

    async def test_process_pipeline_account_change_update_references(
        self,
        task_v2_service: TaskV2Service,
        task_repository: AsyncMock,
        task_query_service: AsyncMock,
        task_v2_factory: type[TaskV2Factory],
    ) -> None:
        """Test successful update of task references when pipeline's account changes."""
        organization_id = uuid4()
        entity_id = uuid4()
        old_account_id = uuid4()
        new_account_id = uuid4()
        user_id = uuid4()
        task_id = uuid4()
        now = zoned_utc_now()

        task = task_v2_factory.build(
            id=task_id,
            organization_id=organization_id,
            created_by_user_id=user_id,
            created_at=now,
        )

        self.mock_task_query_service_get_tasks_by_reference_ids_and_type(
            task_query_service=task_query_service,
            input_reference_ids=[entity_id],
            input_reference_id_type=TaskReferenceIdType.PIPELINE_ID,
            input_organization_id=organization_id,
            result={task_id: [task]},
        )

        # Mock deleting old account reference
        self.mock_repo_delete_task_references(
            mock_repository=task_repository,
            input_task_id=task_id,
            input_organization_id=organization_id,
            input_reference_id=str(old_account_id),
            input_reference_id_type=TaskReferenceIdType.ACCOUNT_ID.name,
            input_deleted_by_user_id=user_id,
            result=1,
        )

        # Mock inserting new account reference
        task_reference = TaskReference(
            id=uuid4(),
            task_id=task_id,
            reference_id=str(new_account_id),
            reference_id_type=TaskReferenceIdType.ACCOUNT_ID.name,
            created_at=now,
            created_by_user_id=user_id,
            organization_id=organization_id,
        )
        self.mock_repo_insert(task_repository, task_reference)

        result = await task_v2_service.process_task_account_change(
            previous_account_id=old_account_id,
            new_account_id=new_account_id,
            task_reference_type=TaskReferenceIdType.PIPELINE_ID,
            organization_id=organization_id,
            updated_by_user_id=user_id,
            reference_id=entity_id,
        )
        assert result == {task_id: [task]}

        # Verify task reference deletion and insertion
        task_repository.delete_task_references.assert_called_once()
        task_repository.insert.assert_called_once()

        # Verify the inserted task reference
        inserted_reference = task_repository.insert.call_args[0][0]
        assert inserted_reference.task_id == task_reference.task_id
        assert inserted_reference.reference_id == task_reference.reference_id
        assert inserted_reference.reference_id_type == task_reference.reference_id_type
        assert inserted_reference.organization_id == task_reference.organization_id
        assert (
            inserted_reference.created_by_user_id == task_reference.created_by_user_id
        )

    def mock_task_query_service_get_tasks_by_reference_ids_and_type(
        self,
        task_query_service: AsyncMock,
        input_reference_ids: list[UUID],
        input_reference_id_type: TaskReferenceIdType,
        input_organization_id: UUID,
        result: dict[UUID, list[TaskV2]],
    ) -> None:
        """Mock task_query_service.get_tasks_by_reference_ids_and_type."""

        def lookup(
            reference_ids: list[UUID],
            reference_id_type: TaskReferenceIdType,
            organization_id: UUID,
        ) -> dict[UUID, list[TaskV2]]:
            assert reference_ids == input_reference_ids
            assert reference_id_type == input_reference_id_type
            assert organization_id == input_organization_id
            return result

        task_query_service.get_tasks_by_reference_ids_and_type = AsyncMock(
            side_effect=lookup
        )

    def mock_repo_delete_task_references(
        self,
        mock_repository: AsyncMock,
        input_task_id: UUID,
        input_organization_id: UUID,
        input_reference_id: str,
        input_reference_id_type: str,
        input_deleted_by_user_id: UUID,
        result: int,
    ) -> None:
        """Mock repository.delete_task_references."""

        def lookup(
            task_id: UUID,
            organization_id: UUID,
            reference_id: str,
            reference_id_type: str,
            deleted_by_user_id: UUID,
            deleted_at: datetime,
        ) -> int:
            assert task_id == input_task_id
            assert organization_id == input_organization_id
            assert reference_id == input_reference_id
            assert reference_id_type == input_reference_id_type
            assert deleted_by_user_id == input_deleted_by_user_id
            return result

        mock_repository.delete_task_references = AsyncMock(side_effect=lookup)

    @pytest.fixture
    def task_repository(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def task_template_repository(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def task_query_service(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def custom_object_service(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def organization_info_repository(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def user_notification_service(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def user_service(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def notification_service(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def knock_client(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def contact_query_service(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def account_query_service(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def activity_service(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def crm_sync_push_service(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def domain_crm_association_service(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def task_v2_service(
        self,
        task_repository: AsyncMock,
        task_template_repository: AsyncMock,
        task_query_service: AsyncMock,
        custom_object_service: AsyncMock,
        organization_info_repository: AsyncMock,
        user_notification_service: AsyncMock,
        user_service: AsyncMock,
        notification_service: AsyncMock,
        contact_query_service: AsyncMock,
        account_query_service: AsyncMock,
        activity_service: AsyncMock,
        crm_sync_push_service: AsyncMock,
        domain_crm_association_service: AsyncMock,
    ) -> TaskV2Service:
        return TaskV2Service(
            task_repository=task_repository,
            task_template_repository=task_template_repository,
            task_query_service=task_query_service,
            custom_object_service=custom_object_service,
            organization_info_repository=organization_info_repository,
            user_notification_service=user_notification_service,
            user_service=user_service,
            notification_service=notification_service,
            contact_query_service=contact_query_service,
            account_query_service=account_query_service,
            activity_service=activity_service,
            crm_sync_push_service=crm_sync_push_service,
            feature_flag_service=AsyncMock(),
            domain_crm_association_service=domain_crm_association_service,
        )

    def mock_task_query_service_get_by_id_v2(
        self,
        task_query_service: AsyncMock,
        input_organization_id: UUID,
        input_task_id: UUID,
        result: TaskV2,
    ) -> None:
        """Mock task_query_service.get_by_id_v2 to return a sequence of results."""

        def lookup(task_id: UUID, organization_id: UUID) -> TaskV2:
            assert organization_id == input_organization_id
            assert task_id == input_task_id
            return result

        task_query_service.get_by_id_v2 = AsyncMock(side_effect=lookup)

    def mock_task_query_service_get_by_id_v2_sequence(
        self,
        task_query_service: AsyncMock,
        input_organization_id: UUID,
        input_task_id_list: list[UUID],
        result_list: list[TaskV2],
    ) -> None:
        """Mock task_query_service.get_by_id_v2 to return a sequence of results."""

        def lookup(task_id: UUID, organization_id: UUID) -> TaskV2:
            assert organization_id == input_organization_id
            assert task_id == input_task_id_list.pop(0)
            return result_list.pop(0)

        task_query_service.get_by_id_v2 = AsyncMock(side_effect=lookup)

    def mock_user_service_get_by_id_and_organization_id(
        self,
        user_service: AsyncMock,
        input_organization_id: UUID,
        input_user_id: UUID,
        result: UserDTO,
    ) -> None:
        """Mock user_service.get_by_id_and_organization_id to return a result."""

        def lookup(user_id: UUID, organization_id: UUID) -> UserDTO:
            assert organization_id == input_organization_id
            assert user_id == input_user_id
            return result

        user_service.get_by_id_and_organization_id = AsyncMock(side_effect=lookup)

    def mock_knock_client_trigger_workflow(
        self,
        knock_client: AsyncMock,
        result: KnockWorkflowResponse,
    ) -> None:
        """Mock knock_client.trigger_workflow to return a workflow response."""

        def lookup() -> KnockWorkflowResponse:
            return result

        knock_client.trigger_workflow = AsyncMock(side_effect=lookup)

    def mock_repo_find_task_references_by_task_ids(
        self,
        mock_repository: AsyncMock,
        input_task_ids: list[UUID],
        input_organization_id: UUID,
        result: list[TaskReference],
    ) -> None:
        def lookup(task_ids: list[UUID], organization_id: UUID) -> list[TaskReference]:
            assert task_ids == input_task_ids
            assert organization_id == input_organization_id
            return result

        mock_repository.find_task_references_by_task_ids = AsyncMock(side_effect=lookup)

    def mock_account_query_service_get_account_v2(
        self,
        account_query_service: AsyncMock,
        input_organization_id: UUID,
        input_account_id: UUID,
        result: AccountV2,
    ) -> None:
        """Mock account_query_service.get_account_v2."""

        def lookup(
            account_id: UUID,
            organization_id: UUID,
            include_custom_object: bool | None = False,
        ) -> AccountV2:
            assert organization_id == input_organization_id
            assert account_id == input_account_id
            return result

        account_query_service.get_account_v2 = AsyncMock(side_effect=lookup)

    def mock_contact_query_service_list_contacts_v2(
        self,
        contact_query_service: AsyncMock,
        input_organization_id: UUID,
        input_contact_ids: set[UUID],
        result: list[ContactV2],
    ) -> None:
        """Mock contact_query_service.list_contacts_v2."""

        def lookup(
            organization_id: UUID,
            only_include_contact_ids: set[UUID],
            include_custom_object: bool | None = False,
        ) -> list[ContactV2]:
            assert organization_id == input_organization_id
            assert only_include_contact_ids == input_contact_ids
            return result

        contact_query_service.list_contacts_v2 = AsyncMock(side_effect=lookup)

    def mock_contact_query_service_list_active_contact_associations_for_account(
        self,
        contact_query_service: AsyncMock,
        input_organization_id: UUID,
        input_account_id: UUID,
        result: list[ContactAccountAssociation],
    ) -> None:
        """Mock contact_query_service.list_active_contact_associations_for_account."""

        def lookup(
            organization_id: UUID, account_id: UUID
        ) -> list[ContactAccountAssociation]:
            assert organization_id == input_organization_id
            assert account_id == input_account_id
            return result

        contact_query_service.list_active_contact_associations_for_account = AsyncMock(
            side_effect=lookup
        )

    def mock_activity_service_insert_activity(
        self,
        activity_service: AsyncMock,
        input_activity_request: ActivityRequest,
        result: Activity,
        input_organization_id: UUID | None = None,
    ) -> None:
        async def lookup(
            organization_id: UUID, insert_activity_request: ActivityRequest
        ) -> Activity:
            if input_organization_id:
                assert organization_id == input_organization_id
            # Compare only the fields we care about, ignoring created_at which can vary
            assert insert_activity_request.type == input_activity_request.type
            assert insert_activity_request.sub_type == input_activity_request.sub_type
            assert insert_activity_request.priority == input_activity_request.priority
            assert insert_activity_request.status == input_activity_request.status
            assert (
                insert_activity_request.owner_user_id
                == input_activity_request.owner_user_id
            )
            assert (
                insert_activity_request.account_id == input_activity_request.account_id
            )
            assert (
                insert_activity_request.reference_id_type
                == input_activity_request.reference_id_type
            )
            assert (
                insert_activity_request.display_name
                == input_activity_request.display_name
            )
            assert (
                insert_activity_request.sub_references
                == input_activity_request.sub_references
            )
            assert (
                insert_activity_request.created_by_user_id
                == input_activity_request.created_by_user_id
            )
            return result

        activity_service.insert_activity = AsyncMock(side_effect=lookup)

    def mock_comment_query_service_find_all_comments_by_reference_id(
        self,
        comment_query_service: AsyncMock,
        input_organization_id: UUID,
        input_reference_id: UUID,
        result: list[Comment],
    ) -> None:
        """Mock comment_query_service.find_all_comments_by_reference_id."""

        async def lookup(
            organization_id: UUID,
            reference_id: UUID,
            reference_id_type: CommentReferenceIdType,
        ) -> list[Comment]:
            assert organization_id == input_organization_id
            assert reference_id == input_reference_id
            assert reference_id_type == CommentReferenceIdType.TASK
            return result

        comment_query_service.find_all_comments_by_reference_id = AsyncMock(
            side_effect=lookup
        )

    async def test_process_contact_account_change_remove_account(
        self,
        task_v2_service: TaskV2Service,
        task_repository: AsyncMock,
        task_query_service: AsyncMock,
        task_v2_factory: type[TaskV2Factory],
    ) -> None:
        """Test successful removal of task references when contact's account is removed."""
        organization_id = uuid4()
        entity_id = uuid4()
        old_account_id = uuid4()
        user_id = uuid4()
        task_id = uuid4()
        now = zoned_utc_now()

        task = task_v2_factory.build(
            id=task_id,
            organization_id=organization_id,
            created_by_user_id=user_id,
            created_at=now,
        )

        self.mock_task_query_service_get_tasks_by_reference_ids_and_type(
            task_query_service=task_query_service,
            input_reference_ids=[entity_id],
            input_reference_id_type=TaskReferenceIdType.CONTACT_ID,
            input_organization_id=organization_id,
            result={task_id: [task]},
        )

        # Mock deleting old account reference
        self.mock_repo_delete_task_references(
            mock_repository=task_repository,
            input_task_id=task_id,
            input_organization_id=organization_id,
            input_reference_id=str(old_account_id),
            input_reference_id_type=TaskReferenceIdType.ACCOUNT_ID.name,
            input_deleted_by_user_id=user_id,
            result=1,
        )

        result = await task_v2_service.process_task_account_change(
            previous_account_id=old_account_id,
            new_account_id=None,
            task_reference_type=TaskReferenceIdType.CONTACT_ID,
            organization_id=organization_id,
            updated_by_user_id=user_id,
            reference_id=entity_id,
        )
        assert result == {task_id: [task]}

        # Verify task reference deletion and no insertion
        task_repository.delete_task_references.assert_called_once()
        task_repository.insert.assert_not_called()

    async def test_process_pipeline_account_change_remove_account(
        self,
        task_v2_service: TaskV2Service,
        task_repository: AsyncMock,
        task_query_service: AsyncMock,
        task_v2_factory: type[TaskV2Factory],
    ) -> None:
        """Test successful removal of task references when pipeline's account is removed."""
        organization_id = uuid4()
        entity_id = uuid4()
        old_account_id = uuid4()
        user_id = uuid4()
        task_id = uuid4()
        now = zoned_utc_now()

        task = task_v2_factory.build(
            id=task_id,
            organization_id=organization_id,
            created_by_user_id=user_id,
            created_at=now,
        )

        self.mock_task_query_service_get_tasks_by_reference_ids_and_type(
            task_query_service=task_query_service,
            input_reference_ids=[entity_id],
            input_reference_id_type=TaskReferenceIdType.PIPELINE_ID,
            input_organization_id=organization_id,
            result={task_id: [task]},
        )

        # Mock deleting old account reference
        self.mock_repo_delete_task_references(
            mock_repository=task_repository,
            input_task_id=task_id,
            input_organization_id=organization_id,
            input_reference_id=str(old_account_id),
            input_reference_id_type=TaskReferenceIdType.ACCOUNT_ID.name,
            input_deleted_by_user_id=user_id,
            result=1,
        )

        result = await task_v2_service.process_task_account_change(
            previous_account_id=old_account_id,
            new_account_id=None,
            task_reference_type=TaskReferenceIdType.PIPELINE_ID,
            organization_id=organization_id,
            updated_by_user_id=user_id,
            reference_id=entity_id,
        )
        assert result == {task_id: [task]}

        # Verify task reference deletion and no insertion
        task_repository.delete_task_references.assert_called_once()
        task_repository.insert.assert_not_called()

    async def test_insert_task_v2_creates_crm_associations(
        self,
        task_v2_service: TaskV2Service,
        task_repository: AsyncMock,
        task_query_service: AsyncMock,
        db_task_factory: type[DbTaskFactory],
        task_reference_factory: type[TaskReferenceFactory],
        user_service: AsyncMock,
        knock_client: AsyncMock,
        account_query_service: AsyncMock,
        contact_query_service: AsyncMock,
        account_v2_factory: type[AccountV2Factory],
        contact_v2_factory: type[ContactV2Factory],
        contact_account_association_factory: type[ContactAccountAssociationFactory],
        activity_factory: type[ActivityFactory],
        activity_service: AsyncMock,
        domain_crm_association_service: AsyncMock,
    ) -> None:
        """Test that CRM associations are created when a task is inserted."""
        organization_id = uuid4()
        user_id = uuid4()
        now = zoned_utc_now()
        account_id = uuid4()
        contact_id = uuid4()
        meeting_id = uuid4()
        task_id = uuid4()

        # Use a fixed UUID for participant to make testing easier
        participant_user_id = UUID("29081c6a-73bf-4073-b20b-4341830d9e5e")

        # Create request with multiple association fields
        request = CreateTaskRequest(
            title="Test Task",
            status=TaskStatus.IN_PROGRESS,
            priority=TaskPriority.HIGH,
            type=TaskType.EMAIL,
            owner_user_id=user_id,
            due_at=now + timedelta(days=1),
            note="Test note",
            pipeline_id=None,
            contact_ids=[contact_id],
            account_id=account_id,
            sequence_id=None,
            email_thread_ids=None,
            email_ids=None,
            meeting_id=meeting_id,
            voice_call_id=None,
            insight_id=None,
            is_time_specified=True,
            source_type=TaskSourceType.USER,
            participant_user_id_list=[participant_user_id],
        )

        # Mock account validation
        self.mock_account_query_service_get_account_v2(
            account_query_service=account_query_service,
            input_organization_id=organization_id,
            input_account_id=account_id,
            result=account_v2_factory.build(
                id=account_id,
                organization_id=organization_id,
            ),
        )

        # Mock contact validation
        self.mock_contact_query_service_list_contacts_v2(
            contact_query_service=contact_query_service,
            input_organization_id=organization_id,
            input_contact_ids={contact_id},
            result=[
                contact_v2_factory.build(
                    id=contact_id,
                    organization_id=organization_id,
                )
            ],
        )

        # Mock contact associations validation
        self.mock_contact_query_service_list_active_contact_associations_for_account(
            contact_query_service=contact_query_service,
            input_organization_id=organization_id,
            input_account_id=account_id,
            result=[
                contact_account_association_factory.build(
                    id=uuid4(),
                    contact_id=contact_id,
                    account_id=account_id,
                    organization_id=organization_id,
                    created_at=now,
                    created_by_user_id=user_id,
                )
            ],
        )

        # Create the task object and mock its insertion
        db_task = db_task_factory.build(
            id=task_id,
            title=request.title,
            organization_id=organization_id,
            type=request.type,
            status=request.status,
            priority=request.priority,
            created_at=now,
            created_by_user_id=request.owner_user_id,
            updated_at=now,
            owner_user_id=request.owner_user_id,
            due_at=request.due_at,
            note=request.note,
            is_time_specified=request.is_time_specified,
            source_type=request.source_type,
            participants=EntityParticipant.list_from_request_field(
                request.participant_user_id_list
            ),
        )

        # Simplified references - just create the basic references we need for the test
        task_references = []
        insert_sequence: list[TableModel] = [db_task]

        # Add required references (simplified from original)
        for ref_type, ref_id in [
            (TaskReferenceIdType.CONTACT_ID, contact_id),
            (TaskReferenceIdType.ACCOUNT_ID, account_id),
            (TaskReferenceIdType.MEETING_ID, meeting_id),
        ]:
            task_reference = task_reference_factory.build(
                task_id=task_id,
                reference_id=str(ref_id),
                reference_id_type=ref_type,
                organization_id=organization_id,
                created_by_user_id=user_id,
            )
            task_references.append(task_reference)
            insert_sequence.append(task_reference)

        self.mock_repo_insert_sequence(task_repository, insert_sequence)

        # Mock activity service
        self.mock_activity_service_insert_activity(
            activity_service=activity_service,
            input_organization_id=organization_id,
            input_activity_request=ActivityRequest(
                type=ActivityType.TASK,
                sub_type=ActivitySubType.TASK_CREATED,
                priority=ActivityPriority.MEDIUM,
                status=ActivityStatus.NEW,
                owner_user_id=user_id,
                account_id=request.account_id,
                reference_id_type=ActivityReferenceIdType.TASK_ID,
                reference_id=str(db_task.id),
                display_name="Task Created.",
                created_at=now,
                created_by_user_id=user_id,
                sub_references=[
                    ActivitySubReferenceRequest(
                        type=ActivitySubReferenceType.TASK_ASSIGNED_TO,
                        value=str(request.owner_user_id),
                        contact_id=contact_id,
                    )
                ],
            ),
            result=activity_factory.build(
                id=uuid4(),
                organization_id=organization_id,
                created_at=now,
                created_by_user_id=user_id,
            ),
        )

        expected_task = task_v2_from_db(
            db_task=db_task,
            db_task_references=task_references,
            comments=[],
        )

        # Mock comment query service
        self.mock_comment_query_service_find_all_comments_by_reference_id(
            comment_query_service=task_query_service.comment_query_service,
            input_organization_id=organization_id,
            input_reference_id=db_task.id,
            result=[],
        )

        self.mock_task_query_service_get_by_id_v2(
            task_query_service=task_query_service,
            input_organization_id=organization_id,
            input_task_id=db_task.id,
            result=expected_task,
        )

        self.mock_user_service_get_by_id_and_organization_id(
            user_service=user_service,
            input_organization_id=organization_id,
            input_user_id=user_id,
            result=UserDTO(
                id=user_id,
                first_name="test",
                last_name="test",
                email="<EMAIL>",
                phone_number=None,
                linkedin_url=None,
                avatar_s3_key=None,
                avatar_url=None,
                display_name="test test",
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                updated_at=zoned_utc_now(),
                updated_by_user_id=user_id,
                deactivated_at=None,
                deactivated_by_user_id=None,
                user_organization=None,
            ),
        )

        # Mock the temporal workflow notification
        with patch(
            "salestech_be.core.task.service.task_v2_service._send_task_notification"
        ) as mock_notification:
            mock_notification.return_value = None

            result = await task_v2_service.insert_task_v2(
                created_by_user_id=user_id,
                organization_id=organization_id,
                request=request,
            )

            # Verify the task was created and returned correctly
            assert result == expected_task

            # Verify that domain CRM associations were created
            domain_crm_association_service.bulk_create_domain_crm_associations.assert_called_once()

            # Get the list of associations that were created
            associations = domain_crm_association_service.bulk_create_domain_crm_associations.call_args[
                1
            ]["domain_crm_associations"]

            # Verify we have the right number of associations (one for owner, one for each contact)
            assert len(associations) == len(request.participant_user_id_list) + len(  # type: ignore
                request.contact_ids  # type: ignore
            )

            # Verify task notification was called with the correct parameters
            mock_notification.assert_called_once_with(
                task_id, organization_id, expected_task.due_at
            )

            # Verify each association has the correct data
            for assoc in associations:
                assert isinstance(assoc, CreateTaskCrmAssociation)
                assert assoc.organization_id == organization_id
                assert assoc.created_by_user_id == user_id
                assert assoc.task_id == task_id

                # If it's a user association
                if assoc.user_id:
                    if assoc.user_id == request.owner_user_id:
                        assert assoc.association_role == DomainCRMAssociationRole.OWNER
                    else:
                        assert (
                            assoc.association_role
                            == DomainCRMAssociationRole.PARTICIPANT
                        )
                        assert assoc.user_id in request.participant_user_id_list  # type: ignore

                # If it's a contact association
                if assoc.contact_id:
                    assert assoc.contact_id in request.contact_ids  # type: ignore

                # Common fields
                assert assoc.account_id == request.account_id
                assert assoc.meeting_id == request.meeting_id


def contact_domain_enriched_cdc_event_factory(
    reference_id_type: TaskReferenceIdType,
    modified_fields: set[str],
    before: Contact | Pipeline | None,
    after: Contact | Pipeline,
    before_domain_model: ContactV2 | PipelineV2 | None,
    current_domain_model: ContactV2 | PipelineV2,
) -> DomainEnrichedCDCEvent[Contact, ContactV2]:
    return DomainEnrichedCDCEvent[Contact, ContactV2](
        modified_fields=modified_fields,
        before=cast(Contact | None, before),
        after=cast(Contact, after),
        before_domain_model=cast(ContactV2 | None, before_domain_model),
        current_domain_model=cast(ContactV2, current_domain_model),
    )


def pipeline_domain_enriched_cdc_event_factory(
    modified_fields: set[str],
    before: Pipeline | None,
    after: Pipeline,
    before_domain_model: PipelineV2 | None,
    current_domain_model: PipelineV2,
) -> DomainEnrichedCDCEvent[Pipeline, PipelineV2]:
    return DomainEnrichedCDCEvent[Pipeline, PipelineV2](
        modified_fields=modified_fields,
        before=before,
        after=after,
        before_domain_model=before_domain_model,
        current_domain_model=current_domain_model,
    )
