import pytest
from unittest.mock import Mock, AsyncMock
from uuid import uuid4

from salestech_be.core.reporting.service.query_builder_service import QueryBuilderService
from salestech_be.core.reporting.type.filter import Filter
from salestech_be.core.reporting.type.query_config import (
    FilterConfig,
    FilterGroup,
    FilterLogic,
    FilterOperator,
    DatasetConfig,
    DatasetFieldConfig,
    FieldConfig,
)
from salestech_be.db.dbengine.core import DatabaseEngine


class TestQueryBuilderService:
    """Test cases for QueryBuilderService"""

    @pytest.fixture
    def mock_db_engine(self):
        """Mock database engine"""
        return Mock(spec=DatabaseEngine)

    @pytest.fixture
    def query_builder_service(self, mock_db_engine):
        """Create QueryBuilderService instance with mocked dependencies"""
        return QueryBuilderService(db_engine=mock_db_engine)

    def test_merge_filters_with_no_filters(self, query_builder_service):
        """Test merge_filters when no new filters are provided"""
        existing_filter_group = FilterGroup(
            filters=[],
            logic=FilterLogic.AND,
        )

        result = query_builder_service.merge_filters(
            filter_group=existing_filter_group,
            filters=None,
        )

        assert result == existing_filter_group

    def test_merge_filters_with_no_existing_group(self, query_builder_service):
        """Test merge_filters when no existing filter group exists"""
        filters = [
            Filter(col="name", op="=", val="test"),
            Filter(col="age", op=">", val=18),
        ]

        result = query_builder_service.merge_filters(
            filter_group=None,
            filters=filters,
            dataset_name="users",
        )

        assert result is not None
        assert result.logic == FilterLogic.AND
        assert len(result.filters) == 2
        assert all(isinstance(f, FilterConfig) for f in result.filters)

    def test_merge_filters_with_and_logic(self, query_builder_service):
        """Test merge_filters with existing AND filter group"""
        # Create existing filter group with AND logic
        existing_filter = FilterConfig(
            dataset_field=DatasetFieldConfig(
                dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                field=FieldConfig(field_id="status", field_name="status"),
            ),
            operator=FilterOperator.EQUALS,
            value="active",
        )
        existing_filter_group = FilterGroup(
            filters=[existing_filter],
            logic=FilterLogic.AND,
        )

        # New filters to merge
        new_filters = [
            Filter(col="name", op="=", val="test"),
        ]

        result = query_builder_service.merge_filters(
            filter_group=existing_filter_group,
            filters=new_filters,
            dataset_name="users",
        )

        assert result is not None
        assert result.logic == FilterLogic.AND
        assert len(result.filters) == 2

    def test_merge_filters_with_or_logic(self, query_builder_service):
        """Test merge_filters with existing OR filter group"""
        # Create existing filter group with OR logic
        existing_filter = FilterConfig(
            dataset_field=DatasetFieldConfig(
                dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                field=FieldConfig(field_id="status", field_name="status"),
            ),
            operator=FilterOperator.EQUALS,
            value="active",
        )
        existing_filter_group = FilterGroup(
            filters=[existing_filter],
            logic=FilterLogic.OR,
        )

        # New filters to merge
        new_filters = [
            Filter(col="name", op="=", val="test"),
        ]

        result = query_builder_service.merge_filters(
            filter_group=existing_filter_group,
            filters=new_filters,
            dataset_name="users",
        )

        assert result is not None
        assert result.logic == FilterLogic.AND  # Should wrap with AND
        assert len(result.filters) == 2
        # First filter should be the original OR group
        assert isinstance(result.filters[0], FilterGroup)
        assert result.filters[0].logic == FilterLogic.OR
        # Second filter should be the new filter config
        assert isinstance(result.filters[1], FilterConfig)

    def test_merge_filters_with_invalid_filter(self, query_builder_service):
        """Test merge_filters with invalid filter that should be skipped"""
        filters = [
            Filter(col="name", op="INVALID_OP", val="test"),  # Invalid operator
            Filter(col="age", op="=", val=18),  # Valid filter
        ]

        result = query_builder_service.merge_filters(
            filter_group=None,
            filters=filters,
            dataset_name="users",
        )

        # Should only have one filter (the valid one)
        assert result is not None
        assert len(result.filters) == 1

    def test_merge_filters_with_all_invalid_filters(self, query_builder_service):
        """Test merge_filters when all filters are invalid"""
        filters = [
            Filter(col="name", op="INVALID_OP", val="test"),
        ]

        result = query_builder_service.merge_filters(
            filter_group=None,
            filters=filters,
            dataset_name="users",
        )

        # Should return None since no valid filters
        assert result is None


class TestFilterConfig:
    """Test cases for FilterConfig.from_filter method"""

    def test_from_filter_basic_equality(self):
        """Test converting basic equality filter"""
        filter_obj = Filter(col="name", op="=", val="test")

        result = FilterConfig.from_filter(filter_obj, "users")

        assert result.operator == FilterOperator.EQUALS
        assert result.value == "test"
        assert result.dataset_field.field.field_name == "name"
        assert result.dataset_field.dataset.dataset_name == "users"

    def test_from_filter_null_value_equals(self):
        """Test converting filter with null value and equals operator"""
        filter_obj = Filter(col="name", op="=", val=None)

        result = FilterConfig.from_filter(filter_obj, "users")

        assert result.operator == FilterOperator.IS_NULL
        assert result.value is None

    def test_from_filter_null_value_not_equals(self):
        """Test converting filter with null value and not equals operator"""
        filter_obj = Filter(col="name", op="!=", val=None)

        result = FilterConfig.from_filter(filter_obj, "users")

        assert result.operator == FilterOperator.IS_NOT_NULL
        assert result.value is None

    def test_from_filter_in_operator(self):
        """Test converting filter with IN operator"""
        filter_obj = Filter(col="status", op="IN", val=["active", "pending"])

        result = FilterConfig.from_filter(filter_obj, "users")

        assert result.operator == FilterOperator.IN
        assert result.value == ["active", "pending"]

    def test_from_filter_case_insensitive_operator(self):
        """Test that operator matching is case insensitive"""
        filter_obj = Filter(col="name", op="like", val="%test%")

        result = FilterConfig.from_filter(filter_obj, "users")

        assert result.operator == FilterOperator.LIKE
        assert result.value == "%test%"

    def test_from_filter_invalid_operator(self):
        """Test that invalid operator raises ValueError"""
        filter_obj = Filter(col="name", op="INVALID", val="test")

        with pytest.raises(ValueError, match="Unsupported filter operator"):
            FilterConfig.from_filter(filter_obj, "users")

    def test_from_filter_invalid_type(self):
        """Test that non-Filter object raises ValueError"""
        with pytest.raises(ValueError, match="Expected Filter object"):
            FilterConfig.from_filter("not a filter", "users")


class TestFilterGroup:
    """Test cases for FilterGroup helper methods"""

    def test_add_filter(self):
        """Test adding a single filter to a group"""
        group = FilterGroup(filters=[], logic=FilterLogic.AND)
        filter_config = FilterConfig(
            dataset_field=DatasetFieldConfig(
                dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                field=FieldConfig(field_id="name", field_name="name"),
            ),
            operator=FilterOperator.EQUALS,
            value="test",
        )

        group.add_filter(filter_config)

        assert len(group.filters) == 1
        assert group.filters[0] == filter_config

    def test_add_filters(self):
        """Test adding multiple filters to a group"""
        group = FilterGroup(filters=[], logic=FilterLogic.AND)
        filter_configs = [
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                    field=FieldConfig(field_id="name", field_name="name"),
                ),
                operator=FilterOperator.EQUALS,
                value="test",
            ),
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                    field=FieldConfig(field_id="age", field_name="age"),
                ),
                operator=FilterOperator.GREATER_THAN,
                value=18,
            ),
        ]

        group.add_filters(filter_configs)

        assert len(group.filters) == 2
        assert group.filters == filter_configs

    def test_is_empty(self):
        """Test checking if filter group is empty"""
        empty_group = FilterGroup(filters=[], logic=FilterLogic.AND)
        non_empty_group = FilterGroup(
            filters=[
                FilterConfig(
                    dataset_field=DatasetFieldConfig(
                        dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                        field=FieldConfig(field_id="name", field_name="name"),
                    ),
                    operator=FilterOperator.EQUALS,
                    value="test",
                )
            ],
            logic=FilterLogic.AND,
        )

        assert empty_group.is_empty() is True
        assert non_empty_group.is_empty() is False

    def test_to_sql_empty_group(self):
        """Test SQL generation for empty filter group"""
        group = FilterGroup(filters=[], logic=FilterLogic.AND)

        result = group.to_sql()

        assert result == ""

    def test_to_sql_single_filter(self):
        """Test SQL generation for single filter"""
        filter_config = FilterConfig(
            dataset_field=DatasetFieldConfig(
                dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                field=FieldConfig(field_id="name", field_name="name"),
            ),
            operator=FilterOperator.EQUALS,
            value="test",
        )
        group = FilterGroup(filters=[filter_config], logic=FilterLogic.AND)

        result = group.to_sql()

        # Should not wrap single filter in parentheses
        assert result == "users.name = 'test'"

    def test_to_sql_multiple_filters_and(self):
        """Test SQL generation for multiple filters with AND logic"""
        filter_configs = [
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                    field=FieldConfig(field_id="name", field_name="name"),
                ),
                operator=FilterOperator.EQUALS,
                value="test",
            ),
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                    field=FieldConfig(field_id="age", field_name="age"),
                ),
                operator=FilterOperator.GREATER_THAN,
                value=18,
            ),
        ]
        group = FilterGroup(filters=filter_configs, logic=FilterLogic.AND)

        result = group.to_sql()

        # Should wrap multiple filters in parentheses
        assert result == "(users.name = 'test' AND users.age > 18)"

    def test_to_sql_multiple_filters_or(self):
        """Test SQL generation for multiple filters with OR logic"""
        filter_configs = [
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                    field=FieldConfig(field_id="name", field_name="name"),
                ),
                operator=FilterOperator.EQUALS,
                value="test",
            ),
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                    field=FieldConfig(field_id="age", field_name="age"),
                ),
                operator=FilterOperator.GREATER_THAN,
                value=18,
            ),
        ]
        group = FilterGroup(filters=filter_configs, logic=FilterLogic.OR)

        result = group.to_sql()

        # Should wrap multiple filters in parentheses with OR
        assert result == "(users.name = 'test' OR users.age > 18)"


class TestIntegration:
    """Integration tests demonstrating complete functionality"""

    @pytest.fixture
    def mock_db_engine(self):
        """Mock database engine"""
        return Mock(spec=DatabaseEngine)

    @pytest.fixture
    def query_builder_service(self, mock_db_engine):
        """Create QueryBuilderService instance with mocked dependencies"""
        return QueryBuilderService(db_engine=mock_db_engine)

    def test_complete_filter_merge_workflow(self, query_builder_service):
        """Test a complete workflow of merging filters with different scenarios"""

        # Start with an existing filter group (OR logic)
        existing_filter = FilterConfig(
            dataset_field=DatasetFieldConfig(
                dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                field=FieldConfig(field_id="status", field_name="status"),
            ),
            operator=FilterOperator.IN,
            value=["active", "pending"],
        )
        existing_filter_group = FilterGroup(
            filters=[existing_filter],
            logic=FilterLogic.OR,
        )

        # Add new filters
        new_filters = [
            Filter(col="name", op="LIKE", val="%john%"),
            Filter(col="age", op=">=", val=18),
            Filter(col="email", op="!=", val=None),  # This should become IS NOT NULL
        ]

        # Merge the filters
        result = query_builder_service.merge_filters(
            filter_group=existing_filter_group,
            filters=new_filters,
            dataset_name="users",
        )

        # Verify the result structure
        assert result is not None
        assert result.logic == FilterLogic.AND  # Should wrap with AND
        assert len(result.filters) == 4  # Original group + 3 new filters

        # First filter should be the original OR group
        assert isinstance(result.filters[0], FilterGroup)
        assert result.filters[0].logic == FilterLogic.OR

        # Remaining filters should be the new FilterConfig objects
        for i in range(1, 4):
            assert isinstance(result.filters[i], FilterConfig)

        # Check that the NULL handling worked correctly
        email_filter = result.filters[3]  # The email filter
        assert email_filter.operator == FilterOperator.IS_NOT_NULL
        assert email_filter.value is None

        # Generate SQL to verify the complete structure
        sql = result.to_sql()
        expected_parts = [
            "users.status IN ('active', 'pending')",   # Original OR group (single filter, no parens)
            "users.name LIKE '%john%'",                 # LIKE filter
            "users.age >= 18",                          # Numeric comparison
            "users.email IS NOT NULL"                   # NULL handling
        ]

        # Verify all expected parts are in the SQL
        for part in expected_parts:
            assert part in sql

        # Verify the overall structure (should be wrapped in parentheses with AND)
        assert sql.startswith("(")
        assert sql.endswith(")")
        assert " AND " in sql

    def test_empty_filter_scenarios(self, query_builder_service):
        """Test various empty filter scenarios"""

        # Test with None filters
        result1 = query_builder_service.merge_filters(None, None)
        assert result1 is None

        # Test with empty list
        result2 = query_builder_service.merge_filters(None, [])
        assert result2 is None

        # Test with existing group and no new filters
        existing_group = FilterGroup(filters=[], logic=FilterLogic.AND)
        result3 = query_builder_service.merge_filters(existing_group, None)
        assert result3 == existing_group

        # Test with existing group and empty new filters
        result4 = query_builder_service.merge_filters(existing_group, [])
        assert result4 == existing_group
