from typing import Any
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import UUID, uuid4

import pytest
from pytest_mock import MockerFixture

from salestech_be.common.exception import InvalidArgumentError, ResourceNotFoundError
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.email.account.service_v2 import EmailAccountServiceV2
from salestech_be.core.email.pool.schema import (
    EmailAccountPoolMembershipResponse,
    EmailAccountPoolResponse,
)
from salestech_be.core.email.pool.service import (
    CheckMailboxCapacityResult,
    EmailAccountPoolService,
)
from salestech_be.core.metadata.types import (
    ContactAccountAssociationLite,
)
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    SequenceEnrollmentService,
)
from salestech_be.core.sequence.type.sequence_enrollment_types import (
    SequenceEnrollment,
    SequenceEnrollmentEligibility,
)
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.db.dao.sequence_enrollment_repo import SequenceEnrollmentRepository
from salestech_be.db.dao.sequence_repository import SequenceExecutionRepository
from salestech_be.db.dto.email_dto import EmailAccountDto, EmailAccountPoolDto
from salestech_be.db.models.email_account import EmailAccountPool
from salestech_be.db.models.sequence import (
    SequenceEnrollment as DbSequenceEnrollment,
)
from salestech_be.db.models.sequence import (
    SequenceEnrollmentDisplayStatus,
    SequenceEnrollmentExitReasonCode,
    SequenceEnrollmentStatus,
    SequenceFailureReason,
    SequenceStepExecutionStatus,
)
from salestech_be.db.models.task import TaskReferenceIdType, TaskStatus
from salestech_be.db.models.unsubscription_group import UnsubscriptionGroup
from salestech_be.temporal.workflows.sequence.sequence_enrollment_workflow import (
    SequenceEnrollmentWorkflow,
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.sequence.enrollment.schema import (
    ContactForSequenceEnrollment,
    DeleteSequenceEnrollmentResponse,
    PatchSequenceEnrollmentRequest,
    PatchSequenceEnrollmentResponse,
    PreviewSequenceEnrollmentRequest,
)
from tests.util.factories import (
    AccountV2Factory,
    ContactV2Factory,
    SequenceEnrollmentFactory,
)
from tests.util.service_test import ServiceTest


class TestSequenceEnrollmentService(ServiceTest):
    async def test_create_sequence_enrollment_from_contacts(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_repository: AsyncMock,
        contact_query_service: AsyncMock,
        contact_resolve_service: AsyncMock,
        account_query_service: AsyncMock,
        contact_v2_factory: ContactV2Factory,
        account_v2_factory: AccountV2Factory,
        email_account_pool_service: AsyncMock,
        sequence_enrollment_query_service: AsyncMock,
    ) -> None:
        """Test happy path of creating sequence enrollment from contacts"""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        contact_id = uuid4()
        account_id = uuid4()
        sequence_id = uuid4()
        email = "<EMAIL>"

        # Setup contact to enroll
        contact = ContactForSequenceEnrollment(
            contact_id=contact_id,
            account_id=account_id,
            email=email,
        )

        # Mock contact query service to return valid contact
        contact_v2 = contact_v2_factory.build(
            id=contact_id,
            contact_account_associations=[
                ContactAccountAssociationLite(
                    contact_id=contact_id,
                    account_id=account_id,
                    is_primary=True,
                )
            ],
            owner_user_id=user_id,
        )
        contact_query_service.list_contacts_v2.return_value = [contact_v2]

        # Mock account query service to return valid account
        account_query_service.list_accounts_v2.return_value = [
            account_v2_factory.build(id=account_id)
        ]

        # Setup necessary mock for the query service to make the actual method work
        sequence_enrollment_query_service.find_enrollments_by_contacts_for_sequence.return_value = {}

        pool_id = uuid4()
        email_account_id = uuid4()

        # Mock the map_default_pools_by_owner_ids method
        default_pool = EmailAccountPoolResponse(
            id=pool_id,
            name="Default Pool",
            organization_id=organization_id,
            created_at=str(zoned_utc_now()),
            created_by_user_id=user_id,
        )
        email_account_pool_service.map_default_pools_by_owner_ids.return_value = {
            user_id: default_pool
        }

        # Create a mock EmailAccountPoolDto with a fully warmed up account
        mock_pool_dto = AsyncMock(spec=EmailAccountPoolDto)
        mock_account_dto = AsyncMock(spec=EmailAccountDto)
        mock_account_dto.is_fully_warm = True

        mock_pool_dto.email_account_dtos = {email_account_id: mock_account_dto}
        mock_pool_dto.email_account_pool = EmailAccountPool(
            id=pool_id,
            name="Default Pool",
            organization_id=organization_id,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            is_default=True,
            owner_user_id=user_id,
        )

        # Set warm email accounts
        mock_pool_dto.get_email_account_ids_fully_warm.return_value = {email_account_id}
        mock_pool_dto.get_email_account_ids_with_use_override.return_value = set()

        email_account_pool_service.get_email_account_pool_dto.return_value = (
            mock_pool_dto
        )

        # Mock email account pool membership response for backward compatibility
        email_account_pool_service.get_default_email_account_pool_with_members.return_value = EmailAccountPoolMembershipResponse(
            email_account_pool=default_pool,
            memberships=[],
        )

        # Mock map_the_most_relevant_contact_emails
        contact_resolve_service.map_the_most_relevant_contact_emails.return_value = {
            contact_id: (email, account_id)
        }

        # Mock resolve_relevant_account_by_contact_and_email_pairs
        contact_resolve_service.resolve_relevant_account_by_contact_and_email_pairs.return_value = {
            contact_id: account_id
        }

        # Mock sequence enrollment creation with bulk_insert
        enrollment_id = uuid4()
        enrollment = DbSequenceEnrollment(
            id=enrollment_id,
            organization_id=organization_id,
            sequence_id=sequence_id,
            contact_id=contact_id,
            email=email,
            status=SequenceEnrollmentStatus.ACTIVE,
            enrolled_at=zoned_utc_now(),
            enrolled_by_user_id=user_id,
            updated_at=zoned_utc_now(),
            updated_by_user_id=user_id,
            email_account_pool_id=pool_id,
        )

        # Mock bulk_insert to return enrollment
        sequence_enrollment_repository.bulk_insert.return_value = [enrollment]
        sequence_enrollment_repository.find_count_of_sequence_enrollments_by_sequence_id_and_status.return_value = 0

        # Act
        (
            enrolled_contacts,
            failed_enrollments,
        ) = await sequence_enrollment_service.create_sequence_enrollment_from_contacts(
            contacts=[contact],
            sequence_id=sequence_id,
            organization_id=organization_id,
            user_id=user_id,
        )

        # Assert
        assert len(enrolled_contacts) == 1
        assert len(failed_enrollments) == 0

        enrolled = enrolled_contacts[0]
        assert enrolled.id == enrollment_id
        assert enrolled.contact_id == contact_id
        assert enrolled.status == SequenceEnrollmentStatus.ACTIVE

        # Verify key service calls
        contact_query_service.list_contacts_v2.assert_awaited_once_with(
            organization_id=organization_id,
            only_include_contact_ids={contact_id},
        )
        account_query_service.list_accounts_v2.assert_awaited_once_with(
            organization_id=organization_id,
            only_include_account_ids={account_id},
        )

        # Verify map_the_most_relevant_contact_emails was called correctly
        contact_resolve_service.map_the_most_relevant_contact_emails.assert_awaited_once_with(
            organization_id=organization_id,
            contact_account_pairs=[(contact_id, account_id)],
        )

        # Verify find_enrollments_by_contacts_for_sequence was called
        sequence_enrollment_query_service.find_enrollments_by_contacts_for_sequence.assert_awaited_once_with(
            contact_ids={contact_id},
            sequence_id=sequence_id,
            organization_id=organization_id,
        )

        # Verify pool related methods were called
        email_account_pool_service.map_default_pools_by_owner_ids.assert_awaited_once_with(
            organization_id=organization_id,
            owner_user_ids=[user_id],
        )

        # Verify bulk_insert was called with the right parameters
        sequence_enrollment_repository.bulk_insert.assert_awaited_once()
        args, kwargs = sequence_enrollment_repository.bulk_insert.call_args
        assert kwargs["table_model"] == DbSequenceEnrollment
        assert len(kwargs["instances"]) == 1
        instance = kwargs["instances"][0]
        assert instance.contact_id == contact_id
        assert instance.sequence_id == sequence_id
        assert instance.organization_id == organization_id
        assert instance.status == SequenceEnrollmentStatus.ACTIVE
        assert instance.email == email
        assert instance.account_id == account_id
        assert instance.email_account_pool_id == pool_id

    async def test_create_sequence_enrollment_contact_without_primary_email(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_repository: AsyncMock,
        contact_query_service: AsyncMock,
        contact_resolve_service: AsyncMock,
        account_query_service: AsyncMock,
        contact_v2_factory: ContactV2Factory,
        account_v2_factory: AccountV2Factory,
        email_account_pool_service: AsyncMock,
        sequence_enrollment_query_service: AsyncMock,
    ) -> None:
        """Test the case where a contact doesn't have a primary email"""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        contact_id = uuid4()
        contact_without_email_id = uuid4()
        account_id = uuid4()
        sequence_id = uuid4()
        email = "<EMAIL>"

        # Setup contacts to enroll - one with email, one without
        contact_with_email = ContactForSequenceEnrollment(
            contact_id=contact_id,
            account_id=account_id,
            email=email,
        )
        contact_without_email = ContactForSequenceEnrollment(
            contact_id=contact_without_email_id,
            account_id=account_id,
            email=None,  # No email provided
        )

        # Mock contact query service to return valid contacts
        contact_v2 = contact_v2_factory.build(
            id=contact_id,
            contact_account_associations=[
                ContactAccountAssociationLite(
                    contact_id=contact_id,
                    account_id=account_id,
                    is_primary=True,
                )
            ],
            owner_user_id=user_id,
        )
        contact_without_email_v2 = contact_v2_factory.build(
            id=contact_without_email_id,
            contact_account_associations=[
                ContactAccountAssociationLite(
                    contact_id=contact_without_email_id,
                    account_id=account_id,
                    is_primary=True,
                )
            ],
            owner_user_id=user_id,
        )
        contact_query_service.list_contacts_v2.return_value = [
            contact_v2,
            contact_without_email_v2,
        ]

        # Mock account query service to return valid account
        account_query_service.list_accounts_v2.return_value = [
            account_v2_factory.build(id=account_id)
        ]

        # Setup necessary mock for the query service
        sequence_enrollment_query_service.find_enrollments_by_contacts_for_sequence.return_value = {}

        # Mock email account pool service
        pool_id = uuid4()

        # Mock the map_default_pools_by_owner_ids method
        default_pool = EmailAccountPoolResponse(
            id=pool_id,
            name="Default Pool",
            organization_id=organization_id,
            created_at=str(zoned_utc_now()),
            created_by_user_id=user_id,
        )
        email_account_pool_service.map_default_pools_by_owner_ids.return_value = {
            user_id: default_pool
        }

        # Mock email account pool membership response too for backward compatibility
        email_account_pool_service.get_default_email_account_pool_with_members.return_value = EmailAccountPoolMembershipResponse(
            email_account_pool=default_pool,
            memberships=[],
        )

        # Mock the email account pool dto to return with the SAME pool_id
        mock_pool_dto = AsyncMock(spec=EmailAccountPoolDto)
        mock_pool_dto.email_account_dtos = {}
        mock_pool_dto.email_account_pool = EmailAccountPool(
            id=pool_id,
            name="Default Pool",
            organization_id=organization_id,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            is_default=True,
            owner_user_id=user_id,
        )
        mock_pool_dto.get_email_account_ids_fully_warm.return_value = set()
        mock_pool_dto.get_email_account_ids_with_use_override.return_value = set()

        email_account_pool_service.get_email_account_pool_dto.return_value = (
            mock_pool_dto
        )

        # Mock map_the_most_relevant_contact_emails
        contact_resolve_service.map_the_most_relevant_contact_emails.return_value = {
            contact_id: (email, account_id)
            # No entry for contact_without_email_id
        }

        # Mock resolve_relevant_account_by_contact_and_email_pairs
        contact_resolve_service.resolve_relevant_account_by_contact_and_email_pairs.return_value = {
            contact_id: account_id
        }

        # Mock resolve_relevant_contact_email_by_contact_and_account_pairs
        contact_resolve_service.resolve_relevant_contact_email_by_contact_and_account_pairs.return_value = {
            contact_id: (email, account_id),
            # No entry for contact_without_email_id or returning None for it
        }

        # Mock sequence enrollment creation with bulk_insert
        enrollment_id = uuid4()
        enrollment = DbSequenceEnrollment(
            id=enrollment_id,
            organization_id=organization_id,
            sequence_id=sequence_id,
            contact_id=contact_id,
            email=email,
            status=SequenceEnrollmentStatus.ACTIVE,
            enrolled_at=zoned_utc_now(),
            enrolled_by_user_id=user_id,
            updated_at=zoned_utc_now(),
            updated_by_user_id=user_id,
            email_account_pool_id=pool_id,  # Set the pool ID here
        )

        # Mock bulk_insert to return only one enrollment (for the contact with email)
        sequence_enrollment_repository.bulk_insert.return_value = [enrollment]
        sequence_enrollment_repository.find_count_of_sequence_enrollments_by_sequence_id_and_status.return_value = 0

        contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info.return_value = {
            contact_without_email_id: (None, None)
        }

        # Act
        (
            enrolled_contacts,
            failed_enrollments,
        ) = await sequence_enrollment_service.create_sequence_enrollment_from_contacts(
            contacts=[contact_with_email, contact_without_email],
            sequence_id=sequence_id,
            organization_id=organization_id,
            user_id=user_id,
        )

        # Assert
        assert len(enrolled_contacts) == 1
        assert len(failed_enrollments) == 1  # One failure for the contact without email

        # Verify the contact with email was enrolled successfully
        enrolled = enrolled_contacts[0]
        assert enrolled.id == enrollment_id
        assert enrolled.contact_id == contact_id
        assert enrolled.status == SequenceEnrollmentStatus.ACTIVE

        # Verify the contact without email was added to failures
        failed = failed_enrollments[0]
        assert failed.contact_id == contact_without_email_id
        assert set(failed.failure_reasons) == {
            SequenceFailureReason.NO_EMAIL_FOUND_FOR_CONTACT,
        }

        # Verify map_the_most_relevant_contact_emails was called correctly
        contact_resolve_service.map_the_most_relevant_contact_emails.assert_awaited_once_with(
            organization_id=organization_id,
            contact_account_pairs=[
                (contact_id, account_id),
                (contact_without_email_id, account_id),
            ],
        )

        # Verify map_default_pools_by_owner_ids was called
        email_account_pool_service.map_default_pools_by_owner_ids.assert_awaited_once_with(
            organization_id=organization_id,
            owner_user_ids=[user_id],
        )

        # Verify only one enrollment was inserted
        sequence_enrollment_repository.bulk_insert.assert_awaited_once()
        args, kwargs = sequence_enrollment_repository.bulk_insert.call_args
        assert len(kwargs["instances"]) == 1
        instance = kwargs["instances"][0]
        assert instance.contact_id == contact_id
        assert instance.email == email
        assert instance.account_id == account_id

    async def test_patch_sequence_enrollment_by_id(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_repository: AsyncMock,
        contact_query_service: AsyncMock,
    ) -> None:
        user_id = uuid4()
        # Arrange
        sequence_enrollment_id = uuid4()
        organization_id = uuid4()
        contact_id = uuid4()
        email = "<EMAIL>"
        request = PatchSequenceEnrollmentRequest(
            status=SequenceEnrollmentStatus.INACTIVE
        )

        now = zoned_utc_now()

        # Create expected DB record
        expected_db_record = DbSequenceEnrollment(
            id=sequence_enrollment_id,
            organization_id=organization_id,
            sequence_id=uuid4(),
            contact_id=contact_id,
            email=email,
            status=SequenceEnrollmentStatus.INACTIVE,
            enrolled_at=zoned_utc_now(),
            enrolled_by_user_id=user_id,
            updated_at=now,
            updated_by_user_id=user_id,
        )

        # Mock updating the enrollment
        mock_update_sequence_enrollment(
            mock_repo=sequence_enrollment_repository,
            input_organization_id=organization_id,
            input_enrollment_id=sequence_enrollment_id,
            result=expected_db_record,
        )

        # Act
        result = await sequence_enrollment_service.patch_sequence_enrollment_by_id(
            request=request,
            sequence_enrollment_id=sequence_enrollment_id,
            user_auth_context=UserAuthContext(
                user_id=user_id,
                organization_id=organization_id,
            ),
        )

        # Assert
        assert isinstance(result, PatchSequenceEnrollmentResponse)
        assert result.id == sequence_enrollment_id
        assert result.status == expected_db_record.status

        # Verify update repository call
        sequence_enrollment_repository.update_by_tenanted_primary_key.assert_called_once_with(
            table_model=DbSequenceEnrollment,
            primary_key_to_value={"id": sequence_enrollment_id},
            organization_id=organization_id,
            column_to_update={
                "status": SequenceEnrollmentStatus.INACTIVE,
                "updated_at": result.updated_at,
                "updated_by_user_id": user_id,
            },
        )

    async def test_delete_sequence_enrollment_by_id_already_removed(
        self,
        sequence_enrollment_factory: SequenceEnrollmentFactory,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_repository: AsyncMock,
    ) -> None:
        sequence_enrollment_id = uuid4()
        organization_id = uuid4()
        user_id = uuid4()
        existing_enrollment = sequence_enrollment_factory.build(
            id=sequence_enrollment_id,
            organization_id=organization_id,
            sequence_id=uuid4(),
            contact_id=uuid4(),
            email="<EMAIL>",
            deleted_at=zoned_utc_now(),
            exited_at=zoned_utc_now(),
            deleted_by_user_id=user_id,
        )

        self.mock_repo_find_by_tenanted_primary_key(
            mock_repository=sequence_enrollment_repository,
            input_organization_id=organization_id,
            input_lookup_fields={
                "id": sequence_enrollment_id,
                "exclude_deleted": False,
            },
            result=existing_enrollment,
        )

        result = await sequence_enrollment_service.delete_sequence_enrollment_by_id(
            sequence_enrollment_id=sequence_enrollment_id,
            user_auth_context=UserAuthContext(
                user_id=user_id,
                organization_id=organization_id,
            ),
        )

        # Assert
        expected = DeleteSequenceEnrollmentResponse(
            message="Sequence enrollment deleted successfully",
            id=sequence_enrollment_id,
            exited_at=not_none(existing_enrollment.exited_at),
            deleted_at=not_none(existing_enrollment.deleted_at),
            deleted_by_user_id=not_none(existing_enrollment.deleted_by_user_id),
        )
        assert result == expected

    async def test_delete_sequence_enrollment_by_id_no_tasks(
        self,
        task_query_service: AsyncMock,
        sequence_enrollment_factory: SequenceEnrollmentFactory,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_repository: AsyncMock,
    ) -> None:
        sequence_enrollment_id = uuid4()
        organization_id = uuid4()
        user_id = uuid4()
        existing_enrollment = sequence_enrollment_factory.build(
            id=sequence_enrollment_id,
            organization_id=organization_id,
            sequence_id=uuid4(),
            contact_id=uuid4(),
            email="<EMAIL>",
            deleted_at=None,
            current_step_id=None,
        )

        self.mock_repo_find_by_tenanted_primary_key(
            mock_repository=sequence_enrollment_repository,
            input_organization_id=organization_id,
            input_lookup_fields={
                "id": sequence_enrollment_id,
                "exclude_deleted": False,
            },
            result=existing_enrollment,
        )
        updated_enrollment = existing_enrollment.copy(
            update={
                "deleted_at": zoned_utc_now(),
                "updated_at": zoned_utc_now(),
                "exited_at": zoned_utc_now(),
                "deleted_by_user_id": user_id,
            }
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=sequence_enrollment_repository,
            input_organization_id=organization_id,
            where_columns={"id": sequence_enrollment_id},
            update_columns={
                "deleted_by_user_id": user_id,
                "status": SequenceEnrollmentStatus.REMOVED,
                "exited_by_reference_id": None,
                "exited_by_reference_id_type": None,
                "exited_reason": SequenceEnrollmentExitReasonCode.USER_EXITED,
            },
            update_date_fields=["deleted_at", "updated_at", "exited_at"],
            result=updated_enrollment,
        )
        mock_task_lookup(
            mock_task_query_service=task_query_service,
            input_sequence_enrollment_id=sequence_enrollment_id,
            input_organization_id=organization_id,
            result={sequence_enrollment_id: []},
        )

        result = await sequence_enrollment_service.delete_sequence_enrollment_by_id(
            sequence_enrollment_id=sequence_enrollment_id,
            user_auth_context=UserAuthContext(
                user_id=user_id,
                organization_id=organization_id,
            ),
        )

        # Assert
        expected = DeleteSequenceEnrollmentResponse(
            message="Sequence enrollment deleted successfully",
            id=sequence_enrollment_id,
            exited_at=not_none(updated_enrollment.exited_at),
            deleted_at=not_none(updated_enrollment.deleted_at),
            deleted_by_user_id=not_none(updated_enrollment.deleted_by_user_id),
        )
        assert result == expected

    async def test_preview_sequence_enrollments_from_contacts(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        contact_query_service: AsyncMock,
        sequence_query_service: AsyncMock,
        sequence_enrollment_query_service: AsyncMock,
        email_account_pool_service: AsyncMock,
        contact_v2_factory: ContactV2Factory,
        unsubscription_group_service_ext: AsyncMock,
        contact_resolve_service: AsyncMock,
    ) -> None:
        """Test the sequence enrollment preview with the new implementation"""
        # Arrange
        organization_id = uuid4()
        sequence_id = uuid4()
        user_id = uuid4()

        # Create test contacts
        contact1_id = uuid4()
        contact2_id = uuid4()
        contact3_id = uuid4()
        contact4_id = uuid4()  # This will be an unsubscribed contact

        # Generate test account IDs
        account1_id = uuid4()
        account2_id = uuid4()
        account3_id = uuid4()
        account4_id = uuid4()

        contacts = [
            ContactForSequenceEnrollment(
                contact_id=contact1_id,
                email="<EMAIL>",
            ),
            ContactForSequenceEnrollment(
                contact_id=contact2_id,
                email="<EMAIL>",
            ),
            ContactForSequenceEnrollment(
                contact_id=contact3_id,
                email="<EMAIL>",
            ),
            ContactForSequenceEnrollment(
                contact_id=contact4_id,
                email="<EMAIL>",
            ),
        ]

        # Mock contact query service to return contacts with display names and owner_user_id
        contact_query_service.list_contacts_v2.return_value = [
            contact_v2_factory.build(
                id=contact1_id,
                display_name="Contact 1",
                owner_user_id=user_id,
            ),
            contact_v2_factory.build(
                id=contact2_id,
                display_name="Contact 2",
                owner_user_id=user_id,
            ),
            contact_v2_factory.build(
                id=contact3_id,
                display_name="Contact 3",
                owner_user_id=user_id,
            ),
            contact_v2_factory.build(
                id=contact4_id,
                display_name="Unsubscribed Contact",
                owner_user_id=user_id,
            ),
        ]

        contact_resolve_service.map_the_most_relevant_contact_emails.return_value = {
            contact1_id: ("<EMAIL>", account1_id),
            contact2_id: ("<EMAIL>", account2_id),
            contact3_id: ("<EMAIL>", account3_id),
            contact4_id: ("<EMAIL>", account4_id),
        }

        # For resolve_and_fill_sequence_enrollment_v2 to fill in the account_ids
        contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info = (
            AsyncMock(
                return_value={
                    contact1_id: ("<EMAIL>", account1_id),
                    contact2_id: ("<EMAIL>", account2_id),
                    contact3_id: ("<EMAIL>", account3_id),
                    contact4_id: ("<EMAIL>", account4_id),
                }
            )
        )

        # Mock query service methods to return appropriate data for the check method
        # Simulate contact1 being already enrolled in this sequence
        now = zoned_utc_now()
        contact1_enrollment = SequenceEnrollment(
            id=uuid4(),
            organization_id=organization_id,
            sequence_id=sequence_id,
            contact_id=contact1_id,
            email="<EMAIL>",
            status=SequenceEnrollmentStatus.ACTIVE,
            display_status=SequenceEnrollmentDisplayStatus.ENROLLED,
            enrolled_at=now,
            enrolled_by_user_id=uuid4(),
            updated_at=now,
            updated_by_user_id=uuid4(),
        )

        sequence_enrollment_query_service.find_enrollments_by_contacts_for_sequence.return_value = {
            contact1_id: contact1_enrollment
        }

        # Mock get_contacts_enrollments to return enrollments
        # Contact2 is enrolled in other sequence
        other_sequence_id = uuid4()
        sequence_enrollment = SequenceEnrollment(
            id=uuid4(),
            organization_id=organization_id,
            sequence_id=other_sequence_id,
            contact_id=contact2_id,
            email="<EMAIL>",
            status=SequenceEnrollmentStatus.ACTIVE,
            display_status=SequenceEnrollmentDisplayStatus.ENROLLED,
            enrolled_at=now,
            enrolled_by_user_id=uuid4(),
            updated_at=now,
            updated_by_user_id=uuid4(),
        )

        sequence_enrollment_query_service.get_contacts_enrollments.return_value = {
            contact2_id: [sequence_enrollment],
            # contact1_id and contact3_id not in other sequences
        }

        # Mock sequence query service for other sequence name
        other_sequence = type(
            "Sequence", (), {"id": other_sequence_id, "name": "Other Sequence"}
        )
        sequence_query_service.list_sequences.return_value = [other_sequence]
        sequence_query_service.map_sequences_by_id.return_value = {
            other_sequence_id: other_sequence
        }

        # Mock unsubscription group service
        unsub_group_id = uuid4()
        unsubscription_group = UnsubscriptionGroup(
            id=unsub_group_id,
            organization_id=organization_id,
            name="Unsubscribed Group",
            is_default=True,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=None,
        )
        unsubscription_group_service_ext.get_default_unsub_group_for_org.return_value = unsubscription_group

        # Mock the list_unsubscribed_emails method to return the unsubscribed email
        unsubscription_group_service_ext.list_unsubscribed_emails.return_value = [
            "<EMAIL>"
        ]

        # Mock email account pool service to return warm accounts
        pool_id = uuid4()
        email_account_id = uuid4()

        # Mock the map_default_pools_by_owner_ids method
        default_pool = EmailAccountPoolResponse(
            id=pool_id,
            name="Default Pool",
            organization_id=organization_id,
            created_at=str(zoned_utc_now()),
            created_by_user_id=user_id,
        )
        email_account_pool_service.map_default_pools_by_owner_ids.return_value = {
            user_id: default_pool
        }

        # Mock check_mailbox_capacities_for_pools to return empty list (no capacity issues)
        email_account_pool_service.check_mailbox_capacities_for_pools.return_value = {
            pool_id: CheckMailboxCapacityResult(
                over_capacity_email_accounts=[],
                under_capacity_email_accounts=[],
            )
        }

        # Create a mock EmailAccountPoolDto with fully warmed up accounts
        mock_pool_dto = AsyncMock(spec=EmailAccountPoolDto)

        # Add a warmed up email account to the dto
        mock_account_dto = AsyncMock(spec=EmailAccountDto)
        mock_account_dto.is_fully_warm = True  # Set as fully warm

        mock_pool_dto.email_account_dtos = {email_account_id: mock_account_dto}

        mock_pool_dto.email_account_pool = EmailAccountPool(
            id=pool_id,
            name="Default Pool",
            organization_id=organization_id,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            is_default=True,
            owner_user_id=user_id,
        )

        # Set warm email accounts
        mock_pool_dto.get_email_account_ids_fully_warm.return_value = {email_account_id}
        mock_pool_dto.get_email_account_ids_with_use_override.return_value = set()

        email_account_pool_service.get_email_account_pool_dto.return_value = (
            mock_pool_dto
        )

        # Act
        result = await sequence_enrollment_service.preview_sequence_enrollments_from_contacts(
            contacts=contacts,
            sequence_id=sequence_id,
            organization_id=organization_id,
            user_id=user_id,
        )

        # Assert
        assert len(result) == 4

        # Check contact1 (already in this sequence)
        contact1_preview = next(p for p in result if p.contact_id == contact1_id)
        assert contact1_preview.eligibility == SequenceEnrollmentEligibility.INELIGIBLE
        assert contact1_preview.can_enroll is False
        assert "already_enrolled" in contact1_preview.reason.lower()
        assert contact1_preview.display_name == "Contact 1"

        # Check contact2 (in another sequence)
        contact2_preview = next(p for p in result if p.contact_id == contact2_id)
        assert (
            contact2_preview.eligibility
            == SequenceEnrollmentEligibility.ELIGIBLE_WITH_WARNINGS
        )
        assert contact2_preview.can_enroll is True
        assert "other sequence" in contact2_preview.reason.lower()
        assert contact2_preview.display_name == "Contact 2"

        # Check contact3 (fully eligible)
        contact3_preview = next(p for p in result if p.contact_id == contact3_id)
        assert contact3_preview.eligibility == SequenceEnrollmentEligibility.ELIGIBLE
        assert contact3_preview.can_enroll is True
        assert (
            contact3_preview.reason == ""
        )  # No warnings or reasons for fully eligible
        assert contact3_preview.display_name == "Contact 3"

        # Check contact4 (unsubscribed)
        contact4_preview = next(p for p in result if p.contact_id == contact4_id)
        assert (
            contact4_preview.eligibility
            == SequenceEnrollmentEligibility.ELIGIBLE_WITH_WARNINGS
        )
        assert contact4_preview.can_enroll is True
        assert "unsubscribed" in contact4_preview.reason.lower()
        assert contact4_preview.display_name == "Unsubscribed Contact"

        # Verify key service calls
        contact_query_service.list_contacts_v2.assert_awaited_once_with(
            organization_id=organization_id,
            only_include_contact_ids={
                contact1_id,
                contact2_id,
                contact3_id,
                contact4_id,
            },
        )

        # Verify enrollment info was checked
        sequence_enrollment_query_service.find_enrollments_by_contacts_for_sequence.assert_awaited_once_with(
            contact_ids={contact1_id, contact2_id, contact3_id, contact4_id},
            sequence_id=sequence_id,
            organization_id=organization_id,
        )
        sequence_enrollment_query_service.get_contacts_enrollments.assert_awaited_once_with(
            contact_ids={contact1_id, contact2_id, contact3_id, contact4_id},
            organization_id=organization_id,
        )

        # Verify unsubscription group service calls
        unsubscription_group_service_ext.get_default_unsub_group_for_org.assert_awaited_once_with(
            organization_id=organization_id,
        )
        unsubscription_group_service_ext.list_unsubscribed_emails.assert_awaited_once_with(
            emails=[
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
            unsubscription_group_id=unsub_group_id,
            organization_id=organization_id,
        )

        # Verify the pool methods were called
        email_account_pool_service.map_default_pools_by_owner_ids.assert_awaited_once_with(
            organization_id=organization_id,
            owner_user_ids=[user_id],
        )
        email_account_pool_service.get_email_account_pool_dto.assert_awaited()

    async def test_preview_sequence_enrollments_empty_contacts(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_repository: AsyncMock,
        unsubscription_group_service_ext: AsyncMock,
        mocker: MockerFixture,
    ) -> None:
        """Test preview with empty contacts list"""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        sequence_id = uuid4()

        # Create the request with empty contacts list
        request = PreviewSequenceEnrollmentRequest(
            sequence_id=sequence_id,
            contacts=[],
        )

        # Mock the unsubscription group service even though it won't be used for empty contacts
        unsub_group_id = uuid4()
        mock_unsub_group = MagicMock()
        mock_unsub_group.id = unsub_group_id
        unsubscription_group_service_ext.get_default_unsub_group_for_org.return_value = mock_unsub_group
        unsubscription_group_service_ext.list_unsubscribed_emails.return_value = []

        mocker.patch.object(
            sequence_enrollment_service.sequence_permission_service,
            "can_access_sequence_by_ids_for_read",
            return_value={sequence_id: True},
        )

        # Act
        result = await sequence_enrollment_service.preview_sequence_enrollments(
            request=request,
            user_auth_context=UserAuthContext(
                user_id=user_id,
                organization_id=organization_id,
            ),
        )

        # Assert
        assert len(result.preview) == 0
        assert result.sequence_id == sequence_id

        # Verify no repository calls were made for empty contacts list
        sequence_enrollment_repository.find_sequence_enrollments_by_contacts_for_sequence.assert_not_awaited()
        sequence_enrollment_repository.find_sequence_enrollments_by_contacts_and_status.assert_not_awaited()

        # Since contacts list is empty, the unsubscription methods should not be called
        unsubscription_group_service_ext.get_default_unsub_group_for_org.assert_not_awaited()
        unsubscription_group_service_ext.filter_emails_unsubscribed_from_group.assert_not_awaited()

    async def test_preview_sequence_enrollments_no_email_account_pool(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        contact_query_service: AsyncMock,
        sequence_query_service: AsyncMock,
        sequence_enrollment_query_service: AsyncMock,
        email_account_pool_service: AsyncMock,
        contact_v2_factory: ContactV2Factory,
        unsubscription_group_service_ext: AsyncMock,
        contact_resolve_service: AsyncMock,
    ) -> None:
        """Test the sequence enrollment preview when no email account pool is available"""
        # Arrange
        organization_id = uuid4()
        sequence_id = uuid4()
        user_id = uuid4()

        # Create test contacts
        contact_id = uuid4()
        account_id = uuid4()

        contact = ContactForSequenceEnrollment(
            contact_id=contact_id,
            email="<EMAIL>",
        )

        # Mock contact query service to return contact with display name
        contact_v2 = contact_v2_factory.build(
            id=contact_id,
            display_name="Test Contact",
            owner_user_id=user_id,  # Set owner user ID for email account pool lookup
        )
        contact_query_service.list_contacts_v2.return_value = [contact_v2]

        # Mock for resolve_and_fill_sequence_enrollment_v2
        contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info = (
            AsyncMock(
                return_value={
                    contact_id: ("<EMAIL>", account_id),
                }
            )
        )

        # Set up contact to owner mapping
        contact_query_service.get_contact_email_account_associations_by_emails.return_value = {}

        # Mock query service to report no existing enrollments
        sequence_enrollment_query_service.find_enrollments_by_contacts_for_sequence.return_value = {}
        sequence_enrollment_query_service.get_contacts_enrollments.return_value = {}

        # Mock unsubscription group service
        unsubscription_group = UnsubscriptionGroup(
            id=uuid4(),
            organization_id=organization_id,
            name="Unsubscribed Group",
            is_default=True,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=None,
        )
        unsubscription_group_service_ext.get_default_unsub_group_for_org.return_value = unsubscription_group

        # Mock the list_unsubscribed_emails method to return empty list (no unsubscribed emails)
        unsubscription_group_service_ext.list_unsubscribed_emails.return_value = []

        # Mock map_default_pools_by_owner_ids to return empty dict (no pool available)
        email_account_pool_service.map_default_pools_by_owner_ids.return_value = {}

        # Act
        result = await sequence_enrollment_service.preview_sequence_enrollments_from_contacts(
            contacts=[contact],
            sequence_id=sequence_id,
            organization_id=organization_id,
            user_id=user_id,
        )

        # Assert
        assert len(result) == 1
        preview_item = result[0]
        assert preview_item.contact_id == contact_id
        assert preview_item.eligibility == SequenceEnrollmentEligibility.INELIGIBLE
        assert preview_item.can_enroll is False
        assert "no email account pool" in preview_item.reason.lower()

        # Verify email_account_pool_service method was called
        email_account_pool_service.map_default_pools_by_owner_ids.assert_awaited_once_with(
            organization_id=organization_id,
            owner_user_ids=[user_id],
        )

    async def test_create_sequence_enrollment_no_email_account_pool(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_repository: AsyncMock,
        contact_query_service: AsyncMock,
        account_query_service: AsyncMock,
        contact_v2_factory: ContactV2Factory,
        account_v2_factory: AccountV2Factory,
        email_account_pool_service: AsyncMock,
        sequence_enrollment_query_service: AsyncMock,
        contact_resolve_service: AsyncMock,
    ) -> None:
        """Test creation of sequence enrollment when no email account pool is available"""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        contact_id = uuid4()
        account_id = uuid4()
        sequence_id = uuid4()

        # Create a test contact
        contact = ContactForSequenceEnrollment(
            contact_id=contact_id,
            account_id=account_id,
            email="<EMAIL>",
        )

        # Mock contact query service
        contact_v2 = contact_v2_factory.build(
            id=contact_id,
            contact_account_associations=[
                ContactAccountAssociationLite(
                    contact_id=contact_id,
                    account_id=account_id,
                    is_primary=True,
                ),
            ],
            owner_user_id=user_id,
        )
        contact_query_service.list_contacts_v2.return_value = [contact_v2]

        # Mock account query service
        account_query_service.list_accounts_v2.return_value = [
            account_v2_factory.build(id=account_id)
        ]

        # Mock sequence enrollment query service
        sequence_enrollment_query_service.find_enrollments_by_contacts_for_sequence.return_value = {}

        # Mock necessary contact resolution
        contact_resolve_service.map_the_most_relevant_contact_emails.return_value = {
            contact_id: ("<EMAIL>", account_id)
        }

        # Configure email_account_pool_service to return empty result (no pool available)
        # Mock map_default_pools_by_owner_ids to return empty map
        email_account_pool_service.map_default_pools_by_owner_ids.return_value = {}

        sequence_enrollment_repository.find_count_of_sequence_enrollments_by_sequence_id_and_status.return_value = 0

        # Act
        (
            enrolled_contacts,
            failed_enrollments,
        ) = await sequence_enrollment_service.create_sequence_enrollment_from_contacts(
            contacts=[contact],
            sequence_id=sequence_id,
            organization_id=organization_id,
            user_id=user_id,
        )

        # Assert
        assert len(enrolled_contacts) == 0
        assert len(failed_enrollments) == 1

        # Verify the failure reason
        failed = failed_enrollments[0]
        assert failed.contact_id == contact_id
        assert (
            SequenceFailureReason.EMAIL_ACCOUNT_NOT_AVAILABLE in failed.failure_reasons
        )

        # Should not attempt to create any enrollments
        sequence_enrollment_repository.bulk_insert.assert_not_called()

        # Verify email_account_pool_service method was called
        email_account_pool_service.map_default_pools_by_owner_ids.assert_awaited_once_with(
            organization_id=organization_id,
            owner_user_ids=[user_id],
        )

    async def test_proceed_sequence_enrollment_success(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_repository: AsyncMock,
        sequence_step_repository: AsyncMock,
    ) -> None:
        # Arrange
        enrollment_id = uuid4()
        user_id = uuid4()
        organization_id = uuid4()
        workflow_id = "test-workflow-id"
        current_step_id = uuid4()

        # Mock enrollment retrieval
        mock_enrollment = MagicMock(
            id=enrollment_id,
            workflow_id=workflow_id,
            current_step_id=current_step_id,
            status=SequenceEnrollmentStatus.ACTIVE,
        )
        sequence_enrollment_repository.find_by_tenanted_primary_key.return_value = (
            mock_enrollment
        )

        # Mock step execution retrieval
        mock_step_execution = MagicMock(
            status=SequenceStepExecutionStatus.TASK_COMPLETED_WITH_PAUSED
        )
        # Add the method to the mock's spec
        sequence_step_repository.find_execution_by_step_id_and_enrollment_id = (
            AsyncMock(return_value=mock_step_execution)
        )

        # Mock temporal client
        with patch(
            "salestech_be.core.sequence.service.sequence_enrollment_service.get_temporal_client"
        ) as mock_get_client:
            mock_client = MagicMock()
            mock_workflow_handle = MagicMock()
            mock_workflow_handle.signal = AsyncMock()
            mock_client.get_workflow_handle.return_value = mock_workflow_handle
            mock_get_client.return_value = mock_client

            # Act
            result = await sequence_enrollment_service.proceed_sequence_enrollment(
                enrollment_id=enrollment_id,
                user_auth_context=UserAuthContext(
                    user_id=user_id,
                    organization_id=organization_id,
                ),
            )

            # Assert
            sequence_enrollment_repository.find_by_tenanted_primary_key.assert_awaited_once_with(
                table_model=DbSequenceEnrollment,
                organization_id=organization_id,
                id=enrollment_id,
            )

            sequence_step_repository.find_execution_by_step_id_and_enrollment_id.assert_awaited_once_with(
                sequence_step_id=current_step_id,
                sequence_enrollment_id=enrollment_id,
                organization_id=organization_id,
            )

            mock_client.get_workflow_handle.assert_called_once_with(workflow_id)
            mock_workflow_handle.signal.assert_awaited_once_with(
                SequenceEnrollmentWorkflow.resume
            )

            assert result.id == enrollment_id
            assert result.status == mock_enrollment.status

    async def test_proceed_sequence_enrollment_missing_enrollment(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_repository: AsyncMock,
    ) -> None:
        # Arrange
        enrollment_id = uuid4()
        user_id = uuid4()
        organization_id = uuid4()

        # Mock enrollment not found
        sequence_enrollment_repository.find_by_tenanted_primary_key.return_value = None

        # Act & Assert
        with pytest.raises(ResourceNotFoundError) as exc_info:
            await sequence_enrollment_service.proceed_sequence_enrollment(
                enrollment_id=enrollment_id,
                user_auth_context=UserAuthContext(
                    user_id=user_id,
                    organization_id=organization_id,
                ),
            )

        assert (
            str(exc_info.value)
            == f"Sequence enrollment {enrollment_id} not found or missing required data"
        )

    async def test_proceed_sequence_enrollment_missing_workflow_id(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_repository: AsyncMock,
    ) -> None:
        # Arrange
        enrollment_id = uuid4()
        organization_id = uuid4()
        user_id = uuid4()
        current_step_id = uuid4()

        # Mock enrollment with missing workflow_id
        mock_enrollment = MagicMock(
            id=enrollment_id,
            workflow_id=None,  # Missing workflow_id
            current_step_id=current_step_id,
            status=SequenceEnrollmentStatus.ACTIVE,
        )
        sequence_enrollment_repository.find_by_tenanted_primary_key.return_value = (
            mock_enrollment
        )

        # Act & Assert
        with pytest.raises(ResourceNotFoundError) as exc_info:
            await sequence_enrollment_service.proceed_sequence_enrollment(
                enrollment_id=enrollment_id,
                user_auth_context=UserAuthContext(
                    user_id=user_id,
                    organization_id=organization_id,
                ),
            )

        assert (
            str(exc_info.value)
            == f"Sequence enrollment {enrollment_id} not found or missing required data"
        )

    async def test_proceed_sequence_enrollment_invalid_step_status(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_repository: AsyncMock,
        sequence_step_repository: AsyncMock,
    ) -> None:
        # Arrange
        enrollment_id = uuid4()
        user_id = uuid4()
        organization_id = uuid4()
        workflow_id = "test-workflow-id"
        current_step_id = uuid4()

        # Mock enrollment retrieval
        mock_enrollment = MagicMock(
            id=enrollment_id,
            workflow_id=workflow_id,
            current_step_id=current_step_id,
            status=SequenceEnrollmentStatus.ACTIVE,
        )
        sequence_enrollment_repository.find_by_tenanted_primary_key.return_value = (
            mock_enrollment
        )

        # Mock step execution with wrong status
        mock_step_execution = MagicMock(
            status=SequenceStepExecutionStatus.QUEUED  # Wrong status
        )
        # Add the method to the mock's spec
        sequence_step_repository.find_execution_by_step_id_and_enrollment_id = (
            AsyncMock(return_value=mock_step_execution)
        )

        # Act & Assert
        with pytest.raises(InvalidArgumentError) as exc_info:
            await sequence_enrollment_service.proceed_sequence_enrollment(
                enrollment_id=enrollment_id,
                user_auth_context=UserAuthContext(
                    user_id=user_id,
                    organization_id=organization_id,
                ),
            )

        assert (
            str(exc_info.value)
            == "Cannot proceed enrollment - current step is not in TASK_COMPLETED_WITH_PAUSED status"
        )

    async def test_proceed_sequence_enrollment_missing_step_execution(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        sequence_enrollment_repository: AsyncMock,
        sequence_step_repository: AsyncMock,
    ) -> None:
        # Arrange
        enrollment_id = uuid4()
        user_id = uuid4()
        organization_id = uuid4()
        workflow_id = "test-workflow-id"
        current_step_id = uuid4()

        # Mock enrollment retrieval
        mock_enrollment = MagicMock(
            id=enrollment_id,
            workflow_id=workflow_id,
            current_step_id=current_step_id,
            status=SequenceEnrollmentStatus.ACTIVE,
        )
        sequence_enrollment_repository.find_by_tenanted_primary_key.return_value = (
            mock_enrollment
        )

        # Mock missing step execution
        # Add the method to the mock's spec
        sequence_step_repository.find_execution_by_step_id_and_enrollment_id = (
            AsyncMock(return_value=None)
        )

        # Act & Assert
        with pytest.raises(InvalidArgumentError) as exc_info:
            await sequence_enrollment_service.proceed_sequence_enrollment(
                enrollment_id=enrollment_id,
                user_auth_context=UserAuthContext(
                    user_id=user_id,
                    organization_id=organization_id,
                ),
            )

        assert (
            str(exc_info.value)
            == "Cannot proceed enrollment - current step is not in TASK_COMPLETED_WITH_PAUSED status"
        )

    async def test_resolve_and_fill_sequence_enrollment(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        contact_resolve_service: AsyncMock,
    ) -> None:
        """Test resolving and filling sequence enrollment with contact and account info"""
        # Arrange
        organization_id = uuid4()
        contact_id = uuid4()
        account_id = uuid4()
        email = "<EMAIL>"

        # Case 1: contact_id + email
        enrollment_no_account = ContactForSequenceEnrollment(
            contact_id=contact_id,
            email=email,
            account_id=None,
        )

        # Mock contact resolve service
        contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info.return_value = {
            contact_id: (None, account_id)
        }

        # Act & Assert Case 1
        await sequence_enrollment_service.resolve_and_fill_sequence_enrollment(
            organization_id=organization_id, contacts=[enrollment_no_account]
        )

        assert enrollment_no_account.account_id == account_id

        # Case 2: Both contact_id and account_id exist
        enrollment_with_all_fields = ContactForSequenceEnrollment(
            contact_id=contact_id,
            email=email,
            account_id=account_id,  # Account ID already set
        )

        # Reset mock
        contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info.reset_mock()

        # Act & Assert Case 2
        await sequence_enrollment_service.resolve_and_fill_sequence_enrollment(
            organization_id=organization_id, contacts=[enrollment_with_all_fields]
        )

        # Verify account_id remains unchanged and no resolution was attempted
        assert enrollment_with_all_fields.account_id == account_id
        assert enrollment_with_all_fields.email == email

        # Case 3: Email is None
        enrollment_no_email_and_account = ContactForSequenceEnrollment(
            contact_id=contact_id,
            email=None,  # Email is None
            account_id=None,
        )

        contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info.return_value = {
            contact_id: (email, account_id)
        }

        # Act & Assert Case 3
        await sequence_enrollment_service.resolve_and_fill_sequence_enrollment(
            organization_id=organization_id, contacts=[enrollment_no_email_and_account]
        )

        # Verify no resolution was attempted when email is None
        assert enrollment_no_email_and_account.account_id == account_id
        assert enrollment_no_email_and_account.email == email

        # Case 4: Email is None but account_id exists
        enrollment_no_email = ContactForSequenceEnrollment(
            contact_id=contact_id,
            email=None,  # Email is None
            account_id=account_id,  # Account ID is set
        )

        contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info.return_value = {
            contact_id: (email, account_id)
        }

        # Act & Assert Case 4
        await sequence_enrollment_service.resolve_and_fill_sequence_enrollment(
            organization_id=organization_id, contacts=[enrollment_no_email]
        )

        # Verify account_id remains unchanged and no resolution was attempted
        assert enrollment_no_email.email == email
        assert enrollment_no_email.account_id == account_id

    async def test_resolve_and_fill_sequence_enrollment_v2(
        self,
        sequence_enrollment_service: SequenceEnrollmentService,
        contact_resolve_service: AsyncMock,
    ) -> None:
        """Test the two-step resolution process in resolve_and_fill_sequence_enrollment_v2 method"""
        # Arrange
        organization_id = uuid4()

        # Create test contacts with different data combinations
        contact1_id = uuid4()  # No email, no account
        contact2_id = uuid4()  # Has email, no account
        contact3_id = uuid4()  # No email, has account
        contact4_id = uuid4()  # Has both email and account (should be unchanged)

        account1_id = uuid4()
        account2_id = uuid4()
        account3_id = uuid4()
        account4_id = uuid4()

        email1 = "<EMAIL>"
        email2 = "<EMAIL>"
        email3 = "<EMAIL>"
        email4 = "<EMAIL>"

        contacts = [
            # Contact 1: No email, no account
            ContactForSequenceEnrollment(
                contact_id=contact1_id,
                email=None,
                account_id=None,
            ),
            # Contact 2: Has email, no account
            ContactForSequenceEnrollment(
                contact_id=contact2_id,
                email=email2,
                account_id=None,
            ),
            # Contact 3: No email, has account
            ContactForSequenceEnrollment(
                contact_id=contact3_id,
                email=None,
                account_id=account3_id,
            ),
            # Contact 4: Has both email and account
            ContactForSequenceEnrollment(
                contact_id=contact4_id,
                email=email4,
                account_id=account4_id,
            ),
        ]

        # Set up first mock response - resolving emails for contacts without email
        first_mock_response = {
            contact1_id: (email1, None),  # Only resolve email for contact1
            contact3_id: (email3, None),  # Only resolve email for contact3
        }

        # Set up second mock response - resolving accounts for contacts with email but no account
        second_mock_response = {
            contact1_id: (
                None,
                account1_id,
            ),  # Resolve account for contact1 (now has email from first step)
            contact2_id: (
                None,
                account2_id,
            ),  # Resolve account for contact2 (already had email)
        }

        # Configure mock to return different values on consecutive calls
        contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info.side_effect = [
            first_mock_response,
            second_mock_response,
        ]

        # Act
        await sequence_enrollment_service.resolve_and_fill_sequence_enrollment_v2(
            organization_id=organization_id, contacts=contacts
        )

        # Assert
        # Contact 1: Should have both email and account resolved
        assert contacts[0].email == email1
        assert contacts[0].account_id == account1_id

        # Contact 2: Should have account resolved, email unchanged
        assert contacts[1].email == email2  # Original value preserved
        assert contacts[1].account_id == account2_id

        # Contact 3: Should have email resolved, account unchanged
        assert contacts[2].email == email3
        assert contacts[2].account_id == account3_id  # Original value preserved

        # Contact 4: Should be completely unchanged
        assert contacts[3].email == email4  # Original value preserved
        assert contacts[3].account_id == account4_id  # Original value preserved

        # Verify both resolution calls were made with appropriate parameters
        assert (
            contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info.call_count
            == 2
        )

        # First call should pass contacts without email (contact1 and contact3)
        first_call_args = contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info.call_args_list[
            0
        ][1]
        assert first_call_args["organization_id"] == organization_id
        assert set(
            first_call_args["resolve_contact_infos"].contact_account_map.keys()
        ) == {contact1_id, contact3_id}

        # Second call should pass contacts with email but no account (contact1 with newly resolved email and contact2)
        second_call_args = contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info.call_args_list[
            1
        ][1]
        assert second_call_args["organization_id"] == organization_id
        assert set(
            second_call_args["resolve_contact_infos"].contact_email_map.keys()
        ) == {contact1_id, contact2_id}


@pytest.fixture
def sequence_enrollment_repository() -> AsyncMock:
    return AsyncMock(spec=SequenceEnrollmentRepository)


@pytest.fixture
def sequence_execution_repository() -> AsyncMock:
    return AsyncMock(spec=SequenceExecutionRepository)


@pytest.fixture
def email_account_pool_service() -> AsyncMock:
    mock = AsyncMock(spec=EmailAccountPoolService)
    pool_id = uuid4()

    # Create a response object with proper attributes
    mock.get_default_email_account_pool_with_members.return_value = (
        EmailAccountPoolMembershipResponse(
            email_account_pool=EmailAccountPoolResponse(
                id=pool_id,
                name="Default Pool",
                organization_id=uuid4(),
                created_at=str(zoned_utc_now()),
                created_by_user_id=uuid4(),
            ),
            memberships=[],
        )
    )

    # For backwards compatibility
    mock.get_default_email_account_pool.return_value = EmailAccountPoolResponse(
        id=pool_id,
        name="Default Pool",
        organization_id=uuid4(),
        created_at=str(zoned_utc_now()),
        created_by_user_id=uuid4(),
    )

    # Mock the map_default_pools_by_owner_ids method (default empty - tests will override this)
    mock.map_default_pools_by_owner_ids.return_value = {}

    # Create a standard mock EmailAccountPoolDto that tests can customize
    mock_pool_dto = AsyncMock(spec=EmailAccountPoolDto)
    mock_pool_dto.email_account_dtos = {}
    mock_pool_dto.email_account_pool = EmailAccountPool(
        id=pool_id,
        name="Default Pool",
        organization_id=uuid4(),
        created_at=zoned_utc_now(),
        created_by_user_id=uuid4(),
        is_default=True,
        owner_user_id=uuid4(),
    )
    # By default, no warmed accounts or overrides
    mock_pool_dto.get_email_account_ids_fully_warm.return_value = set()
    mock_pool_dto.get_email_account_ids_with_use_override.return_value = set()

    mock.get_email_account_pool_dto.return_value = mock_pool_dto

    return mock


@pytest.fixture
def domain_object_list_query_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def contact_query_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def account_query_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def sequence_query_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def message_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def sequence_enrollment_query_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def contact_resolve_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def email_account_service_v2() -> AsyncMock:
    mock = AsyncMock(spec=EmailAccountServiceV2)

    # Mock the map_warmup_status_by_email_account_ids method to return an empty dict
    mock.map_warmup_status_by_email_account_ids.return_value = {}

    return mock


@pytest.fixture
def domain_crm_association_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def sequence_step_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def sequence_permission_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def task_query_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def task_v2_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def unsubscription_group_service_ext() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def notification_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def thread_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def sequence_enrollment_service(
    sequence_permission_service: AsyncMock,
    sequence_enrollment_repository: AsyncMock,
    sequence_execution_repository: AsyncMock,
    domain_object_list_query_service: AsyncMock,
    contact_query_service: AsyncMock,
    contact_resolve_service: AsyncMock,
    account_query_service: AsyncMock,
    sequence_query_service: AsyncMock,
    email_account_pool_service: AsyncMock,
    email_account_service_v2: AsyncMock,
    message_service: AsyncMock,
    sequence_enrollment_query_service: AsyncMock,
    sequence_step_repository: AsyncMock,
    task_query_service: AsyncMock,
    task_v2_service: AsyncMock,
    thread_repository: AsyncMock,
    unsubscription_group_service_ext: AsyncMock,
    domain_crm_association_service: AsyncMock,
    notification_service: AsyncMock,
) -> SequenceEnrollmentService:
    return SequenceEnrollmentService(
        sequence_permission_service=sequence_permission_service,
        sequence_enrollment_repository=sequence_enrollment_repository,
        sequence_execution_repository=sequence_execution_repository,
        domain_object_list_query_service=domain_object_list_query_service,
        contact_query_service=contact_query_service,
        contact_resolve_service=contact_resolve_service,
        account_query_service=account_query_service,
        sequence_query_service=sequence_query_service,
        email_account_pool_service=email_account_pool_service,
        email_account_service_v2=email_account_service_v2,
        message_service=message_service,
        sequence_enrollment_query_service=sequence_enrollment_query_service,
        sequence_step_repository=sequence_step_repository,
        task_query_service=task_query_service,
        task_v2_service=task_v2_service,
        thread_repository=thread_repository,
        unsubscription_group_service_ext=unsubscription_group_service_ext,
        domain_crm_association_service=domain_crm_association_service,
        notification_service=notification_service,
    )


def mock_find_sequence_enrollment_by_sequence_id_and_contact_id_and_organization_id(
    mock_repo: AsyncMock,
    input_sequence_id: UUID,
    input_contact_id: UUID,
    input_organization_id: UUID,
    result: DbSequenceEnrollment | None,
) -> None:
    """Mock the find_sequence_enrollment_by_sequence_id_and_contact_id_and_organization_id method.

    This function sets up a mock implementation for finding a sequence enrollment by its
    sequence ID, contact ID, and organization ID, returning the specified result.

    Args:
        mock_repo: The mock repository to set up
        input_sequence_id: Expected sequence ID to be matched
        input_contact_id: Expected contact ID to be matched
        input_organization_id: Expected organization ID to be matched
        result: The DbSequenceEnrollment object to return or None if not found
    """

    def find(
        sequence_id: UUID,
        contact_id: UUID,
        organization_id: UUID,
    ) -> DbSequenceEnrollment | None:
        assert sequence_id == input_sequence_id, (
            f"Expected sequence_id {input_sequence_id}, got {sequence_id}"
        )
        assert contact_id == input_contact_id, (
            f"Expected contact_id {input_contact_id}, got {contact_id}"
        )
        assert organization_id == input_organization_id, (
            f"Expected organization_id {input_organization_id}, got {organization_id}"
        )
        return result

    mock_repo.find_sequence_enrollment_by_sequence_id_and_contact_id_and_organization_id = AsyncMock(
        side_effect=find
    )


def mock_insert_sequence_enrollment(
    mock_repo: AsyncMock,
    result: DbSequenceEnrollment,
) -> None:
    """Mock the insert method of a repository.

    This function sets up a mock implementation for inserting a sequence enrollment
    and returning the specified result.

    Args:
        mock_repo: The mock repository to set up
        result: The DbSequenceEnrollment object to return from the insert operation
    """

    def insert(
        table_model: type[DbSequenceEnrollment], instance: DbSequenceEnrollment
    ) -> DbSequenceEnrollment:
        assert table_model == DbSequenceEnrollment, (
            f"Expected table model DbSequenceEnrollment, got {table_model}"
        )
        return result

    mock_repo.insert = AsyncMock(side_effect=insert)


def mock_update_sequence_enrollment(
    mock_repo: AsyncMock,
    input_organization_id: UUID,
    input_enrollment_id: UUID,
    result: DbSequenceEnrollment | None,
) -> None:
    """Mock the update_by_tenanted_primary_key method of a repository.

    This function sets up a mock implementation for updating a sequence enrollment
    that validates the input parameters match the expected values and returns
    the specified result.

    Args:
        mock_repo: The mock repository to set up
        input_organization_id: Expected organization ID to be passed to the update method
        input_enrollment_id: Expected enrollment ID to be used in the primary key lookup
        result: The DbSequenceEnrollment object to return from the update
    """

    def update(
        table_model: type[DbSequenceEnrollment],
        primary_key_to_value: dict[str, UUID],
        organization_id: UUID,
        column_to_update: dict[str, Any],
    ) -> DbSequenceEnrollment | None:
        assert organization_id == input_organization_id, (
            f"Expected organization_id {input_organization_id}, got {organization_id}"
        )
        assert primary_key_to_value.get("id") == input_enrollment_id, (
            f"Expected enrollment_id {input_enrollment_id}, got {primary_key_to_value.get('id')}"
        )
        assert table_model == DbSequenceEnrollment, (
            f"Expected table model DbSequenceEnrollment, got {table_model}"
        )
        return result

    # Use AsyncMock with side_effect so we can check the arguments
    mock_repo.update_by_tenanted_primary_key = AsyncMock(side_effect=update)


def mock_task_lookup(
    mock_task_query_service: AsyncMock,
    input_sequence_enrollment_id: UUID,
    input_organization_id: UUID,
    result: dict[UUID, list[TaskV2]],
) -> None:
    """Mock the get_tasks_by_reference_ids_and_type method of a task query service.

    This function sets up a mock implementation for looking up tasks by reference IDs
    that validates the input parameters match the expected values and returns
    the specified result.

    Args:
        mock_task_query_service: The mock task query service to set up
        input_sequence_enrollment_id: Expected sequence enrollment ID to be queried
        input_organization_id: Expected organization ID to be passed to the method
        result: Dictionary mapping enrollment IDs to lists of tasks to return
    """

    async def lookup(
        reference_ids: list[UUID],
        reference_id_type: TaskReferenceIdType,
        organization_id: UUID,
        status_in: list[TaskStatus] | None = None,
    ) -> dict[UUID, list[TaskV2]]:
        assert reference_ids == [input_sequence_enrollment_id], (
            f"Expected reference_ids [{input_sequence_enrollment_id}], got {reference_ids}"
        )
        assert reference_id_type == TaskReferenceIdType.SEQUENCE_ENROLLMENT_ID, (
            f"Expected reference_id_type SEQUENCE_ENROLLMENT_ID, got {reference_id_type}"
        )
        assert organization_id == input_organization_id, (
            f"Expected organization_id {input_organization_id}, got {organization_id}"
        )
        assert status_in == [
            TaskStatus.OPEN,
            TaskStatus.IN_PROGRESS,
            TaskStatus.BLOCKED,
        ], f"Expected status_in [OPEN, IN_PROGRESS, BLOCKED], got {status_in}"
        return result

    mock_task_query_service.get_tasks_by_reference_ids_and_type = AsyncMock(
        side_effect=lookup
    )
