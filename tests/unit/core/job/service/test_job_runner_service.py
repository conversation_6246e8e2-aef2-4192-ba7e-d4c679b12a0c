import uuid
from collections.abc import Generator
from unittest import mock
from unittest.mock import AsyncMock, <PERSON>ck

import pytest

from salestech_be.core.job.models import JobStatus, JobType
from salestech_be.core.job.service.job_runner_service import JobRunnerService
from salestech_be.db.models.job import Job
from salestech_be.util.time import zoned_utc_now


@pytest.fixture
def mock_metrics() -> Generator[Mock, None, None]:
    with mock.patch(
        "salestech_be.core.job.service.job_runner_service.custom_metric"
    ) as mock_metrics:
        yield mock_metrics


class TestJobRunnerService:
    mocked_job_repository = AsyncMock()
    mocked_job_runner_service = JobRunnerService(
        job_repository=mocked_job_repository,
        crm_sync_service=AsyncMock(),
        prompt_execution_service=AsyncMock(),
        export_service=AsyncMock(),
    )

    job = Job(
        id=uuid.uuid4(),
        type=JobType.ACCOUNT_CSV_UPLOAD,
        status=JobStatus.CONFLICT,
        metadata={},
        user_id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )

    async def test_job_runner_service_with_job_not_found(
        self, mock_metrics: Mock
    ) -> None:
        self.mocked_job_repository.find_by_primary_key.return_value = None

        await self.mocked_job_runner_service.run_job(job_id=uuid.uuid4())

        mock_metrics.increment.assert_called_once_with(
            metric_name="job_status",
            tags=["job_type:unknown", "succeeded:False", "error_code:job_not_found"],
        )
