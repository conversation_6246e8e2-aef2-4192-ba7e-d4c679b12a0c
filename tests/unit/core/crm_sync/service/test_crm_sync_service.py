import os
import uuid
from datetime import datetime
from unittest.mock import As<PERSON><PERSON>ock, MagicMock, patch

import pytest
import pytz

from salestech_be.common.exception.exception import ImportEntityDependencyNotFoundError
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.common import (
    StandardObjectIdentifier,
)
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.imports.models.import_csv_fields import (
    AccountImportCsvFields,
    ContactImportCsvFields,
)
from salestech_be.core.imports.models.import_job import (
    AssociationLabelMapping,
    ColumnMapping,
    FileMetadata,
    ImportCsvJobStatus,
    ImportJob,
    ObjectMapping,
    QualifiedImportField,
)
from salestech_be.core.imports.service.crm_sync_service import CrmSyncService
from salestech_be.core.job.models import JobStatus, JobType
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.db.models.job import Job
from salestech_be.ree_logging import get_logger
from tests.util.factories import AccountFactory, AccountV2Factory
from tests.util.service_test import ServiceTest

logger = get_logger()
_my_dir = os.path.dirname(os.path.abspath(__file__))


def generate_setup_job(job_type: JobType) -> Job:
    return Job(
        id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        user_id=uuid.uuid4(),
        type=job_type,
        status=JobStatus.ENQUEUED,
        created_at=datetime.now(pytz.UTC),
        updated_at=datetime.now(pytz.UTC),
        metadata={},
    )


class TestCrmSyncService(ServiceTest):
    async def test_process_crm_sync_job_account_sync(
        self, crm_sync_service: CrmSyncService
    ) -> None:
        job = generate_setup_job(JobType.HUBSPOT_ACCOUNT_SYNC)

        with (
            patch.object(
                crm_sync_service.hubspot_service,
                "get_or_refresh_hubspot_access_token",
                AsyncMock(return_value="fake_token"),
            ) as mock_get_token,
            patch.object(
                crm_sync_service.hubspot_service.hubspot_client,
                "list_companies",
                AsyncMock(return_value=MagicMock(data=[], next_cursor=None)),
            ) as mock_list_companies,
        ):
            await crm_sync_service.process_hubspot_crm_sync_job(job)

            mock_get_token.assert_called_once_with(organization_id=job.organization_id)
            mock_list_companies.assert_called_once()

    async def test_process_crm_sync_job_user_sync(
        self,
        crm_sync_service: CrmSyncService,
    ) -> None:
        job = generate_setup_job(JobType.HUBSPOT_USER_SYNC)

        with (
            patch.object(
                crm_sync_service.hubspot_service,
                "get_or_refresh_hubspot_access_token",
                AsyncMock(return_value="fake_token"),
            ) as mock_get_token,
            patch.object(
                crm_sync_service.hubspot_service.hubspot_client,
                "list_users",
                AsyncMock(return_value=MagicMock(data=[], next_cursor=None)),
            ) as mock_list_users,
        ):
            await crm_sync_service.process_hubspot_crm_sync_job(job)

            mock_get_token.assert_called_once_with(organization_id=job.organization_id)
            mock_list_users.assert_called_once()

    async def test_find_or_create_account_no_company_or_domain_name(
        self, crm_sync_service: CrmSyncService
    ) -> None:
        organization_id = uuid.uuid4()
        user_id = uuid.uuid4()
        account_import_csv_fields = AccountImportCsvFields(
            company_display_name=None,
            company_official_website=None,
            company_domain_name=None,
            company_description=None,
            company_industry=None,
            company_technologies=None,
            company_estimated_annual_revenue=None,
            company_estimated_employee_count=None,
            company_linkedin_url=None,
            company_facebook_url=None,
            company_x_url=None,
            company_owner_email=None,
            company_created_at=None,
            company_status=None,
        )
        with pytest.raises(ImportEntityDependencyNotFoundError):
            await crm_sync_service.find_or_create_account(
                account_import_csv_fields=account_import_csv_fields,
                organization_id=organization_id,
                user_id=user_id,
                local_timezone=None,
                owner_user_id=None,
            )

    async def test_find_or_create_account_already_exists_by_domain(
        self,
        account_factory: AccountFactory,
        account_v2_factory: AccountV2Factory,
        account_service: AsyncMock,
        crm_sync_service: CrmSyncService,
    ) -> None:
        organization_id = uuid.uuid4()
        user_id = uuid.uuid4()
        account_import_csv_fields = AccountImportCsvFields(
            company_display_name="niccage.com",
            company_domain_name="niccage.com",
            company_official_website=None,
            company_description=None,
            company_industry=None,
            company_technologies=None,
            company_estimated_annual_revenue=None,
            company_estimated_employee_count=None,
            company_linkedin_url=None,
            company_facebook_url=None,
            company_x_url=None,
            company_owner_email=None,
            company_created_at=None,
            company_status=None,
        )
        existing_account_record = account_factory.build()
        self.mock_repo_find_by_column_values(
            mock_repository=account_service.account_repository,
            input_lookup_fields={
                "organization_id": organization_id,
                "exclude_deleted_or_archived": False,
                "domain_name": "niccage.com",
            },
            result=[existing_account_record],
        )

        expected_result = account_v2_factory.build()
        mock_account_service_get_account_v2(
            account_service=account_service,
            input_account_id=existing_account_record.id,
            input_organization_id=organization_id,
            result=expected_result,
        )
        result, _ = await crm_sync_service.find_or_create_account(
            account_import_csv_fields=account_import_csv_fields,
            organization_id=organization_id,
            user_id=user_id,
            local_timezone=None,
            owner_user_id=None,
        )
        assert result == expected_result

    async def test_find_or_create_account_new_record(
        self,
        account_v2_factory: AccountV2Factory,
        account_service: AsyncMock,
        crm_sync_service: CrmSyncService,
    ) -> None:
        organization_id = uuid.uuid4()
        user_id = uuid.uuid4()
        account_import_csv_fields = AccountImportCsvFields(
            company_display_name="niccage.com",
            company_domain_name="niccage.com",
            company_official_website=None,
            company_description=None,
            company_industry=None,
            company_technologies=None,
            company_estimated_annual_revenue=None,
            company_estimated_employee_count=None,
            company_linkedin_url=None,
            company_facebook_url=None,
            company_x_url=None,
            company_owner_email=None,
            company_created_at=None,
            company_status=None,
        )
        self.mock_repo_find_by_column_values_sequence(
            mock_repository=account_service.account_repository,
            input_lookup_fields_sequence=[
                {
                    "organization_id": organization_id,
                    "domain_name": "niccage.com",
                    "exclude_deleted_or_archived": False,
                },
                {"organization_id": organization_id, "display_name": "niccage.com"},
            ],
            result_sequence=[[], []],
        )

        expected_result = account_v2_factory.build()
        mock_account_service_create_account_v2(
            account_service=account_service,
            input_organization_id=organization_id,
            input_user_id=user_id,
            input_create_account_request=CreateAccountRequest(
                display_name="niccage.com",
                owner_user_id=user_id,
                official_website="niccage.com",
                domain_name="niccage.com",
                created_source=CreatedSource.CSV_IMPORT,
            ),
            result=expected_result,
        )
        result, _ = await crm_sync_service.find_or_create_account(
            account_import_csv_fields=account_import_csv_fields,
            organization_id=organization_id,
            user_id=user_id,
            local_timezone=None,
            owner_user_id=None,
        )
        assert result == expected_result

    async def test_find_or_create_account_new_record_with_official_website(
        self,
        account_v2_factory: AccountV2Factory,
        account_service: AsyncMock,
        crm_sync_service: CrmSyncService,
    ) -> None:
        organization_id = uuid.uuid4()
        user_id = uuid.uuid4()
        company_name = str(uuid.uuid4())
        account_import_csv_fields = AccountImportCsvFields(
            company_display_name=company_name,
            company_domain_name=f"{company_name}.com",
            company_official_website=f"{company_name}.com",
            company_description=None,
            company_industry=None,
            company_technologies=None,
            company_estimated_annual_revenue=None,
            company_estimated_employee_count=None,
            company_linkedin_url=None,
            company_facebook_url=None,
            company_x_url=None,
            company_owner_email=None,
            company_created_at=None,
            company_status=None,
        )
        self.mock_repo_find_by_column_values_sequence(
            mock_repository=account_service.account_repository,
            input_lookup_fields_sequence=[
                {
                    "organization_id": organization_id,
                    "domain_name": f"{company_name}.com",
                    "exclude_deleted_or_archived": False,
                },
                {"organization_id": organization_id, "display_name": company_name},
            ],
            result_sequence=[[], []],
        )

        expected_result = account_v2_factory.build()
        mock_account_service_create_account_v2(
            account_service=account_service,
            input_organization_id=organization_id,
            input_user_id=user_id,
            input_create_account_request=CreateAccountRequest(
                display_name=company_name,
                owner_user_id=user_id,
                official_website=f"{company_name}.com",
                domain_name=f"{company_name}.com",
                created_source=CreatedSource.CSV_IMPORT,
            ),
            result=expected_result,
        )
        result, _ = await crm_sync_service.find_or_create_account(
            account_import_csv_fields=account_import_csv_fields,
            organization_id=organization_id,
            user_id=user_id,
            local_timezone=None,
            owner_user_id=None,
        )
        assert result == expected_result


def mock_account_service_get_account_v2(
    account_service: AsyncMock,
    input_account_id: uuid.UUID,
    input_organization_id: uuid.UUID,
    result: AccountV2,
) -> None:
    async def get_account_v2(
        account_id: uuid.UUID, organization_id: uuid.UUID
    ) -> AccountV2:
        assert account_id == input_account_id
        assert organization_id == input_organization_id
        return result

    account_service.get_account_v2 = AsyncMock(side_effect=get_account_v2)


def mock_account_service_create_account_v2(
    account_service: AsyncMock,
    input_organization_id: uuid.UUID,
    input_user_id: uuid.UUID,
    input_create_account_request: CreateAccountRequest,
    result: AccountV2,
) -> None:
    async def create_account_v2(
        organization_id: uuid.UUID,
        user_id: uuid.UUID,
        create_account_request: CreateAccountRequest,
    ) -> AccountV2:
        assert organization_id == input_organization_id
        assert user_id == input_user_id
        assert create_account_request == input_create_account_request
        return result

    account_service.create_account_v2 = AsyncMock(side_effect=create_account_v2)


class TestCrmSyncServiceCsvParse:
    def _get_fake_import_job(self) -> ImportJob:
        return ImportJob(
            id=uuid.uuid4(),
            organization_id=uuid.uuid4(),
            created_by_user_id=uuid.uuid4(),
            display_name="Test Account Import",
            status=ImportCsvJobStatus.STARTED,
            created_at=datetime.now(pytz.UTC),
            updated_at=datetime.now(pytz.UTC),
            metadata=FileMetadata(
                file_id=uuid.uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="display_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="official_website_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_official_website"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="description",
                                qualified_field=QualifiedImportField(
                                    path=["company_description"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="industry",
                                qualified_field=QualifiedImportField(
                                    path=["company_industry"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="technologies",
                                qualified_field=QualifiedImportField(
                                    path=["company_technologies"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="estimated_annual_revenue",
                                qualified_field=QualifiedImportField(
                                    path=["company_estimated_annual_revenue"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="estimated_employee_count",
                                qualified_field=QualifiedImportField(
                                    path=["company_estimated_employee_count"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="linkedin_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_linkedin_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="facebook_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_facebook_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="x_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_x_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="street_one",
                                qualified_field=QualifiedImportField(
                                    path=["street_one"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="street_two",
                                qualified_field=QualifiedImportField(
                                    path=["street_two"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="zip_code",
                                qualified_field=QualifiedImportField(path=["zip_code"]),
                            ),
                            ColumnMapping(
                                column_name="city",
                                qualified_field=QualifiedImportField(path=["city"]),
                            ),
                            ColumnMapping(
                                column_name="state",
                                qualified_field=QualifiedImportField(path=["state"]),
                            ),
                            ColumnMapping(
                                column_name="country",
                                qualified_field=QualifiedImportField(path=["country"]),
                            ),
                            ColumnMapping(
                                column_name="account_state",  # This is an example of a custom field not directly on AccountImportCsvFields
                                qualified_field=QualifiedImportField(
                                    path=["account_state"]
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            ),
        )

    async def test_parse_account_csv_example_fe_csv(
        self, crm_sync_service: CrmSyncService
    ) -> None:
        with open(  # noqa: ASYNC230 - Sync file operation acceptable in test code, don't prod this
            f"{_my_dir}/data/sample_account_import.csv", "rb"
        ) as f:
            content = f.read()

        row_num_to_csv_row_map = await crm_sync_service._process_csv_data_v2(
            file_binary=content,
            csv_import_job=self._get_fake_import_job(),
            heartbeat_resume=None,
        )
        assert row_num_to_csv_row_map is not None
        assert len(row_num_to_csv_row_map) == 1, "Should have parsed exactly one row"

        # Row numbers in the map are 1-based for data rows (line 2 in CSV)
        csv_row_data = row_num_to_csv_row_map.get(2)
        assert csv_row_data is not None
        assert csv_row_data.all_entities is not None
        assert len(csv_row_data.all_entities) == 1
        assert isinstance(csv_row_data.all_entities[0], AccountImportCsvFields)
        acct_record: AccountImportCsvFields = csv_row_data.all_entities[0]

        self.assert_sample_csv_standard_account_fields(acct_record)
        assert acct_record.model_extra is not None
        # Assertions for fields mapped to model_extra
        assert acct_record.model_extra.get("street_one") == "2445 augustine dr"
        assert acct_record.model_extra.get("street_two") == ""  # Empty in CSV
        assert acct_record.model_extra.get("zip_code") == "95054"
        assert acct_record.model_extra.get("city") == "Santa Clara"
        assert acct_record.model_extra.get("state") == "CA"
        assert acct_record.model_extra.get("country") == "USA"

    async def test_blank_csv_lines_are_skipped(
        self, crm_sync_service: CrmSyncService
    ) -> None:
        # Open a CSV with blank lines
        with open(  # noqa: ASYNC230 - Sync file operation acceptable in test code, don't prod this
            "tests/unit/core/crm_sync/service/data/sample_account_import.blank_lines.csv",
            "rb",
        ) as account_csv_file:
            content = account_csv_file.read()
        row_num_to_csv_row_map = await crm_sync_service._process_csv_data_v2(
            file_binary=content,
            csv_import_job=self._get_fake_import_job(),
            heartbeat_resume=None,
        )
        assert row_num_to_csv_row_map is not None
        assert len(row_num_to_csv_row_map) == 1, (
            "Should have parsed exactly one row after skipping blank lines"
        )
        # Row numbers in the map are 1-based for data rows (line 8 in CSV)
        csv_row_data = row_num_to_csv_row_map.get(8)
        assert csv_row_data is not None
        assert csv_row_data.all_entities is not None
        assert len(csv_row_data.all_entities) == 1
        assert isinstance(csv_row_data.all_entities[0], AccountImportCsvFields)
        acct_record: AccountImportCsvFields = csv_row_data.all_entities[0]

        self.assert_sample_csv_standard_account_fields(acct_record)
        assert acct_record.model_extra is not None
        # Assertions for fields mapped to model_extra
        assert acct_record.model_extra.get("street_one") == "2445 augustine dr"
        assert acct_record.model_extra.get("street_two") == ""  # Empty in CSV
        assert acct_record.model_extra.get("zip_code") == "95054"
        assert acct_record.model_extra.get("city") == "Santa Clara"
        assert acct_record.model_extra.get("state") == "CA"

    async def test_unrecognized_csv_headers_are_ignored(
        self, crm_sync_service: CrmSyncService
    ) -> None:
        # Open a CSV with blank lines
        with open(  # noqa: ASYNC230 - Sync file operation acceptable in test code, don't prod this
            f"{_my_dir}/data/sample_account_import.extra_unrecognized_headers.csv",
            "rb",
        ) as account_csv_file:
            content = account_csv_file.read()
        row_num_to_csv_row_map = await crm_sync_service._process_csv_data_v2(
            file_binary=content,
            csv_import_job=self._get_fake_import_job(),
            heartbeat_resume=None,
        )

        assert row_num_to_csv_row_map is not None
        assert len(row_num_to_csv_row_map) == 1, "Should have parsed exactly one row"

        # Row numbers in the map are 1-based for data rows (line 2 in CSV)
        csv_row_data = row_num_to_csv_row_map.get(2)
        assert csv_row_data is not None
        assert csv_row_data.all_entities is not None
        assert len(csv_row_data.all_entities) == 1
        assert isinstance(csv_row_data.all_entities[0], AccountImportCsvFields)
        acct_record: AccountImportCsvFields = csv_row_data.all_entities[0]

        self.assert_sample_csv_standard_account_fields(acct_record)
        assert acct_record.model_extra is not None
        # Assertions for fields mapped to model_extra
        assert acct_record.model_extra.get("street_one") == "2445 augustine dr"
        assert acct_record.model_extra.get("street_two") == ""  # Empty in CSV
        assert acct_record.model_extra.get("zip_code") == "95054"
        assert acct_record.model_extra.get("city") == "Santa Clara"
        assert acct_record.model_extra.get("state") == "CA"
        assert acct_record.model_extra.get("country") == "USA"

    def assert_sample_csv_standard_account_fields(
        self, acct_record: AccountImportCsvFields
    ) -> None:
        assert acct_record.company_display_name == "Reeve.ai"
        assert acct_record.company_domain_name == "reeve.ai"
        assert acct_record.company_official_website == "www.reevo.ai"
        assert acct_record.company_description == "Future of CRM AI"
        assert acct_record.company_industry == "Technology"
        assert acct_record.company_technologies == "AI"
        assert acct_record.company_estimated_annual_revenue == "100M"
        assert acct_record.company_estimated_employee_count == "50-200"
        assert (
            acct_record.company_linkedin_url
            == "https://www.linkedin.com/company/reevo-ai"
        )
        # Empty string is returned for empty fields rather than None, these fields are present in the CSV header.
        #   Update: now we manually configured empty strings to be None to respect previous codebase behavior.
        #   This is significant.  These values must be None, not empty string, or the service will try and
        #   validate these empty strings for URL-ness, and fail, when it should've skipped validation.
        assert (
            acct_record.company_facebook_url is None
        )  # Must be None, not empty string!
        assert acct_record.company_x_url is None
        # None if the header isn't even present in the CSV.
        assert acct_record.company_owner_email is None
        assert acct_record.company_created_at is None
        assert acct_record.company_status == "TARGET"

    def _get_fake_prefixed_import_job(self) -> ImportJob:
        return ImportJob(
            id=uuid.uuid4(),
            organization_id=uuid.uuid4(),
            created_by_user_id=uuid.uuid4(),
            display_name="Test Account Import",
            status=ImportCsvJobStatus.STARTED,
            created_at=datetime.now(pytz.UTC),
            updated_at=datetime.now(pytz.UTC),
            metadata=FileMetadata(
                file_id=uuid.uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="company_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_official_website_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_official_website"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_description",
                                qualified_field=QualifiedImportField(
                                    path=["company_description"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_industry",
                                qualified_field=QualifiedImportField(
                                    path=["company_industry"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_technologies",
                                qualified_field=QualifiedImportField(
                                    path=["company_technologies"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_estimated_annual_revenue",
                                qualified_field=QualifiedImportField(
                                    path=["company_estimated_annual_revenue"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_estimated_employee_count",
                                qualified_field=QualifiedImportField(
                                    path=["company_estimated_employee_count"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_linkedin_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_linkedin_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_facebook_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_facebook_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_x_url",
                                qualified_field=QualifiedImportField(
                                    path=["company_x_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_street_one",
                                qualified_field=QualifiedImportField(
                                    path=["street_one"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_street_two",
                                qualified_field=QualifiedImportField(
                                    path=["street_two"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_zip_code",
                                qualified_field=QualifiedImportField(path=["zip_code"]),
                            ),
                            ColumnMapping(
                                column_name="company_city",
                                qualified_field=QualifiedImportField(path=["city"]),
                            ),
                            ColumnMapping(
                                column_name="company_state",
                                qualified_field=QualifiedImportField(path=["state"]),
                            ),
                            ColumnMapping(
                                column_name="company_country",
                                qualified_field=QualifiedImportField(path=["country"]),
                            ),
                            ColumnMapping(
                                column_name="company_account_state",  # This is an example of a custom field not directly on AccountImportCsvFields
                                qualified_field=QualifiedImportField(
                                    path=["account_state"]
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            ),
        )

    async def test_parse_account_csv_example_prefixed_fe_csv(
        self, crm_sync_service: CrmSyncService
    ) -> None:
        with open(  # noqa: ASYNC230 - Sync file operation acceptable in test code, don't prod this
            f"{_my_dir}/data/sample_account_import.prefixed.csv",
            "rb",
        ) as account_csv_file:
            content = account_csv_file.read()
        row_num_to_csv_row_map = await crm_sync_service._process_csv_data_v2(
            file_binary=content,
            csv_import_job=self._get_fake_prefixed_import_job(),
            heartbeat_resume=None,
        )
        assert row_num_to_csv_row_map is not None
        assert len(row_num_to_csv_row_map) == 1, "Should have parsed exactly one row"

        # Row numbers in the map are 1-based for data rows (line 2 in CSV)
        csv_row_data = row_num_to_csv_row_map.get(2)
        assert csv_row_data is not None
        assert csv_row_data.all_entities is not None
        assert len(csv_row_data.all_entities) == 1
        assert isinstance(csv_row_data.all_entities[0], AccountImportCsvFields)
        acct_record: AccountImportCsvFields = csv_row_data.all_entities[0]

        self.assert_sample_csv_standard_account_fields(acct_record)
        assert acct_record.model_extra is not None
        # Assertions for fields mapped to model_extra
        assert acct_record.model_extra.get("street_one") == "2445 augustine dr"
        assert acct_record.model_extra.get("street_two") == ""  # Empty in CSV
        assert acct_record.model_extra.get("zip_code") == "95054"
        assert acct_record.model_extra.get("city") == "Santa Clara"
        assert acct_record.model_extra.get("state") == "CA"
        assert acct_record.model_extra.get("country") == "USA"

    def _get_fake_contact_import_job(self) -> ImportJob:
        return ImportJob(
            id=uuid.uuid4(),
            organization_id=uuid.uuid4(),
            created_by_user_id=uuid.uuid4(),
            display_name="Test Contact Import",
            status=ImportCsvJobStatus.STARTED,
            created_at=datetime.now(pytz.UTC),
            updated_at=datetime.now(pytz.UTC),
            metadata=FileMetadata(
                file_id=uuid.uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="first_name",
                                qualified_field=QualifiedImportField(
                                    path=["contact_first_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="last_name",
                                qualified_field=QualifiedImportField(
                                    path=["contact_last_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="primary_email",
                                qualified_field=QualifiedImportField(
                                    path=["contact_primary_email"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="primary_phone_number",
                                qualified_field=QualifiedImportField(
                                    path=["contact_primary_phone_number"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="company_domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            ),
        )

    async def test_parse_contact_csv_example_fe_csv(
        self, crm_sync_service: CrmSyncService
    ) -> None:
        with open(  # noqa: ASYNC230 - Sync file operation acceptable in test code, don't prod this
            f"{_my_dir}/data/sample_contact_import.csv",
            "rb",
        ) as contact_csv_file:
            content = contact_csv_file.read()

        row_num_to_csv_row_map = await crm_sync_service._process_csv_data_v2(
            file_binary=content,
            csv_import_job=self._get_fake_contact_import_job(),
            heartbeat_resume=None,
        )

        # Verify we have exactly one row (plus header)
        assert row_num_to_csv_row_map is not None
        assert len(row_num_to_csv_row_map) == 1
        csv_row_data = row_num_to_csv_row_map.get(
            2
        )  # First data row is at line 2 (1-indexed)
        assert csv_row_data is not None
        assert csv_row_data.all_entities is not None
        assert len(csv_row_data.all_entities) == 1
        assert isinstance(csv_row_data.all_entities[0], ContactImportCsvFields)
        contact_fields: ContactImportCsvFields = csv_row_data.all_entities[0]

        # Verify the field values match the CSV content
        # Using the actual field names from ContactImportCsvFields
        assert contact_fields.contact_first_name == "John"
        assert contact_fields.contact_last_name == "Doe"
        assert contact_fields.contact_primary_email == "<EMAIL>"
        assert contact_fields.contact_primary_phone_number == "**********"
        assert contact_fields.company_display_name == "Reevo.ai"
        assert contact_fields.company_domain_name == "reevo.ai"

        # Verify optional fields that are not in the CSV are None or default
        assert contact_fields.contact_display_name is None
        assert contact_fields.contact_stage_value is None
        assert contact_fields.contact_job_title is None
        assert contact_fields.contact_department is None
        assert contact_fields.contact_linkedin_url is None
        assert contact_fields.contact_facebook_url is None
        assert contact_fields.contact_x_url is None
        assert contact_fields.contact_owner_email is None
        assert contact_fields.contact_created_at is None
        assert contact_fields.company_official_website is None


class TestCrmSyncServiceCsvParseV2:
    async def test_parse_account_and_contact(
        self, crm_sync_service: CrmSyncService
    ) -> None:
        import uuid
        from datetime import datetime

        import pytz

        # Create a single import job for the combined CSV
        job_id = uuid.uuid4()
        org_id = uuid.uuid4()
        file_id = uuid.uuid4()

        now = datetime.now(pytz.UTC)

        # Setup combined mappings for both account and contact fields
        account_mapping = ObjectMapping(
            object_identifier=StandardObjectIdentifier(
                object_name=StdObjectIdentifiers.account
            ),
            column_mappings=[
                ColumnMapping(
                    column_name="display_name",
                    qualified_field=QualifiedImportField(path=["company_display_name"]),
                ),
                ColumnMapping(
                    column_name="domain_name",
                    qualified_field=QualifiedImportField(path=["company_domain_name"]),
                ),
                ColumnMapping(
                    column_name="official_website_url",
                    qualified_field=QualifiedImportField(
                        path=["company_official_website"]
                    ),
                ),
                ColumnMapping(
                    column_name="description",
                    qualified_field=QualifiedImportField(path=["company_description"]),
                ),
                ColumnMapping(
                    column_name="industry",
                    qualified_field=QualifiedImportField(path=["company_industry"]),
                ),
                ColumnMapping(
                    column_name="technologies",
                    qualified_field=QualifiedImportField(path=["company_technologies"]),
                ),
                ColumnMapping(
                    column_name="account_state",
                    qualified_field=QualifiedImportField(path=["company_status"]),
                ),
            ],
        )

        contact_mapping = ObjectMapping(
            object_identifier=StandardObjectIdentifier(
                object_name=StdObjectIdentifiers.contact
            ),
            column_mappings=[
                ColumnMapping(
                    column_name="first_name",
                    qualified_field=QualifiedImportField(path=["contact_first_name"]),
                ),
                ColumnMapping(
                    column_name="last_name",
                    qualified_field=QualifiedImportField(path=["contact_last_name"]),
                ),
                ColumnMapping(
                    column_name="primary_email",
                    qualified_field=QualifiedImportField(
                        path=["contact_primary_email"]
                    ),
                ),
                ColumnMapping(
                    column_name="primary_phone_number",
                    qualified_field=QualifiedImportField(
                        path=["contact_primary_phone_number"]
                    ),
                ),
                ColumnMapping(
                    column_name="company_name",
                    qualified_field=QualifiedImportField(path=["company_display_name"]),
                ),
                ColumnMapping(
                    column_name="company_domain_name",
                    qualified_field=QualifiedImportField(path=["company_domain_name"]),
                ),
            ],
        )

        # Create the combined import job
        combined_job = ImportJob(
            id=job_id,
            workflow_id="test-workflow-combined",
            organization_id=org_id,
            created_at=now,
            updated_at=now,
            status=ImportCsvJobStatus.STARTED,
            object_identifier=None,  # No single object identifier for combined import
            metadata=FileMetadata(
                file_id=file_id,
                object_mappings=[account_mapping, contact_mapping],
                association_label_mapping=[],
            ),
        )

        # Test combined CSV processing
        with open(  # noqa: ASYNC230 - Sync file operation acceptable in test code
            f"{_my_dir}/data/sample_account_and_contact_import.csv",
            "rb",
        ) as combined_csv_file:
            combined_content = combined_csv_file.read()

        row_map = await crm_sync_service._process_csv_data_v2(
            file_binary=combined_content,
            csv_import_job=combined_job,
            heartbeat_resume=None,
        )

        # Verify combined CSV parsing results
        assert row_map is not None, "Should have parsed the combined CSV"
        assert len(row_map) > 0, "Should have parsed at least one row"

        # Get the first row (which is row 2 in the CSV, 1-indexed with header at row 1)
        csv_row = row_map[2]
        assert csv_row is not None, "Should have parsed row 2"
        assert csv_row.all_entities is not None, "Should have entities"
        # Should have both account and contact entities from the same row
        assert len(csv_row.all_entities) == 2, (
            "Should have two entities (account and contact)"
        )

        # Find the account and contact entities
        account_entity: AccountImportCsvFields | None = None
        contact_entity: ContactImportCsvFields | None = None

        for entity in csv_row.all_entities:
            if isinstance(entity, AccountImportCsvFields):
                assert account_entity is None, "Should only have one account entity"
                account_entity = entity
            elif isinstance(entity, ContactImportCsvFields):
                assert contact_entity is None, "Should only have one contact entity"
                contact_entity = entity
            else:
                raise AssertionError(
                    f"Unexpected entity type: ({type(entity)}) and value: ({entity})"
                )

        # Verify account data
        assert account_entity is not None, "Account entity not found"
        assert account_entity.company_display_name == "Reeve.ai"
        assert account_entity.company_domain_name == "reeve.ai"
        assert account_entity.company_official_website == "www.reevo.ai"
        assert account_entity.company_description == "Future of CRM AI"
        assert account_entity.company_industry == "Technology"
        assert account_entity.company_technologies == "AI"
        assert account_entity.company_status == "TARGET"

        # Verify contact data
        assert contact_entity is not None, "Contact entity not found"
        assert contact_entity.contact_first_name == "John"
        assert contact_entity.contact_last_name == "Doe"
        assert contact_entity.contact_primary_email == "<EMAIL>"
        assert contact_entity.contact_primary_phone_number == "**********"
        assert contact_entity.company_display_name == "Reevo.ai"
        assert contact_entity.company_domain_name == "reevo.ai"

    async def test_parse_with_association_label_mappings(
        self, crm_sync_service: CrmSyncService
    ) -> None:
        """Test that verifies association label mappings are correctly processed."""
        import uuid
        from datetime import datetime

        import pytz

        # Create an import job with association mappings
        job_id = uuid.uuid4()
        org_id = uuid.uuid4()
        file_id = uuid.uuid4()
        now = datetime.now(pytz.UTC)

        # Setup mappings for account and contact
        account_mapping = ObjectMapping(
            object_identifier=StandardObjectIdentifier(
                object_name=StdObjectIdentifiers.account
            ),
            column_mappings=[
                ColumnMapping(
                    column_name="display_name",
                    qualified_field=QualifiedImportField(path=["company_display_name"]),
                ),
                ColumnMapping(
                    column_name="domain_name",
                    qualified_field=QualifiedImportField(path=["company_domain_name"]),
                ),
            ],
        )

        contact_mapping = ObjectMapping(
            object_identifier=StandardObjectIdentifier(
                object_name=StdObjectIdentifiers.contact
            ),
            column_mappings=[
                ColumnMapping(
                    column_name="first_name",
                    qualified_field=QualifiedImportField(path=["contact_first_name"]),
                ),
                ColumnMapping(
                    column_name="last_name",
                    qualified_field=QualifiedImportField(path=["contact_last_name"]),
                ),
                ColumnMapping(
                    column_name="primary_email",
                    qualified_field=QualifiedImportField(
                        path=["contact_primary_email"]
                    ),
                ),
            ],
        )

        # Define association mappings with exact values from the CSV
        # Using constant UUIDs for predictable test results
        ham_uuid = "*************-6666-6666-************"
        spam_uuid = "*************-5555-5555-************"
        eggs_uuid = "*************-4444-4444-************"
        always_included_uuid = "********-1111-1111-1111-********1111"

        association_mappings = [
            AssociationLabelMapping(
                relationship_id=spam_uuid,
                column_mapping={"Account_To_Product": ["Spam"]},
            ),
            AssociationLabelMapping(
                relationship_id=ham_uuid,
                column_mapping={"Account_To_Product": ["Ham"]},
            ),
            AssociationLabelMapping(
                relationship_id=eggs_uuid,
                column_mapping={"Account_To_Product": ["Green Eggs"]},
            ),
            AssociationLabelMapping(
                relationship_id=always_included_uuid,
                column_mapping={},  # Empty column mapping means always included
            ),
        ]

        # Create the import job with association mappings
        test_job = ImportJob(
            id=job_id,
            workflow_id="test-workflow-associations",
            organization_id=org_id,
            created_at=now,
            updated_at=now,
            status=ImportCsvJobStatus.STARTED,
            object_identifier=None,
            metadata=FileMetadata(
                file_id=file_id,
                object_mappings=[account_mapping, contact_mapping],
                association_label_mapping=association_mappings,
            ),
        )

        # Use the CSV file with association data
        with open(  # noqa: ASYNC230 - Sync file operation acceptable in test code
            f"{_my_dir}/data/sample_account_contact_association_mapping.csv",
            "rb",
        ) as csv_file:
            file_content = csv_file.read()

        # Process the CSV data with association mappings
        row_map = await crm_sync_service._process_csv_data_v2(
            file_binary=file_content,
            csv_import_job=test_job,
            heartbeat_resume=None,
        )

        # Verify the CSV was processed
        assert row_map is not None, "Should have parsed the CSV"
        assert len(row_map) > 0, "Should have parsed at least one row"

        # Check the first row (Ham value)
        row_2 = row_map[2]
        assert row_2 is not None, "Should have parsed row 2"
        assert row_2.all_entities is not None, "Should have entities"
        assert row_2.all_relationship_ids is not None, "Should have relationship IDs"
        assert ham_uuid in row_2.all_relationship_ids, (
            f"Row 2 should have Ham relationship UUID: {ham_uuid}"
        )
        assert always_included_uuid in row_2.all_relationship_ids
        assert len(row_2.all_relationship_ids) == 2

        # Check the second row (Spam value)
        row_3 = row_map[3]
        assert row_3 is not None, "Should have parsed row 3"
        assert row_3.all_relationship_ids is not None, "Should have relationship IDs"
        assert spam_uuid in row_3.all_relationship_ids, (
            f"Row 3 should have Spam relationship UUID: {spam_uuid}"
        )
        assert always_included_uuid in row_3.all_relationship_ids
        assert len(row_3.all_relationship_ids) == 2

        # Check the third row (Green Eggs value)
        row_4 = row_map[4]
        assert row_4 is not None, "Should have parsed row 4"
        assert row_4.all_relationship_ids is not None, "Should have relationship IDs"
        assert eggs_uuid in row_4.all_relationship_ids, (
            f"Row 4 should have Green Eggs relationship UUID: {eggs_uuid}"
        )
        assert always_included_uuid in row_4.all_relationship_ids
        assert len(row_4.all_relationship_ids) == 2

    async def test_parse_mission_critical_account_contact_check_description_line_continuation(
        self, crm_sync_service: CrmSyncService
    ) -> None:
        """
        Test that verifies mission critical account and contact check 1.
        """

        account_mapping = ObjectMapping(
            object_identifier=StandardObjectIdentifier(
                object_name=StdObjectIdentifiers.account
            ),
            column_mappings=[
                ColumnMapping(
                    column_name="company_name",
                    qualified_field=QualifiedImportField(path=["display_name"]),
                ),
                ColumnMapping(
                    column_name="company_domain_name",
                    qualified_field=QualifiedImportField(path=["domain_name"]),
                ),
                ColumnMapping(
                    column_name="description",
                    qualified_field=QualifiedImportField(path=["description"]),
                ),
                ColumnMapping(
                    column_name="estimated_annual_revenue",
                    qualified_field=QualifiedImportField(
                        path=["estimated_annual_revenue"]
                    ),
                ),
                ColumnMapping(
                    column_name="estimated_employee_count",
                    qualified_field=QualifiedImportField(
                        path=["estimated_employee_count"]
                    ),
                ),
                ColumnMapping(
                    column_name="industry",
                    qualified_field=QualifiedImportField(path=["category_list"]),
                ),
                ColumnMapping(
                    column_name="technologies",
                    qualified_field=QualifiedImportField(path=["technology_list"]),
                ),
                ColumnMapping(
                    column_name="company_linkedin_url",
                    qualified_field=QualifiedImportField(path=["linkedin_url"]),
                ),
                ColumnMapping(
                    column_name="facebook_url",
                    qualified_field=QualifiedImportField(path=["facebook_url"]),
                ),
                ColumnMapping(
                    column_name="twitter_url",
                    qualified_field=QualifiedImportField(path=["x_url"]),
                ),
                ColumnMapping(
                    column_name="address_placeholder",
                    qualified_field=QualifiedImportField(
                        path=["b25bdc04-1dd5-409e-bc7c-ee293c582346"]
                    ),
                ),
                ColumnMapping(
                    column_name="city_placeholder",
                    qualified_field=QualifiedImportField(
                        path=["04aee581-182f-4426-9a64-186629d6b819"]
                    ),
                ),
                ColumnMapping(
                    column_name="state_placeholder",
                    qualified_field=QualifiedImportField(
                        path=["0b7f9c4f-c2aa-4495-88c0-e5b51699879a"]
                    ),
                ),
            ],
        )

        # Create import job
        job_id = uuid.uuid4()
        org_id = uuid.uuid4()
        file_id = uuid.uuid4()
        now = datetime.now(pytz.UTC)
        test_job = ImportJob(
            id=job_id,
            workflow_id="test-workflow-associations",
            organization_id=org_id,
            created_at=now,
            updated_at=now,
            status=ImportCsvJobStatus.STARTED,
            object_identifier=None,
            metadata=FileMetadata(
                file_id=file_id,
                object_mappings=[account_mapping],
                association_label_mapping=[],
            ),
        )

        with open(  # noqa: ASYNC230 - Sync file operation acceptable in test code
            f"{_my_dir}/data/sample_mission_crit_account_contact_check_1.csv",
            "rb",
        ) as csv_file:
            file_content = csv_file.read()

        # Process the CSV data with association mappings
        row_map = await crm_sync_service._process_csv_data_v2(
            file_binary=file_content,
            csv_import_job=test_job,
            heartbeat_resume=None,
        )

        assert row_map is not None
        assert len(row_map) == 5
        for row_value in row_map.values():
            # check before and after the line_continuation column (description)
            assert row_value is not None
            assert row_value.all_entities is not None
            assert len(row_value.all_entities) == 1
            for entity in row_value.all_entities:
                assert isinstance(entity, AccountImportCsvFields)
                assert entity.company_domain_name in (
                    "cleerlyhealth.com",
                    "ergon.com",
                )
                assert entity.company_estimated_annual_revenue in (
                    "********",
                    "**********",
                )
                assert entity.company_estimated_employee_count in (
                    "230",
                    "820",
                )


@pytest.fixture
def import_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def crm_sync_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def activity_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def association_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def contact_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def contact_query_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def custom_object_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def audience_list_membership_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def pipeline_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def pipeline_stage_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def account_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def user_auth_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def hubspot_client() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def user_integration_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def oauth_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def form_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def encryption_manager() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def form_submission_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def workflow_trigger_service_ext() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def workflow_form_submission_trigger_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def hubspot_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def import_job_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def account_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def user_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def task_v2_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def meeting_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def file_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def user_integration_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def meeting_bot_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def crm_sync_service(
    import_repository: AsyncMock,
    contact_service: AsyncMock,
    contact_query_service: AsyncMock,
    custom_object_service: AsyncMock,
    association_service: AsyncMock,
    audience_list_membership_service: AsyncMock,
    account_service: AsyncMock,
    activity_service: AsyncMock,
    pipeline_service: AsyncMock,
    pipeline_stage_service: AsyncMock,
    user_auth_service: AsyncMock,
    hubspot_service: AsyncMock,
    task_v2_service: AsyncMock,
    meeting_service: AsyncMock,
    file_service: AsyncMock,
    crm_sync_repository: AsyncMock,
    import_job_repository: AsyncMock,
) -> CrmSyncService:
    return CrmSyncService(
        import_repository=import_repository,
        association_service=association_service,
        contact_service=contact_service,
        contact_query_service=contact_query_service,
        custom_object_service=custom_object_service,
        audience_list_membership_service=audience_list_membership_service,
        account_service=account_service,
        activity_service=activity_service,
        pipeline_service=pipeline_service,
        pipeline_stage_service=pipeline_stage_service,
        user_auth_service=user_auth_service,
        task_v2_service=task_v2_service,
        meeting_service=meeting_service,
        file_service=file_service,
        crm_sync_repository=crm_sync_repository,
        import_job_repository=import_job_repository,
        hubspot_service=hubspot_service,
        domain_object_list_service=AsyncMock(),
        feature_flag_service=AsyncMock(),
        select_list_service=AsyncMock(),
    )
