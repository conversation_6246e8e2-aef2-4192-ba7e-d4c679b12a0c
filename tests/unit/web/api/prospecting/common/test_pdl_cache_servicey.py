import json
from typing import cast
from unittest.mock import MagicMock

import pytest

from salestech_be.integrations.pdl.model import PeopleDataLabsSearchCommonRequest
from salestech_be.web.api.prospecting.common.pdl_cache_servicey import (
    PDLCacheService,
    PDLOperationName,
)

JsonPrimitiveTest = str | int | float | bool | None
JsonPayloadTest = (
    JsonPrimitiveTest | list["JsonPayloadTest"] | dict[str, "JsonPayloadTest"]
)


@pytest.fixture
def mock_pdl_client() -> MagicMock:
    return MagicMock()


@pytest.fixture
def mock_pdl_cache_repository() -> MagicMock:
    return MagicMock()


@pytest.fixture
def pdl_cache_service(
    mock_pdl_client: MagicMock,
    mock_pdl_cache_repository: MagicMock,
) -> PDLCacheService:
    return PDLCacheService(
        pdl_client=mock_pdl_client,
        pdl_cache_repository=mock_pdl_cache_repository,
    )


class TestProspectingCacheServiceGetHashKey:
    default_api_name = PDLOperationName.search_company
    default_size = 10
    default_scroll_token: str | None = "scroll123"  # noqa: S105

    def _create_request(
        self,
        query_dict: dict[str, JsonPayloadTest] | None = None,
        size: int | None = None,
        scroll_token_override: str | None = "DEFAULT_TOKEN_PLACEHOLDER",  # noqa: S107
        raw_query_str: str | None = None,
    ) -> PeopleDataLabsSearchCommonRequest:
        current_size = size if size is not None else self.default_size

        current_scroll_token: str | None
        if scroll_token_override == "DEFAULT_TOKEN_PLACEHOLDER":  # noqa: S105
            current_scroll_token = self.default_scroll_token
        else:
            current_scroll_token = scroll_token_override

        query_to_pydantic: str | None
        current_sql: str | None = None

        if raw_query_str is not None:
            query_to_pydantic = raw_query_str
            if not query_to_pydantic:
                current_sql = "SELECT 1"
        elif query_dict is None:
            query_to_pydantic = "{}"
        else:
            query_to_pydantic = json.dumps(query_dict)

        if current_sql is not None and query_to_pydantic:
            query_to_pydantic = None

        return PeopleDataLabsSearchCommonRequest(
            query=query_to_pydantic,
            size=current_size,
            scroll_token=current_scroll_token,
            sql=current_sql,
        )

    def test_get_hash_key_identical_requests_produce_same_hash(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        query_content: dict[str, JsonPayloadTest] = {
            "bool": {
                "must": [{"bool": {"must": [{"terms": {"industry": ["software"]}}]}}]
            }
        }
        request1 = self._create_request(
            query_dict=query_content,
            scroll_token_override="DEFAULT_TOKEN_PLACEHOLDER",  # noqa: S106
        )
        request2 = self._create_request(
            query_dict=query_content,
            scroll_token_override="DEFAULT_TOKEN_PLACEHOLDER",  # noqa: S106
        )
        hash1 = pdl_cache_service.get_hash_key(self.default_api_name, request1)
        hash2 = pdl_cache_service.get_hash_key(self.default_api_name, request2)
        assert hash1 == hash2

    def test_get_hash_key_different_api_names_produce_different_hashes(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        query_content: dict[str, JsonPayloadTest] = {
            "bool": {
                "must": [{"bool": {"must": [{"terms": {"industry": ["software"]}}]}}]
            }
        }
        request = self._create_request(query_dict=query_content)

        hash1 = pdl_cache_service.get_hash_key(PDLOperationName.search_company, request)
        hash2 = pdl_cache_service.get_hash_key(
            PDLOperationName.search_people,
            request,  # Use actual enum member
        )
        assert hash1 != hash2

    def test_get_hash_key_different_sizes_produce_different_hashes(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        query_content: dict[str, JsonPayloadTest] = {
            "bool": {
                "must": [{"bool": {"must": [{"terms": {"industry": ["software"]}}]}}]
            }
        }
        request1 = self._create_request(
            query_dict=query_content,
            size=10,
            scroll_token_override="DEFAULT_TOKEN_PLACEHOLDER",  # noqa: S106
        )
        request2 = self._create_request(
            query_dict=query_content,
            size=20,
            scroll_token_override="DEFAULT_TOKEN_PLACEHOLDER",  # noqa: S106
        )
        hash1 = pdl_cache_service.get_hash_key(self.default_api_name, request1)
        hash2 = pdl_cache_service.get_hash_key(self.default_api_name, request2)
        assert hash1 != hash2

    def test_get_hash_key_different_scroll_tokens_produce_different_hashes(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        query_content: dict[str, JsonPayloadTest] = {
            "bool": {
                "must": [{"bool": {"must": [{"terms": {"industry": ["software"]}}]}}]
            }
        }
        request1 = self._create_request(
            query_dict=query_content,
            scroll_token_override="tokenA",  # noqa: S106
        )
        request2 = self._create_request(
            query_dict=query_content,
            scroll_token_override="tokenB",  # noqa: S106
        )
        hash1 = pdl_cache_service.get_hash_key(self.default_api_name, request1)
        hash2 = pdl_cache_service.get_hash_key(self.default_api_name, request2)
        assert hash1 != hash2

    def test_get_hash_key_none_scroll_token_handled(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        query_content: dict[str, JsonPayloadTest] = {
            "bool": {
                "must": [{"bool": {"must": [{"terms": {"industry": ["software"]}}]}}]
            }
        }
        request1 = self._create_request(
            query_dict=query_content,
            scroll_token_override="tokenA",  # noqa: S106
        )
        request2 = self._create_request(
            query_dict=query_content, scroll_token_override=None
        )
        hash1 = pdl_cache_service.get_hash_key(self.default_api_name, request1)
        hash2 = pdl_cache_service.get_hash_key(self.default_api_name, request2)
        assert hash1 != hash2
        request3 = self._create_request(
            query_dict=query_content, scroll_token_override=None
        )
        hash3 = pdl_cache_service.get_hash_key(self.default_api_name, request3)
        assert hash2 == hash3

    def test_get_hash_key_query_key_order_does_not_affect_hash(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        query_params1: list[JsonPayloadTest] = [
            {"terms": {"industry": ["software"]}},
            {"range": {"last_funding_date": {"gte": "2024"}}},
        ]
        query_params2: list[JsonPayloadTest] = [
            {"range": {"last_funding_date": {"gte": "2024"}}},
            {"terms": {"industry": ["software"]}},  # Order swapped inside query_params
        ]

        query1_dict: dict[str, JsonPayloadTest] = {
            "bool": {  # Top level structure
                "must": [
                    {"bool": {"must": query_params1}}  # query_params here
                ]
            }
        }
        simple_query1: dict[str, JsonPayloadTest] = {"keyA": "valA", "keyB": "valB"}
        simple_query2: dict[str, JsonPayloadTest] = {
            "keyB": "valB",
            "keyA": "valA",
        }  # Key order swapped

        request_simple1 = self._create_request(query_dict=simple_query1)
        request_simple2 = self._create_request(query_dict=simple_query2)
        hash_simple1 = pdl_cache_service.get_hash_key(
            self.default_api_name, request_simple1
        )
        hash_simple2 = pdl_cache_service.get_hash_key(
            self.default_api_name, request_simple2
        )
        assert hash_simple1 == hash_simple2, (
            "Hash for simple queries with swapped keys should be same"
        )

        request_complex1 = self._create_request(query_dict=query1_dict)
        query2_dict_variant: dict[str, JsonPayloadTest] = {
            "bool": {"must": [{"bool": {"must": query_params2}}]}
        }
        request_complex2 = self._create_request(query_dict=query2_dict_variant)
        hash_complex1 = pdl_cache_service.get_hash_key(
            self.default_api_name, request_complex1
        )
        hash_complex2 = pdl_cache_service.get_hash_key(
            self.default_api_name, request_complex2
        )
        assert hash_complex1 == hash_complex2, (
            "Hash for complex queries with reordered bool sub-clauses should be same"
        )

    def test_get_hash_key_bool_clause_order_does_not_affect_hash(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        query1_dict: dict[str, JsonPayloadTest] = {
            "bool": {
                "must": [
                    {
                        "bool": {
                            "must": [
                                {"terms": {"name": "Acme"}},
                                {"terms": {"type": "Corp"}},
                            ]
                        }
                    }
                ]
            }
        }
        query2_dict: dict[str, JsonPayloadTest] = {
            "bool": {
                "must": [
                    {
                        "bool": {
                            "must": [
                                {"terms": {"type": "Corp"}},  # Swapped order
                                {"terms": {"name": "Acme"}},
                            ]
                        }
                    }
                ]
            }
        }
        request1 = self._create_request(query_dict=query1_dict)
        request2 = self._create_request(query_dict=query2_dict)
        hash1 = pdl_cache_service.get_hash_key(self.default_api_name, request1)
        hash2 = pdl_cache_service.get_hash_key(self.default_api_name, request2)
        assert hash1 == hash2

    def test_get_hash_key_bool_different_clause_types_order_does_not_affect_hash(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        query1_dict: dict[str, JsonPayloadTest] = {
            "bool": {  # Top-level query is a bool
                "must": [  # This outer must is what build_pdl_common_search_request creates
                    {
                        "bool": {  # This inner bool contains the actual filters
                            "must": [{"terms": {"name": "Acme"}}],
                            "filter": [{"terms": {"active": True}}],
                        }
                    }
                ]
            }
        }
        query2_dict: dict[str, JsonPayloadTest] = {
            "bool": {
                "must": [
                    {
                        "bool": {
                            "filter": [
                                {"terms": {"active": True}}
                            ],  # Swapped order of must/filter
                            "must": [{"terms": {"name": "Acme"}}],
                        }
                    }
                ]
            }
        }
        request1 = self._create_request(query_dict=query1_dict)
        request2 = self._create_request(query_dict=query2_dict)
        hash1 = pdl_cache_service.get_hash_key(self.default_api_name, request1)
        hash2 = pdl_cache_service.get_hash_key(self.default_api_name, request2)
        assert hash1 == hash2

    def test_get_hash_key_different_query_content_produces_different_hashes(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        query1_dict: dict[str, JsonPayloadTest] = {
            "bool": {"must": [{"bool": {"must": [{"terms": {"name": "Acme Corp"}}]}}]}
        }
        query2_dict: dict[str, JsonPayloadTest] = {
            "bool": {"must": [{"bool": {"must": [{"terms": {"name": "Beta LLC"}}]}}]}
        }
        request1 = self._create_request(query_dict=query1_dict)
        request2 = self._create_request(query_dict=query2_dict)
        hash1 = pdl_cache_service.get_hash_key(self.default_api_name, request1)
        hash2 = pdl_cache_service.get_hash_key(self.default_api_name, request2)
        assert hash1 != hash2

    def test_get_hash_key_query_is_none_or_empty_dict_produces_consistent_hash(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        request_from_none_dict = self._create_request(query_dict=None)
        hash_from_none_dict = pdl_cache_service.get_hash_key(
            self.default_api_name, request_from_none_dict
        )

        request_from_empty_dict = self._create_request(query_dict={})
        hash_from_empty_dict = pdl_cache_service.get_hash_key(
            self.default_api_name, request_from_empty_dict
        )

        assert hash_from_none_dict == hash_from_empty_dict

    def test_get_hash_key_various_invalid_query_strings_produce_consistent_hash_as_empty(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        request_logically_empty = self._create_request(
            query_dict=None,
            size=self.default_size,
            scroll_token_override=self.default_scroll_token,
        )
        hash_logically_empty = pdl_cache_service.get_hash_key(
            self.default_api_name, request_logically_empty
        )

        request_empty_str = PeopleDataLabsSearchCommonRequest(
            query="",
            sql="SELECT 1",
            size=self.default_size,
            scroll_token=self.default_scroll_token,
        )
        hash_empty_str = pdl_cache_service.get_hash_key(
            self.default_api_name, request_empty_str
        )
        assert hash_empty_str == hash_logically_empty, (
            'Hash for query="" should match empty query hash'
        )

        request_invalid_json = PeopleDataLabsSearchCommonRequest(
            query="{invalid_json",
            sql=None,
            size=self.default_size,
            scroll_token=self.default_scroll_token,
        )
        hash_invalid_json = pdl_cache_service.get_hash_key(
            self.default_api_name, request_invalid_json
        )
        assert hash_invalid_json == hash_logically_empty, (
            "Hash for invalid JSON should match empty query hash"
        )

        request_json_array = PeopleDataLabsSearchCommonRequest(
            query='[{"key": "value"}]',
            sql=None,
            size=self.default_size,
            scroll_token=self.default_scroll_token,
        )
        hash_json_array = pdl_cache_service.get_hash_key(
            self.default_api_name, request_json_array
        )
        assert hash_json_array == hash_logically_empty, (
            "Hash for JSON array should match empty query hash"
        )

    def test_get_hash_key_complex_nested_query_normalization(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        def build_complex_query(
            params_list_ordered: list[JsonPayloadTest],
            different_outer_key_order: bool = False,
        ) -> dict[str, JsonPayloadTest]:
            inner_bool: dict[str, JsonPayloadTest] = {"must": params_list_ordered}
            outer_must_content: dict[str, JsonPayloadTest] = {"bool": inner_bool}

            must_clause_value: list[JsonPayloadTest] = [outer_must_content]

            bool_value: dict[str, JsonPayloadTest] = {"must": must_clause_value}

            final_query: dict[str, JsonPayloadTest] = {"bool": bool_value}
            return final_query

        params1: list[JsonPayloadTest] = [
            {
                "bool": {
                    "must_not": [{"exists": {"field": "obsolete"}}],
                    "should": [
                        {"term": {"priority": "high"}},
                        {
                            "bool": {
                                "must": [
                                    {"term": {"tag": "urgent"}},
                                    {"range": {"date": {"gte": "now-1d/d"}}},
                                ]
                            }
                        },
                    ],
                    "filter": [{"term": {"status": "active"}}],
                    "must": [{"match": {"title": "Search Query"}}],
                }
            }
        ]
        params2: list[JsonPayloadTest] = [
            {
                "bool": {
                    "must": [{"match": {"title": "Search Query"}}],
                    "filter": [{"term": {"status": "active"}}],
                    "should": [
                        {
                            "bool": {
                                "must": [
                                    {"range": {"date": {"gte": "now-1d/d"}}},
                                    {"term": {"tag": "urgent"}},
                                ]
                            }
                        },
                        {"term": {"priority": "high"}},
                    ],
                    "must_not": [{"exists": {"field": "obsolete"}}],
                }
            }
        ]

        query1_dict = build_complex_query(params1)
        query2_dict = build_complex_query(params2)

        request1 = self._create_request(query_dict=query1_dict)
        request2 = self._create_request(query_dict=query2_dict)

        hash1 = pdl_cache_service.get_hash_key(self.default_api_name, request1)
        hash2 = pdl_cache_service.get_hash_key(self.default_api_name, request2)

        assert hash1 == hash2, (
            "Complex nested queries with internal reordering should produce same hash"
        )

    def test_get_hash_key_terms_query_value_list_order_does_not_affect_hash(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        query1_params: list[JsonPayloadTest] = [
            {"terms": {"tags": ["alpha", "beta", 10, 2]}}
        ]
        query2_params: list[JsonPayloadTest] = [
            {"terms": {"tags": [10, "beta", 2, "alpha"]}}
        ]

        query1_dict: dict[str, JsonPayloadTest] = {
            "bool": {"must": [{"bool": {"must": query1_params}}]}
        }
        query2_dict: dict[str, JsonPayloadTest] = {
            "bool": {"must": [{"bool": {"must": query2_params}}]}
        }

        request1 = self._create_request(query_dict=query1_dict)
        request2 = self._create_request(query_dict=query2_dict)

        hash1 = pdl_cache_service.get_hash_key(self.default_api_name, request1)
        hash2 = pdl_cache_service.get_hash_key(self.default_api_name, request2)

        assert hash1 == hash2, "Order of items in a 'terms' list should NOT matter"

    def test_get_hash_key_terms_query_with_multiple_fields_and_value_list_order(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        query1_dict: dict[str, JsonPayloadTest] = {
            "bool": {
                "must": [
                    {
                        "bool": {
                            "must": [
                                {"terms": {"tags": ["a", "b"], "category": [2, 1]}}
                            ]
                        }
                    }
                ]
            }
        }
        query2_dict: dict[str, JsonPayloadTest] = {
            "bool": {
                "must": [
                    {
                        "bool": {
                            "must": [
                                {"terms": {"category": [1, 2], "tags": ["b", "a"]}}
                            ]
                        }
                    }
                ]
            }
        }
        request1 = self._create_request(query_dict=query1_dict)
        request2 = self._create_request(query_dict=query2_dict)
        hash1 = pdl_cache_service.get_hash_key(self.default_api_name, request1)
        hash2 = pdl_cache_service.get_hash_key(self.default_api_name, request2)
        assert hash1 == hash2, (
            "Terms query with multiple fields, field order, and value list order should normalize"
        )

    def test_get_hash_key_terms_query_values_are_already_sorted_strings(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        query_params: list[JsonPayloadTest] = [{"terms": {"tags": ["alpha", "beta"]}}]
        query_dict: dict[str, JsonPayloadTest] = {
            "bool": {"must": [{"bool": {"must": query_params}}]}
        }
        request = self._create_request(query_dict=query_dict)
        hash_val = pdl_cache_service.get_hash_key(self.default_api_name, request)

        query_params_presorted_strings: list[JsonPayloadTest] = [
            {"terms": {"tags": ["alpha", "beta"]}}
        ]
        query_dict_presorted_strings: dict[str, JsonPayloadTest] = {
            "bool": {"must": [{"bool": {"must": query_params_presorted_strings}}]}
        }
        request_presorted_strings = self._create_request(
            query_dict=query_dict_presorted_strings
        )
        hash_presorted_strings = pdl_cache_service.get_hash_key(
            self.default_api_name, request_presorted_strings
        )
        assert hash_val == hash_presorted_strings

    def test_get_hash_key_terms_query_mixed_type_values_sorted_as_strings(
        self, pdl_cache_service: PDLCacheService
    ) -> None:
        query1_params: list[JsonPayloadTest] = [
            {"terms": {"tags": [10, "apple", 2, "22"]}}
        ]
        query1_dict: dict[str, JsonPayloadTest] = {
            "bool": {"must": [{"bool": {"must": query1_params}}]}
        }
        request1 = self._create_request(query_dict=query1_dict)
        hash1 = pdl_cache_service.get_hash_key(self.default_api_name, request1)

        expected_terms_values_sorted_as_strings: list[str] = sorted(
            [str(v) for v in [10, "apple", 2, "22"]]
        )

        terms_clause_content: dict[str, JsonPayloadTest] = {
            "tags": cast(JsonPayloadTest, expected_terms_values_sorted_as_strings)
        }

        query_expected_single_param: dict[str, JsonPayloadTest] = {
            "terms": terms_clause_content
        }

        query_expected_params: list[JsonPayloadTest] = [query_expected_single_param]

        query_expected_dict: dict[str, JsonPayloadTest] = {
            "bool": {"must": [{"bool": {"must": query_expected_params}}]}
        }
        request_expected = self._create_request(query_dict=query_expected_dict)
        hash_expected = pdl_cache_service.get_hash_key(
            self.default_api_name, request_expected
        )

        assert hash1 == hash_expected, (
            "Mixed type terms list should be sorted as strings"
        )
