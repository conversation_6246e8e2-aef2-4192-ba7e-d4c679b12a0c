# https://chartdb.io/

ChartDB is an open-source database diagramming tool. It is available both as an online service, as well as a self-hosted version.

## Self-hosted version via Docker

The below command will start the ChartDB server, listening on host port 8080, forwarding to the container's port 80.

### Saving the diagram

Choose the `Backup > Export Diagram` option to export the diagram as a JSON file.

### Loading the diagram

Choose the `Backup > Restore Diagram` option to import the diagram from a JSON file.

```shell
docker run -p 8080:80 ghcr.io/chartdb/chartdb:latest
```
