applicationName: webhook

replicaCount: 2

serviceAccount:
  name: default
  create: false
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/reevo-staging-eks-reevo-be-default-iam-role

containerImage:
  repository: ************.dkr.ecr.us-west-2.amazonaws.com/reevo-ecr/be
  tag: be-eks-v4.4.686
  pullPolicy: IfNotPresent

envVars:
  SALESTECH_BE_HOST: 0.0.0.0
  SALESTECH_BE_PORT: 8080
  SALESTECH_BE_DB_BASE: reevo_main
  SALESTECH_BE_DB_PORT: 5432
  # sensitive info should go into parameter store, and use chamber to load

service:
  enabled: true
  ports:
    app:
      port: 80
      targetPort: 8080
      protocol: TCP

additionalContainerEnv:
- name: SALESTECH_BE_DD_AGENT_HOST
  valueFrom:
    fieldRef:
      fieldPath: status.hostIP
- name: SALESTECH_BE_PUBLIC_FACING_ROUTES_ONLY
  value: "true"

containerResources:
  requests:
    cpu: 1500m
    memory: 10000Mi
  limits:
    cpu: 1500m
    memory: 10000Mi

containerCommand:
- "/bin/chamber"
- "exec"
- "reevo-be-staging"
- "--"
- "/usr/bin/uv"
- "run"
- "python"
- "-m"
- "salestech_be"

# the app has 20*30 seconds max time to startup
# once it starts, it turns over to liveness/readiness probes
# if failed, it's subject to k8s pod restart
# preferred way for slower starting pod before liveness kills it too early
startupProbe:
  httpGet:
    path: /api/v1/monitoring/health
    port: 8080
  failureThreshold: 30
  periodSeconds: 20

livenessProbe:
  httpGet:
    path: /api/v1/monitoring/health
    port: 8080
  initialDelaySeconds: 15
  periodSeconds: 15
  timeoutSeconds: 3

readinessProbe:
  httpGet:
    path: /api/v1/monitoring/health
    port: 8080
  initialDelaySeconds: 15
  periodSeconds: 15
  timeoutSeconds: 3

# improve availability during deployment
terminationGracePeriodSeconds: 60
lifecycleHooks:
  enabled: true
  preStop:
    exec:
      command:
      - sleep
      - "30"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: alb
    #Share a single ALB with all Ingress rules with a specific group name
    alb.ingress.kubernetes.io/group.name: reevo-ng-ingress
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/healthcheck-path: /api/v1/monitoring/health
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: "10"
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:************:certificate/2bafceb4-1f70-42ac-9ca2-708ea47c7313
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/target-group-attributes: deregistration_delay.timeout_seconds=30
    external-dns.alpha.kubernetes.io/hostname: api-ng-staging.reevo.ai
  path: /api/v1/webhook
  pathType: Prefix
  servicePort: 80
  hosts:
  - api-ng-staging.reevo.ai

# this secret is managed by terraform
# provides auth for temporal cloud <> api
secrets:
  ca-cert:
    as: volume
    mountPath: /etc/temporal/certs/
    readOnly: true

deploymentStrategy:
  enabled: true
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 50%
    maxUnavailable: 0
