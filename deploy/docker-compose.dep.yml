services:
  salestech_be-db:
    build:
      context: .
      dockerfile: deploy/dev.postgres.Dockerfile
    user: root
    environment:
      POSTGRES_PASSWORD: "salestech_be"
      POSTGRES_USER: "salestech_be"
      POSTGRES_DB: "salestech_be"
    volumes:
    - salestech_be-db-data:/var/lib/postgresql/data
    - .:/app/src/
    restart: always
    ports:
    - "5432:5432"
    healthcheck:
      # make sure the logical replication is enabled before declaring the service healthy
      test: PGPASSWORD=salestech_be psql -h localhost -p 5432 -U salestech_be -d salestech_be -c "show wal_level" | grep logical
      interval: 5s
      timeout: 1s
      retries: 20

  salestech_be-redis:
    image: redis:alpine3.21
    restart: always
    ports:
    - "6379:6379"
    environment:
      ALLOW_EMPTY_PASSWORD: "yes"
    healthcheck:
      test: redis-cli ping
      interval: 5s
      timeout: 1s
      retries: 20

  salestech_be-temporal:
    build:
      context: .
      dockerfile: deploy/dev.temporal.Dockerfile
    restart: always
    ports:
    - "7233:7233"
    - "8233:8233"
    volumes:
    - salestech_be-temporal-data:/data
    healthcheck:
      test: temporal --version
      interval: 5s
      timeout: 1s
      retries: 20

  salestech_be-kafka:
    image: docker.io/bitnami/kafka:3.7.1
    restart: always
    ports:
    - "9092:9092"
    - "9094:9094"
    volumes:
    - salestech_be-kafka-data:/bitnami
    environment:
    - KAFKA_CFG_NODE_ID=0
    - KAFKA_CFG_PROCESS_ROLES=controller,broker
    - KAFKA_CFG_LISTENERS=INTERNAL://:9092,CONTROLLER://:9093,EXTERNAL://:9094
    - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,INTERNAL:PLAINTEXT,EXTERNAL:PLAINTEXT
    - KAFKA_CFG_ADVERTISED_LISTENERS=INTERNAL://salestech_be-kafka:9092,EXTERNAL://localhost:9094
    - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@salestech_be-kafka:9093
    - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=INTERNAL
    - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
    - KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE=true
    healthcheck:
      test: kafka-topics.sh --bootstrap-server localhost:9092 --list || exit 1
      interval: 5s
      timeout: 1s
      retries: 20

  salestech_be-kafka-ui:
    image: ghcr.io/kafbat/kafka-ui:v1.1.0
    restart: always
    ports:
    - "9080:8080"
    environment:
      DYNAMIC_CONFIG_ENABLED: true
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: salestech_be-kafka:9092

  salestech_be-materialize:
    image: materialize/materialized:v0.121.2
    restart: always
    ports:
    - "6875:6875"
    - "6876:6876"
    healthcheck:
      test: psql -hlocalhost -p6875 -c "SELECT mz_version();"
      interval: 5s
      timeout: 1s
      retries: 20

  salestech_be-debezium:
    image: quay.io/debezium/connect:3.1.0.Final
    platform: linux/amd64
    environment:
      BOOTSTRAP_SERVERS: salestech_be-kafka:9092
      GROUP_ID: salestech_be
      CONFIG_STORAGE_TOPIC: my_connect_configs
      OFFSET_STORAGE_TOPIC: my_connect_offsets
      STATUS_STORAGE_TOPIC: my_connect_statuses
      CONNECT_KEY_CONVERTER: org.apache.kafka.connect.json.JsonConverter
      CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE: "false"
      CONNECT_VALUE_CONVERTER: org.apache.kafka.connect.json.JsonConverter
      CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE: "false"
      CONNECT_REST_ADVERTISED_HOST_NAME: salestech_be-debezium
      CONNECT_PRODUCER_MAX_REQUEST_SIZE: 8388608
    restart: always
    ports:
    - "8083:8083"
    depends_on:
    - salestech_be-kafka
    - salestech_be-db
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8083/connectors || exit 1"]
      interval: 5s
      timeout: 1s
      retries: 20

  salestech_be-es:
    build:
      context: .
      dockerfile: deploy/dev.elasticsearch.Dockerfile
    environment:
    - discovery.type=single-node
    - xpack.security.enabled=false
    - network.host=0.0.0.0
    ports:
    - "9200:9200"
    volumes:
    - es_data:/usr/share/elasticsearch/data
    mem_limit: 2g
    healthcheck:
      test: |
        curl -f http://localhost:9200/_cluster/health?wait_for_status=green&timeout=1s || exit 1
      interval: 5s
      timeout: 1s
      retries: 20

  salestech_be-falkordb:
    image: falkordb/falkordb:latest
    restart: always
    ports:
    - "6380:6379"
    - "3010:3000"
    volumes:
    - falkordb_data:/data
    environment:
      ALLOW_EMPTY_PASSWORD: "yes"
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "6379", "ping"]
      interval: 5s
      timeout: 1s
      retries: 20

volumes:
  salestech_be-db-data:
    name: salestech_be-db-data
  salestech_be-temporal-data:
    name: salestech_be-temporal-data
  salestech_be-kafka-data:
    name: salestech_be-kafka-data
  es_data:
    name: es_data
  falkordb_data:
    name: falkordb_data
