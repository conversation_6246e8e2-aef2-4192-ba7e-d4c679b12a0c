apiVersion: batch/v1
kind: Job
metadata:
  name: PLACEHOLDER
  namespace: reevo-be
spec:
  backoffLimit: 0 # no retries
  ttlSecondsAfterFinished: 3600 # auto cleanup after 1h
  template:
    spec:
      containers:
      - name: migration-job
        image: PLACEHOLDER
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 1000Mi
          requests:
            cpu: 50m
            memory: 50Mi
        command:
        - /bin/chamber
        - exec
        - reevo-be-prod
        - --
        - /usr/bin/uv
        - run
        - python
        - -m
        - "alembic"
        - "upgrade"
        - "head"
      restartPolicy: Never
