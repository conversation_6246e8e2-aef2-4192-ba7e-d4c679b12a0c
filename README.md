# salestech_be

![main branch status](https://github.com/ReevoAI/salestech-be/actions/workflows/tests.yml/badge.svg?branch=main)

Reevo Salestech Core Backend Service.


## Quick Start

1. Set up your local env by following the AWS, Docker, and Dev Setup sections below
2. Validate the project by running tests and ensuring all of them pass
3. (Optional) Run the project locally and spin up the FE project [reevo-webapp](https://github.com/ReevoAI/reevo-webapp) pointing to the local instance you just ran
4. Work on commits, validate locally by running tests and `pre-commit` checks, and publish to GitHub

Check [Makefile](Makefile) for commonly used commands when working on this project.

## AWS CLI

The Docker (see next section) actions we use depend on `aws ecr`. It has to be **installed and set up before the first use**:

### Step 1. Install AWS CLI

Using homebrew: `brew install awscli`

### Step 2. Set up credentials

Log into your AWS console, go to [security credentials](https://us-east-1.console.aws.amazon.com/iam/home#/users/details/andi?section=security_credentials), and [**create access key**](https://us-east-1.console.aws.amazon.com/iam/home#/users/details/andi/create-access-key).

Then run `aws configure` and enter the access key created above.

Alternatively you might also be able to use other auth methods [recommended by AWS](https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html).

## Docker

This project uses Docker to simplify running of the backend app as well as its dependencies.

### Step 1. Install Docker

Install Docker for your device [here](https://docs.docker.com/get-started/get-docker/)

The commonly used Docker actions are defined as build commands in [Makefile](Makefile).

### Step 2: Install uv

We use uv to manage project dependencies. To install:

```shell
# Install uv
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Step 3. Commonly used Docker actions

```shell
# Start only dependency containers, so you can run app or testing on your host
make docker-start-dep
# to access redis by hostname in local dev env, put salestech_be-redis into /etc/hosts.
# to access kafka by hostname in local dev env, put salestech_be-kafka into /etc/hosts.

# Start all dependencies as well as your app within docker container
make docker-start-all

# Stop dependency containers but don't remove attached named volumes, so when you resume it data is still there
make docker-suspend-dep

# Stop app and dependency containers but don't remove attached named volumes, so when you resume it data is still there
make docker-suspend-all

# Stop all containers and remove any named volumes, so when you restart, there won't be any residue data
make docker-cleanup-all
```

## Dev Setup

This project uses Python as the main language, pyenv to manage Python environments, and uv to manage dependencies.

### Setup Python environment

We use uv and Pyenv together to manage project python dependencies.

```bash
# Verify current python version is set correctly by
python --version # should print 3.12

# Install both prod and dev dependencies
uv sync

# To run the project locally through shell
uv run python -m salestech_be

# Active and de-active the virtual environment
# Add the following command to your shell file
alias env-up="source .venv/bin/activate"  ## activate current environment
alias env-down="env-up && deactivate"     ## deactivate current environment
alias env-remove="rm -rf .venv"           ## remove current environment
```

This will start the server on the configured host.

You can find swagger documentation at `/api/docs`.

You can read more about uv here: https://github.com/astral-sh/uv

### Temporal CLI

We use temporal.io for durable execution (background jobs)

#### Step 1. Install

Using homebrew: `brew install temporal`

NOTE: Temporal UI is run on localhost:8233 and API is localhost:7233

#### Step 2. Run Temporal worker locally

```shell
uv run python -m salestech_be.temporal.worker -t main --main-queue <task_queue_name>
```

task_queue_name can be one of the following:

-   main (default)
-   meeting (for meeting AI generation worker)
-   calendar (for calendar sync worker)

## Local Run and Debugging

### Run tests

#### Step 1. Make sure the dependencies (DB, Async engine) are up and running by executing (if not yet)

```
make docker-start-dep
```

or bootstrapping dependencies manually

#### Step 2. Activate the virtual environment

Run `env-up` to activate the virtual environment. Or run `source .venv/bin/activate` to activate the virtual env if you havent setup the alias.

#### Step 3. Invoke `pytest`

```bash
pytest -vv --record-mode=none .
```

or other test commands like `make pytest` as defined in [Makefile](Makefile).

#### Step 4. (Optional) Stop app and/or dependencies when done or needing to refresh the state of app/dependencies

## Project structure

```bash
$ tree "salestech_be"
salestech_be
├── db  # module contains db configurations
│   ├── dao  # Data Access Objects. Contains different classes to interact with database.
│   └── models  # Package contains different models for ORMs.
├── __main__.py  # Startup script. Starts uvicorn.
├── services  # Package for different external services such as rabbit or redis etc.
├── settings.py  # Main configuration settings for project.
├── static  # Static content.
├── tests  # all tests in this project
│   ├── conftest.py # shared top level conftest
│   ├── api # (test your api here) tests from client perspectives, which use httpx `AsyncClient` to directly hit a local app server.
│   ├── integration # (test your DB repo or service component here) tests internal components of the app, typically need dependencies such as http clients, db connections etc.
│   └── unit # (test your util or DRY code here) tests without any dependencies. such as testing a type conversion.
└── web  # Package contains web server. Handlers, startup config.
    ├── api  # Package with all handlers.
    │   └── router.py  # Main router.
    ├── application.py  # FastAPI application configuration.
    └── lifespan.py  # Contains actions to perform on startup and shutdown.
```

## Environment Variables

**Secret can be obtained from 1password. Search for "Backend Env"**

This application can be started with customized environment variables.

You can create `.env` file in the root directory and place all
environment variables here.

All environment variables should start with "SALESTECH*BE*" prefix.

For example if you see in your "salestech_be/settings.py" a variable named like
`random_parameter`, you should provide the "SALESTECH_BE_RANDOM_PARAMETER"
variable to configure the value. This behaviour can be changed by overriding `env_prefix` property
in `salestech_be.settings.Settings.Config`.

An example of .env file:

```bash
SALESTECH_BE_RELOAD="True"
SALESTECH_BE_PORT="8000"
SALESTECH_BE_ENVIRONMENT="dev"
```

You can read more about BaseSettings class here: https://pydantic-docs.helpmanual.io/usage/settings/

### Non-Local Overriding of Environment Variables

When deployed to prod or dev, settings values like those defined in `salestech_be/settings.py` are overridden by AWS Parameter Store values.

### Docker Engine Configuration

If you are using Docker Desktop, be sure to increase the resources required to bring up all the docker containers of `make docker-start-all`. The default configuration is NOT enough to run all the containers. Go to the Docker Desktop Menu Bar Icon --> Settings --> Resources --> Advanced

-   CPU: 7 (leave some CPU for the host machine)
-   Memory: At least 16 GB (12 GB was not enough)
-   Swap: At least 2 GB

### Verifying that your Docker setup is functional

After running `make docker-start-all`, you should be able to curl the swagger page and also successfully perform a health check.

-   Visit http://localhost:8000/api/docs to see the swagger APIs
-   `curl http://localhost:8000/api/v1/monitoring/health` should return `OK`

## VCR Tests

Tests marked with `@pytest.mark.vcr` use VCR cassettes for recording and replaying network interactions with external services.
By default, the tests are run against the recorded cassettes, meaning that no real network requests are made.
But with flags like `--record-mode=once` or `--record-mode=all`, the tests will make real network requests and record the interactions as cassettes.


```bash
e.g. export SALESTECH_BE_APOLLO_API_KEY=$(aws-vault exec $MYAWSVAULT -- chamber export salestech-prod | jq -r '.salestech_be_apollo_api_key')
```

(Before running the command above, install and setup [aws-vault](https://github.com/99designs/aws-vault) and [chamber](https://github.com/99designs/chamber) if not already. Setup of chamber requires your AWS credentials.)

Then you can run the costly tests with e.g.:

```bash
uv run pytest -vvv  --record-mode=once . -k "test_search_people_usage_rate""
```

## OpenTelemetry

If you want to start your project with OpenTelemetry collector
you can add `-f ./deploy/docker-compose.otlp.yml` to your docker command.

Like this:

```bash
docker-compose -f deploy/docker-compose.yml -f deploy/docker-compose.otlp.yml --project-directory . up
```

This command will start OpenTelemetry collector and jaeger.
After sending a requests you can see traces in jaeger's UI
at http://localhost:16686/.

This docker configuration is not supposed to be used in production.
It's only for demo purpose.

You can read more about OpenTelemetry here: https://opentelemetry.io/

## Pre-commit

To enable pre-commit in your local copy of this repo, simply run inside the project root path:

```bash
pre-commit install
```

pre-commit is very useful to check your code before every `commit`.
It's configured using .pre-commit-config.yaml file.

You can read more about pre-commit here: https://pre-commit.com/

## Database Migration

If you want to migrate your database, you should run following commands:

```bash
# To run all migrations until the migration with revision_id.
alembic upgrade "<revision_id>"

# To perform all pending migrations.
alembic upgrade "head"
```

### Reverting migrations

If you want to revert migrations, you should run:

```bash
# revert all migrations up to: revision_id.
alembic downgrade <revision_id>

# Revert everything.
alembic downgrade base
```

### Migration generation

To generate migrations you should run:

```bash 
# Create a new migration file locally
alembic revision -m "Create account table"

# For automatic change detection
# notice that you need to start the DB docker locally to run this command
alembic revision --autogenerate

# For empty file generation.
alembic revision
```

## Locally debug using PyCharm Community

1. Move / add `.env` file in `salestech-be/salestech_be/web`.
2. <img width="595" alt="Screenshot 2024-05-09 at 10 01 06 AM" src="https://github.com/ReevoAI/salestech-be/assets/*********/32aac9b4-937b-4f9d-9e30-9419428beffe">
