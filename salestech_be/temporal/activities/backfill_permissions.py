from uuid import UUID

from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.organization.service.organization_service import (
        get_organization_service_general,
    )
    from salestech_be.core.user.service.user_service import get_user_service_general
    from salestech_be.temporal.database import get_or_init_db_engine


@activity.defn
@with_tracing
async def backfill_organization_permissions_activity(
    organization_id: UUID,
    admin_user_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    user_service = get_user_service_general(db_engine)
    await user_service.backfill_permissions(
        admin_user_id=admin_user_id,
        organization_id=organization_id,
    )


@activity.defn
@with_tracing
async def backfill_all_permissions_activity() -> None:
    db_engine = await get_or_init_db_engine()
    user_service = get_user_service_general(db_engine)
    organization_service = get_organization_service_general(db_engine)
    organizations = await organization_service.list_all()
    for organization in organizations:
        await user_service.backfill_permissions(
            admin_user_id=organization.created_by_user_id,
            organization_id=organization.id,
        )
