from temporalio import activity, workflow

from salestech_be.core.sequence.service.sequence_enrollment_service import (
    get_sequence_enrollment_service_by_db_engine,
)
from salestech_be.core.sequence.type.sequence_signal import (
    SequenceSignal,
)
from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from uuid import UUID

    from salestech_be.temporal.database import get_or_init_db_engine


@activity.defn
@with_tracing
async def signal_all_enrollments_activity(
    organization_id: UUID,
    sequence_id: UUID,
    signal: SequenceSignal,
) -> None:
    db_engine = await get_or_init_db_engine()
    sequence_enrollment_service = get_sequence_enrollment_service_by_db_engine(
        db_engine
    )
    await sequence_enrollment_service.signal_all_eligible_enrollments_for_sequence(
        organization_id=organization_id,
        sequence_id=sequence_id,
        signal=signal,
    )
