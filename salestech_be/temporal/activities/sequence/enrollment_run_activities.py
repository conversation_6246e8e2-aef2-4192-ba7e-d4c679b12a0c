from uuid import UUID

from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.util.validation import not_none

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.sequence.service.sequence_enrollment_service import (
        get_sequence_enrollment_service_by_db_engine,
    )
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.web.api.sequence.enrollment.schema import (
        ContactForSequenceEnrollment,
    )


@activity.defn
@with_tracing
async def create_sequence_enrollment_run_and_enrollments_from_contacts(
    contacts: list[ContactForSequenceEnrollment],
    sequence_id: UUID,
    bypass_warnings: bool,
    user_id: UUID,
    organization_id: UUID,
    enrollment_run_id: UUID | None = None,
) -> UUID:
    db_engine = await get_or_init_db_engine()
    sequence_enrollment_service = get_sequence_enrollment_service_by_db_engine(
        db_engine
    )
    response = await sequence_enrollment_service.create_sequence_enrollment_run_and_enrollments_from_contacts(
        contacts=contacts,
        sequence_id=sequence_id,
        bypass_warnings=bypass_warnings,
        enrollment_run_id=enrollment_run_id,
        user_id=user_id,
        organization_id=organization_id,
    )
    return not_none(response.enrollment_run_id)


@activity.defn
async def create_sequence_enrollment_run_and_enrollments_from_domain_object_list(
    domain_object_list_id: UUID,
    sequence_id: UUID,
    bypass_warnings: bool,
    user_id: UUID,
    organization_id: UUID,
    enrollment_run_id: UUID | None = None,
) -> UUID:
    db_engine = await get_or_init_db_engine()
    sequence_enrollment_service = get_sequence_enrollment_service_by_db_engine(
        db_engine
    )
    response = await sequence_enrollment_service.create_sequence_enrollment_run_and_enrollments_from_domain_object_list(
        domain_object_list_id=domain_object_list_id,
        sequence_id=sequence_id,
        bypass_warnings=bypass_warnings,
        enrollment_run_id=enrollment_run_id,
        user_id=user_id,
        organization_id=organization_id,
    )
    return not_none(response.enrollment_run_id)
