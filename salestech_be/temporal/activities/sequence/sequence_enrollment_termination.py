from uuid import UUID

from temporalio import activity, workflow

from salestech_be.core.sequence.service.sequence_enrollment_service import (
    get_sequence_enrollment_service_by_db_engine,
)
from salestech_be.core.sequence.type.sequence_enrollment_types import (
    SequenceEnrollmentExitReason,
)
from salestech_be.db.dao.sequence_enrollment_repo import SequenceEnrollmentRepository
from salestech_be.db.models.sequence import SequenceEnrollment, SequenceEnrollmentStatus
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.temporal.database import get_or_init_db_engine


logger = get_logger(__name__)


@activity.defn
@with_tracing
async def end_sequence_enrollments_activity(
    organization_id: UUID,
    sequence_enrollment_ids: list[UUID],
) -> None:
    db_engine = await get_or_init_db_engine()

    sequence_enrollment_repository = SequenceEnrollmentRepository(engine=db_engine)
    sequence_enrollment_service = get_sequence_enrollment_service_by_db_engine(
        db_engine
    )

    for sequence_enrollment_id in sequence_enrollment_ids:
        sequence_enrollment = (
            await sequence_enrollment_repository.find_by_tenanted_primary_key(
                table_model=SequenceEnrollment,
                organization_id=organization_id,
                id=sequence_enrollment_id,
            )
        )
        if not sequence_enrollment:
            logger.warning(f"Sequence enrollment not found: {sequence_enrollment_id}")
            continue

        await sequence_enrollment_service.terminate_sequence_enrollment(
            enrollment=sequence_enrollment,
            terminate_reason=SequenceEnrollmentExitReason.USER_TERMINATED,
            organization_id=organization_id,
            end_enrollment_status=SequenceEnrollmentStatus.REMOVED,
            exited_by_reference_id=None,
            exited_by_reference_id_type=None,
        )


@activity.defn
@with_tracing
async def end_all_sequence_enrollments_activity(
    organization_id: UUID,
    sequence_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()

    sequence_enrollment_repository = SequenceEnrollmentRepository(engine=db_engine)
    sequence_enrollment_service = get_sequence_enrollment_service_by_db_engine(
        db_engine
    )

    sequence_enrollments = await sequence_enrollment_repository.find_all_sequence_enrollments_by_sequence_id_and_organization_id(
        organization_id=organization_id,
        sequence_id=sequence_id,
    )

    for status in [
        SequenceEnrollmentStatus.ACTIVE,
        SequenceEnrollmentStatus.INACTIVE,
        SequenceEnrollmentStatus.PENDING,
    ]:
        sequence_enrollments = await sequence_enrollment_repository.find_all_sequence_enrollments_by_sequence_id_and_status(
            organization_id=organization_id,
            sequence_id=sequence_id,
            status=status,
        )

        for sequence_enrollment in sequence_enrollments:
            await sequence_enrollment_service.terminate_sequence_enrollment(
                enrollment=sequence_enrollment,
                terminate_reason=SequenceEnrollmentExitReason.USER_TERMINATED,
                organization_id=organization_id,
                end_enrollment_status=SequenceEnrollmentStatus.REMOVED,
                exited_by_reference_id=None,
                exited_by_reference_id_type=None,
            )
