from typing import assert_never
from uuid import UUID

from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.temporal.database import get_or_init_db_engine

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.email.type.email import EmailAccountArchiveSequenceHandling
    from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


@activity.defn
@with_tracing
async def handle_email_account_remove(
    organization_id: UUID,
    user_id: UUID,
    email_accounts: dict[UUID, EmailAccountArchiveSequenceHandling],
) -> None:
    if not email_accounts:
        logger.bind(organization_id=organization_id).warning(
            "No email accounts to handle"
        )
        return

    db_engine = await get_or_init_db_engine()

    from salestech_be.core.sequence.service.sequence_enrollment_callback_service import (
        get_sequence_enrollment_callback_service_by_db_engine,
    )

    sequence_callback_service = get_sequence_enrollment_callback_service_by_db_engine(
        db_engine
    )

    for (
        email_account_id,
        email_account_archive_sequence_handling,
    ) in email_accounts.items():
        match email_account_archive_sequence_handling:
            case EmailAccountArchiveSequenceHandling.STOP:
                await sequence_callback_service.stop_enrollments_for_email_account_id(
                    email_account_id=email_account_id,
                    organization_id=organization_id,
                )
            case EmailAccountArchiveSequenceHandling.CONTINUE:
                await sequence_callback_service.switch_enrollments_email_account(
                    email_account_id=email_account_id,
                    organization_id=organization_id,
                )
            case EmailAccountArchiveSequenceHandling.RESTART:
                await sequence_callback_service.restart_enrollments_for_email_account(
                    email_account_id=email_account_id,
                    organization_id=organization_id,
                )
            case _ as never:
                assert_never(never)
