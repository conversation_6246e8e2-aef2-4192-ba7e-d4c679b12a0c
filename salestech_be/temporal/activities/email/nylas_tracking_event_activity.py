from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.web.api.email.common.email_webhook_service import (
        get_email_webhook_service_by_db_engine,
    )
    from salestech_be.web.api.webhook.schema import (
        NylasWebhookRequest,
        NylasWebhookType,
    )

logger = get_logger(__name__)


@activity.defn
@with_tracing
async def handle_nylas_tracking_event_activity(
    nylas_webhook_type: NylasWebhookType, webhook_request: NylasWebhookRequest
) -> None:
    db_engine = await get_or_init_db_engine()
    email_webhook_service = get_email_webhook_service_by_db_engine(db_engine=db_engine)
    match nylas_webhook_type:
        case (
            NylasWebhookType.MESSAGE_OPENED
            | NylasWebhookType.MESSAGE_LINK_CLICKED
            | NylasWebhookType.THREAD_REPLIED
        ):
            await email_webhook_service.handle_nylas_tracking_event(
                nylas_webhook_type=nylas_webhook_type, webhook_request=webhook_request
            )
        case NylasWebhookType.MESSAGE_BOUNCE_DETECTED:
            await email_webhook_service.handle_nylas_message_bounced_event(
                webhook_request=webhook_request
            )
        case _:
            logger.bind(
                nylas_webhook_type=nylas_webhook_type,
                webhook_request=webhook_request,
            ).warning(
                "[handle_nylas_tracking_event_activity] unknown event type",
            )

    logger.bind(
        nylas_webhook_type=nylas_webhook_type,
        webhook_request=webhook_request,
    ).info(
        "[handle_nylas_tracking_event_activity] success",
    )
