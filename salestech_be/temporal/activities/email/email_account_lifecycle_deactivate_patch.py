from uuid import UUID

from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.email.account.types import WarmupConfig
    from salestech_be.temporal.database import get_or_init_db_engine


@activity.defn
@with_tracing
async def update_warmup_campaign(
    user_id: UUID,
    organization_id: UUID,
    warmup_configs: dict[UUID, WarmupConfig],
) -> None:
    db_engine = await get_or_init_db_engine()
    from salestech_be.core.email.warmup.service import (
        get_mailivery_warmup_service_by_db_engine,
    )

    mailivery_warmup_service = get_mailivery_warmup_service_by_db_engine(db_engine)
    await mailivery_warmup_service.update_campaign(
        user_id=user_id,
        organization_id=organization_id,
        warmup_configs=warmup_configs,
    )
