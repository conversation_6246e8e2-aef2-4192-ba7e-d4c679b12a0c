from typing import Any
from uuid import UUID

from pydantic import BaseModel
from temporalio import activity, workflow

from salestech_be.db.dto.email_dto import GlobalEmailDto
from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.common.error_code import ErrorCode
    from salestech_be.common.exception.exception import (
        ErrorDetails,
        InvalidArgumentError,
        ResourceNotFoundError,
    )
    from salestech_be.core.email.activity.email_activity_service import (
        get_email_activity_service_by_db_engine,
    )
    from salestech_be.core.email.service.message_service import (
        get_message_service_by_db_engine,
    )
    from salestech_be.core.email.template.email_template_query_service import (
        get_email_template_query_service_from_engine,
    )
    from salestech_be.core.email.template.email_template_service import (
        get_email_template_service_from_engine,
    )
    from salestech_be.core.email.type.email import EmailHydratedParticipant, EmailTag
    from salestech_be.db.models.message import Message
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.util.validation import not_none
    from salestech_be.web.api.email.message.schema import SendMessageRequest


logger = get_logger(__name__)


class SendEmailInput(BaseModel):
    user_id: UUID
    organization_id: UUID
    email_template_id: UUID
    send_from: EmailHydratedParticipant
    send_to: EmailHydratedParticipant
    account_ids: list[UUID] | None = None
    pipeline_id: UUID | None = None
    tags: list[EmailTag] | None = None

    def model_post_init(self, __context: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().model_post_init(__context)
        self.send_to.validate_request()

        if not self.send_from.email_account_id:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_REQUEST,
                    details="Send from email account id is required.",
                )
            )

        if not self.send_to.contact_id:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_REQUEST,
                    details="Send to contact id is required.",
                )
            )


class SendEmailResult(BaseModel):
    global_email_dto: GlobalEmailDto
    reply_to_db_message: Message | None
    is_failure: bool = False


@activity.defn
@with_tracing
async def send_email(
    data: SendEmailInput,
) -> SendEmailResult:
    """Preview template, create message and send it."""
    logger.bind(
        organization_id=data.organization_id,
        email_template_id=data.email_template_id,
        send_from=data.send_from,
        send_to=data.send_to,
    ).info("activity.send_email started")

    db_engine = await get_or_init_db_engine()
    email_template_service = get_email_template_service_from_engine(db_engine=db_engine)
    email_template_query_service = get_email_template_query_service_from_engine(
        db_engine=db_engine
    )
    message_service = get_message_service_by_db_engine(db_engine=db_engine)

    email_template_list = await email_template_query_service.list_email_templates(
        organization_id=data.organization_id,
        user_id=data.user_id,
        only_include_template_ids={data.email_template_id},
    )
    if not email_template_list:
        raise ResourceNotFoundError(
            additional_error_details=ErrorDetails(
                code=ErrorCode.RESOURCE_NOT_FOUND,
                details="Could not find email template",
            )
        )

    email_template = email_template_list[0]

    preview_email_template_body_response = (
        await email_template_service.preview_email_template_body(
            user_id=data.user_id,
            organization_id=data.organization_id,
            send_from=data.send_from,
            send_to=data.send_to,
            body_html=email_template.body_html,
            subject=email_template.subject if email_template.subject else "",
            include_email_signature=email_template.include_email_signature,
            use_default=False,
        )
    )

    if preview_email_template_body_response.unresolved_variables:
        logger.bind(
            organization_id=data.organization_id,
            email_template_id=data.email_template_id,
            unresolved_variables=preview_email_template_body_response.unresolved_variables,
        ).error("Could not resolve variables")

        raise ValueError("Could not resolve variables")

    account_ids = [
        participant.account_id
        for participant in [data.send_from, data.send_to]
        if participant.account_id
    ]
    send_message_request = SendMessageRequest(
        subject=not_none(preview_email_template_body_response.subject),
        send_from=[data.send_from],
        to=[data.send_to],
        cc=None,
        bcc=None,
        reply_to=None,
        body_text="",
        body_html=preview_email_template_body_response.body_html,
        reply_to_message_id=None,
        send_at=None,
        use_draft=False,
        attachment_ids=email_template.attachment_ids,
        account_ids=data.account_ids or (account_ids if account_ids else None),
        pipeline_id=data.pipeline_id,
        email_template_id=data.email_template_id,
        tags=data.tags,
    )

    # Create message in DB
    (
        global_email_dto,
        reply_to_db_message,
    ) = await message_service.create_message_and_thread(
        organization_id=data.organization_id,
        send_message_request=send_message_request,
    )

    # Send message
    sent_global_email_dto = await message_service.send_email_dto(
        organization_id=data.organization_id,
        global_email_dto=global_email_dto,
        reply_to_db_message=reply_to_db_message,
    )

    return SendEmailResult(
        global_email_dto=sent_global_email_dto,
        reply_to_db_message=reply_to_db_message,
        is_failure=False,
    )


@activity.defn
@with_tracing
async def persist_email_activity(
    data: SendEmailInput,
    send_email_result: SendEmailResult,
) -> None:
    """Persist the email activity record."""
    logger.bind(
        organization_id=data.organization_id,
        email_template_id=data.email_template_id,
        message_id=send_email_result.global_email_dto.message_dtos[0].message.id,
        is_failure=send_email_result.is_failure,
    ).info("activity.persist_email_activity started")

    db_engine = await get_or_init_db_engine()
    email_activity_service = get_email_activity_service_by_db_engine(
        db_engine=db_engine
    )
    global_email_dto = send_email_result.global_email_dto
    global_thread_id = global_email_dto.global_thread_id
    if not global_thread_id:
        logger.bind(
            organization_id=data.organization_id,
            email_template_id=data.email_template_id,
            message_id=send_email_result.global_email_dto.message_dtos[0].message.id,
        ).error("Could not find global thread id")
        raise ValueError("Could not find global thread id")
    await email_activity_service.create_activities_for_global_messages(
        global_thread_id=global_thread_id,
        global_message_and_message_mappings=global_email_dto.new_global_message_mappings,
    )
