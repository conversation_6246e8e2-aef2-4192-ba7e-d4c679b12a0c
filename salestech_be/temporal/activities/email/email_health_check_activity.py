from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from uuid import UUID

    from salestech_be.core.email.account.email_account_health_service import (
        get_email_account_health_service_by_db_engine,
    )
    from salestech_be.temporal.database import get_or_init_db_engine


@activity.defn
@with_tracing
async def email_health_check(organization_id: UUID) -> None:
    db_engine = await get_or_init_db_engine()
    email_account_health_service = get_email_account_health_service_by_db_engine(
        db_engine
    )
    await email_account_health_service.sync_email_account_health(
        organization_id=organization_id
    )
