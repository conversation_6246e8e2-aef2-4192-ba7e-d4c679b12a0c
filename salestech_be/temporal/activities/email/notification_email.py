from dataclasses import dataclass
from typing import Any

from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from sendgrid import Email, Mail, Subject
    from sendgrid.helpers.mail import Personalization, To

    from salestech_be.core.email.render.template_render_service import (
        EmailTemplateType,
        get_email_template_render_service,
    )
    from salestech_be.integrations.sendgrid.common.mail_client import SendGridMailClient
    from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


@dataclass
class SendNotificationEmailInput:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    subject: str
    template_type: EmailTemplateType
    variables: dict[str, Any]  # type: ignore[explicit-any] # TODO: fix-any-annotation
    receivers: list[str]
    sender_name: str
    sender_email: str
    reply_to_email: str | None = None
    use_bcc: bool = False  # Whether to use BCC for multiple recipients


@activity.defn
@with_tracing
async def send_notification_email(
    data: SendNotificationEmailInput,
) -> None:
    """Send notification email to recipients"""
    logger.bind(
        subject=data.subject,
        template_type=data.template_type,
        variables=data.variables,
        receivers=data.receivers,
        sender_name=data.sender_name,
    ).info("Starting send_notification_email activity with subject")

    if not data.receivers:
        logger.info("No recipients to send email to")
        return

    email_template_render_service = get_email_template_render_service()
    body = email_template_render_service.render_template(
        template_type=data.template_type,
        context=data.variables,
    )

    email_client = SendGridMailClient()

    if data.use_bcc and len(data.receivers) > 1:
        # Use separate personalization for each recipient
        mail_message = Mail(
            from_email=Email(name=data.sender_name, email=data.sender_email),
            subject=Subject(data.subject),
            html_content=body,
        )
        # Add each recipient in their own personalization
        for email in data.receivers:
            personalization = Personalization()
            personalization.add_to(To(email=email))
            mail_message.add_personalization(personalization)
    else:
        # Single recipient or no BCC requested
        mail_message = Mail(
            to_emails=[To(email=email) for email in data.receivers],
            from_email=Email(name=data.sender_name, email=data.sender_email),
            subject=Subject(data.subject),
            html_content=body,
        )

    if data.reply_to_email:
        mail_message.reply_to = Email(email=data.reply_to_email)

    try:
        await email_client.send_mail(mail_message=mail_message)
        logger.bind(
            subject=data.subject,
            template_type=data.template_type,
            variables=data.variables,
            receivers=data.receivers,
            sender_name=data.sender_name,
        ).info("Successfully sent notification email")
    except Exception as e:
        logger.bind(
            subject=data.subject,
            template_type=data.template_type,
            variables=data.variables,
            receivers=data.receivers,
            sender_name=data.sender_name,
        ).error(
            "Failed to send notification email",
            exec=e,
        )
        raise
