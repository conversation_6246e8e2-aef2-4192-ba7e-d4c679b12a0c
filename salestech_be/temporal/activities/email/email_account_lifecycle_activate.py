from uuid import UUID

from temporalio import activity, workflow

from salestech_be.core.email.warmup.schema import CampaignResponse
from salestech_be.db.models.email_account import EmailAccount
from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.email.account.types import CreateWarmUpRequest
    from salestech_be.core.email.warmup.service import (
        get_mailivery_warmup_service_by_db_engine,
    )
    from salestech_be.db.models.email_account_warm_up import MailboxWarmUpStatus
    from salestech_be.temporal.database import get_or_init_db_engine


@activity.defn
@with_tracing
async def start_warmup_campaign(
    create_warmup_request: CreateWarmUpRequest,
    user_id: UUID,
    organization_id: UUID,
) -> CampaignResponse:
    db_engine = await get_or_init_db_engine()
    mailivery_warmup_service = get_mailivery_warmup_service_by_db_engine(db_engine)
    return await mailivery_warmup_service.create_warm_up_job(
        request=create_warmup_request,
        user_id=user_id,
        organization_id=organization_id,
    )


@activity.defn
@with_tracing
async def get_and_persist_mailbox_credentials(
    email_account_id: UUID,
    organization_id: UUID,
) -> EmailAccount:
    db_engine = await get_or_init_db_engine()
    from salestech_be.core.email.account.service_v2 import (
        get_email_account_service_v2_by_db_engine,
    )

    email_account_service_v2 = get_email_account_service_v2_by_db_engine(db_engine)

    return await email_account_service_v2.get_and_persist_mailbox_credentials(
        email_account_id=email_account_id,
        organization_id=organization_id,
    )


@activity.defn
@with_tracing
async def update_warmup_campaign_status_to_completed(
    email_account_ids: list[UUID],
    organization_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    mailivery_warmup_service = get_mailivery_warmup_service_by_db_engine(db_engine)
    await mailivery_warmup_service.update_warmup_campaign_status_by_email_account_ids(
        email_account_ids=email_account_ids,
        organization_id=organization_id,
        status=MailboxWarmUpStatus.COMPLETED,
    )
