from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.email.outbound_domain.service import (
        get_outbound_domain_service_general,
    )
    from salestech_be.core.email.outbound_domain.types_v2 import (
        DomainPurchaseWorkflowInput,
        OutboundDomainV2,
    )
    from salestech_be.db.models.outbound import OutboundDomain
    from salestech_be.temporal.database import get_or_init_db_engine


@activity.defn
@with_tracing
async def purchase_domains(
    domain_purchase_input: DomainPurchaseWorkflowInput,
) -> OutboundDomain | None:
    db_engine = await get_or_init_db_engine()
    outbound_domain_service = get_outbound_domain_service_general(db_engine)
    return await outbound_domain_service.purchase_and_process_domains(
        request=domain_purchase_input.request,
        user_id=domain_purchase_input.user_id,
        organization_id=domain_purchase_input.organization_id,
        purchasable_domains=domain_purchase_input.purchasable_domains,
    )


@activity.defn
@with_tracing
async def perform_domain_health_checks(
    outbound_domain: OutboundDomain,
) -> OutboundDomainV2:
    db_engine = await get_or_init_db_engine()
    outbound_domain_service = get_outbound_domain_service_general(db_engine)

    return await outbound_domain_service.perform_domain_health_checks(outbound_domain)
