from uuid import UUID

from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.email.service.imap_syncing_service import ImapSyncingService
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.database import get_or_init_db_engine

logger = get_logger()


@activity.defn
@with_tracing
async def sync_imap_activity(email_account_id: UUID) -> None:
    db_engine = await get_or_init_db_engine()
    imap_syncing_service = ImapSyncingService(
        db_engine=db_engine,
    )
    await imap_syncing_service.sync(email_account_id=email_account_id)
    logger.bind(email_account_id=email_account_id).info("[imap activity] success")
