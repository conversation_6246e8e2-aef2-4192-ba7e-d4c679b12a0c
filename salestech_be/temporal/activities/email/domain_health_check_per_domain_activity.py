from temporalio import activity, workflow

from salestech_be.ree_logging import get_logger
from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from uuid import UUID

    from salestech_be.core.email.outbound_domain.dns_check_service import (
        get_dns_check_service,
    )
    from salestech_be.temporal.database import get_or_init_db_engine
logger = get_logger(__name__)


@activity.defn
@with_tracing
async def domain_health_check_per_domain(
    domain_id: UUID, organization_id: UUID
) -> None:
    db_engine = await get_or_init_db_engine()
    dns_check_service = get_dns_check_service(db_engine)
    await dns_check_service.check_domain_health_per_domain(
        domain_id=domain_id, organization_id=organization_id
    )
