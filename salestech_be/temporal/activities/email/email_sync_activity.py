from collections import defaultdict
from datetime import UTC, datetime
from http import HTTPStatus
from uuid import UUID

from temporalio import activity, workflow

from salestech_be.core.ai.event_handlers.stage_criteria import (
    start_stage_criteria_workflow,
)
from salestech_be.core.ff.feature_flag_service import get_feature_flag_service
from salestech_be.core.ff.types import FeatureFlagRequest
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaExtractionSourceObjectId,
    CriteriaExtractionSourceObjectType,
)
from salestech_be.db.models.user_integration import IntegrationProvider
from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from icalendar import Calendar

    from salestech_be.common.exception.exception import ExternalServiceError
    from salestech_be.core.ai.event_handlers.intel import start_intel_workflow
    from salestech_be.core.ai.event_handlers.pipeline_intel import (
        pipeline_intel_handler,
    )
    from salestech_be.core.ai.workflows.schema import IntelTriggerObjectType
    from salestech_be.core.calendar.user_calendar_common_service import (
        get_user_calendar_common_service_by_db_engine,
    )
    from salestech_be.core.email.activity.email_activity_service import (
        get_email_activity_service_by_db_engine,
    )
    from salestech_be.core.email.global_email.global_thread_service import (
        get_global_thread_service_by_db_engine,
    )
    from salestech_be.core.email.insight.email_insight_service import (
        get_email_insight_service_by_db,
    )
    from salestech_be.core.email.insight.events import NewEmailEvent
    from salestech_be.core.organization.service.organization_service import (
        get_organization_service_general,
    )
    from salestech_be.db.dao.message_metadata_repository import (
        MessageMetadataRepository,
    )
    from salestech_be.db.dao.thread_repository import ThreadRepository
    from salestech_be.db.dao.user_calendar_repository import UserCalendarRepository
    from salestech_be.db.dbengine.core import DatabaseEngine
    from salestech_be.db.models.message_metadata import MessageClassification
    from salestech_be.db.models.user_integration import IntegrationType
    from salestech_be.db.models.user_integration_connector import (
        IntegrationConnectorProvider,
    )
    from salestech_be.ree_logging import get_logger
    from salestech_be.settings import settings
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.util.validation import not_none
    from salestech_be.web.api.email.common.email_sync_service import (
        EmailSyncService,
        get_email_sync_service_by_db_engine,
    )

logger = get_logger()

# Constants
MINIMUM_MESSAGES_FOR_TASK_CREATION = 2
EVERYTIME_SYNC_COUNT = 20
INITIAL_BACKFILL_EMAIL_COUNT = 100


# Activity functions
@activity.defn
@with_tracing
async def sync_email_activity(
    organization_id: UUID,
    email_account_id: UUID,
    initial_sync: bool = False,
) -> list[UUID]:
    db_engine = await get_or_init_db_engine()
    email_sync_service = get_email_sync_service_by_db_engine(db_engine=db_engine)
    synced_email_dtos = await email_sync_service.sync_threads(
        organization_id=organization_id,
        email_account_id=email_account_id,
        sync_to=datetime.now(UTC),
        total=INITIAL_BACKFILL_EMAIL_COUNT if initial_sync else EVERYTIME_SYNC_COUNT,
    )
    return [email_dto.thread.id for email_dto in synced_email_dtos]


@activity.defn
@with_tracing
async def sync_nylas_thread_activity(
    nylas_grant_id: str,
    nylas_thread_id: str,
) -> dict[str, list[UUID]]:
    db_engine = await get_or_init_db_engine()
    email_sync_service = get_email_sync_service_by_db_engine(db_engine=db_engine)
    results: dict[str, list[UUID]] = defaultdict(list)

    if not (
        user_integration_dtos
        := await email_sync_service.email_account_service.user_integration_repo.find_user_integrations_by_connector_external_id(
            connector_external_id=nylas_grant_id,
            connector_provider=IntegrationConnectorProvider.NYLAS,
            integration_type=IntegrationType.EMAIL,
        )
    ):
        logger.bind(
            nylas_thread_id=nylas_thread_id, nylas_grant_id=nylas_grant_id
        ).warning("[sync_nylas_thread_activity] User Integration not found")
        return results

    # There maybe multiple user integration with same Nylas grant id, such as email
    # alias, which in thread table is with different email_account. So we do for loop
    # and sync for each email account.
    for user_int in user_integration_dtos:
        try:
            nylas_thread = await email_sync_service.async_nylas_client.get_thread_by_id(
                grant_id=nylas_grant_id,
                nylas_thread_id=nylas_thread_id,
            )
        except ExternalServiceError as e:
            if e.get_original_http_code() == HTTPStatus.NOT_FOUND:
                logger.bind(
                    grant_id=nylas_grant_id,
                    thread_id=nylas_thread_id,
                    email_account_id=user_int.user_integration.email_account_id,
                ).warning("Thread not found, skipping sync")
                continue  # Skip this integration if thread not found
            raise  # Re-raise other errors
        email_dtos = await email_sync_service.insert_thread_and_sync_messages(
            nylas_thread_list=[nylas_thread],
            email_account_id=not_none(user_int.user_integration.email_account_id),
            organization_id=user_int.user_integration.organization_id,
            grant_id=nylas_grant_id,
            integration_provider=IntegrationProvider.GOOGLE,
            should_create_records=True,
        )
        for email_dto in email_dtos:
            results[str(email_dto.thread.organization_id)].append(email_dto.thread.id)
    return results


@activity.defn
@with_tracing
async def construct_global_thread_activity(
    thread_id_list: list[UUID],
    organization_id: UUID,
) -> list[UUID]:
    db_engine = await get_or_init_db_engine()
    email_sync_service = get_email_sync_service_by_db_engine(db_engine=db_engine)
    global_thread_service = get_global_thread_service_by_db_engine(db_engine=db_engine)
    email_activity_service = get_email_activity_service_by_db_engine(
        db_engine=db_engine
    )
    results: list[UUID] = []

    email_dtos = await email_sync_service.list_email_dtos_by_thread_ids(
        thread_ids=thread_id_list,
        organization_id=organization_id,
        include_attachment=False,
    )
    for email_dto in email_dtos:
        account_ids = {
            participant.account_id
            for participant in email_dto.thread.participants
            if participant.account_id
        }
        (
            global_thread,
            global_message_and_message_map,
        ) = await global_thread_service.construct_global_thread_and_messages(
            organization_id=organization_id,
            email_dto=email_dto,
            account_ids=list(account_ids) if account_ids else None,
        )
        new_global_message_and_message_map = {
            global_message_id: message
            for global_message_id, (
                message,
                is_new,
            ) in global_message_and_message_map.items()
            if is_new
        }
        # Only record activities for new global message
        if new_global_message_and_message_map:
            await email_activity_service.create_activities_for_global_messages(
                global_thread_id=global_thread.id,
                global_message_and_message_mappings=new_global_message_and_message_map,
            )
        results.append(global_thread.id)
    return results


@activity.defn
@with_tracing
async def generate_global_thread_insight_activity(
    global_thread_id_list: list[UUID],
    organization_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    global_thread_service = get_global_thread_service_by_db_engine(db_engine)
    email_insight_service = get_email_insight_service_by_db(db_engine=db_engine)
    thread_repository = ThreadRepository(engine=db_engine)
    message_metadata_repo = MessageMetadataRepository(engine=db_engine)

    global_thread_list = await global_thread_service.list_global_thread_by_ids(
        global_thread_ids=global_thread_id_list, organization_id=organization_id
    )
    for global_thread in global_thread_list:
        # 1. Generate insight for global thread
        await email_insight_service.generate_insight_for_global_thread(
            organization_id=organization_id,
            global_thread=global_thread,
        )

        # 2. Create tasks through intel workflow
        messages = await thread_repository.list_messages_by_global_thread_id(
            global_thread_id=global_thread.id,
            organization_id=organization_id,
        )

        if (
            len(messages) < MINIMUM_MESSAGES_FOR_TASK_CREATION
            and messages
            and messages[0]
            and messages[0].send_from
            and messages[0].send_from[0]
            and not messages[0].send_from[0].email_account_id
        ):
            message_metadata = (
                await message_metadata_repo.get_latest_message_metadata_by_message_id(
                    organization_id=organization_id,
                    message_id=messages[0].id,
                )
            )
            if (
                message_metadata is None
                or message_metadata.classification != MessageClassification.SALES
            ):
                logger.bind(global_thread_id=global_thread.id).info(
                    "[generate_global_thread_insight_activity] Skip create task since message is not a sales related email"
                )
                continue

        pipeline_id = global_thread.pipeline_id
        account_id = global_thread.account_ids[0] if global_thread.account_ids else None
        if pipeline_id and account_id:
            logger.bind(global_thread_id=global_thread.id).info(
                "[generate_global_thread_insight_activity] Start pipeline intel handler"
            )
            await pipeline_intel_handler(
                NewEmailEvent(
                    global_thread_id=global_thread.id,
                    organization_id=organization_id,
                    pipeline_id=pipeline_id,
                    account_id=account_id,
                )
            )

        await start_intel_workflow(
            organization_id=organization_id,
            object_id=global_thread.id,
            object_type=IntelTriggerObjectType.GLOBAL_THREAD,
            account_ids=global_thread.account_ids
            if global_thread.account_ids and len(global_thread.account_ids) > 0
            else None,
            pipeline_id=pipeline_id,
        )

        ff_service = get_feature_flag_service()
        is_stage_criteria_enabled = await ff_service.is_enabled(
            request=FeatureFlagRequest(
                flag_key="enable-stage-criteria-generation",
                organization_id=organization_id,
            ),
        )

        if pipeline_id and is_stage_criteria_enabled:
            await start_stage_criteria_workflow(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                user_id=UUID(settings.intel_hardcoded_user_id),
                source_object=CriteriaExtractionSourceObjectId(
                    object_type=CriteriaExtractionSourceObjectType.EMAIL,
                    object_id=global_thread.id,
                ),
            )


@activity.defn
@with_tracing
async def generate_activity_insight_activity(
    global_thread_id_list: list[UUID],
    organization_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    global_thread_service = get_global_thread_service_by_db_engine(db_engine)
    email_insight_service = get_email_insight_service_by_db(db_engine=db_engine)

    global_thread_list = await global_thread_service.list_global_thread_by_ids(
        global_thread_ids=global_thread_id_list, organization_id=organization_id
    )

    for global_thread in global_thread_list:
        if not global_thread.pipeline_id:
            logger.bind(global_thread_id=global_thread.id).info(
                "[generate_global_thread_insight_activity] Skip due to no pipeline id"
            )
            continue

        await email_insight_service.trigger_activity_intelligence(
            pipeline_id=global_thread.pipeline_id,
            organization_id=organization_id,
            global_thread_id=global_thread.id,
        )


@activity.defn
@with_tracing
async def create_bot_assistant_activity(
    nylas_grant_id: str,
    nylas_thread_id: str,
) -> None:
    try:
        logger.bind(grant_id=nylas_grant_id).info("Creating bot assistant via email")
        db_engine = await get_or_init_db_engine()
        email_sync_service = get_email_sync_service_by_db_engine(db_engine=db_engine)
        organization_service = get_organization_service_general(db_engine=db_engine)

        # Fetch thread to get message IDs
        nylas_thread = await email_sync_service.async_nylas_client.get_thread_by_id(
            grant_id=nylas_grant_id,
            nylas_thread_id=nylas_thread_id,
        )

        # Handle case where message_ids might be None
        message_ids = nylas_thread.message_ids or []

        # Process only the latest message if available
        if message_ids:
            # Get the latest message (last in the list)
            message_id = message_ids[-1]
            try:
                message = await email_sync_service.async_nylas_client.get_message_by_id(
                    grant_id=nylas_grant_id,
                    nylas_message_id=message_id,
                    include_headers=True,
                )

                logger.bind(
                    grant_id=nylas_grant_id,
                    message=message,
                ).info("Bot email content")

                # Check if any recipient is a bot assistant email
                bot_email = _extract_bot_assistant_email(message)
                logger.bind(grant_id=nylas_grant_id, bot_email=bot_email).info(
                    "Bot email"
                )
                if bot_email:
                    # Check if this bot assistant email is registered to an organization
                    organization_info = (
                        await organization_service.find_by_bot_assistant(bot_email)
                    )

                    if organization_info:
                        # Process any attachment data
                        ical_uid = await _extract_email_invite_uid(
                            email_sync_service=email_sync_service,
                            grant_id=nylas_grant_id,
                            message_id=message_id,
                            message=message,
                        )

                        # Log the calendar data if found
                        if ical_uid:
                            logger.bind(
                                grant_id=nylas_grant_id, ical_uid=ical_uid
                            ).info("Calendar invite found")
                            # Create meeting from invitation
                            await _create_meeting_from_invitation(
                                db_engine=db_engine,
                                ical_uid=ical_uid,
                                organization_id=organization_info.organization_id,
                            )
                    else:
                        logger.bind(grant_id=nylas_grant_id, email=bot_email).warning(
                            "Email matches bot assistant pattern but not registered to any organization"
                        )
            except Exception as e:
                logger.bind(
                    grant_id=nylas_grant_id,
                    message_id=message_id,
                    error=str(e),
                ).error("Failed to process message for bot assistant", exc_info=e)
    except Exception as e:
        # Log error but don't raise since this is non-critical functionality
        logger.bind(
            grant_id=nylas_grant_id,
            nylas_thread_id=nylas_thread_id,
            error=str(e),
        ).error("Failed to process bot assistant thread", exc_info=e)


async def _create_meeting_from_invitation(
    db_engine: DatabaseEngine,
    ical_uid: str,
    organization_id: UUID,
) -> None:
    """
    Create a meeting from the extracted calendar invitation UID.
    """
    try:
        # Log the attempt to create meeting
        logger.bind(
            organization_id=organization_id,
            invitation_uid=ical_uid,
        ).info("Attempting to create meeting from calendar invitation")

        # Get user calendar repository
        user_cal_repo = UserCalendarRepository(engine=db_engine)

        # Find the confirmed calendar event with this ical_uid
        calendar_event = await user_cal_repo.find_confirmed_calendar_event_by_ical_uid(
            ical_uid=ical_uid, organization_id=organization_id
        )

        if not calendar_event:
            logger.bind(
                organization_id=organization_id,
                invitation_uid=ical_uid,
            ).info("No matching calendar event found, skipping meeting creation")
            return

        # Check if there's already a meeting for this calendar event
        if calendar_event.meeting_id:
            logger.bind(
                organization_id=organization_id,
                invitation_uid=ical_uid,
                meeting_id=calendar_event.meeting_id,
            ).info("Meeting already exists for this calendar event")
            return

        # Get calendar common service to handle meeting creation
        calendar_common_service = get_user_calendar_common_service_by_db_engine(
            db_engine
        )

        # Use the existing service to create meeting and update calendar event in one step
        await calendar_common_service.create_meeting_for_event_async(
            user_id=calendar_event.user_id,
            organization_id=organization_id,
            calendar_event_id=calendar_event.id,
            schedule_bot=True,  # Set to True since this was triggered by bot assistant
            event_schedule_id=None,
            meeting_title=calendar_event.title,
        )

        logger.bind(
            organization_id=organization_id,
            invitation_uid=ical_uid,
            title=calendar_event.title,
            start_time=calendar_event.starts_at,
            end_time=calendar_event.ends_at,
        ).info("Successfully created meeting from invitation")
    except Exception as e:
        logger.bind(
            organization_id=organization_id,
            error=str(e),
        ).error("Failed to create meeting from invitation", exc_info=e)


async def _extract_email_invite_uid(  # noqa: PLR0911
    email_sync_service: EmailSyncService,
    grant_id: str,
    message_id: str,
    message: object,
) -> str | None:
    # Check if message has attachments
    if not (
        hasattr(message, "attachments")
        and message.attachments
        and len(message.attachments) > 0
    ):
        logger.bind(grant_id=grant_id, message=message).info("Not attachments")
        return None

    # Get the first attachment
    first_attachment = message.attachments[0]

    # Only process if it's a calendar invite
    if first_attachment.filename != "invite.ics":
        logger.bind(grant_id=grant_id).info("No Invite")
        return None

    # Download the ics file content
    try:
        ics_content = await email_sync_service.async_nylas_client.download_attachment(
            grant_id=grant_id,
            nylas_attachment_id=first_attachment.id,
            nylas_message_id=message_id,
        )

        # Parse the ics content
        try:
            cal = Calendar.from_ical(ics_content)

            # Extract only the needed calendar event details: uid
            for component in cal.walk():
                if component.name == "VEVENT":
                    # Get required field
                    uid = component.get("uid")

                    # Verify required field is present
                    if not uid:
                        logger.bind(
                            attachment_id=first_attachment.id,
                        ).error(
                            "Missing required field (uid) in the invite.ics attachment"
                        )
                        return None

                    # Return only the uid value
                    return str(uid)

            # If we didn't find a VEVENT component
            logger.error("No VEVENT component found in the invite.ics attachment")
            return None

        except Exception as e:
            logger.bind(
                error=str(e),
            ).error(
                "Failed to parse invite.ics content",
                exc_info=e,
            )
            return None
    except Exception as e:
        logger.bind(
            attachment_id=first_attachment.id,
            error=str(e),
        ).error(
            "Failed to download invite.ics content",
            exc_info=e,
        )
        return None


def _extract_bot_assistant_email(
    message: object,
) -> str | None:  # Using object instead of Any
    recipients: list[object] = []
    for recipient_list in [
        getattr(message, "to", []),
        getattr(message, "cc", []),
        getattr(message, "bcc", []),
    ]:
        if recipient_list:
            recipients.extend(recipient_list)

    # Check if any recipient matches the bot assistant pattern
    for recipient in recipients:
        email = getattr(recipient, "email", "").lower() if recipient else ""
        # Check if email matches the bot assistant pattern
        if email.startswith(settings.bot_assistant_email_prefix) and email.endswith(
            settings.bot_assistant_email_domain
        ):
            return email

    return None
