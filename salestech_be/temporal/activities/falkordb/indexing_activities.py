"""Temporal activities for FalkorDB indexing."""

import asyncio
import inspect
import time
from collections.abc import <PERSON><PERSON><PERSON>
from typing import TypeVar
from uuid import UUID

from temporalio import activity

from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.common.types import CustomizableDomainModel, DomainModel
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.metadata.types import ContactA<PERSON>unt<PERSON>ole, ContactPipelineRole
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.falkordb.falkordb_client import get_falkordb_client
from salestech_be.falkordb.indexing_lib import get_falkordb_indexing_lib
from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.temporal.database import get_or_init_db_engine

T = TypeVar("T")


# Helper for background heartbeat
def run_with_heartbeat(coro: Awaitable[T], interval: int = 30) -> Awaitable[T]:
    """Run a coroutine and send heartbeats every `interval` seconds while it runs."""

    async def runner() -> T:
        # Ensure we have a coroutine object for create_task
        if inspect.iscoroutine(coro):
            task = asyncio.create_task(coro)
        else:

            async def awaiter() -> T:
                return await coro

            task = asyncio.create_task(awaiter())
        try:
            while not task.done():
                try:
                    await asyncio.wait_for(asyncio.shield(task), timeout=interval)
                except TimeoutError:
                    activity.heartbeat()
            return await task  # type: ignore[no-any-return]
        except asyncio.CancelledError:
            activity.logger.warning("Activity was cancelled during run_with_heartbeat.")
            raise

    return runner()


@activity.defn
@with_tracing
async def list_organization_and_user_ids_activity() -> list[tuple[str, str]]:
    activity_logger = activity.logger
    activity_logger.info("Starting activity: list_organization_and_user_ids_activity")
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro = indexing_lib.get_list_of_all_user_and_org_ids_in_instance()
        user_org_ids = await run_with_heartbeat(coro)
        result = [(str(user_id), str(org_id)) for user_id, org_id in user_org_ids]
        activity_logger.info(
            f"Completed activity: list_organization_and_user_ids_activity. Found {len(result)} pairs."
        )
        return result
    except asyncio.CancelledError:
        activity_logger.warning(
            "list_organization_and_user_ids_activity was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception("Error in list_organization_and_user_ids_activity")
        raise


@activity.defn
@with_tracing
async def index_users_activity(org_id_str: str, user_ids_str: list[str]) -> None:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    user_ids = [UUID(uid) for uid in user_ids_str]
    activity_logger.info(
        f"MATTY INDEXING FRESH Starting activity: index_users_activity for org_id={org_id}"
    )
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro = indexing_lib.index_users(user_ids, org_id)
        await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"Completed activity: index_users_activity for org_id={org_id}"
        )
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to index users: {end_time - start_time} seconds"
        )
    except asyncio.CancelledError:
        activity_logger.warning(
            f"index_users_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(f"Error in index_users_activity for org_id={org_id}")
        raise


@activity.defn
@with_tracing
async def index_all_accounts_in_batches_activity(
    org_id_str: str, offset: int = 0, batch_size: int = 50
) -> None:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    activity_logger.info(
        f"MATTY INDEXING FRESH Starting activity: index_all_accounts_in_batches_activity for org_id={org_id}, offset={offset}, batch_size={batch_size}"
    )
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro = indexing_lib.index_all_accounts_for_organization_in_batches(
            organization_id=org_id, offset=offset, batch_size=batch_size
        )
        await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"Finished indexing accounts for org {org_id}, offset={offset}, batch_size={batch_size}"
        )
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to index accounts: {end_time - start_time} seconds"
        )
    except asyncio.CancelledError:
        activity_logger.warning(
            f"index_all_accounts_in_batches_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(
            f"Error in index_all_accounts_in_batches_activity for org_id={org_id}"
        )
        raise


@activity.defn
@with_tracing
async def index_all_contacts_in_batches_activity(
    org_id_str: str, offset: int = 0, batch_size: int = 50
) -> None:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    activity_logger.info(
        f"MATTY INDEXING FRESH Starting activity: index_all_contacts_in_batches_activity for org_id={org_id}, offset={offset}, batch_size={batch_size}"
    )
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro = indexing_lib.index_all_contacts_for_organization_in_batches(
            organization_id=org_id, offset=offset, batch_size=batch_size
        )
        await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"Finished indexing contacts for org {org_id}, offset={offset}, batch_size={batch_size}"
        )
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to index contacts: {end_time - start_time} seconds"
        )
    except asyncio.CancelledError:
        activity_logger.warning(
            f"index_all_contacts_in_batches_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(
            f"Error in index_all_contacts_in_batches_activity for org_id={org_id}"
        )
        raise


@activity.defn
@with_tracing
async def index_all_pipelines_in_batches_activity(
    org_id_str: str, offset: int = 0, batch_size: int = 50
) -> None:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    activity_logger.info(
        f"MATTY INDEXING FRESH Starting activity: index_all_pipelines_in_batches_activity for org_id={org_id}, offset={offset}, batch_size={batch_size}"
    )
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro = indexing_lib.index_all_pipelines_for_organization_in_batches(
            organization_id=org_id, offset=offset, batch_size=batch_size
        )
        await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"Finished indexing pipelines for org {org_id}, offset={offset}, batch_size={batch_size}"
        )
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to index pipelines: {end_time - start_time} seconds"
        )
    except asyncio.CancelledError:
        activity_logger.warning(
            f"index_all_pipelines_in_batches_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(
            f"Error in index_all_pipelines_in_batches_activity for org_id={org_id}"
        )
        raise


@activity.defn
@with_tracing
async def index_all_contact_pipeline_roles_in_batches_activity(
    org_id_str: str, offset: int = 0, batch_size: int = 50
) -> None:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    activity_logger.info(
        f"MATTY INDEXING FRESH Starting activity: index_all_contact_pipeline_roles_in_batches_activity for org_id={org_id}, offset={offset}, batch_size={batch_size}"
    )
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro = (
            indexing_lib.index_all_contact_pipeline_roles_for_organization_in_batches(
                organization_id=org_id, offset=offset, batch_size=batch_size
            )
        )
        await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"Finished indexing contact pipeline roles for org {org_id}, offset={offset}, batch_size={batch_size}"
        )
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to index contact pipeline roles: {end_time - start_time} seconds"
        )
    except asyncio.CancelledError:
        activity_logger.warning(
            f"index_all_contact_pipeline_roles_in_batches_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(
            f"Error in index_all_contact_pipeline_roles_in_batches_activity for org_id={org_id}"
        )
        raise


@activity.defn
@with_tracing
async def index_all_contact_account_roles_in_batches_activity(
    org_id_str: str, offset: int = 0, batch_size: int = 50
) -> None:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    activity_logger.info(
        f"MATTY INDEXING FRESH Starting activity: index_all_contact_account_roles_in_batches_activity for org_id={org_id}, offset={offset}, batch_size={batch_size}"
    )
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro = indexing_lib.index_all_contact_account_roles_for_organization_in_batches(
            organization_id=org_id, offset=offset, batch_size=batch_size
        )
        await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"Finished indexing contact account roles for org {org_id}, offset={offset}, batch_size={batch_size}"
        )
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to index contact account roles: {end_time - start_time} seconds"
        )
    except asyncio.CancelledError:
        activity_logger.warning(
            f"index_all_contact_account_roles_in_batches_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(
            f"Error in index_all_contact_account_roles_in_batches_activity for org_id={org_id}"
        )
        raise


@activity.defn
@with_tracing
async def index_all_custom_objects_for_organization_in_batches_activity(
    org_id_str: str, offset: int = 0, batch_size: int = 50
) -> None:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    activity_logger.info(
        f"MATTY INDEXING FRESH Starting activity: index_all_custom_objects_for_organization_in_batches_activity for org_id={org_id}, offset={offset}, batch_size={batch_size}"
    )
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro = indexing_lib.index_all_custom_objects_for_organization_in_batches(
            organization_id=org_id, offset=offset, batch_size=batch_size
        )
        await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"Finished indexing custom objects for org {org_id}, offset={offset}, batch_size={batch_size}"
        )
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to index custom objects: {end_time - start_time} seconds"
        )
    except asyncio.CancelledError:
        activity_logger.warning(
            f"index_all_custom_objects_for_organization_in_batches_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(
            f"Error in index_all_custom_objects_for_organization_in_batches_activity for org_id={org_id}"
        )
        raise


@activity.defn
@with_tracing
async def index_organization_relationships_activity_in_batches(
    org_id_str: str, last_relationship_id: UUID | None = None, batch_size: int = 50
) -> None:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    activity_logger.info(
        f"MATTY INDEXING FRESH Starting activity: index_organization_relationships_activity for org_id={org_id}, last_relationship_id={last_relationship_id}, batch_size={batch_size}"
    )
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro = indexing_lib.index_relationships_for_organization_in_batches(
            organization_id=org_id,
            batch_size=batch_size,
            start_after=last_relationship_id,
        )
        await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"Finished indexing relationships for org {org_id}, batch_size={batch_size}"
        )
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to index relationships: {end_time - start_time} seconds"
        )
    except asyncio.CancelledError:
        activity_logger.warning(
            f"index_organization_relationships_activity_in_batches for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(
            f"Error in index_organization_relationships_activity for org_id={org_id}"
        )
        raise


@activity.defn
@with_tracing
async def index_organization_relationships_activity(org_id_str: str) -> None:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    activity_logger.info(
        f"MATTY INDEXING FRESH Starting activity: index_organization_relationships_activity for org_id={org_id}"
    )
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro = indexing_lib.index_relationships_for_organization(organization_id=org_id)
        await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"Completed activity: index_organization_relationships_activity for org_id={org_id}"
        )
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to index relationships: {end_time - start_time} seconds"
        )
    except asyncio.CancelledError:
        activity_logger.warning(
            f"index_organization_relationships_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(
            f"Error in index_organization_relationships_activity for org_id={org_id}"
        )
        raise


# For count/getter activities, a single await is usually fast, but for consistency, use the same pattern
@activity.defn
@with_tracing
async def get_accounts_count_activity(org_id_str: str) -> int:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro: Awaitable[int] = indexing_lib.get_accounts_count(organization_id=org_id)
        count = await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to get accounts count: {end_time - start_time} seconds"
        )
        return int(count)
    except asyncio.CancelledError:
        activity_logger.warning(
            f"get_accounts_count_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(f"Error getting accounts count for org_id={org_id}")
        raise


@activity.defn
@with_tracing
async def get_contacts_count_activity(org_id_str: str) -> int:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro: Awaitable[int] = indexing_lib.get_contacts_count(organization_id=org_id)
        count = await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to get contacts count: {end_time - start_time} seconds"
        )
        return int(count)
    except asyncio.CancelledError:
        activity_logger.warning(
            f"get_contacts_count_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(f"Error getting contacts count for org_id={org_id}")
        raise


@activity.defn
@with_tracing
async def get_pipelines_count_activity(org_id_str: str) -> int:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro: Awaitable[int] = indexing_lib.get_pipelines_count(organization_id=org_id)
        count = await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to get pipelines count: {end_time - start_time} seconds"
        )
        return int(count)
    except asyncio.CancelledError:
        activity_logger.warning(
            f"get_pipelines_count_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(f"Error getting pipelines count for org_id={org_id}")
        raise


@activity.defn
@with_tracing
async def get_contact_pipeline_roles_count_activity(org_id_str: str) -> int:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro: Awaitable[int] = indexing_lib.get_contact_pipeline_roles_count(
            organization_id=org_id
        )
        count = await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to get contact pipeline roles count: {end_time - start_time} seconds"
        )
        return int(count)
    except asyncio.CancelledError:
        activity_logger.warning(
            f"get_contact_pipeline_roles_count_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(
            f"Error getting contact pipeline roles count for org_id={org_id}"
        )
        raise


@activity.defn
@with_tracing
async def get_contact_account_roles_count_activity(
    org_id_str: str, contact_id_str: str | None = None
) -> int:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro: Awaitable[int] = indexing_lib.get_contact_account_roles_count(
            organization_id=org_id
        )
        count = await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to get contact account roles count: {end_time - start_time} seconds"
        )
        return int(count)
    except asyncio.CancelledError:
        activity_logger.warning(
            f"get_contact_account_roles_count_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(
            f"Error getting contact account roles count for org_id={org_id}"
        )
        raise


@activity.defn
@with_tracing
async def get_custom_objects_count_activity(org_id_str: str) -> int:
    activity_logger = activity.logger
    start_time = time.time()
    org_id = UUID(org_id_str)
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro: Awaitable[int] = indexing_lib.get_custom_objects_count(
            organization_id=org_id
        )
        count = await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to get custom objects count: {end_time - start_time} seconds"
        )
        return int(count)
    except asyncio.CancelledError:
        activity_logger.warning(
            f"get_custom_objects_count_activity for org_id={org_id} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(
            f"Error getting custom objects count for org_id={org_id}"
        )
        raise


@activity.defn
@with_tracing
async def get_domain_object_count_falkor_activity(
    org_id_str: str, entity_type: str
) -> int:
    activity_logger = activity.logger
    domain_object_map: dict[str, type[CustomizableDomainModel | DomainModel]] = {
        "accounts": AccountV2,
        "contacts": ContactV2,
        "pipelines": PipelineV2,
        "contact_pipeline_roles": ContactPipelineRole,
        "contact_account_roles": ContactAccountRole,
    }
    if entity_type not in domain_object_map:
        activity_logger.error(
            f"Unknown entity type: {entity_type} in get_domain_object_count_falkor_activity"
        )
        raise ValueError(f"Unknown entity type: {entity_type}")
    domain_object = domain_object_map[entity_type]
    start_time = time.time()
    org_id = UUID(org_id_str)
    try:
        db_engine = await get_or_init_db_engine()
        falkordb_client = get_falkordb_client()
        indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)
        coro: Awaitable[int] = indexing_lib.get_domain_object_count_falkor(
            organization_id=org_id, domain_object=domain_object
        )
        count = await run_with_heartbeat(coro)
        end_time = time.time()
        activity_logger.info(
            f"MATTY INDEXING FRESH Time taken to get domain object count: {end_time - start_time} seconds for domain_object={domain_object.__name__}"
        )
        return int(count)
    except asyncio.CancelledError:
        activity_logger.warning(
            f"get_domain_object_count_falkor_activity for org_id={org_id}, entity_type={entity_type} was cancelled."
        )
        raise
    except Exception:
        activity_logger.exception(
            f"Error getting domain object count for org_id={org_id}, domain_object={domain_object.__name__}"
        )
        raise
