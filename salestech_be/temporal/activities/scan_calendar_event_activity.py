from datetime import UTC, datetime, timedelta
from typing import Any
from uuid import UUID

from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.calendar.user_calendar_sync_service import (
        get_user_calendar_sync_service_by_db_engine,
    )
    from salestech_be.temporal.database import get_or_init_db_engine


# define activity to process calendar event sync task
@activity.defn
@with_tracing
async def process_calendar_event_sync_task(  # type: ignore[explicit-any] # TODO: fix-any-annotation
    user_id: UUID,
    organization_id: UUID,
    calendar_account_id: UUID,
) -> dict[str, Any]:
    try:
        # for regular sync, we sync events from now to 30 days from now
        sync_from = datetime.now(UTC)
        sync_to = datetime.now(UTC) + timedelta(days=30)

        db_engine = await get_or_init_db_engine()
        calendar_event_sync_service = get_user_calendar_sync_service_by_db_engine(
            db_engine=db_engine
        )
        synced_result = await calendar_event_sync_service.process_user_calendar_task(
            user_id=user_id,
            organization_id=organization_id,
            calendar_account_id=calendar_account_id,
            sync_from=sync_from,
            sync_to=sync_to,
        )
        activity.logger.info(
            f"synced calendar events for user: {user_id}/{organization_id} from {sync_from} to {sync_to}"
        )
        return synced_result
    except Exception as e:
        activity.logger.error(
            f"Failed to sync calendar events for user Error: {e!s}",
            extra={
                "user_id": user_id,
                "organization_id": organization_id,
                "calendar_account_id": calendar_account_id,
                "sync_from": sync_from,
                "sync_to": sync_to,
            },
        )
        # Re-raise the exception to let Temporal know the activity has failed
        raise


# define activity to process calendar event sync task
@activity.defn
@with_tracing
async def process_sync_contact_task(  # type: ignore[explicit-any] # TODO: fix-any-annotation
    user_id: UUID,
    organization_id: UUID,
    calendar_account_id: UUID,
) -> dict[str, Any]:
    try:
        # for initial sync, we sync all events from 30 days ago to now
        sync_from = datetime.now(UTC) - timedelta(days=30)
        sync_to = datetime.now(UTC)

        db_engine = await get_or_init_db_engine()
        calendar_event_sync_service = get_user_calendar_sync_service_by_db_engine(
            db_engine=db_engine
        )
        synced_result = await calendar_event_sync_service.process_user_calendar_task(
            user_id=user_id,
            organization_id=organization_id,
            calendar_account_id=calendar_account_id,
            sync_from=sync_from,
            sync_to=sync_to,
            only_sync_contact=True,
        )
        activity.logger.info(
            f"synced contact for user: {user_id}/{organization_id} from {sync_from} to {sync_to}"
        )
        return synced_result
    except Exception as e:
        activity.logger.error(
            f"Failed to sync contact for user Error: {e!s}",
            extra={
                "user_id": user_id,
                "organization_id": organization_id,
                "calendar_account_id": calendar_account_id,
                "sync_from": sync_from,
                "sync_to": sync_to,
            },
        )
        # Re-raise the exception to let Temporal know the activity has failed
        raise
