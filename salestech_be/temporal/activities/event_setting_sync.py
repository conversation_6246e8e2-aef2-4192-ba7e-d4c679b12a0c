from uuid import UUID

from temporalio import activity

from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.temporal.database import get_or_init_db_engine


@activity.defn
@with_tracing
async def sync_event_settings_activity(organization_id: UUID) -> None:
    db_engine = await get_or_init_db_engine()
    from salestech_be.core.calendar.user_calendar_sync_service import (
        get_user_calendar_sync_service_by_db_engine,
    )

    user_calendar_sync_service = get_user_calendar_sync_service_by_db_engine(db_engine)
    await user_calendar_sync_service.resync_user_calendar_events_for_meeting_settings(
        organization_id=organization_id,
    )
