from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from uuid import UUID

    from salestech_be.core.organization.service.organization_service import (
        get_organization_service_general,
    )
    from salestech_be.core.sequence.service.sequence_service import (
        get_sequence_service_by_db_engine,
    )
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.util.enum_util import NameValueStrEnum


logger = get_logger(__name__)


class BackfillType(NameValueStrEnum):
    SEQUENCE_BLUEPRINTS = "SEQUENCE_BLUEPRINTS"
    # Add more backfill types here as needed


@activity.defn
@with_tracing
async def backfill_organization_activity(
    organization_ids: list[UUID],
    backfill_type: BackfillType,
    is_recreate: bool = False,
) -> None:
    """Activity to backfill data for an organization.

    Args:
        organization_id: The ID of the organization to backfill. If None, backfill for all organizations.
        user_id: The ID of the user performing the backfill
        backfill_type: The type of data to backfill
    """
    db_engine = await get_or_init_db_engine()

    # If no specific organization_id provided, get all organizations
    org_service = get_organization_service_general(db_engine)
    if not organization_ids:
        organizations = await org_service.list_all()
    else:
        organizations = []
        for org_id in organization_ids:
            organization = await org_service.get_organization_by_id(org_id)
            if not organization:
                logger.bind(organization_id=org_id).error("Organization not found")
                raise ValueError(f"Organization not found: {org_id}")
            organizations.append(organization)

    if backfill_type == BackfillType.SEQUENCE_BLUEPRINTS:
        sequence_service = get_sequence_service_by_db_engine(db_engine)
        for organization in organizations:
            try:
                # Assumptions: Individual service backfilling/onboarding is idempotent and can handle retries
                logger.bind(organization_id=organization.id).info(
                    "Backfilling sequence blueprints"
                )
                await sequence_service.onboard_sequence_blueprints(
                    organization_id=organization.id,
                    user_id=organization.created_by_user_id,
                    is_recreate=is_recreate,
                )
            except Exception as e:
                logger.bind(organization_id=organization.id, exc_info=e).error(
                    "Failed to backfill sequence blueprints for organization"
                )
                raise
