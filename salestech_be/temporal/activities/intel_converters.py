from uuid import UUID

from pydantic import BaseModel
from temporalio import activity
from temporalio.exceptions import ApplicationError

from salestech_be.core.research_agent.research_agent_client import (
    get_research_agent_client,
)
from salestech_be.core.research_agent.research_agent_service import ResearchAgentService
from salestech_be.db.dao.intel_repository import IntelRepository
from salestech_be.db.dao.user_feedback_repository import UserFeedbackRepository
from salestech_be.integrations.crustdata.client import CrustdataClient
from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.temporal.database import get_or_init_db_engine


class GetIntelCompanyIdFromAccountIdInput(BaseModel):
    account_id: UUID
    account_domain_name: str


@activity.defn
@with_tracing
async def get_intel_company_id_from_account_id(
    input_: GetIntelCompanyIdFromAccountIdInput,
) -> UUID:
    """Get the intel company ID from an account ID"""
    db_engine = await get_or_init_db_engine()
    intel_repository = IntelRepository(engine=db_engine)
    crustdata_client = CrustdataClient()
    research_agent_service = ResearchAgentService(
        crustdata_client=crustdata_client,
        intel_repository=intel_repository,
        research_agent_client=get_research_agent_client(),
        user_feedback_repository=UserFeedbackRepository(engine=db_engine),
    )
    result = await research_agent_service.get_or_create_intel_company_id(
        input_.account_id, input_.account_domain_name
    )
    if not result:
        raise ApplicationError(
            f"No intel company association found for account {input_.account_id}",
            non_retryable=True,
        )
    return result


class GetIntelPersonIdFromContactIdInput(BaseModel):
    contact_id: UUID
    contact_linkedin_url: str


@activity.defn
@with_tracing
async def get_intel_person_id_from_contact_id(
    input_: GetIntelPersonIdFromContactIdInput,
) -> UUID:
    """Get the intel person ID from a contact ID"""
    db_engine = await get_or_init_db_engine()
    intel_repository = IntelRepository(engine=db_engine)
    crustdata_client = CrustdataClient()
    research_agent_service = ResearchAgentService(
        crustdata_client=crustdata_client,
        intel_repository=intel_repository,
        research_agent_client=get_research_agent_client(),
        user_feedback_repository=UserFeedbackRepository(engine=db_engine),
    )
    result = await research_agent_service.get_or_create_intel_person_id(
        input_.contact_id, input_.contact_linkedin_url
    )
    if not result:
        raise ApplicationError(
            f"No intel person association found for contact {input_.contact_id}",
            non_retryable=True,
        )
    return result
