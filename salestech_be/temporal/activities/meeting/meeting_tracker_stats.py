from dataclasses import dataclass
from typing import cast
from uuid import UUID

from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.tracker.service.tracker_stats_generation_service import (
        get_tracker_stats_generation_service_from_engine,
    )
    from salestech_be.core.tracker.types import MeetingTrackerFilter
    from salestech_be.db.models.meeting import MeetingReferenceIdType
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.util.time import zoned_utc_now
    from salestech_be.util.validation import not_none

logger = get_logger(__name__)


@dataclass
class MeetingTrackerStatsInput:
    meeting_reference_id: str
    meeting_reference_id_type: MeetingReferenceIdType
    organization_id: UUID


@dataclass
class MeetingTrackerStatsBackfillInput:
    tracker_id: UUID
    organization_id: UUID


@activity.defn
@with_tracing
async def generate_meeting_tracker_stats_activity(
    meeting_tracker_stats_input: MeetingTrackerStatsInput,
) -> None:
    db_engine = await get_or_init_db_engine()
    tracker_stats_generation_service = get_tracker_stats_generation_service_from_engine(
        db_engine=db_engine
    )
    await tracker_stats_generation_service.generate_tracker_stats_from_meeting(
        meeting_reference_id=meeting_tracker_stats_input.meeting_reference_id,
        meeting_reference_id_type=meeting_tracker_stats_input.meeting_reference_id_type,
        organization_id=meeting_tracker_stats_input.organization_id,
    )


@activity.defn
@with_tracing
async def backfill_meeting_tracker_stats_activity(
    backfill_input: MeetingTrackerStatsBackfillInput,
) -> None:
    db_engine = await get_or_init_db_engine()
    tracker_stats_generation_service = get_tracker_stats_generation_service_from_engine(
        db_engine=db_engine
    )
    # Verify tracker
    db_tracker = await tracker_stats_generation_service.tracker_repository.get_tracker(
        tracker_id=backfill_input.tracker_id,
        organization_id=backfill_input.organization_id,
    )
    if not db_tracker:
        logger.bind(
            tracker_id=backfill_input.tracker_id,
        ).error("[backfill_meeting_tracker_stats_activity] tracker not found.")
        return
    tracker_filter = cast(MeetingTrackerFilter, db_tracker.filter)
    if not tracker_filter.all_meetings:
        logger.bind(
            tracker_id=backfill_input.tracker_id,
        ).error(
            "[backfill_meeting_tracker_stats_activity] tracker is not configured for all meetings."
        )
        return

    # Find all recorded meetings
    all_meetings = await tracker_stats_generation_service.meeting_service.meeting_query_service.list_meeting_v2(
        organization_id=backfill_input.organization_id,
        user_id=None,
        include_custom_object=False,
    )
    all_recorded_meetings = sorted(
        [
            meeting_v2
            for meeting_v2 in all_meetings
            if meeting_v2.ended_at
            and meeting_v2.ended_at < zoned_utc_now()
            and meeting_v2.is_recorded
        ],
        key=lambda meeting_v2: not_none(meeting_v2.ended_at),
        reverse=True,
    )
    logger.bind(
        tracker_id=backfill_input.tracker_id,
        num_meetings=len(all_recorded_meetings),
    ).info(
        "[backfill_meeting_tracker_stats_activity] found meetings to backfill for tracker"
    )
    for meeting_v2 in all_recorded_meetings:
        await tracker_stats_generation_service.generate_tracker_stats_from_meeting(
            meeting_reference_id=str(meeting_v2.reference_id),
            meeting_reference_id_type=meeting_v2.reference_id_type,
            organization_id=backfill_input.organization_id,
        )
