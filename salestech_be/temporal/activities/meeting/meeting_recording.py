from uuid import UUID

from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.meeting.meeting_bot_service import (
        meeting_bot_service_general,
    )
    from salestech_be.temporal.database import get_or_init_db_engine


@activity.defn
@with_tracing
async def store_recording_activity(
    external_meeting_bot_id: str,
    organization_id: UUID,
    external_media_url: str,
    s3_key_prefix: str,
    s3_title: str,
) -> None:
    db_engine = await get_or_init_db_engine()
    meeting_bot_service = meeting_bot_service_general(db_engine)
    await meeting_bot_service.copy_external_bot_media_to_s3(
        external_meeting_bot_id=external_meeting_bot_id,
        organization_id=organization_id,
        external_media_url=external_media_url,
        s3_key_prefix=s3_key_prefix,
        s3_title=s3_title,
    )


@activity.defn
@with_tracing
async def persist_external_media_data_activity(
    external_media_url: str,
    s3_key_prefix: str,
    external_meeting_bot_id: str,
    organization_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    meeting_bot_service = meeting_bot_service_general(db_engine)
    await meeting_bot_service.persist_external_media_data(
        external_media_url=external_media_url,
        s3_key_prefix=s3_key_prefix,
        external_meeting_bot_id=external_meeting_bot_id,
        organization_id=organization_id,
    )
