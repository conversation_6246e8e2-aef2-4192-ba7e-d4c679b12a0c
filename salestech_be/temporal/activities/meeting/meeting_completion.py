from uuid import UUID

from temporalio import activity, workflow

with workflow.unsafe.imports_passed_through():
    from salestech_be.common.exception.exception import InvalidArgumentError
    from salestech_be.core.meeting.meeting_bot_service import (
        meeting_bot_service_general,
    )
    from salestech_be.core.meeting.meeting_service import (
        meeting_service_factory_general,
    )
    from salestech_be.core.transcript.transcript_service import (
        transcript_service_from_engine,
    )
    from salestech_be.core.transcript.types import RecallLoadTranscriptRequest
    from salestech_be.db.models.meeting import (
        MeetingBotStatus,
        MeetingReferenceIdType,
        MeetingStatus,
        TranscriptProvider,
    )
    from salestech_be.temporal.activity_decorator import with_tracing
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.util.validation import not_none


@activity.defn
@with_tracing
async def end_active_meeting_activity(
    meeting_id: UUID,
    organization_id: UUID,
) -> MeetingStatus:
    db_engine = await get_or_init_db_engine()
    meeting_service = meeting_service_factory_general(db_engine)
    meeting_bot_service = meeting_bot_service_general(db_engine)
    transcript_service = transcript_service_from_engine(db_engine)
    meeting_dto = await meeting_service.get_meeting(
        meeting_id=meeting_id, organization_id=organization_id
    )
    if meeting_dto.meeting.status != MeetingStatus.ACTIVE:
        raise InvalidArgumentError("Meeting is not active")

    ended_meeting_dto, _ = await meeting_service.end_active_meeting(
        reference_id=meeting_dto.meeting.reference_id,
        reference_id_type=meeting_dto.meeting.reference_id_type,
        organization_id=organization_id,
    )

    result_meeting_status = ended_meeting_dto.meeting.status
    if (
        ended_meeting_dto.meeting_bot
        and ended_meeting_dto.meeting_bot.status == MeetingBotStatus.EXITED
        and ended_meeting_dto.meeting_bot.media_url
    ):
        refreshed_meeting_bot = await meeting_bot_service.refresh_external_media_url(
            ended_meeting_dto.meeting_bot
        )
        if refreshed_meeting_bot.media_url:
            return ended_meeting_dto.meeting.status

        transcript = not_none(
            await transcript_service.get_transcript_record(
                organization_id=organization_id,
                request=RecallLoadTranscriptRequest(
                    transcript_provider=TranscriptProvider.ASSEMBLYAI,
                    meeting_bot_id=ended_meeting_dto.meeting_bot.id,
                ),
            )
        )
        analyzing_meeting, _ = await meeting_service.analyze_from_bot_media(
            organization_id=organization_id,
            meeting=ended_meeting_dto.meeting,
            meeting_bot=ended_meeting_dto.meeting_bot,
            transcript=transcript,
        )
        if analyzing_meeting:
            result_meeting_status = analyzing_meeting.status

    return result_meeting_status


@activity.defn
@with_tracing
async def complete_analyze_meeting_activity(
    meeting_reference_id: str,
    meeting_reference_id_type: str,
    organization_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    meeting_service = meeting_service_factory_general(db_engine)
    await meeting_service.complete_analyze_meeting_by_reference_id(
        reference_id=meeting_reference_id,
        reference_id_type=MeetingReferenceIdType(meeting_reference_id_type),
        organization_id=organization_id,
    )
