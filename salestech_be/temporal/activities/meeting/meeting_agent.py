from uuid import UUID

from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.meeting.meeting_agent_service import (
        get_meeting_agent_service_from_engine,
    )
    from salestech_be.temporal.database import get_or_init_db_engine


@activity.defn
@with_tracing
async def process_meeting_agent_task(meeting_id: UUID, organization_id: UUID) -> None:
    db_engine = await get_or_init_db_engine()
    meeting_agent_service = get_meeting_agent_service_from_engine(db_engine=db_engine)
    await meeting_agent_service.ai_generate_meeting_key_talking_points(
        meeting_id=meeting_id,
        organization_id=organization_id,
        regenerate=False,
    )
