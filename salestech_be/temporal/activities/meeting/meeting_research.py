import time
from collections.abc import Sequence
from dataclasses import dataclass
from pprint import pformat
from uuid import UUID

from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.common.exception.exception import InvalidRecordCountError
    from salestech_be.common.schema_manager.std_object_field_identifier import (
        AccountField,
    )
    from salestech_be.common.schema_manager.std_object_relationship import (
        ContactRelationship,
        MeetingRelationship,
    )
    from salestech_be.common.stats.metric import workflow_metric
    from salestech_be.common.type.metadata.schema import FieldReference, QualifiedField
    from salestech_be.core.account.types_v2 import AccountV2
    from salestech_be.core.contact.types_v2 import ContactV2
    from salestech_be.core.data.service.query_service import (
        get_domain_object_query_service,
    )
    from salestech_be.core.data.types import StandardRecord
    from salestech_be.core.data.util import ObjectRecordFetchConditions
    from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.temporal.metric import (
        CANCELLED_ACTIVITY_TAG,
        COMPLETED_ACTIVITY_TAG,
        FAILED_ACTIVITY_TAG,
        GET_ACCOUNT_AND_CONTACT_IDS_FROM_MEETING_ID_MEETING_QUERIED_METRIC_NAME,
        GET_ACCOUNT_AND_CONTACT_IDS_FROM_MEETING_ID_METRIC_NAME,
    )
    from salestech_be.util.validation import cast_or_error

logger = get_logger(__name__)


@dataclass
class MeetingResearchInput:
    meeting_id: UUID
    organization_id: UUID


@dataclass
class AccountDetails:
    id: UUID
    display_name: str
    domain_name: str | None


@dataclass
class ContactDetails:
    id: UUID
    linkedin_url: str | None
    primary_email: str | None


@dataclass
class MeetingDetails:
    id: UUID
    next_research_time_epoch: float


@dataclass
class MeetingResearchOutput:
    accounts: list[AccountDetails]
    contacts: list[ContactDetails]
    meeting: MeetingDetails | None


@activity.defn
@with_tracing
async def get_account_and_contact_ids_from_meeting_id(
    data: MeetingResearchInput,
) -> MeetingResearchOutput:
    info = activity.info()
    logger.info(
        f"TEMPORAL ACTIVITY INFO get_account_and_contact_ids_from_meeting_id: \n{pformat(info)}"
    )

    start_time = time.perf_counter()
    logger.info("TEMPORAL ACTIVITY BEGINS get_account_and_contact_ids_from_meeting_id")

    db_engine = await get_or_init_db_engine()
    meeting_records: Sequence[StandardRecord[MeetingV2]] = []
    try:
        domain_object_query_service = get_domain_object_query_service(
            db_engine=db_engine
        )
        meeting_records, _ = await domain_object_query_service.list_meeting_records(
            organization_id=data.organization_id,
            user_id=None,
            only_include_meeting_ids={data.meeting_id},
            include_custom_object=False,
            fetch_conditions=ObjectRecordFetchConditions(
                filter_spec=None,
                sorting_spec=None,
                fields=(
                    FieldReference(
                        relationship_id=MeetingRelationship.meeting__to__attendee_contact,
                        field=FieldReference(
                            relationship_id=ContactRelationship.contact__to__primary_account,
                            field=QualifiedField(path=(AccountField.id,)),
                        ),
                    ),
                    FieldReference(
                        relationship_id=MeetingRelationship.meeting__to__invitee_contact,
                        field=FieldReference(
                            relationship_id=ContactRelationship.contact__to__primary_account,
                            field=QualifiedField(path=(AccountField.id,)),
                        ),
                    ),
                ),
            ),
        )
        logger.bind(
            meeting_record_ids=[mr.data.id for mr in meeting_records],
        ).info("Meeting records fetched")
    except InvalidRecordCountError:
        # TODO: clean up later once we find out which values show up in the logs
        logger.info(
            f"TEMPORAL ACTIVITY INFO EXCEPTION get_account_and_contact_ids_from_meeting_id: \n{pformat(info)}"
        )
        logger.bind(activity_info=info).warning(
            f"Meeting record not found: {data.meeting_id}"
        )
        # if meeting not found, we don't need to research
        elapsed_time = (time.perf_counter() - start_time) * 1000
        workflow_metric.timing(
            metric_name=GET_ACCOUNT_AND_CONTACT_IDS_FROM_MEETING_ID_METRIC_NAME,
            value=elapsed_time,
            tags=list(CANCELLED_ACTIVITY_TAG),
        )
        return MeetingResearchOutput(
            accounts=[],
            contacts=[],
            meeting=None,
        )
    except Exception:
        logger.info(
            f"TEMPORAL ACTIVITY INFO EXCEPTION get_account_and_contact_ids_from_meeting_id: \n{pformat(info)}"
        )
        logger.bind(activity_info=info).exception(
            f"Error getting meeting records: {data.meeting_id}"
        )
        elapsed_time = (time.perf_counter() - start_time) * 1000
        workflow_metric.timing(
            metric_name=GET_ACCOUNT_AND_CONTACT_IDS_FROM_MEETING_ID_METRIC_NAME,
            value=elapsed_time,
            tags=list(FAILED_ACTIVITY_TAG),
        )
        raise  # continue raising so it shows up in temporal event history

    logger.info(
        "TEMPORAL ACTIVITY LOGIC get_account_and_contact_ids_from_meeting_id meeting queried"
    )
    elapsed_time = (time.perf_counter() - start_time) * 1000
    workflow_metric.timing(
        metric_name=GET_ACCOUNT_AND_CONTACT_IDS_FROM_MEETING_ID_MEETING_QUERIED_METRIC_NAME,
        value=elapsed_time,
        tags=list(COMPLETED_ACTIVITY_TAG),
    )

    if len(meeting_records) != 1:
        raise InvalidRecordCountError(
            f"Expected 1 meeting record for meeting {data.meeting_id}, got {len(meeting_records)}"
        )

    meeting_record = meeting_records[0]
    meeting = meeting_record.data

    accounts: list[AccountV2] = []
    invitee_contacts: list[ContactV2] = []

    # We try to get the account from deal first, then lead, then invitees
    # We don't fetch any accounts from invitee if we already found an account from deal or lead
    # TODO get account via pipeline, once we add this meeting relationship

    for invitee_cr in meeting_record.related_records.get(
        MeetingRelationship.meeting__to__invitee_contact, ()
    ):
        invitee_contacts.append(cast_or_error(invitee_cr.data, ContactV2))
        if not accounts:
            for ar in invitee_cr.related_records.get(
                ContactRelationship.contact__to__primary_account, ()
            ):
                accounts.append(cast_or_error(ar.data, AccountV2))

    logger.info("TEMPORAL ACTIVITY ENDS get_account_and_contact_ids_from_meeting_id")
    elapsed_time = (time.perf_counter() - start_time) * 1000
    workflow_metric.timing(
        metric_name=GET_ACCOUNT_AND_CONTACT_IDS_FROM_MEETING_ID_METRIC_NAME,
        value=elapsed_time,
        tags=list(COMPLETED_ACTIVITY_TAG),
    )

    accounts_detail = [
        AccountDetails(
            id=account.id,
            domain_name=account.domain_name,
            display_name=account.display_name,
        )
        for account in accounts
    ]
    contacts_detail = [
        ContactDetails(
            id=contact.id,
            linkedin_url=contact.linkedin_url,
            primary_email=contact.primary_email,
        )
        for contact in invitee_contacts
    ]
    day_of_meeting_morning = meeting.starts_at.replace(
        hour=6, minute=0, second=0, microsecond=0
    )
    next_research_time_epoch = day_of_meeting_morning.timestamp()
    meeting_detail = MeetingDetails(
        id=meeting.id,
        next_research_time_epoch=next_research_time_epoch,
    )

    return MeetingResearchOutput(
        accounts=accounts_detail,
        contacts=contacts_detail,
        meeting=meeting_detail,
    )
