from uuid import UUID

from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.meeting.meeting_bot_service import (
        meeting_bot_service_general,
    )
    from salestech_be.temporal.database import get_or_init_db_engine


@activity.defn
@with_tracing
async def propagate_bot_name_change_activity(
    meeting_bot_name: str,
    organization_id: UUID,
    user_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    meeting_bot_service = meeting_bot_service_general(db_engine)
    await meeting_bot_service.change_name_for_scheduled_bots(
        organization_id=organization_id, user_id=user_id, new_bot_name=meeting_bot_name
    )
