from uuid import UUID

from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.meeting.meeting_service import (
        meeting_service_factory_general,
    )
    from salestech_be.temporal.database import get_or_init_db_engine


@activity.defn
@with_tracing
async def clear_future_meeting_bots_activity(
    organization_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    meeting_service = meeting_service_factory_general(db_engine)
    await meeting_service.clear_future_bots(organization_id=organization_id)
