from uuid import UUID

from temporalio import activity, workflow

from salestech_be.core.calendar.user_calendar_common_service import (
    get_user_calendar_common_service_by_db_engine,
)
from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.temporal.workflows.user_calendar.user_calendar_event_meeting_sync_workflow import (
    UserCalendarEventMeetingCreateWorkflowInput,
)

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.calendar.user_calendar_sync_service import (
        get_user_calendar_sync_service_by_db_engine,
    )
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.temporal.workflows.user_calendar.user_calendar_event_meeting_sync_workflow import (
        UserCalendarEventMeetingSyncWorkflowInput,
    )


@activity.defn
@with_tracing
async def meeting_sync_activity(
    data: UserCalendarEventMeetingSyncWorkflowInput,
) -> UUID | None:
    db_engine = await get_or_init_db_engine()
    user_calendar_sync_service = get_user_calendar_sync_service_by_db_engine(db_engine)
    return await user_calendar_sync_service.resync_calendar_event_to_meeting(
        organization_id=data.organization_id,
        group_key=data.group_key,
    )


@activity.defn
@with_tracing
async def meeting_create_activity(
    data: UserCalendarEventMeetingCreateWorkflowInput,
) -> UUID | None:
    db_engine = await get_or_init_db_engine()
    user_calendar_common_service = get_user_calendar_common_service_by_db_engine(
        db_engine
    )
    updated_event = await user_calendar_common_service.create_meeting_for_event_async(
        user_id=data.user_id,
        organization_id=data.organization_id,
        calendar_event_id=data.calendar_event_id,
        schedule_bot=data.schedule_bot,
        event_schedule_id=data.event_schedule_id,
        meeting_title=data.meeting_title,
    )
    return updated_event.meeting_id
