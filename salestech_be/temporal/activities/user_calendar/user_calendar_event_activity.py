from temporalio import activity, workflow

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.calendar.user_calendar_webhook_service import (
        get_user_calendar_webhook_service_by_db_engine,
    )
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.activity_decorator import with_tracing
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.temporal.workflows.user_calendar.user_calendar_webhook_workflow import (
        UserCalendarWebhookWorkflowInput,
    )
    from salestech_be.web.api.webhook.schema import NylasWebhookType


logger = get_logger(__name__)


@activity.defn
@with_tracing
async def handle_user_calendar_event_activity(
    data: UserCalendarWebhookWorkflowInput,
) -> None:
    # Use contextualize to set common logging context for additional fields
    with logger.contextualize(
        external_event_id=data.external_event_id,
        calendar_id=data.calendar_id,
        grant_id=data.grant_id,
    ):
        logger.info("syncing calendar events from external.")

        db_engine = await get_or_init_db_engine()
        user_calendar_webhook_service = get_user_calendar_webhook_service_by_db_engine(
            db_engine
        )
        if data.type == NylasWebhookType.EVENT_DELETED:
            logger.info(
                "deleting calendar events from external",
            )
            await user_calendar_webhook_service.delete_calendar_event_from_external(
                event_external_id=data.external_event_id,
                calendar_external_id=data.calendar_id,
                connection_external_id=data.grant_id,
            )
            logger.info(
                "deleted calendar events from external",
            )
            return

        # Only process non-recurrence events in webhook
        logger.info(
            "updating calendar events from external",
        )
        await user_calendar_webhook_service.upsert_calendar_event_from_external(
            event_external_id=data.external_event_id,
            calendar_external_id=data.calendar_id,
            connection_external_id=data.grant_id,
        )
        logger.info("updated calendar events from external.")
