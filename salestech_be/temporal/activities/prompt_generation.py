from uuid import UUID

from temporalio import activity

from salestech_be.core.prompt.ai_prompt_generation_service import (
    AIPromptGenerationService,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.temporal.activity_decorator import with_tracing


class PromptGenerationActivities:
    def __init__(
        self,
        engine: DatabaseEngine,
    ):
        self.engine = engine

    @activity.defn(name="generate_prompt")
    @with_tracing
    async def generate_prompt(self, job_id: UUID) -> bool:
        """
        Temporal activity to generate prompt for a given job ID
        """
        service = AIPromptGenerationService(engine=self.engine)
        result: bool = await service.generate_prompt(job_id)
        return result
