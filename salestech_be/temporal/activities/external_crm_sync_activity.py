from uuid import UUID

from temporalio import activity, workflow

from salestech_be.core.imports.service.import_job_service import (
    get_import_job_service,
)
from salestech_be.core.job.models import JobStatus
from salestech_be.core.job.service.job_service import job_service_from_engine
from salestech_be.db.models.organization_external_sync import (
    OrganizationExternalSyncStatus,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.web.api.job.schema import CreateJobRequest

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.imports.service.crm_sync_service import (
        get_crm_sync_service_from_db_engine,
    )
    from salestech_be.temporal.database import get_or_init_db_engine


@activity.defn
@with_tracing
async def process_external_crm_sync_activity(
    organization_id: UUID,
    initial_sync: bool = False,
) -> str:
    try:
        db_engine = await get_or_init_db_engine()
        temporal_client = await get_temporal_client()
        crm_sync_service = get_crm_sync_service_from_db_engine(engine=db_engine)
        import_job_service = get_import_job_service(
            temporal_client=temporal_client, db_engine=db_engine
        )
        job_service = job_service_from_engine(engine=db_engine)
        if initial_sync:
            # crm_sync_service.upsert sync config
            activity.logger.info(
                f"external_crm_sync_activity upsert sync config, org: {organization_id}"
            )

        org_sync_config = (
            await crm_sync_service.crm_sync_repository.get_org_external_sync(
                organization_id=organization_id
            )
        )
        if (
            not org_sync_config
            or org_sync_config.status != OrganizationExternalSyncStatus.ACTIVE
        ):
            activity.logger.error(
                "Organization sync config not exist or not active.",
                extra={
                    "organization_id": organization_id,
                    "org_sync_config": org_sync_config,
                },
            )
            return "Failed"
        activity.logger.info(
            "external_crm_sync_activity start the sync.",
            extra={
                "organization_id": organization_id,
                "org_sync_config": org_sync_config,
            },
        )

        crm_sync_job = await import_job_service.create_job(
            organization_id=organization_id,
            user_id=org_sync_config.created_by_user_id,
            job_name="hubspot-sync-job",
            object_identifier="external_crm_sync",
        )

        for job_type in org_sync_config.sync_setting.sync_job_enabled:
            import_job = await job_service.create_job(
                user_id=org_sync_config.created_by_user_id,
                organization_id=organization_id,
                job_request=CreateJobRequest(
                    type=job_type,
                    reference_id=crm_sync_job.id,
                    status=JobStatus.RUNNING,
                ),
            )
            await crm_sync_service.process_hubspot_crm_sync_job(job=import_job)
            await job_service.update_job_status(
                job_id=import_job.id,
                status=JobStatus.COMPLETED,
                organization_id=organization_id,
            )
            activity.logger.info("process_csv_import job completed.")

        return "Completed"
    except Exception as e:
        activity.logger.error(
            f"Failed to sync from external crm: {e!s}",
            extra={
                "organization_id": organization_id,
            },
        )
        raise
