from dataclasses import dataclass
from uuid import UUID

from temporalio import activity

from salestech_be.core.schedule.schedule_common_service import (
    get_event_schedule_common_service_by_db_engine,
)
from salestech_be.core.schedule.schedule_notification_service import (
    get_event_schedule_notification_service_by_db_engine,
)
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.temporal.database import get_or_init_db_engine
from salestech_be.web.api.schedule.schema import SchedulingType

logger = get_logger("schedule_notification_task")


@activity.defn
@with_tracing
async def post_booking_update_activity(
    scheduling_type: str,
    booking_id: UUID,
    organization_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    common_service = get_event_schedule_common_service_by_db_engine(db_engine)

    if scheduling_type in {"book", "reschedule"}:
        try:
            logger.info(f"Updating scheduled calendar event for booking {booking_id}")
            await common_service.update_scheduled_calendar_event(
                booking_id=booking_id,
                organization_id=organization_id,
                scheduling_type=scheduling_type,
            )
            logger.info(
                f"Finished updating scheduled calendar event for booking {booking_id}"
            )
        except Exception as e:
            logger.error(
                f"Failed to update scheduled calendar event for booking {booking_id}",
                exc_info=e,
            )


@activity.defn
@with_tracing
async def send_scheduler_email_notification(
    scheduling_type: str,
    booking_id: UUID,
    organization_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    scheduler_notification_service = (
        get_event_schedule_notification_service_by_db_engine(db_engine)
    )

    logger.info(f"Sending notification emails for booking {booking_id}")
    await scheduler_notification_service.send_notification_emails(
        scheduling_type=SchedulingType(scheduling_type),
        booking_id=booking_id,
        organization_id=organization_id,
    )
    logger.info(f"Finished sending notification emails for booking {booking_id}")


@dataclass
class EventScheduleTimezoneChangeInput:
    user_id: UUID
    organization_id: UUID


@activity.defn
async def update_event_schedule_timezone(
    data: EventScheduleTimezoneChangeInput,
) -> None:
    # Fetch event schedules owned by the user
    # For each, use service to update timezone
    pass
