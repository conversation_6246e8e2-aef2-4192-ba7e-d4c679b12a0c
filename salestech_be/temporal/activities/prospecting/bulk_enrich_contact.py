from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from uuid import UUID

    from salestech_be.core.contact.service.contact_enrichment_service import (
        get_contact_enrichment_service,
    )
    from salestech_be.core.prospecting.prospecting_enrichment_service import (
        get_prospecting_enrichment_service_by_db_engine,
    )
    from salestech_be.core.prospecting.type.person_type_v2 import (
        BulkEnrichPersonRequest,
    )
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.database import get_or_init_db_engine


logger = get_logger(__name__)


@activity.defn
@with_tracing
async def bulk_enrich_contact(
    user_id: UUID,
    organization_id: UUID,
    prospecting_run_id: UUID,
    enrich_requests: list[BulkEnrichPersonRequest],
    enrich_phone_numbers: bool = False,
) -> None:
    db_engine = await get_or_init_db_engine()
    prospecting_enrichment_service = get_prospecting_enrichment_service_by_db_engine(
        db_engine
    )
    contact_enrichment_service = get_contact_enrichment_service(db_engine)
    result = await prospecting_enrichment_service.bulk_enrich_contact_by_run_id(
        prospecting_run_id=prospecting_run_id,
        organization_id=organization_id,
        user_id=user_id,
        enrich_requests=enrich_requests,
        enrich_phone_numbers=enrich_phone_numbers,
    )
    logger.bind(
        organization_id=organization_id,
        enrich_requests=enrich_requests,
        enrich_phone_numbers=enrich_phone_numbers,
        prospecting_run_id=prospecting_run_id,
        result_size=len(result),
    ).info("bulk_enrich_contact workflow bulk_enrich_contact_by_run_id completed.")
    await contact_enrichment_service.process_enriched_results(
        organization_id=organization_id,
        user_id=user_id,
        total_contact_ids=[c.contact_id for c in enrich_requests],
        enrichable_contact_ids=[c.contact_id for c in enrich_requests],
        enriched_person_map=result,
        enrich_phone_numbers=enrich_phone_numbers,
    )
