from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from uuid import UUID, uuid4

    from salestech_be.db.dto.person_dto import PersonDto
    from salestech_be.db.dto.prospecting_dto import ContactSimpleInfo
    from salestech_be.db.models.person import ProspectingEnrichStatus
    from salestech_be.db.models.prospecting_run import (
        ProspectingRunRequest,
        ProspectingRunResult,
        ProspectingRunStatus,
        ProspectingRunUpdate,
    )
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.util.time import zoned_utc_now
    from salestech_be.util.validation import not_none
    from salestech_be.web.api.prospecting.common.prospecting_common_service import (
        get_prospecting_common_service,
    )


logger = get_logger(__name__)


@activity.defn
@with_tracing
async def enrich_people_with_run(
    user_id: UUID,
    organization_id: UUID,
    prospecting_run_id: UUID,
) -> list[UUID]:
    # Retrieve prospecting run details
    db_engine = await get_or_init_db_engine()
    prospecting_common_service = get_prospecting_common_service(db_engine)

    prospecting_run = (
        await prospecting_common_service.prospecting_run_service.get_by_id(
            prospecting_run_id=prospecting_run_id,
            organization_id=organization_id,
        )
    )

    if not isinstance(prospecting_run.run_request, ProspectingRunRequest):
        logger.bind(
            prospecting_run_id=prospecting_run_id,
            organization_id=organization_id,
            user_id=user_id,
        ).error("Invalid run request type")
        raise ValueError("Invalid run request type")

    person_ids = prospecting_run.run_request.person_ids or []
    total_number = prospecting_run.run_request.total_number
    enrich_phone_numbers = prospecting_run.run_request.enrich_phone_numbers

    # Retrieve existing people from the database if person_ids were provided
    db_person_dto_list: list[PersonDto] = []
    if person_ids:
        db_person_dto_list = (
            await prospecting_common_service.find_person_dto_list_by_person_ids(
                person_ids=person_ids,
                organization_id=organization_id,
            )
        )

    # Calculate how many additional people we need and search if necessary
    need_more = max(0, total_number - len(db_person_dto_list))
    all_person_dto_list = db_person_dto_list

    if need_more > 0:
        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            prospecting_run_id=prospecting_run_id,
            need_more=need_more,
        ).info(f"Searching for additional {need_more} people")
        additional_people = await prospecting_common_service.search_additional_people(
            user_id=user_id,
            organization_id=organization_id,
            filter_spec=not_none(prospecting_run.run_request.filter_spec),
            count_needed=need_more,
            exclude_person_ids=prospecting_run.run_request.exclude_person_ids,
        )
        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            prospecting_run_id=prospecting_run_id,
        ).info(f"Total found {len(additional_people)} additional people")
        all_person_dto_list.extend(additional_people)

    if len(all_person_dto_list) < total_number:
        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            prospecting_run_id=prospecting_run_id,
            requested=total_number,
        ).warning(
            f"Not enough people found for prospecting run, requested {total_number}, found {len(all_person_dto_list)}"
        )

    # Prepare bulk records for prospecting_run_result table
    current_time = zoned_utc_now()
    # Deduplicate people as PDL may return the same person multiple times
    person_id_set = set()
    prospecting_run_results_to_add: list[ProspectingRunResult] = []
    for person_dto in all_person_dto_list:
        if person_dto.db_person.id in person_id_set:
            logger.bind(
                organization_id=organization_id,
                user_id=user_id,
                prospecting_run_id=prospecting_run_id,
                person_id=person_dto.db_person.id,
            ).warning("Duplicate person_id found in bulk import")
            continue
        person_id_set.add(person_dto.db_person.id)
        prospecting_run_results_to_add.append(
            ProspectingRunResult(
                id=uuid4(),
                prospecting_run_id=prospecting_run_id,
                organization_id=organization_id,
                person_id=person_dto.db_person.id,
                contact_id=None,
                email_enrichment_status=person_dto.db_person.work_email_enrich_status
                or ProspectingEnrichStatus.NOT_REQUESTED,
                phone_enrichment_status=person_dto.db_person.phone_number_enrich_status
                or ProspectingEnrichStatus.NOT_REQUESTED,
                created_at=current_time,
            )
        )

    # Batch database operations
    await prospecting_common_service.prospecting_run_service.bulk_insert_run_results(
        run_results=prospecting_run_results_to_add
    )

    # Enrich the people
    enriched_person = await prospecting_common_service.enrich_people_with_run(
        person_dto_list=all_person_dto_list,
        prospecting_run=prospecting_run,
        enrich_phone_numbers=enrich_phone_numbers,
        user_id=user_id,
        organization_id=organization_id,
    )

    return [person_dto.db_person.id for person_dto in enriched_person]


@activity.defn
@with_tracing
async def convert_people_to_contacts(
    person_ids: list[UUID],
    prospecting_run_id: UUID,
    organization_id: UUID,
    user_id: UUID,
) -> list[ContactSimpleInfo]:
    db_engine = await get_or_init_db_engine()
    prospecting_common_service = get_prospecting_common_service(db_engine)

    person_dto_list = (
        await prospecting_common_service.find_person_dto_list_by_person_ids(
            person_ids=person_ids,
            organization_id=organization_id,
        )
    )

    return await prospecting_common_service.convert_people_to_contacts(
        person_dto_list=person_dto_list,
        organization_id=organization_id,
        user_id=user_id,
        prospecting_run_id=prospecting_run_id,
    )


@activity.defn
@with_tracing
async def add_contacts_to_sequence(
    contact_simple_list: list[ContactSimpleInfo],
    sequence_id: UUID,
    prospecting_run_id: UUID,
    organization_id: UUID,
    user_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    prospecting_common_service = get_prospecting_common_service(db_engine)

    await prospecting_common_service.add_contacts_to_sequence(
        contact_simple_list=contact_simple_list,
        sequence_id=sequence_id,
        prospecting_run_id=prospecting_run_id,
        organization_id=organization_id,
        user_id=user_id,
    )


@activity.defn
@with_tracing
async def add_contacts_to_domain_lists(
    contact_simple_list: list[ContactSimpleInfo],
    domain_list_ids: list[UUID],
    prospecting_run_id: UUID,
    organization_id: UUID,
    user_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    prospecting_common_service = get_prospecting_common_service(db_engine)

    await prospecting_common_service.add_contacts_to_domain_lists(
        contact_simple_list=contact_simple_list,
        domain_list_ids=domain_list_ids,
        prospecting_run_id=prospecting_run_id,
        organization_id=organization_id,
        user_id=user_id,
    )


@activity.defn
@with_tracing
async def finish_prospecting_run(
    prospecting_run_id: UUID,
    organization_id: UUID,
    status: ProspectingRunStatus,
    error_info: str | None = None,
) -> None:
    db_engine = await get_or_init_db_engine()
    prospecting_common_service = get_prospecting_common_service(db_engine)

    prospecting_run_update = ProspectingRunUpdate(
        status=status,
        ends_at=zoned_utc_now(),
    )
    if error_info:
        prospecting_run_update.error_info = error_info

    await prospecting_common_service.prospecting_run_service.update_run(
        prospecting_run_id=prospecting_run_id,
        organization_id=organization_id,
        prospecting_run_update=prospecting_run_update,
    )
