from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from uuid import UUID

    from salestech_be.db.dto.prospecting_dto import AccountSimpleInfo
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.web.api.prospecting.common.prospecting_common_service import (
        get_prospecting_common_service,
    )


@activity.defn
@with_tracing
async def convert_company_to_accounts(
    company_ids: list[UUID],
    prospecting_run_id: UUID,
    organization_id: UUID,
    user_id: UUID,
) -> list[AccountSimpleInfo]:
    db_engine = await get_or_init_db_engine()
    prospecting_common_service = get_prospecting_common_service(db_engine)

    company_dto_list = (
        await prospecting_common_service.find_company_dto_list_by_company_ids(
            company_ids=company_ids,
            organization_id=organization_id,
            raise_exception_if_missing=True,
        )
    )

    return await prospecting_common_service.convert_company_to_accounts(
        company_dto_list=company_dto_list,
        organization_id=organization_id,
        user_id=user_id,
        prospecting_run_id=prospecting_run_id,
    )


@activity.defn
@with_tracing
async def add_accounts_to_domain_lists(
    account_simple_list: list[AccountSimpleInfo],
    domain_list_ids: list[UUID],
    prospecting_run_id: UUID,
    organization_id: UUID,
    user_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    prospecting_common_service = get_prospecting_common_service(db_engine)

    await prospecting_common_service.add_accounts_to_domain_lists(
        account_simple_list=account_simple_list,
        domain_list_ids=domain_list_ids,
        prospecting_run_id=prospecting_run_id,
        organization_id=organization_id,
        user_id=user_id,
    )
