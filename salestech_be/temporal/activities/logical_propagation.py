from temporalio import activity, workflow

from salestech_be.temporal.activity_decorator import with_tracing

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.logical_propagation.service.evaluation import (
        get_logical_propagation_evaluation_service,
    )
    from salestech_be.core.logical_propagation.service.execution import (
        get_logical_propagation_execution_service,
    )
    from salestech_be.core.logical_propagation.types import (
        LogicalPropagationChangeEvent,
    )
    from salestech_be.temporal.database import get_or_init_db_engine


@activity.defn
@with_tracing
async def evaluate_and_execute_logical_propagation_rules(
    event: LogicalPropagationChangeEvent,
) -> int:
    db_engine = await get_or_init_db_engine()
    evaluation_service = get_logical_propagation_evaluation_service(db_engine=db_engine)
    actions = await evaluation_service.evaluate(event=event)

    execution_service = get_logical_propagation_execution_service(db_engine=db_engine)
    await execution_service.execute(actions=actions)

    return len(actions)
