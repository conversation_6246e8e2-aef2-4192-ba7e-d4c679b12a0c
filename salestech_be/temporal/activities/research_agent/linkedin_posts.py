from datetime import UTC, datetime, timedelta

from temporalio import activity

from salestech_be.core.research_agent.models.response import (
    PendingEnrichmentDetails,
    PendingEnrichmentResponse,
)
from salestech_be.core.research_agent.research_agent_client import ResearchAgentClient
from salestech_be.core.research_agent.research_agent_service import (
    get_research_agent_service,
)
from salestech_be.core.research_agent.types import ResearchContext
from salestech_be.integrations.s3.s3_manager_ext_research import (
    BlobName,
    IntelType,
    S3ManagerExtResearch,
)
from salestech_be.settings import settings
from salestech_be.temporal.activities.research_agent.utils import (
    get_research_epoch,
    track_research_time,
)
from salestech_be.temporal.database import get_or_init_db_engine
from salestech_be.temporal.workflows.research_agent.schema import (
    SearchLinkedinPostsByCompanyToS3Input,
    SearchLinkedinPostsByCompanyToS3Output,
    SearchLinkedinPostsByPersonToS3Input,
    SearchLinkedinPostsByPersonToS3Output,
)


@activity.defn
async def search_linkedin_posts_by_person_to_s3(
    input_: SearchLinkedinPostsByPersonToS3Input,
) -> SearchLinkedinPostsByPersonToS3Output | PendingEnrichmentResponse:
    person_linkedin_url = input_.person_linkedin_url
    activity.logger.info(f"searching for linkedin posts for {person_linkedin_url}")

    research_agent_service = get_research_agent_service(await get_or_init_db_engine())
    latest_timestamp = (
        await research_agent_service.get_latest_timestamp_from_activities_by_intel_id(
            intel_id=input_.intel_person_id,
            intel_type=IntelType.PERSON,
        )
    )

    if latest_timestamp and latest_timestamp > datetime.now(tz=UTC) - timedelta(
        days=settings.research_agent_person_posts_ttl
    ):
        activity.logger.info(
            f"latest timestamp for {person_linkedin_url} is {latest_timestamp}, skipping fetch"
        )
        return SearchLinkedinPostsByPersonToS3Output(
            person_linkedin_post_s3key=None,
            person_linkedin_post_count=0,
        )

    # instantiate research agent
    research_agent_client = ResearchAgentClient()

    # do research
    response = await research_agent_client.get_person_posts_by_linkedin_url(
        url=person_linkedin_url,
        brightdata_snapshot_id=input_.snapshot_id,
        use_brightdata=input_.use_brightdata,
    )
    if isinstance(response, PendingEnrichmentResponse):
        return response

    post_list = response

    activity.logger.info(
        f"found {len(post_list.posts)} linkedin posts for {person_linkedin_url}"
    )

    s3_manager = S3ManagerExtResearch()
    s3key = s3_manager.get_s3key(
        intel_type=IntelType.PERSON,
        intel_id=str(input_.intel_person_id),
        epoch=get_research_epoch(input_.research_context),
        blob_name=BlobName.PERSON_POSTS,
    )

    s3_manager.put_blob(
        key=s3key,
        blob=post_list.model_dump_json(),
    )
    track_research_time(
        research_context=input_.research_context,
        intel_entity_id=input_.intel_person_id,
        extra_tags=["crustdata:list_linkedin_posts_by_person"],
    )

    return SearchLinkedinPostsByPersonToS3Output(
        person_linkedin_post_s3key=s3key,
        person_linkedin_post_count=len(post_list.posts),
    )


@activity.defn
async def search_linkedin_posts_by_company_to_s3(
    ctx: ResearchContext,
    input_: SearchLinkedinPostsByCompanyToS3Input,
) -> SearchLinkedinPostsByCompanyToS3Output | PendingEnrichmentResponse:
    linkedin_url = input_.company_linkedin_url
    activity.logger.info(f"searching for linkedin posts for {linkedin_url}")

    research_agent_service = get_research_agent_service(await get_or_init_db_engine())
    latest_timestamp = (
        await research_agent_service.get_latest_timestamp_from_activities_by_intel_id(
            intel_id=input_.intel_company_id,
            intel_type=IntelType.COMPANY,
        )
    )

    if latest_timestamp and latest_timestamp > datetime.now(tz=UTC) - timedelta(
        days=settings.research_agent_company_posts_ttl
    ):
        activity.logger.info(
            f"latest timestamp for {linkedin_url} is {latest_timestamp}, skipping fetch"
        )
        return SearchLinkedinPostsByCompanyToS3Output(
            company_linkedin_post_s3key=None,
            company_linkedin_post_count=0,
        )

    # instantiate research agent
    research_agent_client = ResearchAgentClient()

    # prioritize domain if provided
    response = await research_agent_client.get_company_posts(
        company_domain=input_.company_domain,
        linkedin_url=linkedin_url,
        brightdata_snapshot_id=input_.snapshot_id,
        use_brightdata=input_.use_brightdata,
    )

    if isinstance(response, PendingEnrichmentResponse):
        if (
            input_.company_domain
            and response.details
            and PendingEnrichmentDetails.NO_MATCHING_COMPANIES_POSTS_FOUND
            in response.details
        ):
            # try to use domain
            response = await research_agent_client.get_company_posts(
                company_domain=input_.company_domain,
                linkedin_url=None,
                brightdata_snapshot_id=input_.snapshot_id,
                use_brightdata=input_.use_brightdata,
            )

        if isinstance(response, PendingEnrichmentResponse):
            return response

    post_list = response

    activity.logger.info(
        f"found {len(post_list.posts)} linkedin posts for {linkedin_url}"
    )

    s3_manager = S3ManagerExtResearch()
    s3key = s3_manager.get_s3key(
        intel_type=IntelType.COMPANY,
        intel_id=str(input_.intel_company_id),
        epoch=get_research_epoch(input_.research_context),
        blob_name=BlobName.COMPANY_POSTS,
    )

    s3_manager.put_blob(
        key=s3key,
        blob=post_list.model_dump_json(),
    )
    track_research_time(
        research_context=input_.research_context,
        intel_entity_id=input_.intel_company_id,
        extra_tags=["crustdata:list_linkedin_posts_by_company"],
    )

    return SearchLinkedinPostsByCompanyToS3Output(
        company_linkedin_post_s3key=s3key,
        company_linkedin_post_count=len(post_list.posts),
    )
