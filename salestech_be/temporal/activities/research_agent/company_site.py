from temporalio import activity

from salestech_be.common import ree_llm
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest
from salestech_be.core.research_agent.models.company_activity import CompanySiteContent
from salestech_be.core.research_agent.types import (
    ResearchContext,
)
from salestech_be.integrations.s3.s3_manager_ext_research import (
    BlobName,
    IntelType,
    S3ManagerExtResearch,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.activities.research_agent.const import (
    GET_COMPANY_CASE_STUDIES_TRACE_NAME,
    GET_COMPANY_SITE_CONTENT_TRACE_NAME,
    SUMMARIZE_COMPANY_SITE_CONTENT_TRACE_NAME,
)
from salestech_be.temporal.activities.research_agent.utils import (
    get_research_epoch,
    track_research_time,
)
from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.temporal.workflows.research_agent.schema import (
    GetCompanySiteContentToS3Input,
    GetCompanySiteContentToS3Output,
)

logger = get_logger(__name__)

langfuse_prompt_service = get_langfuse_prompt_service()


async def get_company_site_content_with_grounding(
    ctx: ResearchContext, company_name: str, company_domain: str
) -> CompanySiteContent:
    combined_results = []

    # Do company name and domain based search
    if company_name:
        prompt_obj = await langfuse_prompt_service.get_prompt(
            request=PromptRequest(
                prompt_name=PromptEnum.RETRIEVE_GENERAL_COMPANY_INFORMATION,
                variables={
                    "company_name": company_name,
                    "company_domain": company_domain,
                },
            )
        )

        response = await ree_llm.acompletion(
            model=prompt_obj.get_model(),
            messages=prompt_obj.messages,
            tools=prompt_obj.get_tools(),
            metadata=ctx.to_litellm_metadata(GET_COMPANY_SITE_CONTENT_TRACE_NAME),
        )

        # Strip markdown tags
        combined_results.append(response.message_content)

    # Additional search specifically for case studies

    case_prompt_obj = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.RETRIEVE_COMPANY_CASE_STUDIES,
            variables={"company_name": company_name, "company_domain": company_domain},
        )
    )
    case_study_response = await ree_llm.acompletion(
        model=case_prompt_obj.get_model(),
        messages=case_prompt_obj.messages,
        tools=case_prompt_obj.get_tools(),
        metadata=ctx.to_litellm_metadata(GET_COMPANY_CASE_STUDIES_TRACE_NAME),
    )

    combined_results.append(case_study_response.message_content)
    combined_text = "\n\n".join(text for text in combined_results)

    content = await summarize_company_site_content(ctx, company_name, combined_text)
    track_research_time(
        research_context=ctx,
        extra_tags=["company:get_company_site_content"],
    )

    return content


@activity.defn
@with_tracing
async def get_company_site_content_to_s3(
    ctx: ResearchContext,
    input_: GetCompanySiteContentToS3Input,
) -> GetCompanySiteContentToS3Output:
    # NOTE
    # Return empty output if company site content is disabled
    # This is put here to avoid over complicating the workflow logic.
    if not settings.research_agent_enable_company_site_content:
        return GetCompanySiteContentToS3Output(
            company_site_content_s3key=None,
        )

    if not input_.company_domain:
        return GetCompanySiteContentToS3Output(
            company_site_content_s3key=None,
        )

    company_site_content = await get_company_site_content_with_grounding(
        ctx=ctx,
        company_name=input_.company_name,
        company_domain=input_.company_domain,
    )

    s3_manager = S3ManagerExtResearch()

    s3key = s3_manager.get_s3key(
        intel_type=IntelType.COMPANY,
        intel_id=str(input_.intel_company_id),
        epoch=get_research_epoch(ctx),
        blob_name=BlobName.COMPANY_SITE_CONTENT,
    )

    s3_manager.put_blob(
        key=s3key,
        blob=company_site_content.model_dump_json(),
    )
    track_research_time(
        research_context=ctx,
        extra_tags=["company:get_company_site_content_to_s3"],
    )

    return GetCompanySiteContentToS3Output(
        company_site_content_s3key=s3key,
    )


@activity.defn
@with_tracing
async def get_company_site_content_from_s3(
    ctx: ResearchContext,
    company_name: str,
    company_domain: str,
) -> CompanySiteContent:
    return await get_company_site_content_with_grounding(
        company_name=company_name,
        company_domain=company_domain,
        ctx=ctx,
    )


async def summarize_company_site_content(
    ctx: ResearchContext, company_name: str, content: str
) -> CompanySiteContent:
    prompt = f"""Summarize the following website content for {company_name}:

    {content}

    Extract the following information from the website content:
    1. What products and services they sell
    2. Based on their messaging and content, who appears to be their ideal customer profile
    3. Find and summarize any case studies or customer success stories (if any. if none, return None)

    Format the response with clear sections for products/services, ideal customer profile, customer logos, and case studies."""

    # NOTE: token limit is hardcoded. but there could be chance where the
    # returned text is an incomplete json str due to the limit (at least for some
    # models). There could be better way to handle that at the litellm layer hopefully.
    # we will just increase the limit from 500 to 2000 for now (in settings.py)
    response = await ree_llm.acompletion(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": prompt}],
        temperature=0,
        max_completion_tokens=settings.litellm_company_info_summarization_max_tokens,
        response_format=CompanySiteContent,
        metadata=ctx.to_litellm_metadata(SUMMARIZE_COMPANY_SITE_CONTENT_TRACE_NAME),
    )

    return response.message_content
