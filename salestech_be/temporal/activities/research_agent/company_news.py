import json
import uuid
from datetime import UTC, datetime, timedelta

from temporalio import activity

from salestech_be.common import ree_llm
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest
from salestech_be.core.research_agent.models.company_activity import (
    ListModelOfNews,
    NewsResult,
    NewsResultList,
)
from salestech_be.core.research_agent.models.company_info import CompanyResearchInfo
from salestech_be.core.research_agent.research_agent_client import ResearchAgentClient
from salestech_be.core.research_agent.research_agent_service import (
    get_research_agent_service,
)
from salestech_be.core.research_agent.research_metric import research_metric
from salestech_be.core.research_agent.types import ResearchContext
from salestech_be.integrations.crustdata.model import CrustdataCompanyInfo
from salestech_be.integrations.s3.s3_manager_ext_research import (
    BlobName,
    IntelType,
    S3ManagerExtResearch,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.activities.research_agent.const import (
    ResearchQualityMetrics,
)
from salestech_be.temporal.activities.research_agent.utils import (
    get_research_epoch,
)
from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.temporal.database import get_or_init_db_engine
from salestech_be.temporal.workflows.research_agent.schema import (
    GetCompanyNewsToS3Input,
    GetCompanyNewsToS3Output,
)

logger = get_logger(__name__)

langfuse_prompt_service = get_langfuse_prompt_service()


MIN_RESULTS = 5


async def get_company_news(
    ctx: ResearchContext,
    company_name: str,
    company_domain: str,
    enforce_company_name: bool,
    company_description: str | None = None,
) -> list[NewsResult]:
    if not settings.research_agent_enable_company_news:
        return []
    logger.bind(
        company_name=company_name,
        company_domain=company_domain,
        enforce_company_name=enforce_company_name,
        company_description=company_description,
        enable_scrapingdog=settings.research_agent_enable_scrapingdog_for_company_news,
    ).info("Research agent get company news")

    research_agent_client = ResearchAgentClient()
    if settings.research_agent_enable_scrapingdog_for_company_news:
        news = await research_agent_client.get_company_news(company_domain)
        news_json_string = json.dumps([news.model_dump() for news in news])
        return await get_formatted_company_news(
            ctx=ctx,
            company_name=company_name,
            company_domain=company_domain,
            company_description=company_description,
            news=news_json_string,
            enforce_company_name=enforce_company_name,
        )
    else:
        return await _get_company_news_by_gemini_grounding(
            ctx=ctx,
            company_name=company_name,
            company_domain=company_domain,
            company_description=company_description,
            enforce_company_name=enforce_company_name,
        )


async def _get_company_news_by_gemini_grounding(  # deprecated, keep as reference
    ctx: ResearchContext,
    company_name: str,
    company_domain: str,
    enforce_company_name: bool,
    company_description: str | None = None,
) -> list[NewsResult]:
    results = []
    for days in [30, 90, 180, 360]:
        results = await get_company_news_with_window_grounding(
            ctx=ctx,
            company_name=company_name,
            company_domain=company_domain,
            days=days,
            enforce_company_name=enforce_company_name,
            company_description=company_description,
        )
        if len(results) >= MIN_RESULTS:  # TODO: revisit if we should return early
            return results
    return results


def upload_company_news_to_s3(
    intel_company_id: uuid.UUID,
    research_epoch: int,
    news_items: list[NewsResult],
) -> str:
    news_list = NewsResultList(root=news_items)

    s3_manager = S3ManagerExtResearch()
    s3key = s3_manager.get_s3key(
        intel_type=IntelType.COMPANY,
        intel_id=str(intel_company_id),
        epoch=research_epoch,
        blob_name=BlobName.COMPANY_NEWS,
    )
    s3_manager.put_blob(
        key=s3key,
        blob=news_list.model_dump_json(),
    )
    return s3key


@activity.defn
@with_tracing
async def get_company_news_to_s3(
    ctx: ResearchContext,
    input_: GetCompanyNewsToS3Input,
) -> GetCompanyNewsToS3Output:
    company_name = input_.company_name
    company_domain = input_.company_domain

    if not company_domain:
        return GetCompanyNewsToS3Output(
            company_news_s3key=None,
            company_news_count=0,
        )

    engine = await get_or_init_db_engine()
    research_agent_service = get_research_agent_service(engine)
    latest_timestamp = (
        await research_agent_service.get_latest_timestamp_from_activities_by_intel_id(
            intel_id=input_.intel_company_id,
            intel_type=IntelType.COMPANY,
        )
    )

    company_description: str | None = None

    if input_.company_info_s3key:
        s3_manager = S3ManagerExtResearch()

        # download company info from S3
        company_info_blob = s3_manager.get_blob(input_.company_info_s3key)
        company_info = CrustdataCompanyInfo.model_validate_json(company_info_blob)
        company_description = company_info.linkedin_company_description

    if not company_description:
        intel_company_info = (
            await research_agent_service.get_intel_company_info_by_intel_id(
                intel_company_id=input_.intel_company_id,
            )
        )
        if intel_company_info and intel_company_info.data:
            company_info_dict = intel_company_info.data.get("company_info")
            if company_info_dict:
                company_research_info = CompanyResearchInfo.model_validate(
                    company_info_dict
                )
                company_description = company_research_info.linkedin_company_description

    if latest_timestamp and latest_timestamp > datetime.now(UTC) - timedelta(
        days=settings.research_agent_company_news_ttl
    ):
        activity.logger.info(
            f"latest timestamp for {company_domain} is {latest_timestamp}, skipping fetch"
        )
        return GetCompanyNewsToS3Output(
            company_news_s3key=None,
            company_news_count=0,
        )

    results = await get_company_news(
        ctx=ctx,
        company_name=company_name,
        company_domain=company_domain,
        enforce_company_name=False,
        company_description=company_description,
    )

    return GetCompanyNewsToS3Output(
        company_news_s3key=upload_company_news_to_s3(
            input_.intel_company_id,
            get_research_epoch(ctx),
            results,
        ),
        company_news_count=len(results),
    )


async def get_company_news_with_window_grounding(
    ctx: ResearchContext,
    company_name: str,
    company_domain: str,
    enforce_company_name: bool,
    days: int = 30,
    company_description: str | None = None,
) -> list[NewsResult]:
    start_date = (datetime.now(tz=UTC) - timedelta(days=days)).strftime("%Y-%m-%d")
    news = await get_company_news_from_gemini_grounding(
        ctx=ctx,
        start_date=start_date,
        company_name=company_name,
        company_domain=company_domain,
        company_description=company_description,
    )
    if news is None:
        return []
    return await get_formatted_company_news(
        ctx=ctx,
        company_name=company_name,
        company_domain=company_domain,
        news=news,
        enforce_company_name=enforce_company_name,
    )


async def get_company_news_from_gemini_grounding(
    ctx: ResearchContext,
    start_date: str,
    company_name: str,
    company_domain: str,
    company_description: str | None = None,
) -> str | None:
    variables = {
        "company_name": company_name,
        "company_domain": company_domain,
        "start_date": start_date,
    }

    if company_description:
        variables["company_description_base"] = company_description

    prompt_obj = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.RESEARCH_AGENT_COMPANY_NEWS_GROUNDING,
            variables=variables,
        )
    )
    output = None
    try:
        response = await ree_llm.acompletion(
            model=prompt_obj.get_model(),
            messages=prompt_obj.messages,
            tools=prompt_obj.get_tools(),  # this is specific to the prompt
            metadata=ctx.to_litellm_metadata(
                PromptEnum.RESEARCH_AGENT_COMPANY_NEWS_GROUNDING.value
            ),
        )
        output = response.message_content
    except Exception as e:
        logger.bind(
            error=e,
            model=prompt_obj.get_model(),
            config=prompt_obj.config,
            messages=prompt_obj.messages,
            tools=prompt_obj.get_tools(),
        ).error(  # leaving it as error for now for debugging visibility
            "Error: An issue occurred while parsing the LLM response from gemini grounding"
        )
    return output


async def get_formatted_company_news(
    ctx: ResearchContext,
    company_name: str,
    company_domain: str,
    news: str,
    enforce_company_name: bool,
    company_description: str | None = None,
) -> list[NewsResult]:
    variables = {
        "company_name": company_name,
        "company_domain": company_domain,
        "news": news,
    }
    if company_description:
        variables["company_description"] = company_description

    format_prompt_obj = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.RESEARCH_AGENT_COMPANY_NEWS_FORMATTING,
            variables=variables,
        )
    )
    try:
        json_output = await ree_llm.acompletion(
            model=format_prompt_obj.get_model(),
            messages=format_prompt_obj.messages,
            temperature=0,
            max_completion_tokens=settings.litellm_company_info_summarization_max_tokens,
            metadata=ctx.to_litellm_metadata(
                PromptEnum.RESEARCH_AGENT_COMPANY_NEWS_FORMATTING.value,
            ),
        )
        formatted_news = ListModelOfNews.model_validate_json(
            json_output.message_content
        )
        news_items = formatted_news.news
    except Exception as e:
        logger.bind(
            error=e,
            model=format_prompt_obj.get_model(),
            config=format_prompt_obj.config,
            messages=format_prompt_obj.messages,
        ).warning(
            "Error: An issue occurred while parsing the LLM response, returning empty list of news articles"
        )

        return []

    if settings.research_agent_enable_scrapingdog_for_company_news:
        return news_items

    filtered_results = []

    for article in news_items:
        if enforce_company_name and (
            (
                article.title is not None
                and company_name.lower() not in article.title.lower()
            )
            or (
                article.summary is not None
                and company_name.lower() not in article.summary.lower()
            )
        ):
            continue

        research_metric.increment(
            metric_name=ResearchQualityMetrics.COMPANY_NEWS_GROUNDING_URL_ALL
        )
        if article.url and article.url.find("vertexaisearch") != -1:
            filtered_results.append(article)
            logger.info(f"Adding article with valid url: {article.url}")
            research_metric.increment(
                metric_name=ResearchQualityMetrics.COMPANY_NEWS_GROUNDING_URL_NON_VERTEX
            )
        else:
            logger.info(f"Skipping article due to hallucinated url: {article.url}")
            research_metric.increment(
                metric_name=ResearchQualityMetrics.COMPANY_NEWS_GROUNDING_URL_VERTEX
            )

    return filtered_results
