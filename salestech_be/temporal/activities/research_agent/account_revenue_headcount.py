from temporalio import activity

from salestech_be.core.research_agent.utils import normalize_headcount
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.models.account import Account
from salestech_be.integrations.crustdata.model import CrustdataCompanyInfo
from salestech_be.integrations.s3.s3_manager_ext_research import S3ManagerExtResearch
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.activities.research_agent.utils import track_research_time
from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.temporal.database import get_or_init_db_engine
from salestech_be.temporal.workflows.research_agent.schema import (
    UpdateAccountRevenueHeadcountFromS3Input,
)

logger = get_logger(__name__)


@activity.defn
@with_tracing
async def update_account_revenue_headcount_from_s3(  # noqa: C901
    input_: UpdateAccountRevenueHeadcountFromS3Input,
) -> None:
    # deprecated by TemporalPatch.SKIP_COMPANY_HEADCOUNT_UPDATE
    if (s3key := input_.company_info_s3key) is None:
        logger.info("no company info s3 key; will not update account revenue/headcount")
        return
    # download PersonInfo from S3
    # parse PersonInfo to get the profile picture url
    s3_manager = S3ManagerExtResearch()
    company_info_blob = s3_manager.get_blob(s3key)
    company_info = CrustdataCompanyInfo.model_validate_json(company_info_blob)

    # access the account db and update the revenue and headcount fields
    try:
        account_db = AccountRepository(engine=await get_or_init_db_engine())

        if not company_info.company_website_domain:
            logger.bind(company_info=company_info).warning(
                "company_website_domain is not available"
            )
            return
        queried_accounts = await account_db.find_accounts_by_domain_names_only(
            [company_info.company_website_domain]
        )

        logger.debug(queried_accounts)

        headcount = (
            company_info.employee_count_range
            if company_info.employee_count_range
            else None
        )
        revenue = (
            company_info.estimated_revenue_higher_bound_usd
            if company_info.estimated_revenue_higher_bound_usd
            else None
        )

        if headcount:
            upper_bound = normalize_headcount(headcount)

        current_account_revenue = None
        current_account_headcount = None
        if queried_accounts:
            queried_account = queried_accounts[0]
            # check if the revenue and headcount fields are already set
            current_account = await account_db.find_by_primary_key(
                Account, id=queried_account.id
            )
            if current_account:
                if current_account.estimated_annual_revenue:
                    current_account_revenue = current_account.estimated_annual_revenue
                if current_account.estimated_employee_count:
                    current_account_headcount = current_account.estimated_employee_count

            # for the queried account, update the revenue and headcount fields
            if revenue and not current_account_revenue:
                await account_db.update_by_primary_key(
                    Account,
                    primary_key_to_value={"id": queried_account.id},
                    column_to_update={"estimated_annual_revenue": revenue},
                )
            if headcount and not current_account_headcount:
                await account_db.update_by_primary_key(
                    Account,
                    primary_key_to_value={"id": queried_account.id},
                    column_to_update={"estimated_employee_count": upper_bound},
                )

        else:
            logger.bind(company_info=company_info).warning(
                "No account found for company domain"
            )

    except Exception as e:
        logger.bind(error=e).warning(
            "Unable to update account revenue/headcount based on company info"
        )

    track_research_time(
        research_context=input_.research_context,
        intel_entity_id=input_.intel_company_id,
        extra_tags=["company:update_account_revenue_headcount_from_s3"],
    )
