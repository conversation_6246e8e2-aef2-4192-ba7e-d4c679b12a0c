from temporalio import activity

from salestech_be.core.research_agent.research_agent_service import (
    get_research_agent_service,
)
from salestech_be.core.research_agent.types import (
    IntelInputType,
    ResearchStatus,
)
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.activities.research_agent.utils import track_research_time
from salestech_be.temporal.database import get_or_init_db_engine
from salestech_be.temporal.workflows.research_agent.schema import (
    GetIntelPersonFromEmailInput,
    GetIntelPersonFromEmailOutput,
)

logger = get_logger(__name__)


@activity.defn
async def get_or_create_intel_person_from_email(
    input_data: GetIntelPersonFromEmailInput,
) -> GetIntelPersonFromEmailOutput | None:
    """
    Looks up a person's LinkedIn URL using their email and creates an intel person record
    if a LinkedIn URL is found.

    Args:
        input_data: Contains business_email, contact_id, and organization_id

    Returns:
        The intel_person_id if a LinkedIn URL is found and an intel person record is created,
        None otherwise
    """
    logger.bind(input_data=input_data).info("intel person from email input")

    # Initialize services
    db_engine = await get_or_init_db_engine()
    research_agent_service = get_research_agent_service(db_engine=db_engine)
    # Try to get LinkedIn URL using email
    (
        linkedin_url,
        linkedin_url_source,
    ) = await research_agent_service.get_linkedin_url_by_email(
        input_data.business_email
    )

    if not linkedin_url:
        logger.bind(input_data=input_data).info(
            "intel person from email no linkedin url found"
        )
        return None

    logger.bind(input_data=input_data, linkedin_url=linkedin_url).info(
        "intel person from email found linkedin url"
    )

    # Create intel person record
    intel_person_association = await research_agent_service.get_or_create_intel_person(
        input_data.contact_id, linkedin_url
    )

    # Update intel person input info
    await research_agent_service.update_intel_person_input_info(
        intel_person_id=intel_person_association.intel_person_id,
        input_type=IntelInputType.BUSINESS_EMAIL,
        linkedin_url_source=linkedin_url_source,
        status=ResearchStatus.PENDING,
    )

    if input_data.research_context:
        input_data.research_context.research_time.intel_created_at = (
            intel_person_association.created_at.timestamp() * 1e9
        )

        track_research_time(
            research_context=input_data.research_context,
            intel_entity_id=intel_person_association.intel_person_id,
            extra_tags=["person:get_or_create_intel_person_from_email"],
        )

    return GetIntelPersonFromEmailOutput(
        intel_person_id=intel_person_association.intel_person_id,
        business_email=input_data.business_email,
        linkedin_url=linkedin_url,
    )
