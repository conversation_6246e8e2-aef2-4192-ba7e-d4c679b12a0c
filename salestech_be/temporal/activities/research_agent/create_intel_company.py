import time

from temporalio import activity

from salestech_be.core.research_agent.research_agent_client import (
    get_research_agent_client,
)
from salestech_be.core.research_agent.research_agent_service import (
    get_research_agent_service,
)
from salestech_be.core.research_agent.types import (
    IntelInputType,
    IntelLinkedinUrlSource,
    ResearchStatus,
)
from salestech_be.temporal.database import get_or_init_db_engine
from salestech_be.temporal.workflows.research_agent.schema import (
    GetCompanyLinkedinUrlByDomainInput,
    GetCompanyLinkedinUrlByDomainOutput,
)


@activity.defn
async def get_or_create_intel_company_by_linkedin_url(
    input_: GetCompanyLinkedinUrlByDomainInput,
) -> GetCompanyLinkedinUrlByDomainOutput:
    # if only domain is provided, we need to find the linkedin url
    research_agent_service = get_research_agent_service(
        db_engine=await get_or_init_db_engine()
    )

    domain = input_.company_domain
    linkedin_url = input_.linkedin_url

    research_agent_client = get_research_agent_client()

    if not domain and not linkedin_url:
        return GetCompanyLinkedinUrlByDomainOutput(
            intel_company_id=None,
            company_linkedin_url=None,
            domain=None,
        )
    elif linkedin_url and not domain:
        domain = await research_agent_client.get_company_domain_from_linkedin_url(
            linkedin_url
        )
    elif domain and not linkedin_url:
        linkedin_url = await research_agent_client.get_linkedin_url_from_domain(domain)

        if linkedin_url is None:
            return GetCompanyLinkedinUrlByDomainOutput(
                intel_company_id=None,
                company_linkedin_url=None,
                domain=domain,
            )

    intel_company_association = (
        await research_agent_service.get_or_create_intel_company(
            account_id=input_.account_id,
            account_domain_name=domain,
            linkedin_url=linkedin_url,
        )
    )

    if input_.research_context and input_.research_context.research_time:
        # temporal does not impose restrictions on time lib in activities
        input_.research_context.research_time.intel_created_at = time.time_ns()

    await research_agent_service.update_intel_company_input_info(
        intel_company_id=intel_company_association.intel_company_id,
        input_type=IntelInputType.LINKEDIN_URL
        if input_.linkedin_url
        else IntelInputType.DOMAIN,
        linkedin_url_source=IntelLinkedinUrlSource.USER_INPUT,
        status=ResearchStatus.PENDING,
    )

    return GetCompanyLinkedinUrlByDomainOutput(
        intel_company_id=intel_company_association.intel_company_id,
        company_linkedin_url=linkedin_url,
        domain=domain,
        research_context=input_.research_context,
    )
