from datetime import UTC, datetime, timedelta

from nameparser import <PERSON><PERSON>ame
from temporalio import activity

from salestech_be.core.research_agent.models.company_info import (
    CompanyResearchInfoList,
)
from salestech_be.core.research_agent.models.person_info import (
    <PERSON><PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    PersonResearchInfo,
)
from salestech_be.core.research_agent.models.response import (
    PendingEnrichmentDetails,
    PendingEnrichmentResponse,
)
from salestech_be.core.research_agent.research_agent_client import ResearchAgentClient
from salestech_be.core.research_agent.research_agent_service import (
    ResearchAgentService,
    get_research_agent_service,
)
from salestech_be.core.research_agent.types import ResearchContext
from salestech_be.integrations.s3.s3_manager_ext_research import (
    BlobName,
    IntelType,
    S3ManagerExtResearch,
)
from salestech_be.settings import settings
from salestech_be.temporal.activities.research_agent.utils import (
    get_research_epoch,
    track_research_time,
)
from salestech_be.temporal.database import get_or_init_db_engine
from salestech_be.temporal.workflows.research_agent.schema import (
    GetCompanyInfoToS3Input,
    GetCompanyInfoToS3Output,
    GetPersonInfoToS3Input,
    GetPersonInfoToS3Output,
)


async def _get_person_linkedin_url(
    input_: GetPersonInfoToS3Input,
    research_agent_service: ResearchAgentService,
) -> str | None:
    person_linkedin_url = (
        input_.person_linkedin_urls[0] if input_.person_linkedin_urls else None
    )

    if not person_linkedin_url:
        intel_person = await research_agent_service.get_intel_person_by_id(
            intel_person_id=input_.intel_person_id,
        )
        if intel_person:
            person_linkedin_url = intel_person.linkedin

    return person_linkedin_url


@activity.defn
async def get_person_info_and_save_to_s3(
    input_: GetPersonInfoToS3Input,
) -> GetPersonInfoToS3Output | PendingEnrichmentResponse:
    activity.logger.info(
        f"searching for person info for {input_.person_linkedin_urls} or {input_.business_emails}"
    )

    research_agent_service = get_research_agent_service(await get_or_init_db_engine())
    intel_person_info = await research_agent_service.get_intel_person_info_by_intel_id(
        intel_person_id=input_.intel_person_id,
    )

    if intel_person_info:
        latest_timestamp = intel_person_info.updated_at

        if latest_timestamp and latest_timestamp > datetime.now(tz=UTC) - timedelta(
            days=settings.research_agent_person_info_ttl
        ):
            activity.logger.info(
                f"latest timestamp for {input_.person_linkedin_urls} is {latest_timestamp}, skipping fetch"
            )
            return GetPersonInfoToS3Output(
                person_info_s3key=None,
                person_info_count=0,
            )

    if not settings.research_worker_enable_crustdata_person_info:
        return GetPersonInfoToS3Output()

    research_agent_client = ResearchAgentClient()

    person_linkedin_url = await _get_person_linkedin_url(
        input_=input_,
        research_agent_service=research_agent_service,
    )

    business_email = input_.business_emails[0] if input_.business_emails else None

    response = await research_agent_client.get_person_info_by_linkedin_url_or_email(
        person_linkedin_url=person_linkedin_url,
        business_email=business_email,
        brightdata_snapshot_id=input_.snapshot_id,
        use_brightdata=input_.use_brightdata,
    )
    if isinstance(response, PendingEnrichmentResponse):
        return response

    raw_info_list = list[PersonResearchInfo]()
    for person in response.infos:
        if isinstance(person, PersonResearchInfo) and person.name:
            name = HumanName(person.name)
            person.first_name = name.first
            person.last_name = name.last
            raw_info_list.append(person)

    # we'll dump an empty list to s3 so the following activities can
    # still work and not writing info to database
    s3_manager = S3ManagerExtResearch()

    s3key = s3_manager.get_s3key(
        intel_type=IntelType.PERSON,
        intel_id=str(input_.intel_person_id),
        epoch=get_research_epoch(input_.research_context),
        blob_name=BlobName.PERSON_INFO,
    )

    s3_manager.put_blob_b(
        key=s3key,
        blob=PersonInfoListAdapter.dump_json(raw_info_list),
    )
    resolved_linkedin_url: str | None = (
        input_.person_linkedin_urls[0] if input_.person_linkedin_urls else None
    )

    if not resolved_linkedin_url:
        if raw_info_list and raw_info_list[0].linkedin_flagship_url:
            resolved_linkedin_url = str(raw_info_list[0].linkedin_flagship_url)
        else:
            activity.logger.warning(
                f"empty raw_info_list of {input_.person_linkedin_urls} or {input_.business_emails}"
            )

    track_research_time(
        research_context=input_.research_context,
        intel_entity_id=input_.intel_person_id,
        extra_tags=["person:get_person_info_and_save_to_s3"],
    )

    return GetPersonInfoToS3Output(
        person_info_s3key=s3key,
        person_info_count=len(raw_info_list),
        person_resolved_linkedin_url=resolved_linkedin_url,
    )


@activity.defn
async def get_company_info_to_s3(
    ctx: ResearchContext,
    input_: GetCompanyInfoToS3Input,
) -> GetCompanyInfoToS3Output | PendingEnrichmentResponse:
    activity.logger.info(f"searching for company info for {input_.company_domain}")

    research_agent_service = get_research_agent_service(await get_or_init_db_engine())
    intel_company_info = (
        await research_agent_service.get_intel_company_info_by_intel_id(
            intel_company_id=input_.intel_company_id,
        )
    )

    if intel_company_info:
        latest_timestamp = intel_company_info.updated_at

        if latest_timestamp and latest_timestamp > datetime.now(tz=UTC) - timedelta(
            days=settings.research_agent_company_info_ttl
        ):
            activity.logger.info(
                f"latest timestamp for {input_.company_domain} is {latest_timestamp}, skipping fetch"
            )
            return GetCompanyInfoToS3Output(
                company_info_s3key=None,
                company_infos_s3key=None,
                company_infos_count=0,
            )

    # instantiate research agent
    research_agent_client = ResearchAgentClient()

    # do research
    # prioritize domain if provided
    response = await research_agent_client.get_company_info(
        linkedin_url=input_.company_linkedin_url,
        company_domain=input_.company_domain,
        display_name=input_.company_name,
        brightdata_snapshot_id=input_.snapshot_id,
        use_brightdata=input_.use_brightdata,
    )

    if isinstance(response, PendingEnrichmentResponse):
        activity.logger.info(
            f"pending enrichment response {response}for {input_.company_domain} or {input_.company_linkedin_url}"
        )
        if (
            input_.company_domain
            and response.details
            and PendingEnrichmentDetails.NO_MATCHING_COMPANIES_INFO_FOUND
            in response.details
        ):
            # try to use domain
            response = await research_agent_client.get_company_info(
                linkedin_url=None,
                company_domain=input_.company_domain,
                display_name=input_.company_name,
                brightdata_snapshot_id=input_.snapshot_id,
                use_brightdata=input_.use_brightdata,
            )

        if isinstance(response, PendingEnrichmentResponse):
            return response

    company_research_info_list = response.infos
    activity.logger.info(
        f"fetched company info for {len(company_research_info_list)} companies"
    )
    info_list = CompanyResearchInfoList(root=company_research_info_list)

    s3_manager = S3ManagerExtResearch()
    infos_s3key = s3_manager.get_s3key(
        intel_type=IntelType.COMPANY,
        intel_id=str(input_.intel_company_id),
        epoch=get_research_epoch(input_.research_context),
        blob_name=BlobName.COMPANY_INFOS,
    )

    s3_manager.put_blob(
        key=infos_s3key,
        blob=info_list.model_dump_json(),
    )

    # dedupe company_infos sharing the same company_domain
    company_info = next(
        (
            x
            for x in company_research_info_list
            if x.company_name == input_.company_name
        ),
        None,
    )
    if not company_info and company_research_info_list:
        company_info = company_research_info_list[0]

    info_s3key = None
    if company_info:
        info_s3key = s3_manager.get_s3key(
            intel_type=IntelType.COMPANY,
            intel_id=str(input_.intel_company_id),
            epoch=get_research_epoch(input_.research_context),
            blob_name=BlobName.COMPANY_INFO,
        )

        s3_manager.put_blob(
            key=info_s3key,
            blob=company_info.model_dump_json(),
        )

    return GetCompanyInfoToS3Output(
        company_info_s3key=info_s3key,
        # TODO
        # company_infos_s3key is not used anywhere, we should remove it
        company_infos_s3key=infos_s3key,
        company_infos_count=len(company_research_info_list),
    )
