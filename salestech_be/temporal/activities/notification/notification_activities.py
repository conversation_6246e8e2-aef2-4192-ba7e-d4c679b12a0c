from uuid import UUID

from temporalio import activity

from salestech_be.core.notification.service.notification_service import (
    get_notification_service_by_db_engine,
)
from salestech_be.core.notification.types import NotificationWrapper
from salestech_be.core.task.service.task_query_service import (
    get_task_query_service_from_engine,
)
from salestech_be.core.user.service.user_service import get_user_service_general
from salestech_be.db.models.notification import (
    NotificationDataType,
    NotificationReferenceIdType,
    NotificationTaskCreateData,
    NotificationTaskDueSoonData,
    NotificationTaskOverdueData,
    NotificationWorkflow,
)
from salestech_be.db.models.task import TaskStatus
from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.temporal.database import get_or_init_db_engine
from salestech_be.util.time import zoned_utc_now


@activity.defn
async def send_notification_activity(
    notification_id: UUID,
    organization_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    notification_service = get_notification_service_by_db_engine(db_engine)
    await notification_service.send_notification_to_provider(
        notification_id, organization_id=organization_id
    )


@activity.defn
@with_tracing
async def trigger_task_notification(
    trigger_type: NotificationWorkflow,
    task_id: UUID,
    organization_id: UUID,
    activity_id: UUID | None = None,
    due_time: str | None = None,
) -> None:
    try:
        db_engine = await get_or_init_db_engine()
        task_query_service = get_task_query_service_from_engine(db_engine)
        task = await task_query_service.get_by_id_v2(
            task_id=task_id,
            organization_id=organization_id,
            exclude_deleted_or_archived=False,
        )

        if task is None or task.status in [TaskStatus.COMPLETED, TaskStatus.CLOSED]:
            return

        if task.archived_at:
            activity.logger.warning(
                "Task is archived, skipping notification",
                extra={"task_id": task_id, "organization_id": organization_id},
            )
            return

        notification_service = get_notification_service_by_db_engine(db_engine)
        recipient_user_ids: list[UUID] = []
        actor_user_id = None
        notification_data: NotificationDataType
        if trigger_type == NotificationWorkflow.TASK_DUE_SOON:
            if due_time is None:
                return
            notification_data = NotificationTaskDueSoonData(
                task_name=task.title,
                due_time=due_time,
                task_page=f"/tasks?taskId={task_id}",
            )
            idempotency_key = f"task_due_soon_{task_id}_{due_time}"
            recipient_user_ids = [task.owner_user_id] if task.owner_user_id else []
        elif trigger_type == NotificationWorkflow.TASK_OVERDUE:
            if due_time is None:
                return
            notification_data = NotificationTaskOverdueData(
                task_name=task.title,
                due_time=due_time,
                task_page=f"/tasks?taskId={task_id}",
            )
            idempotency_key = f"task_overdue_{task_id}_{zoned_utc_now()}"
            recipient_user_ids = [task.owner_user_id] if task.owner_user_id else []
        elif trigger_type == NotificationWorkflow.TASK_CREATE:
            user_service = get_user_service_general(db_engine=db_engine)
            recipient_user_ids = (
                [task.created_by_user_id] if task.created_by_user_id else []
            )
            if task.owner_user_id and task.owner_user_id != task.created_by_user_id:
                recipient_user_ids.append(task.owner_user_id)
            task_owner_user = (
                await user_service.get_by_id_and_organization_id(
                    user_id=task.owner_user_id, organization_id=organization_id
                )
                if task.owner_user_id
                else None
            )
            actor_user_id = task.created_by_user_id
            notification_data = NotificationTaskCreateData(
                task_name=task.title,
                assignee_user_id=str(task_owner_user.id) if task_owner_user else None,
                assignee_name=task_owner_user.display_name if task_owner_user else None,
                task_page=f"/tasks?taskId={task_id}",
            )
            idempotency_key = f"task_create_{task_id}"
        else:
            return

        await notification_service.trigger_notification(
            notification=NotificationWrapper(
                data=notification_data,
                recipient_user_ids=recipient_user_ids,
                idempotency_key=idempotency_key,
                actor_user_id=actor_user_id,
            ),
            organization_id=organization_id,
            reference_id=task_id,
            reference_id_type=NotificationReferenceIdType.TASK,
            activity_id=activity_id,
        )

    except Exception as e:
        activity.logger.error(
            "Failed to trigger task notification",
            extra={
                "organization_id": organization_id,
                "task_id": task_id,
                "notification_type": trigger_type,
                "error": str(e),
            },
        )
        raise
