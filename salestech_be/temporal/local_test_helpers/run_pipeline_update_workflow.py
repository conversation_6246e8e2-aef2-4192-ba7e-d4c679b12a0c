from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    import asyncio
    from uuid import UUID

    from salestech_be.core.ai.event_handlers.pipeline_update import (
        start_pipeline_update_workflow,
    )
    from salestech_be.core.ai.workflows.schema import PipelineUpdateReason
    from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


async def main() -> None:
    logger.info("Starting pipeline update workflow")
    await start_pipeline_update_workflow(
        organization_id=UUID("3351f748-ffa7-414f-8afc-f0a591cbc27b"),
        pipeline_id=UUID("479f9e6d-ff12-4051-a9c8-943abe482618"),
        reason=PipelineUpdateReason.CONTACT_PIPELINE_ROLE_CLASSIFICATION,
    )


if __name__ == "__main__":
    asyncio.run(main())
