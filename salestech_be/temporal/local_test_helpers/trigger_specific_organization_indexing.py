"""Helper script to trigger the IndexAllOrganizationsWorkflow locally."""

import asyncio
import uuid

from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import TemporalTaskQueue
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.workflows.falkordb.indexing_workflows import (
    IndexSpecificOrganizationInput,
    IndexSpecificOrganizationWorkflow,
)

logger = get_logger(__name__)


async def main() -> None:
    """Connects to Temporal and starts the workflow."""
    logger.info("Connecting to Temporal client...")
    client = await get_temporal_client()

    workflow_id = f"index-all-orgs-{uuid.uuid4()}"
    logger.info(
        f"Starting IndexAllOrganizationsWorkflow with ID: {workflow_id} on task queue: {TemporalTaskQueue.FALKOR_TASK_QUEUE}"
    )

    try:
        handle = await client.start_workflow(
            IndexSpecificOrganizationWorkflow.run,  # The workflow run method
            args=[
                IndexSpecificOrganizationInput(
                    org_id="4cfb4ddd-61b1-4bcf-810e-17f5fa6c4294", batch_size=10000
                )
            ],
            id=workflow_id,
            task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
        )
        logger.info(
            f"Workflow started successfully. Workflow ID: {handle.id}, Run ID: {handle.first_execution_run_id}"
        )
        # Optionally wait for the result if needed, otherwise just log start
        # result = await handle.result()
        # logger.info(f"Workflow completed with result: {result}")

    except Exception:
        logger.exception("Failed to start IndexAllOrganizationsWorkflow")


if __name__ == "__main__":
    asyncio.run(main())
