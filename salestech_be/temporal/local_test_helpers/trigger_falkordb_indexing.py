"""Helper script to trigger the IndexAllOrganizationsWorkflow locally."""

import asyncio
import uuid

from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import TemporalTaskQueue
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.workflows.falkordb.indexing_workflows import (
    IndexAllOrganizationsWorkflow,
)

logger = get_logger(__name__)


async def main() -> None:
    """Connects to Temporal and starts the workflow."""
    logger.info("Connecting to Temporal client...")
    client = await get_temporal_client()

    workflow_id = f"index-all-orgs-{uuid.uuid4()}"
    logger.info(
        f"Starting IndexAllOrganizationsWorkflow with ID: {workflow_id} on task queue: {TemporalTaskQueue.FALKOR_TASK_QUEUE}"
    )

    try:
        handle = await client.start_workflow(
            IndexAllOrganizationsWorkflow.run,  # The workflow run method
            id=workflow_id,
            task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
            # No arguments needed for this workflow's run method
        )
        logger.info(
            f"Workflow started successfully. Workflow ID: {handle.id}, Run ID: {handle.first_execution_run_id}"
        )
        # Optionally wait for the result if needed, otherwise just log start
        # result = await handle.result()
        # logger.info(f"Workflow completed with result: {result}")

    except Exception:
        logger.exception("Failed to start IndexAllOrganizationsWorkflow")


if __name__ == "__main__":
    asyncio.run(main())
