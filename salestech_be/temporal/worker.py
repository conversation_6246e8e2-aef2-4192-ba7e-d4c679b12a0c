import argparse
import asyncio
import dataclasses

import sentry_sdk
from sentry_sdk.integrations.asyncio import AsyncioIntegration
from temporalio import workflow
from temporalio.worker import Worker, WorkerTuner
from temporalio.worker.workflow_sandbox import (
    SandboxedWorkflowRunner,
    SandboxRestrictions,
)

from salestech_be.core.ai.activities.meeting_pipeline_intel_activities import (
    generate_meeting_pipeline_intel_tasks,
)
from salestech_be.core.ai.email.activities import get_latest_message_from_global_thread
from salestech_be.core.ai.opportuntity_stages.activities.generate_criteria import (
    generate_criteria,
)
from salestech_be.core.ai.opportuntity_stages.activities.generate_sales_action_role_classification import (
    generate_contact_pipeline_role_classification,
    generate_sales_action_classification,
)
from salestech_be.core.ai.tasks.activities.close_existing_tasks import (
    close_existing_tasks,
)
from salestech_be.core.ai.tasks.activities.generate_intel_tasks import (
    generate_intel_tasks,
)
from salestech_be.core.ai.tasks.workflows.close_existing_tasks import (
    CloseExistingTasksWorkflow,
)
from salestech_be.core.ai.workflows.pipeline_update_workflow import (
    PipelineUpdateWorkflow,
    generate_multi_meeting_transcript,
)
from salestech_be.core.ai.workflows.sales_action_role_classification_workflow import (
    SalesActionRoleClassificationWorkflow,
)
from salestech_be.core.ai.workflows.stage_criteria_workflow import StageCriteriaWorkflow
from salestech_be.core.crm_integrity.atomic_operation_activities.contact_email_related_activity import (
    ContactEmailRelatedActivity,
)
from salestech_be.core.crm_integrity.atomic_operation_activities.phone_number_related_activity import (
    PhoneNumberRelatedActivity,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.add_account_to_pipeline_workflow import (
    AddAccountToPipelineWorkflow,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.archive_account_workflow import (
    ArchiveAccountWorkflow,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.integrity_job_dispatch_workflow import (
    IntegrityJobDispatchWorkflow,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.merge_accounts_workflow import (
    MergeAccountsWorkflow,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.merge_contacts_workflow import (
    MergeContactsWorkflow,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.move_contact_email_to_account_workflow import (
    MoveContactEmailToAccountWorkflow,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.move_contact_email_to_contact_workflow import (
    MoveContactEmailToContactWorkflow,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.move_contact_to_account_workflow import (
    MoveContactToAccountWorkflow,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.remove_contact_email_from_account_workflow import (
    RemoveContactEmailFromAccountWorkflow,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.remove_contact_email_from_contact_workflow import (
    RemoveContactEmailFromContactWorkflow,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.remove_contact_from_account_workflow import (
    RemoveContactFromAccountWorkflow,
)
from salestech_be.core.workflow.activities.manually_run_node_activity import (
    ManuallyRunNodeActivity,
)
from salestech_be.core.workflow.activities.webhook_received_node_activity import (
    WebhookReceivedNodeActivity,
)
from salestech_be.integrations.temporal.config import (
    AI_TASK_QUEUE,
    CRM_SYNC_TASK_QUEUE,
    INTEGRITY_JOB_TASK_QUEUE,
    VOICE_TASK_QUEUE,
    ResearchTaskQueue,
)
from salestech_be.search.activities.es_index import ElasticsearchIndexActivities
from salestech_be.search.workflows.es_index import (
    AccountIndexWorkflow,
    ContactIndexWorkflow,
    CustomObjectIndexWorkflow,
    MeetingIndexWorkflow,
    PipelineIndexWorkflow,
)
from salestech_be.temporal.activities.email.send_email import (
    persist_email_activity,
    send_email,
)
from salestech_be.temporal.activities.intel_converters import (
    get_intel_company_id_from_account_id,
    get_intel_person_id_from_contact_id,
)
from salestech_be.temporal.activities.meeting.meeting_agent import (
    process_meeting_agent_task,
)
from salestech_be.temporal.activities.meeting.meeting_research import (
    get_account_and_contact_ids_from_meeting_id,
)
from salestech_be.temporal.activities.meeting.meeting_tracker_stats import (
    backfill_meeting_tracker_stats_activity,
    generate_meeting_tracker_stats_activity,
)
from salestech_be.temporal.activities.research_agent.account_revenue_headcount import (
    update_account_revenue_headcount_from_s3,
)
from salestech_be.temporal.activities.research_agent.company_news import (
    get_company_news_to_s3,
)
from salestech_be.temporal.activities.research_agent.company_site import (
    get_company_site_content_to_s3,
)
from salestech_be.temporal.activities.research_agent.create_intel_company import (
    get_or_create_intel_company_by_linkedin_url,
)
from salestech_be.temporal.activities.research_agent.db import (
    save_company_info_from_s3,
    save_intel_company_funding_from_s3,
    save_intel_company_news_from_s3,
    save_intel_company_posts_from_s3,
    save_intel_person_activities_from_s3,
    save_person_intel_from_s3,
    update_company_intel_info_provider_status,
    update_intel_status,
    update_person_info_from_s3,
    update_person_intel_info_provider_status,
)
from salestech_be.temporal.activities.research_agent.intel_person_email import (
    get_or_create_intel_person_from_email,
)
from salestech_be.temporal.activities.research_agent.linkedin_posts import (
    search_linkedin_posts_by_company_to_s3,
    search_linkedin_posts_by_person_to_s3,
)
from salestech_be.temporal.activities.research_agent.linkedin_profile import (
    get_company_info_to_s3,
    get_person_info_and_save_to_s3,
)
from salestech_be.temporal.activities.research_agent.schedule import (
    schedule_research_activity,
)
from salestech_be.temporal.activities.research_agent.user_intel_profile_pic import (
    update_user_profile_pic_from_linkedin_from_s3,
)
from salestech_be.temporal.worker_config import (
    TEMPORAL_WORKER_CONFIG_MAP,
    TemporalWorkerConfig,
    TemporalWorkerTask,
)
from salestech_be.temporal.workflows.ai.fsm import FSMAIWorkflow
from salestech_be.temporal.workflows.external_crm_sync import ExternalCrmSyncWorkflow
from salestech_be.temporal.workflows.logical_propagation import (
    LogicalPropagationEventProcessorWorkflow,
    LogicalPropagationWorkflow,
)
from salestech_be.temporal.workflows.meeting.meeting_agent import MeetingAgentWorkflow
from salestech_be.temporal.workflows.meeting.meeting_tracker_stats_backill import (
    MeetingTrackerStatsBackfillWorkflow,
)
from salestech_be.temporal.workflows.meeting_research import MeetingResearchWorkflow
from salestech_be.temporal.workflows.research_agent.research_company_workflow import (
    ResearchCompanyWorkflow,
)
from salestech_be.temporal.workflows.research_agent.research_person_workflow import (
    ResearchPersonWorkflow,
)
from salestech_be.temporal.workflows.voice.handle_follow_up_email_failure import (
    HandleFollowUpEmailFailureWorkflow,
)
from salestech_be.web.api.imports.activities.csv import (
    process_csv_import_v2,
)
from salestech_be.web.api.imports.workflows.entity_Import_workflow import (
    EntityImportWorkflow,
)

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.ai.activities.meeting_pipeline_intel_activities import (
        generate_meeting_activity_summary,
        generate_meeting_how_to_win,
        generate_meeting_objections,
        generate_meeting_risks,
        upsert_pipeline_intel,
    )
    from salestech_be.core.ai.email.activities import (
        classify_message_and_update_metadata,
        parse_main_body_text_and_persist,
    )
    from salestech_be.core.ai.email.workflows.global_thread_cdc_event_workflow import (
        GlobalThreadCDCEventWorkflow,
    )
    from salestech_be.core.ai.workflows.intel_workflow import IntelWorkflow
    from salestech_be.core.ai.workflows.trigger_pipeline_intel import (
        MeetingTriggerPipelineIntelWorkflow,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.account_related_activity import (
        AccountRelatedActivities,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.associate_contact_with_account_activity import (
        AssociateContactWithAccountActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.associate_contact_with_pipeline_activity import (
        AssociateContactWithPipelineActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.contact_related_activity import (
        ContactRelatedActivities,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.integrity_job_activity import (
        IntegrityJobActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.intelligence_related_activity import (
        IntelligenceRelatedActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.move_pipeline_to_alternative_contact_activity import (
        MovePipelineToAlternativeContactActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.move_pipeline_to_new_account_activity import (
        MovePipelineToNewAccountActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.pipeline_related_activity import (
        PipelineRelatedActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.unassociate_contact_with_account_activity import (
        UnassociateContactWithAccountActivity,
    )
    from salestech_be.core.crm_integrity.event_processors.processor_activity import (
        data_operation_event_processor_activity,
    )
    from salestech_be.core.crm_integrity.integrity_operation_workflows.archive_contact_workflow import (
        ArchiveContactWorkflow,
    )
    from salestech_be.core.crm_integrity.utils_activities.send_notification_activity import (
        crm_data_integrity_job_send_notification_activity,
    )
    from salestech_be.core.workflow.activities.adjust_time_node_activity import (
        AdjustTimeNodeActivity,
    )
    from salestech_be.core.workflow.activities.contact_enrichment_node_activity import (
        ContactEnrichmentNodeActivity,
    )
    from salestech_be.core.workflow.activities.create_record_node_activity import (
        CreateRecordNodeActivity,
    )
    from salestech_be.core.workflow.activities.cron_schedule_node_activity import (
        CronScheduleNodeActivity,
    )
    from salestech_be.core.workflow.activities.delay_node_activity import (
        DelayNodeActivity,
    )
    from salestech_be.core.workflow.activities.delay_until_node_activity import (
        DelayUntilNodeActivity,
    )
    from salestech_be.core.workflow.activities.email_notification_node_activity import (
        EmailNotificationNodeActivity,
    )
    from salestech_be.core.workflow.activities.find_records_node_activity import (
        FindRecordsNodeActivity,
    )
    from salestech_be.core.workflow.activities.form_submission_node_activity import (
        FormSubmissionNodeActivity,
    )
    from salestech_be.core.workflow.activities.gateway_node_activity import (
        GatewayNodeActivity,
    )
    from salestech_be.core.workflow.activities.if_else_condition_node_activity import (
        IfElseNodeActivity,
    )
    from salestech_be.core.workflow.activities.one_time_node_activity import (
        OneTimeNodeActivity,
    )
    from salestech_be.core.workflow.activities.random_number_node_activity import (
        RandomNumberNodeActivity,
    )
    from salestech_be.core.workflow.activities.record_created_node_activity import (
        RecordCreatedNodeActivity,
    )
    from salestech_be.core.workflow.activities.record_updated_node_activity import (
        RecordUpdatedNodeActivity,
    )
    from salestech_be.core.workflow.activities.round_robin_node_activity import (
        RoundRobinNodeActivity,
    )
    from salestech_be.core.workflow.activities.send_http_request_node_activity import (
        SendHttpRequestNodeActivity,
    )
    from salestech_be.core.workflow.activities.send_slack_message_node_activity import (
        SendSlackMessageNodeActivity,
    )
    from salestech_be.core.workflow.activities.switch_condition_node_activity import (
        SwitchNodeActivity,
    )
    from salestech_be.core.workflow.activities.update_record_node_activity import (
        UpdateRecordNodeActivity,
    )
    from salestech_be.core.workflow.service.workflow_execution_service import (
        get_workflow_execution_service,
    )
    from salestech_be.integrations.temporal.client import get_temporal_client
    from salestech_be.integrations.temporal.config import (
        DEFAULT_TASK_QUEUE,
        MEETING_TASK_QUEUE,
    )
    from salestech_be.ree_logging import get_logger
    from salestech_be.settings import settings
    from salestech_be.temporal.activities.external_crm_sync_activity import (
        process_external_crm_sync_activity,
    )
    from salestech_be.temporal.activities.logical_propagation import (
        evaluate_and_execute_logical_propagation_rules,
    )
    from salestech_be.temporal.activities.meeting.clear_meeting_bots import (
        clear_future_meeting_bots_activity,
    )
    from salestech_be.temporal.activities.meeting.meeting_completion import (
        complete_analyze_meeting_activity,
        end_active_meeting_activity,
    )
    from salestech_be.temporal.activities.meeting.meeting_recording import (
        persist_external_media_data_activity,
        store_recording_activity,
    )
    from salestech_be.temporal.activities.meeting.meeting_timing_check import (
        detect_meeting_timing_errors_activity,
        meeting_bot_timing_errors_activity,
        meeting_calendar_event_timing_errors_activity,
    )
    from salestech_be.temporal.activities.meeting.propagate_bot_name_change import (
        propagate_bot_name_change_activity,
    )
    from salestech_be.temporal.activities.organization.organization_backfill_activities import (
        backfill_organization_activity,
    )
    from salestech_be.temporal.activities.prompt_generation import (
        PromptGenerationActivities,
    )
    from salestech_be.temporal.activities.voice.release_phone_numbers import (
        release_deleted_phone_numbers_activity,
    )
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.temporal.workflows.backfill_permissions_workflow import (
        BackfillAllOrganizationPermissionsWorkflow,
        BackfillOrganizationPermissionsWorkflow,
    )
    from salestech_be.temporal.workflows.meeting.clear_meeting_bots import (
        ClearFutureMeetingBotsWorkflow,
    )
    from salestech_be.temporal.workflows.meeting.meeting_bot_name_change import (
        MeetingBotNameChangeWorkflow,
    )
    from salestech_be.temporal.workflows.meeting.meeting_completion import (
        MeetingCompletionWorkflow,
        MeetingEndWorkflow,
    )
    from salestech_be.temporal.workflows.meeting.meeting_recording import (
        MeetingRecordingWorkflow,
    )
    from salestech_be.temporal.workflows.meeting.meeting_timing_check import (
        MeetingTimingCheckWorkflow,
    )
    from salestech_be.temporal.workflows.organization.organization_backfill_workflow import (
        OrganizationBackfillWorkflow,
    )
    from salestech_be.temporal.workflows.prompt_generation import (
        PromptGenerationWorkflow,
    )
    from salestech_be.temporal.workflows.voice.release_phone_numbers import (
        ReleasePhoneNumbersWorkflow,
    )
    from salestech_be.temporal.workflows.voice.send_follow_up_email import (
        VoiceCallFollowUpEmailWorkflow,
        get_input_for_send_email_activity,
    )

logger = get_logger()


# https://github.com/temporalio/samples-python/blob/main/pydantic_converter/worker.py
# Due to known issues with Pydantic's use of issubclass and our inability to
# override the check in sandbox, Pydantic will think datetime is actually date
# in the sandbox. At the expense of protecting against datetime.now() use in
# workflows, we're going to remove datetime module restrictions. See sdk-python
# README's discussion of known sandbox issues for more details.
def new_sandbox_runner() -> SandboxedWorkflowRunner:
    # TODO(cretz): Use with_child_unrestricted when https://github.com/temporalio/sdk-python/issues/254
    # is fixed and released
    invalid_module_member_children = dict(
        SandboxRestrictions.invalid_module_members_default.children
    )
    del invalid_module_member_children["datetime"]
    return SandboxedWorkflowRunner(
        restrictions=dataclasses.replace(
            SandboxRestrictions.default,
            invalid_module_members=dataclasses.replace(
                SandboxRestrictions.invalid_module_members_default,
                children=invalid_module_member_children,
            ),
        )
    )


async def run_default_activity_worker(args: argparse.Namespace) -> None:
    logger.info(
        f"Starting worker: settings.temporal_host_url: {settings.temporal_host_url}, settings.temporal_namespace: {settings.temporal_namespace}"
    )
    client = await get_temporal_client()
    db_engine = await get_or_init_db_engine()
    workflow_execution_service = get_workflow_execution_service(db_engine=db_engine)
    contact_enrichment_node_activity = ContactEnrichmentNodeActivity(
        db_engine=db_engine
    )
    cron_schedule_node_activity = CronScheduleNodeActivity(db_engine=db_engine)
    email_notification_node_activity = EmailNotificationNodeActivity(
        db_engine=db_engine
    )
    form_submission_node_activity = FormSubmissionNodeActivity(db_engine=db_engine)
    if_else_node_activity = IfElseNodeActivity(db_engine=db_engine)
    one_time_node_activity = OneTimeNodeActivity(db_engine=db_engine)
    create_record_node_activity = CreateRecordNodeActivity(db_engine=db_engine)
    update_record_node_activity = UpdateRecordNodeActivity(db_engine=db_engine)
    record_created_node_activity = RecordCreatedNodeActivity(db_engine=db_engine)
    record_updated_node_activity = RecordUpdatedNodeActivity(db_engine=db_engine)
    round_robin_node_activity = RoundRobinNodeActivity(db_engine=db_engine)
    switch_node_activity = SwitchNodeActivity(db_engine=db_engine)
    adjust_time_node_activity = AdjustTimeNodeActivity(db_engine=db_engine)
    random_number_node_activity = RandomNumberNodeActivity(db_engine=db_engine)
    delay_node_activity = DelayNodeActivity(db_engine=db_engine)
    delay_until_node_activity = DelayUntilNodeActivity(db_engine=db_engine)
    manually_run_node_activity = ManuallyRunNodeActivity(db_engine=db_engine)
    webhook_received_node_activity = WebhookReceivedNodeActivity(db_engine=db_engine)
    send_http_request_node_activity = SendHttpRequestNodeActivity(db_engine=db_engine)
    send_slack_message_node_activity = SendSlackMessageNodeActivity(db_engine=db_engine)
    find_records_node_activity = FindRecordsNodeActivity(db_engine=db_engine)
    gateway_node_activity = GatewayNodeActivity(db_engine=db_engine)
    elasticsearch_index_activities = ElasticsearchIndexActivities()

    worker = Worker(
        client,
        task_queue=args.main_queue or DEFAULT_TASK_QUEUE,
        max_concurrent_activities=settings.default_worker_concurrent_size,
        workflows=[],
        activities=[
            evaluate_and_execute_logical_propagation_rules,
            process_meeting_agent_task,
            generate_intel_tasks,
            elasticsearch_index_activities.index_accounts,
            elasticsearch_index_activities.index_pipelines,
            elasticsearch_index_activities.index_meetings,
            elasticsearch_index_activities.index_contacts,
            elasticsearch_index_activities.index_custom_objects,
            send_email,
            persist_email_activity,
            *workflow_execution_service.activities,
            *contact_enrichment_node_activity.activities,
            *cron_schedule_node_activity.activities,
            *email_notification_node_activity.activities,
            *form_submission_node_activity.activities,
            *if_else_node_activity.activities,
            *one_time_node_activity.activities,
            *create_record_node_activity.activities,
            *update_record_node_activity.activities,
            *round_robin_node_activity.activities,
            *switch_node_activity.activities,
            *adjust_time_node_activity.activities,
            *random_number_node_activity.activities,
            *delay_node_activity.activities,
            *delay_until_node_activity.activities,
            *record_created_node_activity.activities,
            *record_updated_node_activity.activities,
            *manually_run_node_activity.activities,
            *webhook_received_node_activity.activities,
            *send_http_request_node_activity.activities,
            *send_slack_message_node_activity.activities,
            *find_records_node_activity.activities,
            *gateway_node_activity.activities,
            get_latest_message_from_global_thread,
            parse_main_body_text_and_persist,
            classify_message_and_update_metadata,
            backfill_organization_activity,
        ],
        workflow_runner=new_sandbox_runner(),
    )

    await worker.run()


async def run_default_workflow_worker(args: argparse.Namespace) -> None:
    logger.info(
        f"Starting default workflow worker: settings.temporal_host_url: {settings.temporal_host_url}, settings.temporal_namespace: {settings.temporal_namespace}"
    )
    client = await get_temporal_client()

    worker = Worker(
        client,
        task_queue=args.main_queue or DEFAULT_TASK_QUEUE,
        workflows=[
            LogicalPropagationWorkflow,
            LogicalPropagationEventProcessorWorkflow,
            PipelineIndexWorkflow,
            MeetingIndexWorkflow,
            AccountIndexWorkflow,
            ContactIndexWorkflow,
            IntelWorkflow,
            CustomObjectIndexWorkflow,
            BackfillAllOrganizationPermissionsWorkflow,
            BackfillOrganizationPermissionsWorkflow,
            GlobalThreadCDCEventWorkflow,
            OrganizationBackfillWorkflow,
        ],
        activities=[],
        workflow_runner=new_sandbox_runner(),
    )

    await worker.run()


async def run_crm_sync_worker() -> None:
    logger.info(
        f"Starting crm-sync worker: settings.temporal_host_url: {settings.temporal_host_url}, settings.temporal_namespace: {settings.temporal_namespace}"
    )
    client = await get_temporal_client()
    # db_engine = await get_or_init_db_engine()

    # Then start the worker
    worker = Worker(
        client,
        task_queue=CRM_SYNC_TASK_QUEUE,
        # Meeting WFs took couple of minutes to finish, put env variable to tune the
        # worker performance
        max_concurrent_workflow_tasks=settings.crm_sync_worker_concurrent_size,
        max_concurrent_activities=settings.crm_sync_worker_concurrent_size,
        workflows=[
            ExternalCrmSyncWorkflow,
            EntityImportWorkflow,
        ],
        activities=[
            process_csv_import_v2,
            process_external_crm_sync_activity,
        ],
        workflow_runner=new_sandbox_runner(),
    )

    await worker.run()


async def run_meeting_worker() -> None:
    logger.info(
        f"Starting meeting worker: settings.temporal_host_url: {settings.temporal_host_url}, settings.temporal_namespace: {settings.temporal_namespace}"
    )
    client = await get_temporal_client()
    db_engine = await get_or_init_db_engine()
    prompt_generation_activities = PromptGenerationActivities(engine=db_engine)

    # Then start the worker
    worker = Worker(
        client,
        task_queue=MEETING_TASK_QUEUE,
        # Meeting WFs took couple of minutes to finish, put env variable to tune the
        # worker performance
        max_concurrent_workflow_tasks=settings.meeting_worker_concurrent_size,
        max_concurrent_activities=settings.meeting_worker_concurrent_size,
        workflows=[
            ClearFutureMeetingBotsWorkflow,
            PromptGenerationWorkflow,
            MeetingRecordingWorkflow,
            MeetingCompletionWorkflow,
            MeetingEndWorkflow,
            MeetingTimingCheckWorkflow,
            MeetingTrackerStatsBackfillWorkflow,
            MeetingBotNameChangeWorkflow,
            MeetingAgentWorkflow,
            IntelWorkflow,
        ],
        activities=[
            prompt_generation_activities.generate_prompt,
            store_recording_activity,
            persist_external_media_data_activity,
            complete_analyze_meeting_activity,
            end_active_meeting_activity,
            detect_meeting_timing_errors_activity,
            generate_meeting_tracker_stats_activity,
            backfill_meeting_tracker_stats_activity,
            propagate_bot_name_change_activity,
            process_meeting_agent_task,
            generate_intel_tasks,
            clear_future_meeting_bots_activity,
            meeting_calendar_event_timing_errors_activity,
            meeting_bot_timing_errors_activity,
        ],
        workflow_runner=new_sandbox_runner(),
    )

    await worker.run()


async def run_research_activity_worker(task_queue: ResearchTaskQueue) -> None:
    logger.info(
        f"starting research activity worker with task queue: {task_queue.value}"
    )

    # Set up sentry
    sentry_sdk.init(
        dsn=settings.sentry_dsn,
        send_default_pii=True,
        traces_sample_rate=settings.sentry_traces_sample_rate_temporal_worker_research,
        profiles_sample_rate=settings.sentry_profiles_sample_rate_temporal_worker_research,
        _experiments={
            "continuous_profiling_auto_start": settings.sentry_auto_profiling_temporal_worker_research,
        },
        integrations=[
            AsyncioIntegration(),
        ],
    )
    sentry_sdk.profiler.start_profiler()

    try:
        client = await get_temporal_client()

        # Create a worker tuner with resource-based slot suppliers
        _ = WorkerTuner.create_resource_based(
            target_memory_usage=0.6, target_cpu_usage=0.8
        )

        worker = Worker(
            client,
            task_queue=task_queue.value,
            # NOTE tuner can't co-exist with max_ options https://docs.temporal.io/develop/worker-performance
            # NOTE we're trying out fixed slots instead of tuner
            max_concurrent_workflow_tasks=0,
            max_concurrent_activities=100,
            max_cached_workflows=0,
            # tuner=tuner,
            workflows=[],  # NOTE: all wf processing moved to dedicated wf worker below
            activities=[
                update_account_revenue_headcount_from_s3,
                update_person_info_from_s3,
                save_company_info_from_s3,
                save_person_intel_from_s3,
                save_intel_company_funding_from_s3,
                save_intel_company_news_from_s3,
                save_intel_company_posts_from_s3,
                save_intel_person_activities_from_s3,
                update_user_profile_pic_from_linkedin_from_s3,
                get_account_and_contact_ids_from_meeting_id,
                get_intel_company_id_from_account_id,
                get_intel_person_id_from_contact_id,
                schedule_research_activity,
                update_intel_status,
                update_person_intel_info_provider_status,
                update_company_intel_info_provider_status,
                get_or_create_intel_company_by_linkedin_url,
            ],
            workflow_runner=new_sandbox_runner(),
        )
        await worker.run()
    finally:
        sentry_sdk.profiler.stop_profiler()


async def run_research_wf_worker(task_queue: ResearchTaskQueue) -> None:
    """
    research wf worker without custom sandbox to mitigate stuck temporal job issue.
    only taking temporal wf task, no act tasks.
    """
    logger.info(f"starting research wf worker with task queue: {task_queue.value}")

    # Set up sentry
    sentry_sdk.init(
        dsn=settings.sentry_dsn,
        send_default_pii=True,
        traces_sample_rate=settings.sentry_traces_sample_rate_temporal_worker_research,
        profiles_sample_rate=settings.sentry_profiles_sample_rate_temporal_worker_research,
        _experiments={
            "continuous_profiling_auto_start": settings.sentry_auto_profiling_temporal_worker_research,
        },
        integrations=[
            AsyncioIntegration(),
        ],
    )
    sentry_sdk.profiler.start_profiler()

    try:
        client = await get_temporal_client()

        # Create a worker tuner with resource-based slot suppliers
        # WARNING: tuner is still experimental (REEVO-4488, REEVO-4654)
        _ = WorkerTuner.create_resource_based(
            target_memory_usage=0.6, target_cpu_usage=0.8
        )

        worker = Worker(
            client,
            task_queue=task_queue.value,
            # NOTE tuner can't co-exist with max_ options https://docs.temporal.io/develop/worker-performance
            # NOTE we're trying out fixed slots instead of tuner
            max_concurrent_workflow_tasks=settings.research_worker_max_concurrent_wf_tasks,
            max_concurrent_workflow_task_polls=settings.research_worker_max_concurrent_wf_tasks_polls,
            max_concurrent_activities=0,
            max_cached_workflows=10,
            # tuner=tuner,
            workflows=[
                ResearchCompanyWorkflow,
                ResearchPersonWorkflow,
                MeetingResearchWorkflow,
            ],
            activities=[],
            # NOTE: no sandbox for research wf worker to avoid stuck temporal job issue
            # workflow_runner=new_sandbox_runner(),
        )
        await worker.run()
    finally:
        sentry_sdk.profiler.stop_profiler()


async def run_research_activity_throttled_worker(task_queue: ResearchTaskQueue) -> None:
    logger.info(
        f"starting research activity worker with task queue: {task_queue.value}"
    )
    # Set up sentry
    sentry_sdk.init(
        dsn=settings.sentry_dsn,
        send_default_pii=True,
        traces_sample_rate=settings.sentry_traces_sample_rate_temporal_worker_research,
        profiles_sample_rate=settings.sentry_profiles_sample_rate_temporal_worker_research,
        _experiments={
            "continuous_profiling_auto_start": settings.sentry_auto_profiling_temporal_worker_research,
        },
        integrations=[
            AsyncioIntegration(),
        ],
    )
    sentry_sdk.profiler.start_profiler()

    try:
        client = await get_temporal_client()

        # Create a worker tuner with resource-based slot suppliers
        _ = WorkerTuner.create_resource_based(
            target_memory_usage=0.6, target_cpu_usage=0.8
        )

        is_lowpri = task_queue == ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED

        worker = Worker(
            client,
            task_queue=task_queue.value,
            # NOTE tuner can't co-exist with max_ options https://docs.temporal.io/develop/worker-performance
            # NOTE we're trying out fixed slots instead of tuner
            max_concurrent_workflow_tasks=0,
            max_concurrent_activities=100,
            max_cached_workflows=0,
            # https://github.com/temporalio/sdk-python/blob/1.10.0/temporalio/worker/_worker.py#L165-L174
            max_activities_per_second=(
                settings.research_agent_max_activities_per_second_lowpri
                if is_lowpri
                else settings.research_agent_max_activities_per_second
            )
            if settings.research_agent_enable_throttling
            else None,
            # tuner=tuner,
            workflows=[],  # NOTE: all wf processing moved to dedicated wf worker below
            activities=[
                get_company_info_to_s3,
                get_company_news_to_s3,
                get_company_site_content_to_s3,
                search_linkedin_posts_by_company_to_s3,
                get_person_info_and_save_to_s3,
                search_linkedin_posts_by_person_to_s3,
                get_or_create_intel_person_from_email,
                get_or_create_intel_company_by_linkedin_url,
            ],
            workflow_runner=new_sandbox_runner(),
        )
        await worker.run()
    finally:
        sentry_sdk.profiler.stop_profiler()


async def run_ai_worker() -> None:
    logger.info(f"Starting ai worker with task queue: {AI_TASK_QUEUE}")
    client = await get_temporal_client()

    worker = Worker(
        client,
        task_queue=AI_TASK_QUEUE,
        max_concurrent_workflow_tasks=15,
        max_concurrent_activities=15,
        workflows=[
            FSMAIWorkflow,
            MeetingTriggerPipelineIntelWorkflow,
            IntelWorkflow,
            CloseExistingTasksWorkflow,
            StageCriteriaWorkflow,
            SalesActionRoleClassificationWorkflow,
            PipelineUpdateWorkflow,
        ],
        activities=[
            generate_meeting_activity_summary,
            generate_meeting_how_to_win,
            generate_meeting_risks,
            generate_meeting_objections,
            generate_meeting_pipeline_intel_tasks,
            generate_intel_tasks,
            upsert_pipeline_intel,
            close_existing_tasks,
            generate_criteria,
            generate_sales_action_classification,
            generate_contact_pipeline_role_classification,
            generate_multi_meeting_transcript,
        ],
        workflow_runner=new_sandbox_runner(),
    )
    await worker.run()


async def run_voice_worker() -> None:
    logger.info(f"Starting voice worker with task queue: {VOICE_TASK_QUEUE}")
    client = await get_temporal_client()

    worker = Worker(
        client,
        task_queue=VOICE_TASK_QUEUE,
        workflows=[
            ReleasePhoneNumbersWorkflow,
            VoiceCallFollowUpEmailWorkflow,
            HandleFollowUpEmailFailureWorkflow,
        ],
        activities=[
            release_deleted_phone_numbers_activity,
            get_input_for_send_email_activity,
            send_email,
            persist_email_activity,
        ],
        workflow_runner=new_sandbox_runner(),
    )
    await worker.run()


async def run_integrity_job_worker() -> None:
    logger.info(
        f"Starting integrity job worker with task queue: {INTEGRITY_JOB_TASK_QUEUE}"
    )
    client = await get_temporal_client()
    db_engine = await get_or_init_db_engine()

    integrity_job_activity = IntegrityJobActivity(db_engine=db_engine)
    associate_contact_with_account_activity = AssociateContactWithAccountActivity(
        db_engine=db_engine
    )
    contact_related_activities = ContactRelatedActivities(db_engine=db_engine)
    associate_contact_with_pipeline_activity = AssociateContactWithPipelineActivity(
        db_engine=db_engine
    )

    integrity_job_activity = IntegrityJobActivity(db_engine=db_engine)
    associate_contact_with_account_activity = AssociateContactWithAccountActivity(
        db_engine=db_engine
    )
    move_pipeline_to_new_account_activity = MovePipelineToNewAccountActivity(
        db_engine=db_engine
    )
    unassociate_contact_with_account_activity = UnassociateContactWithAccountActivity(
        db_engine=db_engine
    )
    account_related_activities = AccountRelatedActivities(db_engine=db_engine)
    move_pipeline_to_alternative_contact_activity = (
        MovePipelineToAlternativeContactActivity(db_engine=db_engine)
    )
    contact_email_related_activities = ContactEmailRelatedActivity(db_engine=db_engine)
    phone_number_related_activities = PhoneNumberRelatedActivity(db_engine=db_engine)
    intelligence_activity = IntelligenceRelatedActivity(db_engine=db_engine)
    pipeline_related_activity = PipelineRelatedActivity(db_engine=db_engine)

    worker = Worker(
        client,
        task_queue=INTEGRITY_JOB_TASK_QUEUE,
        workflows=[
            IntegrityJobDispatchWorkflow,
            MergeContactsWorkflow,
            MergeAccountsWorkflow,
            MoveContactToAccountWorkflow,
            RemoveContactFromAccountWorkflow,
            MoveContactEmailToAccountWorkflow,
            MoveContactEmailToContactWorkflow,
            RemoveContactEmailFromAccountWorkflow,
            RemoveContactEmailFromContactWorkflow,
            ArchiveContactWorkflow,
            ArchiveAccountWorkflow,
            AddAccountToPipelineWorkflow,
        ],
        activities=[
            integrity_job_activity.create_integrity_job,
            integrity_job_activity.update_integrity_job,
            integrity_job_activity.update_integrity_job_error,
            integrity_job_activity.acquire_locks_for_entities,
            integrity_job_activity.free_locks_for_entities,
            contact_related_activities.archive_contact,
            associate_contact_with_account_activity.copy_single_contact_association_from_account_to_account,
            associate_contact_with_account_activity.copy_contact_associations_from_contact_to_contact,
            associate_contact_with_account_activity.copy_contact_associations_from_account_to_account,
            unassociate_contact_with_account_activity.unassociate_all_contacts_with_single_account,
            unassociate_contact_with_account_activity.unassociate_single_contact_with_all_accounts,
            unassociate_contact_with_account_activity.unassociate_single_contact_with_single_account,
            associate_contact_with_pipeline_activity.copy_pipelines_associations_from_contact_to_contact,
            move_pipeline_to_new_account_activity.move_pipelines_to_new_account,
            account_related_activities.archive_account,
            contact_email_related_activities.move_contact_email_to_account,
            contact_email_related_activities.remove_contact_email_from_account,
            contact_email_related_activities.delete_contact_email,
            contact_email_related_activities.move_contact_email_to_contact,
            contact_email_related_activities.move_contact_emails_from_contact_to_contact,
            phone_number_related_activities.move_contact_phone_numbers_from_contact_to_contact,
            intelligence_activity.person_intel_activity_job,
            intelligence_activity.pipeline_intel_activity_job,
            pipeline_related_activity.get_pipelines_by_contact_id,
            pipeline_related_activity.list_pipelines_by_account_id,
            contact_email_related_activities.move_contact_emails_from_account_to_account,
            contact_email_related_activities.move_contact_emails_from_contact_to_account,
            contact_email_related_activities.remove_contact_emails_from_account,
            crm_data_integrity_job_send_notification_activity,
            move_pipeline_to_alternative_contact_activity.move_pipeline_to_alternative_contact,
            data_operation_event_processor_activity,
        ],
        workflow_runner=new_sandbox_runner(),
    )
    await worker.run()


async def run_worker(worker_type: TemporalWorkerTask) -> None:
    client = await get_temporal_client()

    config: TemporalWorkerConfig | None = TEMPORAL_WORKER_CONFIG_MAP.get(worker_type)
    if not config:
        raise ValueError(f"Unsupported worker_type: {worker_type}")

    logger.bind(config=config.model_dump()).info(
        f"Starting {worker_type} worker with configs"
    )

    await get_or_init_db_engine(pool_size=int(config.db_pool_size))

    worker = Worker(
        client=client,
        task_queue=config.task_queue,
        max_concurrent_workflow_tasks=config.concurrency.max_concurrent_workflow_tasks,
        max_concurrent_activities=config.concurrency.max_concurrent_activities,
        max_concurrent_workflow_task_polls=config.concurrency.max_concurrent_workflow_task_polls,
        max_concurrent_activity_task_polls=config.concurrency.max_concurrent_activity_task_polls,
        workflows=config.workflows,
        activities=config.activities,
        workflow_runner=new_sandbox_runner(),
    )
    await worker.run()


async def main() -> None:  # noqa: C901, PLR0912
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--main-queue", type=str, help="temporal task queue to use for main worker"
    )
    parser.add_argument(
        "-t",
        "--task",
        type=str,
        choices=[task.value for task in TemporalWorkerTask],
        default=TemporalWorkerTask.DEFAULT_WORKFLOW.value,
        help="Specify the task to run from the choice list",
    )
    args = parser.parse_args()

    if args.task == "default_activity":
        await run_default_activity_worker(args)
    if args.task == "default_workflow":
        await run_default_workflow_worker(args)
    elif args.task == "meeting_all":
        await run_meeting_worker()
    elif args.task == "ai_all":
        await run_ai_worker()
    elif args.task == "research_activity":
        await run_research_activity_worker(ResearchTaskQueue.RESEARCH_TASK_QUEUE)
    elif args.task == "research_wf":
        await run_research_wf_worker(ResearchTaskQueue.RESEARCH_TASK_QUEUE)
    elif args.task == "research_activity_lowpri":
        await run_research_activity_worker(ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW)
    elif args.task == "research_wf_lowpri":
        await run_research_wf_worker(ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW)
    elif args.task == TemporalWorkerTask.RESEARCH_ACTIVITY_THROTTLED.value:
        await run_research_activity_throttled_worker(
            ResearchTaskQueue.RESEARCH_TASK_QUEUE_THROTTLED
        )
    elif args.task == TemporalWorkerTask.RESEARCH_ACTIVITY_THROTTLED_LOWPRI.value:
        await run_research_activity_throttled_worker(
            ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED
        )
    elif args.task == "falkor_activity":
        await run_worker(TemporalWorkerTask.FALKOR_ACTIVITY)
    elif args.task == "falkor_workflow":
        await run_worker(TemporalWorkerTask.FALKOR_WORKFLOW)
    elif args.task in (
        TemporalWorkerTask.CALENDAR_ALL.value,
        TemporalWorkerTask.NOTIFICATION_ALL.value,
    ):
        # Leverage calendar worker pod for notification workflows
        await asyncio.gather(
            run_worker(TemporalWorkerTask.CALENDAR_ALL),
            run_worker(TemporalWorkerTask.NOTIFICATION_ALL),
        )
    elif args.task == TemporalWorkerTask.EMAIL_WORKFLOW_ALL.value:
        await run_worker(TemporalWorkerTask.EMAIL_WORKFLOW_ALL)
    elif args.task == TemporalWorkerTask.EMAIL_ACTIVITY_ALL.value:
        await run_worker(TemporalWorkerTask.EMAIL_ACTIVITY_ALL)
    elif args.task == TemporalWorkerTask.EMAIL_IMAP.value:
        await run_worker(TemporalWorkerTask.EMAIL_IMAP)
    elif args.task == TemporalWorkerTask.PROSPECTING.value:
        await run_worker(TemporalWorkerTask.PROSPECTING)
    elif args.task == "crm_sync_all":
        await run_crm_sync_worker()
    elif args.task == "voice_all":
        await run_voice_worker()
    elif args.task == TemporalWorkerTask.DOMAIN_CRM_ASSOCIATION.value:
        await run_worker(TemporalWorkerTask.DOMAIN_CRM_ASSOCIATION)
    elif args.task == TemporalWorkerTask.SEQUENCE_WORKFLOW.value:
        await run_worker(TemporalWorkerTask.SEQUENCE_WORKFLOW)
    elif args.task == TemporalWorkerTask.SEQUENCE_ACTIVITY.value:
        await run_worker(TemporalWorkerTask.SEQUENCE_ACTIVITY)
    elif args.task == TemporalWorkerTask.INTEGRITY_JOB_ALL.value:
        await run_integrity_job_worker()


if __name__ == "__main__":
    asyncio.run(main())
