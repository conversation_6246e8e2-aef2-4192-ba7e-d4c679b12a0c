import uuid
from typing import Any

from pydantic import BaseModel, model_validator

from salestech_be.core.research_agent.types import (
    ResearchContext,
    ResearchStatus,
    ResearchTime,
    ResearchType,
    SaveIntelActivityResult,
)
from salestech_be.db.models.intel_person_activity import IntelPersonActivityType

# NOTE
# Put all schema here to avoid circular import.

#### models


class SaveIntelPersonActivityParams(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    intel_person_id: uuid.UUID
    activity_type: IntelPersonActivityType
    activity_data: dict[str, Any]  # type: ignore[explicit-any] # TODO: fix-any-annotation
    intel_provider_id: uuid.UUID
    source_url: str | None = None
    activity_date: int | None = None  # DATETIME_EPOCH


#### workflow inputs and outputs


class ResearchPersonResult(BaseModel):
    intel_person_id: uuid.UUID | None = None
    linkedin_profile_count: int
    linkedin_post_count: int


class ResearchCompanyResult(BaseModel):
    details: str
    result_company_info_count: int


class ResearchPersonInput(BaseModel):
    intel_person_id: uuid.UUID | None = None
    linkedin_url: str | None = None
    business_email: str | None = None
    organization_id: uuid.UUID | None = None
    contact_id: uuid.UUID | None = None
    research_time: ResearchTime | None = None
    research_context: ResearchContext | None = None

    @model_validator(mode="before")
    @classmethod
    def check_email_or_linkedin(cls, values: Any) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        linkedin_url, business_email = (
            values.get("linkedin_url"),
            values.get("business_email"),
        )
        if not linkedin_url and not business_email:
            raise ValueError(
                "At least one of business_email or linkedin_url must be provided"
            )
        return values


# linkedin url and company domain serve different purposes:
# linkedin url is the primary key of scraping company info and posts.
# company domain is used to find news and potential site content.
class ResearchCompanyInput(BaseModel):
    intel_company_id: uuid.UUID | None = None
    company_name: str
    linkedin_url: str | None = None
    company_domain: str | None = None
    account_id: uuid.UUID | None = None
    research_time: ResearchTime | None = None


class UpdatePersonInfoInput(BaseModel):
    """Input for updating person information."""

    intel_person_id: uuid.UUID
    first_name: str | None = None
    last_name: str | None = None
    email: str | None = None
    linkedin_url: str | None = None
    title: str | None = None
    company_name: str | None = None


class UpdatePersonInfoResult(BaseModel):
    """Result of person info update."""

    intel_person_id: uuid.UUID
    updated: bool
    details: str


#### activity inputs and outputs


class SaveIntelCompanyPostsFromS3Input(BaseModel):
    intel_company_id: uuid.UUID
    company_posts_s3key: str | None = None
    research_context: ResearchContext | None = None


class SaveIntelCompanyFundingFromS3Input(BaseModel):
    intel_company_id: uuid.UUID
    company_info_s3key: str | None = None  # funding info extracted from company info
    research_context: ResearchContext | None = None


class SaveIntelCompanyNewsFromS3Input(BaseModel):
    intel_company_id: uuid.UUID
    company_news_s3key: str | None = None
    research_context: ResearchContext | None = None


class SaveIntelCompanyPostsFromS3Output(BaseModel):
    activity_save_results: list[SaveIntelActivityResult] = []


class SaveIntelCompanyFundingFromS3Output(BaseModel):
    activity_save_results: list[SaveIntelActivityResult] = []


class SaveIntelCompanyNewsFromS3Output(BaseModel):
    activity_save_results: list[SaveIntelActivityResult] = []


class SaveIntelPersonActivitiesToS3Input(BaseModel):
    intel_person_id: uuid.UUID
    person_activities_s3key: str | None = None
    research_context: ResearchContext | None = None


class SaveIntelPersonActivitiesToS3Output(BaseModel):
    activity_save_results: list[SaveIntelActivityResult] = []


class SaveIntelCompanyInfoParams(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    intel_company_id: uuid.UUID
    intel_data: dict[str, Any]  # type: ignore[explicit-any] # TODO: fix-any-annotation
    research_context: ResearchContext | None = None


class SaveIntelPersonInfoParams(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    intel_person_id: uuid.UUID
    intel_data: dict[str, Any]  # type: ignore[explicit-any] # TODO: fix-any-annotation
    research_context: ResearchContext | None = None


class SaveIntelPersonInfoFromS3Params(BaseModel):
    person_info_s3key: str | None = None
    intel_person_id: uuid.UUID
    research_context: ResearchContext | None = None


class GetPersonInfoToS3Input(BaseModel):
    intel_person_id: uuid.UUID
    person_linkedin_urls: list[str] | None = None
    business_emails: list[str] | None = None
    research_context: ResearchContext | None = None
    snapshot_id: str | None = None  # snapshot is only available in brightdata
    use_brightdata: bool = False


class GetPersonInfoToS3Output(BaseModel):
    person_info_s3key: str | None = None
    person_info_count: int = 0
    person_resolved_linkedin_url: str | None = None


class GetCompanyInfoToS3Input(BaseModel):
    intel_company_id: uuid.UUID
    company_name: str
    company_linkedin_url: str
    company_domain: str | None = None
    research_context: ResearchContext | None = None
    snapshot_id: str | None = None  # snapshot is only available in brightdata
    use_brightdata: bool = False


class GetCompanyInfoToS3Output(BaseModel):
    company_info_s3key: str | None = None  # resolved unique company info
    company_infos_s3key: str | None = None  # undeduped company infos
    company_infos_count: int = 0  # count of undeduped company infos


class SearchLinkedinPostsByPersonToS3Input(BaseModel):
    intel_person_id: uuid.UUID
    person_linkedin_url: str
    research_context: ResearchContext | None = None
    snapshot_id: str | None = None  # snapshot is only available in brightdata
    use_brightdata: bool = False


class SearchLinkedinPostsByPersonToS3Output(BaseModel):
    person_linkedin_post_s3key: str | None = None
    person_linkedin_post_count: int = 0


class UploadPersonAvatarToS3Input(BaseModel):
    person_info_s3key: str | None = None
    research_context: ResearchContext | None = None


class UpdatePersonInfoFromS3Input(BaseModel):
    person_info_s3key: str | None = None
    intel_person_id: uuid.UUID

    contact_id: uuid.UUID | None = None
    organization_id: uuid.UUID | None = None
    business_email: str | None = None

    research_context: ResearchContext | None = None


class SearchLinkedinPostsByCompanyToS3Input(BaseModel):
    intel_company_id: uuid.UUID
    company_linkedin_url: str
    company_domain: str | None = None
    research_context: ResearchContext | None = None
    snapshot_id: str | None = None  # snapshot is only available in brightdata
    use_brightdata: bool = False


class SearchLinkedinPostsByCompanyToS3Output(BaseModel):
    company_linkedin_post_s3key: str | None = None
    company_linkedin_post_count: int = 0


class UpdateAccountRevenueHeadcountFromS3Input(BaseModel):
    intel_company_id: uuid.UUID
    company_info_s3key: str | None = None
    research_context: ResearchContext | None = None


class GetCompanySiteContentToS3Input(BaseModel):
    intel_company_id: uuid.UUID
    company_name: str
    company_domain: str | None = None
    research_context: ResearchContext | None = None


class GetCompanySiteContentToS3Output(BaseModel):
    company_site_content_s3key: str | None = None


class GetCompanyNewsToS3Input(BaseModel):
    intel_company_id: uuid.UUID
    company_name: str
    company_domain: str | None = None
    company_info_s3key: str | None = None
    research_context: ResearchContext | None = None
    # NOTE: this is not used anymore
    # site_content_s3key: str | None = None


class GetCompanyNewsToS3Output(BaseModel):
    company_news_s3key: str | None = None
    company_news_count: int = 0


class SaveIntelCompanyInfoFromS3Input(BaseModel):
    intel_company_id: uuid.UUID
    company_info_s3key: str | None = None
    site_content_s3key: str | None = None
    research_context: ResearchContext | None = None


class MeetingResearchOutput(BaseModel):
    research_person_inputs: list[ResearchPersonInput]
    research_company_inputs: list[ResearchCompanyInput]


class UpdateIntelStatusInput(BaseModel):
    intel_entity_id: uuid.UUID
    status: ResearchStatus
    research_type: ResearchType


class GetIntelPersonFromEmailInput(BaseModel):
    """Input for finding or creating an intel person from a business email."""

    business_email: str
    contact_id: uuid.UUID
    organization_id: uuid.UUID
    research_context: ResearchContext | None = None


class GetIntelPersonFromEmailOutput(BaseModel):
    intel_person_id: uuid.UUID
    business_email: str
    linkedin_url: str | None = None


class GetCompanyLinkedinUrlByDomainInput(BaseModel):
    account_id: uuid.UUID
    company_domain: str | None
    linkedin_url: str | None
    research_context: ResearchContext | None = None


class GetCompanyLinkedinUrlByDomainOutput(BaseModel):
    intel_company_id: uuid.UUID | None
    company_linkedin_url: str | None
    domain: str | None
    research_context: ResearchContext | None = None
