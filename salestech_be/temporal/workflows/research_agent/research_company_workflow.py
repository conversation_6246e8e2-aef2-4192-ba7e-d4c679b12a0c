# WARNING: Do not import anything that touches network, filesystem, or any non-deterministic
# operations in activities. This can lead to non-deterministic behavior in workflows.
# Only import pure functions or deterministic operations in workflows.

# Import activity, passing it through the sandbox without reloading the module

from temporalio import workflow
from temporalio.common import RetryPolicy

# NOTE: use imports_passed_through on ALL non-temporal imports to mitigate memory leak


with workflow.unsafe.imports_passed_through():
    import asyncio
    from datetime import timed<PERSON><PERSON>
    from http import HTTPStatus
    from uuid import UUID

    from salestech_be.core.research_agent.models.response import (
        PendingEnrichmentResponse,
    )
    from salestech_be.core.research_agent.types import (
        ResearchContext,
        ResearchContextType,
        ResearchStatus,
        ResearchTime,
        ResearchType,
    )
    from salestech_be.db.models.intel_company_info import (
        CompanyInfoProvider,
        CompanyInfoProviderStatus,
    )
    from salestech_be.integrations.temporal.config import (
        DEFAULT_TASK_MAX_RETRY,
        ResearchTaskQueue,
    )
    from salestech_be.settings import settings
    from salestech_be.temporal.activities.research_agent.account_revenue_headcount import (
        update_account_revenue_headcount_from_s3,
    )
    from salestech_be.temporal.activities.research_agent.company_news import (
        get_company_news_to_s3,
    )
    from salestech_be.temporal.activities.research_agent.company_site import (
        get_company_site_content_to_s3,
    )
    from salestech_be.temporal.activities.research_agent.create_intel_company import (
        get_or_create_intel_company_by_linkedin_url,
    )
    from salestech_be.temporal.activities.research_agent.db import (
        save_company_info_from_s3,
        save_intel_company_funding_from_s3,
        save_intel_company_news_from_s3,
        save_intel_company_posts_from_s3,
        update_company_intel_info_provider_status,
        update_intel_status,
    )
    from salestech_be.temporal.activities.research_agent.linkedin_posts import (
        search_linkedin_posts_by_company_to_s3,
    )
    from salestech_be.temporal.activities.research_agent.linkedin_profile import (
        get_company_info_to_s3,
    )
    from salestech_be.temporal.activities.research_agent.schedule import (
        schedule_research_activity,
    )
    from salestech_be.temporal.activities.research_agent.utils import (
        is_valid_company_domain,
    )
    from salestech_be.temporal.util import TemporalPatch
    from salestech_be.temporal.workflows.exceptions import (
        PendingEnrichmentError,
    )
    from salestech_be.temporal.workflows.policies import (
        CRUSTDATA_RETRY_POLICY,
        DEFAULT_RESEARCH_ACTIVITY_RETRY_POLICY,
        FIXED_INTERVAL_RETRY_POLICY,
        MAX_BRIGHTDATA_INFO_ENRICHMENT_RETRY_COUNT,
        MAX_BRIGHTDATA_INFO_ENRICHMENT_RETRY_DELAY,
        MAX_BRIGHTDATA_POSTS_ENRICHMENT_RETRY_COUNT,
        MAX_BRIGHTDATA_POSTS_ENRICHMENT_RETRY_DELAY,
        MAX_CRUSTDATA_INFO_ENRICHMENT_RETRY_COUNT,
        MAX_CRUSTDATA_INFO_ENRICHMENT_RETRY_DELAY,
        MAX_CRUSTDATA_POSTS_ENRICHMENT_RETRY_COUNT,
        MAX_CRUSTDATA_POSTS_ENRICHMENT_RETRY_DELAY,
    )

    # NOTE: unsafe due to indirect usage of loguru in referred models which in turn calls datetime
    from salestech_be.temporal.workflows.research_agent.schema import (
        GetCompanyInfoToS3Input,
        GetCompanyLinkedinUrlByDomainInput,
        GetCompanyNewsToS3Input,
        GetCompanySiteContentToS3Input,
        ResearchCompanyInput,
        ResearchCompanyResult,
        SaveIntelCompanyFundingFromS3Input,
        SaveIntelCompanyInfoFromS3Input,
        SaveIntelCompanyNewsFromS3Input,
        SaveIntelCompanyPostsFromS3Input,
        SearchLinkedinPostsByCompanyToS3Input,
        UpdateAccountRevenueHeadcountFromS3Input,
        UpdateIntelStatusInput,
    )
    from salestech_be.util.time import zoned_utc_from_timestamp


logger = workflow.logger


@workflow.defn
class ResearchCompanyWorkflow:
    RETRY_POLICY: RetryPolicy = RetryPolicy(
        maximum_interval=timedelta(seconds=60),
        non_retryable_error_types=[
            "ResearchFailError"
        ],  # FIXME: refactor non retryable capture process
        maximum_attempts=DEFAULT_TASK_MAX_RETRY,
    )

    DEFAULT_ACTIVITY_TIMEOUT: timedelta = timedelta(minutes=10)
    DEFAULT_DB_OPERATION_ACTIVITY_TIMEOUT: timedelta = timedelta(minutes=1)

    @staticmethod
    def get_workflow_id(account_id: UUID | None, intel_company_id: UUID | None) -> str:
        workflow_id = "research:company"
        if account_id:
            workflow_id += f":account:{account_id}"
        if intel_company_id:
            workflow_id += f":intel:{intel_company_id}"

        return workflow_id

    # TODO: refactor to reduce complexity
    @workflow.run
    async def run(self, data: ResearchCompanyInput) -> ResearchCompanyResult:  # noqa: C901, PLR0911, PLR0912, PLR0915
        workflow.logger.info(
            f"Running ResearchCompanyWorkflow for {data.company_domain}, workflow_id={workflow.info().workflow_id}"
        )

        workflow.deprecate_patch(TemporalPatch.RESEARCH_VIA_S3)

        linkedin_url = data.linkedin_url
        intel_company_id = data.intel_company_id
        company_name = data.company_name
        account_id = data.account_id
        company_domain = data.company_domain

        if not account_id:
            return ResearchCompanyResult(
                details="account_id is required",
                result_company_info_count=0,
            )

        if data.research_time is None:
            data.research_time = ResearchTime(
                cdc_triggered_at=workflow.time_ns(),
                wf_started_at=workflow.time_ns(),
                intel_created_at=workflow.time_ns(),
            )
        elif not data.research_time.wf_started_at:
            data.research_time.wf_started_at = workflow.time_ns()

        ctx = ResearchContext(
            research_type=ResearchContextType.COMPANY,
            session_id=workflow.info().workflow_id,
            research_time=data.research_time,
        )

        if not linkedin_url and not company_domain:
            return ResearchCompanyResult(
                details="company domain or linkedin url is required",
                result_company_info_count=0,
            )

        if company_domain and (
            not is_valid_company_domain(company_domain)
            or any(
                company_domain.endswith(suffix)
                for suffix in settings.research_agent_dnr_domain_suffixes
            )
        ):
            return ResearchCompanyResult(
                details=f"company domain {company_domain} is invalid",
                result_company_info_count=0,
            )

        is_low_priority = (
            workflow.info().task_queue == ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW
        )

        if (
            workflow.patched(TemporalPatch.RESEARCH_VIA_LINKEDIN_URL)
            and not intel_company_id
        ):
            company_linkedin_url_output = await workflow.execute_activity(
                get_or_create_intel_company_by_linkedin_url,
                args=[
                    GetCompanyLinkedinUrlByDomainInput(
                        account_id=account_id,
                        company_domain=company_domain,
                        linkedin_url=linkedin_url,
                        research_context=ctx,
                    )
                ],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=CRUSTDATA_RETRY_POLICY,
                task_queue=ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED
                if is_low_priority
                else ResearchTaskQueue.RESEARCH_TASK_QUEUE_THROTTLED,
            )

            if not company_linkedin_url_output.company_linkedin_url:
                return ResearchCompanyResult(
                    details=f"company domain {company_domain} is invalid: {company_linkedin_url_output}",
                    result_company_info_count=0,
                )

            linkedin_url = company_linkedin_url_output.company_linkedin_url
            intel_company_id = company_linkedin_url_output.intel_company_id
            company_domain = company_linkedin_url_output.domain

            if company_linkedin_url_output.research_context:
                ctx = company_linkedin_url_output.research_context

        # if neither linkedin url provided nor found by domain, stop research
        if not linkedin_url:
            return ResearchCompanyResult(
                details=f"Linkedin URL not found for company domain {company_domain}",
                result_company_info_count=0,
            )

        # if not intel company is created, stop research
        if not intel_company_id:
            return ResearchCompanyResult(
                details=f"Intel company not found for linkedin url {linkedin_url}",
                result_company_info_count=0,
            )

        # Prepare data and store in S3
        # 1. Posts, crustdata
        search_posts_input = SearchLinkedinPostsByCompanyToS3Input(
            intel_company_id=intel_company_id,
            company_domain=company_domain,
            company_linkedin_url=linkedin_url,
            research_context=ctx,
        )
        for i in range(MAX_CRUSTDATA_POSTS_ENRICHMENT_RETRY_COUNT):
            posts_output = await workflow.execute_activity(
                search_linkedin_posts_by_company_to_s3,
                task_queue=ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED
                if is_low_priority
                else ResearchTaskQueue.RESEARCH_TASK_QUEUE_THROTTLED,
                args=[ctx, search_posts_input],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=CRUSTDATA_RETRY_POLICY,
            )
            if isinstance(posts_output, PendingEnrichmentResponse):
                if not posts_output.is_recoverable_error:
                    workflow.logger.warning(
                        f"Failed to fetch posts for {data.company_domain}, exiting workflow"
                    )
                    return ResearchCompanyResult(
                        details=f"posts for {data.company_domain} are not available",
                        result_company_info_count=0,
                    )
                workflow.logger.info(
                    f"Found posts for {data.company_domain} after {i + 1} retries"
                )
                if workflow.patched(TemporalPatch.USE_BRIGHTDATA_FALLBACK):
                    search_posts_input.snapshot_id = posts_output.snapshot_id
                    if posts_output.linkedin_url:
                        search_posts_input.company_linkedin_url = (
                            posts_output.linkedin_url
                        )
                    # NOTE: do not raise error if we are not done trying with brightdata
                    elif not workflow.patched(TemporalPatch.SKIP_RAISE_ERROR):
                        raise PendingEnrichmentError(
                            f"Failed to find company linkedin url for {data.company_domain}, exiting workflow"
                        )

                await asyncio.sleep(MAX_CRUSTDATA_POSTS_ENRICHMENT_RETRY_DELAY)
                continue

            break

        # fallback to brightdata if crustdata enrichment is exhausted
        if isinstance(posts_output, PendingEnrichmentResponse):
            # enable brightdata
            search_posts_input.use_brightdata = True
            for _ in range(MAX_BRIGHTDATA_POSTS_ENRICHMENT_RETRY_COUNT):
                posts_output = await workflow.execute_activity(
                    search_linkedin_posts_by_company_to_s3,
                    task_queue=ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED
                    if is_low_priority
                    else ResearchTaskQueue.RESEARCH_TASK_QUEUE_THROTTLED,
                    args=[ctx, search_posts_input],
                    start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=CRUSTDATA_RETRY_POLICY,
                )
                if isinstance(posts_output, PendingEnrichmentResponse):
                    if not posts_output.is_recoverable_error:
                        workflow.logger.warning(
                            f"Failed to fetch posts for {data.company_domain}, exiting workflow"
                        )
                        return ResearchCompanyResult(
                            details=f"posts for {data.company_domain} are not available",
                            result_company_info_count=0,
                        )
                    search_posts_input.snapshot_id = posts_output.snapshot_id
                    # NOTE: for brightdata, null linkedin url means we failed to
                    # find the company linkedin url and cannot proceed, so we raise error
                    if posts_output.linkedin_url:
                        search_posts_input.company_linkedin_url = (
                            posts_output.linkedin_url
                        )
                    else:
                        raise PendingEnrichmentError(
                            f"Failed to find company linkedin url for {data.company_domain}, exiting workflow"
                        )
                    await asyncio.sleep(MAX_BRIGHTDATA_POSTS_ENRICHMENT_RETRY_DELAY)
                    continue

                break

        if isinstance(posts_output, PendingEnrichmentResponse):
            raise PendingEnrichmentError(
                f"Failed to fetch posts for {data.company_domain} after {MAX_CRUSTDATA_POSTS_ENRICHMENT_RETRY_COUNT} retries"
            )

        if workflow.patched(TemporalPatch.STORE_RESEARCH_STATUS):
            await workflow.execute_activity(
                update_intel_status,
                args=[
                    UpdateIntelStatusInput(
                        intel_entity_id=intel_company_id,
                        status=ResearchStatus.PENDING,
                        research_type=ResearchType.COMPANY,
                    )
                ],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=FIXED_INTERVAL_RETRY_POLICY,
            )

        # 2. Company info
        get_company_info_to_s3_input = GetCompanyInfoToS3Input(
            intel_company_id=intel_company_id,
            company_domain=company_domain,
            company_name=company_name,
            company_linkedin_url=linkedin_url,
            research_context=ctx,
        )
        for _ in range(MAX_CRUSTDATA_INFO_ENRICHMENT_RETRY_COUNT):
            company_info_output = await workflow.execute_activity(
                get_company_info_to_s3,
                task_queue=ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED
                if is_low_priority
                else ResearchTaskQueue.RESEARCH_TASK_QUEUE_THROTTLED,
                args=[ctx, get_company_info_to_s3_input],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=CRUSTDATA_RETRY_POLICY,
            )
            if isinstance(company_info_output, PendingEnrichmentResponse):
                if not company_info_output.is_recoverable_error:
                    workflow.logger.warning(
                        f"Failed to fetch company info for {data.company_domain}, exiting workflow"
                    )
                    return ResearchCompanyResult(
                        details=f"company info for {data.company_domain} is not available",
                        result_company_info_count=0,
                    )

                await workflow.execute_activity(
                    update_intel_status,
                    args=[
                        UpdateIntelStatusInput(
                            intel_entity_id=intel_company_id,
                            status=ResearchStatus.PENDING,
                            research_type=ResearchType.COMPANY,
                        )
                    ],
                    start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=FIXED_INTERVAL_RETRY_POLICY,
                )
                if workflow.patched(TemporalPatch.USE_BRIGHTDATA_FALLBACK):
                    get_company_info_to_s3_input.snapshot_id = (
                        company_info_output.snapshot_id
                    )
                    if company_info_output.linkedin_url:
                        get_company_info_to_s3_input.company_linkedin_url = (
                            company_info_output.linkedin_url
                        )
                    # NOTE: do not raise error if we are not done trying with brightdata
                    elif not workflow.patched(TemporalPatch.SKIP_RAISE_ERROR):
                        raise PendingEnrichmentError(
                            f"Failed to find company linkedin url for {data.company_domain}, exiting workflow"
                        )

                await asyncio.sleep(MAX_CRUSTDATA_INFO_ENRICHMENT_RETRY_DELAY)
            else:
                break

        workflow.logger.info(f"Fall back to brightdata: {get_company_info_to_s3_input}")
        # if retry for crustdata enrichment exhausted, try again with brightdata.
        if isinstance(company_info_output, PendingEnrichmentResponse):
            for _ in range(MAX_BRIGHTDATA_INFO_ENRICHMENT_RETRY_COUNT):
                # enable brightdata
                get_company_info_to_s3_input.use_brightdata = True

                company_info_output = await workflow.execute_activity(
                    get_company_info_to_s3,
                    task_queue=ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED
                    if is_low_priority
                    else ResearchTaskQueue.RESEARCH_TASK_QUEUE_THROTTLED,
                    args=[ctx, get_company_info_to_s3_input],
                    start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=CRUSTDATA_RETRY_POLICY,
                )
                if isinstance(company_info_output, PendingEnrichmentResponse):
                    if not company_info_output.is_recoverable_error:
                        workflow.logger.warning(
                            f"Failed to fetch company info for {data.company_domain}, exiting workflow"
                        )
                        return ResearchCompanyResult(
                            details=f"company info for {data.company_domain} is not available",
                            result_company_info_count=0,
                        )
                    await workflow.execute_activity(
                        update_intel_status,
                        args=[
                            UpdateIntelStatusInput(
                                intel_entity_id=intel_company_id,
                                status=ResearchStatus.PENDING,
                                research_type=ResearchType.COMPANY,
                            )
                        ],
                        start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                        retry_policy=FIXED_INTERVAL_RETRY_POLICY,
                    )
                    if workflow.patched(TemporalPatch.USE_BRIGHTDATA_FALLBACK):
                        get_company_info_to_s3_input.snapshot_id = (
                            company_info_output.snapshot_id
                        )
                        if company_info_output.linkedin_url:
                            get_company_info_to_s3_input.company_linkedin_url = (
                                company_info_output.linkedin_url
                            )
                        else:
                            raise PendingEnrichmentError(
                                f"Failed to find company linkedin url for {data.company_domain}, exiting workflow"
                            )

                    await asyncio.sleep(MAX_BRIGHTDATA_INFO_ENRICHMENT_RETRY_DELAY)
                else:
                    break

        if isinstance(company_info_output, PendingEnrichmentResponse):
            await workflow.execute_activity(
                update_intel_status,
                args=[
                    UpdateIntelStatusInput(
                        intel_entity_id=intel_company_id,
                        status=ResearchStatus.FAILED,
                        research_type=ResearchType.COMPANY,
                    )
                ],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=FIXED_INTERVAL_RETRY_POLICY,
            )

            raise PendingEnrichmentError(
                f"Failed to fetch company info for {data.company_domain} after {MAX_CRUSTDATA_INFO_ENRICHMENT_RETRY_COUNT + MAX_BRIGHTDATA_INFO_ENRICHMENT_RETRY_COUNT} retries"
            )

        # 3. Company site content
        get_site_content_input = GetCompanySiteContentToS3Input(
            intel_company_id=intel_company_id,
            company_domain=company_domain,
            company_name=company_name,
            research_context=ctx,
        )
        get_site_content_task = workflow.execute_activity(
            get_company_site_content_to_s3,
            task_queue=ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED
            if is_low_priority
            else ResearchTaskQueue.RESEARCH_TASK_QUEUE_THROTTLED,
            args=[ctx, get_site_content_input],
            start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
            retry_policy=DEFAULT_RESEARCH_ACTIVITY_RETRY_POLICY,
        )

        # 4. Company news
        get_company_news_to_s3_input = GetCompanyNewsToS3Input(
            intel_company_id=intel_company_id,
            company_name=company_name,
            company_domain=company_domain,
            company_info_s3key=company_info_output.company_info_s3key,
            research_context=ctx,
        )
        get_company_news_task = workflow.execute_activity(
            get_company_news_to_s3,
            task_queue=ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED
            if is_low_priority
            else ResearchTaskQueue.RESEARCH_TASK_QUEUE_THROTTLED,
            args=[ctx, get_company_news_to_s3_input],
            start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
            retry_policy=DEFAULT_RESEARCH_ACTIVITY_RETRY_POLICY,
        )

        (
            site_content_output,
            news_output,
        ) = await asyncio.gather(
            get_site_content_task,
            get_company_news_task,
        )

        ########## S3 DIGESTION AND INTEL SAVING #############

        # 1. Posts
        save_intel_company_activities_from_s3_input = SaveIntelCompanyPostsFromS3Input(
            intel_company_id=intel_company_id,
            company_posts_s3key=posts_output.company_linkedin_post_s3key,
        )
        digest_posts_task = workflow.execute_activity(
            save_intel_company_posts_from_s3,
            args=[save_intel_company_activities_from_s3_input],
            start_to_close_timeout=self.DEFAULT_DB_OPERATION_ACTIVITY_TIMEOUT,
            retry_policy=DEFAULT_RESEARCH_ACTIVITY_RETRY_POLICY,
        )

        if not workflow.patched(TemporalPatch.SKIP_ACCOUNT_REVENUE_HEADCOUNT_UPDATE):
            # 2. Company revenue and headcount
            update_account_revenue_headcount_from_s3_input = (
                UpdateAccountRevenueHeadcountFromS3Input(
                    intel_company_id=intel_company_id,
                    company_info_s3key=company_info_output.company_info_s3key,
                    research_context=ctx,
                )
            )
            digest_company_revenue_headcount_task = workflow.execute_activity(  # noqa: F841
                update_account_revenue_headcount_from_s3,
                args=[update_account_revenue_headcount_from_s3_input],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RESEARCH_ACTIVITY_RETRY_POLICY,
            )

        # 3. Company funding
        save_intel_company_funding_from_s3_input = SaveIntelCompanyFundingFromS3Input(
            intel_company_id=intel_company_id,
            company_info_s3key=company_info_output.company_info_s3key,
        )
        digest_company_funding_task = workflow.execute_activity(
            save_intel_company_funding_from_s3,
            args=[save_intel_company_funding_from_s3_input],
            start_to_close_timeout=self.DEFAULT_DB_OPERATION_ACTIVITY_TIMEOUT,
            retry_policy=DEFAULT_RESEARCH_ACTIVITY_RETRY_POLICY,
        )

        # 4. Company intel
        save_company_info_from_s3_input = SaveIntelCompanyInfoFromS3Input(
            intel_company_id=intel_company_id,
            company_info_s3key=company_info_output.company_info_s3key,
            site_content_s3key=site_content_output.company_site_content_s3key,
            research_context=ctx,
        )
        digest_company_info_task = workflow.execute_activity(
            save_company_info_from_s3,
            args=[save_company_info_from_s3_input],
            start_to_close_timeout=self.DEFAULT_DB_OPERATION_ACTIVITY_TIMEOUT,
        )

        # 5. Company news
        save_intel_company_news_from_s3_input = SaveIntelCompanyNewsFromS3Input(
            intel_company_id=intel_company_id,
            company_news_s3key=news_output.company_news_s3key,
        )
        digest_company_news_task = workflow.execute_activity(
            save_intel_company_news_from_s3,
            args=[save_intel_company_news_from_s3_input],
            start_to_close_timeout=self.DEFAULT_DB_OPERATION_ACTIVITY_TIMEOUT,
            retry_policy=DEFAULT_RESEARCH_ACTIVITY_RETRY_POLICY,
        )

        await asyncio.gather(
            digest_posts_task,
            digest_company_funding_task,
            digest_company_info_task,
            digest_company_news_task,
        )

        if workflow.patched(TemporalPatch.STORE_RESEARCH_STATUS):
            await workflow.execute_activity(
                update_intel_status,
                args=[
                    UpdateIntelStatusInput(
                        intel_entity_id=intel_company_id,
                        status=ResearchStatus.COMPLETED,
                        research_type=ResearchType.COMPANY,
                    )
                ],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=FIXED_INTERVAL_RETRY_POLICY,
            )

            await workflow.execute_activity(
                update_company_intel_info_provider_status,
                args=[
                    intel_company_id,
                    [
                        CompanyInfoProviderStatus(
                            name=CompanyInfoProvider.CRUSTDATA,
                            status=HTTPStatus.OK,
                            data_s3key=posts_output.company_linkedin_post_s3key,
                            created_at=zoned_utc_from_timestamp(int(workflow.time())),
                        ),
                        CompanyInfoProviderStatus(
                            name=CompanyInfoProvider.CRUSTDATA,
                            status=HTTPStatus.OK,
                            data_s3key=company_info_output.company_info_s3key,
                            created_at=zoned_utc_from_timestamp(int(workflow.time())),
                        ),
                        CompanyInfoProviderStatus(
                            name=CompanyInfoProvider.GEMINI,
                            status=HTTPStatus.OK,
                            data_s3key=news_output.company_news_s3key,
                            created_at=zoned_utc_from_timestamp(int(workflow.time())),
                        ),
                        CompanyInfoProviderStatus(
                            name=CompanyInfoProvider.GEMINI,
                            status=HTTPStatus.OK,
                            data_s3key=site_content_output.company_site_content_s3key,
                            created_at=zoned_utc_from_timestamp(int(workflow.time())),
                        ),
                    ],
                ],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=FIXED_INTERVAL_RETRY_POLICY,
            )

        await workflow.execute_activity(
            schedule_research_activity,
            args=[data],
            start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
            retry_policy=FIXED_INTERVAL_RETRY_POLICY,
        )

        return ResearchCompanyResult(
            details="company research succeeded",
            result_company_info_count=company_info_output.company_infos_count,
        )
