# WARNING: Do not import anything that touches network, filesystem, or any non-deterministic
# operations in activities. This can lead to non-deterministic behavior in workflows.
# Only import pure functions or deterministic operations in workflows.

# Import activity, passing it through the sandbox without reloading the module

from temporalio import workflow

# NOTE: use imports_passed_through on ALL non-temporal imports to mitigate memory leak
with workflow.unsafe.imports_passed_through():
    import asyncio
    from datetime import timed<PERSON><PERSON>
    from http import HTTPStatus
    from uuid import UUID

    from salestech_be.core.research_agent.models.response import (
        PendingEnrichmentResponse,
    )
    from salestech_be.core.research_agent.types import (
        ResearchContext,
        ResearchContextType,
        ResearchStatus,
        ResearchTime,
        ResearchType,
    )
    from salestech_be.db.models.intel_person_info import (
        PersonInfoProvider,
        PersonInfoProviderStatus,
    )
    from salestech_be.integrations.temporal.config import ResearchTaskQueue
    from salestech_be.temporal.activities.research_agent.db import (
        save_intel_person_activities_from_s3,
        save_person_intel_from_s3,
        update_intel_status,
        update_person_info_from_s3,
        update_person_intel_info_provider_status,
    )
    from salestech_be.temporal.activities.research_agent.intel_person_email import (
        get_or_create_intel_person_from_email,
    )
    from salestech_be.temporal.activities.research_agent.linkedin_posts import (
        search_linkedin_posts_by_person_to_s3,
    )
    from salestech_be.temporal.activities.research_agent.linkedin_profile import (
        get_person_info_and_save_to_s3,
    )
    from salestech_be.temporal.activities.research_agent.schedule import (
        schedule_research_activity,
    )
    from salestech_be.temporal.activities.research_agent.user_intel_profile_pic import (
        update_user_profile_pic_from_linkedin_from_s3,
    )
    from salestech_be.temporal.util import TemporalPatch
    from salestech_be.temporal.workflows.exceptions import (
        PendingEnrichmentError,
    )
    from salestech_be.temporal.workflows.policies import (
        CRUSTDATA_RETRY_POLICY,
        DEFAULT_RESEARCH_ACTIVITY_RETRY_POLICY,
        FIXED_INTERVAL_RETRY_POLICY,
        MAX_BRIGHTDATA_INFO_ENRICHMENT_RETRY_COUNT,
        MAX_BRIGHTDATA_INFO_ENRICHMENT_RETRY_DELAY,
        MAX_BRIGHTDATA_POSTS_ENRICHMENT_RETRY_COUNT,
        MAX_BRIGHTDATA_POSTS_ENRICHMENT_RETRY_DELAY,
        MAX_CRUSTDATA_INFO_ENRICHMENT_RETRY_COUNT,
        MAX_CRUSTDATA_INFO_ENRICHMENT_RETRY_DELAY,
        MAX_CRUSTDATA_POSTS_ENRICHMENT_RETRY_COUNT,
        MAX_CRUSTDATA_POSTS_ENRICHMENT_RETRY_DELAY,
        RATE_LIMITED_ACTIVITY_RETRY_POLICY,
    )
    from salestech_be.temporal.workflows.research_agent.schema import (
        GetIntelPersonFromEmailInput,
        GetPersonInfoToS3Input,
        ResearchPersonInput,
        ResearchPersonResult,
        SaveIntelPersonActivitiesToS3Input,
        SaveIntelPersonInfoFromS3Params,
        SearchLinkedinPostsByPersonToS3Input,
        UpdateIntelStatusInput,
        UpdatePersonInfoFromS3Input,
        UploadPersonAvatarToS3Input,
    )
    from salestech_be.util.time import zoned_utc_from_timestamp


@workflow.defn
class ResearchPersonWorkflow:
    DEFAULT_ACTIVITY_TIMEOUT: timedelta = timedelta(minutes=3)
    DEFAULT_DB_OPERATION_ACTIVITY_TIMEOUT: timedelta = timedelta(minutes=1)

    @staticmethod
    def get_workflow_id(contact_id: UUID | None, intel_person_id: UUID | None) -> str:
        workflow_id = "research:person"
        if contact_id:
            workflow_id += f":contact:{contact_id}"
        if intel_person_id:
            workflow_id += f":intel:{intel_person_id}"

        return workflow_id

    # TODO: refactor to reduce complexity - REEVO-5921
    @workflow.run
    async def run(self, data: ResearchPersonInput) -> ResearchPersonResult:  # noqa: C901, PLR0911, PLR0912, PLR0915
        workflow.logger.info(
            f"Running ResearchPersonWorkflow for {data.linkedin_url} {data.contact_id}, workflow_id={workflow.info().workflow_id}"
        )
        if data.research_time is None:
            data.research_time = ResearchTime(
                cdc_triggered_at=workflow.time_ns(),
                wf_started_at=workflow.time_ns(),
                intel_created_at=workflow.time_ns(),
            )
        elif data.research_time.wf_started_at is None:
            data.research_time.wf_started_at = workflow.time_ns()

        ctx = ResearchContext(
            research_type=ResearchContextType.PERSON,
            session_id=workflow.info().workflow_id,
            research_time=data.research_time,
        )

        workflow.deprecate_patch(TemporalPatch.RESEARCH_VIA_S3)
        # Note: Temporal Python SDK only has deprecate_patch and patched methods
        # No explicit registration of patches is needed

        # Early return if no input data
        if not data.linkedin_url and not data.business_email:
            workflow.logger.info(
                "no linkedin_url or business_email provided; will end research"
            )
            return ResearchPersonResult(
                intel_person_id=data.intel_person_id,
                linkedin_profile_count=0,
                linkedin_post_count=0,
            )
        is_low_priority = (
            workflow.info().task_queue == ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW
        )

        # If no LinkedIn URL, try to lookup and create intel_person from email
        if (
            workflow.patched(TemporalPatch.EMAIL_BASED_LINKEDIN_LOOKUP)
            and not data.linkedin_url
            and not data.intel_person_id
            and data.business_email
            and data.contact_id
            and data.organization_id
        ):
            workflow.logger.info(
                f"Get or create intel_person from email: {data.business_email}"
            )

            intel_person_from_email_input = GetIntelPersonFromEmailInput(
                business_email=data.business_email,
                contact_id=data.contact_id,
                organization_id=data.organization_id,
                research_context=ctx,
            )

            intel_person_from_email_output = await workflow.execute_activity(
                get_or_create_intel_person_from_email,
                task_queue=ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED
                if is_low_priority
                else ResearchTaskQueue.RESEARCH_TASK_QUEUE_THROTTLED,
                args=[intel_person_from_email_input],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RESEARCH_ACTIVITY_RETRY_POLICY,
            )

            if intel_person_from_email_output is None:
                workflow.logger.info(
                    "no intel_person created from email; will end research"
                )
                return ResearchPersonResult(
                    intel_person_id=None,
                    linkedin_profile_count=0,
                    linkedin_post_count=0,
                )
            else:
                data.intel_person_id = intel_person_from_email_output.intel_person_id
                data.linkedin_url = intel_person_from_email_output.linkedin_url

        # If no intel_person_id, skip the rest of the workflow
        if not data.intel_person_id:
            workflow.logger.info("no intel_person created; will end research")
            return ResearchPersonResult(
                intel_person_id=None,
                linkedin_profile_count=0,
                linkedin_post_count=0,
            )

        # 1. Get person info from Crustdata and store to S3
        get_person_info_to_s3_input = GetPersonInfoToS3Input(
            intel_person_id=data.intel_person_id,
            research_context=ctx,
            person_linkedin_urls=[data.linkedin_url] if data.linkedin_url else None,
            business_emails=[data.business_email] if data.business_email else None,
        )

        for _ in range(MAX_CRUSTDATA_INFO_ENRICHMENT_RETRY_COUNT):
            person_info_result = await workflow.execute_activity(
                get_person_info_and_save_to_s3,
                task_queue=ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED
                if is_low_priority
                else ResearchTaskQueue.RESEARCH_TASK_QUEUE_THROTTLED,
                args=[get_person_info_to_s3_input],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=CRUSTDATA_RETRY_POLICY,
            )

            if isinstance(person_info_result, PendingEnrichmentResponse):
                if not person_info_result.is_recoverable_error:
                    workflow.logger.warning(
                        f"Failed to fetch person info for {data.intel_person_id} {data.linkedin_url}, exiting workflow"
                    )
                    return ResearchPersonResult(
                        intel_person_id=data.intel_person_id,
                        linkedin_profile_count=0,
                        linkedin_post_count=0,
                    )

                if workflow.patched(TemporalPatch.STORE_RESEARCH_STATUS):
                    await workflow.execute_activity(
                        update_intel_status,
                        args=[
                            UpdateIntelStatusInput(
                                intel_entity_id=data.intel_person_id,
                                status=ResearchStatus.PENDING,
                                research_type=ResearchType.PERSON,
                            )
                        ],
                        start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                        retry_policy=FIXED_INTERVAL_RETRY_POLICY,
                    )

                if workflow.patched(TemporalPatch.USE_BRIGHTDATA_FALLBACK):
                    get_person_info_to_s3_input.snapshot_id = (
                        person_info_result.snapshot_id
                    )

                await asyncio.sleep(MAX_CRUSTDATA_INFO_ENRICHMENT_RETRY_DELAY)
            else:
                break

        # if retry for crustdata enrichment exhausted, try again with brightdata.
        if isinstance(person_info_result, PendingEnrichmentResponse):
            # enable brightdata
            get_person_info_to_s3_input.use_brightdata = True

            for _ in range(MAX_BRIGHTDATA_INFO_ENRICHMENT_RETRY_COUNT):
                person_info_result = await workflow.execute_activity(
                    get_person_info_and_save_to_s3,
                    task_queue=ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED
                    if is_low_priority
                    else ResearchTaskQueue.RESEARCH_TASK_QUEUE_THROTTLED,
                    args=[get_person_info_to_s3_input],
                    start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=CRUSTDATA_RETRY_POLICY,
                )

                if isinstance(person_info_result, PendingEnrichmentResponse):
                    if not person_info_result.is_recoverable_error:
                        workflow.logger.warning(
                            f"Failed to fetch person info for {data.intel_person_id} {data.linkedin_url}, exiting workflow"
                        )
                        return ResearchPersonResult(
                            intel_person_id=data.intel_person_id,
                            linkedin_profile_count=0,
                            linkedin_post_count=0,
                        )
                    await workflow.execute_activity(
                        update_intel_status,
                        args=[
                            UpdateIntelStatusInput(
                                intel_entity_id=data.intel_person_id,
                                status=ResearchStatus.PENDING,
                                research_type=ResearchType.PERSON,
                            )
                        ],
                        start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                        retry_policy=FIXED_INTERVAL_RETRY_POLICY,
                    )

                    get_person_info_to_s3_input.snapshot_id = (
                        person_info_result.snapshot_id
                    )

                    await asyncio.sleep(MAX_BRIGHTDATA_INFO_ENRICHMENT_RETRY_DELAY)
                else:
                    break

        # if both providers fail, raise error and stop workflow.
        if isinstance(person_info_result, PendingEnrichmentResponse):
            if workflow.patched(TemporalPatch.STORE_RESEARCH_STATUS):
                await workflow.execute_activity(
                    update_intel_status,
                    args=[
                        UpdateIntelStatusInput(
                            intel_entity_id=data.intel_person_id,
                            status=ResearchStatus.FAILED,
                            research_type=ResearchType.PERSON,
                        )
                    ],
                    start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=FIXED_INTERVAL_RETRY_POLICY,
                )

            raise PendingEnrichmentError(
                f"Failed to fetch person info for {data.linkedin_url} after {MAX_CRUSTDATA_INFO_ENRICHMENT_RETRY_COUNT + MAX_BRIGHTDATA_INFO_ENRICHMENT_RETRY_COUNT} retries"
            )

        # 2. Run these activities in parallel since they're independent
        #   1) Update profile pic from linkedin
        #   2) Update person info from S3
        #   3) Save person intel from S3
        #   4) Get LinkedIn posts from Crustdata and store to S3
        profile_pic_task = workflow.execute_activity(
            update_user_profile_pic_from_linkedin_from_s3,
            args=[
                UploadPersonAvatarToS3Input(
                    person_info_s3key=person_info_result.person_info_s3key,
                    research_context=ctx,
                )
            ],
            start_to_close_timeout=self.DEFAULT_DB_OPERATION_ACTIVITY_TIMEOUT,
            retry_policy=DEFAULT_RESEARCH_ACTIVITY_RETRY_POLICY,
        )
        if not workflow.patched(TemporalPatch.SKIP_PERSON_INFO_UPDATE):
            update_info_task = workflow.execute_activity(
                update_person_info_from_s3,
                args=[
                    UpdatePersonInfoFromS3Input(
                        person_info_s3key=person_info_result.person_info_s3key,
                        intel_person_id=data.intel_person_id,
                        contact_id=data.contact_id,
                        organization_id=data.organization_id,
                        business_email=data.business_email,
                        research_context=ctx,
                    )
                ],
                start_to_close_timeout=self.DEFAULT_DB_OPERATION_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RESEARCH_ACTIVITY_RETRY_POLICY,
            )

        save_intel_task = workflow.execute_activity(
            save_person_intel_from_s3,
            args=[
                SaveIntelPersonInfoFromS3Params(
                    person_info_s3key=person_info_result.person_info_s3key,
                    intel_person_id=data.intel_person_id,
                    research_context=ctx,
                )
            ],
            start_to_close_timeout=self.DEFAULT_DB_OPERATION_ACTIVITY_TIMEOUT,
            retry_policy=DEFAULT_RESEARCH_ACTIVITY_RETRY_POLICY,
        )

        if not person_info_result.person_resolved_linkedin_url:
            # Wait for parallel tasks to complete
            if not workflow.patched(TemporalPatch.SKIP_PERSON_INFO_UPDATE):
                await asyncio.gather(
                    profile_pic_task, update_info_task, save_intel_task
                )
            else:
                await asyncio.gather(profile_pic_task, save_intel_task)

            workflow.logger.info(
                "no resolved linkedin_url; will end research without linkedin posts"
            )
            return ResearchPersonResult(
                intel_person_id=data.intel_person_id,
                linkedin_profile_count=person_info_result.person_info_count,
                linkedin_post_count=0,
            )

        search_posts_input = SearchLinkedinPostsByPersonToS3Input(
            intel_person_id=data.intel_person_id,
            person_linkedin_url=person_info_result.person_resolved_linkedin_url,
            research_context=ctx,
        )

        for i in range(MAX_CRUSTDATA_POSTS_ENRICHMENT_RETRY_COUNT):
            linkedin_posts_output = await workflow.execute_activity(
                search_linkedin_posts_by_person_to_s3,
                task_queue=ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED
                if is_low_priority
                else ResearchTaskQueue.RESEARCH_TASK_QUEUE_THROTTLED,
                args=[search_posts_input],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=RATE_LIMITED_ACTIVITY_RETRY_POLICY,
            )

            if isinstance(linkedin_posts_output, PendingEnrichmentResponse):
                if not linkedin_posts_output.is_recoverable_error:
                    workflow.logger.warning(
                        f"Failed to fetch linkedin posts for {data.linkedin_url}, exiting workflow"
                    )
                    return ResearchPersonResult(
                        intel_person_id=data.intel_person_id,
                        linkedin_profile_count=0,
                        linkedin_post_count=0,
                    )
                workflow.logger.info(
                    f"Failed to fetch linkedin posts for {data.linkedin_url}, retrying {i + 1} times"
                )
                if workflow.patched(TemporalPatch.USE_BRIGHTDATA_FALLBACK):
                    search_posts_input.snapshot_id = linkedin_posts_output.snapshot_id

                await asyncio.sleep(MAX_CRUSTDATA_POSTS_ENRICHMENT_RETRY_DELAY)
            else:
                break

        # fallback to brightdata if crustdata enrichment is exhausted
        if isinstance(linkedin_posts_output, PendingEnrichmentResponse):
            # enable brightdata
            search_posts_input.use_brightdata = True
            for _ in range(MAX_BRIGHTDATA_POSTS_ENRICHMENT_RETRY_COUNT):
                linkedin_posts_output = await workflow.execute_activity(
                    search_linkedin_posts_by_person_to_s3,
                    task_queue=ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW_THROTTLED
                    if is_low_priority
                    else ResearchTaskQueue.RESEARCH_TASK_QUEUE_THROTTLED,
                    args=[search_posts_input],
                    start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=CRUSTDATA_RETRY_POLICY,
                )
                if isinstance(linkedin_posts_output, PendingEnrichmentResponse):
                    if not linkedin_posts_output.is_recoverable_error:
                        workflow.logger.warning(
                            f"Failed to fetch linkedin posts for {data.linkedin_url}, exiting workflow"
                        )
                        return ResearchPersonResult(
                            intel_person_id=data.intel_person_id,
                            linkedin_profile_count=0,
                            linkedin_post_count=0,
                        )
                    search_posts_input.snapshot_id = linkedin_posts_output.snapshot_id
                    if linkedin_posts_output.linkedin_url:
                        search_posts_input.person_linkedin_url = (
                            linkedin_posts_output.linkedin_url
                        )
                    else:
                        raise PendingEnrichmentError(
                            f"Failed to find person linkedin url for {data.linkedin_url}, exiting workflow"
                        )
                    await asyncio.sleep(MAX_BRIGHTDATA_POSTS_ENRICHMENT_RETRY_DELAY)
                    continue

                break

        if isinstance(linkedin_posts_output, PendingEnrichmentResponse):
            raise PendingEnrichmentError(
                f"Failed to fetch linkedin posts for {data.linkedin_url} after {MAX_CRUSTDATA_POSTS_ENRICHMENT_RETRY_COUNT} retries"
            )

        if not workflow.patched(TemporalPatch.SKIP_PERSON_INFO_UPDATE):
            await asyncio.gather(profile_pic_task, update_info_task, save_intel_task)
        else:
            await asyncio.gather(profile_pic_task, save_intel_task)

        # 3. Save LinkedIn posts to S3
        await workflow.execute_activity(
            save_intel_person_activities_from_s3,
            args=[
                SaveIntelPersonActivitiesToS3Input(
                    intel_person_id=data.intel_person_id,
                    person_activities_s3key=linkedin_posts_output.person_linkedin_post_s3key,
                    research_context=ctx,
                )
            ],
            start_to_close_timeout=self.DEFAULT_DB_OPERATION_ACTIVITY_TIMEOUT,
            retry_policy=FIXED_INTERVAL_RETRY_POLICY,
        )

        if workflow.patched(TemporalPatch.STORE_RESEARCH_STATUS):
            await workflow.execute_activity(
                update_intel_status,
                args=[
                    UpdateIntelStatusInput(
                        intel_entity_id=data.intel_person_id,
                        status=ResearchStatus.COMPLETED,
                        research_type=ResearchType.PERSON,
                    )
                ],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=FIXED_INTERVAL_RETRY_POLICY,
            )
            await workflow.execute_activity(
                update_person_intel_info_provider_status,
                args=[
                    data.intel_person_id,
                    [
                        PersonInfoProviderStatus(
                            name=PersonInfoProvider.CRUSTDATA,
                            status=HTTPStatus.OK,
                            data_s3key=linkedin_posts_output.person_linkedin_post_s3key,
                            created_at=zoned_utc_from_timestamp(int(workflow.time())),
                        ),
                        PersonInfoProviderStatus(
                            name=PersonInfoProvider.CRUSTDATA,
                            status=HTTPStatus.OK,
                            data_s3key=person_info_result.person_info_s3key,
                            created_at=zoned_utc_from_timestamp(int(workflow.time())),
                        ),
                    ],
                ],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=FIXED_INTERVAL_RETRY_POLICY,
            )

        await workflow.execute_activity(
            schedule_research_activity,
            args=[data],
            start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
            retry_policy=FIXED_INTERVAL_RETRY_POLICY,
        )

        return ResearchPersonResult(
            intel_person_id=data.intel_person_id,
            linkedin_profile_count=person_info_result.person_info_count,
            linkedin_post_count=linkedin_posts_output.person_linkedin_post_count,
        )
