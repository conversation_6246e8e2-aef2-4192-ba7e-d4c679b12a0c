from temporalio import workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    from dataclasses import dataclass
    from datetime import timedelta
    from uuid import UUID

    from salestech_be.db.models.meeting import MeetingReferenceIdType
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_MAX_RETRY
    from salestech_be.temporal.activities.meeting.meeting_completion import (
        complete_analyze_meeting_activity,
        end_active_meeting_activity,
    )
    from salestech_be.temporal.activities.meeting.meeting_tracker_stats import (
        MeetingTrackerStatsInput,
        generate_meeting_tracker_stats_activity,
    )


@dataclass
class MeetingEndWorkflowData:
    meeting_id: UUID
    organization_id: UUID


@workflow.defn
class MeetingEndWorkflow:
    COMPLETE_ANALYZE_MEETING_ACTIVITY_TIMEOUT: timedelta = timedelta(minutes=10)
    GENERATE_MEETING_TRACKER_STATS_ACTIVITY_TIMEOUT: timedelta = timedelta(minutes=10)
    RETRY_POLICY: RetryPolicy = RetryPolicy(
        maximum_interval=timedelta(minutes=1),
        maximum_attempts=DEFAULT_TASK_MAX_RETRY,
        non_retryable_error_types=[
            "NotImplementedError",
            "ResourceNotFoundError",
            "IllegalStateError",
            "ValueError",
        ],
    )

    @workflow.run
    async def run(
        self,
        data: MeetingEndWorkflowData,
    ) -> None:
        await workflow.execute_activity(
            end_active_meeting_activity,
            args=(
                data.meeting_id,
                data.organization_id,
            ),
            start_to_close_timeout=self.COMPLETE_ANALYZE_MEETING_ACTIVITY_TIMEOUT,
            retry_policy=self.RETRY_POLICY,
        )


@dataclass
class MeetingCompletionWorkflowData:
    meeting_reference_id: str
    meeting_reference_id_type: str
    organization_id: UUID


@workflow.defn
class MeetingCompletionWorkflow:
    COMPLETE_ANALYZE_MEETING_ACTIVITY_TIMEOUT: timedelta = timedelta(minutes=15)
    GENERATE_MEETING_TRACKER_STATS_ACTIVITY_TIMEOUT: timedelta = timedelta(minutes=15)
    RETRY_POLICY: RetryPolicy = RetryPolicy(
        maximum_interval=timedelta(minutes=1),
        maximum_attempts=DEFAULT_TASK_MAX_RETRY,
        non_retryable_error_types=[
            "NotImplementedError",
            "ResourceNotFoundError",
            "IllegalStateError",
            "ValueError",
        ],
    )

    @workflow.run
    async def run(
        self,
        data: MeetingCompletionWorkflowData,
    ) -> None:
        await workflow.execute_activity(
            complete_analyze_meeting_activity,
            args=(
                data.meeting_reference_id,
                data.meeting_reference_id_type,
                data.organization_id,
            ),
            start_to_close_timeout=self.COMPLETE_ANALYZE_MEETING_ACTIVITY_TIMEOUT,
            retry_policy=self.RETRY_POLICY,
        )
        await workflow.execute_activity(
            generate_meeting_tracker_stats_activity,
            args=(
                MeetingTrackerStatsInput(
                    meeting_reference_id=data.meeting_reference_id,
                    meeting_reference_id_type=MeetingReferenceIdType(
                        data.meeting_reference_id_type
                    ),
                    organization_id=data.organization_id,
                ),
            ),
            start_to_close_timeout=self.GENERATE_MEETING_TRACKER_STATS_ACTIVITY_TIMEOUT,
            retry_policy=self.RETRY_POLICY,
        )
