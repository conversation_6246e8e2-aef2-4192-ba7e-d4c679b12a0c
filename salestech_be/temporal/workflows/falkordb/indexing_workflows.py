"""Temporal workflow for indexing all organizations in FalkorDB."""

import asyncio
from collections import defaultdict
from datetime import timedelta

from pydantic import BaseModel
from temporalio import workflow
from temporalio.workflow import ParentClosePolicy

# Import activities using the unsafe method as required by Temporal
with workflow.unsafe.imports_passed_through():
    from salestech_be.common.stats.metric import custom_metric
    from salestech_be.integrations.temporal.config import TemporalTaskQueue
    from salestech_be.temporal.activities.falkordb.indexing_activities import (
        get_domain_object_count_falkor_activity,
        index_all_accounts_in_batches_activity,
        index_all_contact_account_roles_in_batches_activity,
        index_all_contact_pipeline_roles_in_batches_activity,
        index_all_contacts_in_batches_activity,
        index_all_custom_objects_for_organization_in_batches_activity,
        index_all_pipelines_in_batches_activity,
        index_organization_relationships_activity,
        index_organization_relationships_activity_in_batches,
        index_users_activity,
        list_organization_and_user_ids_activity,
    )


@workflow.defn
class IndexAllOrganizationsWorkflow:
    BATCH_SIZE = 10000
    MAX_PARALLEL_ORGS = 1

    @workflow.run
    async def run(self) -> None:
        """Workflow entry point to index all organizations."""
        workflow.logger.info("Starting workflow: IndexAllOrganizationsWorkflow")

        # 1. Get all (user_id, org_id) pairs
        try:
            user_org_id_pairs_str = await workflow.execute_activity(
                list_organization_and_user_ids_activity,
                start_to_close_timeout=timedelta(minutes=10),
                schedule_to_close_timeout=timedelta(minutes=15),
                task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
            )
        except Exception as e:
            workflow.logger.exception("Failed to list organization and user IDs")
            raise e

        if not user_org_id_pairs_str:
            workflow.logger.warning(
                "No organization/user ID pairs found. Exiting workflow."
            )
            return

        # 2. Group user IDs by organization ID
        org_to_user_ids_map = defaultdict(list)
        for user_id_str, org_id_str in user_org_id_pairs_str:
            org_to_user_ids_map[org_id_str].append(user_id_str)
        workflow.logger.info(
            f"Found {len(org_to_user_ids_map)} organizations to index."
        )

        org_ids = list(org_to_user_ids_map.keys())
        for i in range(0, len(org_ids), self.MAX_PARALLEL_ORGS):
            batch = org_ids[i : i + self.MAX_PARALLEL_ORGS]
            indexing_tasks = []
            for org_id_str in batch:
                workflow.logger.info(
                    f"Starting IndexSpecificOrganizationWorkflow for org {org_id_str}"
                )
                input_data = IndexSpecificOrganizationInput(
                    org_id=org_id_str, batch_size=self.BATCH_SIZE
                )
                task = await workflow.start_child_workflow(
                    IndexSpecificOrganizationWorkflow.run,
                    args=[input_data],
                    id=f"index-org-{org_id_str}",
                    task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
                    execution_timeout=timedelta(hours=2),
                    parent_close_policy=ParentClosePolicy.ABANDON,
                )
                indexing_tasks.append(task)
            await asyncio.gather(*indexing_tasks)

        workflow.logger.info("Completed workflow: IndexAllOrganizationsWorkflow")


class IndexSpecificOrganizationInput(BaseModel):
    org_id: str
    batch_size: int


@workflow.defn
class IndexSpecificOrganizationWorkflow:
    MAX_PARALLEL_ENTITY_TASKS = 10

    async def _index_all_batches(
        self, entity_type: str, org_id: str, batch_size: int
    ) -> None:
        """Generic helper method to handle batched indexing for any entity type."""
        activity_map = {
            "accounts": index_all_accounts_in_batches_activity,
            "contacts": index_all_contacts_in_batches_activity,
            "pipelines": index_all_pipelines_in_batches_activity,
            "contact_pipeline_roles": index_all_contact_pipeline_roles_in_batches_activity,
            "contact_account_roles": index_all_contact_account_roles_in_batches_activity,
            "custom_objects": index_all_custom_objects_for_organization_in_batches_activity,
        }

        # First get Postgres count
        postgres_count = await workflow.execute_activity(
            f"get_{entity_type}_count_activity",
            args=[org_id],
            start_to_close_timeout=timedelta(minutes=5),
            schedule_to_close_timeout=timedelta(minutes=10),
            task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
        )

        # Calculate number of batches needed
        num_batches = (postgres_count + batch_size - 1) // batch_size
        batch_tasks = []

        # Start all batches immediately with offset-based pagination
        for batch_num in range(num_batches):
            offset = batch_num * batch_size
            workflow.logger.info(f"Starting batch {batch_num} at offset {offset}")
            batch_task = workflow.start_activity(
                activity_map[entity_type],
                args=[org_id, offset, batch_size],
                start_to_close_timeout=timedelta(hours=1),
                schedule_to_close_timeout=timedelta(hours=1, minutes=10),
                heartbeat_timeout=timedelta(minutes=2),
                task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
            )
            batch_tasks.append(batch_task)

        # Wait for all batches to complete
        for i in range(0, len(batch_tasks), self.MAX_PARALLEL_ENTITY_TASKS):
            batch = batch_tasks[i : i + self.MAX_PARALLEL_ENTITY_TASKS]
            await asyncio.gather(*batch)

        # Determine if counts match on Postgres and Falkor
        if entity_type != "custom_objects":
            falkor_count = await workflow.execute_activity(
                get_domain_object_count_falkor_activity,
                args=[org_id, entity_type],
                start_to_close_timeout=timedelta(minutes=5),
                schedule_to_close_timeout=timedelta(minutes=10),
                task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
            )

            if falkor_count != postgres_count:
                workflow.logger.error(
                    f"MATTY INDEXING FRESH MISMATCH for org_id={org_id}, entity_type={entity_type}. "
                    f"postgres_count={postgres_count}, falkor_count={falkor_count}"
                )

                # Record metric for mismatch
                custom_metric.increment(
                    metric_name="alert.indexing.mismatch",
                    tags=[
                        f"organization_id:{org_id}",
                        f"entity_type:{entity_type}",
                        f"postgres_count:{postgres_count}",
                        f"falkor_count:{falkor_count}",
                    ],
                )
            else:
                workflow.logger.info(
                    f"MATTY INDEXING FRESH MATCH for org_id={org_id}, entity_type={entity_type}. "
                    f"postgres_count={postgres_count}, falkor_count={falkor_count}"
                )

    @workflow.run
    async def run(self, data: IndexSpecificOrganizationInput) -> None:
        """Workflow entry point to index CoverRight organization."""
        workflow.logger.info(
            f"Starting workflow: IndexSpecificOrganizationWorkflow for org {data.org_id}"
        )

        # Get user IDs for the org (this needs to be done first)
        user_org_id_pairs_str = await workflow.execute_activity(
            list_organization_and_user_ids_activity,
            start_to_close_timeout=timedelta(minutes=10),
            schedule_to_close_timeout=timedelta(minutes=15),
            task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
        )

        if not user_org_id_pairs_str:
            workflow.logger.warning(
                "No organization/user ID pairs found. Exiting workflow."
            )
            return

        # Filter for specific org users
        org_users = [
            user_id
            for user_id, org_id in user_org_id_pairs_str
            if org_id == data.org_id
        ]

        # Start all indexing tasks in parallel
        indexing_tasks = [
            # User indexing
            workflow.start_activity(
                index_users_activity,
                args=[data.org_id, org_users],
                start_to_close_timeout=timedelta(hours=1),
                schedule_to_close_timeout=timedelta(hours=1, minutes=10),
                task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
            ),
            # Entity indexing
            self._index_all_batches("accounts", data.org_id, data.batch_size),
            self._index_all_batches("contacts", data.org_id, data.batch_size),
            self._index_all_batches("pipelines", data.org_id, data.batch_size),
            self._index_all_batches(
                "contact_pipeline_roles", data.org_id, data.batch_size
            ),
            self._index_all_batches(
                "contact_account_roles", data.org_id, data.batch_size
            ),
            self._index_all_batches("custom_objects", data.org_id, data.batch_size),
        ]

        # Wait for all indexing tasks to complete
        for i in range(0, len(indexing_tasks), self.MAX_PARALLEL_ENTITY_TASKS):
            batch = indexing_tasks[i : i + self.MAX_PARALLEL_ENTITY_TASKS]
            await asyncio.gather(*batch)

        # After all entities are indexed, index relationships
        await workflow.execute_activity(
            index_organization_relationships_activity_in_batches,
            args=[data.org_id, None, data.batch_size],
            start_to_close_timeout=timedelta(hours=1),
            schedule_to_close_timeout=timedelta(hours=1, minutes=10),
            task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
        )

        workflow.logger.info("Completed workflow: IndexCoverRightOrganizationsWorkflow")


@workflow.defn
class IndexCoverRightOrganizationsWorkflow:
    """Workflow to index CoverRight organization using IndexSpecificOrganizationWorkflow."""

    COVER_RIGHT_ORG_ID = "cce7b290-8a08-4904-a6c7-2b6613877cf5"
    BATCH_SIZE = 10000

    @workflow.run
    async def run(self) -> None:
        """Workflow entry point to index CoverRight organization."""
        workflow.logger.info("Starting workflow: IndexCoverRightOrganizationsWorkflow")

        # Create input for IndexSpecificOrganizationWorkflow
        input_data = IndexSpecificOrganizationInput(
            org_id=self.COVER_RIGHT_ORG_ID, batch_size=self.BATCH_SIZE
        )

        # Start the workflow
        await workflow.start_child_workflow(
            IndexSpecificOrganizationWorkflow.run,
            args=[input_data],
            id=f"index-coverright-{self.COVER_RIGHT_ORG_ID}",
            task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
            execution_timeout=timedelta(hours=2),
            parent_close_policy=ParentClosePolicy.ABANDON,
        )

        workflow.logger.info("Completed workflow: IndexCoverRightOrganizationsWorkflow")


@workflow.defn
class IndexCoverRightRelationshipsWorkflow:
    """Indexes ONLY relationships for the CoverRight organization."""

    COVER_RIGHT_ORG_ID = "cce7b290-8a08-4904-a6c7-2b6613877cf5"
    # COVER_RIGHT_ORG_ID = (
    #     "659ccdae-3b6b-4fd9-8aff-ffa4ef777bde"  # Test org in dev
    # )

    @workflow.run
    async def run(self) -> None:
        """Workflow entry point to index CoverRight organization relationships."""
        workflow.logger.info("Starting workflow: IndexCoverRightRelationshipsWorkflow")

        try:
            await workflow.execute_activity(
                index_organization_relationships_activity,
                args=[self.COVER_RIGHT_ORG_ID],
                start_to_close_timeout=timedelta(
                    hours=1
                ),  # Relationship indexing might take time
                schedule_to_close_timeout=timedelta(hours=1, minutes=10),
                task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
            )
        except Exception as e:
            workflow.logger.exception(
                f"Error during relationship indexing for org {self.COVER_RIGHT_ORG_ID}"
            )
            raise e

        workflow.logger.info("Completed workflow: IndexCoverRightRelationshipsWorkflow")


@workflow.defn
class IndexMonitoringWorkflow:
    """Workflow to monitor and compare entity counts between Postgres and FalkorDB."""

    @workflow.run
    async def run(self) -> None:
        """Workflow entry point to monitor indexing."""
        workflow.logger.info("Starting workflow: IndexingMonitoringWorkflow")

        # Get all organizations
        user_org_id_pairs_str = await workflow.execute_activity(
            list_organization_and_user_ids_activity,
            start_to_close_timeout=timedelta(minutes=10),
            schedule_to_close_timeout=timedelta(minutes=15),
            task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
        )

        if not user_org_id_pairs_str:
            workflow.logger.warning("No organizations found. Exiting workflow.")
            return

        # Get unique organization IDs
        org_ids = {org_id for _, org_id in user_org_id_pairs_str}
        workflow.logger.info(f"Found {len(org_ids)} organizations to monitor")

        # Entity types to monitor with their corresponding domain model classes
        entity_types = {
            "accounts": "AccountV2",
            "contacts": "ContactV2",
            "pipelines": "PipelineV2",
            "contact_pipeline_roles": "ContactPipelineRoleV2",
            "contact_account_roles": "ContactAccountRoleV2",
            "custom_objects": None,  # Custom objects are handled differently
        }

        # Monitor each organization
        for org_id in org_ids:
            workflow.logger.info(f"Monitoring organization: {org_id}")

            # Check each entity type
            for entity_type, domain_model in entity_types.items():
                # Get Postgres count
                postgres_count = await workflow.execute_activity(
                    f"get_{entity_type}_count_activity",
                    args=[org_id],
                    start_to_close_timeout=timedelta(minutes=5),
                    schedule_to_close_timeout=timedelta(minutes=10),
                    task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
                )

                # Get FalkorDB count (skip for custom_objects as it's not supported)
                if domain_model is not None:
                    falkor_count = await workflow.execute_activity(
                        get_domain_object_count_falkor_activity,
                        args=[org_id, entity_type],
                        start_to_close_timeout=timedelta(minutes=5),
                        schedule_to_close_timeout=timedelta(minutes=10),
                        task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
                    )

                    # Compare counts and log metrics
                    if postgres_count != falkor_count:
                        workflow.logger.error(
                            f"MATTY INDEXING FRESH MISMATCH for org {org_id}, entity {entity_type}: "
                            f"Postgres={postgres_count}, FalkorDB={falkor_count}"
                        )

                        # Record metric for mismatch
                        custom_metric.increment(
                            metric_name="alert.indexing.mismatch",
                            tags=[
                                f"organization_id:{org_id}",
                                f"entity_type:{entity_type}",
                                f"postgres_count:{postgres_count}",
                                f"falkor_count:{falkor_count}",
                            ],
                        )
                    else:
                        workflow.logger.info(
                            f"MATTY INDEXING FRESH MATCH for org {org_id}, entity {entity_type}: "
                            f"Postgres={postgres_count}, FalkorDB={falkor_count}"
                        )

        workflow.logger.info("Completed workflow: IndexingMonitoringWorkflow")
