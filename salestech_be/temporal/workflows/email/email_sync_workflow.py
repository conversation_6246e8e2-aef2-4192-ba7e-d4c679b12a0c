from temporalio import workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    from dataclasses import dataclass
    from datetime import timedelta
    from uuid import UUID

    from salestech_be.core.ai.email.activities import (
        parse_and_classify_main_email_body_for_global_threads,
    )
    from salestech_be.integrations.temporal.config import (
        DEFAULT_TASK_MAX_RETRY,
        TemporalTaskQueue,
    )
    from salestech_be.settings import settings
    from salestech_be.temporal.activities.email.email_sync_activity import (
        construct_global_thread_activity,
        create_bot_assistant_activity,
        generate_activity_insight_activity,
        generate_global_thread_insight_activity,
        sync_email_activity,
        sync_nylas_thread_activity,
    )


@dataclass
class EmailSyncWorkflowInput:
    email_account_id: UUID
    organization_id: UUID
    initial_sync: bool = False


@workflow.defn
class EmailSyncWorkflow:
    RETRY_POLICY: RetryPolicy = RetryPolicy(
        maximum_interval=timedelta(minutes=1),
        maximum_attempts=DEFAULT_TASK_MAX_RETRY,
        non_retryable_error_types=[
            "ValueError",
            "ResourceNotFoundError",
            "IllegalStateError",
            "WorkflowFailError",
        ],
    )

    @workflow.run
    async def run(
        self,
        email_sync_input: EmailSyncWorkflowInput,
    ) -> None:
        # Step 1: Sync email (thread and messages)
        synced_thread_id_list = await workflow.execute_activity(
            sync_email_activity,
            args=(
                email_sync_input.organization_id,
                email_sync_input.email_account_id,
                email_sync_input.initial_sync,
            ),
            start_to_close_timeout=timedelta(minutes=30),
            retry_policy=self.RETRY_POLICY,
        )

        # Step 2: Construct global thread and messages
        global_thread_id_list: list[UUID] = await workflow.execute_activity(
            construct_global_thread_activity,
            args=(synced_thread_id_list, email_sync_input.organization_id),
            # Single invocation timeout 3 minutes
            start_to_close_timeout=timedelta(minutes=3),
            # Max overall execution time including retries
            schedule_to_close_timeout=timedelta(minutes=5),
            retry_policy=self.RETRY_POLICY,
        )

        # Step 3: Parse main body text and classify and persist metadata
        await workflow.execute_activity(
            parse_and_classify_main_email_body_for_global_threads,
            args=(global_thread_id_list, email_sync_input.organization_id),
            # Single invocation timeout 5 minutes
            start_to_close_timeout=timedelta(minutes=5),
            # Max overall execution time including retries
            schedule_to_close_timeout=timedelta(minutes=10),
            retry_policy=self.RETRY_POLICY,
        )

        # Step 4: Generate global thread insight
        await workflow.execute_activity(
            generate_global_thread_insight_activity,
            args=(global_thread_id_list, email_sync_input.organization_id),
            # Single invocation timeout 5 minutes
            start_to_close_timeout=timedelta(minutes=10),
            # Max overall execution time including retries
            schedule_to_close_timeout=timedelta(minutes=15),
            retry_policy=self.RETRY_POLICY,
        )

        # Step 5: Generate Activity insight
        await workflow.execute_activity(
            generate_activity_insight_activity,
            args=(global_thread_id_list, email_sync_input.organization_id),
            # Single invocation timeout 3 minutes
            start_to_close_timeout=timedelta(minutes=3),
            # Max overall execution time including retries
            schedule_to_close_timeout=timedelta(minutes=10),
            retry_policy=self.RETRY_POLICY,
        )


@workflow.defn
class NylasThreadSyncWorkflow:
    RETRY_POLICY: RetryPolicy = RetryPolicy(
        maximum_interval=timedelta(minutes=1),
        maximum_attempts=DEFAULT_TASK_MAX_RETRY,
        non_retryable_error_types=[],
    )

    @staticmethod
    def get_workflow_id(nylas_grant_id: str, nylas_thread_id: str) -> str:
        return f"nylas_thread_sync::{nylas_grant_id}::{nylas_thread_id}"

    @staticmethod
    def get_task_queue() -> str:
        return TemporalTaskQueue.EMAIL_TASK_QUEUE

    @workflow.run
    async def run(
        self,
        nylas_grant_id: str,
        nylas_thread_id: str,
    ) -> None:
        # Step 0: Create bot assistant if conditions are met
        # Only process if this is a bot assistant grant ID
        if nylas_grant_id == settings.bot_assistant_nylas_grant_id:
            # Execute the bot assistant activity
            await workflow.execute_activity(
                create_bot_assistant_activity,
                args=(nylas_grant_id, nylas_thread_id),
                start_to_close_timeout=timedelta(minutes=5),
                retry_policy=self.RETRY_POLICY,
            )
            return

        # Step 1: Sync nylas thread and messages
        # Will only return EmailDto with new messages synced.
        results: dict[str, list[UUID]] = await workflow.execute_activity(
            sync_nylas_thread_activity,
            args=(nylas_grant_id, nylas_thread_id),
            # Single invocation timeout 4 minutes
            start_to_close_timeout=timedelta(minutes=4),
            # Max overall execution time including retries
            schedule_to_close_timeout=timedelta(minutes=8),
            retry_policy=self.RETRY_POLICY,
        )

        for organization_id_str, thread_id_list in results.items():
            # Step 2: Construct global thread and messages
            global_thread_id_list: list[UUID] = await workflow.execute_activity(
                construct_global_thread_activity,
                args=(thread_id_list, UUID(organization_id_str)),
                # Single invocation timeout 3 minutes
                start_to_close_timeout=timedelta(minutes=3),
                # Max overall execution time including retries
                schedule_to_close_timeout=timedelta(minutes=5),
                retry_policy=self.RETRY_POLICY,
            )

            # Step 3: Parse main body text and classify and persist metadata
            await workflow.execute_activity(
                parse_and_classify_main_email_body_for_global_threads,
                args=(global_thread_id_list, UUID(organization_id_str)),
                # Single invocation timeout 5 minutes
                start_to_close_timeout=timedelta(minutes=5),
                # Max overall execution time including retries
                schedule_to_close_timeout=timedelta(minutes=10),
                retry_policy=self.RETRY_POLICY,
            )

            # Step 4: Generate global thread insight
            await workflow.execute_activity(
                generate_global_thread_insight_activity,
                args=(global_thread_id_list, UUID(organization_id_str)),
                # Single invocation timeout 5 minutes
                start_to_close_timeout=timedelta(minutes=10),
                # Max overall execution time including retries
                schedule_to_close_timeout=timedelta(minutes=15),
                retry_policy=self.RETRY_POLICY,
            )

            # Step 5: Generate Activity insight
            await workflow.execute_activity(
                generate_activity_insight_activity,
                args=(global_thread_id_list, UUID(organization_id_str)),
                # Single invocation timeout 3 minutes
                start_to_close_timeout=timedelta(minutes=3),
                # Max overall execution time including retries
                schedule_to_close_timeout=timedelta(minutes=10),
                retry_policy=self.RETRY_POLICY,
            )
