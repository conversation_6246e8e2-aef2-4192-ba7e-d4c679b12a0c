from temporalio import workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    from datetime import timedelta
    from uuid import UUID

    from salestech_be.core.ai.email.activities import (
        parse_and_classify_main_email_body_for_global_threads,
    )
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_MAX_RETRY
    from salestech_be.temporal.activities.email.email_sync_activity import (
        generate_activity_insight_activity,
        generate_global_thread_insight_activity,
    )


@workflow.defn
class EmailInsightWorkflow:
    RETRY_POLICY: RetryPolicy = RetryPolicy(
        maximum_interval=timedelta(minutes=1),
        maximum_attempts=DEFAULT_TASK_MAX_RETRY,
        non_retryable_error_types=[
            "ValueError",
            "ResourceNotFoundError",
            "IllegalStateError",
            "WorkflowFailError",
        ],
    )

    @workflow.run
    async def run(
        self, global_thread_id_list: list[UUID], organization_id: UUID
    ) -> None:
        # Step 1: Parse main body text and classify and persist metadata
        await workflow.execute_activity(
            parse_and_classify_main_email_body_for_global_threads,
            args=(global_thread_id_list, organization_id),
            # Single invocation timeout 5 minutes
            start_to_close_timeout=timedelta(minutes=5),
            # Max overall execution time including retries
            schedule_to_close_timeout=timedelta(minutes=10),
            retry_policy=self.RETRY_POLICY,
        )

        # Step 2: Generate global thread insight
        await workflow.execute_activity(
            generate_global_thread_insight_activity,
            args=(global_thread_id_list, organization_id),
            # Single invocation timeout 5 minutes
            start_to_close_timeout=timedelta(minutes=10),
            # Max overall execution time including retries
            schedule_to_close_timeout=timedelta(minutes=15),
            retry_policy=self.RETRY_POLICY,
        )

        # Step 3: Generate Activity insight
        await workflow.execute_activity(
            generate_activity_insight_activity,
            args=(global_thread_id_list, organization_id),
            # Single invocation timeout 2 minutes
            start_to_close_timeout=timedelta(minutes=3),
            # Max overall execution time including retries
            schedule_to_close_timeout=timedelta(minutes=10),
            retry_policy=self.RETRY_POLICY,
        )
