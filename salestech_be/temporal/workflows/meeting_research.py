# WARNING: Do not import anything that touches network, filesystem, or any non-deterministic
# operations in activities. This can lead to non-deterministic behavior in workflows.
# Only import pure functions or deterministic operations in workflows.

# Import activity, passing it through the sandbox without reloading the module

from temporalio import workflow
from temporalio.common import RetryPolicy, WorkflowIDReusePolicy
from temporalio.exceptions import WorkflowAlreadyStartedError
from temporalio.workflow import ParentClosePolicy

# NOTE: use imports_passed_through on ALL non-temporal imports to mitigate memory leak
with workflow.unsafe.imports_passed_through():
    import asyncio
    from datetime import timedelta
    from uuid import UUID

    from salestech_be.settings import settings
    from salestech_be.temporal.activities.intel_converters import (
        GetIntelPersonIdFromContactIdInput,
        get_intel_person_id_from_contact_id,
    )
    from salestech_be.temporal.activities.meeting.meeting_research import (
        MeetingResearchInput,
        get_account_and_contact_ids_from_meeting_id,
    )
    from salestech_be.temporal.util import TemporalPatch
    from salestech_be.temporal.workflows.research_agent.const import (
        DEFAULT_MEETING_RESEARCH_ACTIVITY_RETRY_POLICY,
    )
    from salestech_be.temporal.workflows.research_agent.research_company_workflow import (
        ResearchCompanyWorkflow,
    )
    from salestech_be.temporal.workflows.research_agent.research_person_workflow import (
        ResearchPersonWorkflow,
    )
    from salestech_be.temporal.workflows.research_agent.schema import (
        MeetingResearchOutput,
        ResearchCompanyInput,
        ResearchPersonInput,
        ResearchTime,
    )


logger = workflow.logger


@workflow.defn
class MeetingResearchWorkflow:
    """Run research for a meeting

    This workflow is created whenever a meeting is created. It researches the meeting
    attendees and company. The workflow can be cancelled when the meeting is deleted
    using the workflow ID from get_workflow_id(). On meeting update, the workflow
    should be cancelled and recreated.
    """

    @staticmethod
    def get_workflow_id(meeting_id: UUID, organization_id: UUID) -> str:
        return f"meeting_research:meeting:{meeting_id}:org:{organization_id}"

    @workflow.run
    async def run(  # noqa: C901,PLR0912,PLR0915
        self,
        meeting_research_input: MeetingResearchInput,
    ) -> MeetingResearchOutput:
        output_: MeetingResearchOutput = MeetingResearchOutput(
            research_person_inputs=[],
            research_company_inputs=[],
        )

        # Convert company_id and contact_id to intel IDs
        meeting_research_output = await workflow.execute_activity(
            get_account_and_contact_ids_from_meeting_id,
            args=[meeting_research_input],
            start_to_close_timeout=timedelta(seconds=90),
            retry_policy=DEFAULT_MEETING_RESEARCH_ACTIVITY_RETRY_POLICY,
        )

        if not meeting_research_output.meeting:
            logger.info(f"Meeting not found: {meeting_research_input.meeting_id}")
            return output_

        for contact in meeting_research_output.contacts:
            if not contact or not contact.linkedin_url:
                logger.info(f"Skipping contact {contact}: No LinkedIn URL")
                continue

            intel_person_id = await workflow.execute_activity(
                get_intel_person_id_from_contact_id,
                args=[
                    GetIntelPersonIdFromContactIdInput(
                        contact_id=contact.id, contact_linkedin_url=contact.linkedin_url
                    )
                ],
                start_to_close_timeout=timedelta(seconds=30),
                retry_policy=DEFAULT_MEETING_RESEARCH_ACTIVITY_RETRY_POLICY,
            )

            if contact.primary_email:
                output_.research_person_inputs.append(
                    ResearchPersonInput(
                        intel_person_id=intel_person_id,
                        linkedin_url=contact.linkedin_url,
                        business_email=contact.primary_email,
                        organization_id=meeting_research_input.organization_id,
                        contact_id=contact.id,
                        research_time=ResearchTime(
                            wf_started_at=workflow.time_ns(),
                            intel_created_at=workflow.time_ns(),
                            cdc_triggered_at=workflow.time_ns(),
                        ),
                    )
                )

        for account in meeting_research_output.accounts:
            if not account.domain_name:
                continue

            if account and account.domain_name:
                output_.research_company_inputs.append(
                    ResearchCompanyInput(
                        account_id=account.id,
                        company_name=account.display_name,
                        company_domain=account.domain_name,
                        research_time=ResearchTime(
                            wf_started_at=workflow.time_ns(),
                            intel_created_at=workflow.time_ns(),
                            cdc_triggered_at=workflow.time_ns(),
                        ),
                    )
                )
        # Store handles from starting child workflows
        company_workflow_handles = []
        person_workflow_handles = []

        for research_company_input in output_.research_company_inputs:
            if workflow.patched(TemporalPatch.REJECT_DUP):
                try:
                    handle = await workflow.start_child_workflow(
                        ResearchCompanyWorkflow.run,
                        args=[research_company_input],
                        id=ResearchCompanyWorkflow.get_workflow_id(
                            research_company_input.account_id,
                            research_company_input.intel_company_id,
                        ),
                        id_reuse_policy=WorkflowIDReusePolicy.REJECT_DUPLICATE,
                        retry_policy=RetryPolicy(maximum_attempts=3),
                        parent_close_policy=ParentClosePolicy.ABANDON,
                        # ParentClosePolicy.REQUEST_CANCEL would fail parent undesirably when child gets deduped
                        # task queue is inherited from the parent workflow
                        run_timeout=timedelta(
                            days=settings.research_company_workflow_default_run_timeout
                        ),
                    )
                    company_workflow_handles.append(handle)
                except WorkflowAlreadyStartedError as e:
                    logger.info(
                        f"skipping workflow run because one is already running: {e}"
                    )
            else:
                handle = await workflow.start_child_workflow(
                    ResearchCompanyWorkflow.run,
                    args=[research_company_input],
                    id=ResearchCompanyWorkflow.get_workflow_id(
                        research_company_input.account_id,
                        research_company_input.intel_company_id,
                    ),
                    id_reuse_policy=WorkflowIDReusePolicy.TERMINATE_IF_RUNNING,
                    retry_policy=RetryPolicy(maximum_attempts=3),
                    parent_close_policy=ParentClosePolicy.ABANDON,
                    # ParentClosePolicy.REQUEST_CANCEL would fail parent undesirably when child gets deduped
                    # task queue is inherited from the parent workflow
                    run_timeout=timedelta(
                        days=settings.research_company_workflow_default_run_timeout
                    ),
                )
                company_workflow_handles.append(handle)

        for research_person_input in output_.research_person_inputs:
            if workflow.patched(TemporalPatch.REJECT_DUP):
                try:
                    handle = await workflow.start_child_workflow(
                        # NOTE: mypy bug
                        # salestech_be/temporal/workflows/meeting_research.py:123: error: Argument 1 to
                        # "start_child_workflow" has incompatible type
                        # "Callable[[ResearchPersonWorkflow, ResearchPersonInput], Coroutine[Any, Any, ResearchPersonResult]]";
                        # expected
                        # "Callable[[ResearchCompanyWorkflow, ResearchPersonInput], Awaitable[ResearchCompanyResult]]"
                        ResearchPersonWorkflow.run,  # type: ignore
                        args=[research_person_input],
                        id=ResearchPersonWorkflow.get_workflow_id(
                            research_person_input.contact_id,
                            research_person_input.intel_person_id,
                        ),
                        id_reuse_policy=WorkflowIDReusePolicy.REJECT_DUPLICATE,
                        retry_policy=RetryPolicy(maximum_attempts=3),
                        parent_close_policy=ParentClosePolicy.ABANDON,
                        # ParentClosePolicy.REQUEST_CANCEL would fail parent undesirably when child gets deduped
                        # task queue is inherited from the parent workflow
                        run_timeout=timedelta(
                            days=settings.research_person_workflow_default_run_timeout
                        ),
                    )
                    person_workflow_handles.append(handle)
                except WorkflowAlreadyStartedError as e:
                    logger.info(
                        f"skipping workflow run because one is already running: {e}"
                    )
            else:
                handle = await workflow.start_child_workflow(
                    # NOTE: mypy bug
                    # salestech_be/temporal/workflows/meeting_research.py:123: error: Argument 1 to
                    # "start_child_workflow" has incompatible type
                    # "Callable[[ResearchPersonWorkflow, ResearchPersonInput], Coroutine[Any, Any, ResearchPersonResult]]";
                    # expected
                    # "Callable[[ResearchCompanyWorkflow, ResearchPersonInput], Awaitable[ResearchCompanyResult]]"
                    ResearchPersonWorkflow.run,  # type: ignore
                    args=[research_person_input],
                    id=ResearchPersonWorkflow.get_workflow_id(
                        research_person_input.contact_id,
                        research_person_input.intel_person_id,
                    ),
                    id_reuse_policy=WorkflowIDReusePolicy.TERMINATE_IF_RUNNING,
                    retry_policy=RetryPolicy(maximum_attempts=3),
                    parent_close_policy=ParentClosePolicy.ABANDON,
                    # ParentClosePolicy.REQUEST_CANCEL would fail parent undesirably when child gets deduped
                    # task queue is inherited from the parent workflow
                    run_timeout=timedelta(
                        days=settings.research_person_workflow_default_run_timeout
                    ),
                )
                person_workflow_handles.append(handle)

        # Wait for all child workflows to complete
        try:
            await asyncio.gather(
                *company_workflow_handles,
                *person_workflow_handles,
                return_exceptions=True,  # This prevents exceptions from propagating up
            )
        except Exception as e:
            logger.error(f"Some child workflows failed: {e}")
            # Continue execution - don't let child failures stop the parent workflow

        # Schedule day-of research
        next_research_time_epoch = (
            meeting_research_output.meeting.next_research_time_epoch
        )
        current_time = workflow.now().timestamp()
        if next_research_time_epoch > current_time:
            # Sleep until the day of the meeting
            sleep_duration = next_research_time_epoch - current_time
            # workflow.sleep sleeps while asyncio.sleep creates a timer
            # both work though
            await workflow.sleep(duration=sleep_duration)

            for research_company_input in output_.research_company_inputs:
                if workflow.patched(TemporalPatch.REJECT_DUP):
                    try:
                        await workflow.start_child_workflow(
                            ResearchCompanyWorkflow.run,
                            args=[research_company_input],
                            id=ResearchCompanyWorkflow.get_workflow_id(
                                research_company_input.account_id,
                                research_company_input.intel_company_id,
                            ),
                            id_reuse_policy=WorkflowIDReusePolicy.REJECT_DUPLICATE,
                            retry_policy=RetryPolicy(maximum_attempts=3),
                            parent_close_policy=ParentClosePolicy.ABANDON,
                            # ParentClosePolicy.REQUEST_CANCEL would fail parent undesirably when child gets deduped
                            # task queue is inherited from the parent workflow
                            run_timeout=timedelta(
                                days=settings.research_company_workflow_default_run_timeout
                            ),
                        )
                    except WorkflowAlreadyStartedError as e:
                        logger.info(
                            f"skipping workflow run because one is already running: {e}"
                        )
                else:
                    await workflow.start_child_workflow(
                        ResearchCompanyWorkflow.run,
                        args=[research_company_input],
                        id=ResearchCompanyWorkflow.get_workflow_id(
                            research_company_input.account_id,
                            research_company_input.intel_company_id,
                        ),
                        id_reuse_policy=WorkflowIDReusePolicy.TERMINATE_IF_RUNNING,
                        retry_policy=RetryPolicy(maximum_attempts=3),
                        parent_close_policy=ParentClosePolicy.ABANDON,
                        # ParentClosePolicy.REQUEST_CANCEL would fail parent undesirably when child gets deduped
                        # task queue is inherited from the parent workflow
                        run_timeout=timedelta(
                            days=settings.research_company_workflow_default_run_timeout
                        ),
                    )

            for research_person_input in output_.research_person_inputs:
                if workflow.patched(TemporalPatch.REJECT_DUP):
                    try:
                        await workflow.start_child_workflow(
                            ResearchPersonWorkflow.run,
                            args=[
                                research_person_input,
                            ],
                            id=ResearchPersonWorkflow.get_workflow_id(
                                research_person_input.contact_id,
                                research_person_input.intel_person_id,
                            ),
                            id_reuse_policy=WorkflowIDReusePolicy.REJECT_DUPLICATE,
                            retry_policy=RetryPolicy(maximum_attempts=3),
                            parent_close_policy=ParentClosePolicy.ABANDON,
                            # ParentClosePolicy.REQUEST_CANCEL would fail parent undesirably when child gets deduped
                            # task queue is inherited from the parent workflow
                            run_timeout=timedelta(
                                days=settings.research_person_workflow_default_run_timeout
                            ),
                        )
                    except WorkflowAlreadyStartedError as e:
                        logger.info(
                            f"skipping workflow run because one is already running: {e}"
                        )
                else:
                    await workflow.start_child_workflow(
                        ResearchPersonWorkflow.run,
                        args=[
                            research_person_input,
                        ],
                        id=ResearchPersonWorkflow.get_workflow_id(
                            research_person_input.contact_id,
                            research_person_input.intel_person_id,
                        ),
                        id_reuse_policy=WorkflowIDReusePolicy.TERMINATE_IF_RUNNING,
                        retry_policy=RetryPolicy(maximum_attempts=3),
                        parent_close_policy=ParentClosePolicy.ABANDON,
                        # ParentClosePolicy.REQUEST_CANCEL would fail parent undesirably when child gets deduped
                        # task queue is inherited from the parent workflow
                    )
        return output_
