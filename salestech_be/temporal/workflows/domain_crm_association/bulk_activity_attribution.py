import uuid
from collections.abc import Sequence
from datetime import timedelta

from temporalio import workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.domain_crm_association.types import (
        BasicDomainCrmCaptureInput,
    )
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_MAX_RETRY
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.workflows.domain_crm_association.activity_attribution import (
        DomainCrmAssociationAttributionWorkflow,
    )

logger = get_logger(__name__)


@workflow.defn
class BulkActivityAttributionWorkflow:
    """Workflow for attributing multiple activities to CRM entities in bulk."""

    RETRY_POLICY: RetryPolicy = RetryPolicy(
        initial_interval=timedelta(seconds=1),
        maximum_interval=timedelta(minutes=1),
        maximum_attempts=DEFAULT_TASK_MAX_RETRY,
        non_retryable_error_types=[
            "ValueError",
            "ResourceNotFoundError",
            "IllegalStateError",
            "WorkflowFailError",
        ],
    )

    DEFAULT_ACTIVITY_TIMEOUT: timedelta = timedelta(seconds=60)
    DEFAULT_CHILD_WORKFLOW_TIMEOUT: timedelta = timedelta(minutes=5)

    @workflow.run
    async def run(
        self,
        data_items: Sequence[BasicDomainCrmCaptureInput],
    ) -> list[bool]:
        """
        Run the workflow to attribute multiple activities to CRM entities in bulk.

        Args:
            data_items: A sequence of input data items for the workflow, each containing:
                organization_id: The organization ID
                user_id: The user ID who created the activity
                associations: List of domain CRM associations
                contact_id: Optional contact ID to attribute
                account_id: Optional account ID to attribute
                pipeline_id: Optional pipeline ID to attribute
                phone_number: Optional phone number associated with the activity
                email: Optional email associated with the activity

        Returns:
            list[bool]: Whether each attribution was successful
        """
        workflow.logger.info(
            f"Starting bulk activity attribution workflow for {len(data_items)} activities"
        )
        results = []

        for data in data_items:
            try:
                # Instead of processing each activity directly with activities,
                # delegate to a child workflow for each item
                child_workflow_result = await workflow.execute_child_workflow(
                    DomainCrmAssociationAttributionWorkflow.run,
                    args=[data],
                    id=f"domain_crm_attribution_{data.organization_id}_{data.associations[0].domain_type if data.associations else 'unknown'}_{data.associations[0].id if data.associations else uuid.uuid4()}",
                    task_queue=workflow.info().task_queue,
                    retry_policy=self.RETRY_POLICY,
                    execution_timeout=self.DEFAULT_CHILD_WORKFLOW_TIMEOUT,
                )

                results.append(child_workflow_result)

            except Exception as e:
                workflow.logger.error(f"Failed to attribute activity in bulk: {e}")
                results.append(False)

        return results
