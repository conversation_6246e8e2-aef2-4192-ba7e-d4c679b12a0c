"""
Decorator for Temporal activities that automatically adds trace_id to logging context.

This ensures all logs within a single activity execution use the same trace_id,
making it easier to trace the activity's execution.
"""

from __future__ import annotations

import functools
import uuid
from collections.abc import Awaitable, Callable
from typing import Param<PERSON><PERSON>, TypeVar, cast

from salestech_be.ree_logging import get_logger

# Get the root logger for better visibility
logger = get_logger("salestech_be")

T = TypeVar("T")
P = ParamSpec("P")


def with_tracing(
    activity_fn: Callable[P, Awaitable[T]],
) -> Callable[P, Awaitable[T]]:
    """
    Decorator for Temporal activities that automatically adds trace_id to the logging context.

    This ensures all logs within a single activity execution use the same trace_id,
    making it easier to trace the activity's execution.

    Example usage:
    ```
    @activity.defn
    @with_tracing
    async def my_activity(param1, param2):
        # All logging inside this function will have the same trace_id
        logger.info("Doing something")
        return result
    ```

    Note: Apply this decorator AFTER the @activity.defn decorator.
    """

    @functools.wraps(activity_fn)
    async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
        # Generate a unique trace_id for this activity execution
        trace_id = str(uuid.uuid4())

        # Use the same logger for everything to maintain trace_id consistency
        # Execute everything with the trace_id in the logging context
        with logger.contextualize(trace_id=trace_id):
            # Log before activity execution with explicit level for Datadog visibility
            logger.info(
                f"[TEMPORAL_TRACING] Starting activity {activity_fn.__name__} with trace_id={trace_id}"
            )

            # Execute the activity within the same trace context
            result = await activity_fn(*args, **kwargs)

            # Log after activity execution with explicit level for Datadog visibility
            logger.info(
                f"[TEMPORAL_TRACING] Completed activity {activity_fn.__name__} with trace_id={trace_id}"
            )

            return result

    return cast(Callable[P, Awaitable[T]], wrapper)
