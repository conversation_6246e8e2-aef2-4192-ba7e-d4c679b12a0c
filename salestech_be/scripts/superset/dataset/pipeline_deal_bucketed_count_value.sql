insert into "public"."reporting_dataset" (
  "id",
  "name",
  "description",
  "organization_id",
  "created_at",
  "created_by_user_id",
  "updated_at",
  "updated_by_user_id",
  "deleted_at",
  "deleted_by_user_id",
  "type",
  "source",
  "table_reference",
  "query_config",
  "sql_statement"
) values (
  gen_random_uuid(),
  'pipeline_deal_bucketed_count_value',
  NULL,
  NULL,
  NOW(),
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  'SQL',
  'TEMPLATE',
  NULL,
  NULL,
  'WITH

-- Generate amount boundary
amount_boundary AS (
  SELECT 0 AS lower_boundary, power(10, 3) AS upper_boundary
  UNION ALL
  SELECT power(10, 3), power(10, 4)
  UNION ALL
  SELECT power(10, 4), power(10, 5)
  UNION ALL
  SELECT power(10, 5), power(10, 6)
  UNION ALL
  SELECT power(10, 6), power(10, 7)
  UNION ALL
  SELECT power(10, 7), power(10, 8)
  UNION ALL
  SELECT power(10, 8), power(10, 9)
  UNION ALL
  SELECT power(10, 9), power(10, 10)
  UNION ALL
  SELECT power(10, 10), power(10, 11)
  UNION ALL
  SELECT power(10, 11), power(10, 12)
),

-- Get all closed won pipelines
closed_pipeline AS (
  SELECT
    p.id,
    p.amount
  FROM raw_pipeline as p
  JOIN raw_pipeline_stage_select_list_value_metadata AS psslv
    ON psslv.select_list_value_id = p.stage_id
    AND psslv.organization_id = p.organization_id
    AND psslv.outcome_state = ''CLOSED_WON''
  WHERE p.amount IS NOT NULL
    AND p.closed_at IS NOT NULL
    AND p.archived_at IS NULL
    {{ and_filter_values(''organization_id'', col_alias=''p.organization_id'') }}
    {{ and_filter_values(''owner_user_id'', col_alias=''p.owner_user_id'') }}
    {{ and_filter_values(''closed_at'', col_alias=''p.closed_at'') }}
    {{ and_filter_values(''amount'', col_alias=''p.amount'') }}
),

-- Cal the min_amount, max_amount
pipeline_stats AS (
  SELECT
    MIN(amount) AS min_amount,
    MAX(amount) AS max_amount
  FROM closed_pipeline
),

-- Filter the buckets by both upper_boundary greater than min_amount
-- and lower_boundary less than or equal to max_amount
pipeline_boundary_buckets AS (
  SELECT *
  FROM pipeline_stats ps, amount_boundary ab
  WHERE ab.upper_boundary > ps.min_amount
    AND ab.lower_boundary <= ps.max_amount
),

-- Count the number and total amount of closed pipelines in each bucket
pipeline_bucket_stats AS (
  SELECT
    fbb.lower_boundary AS bucket_range_start,
    fbb.upper_boundary AS bucket_range_end,
    COUNT(cp.id) AS pipeline_count,
    ROUND(COALESCE(SUM(cp.amount), 0), 2) AS pipeline_value
  FROM pipeline_boundary_buckets AS fbb
  LEFT JOIN closed_pipeline cp
    ON cp.amount >= fbb.lower_boundary
    AND cp.amount < fbb.upper_boundary
  GROUP BY
    fbb.lower_boundary,
    fbb.upper_boundary
  ORDER BY
    fbb.lower_boundary,
    fbb.upper_boundary
)

SELECT * FROM pipeline_bucket_stats
'
)
