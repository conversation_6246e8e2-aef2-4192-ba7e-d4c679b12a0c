-- Example

-- amount_boundary
--  lower_boundary | upper_boundary
-- ----------------+----------------
--               0 |           1000
--            1000 |          10000
--           10000 |         100000
--          100000 |        1000000
--         1000000 |       10000000
--        10000000 |      100000000
--       100000000 |     1000000000
--      1000000000 |    10000000000
--     10000000000 |   100000000000
--    100000000000 |  1000000000000

-- closed_pipeline
--  id | organization_id | amount
-- ----+-----------------+---------
--   1 | A               |     100
--   2 | A               |     200
--   3 | A               |    2000
--   4 | A               |    3000
--   5 | A               |  400000
--   6 | A               |  500000
--   7 | B               |    1000
--   8 | B               |  100000
--   9 | C               | 1000000

-- pipeline_stats
--  organization_id | min_amount | max_amount
-- -----------------+------------+------------
--  B               |       1000 |     100000
--  A               |        100 |     500000
--  C               |    1000000 |    1000000

-- pipeline_boundary_buckets
--  organization_id | min_amount | max_amount | lower_boundary | upper_boundary
-- -----------------+------------+------------+----------------+----------------
--  A               |        100 |     500000 |              0 |           1000
--  A               |        100 |     500000 |           1000 |          10000
--  A               |        100 |     500000 |          10000 |         100000
--  A               |        100 |     500000 |         100000 |        1000000
--  B               |       1000 |     100000 |           1000 |          10000
--  B               |       1000 |     100000 |          10000 |         100000
--  B               |       1000 |     100000 |         100000 |        1000000
--  C               |    1000000 |    1000000 |        1000000 |       10000000

-- pipeline_bucket_stats
--  organization_id | bucket_range_start | bucket_range_end | pipeline_count | pipeline_value
-- -----------------+--------------------+------------------+----------------+----------------
--  A               |                  0 |             1000 |              2 |         300.00
--  A               |               1000 |            10000 |              2 |        5000.00
--  A               |              10000 |           100000 |              0 |           0.00
--  A               |             100000 |          1000000 |              2 |      900000.00
--  B               |               1000 |            10000 |              1 |        1000.00
--  B               |              10000 |           100000 |              0 |           0.00
--  B               |             100000 |          1000000 |              1 |      100000.00
--  C               |            1000000 |         10000000 |              1 |     1000000.00

WITH

-- Generate amount boundary
amount_boundary AS (
  SELECT 0 AS lower_boundary, power(10, 3) AS upper_boundary
  UNION ALL
  SELECT power(10, 3), power(10, 4)
  UNION ALL
  SELECT power(10, 4), power(10, 5)
  UNION ALL
  SELECT power(10, 5), power(10, 6)
  UNION ALL
  SELECT power(10, 6), power(10, 7)
  UNION ALL
  SELECT power(10, 7), power(10, 8)
  UNION ALL
  SELECT power(10, 8), power(10, 9)
  UNION ALL
  SELECT power(10, 9), power(10, 10)
  UNION ALL
  SELECT power(10, 10), power(10, 11)
  UNION ALL
  SELECT power(10, 11), power(10, 12)
),

-- Get all closed won pipelines
closed_pipeline AS (
  SELECT
    p.organization_id,
    p.id,
    p.amount
  FROM pipeline as p
  JOIN pipeline_stage_select_list_value_metadata AS psslv
    ON psslv.select_list_value_id = p.stage_id
    AND psslv.organization_id = p.organization_id
    AND psslv.outcome_state = 'CLOSED_WON'
  WHERE p.amount IS NOT NULL
    AND p.closed_at IS NOT NULL
    AND p.archived_at IS NULL
    {% if filter_values('owner_user_id') %}
    AND p.owner_user_id IN {{ filter_values('owner_user_id') | where_in }}
    {% endif %}
    {% if from_dttm %}
    AND p.closed_at > '{{ from_dttm }}'
    {% endif %}
    {% if to_dttm %}
    AND p.closed_at < '{{ to_dttm }}'
    {% endif %}
    {%- for filter in get_filters('pipeline_amount', remove_filter=True) -%}
      {%- if filter.get('op') == '>' -%}
        AND p.amount > {{ filter.get('val') }}
      {%- endif -%}
      {%- if filter.get('op') == '>=' -%}
        AND p.amount >= {{ filter.get('val') }}
      {%- endif -%}
      {%- if filter.get('op') == '<' -%}
        AND p.amount < {{ filter.get('val') }}
      {%- endif -%}
      {%- if filter.get('op') == '<=' -%}
        AND p.amount <= {{ filter.get('val') }}
      {%- endif -%}
      {%- if filter.get('op') == '==' -%}
        AND p.amount = {{ filter.get('val') }}
      {%- endif -%}
      {%- if filter.get('op') == 'IN' -%}
        AND p.amount IN {{ filter.get('val') | where_in }}
      {%- endif -%}
    {%- endfor -%}
),

-- Cal the min_amount, max_amount for each org
pipeline_stats AS (
  SELECT
    organization_id,
    MIN(amount) AS min_amount,
    MAX(amount) AS max_amount
  FROM closed_pipeline
  GROUP BY organization_id
),

-- Filter the buckets by both upper_boundary greater than min_amount
-- and lower_boundary less than or equal to max_amount
pipeline_boundary_buckets AS (
  SELECT *
  FROM pipeline_stats ps, amount_boundary ab
  WHERE ab.upper_boundary > ps.min_amount
    AND ab.lower_boundary <= ps.max_amount
),

-- Count the number and total amount of closed pipelines in each bucket
pipeline_bucket_stats AS (
  SELECT
    fbb.organization_id,
    fbb.lower_boundary AS bucket_range_start,
    fbb.upper_boundary AS bucket_range_end,
    COUNT(cp.id) AS pipeline_count,
    ROUND(COALESCE(SUM(cp.amount), 0), 2) AS pipeline_value
  FROM pipeline_boundary_buckets AS fbb
  LEFT JOIN closed_pipeline cp
    ON cp.organization_id = fbb.organization_id
    AND cp.amount >= fbb.lower_boundary
    AND cp.amount < fbb.upper_boundary
  GROUP BY
    fbb.organization_id,
    fbb.lower_boundary,
    fbb.upper_boundary
  ORDER BY
    fbb.organization_id,
    fbb.lower_boundary,
    fbb.upper_boundary
)

SELECT * FROM pipeline_bucket_stats
