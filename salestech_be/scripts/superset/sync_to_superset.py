import argparse
import async<PERSON>
import os
from typing import Any

import httpx

from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)

DEV_SUPERSET_DATABASE_ID = 6
DEV_SUPERSET_BASE_URL = "https://analytics-dev.reevo.ai/api/v1"

PROD_SUPERSET_DATABASE_ID = 67
PROD_SUPERSET_BASE_URL = "https://analytics.reevo.ai/api/v1"


class SupersetSync:
    def __init__(
        self,
        env: str,
        username: str,
        password: str,
    ):
        self.env = env
        self.username = username
        self.password = password
        self.auth_token: str | None = None
        if self.env == "prod":
            self.database_id = PROD_SUPERSET_DATABASE_ID
            self.base_url = PROD_SUPERSET_BASE_URL
        else:
            self.database_id = DEV_SUPERSET_DATABASE_ID
            self.base_url = DEV_SUPERSET_BASE_URL
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=30,
        )

    async def get_auth_token(
        self,
    ) -> str | None:
        """
        Get authentication token from Superset API.

        Returns:
            str: Authentication token
        """
        response = await self.client.post(
            "/security/login",
            json={
                "password": self.password,
                "username": self.username,
                "provider": "db",
                "refresh": True,
            },
        )
        result = response.json()
        if not result or "access_token" not in result:
            return None
        return str(result.get("access_token"))

    async def get_dataset_id(
        self,
        table_name: str,
    ) -> int | None:
        headers = {
            "Authorization": f"Bearer {self.auth_token}",
        }

        payload = {"database_id": self.database_id, "table_name": table_name}

        response = await self.client.post(
            "/dataset/get_or_create/", headers=headers, json=payload
        )
        data = response.json()
        result = data.get("result")
        if not result or "table_id" not in result:
            return None
        return int(result.get("table_id"))

    async def get_dataset(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        dataset_id: int,
    ) -> dict[Any, Any]:
        headers = {
            "Authorization": f"Bearer {self.auth_token}",
        }

        response = await self.client.get(f"/dataset/{dataset_id}", headers=headers)
        return dict(response.json())

    async def create_dataset(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        database_id: int,
        sql_query: str,
        table_name: str,
    ) -> dict[Any, Any]:
        headers = {
            "Authorization": f"Bearer {self.auth_token}",
        }

        payload = {
            "database": database_id,
            "sql": sql_query,
            "table_name": table_name,
        }

        response = await self.client.post("/dataset/", headers=headers, json=payload)
        return dict(response.json())

    async def update_dataset(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        dataset_id: int,
        sql_query: str,
    ) -> dict[Any, Any]:
        headers = {
            "Authorization": f"Bearer {self.auth_token}",
        }

        payload = {"sql": sql_query}
        response = await self.client.put(
            f"/dataset/{dataset_id}", headers=headers, json=payload
        )
        return dict(response.json())

    def read_dataset_files(
        self,
    ) -> dict[str, str]:
        # read dataset folder all files then get all files name and all files contents
        dataset_dir = os.path.join(os.path.dirname(__file__), "dataset")
        dataset_files = {}

        try:
            # Get all .sql files from the datasets directory
            for filename in os.listdir(dataset_dir):
                if filename.endswith(".sql"):
                    file_path = os.path.join(dataset_dir, filename)
                    with open(file_path) as f:
                        # Store filename without .sql extension as key and content as value
                        dataset_files[filename[:-4]] = f.read()

            logger.info(f"Found {len(dataset_files)} dataset files")
            return dataset_files
        except FileNotFoundError:
            logger.error(f"Dataset directory not found at {dataset_dir}")
            raise
        except Exception as e:
            logger.error(f"Error reading dataset files: {e!s}")
            raise

    async def login(
        self,
    ) -> None:
        self.auth_token = await self.get_auth_token()
        if self.auth_token:
            logger.info(f"Login to Superset {self.env} success")
        else:
            logger.error(f"Login to Superset {self.env} failed")

    async def upload(
        self,
    ) -> None:
        logger.info("Starting sync dataset to superset")
        await self.login()
        dataset_files = self.read_dataset_files()
        if not self.auth_token:
            logger.error("Failed to get auth token")
            return

        for table_name, sql_query in dataset_files.items():
            # get dataset_id via table_name
            dataset_id = await self.get_dataset_id(
                table_name=table_name,
            )
            logger.info(f"Dataset ID for {table_name}: {dataset_id}")

            if dataset_id:
                await self.update_dataset(
                    dataset_id=dataset_id,
                    sql_query=sql_query,
                )
            else:
                await self.create_dataset(
                    database_id=self.database_id,
                    sql_query=sql_query,
                    table_name=table_name,
                )
        logger.info("Sync dataset to superset completed")


async def main() -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-e",
        "--env",
        type=str,
        choices=[
            "dev",
            "prod",
        ],
        default="dev",
        help="Specify the environment to use",
    )
    parser.add_argument(
        "-u",
        "--username",
        type=str,
        required=True,
        help="Specify the username to use",
    )
    parser.add_argument(
        "-p",
        "--password",
        type=str,
        required=True,
        help="Specify the password to use",
    )
    parser.add_argument(
        "-f",
        "--function",
        type=str,
        choices=[
            "login",
            "upload",
        ],
        default="upload",
        help="Specify the function to use",
    )
    args = parser.parse_args()

    superset_sync = SupersetSync(
        env=args.env,
        username=args.username,
        password=args.password,
    )

    if args.function == "login":
        await superset_sync.login()
    elif args.function == "upload":
        await superset_sync.upload()
    else:
        logger.error(f"Invalid function: {args.function}")
        parser.print_help()


if __name__ == "__main__":
    asyncio.run(main())
