from enum import StrEnum

DEFAULT_TASK_QUEUE = "default-queue"
MEETING_TASK_QUEUE = "meeting-task-queue"
AI_TASK_QUEUE = "ai-task-queue"
DEFAULT_TASK_MAX_RETRY = 3
CRM_SYNC_TASK_QUEUE = "crm-sync-task-queue"
VOICE_TASK_QUEUE = "voice-task-queue"
DOMAIN_CRM_ASSOCIATION_TASK_QUEUE = "domain-crm-association-task-queue"
INTEGRITY_JOB_TASK_QUEUE = "integrity-job-task-queue"


class TemporalTaskQueue(StrEnum):
    EMAIL_TASK_QUEUE = "email-task-queue"
    EMAIL_IMAP_TASK_QUEUE = "email-imap-task-queue"
    CALENDAR_TASK_QUEUE = "calendar-task-queue"
    NOTIFICATION_TASK_QUEUE = "notification-task-queue"
    PROSPECTING_TASK_QUEUE = "prospecting-task-queue"
    SEQUENCE_TASK_QUEUE = "sequence-task-queue"
    DOMAIN_CRM_ASSOCIATION_TASK_QUEUE = "domain-crm-association-task-queue"
    FALKOR_TASK_QUEUE = "falkor-task-queue"


##########################################
# Temporal Task Queues - Research Domain #
##########################################
class ResearchTaskQueue(StrEnum):
    # Default
    RESEARCH_TASK_QUEUE = "research-task-queue"

    # Low Priority
    RESEARCH_TASK_QUEUE_LOW = "research-task-queue-low"

    # Throttled - for activities involving with external API calls
    RESEARCH_TASK_QUEUE_THROTTLED = "research-task-queue-throttled"

    # Low Priority Throttled - for activities involving with external API calls
    RESEARCH_TASK_QUEUE_LOW_THROTTLED = "research-task-queue-low-throttled"
