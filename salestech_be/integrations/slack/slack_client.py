from enum import Str<PERSON><PERSON>
from http import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import override
from urllib.parse import urlenco<PERSON>

from httpx import AsyncClient, Response
from pydantic import BaseModel

from salestech_be.common.exception import ErrorDetails
from salestech_be.integrations.abstract_async_rest_client_v3 import (
    AbstractAsyncRestClientV3,
    ResponseModelT,
)
from salestech_be.ree_logging import get_logger


class SlackMessagePayload(BaseModel):
    """Slack message payload model."""

    channel: str
    username: str = "SalesTech Bot"
    text: str
    icon_emoji: str = ":robot_face:"


class SlackResponse(BaseModel):
    """Slack API response model."""

    ok: bool


class SlackWebhookUrls(StrEnum):
    CRM_IMPORT_STATUS = "*********************************************************************************"
    DOMAIN_PURCHASE_INVOICE = "*********************************************************************************"


class SlackChannels(StrEnum):
    DOMAIN_PURCHASE_INVOICE = "#domain-purchase-invoices"
    CRM_IMPORT_STATUS = "#crm-import-status"


class SlackClient(AbstractAsyncRestClientV3):
    """Slack Webhook client for sending messages to Slack."""

    def __init__(self, webhook_url: str) -> None:
        """Initialize the Slack client.

        Args:
            webhook_url: The webhook URL for the Slack channel
        """
        self.webhook_url = webhook_url
        self.rest = AsyncClient()
        self.logger = get_logger(__name__)
        # Set metrics name for this client
        self.METRIC_NAME = "slack_client"

    def parse_error(self, response: Response) -> ErrorDetails:
        """Parse error response from Slack API.

        Args:
            response: The HTTP response

        Returns:
            ErrorDetails: Structured error details
        """
        error_message = "Unknown Slack error"
        error_code = "slack_error"

        try:
            error_body = response.json()
            if isinstance(error_body, dict):
                error_message = error_body.get("error", error_message)
                error_code = f"slack_{error_message}".lower().replace(" ", "_")
        except Exception:
            # If we can't parse the JSON, use the text content
            error_message = response.text or error_message

        return ErrorDetails(
            code=error_code,
            details=error_message,
        )

    async def send_message(
        self,
        *,
        env: str,
        channel: str,
        text: str,
        username: str | None = None,
        icon_emoji: str | None = None,
    ) -> None:
        if env not in {"dev", "prod"}:
            return

        """Send a message to a Slack channel.

        Args:
            channel: The channel to send the message to (e.g., "#general")
            text: The message text
            username: Override the default bot username
            icon_emoji: Override the default bot icon (e.g., ":ghost:")

        Returns:
            SlackResponse: Slack API response, or None if an error occurred
        """
        try:
            payload = SlackMessagePayload(
                channel=channel,
                text=f"{text}",
                username=username or "Reevo Bot",
                icon_emoji=icon_emoji or ":robot_face:",
            )

            # Slack webhook expects a form-encoded payload
            form_data = {"payload": payload.model_dump_json()}

            await self.request(
                method=HTTPMethod.POST,
                path=self.webhook_url,
                response_model=SlackResponse,
                content=urlencode(form_data).encode("utf-8"),
                headers={"Content-Type": "application/x-www-form-urlencoded"},
            )
        except Exception as e:
            self.logger.error(
                "Failed to send Slack message",
                exc_info=e,
                channel=channel,
                text_length=len(text),
                username=username,
            )

    @override
    def parse_response(
        self,
        response: Response,
        response_model: type[ResponseModelT],
    ) -> ResponseModelT:
        """Parse response body into model instance"""
        # Special handling for raw 'ok' response
        if response.content == b"ok":
            return SlackResponse(ok=True)  # type: ignore

        # For regular JSON responses, use the parent implementation
        return super().parse_response(response, response_model)
