from typing import Any

from fastapi import status
from requests import Response

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import ErrorDetails
from salestech_be.common.exception.exception import ExternalServiceError
from salestech_be.integrations.abstract_async_rest_client import (
    AbstractAsyncRestClient,
    HeaderModelT,
    ListResponse,
    ResponseModelT,
)
from salestech_be.integrations.infraforge.type import (
    DomainForwardRequest,
    Error,
    ExternalBuyDomainsRequest,
    ExternalBuyDomainsResponse,
    ExternalBuyMailboxesRequest,
    ExternalBuyMailboxesResponse,
    ExternalCheckDomainAvailabilityResponse,
    ExternalCreateWorkspaceRequest,
    ExternalCreateWorkspaceResponse,
    ExternalGenerateAlternativeDomainResponse,
    ExternalGenerateAlternativeDomainsRequest,
    ExternalGenerateMailboxesRequest,
    ExternalGenerateMailboxesResponse,
    ExternalGetDomainDNSResponse,
    ExternalGetDomainsResponse,
    ExternalGetMailboxesResponse,
    ExternalGetWorkspacesResponse,
    ListExternalGenerateAlternativeDomainResponse,
    ListExternalGetDomainsResponse,
    ListExternalGetMailboxesResponse,
    ListExternalGetWorkspacesResponse,
    UpdateDomainDNSRequest,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings


class AsyncInfraForgeClient(AbstractAsyncRestClient):
    base_url = settings.infraforge_api_url
    access_token = settings.infraforge_api_key.get_secret_value()
    logger = get_logger("AsyncInfraForgeClient")

    @property
    def default_headers(self) -> dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return {
            "Authorization": self.access_token,
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

    async def create_workspace(
        self, request: ExternalCreateWorkspaceRequest
    ) -> ExternalCreateWorkspaceResponse:
        response, _ = await self._post(
            uri="workspaces",
            request_body=request.model_dump(),
            response_model=ExternalCreateWorkspaceResponse,
        )
        return response

    async def get_workspaces(self) -> ListResponse[ExternalGetWorkspacesResponse]:
        response, _ = await self._get(
            uri="workspaces",
            response_model=ListExternalGetWorkspacesResponse,
        )
        return ListResponse[ExternalGetWorkspacesResponse](data=response.data)

    async def check_domain_availability(
        self, domain: str
    ) -> ExternalCheckDomainAvailabilityResponse | None:
        try:
            response, _ = await self._get(
                uri=f"check-domain-availability?domain={domain}",
                response_model=ExternalCheckDomainAvailabilityResponse,
            )
        except ExternalServiceError as e:
            self.logger.bind(domain=domain).error(
                "Error checking domain availability", exc_info=e
            )
            return None
        return response

    async def buy_domains(
        self, request: ExternalBuyDomainsRequest
    ) -> ExternalBuyDomainsResponse:
        response, _ = await self._post(
            uri="domains",
            request_body=request.model_dump(),
            response_model=ExternalBuyDomainsResponse,
        )
        return response

    async def get_domains(self) -> ListResponse[ExternalGetDomainsResponse]:
        response, _ = await self._get(
            uri="domains",
            response_model=ListExternalGetDomainsResponse,
        )
        return ListResponse[ExternalGetDomainsResponse](data=response.data)

    async def get_domain_dns(self, domain_id: str) -> ExternalGetDomainDNSResponse:
        response, _ = await self._get(
            uri=f"domains/{domain_id}/dns",
            response_model=ExternalGetDomainDNSResponse,
        )
        return response

    async def put_domain_dns(
        self, domain_id: str, request: UpdateDomainDNSRequest
    ) -> None:
        await self._put(
            uri=f"domains/{domain_id}/dns",
            request_body=request.model_dump(),
            response_model=AbstractAsyncRestClient.ResponseModelNoContent,
        )

    async def generate_alternative_domains(
        self, request: ExternalGenerateAlternativeDomainsRequest
    ) -> ListResponse[ExternalGenerateAlternativeDomainResponse]:
        response, _ = await self._post(
            uri="domains/alternative-domains",
            request_body=request.model_dump(),
            response_model=ListExternalGenerateAlternativeDomainResponse,
        )
        return ListResponse[ExternalGenerateAlternativeDomainResponse](
            data=response.data
        )

    async def get_mailboxes(
        self, workspace_id: str | None = None
    ) -> ListResponse[ExternalGetMailboxesResponse]:
        response, _ = await self._get(
            uri=f"mailboxes?workspace_id={workspace_id}&with_credentials=true",
            response_model=ListExternalGetMailboxesResponse,
        )
        return ListResponse[ExternalGetMailboxesResponse](data=response.data)

    async def get_mailbox_by_id(self, mailbox_id: str) -> ExternalGetMailboxesResponse:
        response, _ = await self._get(
            uri=f"mailboxes/{mailbox_id}",
            response_model=ExternalGetMailboxesResponse,
        )
        return response

    async def buy_mailboxes(
        self, request: ExternalBuyMailboxesRequest
    ) -> ExternalBuyMailboxesResponse:
        response, _ = await self._post(
            uri="mailboxes",
            request_body=request.model_dump(),
            response_model=ExternalBuyMailboxesResponse,
        )
        if not response.mailboxes:
            raise ExternalServiceError(
                additional_error_details=ErrorDetails(
                    error_code=ErrorCode.EMAIL_ACCOUNT_FAILED_TO_PURCHASE_MAILBOX,
                    details="Failed to buy mailbox",
                )
            )
        return response

    async def delete_mailbox(self, mailbox_id: str) -> None:
        """Delete a mailbox by ID."""
        await self._delete(
            uri=f"mailboxes/{mailbox_id}",
        )

    async def generate_mailboxes(
        self, request: ExternalGenerateMailboxesRequest
    ) -> ExternalGenerateMailboxesResponse:
        response, _ = await self._post(
            uri="mailboxes/generate",
            request_body=request.model_dump(),
            response_model=ExternalGenerateMailboxesResponse,
        )
        return response

    async def update_domain_forwarding(self, request: DomainForwardRequest) -> None:
        await self._patch(
            uri="domains/forwards",
            request_body=[request.model_dump()],
            response_model=AbstractAsyncRestClient.ResponseModelNoContent,
        )

    def parse_response(
        self,
        response: Response,
        response_model: type[ResponseModelT],
        header_model: type[HeaderModelT] | None = None,
    ) -> tuple[ResponseModelT, HeaderModelT | None]:
        # Handle NoContent responses
        if response_model is AbstractAsyncRestClient.ResponseModelNoContent:
            if response.status_code != status.HTTP_204_NO_CONTENT:
                self.logger.bind(
                    request=response.request,
                    message=response.content,
                    status_code=response.status_code,
                ).warning(
                    "Content returned but NoContent response model provided. Dev needs to fix."
                )
            return response_model.model_validate({}), None

        # Handle JSON responses
        response_json = response.json()
        if isinstance(response_json, list):
            response_dict = {"data": response_json}
        elif isinstance(response_json, dict):
            response_dict = response_json
        else:
            raise ValueError(f"Invalid response model: {response_model}")
        if header_model:
            return response_model.model_validate(
                response_dict
            ), header_model.model_validate(response.headers)
        return response_model.model_validate(response_dict), None

    def parse_error(self, response: Response) -> ErrorDetails:
        try:
            infraforge_error = Error.model_validate(response.json())
            return ErrorDetails(
                code="infraforge_error",
                details=infraforge_error.message,
                reference_id=f"InfraForge request id {response.json().get('requestId')}",
            )
        except Exception:
            return ErrorDetails(code="infraforge_error", details=str(response.content))
