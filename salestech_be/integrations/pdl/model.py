from enum import IntEnum, StrEnum
from typing import Any, Self

from pydantic import BaseModel, ConfigDict, Field, field_validator, model_validator

from salestech_be.ree_logging import get_logger
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.str import (
    PhoneNumberWithExtension,
    validate_e164_with_extension,
)

PDL_SEARCH_MAX_PAGE_SIZE = 100
logger = get_logger(__name__)


class PeopleDataLabsPersonResponseStatus(IntEnum):
    SUCCESS = 200
    NOT_FOUND = 404
    ERROR = 500


class PeopleDataLabsAutocompleteField(NameValueStrEnum):
    person_title = "person_title"
    company = "company"
    role = "role"
    sub_role = "sub_role"
    country = "country"
    location = "location"
    region = "region"
    industry = "industry"
    skill = "skill"
    school = "school"
    major = "major"


class PeopleDataLabsAutocompleteRequest(BaseModel):
    field: PeopleDataLabsAutocompleteField
    text: str
    size: int = 20
    titlecase: bool = True

    def to_request_body(self) -> dict[str, str | int]:
        field = (
            "title"
            if self.field == PeopleDataLabsAutocompleteField.person_title
            else self.field.value
        )
        return {
            "field": field,
            "text": self.text,
            "size": self.size,
            "titlecase": self.titlecase,
        }


class PeopleDataLabsCompanyMeta(BaseModel):
    id: str
    website: str | None = None
    industry: str | None = None
    linkedin_slug: str | None = None
    location_name: str | None = None
    display_name: str
    display_name_history: list[str] | None = None
    alternative_names: list[str] | None = None


class PeopleDataLabsLocationMeta(BaseModel):
    locality: str | None = None
    region: str | None = None
    country: str | None = None


class PeopleDataLabsAutocompleteItem(BaseModel):
    name: str
    count: int
    meta: PeopleDataLabsCompanyMeta | PeopleDataLabsLocationMeta | None = None


class PdlAutocompleteResponse(BaseModel):
    data: list[PeopleDataLabsAutocompleteItem]
    fields: list[str]
    status: int


class PeopleDataLabsSearchPeopleDataset(NameValueStrEnum):
    all = "all"
    resume = "resume"


class PeopleDataLabsPeopleSearchField(NameValueStrEnum):
    location_name = "location_name"
    job_title = "job_title"
    industry = "industry"
    job_company_name = "job_company_name"
    job_company_size = "job_company_size"
    job_title_levels = "job_title_levels"
    job_company_id = "job_company_id"
    location_country = "location_country"
    location_metro = "location_metro"  # US ONLY
    location_locality = "location_locality"
    location_region = "location_region"
    location_postal_code = "location_postal_code"
    job_title_role = "job_title_role"
    summary = "summary"
    id = "id"


class PeopleDataLabsCompanySearchField(StrEnum):
    company_name = "name"
    location_name = "location.name"
    size = "size"
    industry = "industry"
    inferred_revenue = "inferred_revenue"
    website = "website"
    location_country = "location.country"
    location_metro = "location.metro"  # US ONLY
    location_locality = "location.locality"
    location_region = "location.region"
    location_postal_code = "location.postal_code"
    latest_funding_stage = "latest_funding_stage"
    last_funding_date = "last_funding_date"
    summary = "summary"
    employee_growth_rate_12_month = "employee_growth_rate.12_month"
    id = "id"
    company_url = "website"


class PeopleDataLabsSearchCommonRequest(BaseModel):
    query: str | None = None
    sql: str | None = None
    size: int = PDL_SEARCH_MAX_PAGE_SIZE
    scroll_token: str | None = None
    pretty: bool = True
    titlecase: bool = True

    @model_validator(mode="after")
    def _validate_labels(self) -> Self:
        if bool(self.query) == bool(self.sql):
            raise ValueError("Exactly one of 'query' or 'sql' must be provided.")
        return self


class PeopleDataLabsSearchPeopleRequest(PeopleDataLabsSearchCommonRequest):
    dataset: PeopleDataLabsSearchPeopleDataset = PeopleDataLabsSearchPeopleDataset.all

    @staticmethod
    def from_common_request(
        common_request: PeopleDataLabsSearchCommonRequest,
    ) -> "PeopleDataLabsSearchPeopleRequest":
        return PeopleDataLabsSearchPeopleRequest(
            query=common_request.query,
            sql=common_request.sql,
            size=common_request.size,
            scroll_token=common_request.scroll_token,
            pretty=common_request.pretty,
        )


class PeopleDataLabsJobTitle(BaseModel):
    name: str
    raw: list[str] | None = None
    class_: str | None = Field(None, alias="class")  # class is reserved word
    role: str | None = None
    sub_role: str | None = None
    levels: list[str] | None = None


class PeopleDataLabsLocation(BaseModel):
    name: str | None = None
    locality: str | None = None
    region: str | None = None
    metro: str | None = None
    country: str | None = None
    continent: str | None = None
    street_address: str | None = None
    address_line_2: str | None = None
    postal_code: str | None = None
    geo: str | None = None
    first_seen: str | None = None
    last_seen: str | None = None
    num_sources: int | None = None


class PeopleDataLabsSchool(BaseModel):
    name: str
    type: str | None = None
    id: str | None = None
    location: PeopleDataLabsLocation | None = None
    linkedin_url: str | None = None
    facebook_url: str | None = None
    twitter_url: str | None = None
    linkedin_id: str | None = None
    website: str | None = None
    domain: str | None = None
    raw: list[str] | None = None


class PeopleDataLabsEducation(BaseModel):
    school: PeopleDataLabsSchool | None = None
    degrees: list[str] | None = None
    start_date: str | None = None
    end_date: str | None = None
    majors: list[str] | None = None
    minors: list[str] | None = None
    gpa: float | None = None
    raw: list[str] | None = None
    summary: str | None = None


class PeopleDataLabsProfile(BaseModel):
    network: str
    id: str | None = None
    url: str | None = None
    username: str | None = None
    num_sources: int | None = None
    first_seen: str | None = None
    last_seen: str | None = None


class PeopleDataLabsPersonBase(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Base model for PDL person data common between preview search and full enrichment responses."""

    model_config = ConfigDict(extra="allow")

    raw_data: dict[str, Any] = Field({}, exclude=True)  # type: ignore[explicit-any] # TODO: fix-any-annotation

    id: str
    full_name: str
    sex: str | None = None
    linkedin_url: str | None = None
    industry: str | None = None
    job_title: str | None = None
    job_title_role: str | None = None
    job_title_sub_role: str | None = None
    job_title_levels: list[str] | None = None
    job_company_name: str | None = None
    job_company_website: str | None = None
    location_name: str | None = None

    def __init__(self, **data: dict[str, Any]):  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().__init__(**data)
        self.raw_data = data  # Store the raw response


class PeopleDataLabsPreviewPerson(PeopleDataLabsPersonBase):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Preview version of a person's data from the search endpoint."""

    # Boolean flags indicating data availability
    birth_date: bool = False
    birth_year: bool = False
    countries: bool = False
    education: bool = False
    emails: bool = False
    experience: bool = False
    facebook_id: bool = False
    facebook_url: bool = False
    facebook_username: bool = False
    first_name: bool = False
    github_url: bool = False
    github_username: bool = False
    interests: bool = False
    job_company_12mo_employee_growth_rate: bool = False
    job_company_employee_count: bool = False
    job_company_facebook_url: bool = False
    job_company_founded: bool = False
    job_company_id: bool = False
    job_company_industry: bool = False
    job_company_inferred_revenue: bool = False
    job_company_linkedin_id: bool = False
    job_company_linkedin_url: bool = False
    job_company_location_address_line_2: bool = False
    job_company_location_continent: bool = False
    job_company_location_country: bool = False
    job_company_location_geo: bool = False
    job_company_location_locality: bool = False
    job_company_location_metro: bool = False
    job_company_location_name: bool = False
    job_company_location_postal_code: bool = False
    job_company_location_region: bool = False
    job_company_location_street_address: bool = False
    job_company_size: bool = False
    job_company_total_funding_raised: bool = False
    job_company_twitter_url: bool = False
    job_last_verified: bool = False
    job_start_date: bool = False
    last_initial: bool = False
    last_name: bool = False
    linkedin_id: bool = False
    linkedin_username: bool = False
    location_address_line_2: bool = False
    location_continent: bool = False
    location_country: bool = False
    location_geo: bool = False
    location_last_updated: bool = False
    location_locality: bool = False
    location_metro: bool = False
    location_names: bool = False
    location_postal_code: bool = False
    location_region: bool = False
    location_street_address: bool = False
    middle_initial: bool = False
    middle_name: bool = False
    mobile_phone: bool = False
    personal_emails: bool = False
    phone_numbers: bool = False
    profiles: bool = False
    recommended_personal_email: bool = False
    regions: bool = False
    skills: bool = False
    street_addresses: bool = False
    twitter_url: bool = False
    twitter_username: bool = False
    work_email: bool = False


class PeopleDataLabsEmailType(NameValueStrEnum):
    personal = "personal"
    professional = "professional"
    current_professional = "current_professional"
    disposable = "disposable"


class PeopleDataLabsEmail(BaseModel):
    address: str
    type: PeopleDataLabsEmailType | None = None


class PeopleDataLabsStreetAddress(BaseModel):
    name: str | None = None
    locality: str | None = None
    region: str | None = None
    metro: str | None = None
    country: str | None = None
    continent: str | None = None
    street_address: str | None = None
    address_line_2: str | None = None
    postal_code: str | None
    geo: str | None


class PeopleDataLabsNaics(BaseModel):
    naics_code: str
    sector: str | None = None
    sub_sector: str | None = None
    industry_group: str | None = None
    naics_industry: str | None = None
    national_industry: str | None = None


class PeopleDataLabsSic(BaseModel):
    sic_code: str
    major_group: str | None = None
    industry_group: str | None = None
    industry_sector: str | None = None


class PeopleDataLabsCompany(BaseModel):
    name: str
    display_name: str | None = None
    size: str | None = None
    employee_count: int | None = None
    id: str | None = None
    founded: int | None = None
    industry: str | None = None
    naics: list[PeopleDataLabsNaics] | None = None
    sic: list[PeopleDataLabsSic] | None = None
    location: PeopleDataLabsLocation | None = None
    linkedin_id: str | None = None
    linkedin_url: str | None = None
    linkedin_slug: str | None = None
    facebook_url: str | None = None
    twitter_url: str | None = None
    profiles: list[str] | None = None
    website: str | None = None
    ticker: str | None = None
    mic_exchange: str | None = None
    type: str | None = None
    summary: str | None = None
    tags: list[str] | None = None
    headline: str | None = None
    alternative_names: list[str] | None = None
    alternative_domains: list[str] | None = None
    affiliated_profiles: list[str] | None = None
    total_funding_raised: float | None = None
    latest_funding_stage: str | None = None
    last_funding_date: str | None = None
    number_funding_rounds: int | None = None
    funding_stages: list[str] | None = None
    employee_count_by_country: dict[str, int] | None = None
    dataset_version: str | None = None


class PeopleDataLabsExperience(BaseModel):
    company: PeopleDataLabsCompany | None
    location_names: list[str]
    end_date: str | None = None
    start_date: str | None = None
    title: PeopleDataLabsJobTitle | None = None
    is_primary: bool | None = None
    summary: str | None = None
    num_sources: int | None = None
    first_seen: str | None = None
    last_seen: str | None = None


class PeopleDataLabsPerson(PeopleDataLabsPersonBase):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Full enriched person data from the enrichment endpoint."""

    # Identity fields
    first_name: str | None = None
    middle_initial: str | None = None
    middle_name: str | None = None
    last_initial: str | None = None
    last_name: str | None = None
    birth_date: str | None = None
    birth_year: int | None = None

    # Social profiles
    linkedin_username: str | None = None
    linkedin_id: str | None = None
    facebook_url: str | None = None
    facebook_username: str | None = None
    facebook_id: str | None = None
    twitter_url: str | None = None
    twitter_username: str | None = None
    github_url: str | None = None
    github_username: str | None = None

    # Job details
    job_title_class: str | None = None
    job_company_id: str | None = None
    job_company_size: str | None = None
    job_company_founded: int | None = None
    job_company_industry: str | None = None
    job_company_linkedin_url: str | None = None
    job_company_linkedin_id: str | None = None
    job_company_facebook_url: str | None = None
    job_company_twitter_url: str | None = None

    # Company location
    job_company_location_locality: str | None = None
    job_company_location_metro: str | None = None
    job_company_location_region: str | None = None
    job_company_location_geo: str | None = None
    job_company_location_street_address: str | None = None
    job_company_location_address_line_2: str | None = None
    job_company_location_postal_code: str | None = None
    job_company_location_country: str | None = None
    job_company_location_continent: str | None = None

    # Job timing
    job_last_changed: str | None = None
    job_last_verified: str | None = None
    job_start_date: str | None = None

    # Location details
    location_locality: str | None = None
    location_metro: str | None = None
    location_region: str | None = None
    location_country: str | None = None
    location_continent: str | None = None
    location_street_address: str | None = None
    location_address_line_2: str | None = None
    location_postal_code: str | None = None
    location_geo: str | None = None
    location_last_updated: str | None = None
    location_names: list[str] = []

    # Contact information
    emails: list[PeopleDataLabsEmail] = []
    work_email: str | None = None
    personal_emails: list[str] = []
    mobile_phone: str | None = None
    phone_numbers: list[PhoneNumberWithExtension] = []
    recommended_personal_email: str | None = None

    # Additional information
    interests: list[str] | None = None
    skills: list[str] | None = None
    regions: list[str] = []
    countries: list[str] = []
    street_addresses: list[PeopleDataLabsStreetAddress] = []

    # Detailed records
    experience: list[PeopleDataLabsExperience] = []
    education: list[PeopleDataLabsEducation] = []
    profiles: list[PeopleDataLabsProfile] = []

    @property
    def current_experience(self) -> PeopleDataLabsExperience | None:
        return max(
            (_exp for _exp in self.experience if _exp.is_primary),
            key=lambda _exp: (_exp and _exp.start_date) or "",
            default=None,
        )

    @property
    def current_company(self) -> PeopleDataLabsCompany | None:
        if self.current_experience:
            return self.current_experience.company
        return None

    @field_validator("phone_numbers", mode="before")
    @classmethod
    def validate_phone_numbers(cls, values: list[str] | None) -> list[str]:
        if not values:
            return []
        results: list[str] = []
        for value in values:
            try:
                validate_e164_with_extension(value)
                results.append(value)
            except Exception:
                logger.bind(number=value).warning("Invalid phone number from PDL")
        return results


class PeopleDataLabsPreviewSearchPeopleResponse(BaseModel):
    """Response model for preview search people endpoint"""

    status: int
    data: list[PeopleDataLabsPreviewPerson]
    scroll_token: str | None = None
    total: int
    dataset_version: str


class PeopleDataLabsSearchPeopleResponse(BaseModel):
    status: int
    data: list[PeopleDataLabsPerson]
    scroll_token: str | None = None
    total: int
    dataset_version: str


class PeopleDataLabsBulkEnrichMetadata(BaseModel):
    contact_id: str


class PeopleDataLabsPersonResponse(BaseModel):
    status: int
    error: dict[str, str | list[str]] | None = None

    likelihood: int | None = None
    data: PeopleDataLabsPerson | None = None
    dataset_version: str | None = None
    metadata: PeopleDataLabsBulkEnrichMetadata | None = None


class PeopleDataLabsEnrichPersonRequest(BaseModel):
    pdl_id: str
    required: str = "work_email OR phone_numbers"
    titlecase: bool = True


class PeopleDataLabsBulkEnrichParams(BaseModel):
    pdl_id: str | None = None
    lid: str | None = None  # linkedin id
    email: str | None = None
    profile: list[str] | None = None  # array of linkedin_url
    first_name: str | None = None
    last_name: str | None = None
    name: str | None = None
    country: str | None = None
    birth_date: str | None = None
    school: list[str] | None = None
    postal_code: str | None = None
    region: str | None = None
    company: str | None = None
    locality: str | None = None
    street_address: str | None = None

    @field_validator("profile", mode="before")
    @classmethod
    def normalize_linkedin_urls(cls, value: list[str] | None) -> list[str] | None:
        if not value:
            return value

        normalized_urls = []
        for url in value:
            # Remove common prefixes (http://, https://, www.)
            normalized = (
                url.replace("https://", "").replace("http://", "").replace("www.", "")
            )
            normalized_urls.append(normalized)

        return normalized_urls


class PeopleDataLabsBulkEnrichItem(BaseModel):
    params: PeopleDataLabsBulkEnrichParams
    metadata: PeopleDataLabsBulkEnrichMetadata | None = None


class PeopleDataLabsBulkEnrichPersonRequest(BaseModel):
    required: str = "work_email OR phone_numbers"
    requests: list[PeopleDataLabsBulkEnrichItem]
    titlecase: bool = True

    @model_validator(mode="after")
    def _validate_requests(self) -> Self:
        if not self.requests:
            raise ValueError("requests must be a non-empty list")
        if len(self.requests) > PDL_SEARCH_MAX_PAGE_SIZE:
            raise ValueError(
                f"requests must be less than {PDL_SEARCH_MAX_PAGE_SIZE} items"
            )
        return self


class PeopleDataLabsBulkEnrichResponse(BaseModel):
    list_data: list[PeopleDataLabsPersonResponse] = Field(default_factory=list)

    @classmethod
    def from_response_list(  # type: ignore[explicit-any]
        cls,
        obj: list[Any],
    ) -> Self:
        return cls(
            list_data=[
                PeopleDataLabsPersonResponse.model_validate(item) for item in obj
            ]
        )


class PeopleDataLabsSearchCompanyRequest(PeopleDataLabsSearchCommonRequest):
    @staticmethod
    def from_common_request(
        common_request: PeopleDataLabsSearchCommonRequest,
    ) -> "PeopleDataLabsSearchCompanyRequest":
        return PeopleDataLabsSearchCompanyRequest(
            query=common_request.query,
            sql=common_request.sql,
            size=common_request.size,
            scroll_token=common_request.scroll_token,
            pretty=common_request.pretty,
        )


class PeopleDataLabsSearchCompanyResponse(BaseModel):
    status: int
    data: list[PeopleDataLabsCompany]
    scroll_token: str | None = None
    total: int
    dataset_version: str
