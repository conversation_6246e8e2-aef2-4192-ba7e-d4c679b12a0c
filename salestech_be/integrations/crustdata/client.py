from enum import StrEnum
from http import H<PERSON>PMethod, HTTPStatus

from httpx import AsyncClient, Response

from salestech_be.common.exception.exception import (
    ErrorDetails,
    ExternalServiceError,
    RequestEntityTooLargeError,
)
from salestech_be.common.stats.metric import client_metric
from salestech_be.core.research_agent.models.response import (
    PendingEnrichmentDetails,
    PendingEnrichmentResponse,
)
from salestech_be.core.research_agent.types import IntelProviderTypeEnum
from salestech_be.core.research_agent.utils import sanitize_linkedin_url
from salestech_be.integrations.abstract_async_rest_client_v2 import (
    AbstractAsyncRestClientV2,
)
from salestech_be.integrations.crustdata.model import (
    CrustdataCompanyInfo,
    CrustdataCompanyInfoEnrichmentResponse,
    CrustdataCompanyInfoWithEnrichmentStatusResponse,
    CrustdataLinkedinPostListResponse,
    CrustdataPersonInfo,
    CrustdataPersonInfoEnrichmentResponse,
    CrustdataPersonInfoWithEnrichmentStatusResponse,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

STD_FIELDS = [
    "acquisition_status",
    "all_office_addresses",
    "ceo_location",
    "linkedin_id",
    "largest_headcount_country",
    "linkedin_profile_url",
    "linkedin_logo_url",
    "linkedin_company_description",
    "company_id",
    "company_name",
    "company_twitter_url",
    "company_type",
    "company_website_domain",
    "company_website",
    "competitors",
    "hq_country",
    "headquarters",
    "hq_street_address",
    "estimated_revenue_lower_bound_usd",
    "estimated_revenue_higher_bound_usd",
    "employee_count_range",
    "funding_and_investment",
    "headcount.linkedin_headcount",
    "markets",
    "stock_symbols",
    "taxonomy",
    "year_founded",
]

# avoid requesting unused fields could improve speed significantly
#
# "founders" (too fake, crustdata to fix)
# "decision_makers" (too big, to handle separately)
# "job_openings" (too big)
# "web_traffic",
# "headcount" (headcount history is not needed),
# "glassdoor",
# "g2",
# "seo",
# "linkedin_followers",
# "news_articles",
# "fiscal_year_end" (mostly null),


class CrustdataErrorMessage(StrEnum):
    INVALID_DOMAIN = "Invalid company domains"
    INVALID_LINKEDIN_URL = "Invalid LinkedIn company URLs"
    CRUSTDATA_NOT_FOUND_ERROR_MESSAGE = "Data will be enriched shortly"


class CrustdataClient(AbstractAsyncRestClientV2):
    base_url = settings.crustdata_base_url
    api_key = settings.crustdata_api_key

    MAX_COMPANIES_PER_REQUEST = 25
    MAX_PERSONS_PER_REQUEST = 25
    # 1 minute in seconds. Crustdata is expected to return results within 20 seconds.
    DEFAULT_TIMEOUT = 1 * 60

    def __init__(self) -> None:
        self.logger = get_logger("CrustdataClient")
        self.rest = AsyncClient(
            headers={
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "en-US,en;q=0.9",
                "Authorization": f"Token {self.api_key.get_secret_value()}",
            },
            base_url=self.base_url,
        )
        self.max_retries = 1

    def handle_http_client_error(self, response: Response) -> None:
        text = response.text
        client_metric.increment(
            "crustdata_bad_request_count", tags=[f"code:{response.status_code}"]
        )

        if response.status_code == HTTPStatus.NOT_FOUND:
            # should not raise error for 404s
            self.logger.bind(text=text).warning(
                "crustdata not found, will retry with brightdata",
            )
            return

        if response.status_code == HTTPStatus.TOO_MANY_REQUESTS:
            self.logger.bind(text=text).warning(
                "crustdata fetching returns 429, pending retry...",
            )
            return

        if response.status_code == HTTPStatus.BAD_REQUEST and (
            CrustdataErrorMessage.INVALID_DOMAIN in text
            or CrustdataErrorMessage.INVALID_LINKEDIN_URL in text
        ):
            self.logger.bind(text=text).warning(
                "crustdata invalid domain, will retry with brightdata",
            )
            return

        self.logger.bind(text=text).warning("crustdata bad request")
        raise ExternalServiceError(
            return_http_code=response.status_code,
            additional_error_details=ErrorDetails(
                details=text,
            ),
        )

    # NOTE: company_domain can't contain protocol and slashes
    async def list_linkedin_posts_by_company(
        self,
        company_linkedin_url: str | None = None,
        company_domain: str | None = None,
    ) -> CrustdataLinkedinPostListResponse | PendingEnrichmentResponse:
        if not company_linkedin_url and not company_domain:
            return CrustdataLinkedinPostListResponse(posts=[])

        return await self.list_linkedin_posts(
            company_domain=company_domain,
            company_linkedin_url=company_linkedin_url,
            func_tag="list_linkedin_posts_by_company",
        )

    async def list_linkedin_posts_by_person(
        self, person_linkedin_url: str
    ) -> CrustdataLinkedinPostListResponse | PendingEnrichmentResponse:
        return await self.list_linkedin_posts(
            person_linkedin_url=person_linkedin_url,
            func_tag="list_linkedin_posts_by_person",
        )

    async def list_linkedin_posts(  # noqa: C901
        self,
        company_domain: str | None = None,
        company_linkedin_url: str | None = None,
        person_linkedin_url: str | None = None,
        # temp fix for metrics tagging
        func_tag: str = "list_linkedin_posts",
    ) -> CrustdataLinkedinPostListResponse | PendingEnrichmentResponse:
        self.logger.bind(
            company_domain=company_domain,
            company_linkedin_url=company_linkedin_url,
            person_linkedin_url=person_linkedin_url,
        ).info("crustdata list_linkedin_posts")
        response: Response | None = None

        if not company_domain and not person_linkedin_url and not company_linkedin_url:
            self.logger.bind(
                company_domain=company_domain,
                company_linkedin_url=company_linkedin_url,
                person_linkedin_url=person_linkedin_url,
            ).warning("no company or person linkedin url provided")
            return CrustdataLinkedinPostListResponse(posts=[])

        params = {
            # "page": 1,  # optional, default is 1
        }
        if company_linkedin_url:
            params["company_linkedin_url"] = sanitize_linkedin_url(company_linkedin_url)
        elif company_domain:
            params["company_domain"] = company_domain
        elif person_linkedin_url:
            params["person_linkedin_url"] = person_linkedin_url
        # Each request returns up-to 5 results per page. To paginate, increment the page query param.
        # - Most recent posts will be in first page and then so on.
        # - Currently, you can only fetch only upto 20 pages of latest posts.
        # https://crustdata.notion.site/Crustdata-Discovery-And-Enrichment-API-c66d5236e8ea40df8af114f6d447ab48#675b493db1144c00a31c89e91da7c082
        try:
            response = await self._request(
                function=func_tag,
                method=HTTPMethod.GET,
                path="/screener/linkedin_posts",
                params=params,
                timeout=self.DEFAULT_TIMEOUT,
                handle_client_error=self.handle_http_client_error,
            )
            if response.status_code in [
                HTTPStatus.NOT_FOUND,
                HTTPStatus.TOO_MANY_REQUESTS,
                HTTPStatus.BAD_REQUEST,
            ]:
                return PendingEnrichmentResponse(
                    pending_enrichment=True,
                    provider=IntelProviderTypeEnum.CRUSTDATA,
                    linkedin_url=company_linkedin_url or person_linkedin_url,
                    details=response.text,
                )

            response.raise_for_status()
        except Exception as e:
            raise ExternalServiceError(
                return_http_code=response.status_code
                if response
                else HTTPStatus.INTERNAL_SERVER_ERROR,
            ) from e

        post_response = CrustdataLinkedinPostListResponse(**response.json())
        # Filter out deleted posts by checking URLs
        valid_posts = []
        async with AsyncClient() as client:
            for post in post_response.posts:
                if not post.share_url:
                    continue
                try:
                    resp = await client.head(str(post.share_url), follow_redirects=True)
                    if resp.status_code == HTTPStatus.OK:
                        valid_posts.append(post)
                    else:
                        self.logger.info(
                            f"Post URL {post.share_url} returned status {resp.status_code}"
                        )
                except Exception as e:
                    self.logger.warning(
                        f"Failed to check post URL {post.share_url}: {e}"
                    )
                    continue

        # Check if any valid posts remain
        if not valid_posts:
            self.logger.info("No valid posts found", params=params)
            # raise ExternalServiceError(return_http_code=HTTPStatus.NOT_FOUND)
        post_response.posts = valid_posts

        return post_response

    async def get_person_info_batch_by_linkedin_urls_and_business_emails(
        self,
        person_linkedin_urls: list[str],
        business_emails: list[str],
        # TODO: also support business_email as input
    ) -> CrustdataPersonInfoWithEnrichmentStatusResponse | PendingEnrichmentResponse:
        response: Response

        self.logger.bind(
            person_linkedin_urls=person_linkedin_urls, business_emails=business_emails
        ).info("crustdata get_person_info_batch_by_linkedin_urls_and_business_emails")

        if not person_linkedin_urls and not business_emails:
            self.logger.bind(
                person_linkedin_urls=person_linkedin_urls,
                business_emails=business_emails,
            ).warning("no person linkedin urls or business emails provided")
            return CrustdataPersonInfoWithEnrichmentStatusResponse([])

        if (
            person_linkedin_urls
            and len(person_linkedin_urls) > self.MAX_PERSONS_PER_REQUEST
        ) or (business_emails and len(business_emails) > self.MAX_PERSONS_PER_REQUEST):
            raise RequestEntityTooLargeError(
                f"at most {self.MAX_PERSONS_PER_REQUEST} persons per request"
            )

        params = {}
        if person_linkedin_urls:
            params["linkedin_profile_url"] = ",".join(person_linkedin_urls)
        elif business_emails:
            sanitized_business_emails = list(
                filter(
                    lambda email: email.split("@")[-1]
                    not in settings.research_agent_public_email_domains,
                    business_emails,
                )
            )
            if not sanitized_business_emails:
                # if all business emails are public, return empty results directly
                return CrustdataPersonInfoWithEnrichmentStatusResponse([])

            params["business_email"] = ",".join(sanitized_business_emails)
        else:
            # if missing both, return empty results directly
            return CrustdataPersonInfoWithEnrichmentStatusResponse([])

        try:
            # we can call with multiple linkedin urls, but the response behaves differently:
            #
            # 1. If one of the profile is found, it returns 200 and a list with two types of objects:
            # - CrustdataPersonInfo: person info
            # - CrustdataPersonInfoEnrichmentResponse: the enrichment status of the object
            # 2. If all profiles are not found (one or multiple), it returns 404 and a list with only CrustdataPersonInfoEnrichmentResponse
            #
            # so we need to handle the response differently depending on the status code

            response = await self._request(
                function="get_person_info",
                method=HTTPMethod.GET,
                path="/screener/person/enrich",
                params=params,
                timeout=self.DEFAULT_TIMEOUT,
                handle_client_error=self.handle_http_client_error,
            )

            # if all profiles are not found, don't raise error and return early.
            if response.status_code in [
                HTTPStatus.NOT_FOUND,
                HTTPStatus.TOO_MANY_REQUESTS,
            ]:
                return PendingEnrichmentResponse(
                    pending_enrichment=True,
                    provider=IntelProviderTypeEnum.CRUSTDATA,
                    linkedin_url=person_linkedin_urls[0]
                    if person_linkedin_urls
                    else None,
                )

            response.raise_for_status()
        except Exception as e:
            self.logger.bind(person_linkedin_urls=person_linkedin_urls).error(
                "Exception when getting person info for linkedin url from crustadata",
                exc_info=e,
            )

            raise ExternalServiceError from e

        return CrustdataPersonInfoWithEnrichmentStatusResponse(response.json())

    async def get_person_info_by_linkedin_url_or_email(
        self,
        person_linkedin_url: str | None = None,
        business_email: str | None = None,
    ) -> CrustdataPersonInfo | PendingEnrichmentResponse:
        response = (
            await self.get_person_info_batch_by_linkedin_urls_and_business_emails(
                person_linkedin_urls=[person_linkedin_url]
                if person_linkedin_url
                else [],
                business_emails=[business_email] if business_email else [],
            )
        )

        if isinstance(response, PendingEnrichmentResponse):
            return response

        if len(response.root) == 0:
            return PendingEnrichmentResponse(
                pending_enrichment=True,
                provider=IntelProviderTypeEnum.CRUSTDATA,
            )

        person_info = response.root[0]
        if isinstance(person_info, CrustdataPersonInfoEnrichmentResponse):
            return PendingEnrichmentResponse(
                pending_enrichment=True,
                provider=IntelProviderTypeEnum.CRUSTDATA,
                linkedin_url=person_linkedin_url,
            )

        return person_info

    # by default omitting large fields using STD_FIELDS
    async def get_company_info_batch_by_domains(
        self,
        company_domains: list[str],
        company_linkedin_urls: list[str],
        fields: list[str] | None = None,
        # TODO: also support company_linkedin_url using e.g. pydantic Union instead of just a domain str
    ) -> CrustdataCompanyInfoWithEnrichmentStatusResponse | PendingEnrichmentResponse:
        self.logger.bind(company_domains=company_domains, fields=fields).info(
            "crustdata get_company_info_batch_by_domains"
        )
        fields = fields or STD_FIELDS
        response: Response

        if len(company_domains) > self.MAX_COMPANIES_PER_REQUEST:
            raise RequestEntityTooLargeError(
                f"at most {self.MAX_COMPANIES_PER_REQUEST} companies per request"
            )
        # at most 25 companies, otherwise Crustdata returns (tested): 400 Bad Request
        # `{"non_field_errors":["You can only provide up to 25 values for company_name, company_domain, company_linkedin_url, or company_id"]}`
        # https://crustdata.notion.site/Crustdata-Discovery-And-Enrichment-API-c66d5236e8ea40df8af114f6d447ab48?pvs=25#10ee4a7d95b1807bb965dad5a67086e3
        params = {
            # "enrich_realtime": False,
        }
        if company_linkedin_urls:
            params["company_linkedin_url"] = ",".join(
                sanitize_linkedin_url(url) for url in company_linkedin_urls
            )
        elif company_domains:
            params["company_domain"] = ",".join(company_domains)

        if fields:
            params["fields"] = ",".join(fields)
        try:
            self.logger.bind(company_domains=company_domains).info(
                "requesting company info"
            )
            response = await self._request(
                function="get_company_info",
                method=HTTPMethod.GET,
                path="/screener/company",
                params=params,
                timeout=self.DEFAULT_TIMEOUT,
                handle_client_error=self.handle_http_client_error,
            )

            # 1. missing company info does not return 404, it returns 200 with pending enrichment status
            # so we need to raise error when 404 happens
            # 2. retry if 429
            # 3. retry if response is 400 and contains invalid domain error
            # TODO break retry loop in workflow directly if invalid domains
            # TODO investigate options that can identity invalid domains without sending request to crustdata

            # Crustdata returns 200 for missing company info
            if (
                response.status_code == HTTPStatus.OK
                and PendingEnrichmentDetails.NO_MATCHING_COMPANIES_INFO_FOUND
                in response.text
            ):
                return PendingEnrichmentResponse(
                    pending_enrichment=True,
                    provider=IntelProviderTypeEnum.CRUSTDATA,
                    details=response.text,
                )

            if response.status_code in [
                HTTPStatus.TOO_MANY_REQUESTS,
                HTTPStatus.BAD_REQUEST,
            ]:
                return PendingEnrichmentResponse(
                    pending_enrichment=True,
                    provider=IntelProviderTypeEnum.CRUSTDATA,
                    details=response.text,
                )

            response.raise_for_status()
        except ExternalServiceError as e:
            # See handle_http_client_error for more details on how this error type is handled
            raise e
        except Exception as e:
            self.logger.bind(params=params).error(
                "Error getting company info",
                exc_info=e,
            )
            raise ExternalServiceError from e

        return CrustdataCompanyInfoWithEnrichmentStatusResponse(response.json())

    async def get_company_info_by_domain(
        self,
        company_domain: str,
        fields: list[str] | None = None,
    ) -> CrustdataCompanyInfo | PendingEnrichmentResponse:
        response = await self.get_company_info_batch_by_domains(
            company_domains=[company_domain],
            company_linkedin_urls=[],
            fields=fields,
        )

        # assume the first object is the company info
        if isinstance(response, PendingEnrichmentResponse) or len(response.root) == 0:
            return PendingEnrichmentResponse(
                pending_enrichment=True, provider=IntelProviderTypeEnum.CRUSTDATA
            )

        company_info = response.root[0]
        if isinstance(company_info, CrustdataCompanyInfoEnrichmentResponse):
            return PendingEnrichmentResponse(
                pending_enrichment=True,
                provider=IntelProviderTypeEnum.CRUSTDATA,
            )

        return company_info

    async def get_company_info_by_linkedin_url(
        self,
        company_linkedin_url: str,
        fields: list[str] | None = None,
    ) -> CrustdataCompanyInfo | PendingEnrichmentResponse:
        response = await self.get_company_info_batch_by_domains(
            company_domains=[],
            company_linkedin_urls=[company_linkedin_url],
            fields=fields,
        )

        if isinstance(response, PendingEnrichmentResponse) or len(response.root) == 0:
            return PendingEnrichmentResponse(
                pending_enrichment=True, provider=IntelProviderTypeEnum.CRUSTDATA
            )

        company_info = response.root[0]
        if isinstance(company_info, CrustdataCompanyInfoEnrichmentResponse):
            return PendingEnrichmentResponse(
                pending_enrichment=True,
                provider=IntelProviderTypeEnum.CRUSTDATA,
            )

        return company_info
