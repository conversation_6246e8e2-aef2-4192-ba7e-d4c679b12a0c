import time
from enum import StrEnum
from typing import Any

from salestech_be.common.events import (
    EnrichedCDCEventProcessor,
)
from salestech_be.common.stats.metric import custom_metric
from salestech_be.core.account.service.account_query_service import (
    get_account_query_service,
)
from salestech_be.core.account.service.account_service import get_account_service
from salestech_be.core.account.service.event_processor import AccountCDCEventProcessor
from salestech_be.core.contact.service.contact_query_service import (
    get_contact_query_service,
)
from salestech_be.core.contact.service.contact_service import get_contact_service
from salestech_be.core.contact.service.event_processor import ContactCDCEventProcessor
from salestech_be.core.crm_ai_rec.crm_ai_rec_service import get_crm_ai_rec_service
from salestech_be.core.crm_sync.processor.event_processor import (
    ExternalSyncCDCEventProcessor,
)
from salestech_be.core.crm_sync.service.external_sync_service import (
    get_external_sync_service,
)
from salestech_be.core.custom_object.service.event_processor import (
    CustomObjectCDCEventProcessor,
)
from salestech_be.core.custom_object.service.metadata_cache import <PERSON>ada<PERSON><PERSON>ache
from salestech_be.core.email.event.email_event_cdc_event_processor import (
    EmailEventCDCEventProcessor,
)
from salestech_be.core.email.global_email.global_thread_query_service import (
    get_global_thread_query_service,
)
from salestech_be.core.email.service.global_thread_cdc_event_processor import (
    GlobalThreadCDCEventProcessor,
)
from salestech_be.core.email.warmup.event_processor import (
    get_email_account_warmup_campaign_cdc_event_processor_by_db_engine,
)
from salestech_be.core.logical_propagation.service.trigger import (
    get_logical_propagation_trigger_service,
)
from salestech_be.core.meeting.meeting_bot_service import meeting_bot_service_general
from salestech_be.core.meeting.meeting_service import meeting_service_factory_general
from salestech_be.core.meeting.service.event_processor import MeetingCDCEventProcessor
from salestech_be.core.meeting.service.meeting_query_service import (
    get_meeting_query_service,
)
from salestech_be.core.pipeline.service.event_processor import PipelineCDCEventProcessor
from salestech_be.core.pipeline.service.pipeline_query_service import (
    get_pipeline_query_service,
)
from salestech_be.core.pipeline.service.pipeline_service import get_pipeline_service
from salestech_be.core.sequence.service.sequence_enrollment_callback_service import (
    get_sequence_enrollment_callback_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_enrollment_event_processor import (
    SequenceEnrollmentCDCEventProcessor,
)
from salestech_be.core.sequence.service.sequence_enrollment_query_service import (
    get_sequence_enrollment_query_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    get_sequence_enrollment_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_event_processor import (
    SequenceCDCEventProcessor,
)
from salestech_be.core.sequence.service.sequence_execution_query_service import (
    get_sequence_execution_query_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_execution_service import (
    get_sequence_execution_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_query_service import (
    get_sequence_query_service_by_db,
)
from salestech_be.core.task.service.task_event_processor import TaskCDCEventProcessor
from salestech_be.core.task.service.task_query_service import (
    get_task_query_service_from_engine,
)
from salestech_be.core.task.service.task_v2_service import get_task_v2_service_general
from salestech_be.core.user.service.event_processor import (
    UserOrganizationAssociationEventProcessor,
)
from salestech_be.core.user.service.user_service import get_user_service_general
from salestech_be.core.workflow.service.workflow_trigger_service import (
    get_workflow_trigger_service_by_db_engine,
)
from salestech_be.db.dao.sequence_enrollment_repo import SequenceEnrollmentRepository
from salestech_be.db.dao.sequence_repository import (
    SequenceStepRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.event.types import EventProcessor
from salestech_be.integrations.kafka.kafka_manager import KafkaMessage, MSKConsumer
from salestech_be.integrations.kafka.types import CDCEvent, EnrichedCDCEvent
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

logger = get_logger(__name__)


class CDCEventProcessorFactory:
    def __init__(self, db_engine: DatabaseEngine):
        self.contact_processor = ContactCDCEventProcessor(
            contact_service=get_contact_service(
                db_engine=db_engine,
            ),
            contact_query_service=get_contact_query_service(db_engine),
            trigger_event_service=get_workflow_trigger_service_by_db_engine(db_engine),
            logical_propagation_trigger_service=get_logical_propagation_trigger_service(
                db_engine=db_engine
            ),
            task_v2_service=get_task_v2_service_general(db_engine),
            meeting_service=meeting_service_factory_general(db_engine),
        )

        self.account_processor = AccountCDCEventProcessor(
            account_service=get_account_service(
                db_engine=db_engine,
            ),
            logical_propagation_trigger_service=get_logical_propagation_trigger_service(
                db_engine=db_engine
            ),
            account_query_service=get_account_query_service(
                db_engine=db_engine,
            ),
        )
        self.meeting_processor = MeetingCDCEventProcessor(
            meeting_query_service=get_meeting_query_service(
                db_engine=db_engine,
            ),
            meeting_bot_service=meeting_bot_service_general(db_engine),
            crm_ai_rec_service=get_crm_ai_rec_service(db_engine=db_engine),
        )
        self.pipeline_processor = PipelineCDCEventProcessor(
            pipeline_service=get_pipeline_service(db_engine=db_engine),
            pipeline_query_service=get_pipeline_query_service(db_engine),
            logical_propagation_trigger_service=get_logical_propagation_trigger_service(
                db_engine=db_engine
            ),
            task_v2_service=get_task_v2_service_general(db_engine),
        )

        self.external_sync_processor = ExternalSyncCDCEventProcessor(
            external_sync_service=get_external_sync_service(db_engine=db_engine)
        )
        self.user_organization_association_processor = (
            UserOrganizationAssociationEventProcessor(
                user_service=get_user_service_general(db_engine)
            )
        )

        # Initialize metadata cache once and reuse
        self.metadata_cache = MetadataCache(engine=db_engine)

        self.custom_object_processor = CustomObjectCDCEventProcessor(
            metadata_cache=self.metadata_cache
        )
        self.global_thread_processor = GlobalThreadCDCEventProcessor(
            global_thread_query_service=get_global_thread_query_service(
                db_engine=db_engine
            ),
        )

        # TODO: (hao) make a separate consumer group for sequence related?
        self.sequence_enrollment_processor = SequenceEnrollmentCDCEventProcessor(
            sequence_enrollment_service=get_sequence_enrollment_service_by_db_engine(
                db_engine=db_engine
            ),
            sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
                db_engine=db_engine
            ),
            sequence_query_service=get_sequence_query_service_by_db(
                db_engine=db_engine
            ),
            sequence_enrollment_repository=SequenceEnrollmentRepository(
                engine=db_engine
            ),
            sequence_step_repository=SequenceStepRepository(engine=db_engine),
            sequence_execution_service=get_sequence_execution_service_by_db_engine(
                db_engine=db_engine
            ),
            sequence_execution_query_service=get_sequence_execution_query_service_by_db_engine(
                db_engine=db_engine
            ),
        )
        self.sequence_processor = SequenceCDCEventProcessor(
            sequence_query_service=get_sequence_query_service_by_db(db_engine=db_engine)
        )

        self.email_event_processor = EmailEventCDCEventProcessor(db_engine=db_engine)
        self.task_event_processor = TaskCDCEventProcessor(
            task_query_service=get_task_query_service_from_engine(db_engine=db_engine),
            task_service=get_task_v2_service_general(db_engine=db_engine),
            sequence_enrollment_callback_service=get_sequence_enrollment_callback_service_by_db_engine(
                db_engine=db_engine
            ),
        )
        self.email_account_warm_up_campaign_processor = (
            get_email_account_warmup_campaign_cdc_event_processor_by_db_engine(
                db_engine=db_engine
            )
        )

    def get_processor(  # type: ignore[explicit-any] # TODO: fix-any-annotation #noqa: C901,PLR0911,PLR0912
        self, event: CDCEvent
    ) -> EnrichedCDCEventProcessor[Any, Any] | EmailEventCDCEventProcessor:
        match event.view_model:
            case "contact_view" | "contact_view_v2":
                return self.contact_processor
            case "account_view":
                return self.account_processor
            case "meeting_view":
                return self.meeting_processor
            case "pipeline_view":
                return self.pipeline_processor
            case "user_organization_association_view":
                return self.user_organization_association_processor
            case "custom_object_data_view":
                return self.custom_object_processor
            case "global_thread_view":
                return self.global_thread_processor
            case "sequence_enrollment_view":
                return self.sequence_enrollment_processor
            case "sequence_view":
                return self.sequence_processor
            case "email_event_view":
                return self.email_event_processor
            case "task_view":
                return self.task_event_processor
            case "email_account_warm_up_campaign_view":
                return self.email_account_warm_up_campaign_processor
            case _:
                raise ValueError(f"Unsupported view model: {event.view_model}")

    def get_external_sync_processor(
        self, event: CDCEvent
    ) -> ExternalSyncCDCEventProcessor | None:
        if event.view_model in [
            "contact_view",
            "contact_view_v2",
            "account_view",
            "task_view",
            "meeting_view",
            "pipeline_view",
            "user_organization_association_view",
        ]:
            return self.external_sync_processor
        return None


class CDCEventProcessorMetrics(StrEnum):
    MESSAGE_COUNT = "cdc_event_processor_message_count"
    MESSAGE_TIME = "cdc_event_processor_message_time"


class CDCEventProcessorMetricTags(StrEnum):
    VIEW_MODEL = "view_model"
    IS_SUCCESS = "is_success"
    ERROR_TYPE = "error_type"


class CDCEventPayloadError(ValueError):
    """Raised when CDC event payload is invalid or cannot be parsed"""


class CDCEventProcessor(EventProcessor):
    def __init__(
        self,
        consumer: MSKConsumer[CDCEvent],
        delay_seconds_after_processing: int,
        processor_factory: CDCEventProcessorFactory,
    ) -> None:
        super().__init__(delay_seconds_after_processing=delay_seconds_after_processing)
        self.consumer = consumer
        self.processor_factory = processor_factory

    def _send_metrics(self, tag_list: list[str], elapsed_time: float) -> None:
        custom_metric.increment(
            metric_name=CDCEventProcessorMetrics.MESSAGE_COUNT,
            tags=tag_list,
        )
        custom_metric.timing(
            metric_name=CDCEventProcessorMetrics.MESSAGE_TIME,
            value=elapsed_time,
            tags=tag_list,
        )

    async def _handle_error(
        self,
        e: Exception,
        message: KafkaMessage[CDCEvent],
        cdc_event: CDCEvent | None,
        start_time: float,
        should_commit: bool = True,
    ) -> None:
        error_type = type(e).__name__.lower()
        logger.bind(payload=message.payload, error=str(e)).error(
            f"Error processing CDC event: {error_type}",
            exc_info=e,
        )

        if should_commit:
            await self.consumer.commit(message)

        self._send_metrics(
            tag_list=[
                f"{CDCEventProcessorMetricTags.VIEW_MODEL}:{cdc_event.view_model if cdc_event else 'unknown'}",
                f"{CDCEventProcessorMetricTags.IS_SUCCESS}:False",
                f"{CDCEventProcessorMetricTags.ERROR_TYPE}:{error_type}",
            ],
            elapsed_time=(time.perf_counter() - start_time) * 1000,
        )

    async def process(self) -> None:  # noqa: C901
        """Process CDC events."""
        logger.info("Process CDC event handler")
        try:
            async for message in self.consumer.events():
                start_time = time.perf_counter()
                cdc_event = None
                try:
                    cdc_event = message.payload
                    if not cdc_event or not isinstance(cdc_event, CDCEvent):
                        logger.bind(payload=message.payload).error(
                            "Failed to parse Kafka message"
                        )
                        raise CDCEventPayloadError("invalid_cdc_event_payload")

                    logger.bind(payload=cdc_event).info("Processing CDC event")

                    if (
                        hasattr(cdc_event.after, "organization_id")
                        and str(cdc_event.after.organization_id)
                        in settings.organization_ids_without_cdc_event_proc
                    ):
                        logger.bind(payload=cdc_event).info(
                            "Skipping CDC event due to test organization"
                        )
                        await self.consumer.commit(message)
                        continue

                    try:
                        processor = self.processor_factory.get_processor(cdc_event)
                    except ValueError as e:
                        logger.bind(
                            view_model=cdc_event.view_model,
                            payload=cdc_event,
                            error=str(e),
                        ).warning(
                            "Unsupported view model, skipping event processing",
                            exc_info=e,
                        )
                        raise CDCEventPayloadError("unsupported_view_model")

                    try:
                        event = EnrichedCDCEvent.from_cdc_event(cdc_event)
                    except Exception as e:
                        logger.bind(
                            view_model=cdc_event.view_model,
                            payload=cdc_event,
                            error=str(e),
                        ).warning(
                            "Failed to parse CDC event into EnrichedCDCEvent",
                            exc_info=e,
                        )
                        raise CDCEventPayloadError("parse_cdc_event_payload_error")

                    custom_metric.timing(
                        metric_name="cdc_event_processor_event_parsing",
                        value=(time.perf_counter() - start_time) * 1000,
                        tags=[
                            f"{CDCEventProcessorMetricTags.VIEW_MODEL}:{cdc_event.view_model}",
                        ],
                    )

                    # Process with domain processor first
                    try:
                        await processor.process(event)
                    except Exception as e:
                        logger.exception(
                            "Domain processor event processing failed",
                            exc_info=e,
                        )
                        raise

                    # Then process with external sync if applicable
                    if (
                        external_sync_processor
                        := self.processor_factory.get_external_sync_processor(cdc_event)
                    ):
                        process_start_time = time.perf_counter()
                        await external_sync_processor.process_cdc_event(
                            cdc_event
                        )  # Use original CDCEvent
                        custom_metric.timing(
                            metric_name="cdc_event_processor_external_sync",
                            value=(time.perf_counter() - process_start_time) * 1000,
                            tags=[
                                f"{CDCEventProcessorMetricTags.VIEW_MODEL}:{cdc_event.view_model}",
                            ],
                        )

                    commit_start_time = time.perf_counter()
                    await self.consumer.commit(message)
                    custom_metric.timing(
                        metric_name="cdc_event_processor_commit",
                        value=(time.perf_counter() - commit_start_time) * 1000,
                        tags=[
                            f"{CDCEventProcessorMetricTags.VIEW_MODEL}:{cdc_event.view_model}",
                        ],
                    )

                    self._send_metrics(
                        tag_list=[
                            f"{CDCEventProcessorMetricTags.VIEW_MODEL}:{cdc_event.view_model if cdc_event else 'unknown'}",
                            f"{CDCEventProcessorMetricTags.IS_SUCCESS}:True",
                        ],
                        elapsed_time=(time.perf_counter() - start_time) * 1000,
                    )
                except CDCEventPayloadError as e:
                    await self._handle_error(
                        e=e,
                        message=message,
                        cdc_event=cdc_event,
                        start_time=start_time,
                        should_commit=True,
                    )
                except Exception as e:
                    await self._handle_error(
                        e=e,
                        message=message,
                        cdc_event=cdc_event,
                        start_time=start_time,
                        should_commit=False,
                    )
        except Exception as e:
            logger.bind(error=str(e)).error("Error processing CDC events", exc_info=e)
            raise
        finally:
            logger.info("Finished processing CDC event")

    async def close_resources(self) -> None:
        if self.consumer:
            await self.consumer.close()
