import re
from datetime import datetime
from decimal import ROUND_HALF_UP, <PERSON><PERSON><PERSON>
from typing import Any, TypeVar
from uuid import UUID

import pytz
from dateutil import parser
from pydantic_extra_types.timezone_name import TimeZoneName

from salestech_be.common.type.patch_request import is_unset
from salestech_be.db.models.core.base import (
    TableModel,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

AnyT = TypeVar("AnyT", bound=Any)  # type: ignore[explicit-any] # TODO: fix-any-annotation

TableModelT = TypeVar("TableModelT", bound=TableModel)


def not_none(any_value: AnyT | None) -> AnyT:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    if any_value is None:
        raise ValueError("expected value to present")
    return any_value


def count_not_none(*values: Any | None) -> int:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    return sum(0 if v is None else 1 for v in values)


def count_specified_or_none(*values: Any | None) -> int:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    return sum(0 if is_unset(v) else 1 for v in values)


def one_row_only(values: list[AnyT]) -> AnyT:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    if len(values) != 1:
        raise ValueError(f"expected exactly one row, got {len(values)}")
    return values[0]


def one_row_or_none(values: list[AnyT]) -> AnyT | None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    if len(values) > 1:
        raise ValueError(f"expected at most one row, got {len(values)}")
    return values[0] if values else None


def get_earliest_db_object(objs: list[TableModelT]) -> TableModelT:
    if len(objs) > 0 and hasattr(objs[0], "created_at"):
        return sorted(
            objs,
            key=lambda obj: obj.created_at,  # type: ignore
        )[0]
    raise AttributeError("object does not have created_at attribute")


def get_latest_db_object(objs: list[TableModelT]) -> TableModelT:
    if len(objs) > 0 and hasattr(objs[0], "created_at"):
        return sorted(
            objs,
            key=lambda obj: obj.created_at,  # type: ignore
            reverse=True,
        )[0]
    raise AttributeError("object does not have created_at attribute")


def cast_or_error(val: Any, tp: type[AnyT]) -> AnyT:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    if not isinstance(val, tp):
        raise TypeError(f"expected {tp} instance, got {type(val)}")
    return val


def cast_uuid_or_none(val: Any | None) -> UUID | None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    if not val:
        return None
    if isinstance(val, UUID):
        return val
    return UUID(val)


def eq_str_value(left: str | None, right: str | None) -> bool:
    if not left or not right:
        return False
    return left.strip().lower() == right.strip().lower()


def cast_datetime_or_none(
    val: str | datetime | None,
    local_timezone: TimeZoneName | None,
) -> datetime | None:
    """Convert a string to a timezone-aware datetime object.

    Args:
        val: The string value to convert to datetime
        local_timezone: The timezone name to use if the datetime is naive (default: UTC)

    Returns:
        A timezone-aware datetime object or None if conversion fails
    """
    if not val:
        return None

    # Get the timezone object
    timezone = pytz.timezone(local_timezone if local_timezone else TimeZoneName("UTC"))

    # If val is already a datetime, make sure it has timezone
    if isinstance(val, datetime):
        return val if val.tzinfo else timezone.localize(val)

    # Try a list of common datetime formats
    datetime_formats = [
        "%Y-%m-%d %H:%M",  # 2023-01-01 12:30
        "%Y-%m-%d %H:%M:%S",  # 2023-01-01 12:30:45
        "%Y-%m-%d %H:%M:%S.%f",  # 2023-01-01 12:30:45.123456
        "%Y-%m-%d",  # 2023-01-01
        "%Y/%m/%d %H:%M",  # 2023/01/01 12:30
        "%Y/%m/%d %H:%M:%S",  # 2023/01/01 12:30:45
        "%Y/%m/%d",  # 2023/01/01
        "%m/%d/%Y %H:%M",  # 01/01/2023 12:30
        "%m/%d/%Y %H:%M:%S",  # 01/01/2023 12:30:45
        "%m/%d/%Y",  # 01/01/2023
        "%d/%m/%Y %H:%M",  # 01/01/2023 12:30
        "%d/%m/%Y %H:%M:%S",  # 01/01/2023 12:30:45
        "%d/%m/%Y",  # 01/01/2023
        "%b %d %Y %H:%M",  # Jan 01 2023 12:30
        "%b %d %Y %H:%M:%S",  # Jan 01 2023 12:30:45
        "%b %d %Y",  # Jan 01 2023
        "%B %d %Y %H:%M",  # January 01 2023 12:30
        "%B %d %Y %H:%M:%S",  # January 01 2023 12:30:45
        "%B %d %Y",  # January 01 2023
        "%b %d, %Y %H:%M",  # Jan 01, 2023 12:30
        "%b %d, %Y %H:%M:%S",  # Jan 01, 2023 12:30:45
        "%b %d, %Y",  # Jan 01, 2023
        "%B %d, %Y %H:%M",  # January 01, 2023 12:30
        "%B %d, %Y %H:%M:%S",  # January 01, 2023 12:30:45
        "%B %d, %Y",  # January 01, 2023
        "%d %b %Y %H:%M",  # 01 Jan 2023 12:30
        "%d %b %Y %H:%M:%S",  # 01 Jan 2023 12:30:45
        "%d %b %Y",  # 01 Jan 2023
        "%d %B %Y %H:%M",  # 01 January 2023 12:30
        "%d %B %Y %H:%M:%S",  # 01 January 2023 12:30:45
        "%d %B %Y",  # 01 January 2023
        "%Y-%m-%dT%H:%M",  # 2023-01-01T12:30
        "%Y-%m-%dT%H:%M:%S",  # 2023-01-01T12:30:45
        "%Y-%m-%dT%H:%M:%S.%f",  # 2023-01-01T12:30:45.123456
    ]

    # Try each format
    for fmt in datetime_formats:
        try:
            dt = datetime.strptime(val, fmt)  # noqa: DTZ007
            return timezone.localize(dt)
        except (ValueError, TypeError):
            continue

    # If standard formats fail, try using dateutil parser
    try:
        dt = parser.parse(val)
        # If the parsed datetime is naive (no timezone), add the specified timezone
        if dt.tzinfo is None:
            return timezone.localize(dt)
        # If it already has timezone, return as is
        return dt
    except (ValueError, TypeError):
        pass

    # If all parsing attempts fail, return None
    return None


def cast_decimal_or_none(
    val: str | Decimal | None, precision: int = 2
) -> Decimal | None:
    if not val:
        return None
    try:
        decimal_val = Decimal(val)
        rounded_val = decimal_val.quantize(
            Decimal(10) ** -precision, rounding=ROUND_HALF_UP
        )
        return rounded_val.normalize()
    except Exception:
        return None


def cast_int_or_none(val: str | None) -> int | None:
    if not val:
        return None
    try:
        float_number = float(val)
        return int(float_number)
    except Exception:
        return None


def cast_str_or_none(val: Any | None) -> str | None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    if val is None:
        return None
    try:
        return str(val)
    except Exception:
        return None


def strip_str_or_none(val: str | None) -> str | None:
    if not val:
        return None
    return val.strip()


def none_or_split(val: str | None) -> list[str] | None:
    if not val:
        return None
    # Count delimiters
    comma_count = val.count(",")
    semicolon_count = val.count(";")

    # Choose the delimiter that occurs more frequently; if equal, default to comma.
    delimiter = "," if comma_count >= semicolon_count else ";"
    # Split on the determined delimiter, trim each part, and filter out empties.
    return [item.strip() for item in val.split(delimiter) if item.strip()]


def extract_website_address(text: str | None) -> str | None:
    if not text:
        return None
    # Define the regex pattern for URLs
    pattern = r"https?://[^\s]+"

    # Search for the URL in the text
    match = re.search(pattern, text)

    # Return the URL if found, else return None
    return match.group(0) if match else None


def uuid_or_error(val: Any) -> UUID:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    if isinstance(val, UUID):
        return val
    try:
        return UUID(val)
    except Exception:
        raise ValueError(f"expected UUID, got {type(val)}")
