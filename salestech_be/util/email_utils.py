def format_email_username_as_display_name(
    email_username: str,
) -> str:
    """
    Transform email username into a more user-friendly display name.
    Only parses usernames with at most 1 delimiter to avoid complex cases.

    Examples:
    - tian.zhang -> <PERSON><PERSON> <PERSON>
    - kev.ye -> <PERSON><PERSON> <PERSON>
    - kev<PERSON>.y -> <PERSON>.
    - t.zhang -> <PERSON><PERSON> <PERSON>
    - john -> <PERSON>
    - tian.b.zhang -> tian.b.zhang
    """
    if not email_username:
        return email_username

    # Normalize underscores to dots for counting
    normalized = email_username.replace("_", ".")

    # Only parse if there's at most 1 delimiter
    if normalized.count(".") > 1:
        return email_username

    # Split by dots and underscores
    parts = normalized.split(".")

    formatted_parts = []
    for part in parts:
        if len(part) == 1:
            # Single letter gets capitalized with period
            formatted_parts.append(f"{part.upper()}.")
        else:
            # Multi-letter parts get title cased
            formatted_parts.append(part.capitalize())

    return " ".join(formatted_parts)


def generate_first_and_last_name(full_name: str | None) -> tuple[str, str]:
    if not full_name:
        return "", ""
    names = full_name.split(" ")
    if len(names) == 1:
        return names[0], ""
    return names[0], " ".join(names[1:])
