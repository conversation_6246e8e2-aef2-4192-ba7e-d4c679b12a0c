from typing import Annotated
from uuid import UUID

from salestech_be.common.schema_manager.std_object_field_identifier import (
    ProspectingFilterFieldOptionsFacetField,
    ProspectingFilterFieldOptionsField,
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    BooleanCheckboxFieldProperty,
    DefaultEnumFieldProperty,
    NumericFieldProperty,
    TextFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.schema import SkipDescriptor
from salestech_be.core.common.types import (
    DomainModel,
    FieldMetadata,
)
from salestech_be.db.dto.prospecting_dto import (
    SAMPLE_VALUES,
    FilterFieldValue,
)
from salestech_be.db.models.prospecting_tag import (
    ProspectingFilterFieldDateType,
    ProspectingFilterFieldType,
)
from salestech_be.db.models.search_query import ProspectingSearchQueryType
from salestech_be.integrations.pdl.model import (
    PeopleDataLabsAutocompleteField,
    PeopleDataLabsCompanySearchField,
    PeopleDataLabsPeopleSearchField,
)


class FilterFieldOptionsFacetV2(DomainModel):
    """Prospecting filter field"""

    object_id = StdObjectIdentifiers.prospecting_filter_field_options_facet.identifier
    field_name_provider = ProspectingFilterFieldOptionsFacetField
    object_display_name = "ProspectingFilterFieldOptionsFacet"

    filter_field_type: Annotated[
        ProspectingFilterFieldType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=ProspectingFilterFieldType,
                field_display_name="Filter Field Type",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    is_searchable: Annotated[
        bool,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Is Searchable",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = False

    values: Annotated[list[FilterFieldValue], SkipDescriptor()]

    prospecting_search_query_type: Annotated[
        ProspectingSearchQueryType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=ProspectingSearchQueryType,
                field_display_name="Prospecting Search Query Type",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    value_format_list: Annotated[
        list[str] | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Value Format List",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    data_type: Annotated[
        ProspectingFilterFieldDateType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=ProspectingFilterFieldDateType,
                field_display_name="Data Type",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    @classmethod
    def get_sample_values_by_pdl(
        cls,
        filter_field_type: ProspectingFilterFieldType,
        prospecting_search_query_type: ProspectingSearchQueryType,
    ) -> list[FilterFieldValue]:
        # Get the sample values from the SAMPLE_VALUES dictionary
        search_type_mapping = SAMPLE_VALUES.get(prospecting_search_query_type, {})
        return search_type_mapping.get(filter_field_type, [])

    @classmethod
    def to_pdl_autocomplete_field(
        cls,
        filter_field_type: ProspectingFilterFieldType,
    ) -> PeopleDataLabsAutocompleteField:
        mapping = {
            ProspectingFilterFieldType.location: PeopleDataLabsAutocompleteField.location,
            ProspectingFilterFieldType.person_title: PeopleDataLabsAutocompleteField.person_title,
            ProspectingFilterFieldType.linkedin_industry: PeopleDataLabsAutocompleteField.industry,
            ProspectingFilterFieldType.company_name: PeopleDataLabsAutocompleteField.company,
            ProspectingFilterFieldType.country: PeopleDataLabsAutocompleteField.country,
            ProspectingFilterFieldType.region: PeopleDataLabsAutocompleteField.region,
        }

        field = mapping.get(filter_field_type)
        if not field:
            raise ValueError(
                f"No PDL autocomplete field found for filter field type: {filter_field_type}"
            )
        return field

    @classmethod
    def to_pdl_search_field(
        cls,
        filter_field_type: ProspectingFilterFieldType,
        search_query_type: ProspectingSearchQueryType,
    ) -> PeopleDataLabsPeopleSearchField | PeopleDataLabsCompanySearchField:
        mapping: dict[
            ProspectingSearchQueryType,
            dict[
                ProspectingFilterFieldType,
                PeopleDataLabsPeopleSearchField | PeopleDataLabsCompanySearchField,
            ],
        ] = {
            ProspectingSearchQueryType.PEOPLE: {
                ProspectingFilterFieldType.location: PeopleDataLabsPeopleSearchField.location_name,
                ProspectingFilterFieldType.person_title: PeopleDataLabsPeopleSearchField.job_title,
                ProspectingFilterFieldType.linkedin_industry: PeopleDataLabsPeopleSearchField.industry,
                ProspectingFilterFieldType.company_name: PeopleDataLabsPeopleSearchField.job_company_name,
                ProspectingFilterFieldType.number_of_employees: PeopleDataLabsPeopleSearchField.job_company_size,
                ProspectingFilterFieldType.person_seniorities: PeopleDataLabsPeopleSearchField.job_title_levels,
                ProspectingFilterFieldType.company_ids: PeopleDataLabsPeopleSearchField.job_company_id,
                ProspectingFilterFieldType.country: PeopleDataLabsPeopleSearchField.location_country,
                ProspectingFilterFieldType.state: PeopleDataLabsPeopleSearchField.location_metro,
                ProspectingFilterFieldType.city: PeopleDataLabsPeopleSearchField.location_locality,
                ProspectingFilterFieldType.region: PeopleDataLabsPeopleSearchField.location_region,
                ProspectingFilterFieldType.postal_code: PeopleDataLabsPeopleSearchField.location_postal_code,
                ProspectingFilterFieldType.job_title_role: PeopleDataLabsPeopleSearchField.job_title_role,
                ProspectingFilterFieldType.technology: PeopleDataLabsPeopleSearchField.summary,
                ProspectingFilterFieldType.person_id: PeopleDataLabsPeopleSearchField.id,
            },
            ProspectingSearchQueryType.COMPANY: {
                ProspectingFilterFieldType.location: PeopleDataLabsCompanySearchField.location_name,
                ProspectingFilterFieldType.company_name: PeopleDataLabsCompanySearchField.company_name,
                ProspectingFilterFieldType.number_of_employees: PeopleDataLabsCompanySearchField.size,
                ProspectingFilterFieldType.linkedin_industry: PeopleDataLabsCompanySearchField.industry,
                ProspectingFilterFieldType.inferred_revenue: PeopleDataLabsCompanySearchField.inferred_revenue,
                ProspectingFilterFieldType.website: PeopleDataLabsCompanySearchField.website,
                ProspectingFilterFieldType.country: PeopleDataLabsCompanySearchField.location_country,
                ProspectingFilterFieldType.state: PeopleDataLabsCompanySearchField.location_metro,
                ProspectingFilterFieldType.city: PeopleDataLabsCompanySearchField.location_locality,
                ProspectingFilterFieldType.region: PeopleDataLabsCompanySearchField.location_region,
                ProspectingFilterFieldType.postal_code: PeopleDataLabsCompanySearchField.location_postal_code,
                ProspectingFilterFieldType.latest_funding_stage: PeopleDataLabsCompanySearchField.latest_funding_stage,
                ProspectingFilterFieldType.latest_funding_date: PeopleDataLabsCompanySearchField.last_funding_date,
                ProspectingFilterFieldType.technology: PeopleDataLabsCompanySearchField.summary,
                ProspectingFilterFieldType.employee_growth_rate_12_month: PeopleDataLabsCompanySearchField.employee_growth_rate_12_month,
                ProspectingFilterFieldType.company_id: PeopleDataLabsCompanySearchField.id,
                ProspectingFilterFieldType.company_url: PeopleDataLabsCompanySearchField.website,
            },
        }

        search_type_mapping = mapping.get(search_query_type)
        if not search_type_mapping:
            raise ValueError(
                f"No PDL search mapping found for search query type: {search_query_type}"
            )

        field = search_type_mapping.get(filter_field_type)
        if not field:
            raise ValueError(
                f"No PDL search field found for filter field type: {filter_field_type}"
            )
        return field


class FilterFieldOptionsV2(DomainModel):
    """Prospecting filter field option"""

    object_id = StdObjectIdentifiers.prospecting_filter_field_options.identifier
    field_name_provider = ProspectingFilterFieldOptionsField
    object_display_name = "ProspectingFilterFieldOptions"

    id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    filter_field_type: Annotated[
        ProspectingFilterFieldType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=ProspectingFilterFieldType,
                field_display_name="Filter Field Type",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    display_name: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Display Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    value: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Value",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    category: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Category",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    count: Annotated[
        int,
        FieldMetadata(
            type_property=NumericFieldProperty(
                field_display_name="Count",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]
