import dataclasses
from collections import OrderedDict, defaultdict
from collections.abc import Mapping
from decimal import Decimal
from typing import Annotated, cast
from uuid import UUID

from fastapi import Depends
from frozendict import frozendict
from pydantic import EmailStr

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ErrorDetails,
    IllegalStateError,
    ResourceNotFoundError,
)
from salestech_be.common.query_util.baseline_filter_extraction import BaselineFilters
from salestech_be.common.query_util.domain_fetch_hints import DomainFetchHints
from salestech_be.common.query_util.filter_schema import ValueFilter
from salestech_be.common.query_util.sort_schema import (
    NonRelationalSorter,
)
from salestech_be.common.results import Cursor
from salestech_be.common.schema_manager.std_object_field_identifier import (
    ContactField,
    SelectListValueField,
    StdSelectListIdentifier,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.contact import ContactChannelType
from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.common.type.metadata.schema import <PERSON><PERSON>ield
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.common.type.pipeline import PipelineIdentifiers
from salestech_be.core.common.domain_fetching_hint import to_sql_selection_spec
from salestech_be.core.common.domain_service import (
    DomainQueryService,
)
from salestech_be.core.contact.converter import (
    ContactSelectListValueContainer,
    contact_v2_from_db,
)
from salestech_be.core.contact.fetch_config import contact_domain_db_mapping
from salestech_be.core.contact.types_v2 import (
    ContactAccountIdPair,
    ContactAISuggestedDisplayName,
    ContactAISuggestedLinkedinUrl,
    ContactAISuggestedTitle,
    ContactSearchResult,
    ContactV2,
)
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.job.service.job_service import JobService
from salestech_be.core.metadata.converter import (
    contact_account_email_from_db,
    contact_account_role_from_db,
    contact_phone_number_from_db,
)
from salestech_be.core.metadata.dto.select_list_dto import SelectListDto
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
    get_select_list_service,
)
from salestech_be.core.metadata.types import ContactAccountRole, ContactPhoneNumber
from salestech_be.core.research_agent.research_agent_service import (
    ResearchAgentService,
    get_research_agent_service,
)
from salestech_be.core.research_agent.types import DBContactAISuggestedValues
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.address_repository import AddressRepository
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.custom_object_repository import CustomObjectRepository
from salestech_be.db.dao.generic_repository import SelectionSpec
from salestech_be.db.dao.job_repository import JobRepository
from salestech_be.db.dao.person_repository import PersonRepository
from salestech_be.db.dao.pipeline_repository import PipelineRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.contact_email_dto import ContactEmailDto
from salestech_be.db.dto.contact_phone_number_dto import ContactPhoneNumberDto
from salestech_be.db.dto.custom_object_data_dto import ExtensionCustomObjectDataGroupDto
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.contact import Contact as DbContact
from salestech_be.db.models.contact_account_association import ContactAccountAssociation
from salestech_be.db.models.contact_email import (
    ContactEmail,
    ContactEmailAccountAssociation,
)
from salestech_be.db.models.contact_phone_number import (
    ContactPhoneNumber as DbContactPhoneNumber,
)
from salestech_be.db.models.contact_phone_number import (
    ContactPhoneNumberAccountAssociation,
)
from salestech_be.db.models.core.utils import (
    ComparableSingularValueType,
    SortRankMappingProvider,
    SqlComparator,
    SqlSelection,
    SqlSortRankMapping,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.copy_util import strict_model_copy
from salestech_be.util.validation import one_row_or_none

logger = get_logger()

_contact_stage_id_field_path: tuple[str, ...] = (ContactField.stage_id,)
_contact_stage_nested_id_path: tuple[str, ...] = (
    ContactField.stage,
    SelectListValueField.id,
)
_contact_stage_nested_rank_path: tuple[str, ...] = (
    ContactField.stage,
    SelectListValueField.rank,
)


def _is_filter_for_stage_id(filter_: ValueFilter) -> bool:
    return filter_.is_for_field(_contact_stage_id_field_path) or filter_.is_for_field(
        _contact_stage_nested_id_path
    )


def _is_sorter_for_stage_id(sorter: NonRelationalSorter) -> bool:
    return (
        sorter.is_for_field(_contact_stage_id_field_path)
        or sorter.is_for_field(_contact_stage_nested_id_path)
        or sorter.is_for_field(_contact_stage_nested_rank_path)
    )


def _expand_stage_id_filter(
    filter_: ValueFilter, stage_sl_dto: SelectListDto
) -> list[ValueFilter]:
    values_to_expand: set[UUID] = set()
    if isinstance(filter_.value, UUID):
        values_to_expand.add(filter_.value)
    elif isinstance(filter_.value, set) and all(
        isinstance(v, UUID) for v in filter_.value
    ):
        values_to_expand.update(cast(set[UUID], filter_.value))
    else:
        raise ValueError(f"Unexpected filter value type: {type(filter_.value)}")
    expanded_values: set[UUID] = set()
    for value in values_to_expand:
        expand_to_values = stage_sl_dto.get_equivalent_value_ids_for_sql_selection(
            slv_id=value
        )
        expanded_values.update(expand_to_values)
    return (
        [strict_model_copy(filter_, value=expanded_values)]
        if isinstance(filter_.value, set)
        else [strict_model_copy(filter_, value=value) for value in expanded_values]
    )


def _replace_stage_id_sorter(
    sorter: NonRelationalSorter,
) -> NonRelationalSorter:
    return strict_model_copy(
        sorter,
        field=QualifiedField(path=_contact_stage_id_field_path),
    )


def _transform_stage_id_sort_ranking_provider(
    *, stage_sl_dto: SelectListDto
) -> SortRankMappingProvider:
    ranking_map = stage_sl_dto.get_equivalent_value_id_ranking_map()

    def sort_rank_mapping_provider(
        field_path: tuple[str, ...],
    ) -> SqlSortRankMapping | None:
        if field_path in (_contact_stage_id_field_path, _contact_stage_nested_id_path):
            return SqlSortRankMapping(
                rank_map=cast(
                    Mapping[ComparableSingularValueType, int | Decimal], ranking_map
                ),
                rank_default=None,
            )
        return None

    return sort_rank_mapping_provider


class ContactQueryService(DomainQueryService[ContactV2]):
    def __init__(
        self,
        contact_repository: Annotated[ContactRepository, Depends()],
        account_repository: Annotated[AccountRepository, Depends()],
        address_repository: Annotated[AddressRepository, Depends()],
        custom_object_service: Annotated[CustomObjectService, Depends()],
        job_service: Annotated[JobService, Depends()],
        person_repository: Annotated[PersonRepository, Depends()],
        select_list_service: Annotated[InternalSelectListService, Depends()],
        pipeline_repository: Annotated[PipelineRepository, Depends()],
        feature_flag_service: Annotated[
            FeatureFlagService, Depends(get_feature_flag_service)
        ],
        research_agent_service: Annotated[ResearchAgentService, Depends()],
    ):
        super().__init__(feature_flag_service=feature_flag_service)
        self.contact_repository = contact_repository
        self.account_repository = account_repository
        self.address_repository = address_repository
        self.custom_object_service = custom_object_service
        self.job_service = job_service
        self.person_repository = person_repository
        self.logger = get_logger()
        self.select_list_service = select_list_service
        self.pipeline_repository = pipeline_repository
        self.research_agent_service = research_agent_service

    async def _list_full_contacts_v2(
        self,
        db_contacts: list[DbContact],
        organization_id: UUID,
        include_custom_object: bool | None = False,
        contextual_account_id: UUID | None = None,
    ) -> list[ContactV2]:
        """List full ContactsV2 with their related data."""
        if not db_contacts:
            return []

        contact_ids = {contact.id for contact in db_contacts}

        # Only fetch if any contacts have stage_id
        contacts_with_stage = [c for c in db_contacts if c.stage_id]
        std_sl_dtos_by_slv_id: Mapping[UUID, SelectListDto] = (
            await self.select_list_service.map_standard_select_list_dtos_by_value_id(
                organization_id=organization_id,
                standard_select_list_ids={StdSelectListIdentifier.contact_stage},
                exclude_deleted=True,
            )
            if contacts_with_stage
            else frozendict()
        )

        contact_account_associations_by_contact_id: Mapping[
            UUID, list[ContactAccountAssociation]
        ] = await self.contact_repository.map_active_contact_account_association_by_contact_ids(
            organization_id=organization_id,
            contact_ids=contact_ids,
        )

        custom_object_map: ExtensionCustomObjectDataGroupDto | None = None
        if include_custom_object:
            custom_object_map = await self.custom_object_service.map_custom_object_data_by_extension_ids_if_custom_object_exists(
                organization_id=organization_id,
                parent_object_name=ExtendableStandardObject.contact,
                extension_ids=contact_ids,
            )
        # Get all address IDs and fetch them in bulk
        address_ids = {
            contact.address_id
            for contact in db_contacts
            if contact.address_id is not None
        }
        address_map = (
            await self.address_repository.map_addresses_by_ids(
                organization_id=organization_id,
                ids=list(address_ids),
            )
            if address_ids
            else {}
        )
        contact_emails_by_contact_id = cast(
            frozendict[UUID, list[ContactEmail]],
            await self.contact_repository.map_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_ids=contact_ids,
            ),
        )
        contact_email_account_associations_by_contact_id = cast(
            frozendict[UUID, list[ContactEmailAccountAssociation]],
            await self.contact_repository.map_contact_channel_info_account_association_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_ids=contact_ids,
            ),
        )
        contact_phone_numbers_by_contact_id = cast(
            frozendict[UUID, list[DbContactPhoneNumber]],
            await self.contact_repository.map_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.PHONE_NUMBER,
                organization_id=organization_id,
                contact_ids=contact_ids,
            ),
        )

        ai_suggested_values_by_contact_ids: dict[UUID, DBContactAISuggestedValues] = {}
        if settings.enable_contact_research_enrichment:
            try:
                ai_suggested_values_by_contact_ids = await self.research_agent_service.map_ai_suggested_values_by_contact_ids(
                    contact_ids=contact_ids,
                )
            except Exception as e:
                self.logger.bind(error=e).warning("Error getting ai suggested values")

        result: list[ContactV2] = []
        for db_contact in db_contacts:
            contact_select_list_value_container: ContactSelectListValueContainer
            if db_contact.stage_id:
                if db_contact.stage_id not in std_sl_dtos_by_slv_id:
                    self.logger.error(
                        "stage_id not found in std_sl_dtos_by_slv_id",
                        stage_id=db_contact.stage_id,
                        contact_id=db_contact.id,
                    )
                    raise IllegalStateError(
                        f"stage_id {db_contact.stage_id} not found in std_sl_dtos_by_slv_id"
                    )
                stage_dto = std_sl_dtos_by_slv_id[db_contact.stage_id]
                contact_select_list_value_container = ContactSelectListValueContainer(
                    stage=stage_dto.get_effective_value(slv_id=db_contact.stage_id)
                )

            contact_emails = contact_emails_by_contact_id.get(db_contact.id) or []
            primary_contact_email = one_row_or_none(
                [
                    contact_email
                    for contact_email in contact_emails
                    if contact_email.is_contact_primary
                ]
            )
            contact_account_associations = (
                contact_account_associations_by_contact_id.get(db_contact.id)
            )
            contact_email_account_associations = (
                contact_email_account_associations_by_contact_id.get(db_contact.id)
            )
            ai_suggested_values = ai_suggested_values_by_contact_ids.get(db_contact.id)
            ai_suggested_linkedin_url: ContactAISuggestedLinkedinUrl | None = None
            ai_suggested_display_name: ContactAISuggestedDisplayName | None = None
            ai_suggested_title: ContactAISuggestedTitle | None = None
            try:
                if ai_suggested_values:
                    ai_suggested_linkedin_url = (
                        ContactAISuggestedLinkedinUrl.from_db_ai_suggested_values(
                            ai_suggested_values
                        )
                    )
                    ai_suggested_display_name = (
                        ContactAISuggestedDisplayName.from_db_ai_suggested_values(
                            ai_suggested_values
                        )
                    )
                    ai_suggested_title = (
                        ContactAISuggestedTitle.from_db_ai_suggested_values(
                            ai_suggested_values
                        )
                    )
            except Exception as e:
                self.logger.bind(error=e, contact_id=db_contact.id).warning(
                    "Error getting ai suggested values"
                )
            result.append(
                contact_v2_from_db(
                    db_contact=db_contact,
                    db_address=address_map.get(db_contact.address_id)
                    if db_contact.address_id
                    else None,
                    contact_emails=await self._sort_contact_emails_by_relevant_order(
                        contact_emails=contact_emails,
                        contact_account_associations=contact_account_associations,
                        contact_email_account_associations=contact_email_account_associations,
                        account_id=contextual_account_id,
                    ),
                    contact_phone_numbers=contact_phone_numbers_by_contact_id.get(
                        db_contact.id
                    )
                    or [],
                    extension_custom_object_data_group_dto=custom_object_map,
                    contact_select_list_value_container=contact_select_list_value_container,
                    contact_account_associations=contact_account_associations,
                    contact_email_account_associations=contact_email_account_associations,
                    primary_contact_email=primary_contact_email.email
                    if primary_contact_email
                    else None,
                    ai_suggested_linkedin_url=ai_suggested_linkedin_url,
                    ai_suggested_display_name=ai_suggested_display_name,
                    ai_suggested_title=ai_suggested_title,
                ),
            )
        return result

    async def list_contact_v2_untenanted_by_contact_ids(
        self, *, contact_ids: set[UUID]
    ) -> list[ContactV2]:
        if not contact_ids:
            return []
        contacts = await self.contact_repository.list_contacts_by_ids_untenanted(
            ids=list(contact_ids),
            exclude_deleted_or_archived=True,
        )
        organization_contact_map: dict[UUID, list[Contact]] = defaultdict(list)
        for contact in contacts:
            organization_contact_map[contact.organization_id].append(contact)

        res: list[ContactV2] = []
        for organization_id, contacts in organization_contact_map.items():
            contacts_v2 = await self._list_full_contacts_v2(
                organization_id=organization_id,
                db_contacts=contacts,
            )
            res.extend(contacts_v2)
        return res

    async def get_count_of_all_contacts_in_organization(
        self,
        organization_id: UUID,
        exclude_archived: bool = True,
    ) -> int:
        return await self.contact_repository.count_by_column_values(
            DbContact,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_archived,
        )

    async def get_count_of_all_contact_account_associations_in_organization(
        self,
        organization_id: UUID,
        exclude_archived: bool = True,
    ) -> int:
        return await self.contact_repository.count_by_column_values(
            ContactAccountAssociation,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_archived,
        )

    async def list_contacts_v2(
        self,
        *,
        organization_id: UUID,
        only_include_contact_ids: UnsetAware[set[UUID]] = UNSET,
        include_custom_object: bool | None = False,
        domain_fetch_hints: UnsetAware[DomainFetchHints | None] = UNSET,
        contextual_account_id: UUID | None = None,
    ) -> list[ContactV2]:
        if specified(only_include_contact_ids) and not only_include_contact_ids:
            return []

        if specified(domain_fetch_hints):
            selection_spec: SelectionSpec = await self._to_selection_spec(
                organization_id=organization_id,
                domain_fetch_hints=domain_fetch_hints,
            )
            if specified(only_include_contact_ids) and only_include_contact_ids:
                selection_spec = selection_spec._replace(
                    must=(
                        (
                            SqlSelection(
                                column_name="id",
                                sql_comparator=SqlComparator.IN,
                                comparable_value=list(only_include_contact_ids),
                            ),
                            *selection_spec.must,
                        )
                    )
                )
            db_contacts: list[
                DbContact
            ] = await self.contact_repository.list_by_selection_spec(
                table_model=DbContact,
                organization_id=organization_id,
                selection_spec=selection_spec,
            )
        else:
            db_contacts = (
                await self.contact_repository.list_by_ids(
                    contact_ids=list(only_include_contact_ids),
                    organization_id=organization_id,
                )
                if specified(only_include_contact_ids)
                else await self.contact_repository.list_all(
                    organization_id=organization_id,
                    exclude_archived=True,
                    exclude_locked_by_integrity_jobs=False,
                )
            )

        return await self._list_full_contacts_v2(
            db_contacts=db_contacts,
            organization_id=organization_id,
            include_custom_object=include_custom_object,
            contextual_account_id=contextual_account_id,
        )

    async def list_contact_account_roles_by_association_ids(
        self,
        organization_id: UUID,
        association_ids: list[UUID],
    ) -> list[ContactAccountRole]:
        return await self.contact_repository.find_contact_account_roles_map_by_association_ids(
            organization_id=organization_id,
            association_ids=association_ids,
        )

    async def list_contact_account_roles_by_organization_id(
        self,
        organization_id: UUID,
    ) -> list[ContactAccountRole]:
        return await self.contact_repository.find_contact_account_roles_map_by_organization_id(
            organization_id=organization_id,
        )

    async def list_contact_account_roles_by_organization_id_in_batches(
        self,
        organization_id: UUID,
        offset: int = 0,
        limit: int = 50,
    ) -> list[ContactAccountRole]:
        """List contact account roles with offset-based pagination."""
        contact_account_roles_list = await self.contact_repository.find_contact_account_roles_map_by_organization_id(
            organization_id=organization_id,
        )

        # Apply offset/limit pagination
        if limit:
            return contact_account_roles_list[offset : offset + limit]
        return contact_account_roles_list[offset:]

    async def list_contact_account_roles_by_contact_id(
        self,
        organization_id: UUID,
        contact_ids: set[UUID] | None = None,
        account_ids: set[UUID] | None = None,
    ) -> list[ContactAccountRole]:
        if not contact_ids and not account_ids:
            logger.warning(
                "No account_ids and no contact_ids, skipping contact account role lookup"
            )
            return []
        contact_account_roles_map = (
            await self.find_contact_account_roles_map_by_contact_or_account_id(
                organization_id=organization_id,
                contact_ids=contact_ids,
                account_ids=account_ids,
            )
        )
        all_contact_account_roles: list[ContactAccountRole] = []
        for contact_account_roles in contact_account_roles_map.values():
            all_contact_account_roles.extend(contact_account_roles)
        return all_contact_account_roles

    async def find_contact_account_roles_map_by_contact_or_account_id(
        self,
        organization_id: UUID,
        contact_ids: set[UUID] | None = None,
        account_ids: set[UUID] | None = None,
    ) -> dict[UUID, list[ContactAccountRole]]:
        if (not contact_ids and not account_ids) or (contact_ids and account_ids):
            raise ValueError("only one of contact_ids or account_ids may be specified.")

        contact_account_associations_map_by_contact_id: Mapping[
            UUID, list[ContactAccountAssociation]
        ] = defaultdict(list)
        if contact_ids:
            contact_account_associations_map_by_contact_id = await self.contact_repository.map_active_contact_account_association_by_contact_ids(
                organization_id=organization_id,
                contact_ids=contact_ids,
            )
        elif account_ids:
            contact_account_associations_map_by_account_id: Mapping[
                UUID, list[ContactAccountAssociation]
            ] = await self.contact_repository.map_active_contact_account_association_by_account_ids(
                organization_id=organization_id,
                account_ids=account_ids,
            )
            for (
                contact_account_associations
            ) in contact_account_associations_map_by_account_id.values():
                for contact_account_association in contact_account_associations:
                    contact_account_associations_map_by_contact_id[
                        contact_account_association.contact_id
                    ].append(contact_account_association)
            contact_ids = set(contact_account_associations_map_by_contact_id.keys())

        contact_ids = contact_ids or set()
        contact_emails_by_contact_id = cast(
            frozendict[UUID, list[ContactEmail]],
            await self.contact_repository.map_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_ids=contact_ids,
            ),
        )
        contact_email_account_associations_by_contact_id = cast(
            frozendict[UUID, list[ContactEmailAccountAssociation]],
            await self.contact_repository.map_contact_channel_info_account_association_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_ids=contact_ids,
            ),
        )

        contact_account_roles_map: dict[UUID, list[ContactAccountRole]] = defaultdict(
            list
        )
        for contact_id in contact_ids:
            contact_account_associations = (
                contact_account_associations_map_by_contact_id.get(contact_id) or []
            )
            contact_emails = contact_emails_by_contact_id.get(contact_id) or []
            contact_email_account_associations = (
                contact_email_account_associations_by_contact_id.get(contact_id) or []
            )

            contact_email_map: dict[UUID, EmailStrLower] = {
                contact_email.id: contact_email.email
                for contact_email in contact_emails
            }

            contact_account_map: dict[UUID, ContactAccountRole] = defaultdict(None)
            for contact_account_association in contact_account_associations or []:
                contact_account_map[contact_account_association.account_id] = (
                    contact_account_role_from_db(
                        contact_account_association=contact_account_association
                    )
                )
            for eaa in contact_email_account_associations or []:
                email = contact_email_map[eaa.contact_email_id]
                contact_account_role = contact_account_map.get(eaa.account_id)
                if not contact_account_role or not email:
                    continue
                contact_account_role.contact_account_emails.append(
                    contact_account_email_from_db(
                        email=email,
                        contact_email_account_association=eaa,
                    )
                )
            contact_accounts = list(contact_account_map.values())
            contact_accounts.sort(key=lambda x: x.account_id)
            contact_account_roles_map[contact_id] = contact_accounts
        return contact_account_roles_map

    async def list_active_pipelines_by_contact_id(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None = None,
    ) -> list[PipelineIdentifiers]:
        active_pipelines = await self.pipeline_repository.list_pipelines_by_contact_id(
            organization_id=organization_id,
            contact_id=contact_id,
            account_id=account_id,
        )
        return [
            PipelineIdentifiers(
                pipeline_id=pipeline.id,
                pipeline_display_name=pipeline.display_name,
                stage_id=pipeline.stage_id,
            )
            for pipeline in active_pipelines
        ]

    async def list_contacts_by_pipeline_id(
        self, *, organization_id: UUID, pipeline_id: UUID
    ) -> list[ContactV2]:
        db_contacts = await self.contact_repository.list_by_pipeline_id(
            organization_id=organization_id, pipeline_id=pipeline_id
        )
        return await self._list_full_contacts_v2(db_contacts, organization_id)

    async def get_contact_v2(
        self,
        *,
        contact_id: UUID,
        organization_id: UUID,
        include_custom_object: bool | None = False,
    ) -> ContactV2:
        list_result = await self.list_contacts_v2(
            organization_id=organization_id,
            only_include_contact_ids={contact_id},
            include_custom_object=include_custom_object,
        )
        if not list_result:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.CONTACT_NOT_FOUND,
                    details="Contact not found",
                    reference_id=str(contact_id),
                )
            )
        if len(list_result) > 1:
            self.logger.error(
                "more than 1 contact found by contact_id and organization_id",
                contact_id=contact_id,
                organization_id=organization_id,
                found_contacts=list_result,
            )
            raise IllegalStateError(
                "more than 1 contact found by contact_id and organization_id"
            )
        return list_result[0]

    async def map_contact_by_primary_account_ids(
        self,
        organization_id: UUID,
        primary_account_ids: set[UUID],
        include_archived: bool = False,
        contextual_account_id: UUID | None = None,
    ) -> Mapping[UUID, list[ContactV2]]:
        db_contact_map = (
            await self.contact_repository.map_contact_by_primary_account_ids(
                organization_id=organization_id,
                primary_account_ids=primary_account_ids,
                include_archived=include_archived,
            )
        )

        all_contacts = [
            contact for contacts in db_contact_map.values() for contact in contacts
        ]

        full_contacts = await self._list_full_contacts_v2(
            all_contacts,
            organization_id,
            contextual_account_id=contextual_account_id,
        )

        result: dict[UUID, list[ContactV2]] = defaultdict(list)
        for contact in full_contacts:
            if contact.primary_account_id:
                result[contact.primary_account_id].append(contact)

        return result

    async def map_contact_v2_by_primary_account_ids(
        self,
        *,
        organization_id: UUID,
        account_ids: set[UUID],
        include_custom_object: bool = False,
    ) -> Mapping[UUID, list[ContactV2]]:
        db_contacts_by_account_id = (
            await self.contact_repository.map_contact_by_primary_account_ids(
                organization_id=organization_id,
                primary_account_ids=account_ids,
            )
        )
        contact_ids: set[UUID] = set()
        for contacts in db_contacts_by_account_id.values():
            contact_ids.update({contact.id for contact in contacts})

        domain_contacts = await self.list_contacts_v2(
            organization_id=organization_id,
            only_include_contact_ids=contact_ids,
            include_custom_object=include_custom_object,
        )
        domain_contacts_by_account_id: defaultdict[UUID, list[ContactV2]] = defaultdict(
            list
        )
        for contact in domain_contacts:
            if contact.primary_account_id:
                domain_contacts_by_account_id[contact.primary_account_id].append(
                    contact
                )
        return domain_contacts_by_account_id

    async def list_active_contact_account_associations(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None = None,
    ) -> list[ContactAccountAssociation]:
        return list(
            (
                await self.contact_repository.map_active_contact_account_association_by_account_id_for_contact(
                    organization_id=organization_id,
                    contact_id=contact_id,
                    account_ids={account_id} if account_id else UNSET,
                )
            ).values()
        )

    async def list_latest_contact_account_associations(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None = None,
    ) -> list[ContactAccountAssociation]:
        return list(
            (
                await self.contact_repository.map_latest_contact_account_association_by_account_id_for_contact(
                    organization_id=organization_id,
                    contact_id=contact_id,
                    account_ids={account_id} if account_id else UNSET,
                )
            ).values()
        )

    async def list_active_contact_associations_for_account(
        self, *, organization_id: UUID, account_id: UUID
    ) -> list[ContactAccountAssociation]:
        return (
            await self.contact_repository.list_active_contact_associations_for_account(
                organization_id=organization_id,
                account_id=account_id,
            )
        )

    async def list_active_contact_associations_for_contact(
        self, *, organization_id: UUID, contact_id: UUID
    ) -> list[ContactAccountAssociation]:
        return (
            await self.contact_repository.list_active_contact_associations_for_contact(
                organization_id=organization_id,
                contact_id=contact_id,
            )
        )

    async def list_active_contact_associations_for_contact_ids(
        self,
        *,
        organization_id: UUID,
        contact_ids: set[UUID],
        is_primary: bool = False,
    ) -> frozendict[UUID, list[ContactAccountAssociation]]:
        return await self.contact_repository.map_active_contact_account_association_by_contact_ids(
            organization_id=organization_id,
            contact_ids=contact_ids,
            is_primary=is_primary,
        )

    async def _db_contact_to_contact_v2(
        self,
        db_contacts: list[DbContact],
    ) -> list[ContactV2]:
        contacts: list[ContactV2] = []
        if not db_contacts:
            return contacts
        org_id_contact_map: dict[UUID, list[UUID]] = {}
        for db_contact in db_contacts:
            org_id_contact_map.setdefault(
                db_contact.organization_id,
                [],
            ).append(db_contact.id)
        for org_id, contact_ids in org_id_contact_map.items():
            contacts.extend(
                await self.list_contacts_v2(
                    organization_id=org_id,
                    only_include_contact_ids=set(contact_ids),
                )
            )
        return contacts

    async def list_by_person_ids(
        self, person_ids: list[UUID], organization_id: UUID | None
    ) -> list[ContactV2]:
        if not person_ids:
            return []
        db_contacts = await self.contact_repository.list_by_person_ids(
            person_ids=person_ids,
            organization_id=organization_id,
        )
        return await self._db_contact_to_contact_v2(
            db_contacts,
        )

    async def list_by_emails(
        self,
        emails: list[EmailStr],
        organization_id: UUID | None,
    ) -> list[ContactV2]:
        if not emails:
            return []
        db_contacts = await self.contact_repository.list_by_emails(
            emails=emails,
            organization_id=organization_id,
        )
        return await self._db_contact_to_contact_v2(
            db_contacts,
        )

    async def get_by_id_or_fail(
        self,
        contact_id: UUID,
        organization_id: UUID,
    ) -> Contact:
        db_contact = await self.contact_repository.find_by_tenanted_primary_key(
            DbContact,
            id=contact_id,
            organization_id=organization_id,
        )
        if not db_contact:
            raise ResourceNotFoundError(
                f"Contact (id={contact_id}) doesn't exist",
            )
        return db_contact

    async def get_by_id(
        self,
        contact_id: UUID,
        organization_id: UUID,
    ) -> Contact | None:
        return await self.contact_repository.find_by_tenanted_primary_key(
            DbContact,
            id=contact_id,
            organization_id=organization_id,
        )

    async def list_by_ids(
        self,
        contact_ids: list[UUID],
        organization_id: UUID,
    ) -> list[Contact]:
        return await self.contact_repository.list_by_ids(
            contact_ids=contact_ids,
            organization_id=organization_id,
        )

    async def list_all_db_contact_no_associated_data(
        self,
        organization_id: UUID,
        include_archived: bool | None = False,
    ) -> list[DbContact]:
        return await self.contact_repository.list_all(
            organization_id=organization_id,
            exclude_archived=not include_archived,
        )

    async def find_by_primary_emails(
        self, organization_id: UUID, primary_emails: list[str]
    ) -> list[Contact]:
        if not primary_emails:
            return []

        return await self.contact_repository.list_by_emails(
            organization_id=organization_id, emails=primary_emails
        )

    async def find_contact_ids_by_primary_emails(
        self, *, organization_id: UUID, primary_emails: set[str]
    ) -> frozendict[EmailStr, UUID]:
        contact_by_primary_email = (
            await self.contact_repository.map_contacts_by_primary_emails(
                organization_id=organization_id, emails=primary_emails
            )
        )
        return frozendict[EmailStr, UUID](
            {
                primary_email: contact.id
                for primary_email, contact in contact_by_primary_email.items()
            }
        )

    async def find_contacts_by_primary_emails(
        self, *, organization_id: UUID, primary_emails: set[str]
    ) -> frozendict[EmailStr, ContactV2]:
        contact_id_by_primary_email = await self.find_contact_ids_by_primary_emails(
            organization_id=organization_id,
            primary_emails=primary_emails,
        )
        return frozendict[EmailStr, ContactV2](
            {
                primary_email: await self.get_contact_v2(
                    organization_id=organization_id,
                    contact_id=contact_id_by_primary_email[primary_email],
                )
                for primary_email in contact_id_by_primary_email
            }
        )

    async def list_contacts_by_emails(
        self, organization_id: UUID, emails: list[EmailStr]
    ) -> list[ContactV2]:
        if not emails:
            return []
        db_contacts = await self.contact_repository.list_by_emails(
            organization_id=organization_id, emails=emails
        )
        return await self.list_contacts_v2(
            organization_id=organization_id,
            only_include_contact_ids={db_contact.id for db_contact in db_contacts},
        )

    async def find_by_linkedin_urls(
        self, *, organization_id: UUID, linkedin_urls: list[str]
    ) -> list[Contact]:
        if not linkedin_urls:
            return []
        return await self.contact_repository.list_by_linkedin_urls(
            organization_id=organization_id, linkedin_urls=linkedin_urls
        )

    async def list_by_linkedin_urls(
        self, *, organization_id: UUID | None, linkedin_urls: list[str]
    ) -> list[ContactV2]:
        if not linkedin_urls:
            return []
        db_contacts = await self.contact_repository.list_by_linkedin_urls(
            organization_id=organization_id, linkedin_urls=linkedin_urls
        )
        return await self._db_contact_to_contact_v2(
            db_contacts,
        )

    async def _to_selection_spec(  # noqa: C901, PLR0912
        self,
        *,
        organization_id: UUID,
        domain_fetch_hints: DomainFetchHints | None,
    ) -> SelectionSpec:
        if not domain_fetch_hints:
            return to_sql_selection_spec(
                domain_fetch_hints=domain_fetch_hints,
                domain_to_table_model_field_mapping=contact_domain_db_mapping,
            )

        # check if the domain fetch hints include any stage_id in filter or sorter
        baseline_filters = (
            domain_fetch_hints.baseline_filters or BaselineFilters.empty()
        )
        has_stage_id_condition = False
        for filter_ in (
            baseline_filters.must_filters
            + baseline_filters.at_least_one_filters
            + baseline_filters.must_not_filters
        ):
            if _is_filter_for_stage_id(filter_):
                has_stage_id_condition = True
                break
        if not has_stage_id_condition:
            for (
                sorter
            ) in domain_fetch_hints.non_relational_sorting_spec.ordered_sorters:
                if _is_sorter_for_stage_id(sorter):
                    has_stage_id_condition = True
                    break
        if not has_stage_id_condition:
            return to_sql_selection_spec(
                domain_fetch_hints=domain_fetch_hints,
                domain_to_table_model_field_mapping=contact_domain_db_mapping,
            )

        # when the request need to filter or sort on contact.stage_id, we need to:
        # 1. translate any filter to pre-mapped stage_ids , in case of remapp happened for this org
        # 2. provide rank mapping for the stage_id field for sorter

        full_contact_stage_select_list = await self.select_list_service.list_full_select_lists_by_application_code_name(
            application_code_name=StdSelectListIdentifier.contact_stage,
            organization_id=organization_id,
        )
        if not full_contact_stage_select_list:
            logger.error(
                "No full contact stage select list found",
                application_code_name=StdSelectListIdentifier.contact_stage,
                organization_id=organization_id,
            )
            return to_sql_selection_spec(
                domain_fetch_hints=domain_fetch_hints,
                domain_to_table_model_field_mapping=contact_domain_db_mapping,
            )
        # there can only be one full contact stage select list
        contact_stage_select_list = full_contact_stage_select_list[0]

        new_must_filters: list[ValueFilter] = []
        new_must_not_filters: list[ValueFilter] = []
        new_at_least_one_filters: list[ValueFilter] = []

        for filter_ in baseline_filters.must_filters:
            if _is_filter_for_stage_id(filter_):
                _expanded_filters = _expand_stage_id_filter(
                    filter_, contact_stage_select_list
                )
                # we need to fanout the filter by value, and add the expanded filters to the at_least_one_filters
                # in case of multiple values are expanded
                new_at_least_one_filters.extend(_expanded_filters)
            else:
                new_must_filters.append(filter_)

        for filter_ in baseline_filters.at_least_one_filters:
            if _is_filter_for_stage_id(filter_):
                _expanded_filters = filter_.fanout_by_value(filter_.value)
                new_at_least_one_filters.extend(_expanded_filters)
            else:
                new_at_least_one_filters.append(filter_)

        for filter_ in baseline_filters.must_not_filters:
            if _is_filter_for_stage_id(filter_):
                _expanded_filters = _expand_stage_id_filter(
                    filter_, contact_stage_select_list
                )
                new_must_not_filters.extend(_expanded_filters)
            else:
                new_must_not_filters.append(filter_)

        new_sorters: list[NonRelationalSorter] = []
        for sorter in domain_fetch_hints.non_relational_sorting_spec.ordered_sorters:
            if _is_sorter_for_stage_id(sorter):
                new_sorters.append(_replace_stage_id_sorter(sorter))
            else:
                new_sorters.append(sorter)

        new_fetch_hints = strict_model_copy(
            domain_fetch_hints,
            baseline_filters=dataclasses.replace(
                baseline_filters,
                must_filters=new_must_filters,
                must_not_filters=new_must_not_filters,
                at_least_one_filters=new_at_least_one_filters,
            ),
            non_relational_sorting_spec=strict_model_copy(
                domain_fetch_hints.non_relational_sorting_spec,
                ordered_sorters=tuple(new_sorters),
            ),
        )
        selection_spec = to_sql_selection_spec(
            domain_fetch_hints=new_fetch_hints,
            domain_to_table_model_field_mapping=contact_domain_db_mapping,
            sort_rank_mapping_provider=_transform_stage_id_sort_ranking_provider(
                stage_sl_dto=contact_stage_select_list
            ),
        )

        logger.info(
            "expanded fetch hints for contact stage",
            organization_id=organization_id,
            original_fetch_hints=domain_fetch_hints,
            resulting_fetch_hints=new_fetch_hints,
            resulting_selection_spec=selection_spec,
        )
        return selection_spec

    async def list_contacts_with_primary_account(
        self, *, organization_id: UUID
    ) -> list[ContactV2]:
        db_contacts = await self.contact_repository.list_with_primary_account(
            organization_id=organization_id,
        )
        return await self._list_full_contacts_v2(
            db_contacts=db_contacts,
            organization_id=organization_id,
            include_custom_object=False,
        )

    async def find_contact_id_by_phone_number(
        self,
        organization_id: UUID,
        phone_number: str,
    ) -> UUID | None:
        contact = await self.contact_repository.find_contact_by_phone_number(
            organization_id=organization_id,
            phone_number=phone_number,
        )
        return contact.id if contact else None

    async def list_contact_ids_by_phone_number(
        self,
        organization_id: UUID,
        phone_number: str,
    ) -> list[UUID]:
        # Method name now correctly reflects returning multiple IDs
        contacts = await self.contact_repository.list_contacts_by_phone_number(
            organization_id=organization_id,
            phone_number=phone_number,
        )
        return [contact.id for contact in contacts]

    async def list_contact_emails_by_contact_id(
        self,
        organization_id: UUID,
        contact_id: UUID,
    ) -> list[ContactEmail]:
        return cast(
            list[ContactEmail],
            await self.contact_repository.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_id=contact_id,
            ),
        )

    async def list_contact_emails_by_contact_ids(
        self,
        organization_id: UUID,
        contact_ids: set[UUID],
    ) -> dict[UUID, list[ContactEmail]]:
        return cast(
            dict[UUID, list[ContactEmail]],
            await self.contact_repository.map_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_ids=contact_ids,
            ),
        )

    async def list_contact_phone_numbers_by_contact_id(
        self,
        organization_id: UUID,
        contact_id: UUID,
    ) -> list[ContactPhoneNumber]:
        db_contact_phone_numbers = cast(
            list[DbContactPhoneNumber],
            await self.contact_repository.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.PHONE_NUMBER,
                organization_id=organization_id,
                contact_id=contact_id,
            ),
        )
        return [
            contact_phone_number_from_db(
                db_contact_phone_number=db_contact_phone_number
            )
            for db_contact_phone_number in db_contact_phone_numbers
        ]

    async def list_contact_phone_number_account_associations_by_contact_id(
        self,
        organization_id: UUID,
        contact_id: UUID,
    ) -> list[ContactPhoneNumberAccountAssociation]:
        return cast(
            list[ContactPhoneNumberAccountAssociation],
            await self.contact_repository.list_contact_channel_info_account_associations_by_contact_id(
                contact_channel_type=ContactChannelType.PHONE_NUMBER,
                organization_id=organization_id,
                contact_id=contact_id,
            ),
        )

    async def list_contact_email_ids_by_contact_id(
        self,
        organization_id: UUID,
        contact_id: UUID,
    ) -> list[UUID]:
        contact_emails = cast(
            list[ContactEmail],
            await self.contact_repository.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_id=contact_id,
            ),
        )
        return [email.id for email in contact_emails]

    async def find_primary_account_company_name_for_contact_ids(
        self,
        contact_ids: set[UUID],
        organization_id: UUID,
    ) -> Mapping[UUID, str]:
        return await self.contact_repository.find_primary_account_company_name_for_contact_ids(
            contact_ids=contact_ids,
            organization_id=organization_id,
        )

    async def get_primary_email_by_contact_id(
        self,
        organization_id: UUID,
        contact_id: UUID,
    ) -> EmailStrLower | None:
        return cast(
            EmailStrLower,
            await self.contact_repository.get_primary_channel_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_id=contact_id,
            ),
        )

    async def get_primary_emails_by_contact_ids(
        self,
        organization_id: UUID,
        contact_ids: set[UUID],
    ) -> dict[UUID, EmailStrLower]:
        return cast(
            dict[UUID, EmailStrLower],
            await self.contact_repository.get_primary_channels_by_contact_ids(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_ids=contact_ids,
            ),
        )

    async def list_contact_emails_by_emails(
        self,
        organization_id: UUID,
        emails: set[EmailStrLower],
    ) -> list[ContactEmail]:
        return await self.contact_repository.list_contact_emails_by_emails(
            organization_id=organization_id,
            emails=emails,
        )

    async def find_the_most_relevant_contact_email(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None = None,
    ) -> EmailStrLower | None:
        """
        Find the most relevant contact email for the contact and account context

        Selection Criteria:
            * if account_id is specified
                * return primary contact account email for this account if have and not null
                * otherwise, return primary contact account email for contact's primary account if have and not null
                * otherwise, return contact's primary email if have and not null
                * otherwise, return None
            * if account_id is not specified
                * return primary contact account email for contact's primary account if have and not null
                * otherwise, return primary contact email if have and not null
                * otherwise, return None

        Note:
        To keep backward compatibility, fallback to contact's primary email if no relevant contact emails found
        """
        # account context specified case
        if account_id and (
            primary_contact_account_email
            := await self.contact_repository.get_contact_account_primary_email(
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=account_id,
            )
        ):
            return primary_contact_account_email

        # try primary account's primary email
        contact = await self.get_contact_v2(
            organization_id=organization_id,
            contact_id=contact_id,
        )

        if contact.primary_account_id and (
            primary_account_primary_email
            := await self.contact_repository.get_contact_account_primary_email(
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=contact.primary_account_id,
            )
        ):
            return primary_account_primary_email

        # try contact primary email
        if primary_contact_email := await self.get_primary_email_by_contact_id(
            organization_id=organization_id,
            contact_id=contact_id,
        ):
            return primary_contact_email

        # fallback to primary contact email
        return contact.primary_email if contact.primary_email else None

    async def list_contact_email_dtos_by_contact_id(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        contextual_account_id: UUID | None = None,
    ) -> list[ContactEmailDto]:
        (
            contact_emails,
            association_map,
        ) = await self.contact_repository.list_contact_email_and_email_association_by_contact_id(
            organization_id=organization_id, contact_id=contact_id
        )
        all_associations: list[ContactEmailAccountAssociation] = []
        for associations in association_map.values():
            all_associations.extend(associations)

        contact_account_association_map = await self.contact_repository.map_active_contact_account_association_by_contact_ids(
            organization_id=organization_id, contact_ids={contact_id}
        )
        ordered_contact_email = await self._sort_contact_emails_by_relevant_order(
            contact_emails=contact_emails,
            contact_email_account_associations=all_associations,
            contact_account_associations=contact_account_association_map.get(
                contact_id
            ),
            account_id=contextual_account_id,
        )
        return [
            ContactEmailDto(
                contact_email=contact_email,
                contact_email_account_associations=association_map.get(
                    contact_email.id, []
                ),
            )
            for contact_email in ordered_contact_email
        ]

    async def list_contact_phone_number_dtos_by_contact_id(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
    ) -> list[ContactPhoneNumberDto]:
        db_contact_phone_numbers = cast(
            list[DbContactPhoneNumber],
            await self.contact_repository.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.PHONE_NUMBER,
                organization_id=organization_id,
                contact_id=contact_id,
            ),
        )
        all_associations = (
            await self.list_contact_phone_number_account_associations_by_contact_id(
                organization_id=organization_id,
                contact_id=contact_id,
            )
        )
        association_map: dict[UUID, list[ContactPhoneNumberAccountAssociation]] = (
            defaultdict(list)
        )
        for association in all_associations:
            association_map[association.contact_phone_number_id].append(association)

        return [
            ContactPhoneNumberDto(
                contact_phone_number=contact_phone_number,
                contact_phone_number_account_associations=association_map.get(
                    contact_phone_number.id, []
                ),
            )
            for contact_phone_number in db_contact_phone_numbers
        ]

    async def _sort_contact_emails_by_relevant_order(
        self,
        contact_account_associations: list[ContactAccountAssociation] | None,
        contact_emails: list[ContactEmail],
        contact_email_account_associations: list[ContactEmailAccountAssociation] | None,
        account_id: UUID | None = None,
    ) -> list[ContactEmail]:
        """
        1/ when account_id in the context, find the most relevant emails in the following priority order:
            - `account preferred email` → `account associated email` → `contact primary email` -> the others
        2/ when opportunity in the context -> use (account_id, primary contact_id) of the opportunity -> became use case 1
            - `account preferred email` → `account associated email` → `contact primary email` -> the others
        3/ not provide account / opportunity as context, find the most relevant emails in the following priority order:
            - `contact primary email` -> `primary account preferred email` -> the others
        """
        if not contact_emails:
            return []
        primary_account_id: UUID | None = None
        for contact_account_association in contact_account_associations or []:
            if contact_account_association.is_primary:
                primary_account_id = contact_account_association.account_id
                break
        email_account_association_map: dict[
            UUID, list[ContactEmailAccountAssociation]
        ] = defaultdict(list)
        for association in contact_email_account_associations or []:
            email_account_association_map[association.contact_email_id].append(
                association
            )

        contact_primary_email: ContactEmail | None = None
        account_associated_emails: list[ContactEmail] = []
        for contact_email in contact_emails:
            if contact_email.is_contact_primary:
                contact_primary_email = contact_email
            if email_account_association_map.get(contact_email.id):
                account_associated_emails.append(contact_email)

        duplicated_ordered_contact_emails = (
            await self._append_duplicated_ordered_contact_emails(
                account_id=account_id,
                primary_account_id=primary_account_id,
                contact_primary_email=contact_primary_email,
                contact_emails=contact_emails,
                account_associated_emails=account_associated_emails,
                email_account_association_map=email_account_association_map,
            )
        )

        ordered_contact_email_map: OrderedDict[UUID, ContactEmail] = OrderedDict()
        for duplicated_contact_email in duplicated_ordered_contact_emails:
            if not duplicated_contact_email:
                continue
            ordered_contact_email_map.update(
                {duplicated_contact_email.id: duplicated_contact_email}
            )
        return list(ordered_contact_email_map.values())

    async def _append_duplicated_ordered_contact_emails(
        self,
        account_id: UUID | None,
        primary_account_id: UUID | None,
        contact_primary_email: ContactEmail | None,
        contact_emails: list[ContactEmail],
        account_associated_emails: list[ContactEmail],
        email_account_association_map: dict[UUID, list[ContactEmailAccountAssociation]],
    ) -> list[ContactEmail | None]:
        duplicated_ordered_contact_emails: list[ContactEmail | None] = []
        # Through the context, account_id can be clearly identified
        # - `account preferred email` → `account associated email` → `contact primary email` -> the others
        if account_id:
            account_primary_email: ContactEmail | None = None
            for contact_email in account_associated_emails:
                contact_email_account_associations = (
                    email_account_association_map.get(contact_email.id) or []
                )
                for (
                    contact_email_account_association
                ) in contact_email_account_associations:
                    if contact_email_account_association.account_id == account_id:
                        if contact_email_account_association.is_contact_account_primary:
                            account_primary_email = contact_email
                        else:
                            duplicated_ordered_contact_emails.append(contact_email)
            duplicated_ordered_contact_emails.insert(0, account_primary_email)
            duplicated_ordered_contact_emails.append(contact_primary_email)
            duplicated_ordered_contact_emails.extend(contact_emails)
        # The context cannot determine the account_id
        # - `contact primary email` -> `primary account preferred email` -> the others
        else:
            duplicated_ordered_contact_emails.append(contact_primary_email)
            primary_account_primary_email: ContactEmail | None = None
            for contact_email in account_associated_emails:
                contact_email_account_associations = (
                    email_account_association_map.get(contact_email.id) or []
                )
                for (
                    contact_email_account_association
                ) in contact_email_account_associations:
                    if (
                        contact_email_account_association.account_id
                        == primary_account_id
                    ):
                        if contact_email_account_association.is_contact_account_primary:
                            primary_account_primary_email = contact_email
                        else:
                            duplicated_ordered_contact_emails.append(contact_email)
            duplicated_ordered_contact_emails.insert(1, primary_account_primary_email)
            duplicated_ordered_contact_emails.extend(contact_emails)
        return duplicated_ordered_contact_emails

    async def get_contact_email_account_associations_by_emails(
        self,
        *,
        organization_id: UUID,
        emails: list[EmailStrLower],
    ) -> dict[EmailStrLower, list[ContactEmailAccountAssociation]]:
        return await self.contact_repository.get_contact_email_account_associations_by_emails(
            organization_id=organization_id,
            emails=emails,
        )

    async def get_primary_contact_email_account_associations_by_emails(
        self,
        *,
        organization_id: UUID,
        emails: list[EmailStrLower],
    ) -> dict[EmailStrLower, list[ContactEmailAccountAssociation]]:
        return await self.contact_repository.get_primary_contact_email_account_associations_by_emails(
            organization_id=organization_id,
            emails=emails,
        )

    # TODO: resolve duplicated code later
    # with ryan's func map_the_most_relevant_contact_emails
    async def get_preferred_email_of_account(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID,
    ) -> str | None:
        contact_email = (
            await self.contact_repository.get_preferred_contact_email_of_account(
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=account_id,
            )
        )
        return contact_email.email if contact_email else None

    async def get_single_association_account_id_by_emails(
        self,
        *,
        organization_id: UUID,
        emails: list[EmailStrLower],
    ) -> UUID | None:
        """
        Find the associated account_id by emails:
            If multiple account_ids exist, return None
            If only one account_id exists, return it
        """
        contact_email_account_association_map = (
            await self.get_contact_email_account_associations_by_emails(
                organization_id=organization_id,
                emails=emails,
            )
        )
        # some emails have no association
        if len(emails) != len(contact_email_account_association_map):
            return None
        account_id = None
        account_ids: set[UUID] = set()
        if contact_email_account_association_map:
            for (
                contact_email_account_associations
            ) in contact_email_account_association_map.values():
                account_ids = account_ids.union(
                    {
                        contact_email_account_association.account_id
                        for contact_email_account_association in contact_email_account_associations
                    }
                )
        if len(account_ids) == 1:
            account_id = account_ids.pop()
        return account_id

    async def find_primary_account_id_with_contact_id_pair(
        self, organization_id: UUID, only_include_contact_ids: list[UUID] | None
    ) -> list[ContactAccountIdPair]:
        id_pairs = await self.contact_repository.list_primary_contact_account_id_pairs(
            organization_id=organization_id,
            only_include_contact_ids=only_include_contact_ids,
        )
        return [
            ContactAccountIdPair(contact_id=pair[0], account_id=pair[1])
            for pair in id_pairs
        ]

    async def search_contact_by_display_name_or_email(
        self,
        *,
        organization_id: UUID,
        keyword: str,
        account_id: UUID | None = None,
        exclude_contact_ids: list[UUID] | None = None,
        need_to_have_account_association: bool = False,
        additional_contact_ids: list[UUID] | None = None,
    ) -> list[ContactSearchResult]:
        # Get contact IDs from both searches
        email_contact_ids = await self.contact_repository.search_contact_by_email(
            organization_id=organization_id,
            keyword=keyword,
            account_id=account_id,
            need_to_have_account_association=need_to_have_account_association,
        )

        display_name_contact_ids = (
            await self.contact_repository.search_contact_by_display_name(
                organization_id=organization_id,
                keyword=keyword,
                account_id=account_id,
                need_to_have_account_association=need_to_have_account_association,
            )
        )

        exclude_contact_id_set = (
            set(exclude_contact_ids) if exclude_contact_ids else set()
        )
        additional_contact_ids = (
            additional_contact_ids if additional_contact_ids else []
        )
        additional_contact_id_set = set(additional_contact_ids)

        # Deduplicate contact IDs
        unique_contact_ids = list(
            (set(email_contact_ids) | set(display_name_contact_ids))
            - exclude_contact_id_set
            - additional_contact_id_set
        )

        # not specify keyword, return 100 records default
        if not keyword:
            unique_contact_ids = unique_contact_ids[:100]

        if not unique_contact_ids and not additional_contact_ids:
            return []

        unique_contact_res = (
            await self.contact_repository.list_contacts_with_display_name_and_emails(
                organization_id=organization_id,
                contact_ids=unique_contact_ids,
            )
        )
        additional_contact_res = (
            await self.contact_repository.list_contacts_with_display_name_and_emails(
                organization_id=organization_id,
                contact_ids=additional_contact_ids,
            )
        )
        return additional_contact_res + unique_contact_res

    async def search_paginated_contact_by_display_name_or_email(
        self,
        *,
        organization_id: UUID,
        keyword: str,
        account_id: UUID | None = None,
        exclude_contact_ids: list[UUID] | None = None,
        need_to_have_account_association: bool = False,
        additional_contact_ids: list[UUID] | None = None,
        cursor: Cursor | None = None,
    ) -> tuple[list[ContactSearchResult], list[ContactSearchResult], Cursor]:
        # not specify keyword, return 100 records default
        limit = None if keyword else 100
        cursor = cursor if cursor else Cursor()

        # Get contact IDs from both searches
        email_contact_ids = await self.contact_repository.search_contact_by_email(
            organization_id=organization_id,
            keyword=keyword,
            account_id=account_id,
            need_to_have_account_association=need_to_have_account_association,
            limit=limit,
        )

        display_name_contact_ids = (
            await self.contact_repository.search_contact_by_display_name(
                organization_id=organization_id,
                keyword=keyword,
                account_id=account_id,
                need_to_have_account_association=need_to_have_account_association,
                limit=limit,
            )
        )

        exclude_contact_id_set = (
            set(exclude_contact_ids) if exclude_contact_ids else set()
        )
        additional_contact_ids = (
            additional_contact_ids if additional_contact_ids else []
        )
        additional_contact_id_set = set(additional_contact_ids)

        # Deduplicate contact IDs
        unique_contact_ids = list(
            (set(email_contact_ids) | set(display_name_contact_ids))
            - exclude_contact_id_set
            - additional_contact_id_set
        )

        if not unique_contact_ids and not additional_contact_ids:
            return [], [], cursor

        unique_contact_ids.sort(reverse=True)
        cursor.total_number = limit or len(unique_contact_ids)
        cursor.total_page_number = (
            cursor.total_number // cursor.page_size
            if cursor.total_number % cursor.page_size == 0
            else cursor.total_number // cursor.page_size + 1
        )
        page_size = cursor.page_size
        offset = cursor.page_size * (cursor.page_index - 1)
        paginated_unique_contact_ids = unique_contact_ids[offset : offset + page_size]

        paginated_unique_contact_res = (
            await self.contact_repository.list_contacts_with_display_name_and_emails(
                organization_id=organization_id,
                contact_ids=paginated_unique_contact_ids,
            )
        )

        additional_contact_res = (
            await self.contact_repository.list_contacts_with_display_name_and_emails(
                organization_id=organization_id,
                contact_ids=additional_contact_ids,
            )
        )
        return additional_contact_res, paginated_unique_contact_res, cursor


class SingletonContactQueryService(Singleton, ContactQueryService):
    pass


def get_contact_query_service(
    db_engine: DatabaseEngine,
) -> ContactQueryService:
    if SingletonContactQueryService.has_instance():
        return SingletonContactQueryService.get_singleton_instance()
    return SingletonContactQueryService(
        address_repository=AddressRepository(
            engine=db_engine,
        ),
        contact_repository=ContactRepository(
            engine=db_engine,
        ),
        custom_object_service=CustomObjectService(
            custom_object_repo=CustomObjectRepository(engine=db_engine),
            select_list_service=get_select_list_service(engine=db_engine),
        ),
        account_repository=AccountRepository(
            engine=db_engine,
        ),
        job_service=JobService(
            job_repository=JobRepository(
                engine=db_engine,
            )
        ),
        person_repository=PersonRepository(engine=db_engine),
        select_list_service=get_select_list_service(engine=db_engine),
        pipeline_repository=PipelineRepository(engine=db_engine),
        feature_flag_service=get_feature_flag_service(),
        research_agent_service=get_research_agent_service(db_engine=db_engine),
    )
