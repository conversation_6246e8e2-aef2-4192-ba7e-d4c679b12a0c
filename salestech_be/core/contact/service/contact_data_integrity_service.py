from uuid import UUID

from salestech_be.common.exception.exception import (
    ResourceNotFoundError,
)
from salestech_be.common.type.metadata.common import ObjectAccessStatus
from salestech_be.core.contact.service.contact_service import (
    get_contact_service,
)
from salestech_be.core.contact.service_api_schema import (
    MergeContactAccountAssociationPreviewResult,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityDataOperationValidationError,
    IntegrityOperationValidation,
)
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.crm_integrity_repository import CRMIntegrityJobRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.account import Account
from salestech_be.db.models.contact import (
    Contact,
    ContactUpdate,
    ContactUpdateCondition,
)
from salestech_be.db.models.contact_account_association import ContactAccountAssociation
from salestech_be.db.models.contact_email import (
    ContactEmail,
    ContactEmailAccountAssociation,
)
from salestech_be.db.models.crm_integrity import (
    EntityType,
    IntegrityOperation,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now

logger = get_logger()


class ContactDataIntegrityService:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.contact_service = get_contact_service(db_engine=db_engine)
        self.contact_repository = ContactRepository(
            engine=db_engine,
        )
        self.account_repository = AccountRepository(
            engine=db_engine,
        )
        self.crm_integrity_job_repository = CRMIntegrityJobRepository(
            engine=db_engine,
        )

    async def preview_contact_account_associations_for_merging_accounts(
        self,
        *,
        src_account_id: UUID,
        dest_account_id: UUID,
        organization_id: UUID,
    ) -> MergeContactAccountAssociationPreviewResult:
        """
        Preview contact account associations that will be (removed, created) when merging two active accounts.

        NOTE: ids in returned associations have no acutal meaning, do NOT use them as reference.
        """
        src_contact_account_associations = (
            await self.contact_service.list_active_contact_associations_for_account(
                organization_id=organization_id,
                account_id=src_account_id,
            )
        )

        # this a double safeguard to ensure we don't touch archived contacts
        src_contacts_v2 = (
            await self.contact_service.contact_query_service.list_contacts_v2(
                organization_id=organization_id,
                only_include_contact_ids={
                    association.contact_id
                    for association in src_contact_account_associations
                },
            )
        )

        src_active_contact_ids = {
            contact.id
            for contact in src_contacts_v2
            if contact.access_status == ObjectAccessStatus.ACTIVE
        }

        src_contact_id_to_association_map = {
            association.contact_id: association
            for association in src_contact_account_associations
            if association.contact_id in src_active_contact_ids
        }

        dest_contact_account_associations = (
            await self.contact_service.list_active_contact_associations_for_account(
                organization_id=organization_id,
                account_id=dest_account_id,
            )
        )

        dest_contact_ids = {
            association.contact_id for association in dest_contact_account_associations
        }

        # For associations that link to dest contacts already, we will keep the association as is.
        # So to save db effort, here we only create associations for contacts that
        # are NOT linked to dest account.
        associations_to_create = [
            src_contact_id_to_association_map[contact_id].model_copy(
                update={"account_id": dest_account_id}
            )
            for contact_id in src_contact_id_to_association_map
            if contact_id not in dest_contact_ids
        ]

        return MergeContactAccountAssociationPreviewResult(
            to_archive=list(src_contact_id_to_association_map.values()),
            to_create=associations_to_create,
        )

    async def preview_contact_account_associations_for_merging_contacts(
        self,
        *,
        src_contact_id: UUID,
        dest_contact_id: UUID,
        organization_id: UUID,
    ) -> MergeContactAccountAssociationPreviewResult:
        """
        Preview contact account associations that will be (removed, created) when merging two active contacts.

        NOTE: ids in returned associations have no acutal meaning, do NOT use them as reference.
        """
        src_contact_account_associations = (
            await self.contact_service.list_active_contact_account_associations(
                organization_id=organization_id,
                contact_id=src_contact_id,
            )
        )

        dest_contact_account_associations = (
            await self.contact_service.list_active_contact_account_associations(
                organization_id=organization_id,
                contact_id=dest_contact_id,
            )
        )

        src_account_id_to_association_map = {
            association.account_id: association
            for association in src_contact_account_associations
        }

        dest_account_ids = {
            association.account_id for association in dest_contact_account_associations
        }

        # For associations that link to dest contacts already, we will keep the association as is.
        # So to save db effort, here we only call upsert to create associations for accounts that
        # are NOT linked to dest contact.
        associations_to_create = [
            src_account_id_to_association_map[account_id].model_copy(
                update={"contact_id": dest_contact_id}
            )
            for account_id in src_account_id_to_association_map
            if account_id not in dest_account_ids
        ]

        return MergeContactAccountAssociationPreviewResult(
            to_archive=src_contact_account_associations,
            to_create=associations_to_create,
        )

    async def preview_solo_contact_email_account_associations(
        self,
        *,
        contact_id: UUID,
        contact_email_id: UUID,
        organization_id: UUID,
    ) -> list[ContactEmailAccountAssociation]:
        """
        Given a contact and a contact email, return all contact email account associations
        that are solo, aka. the contact email is the only contact email for the account.
        """
        result: list[ContactEmailAccountAssociation] = []
        contact_account_associations = await self.contact_service.list_contact_email_account_associations_by_contact_id(
            contact_id=contact_id,
            organization_id=organization_id,
        )

        account_id_to_associations_map: dict[
            UUID, list[ContactEmailAccountAssociation]
        ] = {}
        for association in contact_account_associations:
            account_id_to_associations_map.setdefault(
                association.account_id, []
            ).append(association)

        for associations in account_id_to_associations_map.values():
            if (
                len(associations) == 1
                and associations[0].contact_email_id == contact_email_id
            ):
                result.append(associations[0])

        return result

    async def preview_contact_account_associations_in_source_only(
        self,
        *,
        src_contact_id: UUID,
        dest_contact_id: UUID,
        organization_id: UUID,
    ) -> list[ContactAccountAssociation]:
        """
        Given a couple of contacts and one account, return the contact account associations
        that are existing in src contact but not in dest contact.
        """
        source_contact_account_associations = (
            await self.contact_service.list_active_contact_account_associations(
                contact_id=src_contact_id,
                organization_id=organization_id,
            )
        )

        source_account_id_to_association_map = {
            association.account_id: association
            for association in source_contact_account_associations
        }

        dest_contact_account_associations = (
            await self.contact_service.list_active_contact_account_associations(
                contact_id=dest_contact_id,
                organization_id=organization_id,
            )
        )

        dest_account_ids = {
            association.account_id for association in dest_contact_account_associations
        }

        return [
            association
            for account_id, association in source_account_id_to_association_map.items()
            if account_id not in dest_account_ids
        ]

    async def validate_contact_availability(
        self,
        *,
        contact_id: UUID,
        organization_id: UUID,
    ) -> list[IntegrityOperationValidation]:
        validations: list[IntegrityOperationValidation] = []
        contacts = await self.contact_service.list_contacts_v2(
            organization_id=organization_id,
            only_include_contact_ids={contact_id},
        )

        if not contacts:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.object_not_found,
                    error_message=f"contact {contact_id} not found",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=contact_id,
                    dest_entity_type=EntityType.CONTACT,
                    dest_entity_id=contact_id,
                )
            )
            return validations

        if contacts[0].access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.object_has_running_integrity_job,
                    error_message=f"contact {contact_id} has running integrity job",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=contact_id,
                    dest_entity_type=EntityType.CONTACT,
                    dest_entity_id=contact_id,
                )
            )
        elif contacts[0].access_status != ObjectAccessStatus.ACTIVE:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.object_not_in_active_state,
                    error_message=f"contact {contact_id} is not in active state",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=contact_id,
                    dest_entity_type=EntityType.CONTACT,
                    dest_entity_id=contact_id,
                )
            )

        return validations

    async def validate_contact_email_availability(
        self,
        *,
        contact_id: UUID,
        contact_email_id: UUID,
        organization_id: UUID,
    ) -> list[IntegrityOperationValidation]:
        validations: list[IntegrityOperationValidation] = []
        contact_email = await self.contact_service.get_contact_email_by_id(
            organization_id=organization_id,
            contact_email_id=contact_email_id,
        )

        if not contact_email:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.object_not_found,
                    error_message=f"contact email {contact_email_id} not found",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.CONTACT_EMAIL,
                    src_entity_id=contact_email_id,
                    dest_entity_type=EntityType.CONTACT_EMAIL,
                    dest_entity_id=contact_email_id,
                )
            )
        elif contact_email.contact_id != contact_id:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.contact_email_not_associated_with_contact,
                    error_message=f"contact email {contact_email_id} is not associated with contact {contact_id}",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.CONTACT_EMAIL,
                    src_entity_id=contact_email_id,
                    dest_entity_type=EntityType.CONTACT,
                    dest_entity_id=contact_id,
                )
            )

        return validations

    async def validate_contact_email_account_association_availability(
        self,
        *,
        contact_email_id: UUID,
        contact_id: UUID,
        account_id: UUID,
        organization_id: UUID,
        expected_existence: bool,
    ) -> list[IntegrityOperationValidation]:
        validations: list[IntegrityOperationValidation] = []
        contact_email_account_association = (
            await self.contact_repository.get_contact_email_account_association(
                organization_id=organization_id,
                contact_email_id=contact_email_id,
                contact_id=contact_id,
                account_id=account_id,
            )
        )

        if contact_email_account_association and not expected_existence:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.association_to_dest_already_exists,
                    error_message="contact email already associated to destination account",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.CONTACT_EMAIL,
                    src_entity_id=contact_email_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=account_id,
                )
            )
        elif not contact_email_account_association and expected_existence:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.association_to_dest_does_not_exist,
                    error_message="contact email is not associated to destination account",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.CONTACT_EMAIL,
                    src_entity_id=contact_email_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=account_id,
                )
            )

        return validations

    async def validate_contact_account_association_availability(
        self,
        *,
        contact_id: UUID,
        account_id: UUID,
        organization_id: UUID,
        expected_existence: bool,
    ) -> list[IntegrityOperationValidation]:
        validations: list[IntegrityOperationValidation] = []
        contact_account_associations = (
            await self.contact_service.list_active_contact_account_associations(
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=account_id,
            )
        )

        if contact_account_associations and not expected_existence:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.association_to_dest_already_exists,
                    error_message="contact already associated to destination account",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=contact_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=account_id,
                )
            )
        elif not contact_account_associations and expected_existence:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.association_to_dest_does_not_exist,
                    error_message="contact is not associated to destination account",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=contact_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=account_id,
                )
            )

        return validations

    async def validate_contact_has_account_associations(
        self,
        *,
        contact_id: UUID,
        organization_id: UUID,
    ) -> list[IntegrityOperationValidation]:
        validations: list[IntegrityOperationValidation] = []
        contact_account_associations = (
            await self.contact_service.list_active_contact_account_associations(
                organization_id=organization_id,
                contact_id=contact_id,
            )
        )

        if contact_account_associations:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.contact_has_active_account_association,
                    error_message="contact is associated to an account already",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=contact_id,
                    dest_entity_type=EntityType.CONTACT,
                    dest_entity_id=contact_id,
                )
            )

        return validations

    async def move_contact_email_to_account(
        self,
        *,
        contact_email_id: UUID,
        src_account_id: UUID,
        dest_account_id: UUID,
        user_id: UUID,
        organization_id: UUID,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> tuple[ContactEmailAccountAssociation, ContactEmailAccountAssociation | None]:
        contact_email = (
            await self.contact_repository.find_by_tenanted_primary_key_or_fail(
                table_model=ContactEmail,
                organization_id=organization_id,
                id=contact_email_id,
            )
        )

        # check entity states
        contact = await self.contact_repository.find_by_tenanted_primary_key_or_fail(
            table_model=Contact,
            organization_id=organization_id,
            id=contact_email.contact_id,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )

        await self.account_repository.find_by_tenanted_primary_key_or_fail(
            table_model=Account,
            organization_id=organization_id,
            id=dest_account_id,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )

        await self.account_repository.find_by_tenanted_primary_key_or_fail(
            table_model=Account,
            organization_id=organization_id,
            id=dest_account_id,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )

        dest_email_account_association = (
            await self.contact_repository.get_contact_email_account_association(
                organization_id=organization_id,
                contact_id=contact.id,
                contact_email_id=contact_email.id,
                account_id=dest_account_id,
            )
        )

        async with self.contact_repository.engine.begin():
            delete_result = (
                await self.contact_repository.delete_contact_email_account_association(
                    organization_id=organization_id,
                    user_id=user_id,
                    contact_id=contact.id,
                    account_id=src_account_id,
                    contact_email_id=contact_email.id,
                    _in_nested_transaction=True,
                )
            )

            inserted_association = None
            if not dest_email_account_association:
                (
                    upserted_contact_email_account_association,
                    _,
                    _,
                ) = await self.contact_repository.upsert_contact_email_account_association(
                    _in_nested_transaction=True,
                    target_contact_email=contact_email,
                    contact_email_account_association=ContactEmailAccountAssociation(
                        organization_id=organization_id,
                        contact_email_id=contact_email.id,
                        contact_id=contact.id,
                        account_id=dest_account_id,
                        is_contact_account_primary=False,
                        created_by_user_id=user_id,
                        updated_by_user_id=user_id,
                    ),
                )
                inserted_association = upserted_contact_email_account_association

            return delete_result.deleted_email_account_association, inserted_association

    async def acquire_lock_for_integrity_job(
        self, *, job_id: UUID, contact_id: UUID, user_id: UUID, organization_id: UUID
    ) -> ContactV2:
        updated_contact = await self.contact_repository.update_by_tenanted_primary_key(
            table_model=Contact,
            organization_id=organization_id,
            primary_key_to_value={
                "id": contact_id,
            },
            column_to_update=ContactUpdate(
                integrity_job_started_at=zoned_utc_now(),
                integrity_job_started_by_user_id=user_id,
                integrity_job_started_by_job_ids=[job_id],
                updated_by_user_id=user_id,
            ),
            column_condition=ContactUpdateCondition(
                integrity_job_started_at=None,
                integrity_job_started_by_user_id=None,
                integrity_job_started_by_job_ids=[],
            ),
        )

        if not updated_contact:
            raise ResourceNotFoundError(
                f"contact not found, contact_id: {contact_id}, organization_id: {organization_id}"
            )
        return await self.contact_service.get_contact_v2(
            contact_id=contact_id, organization_id=organization_id
        )

    async def free_lock_for_integrity_job(
        self, *, job_id: UUID, contact_id: UUID, user_id: UUID, organization_id: UUID
    ) -> ContactV2:
        updated_contact = await self.contact_repository.update_by_tenanted_primary_key(
            table_model=Contact,
            organization_id=organization_id,
            exclude_deleted_or_archived=False,
            exclude_locked_by_integrity_jobs=False,
            primary_key_to_value={
                "id": contact_id,
            },
            column_to_update=ContactUpdate(
                integrity_job_started_at=None,
                integrity_job_started_by_user_id=None,
                integrity_job_started_by_job_ids=[],
                updated_by_user_id=user_id,
            ),
        )

        if not updated_contact:
            raise ResourceNotFoundError(
                f"contact not found, contact_id: {contact_id}, organization_id: {organization_id}"
            )
        return await self.contact_service.get_contact_v2(
            contact_id=contact_id, organization_id=organization_id
        )


def get_contact_data_integrity_service(
    db_engine: DatabaseEngine,
) -> ContactDataIntegrityService:
    return ContactDataIntegrityService(db_engine=db_engine)
