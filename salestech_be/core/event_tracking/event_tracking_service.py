import json
from typing import Annotated
from uuid import uuid4

from fastapi import Depends

from salestech_be.common.singleton import Singleton
from salestech_be.core.email.type.email import (
    TrackingEventShortenedKeys,
    TrackingEventType,
)
from salestech_be.core.event_tracking.constants import (
    EVENT_TRACKING_PATH,
    EVENT_TRACKING_SUBDOMAIN,
)
from salestech_be.db.dao.event_tracking_repository import EventTrackingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.event_tracking import (
    CreateDbEventTrackingRequest,
    EventTracking,
)
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.encryptions import EncryptionManager
from salestech_be.settings import settings

logger = get_logger()


class EventTrackingService:
    def __init__(
        self, event_tracking_repository: Annotated[EventTrackingRepository, Depends()]
    ):
        self.event_tracking_repository = event_tracking_repository
        self.encryption_manager = EncryptionManager(settings.event_tracking_fernet_keys)

    async def get_or_create_tracker(
        self, req: CreateDbEventTrackingRequest, sender_domain: str | None = None
    ) -> EventTracking:
        """Get or create an event tracking record based on the request.

        This method processes tracking requests for email open and link click events. It:
        1. Generates a unique ID if not provided
        2. Creates encrypted tracking data with the event details
        3. Builds a tracking URL using either a customer-specific domain or the default domain
        4. Persists the tracking information to the database

        The tracking URL format will be either:
        - For custom domains (when enabled): https://{subdomain}.{sender_domain}/{path}/{encrypted_str}
        - For default domain: https://{subdomain}.{event_tracking_default_host}/{path}/{encrypted_str}

        Args:
            req: The event tracking request containing organization, reference and event details
            sender_domain: The domain of the sender of the email. If provided, it will be used to generate a tracking URL with a custom domain.

        Returns:
            EventTracking: The created or retrieved event tracking record

        Raises:
            ValueError: If original_url is empty for non-EMAIL_OPENED event types
        """
        # the caller should not have to set the id, but we might as well allow it.
        req.id = req.id or uuid4()

        if not req.original_url and req.event_type not in (
            TrackingEventType.EMAIL_OPENED
        ):
            raise ValueError(
                f"original_url cannot be empty for event type {req.event_type}"
            )

        event_details = {
            TrackingEventShortenedKeys.ID: str(
                req.id
            ),  # this is the only thing the Kafka Consumer needs to know to blindly insert historical data.
        }

        # add the original_url so we can decrypt, and redirect to it when the event is received.
        if req.original_url:
            event_details[TrackingEventShortenedKeys.ORIGINAL_URL] = req.original_url

        clear_text_str = json.dumps(event_details)

        # this creates the <encrypted_str> portion of the tracking URL.
        # e.g. https://m.reevo.ai/e/<encrypted_str>
        encrypted_str = self.encryption_manager.encrypt_for_url(clear_text_str)

        # Use sender_domain to construct the tracking url if provided, otherwise use the default base URL

        host = sender_domain or settings.event_tracking_default_host
        base_url = f"https://{EVENT_TRACKING_SUBDOMAIN}.{host}/{EVENT_TRACKING_PATH}"

        tracking_url = f"{base_url}/{encrypted_str}"
        req.tracking_url = tracking_url

        # TODO: what to do if it fails?
        # should we have a unique constraint on (organization_id, reference_id)?
        # should we have a unique constraint on the tracking_url?
        # under which conditions would the insert fail, and have a successful retry? non-retryable error?
        # await self.event_tracking_repository.create_event_tracking(
        #     create_db_event_tracking_request
        # )

        # this inserts the event tracker into the DB.
        return await self.event_tracking_repository.get_or_create_tracker(req)


class SingletonEventTrackingService(Singleton, EventTrackingService):
    pass


def get_event_tracking_service(db_engine: DatabaseEngine) -> EventTrackingService:
    return SingletonEventTrackingService(
        event_tracking_repository=EventTrackingRepository(engine=db_engine)
    )
