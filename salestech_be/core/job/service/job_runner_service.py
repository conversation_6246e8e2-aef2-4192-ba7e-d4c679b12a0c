from uuid import UUID

from salestech_be.common.stats.metric import custom_metric
from salestech_be.core.exports.service.export_service import (
    ExportService,
    get_export_service,
)
from salestech_be.core.imports.service.crm_sync_service import (
    CrmSyncService,
    get_crm_sync_service_from_db_engine,
)
from salestech_be.core.job.models import JobStatus, JobType
from salestech_be.core.job.processor.crm_sync_job import CrmSyncJob
from salestech_be.core.job.processor.csv_export_job import CsvExportJob
from salestech_be.core.job.processor.prompt_execution_job import (
    PromptExecutionJob,
)
from salestech_be.core.prompt.prompt_execution_service import (
    PromptExecutionService,
    get_prompt_execution_from_engine,
)
from salestech_be.db.dao.job_repository import JobRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.job import Job
from salestech_be.ree_logging import get_logger

logger = get_logger()


class JobRunnerService:
    job_metrics_name: str = "job"

    def __init__(
        self,
        job_repository: JobRepository,
        crm_sync_service: CrmSyncService,
        prompt_execution_service: PromptExecutionService,
        export_service: ExportService,
    ):
        self.job_repository = job_repository
        self.crm_sync_job = CrmSyncJob(
            crm_sync_service=crm_sync_service,
            job_repository=job_repository,
        )
        self.prompt_execution_job = PromptExecutionJob(
            job_repository=job_repository,
            prompt_execution_service=prompt_execution_service,
        )
        self.csv_export_job = CsvExportJob(
            job_repository=job_repository,
            export_service=export_service,
        )

    async def run_job(self, job_id: UUID) -> None:
        logger.bind(job_id=job_id).info("Starting running job")

        succeeded: bool = False
        error_code: str | None = None
        job_type: str = "unknown"
        try:
            job = await self.job_repository.find_by_primary_key(Job, id=job_id)
            if not job:
                logger.error(f"Job with id {job_id} not found.")
                error_code = "job_not_found"
                return
            job_type = job.type
            # Directly return if job is in not runnable state
            if job.status not in (JobStatus.ENQUEUED, JobStatus.FAILED):
                logger.error(
                    f"Job {job.type} with id {job.id} is not runnable, current status: {job.status} ."
                )
                error_code = "job_invalid_state"
                return
            if job.type in (
                JobType.CONTACT_CSV_UPLOAD,
                JobType.ACCOUNT_CSV_UPLOAD,
                JobType.PIPELINE_CSV_UPLOAD,
                JobType.MEETING_CSV_UPLOAD,
            ):
                raise NotImplementedError(
                    f"Job type {job.type} is deprecated and implementation removed"
                )
                # succeeded = await self.file_import_job.run(job)
            elif job.type in (
                JobType.HUBSPOT_USER_SYNC,
                JobType.HUBSPOT_ACCOUNT_SYNC,
                JobType.HUBSPOT_DEAL_SYNC,
                JobType.HUBSPOT_CONTACT_SYNC,
            ):
                succeeded = await self.crm_sync_job.run(job)
            elif job.type == JobType.GENERATE_LLM_PROMPT:
                succeeded = await self.prompt_execution_job.run(job)
            elif job.type in (
                JobType.ACCOUNT_CSV_EXPORT,
                JobType.CONTACT_CSV_EXPORT,
                JobType.DEAL_CSV_EXPORT,
                JobType.USER_CSV_EXPORT,
            ):
                succeeded = await self.csv_export_job.run(job)
            else:
                error_code = "job_missing_processor"
                logger.warning(
                    f"Job {job.type} with id {job.id} does not have processor."
                )
            error_code = "exception_found" if not succeeded else None
        finally:
            custom_metric.increment(
                metric_name=f"{self.job_metrics_name}_status",
                tags=[
                    f"job_type:{job_type.lower()}",
                    f"succeeded:{succeeded}",
                    f"error_code:{error_code}",
                ],
            )


def job_runner_service_from_engine(
    engine: DatabaseEngine,
) -> JobRunnerService:
    return JobRunnerService(
        job_repository=JobRepository(engine=engine),
        crm_sync_service=get_crm_sync_service_from_db_engine(engine=engine),
        prompt_execution_service=get_prompt_execution_from_engine(db_engine=engine),
        export_service=get_export_service(engine=engine),
    )
