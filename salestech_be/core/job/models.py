from enum import StrEnum

from salestech_be.db.models.core.types import CreatedSource


class JobType(StrEnum):
    ACCOUNT_CSV_UPLOAD = "ACCOUNT_CSV_UPLOAD"  # Deprecated
    CONTACT_CSV_UPLOAD = "CONTACT_CSV_UPLOAD"  # Deprecated
    CUSTOM_OBJECT_CSV_UPLOAD = "CUSTOM_OBJECT_CSV_UPLOAD"  # Deprecated
    PIPELINE_CSV_UPLOAD = "PIPELINE_CSV_UPLOAD"  # Deprecated
    MEETING_CSV_UPLOAD = "MEETING_CSV_UPLOAD"  # Deprecated
    ACCOUNT_CSV_EXPORT = "ACCOUNT_CSV_EXPORT"
    CONTACT_CSV_EXPORT = "CONTACT_CSV_EXPORT"
    DEAL_CSV_EXPORT = "DEAL_CSV_EXPORT"
    USER_CSV_EXPORT = "USER_CSV_EXPORT"
    HUBSPOT_USER_SYNC = "HUBSPOT_USER_SYNC"
    HUBSPOT_CONTACT_SYNC = "HUBSPOT_CONTACT_SYNC"
    HUBSPOT_ACCOUNT_SYNC = "HUBSPOT_ACCOUNT_SYNC"
    HUBSPOT_DEAL_SYNC = "HUBSPOT_DEAL_SYNC"
    HUBSPOT_TASK_SYNC = "HUBSPOT_TASK_SYNC"
    GENERATE_LLM_PROMPT = "GENERATE_LLM_PROMPT"

    def to_created_source(self) -> CreatedSource | None:
        match self:
            case (
                JobType.HUBSPOT_ACCOUNT_SYNC
                | JobType.HUBSPOT_CONTACT_SYNC
                | JobType.HUBSPOT_DEAL_SYNC
                | JobType.HUBSPOT_TASK_SYNC
                | JobType.HUBSPOT_USER_SYNC
            ):
                return CreatedSource.HUBSPOT_SYNC
            # Fill in more cases.
            case _:
                return None


class JobStatus(StrEnum):
    ENQUEUED = "ENQUEUED"
    RUNNING = "RUNNING"
    FAILED = "FAILED"
    COMPLETED = "COMPLETED"
    CONFLICT = "CONFLICT"
    REVERTED = "REVERTED"


class JobConfigType(StrEnum):
    ACCOUNT_ID = "ACCOUNT_ID"
    OWNER_USER_ID = "OWNER_USER_ID"
    ADD_TO_AUDIENCE_LIST_ID = "ADD_TO_AUDIENCE_LIST_ID"
