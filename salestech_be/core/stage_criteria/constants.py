from enum import StrEnum
from uuid import UUID

from frozendict import frozendict

from salestech_be.common.core_crm.contact_pipeline_role import ContactPipelineRoleType
from salestech_be.common.core_crm.sales_action import (
    SalesActionActivityState,
    SalesActionActivityType,
    StandardSalesActionRequirement,
    StandardSalesActionType,
)
from salestech_be.common.query_util.filter_schema import (
    CompositeFilter,
    FilterSpec,
    ValueFilter,
)
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.schema_manager.std_object_field_identifier import (
    CompetitionField,
    ContactPipelineRoleField,
    DecisionCriteriaField,
    DecisionProcessField,
    IdentifiedPainField,
    MetricField,
    PaperProcessField,
    PipelineField,
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    PipelineRelationship,
)
from salestech_be.common.type.metadata.schema import FieldReference, QualifiedField
from salestech_be.core.metadata.types import StageType
from salestech_be.core.stage_criteria.service_api_schema import CriteriaLibraryItem
from salestech_be.db.models.stage_criteria_v2 import CriteriaTypeEnum


class CriteriaLibraryItemDisplayName(StrEnum):
    confirm_intro_meeting = "Confirm Intro Meeting"
    complete_intro_meeting = "Complete Intro Meeting"
    confirm_followup_meeting = "Confirm Followup Meeting"
    complete_followup_meeting = "Complete Followup Meeting"
    share_generic_demo = "Share Generic Product Demo"
    share_pricing_information = "Share Pricing Information"
    share_meeting_deck = "Share Meeting Deck"
    share_buyer_specific_demo = "Share Buyer Specific Demo"
    share_product_one_pager = "Share Product One Pager"
    probe_needs = "Probe Needs (Why Anything)"
    probe_pain = "Probe Pain (Why Anything & Why Now)"
    probe_product_solution_fit = "Probe Product Solution Fit (Why Us)"
    probe_timing = "Probe Timing (Why Now)"
    probe_budget = "Probe Budget"

    document_negative_consequences = "Document Business Negative Consequences"
    document_positive_outcomes = "Document Business Positive Outcomes"
    document_key_metrics = "Document Key Metrics"
    document_decision_criteria = "Document Decision Criteria"
    document_product_solution_fit = "Document Product Solution Fit"
    document_seller_competitors = "Document Competitors to Our Product"
    document_buyer_pain_points = "Document Buyer's Pain Points"
    document_buyer_paper_process = "Document Buyer's Paper Process"
    document_buyer_decision_process = "Document Buyer's Decision Process"
    document_buyer_expected_decision_date = "Document Buyer's Expected Decision Date"
    document_buyer_expected_testing_start_date = (
        "Document Buyer's Expected Testing Start Date"
    )
    document_buyer_expected_testing_end_date = (
        "Document Buyer's Expected Testing End Date"
    )
    document_buyer_expected_launch_date = "Document Buyer's Expected Launch Date"
    document_product_solution_differentiation = (
        "Document Product Solution Differentiation"
    )
    document_buyer_budget = "Document Buyer's Budget Considerations"
    document_buyer_expected_implementation_start_date = (
        "Document Buyer's Expected Implementation Start Date"
    )
    identify_champions = "Identify Champions"
    identify_economic_buyer_or_decision_maker = (
        "Identify Economic Buyer or Decision Maker"
    )


INTRO_MEETING_CONFIRMED_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.confirm_intro_meeting,
    sales_action=StandardSalesActionType.INTRO,
    required_activity_types=[SalesActionActivityType.MEETING],
    required_activity_states=[SalesActionActivityState.SCHEDULE_CONFIRMED],
    description="Confirmed an introductory meeting with prospects to establish a foundation for future interactions.",
)

INTRO_MEETING_COMPLETED_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.complete_intro_meeting,
    sales_action=StandardSalesActionType.INTRO,
    required_activity_types=[SalesActionActivityType.MEETING],
    required_activity_states=[SalesActionActivityState.COMPLETED],
    description="Completed an introductory meeting with prospects to establish a foundation for future interactions.",
)

FOLLOWUP_MEETING_SCHEDULE_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.confirm_followup_meeting,
    sales_action=StandardSalesActionType.FOLLOWUP,
    required_activity_types=[SalesActionActivityType.MEETING],
    required_activity_states=[SalesActionActivityState.SCHEDULE_CONFIRMED],
    description="Scheduled a follow-up meeting with prospects to continue the sales process.",
)

FOLLOWUP_MEETING_COMPLETED_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.complete_followup_meeting,
    sales_action=StandardSalesActionType.FOLLOWUP,
    required_activity_types=[SalesActionActivityType.MEETING],
    required_activity_states=[SalesActionActivityState.COMPLETED],
    description="Completed a follow-up meeting with prospects to continue the sales process.",
)


GENERIC_DEMO_SHARING_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.share_generic_demo,
    sales_action=StandardSalesActionType.GENERIC_DEMO_SHARING,
    required_activity_types=[
        SalesActionActivityType.MEETING,
        SalesActionActivityType.EMAIL,
    ],
    description="Presenting or sharing a standard demonstration to introduce solution capabilities, establish credibility, and create initial product interest.",
)

PRICING_SHARING_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.share_pricing_information,
    sales_action=StandardSalesActionType.PRICING_SHARING,
    required_activity_types=[
        SalesActionActivityType.EMAIL,
        SalesActionActivityType.MEETING,
    ],
    description="Sharing pricing information with prospects after establishing value. This critical step should follow proper qualification and pain discovery to ensure price is positioned within the context of ROI and business impact.",
)

MEETING_DECK_SHARING_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.share_meeting_deck,
    sales_action=StandardSalesActionType.MEETING_DECK_SHARING,
    required_activity_types=[
        SalesActionActivityType.MEETING,
        SalesActionActivityType.EMAIL,
    ],
    description="Distributing presentation materials that articulate business value, solution capabilities, and customer success stories. Effective decks align with decision criteria and reinforce key differentiators while providing champions with internal selling tools.",
)

BUYER_SPECIFIC_DEMO_SHARING_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.share_buyer_specific_demo,
    sales_action=StandardSalesActionType.BUYER_SPECIFIC_DEMO_SHARING,
    required_activity_types=[
        SalesActionActivityType.MEETING,
        SalesActionActivityType.EMAIL,
    ],
    description="Tailored demonstration addressing specific pain points and use cases for a particular buyer or organization. Customized demos significantly increase engagement by showing exactly how your solution solves their unique challenges and delivers measurable outcomes.",
)

PRODUCT_ONE_PAGER_SHARING_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.share_product_one_pager,
    sales_action=StandardSalesActionType.PRODUCT_ONE_PAGER_SHARING,
    required_activity_types=[
        SalesActionActivityType.MEETING,
        SalesActionActivityType.EMAIL,
    ],
    description="Providing concise, high-impact documentation that champions can circulate internally. Effective one-pagers translate technical capabilities into business outcomes, helping internal advocates articulate value to other stakeholders in the buying committee.",
)

PROBING_NEEDS_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.probe_needs,
    sales_action=StandardSalesActionType.PROBING_NEEDS,
    required_activity_types=[SalesActionActivityType.MEETING],
    description="Strategic questioning to uncover business requirements and desired outcomes. Effective needs discovery reveals gaps between current state and desired future state, establishing the foundation for relevant solution positioning.",
)

PROBING_PAIN_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.probe_pain,
    sales_action=StandardSalesActionType.PROBING_PAIN,
    required_activity_types=[SalesActionActivityType.MEETING],
    description="Focused exploration of customer challenges, frustrations, and negative business impacts. Identifying and quantifying pain points creates motivation for change and helps establish value metrics for your proposed solution.",
)

PROBING_PRODUCT_SOLUTION_FIT_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.probe_product_solution_fit,
    sales_action=StandardSalesActionType.PROBING_PRODUCT_SOLUTION_FIT,
    required_activity_types=[SalesActionActivityType.MEETING],
    description="Evaluating alignment between customer requirements and solution capabilities. This dialogue confirms relevance, addresses potential implementation concerns, and validates that your offering will deliver the expected business outcomes.",
)

PROBING_TIMING_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.probe_timing,
    sales_action=StandardSalesActionType.PROBING_TIMING,
    required_activity_types=[SalesActionActivityType.MEETING],
    description="Investigating timing factors, deadlines, and organizational readiness for change. Understanding the prospect's decision timeline helps forecast accurately and identify events creating urgency or potential barriers to moving forward.",
)

PROBING_BUDGET_REQUIREMENT = StandardSalesActionRequirement(
    name=CriteriaLibraryItemDisplayName.probe_budget,
    sales_action=StandardSalesActionType.PROBING_BUDGET,
    required_activity_types=[SalesActionActivityType.MEETING],
    description="Exploring financial considerations, investment thresholds, and ROI expectations. Effective budget discussions focus on value rather than cost, helping prospects justify the investment and secure necessary funding approval.",
)

_STANDARD_SALES_ACTION_REQUIREMENT_MAP: frozendict[
    StandardSalesActionType, StandardSalesActionRequirement
] = frozendict(
    {
        StandardSalesActionType.INTRO: INTRO_MEETING_CONFIRMED_REQUIREMENT,
        StandardSalesActionType.FOLLOWUP: FOLLOWUP_MEETING_SCHEDULE_REQUIREMENT,
        StandardSalesActionType.GENERIC_DEMO_SHARING: GENERIC_DEMO_SHARING_REQUIREMENT,
        StandardSalesActionType.PRICING_SHARING: PRICING_SHARING_REQUIREMENT,
        StandardSalesActionType.MEETING_DECK_SHARING: MEETING_DECK_SHARING_REQUIREMENT,
        StandardSalesActionType.BUYER_SPECIFIC_DEMO_SHARING: BUYER_SPECIFIC_DEMO_SHARING_REQUIREMENT,
        StandardSalesActionType.PRODUCT_ONE_PAGER_SHARING: PRODUCT_ONE_PAGER_SHARING_REQUIREMENT,
        StandardSalesActionType.PROBING_NEEDS: PROBING_NEEDS_REQUIREMENT,
        StandardSalesActionType.PROBING_PAIN: PROBING_PAIN_REQUIREMENT,
        StandardSalesActionType.PROBING_PRODUCT_SOLUTION_FIT: PROBING_PRODUCT_SOLUTION_FIT_REQUIREMENT,
        StandardSalesActionType.PROBING_TIMING: PROBING_TIMING_REQUIREMENT,
        StandardSalesActionType.PROBING_BUDGET: PROBING_BUDGET_REQUIREMENT,
    }
)


def get_standard_sales_action_requirement(
    sales_action_type: StandardSalesActionType,
) -> StandardSalesActionRequirement:
    return _STANDARD_SALES_ACTION_REQUIREMENT_MAP[sales_action_type]


def get_standard_sales_action_requirements_by_activity_type(
    activity_type: SalesActionActivityType,
) -> list[StandardSalesActionRequirement]:
    return [
        requirement
        for requirement in _STANDARD_SALES_ACTION_REQUIREMENT_MAP.values()
        if activity_type in requirement.required_activity_types
    ]


# Define common library items for pipeline stages
PIPELINE_STAGE_LIBRARY_ITEMS = [
    CriteriaLibraryItem(
        library_item_id=UUID("0EBABBDE-AE63-44C7-BDB5-7B97348AFEB1"),
        display_name=CriteriaLibraryItemDisplayName.document_negative_consequences,
        description="Document the business negative consequences if buyer does not solve the problem",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.metric,
                        MetricField.business_negative_consequences,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Metric"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("F217A6A5-6BC9-4C2A-9775-43010F2997A2"),
        display_name=CriteriaLibraryItemDisplayName.document_positive_outcomes,
        description="Document the business positive outcomes if buyer solves the problem",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.metric,
                        MetricField.business_positive_outcomes,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Metric"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("9A5BA9F9-1E25-41FE-93ED-17C80B783B39"),
        display_name=CriteriaLibraryItemDisplayName.document_key_metrics,
        description="Quantifiable business outcomes metrics that demonstrate tangible value of a solution to customers.",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.metric,
                        MetricField.metrics,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Metric"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("39E68DF1-78DB-4110-BE72-CBE6E32AA11E"),
        display_name=CriteriaLibraryItemDisplayName.document_product_solution_differentiation,
        description="Document the product solution differentiation - why buyer should choose us over competitors and how our solution is a unique fit for them",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.competition,
                        CompetitionField.solution_differentiation,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Competition"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("6DA1CFF2-A195-4F71-B18B-87D72B11510A"),
        display_name=CriteriaLibraryItemDisplayName.document_seller_competitors,
        description="Document the competitors - who are they and what are their advantages and disadvantages compared to us",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.competition,
                        CompetitionField.competitors,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Competition"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("D05FFDBA-CFEE-4C7E-8D7D-0E3BD9F6575E"),
        display_name=CriteriaLibraryItemDisplayName.document_product_solution_fit,
        description="Document the product solution fit - how our solution aligns with the buyer's needs and challenges",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.decision_criteria,
                        DecisionCriteriaField.product_solution_fit_note,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Decision Criteria"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("7E45B6AD-145D-44F6-B919-AF11257DE60F"),
        display_name=CriteriaLibraryItemDisplayName.document_decision_criteria,
        description="Document the decision criteria - what are the criteria for the buyer to make a decision",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.decision_criteria,
                        DecisionCriteriaField.decision_criteria_items,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Decision Criteria"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("FEEEED01-9EE8-4765-A715-A8971E2C3875"),
        display_name=CriteriaLibraryItemDisplayName.document_buyer_budget,
        description="Document the budget - what is the budget or budget consideration for the buyer to make a decision",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=CompositeFilter(
                any_of=[
                    ValueFilter(
                        field=QualifiedField(
                            path=(
                                PipelineField.decision_criteria,
                                DecisionCriteriaField.budget_amount,
                            )
                        ),
                        operator=MatchOperator.NBLANK,
                    ),
                    ValueFilter(
                        field=QualifiedField(
                            path=(
                                PipelineField.decision_criteria,
                                DecisionCriteriaField.budget_note,
                            )
                        ),
                        operator=MatchOperator.NBLANK,
                    ),
                ],
            ),
        ),
        sales_action_requirement=None,
        tags=["Decision Criteria"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("88165424-BD63-4A7A-9097-683F32613BF4"),
        display_name=CriteriaLibraryItemDisplayName.document_buyer_expected_launch_date,
        description="Document the buyer's expected launch date of the solution",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.decision_process,
                        DecisionProcessField.solution_launches_by,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Decision Process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("69950A44-597D-4BB7-A379-15E905B10FDA"),
        display_name=CriteriaLibraryItemDisplayName.document_buyer_expected_implementation_start_date,
        description="Document the buyer's expected implementation start date",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.decision_process,
                        DecisionProcessField.implementation_starts_by,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Decision Process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("E83517D5-A0BD-47F9-A785-48654A0B697B"),
        display_name=CriteriaLibraryItemDisplayName.document_buyer_expected_decision_date,
        description="Document the buyer's expected date to make a decision on purchasing our product",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.decision_process,
                        DecisionProcessField.decision_by,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Decision Process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("FABE075C-71F5-49F8-B108-3BC8B5C42E22"),
        display_name=CriteriaLibraryItemDisplayName.document_buyer_expected_testing_start_date,
        description="Document the buyer's expected date to start validating the solution effectiveness",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.decision_process,
                        DecisionProcessField.testing_starts_by,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Decision Process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("FA79543E-B3CF-4EA4-8DBF-BE5110F353D0"),
        display_name=CriteriaLibraryItemDisplayName.document_buyer_expected_testing_end_date,
        description="Document the buyer's expected date to end validating the solution effectiveness",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.decision_process,
                        DecisionProcessField.testing_ends_by,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Decision Process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("EB349F59-308C-40AB-8A19-0FE1C633768F"),
        display_name=CriteriaLibraryItemDisplayName.document_buyer_decision_process,
        description="Document the decision process - what are the detailed processes for the buyer to make a decision",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.decision_process,
                        DecisionProcessField.ordered_decision_processes,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Decision Process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("FF77FF03-F7B6-4D53-B3BE-18FBDAD13FEF"),
        display_name=CriteriaLibraryItemDisplayName.document_buyer_pain_points,
        description="Document the buyer's pain points - what are the pain points of the buyer",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.identified_pain,
                        IdentifiedPainField.ordered_pain_items,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Identified Pain"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("6DD6949C-CBED-4158-AB21-5DCBE417E09C"),
        display_name=CriteriaLibraryItemDisplayName.document_buyer_paper_process,
        description="Document the buyer's paper process - what are the contractual and compliance steps for the buyer to make a decision",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=QualifiedField(
                    path=(
                        PipelineField.paper_process,
                        PaperProcessField.ordered_paper_processes,
                    )
                ),
                operator=MatchOperator.NBLANK,
            ),
        ),
        sales_action_requirement=None,
        tags=["Paper Process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("*************-437E-8BE8-263A58B6F89E"),
        display_name=CriteriaLibraryItemDisplayName.identify_champions,
        description="Identify the champions - who are the champions from the buyer that will be involved to influence the decision",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=ValueFilter(
                field=FieldReference(
                    relationship_id=PipelineRelationship.pipeline__from__contact_pipeline_role,
                    field=QualifiedField(path=(ContactPipelineRoleField.role_types,)),
                ),
                operator=MatchOperator.CONTAINS,
                value=ContactPipelineRoleType.CHAMPION,
            ),
        ),
        sales_action_requirement=None,
        tags=["Champion"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("1CEEA355-DD16-454D-ADB9-D8D74746AEB7"),
        display_name=CriteriaLibraryItemDisplayName.identify_economic_buyer_or_decision_maker,
        description="Identify the economic buyer or decision maker - who is the economic buyer or decision maker from the buyer that will be involved to make the purchase decision",
        filter_spec=FilterSpec(
            primary_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            filter=CompositeFilter(
                any_of=[
                    ValueFilter(
                        field=FieldReference(
                            relationship_id=PipelineRelationship.pipeline__from__contact_pipeline_role,
                            field=QualifiedField(
                                path=(ContactPipelineRoleField.role_types,)
                            ),
                        ),
                        operator=MatchOperator.CONTAINS,
                        value=ContactPipelineRoleType.ECONOMIC_BUYER,
                    ),
                    ValueFilter(
                        field=FieldReference(
                            relationship_id=PipelineRelationship.pipeline__from__contact_pipeline_role,
                            field=QualifiedField(
                                path=(ContactPipelineRoleField.role_types,)
                            ),
                        ),
                        operator=MatchOperator.CONTAINS,
                        value=ContactPipelineRoleType.DECISION_MAKER,
                    ),
                ],
            ),
        ),
        sales_action_requirement=None,
        tags=["Economic Buyer"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("D5F2A307-E124-46A1-A82F-B81BEF32F7EA"),
        display_name=INTRO_MEETING_CONFIRMED_REQUIREMENT.name,
        description=INTRO_MEETING_CONFIRMED_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=INTRO_MEETING_CONFIRMED_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("92BF36FC-E2B6-4A91-A29E-46FAE0BE2E0E"),
        display_name=INTRO_MEETING_COMPLETED_REQUIREMENT.name,
        description=INTRO_MEETING_COMPLETED_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=INTRO_MEETING_COMPLETED_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("BE0DE9B9-3B64-401C-89EF-D04B00DD83C4"),
        display_name=FOLLOWUP_MEETING_SCHEDULE_REQUIREMENT.name,
        description=FOLLOWUP_MEETING_SCHEDULE_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=FOLLOWUP_MEETING_SCHEDULE_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("B50B63B3-CA5B-4D99-A9E8-18A8BFD3AD8E"),
        display_name=FOLLOWUP_MEETING_COMPLETED_REQUIREMENT.name,
        description=FOLLOWUP_MEETING_COMPLETED_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=FOLLOWUP_MEETING_COMPLETED_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("04E89164-C226-40D9-9B4B-83D153D57E45"),
        display_name=GENERIC_DEMO_SHARING_REQUIREMENT.name,
        description=GENERIC_DEMO_SHARING_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=GENERIC_DEMO_SHARING_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("30061F9B-94FA-4D41-ABA4-6EC09AACBB36"),
        display_name=PRICING_SHARING_REQUIREMENT.name,
        description=PRICING_SHARING_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=PRICING_SHARING_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("ED320867-1248-4117-AC25-B9506D3B6F01"),
        display_name=MEETING_DECK_SHARING_REQUIREMENT.name,
        description=MEETING_DECK_SHARING_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=MEETING_DECK_SHARING_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("F46D18FC-249D-48C2-9897-DEC7ED95F499"),
        display_name=BUYER_SPECIFIC_DEMO_SHARING_REQUIREMENT.name,
        description=BUYER_SPECIFIC_DEMO_SHARING_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=BUYER_SPECIFIC_DEMO_SHARING_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("ED2B3545-4D4C-42A0-9204-F68D5B8DB905"),
        display_name=PRODUCT_ONE_PAGER_SHARING_REQUIREMENT.name,
        description=PRODUCT_ONE_PAGER_SHARING_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=PRODUCT_ONE_PAGER_SHARING_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("C46FDB80-7009-4BBA-AB11-C17A6041BF82"),
        display_name=PROBING_NEEDS_REQUIREMENT.name,
        description=PROBING_NEEDS_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=PROBING_NEEDS_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("C9EA326F-E822-4859-852C-DCA42F0D9672"),
        display_name=PROBING_PAIN_REQUIREMENT.name,
        description=PROBING_PAIN_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=PROBING_PAIN_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("151DD089-7E77-4C08-B9FC-81F76613101B"),
        display_name=PROBING_PRODUCT_SOLUTION_FIT_REQUIREMENT.name,
        description=PROBING_PRODUCT_SOLUTION_FIT_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=PROBING_PRODUCT_SOLUTION_FIT_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("92438590-A76A-4DD9-9509-A7926EA2D713"),
        display_name=PROBING_TIMING_REQUIREMENT.name,
        description=PROBING_TIMING_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=PROBING_TIMING_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
    CriteriaLibraryItem(
        library_item_id=UUID("4849C322-EF53-49E2-8B04-CA3E52E22376"),
        display_name=PROBING_BUDGET_REQUIREMENT.name,
        description=PROBING_BUDGET_REQUIREMENT.description,
        filter_spec=None,
        sales_action_requirement=PROBING_BUDGET_REQUIREMENT,
        tags=["decision process"],
        is_ai_assisted=True,
        applicable_criteria_types=[CriteriaTypeEnum.STAGE_EXIT_CRITERIA],
    ),
]


# Combine all library items
ALL_LIBRARY_ITEMS = PIPELINE_STAGE_LIBRARY_ITEMS

# Dictionary of all library items by ID for direct lookup
CRITERIA_LIBRARY_ITEMS: dict[UUID, CriteriaLibraryItem] = {
    item.library_item_id: item for item in ALL_LIBRARY_ITEMS
}

# Dictionary of library items organized by stage type for filtering
CRITERIA_LIBRARY_ITEMS_BY_STAGE_TYPE: dict[StageType, list[CriteriaLibraryItem]] = {
    StageType.PIPELINE_STAGE: PIPELINE_STAGE_LIBRARY_ITEMS,
}

MEETING_DELIMITER = "--- MEETING"
MEETING_DELIMITER_END = "--- END OF MEETING ---"
