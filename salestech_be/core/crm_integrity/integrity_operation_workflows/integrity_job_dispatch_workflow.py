from temporalio import workflow
from temporalio.common import WorkflowIDReusePolicy
from temporalio.exceptions import (
    ChildWorkflowError,
    WorkflowAlreadyStartedError,
)
from temporalio.workflow import ParentClosePolicy

with workflow.unsafe.imports_passed_through():
    import asyncio
    from uuid import UUID

    from salestech_be.core.crm_integrity.atomic_operation_activities.integrity_job_activity import (
        IntegrityJobActivity,
    )
    from salestech_be.core.crm_integrity.integrity_operation_workflows.add_account_to_pipeline_workflow import (
        AddAccountToPipelineWorkflow,
    )
    from salestech_be.core.crm_integrity.integrity_operation_workflows.archive_account_workflow import (
        ArchiveAccountWorkflow,
    )
    from salestech_be.core.crm_integrity.integrity_operation_workflows.archive_contact_workflow import (
        ArchiveContactWorkflow,
    )
    from salestech_be.core.crm_integrity.integrity_operation_workflows.merge_accounts_workflow import (
        MergeAccountsWorkflow,
    )
    from salestech_be.core.crm_integrity.integrity_operation_workflows.merge_contacts_workflow import (
        MergeContactsWorkflow,
    )
    from salestech_be.core.crm_integrity.integrity_operation_workflows.move_contact_email_to_account_workflow import (
        MoveContactEmailToAccountWorkflow,
    )
    from salestech_be.core.crm_integrity.integrity_operation_workflows.move_contact_email_to_contact_workflow import (
        MoveContactEmailToContactWorkflow,
    )
    from salestech_be.core.crm_integrity.integrity_operation_workflows.move_contact_to_account_workflow import (
        MoveContactToAccountWorkflow,
    )
    from salestech_be.core.crm_integrity.integrity_operation_workflows.remove_contact_email_from_account_workflow import (
        RemoveContactEmailFromAccountWorkflow,
    )
    from salestech_be.core.crm_integrity.integrity_operation_workflows.remove_contact_email_from_contact_workflow import (
        RemoveContactEmailFromContactWorkflow,
    )
    from salestech_be.core.crm_integrity.integrity_operation_workflows.remove_contact_from_account_workflow import (
        RemoveContactFromAccountWorkflow,
    )
    from salestech_be.core.crm_integrity.types.activity_params import (
        AddAccountToPipelineParam,
        ArchiveAccountParam,
        ArchiveContactParam,
        BaseIntegrityJobParam,
        MergeAccountsParam,
        MergeContactsParam,
        MoveContactEmailToAccountParam,
        MoveContactEmailToContactParam,
        MoveContactToAccountParam,
        RemoveContactEmailFromAccountParam,
        RemoveContactEmailFromContactParam,
        RemoveContactFromAccountParam,
        UpdateIntegrityJobErrorParam,
        WorkflowEntrypointT,
        WorkflowParamT,
    )
    from salestech_be.core.crm_integrity.types.workflow_default_config import (
        DEFAULT_ACTIVITY_TIMEOUT,
        DEFAULT_RETRY_POLICY,
    )
    from salestech_be.db.models.crm_integrity import (
        CRMIntegrityJobContextualParam,
        CRMIntegrityJobUserOption,
        CRMSubDomain,
        IntegrityJobName,
        MoveContactEmailToAccountContextualParam,
        MoveContactEmailToContactContextualParam,
        MoveContactToAccountContextualParam,
        MoveContactToAccountUserOption,
        RemoveContactEmailFromAccountContextualParam,
        RemoveContactEmailFromContactContextualParam,
    )
    from salestech_be.integrations.temporal.config import INTEGRITY_JOB_TASK_QUEUE
    from salestech_be.settings import settings
    from salestech_be.util.validation import cast_or_error, not_none


def get_integrity_job_dispatch_workflow_id(organization_id: UUID) -> str:
    return f"crm_integrity_job_dispatcher_org_{organization_id}"


def get_integrity_job_workflow_id(job_param: BaseIntegrityJobParam) -> str:
    return f"crm_integrity_job_{job_param.integrity_job_name}_{job_param.job_id}_{job_param.retry_count}"


def get_workflow_entrypoint_and_params(  # noqa: C901
    integrity_job_name: IntegrityJobName,
    job_id: UUID,
    retry_count: int,
    src_entity_id: UUID,
    dest_entity_id: UUID | None,
    user_id: UUID,
    organization_id: UUID,
    user_choices: dict[CRMSubDomain, CRMIntegrityJobUserOption],
    contextual_param: CRMIntegrityJobContextualParam | None = None,
) -> tuple[WorkflowEntrypointT, WorkflowParamT]:
    workflow_entrypoint: WorkflowEntrypointT
    workflow_param: WorkflowParamT

    match integrity_job_name:
        case IntegrityJobName.MERGE_ACCOUNTS:
            workflow_entrypoint = MergeAccountsWorkflow.run
            workflow_param = MergeAccountsParam(
                job_id=job_id,
                retry_count=retry_count,
                src_account_id=src_entity_id,
                dest_account_id=not_none(dest_entity_id),
                user_id=user_id,
                organization_id=organization_id,
            )
        case IntegrityJobName.MERGE_CONTACTS:
            workflow_entrypoint = MergeContactsWorkflow.run
            workflow_param = MergeContactsParam(
                job_id=job_id,
                retry_count=retry_count,
                src_contact_id=src_entity_id,
                dest_contact_id=not_none(dest_entity_id),
                user_id=user_id,
                organization_id=organization_id,
            )
        case IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT:
            workflow_entrypoint = MoveContactToAccountWorkflow.run
            workflow_param = MoveContactToAccountParam(
                job_id=job_id,
                retry_count=retry_count,
                contact_id=src_entity_id,
                src_account_id=cast_or_error(
                    contextual_param,
                    MoveContactToAccountContextualParam,
                ).src_account_id,
                dest_account_id=not_none(dest_entity_id),
                user_id=user_id,
                organization_id=organization_id,
                user_choices={
                    subdomain: cast_or_error(
                        choice,
                        MoveContactToAccountUserOption,
                    )
                    for subdomain, choice in user_choices.items()
                },
                title=cast_or_error(
                    contextual_param,
                    MoveContactToAccountContextualParam,
                ).title,
                department=cast_or_error(
                    contextual_param,
                    MoveContactToAccountContextualParam,
                ).department,
            )
        case IntegrityJobName.REMOVE_CONTACT_FROM_ACCOUNT:
            workflow_entrypoint = RemoveContactFromAccountWorkflow.run
            workflow_param = RemoveContactFromAccountParam(
                job_id=job_id,
                retry_count=retry_count,
                contact_id=src_entity_id,
                account_id=not_none(dest_entity_id),
                user_id=user_id,
                organization_id=organization_id,
            )
        case IntegrityJobName.ARCHIVE_CONTACT:
            workflow_entrypoint = ArchiveContactWorkflow.run
            workflow_param = ArchiveContactParam(
                job_id=job_id,
                retry_count=retry_count,
                archive_contact_id=src_entity_id,
                user_id=user_id,
                organization_id=organization_id,
            )
        case IntegrityJobName.ARCHIVE_ACCOUNT:
            workflow_entrypoint = ArchiveAccountWorkflow.run
            workflow_param = ArchiveAccountParam(
                job_id=job_id,
                retry_count=retry_count,
                archive_account_id=src_entity_id,
                user_id=user_id,
                organization_id=organization_id,
            )
        case IntegrityJobName.MOVE_CONTACT_EMAIL_TO_CONTACT:
            workflow_entrypoint = MoveContactEmailToContactWorkflow.run
            workflow_param = MoveContactEmailToContactParam(
                job_id=job_id,
                retry_count=retry_count,
                contact_email_id=src_entity_id,
                src_contact_id=cast_or_error(
                    contextual_param,
                    MoveContactEmailToContactContextualParam,
                ).src_contact_id,
                dest_contact_id=not_none(dest_entity_id),
                user_id=user_id,
                organization_id=organization_id,
                remove_contact_account_association_if_last=cast_or_error(
                    contextual_param,
                    MoveContactEmailToContactContextualParam,
                ).remove_contact_account_association_if_last,
                create_contact_account_association_if_missing=cast_or_error(
                    contextual_param,
                    MoveContactEmailToContactContextualParam,
                ).create_contact_account_association_if_missing,
            )
        case IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_CONTACT:
            workflow_entrypoint = RemoveContactEmailFromContactWorkflow.run
            workflow_param = RemoveContactEmailFromContactParam(
                job_id=job_id,
                retry_count=retry_count,
                contact_email_id=src_entity_id,
                contact_id=not_none(dest_entity_id),
                user_id=user_id,
                organization_id=organization_id,
                remove_contact_account_association_if_last=cast_or_error(
                    contextual_param,
                    RemoveContactEmailFromContactContextualParam,
                ).remove_contact_account_association_if_last,
            )
        case IntegrityJobName.MOVE_CONTACT_EMAIL_TO_ACCOUNT:
            workflow_entrypoint = MoveContactEmailToAccountWorkflow.run
            workflow_param = MoveContactEmailToAccountParam(
                job_id=job_id,
                retry_count=retry_count,
                contact_email_id=src_entity_id,
                contact_id=cast_or_error(
                    contextual_param,
                    MoveContactEmailToAccountContextualParam,
                ).contact_id,
                src_account_id=cast_or_error(
                    contextual_param,
                    MoveContactEmailToAccountContextualParam,
                ).src_account_id,
                dest_account_id=not_none(dest_entity_id),
                user_id=user_id,
                organization_id=organization_id,
            )
        case IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_ACCOUNT:
            workflow_entrypoint = RemoveContactEmailFromAccountWorkflow.run
            workflow_param = RemoveContactEmailFromAccountParam(
                job_id=job_id,
                retry_count=retry_count,
                contact_email_id=src_entity_id,
                contact_id=cast_or_error(
                    contextual_param,
                    RemoveContactEmailFromAccountContextualParam,
                ).contact_id,
                account_id=not_none(dest_entity_id),
                user_id=user_id,
                organization_id=organization_id,
            )
        case IntegrityJobName.ADD_ACCOUNT_TO_PIPELINE:
            workflow_entrypoint = AddAccountToPipelineWorkflow.run
            workflow_param = AddAccountToPipelineParam(
                job_id=job_id,
                retry_count=retry_count,
                account_id=src_entity_id,
                pipeline_id=not_none(dest_entity_id),
                user_id=user_id,
                organization_id=organization_id,
            )
        case _:
            raise NotImplementedError(
                f"integrity job name {integrity_job_name} not implemented"
            )

    return workflow_entrypoint, workflow_param


@workflow.defn
class IntegrityJobDispatchWorkflow:
    """
    Entrypoint for integrity job dispatching.

    After evaluating params, will call the appropriate workflow to run the job.
    """

    MAX_TIME_TO_WAIT_FOR_NEW_EVENT: float = 1800.0  # 30 minutes

    def __init__(self) -> None:
        self.event_queue: asyncio.Queue[BaseIntegrityJobParam] = asyncio.Queue()
        self.max_concurrency: int = settings.crm_integrity_max_concurrent_running_jobs
        self.semaphore: asyncio.Semaphore = asyncio.Semaphore(self.max_concurrency)
        self.running_tasks: set[asyncio.Task[None]] = set()

    async def process_single_job_param(self, job_param: BaseIntegrityJobParam) -> None:
        async with self.semaphore:
            try:
                workflow_entrypoint, workflow_param = (
                    get_workflow_entrypoint_and_params(
                        integrity_job_name=job_param.integrity_job_name,
                        job_id=job_param.job_id,
                        retry_count=job_param.retry_count,
                        src_entity_id=job_param.src_entity_id,
                        dest_entity_id=job_param.dest_entity_id,
                        user_id=job_param.user_id,
                        organization_id=job_param.organization_id,
                        user_choices=job_param.user_choices,
                        contextual_param=job_param.contextual_param,
                    )
                )

                await workflow.execute_child_workflow(
                    workflow_entrypoint,
                    workflow_param,
                    id=get_integrity_job_workflow_id(job_param),
                    task_queue=INTEGRITY_JOB_TASK_QUEUE,
                    id_reuse_policy=WorkflowIDReusePolicy.ALLOW_DUPLICATE_FAILED_ONLY,
                    parent_close_policy=ParentClosePolicy.ABANDON,
                )
            except Exception as e:
                if isinstance(e, WorkflowAlreadyStartedError):
                    workflow.logger.info(
                        f"Child workflow already started for job with params: {job_param}. Skipping..."
                    )
                elif isinstance(e, ChildWorkflowError):
                    update_integrity_job_error_param = UpdateIntegrityJobErrorParam(
                        job_id=job_param.job_id,
                        namespace=e.namespace,
                        message=e.message,
                        workflow_id=e.workflow_id,
                        workflow_type=e.workflow_type,
                        workflow_run_id=e.run_id,
                        organization_id=job_param.organization_id,
                        user_id=job_param.user_id,
                    )
                    await workflow.execute_activity_method(
                        IntegrityJobActivity.update_integrity_job_error,
                        update_integrity_job_error_param,
                        start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                        retry_policy=DEFAULT_RETRY_POLICY,
                    )
                    workflow.logger.error(
                        f"Error processing child workflow with params: {job_param}. Error: {update_integrity_job_error_param.model_dump()}"
                    )
                else:
                    workflow.logger.error(
                        f"Error processing job with params: {job_param}. Error: {e}"
                    )

    @workflow.run
    async def run(
        self, init_job_params: list[BaseIntegrityJobParam] | None = None
    ) -> None:
        async def post_run() -> None:
            # wait for all running tasks to finish (dispatched and waiting for semaphore)
            if self.running_tasks:
                await asyncio.wait(
                    self.running_tasks, return_when=asyncio.ALL_COMPLETED
                )

            # if more jobs are coming during finish running tasks, collect them and start
            # a new workflow instance to process them after finishing running tasks
            remaining_jobs: list[BaseIntegrityJobParam] = []
            while not self.event_queue.empty():
                remaining_jobs.append(self.event_queue.get_nowait())

            if remaining_jobs:
                await workflow.wait_condition(workflow.all_handlers_finished)
                workflow.continue_as_new(arg=remaining_jobs)

        try:
            for job_param in init_job_params or []:
                await self.event_queue.put(job_param)

            while True:
                try:
                    job_param = await asyncio.wait_for(
                        self.event_queue.get(),
                        timeout=self.MAX_TIME_TO_WAIT_FOR_NEW_EVENT,
                    )

                    task = asyncio.create_task(self.process_single_job_param(job_param))
                    self.running_tasks.add(task)
                    task.add_done_callback(self.running_tasks.discard)

                except TimeoutError:
                    workflow.logger.info(
                        f"No events received in {self.MAX_TIME_TO_WAIT_FOR_NEW_EVENT} seconds. "
                        "Exiting dispatching workflow and initiating post run procedure."
                    )
                    await post_run()
                    break
        except asyncio.CancelledError:
            workflow.logger.info(
                "Received external cancel signal. Exiting dispatching workflow and initiating post run procedure."
            )
            await post_run()
            raise

    @workflow.signal
    async def enqueue_job_param(self, job_param: BaseIntegrityJobParam) -> None:
        await self.event_queue.put(job_param)
