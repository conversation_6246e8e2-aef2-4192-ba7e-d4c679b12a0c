from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.crm_integrity.atomic_operation_activities.integrity_job_activity import (
        IntegrityJobActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.intelligence_related_activity import (
        IntelligenceRelatedActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.pipeline_related_activity import (
        PipelineRelatedActivity,
    )
    from salestech_be.core.crm_integrity.event_processors.processor_activity import (
        data_operation_event_processor_activity,
    )
    from salestech_be.core.crm_integrity.types.activity_params import (
        AddAccountToPipelineParam,
        DataAdditionParam,
        GetAccountPipelinesParams,
        IntegrityJobEntityDescriptor,
        PipelineIntelActivityJobParams,
        RequireOrFreeLocksForEntitiesParam,
        UpdateIntegrityJobParam,
    )
    from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
        get_affected_sub_domains_by_integrity_job_name,
    )
    from salestech_be.core.crm_integrity.types.workflow_default_config import (
        DEFAULT_ACTIVITY_TIMEOUT,
        DEFAULT_RETRY_POLICY,
    )
    from salestech_be.db.models.crm_integrity import (
        EntityType,
        IntegrityJobName,
        JobStatus,
    )


@workflow.defn
class AddAccountToPipelineWorkflow:
    @workflow.run
    async def run(self, param: AddAccountToPipelineParam) -> None:
        try:
            await workflow.execute_activity_method(
                IntegrityJobActivity.acquire_locks_for_entities,
                RequireOrFreeLocksForEntitiesParam(
                    job_id=param.job_id,
                    entity_descriptors=[
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.ACCOUNT,
                            entity_id=param.account_id,
                        ),
                    ],
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.RUNNING,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            pipelines = await workflow.execute_activity_method(
                PipelineRelatedActivity.list_pipelines_by_account_id,
                GetAccountPipelinesParams(
                    account_id=param.account_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            if len(pipelines) == 1 and pipelines[0].id == param.pipeline_id:
                # only move data if the pipeline is the only pipeline for the account
                for subdomain in get_affected_sub_domains_by_integrity_job_name(
                    integrity_job_name=IntegrityJobName.ADD_ACCOUNT_TO_PIPELINE
                ):
                    await workflow.execute_activity_method(
                        data_operation_event_processor_activity,
                        args=(
                            IntegrityJobName.ADD_ACCOUNT_TO_PIPELINE,
                            subdomain,
                            DataAdditionParam(
                                integrity_job_id=param.job_id,
                                src_entity_id=param.account_id,
                                dest_entity_id=param.pipeline_id,
                                user_id=param.user_id,
                                organization_id=param.organization_id,
                            ),
                            None,
                        ),
                        start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                        retry_policy=DEFAULT_RETRY_POLICY,
                    )

                await workflow.execute_activity_method(
                    IntelligenceRelatedActivity.pipeline_intel_activity_job,
                    PipelineIntelActivityJobParams(
                        organization_id=param.organization_id,
                        pipeline_ids={param.pipeline_id},
                    ),
                    start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=DEFAULT_RETRY_POLICY,
                )

            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.SUCCESS,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

        except Exception:
            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.FAIL,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            raise
        finally:
            await workflow.execute_activity_method(
                IntegrityJobActivity.free_locks_for_entities,
                RequireOrFreeLocksForEntitiesParam(
                    job_id=param.job_id,
                    entity_descriptors=[
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.ACCOUNT,
                            entity_id=param.account_id,
                        ),
                    ],
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
