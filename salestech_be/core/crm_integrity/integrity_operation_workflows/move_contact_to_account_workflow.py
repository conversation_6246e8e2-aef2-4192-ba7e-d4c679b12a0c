from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    from temporalio import workflow

    from salestech_be.core.crm_integrity.atomic_operation_activities.associate_contact_with_account_activity import (
        AssociateContactWithAccountActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.contact_email_related_activity import (
        ContactEmailRelatedActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.integrity_job_activity import (
        IntegrityJobActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.intelligence_related_activity import (
        IntelligenceRelatedActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.move_pipeline_to_alternative_contact_activity import (
        MovePipelineToAlternativeContactActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.unassociate_contact_with_account_activity import (
        UnassociateContactWithAccountActivity,
    )
    from salestech_be.core.crm_integrity.event_processors.processor_activity import (
        data_operation_event_processor_activity,
    )
    from salestech_be.core.crm_integrity.types.activity_params import (
        AssociateSingleContactWithAccountParam,
        DataMovementParam,
        IntegrityJobEntityDescriptor,
        IntegrityJobTriggerNotificationParams,
        MoveContactEmailsFromContactToAccountParam,
        MoveContactToAccountParam,
        MovePipelineToAlternativeContactParam,
        PipelineIntelActivityJobParams,
        RequireOrFreeLocksForEntitiesParam,
        UnassociateSingleContactWithSingleAccountParam,
        UpdateIntegrityJobParam,
    )
    from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
        get_affected_sub_domains_by_integrity_job_name,
    )
    from salestech_be.core.crm_integrity.types.workflow_default_config import (
        DEFAULT_ACTIVITY_TIMEOUT,
        DEFAULT_RETRY_POLICY,
    )
    from salestech_be.core.crm_integrity.utils_activities.send_notification_activity import (
        crm_data_integrity_job_send_notification_activity,
    )
    from salestech_be.db.models.crm_integrity import (
        EntityType,
        IntegrityJobName,
        IntegrityJobType,
        JobStatus,
    )


@workflow.defn
class MoveContactToAccountWorkflow:
    """
    This workflow is used to move a contact that already associates with an account,
    to a different account.

    Consequences:
    * The old contact->src_account association will be removed.
    * if pipelines associated with the contact as primary,
        they will be moved to another contact in the src_account.
    * The new contact->dest_account association will be created.

    NOTE:
    This workflow requires:
        * The src_account has at least one more contact to be primarily associated with the existing
            pipelines that are associating with the contact.
        * The contact and dest_account has no existing associations.
    """

    @workflow.run
    async def run(self, param: MoveContactToAccountParam) -> None:
        entity_descriptors = [
            IntegrityJobEntityDescriptor(
                entity_type=EntityType.CONTACT,
                entity_id=param.contact_id,
            ),
            IntegrityJobEntityDescriptor(
                entity_type=EntityType.ACCOUNT,
                entity_id=param.dest_account_id,
                is_dest_account_in_move_contact_to_account_job=True,
            ),
        ]
        if param.src_account_id:
            entity_descriptors.append(
                IntegrityJobEntityDescriptor(
                    entity_type=EntityType.ACCOUNT,
                    entity_id=param.src_account_id,
                )
            )

        try:
            await workflow.execute_activity_method(
                IntegrityJobActivity.acquire_locks_for_entities,
                RequireOrFreeLocksForEntitiesParam(
                    job_id=param.job_id,
                    entity_descriptors=entity_descriptors,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.RUNNING,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.MOVE,
                    integrity_job_status=JobStatus.RUNNING,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=param.contact_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=param.dest_account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"running_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.CONTACT,
                    redirection_entity_id=param.contact_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            if param.src_account_id:
                pipelines = await workflow.execute_activity_method(
                    MovePipelineToAlternativeContactActivity.move_pipeline_to_alternative_contact,
                    MovePipelineToAlternativeContactParam(
                        integrity_job_id=param.job_id,
                        src_account_id=param.src_account_id,
                        moving_contact_id=param.contact_id,
                        user_id=param.user_id,
                        organization_id=param.organization_id,
                    ),
                    start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=DEFAULT_RETRY_POLICY,
                )

                await workflow.execute_activity_method(
                    AssociateContactWithAccountActivity.copy_single_contact_association_from_account_to_account,
                    AssociateSingleContactWithAccountParam(
                        integrity_job_id=param.job_id,
                        contact_id=param.contact_id,
                        src_account_id=param.src_account_id,
                        dest_account_id=param.dest_account_id,
                        user_id=param.user_id,
                        organization_id=param.organization_id,
                    ),
                    start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=DEFAULT_RETRY_POLICY,
                )

                await workflow.execute_activity_method(
                    ContactEmailRelatedActivity.move_contact_emails_from_contact_to_account,
                    MoveContactEmailsFromContactToAccountParam(
                        integrity_job_id=param.job_id,
                        contact_id=param.contact_id,
                        src_account_id=param.src_account_id,
                        dest_account_id=param.dest_account_id,
                        user_id=param.user_id,
                        organization_id=param.organization_id,
                    ),
                    start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=DEFAULT_RETRY_POLICY,
                )

                await workflow.execute_activity_method(
                    UnassociateContactWithAccountActivity.unassociate_single_contact_with_single_account,
                    UnassociateSingleContactWithSingleAccountParam(
                        integrity_job_id=param.job_id,
                        contact_id=param.contact_id,
                        account_id=param.src_account_id,
                        user_id=param.user_id,
                        organization_id=param.organization_id,
                    ),
                    start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=DEFAULT_RETRY_POLICY,
                )

                await workflow.execute_activity_method(
                    IntelligenceRelatedActivity.pipeline_intel_activity_job,
                    PipelineIntelActivityJobParams(
                        organization_id=param.organization_id,
                        pipeline_ids={p.id for p in pipelines},
                    ),
                    start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=DEFAULT_RETRY_POLICY,
                )

            else:
                # orphan contact won't have any account associations, contact email account associations or pipelines to move
                # hence, only need to create the new contact -> dest_account association here
                await workflow.execute_activity_method(
                    AssociateContactWithAccountActivity.copy_single_contact_association_from_account_to_account,
                    AssociateSingleContactWithAccountParam(
                        integrity_job_id=param.job_id,
                        contact_id=param.contact_id,
                        src_account_id=None,
                        dest_account_id=param.dest_account_id,
                        user_id=param.user_id,
                        organization_id=param.organization_id,
                        title=param.title,
                        department=param.department,
                    ),
                    start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=DEFAULT_RETRY_POLICY,
                )

            for subdomain in get_affected_sub_domains_by_integrity_job_name(
                integrity_job_name=IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT
            ):
                await workflow.execute_activity_method(
                    data_operation_event_processor_activity,
                    args=(
                        IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT,
                        subdomain,
                        DataMovementParam(
                            integrity_job_id=param.job_id,
                            user_id=param.user_id,
                            organization_id=param.organization_id,
                            move_entity_id=param.contact_id,
                            src_entity_id=param.src_account_id,
                            dest_entity_id=param.dest_account_id,
                        ),
                        param.user_choices.get(subdomain),
                    ),
                    start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=DEFAULT_RETRY_POLICY,
                )

            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.SUCCESS,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.MOVE,
                    integrity_job_status=JobStatus.SUCCESS,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=param.contact_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=param.dest_account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"success_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.CONTACT,
                    redirection_entity_id=param.contact_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

        except Exception:
            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.FAIL,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.MOVE,
                    integrity_job_status=JobStatus.FAIL,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=param.contact_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=param.dest_account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"fail_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.CONTACT,
                    redirection_entity_id=param.contact_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
            raise
        finally:
            await workflow.execute_activity_method(
                IntegrityJobActivity.free_locks_for_entities,
                RequireOrFreeLocksForEntitiesParam(
                    job_id=param.job_id,
                    entity_descriptors=entity_descriptors,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
