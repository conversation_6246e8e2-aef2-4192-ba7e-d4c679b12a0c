from typing import Any

from salestech_be.core.activity.service.activity_data_integrity_service import (
    get_activity_data_integrity_service,
)
from salestech_be.core.activity.service.activity_service import (
    get_activity_service_general,
)
from salestech_be.core.calendar.crm_integrity_event_processors.account_merge import (
    CalendarAccountMergeEventProcessor,
)
from salestech_be.core.calendar.crm_integrity_event_processors.contact_email_account_move import (
    CalendarContactEmailAccountMoveEventProcessor,
)
from salestech_be.core.calendar.crm_integrity_event_processors.contact_email_account_remove import (
    CalendarContactEmailAccountRemoveEventProcessor,
)
from salestech_be.core.calendar.crm_integrity_event_processors.contact_email_move import (
    CalendarContactEmailMoveEventProcessor,
)
from salestech_be.core.calendar.crm_integrity_event_processors.contact_merge import (
    CalendarContactMergeEventProcessor,
)
from salestech_be.core.calendar.crm_integrity_event_processors.contact_move import (
    CalendarContactMoveEventProcessor,
)
from salestech_be.core.calendar.crm_integrity_event_processors.contact_remove import (
    CalendarContactRemoveEventProcessor,
)
from salestech_be.core.calendar.user_calendar_data_integrity_service import (
    get_user_calendar_data_integrity_service,
)
from salestech_be.core.contact.service.contact_resolve_service import (
    get_contact_resolve_service,
)
from salestech_be.core.contact.service.contact_service import get_contact_service
from salestech_be.core.crm_integrity.event_processors.abstract_processor import (
    AbstractDataOperationProcessor,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
    DataAdditionParam,
    DataArchivalParam,
    DataMovementParam,
    DataRemovalParam,
    DataReplacementParam,
)
from salestech_be.core.domain_object_list.crm_integrity_event_processors.account_merge import (
    DomainObjectListAccountMergeEventProcessor,
)
from salestech_be.core.domain_object_list.crm_integrity_event_processors.contact_merge import (
    DomainObjectListContactMergeEventProcessor,
)
from salestech_be.core.domain_object_list.service.domain_object_list_query_service import (
    get_domain_object_list_query_service_by_db_engine,
)
from salestech_be.core.domain_object_list.service.domain_object_list_service import (
    get_domain_object_list_service,
)
from salestech_be.core.email.crm_integrity_event_processors.account_add_to_pipeline import (
    EmailAccountAddToPipelineEventProcessor,
)
from salestech_be.core.email.crm_integrity_event_processors.account_archive import (
    EmailAccountArchiveEventProcessor,
)
from salestech_be.core.email.crm_integrity_event_processors.account_merge import (
    EmailAccountMergeEventProcessor,
)
from salestech_be.core.email.crm_integrity_event_processors.archive_contact import (
    EmailArchiveContactEventProcessor,
)
from salestech_be.core.email.crm_integrity_event_processors.contact_email_account_move import (
    EmailContactEmailAccountMoveEventProcessor,
)
from salestech_be.core.email.crm_integrity_event_processors.contact_email_account_remove import (
    EmailContactEmailAccountRemoveEventProcessor,
)
from salestech_be.core.email.crm_integrity_event_processors.contact_email_move import (
    EmailContactEmailMoveEventProcessor,
)
from salestech_be.core.email.crm_integrity_event_processors.contact_email_remove import (
    EmailContactEmailRemoveEventProcessor,
)
from salestech_be.core.email.crm_integrity_event_processors.contact_merge import (
    EmailContactMergeEventProcessor,
)
from salestech_be.core.email.crm_integrity_event_processors.contact_move import (
    EmailContactMoveEventProcessor,
)
from salestech_be.core.email.crm_integrity_event_processors.contact_remove import (
    EmailContactRemoveEventProcessor,
)
from salestech_be.core.email.global_email.global_thread_query_service import (
    get_global_thread_query_service,
)
from salestech_be.core.email.global_email.global_thread_resolve_service import (
    get_global_thread_resolve_service,
)
from salestech_be.core.email.insight.email_insight_service import (
    get_email_insight_service_by_db,
)
from salestech_be.core.email.service.email_data_integrity_service import (
    get_email_data_integrity_service,
)
from salestech_be.core.email.service.message_service import (
    get_message_service_by_db_engine,
)
from salestech_be.core.email.service.message_service_ext import (
    get_message_service_ext,
)
from salestech_be.core.meeting.crm_integrity_event_processors.account_add_to_pipeline import (
    MeetingAccountAddToPipelineEventProcessor,
)
from salestech_be.core.meeting.crm_integrity_event_processors.account_archive import (
    MeetingAccountArchiveEventProcessor,
)
from salestech_be.core.meeting.crm_integrity_event_processors.account_merge import (
    MeetingAccountMergeEventProcessor,
)
from salestech_be.core.meeting.crm_integrity_event_processors.contact_email_account_move import (
    MeetingContactEmailAccountMoveEventProcessor,
)
from salestech_be.core.meeting.crm_integrity_event_processors.contact_email_account_remove import (
    MeetingContactEmailAccountRemoveEventProcessor,
)
from salestech_be.core.meeting.crm_integrity_event_processors.contact_email_move import (
    MeetingContactEmailMoveEventProcessor,
)
from salestech_be.core.meeting.crm_integrity_event_processors.contact_email_remove import (
    MeetingContactEmailRemoveEventProcessor,
)
from salestech_be.core.meeting.crm_integrity_event_processors.contact_merge import (
    MeetingContactMergeEventProcessor,
)
from salestech_be.core.meeting.crm_integrity_event_processors.contact_move import (
    MeetingContactMoveEventProcessor,
)
from salestech_be.core.meeting.crm_integrity_event_processors.contact_remove import (
    MeetingContactRemoveEventProcessor,
)
from salestech_be.core.meeting.meeting_data_integrity_service import (
    get_meeting_data_integrity_service,
)
from salestech_be.core.meeting.meeting_insight_service import (
    meeting_insight_service_factory_general,
)
from salestech_be.core.meeting.meeting_service import meeting_service_factory_general
from salestech_be.core.note.crm_integrity_event_processors.account_merge import (
    NoteAccountMergeEventProcessor,
)
from salestech_be.core.note.crm_integrity_event_processors.contact_merge import (
    NoteContactMergeEventProcessor,
)
from salestech_be.core.note.crm_integrity_event_processors.contact_move import (
    NoteContactMoveEventProcessor,
)
from salestech_be.core.note.service.note_data_integrity_service import (
    get_note_data_integrity_service,
)
from salestech_be.core.note.service.note_service import get_note_service_general
from salestech_be.core.pipeline.service.pipeline_query_service import (
    get_pipeline_query_service,
)
from salestech_be.core.research_agent.crm_integrity_event_processors.account_merge import (
    IntelligenceAccountMergeEventProcessor,
)
from salestech_be.core.research_agent.crm_integrity_event_processors.contact_merge import (
    IntelligenceContactMergeEventProcessor,
)
from salestech_be.core.research_agent.research_agent_service import (
    get_research_agent_service,
)
from salestech_be.core.sequence.crm_integrity_event_processors.account_archive import (
    SequenceAccountArchiveEventProcessor,
)
from salestech_be.core.sequence.crm_integrity_event_processors.account_merge import (
    SequenceAccountMergeEventProcessor,
)
from salestech_be.core.sequence.crm_integrity_event_processors.contact_archive import (
    SequenceContactArchiveEventProcessor,
)
from salestech_be.core.sequence.crm_integrity_event_processors.contact_email_account_move import (
    SequenceContactEmailAccountMoveEventProcessor,
)
from salestech_be.core.sequence.crm_integrity_event_processors.contact_email_account_remove import (
    SequenceContactEmailAccountRemoveEventProcessor,
)
from salestech_be.core.sequence.crm_integrity_event_processors.contact_email_move import (
    SequenceContactEmailMoveEventProcessor,
)
from salestech_be.core.sequence.crm_integrity_event_processors.contact_email_remove import (
    SequenceContactEmailRemoveEventProcessor,
)
from salestech_be.core.sequence.crm_integrity_event_processors.contact_merge import (
    SequenceContactMergeEventProcessor,
)
from salestech_be.core.sequence.crm_integrity_event_processors.contact_move import (
    SequenceContactMoveEventProcessor,
)
from salestech_be.core.sequence.crm_integrity_event_processors.contact_remove import (
    SequenceContactRemoveEventProcessor,
)
from salestech_be.core.sequence.service.sequence_data_integrity_service import (
    get_sequence_data_integrity_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_enrollment_query_service import (
    get_sequence_enrollment_query_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    get_sequence_enrollment_service_by_db_engine,
)
from salestech_be.core.task.crm_integrity_event_processors.account_add_to_pipeline import (
    TaskAccountAddToPipelineEventProcessor,
)
from salestech_be.core.task.crm_integrity_event_processors.account_archive import (
    TaskAccountArchiveEventProcessor,
)
from salestech_be.core.task.crm_integrity_event_processors.account_merge import (
    TaskAccountMergeEventProcessor,
)
from salestech_be.core.task.crm_integrity_event_processors.contact_merge import (
    TaskContactMergeEventProcessor,
)
from salestech_be.core.task.crm_integrity_event_processors.contact_move import (
    TaskContactMoveEventProcessor,
)
from salestech_be.core.task.crm_integrity_event_processors.contact_remove import (
    TaskContactRemoveEventProcessor,
)
from salestech_be.core.task.service.task_v2_service import get_task_v2_service_general
from salestech_be.core.voice.v2.crm_integrity_event_processors.account_add_to_pipeline import (
    VoiceCallAccountAddToPipelineEventProcessor,
)
from salestech_be.core.voice.v2.crm_integrity_event_processors.account_merge import (
    VoiceCallAccountMergeEventProcessor,
)
from salestech_be.core.voice.v2.crm_integrity_event_processors.contact_merge import (
    VoiceCallContactMergeEventProcessor,
)
from salestech_be.core.voice.v2.crm_integrity_event_processors.contact_move import (
    VoiceCallContactMoveEventProcessor,
)
from salestech_be.core.voice.v2.crm_integrity_event_processors.contact_remove import (
    VoiceCallContactRemoveEventProcessor,
)
from salestech_be.core.voice.v2.voice_call_data_integrity_service import (
    get_voice_call_data_integrity_service_from_engine,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_integrity import CRMSubDomain, IntegrityJobName


class ProcessorFactory:
    @staticmethod
    def create(  # type: ignore[explicit-any]  # TODO: fix-any-annotation # noqa: C901
        job_name: IntegrityJobName,
        subdomain: CRMSubDomain,
        db_engine: DatabaseEngine,
    ) -> AbstractDataOperationProcessor[Any, Any]:
        processor: AbstractDataOperationProcessor[Any, Any]  # type: ignore[explicit-any]  # TODO: fix-any-annotation
        match job_name:
            case IntegrityJobName.MERGE_ACCOUNTS:
                processor = AccountMergeProcessorFactory.create(subdomain, db_engine)
            case IntegrityJobName.MERGE_CONTACTS:
                processor = ContactMergeProcessorFactory.create(subdomain, db_engine)
            case IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT:
                processor = ContactMoveProcessorFactory.create(subdomain, db_engine)
            case IntegrityJobName.REMOVE_CONTACT_FROM_ACCOUNT:
                processor = ContactRemoveProcessorFactory.create(subdomain, db_engine)
            case IntegrityJobName.ARCHIVE_CONTACT:
                processor = ContactArchiveProcessorFactory.create(subdomain, db_engine)
            case IntegrityJobName.ARCHIVE_ACCOUNT:
                processor = AccountArchiveProcessorFactory.create(subdomain, db_engine)
            case IntegrityJobName.MOVE_CONTACT_EMAIL_TO_CONTACT:
                processor = ContactEmailMoveProcessorFactory.create(
                    subdomain, db_engine
                )
            case IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_CONTACT:
                processor = ContactEmailRemoveProcessorFactory.create(
                    subdomain, db_engine
                )
            case IntegrityJobName.MOVE_CONTACT_EMAIL_TO_ACCOUNT:
                processor = ContactEmailAccountMoveProcessorFactory.create(
                    subdomain, db_engine
                )
            case IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_ACCOUNT:
                processor = ContactEmailAccountRemoveProcessorFactory.create(
                    subdomain, db_engine
                )
            case IntegrityJobName.ADD_ACCOUNT_TO_PIPELINE:
                processor = AccountAddToPipelineProcessorFactory.create(
                    subdomain, db_engine
                )
            case _:
                raise ValueError(f"invalid job name: {job_name}")

        return processor


class AccountMergeProcessorFactory:
    @staticmethod
    def create(  # noqa: PLR0911
        subdomain: CRMSubDomain,
        db_engine: DatabaseEngine,
    ) -> AbstractDataOperationProcessor[
        DataReplacementParam, CrmIdReplacementTrackParam
    ]:
        match subdomain:
            case CRMSubDomain.CALENDAR:
                return CalendarAccountMergeEventProcessor(
                    user_calendar_data_integrity_service=get_user_calendar_data_integrity_service(
                        db_engine
                    ),
                )
            case CRMSubDomain.TASK:
                return TaskAccountMergeEventProcessor(
                    task_v2_service=get_task_v2_service_general(db_engine=db_engine),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                )
            case CRMSubDomain.INTELLIGENCE:
                return IntelligenceAccountMergeEventProcessor(
                    research_agent_service=get_research_agent_service(
                        db_engine=db_engine,
                    )
                )
            case CRMSubDomain.NOTE:
                return NoteAccountMergeEventProcessor(
                    note_service=get_note_service_general(db_engine=db_engine),
                    note_data_integrity_service=get_note_data_integrity_service(
                        db_engine=db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                )
            case CRMSubDomain.VOICE_CALL:
                return VoiceCallAccountMergeEventProcessor(
                    voice_call_data_integrity_service=get_voice_call_data_integrity_service_from_engine(
                        db_engine=db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                )
            case CRMSubDomain.EMAIL:
                return EmailAccountMergeEventProcessor(
                    global_thread_query_service=get_global_thread_query_service(
                        db_engine=db_engine
                    ),
                    contact_resolve_service=get_contact_resolve_service(
                        db_engine=db_engine
                    ),
                    email_data_integrity_service=get_email_data_integrity_service(
                        db_engine=db_engine
                    ),
                    global_thread_resolve_service=get_global_thread_resolve_service(
                        db_engine=db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                )
            case CRMSubDomain.MEETING:
                return MeetingAccountMergeEventProcessor(
                    meeting_data_integrity_service=get_meeting_data_integrity_service(
                        db_engine=db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                    meeting_service=meeting_service_factory_general(
                        db_engine=db_engine
                    ),
                )
            case CRMSubDomain.SEQUENCE:
                return SequenceAccountMergeEventProcessor(
                    sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_data_integrity_service=get_sequence_data_integrity_service_by_db_engine(
                        db_engine=db_engine
                    ),
                )
            case CRMSubDomain.DOMAIN_OBJECT_LIST:
                return DomainObjectListAccountMergeEventProcessor(
                    domain_object_list_service=get_domain_object_list_service(
                        db_engine=db_engine
                    ),
                    domain_object_list_query_service=get_domain_object_list_query_service_by_db_engine(
                        db_engine=db_engine
                    ),
                )
            case _:
                raise ValueError(f"invalid sub domain: {subdomain}")


class ContactMergeProcessorFactory:
    @staticmethod
    def create(  # noqa: PLR0911
        subdomain: CRMSubDomain,
        db_engine: DatabaseEngine,
    ) -> AbstractDataOperationProcessor[
        DataReplacementParam, CrmIdReplacementTrackParam
    ]:
        match subdomain:
            case CRMSubDomain.CALENDAR:
                return CalendarContactMergeEventProcessor(
                    user_calendar_data_integrity_service=get_user_calendar_data_integrity_service(
                        db_engine
                    ),
                    activity_data_integrity_service=get_activity_data_integrity_service(
                        db_engine
                    ),
                )
            case CRMSubDomain.EMAIL:
                return EmailContactMergeEventProcessor(
                    message_service_ext=get_message_service_ext(db_engine=db_engine),
                    email_data_integrity_service=get_email_data_integrity_service(
                        db_engine=db_engine
                    ),
                    email_insight_service=get_email_insight_service_by_db(
                        db_engine=db_engine
                    ),
                    message_service=get_message_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    activity_data_integrity_service=get_activity_data_integrity_service(
                        db_engine=db_engine
                    ),
                    global_thread_query_service=get_global_thread_query_service(
                        db_engine=db_engine
                    ),
                    global_thread_resolve_service=get_global_thread_resolve_service(
                        db_engine=db_engine
                    ),
                )
            case CRMSubDomain.INTELLIGENCE:
                return IntelligenceContactMergeEventProcessor(
                    research_agent_service=get_research_agent_service(
                        db_engine=db_engine,
                    )
                )
            case CRMSubDomain.MEETING:
                return MeetingContactMergeEventProcessor(
                    meeting_data_integrity_service=get_meeting_data_integrity_service(
                        db_engine
                    ),
                    activity_data_integrity_service=get_activity_data_integrity_service(
                        db_engine=db_engine
                    ),
                    meeting_insight_service=meeting_insight_service_factory_general(
                        db_engine=db_engine
                    ),
                    meeting_service=meeting_service_factory_general(
                        db_engine=db_engine
                    ),
                )
            case CRMSubDomain.NOTE:
                return NoteContactMergeEventProcessor(
                    note_data_integrity_service=get_note_data_integrity_service(
                        db_engine=db_engine
                    ),
                    activity_data_integrity_service=get_activity_data_integrity_service(
                        db_engine=db_engine
                    ),
                )
            case CRMSubDomain.SEQUENCE:
                return SequenceContactMergeEventProcessor(
                    sequence_enrollment_service=get_sequence_enrollment_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_data_integrity_service=get_sequence_data_integrity_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
                        db_engine=db_engine
                    ),
                )
            case CRMSubDomain.TASK:
                return TaskContactMergeEventProcessor(
                    task_v2_service=get_task_v2_service_general(db_engine=db_engine),
                    activity_data_integrity_service=get_activity_data_integrity_service(
                        db_engine=db_engine
                    ),
                )
            case CRMSubDomain.VOICE_CALL:
                return VoiceCallContactMergeEventProcessor(
                    voice_call_data_integrity_service=get_voice_call_data_integrity_service_from_engine(
                        db_engine=db_engine
                    ),
                    activity_data_integrity_service=get_activity_data_integrity_service(
                        db_engine=db_engine
                    ),
                )
            case CRMSubDomain.DOMAIN_OBJECT_LIST:
                return DomainObjectListContactMergeEventProcessor(
                    domain_object_list_service=get_domain_object_list_service(
                        db_engine=db_engine
                    ),
                    domain_object_list_query_service=get_domain_object_list_query_service_by_db_engine(
                        db_engine=db_engine
                    ),
                )
            case _:
                raise ValueError(f"invalid sub domain: {subdomain}")


class ContactMoveProcessorFactory:
    @staticmethod
    def create(  # noqa: PLR0911
        subdomain: CRMSubDomain,
        db_engine: DatabaseEngine,
    ) -> AbstractDataOperationProcessor[DataMovementParam, CrmIdReplacementTrackParam]:
        match subdomain:
            case CRMSubDomain.CALENDAR:
                return CalendarContactMoveEventProcessor(
                    user_calendar_data_integrity_service=get_user_calendar_data_integrity_service(
                        db_engine
                    ),
                )
            case CRMSubDomain.MEETING:
                return MeetingContactMoveEventProcessor(
                    meeting_data_integrity_service=get_meeting_data_integrity_service(
                        db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                    meeting_service=meeting_service_factory_general(
                        db_engine=db_engine
                    ),
                )
            case CRMSubDomain.TASK:
                return TaskContactMoveEventProcessor(
                    task_v2_service=get_task_v2_service_general(db_engine=db_engine),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                )
            case CRMSubDomain.NOTE:
                return NoteContactMoveEventProcessor(
                    note_service=get_note_service_general(db_engine=db_engine),
                    note_data_integrity_service=get_note_data_integrity_service(
                        db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                )
            case CRMSubDomain.EMAIL:
                return EmailContactMoveEventProcessor(
                    global_thread_query_service=get_global_thread_query_service(
                        db_engine=db_engine
                    ),
                    global_thread_resolve_service=get_global_thread_resolve_service(
                        db_engine=db_engine
                    ),
                    email_data_integrity_service=get_email_data_integrity_service(
                        db_engine=db_engine
                    ),
                )
            case CRMSubDomain.VOICE_CALL:
                return VoiceCallContactMoveEventProcessor(
                    voice_call_data_integrity_service=get_voice_call_data_integrity_service_from_engine(
                        db_engine=db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                )
            case CRMSubDomain.SEQUENCE:
                return SequenceContactMoveEventProcessor(
                    sequence_enrollment_service=get_sequence_enrollment_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_data_integrity_service=get_sequence_data_integrity_service_by_db_engine(
                        db_engine=db_engine
                    ),
                )
            case _:
                raise ValueError(f"invalid sub domain: {subdomain}")


class ContactArchiveProcessorFactory:
    @staticmethod
    def create(
        subdomain: CRMSubDomain,
        db_engine: DatabaseEngine,
    ) -> AbstractDataOperationProcessor[DataArchivalParam, CrmIdReplacementTrackParam]:
        match subdomain:
            case CRMSubDomain.EMAIL:
                return EmailArchiveContactEventProcessor(
                    message_service=get_message_service_by_db_engine(db_engine),
                    email_data_integrity_service=get_email_data_integrity_service(
                        db_engine
                    ),
                )
            case CRMSubDomain.SEQUENCE:
                return SequenceContactArchiveEventProcessor(
                    sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_enrollment_service=get_sequence_enrollment_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_data_integrity_service=get_sequence_data_integrity_service_by_db_engine(
                        db_engine=db_engine
                    ),
                )
            case _:
                raise ValueError(f"invalid sub domain: {subdomain}")


class ContactEmailMoveProcessorFactory:
    @staticmethod
    def create(
        subdomain: CRMSubDomain,
        db_engine: DatabaseEngine,
    ) -> AbstractDataOperationProcessor[DataMovementParam, CrmIdReplacementTrackParam]:
        match subdomain:
            case CRMSubDomain.CALENDAR:
                return CalendarContactEmailMoveEventProcessor(
                    user_calendar_data_integrity_service=get_user_calendar_data_integrity_service(
                        db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case CRMSubDomain.SEQUENCE:
                return SequenceContactEmailMoveEventProcessor(
                    sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_enrollment_service=get_sequence_enrollment_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_data_integrity_service=get_sequence_data_integrity_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case CRMSubDomain.EMAIL:
                return EmailContactEmailMoveEventProcessor(
                    global_thread_query_service=get_global_thread_query_service(
                        db_engine=db_engine
                    ),
                    email_data_integrity_service=get_email_data_integrity_service(
                        db_engine=db_engine
                    ),
                    global_thread_resolve_service=get_global_thread_resolve_service(
                        db_engine=db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case CRMSubDomain.MEETING:
                return MeetingContactEmailMoveEventProcessor(
                    meeting_data_integrity_service=get_meeting_data_integrity_service(
                        db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                    meeting_service=meeting_service_factory_general(
                        db_engine=db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case _:
                raise ValueError(f"invalid sub domain: {subdomain}")


class ContactEmailRemoveProcessorFactory:
    @staticmethod
    def create(
        subdomain: CRMSubDomain,
        db_engine: DatabaseEngine,
    ) -> AbstractDataOperationProcessor[DataRemovalParam, CrmIdReplacementTrackParam]:
        match subdomain:
            case CRMSubDomain.EMAIL:
                return EmailContactEmailRemoveEventProcessor(
                    global_thread_query_service=get_global_thread_query_service(
                        db_engine=db_engine
                    ),
                    email_data_integrity_service=get_email_data_integrity_service(
                        db_engine=db_engine
                    ),
                    global_thread_resolve_service=get_global_thread_resolve_service(
                        db_engine=db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case CRMSubDomain.SEQUENCE:
                return SequenceContactEmailRemoveEventProcessor(
                    sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_enrollment_service=get_sequence_enrollment_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_data_integrity_service=get_sequence_data_integrity_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case CRMSubDomain.MEETING:
                return MeetingContactEmailRemoveEventProcessor(
                    meeting_data_integrity_service=get_meeting_data_integrity_service(
                        db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case _:
                raise ValueError(f"invalid sub domain: {subdomain}")


class ContactEmailAccountMoveProcessorFactory:
    @staticmethod
    def create(
        subdomain: CRMSubDomain,
        db_engine: DatabaseEngine,
    ) -> AbstractDataOperationProcessor[DataMovementParam, CrmIdReplacementTrackParam]:
        match subdomain:
            case CRMSubDomain.CALENDAR:
                return CalendarContactEmailAccountMoveEventProcessor(
                    user_calendar_data_integrity_service=get_user_calendar_data_integrity_service(
                        db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case CRMSubDomain.SEQUENCE:
                return SequenceContactEmailAccountMoveEventProcessor(
                    sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_enrollment_service=get_sequence_enrollment_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_data_integrity_service=get_sequence_data_integrity_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case CRMSubDomain.EMAIL:
                return EmailContactEmailAccountMoveEventProcessor(
                    global_thread_query_service=get_global_thread_query_service(
                        db_engine=db_engine
                    ),
                    email_data_integrity_service=get_email_data_integrity_service(
                        db_engine=db_engine
                    ),
                    global_thread_resolve_service=get_global_thread_resolve_service(
                        db_engine=db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case CRMSubDomain.MEETING:
                return MeetingContactEmailAccountMoveEventProcessor(
                    meeting_data_integrity_service=get_meeting_data_integrity_service(
                        db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                    meeting_service=meeting_service_factory_general(
                        db_engine=db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case _:
                raise ValueError(f"invalid sub domain: {subdomain}")


class ContactEmailAccountRemoveProcessorFactory:
    @staticmethod
    def create(
        subdomain: CRMSubDomain,
        db_engine: DatabaseEngine,
    ) -> AbstractDataOperationProcessor[DataRemovalParam, CrmIdReplacementTrackParam]:
        match subdomain:
            case CRMSubDomain.CALENDAR:
                return CalendarContactEmailAccountRemoveEventProcessor(
                    user_calendar_data_integrity_service=get_user_calendar_data_integrity_service(
                        db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case CRMSubDomain.SEQUENCE:
                return SequenceContactEmailAccountRemoveEventProcessor(
                    sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_enrollment_service=get_sequence_enrollment_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_data_integrity_service=get_sequence_data_integrity_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case CRMSubDomain.EMAIL:
                return EmailContactEmailAccountRemoveEventProcessor(
                    global_thread_query_service=get_global_thread_query_service(
                        db_engine=db_engine
                    ),
                    email_data_integrity_service=get_email_data_integrity_service(
                        db_engine=db_engine
                    ),
                    global_thread_resolve_service=get_global_thread_resolve_service(
                        db_engine=db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case CRMSubDomain.MEETING:
                return MeetingContactEmailAccountRemoveEventProcessor(
                    meeting_data_integrity_service=get_meeting_data_integrity_service(
                        db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                    meeting_service=meeting_service_factory_general(
                        db_engine=db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case _:
                raise ValueError(f"invalid sub domain: {subdomain}")


class ContactRemoveProcessorFactory:
    @staticmethod
    def create(
        subdomain: CRMSubDomain,
        db_engine: DatabaseEngine,
    ) -> AbstractDataOperationProcessor[DataRemovalParam, CrmIdReplacementTrackParam]:
        match subdomain:
            case CRMSubDomain.TASK:
                return TaskContactRemoveEventProcessor(
                    task_v2_service=get_task_v2_service_general(db_engine=db_engine),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                    activity_data_integrity_service=get_activity_data_integrity_service(
                        db_engine=db_engine
                    ),
                )
            case CRMSubDomain.CALENDAR:
                return CalendarContactRemoveEventProcessor(
                    user_calendar_data_integrity_service=get_user_calendar_data_integrity_service(
                        db_engine
                    ),
                )
            case CRMSubDomain.EMAIL:
                return EmailContactRemoveEventProcessor(
                    email_data_integrity_service=get_email_data_integrity_service(
                        db_engine
                    ),
                    global_thread_query_service=get_global_thread_query_service(
                        db_engine=db_engine
                    ),
                    global_thread_resolve_service=get_global_thread_resolve_service(
                        db_engine=db_engine
                    ),
                )
            case CRMSubDomain.MEETING:
                return MeetingContactRemoveEventProcessor(
                    meeting_data_integrity_service=get_meeting_data_integrity_service(
                        db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                    meeting_service=meeting_service_factory_general(
                        db_engine=db_engine
                    ),
                )
            case CRMSubDomain.SEQUENCE:
                return SequenceContactRemoveEventProcessor(
                    sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_enrollment_service=get_sequence_enrollment_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    sequence_data_integrity_service=get_sequence_data_integrity_service_by_db_engine(
                        db_engine=db_engine
                    ),
                    contact_service=get_contact_service(db_engine=db_engine),
                )
            case CRMSubDomain.VOICE_CALL:
                return VoiceCallContactRemoveEventProcessor(
                    voice_call_data_integrity_service=get_voice_call_data_integrity_service_from_engine(
                        db_engine=db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                )
            case _:
                raise ValueError(f"invalid sub domain: {subdomain}")


class AccountArchiveProcessorFactory:
    @staticmethod
    def create(
        subdomain: CRMSubDomain,
        db_engine: DatabaseEngine,
    ) -> AbstractDataOperationProcessor[DataArchivalParam, CrmIdReplacementTrackParam]:
        match subdomain:
            case CRMSubDomain.TASK:
                return TaskAccountArchiveEventProcessor(
                    task_v2_service=get_task_v2_service_general(db_engine=db_engine),
                )
            case CRMSubDomain.EMAIL:
                return EmailAccountArchiveEventProcessor(
                    email_data_integrity_service=get_email_data_integrity_service(
                        db_engine
                    ),
                )
            case CRMSubDomain.MEETING:
                return MeetingAccountArchiveEventProcessor(
                    meeting_data_integrity_service=get_meeting_data_integrity_service(
                        db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                )
            case CRMSubDomain.SEQUENCE:
                return SequenceAccountArchiveEventProcessor(
                    sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
                        db_engine=db_engine
                    ),
                )
            case _:
                raise ValueError(f"invalid sub domain: {subdomain}")


class AccountAddToPipelineProcessorFactory:
    @staticmethod
    def create(
        subdomain: CRMSubDomain,
        db_engine: DatabaseEngine,
    ) -> AbstractDataOperationProcessor[DataAdditionParam, CrmIdReplacementTrackParam]:
        match subdomain:
            case CRMSubDomain.TASK:
                return TaskAccountAddToPipelineEventProcessor(
                    task_v2_service=get_task_v2_service_general(db_engine=db_engine),
                    pipeline_query_service=get_pipeline_query_service(
                        db_engine=db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                )
            case CRMSubDomain.EMAIL:
                return EmailAccountAddToPipelineEventProcessor(
                    email_data_integrity_service=get_email_data_integrity_service(
                        db_engine
                    ),
                    global_thread_resolve_service=get_global_thread_resolve_service(
                        db_engine=db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                )
            case CRMSubDomain.MEETING:
                return MeetingAccountAddToPipelineEventProcessor(
                    meeting_data_integrity_service=get_meeting_data_integrity_service(
                        db_engine
                    ),
                    meeting_service=meeting_service_factory_general(
                        db_engine=db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                )
            case CRMSubDomain.VOICE_CALL:
                return VoiceCallAccountAddToPipelineEventProcessor(
                    voice_call_data_integrity_service=get_voice_call_data_integrity_service_from_engine(
                        db_engine=db_engine
                    ),
                    pipeline_query_service=get_pipeline_query_service(
                        db_engine=db_engine
                    ),
                    activity_service=get_activity_service_general(db_engine=db_engine),
                )
            case _:
                raise ValueError(f"invalid sub domain: {subdomain}")


# todo: add more consumer factories grouped by event type here ...
