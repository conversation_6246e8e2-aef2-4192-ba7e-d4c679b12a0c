from abc import ABC, abstractmethod
from typing import Generic, TypeVar

from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
    DataAdditionParam,
    DataArchivalParam,
    DataMovementParam,
    DataRemovalParam,
    DataReplacementParam,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationPreviewIdentifiers,
)
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityJobUserOption,
)

DataOperationParamT = TypeVar(
    "DataOperationParamT",
    bound=DataReplacementParam
    | DataMovementParam
    | DataArchivalParam
    | DataRemovalParam
    | DataAdditionParam,
)
DataOperationTrackParamT = TypeVar(
    "DataOperationTrackParamT", bound=CrmIdReplacementTrackParam
)


class AbstractDataOperationProcessor(
    ABC, Generic[DataOperationParamT, DataOperationTrackParamT]
):
    """Abstract base class for data operation consumers.

    Each domain-specific consumer should inherit from this class and implement
    the required methods to handle data change events for their domain.

    """

    @abstractmethod
    async def process(
        self,
        *,
        param: DataOperationParamT,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[DataOperationTrackParamT]:
        """Process and apply the data change event to the domain's data.

        Implements the main logic for updating domain data based on the event.
        """
        ...

    async def revert(self) -> None:
        """Placeholder method for reverting any changes made during the process
        operation, It implements the logic to restore domain data to its original
        state if the process operation fails to keep data consistency.

        TODO: this method is supposed to be implemented by each domain-specific processor
        mandatorily. But for now, we set it optional but keep it as a no-op interface.
        """
        return

    @abstractmethod
    async def preview(
        self,
        *,
        param: DataOperationParamT,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[IntegrityAssociatedDataOperationPreviewIdentifiers]:
        """Preview the changes that would be made by processing the event.

        Returns a list of associated data operation preview identifiers.

        Note: only certain types of subdomain objects need to be listed in the preview result
        """
        ...
