from uuid import UUID

from salestech_be.core.account.service.account_data_integrity_service import (
    get_account_data_integrity_service,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityJobValidationResponse,
    IntegrityOperationValidation,
)
from salestech_be.core.pipeline.service.pipeline_data_integrity_service import (
    get_pipeline_data_integrity_service,
)
from salestech_be.db.dbengine.core import DatabaseEngine


class AddAccountToPipelineValidationService:
    def __init__(self, database_engine: DatabaseEngine) -> None:
        self.account_data_integrity_service = get_account_data_integrity_service(
            db_engine=database_engine
        )
        self.pipeline_data_integrity_service = get_pipeline_data_integrity_service(
            db_engine=database_engine
        )

    async def validate_integrity_job(
        self,
        account_id: UUID,
        pipeline_id: UUID,
        organization_id: UUID,
    ) -> IntegrityJobValidationResponse:
        validation_results: list[IntegrityOperationValidation] = []

        validation_results.extend(
            await self.account_data_integrity_service.validate_account_availability(
                account_id=account_id,
                organization_id=organization_id,
            )
        )

        validation_results.extend(
            await self.pipeline_data_integrity_service.validate_pipeline_availability(
                pipeline_id=pipeline_id,
                organization_id=organization_id,
            )
        )

        # unlike other integrity jobs, this job requires the pipeline <> account association is exsited
        # because account_id is a required field for pipeline
        validation_results.extend(
            await self.pipeline_data_integrity_service.validate_active_account_pipeline_association_existence(
                account_id=account_id,
                pipeline_id=pipeline_id,
                organization_id=organization_id,
            )
        )

        return IntegrityJobValidationResponse(validations=validation_results)
