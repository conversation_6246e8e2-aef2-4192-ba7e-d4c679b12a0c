from uuid import UUID

from salestech_be.core.crm_integrity.integrity_operation_validation.add_account_to_pipeline_validation import (
    AddAccountToPipelineValidationService,
)
from salestech_be.core.crm_integrity.integrity_operation_validation.archive_account_validation import (
    ArchiveAccountValidationService,
)
from salestech_be.core.crm_integrity.integrity_operation_validation.archive_contact_validation import (
    ArchiveContactValidationService,
)
from salestech_be.core.crm_integrity.integrity_operation_validation.merge_account_validation import (
    MergeAccountValidationService,
)
from salestech_be.core.crm_integrity.integrity_operation_validation.merge_contact_validation import (
    MergeContactValidationService,
)
from salestech_be.core.crm_integrity.integrity_operation_validation.move_contact_email_to_account_validation import (
    MoveContactEmailToAccountValidationService,
)
from salestech_be.core.crm_integrity.integrity_operation_validation.move_contact_email_to_contact_validation import (
    MoveContactEmailToContactValidationService,
)
from salestech_be.core.crm_integrity.integrity_operation_validation.move_contact_to_account_validation import (
    MoveContactToAccountValidationService,
)
from salestech_be.core.crm_integrity.integrity_operation_validation.remove_contact_email_from_account_validation import (
    RemoveContactEmailFromAccountValidationService,
)
from salestech_be.core.crm_integrity.integrity_operation_validation.remove_contact_email_from_contact_validation import (
    RemoveContactEmailFromContactValidationService,
)
from salestech_be.core.crm_integrity.integrity_operation_validation.remove_contact_from_account_validation import (
    RemoveContactFromAccountValidationService,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityJobValidationRequest,
    IntegrityJobValidationResponse,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_integrity import (
    IntegrityJobName,
    MoveContactEmailToAccountContextualParam,
    MoveContactEmailToContactContextualParam,
    MoveContactToAccountContextualParam,
    RemoveContactEmailFromAccountContextualParam,
    get_integrity_job_name,
)
from salestech_be.util.validation import cast_or_error, not_none


class IntegrityJobValidationService:
    def __init__(self, database_engine: DatabaseEngine) -> None:
        self.move_contact_to_account_validation_service = (
            MoveContactToAccountValidationService(database_engine=database_engine)
        )
        self.remove_contact_from_account_validation_service = (
            RemoveContactFromAccountValidationService(database_engine=database_engine)
        )
        self.merge_account_validation_service = MergeAccountValidationService(
            database_engine=database_engine
        )
        self.merge_contact_validation_service = MergeContactValidationService(
            database_engine=database_engine
        )
        self.archive_contact_validation_service = ArchiveContactValidationService(
            database_engine=database_engine
        )
        self.archive_account_validation_service = ArchiveAccountValidationService(
            database_engine=database_engine
        )
        self.remove_contact_email_from_contact_validation_service = (
            RemoveContactEmailFromContactValidationService(
                database_engine=database_engine
            )
        )
        self.remove_contact_email_from_account_validation_service = (
            RemoveContactEmailFromAccountValidationService(
                database_engine=database_engine
            )
        )
        self.move_contact_email_to_account_validation_service = (
            MoveContactEmailToAccountValidationService(database_engine=database_engine)
        )
        self.move_contact_email_to_contact_validation_service = (
            MoveContactEmailToContactValidationService(database_engine=database_engine)
        )
        self.add_account_to_pipeline_validation_service = (
            AddAccountToPipelineValidationService(database_engine=database_engine)
        )

    async def validate_crm_integrity_job(  # noqa: C901
        self,
        integrity_job_validation_request: IntegrityJobValidationRequest,
        organization_id: UUID,
    ) -> IntegrityJobValidationResponse:
        integrity_job_name = get_integrity_job_name(
            integrity_job_type=integrity_job_validation_request.job_type,
            src_entity_type=integrity_job_validation_request.src_entity_type,
            dest_entity_type=integrity_job_validation_request.dest_entity_type,
        )

        validation_response: IntegrityJobValidationResponse

        match integrity_job_name:
            case IntegrityJobName.MERGE_ACCOUNTS:
                validation_response = (
                    await self.merge_account_validation_service.validate_integrity_job(
                        src_account_id=integrity_job_validation_request.src_entity_id,
                        dest_account_id=not_none(
                            integrity_job_validation_request.dest_entity_id
                        ),
                        organization_id=organization_id,
                    )
                )
            case IntegrityJobName.MERGE_CONTACTS:
                validation_response = (
                    await self.merge_contact_validation_service.validate_integrity_job(
                        src_contact_id=integrity_job_validation_request.src_entity_id,
                        dest_contact_id=not_none(
                            integrity_job_validation_request.dest_entity_id
                        ),
                        organization_id=organization_id,
                    )
                )
            case IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT:
                validation_response = await self.move_contact_to_account_validation_service.validate_integrity_job(
                    contact_id=integrity_job_validation_request.src_entity_id,
                    src_account_id=cast_or_error(
                        integrity_job_validation_request.contextual_param,
                        MoveContactToAccountContextualParam,
                    ).src_account_id,
                    dest_account_id=not_none(
                        integrity_job_validation_request.dest_entity_id
                    ),
                    organization_id=organization_id,
                )
            case IntegrityJobName.REMOVE_CONTACT_FROM_ACCOUNT:
                validation_response = await self.remove_contact_from_account_validation_service.validate_integrity_job(
                    contact_id=integrity_job_validation_request.src_entity_id,
                    account_id=not_none(
                        integrity_job_validation_request.dest_entity_id
                    ),
                    organization_id=organization_id,
                )
            case IntegrityJobName.ARCHIVE_CONTACT:
                validation_response = await self.archive_contact_validation_service.validate_integrity_job(
                    archive_contact_id=integrity_job_validation_request.src_entity_id,
                    organization_id=organization_id,
                )
            case IntegrityJobName.ARCHIVE_ACCOUNT:
                validation_response = await self.archive_account_validation_service.validate_integrity_job(
                    archive_account_id=integrity_job_validation_request.src_entity_id,
                    organization_id=organization_id,
                )
            case IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_CONTACT:
                validation_response = await self.remove_contact_email_from_contact_validation_service.validate_integrity_job(
                    remove_contact_email_id=integrity_job_validation_request.src_entity_id,
                    contact_id=not_none(
                        integrity_job_validation_request.dest_entity_id
                    ),
                    organization_id=organization_id,
                )
            case IntegrityJobName.MOVE_CONTACT_EMAIL_TO_CONTACT:
                validation_response = await self.move_contact_email_to_contact_validation_service.validate_integrity_job(
                    move_contact_email_id=integrity_job_validation_request.src_entity_id,
                    src_contact_id=cast_or_error(
                        integrity_job_validation_request.contextual_param,
                        MoveContactEmailToContactContextualParam,
                    ).src_contact_id,
                    dest_contact_id=not_none(
                        integrity_job_validation_request.dest_entity_id
                    ),
                    organization_id=organization_id,
                )
            case IntegrityJobName.MOVE_CONTACT_EMAIL_TO_ACCOUNT:
                validation_response = await self.move_contact_email_to_account_validation_service.validate_integrity_job(
                    move_contact_email_id=integrity_job_validation_request.src_entity_id,
                    contact_id=cast_or_error(
                        integrity_job_validation_request.contextual_param,
                        MoveContactEmailToAccountContextualParam,
                    ).contact_id,
                    src_account_id=cast_or_error(
                        integrity_job_validation_request.contextual_param,
                        MoveContactEmailToAccountContextualParam,
                    ).src_account_id,
                    dest_account_id=not_none(
                        integrity_job_validation_request.dest_entity_id
                    ),
                    organization_id=organization_id,
                )
            case IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_ACCOUNT:
                validation_response = await self.remove_contact_email_from_account_validation_service.validate_integrity_job(
                    remove_contact_email_id=integrity_job_validation_request.src_entity_id,
                    contact_id=cast_or_error(
                        integrity_job_validation_request.contextual_param,
                        RemoveContactEmailFromAccountContextualParam,
                    ).contact_id,
                    account_id=not_none(
                        integrity_job_validation_request.dest_entity_id
                    ),
                    organization_id=organization_id,
                )
            case IntegrityJobName.ADD_ACCOUNT_TO_PIPELINE:
                validation_response = await self.add_account_to_pipeline_validation_service.validate_integrity_job(
                    account_id=integrity_job_validation_request.src_entity_id,
                    pipeline_id=not_none(
                        integrity_job_validation_request.dest_entity_id
                    ),
                    organization_id=organization_id,
                )
            case _:
                # return no errors by default
                validation_response = IntegrityJobValidationResponse(validations=[])

        return validation_response


def get_crm_integrity_job_validation_service(
    db_engine: DatabaseEngine,
) -> IntegrityJobValidationService:
    return IntegrityJobValidationService(database_engine=db_engine)
