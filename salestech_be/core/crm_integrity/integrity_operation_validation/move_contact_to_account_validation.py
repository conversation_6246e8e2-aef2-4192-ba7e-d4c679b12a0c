from uuid import UUID

from salestech_be.core.account.service.account_data_integrity_service import (
    get_account_data_integrity_service,
)
from salestech_be.core.contact.service.contact_data_integrity_service import (
    get_contact_data_integrity_service,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityJobValidationResponse,
    IntegrityOperationValidation,
)
from salestech_be.core.pipeline.service.pipeline_data_integrity_service import (
    get_pipeline_data_integrity_service,
)
from salestech_be.db.dbengine.core import DatabaseEngine


class MoveContactToAccountValidationService:
    def __init__(self, database_engine: DatabaseEngine) -> None:
        self.pipeline_data_integrity_service = get_pipeline_data_integrity_service(
            db_engine=database_engine
        )
        self.contact_data_integrity_service = get_contact_data_integrity_service(
            db_engine=database_engine
        )
        self.account_data_integrity_service = get_account_data_integrity_service(
            db_engine=database_engine
        )

    async def validate_integrity_job(
        self,
        *,
        contact_id: UUID,
        src_account_id: UUID | None,
        dest_account_id: UUID,
        organization_id: UUID,
    ) -> IntegrityJobValidationResponse:
        validation_results: list[IntegrityOperationValidation] = []

        validation_results.extend(
            await self.contact_data_integrity_service.validate_contact_availability(
                contact_id=contact_id,
                organization_id=organization_id,
            )
        )

        validation_results.extend(
            await self.account_data_integrity_service.validate_account_availability(
                account_id=dest_account_id,
                organization_id=organization_id,
                is_dest_account_in_move_contact_to_account_job=True,
            )
        )

        validation_results.extend(
            await self.contact_data_integrity_service.validate_contact_account_association_availability(
                contact_id=contact_id,
                account_id=dest_account_id,
                organization_id=organization_id,
                expected_existence=False,
            )
        )

        if src_account_id:
            validation_results.extend(
                await self.account_data_integrity_service.validate_account_availability(
                    account_id=src_account_id,
                    organization_id=organization_id,
                )
            )

            validation_results.extend(
                await self.pipeline_data_integrity_service.validate_move_pipeline_to_alternative_contact(
                    src_account_id=src_account_id,
                    moving_contact_id=contact_id,
                    organization_id=organization_id,
                )
            )
        else:
            # if contact is newly added to dest account, it should not have existing account associations
            validation_results.extend(
                await self.contact_data_integrity_service.validate_contact_has_account_associations(
                    contact_id=contact_id,
                    organization_id=organization_id,
                )
            )

        return IntegrityJobValidationResponse(validations=validation_results)
