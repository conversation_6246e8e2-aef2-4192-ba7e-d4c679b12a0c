from uuid import UUID

from temporalio import activity

from salestech_be.core.account.service.account_service import get_account_service
from salestech_be.core.contact.service.contact_service import get_contact_service
from salestech_be.core.crm_integrity.types.activity_params import (
    IntegrityJobTriggerNotificationParams,
)
from salestech_be.core.notification.service.notification_service import (
    get_notification_service_by_db_engine,
)
from salestech_be.core.notification.types import NotificationWrapper
from salestech_be.core.pipeline.service.pipeline_service import (
    get_pipeline_service,
)
from salestech_be.db.models.crm_integrity import EntityType, get_integrity_job_name
from salestech_be.db.models.notification import (
    NotificationCRMDataIntegrityJobData,
    NotificationReferenceIdType,
)
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.validation import not_none


@activity.defn
async def crm_data_integrity_job_send_notification_activity(
    param: IntegrityJobTriggerNotificationParams,
) -> None:
    db_engine = await get_or_init_db_engine()
    notification_service = get_notification_service_by_db_engine(db_engine)
    contact_service = get_contact_service(db_engine=db_engine)
    account_service = get_account_service(
        db_engine=db_engine,
    )
    pipeline_service = get_pipeline_service(db_engine=db_engine)

    async def _get_display_name_by_entity_id(
        entity_id: UUID, entity_type: EntityType
    ) -> str:
        match entity_type:
            case EntityType.ACCOUNT:
                return (
                    await account_service.get_account_v2(
                        account_id=entity_id,
                        organization_id=param.organization_id,
                    )
                ).display_name
            case EntityType.CONTACT:
                return (
                    await contact_service.get_contact_v2(
                        contact_id=entity_id,
                        organization_id=param.organization_id,
                    )
                ).display_name
            case EntityType.CONTACT_EMAIL:
                return not_none(
                    await contact_service.get_contact_email_by_id(
                        contact_email_id=entity_id,
                        organization_id=param.organization_id,
                        exclude_deleted_or_archived=False,
                    )
                ).email
            case EntityType.PIPELINE:
                return (
                    await pipeline_service.get_pipeline_by_id(
                        pipeline_id=entity_id,
                        organization_id=param.organization_id,
                    )
                ).display_name
            case _:
                raise ValueError(f"Not supported entity type: {entity_type}")

    def _get_redirection_url(entity_type: EntityType, entity_id: UUID) -> str:
        match entity_type:
            case EntityType.ACCOUNT:
                return account_service.get_relative_redirection_url(entity_id)
            case EntityType.CONTACT:
                return contact_service.get_relative_redirection_url(entity_id)
            case EntityType.PIPELINE:
                return pipeline_service.get_relative_redirection_url(entity_id)
            case _:
                raise ValueError(f"Not supported entity type: {entity_type}")

    src_display_name = await _get_display_name_by_entity_id(
        entity_id=param.src_entity_id, entity_type=param.src_entity_type
    )

    dest_display_name_or_none = (
        await _get_display_name_by_entity_id(
            entity_id=param.dest_entity_id, entity_type=param.dest_entity_type
        )
        if param.dest_entity_id and param.dest_entity_type
        else None
    )

    await notification_service.trigger_notification(
        notification=NotificationWrapper(
            recipient_user_ids=[param.user_id],
            data=NotificationCRMDataIntegrityJobData(
                job_name=get_integrity_job_name(
                    integrity_job_type=param.integrity_job_type,
                    src_entity_type=param.src_entity_type,
                    dest_entity_type=param.dest_entity_type,
                ),
                job_status=param.integrity_job_status,
                source_entity_display_name=src_display_name,
                source_entity_id=str(param.src_entity_id),
                source_entity_type=param.src_entity_type,
                dest_entity_display_name=dest_display_name_or_none,
                dest_entity_id=str(param.dest_entity_id)
                if param.dest_entity_id
                else None,
                dest_entity_type=param.dest_entity_type
                if param.dest_entity_type
                else None,
                redirection_url=_get_redirection_url(
                    entity_type=param.redirection_entity_type,
                    entity_id=param.redirection_entity_id,
                ),
            ),
            idempotency_key=param.idempotency_key,
            actor_user_id=param.user_id,
        ),
        organization_id=param.organization_id,
        reference_id=param.integrity_job_id,
        reference_id_type=NotificationReferenceIdType.CRM_DATA_INTEGRITY_JOB,
    )
