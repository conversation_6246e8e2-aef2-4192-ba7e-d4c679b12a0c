import asyncio
from uuid import UUID, uuid4

from salestech_be.core.crm_integrity.event_processors.processor_factory import (
    ProcessorFactory,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    DataAdditionParam,
)
from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
    get_affected_sub_domains_by_integrity_job_name,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationCommonPreviewIdentifier,
    IntegrityAssociatedDataOperationPreviewIdentifiers,
    IntegrityJobName,
    IntegrityJobPreviewResponse,
)
from salestech_be.core.pipeline.service.pipeline_query_service import (
    get_pipeline_query_service,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.util.validation import cast_or_error, not_none


class AddAccountToPipelinePreviewService:
    def __init__(self, database_engine: DatabaseEngine) -> None:
        self.database_engine = database_engine
        self.pipeline_query_service = get_pipeline_query_service(
            db_engine=database_engine
        )

    async def preview_integrity_job(
        self,
        *,
        integrity_job_name: IntegrityJobName,
        account_id: UUID,
        pipeline_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> IntegrityJobPreviewResponse:
        # check if the account has any other pipelines
        associated_pipelines = not_none(
            await self.pipeline_query_service.list_pipelines_by_account_id(
                organization_id=organization_id,
                account_id=account_id,
            )
        )

        pipelines_excluding_target_pipeline = [
            p for p in associated_pipelines if p.id != pipeline_id
        ]

        if pipelines_excluding_target_pipeline:
            # if the pipeline is not the first pipeline this account has, do nothing
            return IntegrityJobPreviewResponse(
                src_entity_id=account_id,
                dest_entity_id=pipeline_id,
            )

        associated_data_changes_identifiers = []
        preview_tasks = []

        preview_param = DataAdditionParam(
            integrity_job_id=uuid4(),
            src_entity_id=account_id,
            dest_entity_id=pipeline_id,
            user_id=user_id,
            organization_id=organization_id,
        )

        for subdomain in get_affected_sub_domains_by_integrity_job_name(
            integrity_job_name=integrity_job_name,
        ):
            processor = ProcessorFactory.create(
                job_name=integrity_job_name,
                subdomain=subdomain,
                db_engine=self.database_engine,
            )

            preview_tasks.append(processor.preview(param=preview_param))

        preview_results = await asyncio.gather(*preview_tasks)

        associated_data_changes_identifiers = [
            identifiers
            for preview_result in preview_results
            for identifiers in preview_result
            if cast_or_error(
                identifiers, IntegrityAssociatedDataOperationPreviewIdentifiers
            ).entity_ids
        ]

        return IntegrityJobPreviewResponse(
            src_entity_id=account_id,
            dest_entity_id=pipeline_id,
            associated_data_changes_identifiers=associated_data_changes_identifiers,
            affected_intel_pipeline_identifiers=[
                IntegrityAssociatedDataOperationCommonPreviewIdentifier(
                    id=pipeline_id,
                )
            ],
        )
