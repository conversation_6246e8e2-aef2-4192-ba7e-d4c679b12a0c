from typing import assert_never

from temporalio import activity

from salestech_be.core.account.service.account_data_integrity_service import (
    get_account_data_integrity_service,
)
from salestech_be.core.account.service.account_service import get_account_service
from salestech_be.core.contact.service.contact_data_integrity_service import (
    get_contact_data_integrity_service,
)
from salestech_be.core.contact.service.contact_service import get_contact_service
from salestech_be.core.crm_integrity.data_operation_tracker.atomic_operation_tracker_service import (
    get_atomic_operation_tracker_service,
)
from salestech_be.core.crm_integrity.data_operation_tracker.integrity_job_service import (
    IntegrityJobService,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CreateIntegrityJobParam,
    RequireOrFreeLocksForEntitiesParam,
    UpdateIntegrityJobErrorParam,
    UpdateIntegrityJobParam,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_integrity import CRMIntegrityJob, EntityType


class IntegrityJobActivity:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.integrity_job_service = IntegrityJobService(db_engine=db_engine)
        self.contact_service = get_contact_service(db_engine=db_engine)
        self.account_service = get_account_service(db_engine=db_engine)
        self.account_data_integrity_service = get_account_data_integrity_service(
            db_engine=db_engine,
        )
        self.contact_data_integrity_service = get_contact_data_integrity_service(
            db_engine=db_engine,
        )
        self.atomic_operation_tracker_service = get_atomic_operation_tracker_service(
            db_engine=db_engine
        )

    @activity.defn(name="create_integrity_job")
    async def create_integrity_job(
        self, param: CreateIntegrityJobParam
    ) -> CRMIntegrityJob:
        integrity_job, _ = await self.integrity_job_service.create_integrity_job(
            user_id=param.user_id,
            organization_id=param.organization_id,
            job_type=param.job_type,
            src_entity_type=param.src_entity_type,
            src_entity_id=param.src_entity_id,
            dest_entity_type=param.dest_entity_type,
            dest_entity_id=param.dest_entity_id,
            user_choices=param.user_choices or {},
        )

        return integrity_job

    @activity.defn(name="update_integrity_job")
    async def update_integrity_job(
        self, param: UpdateIntegrityJobParam
    ) -> CRMIntegrityJob:
        return await self.integrity_job_service.update_integrity_job(param)

    @activity.defn(name="update_integrity_job_error")
    async def update_integrity_job_error(
        self, param: UpdateIntegrityJobErrorParam
    ) -> CRMIntegrityJob:
        return await self.integrity_job_service.update_integrity_job_error(param)

    @activity.defn(name="acquire_locks_for_entities")
    async def acquire_locks_for_entities(
        self, param: RequireOrFreeLocksForEntitiesParam
    ) -> None:
        async with self.integrity_job_service.integrity_job_repo.engine.begin():
            for descriptor in param.entity_descriptors:
                match descriptor.entity_type:
                    case EntityType.CONTACT:
                        await self.contact_data_integrity_service.acquire_lock_for_integrity_job(
                            job_id=param.job_id,
                            contact_id=descriptor.entity_id,
                            user_id=param.user_id,
                            organization_id=param.organization_id,
                        )
                    case EntityType.ACCOUNT:
                        await self.account_data_integrity_service.acquire_lock_for_integrity_job(
                            job_id=param.job_id,
                            account_id=descriptor.entity_id,
                            user_id=param.user_id,
                            organization_id=param.organization_id,
                            is_dest_account_in_move_contact_to_account_job=descriptor.is_dest_account_in_move_contact_to_account_job,
                        )
                    case _ as never:
                        assert_never(never)

                await self.atomic_operation_tracker_service.track_lock_operation(
                    integrity_job_id=param.job_id,
                    entity_type=descriptor.entity_type,
                    entity_id=descriptor.entity_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                )

    @activity.defn(name="free_locks_for_entities")
    async def free_locks_for_entities(
        self, param: RequireOrFreeLocksForEntitiesParam
    ) -> None:
        async with self.integrity_job_service.integrity_job_repo.engine.begin():
            for descriptor in param.entity_descriptors:
                match descriptor.entity_type:
                    case EntityType.CONTACT:
                        await self.contact_data_integrity_service.free_lock_for_integrity_job(
                            job_id=param.job_id,
                            contact_id=descriptor.entity_id,
                            user_id=param.user_id,
                            organization_id=param.organization_id,
                        )
                    case EntityType.ACCOUNT:
                        await self.account_data_integrity_service.free_lock_for_integrity_job(
                            job_id=param.job_id,
                            account_id=descriptor.entity_id,
                            user_id=param.user_id,
                            organization_id=param.organization_id,
                        )
                    case _ as never:
                        assert_never(never)

                await self.atomic_operation_tracker_service.track_unlock_operation(
                    integrity_job_id=param.job_id,
                    entity_type=descriptor.entity_type,
                    entity_id=descriptor.entity_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                )
