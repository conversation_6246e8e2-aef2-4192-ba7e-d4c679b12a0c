import uuid
from typing import Annotated, assert_never, cast
from uuid import UUID

from fastapi import Depends

from salestech_be.common.core_crm.contact_pipeline_role import ContactPipelineRoleType
from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception.exception import (
    ConflictErrorDetails,
    ConflictResourceError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.schema_manager.std_object_field_identifier import (
    PipelineField,
    StdObjectIdentifiers,
    StdSelectListIdentifier,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import (
    UNSET,
    BasePatchRequest,
    UnsetAware,
    specified,
    specified_any,
    specified_or_default,
)
from salestech_be.core.approval_request.service.approval_service import (
    ApprovalService,
    get_approval_service_with_engine,
)
from salestech_be.core.approval_request.types import (
    ApprovalRequest,
    UpsertApprovalRequestRequest,
)
from salestech_be.core.common.domain_service import (
    DomainService,
)
from salestech_be.core.common.types import User<PERSON>uthContext
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.data.service.query_service import get_domain_object_query_service
from salestech_be.core.data.types import ObjectRecordPrimaryDataUpdater
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.imports.service.crm_sync_push_service import (
    CrmSyncPushService,
    get_crm_sync_push_service,
)
from salestech_be.core.job.service.job_service import JobService
from salestech_be.core.metadata.converter import (
    contact_pipeline_role_from_db,
    select_list_value_lite_from_db,
)
from salestech_be.core.metadata.dto.select_list_dto import (
    PipelineStageSelectListValueDto,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
    get_select_list_service,
)
from salestech_be.core.metadata.service.pipeline_stage_service import (
    PipelineStageSelectListService,
    get_pipeline_stage_select_list_service,
)
from salestech_be.core.metadata.service.stage_criteria_service import (
    StageCriteriaService,
    get_stage_criteria_service,
)
from salestech_be.core.metadata.types import (
    ContactPipelineRole,
)
from salestech_be.core.notification.service.notification_service import (
    NotificationService,
    get_notification_service_by_db_engine,
)
from salestech_be.core.notification.types import SendNotificationRequest
from salestech_be.core.pipeline.converter import (
    PipelineSelectListValueContainer,
    contact_pipeline_association_request_to_db_contact_pipeline_association,
    create_pipeline_req_to_db_contact_pipeline_associations,
    create_pipeline_req_to_db_pipeline,
    patch_pipeline_req_to_db_pipeline_update,
    pipeline_v2_from_db,
)
from salestech_be.core.pipeline.service.contact_pipeline_role_ai_rec_service import (
    ContactPipelineRoleAIRecService,
    get_contact_pipeline_role_ai_rec_service,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_service import (
    PipelineQualificationPropertyService,
    get_pipeline_qualification_property_service,
)
from salestech_be.core.pipeline.service.pipeline_query_service import (
    PipelineQueryService,
    get_pipeline_query_service,
)
from salestech_be.core.pipeline.service_api_schema import (
    ArchiveContactPipelineAssociationRequest,
    ArchiveContactPipelineAssociationResult,
    ContactPipelineAssociationRequest,
    ContactPipelineRoleRequest,
    CreatePipelineRequest,
    FullContactPipelineAssociationRequests,
    FullContactPipelineRoleRequests,
    InternalPatchPipelineRequest,
    OpportunityShiftPipelineRequest,
    PatchContactPipelineRoleTypesRequest,
    PatchPipelineRequest,
    PutAllContactPipelineAssociationsResult,
    PutAllContactPipelineRolesResult,
    ReplaceContactPipelineAssociationRequest,
    ReplaceContactPipelineAssociationResult,
    ShiftPipelineStageDefaultRule,
    ShiftPipelineStageRequest,
    ShiftPipelineStageRequestV2,
    ShiftPipelineStageResponse,
    ShiftPipelineStageResponseV2,
    UpsertContactPipelineAssociationRequests,
    UpsertContactPipelineAssociationResult,
    UpsertContactPipelineRoleRequests,
    UpsertContactPipelineRoleResult,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.stage_criteria.service_api_schema import (
    StageCriteriaEvaluationResultV2,
    StageCriteriaResultState,
)
from salestech_be.core.stage_criteria.stage_criteria_service_v2 import (
    StageCriteriaServiceV2,
    get_stage_criteria_service_v2,
)
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.address_repository import AddressRepository
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.custom_object_repository import CustomObjectRepository
from salestech_be.db.dao.job_repository import JobRepository
from salestech_be.db.dao.note_repository import NoteRepository
from salestech_be.db.dao.pipeline_repository import (
    AccountStatusUpdateForPipelineCreationOrUpdate,
    AccountStatusUpdateForPipelineStageShift,
    PipelineRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.account import Account, AccountStatus
from salestech_be.db.models.approval_request import (
    ApprovalRequestStatus,
    ApprovalRequestType,
    PipelineStageBackwardTransitApprovalRequestReference,
)
from salestech_be.db.models.contact_pipeline_association import (
    ContactPipelineAssociation,
    ContactPipelineAssociationUpdate,
)
from salestech_be.db.models.notification import (
    NotificationCRMChangeData,
    NotificationReferenceIdType,
)
from salestech_be.db.models.pipeline import Pipeline, PipelineStatus
from salestech_be.db.models.pipeline_stage_select_list_value_metadata import (
    PipelineOutcomeState,
)
from salestech_be.db.models.select_list import (
    SelectListStatus,
    SelectListValue,
    SelectListValueStatus,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.copy_util import strict_model_copy
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger()


class PipelineService(DomainService[PipelineV2]):
    def __init__(
        self,
        pipeline_repository: Annotated[PipelineRepository, Depends()],
        address_repository: Annotated[AddressRepository, Depends()],
        custom_object_service: Annotated[CustomObjectService, Depends()],
        contact_repository: Annotated[ContactRepository, Depends()],
        account_repository: Annotated[AccountRepository, Depends()],
        note_repository: Annotated[NoteRepository, Depends()],
        job_service: Annotated[JobService, Depends()],
        user_service: Annotated[UserService, Depends()],
        pipeline_query_service: Annotated[PipelineQueryService, Depends()],
        pipeline_stage_sl_service: Annotated[PipelineStageSelectListService, Depends()],
        select_list_service: Annotated[InternalSelectListService, Depends()],
        stage_criteria_service: Annotated[StageCriteriaService, Depends()],
        approval_service: Annotated[ApprovalService, Depends()],
        notification_service: Annotated[NotificationService, Depends()],
        feature_flag_service: Annotated[
            FeatureFlagService, Depends(get_feature_flag_service)
        ],
        crm_sync_push_service: Annotated[
            CrmSyncPushService, Depends(get_crm_sync_push_service)
        ],
        stage_criteria_service_v2: Annotated[StageCriteriaServiceV2, Depends()],
        pipeline_qualification_property_service: Annotated[
            PipelineQualificationPropertyService, Depends()
        ],
        contact_pipeline_role_ai_rec_service: Annotated[
            ContactPipelineRoleAIRecService, Depends()
        ],
    ):
        super().__init__(feature_flag_service=feature_flag_service)
        self.pipeline_repository = pipeline_repository
        self.address_repository = address_repository
        self.custom_object_service = custom_object_service
        self.contact_repository = contact_repository
        self.account_repository = account_repository
        self.note_repository = note_repository
        self.job_service = job_service
        self.user_service = user_service
        self.logger = get_logger()
        self.pipeline_query_service = pipeline_query_service
        self.pipeline_stage_sl_service = pipeline_stage_sl_service
        self.select_list_service = select_list_service
        self.stage_criteria_service = stage_criteria_service
        self.stage_criteria_service_v2 = stage_criteria_service_v2
        self.approval_service = approval_service
        self.notification_service = notification_service
        self.crm_sync_push_service = crm_sync_push_service
        self.pipeline_qualification_property_service = (
            pipeline_qualification_property_service
        )
        self.contact_pipeline_role_ai_rec_service = contact_pipeline_role_ai_rec_service

    def get_relative_redirection_url(self, pipeline_id: UUID) -> str:
        return f"/pipelines/{pipeline_id}"

    async def _validate_closed_reason_for_stage(
        self,
        *,
        organization_id: UUID,
        stage_value_dto: PipelineStageSelectListValueDto,
        closed_reason_select_list_value_ids: list[UUID] | None,
        closed_reason_custom_detail: str | None,
    ) -> list[SelectListValue] | None:
        """
        Validates closed reason requirements for a given stage.
        Returns the validated SelectListValue list if provided and valid, None otherwise.

        Raises InvalidArgumentError if validation fails for any of these cases:
            - Terminal stage missing required closed reasons
            - Non-terminal stage has closed reasons
            - Non-terminal stage has custom detail

        """
        is_terminal_stage = (
            stage_value_dto.pipeline_stage_select_list_value_metadata.outcome_state
            is not None
        )

        is_won_stage = (
            stage_value_dto.pipeline_stage_select_list_value_metadata.outcome_state
            == PipelineOutcomeState.CLOSED_WON
        )

        if is_terminal_stage:
            if not closed_reason_select_list_value_ids:
                raise InvalidArgumentError(
                    "Closed reason select list value ids are required or cannot be empty for terminal stages"
                )

            # Require:
            # - Won stage: closed_reason_select_list_value_id must be in the won reason select list
            # - Lost stage: closed_reason_select_list_value_id must be in the lost reason select list
            application_code_name = (
                StdSelectListIdentifier.pipeline_closed_won_reasons
                if is_won_stage
                else StdSelectListIdentifier.pipeline_closed_lost_reasons
            )

            return [
                await self.select_list_service.validate_select_list_value_reference(
                    organization_id=organization_id,
                    select_list_value_id=closed_reason_select_list_value_id,
                    application_code_name=application_code_name,
                )
                for closed_reason_select_list_value_id in closed_reason_select_list_value_ids
            ]
        else:
            if closed_reason_select_list_value_ids:
                raise InvalidArgumentError(
                    "Not allowed to set closed reason select list value ids for non-terminal stage"
                )
            if closed_reason_custom_detail:
                raise InvalidArgumentError(
                    "Not allowed to set closed reason custom detail for non-terminal stage"
                )
            return None

    async def get_entity(
        self, entity_id: UUID, organization_id: UUID, user_id: UUID
    ) -> PipelineV2 | None:
        return await self.pipeline_query_service.get_pipeline_by_id(
            organization_id=organization_id,
            pipeline_id=entity_id,
        )

    def determine_account_status_after_pipeline_creation_or_update(
        self,
        pipeline_outcome_state: PipelineOutcomeState | None,
        pre_update_account_status: AccountStatus,
    ) -> AccountStatus | None:
        """
        Determines the target account status lower bound after a pipeline is created or updated.
        Returns the target account status lower bound if it is greater than the current account status,
        otherwise returns None.
        """
        _target_account_status_lower_bound = (
            AccountStatus.SELLING
            if pipeline_outcome_state != PipelineOutcomeState.CLOSED_WON
            else AccountStatus.CUSTOMER
        )

        return (
            _target_account_status_lower_bound
            if _target_account_status_lower_bound > pre_update_account_status
            else None
        )

    async def create_entity(
        self, organization_id: UUID, user_id: UUID, request: CreatePipelineRequest
    ) -> PipelineV2:
        return await self.create_pipeline(
            organization_id=organization_id,
            user_id=user_id,
            req=request,
        )

    async def create_pipeline(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        req: CreatePipelineRequest,
    ) -> PipelineV2:
        """
        Creates a new pipeline.
        """
        account = await self.account_repository.find_by_tenanted_primary_key_or_fail(
            table_model=Account,
            organization_id=organization_id,
            id=req.account_id,
        )

        # check if pipeline already exists by org_id, account_id and display_name
        existing_pipelines = (
            await self.pipeline_repository.find_pipelines_by_display_name_account(
                organization_id=organization_id,
                display_name=req.display_name,
                account_id=account.id,
            )
        )

        if existing_pipelines:
            self.logger.bind(existing_pipelines=existing_pipelines).warning(
                "Found existing pipelines with same display_name and account_id."
            )
            raise ConflictResourceError(
                additional_error_details=ConflictErrorDetails(
                    code=ErrorCode.PIPELINE_ALREADY_EXISTS_WITH_NAME_AND_ACCOUNT,
                    details="Pipeline with display_name and account_id already exists",
                    reference_id=str(existing_pipelines[0].id),
                    conflicted_existing_object=StdObjectIdentifiers.pipeline.identifier,
                    conflicted_existing_object_attrs={
                        PipelineField.organization_id: existing_pipelines[
                            0
                        ].organization_id,
                        PipelineField.id: existing_pipelines[0].id,
                        PipelineField.display_name: existing_pipelines[0].display_name,
                        PipelineField.account_id: existing_pipelines[0].account_id,
                    },
                )
            )

        stage_value_dto = (
            await self.pipeline_stage_sl_service.validate_pipeline_stage_reference(
                organization_id=organization_id,
                pipeline_stage_select_list_value_id=req.stage_id,
            )
        )

        # source_value = (
        #     await self.select_list_service.validate_select_list_value_reference(
        #         organization_id=organization_id,
        #         select_list_value_id=req.source_id,
        #         application_code_name=StdSelectListIdentifier.pipeline_source,
        #     )
        #     if req.source_id
        #     else None
        # )

        type_value = (
            await self.select_list_service.validate_select_list_value_reference(
                organization_id=organization_id,
                select_list_value_id=req.type_id,
                application_code_name=StdSelectListIdentifier.pipeline_type,
            )
            if req.type_id
            else None
        )

        closed_reason_select_list_values = await self._validate_closed_reason_for_stage(
            organization_id=organization_id,
            stage_value_dto=stage_value_dto,
            closed_reason_select_list_value_ids=req.closed_reason_select_list_value_ids,
            closed_reason_custom_detail=req.closed_reason_custom_detail,
        )

        _db_pipeline = create_pipeline_req_to_db_pipeline(
            organization_id=organization_id,
            user_id=user_id,
            create_req=req,
            pipeline_stage_slvm=stage_value_dto.pipeline_stage_select_list_value_metadata,
        )

        # determine the target account state lower bound after the pipeline is created
        account_state_update_for_pipeline_creation = AccountStatusUpdateForPipelineCreationOrUpdate(
            update_account_status_from=AccountStatus(account.status),
            new_account_status=self.determine_account_status_after_pipeline_creation_or_update(
                pipeline_outcome_state=stage_value_dto.pipeline_stage_select_list_value_metadata.outcome_state,
                pre_update_account_status=AccountStatus(account.status),
            ),
        )

        if not req.created_source or req.created_source.is_not_crm():
            # Push to CRM before database creation, but only if this creation request isn't an import from a CRM.
            await self.crm_sync_push_service.sync_push_obj_pipeline_create(
                organization_id=organization_id,
                pipeline_db=_db_pipeline,
            )

        default_qualification_properties = []
        if settings.enable_pipeline_qual_prop_during_creation:
            default_qualification_properties = self.pipeline_qualification_property_service.build_pipeline_creation_qualification_properties(
                pipeline_id=_db_pipeline.id,
                organization_id=organization_id,
                user_id=user_id,
            )

        (
            created_db_pipeline,
            created_db_contact_pipeline_associations,
            created_qualification_properties,
        ) = await self.pipeline_repository.create_pipeline(
            pipeline=_db_pipeline,
            contact_pipeline_associations=create_pipeline_req_to_db_contact_pipeline_associations(
                organization_id=organization_id,
                pipeline_id=_db_pipeline.id,
                user_id=user_id,
                create_req=req,
            ),
            account_state_update_for_pipeline_creation=account_state_update_for_pipeline_creation,
            qualification_properties=default_qualification_properties,
        )
        if req.custom_field_data:
            await self.custom_object_service.create_custom_object_data_by_extension_id(
                organization_id=organization_id,
                user_id=user_id,
                extension_id=created_db_pipeline.id,
                parent_object_name=ExtendableStandardObject.pipeline,
                custom_field_data_by_field_id=req.custom_field_data,
            )
        custom_field_data_group = await self.custom_object_service.map_custom_object_data_by_extension_ids_if_custom_object_exists(
            organization_id=organization_id,
            parent_object_name=ExtendableStandardObject.pipeline,
            extension_ids={created_db_pipeline.id},
        )

        return pipeline_v2_from_db(
            db_pipeline=created_db_pipeline,
            db_contact_pipeline_associations=created_db_contact_pipeline_associations,
            extension_custom_object_data_group_dto=custom_field_data_group,
            pipeline_select_list_value_container=PipelineSelectListValueContainer(
                stage_select_list_value_dto=stage_value_dto,
                source_select_list_value=None,
                type_select_list_value=type_value,
                closed_reason_select_list_values=closed_reason_select_list_values,
            ),
            db_qualification_properties=created_qualification_properties,
        )

    async def list_by_ids_untenanted(
        self,
        pipeline_ids: list[UUID],
        exclude_deleted_or_archived: bool = True,
    ) -> list[Pipeline]:
        return await self.pipeline_repository.list_by_ids_untenanted(
            pipeline_ids=list(pipeline_ids),
            exclude_deleted_or_archived=exclude_deleted_or_archived,
        )

    async def update_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        entity: PipelineV2,
        request: BasePatchRequest,
    ) -> PipelineV2:
        pipeline_patch_request = cast(PatchPipelineRequest, request)
        return await self.patch_pipeline(
            organization_id=organization_id,
            user_id=user_id,
            pipeline_id=entity.id,
            req=InternalPatchPipelineRequest.of_public_patch_pipeline_request(
                patch_pipeline_request=pipeline_patch_request
            ),
        )

    async def patch_pipeline(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID,
        req: InternalPatchPipelineRequest,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> PipelineV2:
        """
        Patches a pipeline.
        """
        if not (
            db_pipeline := await self.pipeline_query_service.get_pipeline_by_id(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                exclude_archived=True,
            )
        ):
            raise ResourceNotFoundError(f"Pipeline {pipeline_id} not found")

        _db_pipeline_update = patch_pipeline_req_to_db_pipeline_update(
            user_id=user_id,
            patch_req=req,
        )
        _account_state_update_for_pipeline_update = None
        if specified(_db_pipeline_update.account_id):
            await self._validate_pipeline_account_move(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                to_account_id=_db_pipeline_update.account_id,
            )
            account = (
                await self.account_repository.find_by_tenanted_primary_key_or_fail(
                    table_model=Account,
                    organization_id=organization_id,
                    id=_db_pipeline_update.account_id,
                    exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                )
            )

            _account_state_update_for_pipeline_update = AccountStatusUpdateForPipelineCreationOrUpdate(
                update_account_status_from=AccountStatus(account.status),
                new_account_status=self.determine_account_status_after_pipeline_creation_or_update(
                    pipeline_outcome_state=db_pipeline.stage.outcome_state,
                    pre_update_account_status=AccountStatus(account.status),
                ),
            )
        # if specified(req.source_id) and req.source_id:
        #     await self.select_list_service.validate_select_list_value_reference(
        #         organization_id=organization_id,
        #         select_list_value_id=req.source_id,
        #         application_code_name=StdSelectListIdentifier.pipeline_source,
        #     )

        if specified(req.type_id) and req.type_id:
            await self.select_list_service.validate_select_list_value_reference(
                organization_id=organization_id,
                select_list_value_id=req.type_id,
                application_code_name=StdSelectListIdentifier.pipeline_type,
            )

        if specified_any(
            req.closed_reason_select_list_value_ids, req.closed_reason_custom_detail
        ):
            await self._validate_closed_reason_for_stage(
                organization_id=organization_id,
                stage_value_dto=await self.pipeline_stage_sl_service.get_pipeline_stage_slv_dto_by_slv_id(
                    organization_id=organization_id,
                    pipeline_stage_select_list_value_id=db_pipeline.stage_id,
                ),
                closed_reason_select_list_value_ids=specified_or_default(
                    req.closed_reason_select_list_value_ids,
                    db_pipeline.closed_reason_select_list_value_ids,
                ),
                closed_reason_custom_detail=specified_or_default(
                    req.closed_reason_custom_detail,
                    db_pipeline.closed_reason_custom_detail,
                ),
            )

        if not req.created_source or req.created_source.is_not_crm():
            # Push updates to CRM before DB update
            await self.crm_sync_push_service.sync_push_obj_pipeline_update(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                fields_to_update=_db_pipeline_update.model_dump(),
            )

        updated_db_pipeline = await self.pipeline_repository.patch_pipeline(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            pipeline_update=_db_pipeline_update,
            account_state_update_for_pipeline_update=_account_state_update_for_pipeline_update,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )
        active_contact_pipeline_associations = await self.pipeline_repository.list_contact_pipeline_associations_by_pipeline_id(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            exclude_archived=True,
        )
        pipeline_select_list_value_container = await self.pipeline_query_service.get_pipeline_select_list_value_container(
            organization_id=organization_id,
            stage_id=updated_db_pipeline.stage_id,
            source_id=updated_db_pipeline.source_id,
            type_id=updated_db_pipeline.type_id,
            closed_reason_select_list_value_ids=updated_db_pipeline.closed_reason_select_list_value_ids,
        )
        if specified(req.custom_field_data) and req.custom_field_data:
            await (
                self.custom_object_service.update_custom_object_data_by_extension_id_v2(
                    organization_id=organization_id,
                    user_id=user_id,
                    extension_id=updated_db_pipeline.id,
                    parent_object_name=ExtendableStandardObject.pipeline,
                    custom_field_data_by_field_id=req.custom_field_data,
                )
            )
        custom_field_data_group = await self.custom_object_service.map_custom_object_data_by_extension_ids_if_custom_object_exists(
            organization_id=organization_id,
            parent_object_name=ExtendableStandardObject.pipeline,
            extension_ids={updated_db_pipeline.id},
        )
        pipeline_qualification_property = (
            await self.pipeline_repository.map_pipeline_qualification_property_by_pipeline_id(
                organization_id=organization_id,
                pipeline_ids={pipeline_id},
            )
        ).get(pipeline_id)
        return pipeline_v2_from_db(
            db_pipeline=updated_db_pipeline,
            db_contact_pipeline_associations=active_contact_pipeline_associations,
            extension_custom_object_data_group_dto=custom_field_data_group,
            pipeline_select_list_value_container=pipeline_select_list_value_container,
            db_qualification_properties=pipeline_qualification_property,
        )

    async def authed_archive_entity(
        self,
        user_auth_context: UserAuthContext,
        entity_id: UUID,
    ) -> PipelineV2:
        """
        Archives a pipeline, with object level auth check.  Since this service is expected
        to return a full model instance instead of the standard DeletEntityResponse, we
        have a custom function here rather than using DomainService standard functions.
        """
        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            entity_id=entity_id,
        ).info("Archive entity request")
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=entity_id,
            access_check_function=self.can_access_entity_for_delete,
        )

        return await self.archive_pipeline(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            pipeline_id=entity_id,
        )

    async def remove_entity(  # type: ignore[override]
        self, organization_id: UUID, user_id: UUID, entity_id: UUID
    ) -> None:
        # TODO: when implementing, make sure bring back DeleteEntityResponse as return type
        # It's removed for now to avoid circular dependency into API layer
        # Not used, please see authed_archive_entity instead
        raise NotImplementedError

    async def archive_pipeline(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID,
    ) -> PipelineV2:
        """
        Archives a pipeline.
        """
        pipeline = await self.get_pipeline_by_id(
            pipeline_id=pipeline_id,
            organization_id=organization_id,
        )

        account_update = (
            await self._resolve_account_state_update_for_pipeline_stage_shift(
                organization_id=organization_id,
                account_id=pipeline.account_id,
                pipeline_id=pipeline_id,
                current_outcome_state=pipeline.stage.outcome_state,
                target_outcome_state=None,
            )
        )

        updated_db_pipeline = await self.pipeline_repository.archive_pipeline(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            user_id=user_id,
            account_status_update=account_update,
        )
        active_contact_pipeline_associations = await self.pipeline_repository.list_contact_pipeline_associations_by_pipeline_id(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            exclude_archived=True,
        )
        custom_field_data_group = await self.custom_object_service.map_custom_object_data_by_extension_ids_if_custom_object_exists(
            organization_id=organization_id,
            parent_object_name=ExtendableStandardObject.pipeline,
            extension_ids={updated_db_pipeline.id},
        )
        pipeline_select_list_value_container = await self.pipeline_query_service.get_pipeline_select_list_value_container(
            organization_id=organization_id,
            stage_id=updated_db_pipeline.stage_id,
            source_id=updated_db_pipeline.source_id,
            type_id=updated_db_pipeline.type_id,
            closed_reason_select_list_value_ids=updated_db_pipeline.closed_reason_select_list_value_ids,
        )
        db_qualification_properties = (
            await self.pipeline_repository.map_pipeline_qualification_property_by_pipeline_id(
                organization_id=organization_id,
                pipeline_ids={pipeline_id},
            )
        ).get(pipeline_id)
        return pipeline_v2_from_db(
            db_pipeline=not_none(updated_db_pipeline),
            db_contact_pipeline_associations=active_contact_pipeline_associations,
            extension_custom_object_data_group_dto=custom_field_data_group,
            pipeline_select_list_value_container=pipeline_select_list_value_container,
            db_qualification_properties=db_qualification_properties,
        )

    async def _find_ordered_shifting_stages_slvs(  # noqa: C901, PLR0912, PLR0915
        self,
        *,
        organization_id: UUID,
        target_pipeline_stage_slv_dto: PipelineStageSelectListValueDto,
        current_pipeline_stage_slv_dto: PipelineStageSelectListValueDto,
    ) -> tuple[list[SelectListValue], bool]:
        target_stage_slv = target_pipeline_stage_slv_dto.select_list_value
        current_stage_slv = current_pipeline_stage_slv_dto.select_list_value
        target_stage_outcome_state = target_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.outcome_state
        current_stage_outcome_state = current_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.outcome_state

        all_stages = (
            await self.pipeline_stage_sl_service.get_pipeline_stage_select_list_dto(
                select_list_id=current_pipeline_stage_slv_dto.select_list_id,
                organization_id=organization_id,
            )
        ).select_list_value_dtos
        all_active_stages = tuple(
            dto
            for dto in all_stages
            if dto.select_list_value_status == SelectListValueStatus.ACTIVE
        )
        shifting_backward = target_stage_slv.rank < current_stage_slv.rank

        # pre-define all possible stage conversion series

        # 1. target only stage
        _target_only_stage = [target_stage_slv]

        # 2. from initial stage to target stage excluding any closed stages in between
        _from_initial_to_target_stage_excluding_closed_stages_inbetween = [
            stage.select_list_value
            for stage in all_active_stages
            if (
                stage.select_list_value.rank <= target_stage_slv.rank
                and (
                    stage.select_list_value_id == target_stage_slv.id
                    or (
                        not stage.pipeline_stage_select_list_value_metadata.outcome_state
                    )
                )
            )
        ]

        # 3. from current stage to target stage excluding any closed stages in between
        _from_current_to_target_stage_excluding_closed_stages_inbetween = [
            stage.select_list_value
            for stage in all_active_stages
            if (
                current_stage_slv.rank
                < stage.select_list_value.rank
                <= target_stage_slv.rank
                and (
                    stage.select_list_value_id == target_stage_slv.id
                    or (
                        not stage.pipeline_stage_select_list_value_metadata.outcome_state
                    )
                )
            )
        ]

        result: list[SelectListValue]
        if shifting_backward:
            match current_stage_outcome_state:
                case PipelineOutcomeState.CLOSED_WON:
                    match target_stage_outcome_state:
                        case (
                            PipelineOutcomeState.CLOSED_WON
                            | PipelineOutcomeState.CLOSED_LOST
                            | None
                        ):
                            result = _target_only_stage
                        case _ as unreachable:
                            assert_never(unreachable)
                case PipelineOutcomeState.CLOSED_LOST:
                    match target_stage_outcome_state:
                        case PipelineOutcomeState.CLOSED_WON | None:
                            result = _from_initial_to_target_stage_excluding_closed_stages_inbetween
                        case PipelineOutcomeState.CLOSED_LOST:
                            result = _target_only_stage
                        case _ as unreachable:
                            assert_never(unreachable)
                case None:
                    match target_stage_outcome_state:
                        case PipelineOutcomeState.CLOSED_WON:
                            result = _from_initial_to_target_stage_excluding_closed_stages_inbetween
                            logger.exception(
                                "Unexpected opportunity stage ordering: during backward shift, current stage is not closed but target stage is closed",
                                current_stage_id=current_pipeline_stage_slv_dto.select_list_value_id,
                                target_stage_id=target_pipeline_stage_slv_dto.select_list_value_id,
                                current_full_stage_list=all_stages,
                            )
                        case PipelineOutcomeState.CLOSED_LOST:
                            result = _target_only_stage
                            logger.exception(
                                "Unexpected opportunity stage ordering: during backward shift, current stage is not closed but target stage is closed",
                                current_stage_id=current_pipeline_stage_slv_dto.select_list_value_id,
                                target_stage_id=target_pipeline_stage_slv_dto.select_list_value_id,
                                current_full_stage_list=all_stages,
                            )
                        case None:
                            result = _target_only_stage
                        case _ as unreachable:
                            assert_never(unreachable)
                case _ as unreachable:
                    assert_never(unreachable)
        else:  # shifting forward
            match current_stage_outcome_state:
                case PipelineOutcomeState.CLOSED_WON:
                    match target_stage_outcome_state:
                        case PipelineOutcomeState.CLOSED_WON:
                            result = _target_only_stage
                        case PipelineOutcomeState.CLOSED_LOST:
                            result = _target_only_stage
                        case None:
                            result = _target_only_stage
                            logger.exception(
                                "Unexpected opportunity stage ordering: during forward shift, current stage is closed but target stage is not closed",
                                current_stage_id=current_pipeline_stage_slv_dto.select_list_value_id,
                                target_stage_id=target_pipeline_stage_slv_dto.select_list_value_id,
                                current_full_stage_list=all_stages,
                            )
                        case _ as unreachable:
                            assert_never(unreachable)
                case PipelineOutcomeState.CLOSED_LOST:
                    match target_stage_outcome_state:
                        case PipelineOutcomeState.CLOSED_WON:
                            result = _from_initial_to_target_stage_excluding_closed_stages_inbetween
                        case PipelineOutcomeState.CLOSED_LOST:
                            result = _target_only_stage
                        case None:
                            result = _from_initial_to_target_stage_excluding_closed_stages_inbetween
                            logger.exception(
                                "Unexpected opportunity stage ordering: during forward shift, current stage is closed but target stage is not closed",
                                current_stage_id=current_pipeline_stage_slv_dto.select_list_value_id,
                                target_stage_id=target_pipeline_stage_slv_dto.select_list_value_id,
                                current_full_stage_list=all_stages,
                            )
                        case _ as unreachable:
                            assert_never(unreachable)
                case None:
                    match target_stage_outcome_state:
                        case PipelineOutcomeState.CLOSED_WON | None:
                            result = _from_current_to_target_stage_excluding_closed_stages_inbetween
                        case PipelineOutcomeState.CLOSED_LOST:
                            result = _target_only_stage
                        case _ as unreachable:
                            assert_never(unreachable)
                case _ as unreachable:
                    assert_never(unreachable)

        logger.info(
            "shifting pipeline stages",
            current_stage=current_stage_slv.display_value,
            current_stage_id=current_stage_slv.id,
            current_stage_outcome_state=current_stage_outcome_state,
            current_stage_rank=current_stage_slv.rank,
            target_stage=target_stage_slv.display_value,
            target_stage_id=target_stage_slv.id,
            target_stage_outcome_state=target_stage_outcome_state,
            target_stage_rank=target_stage_slv.rank,
            conversion_journey=[val.display_value for val in result],
        )

        return result, shifting_backward

    async def authed_shift_opportunity_pipeline(
        self,
        user_auth_context: UserAuthContext,
        entity_id: UUID,
        req: OpportunityShiftPipelineRequest,
    ) -> ShiftPipelineStageResponseV2:
        with logger.contextualize(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            pipeline_id=entity_id,
            req=req,
        ):
            logger.info("Shift Opportunity Pipeline Request")
            await self.error_if_no_entity_access(
                user_auth_context=user_auth_context,
                entity_id=entity_id,
                access_check_function=self.can_access_entity_for_patch,
            )

            return await self.shift_opportunity_pipeline(
                organization_id=user_auth_context.organization_id,
                user_id=user_auth_context.user_id,
                pipeline_id=entity_id,
                req=req,
            )

    async def authed_shift_pipeline_stage(
        self,
        user_auth_context: UserAuthContext,
        entity_id: UUID,
        req: ShiftPipelineStageRequest,
    ) -> ShiftPipelineStageResponse:
        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            pipeline_id=entity_id,
            req=req,
        ).info("Shift pipeline stage request")
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=entity_id,
            access_check_function=self.can_access_entity_for_patch,
        )

        return await self.shift_pipeline_stage(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            pipeline_id=entity_id,
            req=req,
        )

    async def shift_pipeline_stage(  # noqa: C901, PLR0912
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID,
        req: ShiftPipelineStageRequest,
    ) -> ShiftPipelineStageResponse:
        """
        Shifts the pipeline stage for a pipeline.
        """
        # 00 fetch non-archived pipeline
        if not (
            pipeline := await self.pipeline_query_service.get_pipeline_by_id(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                exclude_archived=True,
            )
        ):
            raise ResourceNotFoundError(f"Pipeline {pipeline_id} not found")

        # 01 get target and current pipeline stages
        target_pipeline_stage_slv_dto = (
            await self.pipeline_stage_sl_service.validate_pipeline_stage_reference(
                organization_id=organization_id,
                pipeline_stage_select_list_value_id=req.target_stage_id,
            )
        )
        current_pipeline_stage_slv_dto = (
            await self.pipeline_stage_sl_service.get_pipeline_stage_slv_dto_by_slv_id(
                organization_id=organization_id,
                pipeline_stage_select_list_value_id=pipeline.stage_id,
            )
        )

        # 02 jump across currently now allowed
        # 03 no-op if target and current stage are the same
        if (
            target_pipeline_stage_slv_dto.select_list_value_id
            == current_pipeline_stage_slv_dto.select_list_value_id
        ) or (
            target_pipeline_stage_slv_dto.select_list_id
            != current_pipeline_stage_slv_dto.select_list_id
        ):
            return ShiftPipelineStageResponse(
                pipeline=pipeline,
                shifted=False,
            )

        # 03a validate closed reason if target stage is terminal
        closed_reason_select_list_values: list[SelectListValue] | None = None
        if (
            target_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.outcome_state
            is not None
        ):
            try:
                closed_reason_select_list_values = await self._validate_closed_reason_for_stage(
                    organization_id=organization_id,
                    stage_value_dto=target_pipeline_stage_slv_dto,
                    closed_reason_select_list_value_ids=req.closed_reason_select_list_value_ids,
                    closed_reason_custom_detail=req.closed_reason_custom_detail,
                )
            except InvalidArgumentError as e:
                return ShiftPipelineStageResponse(
                    pipeline=pipeline,
                    shifted=False,
                    violated_default_rule=ShiftPipelineStageDefaultRule.CLOSED_REASON_REQUIRED,
                    violation_description=str(e),
                )

            # stop-gap rules on pipeline stage "closed_won"
            if settings.enable_stopgap_pipeline_validation and (
                target_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.outcome_state
                == PipelineOutcomeState.CLOSED_WON
            ):
                if pipeline.amount is None:  # 0 is deemed non empty
                    logger.info(
                        f"Pipeline {pipeline_id} cannot be moved to closed_won due to empty amount"
                    )
                    return ShiftPipelineStageResponse(
                        pipeline=pipeline,
                        shifted=False,
                        violated_default_rule=ShiftPipelineStageDefaultRule.AMOUNT_REQUIRED,
                        violation_description="When opportunity doesn't have a valid amount set, user cannot shift its stage to a value that represents 'Closed Won'.",
                    )
                if pipeline.expires_at and pipeline.expires_at < zoned_utc_now():
                    logger.info(
                        f"Pipeline {pipeline_id} cannot be moved to closed_won due to expiration: {pipeline.expires_at}"
                    )
                    return ShiftPipelineStageResponse(
                        pipeline=pipeline,
                        shifted=False,
                        violated_default_rule=ShiftPipelineStageDefaultRule.VALID_EXPIRATION,
                        violation_description="When opportunity is already expired, user cannot shift its stage to a value that represents 'Closed Won'.",
                    )

        target_stage_outcome_state = target_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.outcome_state
        current_stage_outcome_state = current_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.outcome_state

        (
            target_stage_list_slvs_in_range,
            shifting_backward,
        ) = await self._find_ordered_shifting_stages_slvs(
            organization_id=organization_id,
            target_pipeline_stage_slv_dto=target_pipeline_stage_slv_dto,
            current_pipeline_stage_slv_dto=current_pipeline_stage_slv_dto,
        )
        pipeline_record_validation_updater = (
            self.shift_stage_request_to_pipeline_record_validation_updater(
                req=req,
                closed_reason_select_list_values=closed_reason_select_list_values,
            )
        )
        for sl_slv in target_stage_list_slvs_in_range:
            eval_result = await self.stage_criteria_service.evaluate_stage_criteria(
                organization_id=organization_id,
                record_id=pipeline.id,
                stage_selector=sl_slv.id,
                primary_object_identifier=PipelineV2.object_id,
                object_record_primary_data_updater=pipeline_record_validation_updater,
            )
            # fail and return if any stage in range fails criteria
            if not eval_result.is_success:
                violation_description_result = next(
                    (
                        detail
                        for detail in eval_result.evaluate_details
                        if not detail.is_success
                    ),
                    None,
                )
                if violation_description_result:
                    return ShiftPipelineStageResponse(
                        pipeline=await self.get_pipeline_by_id(
                            pipeline_id=pipeline_id,
                            organization_id=organization_id,
                        ),
                        shifted=False,
                        stage_criteria_evaluation_result=eval_result,
                        violation_description=violation_description_result.condition.description,
                    )
                raise ConflictResourceError(
                    "stage shifting evaluation failed but no condition filter found."
                )

        # 05 check for approval needed in case of backward shift # TODO
        approval_all_clear = True
        approval_request = None
        if shifting_backward:  # approval needed for backward shift
            (
                approval_all_clear,
                approval_request,
            ) = await self.approval_service.check_approval_for_shift(
                organization_id=organization_id,
                user_id=user_id,
                approval_request_type=ApprovalRequestType.CONTACT_STAGE_BACKWARD,
                approval_request_display_name="Backward Shift Approval",
                approval_request_description="Backward shift approval request",
                approval_request_ref=PipelineStageBackwardTransitApprovalRequestReference(
                    reference_model=ApprovalRequestType.PIPELINE_STAGE_BACKWARD,
                    pipeline_id=pipeline_id,
                    target_stage_select_list_value_id=target_pipeline_stage_slv_dto.select_list_value.id,
                ),
                existing_approval_request_id=req.approval_request_id,
                existing_approval_type=ApprovalRequestType.PIPELINE_STAGE_BACKWARD,
            )
        # 06 update pipeline stage, and update approval if needed
        shifted = False
        is_closed_after_update = target_stage_outcome_state is not None
        is_closed_before_update = current_stage_outcome_state is not None

        if approval_all_clear:
            account_state_update = (
                await self._resolve_account_state_update_for_pipeline_stage_shift(
                    organization_id=organization_id,
                    account_id=pipeline.account_id,
                    pipeline_id=pipeline_id,
                    current_outcome_state=current_stage_outcome_state,
                    target_outcome_state=target_stage_outcome_state,
                )
            )
            await self.pipeline_repository.update_pipeline_stages_in_order(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                user_id=user_id,
                is_closed_before_update=is_closed_before_update,
                is_closed_after_update=is_closed_after_update,
                ordered_stage_ids=[slv.id for slv in target_stage_list_slvs_in_range],
                new_status=target_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.pipeline_status,
                new_closed_reason_custom_detail=req.closed_reason_custom_detail
                if is_closed_after_update
                else None,
                new_closed_reason_select_list_value_ids=req.closed_reason_select_list_value_ids
                if is_closed_after_update
                else None,
                account_status_update=account_state_update,
            )
            if approval_request:
                await self.approval_service.update_approval_state(
                    organization_id=organization_id,
                    user_id=user_id,
                    approval_request=approval_request,
                    approval_request_status=ApprovalRequestStatus.EXECUTED,
                )
            shifted = True
        return ShiftPipelineStageResponse(
            pipeline=await self.get_pipeline_by_id(
                pipeline_id=pipeline_id,
                organization_id=organization_id,
            ),
            shifted=shifted,
        )

    async def authed_shift_pipeline_stage_v2(
        self,
        user_auth_context: UserAuthContext,
        entity_id: UUID,
        req: ShiftPipelineStageRequestV2,
    ) -> ShiftPipelineStageResponseV2:
        with logger.contextualize(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            pipeline_id=entity_id,
            req=req,
        ):
            logger.info("Shift pipeline stage v2 request")
            await self.error_if_no_entity_access(
                user_auth_context=user_auth_context,
                entity_id=entity_id,
                access_check_function=self.can_access_entity_for_patch,
            )

            if not (
                pipeline := await self.pipeline_query_service.get_pipeline_by_id(
                    organization_id=user_auth_context.organization_id,
                    pipeline_id=entity_id,
                    exclude_archived=True,
                )
            ):
                raise ResourceNotFoundError(f"Pipeline {entity_id} not found")

            return await self.shift_pipeline_stage_via_exit_criteria(
                organization_id=user_auth_context.organization_id,
                user_id=user_auth_context.user_id,
                pipeline=pipeline,
                req=req,
                user_is_admin=user_auth_context.is_admin,
            )

    # shift_opportunity_pipeline
    # this function has certain limitations and special logic, as follows:
    #
    # 1: If current opportunity is already closed, it cannot be shifted to another pipeline, since its closed reasons are already been settled and belongs to existing pipeline.
    # 2: request's target_pipeline_stage_select_list_value_id is optional. When not provided, move to new pipeline default stage
    # 3: Shift steps:
    #    a: verify request ids are valid and legit, new pipeline/stage exist and active
    #    b: shift to new pipeline default stage (default stage don't have enter/exit criteria so this step should not fail)
    #    c: shift to (if requested) new stage via shift_pipeline_stage_via_exit_criteria. If fails, stage at default stage
    async def shift_opportunity_pipeline(  # noqa: C901
        self,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID,
        req: OpportunityShiftPipelineRequest,
    ) -> ShiftPipelineStageResponseV2:
        # 00 fetch non-archived opportunity
        if not (
            opportunity := await self.pipeline_query_service.get_pipeline_by_id(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                exclude_archived=True,
            )
        ):
            raise ResourceNotFoundError(f"Opportunity {pipeline_id} not found")

        # 1 if opportunity closed, shift now allowed
        if opportunity.closed_at:
            return ShiftPipelineStageResponseV2(
                pipeline=opportunity,
                shifted=False,
                violation_description="Closed Opportunities are not allowed to switch Pipeline.",
            )

        # get target pipeline select list DTO
        if not (
            target_pipeline_stage_select_list_dto
            := await self.pipeline_stage_sl_service.get_pipeline_stage_select_list_dto(
                select_list_id=req.target_pipeline_stage_select_list_id,
                organization_id=organization_id,
            )
        ):
            raise ResourceNotFoundError(
                f"Target Pipeline: {req.target_pipeline_stage_select_list_id} not found."
            )

        # validate target pipeline status
        if (
            target_pipeline_stage_select_list_dto.select_list_status
            != SelectListStatus.ACTIVE
        ):
            raise InvalidArgumentError(
                f"Target Pipeline: {req.target_pipeline_stage_select_list_id} is not active."
            )

        # validate target pipeline has first active stage
        if not target_pipeline_stage_select_list_dto.first_active_pipeline_stage_slv:
            raise InvalidArgumentError(
                f"Target Pipeline: {req.target_pipeline_stage_select_list_id} does not have any active stage."
            )

        # validate target stage id exist in target pipeline stages
        if req.target_pipeline_stage_select_list_value_id:
            target_pipeline_stage_slv_dto = target_pipeline_stage_select_list_dto.get_effective_pipeline_stage_value(
                pipeline_stage_slv_id=req.target_pipeline_stage_select_list_value_id
            )
            if (
                target_pipeline_stage_slv_dto.select_list_value_status
                != SelectListValueStatus.ACTIVE
            ):
                raise InvalidArgumentError(
                    f"Target Pipeline stage: {req.target_pipeline_stage_select_list_value_id} is not active."
                )
            # if target stage is close stage, validate closed_reason_select_list_value_ids
            if (
                target_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.outcome_state
                is not None
            ):
                try:
                    await self._validate_closed_reason_for_stage(
                        organization_id=organization_id,
                        stage_value_dto=target_pipeline_stage_slv_dto,
                        closed_reason_select_list_value_ids=req.target_pipeline_closed_reason_select_list_value_ids,
                        closed_reason_custom_detail=req.target_pipeline_closed_reason_custom_detail,
                    )
                except InvalidArgumentError:
                    return ShiftPipelineStageResponseV2(
                        pipeline=opportunity,
                        shifted=False,
                        violated_default_rule=ShiftPipelineStageDefaultRule.CLOSED_REASON_REQUIRED,
                    )

        # validation done

        # now move to new pipeline stage
        await self.pipeline_repository.update_pipeline_stages_in_order(
            organization_id=organization_id,
            pipeline_id=opportunity.id,
            user_id=user_id,
            is_closed_before_update=False,
            is_closed_after_update=False,
            ordered_stage_ids=[
                target_pipeline_stage_select_list_dto.first_active_pipeline_stage_slv.select_list_value_id
            ],
            new_status=PipelineStatus.PROSPECT,
            account_status_update=None,
        )

        # verify new pipeline
        opportunity = await self.pipeline_query_service.get_pipeline_by_id(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            exclude_archived=True,
        )

        # now move to final stage, if requested
        if (
            not req.target_pipeline_stage_select_list_value_id
            or opportunity.stage_id == req.target_pipeline_stage_select_list_value_id
        ):
            return ShiftPipelineStageResponseV2(
                pipeline=opportunity,
                shifted=True,
            )

        return await self.shift_pipeline_stage_via_exit_criteria(
            organization_id=organization_id,
            user_id=user_id,
            pipeline=opportunity,
            req=ShiftPipelineStageRequestV2(
                target_stage_id=req.target_pipeline_stage_select_list_value_id,
                closed_reason_select_list_value_ids=req.target_pipeline_closed_reason_select_list_value_ids,
                closed_reason_custom_detail=req.target_pipeline_closed_reason_custom_detail,
            ),
        )

    # TODO: Don't use this yet, WIP for new exit criteria
    async def shift_pipeline_stage_via_exit_criteria(  # noqa: C901, PLR0911, PLR0912
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        pipeline: PipelineV2,
        req: ShiftPipelineStageRequestV2,
        user_is_admin: bool = False,
    ) -> ShiftPipelineStageResponseV2:
        """
        Shifts the pipeline stage for a pipeline.
        """

        # 01 get target and current pipeline stages
        target_pipeline_stage_slv_dto = (
            await self.pipeline_stage_sl_service.validate_pipeline_stage_reference(
                organization_id=organization_id,
                pipeline_stage_select_list_value_id=req.target_stage_id,
            )
        )
        current_pipeline_stage_slv_dto = (
            await self.pipeline_stage_sl_service.get_pipeline_stage_slv_dto_by_slv_id(
                organization_id=organization_id,
                pipeline_stage_select_list_value_id=pipeline.stage_id,
            )
        )

        # 02 jump across currently now allowed
        # 03 no-op if target and current stage are the same
        if (
            target_pipeline_stage_slv_dto.select_list_value_id
            == current_pipeline_stage_slv_dto.select_list_value_id
        ) or (
            target_pipeline_stage_slv_dto.select_list_id
            != current_pipeline_stage_slv_dto.select_list_id
        ):
            return ShiftPipelineStageResponseV2(
                pipeline=pipeline,
                shifted=False,
            )

        # 03a validate closed reason if target stage is terminal
        closed_reason_select_list_values: list[SelectListValue] | None = None
        if (
            target_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.outcome_state
            is not None
        ):
            try:
                closed_reason_select_list_values = await self._validate_closed_reason_for_stage(
                    organization_id=organization_id,
                    stage_value_dto=target_pipeline_stage_slv_dto,
                    closed_reason_select_list_value_ids=req.closed_reason_select_list_value_ids,
                    closed_reason_custom_detail=req.closed_reason_custom_detail,
                )
            except InvalidArgumentError:
                return ShiftPipelineStageResponseV2(
                    pipeline=pipeline,
                    shifted=False,
                    violated_default_rule=ShiftPipelineStageDefaultRule.CLOSED_REASON_REQUIRED,
                )

            # stop-gap rules on pipeline stage "closed_won"
            if settings.enable_stopgap_pipeline_validation and (
                target_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.outcome_state
                == PipelineOutcomeState.CLOSED_WON
            ):
                if pipeline.amount is None:  # 0 is deemed non empty
                    logger.info(
                        f"Pipeline {pipeline.id} cannot be moved to closed_won due to empty amount"
                    )
                    return ShiftPipelineStageResponseV2(
                        pipeline=pipeline,
                        shifted=False,
                        violated_default_rule=ShiftPipelineStageDefaultRule.AMOUNT_REQUIRED,
                        violation_description="When opportunity doesn't have a valid amount set, user cannot shift its stage to a value that represents 'Closed Won'.",
                    )
                if pipeline.expires_at and pipeline.expires_at < zoned_utc_now():
                    logger.info(
                        f"Pipeline {pipeline.id} cannot be moved to closed_won due to expiration: {pipeline.expires_at}"
                    )
                    return ShiftPipelineStageResponseV2(
                        pipeline=pipeline,
                        shifted=False,
                        violated_default_rule=ShiftPipelineStageDefaultRule.VALID_EXPIRATION,
                        violation_description="When opportunity is already expired, user cannot shift its stage to a value that represents 'Closed Won'.",
                    )

        target_stage_outcome_state = target_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.outcome_state
        current_stage_outcome_state = current_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.outcome_state

        (
            target_stage_list_slvs_in_range,
            shifting_backward,
        ) = await self._find_ordered_shifting_stages_slvs(
            organization_id=organization_id,
            target_pipeline_stage_slv_dto=target_pipeline_stage_slv_dto,
            current_pipeline_stage_slv_dto=current_pipeline_stage_slv_dto,
        )
        all_exiting_stages: list[SelectListValue] = []
        if (
            target_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.outcome_state
            == PipelineOutcomeState.CLOSED_LOST
        ):
            all_exiting_stages = []
        elif shifting_backward:
            all_exiting_stages = [
                *target_stage_list_slvs_in_range[:-1],
            ]
        else:
            all_exiting_stages = [
                current_pipeline_stage_slv_dto.select_list_value,
                *target_stage_list_slvs_in_range[:-1],
            ]
        pipeline_record_validation_updater = (
            self.shift_stage_request_to_pipeline_record_validation_updater(
                req=req,
                closed_reason_select_list_values=closed_reason_select_list_values,
            )
        )
        for sl_slv in all_exiting_stages:
            eval_result = await self.stage_criteria_service_v2.evaluate_stage_exit_criteria(
                organization_id=organization_id,
                user_id=user_id,
                record_id=pipeline.id,
                stage_value_id=sl_slv.id,
                primary_object_identifier=PipelineV2.object_id,
                object_record_primary_data_updater=pipeline_record_validation_updater,
            )
            # fail and return if any stage in range fails criteria
            match eval_result.result:
                case StageCriteriaResultState.success:
                    pass
                case StageCriteriaResultState.soft_failure:
                    if req.soft_failure_override:
                        logger.info(
                            "Soft failure override applied for stage criteria evaluation",
                            pipeline_id=pipeline.id,
                            stage_value_id=sl_slv.id,
                            override_reason=req.soft_failure_override.override_reason,
                        )
                    else:
                        return ShiftPipelineStageResponseV2(
                            pipeline=await self.get_pipeline_by_id(
                                pipeline_id=pipeline.id,
                                organization_id=organization_id,
                            ),
                            shifted=False,
                            stage_criteria_evaluation_result=eval_result,
                            violation_description="one or more stage criteria failed at data accuracy validation",
                        )
                case StageCriteriaResultState.failure:
                    if user_is_admin and req.soft_failure_override:
                        logger.info(
                            "Admin override applied for stage criteria evaluation",
                            pipeline_id=pipeline.id,
                            stage_value_id=sl_slv.id,
                            override_reason=req.soft_failure_override.override_reason,
                        )
                        # todo: actually persist the override trace
                    else:
                        return ShiftPipelineStageResponseV2(
                            pipeline=await self.get_pipeline_by_id(
                                pipeline_id=pipeline.id,
                                organization_id=organization_id,
                            ),
                            shifted=False,
                            stage_criteria_evaluation_result=eval_result,
                            violation_description="one or more stage criteria failed",  # TODO: add more details
                        )
                case _ as unreachable:
                    assert_never(unreachable)

        # 05 update pipeline stage
        is_closed_after_update = target_stage_outcome_state is not None
        is_closed_before_update = current_stage_outcome_state is not None

        account_state_update = (
            await self._resolve_account_state_update_for_pipeline_stage_shift(
                organization_id=organization_id,
                account_id=pipeline.account_id,
                pipeline_id=pipeline.id,
                current_outcome_state=current_stage_outcome_state,
                target_outcome_state=target_stage_outcome_state,
            )
        )
        await self.pipeline_repository.update_pipeline_stages_in_order(
            organization_id=organization_id,
            pipeline_id=pipeline.id,
            user_id=user_id,
            is_closed_before_update=is_closed_before_update,
            is_closed_after_update=is_closed_after_update,
            ordered_stage_ids=[slv.id for slv in target_stage_list_slvs_in_range],
            new_status=target_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.pipeline_status,
            new_closed_reason_custom_detail=req.closed_reason_custom_detail
            if is_closed_after_update
            else None,
            new_closed_reason_select_list_value_ids=req.closed_reason_select_list_value_ids
            if is_closed_after_update
            else None,
            account_status_update=account_state_update,
        )
        return ShiftPipelineStageResponseV2(
            pipeline=await self.get_pipeline_by_id(
                pipeline_id=pipeline.id,
                organization_id=organization_id,
            ),
            shifted=True,
        )

    async def authed_evaluate_pipeline_current_stage_exit(
        self,
        *,
        user_auth_context: UserAuthContext,
        entity_id: UUID,
    ) -> StageCriteriaEvaluationResultV2:
        with logger.contextualize(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            pipeline_id=entity_id,
        ):
            logger.info("Evaluate pipeline current stage exit request")
            await self.error_if_no_entity_access(
                user_auth_context=user_auth_context,
                entity_id=entity_id,
                access_check_function=self.can_access_entity_for_read,
            )
            return await self.evaluate_pipeline_current_stage_exit(
                organization_id=user_auth_context.organization_id,
                user_id=user_auth_context.user_id,
                pipeline_id=entity_id,
            )

    async def evaluate_pipeline_current_stage_exit(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID,
    ) -> StageCriteriaEvaluationResultV2:
        """
        Evaluates the pipeline stage exit criteria for a pipeline.
        """
        # 00 fetch non-archived pipeline
        if not (
            pipeline := await self.pipeline_query_service.get_pipeline_by_id(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                exclude_archived=True,
            )
        ):
            raise ResourceNotFoundError(f"Pipeline {pipeline_id} not found")

        return await self.stage_criteria_service_v2.evaluate_stage_exit_criteria(
            organization_id=organization_id,
            user_id=user_id,
            record_id=pipeline.id,
            stage_value_id=pipeline.stage_id,
            primary_object_identifier=PipelineV2.object_id,
        )

    @classmethod
    def shift_stage_request_to_pipeline_record_validation_updater(
        cls,
        req: ShiftPipelineStageRequest | ShiftPipelineStageRequestV2,
        closed_reason_select_list_values: list[SelectListValue] | None = None,
    ) -> ObjectRecordPrimaryDataUpdater | None:
        if (not req.closed_reason_select_list_value_ids) and (
            not req.closed_reason_custom_detail
        ):
            return None

        def update_pipeine_v2_close_reason(pipeline: PipelineV2) -> PipelineV2:
            # note: we are not passing in and update the actual nested close reason select list value for now
            # as the internal properties of these select list value are not used / exposed in FE
            return strict_model_copy(
                pipeline,
                closed_reason_select_list_value_ids=req.closed_reason_select_list_value_ids,
                closed_reason_custom_detail=req.closed_reason_custom_detail,
                closed_reason_select_list_values=[
                    select_list_value_lite_from_db(select_list_value=slv)
                    for slv in closed_reason_select_list_values
                ]
                if closed_reason_select_list_values is not None
                else None,
            )

        # todo(xw): figure out typing later

        return update_pipeine_v2_close_reason  # type: ignore

    async def _resolve_account_state_update_for_pipeline_stage_shift(
        self,
        organization_id: UUID,
        account_id: UUID,
        pipeline_id: UUID,
        current_outcome_state: PipelineOutcomeState | None,
        target_outcome_state: PipelineOutcomeState | None,
    ) -> AccountStatusUpdateForPipelineStageShift | None:
        if current_outcome_state == target_outcome_state:
            return None

        if (
            current_outcome_state == PipelineOutcomeState.CLOSED_WON
        ):  # current != target
            # in this case, the pipeline is shifted back to a non-won stage, if this is the only pipeline for the account,
            # we should set the account state from its current stage to selling, if its current state is customer
            # we need to check on the current account state as an optimistic lock, since there might be a race condition
            # where another process is in the mean time also trying to update the account state or creating a new winning pipeline
            current_account = (
                await self.account_repository.find_by_tenanted_primary_key_or_fail(
                    table_model=Account,
                    exclude_deleted_or_archived=False,
                    organization_id=organization_id,
                    id=account_id,
                )
            )
            if current_account.status == AccountStatus.CUSTOMER:
                # find all pipelines for this account
                _sibling_pipelines = (
                    await self.pipeline_query_service.map_pipeline_v2_by_account_ids(
                        organization_id=organization_id,
                        account_ids={account_id},
                        exclude_archived=True,
                    )
                )
                # and if any sibling pipeline is also won, we don't need to change the account state
                for _sibling_pipeline in _sibling_pipelines.get(account_id, []):
                    if (
                        (_sibling_pipeline.id != pipeline_id)
                        and (
                            _sibling_pipeline.stage.outcome_state
                            == PipelineOutcomeState.CLOSED_WON
                        )
                        and (not _sibling_pipeline.archived_at)
                    ):
                        return None

                return AccountStatusUpdateForPipelineStageShift(
                    to_account_status=AccountStatus.SELLING,
                    from_account_status=AccountStatus.CUSTOMER,
                )
            else:
                return None
        elif target_outcome_state == PipelineOutcomeState.CLOSED_WON:
            # in this case, the pipeline is shifted to a won stage, we don't care the current account state,
            # we should simply set the account state to customer
            return AccountStatusUpdateForPipelineStageShift(
                to_account_status=AccountStatus.CUSTOMER,
                from_account_status=UNSET,
            )
        else:
            return None

    async def validate_shift_process(
        self,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID,
        target_pipeline_stage_slv_dto: PipelineStageSelectListValueDto,
        stage_shift_forward: bool,
        existing_approve_request: ApprovalRequest | None,
    ) -> tuple[bool, ApprovalRequest | None]:
        """
        cases:
            if forward: return true
            if backward:
                if request has approve_request_id, verify request:
                    if request approved, return true
                    else -> return existing approve request
                else -> create new approve request
        """
        if stage_shift_forward:
            # Case 1: stage criteria validate pass and forward, should shift
            return True, None
        # Case 2: stage criteria validate pass and backward
        elif existing_approve_request:
            if existing_approve_request.status == ApprovalRequestStatus.APPROVED:
                return True, existing_approve_request
            else:
                return False, existing_approve_request
        else:
            # Create new approve request
            new_backward_request = await self.approval_service.create_approval_request(
                user_id=user_id,
                organization_id=organization_id,
                create_approval_request=UpsertApprovalRequestRequest(
                    type=ApprovalRequestType.PIPELINE_STAGE_BACKWARD,
                    description="Backward shift approval request",
                    display_name="Backward Approval",
                    reference=PipelineStageBackwardTransitApprovalRequestReference(
                        reference_model=ApprovalRequestType.PIPELINE_STAGE_BACKWARD,
                        pipeline_id=pipeline_id,
                        target_stage_select_list_value_id=target_pipeline_stage_slv_dto.select_list_value_id,
                    ),
                ),
            )
            return False, new_backward_request

    async def list_contact_pipeline_associations_by_pipeline_id(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        primary_association_only: bool = False,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineAssociation]:
        return await self.pipeline_query_service.list_contact_pipeline_associations_by_pipeline_id(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            primary_association_only=primary_association_only,
            exclude_archived=exclude_archived,
        )

    async def list_contact_pipeline_associations_by_contact_id(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None = None,
        pipeline_id: UUID | None = None,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineAssociation]:
        return await self.pipeline_query_service.list_contact_pipeline_associations_by_contact_id(
            organization_id=organization_id,
            contact_id=contact_id,
            account_id=account_id,
            pipeline_id=pipeline_id,
            exclude_archived=exclude_archived,
        )

    async def authed_upsert_contact_pipeline_association(
        self,
        *,
        user_auth_context: UserAuthContext,
        entity_id: UUID,
        req: UpsertContactPipelineAssociationRequests,
    ) -> UpsertContactPipelineAssociationResult:
        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            entity_id=entity_id,
            req=req,
        ).info("Upsert contact pipeline association request")
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=entity_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.upsert_contact_pipeline_association(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            pipeline_id=entity_id,
            req=req,
        )

    async def upsert_contact_pipeline_association(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID,
        req: UpsertContactPipelineAssociationRequests,
    ) -> UpsertContactPipelineAssociationResult:
        """
        Upserts the contact pipeline associations for a pipeline.
        - Any other existing associations that are not specified in the request will be preserved.
        - If a new primary association is specified in the request, it will be "promoted" to the
            primary association and any existing primary association (if different) will be demoted to an additional association.
        - If a new primary association is not specified in the request:
            - If the existing primary association is not in the additional contacts,
                the existing primary association will be preserved.
            - If the existing primary association is in the additional contacts, then the upsert
                will fail atomically, since the end result must have exactly one active primary association.
        """
        db_associations: list[ContactPipelineAssociation] = []
        if req.primary:
            db_associations.append(
                contact_pipeline_association_request_to_db_contact_pipeline_association(
                    organization_id=organization_id,
                    pipeline_id=pipeline_id,
                    user_id=user_id,
                    is_primary=True,
                    contact_pipeline_association_request=req.primary,
                )
            )
        if req.additional:
            db_associations.extend(
                contact_pipeline_association_request_to_db_contact_pipeline_association(
                    organization_id=organization_id,
                    pipeline_id=pipeline_id,
                    user_id=user_id,
                    is_primary=False,
                    contact_pipeline_association_request=req,
                )
                for req in req.additional
            )
        result = await self.pipeline_repository.upsert_or_put_all_contact_pipeline_associations(
            organization_id=organization_id,
            requesting_user_id=user_id,
            pipeline_id=pipeline_id,
            adding_contact_pipeline_associations=db_associations,
            archive_unspecified_existing_associations=False,
        )

        current_pipeline = await self.get_pipeline_by_id(
            pipeline_id=pipeline_id,
            organization_id=organization_id,
            exclude_archived=False,
        )

        return UpsertContactPipelineAssociationResult(
            pipeline=current_pipeline,
            upserted=result.upserted[0],
            demoted_primary=result.demoted_primary,
        )

    async def patch_contact_pipeline_associations(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID,
        req: dict[UUID, ContactPipelineAssociationUpdate],
    ) -> list[UpsertContactPipelineAssociationResult]:
        """
        Patch the contact pipeline associations for a pipeline.
        - Any other existing associations that are not specified in the request will be preserved.
        - If a new primary association is specified in the request, it will be "promoted" to the
            primary association and any existing primary association (if different) will be demoted to an additional association.
        - If a new primary association is not specified in the request:
            - If the existing primary association is not in the additional contacts,
                the existing primary association will be preserved.
            - If the existing primary association is in the additional contacts, then the upsert
                will fail atomically, since the end result must have exactly one active primary association.
        """
        contact_pipeline_associations = await self.pipeline_repository.list_contact_pipeline_associations_by_pipeline_id(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
        )
        current_contact_pipeline_association_map = {
            association.contact_id: association
            for association in contact_pipeline_associations
        }

        db_associations: list[ContactPipelineAssociation] = []
        for contact_id, update_association in req.items():
            if contact_id not in current_contact_pipeline_association_map:
                raise ResourceNotFoundError(
                    f"Record not found for table contact_pipeline_association "
                    f"with column values pipeline_id:{pipeline_id}, contact_id:{contact_id}",
                )
            current_contact_pipeline_association = (
                current_contact_pipeline_association_map[contact_id]
            )
            db_associations.append(
                current_contact_pipeline_association.model_copy(
                    update=update_association.flatten_specified_values()
                )
            )

        result = await self.pipeline_repository.upsert_or_put_all_contact_pipeline_associations(
            organization_id=organization_id,
            requesting_user_id=user_id,
            pipeline_id=pipeline_id,
            adding_contact_pipeline_associations=db_associations,
            archive_unspecified_existing_associations=False,
        )

        current_pipeline = await self.get_pipeline_by_id(
            pipeline_id=pipeline_id,
            organization_id=organization_id,
            exclude_archived=False,
        )

        return [
            UpsertContactPipelineAssociationResult(
                pipeline=current_pipeline,
                upserted=upserted_association,
                demoted_primary=result.demoted_primary,
            )
            for upserted_association in result.upserted
        ]

    async def authed_archive_contact_pipeline_association(
        self,
        *,
        user_auth_context: UserAuthContext,
        entity_id: UUID,
        req: ArchiveContactPipelineAssociationRequest,
    ) -> ArchiveContactPipelineAssociationResult:
        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            entity_id=entity_id,
            req=req,
        ).info("Archive contact pipeline association request")
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=entity_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.archive_contact_pipeline_association(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            pipeline_id=entity_id,
            req=req,
        )

    async def archive_contact_pipeline_association(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID,
        req: ArchiveContactPipelineAssociationRequest,
    ) -> ArchiveContactPipelineAssociationResult:
        """
        Removes a contact pipeline association for a pipeline.
        - Any non-primary associations can be removed freely.
        - The primary association can only be archived when the pipeline is also archived.
        """
        await self.pipeline_repository.find_by_tenanted_primary_key_or_fail(
            Pipeline,
            organization_id=organization_id,
            id=pipeline_id,
            exclude_deleted_or_archived=True,
        )
        archived_associations: list[ContactPipelineAssociation] = [
            await self.pipeline_repository.archive_contact_pipeline_association(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                contact_id=contact_id,
                user_id=user_id,
            )
            for contact_id in req.contact_ids
        ]
        pipeline = await self.get_pipeline_by_id(
            pipeline_id=pipeline_id,
            organization_id=organization_id,
            exclude_archived=True,
        )
        return ArchiveContactPipelineAssociationResult(
            pipeline=pipeline,
            archived=archived_associations,
        )

    async def get_next_primary_contact_for_pipeline(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        excluding_contact_id: UUID,
    ) -> ContactPipelineAssociation | None:
        """
        Find the next suitable primary contact for a pipeline,
        typically the most recent additional contact.
        """
        candidate_associations = (
            await self.list_contact_pipeline_associations_by_pipeline_id(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                exclude_archived=True,
            )
        )

        # Sort by creation date, most recent first
        candidate_associations.sort(key=lambda x: x.created_at, reverse=True)

        # Find first non-archived contact that isn't the one being excluded
        candidate = next(
            (
                asso
                for asso in candidate_associations
                if (asso.contact_id != excluding_contact_id) and (not asso.archived_at)
            ),
            None,
        )

        return candidate if candidate else None

    async def replace_contact_pipeline_association(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID,
        req: ReplaceContactPipelineAssociationRequest,
    ) -> ReplaceContactPipelineAssociationResult:
        """
        Different from archive_contact_pipeline_association, this will replace the
        specified "archiving_contact_id" with the specified "replacing_contact_id".

        When the "replacing_contact_id" is not specified and the "archiving_contact_id"
        is the primary association, the primary association will be demoted to the most recent
        additional association.
        """
        archiving_contact_pipeline_association = (
            await self.pipeline_query_service.get_contact_pipeline_association(
                organization_id=organization_id,
                contact_id=req.archiving_contact_id,
                pipeline_id=pipeline_id,
                exclude_archived=False,
            )
        )
        if archiving_contact_pipeline_association.archived_at:
            pipeline = await self.get_pipeline_by_id(
                pipeline_id=pipeline_id,
                organization_id=organization_id,
                exclude_archived=False,
            )
            return ReplaceContactPipelineAssociationResult(
                pipeline=pipeline,
                archived=archiving_contact_pipeline_association,
                replacement=None,
            )

        replacing_contact_association_request = (
            req.replacing_contact_association_request
        )
        upserted_replacement: ContactPipelineAssociation | None = None
        if archiving_contact_pipeline_association.is_primary and (
            not replacing_contact_association_request
        ):
            # Must have a replacing contact figured out if archiving the primary association for an active pipeline
            replacing_contact_association = (
                await self.get_next_primary_contact_for_pipeline(
                    organization_id=organization_id,
                    pipeline_id=pipeline_id,
                    excluding_contact_id=req.archiving_contact_id,
                )
            )
            if not replacing_contact_association:
                raise InvalidArgumentError(
                    "No replacing contact specified for primary association"
                )

            replacing_contact_association_request = ContactPipelineAssociationRequest(
                contact_id=replacing_contact_association.contact_id,
                note=replacing_contact_association.note,
            )
        if replacing_contact_association_request:
            _existing_replacing_contact_association = (
                await self.pipeline_query_service.find_contact_pipeline_association(
                    organization_id=organization_id,
                    contact_id=replacing_contact_association_request.contact_id,
                    pipeline_id=pipeline_id,
                    exclude_archived=True,
                )
                if replacing_contact_association_request
                else None
            )
            _replacement_should_be_primary = (
                archiving_contact_pipeline_association.is_primary
                or (
                    _existing_replacing_contact_association
                    and _existing_replacing_contact_association.is_primary
                )
            )
            _upsert_request = (
                UpsertContactPipelineAssociationRequests(
                    primary=replacing_contact_association_request,
                )
                if (
                    archiving_contact_pipeline_association.is_primary
                    or _replacement_should_be_primary
                )
                else UpsertContactPipelineAssociationRequests(
                    additional=[replacing_contact_association_request]
                )
            )
            upsert_result = await self.upsert_contact_pipeline_association(
                organization_id=organization_id,
                user_id=user_id,
                pipeline_id=pipeline_id,
                req=_upsert_request,
            )
            upserted_replacement = upsert_result.upserted

        archival_result = await self.archive_contact_pipeline_association(
            organization_id=organization_id,
            user_id=user_id,
            pipeline_id=pipeline_id,
            req=ArchiveContactPipelineAssociationRequest(
                contact_ids={req.archiving_contact_id}
            ),
        )
        logger.info(
            "archived contact pipeline association",
            pipeline_id=pipeline_id,
            archived_contact_id=req.archiving_contact_id,
            result=archival_result,
        )
        return ReplaceContactPipelineAssociationResult(
            pipeline=archival_result.pipeline,
            archived=archival_result.archived[0],
            replacement=upserted_replacement,
        )

    async def put_all_contact_pipeline_associations(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID,
        req: FullContactPipelineAssociationRequests,
    ) -> PutAllContactPipelineAssociationsResult:
        """
        Overrides the contact pipeline associations for a pipeline.
        """
        if not (
            await self.get_pipeline_by_id(
                pipeline_id=pipeline_id,
                organization_id=organization_id,
            )
        ):
            raise ResourceNotFoundError(f"Pipeline {pipeline_id} not found")

        db_associations: list[ContactPipelineAssociation] = [
            contact_pipeline_association_request_to_db_contact_pipeline_association(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                user_id=user_id,
                is_primary=True,
                contact_pipeline_association_request=req.primary,
            )
        ]
        if req.additional:
            db_associations.extend(
                contact_pipeline_association_request_to_db_contact_pipeline_association(
                    organization_id=organization_id,
                    pipeline_id=pipeline_id,
                    user_id=user_id,
                    is_primary=False,
                    contact_pipeline_association_request=additional_association_req,
                )
                for additional_association_req in req.additional
            )

        result = await self.pipeline_repository.upsert_or_put_all_contact_pipeline_associations(
            organization_id=organization_id,
            requesting_user_id=user_id,
            pipeline_id=pipeline_id,
            adding_contact_pipeline_associations=db_associations,
            archive_unspecified_existing_associations=True,
        )
        current_pipeline = await self.get_pipeline_by_id(
            pipeline_id=pipeline_id,
            organization_id=organization_id,
            exclude_archived=False,
        )
        return PutAllContactPipelineAssociationsResult(
            pipeline=current_pipeline,
            current=result.upserted,
            archived=result.archived,
            demoted_primary=result.demoted_primary,
        )

    # ContactPipelineRole adapter methods

    async def authed_upsert_contact_pipeline_role(
        self,
        *,
        user_auth_context: UserAuthContext,
        entity_id: UUID,  # pipeline_id
        req: UpsertContactPipelineRoleRequests,
    ) -> UpsertContactPipelineRoleResult:
        """
        Upserts a ContactPipelineRole by adapting the existing
        authed_upsert_contact_pipeline_association method.
        """
        # Convert domain request to ContactPipelineAssociationRequest objects
        upsert_req = UpsertContactPipelineAssociationRequests(
            primary=ContactPipelineAssociationRequest(
                contact_id=req.primary.contact_id,
                note=req.primary.note,
                role_types=req.primary.role_types,
            )
            if req.primary
            else None,
            additional=[
                ContactPipelineAssociationRequest(
                    contact_id=additional.contact_id,
                    note=additional.note,
                    role_types=additional.role_types,
                )
                for additional in (req.additional or [])
            ]
            if req.additional
            else None,
        )

        # Delegate to existing method
        result = await self.authed_upsert_contact_pipeline_association(
            user_auth_context=user_auth_context,
            entity_id=entity_id,
            req=upsert_req,
        )

        # Convert result from ContactPipelineAssociation to ContactPipelineRole
        return UpsertContactPipelineRoleResult(
            pipeline=result.pipeline,
            upserted=contact_pipeline_role_from_db(
                contact_pipeline_association=result.upserted
            ),
            demoted_primary=contact_pipeline_role_from_db(
                contact_pipeline_association=result.demoted_primary
            )
            if result.demoted_primary
            else None,
        )

    async def authed_patch_contact_pipeline_role(
        self,
        *,
        user_auth_context: UserAuthContext,
        entity_id: UUID,  # pipeline_id
        req: ContactPipelineRoleRequest,
    ) -> UpsertContactPipelineRoleResult:
        """
        Patch a ContactPipelineRole by adapting the existing
        authed_upsert_contact_pipeline_association method.
        """
        logger.info(
            "patch contact pipeline role request",
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            entity_id=entity_id,
            req=req,
        )

        # Convert domain request to ContactPipelineAssociationRequest objects
        if specified(req.role_types):
            contact_pipeline_association = await self.find_contact_pipeline_association(
                organization_id=user_auth_context.organization_id,
                contact_id=req.contact_id,
                pipeline_id=entity_id,
            )
            if not contact_pipeline_association:
                raise ResourceNotFoundError(
                    f"ContactPipelineAssociation not found for contact {req.contact_id} and pipeline {entity_id}"
                )
            await self.contact_pipeline_role_ai_rec_service.patch_record(
                organization_id=user_auth_context.organization_id,
                user_id=user_auth_context.user_id,
                record_id=contact_pipeline_association.id,
                patch_request=PatchContactPipelineRoleTypesRequest(
                    role_types=req.role_types,
                ),
            )
        patch_req = ContactPipelineAssociationUpdate(
            note=req.note,
            # Deliberately not updating role_types via the normal patch path, deliberately
            # delegating to the AI Rec service to handle this above.
            # role_types=req.role_types,
            is_primary=req.is_primary_contact,
            updated_by_user_id=user_auth_context.user_id,
        )

        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=entity_id,
            access_check_function=self.can_access_entity_for_patch,
        )

        result = await self.patch_contact_pipeline_associations(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            pipeline_id=entity_id,
            req={req.contact_id: patch_req},
        )

        upserted = await self.get_contact_pipeline_role(
            organization_id=user_auth_context.organization_id,
            pipeline_id=entity_id,
            contact_id=req.contact_id,
        )

        # Convert result from ContactPipelineAssociation to ContactPipelineRole
        return UpsertContactPipelineRoleResult(
            pipeline=result[0].pipeline,
            upserted=upserted,
            demoted_primary=contact_pipeline_role_from_db(
                contact_pipeline_association=result[0].demoted_primary
            )
            if result[0].demoted_primary
            else None,
        )

    async def get_contact_pipeline_role(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        contact_id: UUID,
    ) -> ContactPipelineRole:
        contact_pipeline_association = await self.find_contact_pipeline_association(
            organization_id=organization_id,
            contact_id=contact_id,
            pipeline_id=pipeline_id,
        )
        if not contact_pipeline_association:
            raise ResourceNotFoundError(
                f"ContactPipelineAssociation not found for contact {contact_id} and pipeline {pipeline_id}"
            )
        property_metadata = await self.contact_pipeline_role_ai_rec_service.find_property_metadata_for_record(
            organization_id=organization_id,
            record_ids={contact_pipeline_association.id},
            sobject_name=StdObjectIdentifiers.contact_pipeline_role,
        )
        return contact_pipeline_role_from_db(
            contact_pipeline_association=contact_pipeline_association,
            property_metadata=property_metadata.get(
                contact_pipeline_association.id, None
            ),
        )

    async def authed_list_contact_pipeline_roles_by_pipeline_id(
        self,
        *,
        user_auth_context: UserAuthContext,
        entity_id: UUID,  # pipeline_id
        primary_association_only: bool = False,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineRole]:
        """
        Lists ContactPipelineRoles by adapting the existing
        list_contact_pipeline_associations_by_pipeline_id method.
        """
        # First validate access using existing auth check
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=entity_id,
            access_check_function=self.can_access_entity_for_read,
        )

        return await self.list_contact_pipeline_role_by_pipeline_id(
            organization_id=user_auth_context.organization_id,
            pipeline_id=entity_id,
            primary_association_only=primary_association_only,
            exclude_archived=exclude_archived,
        )

    async def list_contact_pipeline_role_by_pipeline_id(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        primary_association_only: bool = False,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineRole]:
        return (
            await self.pipeline_query_service.list_contact_pipeline_role_by_pipeline_id(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                primary_association_only=primary_association_only,
                exclude_archived=exclude_archived,
            )
        )

    async def authed_list_contact_pipeline_roles_by_contact_id(
        self,
        *,
        user_auth_context: UserAuthContext,
        contact_id: UUID,
        account_id: UUID | None = None,
        pipeline_id: UUID | None = None,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineRole]:
        """
        Lists ContactPipelineRoles by adapting the existing
        list_contact_pipeline_associations_by_contact_id method.
        """
        # Delegate to existing method
        contact_pipeline_associations = (
            await self.list_contact_pipeline_associations_by_contact_id(
                organization_id=user_auth_context.organization_id,
                contact_id=contact_id,
                account_id=account_id,
                pipeline_id=pipeline_id,
                exclude_archived=exclude_archived,
            )
        )

        # Convert from ContactPipelineAssociation to ContactPipelineRole
        return [
            contact_pipeline_role_from_db(contact_pipeline_association=association)
            for association in contact_pipeline_associations
        ]

    async def authed_archive_contact_pipeline_role(
        self,
        *,
        user_auth_context: UserAuthContext,
        entity_id: UUID,  # pipeline_id
        contact_id: UUID,
    ) -> ContactPipelineRole:
        """
        Archives a ContactPipelineRole by adapting the existing
        authed_archive_contact_pipeline_association method.
        """
        # Create request with the contact_id
        archive_req = ArchiveContactPipelineAssociationRequest(contact_ids={contact_id})

        # Delegate to existing method
        result = await self.authed_archive_contact_pipeline_association(
            user_auth_context=user_auth_context,
            entity_id=entity_id,
            req=archive_req,
        )

        # Return the first (and only) archived association converted to ContactPipelineRole
        return contact_pipeline_role_from_db(
            contact_pipeline_association=result.archived[0]
        )

    async def authed_put_all_contact_pipeline_roles(
        self,
        *,
        user_auth_context: UserAuthContext,
        entity_id: UUID,  # pipeline_id
        req: FullContactPipelineRoleRequests,
    ) -> PutAllContactPipelineRolesResult:
        """
        Replaces all ContactPipelineRoles by adapting the existing
        put_all_contact_pipeline_associations method.
        """
        # First validate access using existing auth check
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=entity_id,
            access_check_function=self.can_access_entity_for_patch,
        )

        # Convert to ContactPipelineAssociation request format
        pipeline_association_put_request = FullContactPipelineAssociationRequests(
            primary=ContactPipelineAssociationRequest(
                contact_id=req.primary.contact_id,
                note=req.primary.note,
                role_types=req.primary.role_types,
            ),
            additional=[
                ContactPipelineAssociationRequest(
                    contact_id=additional.contact_id,
                    note=additional.note,
                    role_types=additional.role_types,
                )
                for additional in (req.additional or [])
            ]
            if req.additional
            else None,
        )

        # Collect all contact IDs from both primary and additional associations
        contact_ids_with_role_types_specified: dict[
            UUID, list[ContactPipelineRoleType] | None
        ] = {}
        if pipeline_association_put_request.primary and specified(
            pipeline_association_put_request.primary.role_types
        ):
            contact_ids_with_role_types_specified[
                pipeline_association_put_request.primary.contact_id
            ] = pipeline_association_put_request.primary.role_types
        if pipeline_association_put_request.additional:
            for additional in pipeline_association_put_request.additional:
                if specified(additional.role_types):
                    contact_ids_with_role_types_specified[additional.contact_id] = (
                        additional.role_types
                    )
        existing_contact_pipeline_associations = (
            await self.list_contact_pipeline_associations_by_pipeline_id(
                organization_id=user_auth_context.organization_id,
                pipeline_id=entity_id,
                exclude_archived=True,
            )
        )
        for existing_association in existing_contact_pipeline_associations:
            if existing_association.contact_id in contact_ids_with_role_types_specified:
                await self.contact_pipeline_role_ai_rec_service.patch_record(
                    organization_id=user_auth_context.organization_id,
                    user_id=user_auth_context.user_id,
                    record_id=existing_association.id,
                    patch_request=PatchContactPipelineRoleTypesRequest(
                        role_types=contact_ids_with_role_types_specified[
                            existing_association.contact_id
                        ],
                    ),
                )

        # Delegate to existing method
        result = await self.put_all_contact_pipeline_associations(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            pipeline_id=entity_id,
            req=pipeline_association_put_request,
        )

        # Convert from ContactPipelineAssociation to ContactPipelineRole
        return PutAllContactPipelineRolesResult(
            pipeline=result.pipeline,
            current=[
                contact_pipeline_role_from_db(contact_pipeline_association=assoc)
                for assoc in result.current
            ],
            archived=[
                contact_pipeline_role_from_db(contact_pipeline_association=assoc)
                for assoc in result.archived
            ],
            demoted_primary=contact_pipeline_role_from_db(
                contact_pipeline_association=result.demoted_primary
            )
            if result.demoted_primary
            else None,
        )

    async def _validate_pipeline_account_move(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        to_account_id: UUID,
    ) -> None:
        contact_pipeline_associations = (
            await self.list_contact_pipeline_associations_by_pipeline_id(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                exclude_archived=True,
            )
        )
        contact_ids: set[UUID] = {
            asso.contact_id for asso in contact_pipeline_associations
        }
        for contact_id in contact_ids:
            if not await self.contact_repository.find_active_contact_account_association(
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=to_account_id,
            ):
                raise InvalidArgumentError(
                    f"Contact {contact_id} is not actively associated with Account {to_account_id}, "
                    f"cannot move Pipeline before all its active contacts are associated with the new Account"
                )

    async def list_pipelines_paginated(
        self,
        organization_id: UUID,
        only_include_pipeline_ids: UnsetAware[set[UUID]] = UNSET,
        include_custom_object: bool | None = False,
        offset: int = 0,
        limit: int | None = None,
    ) -> list[PipelineV2]:
        """List pipelines with offset-based pagination.

        Args:
            organization_id: Organization ID
            only_include_pipeline_ids: Optional set of pipeline IDs to filter by
            include_custom_object: Whether to include custom object data
            offset: Number of records to skip
            limit: Maximum number of records to return
        """
        pipelines = await self.pipeline_query_service.list_pipelines(
            organization_id=organization_id,
            only_include_pipeline_ids=only_include_pipeline_ids,
            include_custom_object=include_custom_object,
        )

        if limit:
            return pipelines[offset : offset + limit]
        return pipelines[offset:]

    async def list_pipelines(
        self,
        organization_id: UUID,
        only_include_pipeline_ids: UnsetAware[set[UUID]] = UNSET,
        include_custom_object: bool | None = False,
    ) -> list[PipelineV2]:
        """
        Lists pipelines.
        """
        return await self.pipeline_query_service.list_pipelines(
            organization_id=organization_id,
            only_include_pipeline_ids=only_include_pipeline_ids,
            include_custom_object=include_custom_object,
        )

    async def get_pipeline_by_id(
        self,
        pipeline_id: UUID,
        organization_id: UUID,
        include_custom_object: bool | None = False,
        exclude_archived: bool = True,
    ) -> PipelineV2:
        """
        Gets a pipeline by id.
        """
        return await self.pipeline_query_service.get_pipeline_by_id(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            include_custom_object=include_custom_object,
            exclude_archived=exclude_archived,
        )

    async def send_pipeline_change_notification(
        self, pipeline_v2_before: PipelineV2, pipeline_v2_after: PipelineV2
    ) -> None:
        request = SendNotificationRequest(
            data=NotificationCRMChangeData(
                message="",
                action_url=f"/pipelines/{pipeline_v2_after.id}",
            ),
            reference_id=str(pipeline_v2_after.id),
            reference_id_type=NotificationReferenceIdType.PIPELINE,
            activity_id=None,
            actor_user_id=pipeline_v2_after.updated_by_user_id,
            recipient_user_ids=[pipeline_v2_after.owner_user_id],
            idempotency_key=str(uuid.uuid4()),
        )
        if pipeline_v2_before.status != pipeline_v2_after.status:
            await self.notification_service.send_notification(
                send_notification_request=request.model_copy(
                    update={
                        "data": NotificationCRMChangeData(
                            message=f"Opportunity {pipeline_v2_after.display_name} status changed from {pipeline_v2_before.status} to {pipeline_v2_after.status}",
                            action_url=f"/pipelines/{pipeline_v2_after.id}",
                        ),
                    }
                ),
                organization_id=pipeline_v2_after.organization_id,
            )

        if (
            pipeline_v2_before.stage.display_value
            != pipeline_v2_after.stage.display_value
        ):
            await self.notification_service.send_notification(
                send_notification_request=request.model_copy(
                    update={
                        "data": NotificationCRMChangeData(
                            message=f"Opportunity {pipeline_v2_after.display_name} stage changed from {pipeline_v2_before.stage.display_value} to {pipeline_v2_after.stage.display_value}",
                            action_url=f"/pipelines/{pipeline_v2_after.id}",
                        ),
                        # Use different idempotency_key if both stage and status changed
                        "idempotency_key": str(uuid.uuid4()),
                    }
                ),
                organization_id=pipeline_v2_after.organization_id,
            )

    async def find_contact_pipeline_association(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        pipeline_id: UUID,
    ) -> ContactPipelineAssociation | None:
        """
        Find a contact pipeline association for a given contact ID and pipeline ID.

        Args:
            organization_id: The organization ID
            contact_id: The contact ID
            pipeline_id: The pipeline ID

        Returns:
            The contact pipeline association if found, None otherwise
        """
        return await self.pipeline_query_service.find_contact_pipeline_association(
            organization_id=organization_id,
            contact_id=contact_id,
            pipeline_id=pipeline_id,
        )


class SingletonPipelineService(Singleton, PipelineService):
    pass


def get_pipeline_service(
    db_engine: DatabaseEngine,
) -> PipelineService:
    if SingletonPipelineService.has_instance():
        return SingletonPipelineService.get_singleton_instance()
    return SingletonPipelineService(
        pipeline_repository=PipelineRepository(
            engine=db_engine,
        ),
        address_repository=AddressRepository(
            engine=db_engine,
        ),
        contact_repository=ContactRepository(
            engine=db_engine,
        ),
        account_repository=AccountRepository(
            engine=db_engine,
        ),
        custom_object_service=CustomObjectService(
            custom_object_repo=CustomObjectRepository(engine=db_engine),
            select_list_service=get_select_list_service(engine=db_engine),
        ),
        note_repository=NoteRepository(
            engine=db_engine,
        ),
        job_service=JobService(job_repository=JobRepository(engine=db_engine)),
        user_service=get_user_service_general(db_engine=db_engine),
        pipeline_query_service=get_pipeline_query_service(db_engine=db_engine),
        pipeline_stage_sl_service=get_pipeline_stage_select_list_service(
            engine=db_engine
        ),
        stage_criteria_service=get_stage_criteria_service(
            engine=db_engine,
            domain_object_query_service=get_domain_object_query_service(
                db_engine=db_engine
            ),
        ),
        select_list_service=get_select_list_service(engine=db_engine),
        approval_service=get_approval_service_with_engine(db_engine=db_engine),
        notification_service=get_notification_service_by_db_engine(db_engine=db_engine),
        feature_flag_service=get_feature_flag_service(),
        crm_sync_push_service=get_crm_sync_push_service(db_engine=db_engine),
        stage_criteria_service_v2=get_stage_criteria_service_v2(
            engine=db_engine,
            domain_object_query_service=get_domain_object_query_service(
                db_engine=db_engine
            ),
        ),
        pipeline_qualification_property_service=get_pipeline_qualification_property_service(
            db_engine=db_engine,
        ),
        contact_pipeline_role_ai_rec_service=get_contact_pipeline_role_ai_rec_service(
            db_engine=db_engine,
        ),
    )
