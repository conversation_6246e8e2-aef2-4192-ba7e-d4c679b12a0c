from uuid import UUID

from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityDataOperationValidationError,
    IntegrityOperationValidation,
)
from salestech_be.core.pipeline.service.pipeline_query_service import (
    get_pipeline_query_service,
)
from salestech_be.core.pipeline.service.pipeline_service import get_pipeline_service
from salestech_be.core.pipeline.service_api_schema import (
    MergeContactPipelineAssociationPreviewResult,
    ReplaceContactPipelineAssociationRequest,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.contact_pipeline_association import (
    ContactPipelineAssociation,
)
from salestech_be.db.models.crm_integrity import (
    EntityType,
    IntegrityOperation,
)
from salestech_be.ree_logging import get_logger

logger = get_logger()


class PipelineDataIntegrityService:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.pipeline_service = get_pipeline_service(db_engine=db_engine)
        self.pipeline_query_service = get_pipeline_query_service(db_engine=db_engine)

    async def validate_pipeline_availability(
        self,
        pipeline_id: UUID,
        organization_id: UUID,
    ) -> list[IntegrityOperationValidation]:
        pipelines = await self.pipeline_query_service.list_pipelines(
            organization_id=organization_id,
            only_include_pipeline_ids={pipeline_id},
        )
        if not pipelines:
            return [
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.object_not_found,
                    error_message=f"pipeline {pipeline_id} not found",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.PIPELINE,
                    src_entity_id=pipeline_id,
                    dest_entity_type=EntityType.PIPELINE,
                    dest_entity_id=pipeline_id,
                )
            ]

        return []

    async def move_pipeline_to_alternative_contact_if_exists(
        self,
        src_account_id: UUID,
        moving_contact_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> list[PipelineV2]:
        contact_pipeline_associations = await self.pipeline_query_service.list_contact_pipeline_associations_by_contact_id(
            organization_id=organization_id,
            contact_id=moving_contact_id,
            account_id=src_account_id,
        )
        updated_pipelines = []
        for contact_pipeline_association in contact_pipeline_associations:
            replace_result = (
                await self.pipeline_service.replace_contact_pipeline_association(
                    organization_id=organization_id,
                    user_id=user_id,
                    pipeline_id=contact_pipeline_association.pipeline_id,
                    req=ReplaceContactPipelineAssociationRequest(
                        archiving_contact_id=moving_contact_id,
                    ),
                )
            )
            updated_pipelines.append(replace_result.pipeline)
        return updated_pipelines

    async def preview_move_pipeline_to_alternative_contact(
        self,
        src_account_id: UUID,
        moving_contact_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> MergeContactPipelineAssociationPreviewResult:
        contact_pipeline_associations = await self.pipeline_query_service.list_contact_pipeline_associations_by_contact_id(
            organization_id=organization_id,
            contact_id=moving_contact_id,
            account_id=src_account_id,
        )

        associations_to_archive: list[ContactPipelineAssociation] = []
        associations_to_promote: list[ContactPipelineAssociation] = []
        for contact_pipeline_association in contact_pipeline_associations:
            if contact_pipeline_association.is_primary:
                next_primary_contact_pipeline_association = (
                    await self.pipeline_service.get_next_primary_contact_for_pipeline(
                        organization_id=organization_id,
                        pipeline_id=contact_pipeline_association.pipeline_id,
                        excluding_contact_id=moving_contact_id,
                    )
                )
                if next_primary_contact_pipeline_association:
                    associations_to_promote.append(
                        next_primary_contact_pipeline_association
                    )

            associations_to_archive.append(contact_pipeline_association)

        return MergeContactPipelineAssociationPreviewResult(
            to_archive=associations_to_archive,
            to_create=[],
            to_promote=associations_to_promote,
        )

    async def preview_contact_pipeline_associations_for_merging_contacts(
        self,
        *,
        src_contact_id: UUID,
        dest_contact_id: UUID,
        organization_id: UUID,
    ) -> MergeContactPipelineAssociationPreviewResult:
        """
        Preview contact pipeline associations that will be (removed, created) when merging two contacts.

        NOTE: ids in returned associations have no acutal meaning, do NOT use them as reference.
        """
        src_contact_pipeline_associations = await self.pipeline_query_service.list_contact_pipeline_associations_by_contact_id(
            organization_id=organization_id,
            contact_id=src_contact_id,
        )

        src_pipeline_id_to_association_map = {
            association.pipeline_id: association
            for association in src_contact_pipeline_associations
        }

        # For associations that link to dest contacts already, we will keep the association as is.
        # So to save db effort, here we only call upsert to create associations for pipelines that
        # are NOT linked to dest contact.
        associations_to_create = [
            src_pipeline_id_to_association_map[pipeline_id].model_copy(
                update={"contact_id": dest_contact_id}
            )
            for pipeline_id in src_pipeline_id_to_association_map
        ]

        return MergeContactPipelineAssociationPreviewResult(
            to_archive=src_contact_pipeline_associations,
            to_create=associations_to_create,
            to_promote=[],
        )

    async def validate_move_pipeline_to_alternative_contact(
        self,
        src_account_id: UUID,
        moving_contact_id: UUID,
        organization_id: UUID,
    ) -> list[IntegrityOperationValidation]:
        validations = []
        contact_pipeline_associations = await self.pipeline_query_service.list_contact_pipeline_associations_by_contact_id(
            organization_id=organization_id,
            contact_id=moving_contact_id,
            account_id=src_account_id,
        )
        for contact_pipeline_association in contact_pipeline_associations:
            if contact_pipeline_association.is_primary:
                next_primary_contact_pipeline_association = (
                    await self.pipeline_service.get_next_primary_contact_for_pipeline(
                        organization_id=organization_id,
                        pipeline_id=contact_pipeline_association.pipeline_id,
                        excluding_contact_id=moving_contact_id,
                    )
                )
                if not next_primary_contact_pipeline_association:
                    validations.append(
                        IntegrityOperationValidation(
                            violation=IntegrityDataOperationValidationError.pipeline_has_no_alternative_contact,
                            error_message="cannot move pipeline to alternative contact because no alternative contact is available",
                            type=IntegrityOperation.UNASSOCIATE,
                            src_entity_type=EntityType.CONTACT,
                            src_entity_id=moving_contact_id,
                            dest_entity_type=EntityType.PIPELINE,
                            dest_entity_id=contact_pipeline_association.pipeline_id,
                        )
                    )
        return validations

    async def validate_active_contact_pipeline_association_non_existence(
        self,
        contact_id: UUID,
        organization_id: UUID,
    ) -> list[IntegrityOperationValidation]:
        validations: list[IntegrityOperationValidation] = []
        contact_pipeline_associations = await self.pipeline_query_service.list_contact_pipeline_associations_by_contact_id(
            organization_id=organization_id,
            contact_id=contact_id,
        )

        for contact_pipeline_association in contact_pipeline_associations:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.contact_has_active_pipeline_association,
                    error_message="contact has active pipeline association",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=contact_id,
                    dest_entity_type=EntityType.PIPELINE,
                    dest_entity_id=contact_pipeline_association.pipeline_id,
                )
            )
        return validations

    async def validate_account_has_no_active_pipeline_associations(
        self,
        account_id: UUID,
        organization_id: UUID,
    ) -> list[IntegrityOperationValidation]:
        validations: list[IntegrityOperationValidation] = []
        active_pipelines = (
            await self.pipeline_query_service.list_pipelines_by_account_id(
                account_id=account_id,
                organization_id=organization_id,
            )
        )

        for pipeline in active_pipelines:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.account_has_active_pipeline_association,
                    error_message="account has active pipeline association",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.ACCOUNT,
                    src_entity_id=account_id,
                    dest_entity_type=EntityType.PIPELINE,
                    dest_entity_id=pipeline.id,
                )
            )
        return validations

    async def validate_active_account_pipeline_association_existence(
        self,
        account_id: UUID,
        pipeline_id: UUID,
        organization_id: UUID,
    ) -> list[IntegrityOperationValidation]:
        validations: list[IntegrityOperationValidation] = []
        active_pipeline = [
            p
            for p in (
                await self.pipeline_query_service.list_pipelines_by_account_id(
                    account_id=account_id,
                    organization_id=organization_id,
                )
            )
            if p.id == pipeline_id
        ]

        if not active_pipeline:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.association_to_dest_does_not_exist,
                    error_message=f"account {account_id} does not associate with pipeline {pipeline_id}",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.ACCOUNT,
                    src_entity_id=account_id,
                    dest_entity_type=EntityType.PIPELINE,
                    dest_entity_id=pipeline_id,
                )
            )

        return validations


def get_pipeline_data_integrity_service(
    db_engine: DatabaseEngine,
) -> PipelineDataIntegrityService:
    return PipelineDataIntegrityService(db_engine=db_engine)
