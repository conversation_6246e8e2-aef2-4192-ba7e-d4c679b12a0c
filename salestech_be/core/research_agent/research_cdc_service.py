import time

from salestech_be.common.events import DomainEnrichedCDCEvent
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.service.contact_query_service import (
    get_contact_query_service,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.research_agent.research_agent_client import (
    get_research_agent_client,
)
from salestech_be.core.research_agent.research_agent_service import (
    ResearchAgentService,
)
from salestech_be.core.research_agent.research_agent_trigger import (
    ResearchAgentTriggerService,
)
from salestech_be.core.research_agent.types import (
    IntelInputType,
    IntelLinkedinUrlSource,
    ResearchStatus,
)
from salestech_be.core.research_agent.utils import (
    is_valid_company_linkedin_url,
    is_valid_person_linkedin_url,
)
from salestech_be.db.dao.intel_repository import IntelRepository
from salestech_be.db.dao.user_feedback_repository import UserFeedbackRepository
from salestech_be.db.models.account import Account
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.integrations.crustdata.client import CrustdataClient
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import ResearchTaskQueue
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.temporal.workflows.research_agent.schema import (
    ResearchCompanyInput,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    ResearchPersonInput,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    ResearchTime,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.validation import not_none

logger = get_logger(__name__)


def _is_company_domain_updated(
    event: DomainEnrichedCDCEvent[Account, AccountV2],
) -> bool:
    if not event.before or not event.before.domain_name:
        return False
    if "domain_name" not in event.modified_fields:
        return False
    if not event.after.domain_name:
        return False
    return True


def _is_company_domain_removed(
    event: DomainEnrichedCDCEvent[Account, AccountV2],
) -> bool:
    if not event.before or not event.before.domain_name:
        return False
    if "domain_name" not in event.modified_fields:
        return False
    if event.after.domain_name:
        return False
    return True


async def company_research_handler(
    event: DomainEnrichedCDCEvent[Account, AccountV2],
) -> None:
    """
    Handles CDC trigger for a company by initiating research workflow.

    Args:
        event: The account CDC event
    """
    if str(event.after.organization_id) in settings.research_agent_blocked_org_ids:
        logger.bind(account=event.after).info(
            "Skipping research: Account is in blocked organization"
        )
        return

    if event.before is None and event.after.created_source == CreatedSource.CSV_IMPORT:
        logger.bind(account=event.after).info(
            "account is created from csv import, will go to lowpri queue"
        )
        assigned_task_queue = ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW
    else:
        assigned_task_queue = ResearchTaskQueue.RESEARCH_TASK_QUEUE

    account = event.after

    # If the account isn't new and the domain_name is not modified, we don't need to research
    if event.before and "domain_name" not in event.modified_fields:
        return

    if _is_company_domain_removed(event):
        logger.bind(
            account_id=account.id,
            domain_name=event.before and event.before.domain_name,
        ).info("Skipping research: Company domain is removed")
        return

    # If the account doesn't have a domain_name, we don't need to research
    if not account.domain_name and not account.linkedin_url:
        logger.bind(account_id=account.id).warning(
            "No company domain extracted for account, will not research"
        )
        return

    # If the domain is fake, we don't need to research
    if account.domain_name and any(
        account.domain_name.endswith(suffix)
        for suffix in settings.research_agent_dnr_domain_suffixes
    ):
        logger.bind(account_id=account.id, account_domain=account.domain_name).warning(
            "Company domain is a DNR domain, will not research"
        )
        return

    # If the linkedin url is not valid, we don't need to research
    if account.linkedin_url and not is_valid_company_linkedin_url(account.linkedin_url):
        logger.bind(
            account_id=account.id, account_linkedin_url=account.linkedin_url
        ).warning("Company linkedin url is not valid, will not research")
        return

    temporal_client = await get_temporal_client()
    service = ResearchAgentTriggerService(client=temporal_client)
    logger.info(
        f"triggering company research for account {account.id} with domain {account.domain_name}"
    )
    await service.trigger_company_research(
        account_id=account.id,
        data=ResearchCompanyInput(
            account_id=account.id,
            company_name=account.display_name,
            company_domain=account.domain_name,
            linkedin_url=account.linkedin_url,
            research_time=ResearchTime(
                cdc_triggered_at=time.time_ns(),
            ),
        ),
        task_queue=assigned_task_queue,
    )


def _is_removing_person_linkedin_url(
    event: DomainEnrichedCDCEvent[Contact, ContactV2],
) -> bool:
    return (
        event.before is not None
        and event.before.linkedin_url is not None
        and event.before.linkedin_url != ""
        and "linkedin_url" in event.modified_fields
        and not event.after.linkedin_url
    )


async def person_research_handler(  # noqa: PLR0911
    event: DomainEnrichedCDCEvent[Contact, ContactV2],
) -> None:
    """
    Handles CDC trigger for a person by initiating research workflow.

    Args:
        event: The contact CDC event
    """
    if str(event.after.organization_id) in settings.research_agent_blocked_org_ids:
        logger.bind(contact=event.after).info(
            "Skipping research: Contact is in blocked organization"
        )
        return

    contact = event.after

    if contact.created_source == CreatedSource.CSV_IMPORT and event.before is None:
        logger.bind(contact=contact).info(
            "contact is created from csv import, will go to lowpri queue"
        )
        assigned_task_queue = ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW
    else:
        assigned_task_queue = ResearchTaskQueue.RESEARCH_TASK_QUEUE

    db_engine = await get_or_init_db_engine()
    intel_repo = IntelRepository(engine=db_engine)
    research_agent_service = ResearchAgentService(
        crustdata_client=CrustdataClient(),
        intel_repository=intel_repo,
        research_agent_client=get_research_agent_client(),
        user_feedback_repository=UserFeedbackRepository(engine=db_engine),
    )
    contact_query_service = get_contact_query_service(db_engine=db_engine)

    contact_email = await contact_query_service.get_primary_email_by_contact_id(
        organization_id=contact.organization_id,
        contact_id=contact.id,
    )

    # If the contact is merging or merged, we don't need to research
    if contact.integrity_job_started_at or contact.integrity_job_finished_at:
        return

    # If the contact isn't new and the linkedin_url is not modified, we don't need to research
    if event.before and "linkedin_url" not in event.modified_fields:
        return

    # If the contact doesn't have a linkedin_url or primary_email, we don't need to research
    if not (contact.linkedin_url or contact_email):
        return

    if _is_removing_person_linkedin_url(event):
        logger.bind(
            contact=contact,
            email=contact_email,
            removed_linkedin_url=event.before and event.before.linkedin_url,
        ).info("Skipping research: Removing linkedin url from contact")
        return

    linkedin_url = contact.linkedin_url
    input_type: IntelInputType | None = None
    linkedin_url_source: IntelLinkedinUrlSource | None = None

    if not linkedin_url and not contact_email:
        logger.bind(contact=contact).warning(
            "Skipping research: No LinkedIn URL or business email found for contact"
        )
        return

    temporal_client = await get_temporal_client()
    service = ResearchAgentTriggerService(client=temporal_client)

    if is_valid_person_linkedin_url(linkedin_url):
        input_type = IntelInputType.LINKEDIN_URL
        linkedin_url_source = IntelLinkedinUrlSource.USER_INPUT
    elif contact_email and settings.research_agent_enable_find_linkedin_url_by_email:
        logger.bind(contact=contact).info(
            f"triggering person research for contact {contact.id} with business email {contact_email}"
        )
        await service.trigger_person_research(
            contact_id=contact.id,
            data=ResearchPersonInput(
                intel_person_id=None,
                business_email=contact_email,
                organization_id=contact.organization_id,
                contact_id=contact.id,
                research_time=ResearchTime(
                    cdc_triggered_at=time.time_ns(),
                    intel_created_at=None,
                ),
            ),
            task_queue=assigned_task_queue,
        )
        return

    # Get or create the person association if it doesn't exist
    intel_person_association = await research_agent_service.get_or_create_intel_person(
        contact.id, not_none(linkedin_url)
    )

    await research_agent_service.update_intel_person_input_info(
        intel_person_id=intel_person_association.intel_person_id,
        input_type=input_type,
        linkedin_url_source=linkedin_url_source,
        status=ResearchStatus.PENDING,
    )

    logger.info(
        f"triggering person research for contact {contact.id} with linkedin url {linkedin_url}"
    )
    await service.trigger_person_research(
        contact_id=contact.id,
        data=ResearchPersonInput(
            intel_person_id=intel_person_association.intel_person_id,
            linkedin_url=linkedin_url,
            organization_id=contact.organization_id,
            contact_id=contact.id,
            research_time=ResearchTime(
                cdc_triggered_at=time.time_ns(),
                intel_created_at=intel_person_association.created_at.timestamp() * 1e9,
            ),
        ),
        task_queue=assigned_task_queue,
    )
