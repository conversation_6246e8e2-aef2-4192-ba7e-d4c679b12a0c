from datetime import UTC, datetime
from enum import StrEnum
from typing import Self

from pydantic import BaseModel, field_validator

from salestech_be.core.research_agent.models.company_activity import (
    ResearchLinkedinPost,
)
from salestech_be.core.research_agent.models.company_info import CompanyResearchInfo
from salestech_be.core.research_agent.types import (
    IntelProviderBaseModel,
    IntelProviderTypeEnum,
)
from salestech_be.integrations.brightdata.model import (
    BrightdataCompanyEntry,
    BrightdataLinkedinPostEntry,
)
from salestech_be.integrations.crustdata.model import (
    CrustdataCompanyInfo,
    CrustdataLinkedinPost,
)


class PendingEnrichmentDetails(StrEnum):
    NO_MATCHING_COMPANIES_POSTS_FOUND = "No matching companies found"
    NO_MATCHING_COMPANIES_INFO_FOUND = (
        "Tried enriching the following companies but not found"
    )


# snapshot is only available in brightdata
class PendingEnrichmentResponse(IntelProviderBaseModel):
    pending_enrichment: bool
    snapshot_id: str | None = None
    linkedin_url: str | None = None
    # if set to False, the research agent will break the research immediately
    is_recoverable_error: bool = True
    details: str | None = None


class IntelProviderCompanyInfoResponse(IntelProviderBaseModel):
    infos: list[CompanyResearchInfo]

    @classmethod
    def from_crustdata_company_info_list(
        cls, infos: list[CrustdataCompanyInfo]
    ) -> Self:
        return cls(
            infos=[
                CompanyResearchInfo.from_crustdata_company_info(info) for info in infos
            ],
            provider=IntelProviderTypeEnum.CRUSTDATA,
        )

    @classmethod
    def from_brightdata_company_entry_list(
        cls, infos: list[BrightdataCompanyEntry]
    ) -> Self:
        return cls(
            infos=[
                CompanyResearchInfo.from_brightdata_company_entry(info)
                for info in infos
            ],
            provider=IntelProviderTypeEnum.BRIGHTDATA,
        )


class IntelProviderPostsResponse(IntelProviderBaseModel):
    posts: list[ResearchLinkedinPost]

    @field_validator("posts", mode="before")
    @classmethod
    def sort_posts_by_date_posted(
        cls, posts: list[ResearchLinkedinPost]
    ) -> list[ResearchLinkedinPost]:
        return sorted(
            posts,
            key=lambda x: x.date_posted or datetime.min.replace(tzinfo=UTC),
            reverse=True,
        )

    @classmethod
    def from_crustdata_linkedin_post_list(
        cls, posts: list[CrustdataLinkedinPost]
    ) -> Self:
        return cls(
            posts=[
                ResearchLinkedinPost.from_crustdata_linkedin_post(post)
                for post in posts
            ],
            provider=IntelProviderTypeEnum.CRUSTDATA,
        )

    @classmethod
    def from_brightdata_linkedin_post_list(
        cls, posts: list[BrightdataLinkedinPostEntry]
    ) -> Self:
        return cls(
            posts=[
                ResearchLinkedinPost.from_brightdata_linkedin_post(post)
                for post in posts
            ],
            provider=IntelProviderTypeEnum.BRIGHTDATA,
        )


class UpdateResearchResult(BaseModel):
    is_first_time: bool = False
