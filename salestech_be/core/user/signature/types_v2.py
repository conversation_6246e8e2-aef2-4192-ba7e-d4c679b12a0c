from typing import Annotated
from uuid import UUID

from pydantic import BaseModel

from salestech_be.common.schema_manager.std_object_field_identifier import (
    EmailAccountField,
    OrganizationUserField,
    SignatureField,
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    SignatureRelationship,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    TextFieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.schema import OutboundRelationship
from salestech_be.common.type.patch_request import UNSET, BasePatchRequest, UnsetAware
from salestech_be.core.common.types import DomainModel, FieldMetadata
from salestech_be.db.models.signature import Signature
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class CreateSignatureRequest(BaseModel):
    name: str
    signature_html: str
    attachment_ids: list[UUID] | None = None
    mailbox_ids: list[UUID] | None = None


class PatchSignatureRequest(BasePatchRequest):
    name: UnsetAware[str] = UNSET
    signature_html: UnsetAware[str] = UNSET
    attachment_ids: UnsetAware[list[UUID]] = UNSET


class SignatureV2(DomainModel):
    object_id = StdObjectIdentifiers.signature.identifier
    field_name_provider = SignatureField
    object_display_name = "Signature"

    outbound_relationships = (
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=SignatureRelationship.signature__to__created_by_user,
            relationship_name="Created By User",
            self_object_identifier=StdObjectIdentifiers.signature.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                SignatureField.created_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=SignatureRelationship.signature__to__updated_by_user,
            relationship_name="Updated By User",
            self_object_identifier=StdObjectIdentifiers.signature.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                SignatureField.updated_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=SignatureRelationship.signature__to__deleted_by_user,
            relationship_name="Deleted By User",
            self_object_identifier=StdObjectIdentifiers.signature.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                SignatureField.deleted_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=SignatureRelationship.signature__to__email_account,
            relationship_name="Email Account",
            self_object_identifier=StdObjectIdentifiers.signature.identifier,
            related_object_identifier=StdObjectIdentifiers.email_account.identifier,
            self_cardinality=OutboundRelationship.Cardinality.ONE,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(SignatureField.id.identifier,),
            ordered_related_field_identifiers=(EmailAccountField.id.identifier,),
        ),
    )

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
            ),
        ),
    ]

    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    name: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Name",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ]

    signature_html: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Signature",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ]

    created_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    owner_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Owner User ID",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ]

    created_by_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    updated_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Updated At",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    updated_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Updated By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    deleted_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Deleted At",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    deleted_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Deleted By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    @classmethod
    def from_db_model(cls, signature: Signature) -> "SignatureV2":
        return cls(
            id=signature.id,
            organization_id=signature.organization_id,
            name=signature.name,
            signature_html=signature.signature_html,
            created_at=signature.created_at,
            created_by_user_id=signature.created_by_user_id,
            updated_at=signature.updated_at,
            updated_by_user_id=signature.updated_by_user_id,
            deleted_at=signature.deleted_at,
            deleted_by_user_id=signature.deleted_by_user_id,
            owner_user_id=signature.owner_user_id,
        )
