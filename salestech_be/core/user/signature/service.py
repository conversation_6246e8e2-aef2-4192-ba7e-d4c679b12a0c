from typing import cast, override
from uuid import UUID

from salestech_be.common.exception.exception import (
    ConflictResourceError,
    ResourceNotFoundError,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import BasePatchRequest
from salestech_be.core.common.domain_service import AllowedUsers, DomainService
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.email.account.schema import PatchEmailAccountRequestV2
from salestech_be.core.user.signature.types_v2 import (
    CreateSignatureRequest,
    PatchSignatureRequest,
    SignatureV2,
)
from salestech_be.db.dao.signature_repository import SignatureRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.signature import Signature, SignatureUpdate
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.common.container import (
    DeleteEntityResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class SignatureService(DomainService[SignatureV2]):
    def __init__(self, engine: DatabaseEngine) -> None:
        self.engine = engine
        self.signature_repository = SignatureRepository(engine=engine)
        from salestech_be.core.email.account.service_v2 import (
            get_email_account_service_v2_by_db_engine,
        )

        self.email_account_service = get_email_account_service_v2_by_db_engine(
            db_engine=engine
        )

    async def create_signature(
        self,
        user_auth_context: UserAuthContext,
        create_signature_request: CreateSignatureRequest,
    ) -> SignatureV2:
        # Check if a signature with the same name already exists
        existing_signature = await self.signature_repository.find_signature_by_name(
            organization_id=user_auth_context.organization_id,
            name=create_signature_request.name,
        )
        if existing_signature:
            raise ConflictResourceError("Signature with this name already exists")

        signature = await self.signature_repository.create_signature(
            signature_name=create_signature_request.name,
            signature_html=create_signature_request.signature_html,
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            attachment_ids=create_signature_request.attachment_ids,
        )
        if create_signature_request.mailbox_ids:
            for mailbox_id in create_signature_request.mailbox_ids:
                try:
                    mailbox_signature_patch_req = PatchEmailAccountRequestV2(
                        signature_id=signature.id
                    )
                    await self.email_account_service.update_email_account(
                        user_auth_context=user_auth_context,
                        email_account_id=mailbox_id,
                        patch_email_account_request=mailbox_signature_patch_req,
                    )

                except Exception as e:
                    logger.bind(mailbox_id=mailbox_id, signature_id=signature.id).error(
                        "Error adding signature to mailbox", exc_info=e
                    )

        return SignatureV2.from_db_model(signature)

    async def update_signature(
        self,
        user_auth_context: UserAuthContext,
        signature_id: UUID,
        patch_signature_request: PatchSignatureRequest,
    ) -> SignatureV2:
        signature_update = SignatureUpdate(
            name=patch_signature_request.name,
            signature_html=patch_signature_request.signature_html,
            attachment_ids=patch_signature_request.attachment_ids,
            updated_at=zoned_utc_now(),
            updated_by_user_id=user_auth_context.user_id,
        )
        signature = await self.signature_repository.update_by_tenanted_primary_key(
            table_model=Signature,
            organization_id=user_auth_context.organization_id,
            primary_key_to_value={"id": signature_id},
            column_to_update=signature_update.flatten_specified_values(),
        )
        if not signature:
            raise ResourceNotFoundError("Signature not found")
        return SignatureV2.from_db_model(signature)

    async def delete_signature(
        self,
        user_auth_context: UserAuthContext,
        signature_id: UUID,
    ) -> SignatureV2:
        signature_delete = SignatureUpdate(
            deleted_at=zoned_utc_now(),
            deleted_by_user_id=user_auth_context.user_id,
        )
        signature = await self.signature_repository.update_by_tenanted_primary_key(
            table_model=Signature,
            organization_id=user_auth_context.organization_id,
            primary_key_to_value={"id": signature_id},
            column_to_update=signature_delete.flatten_specified_values(),
        )
        if not signature:
            raise ResourceNotFoundError("Signature not found")
        return SignatureV2.from_db_model(signature)

    async def find_signature_by_ids(
        self,
        signature_ids: list[UUID],
        organization_id: UUID,
    ) -> list[Signature]:
        return await self.signature_repository.find_signatures_by_ids(
            organization_id=organization_id, signature_ids=signature_ids
        )

    async def find_signatures_by_organization_id(
        self,
        organization_id: UUID,
    ) -> list[Signature]:
        return await self.signature_repository.find_signatures_by_organization_id(
            organization_id=organization_id,
        )

    async def get_signature_by_id(
        self, organization_id: UUID, signature_id: UUID | None
    ) -> Signature | None:
        if not signature_id:
            return None
        return await self.signature_repository.find_by_tenanted_primary_key(
            table_model=Signature,
            id=signature_id,
            organization_id=organization_id,
        )

    async def create_entity(
        self, organization_id: UUID, user_id: UUID, request: CreateSignatureRequest
    ) -> SignatureV2:
        return await self.create_signature(
            user_auth_context=UserAuthContext(
                organization_id=organization_id,
                user_id=user_id,
            ),
            create_signature_request=request,
        )

    async def get_entity(
        self, entity_id: UUID, organization_id: UUID, user_id: UUID
    ) -> SignatureV2 | None:
        signature = await self.get_signature_by_id(organization_id, entity_id)
        if not signature:
            return None
        return SignatureV2.from_db_model(signature)

    async def update_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        entity: SignatureV2,
        request: BasePatchRequest,
    ) -> SignatureV2:
        patch_signature_request = cast(PatchSignatureRequest, request)
        return await self.update_signature(
            user_auth_context=UserAuthContext(
                organization_id=organization_id,
                user_id=user_id,
            ),
            signature_id=entity.id,
            patch_signature_request=patch_signature_request,
        )

    async def remove_entity(
        self, organization_id: UUID, user_id: UUID, entity_id: UUID
    ) -> DeleteEntityResponse:
        await self.delete_signature(
            user_auth_context=UserAuthContext(
                organization_id=organization_id,
                user_id=user_id,
            ),
            signature_id=entity_id,
        )
        return DeleteEntityResponse(id=entity_id)

    # No participant users for signature
    @override
    async def get_allowed_users_from_entity(
        self, domain_entity: SignatureV2
    ) -> AllowedUsers:
        """
        Returns AllowedUsers for a signature object.
        """

        model_as_dict = domain_entity.model_dump(
            include={
                "owner_user_id",
            }
        )
        owner_user_id = UUID(str(model_as_dict.get("owner_user_id")))

        return AllowedUsers(owner_user_id=owner_user_id, participant_user_ids=[])

    # temporary flag for rollout
    @override
    async def can_access_entity_for_patch(
        self, user_auth_context: UserAuthContext, entity: SignatureV2
    ) -> bool:
        if not settings.enable_mailbox_perms:
            return True
        return await super().can_access_entity_for_patch(
            user_auth_context=user_auth_context, entity=entity
        )

    @override
    async def can_access_entity_for_delete(
        self, user_auth_context: UserAuthContext, entity: SignatureV2
    ) -> bool:
        if not settings.enable_mailbox_perms:
            return True
        return await super().can_access_entity_for_delete(
            user_auth_context=user_auth_context, entity=entity
        )

    # only admin and owner can read email accounts
    @override
    async def can_access_entity_for_read(
        self, user_auth_context: UserAuthContext, entity: SignatureV2
    ) -> bool:
        if not settings.enable_mailbox_perms:
            return True
        return await super().can_access_entity_for_read_participants_or_owner(
            user_auth_context=user_auth_context, entity=entity
        )

    @override
    async def can_create_entity(self, user_auth_context: UserAuthContext) -> bool:
        if not settings.enable_mailbox_perms:
            return True
        return await super().can_create_entity(user_auth_context=user_auth_context)


class SingletonSignatureService(Singleton, SignatureService):
    pass


def get_signature_service(
    db_engine: DatabaseEngine,
) -> SignatureService:
    return SingletonSignatureService(
        engine=db_engine,
    )
