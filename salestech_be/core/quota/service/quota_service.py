import sys
import uuid
from datetime import date, datetime, timedelta
from uuid import UUID

import pytz
from fastapi import Request
from pydantic_extra_types.timezone_name import TimeZoneName
from sqlalchemy.exc import IntegrityError

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.quota.service.policy_evaluator_factory import (
    PolicyEvaluatorFactory,
)
from salestech_be.core.quota.type.quota_policy_type import (
    QuotaPolicy as QuotaPolicyResponse,
)
from salestech_be.core.quota.type.quota_policy_type import (
    QuotaSummaryItem,
)
from salestech_be.core.quota.type.quota_type import DeliverableWindow
from salestech_be.core.user.service.user_service import (
    get_user_service_general,
)
from salestech_be.db.dao.quota_repository import (
    QuotaPolicyRepository,
    QuotaUsageRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
    QuotaPeriod,
    QuotaPolicy,
    QuotaUsage,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import (
    convert_utc_to_local,
    zoned_utc_now,
)
from salestech_be.util.validation import not_none


class QuotaService:
    def __init__(self, db_engine: DatabaseEngine):
        self.user_service = get_user_service_general(db_engine)
        self._quota_policy_repo = QuotaPolicyRepository(engine=db_engine)
        self._quota_usage_repo = QuotaUsageRepository(engine=db_engine)
        self._policy_evaluator_factory = PolicyEvaluatorFactory(
            policy_repo=self._quota_policy_repo,
            usage_repo=self._quota_usage_repo,
        )

    async def get_usage(
        self,
        organization_id: UUID,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        timestamp: datetime,
    ) -> QuotaUsage:
        hour_start = self._datetime_to_hour_start(time=timestamp)

        usage = await self._quota_usage_repo._find_unique_by_column_values(
            table_model=QuotaUsage,
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
            hour_start=hour_start,
        )
        if usage:
            return usage
        return QuotaUsage(
            id=uuid.uuid4(),
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
            usage=0,
            hour_start=hour_start,
            created_at=zoned_utc_now(),
        )

    async def batch_get_usage(
        self,
        organization_id: UUID,
        entity_ids: list[UUID],
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        timestamp: datetime,
    ) -> dict[UUID, QuotaUsage]:
        """
        Retrieve quota usage for multiple entities with the same entity type and resource starting from timestamp (aligned to hour).

        Args:
            organization_id: The organization ID
            entity_ids: List of entity IDs to get usage for
            entity_type: The entity type (e.g., USER, ORGANIZATION)
            resource: The resource being consumed
            timestamp: The timestamp to get usage for

        Returns:
            Dictionary mapping entity IDs to their QuotaUsage objects
        """
        if not entity_ids:
            return {}

        hour_start = self._datetime_to_hour_start(time=timestamp)

        # Find existing usage records for all entities
        existing_usages = await self._quota_usage_repo._find_by_column_values(
            table_model=QuotaUsage,
            organization_id=organization_id,
            entity_type=entity_type,
            resource=resource,
            hour_start=hour_start,
            entity_id=entity_ids,  # Pass the list of entity IDs
        )

        # Create a mapping of entity ID to usage
        result = {usage.entity_id: usage for usage in existing_usages}

        # For any missing entities, create new QuotaUsage objects with 0 usage
        for entity_id in entity_ids:
            if entity_id not in result:
                result[entity_id] = QuotaUsage(
                    id=uuid.uuid4(),
                    organization_id=organization_id,
                    entity_id=entity_id,
                    entity_type=entity_type,
                    resource=resource,
                    usage=0,
                    hour_start=hour_start,
                    created_at=zoned_utc_now(),
                )

        return result

    async def increase_usage(
        self,
        organization_id: UUID,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        usage: int,
        timestamp: datetime,
    ) -> QuotaUsage:
        if usage <= 0:
            raise InvalidArgumentError("Usage must be greater than 0")

        hour_start = self._datetime_to_hour_start(time=timestamp)

        existing_usage = await self._add_to_existing_usage_if_exist(
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
            usage=usage,
            hour_start=hour_start,
        )
        if existing_usage:
            return existing_usage
        try:
            return await self.insert_new_usage(
                organization_id=organization_id,
                entity_id=entity_id,
                entity_type=entity_type,
                resource=resource,
                usage=usage,
                hour_start=hour_start,
            )
        except IntegrityError:
            return not_none(
                await self._add_to_existing_usage_if_exist(
                    organization_id=organization_id,
                    entity_id=entity_id,
                    entity_type=entity_type,
                    resource=resource,
                    usage=usage,
                    hour_start=hour_start,
                )
            )

    async def decrease_usage(
        self,
        organization_id: UUID,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        usage: int,
        timestamp: datetime,
    ) -> QuotaUsage | None:
        if usage <= 0:
            raise InvalidArgumentError("Usage must be greater than 0")

        hour_start = self._datetime_to_hour_start(time=timestamp)

        return await self._add_to_existing_usage_if_exist(
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
            usage=-usage,
            hour_start=hour_start,
        )

    async def shift_usage(
        self,
        organization_id: UUID,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        usage: int,
        original_usage_timestamp: datetime,
        updated_usage_timestamp: datetime,
    ) -> QuotaUsage | None:
        if usage <= 0:
            raise InvalidArgumentError("Usage must be greater than 0")

        await self.decrease_usage(
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
            usage=usage,
            timestamp=original_usage_timestamp,
        )

        return await self.increase_usage(
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
            usage=usage,
            timestamp=updated_usage_timestamp,
        )

    async def _add_to_existing_usage_if_exist(
        self,
        organization_id: UUID,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        usage: int,
        hour_start: datetime,
    ) -> QuotaUsage | None:
        if usage == 0:
            return None

        existing_usage = await self._quota_usage_repo._find_unique_by_column_values(
            table_model=QuotaUsage,
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
            hour_start=hour_start,
        )

        if existing_usage:
            db_usage = await self._quota_usage_repo.update_by_primary_key(
                QuotaUsage,
                primary_key_to_value={
                    "id": existing_usage.id,
                },
                column_to_update={
                    "usage": max(existing_usage.usage + usage, 0),
                    "updated_at": zoned_utc_now(),
                },
            )
            return not_none(db_usage)
        return None

    async def insert_new_usage(
        self,
        organization_id: UUID,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        usage: int,
        hour_start: datetime,
        applied_sub_resource: QuotaConsumingResource | None = None,
        created_by_user_id: UUID | None = None,
    ) -> QuotaUsage:
        new_usage = QuotaUsage(
            id=uuid.uuid4(),
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
            usage=usage,
            hour_start=hour_start,
            created_at=zoned_utc_now(),
            applied_sub_resource=applied_sub_resource,
            created_by_user_id=created_by_user_id,
        )
        db_usage = await self._quota_usage_repo.insert(new_usage)
        return not_none(db_usage)

    @staticmethod
    def _datetime_to_hour_start(time: datetime) -> datetime:
        return time.replace(minute=0, second=0, microsecond=0)

    async def get_next_available_quota_time_window(
        self,
        organization_id: UUID,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        requested_usage: int,
        request_time: ZoneRequiredDateTime,
        days_to_look_ahead: int = 7,  # Add new parameter with default value
    ) -> list[DeliverableWindow]:
        if requested_usage <= 0:
            raise InvalidArgumentError("Requested usage must be greater than 0")

        db_policies = await self._quota_policy_repo._find_by_column_values(
            table_model=QuotaPolicy,
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
        )

        if not db_policies:
            return [
                DeliverableWindow(
                    start_time=request_time,
                    end_time=None,
                    deliverable_count=requested_usage,
                )
            ]

        policy_evaluators = [
            self._policy_evaluator_factory.create(policy) for policy in db_policies
        ]

        available_periods = []
        remaining_usage = requested_usage

        # Calculate end date (look ahead period from request time)
        end_date = request_time + timedelta(days=days_to_look_ahead)

        current_time = request_time
        # Continue until we reach the end date instead of just until requested usage is satisfied
        while current_time < end_date:
            min_available_quota = sys.maxsize
            period_end_times = []
            violated_policies_next_start_time = []

            for policy_eva in policy_evaluators:
                period_end = policy_eva.get_period_end(current_time)
                next_period_start = policy_eva.get_next_period_start(current_time)
                remaining_quota = await policy_eva.get_remaining_quota(
                    organization_id=organization_id,
                    entity_id=entity_id,
                    request_time=current_time,
                )

                min_available_quota = min(min_available_quota, remaining_quota)

                # Save the period end times for the current iteration
                # Will need the minimum period end time to determine the
                # start time of the next iteration
                period_end_times.append((period_end, next_period_start))

                # If remaining usage is less than or equal to 0,
                # the policy is violated or fully allocated
                if remaining_quota <= 0:
                    violated_policies_next_start_time.append(next_period_start)

            # Determine the start time of the next iteration
            min_period_end, min_next_period_start = min(
                period_end_times, key=lambda x: x[0]
            )

            # if there is quota available, it means all policies are satisfied
            # allocate the quota
            if min_available_quota > 0:
                # Allocate either the remaining requested usage or all available quota
                # if we've already satisfied the original request
                usage_to_allocate = min(
                    min_available_quota,
                    remaining_usage if remaining_usage > 0 else min_available_quota,
                )

                available_periods.append(
                    DeliverableWindow(
                        start_time=current_time,
                        end_time=min_period_end,
                        deliverable_count=usage_to_allocate,
                    )
                )

                # Only reduce remaining usage and soft allocate if still satisfying original request
                if remaining_usage > 0:
                    remaining_usage -= usage_to_allocate

                    for policy_eva in policy_evaluators:
                        policy_eva.soft_allocate_quota(
                            organization_id=organization_id,
                            entity_id=entity_id,
                            usage=usage_to_allocate,
                            request_time=current_time,
                        )

                # Move on to the next start time of the minimal policy
                current_time = min_next_period_start

            # Otherwise, move on to the next start time of the max
            # violated policy
            else:
                current_time = max(violated_policies_next_start_time)

            # Check if we've gone beyond our look-ahead window
            if current_time > end_date:
                break

        return available_periods

    async def get_aggregate_user_usage_in_period(
        self,
        organization_id: UUID,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        period_start: datetime,
        period_end: datetime,
    ) -> int:
        return await self._quota_usage_repo.get_aggregate_user_usage_in_period(
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
            period_start=period_start,
            period_end=period_end,
        )

    async def get_aggregated_daily_user_usage_in_period(
        self,
        organization_id: UUID,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        period_start: ZoneRequiredDateTime,
        period_end: ZoneRequiredDateTime,
    ) -> dict[date, int]:
        return await self._quota_usage_repo.get_aggregated_daily_usage_in_period(
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
            period_start=period_start,
            period_end=period_end,
        )

    async def get_aggregate_user_usage_in_current_quota_period(
        self,
        user_auth_context: UserAuthContext,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        quota_period: QuotaPeriod,
    ) -> int:
        """
        Retrieves the aggregate usage for a specific entity within the current quota period.

        Calculates the appropriate time boundaries for the specified quota period (hourly, daily,
        monthly, or annual) in the user's local timezone and converts them to UTC for database
        operations. The period starts at the beginning of the current period and ends at the current time.
        """

        user_timezone = await self.user_service.get_user_timezone_or_default(
            user_auth_context
        )
        period_start_utc, now_utc = await self.get_utc_period_boundaries(
            user_timezone=user_timezone,
            period=quota_period,
        )
        return await self.get_aggregate_user_usage_in_period(
            organization_id=user_auth_context.organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
            period_start=period_start_utc,
            period_end=now_utc,
        )

    async def get_utc_period_boundaries(
        self, user_timezone: TimeZoneName, period: QuotaPeriod
    ) -> tuple[datetime, datetime]:
        """
        Calculate the start and current time for a given quota period in user's timezone.

        Args:
            user_timezone: The user's timezone as a TimeZoneName
            period: The QuotaPeriod (HOURLY, DAILY, MONTHLY, ANNUAL)

        Returns:
            Tuple of (period_start_utc, now_utc)
        """

        # Get current time in user's timezone
        now_in_user_tz = convert_utc_to_local(
            utc_datetime=zoned_utc_now(), local_timezone=user_timezone
        )

        # Calculate period start based on quota period
        if period == QuotaPeriod.HOURLY:
            # Start of current hour
            period_start = now_in_user_tz.replace(minute=0, second=0, microsecond=0)

        elif period == QuotaPeriod.DAILY:
            # Start of current day
            period_start = now_in_user_tz.replace(
                hour=0, minute=0, second=0, microsecond=0
            )

        elif period == QuotaPeriod.MONTHLY:
            # Start of current month
            period_start = now_in_user_tz.replace(
                day=1, hour=0, minute=0, second=0, microsecond=0
            )

        elif period == QuotaPeriod.ANNUAL:
            # Start of current year
            period_start = now_in_user_tz.replace(
                month=1, day=1, hour=0, minute=0, second=0, microsecond=0
            )

        utc = pytz.timezone("UTC")
        # Convert back to UTC for database operations
        period_start_utc = period_start.astimezone(utc)
        now_utc = now_in_user_tz.astimezone(utc)

        return period_start_utc, now_utc

    async def get_org_quota_summary(
        self,
        organization_id: UUID,
    ) -> list[QuotaSummaryItem]:
        # Get distinct resource/period/entity_type combinations from the database
        resources_to_check = (
            await self._quota_policy_repo.get_distinct_quota_configurations(
                organization_id=organization_id
            )
        )

        # Group resources by entity type for more efficient processing
        org_resources = [
            r
            for r in resources_to_check
            if r.entity_type == QuotaConsumerEntityType.ORGANIZATION
        ]
        user_resources = [
            r
            for r in resources_to_check
            if r.entity_type == QuotaConsumerEntityType.USER
        ]

        quota_summary = []

        # Process organization-level quotas
        if org_resources:
            # Extract resources and periods for bulk operations
            org_resources_list = [item.resource for item in org_resources]
            org_periods_list = [item.period for item in org_resources]

            # Bulk fetch policies for all organization resources
            bulk_policies = (
                await self._quota_policy_repo.bulk_get_entity_relevant_policies(
                    organization_id=organization_id,
                    entity_id=organization_id,
                    entity_type=QuotaConsumerEntityType.ORGANIZATION,
                    resources=org_resources_list,
                    periods=org_periods_list,
                )
            )

            # Bulk fetch usage for all resources
            start_times = {
                policy.resource: policy.created_at for policy in bulk_policies
            }
            bulk_usage = (
                await self._quota_usage_repo.bulk_get_entity_used_credits_by_resources(
                    organization_id=organization_id,
                    resources=org_resources_list,
                    entity_id=organization_id,
                    entity_type=QuotaConsumerEntityType.ORGANIZATION,
                    start_times=start_times,
                )
            )

            # Process the bulk results
            for policy in bulk_policies:
                policy_response = QuotaPolicyResponse.from_db(policy)
                total_used = bulk_usage.get(policy.resource, 0)

                quota_summary.append(
                    QuotaSummaryItem(
                        resource=policy.resource,
                        total_limit=policy_response.quota_limit,
                        total_remaining=policy_response.quota_limit - total_used,
                        total_used=total_used,
                    )
                )

        # Process user-level quotas if any exist
        if user_resources:
            # Fetch all active users once
            users = await self.user_service.list_all_users_in_organization(
                organization_id=organization_id,
                active_users_only=True,
            )
            user_ids = [user.id for user in users]

            if user_ids:
                # Create a mapping of resource to period
                resource_to_period = {
                    item.resource: item.period for item in user_resources
                }

                # Precompute all needed period start times
                periods = list({item.period for item in user_resources})
                period_to_start_time = {}
                for period in periods:
                    start_time, _ = await self.get_utc_period_boundaries(
                        user_timezone=TimeZoneName("UTC"),
                        period=period,
                    )
                    period_to_start_time[period] = start_time

                # Process each user resource with batched operations
                for resource_item in user_resources:
                    period = resource_to_period[resource_item.resource]
                    start_time = period_to_start_time[period]

                    # Batch fetch quota limits and usage data for all users for this resource
                    limits = await self._quota_policy_repo.bulk_get_quota_policies_aggregated_by_resources_and_entity_id(
                        organization_id=organization_id,
                        resource=resource_item.resource,
                        entity_ids=user_ids,
                        entity_type=QuotaConsumerEntityType.USER,
                        period=period,
                    )

                    usage = await self._quota_usage_repo.bulk_get_entity_used_credits_by_resource_and_entity_id(
                        organization_id=organization_id,
                        resource=resource_item.resource,
                        entity_ids=user_ids,
                        entity_type=QuotaConsumerEntityType.USER,
                        start_time=start_time,
                    )

                    # Calculate totals
                    total_limit = sum(limits.values())
                    total_used = sum(usage.values())

                    quota_summary.append(
                        QuotaSummaryItem(
                            resource=resource_item.resource,
                            total_limit=total_limit,
                            total_remaining=total_limit - total_used,
                            total_used=total_used,
                        )
                    )

        return quota_summary

    async def get_quota_summary_per_resource(
        self,
        organization_id: UUID,
        resource: QuotaConsumingResource,
        period: QuotaPeriod,
        user_id: UUID | None = None,
    ) -> QuotaSummaryItem:
        start_time, _ = await self.get_utc_period_boundaries(
            user_timezone=TimeZoneName("UTC"),
            period=period,
        )
        if user_id:
            entity_id = user_id
            entity_type = QuotaConsumerEntityType.USER
        else:
            entity_id = organization_id
            entity_type = QuotaConsumerEntityType.ORGANIZATION
        total_quota_limit = await self._quota_policy_repo.get_quota_policies_aggregated_by_resources_and_entity_id(
            organization_id=organization_id,
            resource=resource,
            entity_id=entity_id,
            entity_type=entity_type,
            period=period,
        )
        total_used = await self._quota_usage_repo.get_entity_used_credits_by_resource_and_entity_id(
            organization_id=organization_id,
            resource=resource,
            entity_id=entity_id,
            entity_type=entity_type,
            start_time=start_time,
        )
        total_remaining = total_quota_limit - total_used
        return QuotaSummaryItem(
            resource=resource,
            total_limit=total_quota_limit,
            total_remaining=total_remaining,
            total_used=total_used,
        )


def get_quota_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> QuotaService:
    return QuotaService(
        db_engine=db_engine,
    )


def get_quota_service(request: Request) -> QuotaService:
    db_engine = get_db_engine(request)
    return QuotaService(db_engine=db_engine)
