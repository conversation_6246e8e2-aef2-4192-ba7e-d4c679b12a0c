from uuid import UUID

from dateutil.relativedelta import relativedelta
from pydantic import BaseModel

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import ErrorDetails, InvalidArgumentError
from salestech_be.common.type.patch_request import BasePatchRequest, UnsetAware
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
    QuotaPeriod,
)
from salestech_be.db.models.quota import QuotaPolicy as DbQuotaPolicy
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now


class QuotaPolicy(BaseModel):
    id: UUID
    quota_limit: int
    period: QuotaPeriod
    resource: QuotaConsumingResource
    entity_type: QuotaConsumerEntityType
    entity_id: UUID | None
    applied_sub_entity_types: list[QuotaConsumerEntityType] | None = None
    organization_id: UUID | None
    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID | None = None
    updated_at: ZoneRequiredDateTime | None
    updated_by_user_id: UUID | None = None

    @staticmethod
    def from_db(db_quota_policy: DbQuotaPolicy) -> "QuotaPolicy":
        return QuotaPolicy(
            id=db_quota_policy.id,
            quota_limit=db_quota_policy.quota_limit,
            period=db_quota_policy.period,
            resource=db_quota_policy.resource,
            entity_type=db_quota_policy.entity_type,
            entity_id=db_quota_policy.entity_id,
            applied_sub_entity_types=db_quota_policy.applied_sub_entity_types,
            organization_id=db_quota_policy.organization_id,
            created_at=db_quota_policy.created_at,
            created_by_user_id=db_quota_policy.created_by_user_id,
            updated_at=db_quota_policy.updated_at,
            updated_by_user_id=db_quota_policy.updated_by_user_id,
        )

    def __hash__(self) -> int:
        return hash(self.id)

    @property
    def is_expired(self) -> bool:
        if self.period == QuotaPeriod.HOURLY:
            return zoned_utc_now() >= self.created_at + relativedelta(hours=1)

        if self.period == QuotaPeriod.DAILY:
            return zoned_utc_now() >= self.created_at + relativedelta(days=1)

        if self.period == QuotaPeriod.MONTHLY:
            return zoned_utc_now() >= self.created_at + relativedelta(months=1)

        if self.period == QuotaPeriod.ANNUAL:
            return zoned_utc_now() >= self.created_at + relativedelta(years=1)

        raise InvalidArgumentError(
            additional_error_details=ErrorDetails(
                code=ErrorCode.INVALID_REQUEST, details="Unknown quota period."
            )
        )


class CreateQuotaPolicyRequest(BaseModel):
    quota_limit: int
    period: QuotaPeriod
    resource: QuotaConsumingResource = QuotaConsumingResource.EMAIL
    entity_id: UUID


class CreateQuotaPoliciesRequest(BaseModel):
    quota_policies: list[CreateQuotaPolicyRequest]

    def validate_request(self) -> None:
        seen = set()
        for policy in self.quota_policies:
            key = (policy.resource, policy.period)
            if key in seen:
                raise InvalidArgumentError(
                    f"Duplicate quota policy for resource {policy.resource} and period {policy.period}"
                )
            seen.add(key)


class UpdateQuotaPolicyRequest(BasePatchRequest):
    require_at_least_one_specified_field = True

    quota_limit: UnsetAware[int]


class QuotaUsage(BaseModel):
    resource: QuotaConsumingResource
    entity_id: UUID
    entity_type: QuotaConsumerEntityType
    usage: int
    hour_start: ZoneRequiredDateTime
    organization_id: UUID
    created_at: ZoneRequiredDateTime
    updated_at: ZoneRequiredDateTime
    deleted_at: ZoneRequiredDateTime


class QuotaSummaryItem(BaseModel):
    resource: QuotaConsumingResource
    total_limit: int
    total_remaining: int
    total_used: int


class QuotaUsageWithActual(BaseModel):
    delta: int
    quota_summary: QuotaSummaryItem


class OutboundEmailQuota(BaseModel):
    org_level_num_domain_quota_object: QuotaUsageWithActual
    org_level_num_mailbox_quota_object: QuotaUsageWithActual
    org_level_plan_included_domain_quota_object: QuotaUsageWithActual
    org_level_plan_included_mailbox_quota_object: QuotaUsageWithActual


class QuotaCheckRequest(BaseModel):
    resource: QuotaConsumingResource
    period: QuotaPeriod


class QuotaCheckResponse(BaseModel):
    organization_id: UUID
    user_id: UUID | None
    resource: QuotaConsumingResource
    period: QuotaPeriod
    reached: bool
    total_usage: int
    total_limit: int
