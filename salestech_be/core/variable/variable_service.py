import re
from typing import Any
from uuid import UUID

from jinja2 import (
    BaseLoader,
    Environment,
    StrictUndefined,
    TemplateSyntaxError,
    Undefined,
    UndefinedError,
)

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import ErrorDetails, InvalidArgumentError
from salestech_be.common.singleton import Singleton
from salestech_be.core.organization.service.organization_service_v2 import (
    OrganizationServiceV2,
    get_organization_service_v2_from_engine,
)
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.core.variable.types import (
    CurrentDateTimeBinding,
    EmailAccountBinding,
    EmailContactBinding,
    EmailSenderEmailAccountBinding,
    EmailSenderOrganizationBinding,
    EmailSenderUserBinding,
    EmailTemplateVariableBindings,
    SchedulerContactBinding,
    SchedulerEventScheduleBinding,
    Variable,
    VariableDomainBoundBaseModel,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.core.base import TableModel
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


class StrictWhitespaceUndefined(Undefined):
    def __str__(self) -> str:
        # Extract the original variable representation
        if self._undefined_name:
            var_name = self._undefined_name.strip()

            # Check if there are spaces anywhere in the variable reference
            if re.search(r"\s", var_name):
                return f"{{{{ {self._undefined_name} }}}}"  # Keep the Jinja2 syntax

        return super().__str__()  # Otherwise, default behavior


class VariableService:
    def __init__(
        self,
        user_service: UserService,
        organization_service: OrganizationServiceV2,
    ) -> None:
        self.user_service = user_service
        self.organization_service = organization_service
        self.jinja2_env = Environment(
            loader=BaseLoader(),
            autoescape=False,  # noqa:  S701
            undefined=StrictWhitespaceUndefined,
        )

    @staticmethod
    def get_variable_placeholders(template: str) -> list[str]:
        valid_variable_regex = re.compile(r"{{\s*[\w-]+\.*[\w-]+\s*}}")
        return valid_variable_regex.findall(template)

    @staticmethod
    def get_variable_placeholders_without_space(template: str) -> list[str]:
        valid_variable_regex = re.compile(r"{{[\w-]+\.*[\w-]+}}")
        return valid_variable_regex.findall(template)

    @staticmethod
    def get_variable_from_placeholder(placeholder: str) -> str:
        return placeholder.replace("{{", "").replace("}}", "").strip()

    @staticmethod
    def _is_variable_and_attribute_in_context(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        inner_variable_str: str, inner_context: dict[str, Any]
    ) -> bool:
        tokens = inner_variable_str.split(".")
        token_len = len(tokens)

        # Not supporting nested attributes yet
        if token_len > 2 or token_len == 0:  # noqa: PLR2004
            return False
        elif token_len == 1:
            return tokens[0] in inner_context
        else:
            variable = tokens[0]
            attribute = tokens[1]
            if variable not in inner_context:
                return False

            value = inner_context[variable]
            return (isinstance(value, dict) and attribute in value) or hasattr(
                value, attribute
            )

    @staticmethod
    def _is_variable_attribute_exposed(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        inner_variable_str: str,
        inner_context: dict[str, Any],
        inner_exposed_fields: list[str],
    ) -> bool:
        tokens = inner_variable_str.split(".")
        if len(tokens) != 2:  # noqa: PLR2004
            return True
        else:
            variable = tokens[0]
            attribute = tokens[1]
            return variable in inner_context and attribute in inner_exposed_fields

    def register_variables(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        template: str,
        context: dict[str, Any],
        exposed_fields: list[str],
    ) -> str:
        # get valid variables placeholders
        placeholders = self.get_variable_placeholders_without_space(template)
        placeholder_values = {}
        for ph in placeholders:
            try:
                variable = self.get_variable_from_placeholder(ph)
                if not self._is_variable_and_attribute_in_context(
                    variable, context
                ) or not self._is_variable_attribute_exposed(
                    variable, context, exposed_fields
                ):
                    placeholder_values[ph] = ph
                    continue

                temp_template = self.jinja2_env.from_string(ph)
                rendered_ph = temp_template.render(context)
                # ignore None or empty string
                if rendered_ph:
                    placeholder_values[ph] = rendered_ph
            except TemplateSyntaxError:
                placeholder_values[ph] = ph
            except UndefinedError:
                placeholder_values[ph] = ph
            except Exception as e:
                raise InvalidArgumentError(
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.INVALID_REQUEST,
                        details=f"Error rendering placeholder: {ph}",
                    )
                ) from e

        for ph, value in placeholder_values.items():
            template = template.replace(ph, value)

        return template

    def render_template_str(  # type: ignore[explicit-any]
        self, template: str, context: dict[str, dict[str, Any]]
    ) -> str:
        """
        Raises:
            jinja2.exceptions.UndefinedError: If a variable is not found in the context
        """
        jinja_template = self.jinja2_env.from_string(template)
        jinja_template.environment.undefined = StrictUndefined
        return jinja_template.render(context)

    def _convert_to_nested_dict(self, data: dict[str, Any]) -> dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        result: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        for key, value in data.items():
            if value is None:
                continue
            if isinstance(value, VariableDomainBoundBaseModel | TableModel):
                model_dict = value.model_dump()
                nested_model_dict = self._convert_to_nested_dict(model_dict)
                self._update_nested_dict(result, key, nested_model_dict)
            elif isinstance(value, dict):
                nested_dict = self._convert_to_nested_dict(value)
                self._update_nested_dict(result, key, nested_dict)
            else:
                self._update_nested_dict(result, key, value)

        return result

    @staticmethod
    def _update_nested_dict(d: dict[str, Any], key: str, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        parts = key.split(".")
        for part in parts[:-1]:
            if part not in d:
                d[part] = {}
            d = d[part]
        d[parts[-1]] = value

    def parse_context(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_models: dict[str, Any],
        custom_contexts: dict[str, Any] | None = None,
    ) -> tuple[dict[str, Any], list[str]]:
        if custom_contexts is None:
            custom_contexts = {}

        context: dict[str, Any] = table_models.copy()  # type: ignore[explicit-any] # TODO: fix-any-annotation
        custom_context_without_none = {
            key: value for key, value in custom_contexts.items() if value is not None
        }

        context.update(custom_context_without_none)

        nested_context = self._convert_to_nested_dict(context)
        # Get exposed fields
        exposed_fields: list[str] = [
            field_info.alias or field
            for member in EmailTemplateVariableBindings
            for field, field_info in member.value.model_fields.items()
        ]

        for variable in custom_context_without_none:
            tokens = variable.split(".")
            if len(tokens) == 1:
                exposed_fields.append(variable)
            elif len(tokens) == 2:  # noqa: PLR2004
                attribute = tokens[-1]
                exposed_fields.append(attribute)
            else:
                raise ValueError(f"Invalid variable format: {variable}")

        return nested_context, exposed_fields

    async def list_email_template_variables(
        self, user_id: UUID, organization_id: UUID
    ) -> list[Variable]:
        # Special handling for SENDER and  SENDER_ORGANIZATION to populate real data
        user_v2 = await self.user_service.get_user_v2(
            user_id=user_id, organization_id=organization_id
        )
        org_v2 = await self.organization_service.get_organization_by_id(
            organization_id=organization_id
        )

        variable_bindings = [
            EmailContactBinding.to_variable(),
            EmailAccountBinding.to_variable(),
            EmailSenderEmailAccountBinding(
                display_name=user_v2.display_name,
                first_name=user_v2.first_name,
                email=user_v2.email,
            ).to_bound_variable(),
            EmailSenderOrganizationBinding.from_domain_object(
                org_v2
            ).to_bound_variable(),
            EmailSenderUserBinding.from_domain_object(user_v2).to_bound_variable(),
            CurrentDateTimeBinding.to_variable(),
        ]

        return self._get_merged_variable_list(variable_bindings)

    async def list_scheduler_variables(self) -> list[Variable]:
        variable_bindings = [
            SchedulerContactBinding.to_variable(),
            # Adding EventScheduleService creates a circular dependency,
            # so return the example variable for now
            SchedulerEventScheduleBinding.to_variable(),
        ]
        return self._get_merged_variable_list(variable_bindings)

    @staticmethod
    def _get_merged_variable_list(variables: list[Variable]) -> list[Variable]:
        """Merges variable fields by the object_name. Mappings are use case, variable
        namespace (object_name), and domain object model specific, thus there may be multiple
        with the same object_name but with values bound to different domain objects."""
        vars_by_name: dict[str, Variable] = {}
        for variable in variables:
            if variable.object_name in vars_by_name:
                vars_by_name[variable.object_name].fields.extend(variable.fields)
            else:
                vars_by_name[variable.object_name] = variable.model_copy()

        return list(vars_by_name.values())

    def render_event_title(
        self,
        event_title_template: str | None,
        meeting_duration: int,
        scheduler_full_name: str | None = None,
    ) -> str | None:
        """Located here to avoid circular dependencies with EventScheduleService"""
        if not event_title_template:
            return None

        # TODO: Make it easier to keep bindings consistent with supported bindings
        bindings: list[VariableDomainBoundBaseModel] = [  # type: ignore[type-arg]
            SchedulerEventScheduleBinding(duration_minutes=meeting_duration),
        ]
        if scheduler_full_name:
            bindings.append(SchedulerContactBinding(full_name=scheduler_full_name))  # type: ignore[call-arg]
        return self._render_event_title(event_title_template, bindings)

    def _render_event_title(
        self,
        event_title_template: str,
        bindings: list[VariableDomainBoundBaseModel],  # type: ignore[type-arg]
    ) -> str | None:
        context = {}
        for binding in bindings:
            binding_context = binding.to_context_items()
            for key, value in binding_context.items():
                if key not in context:
                    context[key] = value
                elif isinstance(value, dict) and isinstance(context[key], dict):
                    # Properly merge nested dictionaries
                    context[key].update(value)
                else:
                    context[key] = value

        try:
            rendered_title = self.render_template_str(event_title_template, context)
        except UndefinedError:
            logger.bind(event_title_template=event_title_template).warning(
                "UndefinedError occurred rendering event_title_template. Returning None."
            )
            return None
        else:
            return rendered_title


class SingletonVariableService(Singleton, VariableService):
    pass


def get_variable_service(
    db_engine: DatabaseEngine,
) -> VariableService:
    return SingletonVariableService(
        user_service=get_user_service_general(db_engine=db_engine),
        organization_service=get_organization_service_v2_from_engine(
            db_engine=db_engine
        ),
    )
