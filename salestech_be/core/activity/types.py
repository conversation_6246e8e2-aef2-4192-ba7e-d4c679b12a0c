from __future__ import annotations

from typing import Annotated, Any
from uuid import UUID, uuid4

from pydantic import BaseModel, Field

from salestech_be.common.schema_manager.std_object_field_identifier import (
    ActivityField,
    ActivitySubReferenceField,
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    DefaultEnum<PERSON>ieldProperty,
    ListFieldProperty,
    NestedObjectFieldProperty,
    TextFieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.schema import SkipDescriptor
from salestech_be.common.type.patch_request import (
    UNSET,
    BasePatchRequest,
    UnsetAware,
)
from salestech_be.core.common.types import DomainModel, FieldMetadata
from salestech_be.db.models.activity import (
    ActivityMetadata,
    ActivityPriority,
    ActivityReferenceIdType,
    ActivityStatus,
    ActivitySubReferenceType,
    ActivitySubType,
    ActivityType,
    ActivityV2,
)
from salestech_be.db.models.activity import (
    ActivitySubReference as DbActivitySubReference,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class ActivitySubReferenceRequest(BaseModel):
    value: str
    type: ActivitySubReferenceType
    contact_id: UUID | None = None
    user_id: UUID | None = None
    email_account_id: UUID | None = None
    display_name: str | None = None


class ActivityRequest(BaseModel):
    """Activity Object."""

    type: ActivityType
    sub_type: ActivitySubType
    priority: ActivityPriority
    status: ActivityStatus
    owner_user_id: UUID
    account_id: UUID | None
    pipeline_id: UUID | None = None
    sequence_id: UUID | None = None
    reference_id: str
    reference_id_type: ActivityReferenceIdType
    display_name: str
    sub_references: list[ActivitySubReferenceRequest] | None
    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    metadata: ActivityMetadata | None = None

    def to_dirty_db_activity_and_references(
        self,
        organization_id: UUID,
    ) -> tuple[ActivityV2, list[DbActivitySubReference]]:
        dirty_activity_id = uuid4()
        dirty_activity = ActivityV2(
            id=dirty_activity_id,
            organization_id=organization_id,
            owner_user_id=self.owner_user_id,
            type=self.type,
            reference_id=self.reference_id,
            sub_type=self.sub_type,
            priority=self.priority,
            status=self.status,
            account_id=self.account_id,
            sequence_id=self.sequence_id,
            display_name=self.display_name,
            created_at=self.created_at,
            created_by_user_id=self.created_by_user_id,
            metadata=self.metadata,
            pipeline_id=self.pipeline_id,
        )
        dirty_activity_sub_references = (
            [
                DbActivitySubReference(
                    id=uuid4(),
                    organization_id=organization_id,
                    activity_id=dirty_activity_id,
                    reference_value=sub_reference.value,
                    reference_type=sub_reference.type,
                    contact_id=sub_reference.contact_id,
                    created_at=self.created_at,
                    created_by_user_id=self.created_by_user_id,
                )
                for sub_reference in self.sub_references
            ]
            if self.sub_references
            else []
        )
        return dirty_activity, dirty_activity_sub_references


class ActivitySubReference(DomainModel):
    object_id = StdObjectIdentifiers.activity_sub_reference.identifier
    object_display_name = "Activity Sub Reference"
    field_name_provider = ActivitySubReferenceField
    value: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Value",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]
    type: Annotated[
        ActivitySubReferenceType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Type",
                enum_class=ActivitySubReferenceType,
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]
    contact_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Contact ID",
                is_ui_displayable=False,
                is_ui_editable=True,
            )
        ),
    ] = None
    user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="User ID",
                is_ui_displayable=False,
                is_ui_editable=True,
            )
        ),
    ] = None
    email_account_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Email Account ID",
                is_ui_displayable=False,
                is_ui_editable=True,
            )
        ),
    ] = None
    display_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Display Name",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ] = None


class Activity(DomainModel):
    object_id = StdObjectIdentifiers.activity.identifier
    object_display_name = "Activity"
    field_name_provider = ActivityField
    # Fields from ActivityRequest
    type: Annotated[
        ActivityType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Type",
                enum_class=ActivityType,
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]
    sub_type: Annotated[
        ActivitySubType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Sub Type",
                enum_class=ActivitySubType,
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]
    priority: Annotated[
        ActivityPriority,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Priority",
                enum_class=ActivityPriority,
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]
    status: Annotated[
        ActivityStatus,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Status",
                enum_class=ActivityStatus,
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]
    owner_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Owner User ID",
                is_ui_displayable=False,
                is_ui_editable=True,
            )
        ),
    ]
    account_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Account ID",
                is_ui_displayable=False,
                is_ui_editable=True,
            )
        ),
    ]
    pipeline_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Opportunity ID",
                is_ui_displayable=False,
                is_ui_editable=True,
            )
        ),
    ] = None
    sequence_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Sequence ID",
                is_ui_displayable=False,
                is_ui_editable=True,
            )
        ),
    ] = None
    reference_id: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Reference ID",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]
    reference_id_type: Annotated[
        ActivityReferenceIdType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Reference ID Type",
                enum_class=ActivityReferenceIdType,
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]
    display_name: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Display Name",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]
    sub_references: Annotated[
        list[ActivitySubReference] | None,
        FieldMetadata(
            type_property=ListFieldProperty(
                field_display_name="Sub References",
                element_field_type_property=NestedObjectFieldProperty(
                    field_display_name="Sub Reference",
                    object_identifier=ActivitySubReference.object_id,
                ),
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]
    created_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    created_by_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]
    metadata: Annotated[
        ActivityMetadata | None,
        SkipDescriptor(),
    ] = None

    # Additional fields specific to Activity
    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]
    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]
    contact_ids: Annotated[
        list[UUID],
        FieldMetadata(
            type_property=ListFieldProperty(
                field_display_name="Contact IDs",
                element_field_type_property=UUIDFieldProperty(
                    field_display_name="Contact ID",
                    is_ui_displayable=False,
                    is_ui_editable=True,
                ),
                is_ui_displayable=False,
                is_ui_editable=True,
            )
        ),
    ] = Field(default_factory=list)
    keywords: Annotated[
        list[str],
        FieldMetadata(
            type_property=ListFieldProperty(
                field_display_name="Keywords",
                element_field_type_property=TextFieldProperty(
                    field_display_name="Keyword",
                    is_ui_displayable=True,
                    is_ui_editable=True,
                ),
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ] = Field(default_factory=list)
    updated_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Updated At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    updated_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Updated By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    def __eq__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self is other or self.id == other.id

    def __hash__(self) -> int:
        return hash(self.id)

    @staticmethod
    def map_from_db(
        db_activity: ActivityV2,
        sub_references: list[DbActivitySubReference] | None = None,
    ) -> Activity:
        contact_ids: list[UUID] = []
        sub_reference_list: list[ActivitySubReference] = []
        keywords: set[str] = set()
        if sub_references:
            for db_sub_reference in sub_references:
                if db_sub_reference.contact_id:
                    contact_ids.append(db_sub_reference.contact_id)
                sub_reference_list.append(
                    ActivitySubReference(
                        type=db_sub_reference.reference_type,
                        value=db_sub_reference.reference_value,
                        contact_id=db_sub_reference.contact_id,
                    )
                )
                keywords.add(db_sub_reference.reference_value)

        return Activity(
            id=db_activity.id,
            organization_id=db_activity.organization_id,
            type=db_activity.type,
            sub_type=db_activity.sub_type,
            priority=db_activity.priority,
            status=db_activity.status,
            owner_user_id=db_activity.owner_user_id,
            account_id=db_activity.account_id,
            sequence_id=db_activity.sequence_id,
            reference_id=db_activity.reference_id,
            reference_id_type=ActivityType[db_activity.type].reference_id_type,
            display_name=db_activity.display_name,
            created_at=db_activity.created_at,
            created_by_user_id=db_activity.created_by_user_id,
            updated_at=db_activity.updated_at,
            sub_references=sub_reference_list,
            metadata=db_activity.metadata,
            contact_ids=contact_ids,
            keywords=list(keywords),
            pipeline_id=db_activity.pipeline_id,
        )


class ActivityPatchRequest(BasePatchRequest):
    pipeline_id: UnsetAware[UUID | None] = UNSET
    account_id: UnsetAware[UUID | None] = UNSET
    sub_type: UnsetAware[ActivitySubType] = UNSET
    display_name: UnsetAware[str] = UNSET
