from uuid import UUID

from salestech_be.db.dao.activity_repository import ActivityRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.activity import (
    ActivitySubReference,
    ActivityType,
)


class ActivityDataIntegrityService:
    def __init__(self, db_engine: DatabaseEngine):
        self.activity_repository = ActivityRepository(engine=db_engine)

    async def find_activity_sub_references_by_contact_id(
        self,
        *,
        contact_id: UUID,
        organization_id: UUID,
    ) -> list[ActivitySubReference]:
        return await self.activity_repository.list_activity_references_by_contact_ids(
            contact_ids=[contact_id],
            organization_id=organization_id,
        )

    async def find_activity_sub_references_by_contact_id_and_activity_type(
        self,
        *,
        contact_id: UUID,
        organization_id: UUID,
        activity_types: list[ActivityType],
    ) -> list[ActivitySubReference]:
        return await self.activity_repository.list_activity_references_by_contact_ids_and_activity_type(
            contact_ids=[contact_id],
            organization_id=organization_id,
            activity_types=activity_types,
        )

    async def replace_contact_id_in_activity_sub_reference(
        self,
        *,
        activity_sub_reference_id: UUID,
        organization_id: UUID,
        contact_id: UUID,
        replaced_contact_id: UUID | None,
        user_id: UUID,
    ) -> ActivitySubReference | None:
        if replaced_contact_id == contact_id:
            return None
        return (
            await self.activity_repository.replace_contact_id_in_activity_sub_reference(
                activity_sub_reference_id=activity_sub_reference_id,
                organization_id=organization_id,
                contact_id=contact_id,
                replaced_contact_id=replaced_contact_id,
                user_id=user_id,
            )
        )


def get_activity_data_integrity_service(
    db_engine: DatabaseEngine,
) -> ActivityDataIntegrityService:
    return ActivityDataIntegrityService(db_engine=db_engine)
