from uuid import UUID

from salestech_be.core.activity.service.activity_service import (
    ActivityService,
)
from salestech_be.core.activity.types import (
    ActivityPatchRequest,
)
from salestech_be.core.crm_integrity.event_processors.abstract_processor import (
    AbstractDataOperationProcessor,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
    DataAdditionParam,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationCommonPreviewIdentifier,
    IntegrityAssociatedDataOperationPreviewIdentifiers,
)
from salestech_be.core.pipeline.service.pipeline_query_service import (
    PipelineQueryService,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.task.service.task_v2_service import TaskV2Service
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.db.models.activity import ActivityType
from salestech_be.db.models.crm_integrity import (
    AssociatedEntityField,
    AssociatedEntityFieldType,
    AssociatedEntityOperation,
    AssociatedEntityType,
    CRMIntegrityJobUserOption,
)
from salestech_be.db.models.task import TaskReferenceIdType
from salestech_be.web.api.task.schema import (
    PatchTaskRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)


class TaskAccountAddToPipelineEventProcessor(
    AbstractDataOperationProcessor[DataAdditionParam, CrmIdReplacementTrackParam]
):
    def __init__(
        self,
        task_v2_service: TaskV2Service,
        pipeline_query_service: PipelineQueryService,
        activity_service: ActivityService,
    ) -> None:
        self.task_v2_service = task_v2_service
        self.pipeline_query_service = pipeline_query_service
        self.activity_service = activity_service

    async def fetch_pipeline(
        self, *, pipeline_id: UUID, organization_id: UUID
    ) -> PipelineV2:
        return await self.pipeline_query_service.get_pipeline_by_id(
            pipeline_id=pipeline_id,
            organization_id=organization_id,
        )

    async def fetch_affected_tasks(
        self, *, account_id: UUID, pipeline_id: UUID, organization_id: UUID
    ) -> list[TaskV2]:
        tasks_map = await self.task_v2_service.task_query_service.get_tasks_by_reference_ids_and_type(
            reference_ids=[account_id],
            reference_id_type=TaskReferenceIdType.ACCOUNT_ID,
            organization_id=organization_id,
        )
        tasks = tasks_map.get(account_id, [])

        pipeline = await self.fetch_pipeline(
            pipeline_id=pipeline_id,
            organization_id=organization_id,
        )
        return [
            task
            for task in tasks
            if set(task.contact_ids or []) & set(pipeline.all_contact_ids)
            and not task.pipeline_id
        ]

    async def process_task_activities(
        self,
        *,
        task_ids: list[UUID],
        replaced_pipeline_id: UUID,
        organization_id: UUID,
    ) -> list[CrmIdReplacementTrackParam]:
        track_params = []
        activities = (
            await self.activity_service.list_activities_by_reference_ids_and_type(
                organization_id=organization_id,
                reference_ids=[str(task_id) for task_id in task_ids],
                activity_type=ActivityType.TASK,
            )
        )

        for activity in activities:
            await self.activity_service.patch_activity_by_id(
                activity_id=activity.id,
                organization_id=organization_id,
                req=ActivityPatchRequest(pipeline_id=replaced_pipeline_id),
            )
            track_params.append(
                CrmIdReplacementTrackParam(
                    entity_type=AssociatedEntityType.ACTIVITY,
                    entity_id=activity.id,
                    entity_field_name=AssociatedEntityField.PIPELINE_ID,
                    entity_field_type=AssociatedEntityFieldType.UUID,
                    entity_operation=AssociatedEntityOperation.UPDATE,
                    before_value=str(activity.pipeline_id)
                    if activity.pipeline_id
                    else None,
                    after_value=str(replaced_pipeline_id),
                )
            )

        return track_params

    async def process_task_records(
        self,
        *,
        tasks: list[TaskV2],
        replaced_pipeline_id: UUID,
        organization_id: UUID,
        user_id: UUID,
    ) -> list[CrmIdReplacementTrackParam]:
        track_params = []

        for task in tasks:
            await self.task_v2_service.patch_by_id_v2(
                task_id=task.id,
                organization_id=organization_id,
                user_id=user_id,
                request=PatchTaskRequest(pipeline_id=replaced_pipeline_id),
            )
            track_params.append(
                CrmIdReplacementTrackParam(
                    entity_type=AssociatedEntityType.TASK,
                    entity_id=task.id,
                    entity_field_name=AssociatedEntityField.PIPELINE_ID,
                    entity_field_type=AssociatedEntityFieldType.UUID,
                    entity_operation=AssociatedEntityOperation.UPDATE,
                    before_value=str(task.pipeline_id) if task.pipeline_id else None,
                    after_value=str(replaced_pipeline_id),
                )
            )
        return track_params

    async def process(
        self,
        *,
        param: DataAdditionParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[CrmIdReplacementTrackParam]:
        track_params = []
        tasks = await self.fetch_affected_tasks(
            account_id=param.src_entity_id,
            pipeline_id=param.dest_entity_id,
            organization_id=param.organization_id,
        )

        track_params.extend(
            await self.process_task_records(
                tasks=tasks,
                replaced_pipeline_id=param.dest_entity_id,
                organization_id=param.organization_id,
                user_id=param.user_id,
            )
        )
        track_params.extend(
            await self.process_task_activities(
                task_ids=[task.id for task in tasks],
                replaced_pipeline_id=param.dest_entity_id,
                organization_id=param.organization_id,
            )
        )

        return track_params

    async def preview(
        self,
        *,
        param: DataAdditionParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[IntegrityAssociatedDataOperationPreviewIdentifiers]:
        tasks = await self.fetch_affected_tasks(
            account_id=param.src_entity_id,
            pipeline_id=param.dest_entity_id,
            organization_id=param.organization_id,
        )
        return [
            IntegrityAssociatedDataOperationPreviewIdentifiers(
                entity_type=AssociatedEntityType.TASK,
                entity_ids=[
                    IntegrityAssociatedDataOperationCommonPreviewIdentifier(id=task.id)
                    for task in tasks
                ],
            )
        ]
