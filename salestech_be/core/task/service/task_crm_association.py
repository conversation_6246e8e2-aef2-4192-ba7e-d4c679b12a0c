from uuid import UUID

from salestech_be.core.domain_crm_association.types import (
    CreateTaskCrmAssociation,
    # DeleteTaskCrmAssociation,
)
from salestech_be.db.models.domain_crm_association import DomainCRMAssociationRole
from salestech_be.ree_logging import get_logger
from salestech_be.web.api.task.schema import (
    CreateTaskRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class _TaskCrmAssociationMapper:
    """
    Internal class intended to be used only in task modules that provides static
    functions for crm association related operations.
    """

    @staticmethod
    async def map_task_to_user_associations_create(
        task_id: UUID,
        create_task_request: CreateTaskRequest,
        organization_id: UUID,
        created_by_user_id: UUID,
    ) -> list[CreateTaskCrmAssociation]:
        user_associations_to_create: list[CreateTaskCrmAssociation] = []
        task_user_ids: set[tuple[UUID, bool]] = set()

        # Add participants to user associations if present
        for user_id in create_task_request.participant_user_id_list or []:
            task_user_ids.add((user_id, user_id == create_task_request.owner_user_id))
        for user_id, is_owner in task_user_ids:
            user_associations_to_create.append(
                CreateTaskCrmAssociation(
                    organization_id=organization_id,
                    created_by_user_id=created_by_user_id,
                    task_id=task_id,
                    association_role=DomainCRMAssociationRole.OWNER
                    if is_owner
                    else DomainCRMAssociationRole.PARTICIPANT,
                    user_id=user_id,
                    contact_id=None,
                    account_id=create_task_request.account_id,
                    pipeline_id=create_task_request.pipeline_id,
                    sequence_id=create_task_request.sequence_id,
                    sequence_enrollment_id=create_task_request.sequence_enrollment_id,
                    sequence_step_id=create_task_request.sequence_step_id,
                    sequence_step_variant_id=create_task_request.sequence_step_variant_id,
                    sequence_step_execution_id=create_task_request.sequence_step_execution_id,
                    global_thread_id=create_task_request.email_thread_ids[0]
                    if create_task_request.email_thread_ids
                    else None,  # ignoring complex relationship between domains
                    meeting_id=create_task_request.meeting_id,
                    call_id=create_task_request.voice_call_id,
                )
            )
        return user_associations_to_create

    @staticmethod
    async def map_task_to_contact_associations_create(
        task_id: UUID,
        create_task_request: CreateTaskRequest,
        organization_id: UUID,
        created_by_user_id: UUID,
    ) -> list[CreateTaskCrmAssociation]:
        contact_associations_to_create: list[CreateTaskCrmAssociation] = []

        # Process contact references
        for contact_id in create_task_request.contact_ids or []:
            contact_associations_to_create.append(
                CreateTaskCrmAssociation(
                    organization_id=organization_id,
                    created_by_user_id=created_by_user_id,
                    task_id=task_id,
                    user_id=None,
                    contact_id=contact_id,
                    account_id=create_task_request.account_id,
                    pipeline_id=create_task_request.pipeline_id,
                    sequence_id=create_task_request.sequence_id,
                    sequence_enrollment_id=create_task_request.sequence_enrollment_id,
                    sequence_step_id=create_task_request.sequence_step_id,
                    sequence_step_variant_id=create_task_request.sequence_step_variant_id,
                    sequence_step_execution_id=create_task_request.sequence_step_execution_id,
                    global_thread_id=create_task_request.email_thread_ids[0]
                    if create_task_request.email_thread_ids
                    else None,  # ignoring complex relationship between domains
                    meeting_id=create_task_request.meeting_id,
                    call_id=create_task_request.voice_call_id,
                )
            )
        return contact_associations_to_create

    @staticmethod
    async def map_task_to_associations_create(
        task_id: UUID,
        create_task_request: CreateTaskRequest,
        organization_id: UUID,
        created_by_user_id: UUID,
    ) -> list[CreateTaskCrmAssociation]:
        associations_to_create: list[CreateTaskCrmAssociation] = []

        # Get user associations
        user_associations = (
            await _TaskCrmAssociationMapper.map_task_to_user_associations_create(
                task_id=task_id,
                create_task_request=create_task_request,
                organization_id=organization_id,
                created_by_user_id=created_by_user_id,
            )
        )
        associations_to_create.extend(user_associations)

        # Get contact associations
        contact_associations = (
            await _TaskCrmAssociationMapper.map_task_to_contact_associations_create(
                task_id=task_id,
                create_task_request=create_task_request,
                organization_id=organization_id,
                created_by_user_id=created_by_user_id,
            )
        )
        associations_to_create.extend(contact_associations)

        return associations_to_create
