from collections import defaultdict
from datetime import datetime, time, timedelta
from sqlite3 import IntegrityError
from typing import Any, cast
from uuid import UUID, uuid4
from zoneinfo import ZoneInfo

import grpc
import temporalio
from fastapi import HTTPException, Request, status
from temporalio.client import WorkflowExecutionStatus
from temporalio.common import WorkflowIDReusePolicy

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import BasePatchRequest, specified
from salestech_be.core.account.service.account_query_service import (
    AccountQueryService,
    get_account_query_service,
)
from salestech_be.core.activity.service.activity_service import (
    ActivityService,
    get_activity_service_general,
)
from salestech_be.core.activity.types import (
    ActivityRequest,
    ActivitySubReferenceRequest,
)
from salestech_be.core.common.domain_service import DomainService
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
    get_custom_object_service,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.domain_crm_association.domain_crm_association_service import (
    DomainCRMAssociationService,
    get_domain_crm_association_service,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.imports.service.crm_sync_push_service import (
    CrmSyncPushService,
    get_crm_sync_push_service,
)
from salestech_be.core.notification.service.notification_service import (
    NotificationService,
    get_notification_service_by_db_engine,
)
from salestech_be.core.notification.types import SendNotificationRequest
from salestech_be.core.task.service.task_crm_association import (
    _TaskCrmAssociationMapper,
)
from salestech_be.core.task.service.task_query_service import (
    TaskQueryService,
    get_task_query_service_from_engine,
)
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.core.user.service.user_notification_service import (
    UserNotificationService,
    get_user_notification_service_by_db_engine,
)
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.core.user.types import UserDTO
from salestech_be.db.dao.organization_info_repository import OrganizationInfoRepository
from salestech_be.db.dao.task_repository import TaskRepository, TaskTemplateRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.activity import (
    ActivityPriority,
    ActivityReferenceIdType,
    ActivityStatus,
    ActivitySubReferenceType,
    ActivitySubType,
    ActivityType,
)
from salestech_be.db.models.notification import (
    NotificationReferenceIdType,
    NotificationTaskAssignedData,
)
from salestech_be.db.models.organization_info import OrganizationInfo
from salestech_be.db.models.task import (
    EntityParticipant,
    TaskReference,
    TaskReferenceIdType,
    TaskReferenceRelationshipType,
    TaskStatus,
    TaskTemplate,
)
from salestech_be.db.models.task import (
    Task as TaskDb,
)
from salestech_be.db.models.user_notification import (
    NotificationPlatform,
    NotificationType,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import (
    TemporalTaskQueue,
)
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.workflows.notification.task_notification_workflow import (
    TaskNotificationWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    TaskNotificationWorkflowInput,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.common.container import (
    DeleteEntityResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.task.schema import (
    CreateTaskRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CreateTaskTemplateRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchTaskRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchTaskTemplateRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    TaskTemplateResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)

_DEFAULT_TASK_SUMMARY_NOTIFICATION_HOUR = 8
_DEFAULT_TASK_SUMMARY_NOTIFICATION_TIMEZONE = "America/Los_Angeles"


class TaskV2Service(DomainService[TaskV2]):
    def __init__(
        self,
        task_repository: TaskRepository,
        custom_object_service: CustomObjectService,
        task_query_service: TaskQueryService,
        task_template_repository: TaskTemplateRepository,
        organization_info_repository: OrganizationInfoRepository,
        user_notification_service: UserNotificationService,
        user_service: UserService,
        notification_service: NotificationService,
        contact_query_service: ContactQueryService,
        account_query_service: AccountQueryService,
        activity_service: ActivityService,
        crm_sync_push_service: CrmSyncPushService,
        feature_flag_service: FeatureFlagService,
        domain_crm_association_service: DomainCRMAssociationService,
    ):
        super().__init__(feature_flag_service=feature_flag_service)
        self.task_repository = task_repository
        self.custom_object_service = custom_object_service
        self.task_query_service = task_query_service
        self.task_template_repository = task_template_repository
        self.organization_info_repository = organization_info_repository
        self.user_notification_service = user_notification_service
        self.user_service = user_service
        self.notification_service = notification_service
        self.contact_query_service = contact_query_service
        self.account_query_service = account_query_service
        self.activity_service = activity_service
        self.crm_sync_push_service = crm_sync_push_service
        self.domain_crm_association_service = domain_crm_association_service

    async def create_entity(
        self, organization_id: UUID, user_id: UUID, request: CreateTaskRequest
    ) -> TaskV2:
        return await self.insert_task_v2(
            created_by_user_id=user_id,
            organization_id=organization_id,
            request=request,
        )

    async def insert_task_v2(  # noqa: C901, PLR0912, PLR0915
        self,
        created_by_user_id: UUID,
        organization_id: UUID,
        request: CreateTaskRequest,
    ) -> TaskV2:
        """Create a new task using TaskV2 model.

        Args:
            user_id: The ID of the user creating the task
            organization_id: The organization ID the task belongs to
            request: The create task request

        Returns:
            TaskV2: The created task

        Raises:
            InvalidArgumentError: If account/contacts validation fails
        """
        logger.bind(
            created_by_user_id=created_by_user_id,
            organization_id=organization_id,
            request=request,
        ).info("Create task V2 request")

        # Validate account exists
        if request.account_id:
            await self.account_query_service.get_account_v2(
                account_id=request.account_id,
                organization_id=organization_id,
            )

        # Validate contacts exist
        if request.contact_ids:
            contacts = await self.contact_query_service.list_contacts_v2(
                organization_id=organization_id,
                only_include_contact_ids=set(request.contact_ids),
            )

            missing_contact_ids = [
                contact_id
                for contact_id in request.contact_ids
                if contact_id not in {contact.id for contact in contacts}
            ]

            if missing_contact_ids:
                raise ResourceNotFoundError(
                    f"Contacts with ids {missing_contact_ids} not found"
                )

        # Validate contacts are associated with account if both are specified
        if request.account_id and request.contact_ids:
            # Get active contact associations for the account
            account_contacts = await self.contact_query_service.list_active_contact_associations_for_account(
                organization_id=organization_id,
                account_id=request.account_id,
            )
            account_contact_ids = {assoc.contact_id for assoc in account_contacts}

            # Check if at least one contact in request is associated with the account
            if not (set(request.contact_ids) & account_contact_ids):
                raise InvalidArgumentError(
                    f"None of the contacts {request.contact_ids} are associated with account {request.account_id}. At least one contact must be associated."
                )

        now = zoned_utc_now()
        task_db = TaskDb(
            id=uuid4(),
            title=request.title,
            status=request.status.name,
            priority=request.priority.name,
            type=request.type.name,
            owner_user_id=request.owner_user_id,
            due_at=request.due_at,
            note=request.note,
            created_at=now,
            created_by_user_id=created_by_user_id,
            updated_at=now,
            organization_id=organization_id,
            is_time_specified=request.is_time_specified,
            source_type=request.source_type.name,
            participants=EntityParticipant.list_from_request_field(
                request.participant_user_id_list
            ),
        )

        if not request.created_source or request.created_source.is_not_crm():
            await self.crm_sync_push_service.sync_push_obj_task_create(
                organization_id=organization_id,
                task_db=task_db,
            )
        task_id = not_none(await self.task_repository.insert(task_db)).id
        logger.bind(task_id=task_id).info("Created task")

        # Log reference ID insertions
        if request.pipeline_id:
            logger.bind(pipeline_id=request.pipeline_id).info(
                "Adding pipeline reference"
            )
            await self._insert_task_reference_ids(
                reference_ids=[request.pipeline_id],
                reference_id_type=TaskReferenceIdType.PIPELINE_ID,
                task_id=task_id,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )

        if request.contact_ids:
            logger.bind(contact_ids=request.contact_ids).info(
                "Adding contact references"
            )
            await self._insert_task_reference_ids(
                reference_ids=request.contact_ids,
                reference_id_type=TaskReferenceIdType.CONTACT_ID,
                task_id=task_id,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )

        if request.account_id:
            logger.bind(account_id=request.account_id).info("Adding account references")
            await self._insert_task_reference_ids(
                reference_ids=[request.account_id],
                reference_id_type=TaskReferenceIdType.ACCOUNT_ID,
                task_id=task_id,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )

        if request.sequence_id:
            logger.bind(sequence_id=request.sequence_id).info(
                "Adding sequence reference"
            )
            await self._insert_task_reference_ids(
                reference_ids=[request.sequence_id],
                reference_id_type=TaskReferenceIdType.SEQUENCE_ID,
                task_id=task_id,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )

        if request.email_thread_ids:
            logger.bind(email_thread_ids=request.email_thread_ids).info(
                "Adding email thread references"
            )
            await self._insert_task_reference_ids(
                reference_ids=request.email_thread_ids,
                reference_id_type=TaskReferenceIdType.EMAIL_THREAD_ID,
                task_id=task_id,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )

        if request.email_ids:
            logger.bind(email_ids=request.email_ids).info("Adding email references")
            await self._insert_task_reference_ids(
                reference_ids=request.email_ids,
                reference_id_type=TaskReferenceIdType.EMAIL_ID,
                task_id=task_id,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )

        if request.meeting_id:
            logger.bind(meeting_id=request.meeting_id).info("Adding meeting reference")
            await self._insert_task_reference_ids(
                reference_ids=[request.meeting_id],
                reference_id_type=TaskReferenceIdType.MEETING_ID,
                task_id=task_id,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )

        if request.voice_call_id:
            logger.bind(voice_call_id=request.voice_call_id).info(
                "Adding voice call reference"
            )
            await self._insert_task_reference_ids(
                reference_ids=[request.voice_call_id],
                reference_id_type=TaskReferenceIdType.VOICE_CALL_ID,
                task_id=task_id,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )

        if request.insight_id:
            logger.bind(insight_id=request.insight_id).info("Adding insight reference")
            await self._insert_task_reference_ids(
                reference_ids=[request.insight_id],
                reference_id_type=TaskReferenceIdType.INSIGHT_ID,
                task_id=task_id,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )

        if request.sequence_enrollment_id:
            logger.bind(sequence_enrollment_id=request.sequence_enrollment_id).info(
                "Adding sequence enrollment reference"
            )
            await self._insert_task_reference_ids(
                reference_ids=[request.sequence_enrollment_id],
                reference_id_type=TaskReferenceIdType.SEQUENCE_ENROLLMENT_ID,
                task_id=task_id,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )

        if request.sequence_step_id:
            logger.bind(sequence_step_id=request.sequence_step_id).info(
                "Adding sequence step reference"
            )
            await self._insert_task_reference_ids(
                reference_ids=[request.sequence_step_id],
                reference_id_type=TaskReferenceIdType.SEQUENCE_STEP_ID,
                task_id=task_id,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )

        if request.sequence_step_execution_id:
            logger.bind(
                sequence_step_execution_id=request.sequence_step_execution_id
            ).info("Adding sequence step execution reference")
            await self._insert_task_reference_ids(
                reference_ids=[request.sequence_step_execution_id],
                reference_id_type=TaskReferenceIdType.SEQUENCE_STEP_EXECUTION_ID,
                task_id=task_id,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )

        if request.sequence_step_variant_id:
            logger.bind(sequence_step_variant_id=request.sequence_step_variant_id).info(
                "Adding sequence step variant reference"
            )
            await self._insert_task_reference_ids(
                reference_ids=[request.sequence_step_variant_id],
                reference_id_type=TaskReferenceIdType.SEQUENCE_STEP_VARIANT_ID,
                task_id=task_id,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )

        if request.custom_field_data and created_by_user_id:
            logger.bind(custom_field_data=request.custom_field_data).info(
                "Creating custom field data"
            )
            await self.custom_object_service.create_custom_object_data_by_extension_id(
                user_id=created_by_user_id,
                organization_id=organization_id,
                parent_object_name=ExtendableStandardObject.task,
                extension_id=task_id,
                custom_field_data_by_field_id=request.custom_field_data,
            )

        if created_by_user_id:  # TODO: clarify expected behavior when user_id is None (i.e task is system-generated)
            # insert activity v2
            activity_sub_references: list[ActivitySubReferenceRequest] = []
            if request.contact_ids:
                logger.bind(reference_id=task_id, contact_ids=request.contact_ids).info(
                    "Adding activity sub references for task assigned to contacts"
                )
                for contact_id in request.contact_ids:
                    activity_sub_references.append(
                        ActivitySubReferenceRequest(
                            type=ActivitySubReferenceType.TASK_ASSIGNED_TO,
                            value=str(request.owner_user_id),
                            contact_id=contact_id,
                        )
                    )
            if request.account_id:
                logger.bind(reference_id=task_id, account_id=request.account_id).info(
                    "Adding activity for task assigned to account"
                )
                await self.activity_service.insert_activity(
                    organization_id=organization_id,
                    insert_activity_request=ActivityRequest(
                        type=ActivityType.TASK,
                        sub_type=ActivitySubType.TASK_CREATED,
                        priority=ActivityPriority.MEDIUM,
                        status=ActivityStatus.NEW,
                        owner_user_id=created_by_user_id,
                        account_id=request.account_id,
                        reference_id_type=ActivityReferenceIdType.TASK_ID,
                        reference_id=str(task_id),
                        display_name="Task Created.",
                        created_at=zoned_utc_now(),
                        created_by_user_id=created_by_user_id,
                        sub_references=activity_sub_references,
                    ),
                )
            else:
                logger.bind(reference_id=task_id).info(
                    "Adding other activity for task created"
                )
                await self.activity_service.insert_activity(
                    organization_id=organization_id,
                    insert_activity_request=ActivityRequest(
                        type=ActivityType.TASK,
                        sub_type=ActivitySubType.TASK_CREATED,
                        priority=ActivityPriority.MEDIUM,
                        status=ActivityStatus.NEW,
                        owner_user_id=created_by_user_id,
                        account_id=None,
                        reference_id_type=ActivityReferenceIdType.TASK_ID,
                        reference_id=str(task_id),
                        display_name="Task Created.",
                        created_at=zoned_utc_now(),
                        created_by_user_id=created_by_user_id,
                        sub_references=activity_sub_references,
                    ),
                )
            if request.custom_field_data:
                await self.custom_object_service.create_custom_object_data_by_extension_id(
                    user_id=created_by_user_id,
                    organization_id=organization_id,
                    parent_object_name=ExtendableStandardObject.task,
                    extension_id=task_id,
                    custom_field_data_by_field_id=request.custom_field_data,
                )

        logger.bind(organization_id=organization_id, task_id=task_id).info(
            "Task V2 creation complete"
        )
        task = await self.task_query_service.get_by_id_v2(task_id, organization_id)

        try:
            await self._create_task_crm_associations(
                task.id, request, organization_id, created_by_user_id
            )
        except Exception as e:
            logger.error("Failed to create task crm associations", exc_info=e)

        # Trigger notification flow
        await _send_task_notification(task_id, organization_id, task.due_at)

        return task

    async def _create_task_crm_associations(
        self,
        task_id: UUID,
        create_task_request: CreateTaskRequest,
        organization_id: UUID,
        created_by_user_id: UUID,
    ) -> None:
        # Get all associations to create
        associations_to_create = (
            await _TaskCrmAssociationMapper.map_task_to_associations_create(
                task_id=task_id,
                create_task_request=create_task_request,
                organization_id=organization_id,
                created_by_user_id=created_by_user_id,
            )
        )
        # Create all associations
        await self.domain_crm_association_service.bulk_create_domain_crm_associations(
            domain_crm_associations=associations_to_create,
        )

    async def _insert_task_reference_ids(
        self,
        reference_ids: list[UUID] | None,
        reference_id_type: TaskReferenceIdType,
        task_id: UUID,
        created_by_user_id: UUID,
        organization_id: UUID,
    ) -> None:
        if reference_ids:
            logger.bind(
                reference_ids=reference_ids,
                reference_id_type=reference_id_type,
                task_id=task_id,
            ).info("Inserting task references")

            for reference_id in reference_ids:
                await self.task_repository.insert(
                    TaskReference(
                        id=uuid4(),
                        task_id=task_id,
                        reference_id=str(reference_id),
                        reference_id_type=reference_id_type.name,
                        created_at=zoned_utc_now(),
                        created_by_user_id=created_by_user_id,
                        organization_id=organization_id,
                    )
                )
                logger.bind(reference_id=reference_id).info(
                    f"Inserted task reference w reference_id: {reference_id}"
                )

    async def get_entity(
        self, entity_id: UUID, organization_id: UUID, user_id: UUID
    ) -> TaskV2 | None:
        try:
            return await self.task_query_service.get_by_id_v2(
                task_id=entity_id,
                organization_id=organization_id,
            )
        except ResourceNotFoundError:
            return None

    async def update_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        entity: TaskV2,
        request: BasePatchRequest,
    ) -> TaskV2:
        request = cast(PatchTaskRequest, request)
        return await self.patch_by_id_v2(
            task_id=entity.id,
            organization_id=organization_id,
            user_id=user_id,
            request=request,
        )

    async def patch_by_id_v2(
        self,
        task_id: UUID,
        organization_id: UUID,
        user_id: UUID,
        request: PatchTaskRequest,
    ) -> TaskV2:
        """Update a task by ID using TaskV2 model.

        Args:
            task_id: The ID of the task to update
            organization_id: The organization ID the task belongs to
            user_id: The ID of the user making the update
            request: The patch request containing fields to update

        Returns:
            TaskV2: The updated task

        Raises:
            ResourceNotFoundError: If task not found
        """
        logger.bind(
            task_id=task_id,
            organization_id=organization_id,
            user_id=user_id,
            request=request,
        ).info("Patch task V2 request")

        existing_task = await self.task_query_service.get_by_id_v2(
            task_id, organization_id
        )

        db_task_fields_to_update = request.model_dump(
            include={
                "title",
                "status",
                "priority",
                "owner_user_id",
                "due_at",
                "note",
                "disposition",
            }
        )

        # re-map Request attribute name to DB column name, along with type conversion.
        if (
            specified(request.participant_user_id_list)
            and request.participant_user_id_list is not None
        ):
            db_task_fields_to_update["participants"] = (
                EntityParticipant.list_from_request_field(
                    request.participant_user_id_list
                )
            )

        if db_task_fields_to_update:
            db_task_fields_to_update["updated_at"] = zoned_utc_now()
            db_task_fields_to_update["updated_by_user_id"] = user_id

            if (
                specified(request.status)
                and request.status != existing_task.status
                and request.status == TaskStatus.COMPLETED
            ):
                db_task_fields_to_update["completed_at"] = zoned_utc_now()
                db_task_fields_to_update["completed_by_user_id"] = user_id
            elif (
                specified(request.status)
                and request.status != existing_task.status
                and request.status
                in (
                    TaskStatus.OPEN,
                    TaskStatus.IN_PROGRESS,
                    TaskStatus.BLOCKED,
                    TaskStatus.CLOSED,
                )
            ):
                db_task_fields_to_update["completed_at"] = None
                db_task_fields_to_update["completed_by_user_id"] = None
            logger.bind(fields_to_update=db_task_fields_to_update).info(
                "Updating task fields"
            )

            if not request.created_source or request.created_source.is_not_crm():
                await self.crm_sync_push_service.sync_push_obj_task_update(
                    organization_id=organization_id,
                    task_id=task_id,
                    fields_to_update=db_task_fields_to_update,
                )
            not_none(
                await self.task_repository.update_by_tenanted_primary_key(
                    table_model=TaskDb,
                    primary_key_to_value={"id": task_id},
                    organization_id=organization_id,
                    column_to_update=db_task_fields_to_update,
                )
            )

        await self._find_target_task_reference(
            request, task_id, organization_id, user_id, existing_task
        )

        if specified(request.custom_field_data) and request.custom_field_data:
            logger.bind(custom_field_data=request.custom_field_data).info(
                "Updating custom field data"
            )
            await (
                self.custom_object_service.update_custom_object_data_by_extension_id_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    parent_object_name=ExtendableStandardObject.task,
                    extension_id=task_id,
                    custom_field_data_by_field_id=request.custom_field_data,
                )
            )

        patched_task = await self.task_query_service.get_by_id_v2(
            task_id, organization_id
        )
        if (
            specified(request.owner_user_id)
            and request.owner_user_id
            and request.owner_user_id != existing_task.owner_user_id
        ):
            logger.bind(owner_user_id=request.owner_user_id).info(
                "Reassigning task from user"
            )
            try:
                await self._send_task_assignment_notification(
                    task_id=task_id,
                    organization_id=organization_id,
                    user_id=user_id,
                    patched_task=patched_task,
                    existing_task=existing_task,
                )
            except Exception as e:
                logger.error("Failed to trigger TASK_ASSIGN notification", exc_info=e)

        if specified(request.due_at) and patched_task.due_at != existing_task.due_at:
            await self._update_task_notification_workflow(
                task_id=task_id,
                organization_id=organization_id,
                due_at=patched_task.due_at,
            )

        # TODO: Update task CRM associations
        return patched_task

    async def remove_entity(
        self, organization_id: UUID, user_id: UUID, entity_id: UUID
    ) -> DeleteEntityResponse:
        logger.bind(
            organization_id=organization_id, user_id=user_id, task_id=entity_id
        ).info("Archive task request")
        deleted_task = not_none(
            await self.task_repository.update_by_tenanted_primary_key(
                table_model=TaskDb,
                organization_id=organization_id,
                primary_key_to_value={"id": entity_id},
                column_to_update={
                    "archived_by_user_id": user_id,
                    "archived_at": zoned_utc_now(),
                },
            )
        )
        return DeleteEntityResponse(
            id=entity_id,
            deleted_at=deleted_task.archived_at,
            deleted_by_user_id=deleted_task.archived_by_user_id,
        )

    async def _find_target_task_reference(
        self,
        request: PatchTaskRequest,
        task_id: UUID,
        organization_id: UUID,
        user_id: UUID,
        existing_task: TaskV2,
    ) -> None:
        if specified(request.pipeline_id):
            logger.bind(pipeline_id=request.pipeline_id).info(
                "Updating pipeline reference"
            )
            await self._patch_task_reference(
                task_id=task_id,
                organization_id=organization_id,
                reference_id_type=TaskReferenceIdType.PIPELINE_ID,
                user_id=user_id,
                reference_ids=[request.pipeline_id] if request.pipeline_id else None,
                existing_reference_ids=[existing_task.pipeline_id]
                if existing_task.pipeline_id
                else None,
                relationship_type=request.relationship_type
                if specified(request.relationship_type)
                else None,
            )

        if specified(request.contact_ids):
            logger.bind(contact_ids=request.contact_ids).info(
                "Updating contact references"
            )
            await self._patch_task_reference(
                task_id=task_id,
                organization_id=organization_id,
                reference_id_type=TaskReferenceIdType.CONTACT_ID,
                user_id=user_id,
                reference_ids=request.contact_ids,
                existing_reference_ids=existing_task.contact_ids,
                relationship_type=request.relationship_type
                if specified(request.relationship_type)
                else None,
            )

        if specified(request.account_id):
            logger.bind(account_ids=request.account_id).info(
                "Updating account reference"
            )
            await self._patch_task_reference(
                task_id=task_id,
                organization_id=organization_id,
                reference_id_type=TaskReferenceIdType.ACCOUNT_ID,
                user_id=user_id,
                reference_ids=[request.account_id] if request.account_id else None,
                existing_reference_ids=[existing_task.account_id]
                if existing_task.account_id
                else None,
                relationship_type=request.relationship_type
                if specified(request.relationship_type)
                else None,
            )

        if specified(request.meeting_id) and request.meeting_id:
            logger.bind(meeting_id=request.meeting_id).info("Updating meeting id")
            await self._patch_task_reference(
                task_id=task_id,
                organization_id=organization_id,
                reference_id_type=TaskReferenceIdType.MEETING_ID,
                user_id=user_id,
                reference_ids=[request.meeting_id] if request.meeting_id else None,
                existing_reference_ids=[existing_task.meeting_id]
                if existing_task.meeting_id
                else None,
                relationship_type=request.relationship_type
                if specified(request.relationship_type)
                else None,
            )

    async def _patch_task_reference(
        self,
        task_id: UUID,
        organization_id: UUID,
        reference_id_type: TaskReferenceIdType,
        user_id: UUID,
        reference_ids: list[UUID] | None,
        existing_reference_id: UUID | None = None,
        existing_reference_ids: list[UUID] | None = None,
        relationship_type: TaskReferenceRelationshipType | None = None,
    ) -> None:
        """Update task references of a specific type.
        Args:
            task_id: The task ID
            organization_id: The organization ID
            reference_id_type: The type of reference to update
            user_id: The user making the update
            reference_ids: The new reference IDs
            existing_reference_id: For single reference fields (e.g. pipeline_id)
            existing_reference_ids: For multi-reference fields (e.g. contact_ids)
        """
        logger.bind(
            task_id=task_id,
            reference_id_type=reference_id_type,
            reference_ids=reference_ids,
            existing_reference_id=existing_reference_id,
            existing_reference_ids=existing_reference_ids,
            relationship_type=relationship_type,
        ).info("Patching task references")

        new_reference_set = set(reference_ids) if reference_ids else set()

        if existing_reference_ids is not None:
            existing_reference_set = set(existing_reference_ids)
        elif existing_reference_id is not None:
            existing_reference_set = (
                {existing_reference_id} if existing_reference_id else set()
            )
        else:
            existing_reference_set = set()

        # Remove old references
        reference_ids_to_remove = existing_reference_set - new_reference_set
        for reference_id_to_remove in reference_ids_to_remove:
            await self.task_repository.delete_task_references(
                task_id=task_id,
                organization_id=organization_id,
                reference_id=str(reference_id_to_remove),
                reference_id_type=reference_id_type.name,
                deleted_by_user_id=user_id,
                deleted_at=zoned_utc_now(),
            )

        # Add new references
        reference_ids_to_add = new_reference_set - existing_reference_set
        for reference_id_to_add in reference_ids_to_add:
            try:
                await self.task_repository.insert(
                    TaskReference(
                        id=uuid4(),
                        task_id=task_id,
                        organization_id=organization_id,
                        reference_id=str(reference_id_to_add),
                        reference_id_type=reference_id_type.name,
                        created_at=zoned_utc_now(),
                        created_by_user_id=user_id,
                        relationship_type=relationship_type
                        if relationship_type
                        else None,
                    )
                )
            except IntegrityError:
                logger.warning(
                    f"reference_id already in db: {reference_id_to_add}, task id: {task_id}"
                )

    async def insert_task_template(
        self,
        user_id: UUID,
        organization_id: UUID,
        request: CreateTaskTemplateRequest,
    ) -> TaskTemplate:
        logger.bind(
            organization_id=organization_id, user_id=user_id, request=request
        ).info("Create task template request")
        task_template = TaskTemplate(
            id=uuid4(),
            title=request.title,
            priority=request.priority.name,
            type=request.type.name,
            note=request.note,
            created_by_user_id=user_id,
            organization_id=organization_id,
        )
        return await self.task_template_repository.insert(task_template)

    async def get_task_template(
        self,
        task_template_id: UUID,
        organization_id: UUID,
    ) -> TaskTemplateResponse:
        task_template = (
            await self.task_template_repository.find_by_tenanted_primary_key(
                TaskTemplate,
                id=task_template_id,
                organization_id=organization_id,
            )
        )
        if not task_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cannot find the task template",
            )
        else:
            return TaskTemplateResponse(task_template)

    async def patch_template(
        self,
        user_id: UUID,
        organization_id: UUID,
        task_template_id: UUID,
        request: PatchTaskTemplateRequest,
    ) -> TaskTemplateResponse:
        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            task_template_id=task_template_id,
            request=request,
        ).info("Patch task template request")
        column_to_update = self._fields_to_update(
            request,
        )
        if column_to_update:
            column_to_update["updated_by_user_id"] = user_id
            logger.bind(
                organization_id=organization_id,
                task_template_id=task_template_id,
                fields_to_update=column_to_update,
            ).info("Updating task template")
            task_template = (
                await self.task_template_repository.update_by_tenanted_primary_key(
                    TaskTemplate,
                    primary_key_to_value={"id": task_template_id},
                    organization_id=organization_id,
                    column_to_update=column_to_update,
                )
            )
            if not task_template:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Cannot find the task template",
                )
            else:
                return TaskTemplateResponse(task_template)
        else:
            raise HTTPException(
                status_code=status.HTTP_204_NO_CONTENT,
                detail="No changes necessary",
            )

    async def delete_template(
        self,
        user_id: UUID,
        organization_id: UUID,
        task_template_id: UUID,
    ) -> TaskTemplate:
        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            task_template_id=task_template_id,
        ).info("Delete task template request")
        return not_none(
            await self.task_template_repository.update_by_tenanted_primary_key(
                TaskTemplate,
                organization_id=organization_id,
                primary_key_to_value={
                    "id": task_template_id,
                },
                column_to_update={
                    "archived_by_user_id": user_id,
                    "archived_at": zoned_utc_now(),
                },
            )
        )

    async def get_task_references(
        self,
        task_ids: list[UUID],
        organization_id: UUID,
    ) -> list[TaskReference]:
        """Get task references for given task IDs.

        Args:
            task_ids: List of task IDs to get references for
            organization_id: Organization ID

        Returns:
            List of TaskReference objects
        """
        return await self.task_repository.find_task_references_by_task_ids(
            task_ids=task_ids,
            organization_id=organization_id,
        )

    async def send_task_summary_notifications_task(self) -> None:
        logger.info("Send task summary notifications request")
        organization_infos = await self.organization_info_repository.find_for_profile_task_digest_enabled()
        for organization_info in organization_infos:
            organization_users = await self.user_service.list_all_users_in_organization(
                organization_id=organization_info.organization_id,
                active_users_only=True,
            )
            await self._send_task_summary_notifications_for_organization(
                organization_info=organization_info,
                organization_users=organization_users,
            )

    async def send_task_summary_notification_for_user(
        self, organization_id: UUID, user_id: UUID
    ) -> None:
        logger.bind(organization_id=organization_id, user_id=user_id).info(
            "Sending task summary notification"
        )
        organization_info = not_none(
            await self.organization_info_repository.find_by_organization_id(
                organization_id=organization_id
            )
        )
        organization_user = not_none(
            await self.user_service.get_by_id_and_organization_id(
                organization_id=organization_id, user_id=user_id
            )
        )
        await self._send_task_summary_notifications_for_organization(
            organization_info=organization_info, organization_users=[organization_user]
        )

    async def _send_task_summary_notifications_for_organization(
        self,
        organization_info: OrganizationInfo,
        organization_users: list[UserDTO],
    ) -> None:
        organization_id = organization_info.organization_id
        profile = organization_info.profile
        logger.bind(organization_id=organization_id, profile=profile).info(
            "Handling summary notifications for organization"
        )

        # For list of notifications sent within the last day by user - used to prevent
        # double sending daily notification
        notifications = (
            await self.user_notification_service.list_by_platform_and_type_sent_after(
                organization_id=organization_id,
                platform=NotificationPlatform.EMAIL,
                notification_type=NotificationType.TASK_DIGEST,
                sent_at_after=zoned_utc_now() - timedelta(hours=23),
            )
        )
        notifications_by_user = defaultdict(list)
        for notification in notifications:
            notifications_by_user[notification.user_id].append(notification)

        notification_time = (
            profile.task_digest_delivery_time
            if profile and profile.task_digest_delivery_time
            else time(hour=_DEFAULT_TASK_SUMMARY_NOTIFICATION_HOUR, minute=0, second=0)
        )
        users_to_notify = []
        # TODO Drop some logging to debug later
        for user in organization_users:
            if user.profile and user.profile.is_task_digest_enabled is False:
                logger.bind(organization_id=organization_id, user_id=user.id).info(
                    "User disabled notification, skipping"
                )

            user_timezone = ZoneInfo(
                user.profile.timezone
                if user.profile and user.profile.timezone
                else _DEFAULT_TASK_SUMMARY_NOTIFICATION_TIMEZONE
            )

            # Notify user if current time is beyond the org configured notification time
            # and they haven't had a notification yet today.
            user_time_now = datetime.now(user_timezone)
            org_notify_time_in_user_timezone = datetime.combine(
                datetime.now(tz=user_timezone).date(), notification_time, user_timezone
            )
            if (
                org_notify_time_in_user_timezone < user_time_now
                and not notifications_by_user[user.id]
            ):
                logger.bind(
                    user_id=user.id,
                    organization_id=organization_id,
                    org_notify_time_in_user_timezone=org_notify_time_in_user_timezone,
                    user_time_now=user_time_now,
                ).info("Adding user to list for notification")
                users_to_notify.append(user)
            else:
                logger.bind(
                    user_id=user.id,
                    organization_id=organization_id,
                    org_notify_time_in_user_timezone=org_notify_time_in_user_timezone,
                    user_time_now=user_time_now,
                    notification_count=len(notifications_by_user[user.id]),
                ).info("Not notification time for user")

        if not users_to_notify:
            logger.bind(organization_id=organization_info.organization_id).info(
                "No users require notifications"
            )
            return

        # TODO: Use task_query_service
        tasks = await self.task_query_service.list_tasks_v2(
            organization_id=organization_id
        )
        tasks_by_user_id = defaultdict(list)
        for task in tasks:
            if task.status not in [TaskStatus.COMPLETED, TaskStatus.CLOSED]:
                tasks_by_user_id[task.owner_user_id].append(task)

        for user in users_to_notify:
            if not tasks_by_user_id[user.id]:
                logger.bind(
                    user_id=user.id, organization_id=organization_info.organization_id
                ).info("User does not require task notification - no open tasks")
                continue

            await self.user_notification_service.send_user_task_summary(
                organization_id=organization_id,
                user=user,
                tasks=tasks_by_user_id[user.id],
            )

    @staticmethod
    def _fields_to_update(patch_request: BasePatchRequest) -> dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        fields_to_update = patch_request.model_dump(exclude_unset=True)
        if fields_to_update:
            if "priority" in fields_to_update:
                fields_to_update["priority"] = fields_to_update["priority"].name
            fields_to_update["updated_at"] = zoned_utc_now()

        return fields_to_update

    async def _send_task_assignment_notification(
        self,
        task_id: UUID,
        organization_id: UUID,
        user_id: UUID,
        patched_task: TaskV2,
        existing_task: TaskV2,
    ) -> None:
        assignee_user = (
            await self.user_service.get_by_id_and_organization_id(
                user_id=patched_task.owner_user_id,
                organization_id=organization_id,
            )
            if patched_task.owner_user_id
            else None
        )
        actor_user = await self.user_service.get_by_id_and_organization_id(
            user_id=user_id,
            organization_id=organization_id,
        )
        if assignee_user and actor_user:
            message_for_actor = f"You assigned task to {assignee_user.display_name}."
            message_for_assignee = f"{actor_user.display_name} assigned task to you."
            await self.notification_service.send_notification(
                send_notification_request=SendNotificationRequest(
                    data=NotificationTaskAssignedData(
                        task_name=patched_task.title,
                        assignee_user_id=str(assignee_user.id),
                        task_page=f"/tasks?taskId={task_id}",
                        message_for_actor=message_for_actor,
                        message_for_assignee=message_for_assignee,
                    ),
                    reference_id=str(task_id),
                    reference_id_type=NotificationReferenceIdType.TASK,
                    activity_id=None,
                    actor_user_id=user_id,
                    recipient_user_ids=[
                        *(
                            [existing_task.owner_user_id]
                            if existing_task.owner_user_id
                            else []
                        ),
                        assignee_user.id,
                    ],
                    idempotency_key=f"task_assigned_{task_id}_{patched_task.updated_at}",
                ),
                organization_id=organization_id,
            )

    async def _update_task_notification_workflow(
        self,
        *,
        task_id: UUID,
        organization_id: UUID,
        due_at: datetime | None,
    ) -> None:
        """Updates the notification workflow for a task.

        This method handles updating task notifications when a task is modified:
        1. Gets the existing workflow and checks if create notification was sent
        2. Terminates the existing workflow
        3. Starts a new workflow with updated parameters

        Args:
            task_id: UUID of the task
            organization_id: UUID of the organization
            due_at: Optional due date for the task
        """
        # Generate workflow ID using task and org IDs
        # workflow_id = TaskNotificationWorkflow.get_workflow_id(task_id, organization_id)
        temporal_client = await get_temporal_client()
        workflow_id = f"task_notification:task:{task_id}:org:{organization_id}"
        has_executed_create_notification = True
        try:
            # Get workflow handle
            workflow_handle = temporal_client.get_workflow_handle(
                workflow_id=workflow_id
            )
            workflow_state = await workflow_handle.describe()

            # Determine if we need to query existing workflow
            if workflow_state.status == WorkflowExecutionStatus.RUNNING:
                # Query if create notification was already sent in existing workflow
                has_executed_create_notification = await workflow_handle.query(
                    "has_executed_create_notification"
                )

            logger.bind(
                task_id=task_id,
                organization_id=organization_id,
            ).info(
                f"Has executed create notification: {has_executed_create_notification},"
                f"existing workflow_state: {workflow_state.status}"
            )
        except temporalio.service.RPCError as e:
            # Ignore if workflow not found
            # # which could happen if task was created before notification feature was enabled
            if e.grpc_status.code == grpc.StatusCode.NOT_FOUND.value[0]:
                logger.bind(task_id=task_id, organization_id=organization_id).info(
                    "TaskNotification workflow not found for task, skipping"
                )
        except Exception as e:
            # Log any errors that occur during workflow update
            logger.bind(
                task_id=task_id,
                organization_id=organization_id,
                due_at=due_at,
            ).error(f"Error updating task notification workflow: {e}", exc_info=e)

        # Start new workflow with updated parameters
        # Pass has_executed_create_notification to avoid duplicate notifications
        await temporal_client.start_workflow(
            TaskNotificationWorkflow.run,
            args=[
                TaskNotificationWorkflowInput(
                    task_id=task_id,
                    due_at_str=str(due_at) if due_at else None,
                    organization_id=organization_id,
                    activity_id=None,
                    exclude_create_notification=has_executed_create_notification,
                ),
            ],
            task_queue=TemporalTaskQueue.NOTIFICATION_TASK_QUEUE,
            id=workflow_id,
            id_reuse_policy=WorkflowIDReusePolicy.TERMINATE_IF_RUNNING,
        )

    async def process_task_account_change(
        self,
        previous_account_id: UUID | None,
        new_account_id: UUID | None,
        organization_id: UUID,
        updated_by_user_id: UUID,
        reference_id: UUID,
        task_reference_type: TaskReferenceIdType,
    ) -> dict[UUID, list[TaskV2]]:
        """Process task account changes when an entity's account changes.

        Args:
            previous_account_id: The previous account ID, None if this is a creation event
            new_account_id: The new account ID, None if the account is being removed
            task_reference_type: The type of task reference (CONTACT_ID or PIPELINE_ID)
            organization_id: The organization ID
            updated_by_user_id: The user ID making the update
            reference_id: The ID of the entity (contact or pipeline) being updated

        Returns:
            Dictionary mapping task IDs to list of affected tasks
        """
        logger.bind(
            previous_account_id=previous_account_id,
            new_account_id=new_account_id,
            organization_id=organization_id,
            reference_id=reference_id,
            task_reference_type=task_reference_type,
        ).info(
            f"Processing task account change upon {task_reference_type} account change"
        )

        if previous_account_id is None:
            logger.bind(
                new_account_id=new_account_id,
                reference_id=reference_id,
                task_reference_type=task_reference_type,
            ).info(f"New {task_reference_type} creation, skipping")
            return {}

        if previous_account_id == new_account_id:
            logger.bind(
                previous_account_id=previous_account_id,
                new_account_id=new_account_id,
                reference_id=reference_id,
                task_reference_type=task_reference_type,
            ).info("Account id is the same, skipping")
            return {}

        tasks: dict[
            UUID, list[TaskV2]
        ] = await self.task_query_service.get_tasks_by_reference_ids_and_type(
            reference_ids=[reference_id],
            reference_id_type=task_reference_type,
            organization_id=organization_id,
        )

        if not tasks:
            logger.bind(
                reference_id=reference_id, task_reference_type=task_reference_type
            ).info(f"No tasks found for {task_reference_type}, skipping")
            return {}

        await self._update_task_account_references_upon_account_change(
            tasks=tasks,
            old_account_id=previous_account_id,
            new_account_id=new_account_id,
            organization_id=organization_id,
            updated_by_user_id=updated_by_user_id,
        )

        return tasks

    async def _update_task_account_references_upon_account_change(
        self,
        tasks: dict[UUID, list[TaskV2]],
        old_account_id: UUID | None,
        new_account_id: UUID | None,
        organization_id: UUID,
        updated_by_user_id: UUID,
    ) -> None:
        """Update task account references when an account changes.

        Args:
            tasks: Dictionary mapping task IDs to list of tasks to update
            old_account_id: The previous account ID
            new_account_id: The new account ID
            organization_id: The organization ID
            updated_by_user_id: The user ID making the update
        """
        for task_id in tasks:
            for task in tasks[task_id]:
                # Delete old account references if they exist
                if old_account_id:
                    deleted_task_references = (
                        await self.task_repository.delete_task_references(
                            task_id=task.id,
                            organization_id=organization_id,
                            reference_id=str(old_account_id),
                            reference_id_type=TaskReferenceIdType.ACCOUNT_ID.name,
                            deleted_by_user_id=updated_by_user_id,
                            deleted_at=zoned_utc_now(),
                        )
                    )

                    logger.bind(
                        task_id=task.id,
                        deleted_task_references=deleted_task_references,
                    ).info("Deleted old account references")

                # Create new account reference
                if new_account_id:
                    inserted_task_reference = await self.task_repository.insert(
                        TaskReference(
                            id=uuid4(),
                            task_id=task.id,
                            reference_id=str(new_account_id),
                            reference_id_type=TaskReferenceIdType.ACCOUNT_ID.name,
                            created_at=zoned_utc_now(),
                            created_by_user_id=updated_by_user_id,
                            organization_id=organization_id,
                        )
                    )

                    logger.bind(
                        task_id=task.id,
                        inserted_task_reference=inserted_task_reference,
                    ).info("Inserted new account reference")


class SingletonTaskV2Service(Singleton, TaskV2Service):
    pass


def get_task_v2_service_general(db_engine: DatabaseEngine) -> TaskV2Service:
    return SingletonTaskV2Service(
        task_repository=TaskRepository(engine=db_engine),
        custom_object_service=get_custom_object_service(db_engine=db_engine),
        task_query_service=get_task_query_service_from_engine(db_engine=db_engine),
        task_template_repository=TaskTemplateRepository(engine=db_engine),
        organization_info_repository=OrganizationInfoRepository(engine=db_engine),
        user_notification_service=get_user_notification_service_by_db_engine(
            db_engine=db_engine
        ),
        user_service=get_user_service_general(db_engine=db_engine),
        notification_service=get_notification_service_by_db_engine(db_engine),
        contact_query_service=get_contact_query_service(db_engine=db_engine),
        account_query_service=get_account_query_service(
            db_engine=db_engine,
        ),
        activity_service=get_activity_service_general(db_engine=db_engine),
        crm_sync_push_service=get_crm_sync_push_service(db_engine=db_engine),
        domain_crm_association_service=get_domain_crm_association_service(
            db_engine=db_engine
        ),
        feature_flag_service=get_feature_flag_service(),
    )


def get_task_v2_service(request: Request) -> TaskV2Service:
    return get_task_v2_service_general(db_engine=get_db_engine(request))


async def _send_task_notification(
    task_id: UUID,
    organization_id: UUID,
    due_at: datetime | None,
) -> None:
    client = await get_temporal_client()
    await client.start_workflow(
        TaskNotificationWorkflow.run,
        args=[
            TaskNotificationWorkflowInput(
                task_id=task_id,
                due_at_str=str(due_at) if due_at else None,
                organization_id=organization_id,
                activity_id=None,
                exclude_create_notification=False,
            )
        ],
        task_queue=TemporalTaskQueue.NOTIFICATION_TASK_QUEUE,
        id=TaskNotificationWorkflow.get_workflow_id(task_id, organization_id),
    )
