from collections.abc import Mapping
from typing import Annotated, Literal
from uuid import UUID

from fastapi import Depends, Request

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.results import Cursor
from salestech_be.core.common.domain_service import DomainQueryService
from salestech_be.core.metadata.repository.select_list_repository import (
    SelectListRepository,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.task.giant_task_types import GiantTask
from salestech_be.db.dao.task_repository import TaskRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.select_list import SelectListValueIdUnion
from salestech_be.db.models.task import TaskPriority, TaskStatus, TaskType
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime

logger = get_logger(__name__)


class GiantTaskQueryService(DomainQueryService[GiantTask]):
    def __init__(
        self,
        task_repository: Annotated[TaskRepository, Depends()],
        internal_select_list_service: Annotated[InternalSelectListService, Depends()],
    ):
        self.task_repository = task_repository
        self.internal_select_list_service = internal_select_list_service

    async def list_giant_tasks(
        self,
        organization_id: UUID,
        only_include_task_ids: set[UUID] | None = None,
        owner_user_id: UUID | None = None,
        owner_user_ids_in: list[UUID] | None = None,
        status_ne: str | None = None,
        due_at_gt: ZoneRequiredDateTime | None = None,
        due_at_lt: ZoneRequiredDateTime | None = None,
        due_at_blank: bool | None = None,
        sorting_field: Literal["due_at", "created_at", "updated_at"] | None = None,
        sorting_direction: Literal["asc", "desc"] | None = None,
        null_first: bool = False,
        account_id: UUID | None = None,
        account_ids_in: list[UUID] | None = None,
        contact_ids: list[UUID] | None = None,
        pipeline_id: UUID | None = None,
        pipeline_ids_in: list[UUID] | None = None,
        require_pipeline_id: bool = False,
        pipeline_stage_select_list_value_ids_in: list[UUID] | None = None,
        meeting_id: UUID | None = None,
        email_thread_ids: list[UUID] | None = None,
        sequence_id: UUID | None = None,
        sequence_step_id_in: list[UUID] | None = None,
        cursor: Cursor | None = None,
        priority_in: list[TaskPriority] | None = None,
        type_in: list[TaskType] | None = None,
        status_in: list[TaskStatus] | None = None,
    ) -> tuple[list[GiantTask], Cursor | None]:
        only_include_task_ids_list = (
            list(only_include_task_ids) if only_include_task_ids else None
        )

        logger.bind(
            organization_id=organization_id,
            only_include_task_ids_list=only_include_task_ids_list,
            owner_user_id=owner_user_id,
            owner_user_ids_in=owner_user_ids_in,
            status_ne=status_ne,
            due_at_gt=due_at_gt,
            due_at_lt=due_at_lt,
            sorting_field=sorting_field,
            sorting_direction=sorting_direction,
            null_first=null_first,
            account_id=account_id,
            account_ids_in=account_ids_in,
            contact_ids=contact_ids,
            pipeline_id=pipeline_id,
            pipeline_ids_in=pipeline_ids_in,
            require_pipeline_id=require_pipeline_id,
            pipeline_stage_select_list_value_ids_in=pipeline_stage_select_list_value_ids_in,
            meeting_id=meeting_id,
            email_thread_ids=email_thread_ids,
            sequence_id=sequence_id,
            sequence_step_id_in=sequence_step_id_in,
            priority_in=priority_in,
            type_in=type_in,
            status_in=status_in,
        ).info("Listing giant tasks")

        # pipeline_stage_id is what we want to filter on. however, this may be a recursively mapped value.
        pipeline_stage_select_list_values_ids_in_expanded: list[UUID] = []
        pipeline_stage_select_list_value_id_union_map: (
            Mapping[UUID, SelectListValueIdUnion] | None
        ) = None
        if (
            pipeline_stage_select_list_value_ids_in
            and len(pipeline_stage_select_list_value_ids_in) > 0
        ):
            # gets a mappping of SelectListDto objects, each containing a select list and all its values.
            pipeline_stage_select_list_value_id_union_map = await self.internal_select_list_service.find_select_list_value_unions_for_value_ids(
                organization_id=organization_id,
                select_list_value_ids=set(pipeline_stage_select_list_value_ids_in),
            )
            # we need to extract the equivalent value ids (any potential ancestry) for each select list value id.
            for slv_id_union in pipeline_stage_select_list_value_id_union_map.values():
                pipeline_stage_select_list_values_ids_in_expanded.extend(
                    list(slv_id_union.id_union)
                )

        db_giant_tasks = await self.task_repository.list_giant_tasks(
            organization_id=organization_id,
            only_include_task_ids=only_include_task_ids_list,
            owner_user_id=owner_user_id,
            owner_user_ids_in=owner_user_ids_in,
            status_ne=status_ne,
            due_at_gt=due_at_gt,
            due_at_lt=due_at_lt,
            due_at_blank=due_at_blank,
            sorting_field=sorting_field,
            sorting_direction=sorting_direction,
            null_first=null_first,
            cursor=cursor,
            account_id=account_id,
            account_ids_in=account_ids_in,
            contact_ids=contact_ids,
            pipeline_id=pipeline_id,
            pipeline_ids_in=pipeline_ids_in,
            require_pipeline_id=require_pipeline_id,
            pipeline_stage_select_list_value_ids_in=pipeline_stage_select_list_values_ids_in_expanded,
            meeting_id=meeting_id,
            email_thread_ids=email_thread_ids,
            sequence_id=sequence_id,
            sequence_step_id_in=sequence_step_id_in,
            priority_in=priority_in,
            type_in=type_in,
            status_in=status_in,
        )

        # Rewrite the result set to get the effective select-list value.
        if (
            pipeline_stage_select_list_value_ids_in
            and len(pipeline_stage_select_list_value_ids_in) > 0
            and pipeline_stage_select_list_value_id_union_map
        ):
            for index, task in enumerate(db_giant_tasks):
                if not hasattr(task, "pipeline_stage_id"):
                    continue
                if not task.pipeline_stage_id:
                    continue
                stage_id: UUID = task.pipeline_stage_id
                if not (
                    psslv_id_union := pipeline_stage_select_list_value_id_union_map.get(
                        stage_id
                    )
                ):
                    continue
                db_giant_tasks[index] = task.model_copy(
                    update={"pipeline_stage_id": psslv_id_union.effective_slv_id}
                )

        new_cursor = None
        if cursor:
            has_more: bool = len(db_giant_tasks) > cursor.page_size
            new_cursor = Cursor(
                page_size=cursor.page_size,
                page_index=cursor.page_index,
                has_more=has_more,
            )
            db_giant_tasks = db_giant_tasks[0 : cursor.page_size]

        return [
            GiantTask.from_db_giant_task(db_giant_task)
            for db_giant_task in db_giant_tasks
        ], new_cursor


def get_giant_task_query_service_from_engine(
    db_engine: DatabaseEngine,
) -> GiantTaskQueryService:
    return GiantTaskQueryService(
        task_repository=TaskRepository(engine=db_engine),
        internal_select_list_service=InternalSelectListService(
            select_list_repository=SelectListRepository(engine=db_engine)
        ),
    )


def get_giant_task_query_service(request: Request) -> GiantTaskQueryService:
    return get_giant_task_query_service_from_engine(db_engine=get_db_engine(request))
