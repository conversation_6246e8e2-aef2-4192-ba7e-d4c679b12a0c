import uuid
from datetime import datetime
from typing import Annotated
from uuid import UUID

from fastapi import Depends
from pytz import UTC
from temporalio.client import Client

from salestech_be.common.exception.exception import (
    ConflictResourceError,
    ForbiddenError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.imports.models.import_job import (
    ImportCsvJobStatus,
    ImportJob,
    ImportMetadata,
)
from salestech_be.core.imports.service.import_job_service import ImportJobService
from salestech_be.core.organization.service.organization_service import (
    OrganizationService,
    get_organization_service_general,
)
from salestech_be.db.dao.import_csv_job_review_repository import (
    ImportCsvJobReviewRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.import_csv_job_review_db import (
    ImportCsvJobReviewDb,
    ImportCsvJobReviewExtraInfoDb,
    ImportCsvJobReviewStatusDb,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.ree_logging import get_logger
from salestech_be.util.validation import strip_str_or_none
from salestech_be.web.api.organization.schema import (
    OrganizationResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger()


class ImportCsvJobReviewService:
    def __init__(
        self,
        import_csv_job_review_repository: ImportCsvJobReviewRepository,
        import_job_service: ImportJobService,
        organization_service: OrganizationService,
    ) -> None:
        self.icjr_repository = import_csv_job_review_repository
        self.import_job_service = import_job_service
        self.organization_service = organization_service

    async def create_job_review(
        self,
        organization_id: UUID,
        submitter_user_id: UUID,
        import_metadata: ImportMetadata,
    ) -> ImportCsvJobReviewDb:
        # Create draft job first
        import_csv_job_draft = await self.import_job_service.create_job_draft(
            organization_id=organization_id,
            user_id=submitter_user_id,
            import_metadata=import_metadata,
        )

        now = datetime.now(UTC)
        # Create job_review with the draft job
        job_review = ImportCsvJobReviewDb(
            id=uuid.uuid4(),
            organization_id=organization_id,
            display_name=import_metadata.display_name,
            submitter_user_id=submitter_user_id,
            import_csv_job_id=import_csv_job_draft.id,
            reviewer_user_id=None,
            reviewer_notes=None,
            status=ImportCsvJobReviewStatusDb.PENDING_REVIEW,
            status_changed_at=now,
            created_at=now,
            updated_at=now,
            archived_at=None,
        )
        await self.icjr_repository.insert(job_review)

        # TODO: Notify reviewer through slack (if needed later)

        return job_review

    async def set_pending_review(
        self,
        organization_id: UUID,
        job_review_id: UUID,
        submitter_user_id: UUID,
        import_metadata: ImportMetadata,
    ) -> ImportCsvJobReviewDb:
        """
        Making this the catchall function, lenient on setting the status to PENDING_REVIEW.
        """
        existing_job_review = await self.get_job_review(
            job_review_id, include_is_archived=True
        )
        existing_import_job = None
        if existing_job_review.import_csv_job_id:
            existing_import_job = await self.import_job_service.get_job(
                existing_job_review.import_csv_job_id,
                existing_job_review.organization_id,
            )

        create_new_import_job = False
        if (
            existing_import_job
            and existing_import_job.status == ImportCsvJobStatus.DRAFT
        ):
            # Everything as expected.
            pass
        elif existing_import_job is None:
            logger.bind(
                organization_id=organization_id,
                job_review_id=job_review_id,
                import_csv_job_id=str(existing_job_review.import_csv_job_id),
            ).info("Does not exist, creating new import job")
            create_new_import_job = True
        elif existing_import_job.status == ImportCsvJobStatus.STARTED:
            logger.bind(
                organization_id=organization_id,
                job_review_id=job_review_id,
                import_csv_job_id=str(existing_job_review.import_csv_job_id),
            ).error(
                "Import job already running. Please wait until it's completed, canceled, or timed out"
            )
            raise ConflictResourceError(
                f"Import job({existing_job_review.import_csv_job_id}) of job_review({job_review_id}) is already running. "
                "Wait until it's completed, canceled, or timed out."
            )
        else:
            logger.bind(
                organization_id=organization_id,
                job_review_id=job_review_id,
                import_csv_job_id=str(existing_job_review.import_csv_job_id),
            ).info(
                f"Import job status is {existing_import_job.status}. Creating new import job"
            )
            create_new_import_job = True

        if create_new_import_job:
            existing_import_job = await self.import_job_service.create_job_draft(
                organization_id=organization_id,
                user_id=submitter_user_id,
                import_metadata=import_metadata,
            )

        if existing_import_job:
            now = datetime.now(UTC)
            to_return = await self.icjr_repository.update_by_tenanted_primary_key(
                table_model=ImportCsvJobReviewDb,
                organization_id=organization_id,
                primary_key_to_value={"id": job_review_id},
                column_to_update={
                    "submitter_user_id": submitter_user_id,
                    "import_csv_job_id": existing_import_job.id,
                    "status": ImportCsvJobReviewStatusDb.PENDING_REVIEW,
                    "status_changed_at": now,
                    "reviewer_user_id": None,
                    "reviewer_notes": None,
                    "updated_at": now,
                    "archived_at": None,
                },
            )

            if to_return:
                return to_return
            else:
                raise ValueError(f"Failed to update job_review({job_review_id})")
        else:
            raise ValueError(
                f"No corresponding import job for job_review({job_review_id})"
            )

    async def set_approve_job_review(
        self,
        organization_id: UUID,
        job_review_id: UUID,
        reviewer_user_id: UUID,
    ) -> ImportCsvJobReviewDb:
        job_review = await self.get_job_review(job_review_id)

        if job_review.status != ImportCsvJobReviewStatusDb.PENDING_REVIEW:
            raise InvalidArgumentError(
                f"Cannot approve job_review in {job_review.status} status"
            )

        if job_review.import_csv_job_id:
            await self.import_job_service.process_draft_file_import(
                organization_id=organization_id,
                csv_import_job_id=job_review.import_csv_job_id,
            )

        now = datetime.now(UTC)
        to_return = await self.icjr_repository.update_by_tenanted_primary_key(
            table_model=ImportCsvJobReviewDb,
            organization_id=organization_id,
            primary_key_to_value={"id": job_review_id},
            column_to_update={
                "status": ImportCsvJobReviewStatusDb.APPROVED,
                "status_changed_at": now,
                "reviewer_user_id": reviewer_user_id,
                "updated_at": now,
            },
        )
        if to_return:
            return to_return
        else:
            raise ValueError(f"Failed to update approve job_review({job_review_id})")

    async def set_reject_job_review(
        self,
        organization_id: UUID,
        job_review_id: UUID,
        reviewer_user_id: UUID,
        reviewer_notes: str | None = None,
    ) -> ImportCsvJobReviewDb:
        job_review = await self.get_job_review(job_review_id)

        if job_review.status != ImportCsvJobReviewStatusDb.PENDING_REVIEW:
            raise InvalidArgumentError(
                f"Cannot reject import_csv_job_review({job_review_id}) in {job_review.status} status. Must be PENDING_REVIEW."
            )

        final_reviewer_notes = strip_str_or_none(reviewer_notes)
        if not final_reviewer_notes:
            final_reviewer_notes = strip_str_or_none(job_review.reviewer_notes)
            if not final_reviewer_notes:
                raise InvalidArgumentError(
                    f"Cannot reject import_csv_job_review({job_review_id}) with no reviewer notes."
                )

        now = datetime.now(UTC)
        to_return = await self.icjr_repository.update_by_tenanted_primary_key(
            table_model=ImportCsvJobReviewDb,
            organization_id=organization_id,
            primary_key_to_value={"id": job_review_id},
            column_to_update={
                "status": ImportCsvJobReviewStatusDb.REJECTED,
                "status_changed_at": now,
                "reviewer_user_id": reviewer_user_id,
                "reviewer_notes": final_reviewer_notes,
                "updated_at": now,
            },
        )
        if to_return:
            return to_return
        else:
            raise ValueError(f"Failed to update reject job_review({job_review_id})")

    async def list_job_reviews(
        self,
        organization_id: UUID | None,
        statuses: list[ImportCsvJobReviewStatusDb] | None,
        limit: int,
        offset: int,
    ) -> list[ImportCsvJobReviewExtraInfoDb]:
        return await self.icjr_repository.list_job_reviews(
            include_organization_id=organization_id,
            include_statuses=statuses,
            limit=limit,
            offset=offset,
        )

    async def delete_job_review(
        self,
        organization_id: UUID,
        job_review_id: UUID,
    ) -> ImportCsvJobReviewDb:
        to_return = await self.icjr_repository.update_by_tenanted_primary_key(
            table_model=ImportCsvJobReviewDb,
            organization_id=organization_id,
            primary_key_to_value={"id": job_review_id},
            column_to_update={"archived_at": datetime.now(UTC)},
        )
        if to_return:
            return to_return
        else:
            raise ValueError(f"Failed to delete job_review({job_review_id})")

    # Allow breaking of tenancy because super-admin might get a job from a different org.
    #   Would be awkward for HTTP GET requests to sepcify a job_organization_id.
    #   Above _should_ be done, but this is a quick fix for now.
    async def get_job_review(
        self,
        job_review_id: UUID,
        include_is_archived: bool = False,
    ) -> ImportCsvJobReviewDb:
        to_return = await self.icjr_repository.find_by_primary_key(
            ImportCsvJobReviewDb,
            id=job_review_id,
            exclude_deleted_or_archived=not include_is_archived,
        )
        if to_return:
            return to_return
        else:
            raise ResourceNotFoundError(f"Job review {job_review_id} not found")

    async def get_job_review_detail(
        self,
        job_review_id: UUID,
        organization_id_to_enforce: UUID | None,
    ) -> tuple[ImportCsvJobReviewDb, ImportJob | None, OrganizationResponse | None]:
        job_review = await self.get_job_review(job_review_id)
        if (
            organization_id_to_enforce is not None
            and job_review.organization_id != organization_id_to_enforce
        ):
            raise ForbiddenError(
                f"Not allowed operation on organization({job_review.organization_id})"
                f" from organization({organization_id_to_enforce})"
            )
        import_job: ImportJob | None = None
        if job_review.import_csv_job_id:
            import_job = await self.import_job_service.get_job(
                job_review.import_csv_job_id, job_review.organization_id
            )
            if (
                import_job
                and import_job.metadata
                and import_job.metadata.file_id
                and import_job.metadata.original_filename is None
            ):
                import_job.metadata.original_filename = (
                    await self.import_job_service.file_service.get_original_filename(
                        file_id=import_job.metadata.file_id,
                        organization_id=job_review.organization_id,
                    )
                )
        organization = None
        if job_review.organization_id:
            organization = await self.organization_service.get_organization_by_id(
                job_review.organization_id
            )
        return job_review, import_job, organization

    async def set_job_review_notes(
        self,
        organization_id: UUID,
        job_review_id: UUID,
        reviewer_user_id: UUID,
        reviewer_notes: str | None,
    ) -> ImportCsvJobReviewDb:
        to_return = await self.icjr_repository.update_by_tenanted_primary_key(
            table_model=ImportCsvJobReviewDb,
            organization_id=organization_id,
            primary_key_to_value={"id": job_review_id},
            column_to_update={
                "reviewer_user_id": reviewer_user_id,
                "reviewer_notes": reviewer_notes,
                "updated_at": datetime.now(UTC),
            },
        )
        if to_return:
            return to_return
        else:
            raise ValueError(
                f"Failed to update review_notes forjob_review({job_review_id})"
            )


class SingletonImportCsvJobReviewService(Singleton, ImportCsvJobReviewService):
    pass


def get_import_csv_job_review_service(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
    temporal_client: Annotated[Client, Depends(get_temporal_client)],
) -> ImportCsvJobReviewService:
    # Import moved here to avoid circular dependency
    from salestech_be.core.imports.service.import_job_service import (
        get_import_job_service,
    )

    if SingletonImportCsvJobReviewService.has_instance():
        return SingletonImportCsvJobReviewService.get_singleton_instance()
    return SingletonImportCsvJobReviewService(
        import_csv_job_review_repository=ImportCsvJobReviewRepository(engine=db_engine),
        import_job_service=get_import_job_service(
            db_engine=db_engine,
            temporal_client=temporal_client,
        ),
        organization_service=get_organization_service_general(db_engine=db_engine),
    )
