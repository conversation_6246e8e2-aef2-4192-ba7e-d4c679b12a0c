import csv
import io
import json
import re
import uuid
from collections import defaultdict
from collections.abc import Mapping
from datetime import datetime, timedelta
from decimal import Decimal
from typing import (
    Annotated,
    Any,
)

import pytz
from botocore.exceptions import ClientError
from fastapi import Depends
from pydantic import BaseModel, NonNegativeInt, TypeAdapter, ValidationError
from pydantic_extra_types.timezone_name import TimeZoneName
from starlette.requests import Request
from temporalio import activity

from salestech_be.common.core_crm.contact_pipeline_role import ContactPipelineRoleType
from salestech_be.common.exception import (
    ConflictResourceError,
    ResourceNotFoundError,
)
from salestech_be.common.exception.exception import (
    ImportEntityConflictError,
    ImportEntityDependencyNotFoundError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
    StdSelectListIdentifier,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    AccountRelationship,
    ContactAccountRoleRelationship,
    ContactPipelineRoleRelationship,
    ContactRelationship,
    CustomRecordRelationship,
    PipelineRelationship,
)
from salestech_be.common.stats.metric import custom_metric
from salestech_be.common.type.formatted_string import strip_and_lower
from salestech_be.common.type.metadata.common import (
    CustomObjectIdentifier,
    ObjectKind,
    StandardObjectIdentifier,
    object_id_or_name,
)
from salestech_be.common.type.metadata.common import (
    ObjectIdentifier as ObjectIdentifierCommon,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    SingleSelectFieldProperty,
    TimestampFieldProperty,
)
from salestech_be.common.type.metadata.field.field_value import (
    FieldValueOrAny,
    TimestampFieldValue,
)
from salestech_be.common.type.numbers import NonNegativeDecimal
from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.core.account.service.account_service import (
    AccountService,
    get_account_service,
)
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import (
    AccountV2,
    PatchAccountRequest,
)
from salestech_be.core.auth.service import (
    UserAuthService,
    get_user_auth_service,
    get_user_auth_service_with_engine,
)
from salestech_be.core.common.types import Address, UserAuthContext
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.contact.service.contact_service import (
    ContactService,
    get_contact_service,
)
from salestech_be.core.contact.service_api_schema import (
    PatchContactRequest,
    UpsertContactAccountRoleRequest,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.crm_sync.hubspot.hubspot_service import (
    HubSpotService,
    get_hubspot_service_by_db_engine,
)
from salestech_be.core.custom_object.service.association_service import (
    AssociationService,
    get_association_service,
)
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
    get_custom_object_service,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.domain_object_list.service.domain_object_list_service import (
    DomainObjectListService,
    get_domain_object_list_service,
    get_domain_object_list_service_by_request,
)
from salestech_be.core.domain_object_list.types import (
    DomainObjectList,
    DomainObjectListItemType,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.files.service.file_service import (
    FileService,
    S3BucketType,
    file_service_from_engine,
    get_file_service,
)
from salestech_be.core.files.type.models import CreatePresignedUrlRequest
from salestech_be.core.imports.models.import_csv_fields import (
    CSV_TYPE,
    CUSTOM_FIELD_DATA,
    AccountImportCsvFields,
    ContactAccountRoleImportCsvFields,
    ContactImportCsvFields,
    ContactOpportunityRoleImportCsvFields,
    CustomObjectImportCsvFields,
    OpportunityImportCsvFields,
)
from salestech_be.core.imports.models.import_job import (
    AssociationLabelMapping,
    ImportJob,
    ImportResultMetadata,
    ObjectImportMode,
    ObjectMapping,
)
from salestech_be.core.imports.repository.import_job_repository import (
    ImportJobRepository,
)
from salestech_be.core.imports.types import (
    ImportAccountRecord,
    ImportContactRecord,
    ImportMetricsName,
    ImportPipelineRecord,
    ImportRecord,
    PipelineStageRequest,
)
from salestech_be.core.job.models import JobStatus, JobType
from salestech_be.core.meeting.meeting_bot_service import MEDIA_FILE_NAME
from salestech_be.core.meeting.meeting_service import (
    MeetingService,
    meeting_service_factory,
    meeting_service_factory_general,
)
from salestech_be.core.metadata.dto.select_list_dto import (
    PipelineStageDto,
)
from salestech_be.core.metadata.dto.service_api_schema import (
    PipelineStageSelectListCreateRequest,
    PipelineStageSelectListValueCreateRequest,
    SelectListValueCreateRequest,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
    get_select_list_service,
)
from salestech_be.core.metadata.service.pipeline_stage_service import (
    PipelineStageSelectListService,
    get_pipeline_stage_select_list_service,
)
from salestech_be.core.metadata.types import (
    PipelineStageSelectListDetails,
)
from salestech_be.core.pipeline.service.pipeline_service import (
    PipelineService,
    get_pipeline_service,
)
from salestech_be.core.pipeline.service_api_schema import (
    ContactPipelineAssociationRequest,
    CreatePipelineRequest,
    FullContactPipelineAssociationRequests,
    InternalPatchPipelineRequest,
    UpsertContactPipelineAssociationRequests,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.task.service.task_v2_service import (
    TaskV2Service,
    get_task_v2_service,
    get_task_v2_service_general,
)
from salestech_be.db.dao.crm_sync_repository import CRMSyncRepository
from salestech_be.db.dao.import_repository import ImportRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.custom_object_data_dto import CustomObjectDataDto
from salestech_be.db.dto.custom_object_dto import CustomObjectDto
from salestech_be.db.dto.email_attchment_dto import AttachmentDto
from salestech_be.db.models.account import Account as DbAccount
from salestech_be.db.models.account import AccountStatus
from salestech_be.db.models.address import (
    AddressCreateRequest,
    AddressNoFieldDefinedError,
)
from salestech_be.db.models.contact import (
    CreateContactAccountRoleRequest,
    CreateContactRequest,
    CreateDbContactEmailAccountAssociationRequest,
    CreateDbContactEmailRequest,
    CreateDbContactRequest,
)
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.db.models.custom_field import CustomField
from salestech_be.db.models.custom_object_association import CustomObjectAssociation
from salestech_be.db.models.domain_object_list import DomainObjectListOrigin
from salestech_be.db.models.import_record import (
    ImportEntityType,
    ImportRecordStatus,
)
from salestech_be.db.models.import_record import (
    ImportRecord as DbImportRecord,
)
from salestech_be.db.models.job import Job
from salestech_be.db.models.organization_external_sync import (
    CrmProvider,
    OrganizationExternalSync,
    OrganizationExternalSyncSetting,
    OrganizationExternalSyncStatus,
    SyncMode,
)
from salestech_be.db.models.task import (
    TaskPriority,
    TaskSourceType,
    TaskStatus,
    TaskType,
)
from salestech_be.db.models.user import UserRole
from salestech_be.db.models.user_organization_association import (
    UserOrganizationAssociationStatus,
)
from salestech_be.integrations.hubspot.model import (
    HubSpotDeal,
    HubSpotListBaseRequest,
    HubSpotListCompanyRequest,
    HubSpotListContactRequest,
    HubSpotListDealRequest,
    HubSpotListTaskRequest,
)
from salestech_be.integrations.hubspot.types import HubSpotApiEntityType
from salestech_be.integrations.s3.s3_bucket_manager import (
    S3BucketManager,
    S3BucketManagerException,
)
from salestech_be.integrations.slack.slack_client import (
    SlackChannels,
    SlackClient,
    SlackWebhookUrls,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.str import (
    SchemeOptionalHttpUrlStr,
    validate_domain_name,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import (
    cast_datetime_or_none,
    cast_decimal_or_none,
    cast_int_or_none,
    cast_str_or_none,
    cast_uuid_or_none,
    eq_str_value,
    none_or_split,
    not_none,
    one_row_or_none,
    strip_str_or_none,
    uuid_or_error,
)
from salestech_be.web.api.domain_object_list.schema import (
    AddItemsRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CreateDomainObjectListRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.meeting.schema import (
    ImportMeetingFromExternalRecordingRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.task.schema import (
    CreateTaskRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger()


class CsvRow:
    """Encapsulates CSV row data with both raw dictionary and typed representation."""

    def __init__(
        self,
        src_line_num: int | None,  # Line number in the original CSV file
        all_entities: list[BaseModel] | None,
        all_relationship_ids: list[str] | None,
    ):
        # Validate that we have either raw data or typed data
        num_input_entries = 0
        if all_entities is not None:
            num_input_entries += 1

        if num_input_entries != 1:
            raise ValueError(
                "Exactly one input entry must be provided: raw_record, typed_record, or all_entities"
            )
        self.src_line_num = src_line_num
        self.all_entities = all_entities
        self.all_relationship_ids = all_relationship_ids


class HeartbeatResume:
    heartbeat_resume_state: int
    # We cannot send heartbeats too often.  Heartbeats are sent once and cleared every minute (60s!!) by
    # the temporal client.  The queue is only 1000 large, and raises Exception when surpassed.
    # Do not fill up that queue.
    # https://github.com/temporalio/sdk-python/blob/b0aae9bf23a635c86528c9483a9dc306ebf8d961/temporalio/worker/_activity.py#L168
    # https://github.com/temporalio/sdk-python/blob/b0aae9bf23a635c86528c9483a9dc306ebf8d961/temporalio/worker/_worker.py#L505-L507
    last_heartbeat_time: datetime

    def __init__(
        self,
        heartbeat_resume_state: int,
        last_heartbeat_time: datetime,
    ):
        self.heartbeat_resume_state = heartbeat_resume_state
        self.last_heartbeat_time = last_heartbeat_time

    def update_resume(self, resume_state: int) -> None:
        """
        Args:
            resume_state
                -1 is initial state, only when reevo objects created is a row "processed".
                postivive integer indicates the csv row number actually processed and reevo entity stored in the DB
        """
        self.heartbeat_resume_state = resume_state


class LineContinuation:
    def __init__(
        self,
        prev_lines: str,
        is_quote_open: bool,
        start_line_number: int,
        end_line_number: int,
    ):
        self.prev_lines = prev_lines
        self.is_quote_open = is_quote_open
        self.start_line_number = start_line_number
        self.end_line_number = end_line_number


def _get_except_msg(e: Exception) -> str:
    """Intended for catchall exceptions of unknown type"""
    return f"Error:({type(e).__name__}, {e})"


"""
Abstraction layer to potentially handle more CRM providers
for pull sync (from the external CRM) operations.
"""


class CrmSyncService:
    def __init__(
        self,
        import_repository: Annotated[ImportRepository, Depends()],
        contact_service: Annotated[ContactService, Depends(get_contact_service)],
        contact_query_service: Annotated[
            ContactQueryService, Depends(get_contact_query_service)
        ],
        custom_object_service: Annotated[CustomObjectService, Depends()],
        association_service: Annotated[AssociationService, Depends()],
        account_service: Annotated[AccountService, Depends()],
        pipeline_service: Annotated[PipelineService, Depends()],
        hubspot_service: Annotated[
            HubSpotService, Depends(get_hubspot_service_by_db_engine)
        ],
        pipeline_stage_service: Annotated[PipelineStageSelectListService, Depends()],
        user_auth_service: Annotated[UserAuthService, Depends(get_user_auth_service)],
        task_v2_service: Annotated[TaskV2Service, Depends(get_task_v2_service)],
        meeting_service: Annotated[MeetingService, Depends(meeting_service_factory)],
        file_service: Annotated[FileService, Depends(get_file_service)],
        domain_object_list_service: Annotated[
            DomainObjectListService, Depends(get_domain_object_list_service_by_request)
        ],
        feature_flag_service: Annotated[
            FeatureFlagService, Depends(get_feature_flag_service)
        ],
        crm_sync_repository: Annotated[CRMSyncRepository, Depends()],
        import_job_repository: Annotated[ImportJobRepository, Depends()],
        select_list_service: Annotated[InternalSelectListService, Depends()],
    ):
        super().__init__()
        self.import_repository = import_repository
        self.contact_service = contact_service
        self.contact_query_service = contact_query_service
        self.custom_object_service = custom_object_service
        self.association_service = association_service
        self.pipeline_service = pipeline_service
        self.pipeline_stage_service = pipeline_stage_service
        self.account_service = account_service
        self.task_v2_service = task_v2_service
        self.hubspot_service = hubspot_service
        self.user_auth_service = user_auth_service
        self.meeting_service = meeting_service
        self.file_service = file_service
        self.domain_object_list_service = domain_object_list_service
        self.select_list_service = select_list_service
        self.feature_flag_service = feature_flag_service
        self.crm_sync_repository = crm_sync_repository
        self.import_job_repository = import_job_repository
        self.supported_job_to_csv_fields_map: dict[JobType, type[BaseModel]] = {
            JobType.ACCOUNT_CSV_UPLOAD: AccountImportCsvFields,
            JobType.CONTACT_CSV_UPLOAD: ContactImportCsvFields,
            JobType.PIPELINE_CSV_UPLOAD: OpportunityImportCsvFields,
            JobType.CUSTOM_OBJECT_CSV_UPLOAD: CustomObjectImportCsvFields,
        }
        self.logger = get_logger()
        self.max_qualified_field_path_depth = 10
        self.slack_client = SlackClient(webhook_url=SlackWebhookUrls.CRM_IMPORT_STATUS)
        self.warn_csv_line_continuation_length = 20
        self.last_heartbeat_time: datetime = datetime.fromtimestamp(0, tz=pytz.utc)
        self.debug_skipped_heartbeat_count = 0

    async def process_meeting_import_row(
        self,
        user_id: uuid.UUID,
        organization_id: uuid.UUID,
        field_values_map: dict[str, str],
    ) -> ImportRecord:
        job = Job(
            id=uuid.uuid4(),
            user_id=user_id,
            organization_id=organization_id,  # only first 3 fields are actually used in _process_meeting_import_row()
            type=JobType.MEETING_CSV_UPLOAD,
            status=JobStatus.RUNNING,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            metadata={},
        )

        db_import_record = await self._process_meeting_import_row(
            job=job, mapped_fields=field_values_map, owner_user_id=user_id
        )
        return ImportRecord.map_from_db(db_import_record)

    async def _process_meeting_import_row(  # type: ignore[explicit-any] # TODO: fix-any-annotation  # noqa: PLR0915
        self,
        job: Job,
        mapped_fields: dict[str, Any],
        reference_id: str | None = None,
        owner_user_id: uuid.UUID | None = None,
    ) -> DbImportRecord:
        """
        Process a single meeting import row by calling import_meeting_from_external_recording.

        Args:
            job: The import job
            mapped_fields: Dictionary containing the mapped field values from the import file
            reference_id: Optional reference ID for the import record
            owner_user_id: Optional owner user ID

        Returns:
            ImportRecord: The record of the import attempt
        """

        status = ImportRecordStatus.SUCCESS
        status_detail = None
        entity_id = None
        existing_record = None
        conflicting_record = None

        try:
            file_name = S3BucketManager.convert_title_and_date_to_s3_file_name(
                title=mapped_fields["title"],
                date=mapped_fields["date_of_meeting"],
            )

            # Step 1: Upload video file to S3
            try:
                attachment_dto: AttachmentDto = (
                    await self.file_service.upload_file_from_url(
                        user_id=job.user_id,
                        organization_id=job.organization_id,
                        url=mapped_fields["media_url"],
                        file_name=file_name,
                        bucket_type=S3BucketType.MEETING,
                        content_type="video/mp4",
                    )
                )
                file = attachment_dto.attachment
                duration_seconds: int = (
                    self.file_service.get_video_duration_seconds_from_gdrive_url(
                        mapped_fields["media_url"]
                    )
                )
            except S3BucketManagerException as e:
                status = ImportRecordStatus.FAILED
                status_detail = e.message
                entity_id = None
                self.logger.opt(exception=e).exception(
                    "Failed to upload video",
                    extra={
                        "error_message": e.message,
                        "error_code": e.code,
                        "exc_info": e,
                    },
                )
                return DbImportRecord(
                    id=uuid.uuid4(),
                    import_entity_type=ImportEntityType.MEETING,
                    organization_id=job.organization_id,
                    created_at=datetime.now(pytz.utc),
                    job_id=job.id,
                    status=status,
                    status_detail=status_detail,
                    entity_id=entity_id,
                    existing_record=existing_record,
                    conflicting_record=conflicting_record,
                    row_reference_id=reference_id,
                )
            except ImportEntityConflictError as e:
                status = ImportRecordStatus.CONFLICT
                entity_id = e.conflict_entity_id
                self.logger.warning(
                    f"[ImportService] [process_meeting_import_row] Video conflict: {e}"
                )
                return DbImportRecord(
                    id=uuid.uuid4(),
                    import_entity_type=ImportEntityType.MEETING,
                    organization_id=job.organization_id,
                    created_at=datetime.now(pytz.utc),
                    job_id=job.id,
                    status=status,
                    status_detail=e.message,
                    entity_id=entity_id,
                    existing_record=existing_record,
                    conflicting_record=conflicting_record,
                    row_reference_id=reference_id,
                )
            except Exception as e:
                self.logger.opt(exception=e).exception(
                    f"[ImportService] [process_meeting_import_row] Failed to upload video: {e}"
                )
                return DbImportRecord(
                    id=uuid.uuid4(),
                    import_entity_type=ImportEntityType.MEETING,
                    organization_id=job.organization_id,
                    created_at=datetime.now(pytz.utc),
                    job_id=job.id,
                    status=ImportRecordStatus.FAILED,
                    status_detail=_get_except_msg(e),
                    entity_id=None,
                    existing_record=None,
                    conflicting_record=None,
                    row_reference_id=reference_id,
                )

            # Step 2: Generate presigned URL and thumbnails
            try:
                presigned_url_req = CreatePresignedUrlRequest(
                    key=not_none(file.s3_key),
                    method="get_object",
                    expiration_seconds=60 * 60 * 24,
                    s3_bucket_type=S3BucketType.MEETING,
                )

                response = await self.file_service.generate_presigned_url(
                    presigned_url_req
                )
                presigned_url = response.presigned_url

                (
                    sprite_file_s3_path,
                    vtt_file_s3_path,
                ) = await self.file_service.create_and_upload_video_thumbnail_sprite(
                    external_media_url=presigned_url,
                    s3_key_prefix=str(file.id),
                )

                file = await self.file_service.set_attachment_thumbnail_media_urls(
                    attachment_id=file.id,
                    organization_id=job.organization_id,
                    sprite_url=sprite_file_s3_path,
                    vtt_url=vtt_file_s3_path,
                    media_preview_thumbnail_url=f"{file.id}/{MEDIA_FILE_NAME}",
                )
            except ClientError as e:
                self.logger.opt(exception=e).exception(
                    f"[ImportService] [process_meeting_import_row] Failed to process video thumbnails: {e}"
                )
                return DbImportRecord(
                    id=uuid.uuid4(),
                    import_entity_type=ImportEntityType.MEETING,
                    organization_id=job.organization_id,
                    created_at=datetime.now(pytz.utc),
                    job_id=job.id,
                    status=ImportRecordStatus.FAILED,
                    status_detail=str(e),
                    entity_id=None,
                    existing_record=None,
                    conflicting_record=None,
                    row_reference_id=reference_id,
                )

            # Step 3: Import meeting
            try:
                import_request = ImportMeetingFromExternalRecordingRequest(
                    reference_id=file.id,
                    user_id=job.user_id,
                    account_id=mapped_fields.get("account_id"),
                    title=mapped_fields.get("title", "Imported Meeting"),
                    description=mapped_fields.get("description", ""),
                    user_names=mapped_fields.get("user_names", []),
                    contact_emails=mapped_fields.get("contact_emails", []),
                    date_of_meeting=mapped_fields.get(
                        "date_of_meeting", zoned_utc_now()
                    ),
                    presigned_media_url=presigned_url,
                    pipeline_id=mapped_fields.get("pipeline_id"),
                    pipeline_display_name=mapped_fields.get("pipeline_display_name"),
                    duration_seconds=duration_seconds,
                )

                meeting = (
                    await self.meeting_service.import_meeting_from_external_recording(
                        organization_id=job.organization_id, request=import_request
                    )
                )

                entity_id = meeting.id
                conflicting_record = json.loads(import_request.model_dump_json())

            except ResourceNotFoundError as e:
                status = ImportRecordStatus.VALIDATION_ERROR
                status_detail = e.to_reevo_error_response().message
                self.logger.bind(exception=e, status_detail=status_detail).warning(
                    "[ImportService] [process_meeting_import_row] Resource not found"
                )
            except ValueError as e:
                status = ImportRecordStatus.VALIDATION_ERROR
                status_detail = f"Meeting validation failed: {e}"
                self.logger.bind(exception=e, status_detail=status_detail).warning(
                    f"[ImportService] [process_meeting_import_row] Validation error: {e}"
                )
            except Exception as e:
                status = ImportRecordStatus.FAILED
                status_detail = _get_except_msg(e)
                self.logger.bind(status_detail=status_detail).opt(
                    exception=e
                ).exception(
                    f"[ImportService] [process_meeting_import_row] Failed to import meeting: {e}"
                )

        except Exception as e:
            status = ImportRecordStatus.FAILED
            status_detail = _get_except_msg(e)
            self.logger.bind(status_detail=status_detail).opt(exception=e).exception(
                "[ImportService] [process_meeting_import_row] Unexpected error"
            )

        return DbImportRecord(
            id=uuid.uuid4(),
            import_entity_type=ImportEntityType.MEETING,
            organization_id=job.organization_id,
            created_at=datetime.now(pytz.utc),
            job_id=job.id,
            status=status,
            entity_id=entity_id,
            status_detail=status_detail,
            existing_record=existing_record,
            conflicting_record=conflicting_record,
            row_reference_id=reference_id,
        )

    async def process_hubspot_crm_sync_job(self, job: Job) -> None:  # noqa: C901, PLR0912, PLR0915
        token = await self.hubspot_service.get_or_refresh_hubspot_access_token(
            organization_id=job.organization_id
        )

        existing_imported_records = await self.import_repository.list_all(
            organization_id=job.organization_id
        )
        existing_imported_record_map = {
            record.external_id: record
            for record in existing_imported_records
            if record.external_id
        }
        self.logger.info(
            f"existing_imported_record_map keys - {existing_imported_record_map.keys()}"
        )
        after: str | None = None

        match job.type:
            case JobType.HUBSPOT_TASK_SYNC:
                while True:
                    hubspot_task_response = (
                        await self.hubspot_service.hubspot_client.list_tasks(
                            access_token=token,
                            hubspot_list_request=HubSpotListTaskRequest(after=after),
                        )
                    )
                    for task in hubspot_task_response.data:
                        await self._process_single_task(
                            job, task, existing_imported_record_map
                        )
                    after = hubspot_task_response.next_cursor
                    if not after:
                        break
            case JobType.HUBSPOT_DEAL_SYNC:
                hubspot_pipelines = (
                    await self.hubspot_service.hubspot_client.list_pipelines(
                        access_token=token,
                        object_type=HubSpotApiEntityType.DEALS,
                        hubspot_list_request=HubSpotListDealRequest(),
                    )
                )
                # take first pipeline only
                new_pipeline_stages: list[PipelineStageRequest] = []
                if hubspot_pipelines and hubspot_pipelines.data:
                    for stage in hubspot_pipelines.data[0].stages:
                        new_pipeline_stages.append(
                            PipelineStageRequest(
                                display_name=stage.label,
                                label=stage.id,
                                probability=self._cast_probability(
                                    stage.metadata.probability
                                ),
                                is_closed=stage.metadata.is_closed,
                            )
                        )

                pipeline_stage_select_list = (
                    await self._find_or_create_pipeline_stage_select_list(
                        organization_id=job.organization_id,
                        user_id=job.user_id,
                        pipeline_stages=new_pipeline_stages,
                    )
                )

                stage_display_name_id_map = {
                    stage.display_value: stage.id
                    for stage in pipeline_stage_select_list.values
                }

                stage_label_id_map: dict[str, uuid.UUID] = {}

                for pipeline_stage in new_pipeline_stages:
                    if stage_id := stage_display_name_id_map.get(  # noqa: SIM102
                        pipeline_stage.display_name
                    ):
                        if pipeline_stage.label:
                            stage_label_id_map[pipeline_stage.label] = stage_id
                self.logger.bind(
                    stage_label_id_map=stage_label_id_map,
                    hubspot_pipelines=hubspot_pipelines,
                    pipeline_stage_select_list=pipeline_stage_select_list,
                ).info("process_hubspot_crm_sync_job, processed pipelines.")
                while True:
                    hubspot_deal_response = (
                        await self.hubspot_service.hubspot_client.list_deals(
                            access_token=token,
                            hubspot_list_request=HubSpotListDealRequest(after=after),
                        )
                    )
                    for deal in hubspot_deal_response.data:
                        await self._process_single_pipeline(
                            job=job,
                            deal=deal,
                            existing_imported_record_map=existing_imported_record_map,
                            pipeline_stage_select_list_value_map=stage_label_id_map,
                        )
                    after = hubspot_deal_response.next_cursor
                    if not after:
                        break

            case JobType.HUBSPOT_CONTACT_SYNC:
                while True:
                    hubspot_contact_response = (
                        await self.hubspot_service.hubspot_client.list_contacts(
                            access_token=token,
                            hubspot_list_request=HubSpotListContactRequest(after=after),
                        )
                    )
                    for contact in hubspot_contact_response.data:
                        await self._process_single_contact(
                            job, contact, existing_imported_record_map
                        )
                    after = hubspot_contact_response.next_cursor
                    if not after:
                        break
            case JobType.HUBSPOT_ACCOUNT_SYNC:
                while True:
                    hubspot_account_response = (
                        await self.hubspot_service.hubspot_client.list_companies(
                            access_token=token,
                            hubspot_list_request=HubSpotListCompanyRequest(after=after),
                        )
                    )
                    for account in hubspot_account_response.data:
                        await self._process_single_account(
                            job, account, existing_imported_record_map
                        )
                    after = hubspot_account_response.next_cursor
                    if not after:
                        break
            case JobType.HUBSPOT_USER_SYNC:
                while True:
                    hubspot_users_response = (
                        await self.hubspot_service.hubspot_client.list_users(
                            access_token=token,
                            hubspot_list_request=HubSpotListBaseRequest(after=after),
                        )
                    )
                    self.logger.info(
                        f"[CrmSyncService] process_crm_sync_job, hubspot_users: {hubspot_users_response}"
                    )
                    for user in hubspot_users_response.data:
                        await self._process_single_user(
                            job, user, existing_imported_record_map
                        )
                    after = hubspot_users_response.next_cursor
                    if not after:
                        break

    async def _process_single_task(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        job: Job,
        task: Any,
        existing_imported_record_map: dict[str, ImportRecord],
    ) -> None:
        owner_user_id = job.user_id
        if existing_task := existing_imported_record_map.get(task.id):
            self.logger.info(
                f"[process_crm_sync_job] - task already imported: {task}, existing: {existing_task}"
            )
        else:
            if hubspot_owner_id := task.properties.hubspot_owner_id:
                imported_user = existing_imported_record_map.get(hubspot_owner_id)
                if not imported_user or not imported_user.entity_id:
                    self.logger.error(
                        f"[process_crm_sync_job] - owner: {hubspot_owner_id} not imported. "
                    )
                else:
                    owner_user_id = imported_user.entity_id

            mapped_fields = task.properties.model_dump()
            if hubspot_company_association := task.associations:  # noqa: SIM102
                if (
                    hubspot_company_association_results
                    := hubspot_company_association.get("companies")
                ):
                    for result in hubspot_company_association_results.results:
                        if result.type == "task_to_company":  # noqa: SIM102
                            if imported_company := existing_imported_record_map.get(
                                result.id
                            ):
                                mapped_fields["account_id"] = imported_company.entity_id
                                self.logger.info(
                                    f"[process_crm_sync_job] - bingo {mapped_fields}"
                                )
            record = await self._process_task_import_row(
                job=job,
                field_values_map=mapped_fields,
                owner_user_id=owner_user_id,
            )
            await self.import_repository.insert(record)

    async def _process_single_pipeline(  # noqa: C901,PLR0912
        self,
        job: Job,
        deal: HubSpotDeal,
        existing_imported_record_map: dict[str, ImportRecord],
        pipeline_stage_select_list_value_map: dict[str, uuid.UUID] | None = None,
    ) -> None:
        owner_user_id = job.user_id
        import_pipeline_record = ImportPipelineRecord.from_hubspot_deal(deal=deal)
        if existing_pipeline := existing_imported_record_map.get(deal.id):
            self.logger.info(
                f"[process_crm_sync_job] - pipeline already imported: {deal}, existing: {existing_pipeline}"
            )
            existing_record = (
                ImportPipelineRecord.from_dict(
                    json_dict=existing_pipeline.existing_record
                )
                if existing_pipeline.existing_record
                else None
            )
            self.logger.bind(
                incoming_record=import_pipeline_record,
                existing_record=existing_record,
                deal=deal,
            ).info("[process_crm_sync_job] - deal already imported")
            if existing_record and import_pipeline_record == existing_record:
                if existing_pipeline.status == ImportRecordStatus.SUCCESS:
                    return
                await self.import_repository.update_import_record_status(
                    ids=[existing_pipeline.id],
                    status=ImportRecordStatus.SUCCESS,
                    organization_id=job.organization_id,
                )
            else:
                await self.import_repository.update_import_record(
                    organization_id=job.organization_id,
                    record_id=existing_pipeline.id,
                    status=ImportRecordStatus.CONFLICT,
                    status_detail="Conflict during sync",
                    job_id=job.id,
                    conflicting_record=import_pipeline_record.model_dump(),
                )
        else:
            if hubspot_owner_id := deal.properties.hubspot_owner_id:
                imported_user = existing_imported_record_map.get(hubspot_owner_id)
                if not imported_user or not imported_user.entity_id:
                    self.logger.error(
                        f"[process_crm_sync_job] - owner: {hubspot_owner_id} not imported. "
                    )
                else:
                    owner_user_id = imported_user.entity_id

            mapped_fields = deal.properties.model_dump()
            if pipeline_stage_select_list_value_map:  # noqa: SIM102
                if pipeline_stage_label := mapped_fields.get("deal_stage"):
                    mapped_fields["pipeline_stage_select_list_value_id"] = (
                        pipeline_stage_select_list_value_map.get(pipeline_stage_label)
                    )
            if hubspot_associations := deal.associations:
                if hubspot_company_association_results := hubspot_associations.get(
                    "companies"
                ):
                    for result in hubspot_company_association_results.results:
                        if result.type == "deal_to_company":
                            if imported_company := existing_imported_record_map.get(
                                result.id
                            ):
                                mapped_fields["account_id"] = str(
                                    imported_company.entity_id
                                )
                                self.logger.bind(
                                    imported_company=imported_company
                                ).info("[process_crm_sync_job] - bingo deal_to_company")
                            else:
                                self.logger.bind(
                                    company=result,
                                    mapped_fields=mapped_fields,
                                    deal=deal,
                                ).error(
                                    "[process_crm_sync_job] - deal_to_company company not found in existing import_records."
                                )
                if hubspot_contact_association_results := hubspot_associations.get(
                    "contacts"
                ):
                    for result in hubspot_contact_association_results.results:
                        contact_ids = []
                        if result.type == "deal_to_contact":
                            if imported_contact := existing_imported_record_map.get(
                                result.id
                            ):
                                contact_ids.append(str(imported_contact.entity_id))
                                self.logger.bind(
                                    imported_contact=imported_contact
                                ).info("[process_crm_sync_job] - bingo deal_to_contact")
                            else:
                                self.logger.bind(
                                    contact=result,
                                    mapped_fields=mapped_fields,
                                    deal=deal,
                                ).error(
                                    "[process_crm_sync_job] - deal_to_contact contact not found in existing import_records."
                                )
                        mapped_fields["contact_ids"] = contact_ids
            opportunity_import_csv_fields = await self._map_pipeline_field_values(
                organization_id=job.organization_id,
                field_values_map=mapped_fields,
                already_mapped_csv_fields=None,
            )
            record = await self._process_pipeline_import_row(
                job_id=job.id,
                user_id=job.user_id,
                organization_id=job.organization_id,
                opportunity_import_csv_fields=opportunity_import_csv_fields,
                local_timezone=None,
                owner_user_id=owner_user_id,
                import_pipeline_record=import_pipeline_record,
            )
            await self.import_repository.insert(record)

    async def _process_single_contact(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        job: Job,
        contact: Any,
        existing_imported_record_map: dict[str, ImportRecord],
    ) -> None:
        owner_user_id = job.user_id
        if existing_contact := existing_imported_record_map.get(contact.id):
            self.logger.info(
                f"[process_crm_sync_job] - contact already imported: {contact}, existing: {existing_contact}"
            )
        else:
            if hubspot_owner_id := contact.properties.hubspot_owner_id:
                imported_user = existing_imported_record_map.get(hubspot_owner_id)
                if not imported_user or not imported_user.entity_id:
                    self.logger.error(
                        f"[process_crm_sync_job] - owner: {hubspot_owner_id} not imported. "
                    )
                else:
                    owner_user_id = imported_user.entity_id

            mapped_fields = contact.properties.model_dump()
            mapped_fields["display_name"] = " ".join(
                [
                    mapped_fields.get("first_name") or "UNKNOWN",
                    mapped_fields.get("last_name") or "UNKNOWN",
                ]
            )
            if hubspot_company_association := contact.associations:  # noqa: SIM102
                if (
                    hubspot_company_association_results
                    := hubspot_company_association.get("companies")
                ):
                    for result in hubspot_company_association_results.results:
                        if result.type == "contact_to_company":  # noqa: SIM102
                            if imported_company := existing_imported_record_map.get(
                                result.id
                            ):
                                mapped_fields["account_id"] = imported_company.entity_id
                                self.logger.info(
                                    f"[process_crm_sync_job] - bingo {mapped_fields}"
                                )
            contact_import_csv_fields = await self._map_contact_field_values(
                organization_id=job.organization_id,
                field_values_map=mapped_fields,
                already_mapped_csv_fields=None,
            )
            record = await self._process_contact_import_row(
                job_id=job.id,
                user_id=job.user_id,
                organization_id=job.organization_id,
                contact_import_csv_fields=contact_import_csv_fields,
                local_timezone=None,
                owner_user_id=owner_user_id,
            )
            await self.import_repository.insert(record)

    async def _process_single_account(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        job: Job,
        account: Any,
        existing_imported_record_map: dict[str, ImportRecord],
    ) -> None:
        self.logger.info(
            f"[_process_single_account] - started process single account, job id: {job.id}, account: {account}"
        )
        owner_user_id = job.user_id
        if existing_account := existing_imported_record_map.get(account.id):
            # check if entity fields all match
            incoming_record_fields = await self._map_account_field_values(
                organization_id=job.organization_id,
                field_values_map=account.properties.model_dump(),
                already_mapped_csv_fields=None,
            )
            incoming_record = ImportAccountRecord.from_import_account_dict(
                account_import_csv_fields=incoming_record_fields,
                owner_user_id=owner_user_id,
            )
            existing_record = None
            if existing_account.existing_record:
                existing_record_fields = await self._map_account_field_values(
                    organization_id=job.organization_id,
                    field_values_map=existing_account.existing_record,
                    already_mapped_csv_fields=None,
                )
                existing_record = ImportAccountRecord.from_import_account_dict(
                    account_import_csv_fields=existing_record_fields,
                    owner_user_id=owner_user_id,
                )
            # if matches, check status:
            self.logger.bind(
                incoming_record=incoming_record,
                existing_record=existing_record,
                account=account,
            ).info("[process_crm_sync_job] - account already imported")
            if incoming_record == existing_record:
                if existing_account.status == ImportRecordStatus.SUCCESS:
                    return
                await self.import_repository.update_import_record_status(
                    ids=[existing_account.id],
                    status=ImportRecordStatus.SUCCESS,
                    organization_id=job.organization_id,
                )
            else:
                await self.import_repository.update_import_record(
                    organization_id=job.organization_id,
                    record_id=existing_account.id,
                    status=ImportRecordStatus.CONFLICT,
                    status_detail="Conflict during sync",
                    job_id=job.id,
                    conflicting_record=account.properties.model_dump(),
                )
        else:
            if hubspot_owner_id := account.properties.hubspot_owner_id:
                imported_user = existing_imported_record_map.get(hubspot_owner_id)
                if not imported_user or not imported_user.entity_id:
                    self.logger.error(
                        f"[process_crm_sync_job] - owner: {hubspot_owner_id} not imported. "
                    )
                else:
                    owner_user_id = imported_user.entity_id
            account_import_csv_fields = await self._map_account_field_values(
                organization_id=job.organization_id,
                field_values_map=account.properties.model_dump(),
                already_mapped_csv_fields=None,
            )
            record = await self._process_account_import_row(
                job_id=job.id,
                organization_id=job.organization_id,
                user_id=job.user_id,
                account_import_csv_fields=account_import_csv_fields,
                local_timezone=None,
                owner_user_id=owner_user_id,
                is_required=True,
            )
            await self.import_repository.insert(record)

    async def _process_single_user(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        job: Job,
        user: Any,
        existing_imported_record_map: dict[str, ImportRecord],
    ) -> None:
        hubspot_user_id = str(user.user_id)
        if existing_imported_record_map.get(hubspot_user_id):
            self.logger.warning(
                f"[process_crm_sync_job] - user already imported: {user}"
            )
        else:
            if user_with_the_email := await self.user_auth_service.get_user_by_email(
                email=user.email
            ):
                user_id = user_with_the_email.id
                status = ImportRecordStatus.CONFLICT
                status_detail = "User already exists"
            else:
                (
                    user_organization_association,
                    link,
                ) = await self.user_auth_service.invite_new_user(
                    user_auth_context=UserAuthContext(
                        organization_id=job.organization_id,
                        user_id=job.user_id,
                    ),
                    email=user.email,
                    organization_id=job.organization_id,
                    roles=[UserRole.USER],
                )
                if not user_organization_association:
                    raise ValueError(
                        f"User organization association not found for user {user.email}"
                    )
                user_id = user_organization_association.user_id
                status = ImportRecordStatus.SUCCESS
                status_detail = link or str(user_organization_association.user_id)
            await self.import_repository.insert(
                DbImportRecord(
                    id=uuid.uuid4(),
                    import_entity_type=ImportEntityType.USER,
                    organization_id=job.organization_id,
                    created_at=datetime.now(pytz.utc),
                    job_id=job.id,
                    row_reference_id=hubspot_user_id,
                    status=status,
                    entity_id=user_id,
                    status_detail=status_detail,
                    existing_record=None,
                    conflicting_record=json.loads(user.model_dump_json()),
                    external_id=hubspot_user_id,
                )
            )

    async def _get_existing_records_row_reference_ids(
        self,
        organization_id: uuid.UUID,
        job_id: uuid.UUID,
    ) -> set[str]:
        """Get map of existing import records."""
        existing_records = await self.import_repository.list_by_job_id(
            organization_id=organization_id, job_id=job_id
        )
        return set(  # noqa: C403
            [
                record.row_reference_id
                for record in existing_records
                if record.row_reference_id
            ]
        )

    async def _process_import_job_file_v2(
        self,
        file_binary: bytes,
        csv_import_job: ImportJob,
        heartbeat_resume: HeartbeatResume | None,
    ) -> list[ImportRecord]:
        # Process CSV data
        row_num_to_csv_row_map = await self._process_csv_data_v2(
            file_binary=file_binary,
            csv_import_job=csv_import_job,
            heartbeat_resume=heartbeat_resume,
        )
        if not row_num_to_csv_row_map:
            self.logger.error("process_import_job_v2 _process_csv_data_v2 failed")
            raise ResourceNotFoundError(
                "process_import_job_v2 _process_csv_data_v2 failed"
            )

        return await self._process_import_rows_v2(
            csv_import_job=csv_import_job,
            row_num_to_csv_row_map=row_num_to_csv_row_map,
            heartbeat_resume=heartbeat_resume,
        )

    async def _activity_temporal_heartbeat(
        self, heartbeat_resume: HeartbeatResume | None
    ) -> None:
        """
        Heartbeat needs to be sent regularly for long runing loops, even to the same value
          to avoid the heartbeat timeout.
        """
        if not heartbeat_resume or not activity.in_activity():
            return

        now = datetime.now(tz=pytz.utc)
        update_heartbeat: bool = now - heartbeat_resume.last_heartbeat_time > timedelta(
            seconds=1
        )

        # throttle to 1 heartbeat per second.  Cannot send too often or queue will be filled up and QueueFull exception raised.
        if update_heartbeat:
            heartbeat_resume.last_heartbeat_time = now
            activity.heartbeat(heartbeat_resume.heartbeat_resume_state)

    async def process_import_job_v2(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        job_id: uuid.UUID,
        organization_id: uuid.UUID,
    ) -> dict[str, Any]:
        """
        Returns: dictionary of error message, but keep it short to not pollute temporal workflow tracking
           Currently, it's only used for a success message of {"error": None}.
        """
        heartbeat_resume: HeartbeatResume | None = None
        if activity.in_activity():
            if activity.info().heartbeat_details:
                # Unlike its documentation, heartbeat_details never changes until and activity dies and is resumed.
                #   Only read once during HeartbeatResume creation.
                heartbeat_detail: int = int(activity.info().heartbeat_details[0])
                self.logger.bind(
                    job_id=job_id,
                    organization_id=organization_id,
                    heartbeat_detail=heartbeat_detail,
                ).info("Found resumed heartbeat details")
                heartbeat_resume = HeartbeatResume(
                    heartbeat_resume_state=heartbeat_detail,
                    last_heartbeat_time=datetime.fromtimestamp(0, tz=pytz.utc),
                )
            else:
                heartbeat_resume = HeartbeatResume(
                    heartbeat_resume_state=-1,
                    last_heartbeat_time=datetime.fromtimestamp(0, tz=pytz.utc),
                )
            # Needs to be set for long running loops, even to the same value
            #   before the heartbeat timeout
            await self._activity_temporal_heartbeat(heartbeat_resume)

        csv_import_job = await self.import_job_repository.get_by_id(
            job_id=job_id, organization_id=organization_id
        )
        self.logger.bind(csv_import_job=csv_import_job, job_id=job_id).info(
            "process_import_job_v2 retrieved csv_import_job."
        )
        if not csv_import_job or not csv_import_job.metadata:
            self.logger.error("process_import_job_v2 retrieved job missing metadata.")
            raise ResourceNotFoundError(
                "process_import_job_v2 retrieved job missing metadata"
            )
        # retrieve the file bytes
        file_binary = await self.file_service.get_file_from_import_csv_bucket(
            file_id=csv_import_job.metadata.file_id, organization_id=organization_id
        )
        processed_records = await self._process_import_job_file_v2(
            file_binary=file_binary,
            csv_import_job=csv_import_job,
            heartbeat_resume=heartbeat_resume,
        )

        created_domain_object_lists = await self._create_import_lists(
            post_processing_records=processed_records,
            organization_id=csv_import_job.organization_id,
            user_id=not_none(csv_import_job.created_by_user_id),
            heartbeat_resume=heartbeat_resume,
            list_name=csv_import_job.display_name,
        )

        output_file = await self._create_output_file(
            csv_import_job=csv_import_job,
            file_binary=file_binary,
            processed_records=processed_records,
            heartbeat_resume=heartbeat_resume,
        )

        # Create the result metadata
        result_metadata = ImportResultMetadata(
            output_file_link=output_file.public_url if output_file else None,
            output_file_s3_key=output_file.attachment.s3_key if output_file else None,
            created_list_map={
                created_domain_object_list.item_type.upper(): created_domain_object_list
                for created_domain_object_list in created_domain_object_lists
            },
        )

        self.logger.bind(
            csv_import_job=csv_import_job, result_metadata=result_metadata
        ).info("Process completed csv_import_job")
        # Update the import job with result metadata
        updated_job = await self.import_job_repository.set_result_metadata(
            job_id=csv_import_job.id,
            organization_id=csv_import_job.organization_id,
            result_metadata=result_metadata,
        )

        self.logger.bind(
            updated_job=updated_job,
            post_processing_records=processed_records,
            created_domain_object_lists=created_domain_object_lists,
        ).info(
            "[CrmSyncService] process_import_job completed. Updated import job with result metadata"
        )
        return {"error": None}

    async def _create_output_file_content(
        self,
        file_binary: bytes,
        processed_records: list[ImportRecord],
        heartbeat_resume: HeartbeatResume | None,
    ) -> str:
        # 1. Filter records that are not SUCCESS and group by row number
        non_success_records_by_row: dict[int, list[ImportRecord]] = {}
        for record in processed_records:
            await self._activity_temporal_heartbeat(heartbeat_resume)
            if record.status != ImportRecordStatus.SUCCESS and record.row_reference_id:
                row_num = int(record.row_reference_id)
                if row_num not in non_success_records_by_row:
                    non_success_records_by_row[row_num] = []
                non_success_records_by_row[row_num].append(record)
        # 2. Decode the original CSV file
        csv_content = file_binary.decode("utf-8")
        csv_reader = csv.reader(io.StringIO(csv_content))
        headers = next(csv_reader)  # Get the original headers

        # 3. Generate a new CSV with an additional error_details column
        output_file_content = io.StringIO()
        csv_writer = csv.writer(output_file_content)

        # Write headers with additional error_details column
        new_headers = [*headers, "error_details"]
        csv_writer.writerow(new_headers)

        # Process each row
        for row_idx, row in enumerate(csv_reader, start=1):
            await self._activity_temporal_heartbeat(heartbeat_resume)
            error_details = ""
            src_row_num = row_idx + 1  # row_idx is 0-based, src_row_num is 1-based
            if src_row_num in non_success_records_by_row:
                error_details_parts = []
                for record in non_success_records_by_row[src_row_num]:
                    status_str = f"Status: {record.status.value}"
                    if record.status_detail:
                        status_str += f" - {record.status_detail}"
                    error_details_parts.append(status_str)
                error_details = "; ".join(error_details_parts)

            # Write the original row data plus the error details
            csv_writer.writerow([*row, error_details])

        # 4. Upload the new CSV file
        return output_file_content.getvalue()

    async def _create_output_file(
        self,
        csv_import_job: ImportJob,
        file_binary: bytes,
        processed_records: list[ImportRecord],
        heartbeat_resume: HeartbeatResume | None,
    ) -> AttachmentDto:
        """
        Create an output CSV file with error details for non-successful records.

        Args:
            csv_import_job: The import job
            file_binary: The original CSV file as bytes
            processed_records: List of processed import records

        Returns:
            AttachmentDto: The uploaded file information
        """
        output_content_str = await self._create_output_file_content(
            file_binary=file_binary,
            processed_records=processed_records,
            heartbeat_resume=heartbeat_resume,
        )

        # Upload the new CSV file
        result = await self.file_service.upload_file(
            user_id=not_none(csv_import_job.created_by_user_id),
            organization_id=csv_import_job.organization_id,
            file_name=f"{csv_import_job.display_name}__{csv_import_job.id}__with_errors.csv",
            content_type="text/csv",
            file_binary=io.BytesIO(output_content_str.encode("utf-8")),
            bucket_type=S3BucketType.IMPORT_CSV,
        )
        custom_metric.increment(ImportMetricsName.IMPORT_UPLOAD_OUTPUT_FILE)
        return result

    def _set_nested_dict_value(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, target_dict_to_set: dict[str, Any], path: list[str], value: Any
    ) -> dict[str, Any]:
        """
        Set a value in a nested dictionary based on a path.

        Args:
            target_dict: The dictionary to modify
            path: List of keys forming the path to the target location
            value: The value to set at the target location
        """
        current = target_dict_to_set
        # Navigate to the correct nested location
        for _i, key in enumerate(path[:-1]):
            if key not in current:
                current[key] = {}
            current = current[key]

        # Set the value at the final location
        final_key = path[-1]
        if (existing_value := current.get(final_key)) is None:
            current[final_key] = value
        else:
            logger.warning(
                f"Qualified field path is already set: ({path}) The existingValue({existing_value}) is competing with ({value}). Taking first non-empty value."
            )
            if existing_value is None or str(existing_value).strip() == "":
                current[final_key] = value

        return target_dict_to_set

    def _generate_csv_object_instance(
        self, object_mapping: ObjectMapping, csv_row_header_to_value: dict[str, str]
    ) -> BaseModel:
        field_to_value_map: dict[
            str, str
        ] = {}  # Intermediate dictionary before model validation if model is availalble
        for column_mapping in object_mapping.column_mappings:
            if column_mapping.qualified_field is not None:
                if (
                    len(column_mapping.qualified_field.path)
                    > self.max_qualified_field_path_depth
                ):
                    raise ValueError(
                        f"Qualified field path depth is too deep: ({column_mapping.qualified_field.path}). Max depth is {self.max_qualified_field_path_depth}."
                    )
                elif len(column_mapping.qualified_field.path) <= 0:
                    logger.bind(
                        object_mapping=object_mapping,
                    ).error(
                        f"Qualified field path is empty for ({object_mapping.object_identifier}). Skipping column and continuing."
                    )
                    continue
                else:
                    field_to_value_map = self._set_nested_dict_value(
                        field_to_value_map,
                        column_mapping.qualified_field.path,
                        csv_row_header_to_value[column_mapping.column_name],
                    )

        # Get the appropriate model class for the object
        model_class: type[BaseModel] = self._get_model_class_for_object(
            object_mapping.object_identifier
        )
        return model_class.model_validate(field_to_value_map)

    def _get_model_class_for_object(
        self,
        object_identifier: StandardObjectIdentifier | CustomObjectIdentifier,
    ) -> type[BaseModel]:
        """
        Get the appropriate Pydantic model class for the specified object.

        Args:
            object_kind: The kind of object (e.g., 'standard', 'custom')
            object_name: The name of the object (e.g., 'contact', 'account')

        Returns:
            The Pydantic model class to use
        """
        # Map standard objects to their corresponding model classes
        standard_objects: dict[str, type[BaseModel]] = {
            StdObjectIdentifiers.contact: ContactImportCsvFields,
            StdObjectIdentifiers.account: AccountImportCsvFields,
            StdObjectIdentifiers.pipeline: OpportunityImportCsvFields,
            StdObjectIdentifiers.contact_account_role: ContactAccountRoleImportCsvFields,
            StdObjectIdentifiers.contact_pipeline_role: ContactOpportunityRoleImportCsvFields,
        }

        # Handle standard objects
        if isinstance(object_identifier, StandardObjectIdentifier):
            object_name_lower = object_identifier.object_name.lower()
            if object_name_lower in standard_objects:
                return standard_objects[object_name_lower]
            else:
                raise ValueError(f"Unsupported standard object: {object_name_lower}")

        # Handle custom objects
        elif isinstance(object_identifier, CustomObjectIdentifier):
            # For custom objects, you might need to dynamically create or retrieve the model
            # based on the organization's custom object schema
            return CustomObjectImportCsvFields
        else:
            raise ValueError(f"Unsupported object type: ({type(object_identifier)})")

    def _detect_line_continuation(
        self,
        line: str,
        line_continuation: LineContinuation | None,
        line_number: int,
    ) -> LineContinuation | None:
        """
        https://en.wikipedia.org/wiki/Comma-separated_values#Basic_rules
        A LineContinuation object only exists when the current line ends in an open quote for line continuation within a CSV cell.
        Args:
            pass in a LineContinuation if it exist; otherwise pass in None
        Returns:
            LineContinuation if it exists, otherwise pass in None
        """
        is_quote_open = False
        if line_continuation:
            is_quote_open = line_continuation.is_quote_open

        # go through the line character by character
        for char in line:
            if char == '"':
                is_quote_open = not is_quote_open

        if not is_quote_open and not line_continuation:
            # Basic CSV row without line continuations
            return None

        if not line_continuation:
            return LineContinuation(
                prev_lines=line,
                is_quote_open=is_quote_open,
                start_line_number=line_number,
                end_line_number=line_number,
            )
        else:
            return LineContinuation(
                prev_lines=line_continuation.prev_lines + "\n" + line,
                is_quote_open=is_quote_open,
                start_line_number=line_continuation.start_line_number,
                end_line_number=line_number,
            )

    async def _process_csv_data_v2(  # noqa: C901, PLR0915, PLR0912
        self,
        file_binary: bytes,
        csv_import_job: ImportJob,
        heartbeat_resume: HeartbeatResume | None,
    ) -> dict[int, CsvRow | None] | None:
        """Process CSV file and return row dictionary map with preserved line numbers."""
        try:
            if (
                csv_import_job.metadata is None
                or not csv_import_job.metadata.object_mappings
            ):
                raise ValueError(
                    "Required csv_import_job column specification mapping is None"
                )

            object_mappings: list[ObjectMapping] = (
                csv_import_job.metadata.object_mappings
            )
            association_label_mappings: list[AssociationLabelMapping] = (
                csv_import_job.metadata.association_label_mapping
            )
            association_columns_to_names_to_ids: dict[str, dict[str, str]] = {}
            always_included_relationship_ids: list[str] = []
            for association_label_mapping in association_label_mappings:
                if not association_label_mapping.column_mapping:
                    always_included_relationship_ids.append(
                        association_label_mapping.relationship_id
                    )
                    continue
                for (
                    column_name,
                    relationship_names,
                ) in association_label_mapping.column_mapping.items():
                    if column_name not in association_columns_to_names_to_ids:
                        association_columns_to_names_to_ids[column_name] = {}
                    for relationship_name in relationship_names:
                        association_columns_to_names_to_ids[column_name][
                            relationship_name
                        ] = association_label_mapping.relationship_id

            data_str = file_binary.decode("utf-8")
            # python csv reader would completely stop processing for the entire file if any error is encoutered
            #   We have to split manually, and account for line continuations manually as well.
            lines = data_str.splitlines()
            if not lines:
                return {}

            header = lines[0]  # csv header is the first line
            if not header.strip():
                raise ValueError(
                    "CSV header line is empty. First line must be CSV header."
                )

            to_return: dict[int, CsvRow | None] = {}

            line_continuation = None

            # Start at 2 because line 1 is header

            for line_number, line_value in enumerate(lines[1:], start=2):
                line_continuation = self._detect_line_continuation(
                    line_value, line_continuation, line_number
                )
                if line_continuation:
                    if (
                        line_continuation.is_quote_open
                    ):  # Open quote means csv cell continues to next line
                        continue
                    line = line_continuation.prev_lines
                    num_lines = (
                        line_continuation.end_line_number
                        - line_continuation.start_line_number
                        + 1
                    )
                    if num_lines >= self.warn_csv_line_continuation_length:
                        logger.info(
                            f"Warning only, line continuation was long({num_lines}),"
                            f" start_line_number({line_continuation.start_line_number}),"
                            f" end_line_number({line_continuation.end_line_number}), line:({line[0:1000]})..."
                        )
                    line_continuation = None
                else:
                    line = line_value

                # Heartbeat to prevent timeout but must not increment progress
                #   because it's not real progress until it's stored in the DB.
                # Also, must not heartbeat mid-line.
                await self._activity_temporal_heartbeat(heartbeat_resume)
                if (
                    heartbeat_resume
                    and line_number < heartbeat_resume.heartbeat_resume_state
                ):
                    continue

                if not line.strip():  # Skip empty lines but preserve line numbering
                    continue

                # Create a CSV reader for just this line with the header
                line_io = io.StringIO(f"{header}\n{line}")
                try:
                    reader = csv.DictReader(line_io)
                    csv_row_header_to_value: dict[str, str] = next(reader)
                except Exception as e:
                    logger.opt(exception=e).exception(
                        f"Error processing lineNum {line_number}: {e}"
                    )
                    continue

                # Skip if all values are empty
                if all(
                    not str(value).strip() for value in csv_row_header_to_value.values()
                ):
                    continue
                info_last_object: str = ""
                info_last_column: str = ""
                try:
                    # (1) Find object instances
                    csv_object_instances: list[BaseModel] = []
                    for object_mapping in object_mappings:
                        info_last_object = ImportEntityType.from_object_identifier(
                            object_mapping.object_identifier
                        )
                        # Create a typed record
                        csv_object_instance = self._generate_csv_object_instance(
                            object_mapping, csv_row_header_to_value
                        )
                        csv_object_instances.append(csv_object_instance)

                    # (2) Find relationship ids
                    relationship_ids: list[str] = (
                        always_included_relationship_ids.copy()
                    )
                    for (
                        column,
                        relationship_name_to_id,
                    ) in association_columns_to_names_to_ids.items():
                        try:
                            info_last_column = column
                            relationship_value = csv_row_header_to_value[column]
                            relationship_id = relationship_name_to_id.get(
                                relationship_value
                            )
                            if relationship_id:
                                relationship_ids.append(relationship_id)
                            else:
                                logger.bind(
                                    column=column,
                                    relationship_value=relationship_value,
                                ).error(
                                    f"Relationship ID not found for column: {column}, relationship value: {relationship_value}"
                                )
                        except KeyError:
                            logger.bind(column=column).warning(
                                f"Column '{column}' not found in CSV headers: {csv_row_header_to_value.keys()}"
                            )
                    to_return[line_number] = CsvRow(
                        src_line_num=line_number,
                        all_entities=csv_object_instances,
                        all_relationship_ids=relationship_ids,
                    )
                except Exception as e:
                    err_msg = f"Error validating parsed CSV row({line_number}) column({info_last_column}): {e}"
                    logger.opt(exception=e).exception(err_msg)
                    await self.import_repository.insert(
                        DbImportRecord(
                            id=uuid.uuid4(),
                            import_entity_type=info_last_object,
                            organization_id=csv_import_job.organization_id,
                            created_at=datetime.now(pytz.utc),
                            job_id=csv_import_job.id,
                            row_reference_id=str(line_number),
                            status=ImportRecordStatus.FAILED,
                            entity_id=csv_import_job.created_by_user_id,
                            status_detail=err_msg + f", lineText:({line})",
                            existing_record=None,
                            conflicting_record=None,
                            external_id=None,
                        )
                    )
                    # Recorded the error, going to the next line
                    continue
            if line_continuation:
                err_msg = (
                    f"CSV file ends with open quotes, trailing rows unprocessed."
                    f" start_line_number:({line_continuation.start_line_number}),"
                    f" end_line_number:({line_continuation.end_line_number}),"
                    f" first 1000 chars:({line_continuation.prev_lines[0:1000]})"
                )
                logger.error(err_msg)
                for object_mapping in object_mappings:
                    await self.import_repository.insert(
                        DbImportRecord(
                            id=uuid.uuid4(),
                            import_entity_type=ImportEntityType.from_object_identifier(
                                object_mapping.object_identifier
                            ),
                            organization_id=csv_import_job.organization_id,
                            created_at=datetime.now(pytz.utc),
                            job_id=csv_import_job.id,
                            row_reference_id=str(line_continuation.start_line_number),
                            status=ImportRecordStatus.VALIDATION_ERROR,
                            entity_id=csv_import_job.created_by_user_id,
                            status_detail=err_msg,
                            existing_record=None,
                            conflicting_record=None,
                            external_id=None,
                        )
                    )
                # continuing to import with lines that did parse.
            return to_return
        except Exception as e:
            err_msg = f"Error processing CSV data: {e}"
            self.logger.opt(exception=e).exception(err_msg)
            await self.import_repository.insert(
                DbImportRecord(
                    id=uuid.uuid4(),
                    import_entity_type="",
                    organization_id=csv_import_job.organization_id,
                    created_at=datetime.now(pytz.utc),
                    job_id=csv_import_job.id,
                    row_reference_id="",
                    status=ImportRecordStatus.FAILED,
                    entity_id=csv_import_job.created_by_user_id,
                    status_detail=err_msg,
                    existing_record=None,
                    conflicting_record=None,
                    external_id=None,
                )
            )
            raise ValueError(err_msg)

    class CurrentlyProcessing:
        def __init__(
            self,
            row_number: int,
            entity_type: ImportEntityType | None,
            field_name: str | None,
            field_value: str | None,
            custom_object_name: str | None,
            association_source: str | None,
            association_target: str | None,
        ):
            self.row_number = row_number
            self.entity_type = entity_type
            self.field_name = field_name
            self.field_value = field_value
            self.custom_object_name = custom_object_name
            self.association_source = association_source
            self.association_target = association_target

        def set_entity_type_and_clear_detail_fields(
            self, entity_type: ImportEntityType
        ) -> None:
            """
            Set the entity type and clear the detail fields. row_number is preserved.
            """
            self.entity_type = entity_type
            self.field_name = None
            self.field_value = None
            self.custom_object_name = None
            self.association_source = None
            self.association_target = None

    class ImportProcessingError(Exception):
        """Custom exception for errors during CSV import processing."""

        def __init__(
            self,
            message: str,
            # Use quotes for forward reference to nested class
            currently_processing: "CrmSyncService.CurrentlyProcessing",
            original_exception: Exception | None = None,
        ):
            super().__init__(message)
            self.currently_processing = currently_processing
            self.original_exception = original_exception

        def __str__(self) -> str:
            parts = []
            if self.currently_processing.row_number is not None:
                parts.append(f"CsvRowNum({self.currently_processing.row_number})")
            if self.currently_processing.entity_type:
                parts.append(f"EntityType({self.currently_processing.entity_type})")
            if self.currently_processing.field_name:
                parts.append(f"FieldName({self.currently_processing.field_name})")
            if self.currently_processing.field_value:
                parts.append(f"FieldValue({self.currently_processing.field_value})")
            if self.currently_processing.custom_object_name:
                parts.append(
                    f"CustomObjectName('{self.currently_processing.custom_object_name}')"
                )
            if self.currently_processing.association_source:
                parts.append(
                    f"AssociationSource('{self.currently_processing.association_source}')"
                )
            if self.currently_processing.association_target:
                parts.append(
                    f"AssociationTarget('{self.currently_processing.association_target}')"
                )
            if self.original_exception:
                parts.append(
                    f"(OriginalError: ({type(self.original_exception).__name__}, {self.original_exception})"
                )
            return " ".join(parts)

        def get_import_status_detail(self) -> str:
            parts = []
            if self.currently_processing.field_name:
                parts.append(f"FieldName({self.currently_processing.field_name})")
            if self.currently_processing.field_value:
                parts.append(f"FieldValue({self.currently_processing.field_value})")
            if self.currently_processing.custom_object_name:
                parts.append(
                    f"CustomObjectName('{self.currently_processing.custom_object_name}')"
                )
            if self.currently_processing.association_source:
                parts.append(
                    f"AssociationSource('{self.currently_processing.association_source}')"
                )
            if self.currently_processing.association_target:
                parts.append(
                    f"AssociationTarget('{self.currently_processing.association_target}')"
                )
            if self.original_exception:
                parts.append(
                    f"(Err: ({type(self.original_exception).__name__}, {self.original_exception})"
                )
            return " ".join(parts)

    async def _process_import_rows_v2(
        self,
        csv_import_job: ImportJob,
        row_num_to_csv_row_map: dict[int, CsvRow | None],
        heartbeat_resume: HeartbeatResume | None,
    ) -> list[ImportRecord]:
        # Get existing records
        existing_row_ids = await self._get_existing_records_row_reference_ids(
            organization_id=csv_import_job.organization_id,
            job_id=csv_import_job.id,
        )
        self.logger.info(f"[CrmSyncService] existing_row_ids: {existing_row_ids}")

        # get custom object dtos by org_id
        custom_object_dtos = (
            await self.custom_object_service.get_organization_custom_objects_details(
                organization_id=csv_import_job.organization_id, include_frozen=True
            )
        )
        resume_msg = ""
        if heartbeat_resume and heartbeat_resume.heartbeat_resume_state > 0:
            resume_msg = (
                f" resuming from csv row {heartbeat_resume.heartbeat_resume_state}"
            )

        self.logger.bind(
            custom_object_dtos=custom_object_dtos, row_count=len(row_num_to_csv_row_map)
        ).info("[CrmSyncService] _process_import_rows_v2 starts" + resume_msg)

        # Send Slack notification but don't block on errors
        await self.slack_client.send_message(
            env=settings.environment,
            channel=SlackChannels.CRM_IMPORT_STATUS,
            text=f"{settings.environment} - Start processing new import job: {csv_import_job.id}"
            + resume_msg,
        )

        last_row_number_processed = 0
        for row_number in row_num_to_csv_row_map:
            last_row_number_processed = row_number
            if heartbeat_resume:
                if row_number < heartbeat_resume.heartbeat_resume_state:
                    await self._activity_temporal_heartbeat(heartbeat_resume)
                    continue
                heartbeat_resume.update_resume(row_number)
                await self._activity_temporal_heartbeat(heartbeat_resume)
            try:
                if str(row_number) not in existing_row_ids:
                    await self._process_row_v2(
                        csv_import_job=csv_import_job,
                        new_record_csv_row=row_num_to_csv_row_map[row_number],
                        custom_object_dtos=custom_object_dtos,
                        row_number=row_number,
                    )
            except self.ImportProcessingError as e:
                self.logger.bind(
                    row_number=row_number,
                    import_job_id=csv_import_job.id,
                    organization_id=csv_import_job.organization_id,
                ).error(
                    "Error processing CSV row. Continuing with next row.", exc_info=e
                )
                entity_type = str(
                    e.currently_processing.entity_type.value
                    if e.currently_processing.entity_type
                    else "None",
                )
                custom_metric.increment(
                    ImportMetricsName.IMPORT_PROCESSING_ERROR, tags=[entity_type]
                )
                await self.import_repository.insert(
                    DbImportRecord(
                        id=uuid.uuid4(),
                        import_entity_type=entity_type,
                        organization_id=csv_import_job.organization_id,
                        created_at=datetime.now(pytz.utc),
                        job_id=csv_import_job.id,
                        row_reference_id=str(row_number),
                        status=ImportRecordStatus.FAILED,
                        entity_id=None,
                        status_detail=f"ImportProcessingError:({e.get_import_status_detail()})",
                        existing_record=None,
                        conflicting_record=None,
                        external_id=None,
                    )
                )
            except (
                Exception
            ) as e:  # Catchall precaution, should not become a regular path.
                self.logger.bind(
                    row_number=row_number,
                    import_job_id=csv_import_job.id,
                    organization_id=csv_import_job.organization_id,
                ).opt(exception=e).error(
                    "Unknown Error processing CSV row. Continuing with next row.",
                    exc_info=e,
                )
                custom_metric.increment(ImportMetricsName.IMPORT_UNKNOWN_ERROR)
                await self.import_repository.insert(
                    DbImportRecord(
                        id=uuid.uuid4(),
                        import_entity_type="UNKNOWN",
                        organization_id=csv_import_job.organization_id,
                        created_at=datetime.now(pytz.utc),
                        job_id=csv_import_job.id,
                        row_reference_id=str(row_number),
                        status=ImportRecordStatus.FAILED,
                        entity_id=None,
                        status_detail=_get_except_msg(e),
                        existing_record=None,
                        conflicting_record=None,
                        external_id=None,
                    )
                )
        if heartbeat_resume:
            heartbeat_resume.update_resume(last_row_number_processed + 1)
            await self._activity_temporal_heartbeat(heartbeat_resume)

        # Grabbing this result from the Database is pure awesomeness because it
        #   enables resume functionality.
        return await self.import_repository.list_by_job_id(
            job_id=csv_import_job.id, organization_id=csv_import_job.organization_id
        )

    def _supported_domain_object_list_type_from_import_type(
        self, import_entity_type: ImportEntityType
    ) -> DomainObjectListItemType | None:
        match import_entity_type:
            case ImportEntityType.ACCOUNT:
                return DomainObjectListItemType.ACCOUNT
            case ImportEntityType.CONTACT:
                return DomainObjectListItemType.CONTACT
            case ImportEntityType.PIPELINE:
                return DomainObjectListItemType.PIPELINE
            case _:
                return None

    async def _create_import_lists(
        self,
        post_processing_records: list[ImportRecord],
        organization_id: uuid.UUID,
        user_id: uuid.UUID,
        heartbeat_resume: HeartbeatResume | None,
        list_name: str | None = None,
    ) -> list[DomainObjectList]:
        result_lists: list[DomainObjectList] = []
        records_by_type = defaultdict(list)
        for record in post_processing_records:
            await self._activity_temporal_heartbeat(heartbeat_resume)
            records_by_type[record.import_entity_type].append(record)

        # Convert to regular dict if needed
        records_map = dict(records_by_type)

        for record_type in records_map:
            await self._activity_temporal_heartbeat(heartbeat_resume)
            if (
                domain_list_type
                := self._supported_domain_object_list_type_from_import_type(record_type)
            ):
                records = records_by_type[record_type]
                domain_object_list = await self.domain_object_list_service.create_domain_object_list(
                    request=CreateDomainObjectListRequest(
                        name=list_name
                        or f"Imported list {zoned_utc_now().strftime('%Y-%m-%d')}",
                        description=f"List from import {zoned_utc_now().strftime('%Y-%m-%d')}",
                        item_type=domain_list_type,
                        origin=DomainObjectListOrigin.IMPORT,
                        initial_items=AddItemsRequest(
                            item_ids_to_add={
                                record.entity_id
                                for record in records
                                if record.entity_id
                            },
                            add_by_filter=None,
                        ),
                    ),
                    user_id=user_id,
                    organization_id=organization_id,
                )
                custom_metric.increment(
                    ImportMetricsName.IMPORT_CREATE_DOMAIN_OBJECT_LIST,
                    tags=[domain_list_type],
                )
                result_lists.append(domain_object_list)
        return result_lists

    async def get_owner_user_id_v2(
        self,
        organization_id: uuid.UUID,
        default_user_id: uuid.UUID,
        user_email: str | None,
    ) -> uuid.UUID:
        if user_email:
            try:
                org_user = (
                    await self.user_auth_service.user_service.get_user_by_email_v2(
                        email=user_email, organization_id=organization_id
                    )
                )
                if (
                    org_user.organization_association_status
                    == UserOrganizationAssociationStatus.ACTIVE
                ):
                    return org_user.id
            except Exception as e:
                self.logger.bind(
                    organization_id=organization_id,
                    user_email=user_email,
                ).warning(  # just printing out warning rather than trace to avoid common issue clutter.
                    "V2 Import file provided owner user email but failed finding user for org, "
                    f"Ignoring this error and using the default user_id({default_user_id}), e({type(e)}, {e})"
                )
        return default_user_id

    def _get_first_csv_import_entity_by_type(
        self, all_entities: list[BaseModel], entity_type: type[BaseModel]
    ) -> BaseModel | None:
        all_entities_of_type = [
            entity for entity in all_entities if isinstance(entity, entity_type)
        ]
        if all_entities_of_type:
            if len(all_entities_of_type) > 1:
                self.logger.warning(
                    f"Found multiple ({entity_type}) in the import row. Using the first one."
                )
            return all_entities_of_type[0]
        return None

    async def _process_row_v2(  # noqa: C901,PLR0912,PLR0915
        self,
        csv_import_job: ImportJob,
        row_number: int,
        custom_object_dtos: list[CustomObjectDto],
        new_record_csv_row: CsvRow | None,
    ) -> None:
        """Process a single import row."""
        self.logger.bind(
            all_entities=new_record_csv_row.all_entities
            if new_record_csv_row
            else None,
            all_relationship_ids=new_record_csv_row.all_relationship_ids
            if new_record_csv_row
            else None,
            csv_import_job=csv_import_job,
        ).info("[CrmSyncService] _process_row_v2 starts")

        if not new_record_csv_row:
            raise ResourceNotFoundError(
                f"[CrmSyncService] _process_row_v2, invalid new_record_csv_row, job_id: {csv_import_job.id}"
            )

        # based on job's configuration.object_identifiers, process following objects in order:
        # account, contact, contact_account_role, pipeline, pipeline_account_role, custom_object(s), custom_object_association(s)

        job_configuration = csv_import_job.configuration
        if not job_configuration or not job_configuration.object_identifiers:
            raise ResourceNotFoundError(
                f"[CrmSyncService] _process_row_v2, invalid job_configuration, job_id: {csv_import_job.id}"
            )

        object_id_or_names = [
            object_id_or_name(oi) for oi in job_configuration.object_identifiers
        ]
        local_timezone: TimeZoneName | None = (
            job_configuration.timezone if job_configuration else None
        )

        info_currently_processing = self.CurrentlyProcessing(
            row_number=row_number,
            entity_type=None,
            field_name=None,
            field_value=None,
            custom_object_name=None,
            association_source=None,
            association_target=None,
        )

        try:
            # account
            account_id = None
            if StdObjectIdentifiers.account in object_id_or_names:
                info_currently_processing.set_entity_type_and_clear_detail_fields(
                    ImportEntityType.ACCOUNT
                )
                account_import_csv_fields = self._get_first_csv_import_entity_by_type(
                    new_record_csv_row.all_entities or [], AccountImportCsvFields
                )

                custom_object_dto_matches = [
                    co_dto
                    for co_dto in custom_object_dtos
                    if co_dto.custom_object.parent_object_name
                    == ExtendableStandardObject.account
                ]
                if (
                    account_import_csv_fields and custom_object_dto_matches
                ):  # Handle custom fields
                    custom_object_dto = custom_object_dto_matches[0]
                    custom_field_data_account: dict[
                        uuid.UUID, FieldValueOrAny
                    ] = await self.map_custom_field_data(
                        organization_id=custom_object_dto.organization_id,
                        custom_object_dto=custom_object_dto,
                        model_extra=account_import_csv_fields.model_extra or {},
                        local_timezone=local_timezone,
                        info_currently_processing=info_currently_processing,
                    )
                    if (
                        custom_field_data_account
                        and account_import_csv_fields.model_extra
                    ):
                        account_import_csv_fields.model_extra[CUSTOM_FIELD_DATA] = (
                            custom_field_data_account
                        )
                        account_import_csv_fields.model_extra[CSV_TYPE] = (
                            StdObjectIdentifiers.account
                        )

                self.logger.bind(
                    account_import_csv_fields=account_import_csv_fields.model_dump_json()
                    if account_import_csv_fields
                    else "NONE"
                ).info("[CrmSyncService] account_import_csv_fields.")
                if isinstance(account_import_csv_fields, AccountImportCsvFields):
                    info_currently_processing.set_entity_type_and_clear_detail_fields(
                        ImportEntityType.ACCOUNT
                    )
                    self._normalize_csv_account_import_model_extra(
                        account_import_csv_fields
                    )
                    account_owner_user_id = await self.get_owner_user_id_v2(
                        organization_id=csv_import_job.organization_id,
                        default_user_id=not_none(csv_import_job.created_by_user_id),
                        user_email=account_import_csv_fields.company_owner_email,
                    )
                    # If multi-object import with Contacts, then Account is NOT required.  Contact can be standalone.
                    #    account is REQUIRED to be present if Contact is NOT present.
                    is_account_required: bool = (
                        self._get_first_csv_import_entity_by_type(
                            new_record_csv_row.all_entities or [],
                            ContactImportCsvFields,
                        )
                        is None
                    )
                    account_import_record = await self._process_account_import_row(
                        job_id=csv_import_job.id,
                        organization_id=csv_import_job.organization_id,
                        user_id=not_none(csv_import_job.created_by_user_id),
                        account_import_csv_fields=account_import_csv_fields,
                        reference_id=str(row_number),
                        local_timezone=local_timezone,
                        owner_user_id=account_owner_user_id,
                        is_required=is_account_required,
                        upsert_mode=(
                            csv_import_job.configuration.object_import_mode
                            == ObjectImportMode.UPSERT
                        )
                        if csv_import_job.configuration
                        else False,
                    )
                    custom_metric.increment(
                        ImportMetricsName.IMPORT_ACCOUNT,
                        tags=[account_import_record.status],
                    )
                    # Insert the record into the database
                    await self.import_repository.insert(instance=account_import_record)
                    account_id = (
                        account_import_record.entity_id
                    )  # possibly None for failed records

            # contact
            contact_id = None
            contact_import_csv_fields = None
            if StdObjectIdentifiers.contact in object_id_or_names:
                info_currently_processing.set_entity_type_and_clear_detail_fields(
                    ImportEntityType.CONTACT
                )
                contact_import_csv_fields = self._get_first_csv_import_entity_by_type(
                    new_record_csv_row.all_entities or [], ContactImportCsvFields
                )
                custom_object_dto_matches = [
                    co_dto
                    for co_dto in custom_object_dtos
                    if co_dto.custom_object.parent_object_name
                    == ExtendableStandardObject.contact
                ]
                if (
                    contact_import_csv_fields and custom_object_dto_matches
                ):  # Handle custom fields
                    custom_object_dto = custom_object_dto_matches[0]
                    custom_field_data_contact: dict[
                        uuid.UUID, FieldValueOrAny
                    ] = await self.map_custom_field_data(
                        organization_id=custom_object_dto.organization_id,
                        custom_object_dto=custom_object_dto,
                        model_extra=contact_import_csv_fields.model_extra or {},
                        local_timezone=local_timezone,
                        info_currently_processing=info_currently_processing,
                    )
                    if (
                        custom_field_data_contact
                        and contact_import_csv_fields.model_extra
                    ):
                        contact_import_csv_fields.model_extra[CUSTOM_FIELD_DATA] = (
                            custom_field_data_contact
                        )
                        contact_import_csv_fields.model_extra[CSV_TYPE] = (
                            StdObjectIdentifiers.contact
                        )

                self.logger.bind(
                    contact_import_csv_fields=contact_import_csv_fields
                ).info("[CrmSyncService] contact_import_csv_fields.")
                # TODO: move this out to StdObjectIdentifiers.contact_account_role section

                if isinstance(contact_import_csv_fields, ContactImportCsvFields):
                    info_currently_processing.set_entity_type_and_clear_detail_fields(
                        ImportEntityType.CONTACT
                    )
                    contact_owner_user_id = await self.get_owner_user_id_v2(
                        organization_id=csv_import_job.organization_id,
                        default_user_id=not_none(csv_import_job.created_by_user_id),
                        user_email=contact_import_csv_fields.contact_owner_email,
                    )
                    import_record = await self._process_contact_import_row_v2(
                        job=csv_import_job,
                        contact_import_csv_fields=contact_import_csv_fields,
                        owner_user_id=contact_owner_user_id,
                        reference_id=str(row_number),
                    )
                    custom_metric.increment(
                        ImportMetricsName.IMPORT_CONTACT, tags=[import_record.status]
                    )
                    # Insert the record into the database
                    await self.import_repository.insert(instance=import_record)
                    contact_id = (
                        import_record.entity_id
                    )  # possibly None for failed records

            # contact_account_role
            if StdObjectIdentifiers.contact_account_role in object_id_or_names:
                info_currently_processing.set_entity_type_and_clear_detail_fields(
                    ImportEntityType.CONTACT_ACCOUNT_ROLE
                )
                car_import_record: DbImportRecord | None = None
                if account_id and contact_id:
                    car_import_csv_fields = self._get_first_csv_import_entity_by_type(
                        new_record_csv_row.all_entities or [],
                        ContactAccountRoleImportCsvFields,
                    )
                    if car_import_csv_fields is None:
                        # Contact Account Role has no required fields, creating an empty
                        #   instance if it is not present.
                        car_import_csv_fields = ContactAccountRoleImportCsvFields(
                            contact_account_role_title=None,
                            contact_account_role_department=None,
                        )
                    if isinstance(
                        car_import_csv_fields, ContactAccountRoleImportCsvFields
                    ):  # This if-statement to satisfy the type checker, but unnecessary.
                        car_import_record = await self._process_contact_import_role_row(
                            job=csv_import_job,
                            account_id=account_id,
                            contact_id=contact_id,
                            car_import_csv_fields=not_none(car_import_csv_fields),
                            reference_id=str(row_number),
                        )
                        custom_metric.increment(
                            ImportMetricsName.IMPORT_CONTACT_ACCOUNT_ROLE,
                            tags=[car_import_record.status],
                        )

                        # check contact title and dual-write if suitable
                        if (
                            car_import_record.status == ImportRecordStatus.SUCCESS
                            and car_import_csv_fields.contact_account_role_title
                        ):
                            contact = await self.contact_service.get_contact_v2(
                                contact_id=contact_id,
                                organization_id=csv_import_job.organization_id,
                            )
                            if not contact.title:
                                await self.contact_service.patch_by_id(
                                    organization_id=csv_import_job.organization_id,
                                    user_id=not_none(csv_import_job.created_by_user_id),
                                    contact_id=contact_id,
                                    request=PatchContactRequest(
                                        title=car_import_csv_fields.contact_account_role_title
                                    ),
                                )

                    else:
                        raise ValueError(
                            "[CrmSyncService] Violated Invariant: car_import_csv_fields must be ContactAccountRoleImportCsvFields. Developer Error."
                        )
                elif contact_id or account_id:  # Only 1 object is present.
                    # In multi-object import, allow stand-alone contact or account
                    if contact_id:
                        status_detail = "Contact is present but Account is not."
                        entity_id = contact_id
                    elif account_id:
                        status_detail = "Account is present but Contact is not."
                        entity_id = account_id
                    else:
                        raise ValueError(
                            "[CrmSyncService] Violated Invariant: No Contact and no Account. Developer Error."
                        )

                    self.logger.bind(
                        csv_import_job=csv_import_job,
                        account_id=account_id,
                        contact_id=contact_id,
                    ).info(
                        "[CrmSyncService] import contact_account_role but no account_id or no contact_id. Skipping."
                    )
                    car_import_record = DbImportRecord(
                        id=uuid.uuid4(),
                        import_entity_type=ImportEntityType.CONTACT_ACCOUNT_ROLE,
                        organization_id=csv_import_job.organization_id,
                        job_id=csv_import_job.id,
                        row_reference_id=str(row_number),
                        status=ImportRecordStatus.SKIPPED,
                        status_detail=status_detail,
                        entity_id=entity_id,
                        created_at=datetime.now(pytz.utc),
                        existing_record=None,
                        conflicting_record=None,
                        external_id=None,
                    )
                else:  # No contact_id and no account_id.
                    # This is a validation error and record as such.
                    self.logger.bind(
                        csv_import_job=csv_import_job,
                    ).error(
                        "[CrmSyncService] import contact_account_role error: no account_id and no contact_id."
                    )
                    car_import_record = DbImportRecord(
                        id=uuid.uuid4(),
                        import_entity_type=ImportEntityType.CONTACT_ACCOUNT_ROLE,
                        organization_id=csv_import_job.organization_id,
                        job_id=csv_import_job.id,
                        row_reference_id=str(row_number),
                        status=ImportRecordStatus.VALIDATION_ERROR,
                        status_detail="No Contact and No Account present in the row",
                        entity_id=None,
                        created_at=datetime.now(pytz.utc),
                        existing_record=None,
                        conflicting_record=None,
                        external_id=None,
                    )

                if not car_import_record:
                    raise ValueError(
                        "[CrmSyncService] Violated Invariant: car_import_record must NEVER be None. Developer Error."
                    )

                # Insert the record into the database
                await self.import_repository.insert(instance=car_import_record)

            # pipeline
            pipeline_id = None
            opportunity_import_csv_fields = None
            if StdObjectIdentifiers.pipeline in object_id_or_names:
                info_currently_processing.set_entity_type_and_clear_detail_fields(
                    ImportEntityType.PIPELINE
                )
                pipeline_import_record: DbImportRecord | None = None
                if account_id:
                    opportunity_import_csv_fields = (
                        self._get_first_csv_import_entity_by_type(
                            new_record_csv_row.all_entities or [],
                            OpportunityImportCsvFields,
                        )
                    )
                    custom_object_dto_matches = [
                        co_dto
                        for co_dto in custom_object_dtos
                        if co_dto.custom_object.parent_object_name
                        == ExtendableStandardObject.pipeline
                    ]
                    if (
                        opportunity_import_csv_fields and custom_object_dto_matches
                    ):  # Handle custom fields
                        custom_object_dto = custom_object_dto_matches[0]
                        custom_field_data_opportunity: dict[
                            uuid.UUID, FieldValueOrAny
                        ] = await self.map_custom_field_data(
                            organization_id=custom_object_dto.organization_id,
                            custom_object_dto=custom_object_dto,
                            model_extra=opportunity_import_csv_fields.model_extra or {},
                            local_timezone=local_timezone,
                            info_currently_processing=info_currently_processing,
                        )
                        if (
                            custom_field_data_opportunity
                            and opportunity_import_csv_fields.model_extra
                        ):
                            opportunity_import_csv_fields.model_extra[
                                CUSTOM_FIELD_DATA
                            ] = custom_field_data_opportunity
                            opportunity_import_csv_fields.model_extra[CSV_TYPE] = (
                                StdObjectIdentifiers.pipeline
                            )

                    self.logger.bind(
                        opportunity_import_csv_fields=opportunity_import_csv_fields
                    ).info(
                        "[CrmSyncService] mapped_fields after _map_pipeline_field_values"
                    )
                    if isinstance(
                        opportunity_import_csv_fields, OpportunityImportCsvFields
                    ):
                        info_currently_processing.set_entity_type_and_clear_detail_fields(
                            ImportEntityType.PIPELINE
                        )
                        pipeline_owner_user_id = await self.get_owner_user_id_v2(
                            organization_id=csv_import_job.organization_id,
                            default_user_id=not_none(csv_import_job.created_by_user_id),
                            user_email=opportunity_import_csv_fields.opportunity_owner_email,
                        )
                        # TODO move contact out to StdObjectIdentifiers.contact_pipeline_role section
                        pipeline_import_record = await self._process_pipeline_import_row(
                            job_id=csv_import_job.id,
                            organization_id=csv_import_job.organization_id,
                            user_id=not_none(csv_import_job.created_by_user_id),
                            opportunity_import_csv_fields=opportunity_import_csv_fields,
                            local_timezone=local_timezone,
                            owner_user_id=pipeline_owner_user_id,
                            reference_id=str(row_number),
                            account_id=account_id,
                            create_contact_pipeline_role=False,
                            upsert_mode=(
                                csv_import_job.configuration.object_import_mode
                                == ObjectImportMode.UPSERT
                            )
                            if csv_import_job.configuration
                            else False,
                            pipeline_select_list_id=csv_import_job.metadata.pipeline_select_list_id
                            if csv_import_job.metadata
                            else None,
                        )
                        custom_metric.increment(
                            ImportMetricsName.IMPORT_PIPELINE,
                            tags=[pipeline_import_record.status],
                        )
                        pipeline_id = (
                            pipeline_import_record.entity_id
                        )  # possibly None for failed records

                else:  # no account_id
                    self.logger.bind(
                        csv_import_job=csv_import_job,
                    ).error(
                        "[CrmSyncService] import pipeline but no required account_id."
                    )
                    pipeline_import_record = DbImportRecord(
                        id=uuid.uuid4(),
                        import_entity_type=ImportEntityType.PIPELINE,
                        organization_id=csv_import_job.organization_id,
                        job_id=csv_import_job.id,
                        row_reference_id=str(row_number),
                        status=ImportRecordStatus.VALIDATION_ERROR,
                        status_detail="No Account present in the row, required for opportunity import",
                        entity_id=None,
                        created_at=datetime.now(pytz.utc),
                        existing_record=None,
                        conflicting_record=None,
                        external_id=None,
                    )
                if not pipeline_import_record:
                    raise ValueError(
                        "[CrmSyncService] Violated Invariant: pipeline_import_record must NEVER be None. Developer Error."
                    )
                await self.import_repository.insert(instance=pipeline_import_record)

            # contact_pipeline_role
            if StdObjectIdentifiers.contact_pipeline_role in object_id_or_names:
                info_currently_processing.set_entity_type_and_clear_detail_fields(
                    ImportEntityType.CONTACT_PIPELINE_ROLE
                )
                if not (contact_id and pipeline_id):
                    self.logger.bind(
                        csv_import_job=csv_import_job,
                        contact_id=contact_id,
                        pipeline_id=pipeline_id,
                    ).error(
                        "[CrmSyncService] import contact_pipeline_role but invalid contact_id or pipeline_id."
                    )
                    raise ResourceNotFoundError(
                        "Cannot find valid pipeline or contact to associate."
                    )
                cor_import_record: DbImportRecord | None = (
                    None  # contact_opportunity_role_import_record
                )
                if contact_id and pipeline_id:
                    cor_import_csv_fields = self._get_first_csv_import_entity_by_type(
                        new_record_csv_row.all_entities or [],
                        ContactOpportunityRoleImportCsvFields,
                    )
                    if cor_import_csv_fields is None:
                        # Contact Opportunity Role has no required fields, creating an empty
                        #   instance if it is not present.
                        cor_import_csv_fields = ContactOpportunityRoleImportCsvFields(
                            contact_opportunity_note=None,
                            contact_opportunity_role_types=None,
                        )
                    if isinstance(
                        cor_import_csv_fields, ContactOpportunityRoleImportCsvFields
                    ):  # This if-statement to satisfy the type checker, but unnecessary.
                        cor_import_record = (
                            await self._process_contact_pipeline_role_row(
                                job=csv_import_job,
                                pipeline_id=pipeline_id,
                                contact_id=contact_id,
                                reference_id=str(row_number),
                                cor_import_csv_fields=not_none(cor_import_csv_fields),
                            )
                        )
                        custom_metric.increment(
                            ImportMetricsName.IMPORT_CONTACT_PIPELINE_ROLE,
                            tags=[cor_import_record.status],
                        )
                    else:
                        raise ValueError(
                            "[CrmSyncService] Violated Invariant: cor_import_csv_fields must be ContactOpportunityRoleImportCsvFields. Developer Error."
                        )
                elif (
                    contact_id or pipeline_id or account_id
                ):  # multi-object import with skip
                    # Something got specified, so with multi-object import, mark this as skip rather than error.
                    # In multi-object import, allow stand-alone contact or account
                    if contact_id:
                        status_detail = "Contact is present but Pipeline is not."
                        entity_id = contact_id
                    elif pipeline_id:
                        status_detail = "Pipeline is present but Contact is not."
                        entity_id = pipeline_id
                    elif account_id:
                        status_detail = "Both Contact and Pipeline are missing but Account is present."
                        entity_id = account_id
                    else:
                        raise ValueError(
                            "[CrmSyncService] Violated Invariant: No Contact and no Pipeline and no Account. Developer Error."
                        )
                    self.logger.bind(
                        csv_import_job=csv_import_job,
                        contact_id=contact_id,
                        pipeline_id=pipeline_id,
                        account_id=account_id,
                    ).info(
                        f"[CrmSyncService] import contact_pipeline_role: {status_detail}. Skipping."
                    )
                    cor_import_record = DbImportRecord(
                        id=uuid.uuid4(),
                        import_entity_type=ImportEntityType.CONTACT_PIPELINE_ROLE,
                        organization_id=csv_import_job.organization_id,
                        job_id=csv_import_job.id,
                        row_reference_id=str(row_number),
                        status=ImportRecordStatus.SKIPPED,
                        status_detail=status_detail,
                        entity_id=entity_id,
                        created_at=datetime.now(pytz.utc),
                        existing_record=None,
                        conflicting_record=None,
                        external_id=None,
                    )
                else:  # nothing was specified, mark this as validation error.
                    self.logger.bind(
                        csv_import_job=csv_import_job,
                    ).error(
                        "[CrmSyncService] import contact_pipeline_role but no contact_id and no pipeline_id."
                    )
                    cor_import_record = DbImportRecord(
                        id=uuid.uuid4(),
                        import_entity_type=ImportEntityType.CONTACT_PIPELINE_ROLE,
                        organization_id=csv_import_job.organization_id,
                        job_id=csv_import_job.id,
                        row_reference_id=str(row_number),
                        status=ImportRecordStatus.VALIDATION_ERROR,
                        status_detail="No Reevo objects (Contact, Opportunity, Account) present in the row",
                        entity_id=None,
                        created_at=datetime.now(pytz.utc),
                        existing_record=None,
                        conflicting_record=None,
                        external_id=None,
                    )
                if not cor_import_record:
                    raise ValueError(
                        "[CrmSyncService] Violated Invariant: cor_import_record must NEVER be None. Developer Error."
                    )
                await self.import_repository.insert(instance=cor_import_record)

            self.logger.bind(
                account_id=account_id,
                contact_id=contact_id,
                pipeline_id=pipeline_id,
            ).info("[CrmSyncService] created standard object ids")

            # custom objects processing
            info_currently_processing.set_entity_type_and_clear_detail_fields(
                ImportEntityType.CUSTOM_OBJECT
            )
            if custom_object_ids := await self._create_custom_objects_from_csv_import(
                csv_import_job=csv_import_job,
                row_number=row_number,
                custom_object_dtos=custom_object_dtos,
                new_record_csv_row=new_record_csv_row,
                local_timezone=local_timezone,
                info_currently_processing=info_currently_processing,
            ):
                self.logger.bind(custom_object_ids=custom_object_ids).info(
                    "[CrmSyncService] created custom object ids"
                )

                # relationship_ids - objects must be defined.
                info_currently_processing.set_entity_type_and_clear_detail_fields(
                    ImportEntityType.CUSTOM_OBJECT_ASSOCIATION
                )
                # Lookup relationship by ID
                association_records_created = (
                    await self._create_relationship_associations_from_csv_import(
                        csv_import_job,
                        new_record_csv_row,
                        account_id,
                        contact_id,
                        pipeline_id,
                        custom_object_ids,
                        info_currently_processing,
                    )
                )
                self.logger.bind(
                    association_records_created=association_records_created
                ).info("[CrmSyncService] created custom relationship associations")
        except Exception as e:
            # catchall for unexpected strange errors. Seek to reduce usage if this path is regularly hit.
            #   Remapping exception to preserve info_currently_processing.
            self.logger.bind(
                currently_processing=info_currently_processing,
            ).opt(
                exception=e,
            ).exception("[CrmSyncService] _process_row_v2 error processing CSV row.")
            raise self.ImportProcessingError(
                message=f"Error processing CSV row({row_number})",
                currently_processing=info_currently_processing,
                original_exception=e,
            ) from e

    async def _create_custom_objects_from_csv_import(  # noqa: C901
        self,
        csv_import_job: ImportJob,
        row_number: int,
        custom_object_dtos: list[CustomObjectDto],
        new_record_csv_row: CsvRow,
        local_timezone: TimeZoneName | None,
        info_currently_processing: CurrentlyProcessing,
    ) -> dict[str, uuid.UUID]:
        custom_object_ids: dict[str, uuid.UUID] = {}
        custom_object_identifier: CustomObjectIdentifier | None = None
        if csv_import_job.configuration is not None:
            for object_identifier in (
                csv_import_job.configuration.object_identifiers or []
            ):
                if object_identifier.object_kind == ObjectKind.CUSTOM:
                    custom_object_identifier = object_identifier
                    break  # getting first custom object
        if custom_object_identifier is None:
            logger.info("No custom objects mapped")
            return custom_object_ids
        for entity in new_record_csv_row.all_entities or []:
            if (
                isinstance(entity, CustomObjectImportCsvFields)
                and entity.custom_object_display_name
            ):
                info_currently_processing.set_entity_type_and_clear_detail_fields(
                    ImportEntityType.CUSTOM_OBJECT
                )
                info_currently_processing.custom_object_name = (
                    entity.custom_object_display_name
                )
                custom_object_dto_arr = [
                    co_dto
                    for co_dto in custom_object_dtos
                    if co_dto.custom_object.id == custom_object_identifier.object_id
                ]
                is_custom_object_created = False
                if custom_object_dto_arr:
                    custom_object_dto = custom_object_dto_arr[0]
                    custom_field_data: dict[
                        uuid.UUID, FieldValueOrAny
                    ] = await self.map_custom_field_data(
                        organization_id=custom_object_identifier.organization_id,
                        custom_object_dto=custom_object_dto,
                        model_extra=entity.model_extra or {},
                        local_timezone=local_timezone,
                        info_currently_processing=info_currently_processing,
                    )
                    if custom_field_data and entity.model_extra:
                        entity.model_extra[CUSTOM_FIELD_DATA] = custom_field_data
                        entity.model_extra[CSV_TYPE] = "custom_object"
                    import_record = await self._process_custom_object_import_row(
                        job=None,
                        import_job=csv_import_job,
                        reference_id=str(row_number),
                        mapped_fields=entity,
                        custom_object_dto=custom_object_dto,
                    )
                    if import_record:
                        custom_metric.increment(
                            ImportMetricsName.IMPORT_CUSTOM_OBJECT,
                            tags=[import_record.status],
                        )
                        await self.import_repository.insert(instance=import_record)
                        if import_record.entity_id:
                            custom_object_ids[entity.custom_object_display_name] = (
                                import_record.entity_id
                            )
                            is_custom_object_created = True
                if not is_custom_object_created:
                    self.logger.error(
                        f"Failed to find or create custom object for ({entity.custom_object_display_name})."
                    )

        return custom_object_ids

    async def _create_relationship_associations_from_csv_import(
        self,
        csv_import_job: ImportJob,
        new_record_csv_row: CsvRow,
        account_id: uuid.UUID | None,
        contact_id: uuid.UUID | None,
        pipeline_id: uuid.UUID | None,
        custom_object_ids: dict[str, uuid.UUID],
        info_currently_processing: CurrentlyProcessing,
    ) -> list[uuid.UUID]:
        association_records_to_return: list[uuid.UUID] = []
        for relationship_id in new_record_csv_row.all_relationship_ids or []:
            if (
                relationship_id in AccountRelationship
                or relationship_id in ContactRelationship
                or relationship_id in PipelineRelationship
                or relationship_id in ContactAccountRoleRelationship
                or relationship_id in ContactPipelineRoleRelationship
                or relationship_id in CustomRecordRelationship
            ):  # Standard relationships should already be handled in the course of standard object creation.
                continue

            info_currently_processing.custom_object_name = relationship_id
            try:
                custom_object_association: CustomObjectAssociation = (
                    await self.association_service.get_association_by_id(
                        organization_id=csv_import_job.organization_id,
                        association_id=uuid.UUID(relationship_id),
                    )
                )
                source_entity_match: uuid.UUID | None = None
                for entity in new_record_csv_row.all_entities or []:
                    source_entity_match = self._get_entity_match(
                        entity=entity,
                        object_identifier=custom_object_association.source_object_identifier,
                        account_id=account_id,
                        contact_id=contact_id,
                        pipeline_id=pipeline_id,
                        custom_object_ids=custom_object_ids,
                    )
                    if source_entity_match:
                        info_currently_processing.association_source = str(
                            source_entity_match
                        )
                        break

                target_entity_match: uuid.UUID | None = None
                for entity in reversed(new_record_csv_row.all_entities or []):
                    # search for target in revrse order. doesn't fix any real issues but auto handles some minor edge
                    # cases. Perhaps we can institute some rule where target must be after source in csv column
                    # position, and check from source index + 1. For now, this is good enough.
                    target_entity_match = self._get_entity_match(
                        entity=entity,
                        object_identifier=custom_object_association.target_object_identifier,
                        account_id=account_id,
                        contact_id=contact_id,
                        pipeline_id=pipeline_id,
                        custom_object_ids=custom_object_ids,
                    )
                    if target_entity_match:
                        info_currently_processing.association_target = str(
                            target_entity_match
                        )
                        break

                if source_entity_match and target_entity_match:
                    association_record = (
                        await self.association_service.create_association_record(
                            organization_id=csv_import_job.organization_id,
                            user_id=not_none(csv_import_job.created_by_user_id),
                            association=custom_object_association,
                            source_record_id=source_entity_match,
                            target_record_id=target_entity_match,
                        )
                    )
                    await self.import_repository.insert(
                        instance=DbImportRecord(
                            id=uuid.uuid4(),
                            import_entity_type=ImportEntityType.CUSTOM_OBJECT_ASSOCIATION,
                            organization_id=csv_import_job.organization_id,
                            created_at=association_record.created_at,
                            job_id=csv_import_job.id,
                            row_reference_id=str(new_record_csv_row.src_line_num),
                            status=ImportRecordStatus.SUCCESS,
                            status_detail=None,
                            entity_id=association_record.id,
                            existing_record=association_record.model_dump(),
                            conflicting_record=None,
                            external_id=None,
                        )
                    )
                    custom_metric.increment(
                        ImportMetricsName.IMPORT_CUSTOM_OBJECT_ASSOCIATION
                    )
                    association_records_to_return.append(association_record.id)
            except ResourceNotFoundError:
                self.logger.bind(
                    relationship_id=relationship_id,
                    organization_id=csv_import_job.organization_id,
                ).error("[CrmSyncService] relationship_id not found")
                raise ResourceNotFoundError(
                    f"Relationship ID not found: {relationship_id}"
                )
        return association_records_to_return

    def _get_entity_match(
        self,
        entity: BaseModel,
        object_identifier: ObjectIdentifierCommon,
        account_id: uuid.UUID | None = None,
        contact_id: uuid.UUID | None = None,
        pipeline_id: uuid.UUID | None = None,
        custom_object_ids: dict[str, uuid.UUID] | None = None,
    ) -> uuid.UUID | None:
        """The entities should've already been created, because relationships/associations
        are created after entities are created"""
        if object_identifier.object_kind == ObjectKind.STANDARD:
            object_name_upper = object_identifier.object_name.strip().lower()
            match object_name_upper:
                case "account":
                    if isinstance(entity, AccountImportCsvFields):
                        return account_id
                case "contact":
                    if isinstance(entity, ContactImportCsvFields):
                        return contact_id
                case "opportunity" | "pipeline":
                    if isinstance(entity, OpportunityImportCsvFields):
                        return pipeline_id
                case _:
                    self.logger.error(
                        f"unrecognized standard object ({object_name_upper})"
                    )
        elif object_identifier.object_kind == ObjectKind.CUSTOM:
            if (
                isinstance(entity, CustomObjectImportCsvFields)
                and entity.custom_object_display_name
                and custom_object_ids
            ):
                return custom_object_ids.get(entity.custom_object_display_name)
        else:
            self.logger.error(
                f"Should NEVER happen. Unhandled object_kind({object_identifier.object_kind})"
            )
        return None

    def _build_full_contact_pipeline_association_requests(
        self,
        contact_ids: list[uuid.UUID],
    ) -> FullContactPipelineAssociationRequests | None:
        if not contact_ids:
            return None
        primary_contact_id = contact_ids[0]
        additional_contact_ids = contact_ids[1:]
        primary_request = ContactPipelineAssociationRequest(
            contact_id=primary_contact_id
        )
        additional_requests = [
            ContactPipelineAssociationRequest(contact_id=contact_id)
            for contact_id in additional_contact_ids
        ]
        return FullContactPipelineAssociationRequests(
            primary=primary_request,
            additional=additional_requests if additional_requests else None,
        )

    async def _create_pipeline(  # noqa:C901, PLR0912
        self,
        opportunity_import_csv_fields: OpportunityImportCsvFields,
        user_id: uuid.UUID,
        account_id: uuid.UUID,
        organization_id: uuid.UUID,
        local_timezone: TimeZoneName | None,
        owner_user_id: uuid.UUID,
        create_contact_pipeline_role: bool = True,
        pipeline_select_list_id: uuid.UUID | None = None,
    ) -> PipelineV2:
        pipeline_display_name = opportunity_import_csv_fields.opportunity_display_name
        if not pipeline_display_name:
            raise ValueError("Import Opportunity missing display_name")

        contact_pipeline_associations = None
        if create_contact_pipeline_role:
            contact_ids: list[uuid.UUID] = []
            # get contact_id
            if (
                primary_contact_email
                := opportunity_import_csv_fields.contact_primary_email
            ):
                contacts = await self.contact_service.contact_query_service.find_by_primary_emails(
                    organization_id=organization_id,
                    primary_emails=[primary_contact_email],
                )
                contact_ids.extend([contact.id for contact in contacts])
            provided_contact_ids = (
                opportunity_import_csv_fields.model_extra.get("contact_ids")
                if opportunity_import_csv_fields.model_extra
                else []
            )
            if provided_contact_ids:
                contact_ids.extend([uuid_or_error(i) for i in provided_contact_ids])

            # if contact_ids not provided, and not contacts found by primary_contact_email, create contact by primary email and account_id
            if not contact_ids:
                contact_import_csv_fields = opportunity_import_csv_fields.to_associated_contact_import_csv_fields()
                created_contact, status, status_detail = await self._create_contact(
                    user_id=user_id,
                    owner_user_id=owner_user_id,
                    organization_id=organization_id,
                    contact_stage_id=await self.contact_service.get_default_stage_id_for_contact(
                        organization_id
                    ),
                    contact_import_csv_fields=contact_import_csv_fields,
                    local_timezone=local_timezone,
                    primary_account_id=account_id,
                )
                if created_contact:
                    contact_ids.append(created_contact.id)
                else:
                    self.logger.bind(
                        opportunity_import_csv_fields=opportunity_import_csv_fields,
                        status=status,
                        status_detail=status_detail,
                    ).error(
                        "[_find_or_create_pipeline] no contact found or created, pipeline failed import."
                    )
                    raise ImportEntityDependencyNotFoundError(
                        entity_type="contact",
                        message="Must have valid contact to create pipeline.",
                    )
            # build contact_pipeline_associations
            contact_pipeline_associations = (
                self._build_full_contact_pipeline_association_requests(
                    contact_ids=contact_ids
                )
            )
            # need to validate and create contact account associations
            contact_account_map = await self.contact_service.contact_repository.map_active_contact_account_association_by_contact_ids(
                organization_id=organization_id, contact_ids=set(contact_ids)
            )
            for contact_id, c_a_associations in contact_account_map.items():
                if account_id not in [c_a_a.account_id for c_a_a in c_a_associations]:
                    await self.contact_service.upsert_contact_account_association(
                        organization_id=organization_id,
                        user_id=user_id,
                        contact_id=contact_id,
                        req=UpsertContactAccountRoleRequest(
                            account_id=account_id,
                            is_primary=True,
                            title=opportunity_import_csv_fields.model_extra.get(
                                "contact_job_title"
                            )
                            if opportunity_import_csv_fields.model_extra is not None
                            else None,
                            department=opportunity_import_csv_fields.model_extra.get(
                                "contact_department"
                            )
                            if opportunity_import_csv_fields.model_extra is not None
                            else None,
                        ),
                    )
        # need to find stage_pipeline_id by name and is_default or not
        pipeline_stage_slv_id = cast_uuid_or_none(
            opportunity_import_csv_fields.model_extra.get(
                "pipeline_stage_select_list_value_id"
            )
            if opportunity_import_csv_fields.model_extra is not None
            else None
        )

        if not pipeline_stage_slv_id:
            pipeline_stage_slv_id = await self._find_pipeline_stage_slv_id(
                organization_id=organization_id,
                pipeline_id=pipeline_select_list_id,
                pipeline_name=opportunity_import_csv_fields.opportunity_pipeline_id,
                pipeline_stage_select_list_value=opportunity_import_csv_fields.opportunity_stage_value,
            )

        # Process closed reasons if they exist
        closed_reason_ids: list[uuid.UUID] = []
        if closed_reasons := none_or_split(
            opportunity_import_csv_fields.opportunity_closed_reasons
        ):
            # Get pipeline stage and its select lists
            pipeline_stage_reference = (
                await self.pipeline_stage_service.validate_pipeline_stage_reference(
                    organization_id=organization_id,
                    pipeline_stage_select_list_value_id=pipeline_stage_slv_id,
                )
            )

            pipeline_stage = (
                await self.pipeline_stage_service.get_pipeline_stage_select_list_dto(
                    select_list_id=pipeline_stage_reference.select_list_id,
                    organization_id=organization_id,
                )
            )

            # Get the relevant select list IDs from metadata
            stage_metadata = pipeline_stage.select_list_dto.list_metadata
            select_list_ids = set()

            # Only include IDs that are not None
            if win_reason_id := stage_metadata.closed_win_reason_select_list_id:
                select_list_ids.add(win_reason_id)
            if lost_reason_id := stage_metadata.closed_lost_reason_select_list_id:
                select_list_ids.add(lost_reason_id)

            # If we have valid select lists, fetch their values
            if select_list_ids:
                # Get mapping of select list values
                reason_select_lists = await self.select_list_service.map_select_list_value_by_select_list_ids(
                    organization_id=organization_id,
                    select_list_ids=select_list_ids,
                )

                # Find matching reason values and collect their IDs
                for select_list_values in reason_select_lists.values():
                    for reason_value in select_list_values:
                        # If any of the closed reasons match this value, add its ID
                        if any(
                            eq_str_value(reason, reason_value.display_value)
                            for reason in closed_reasons
                        ):
                            closed_reason_ids.append(reason_value.id)

        self.logger.bind(
            account_id=account_id,
            contact_pipeline_associations=contact_pipeline_associations,
            pipeline_stage_slv_id=pipeline_stage_slv_id,
            opportunity_import_csv_fields=opportunity_import_csv_fields,
            closed_reason_ids=closed_reason_ids,
            closed_reasons=closed_reasons,
        ).info("[_find_or_create_pipeline] creating pipeline.")

        # Check if there's custom field data
        if opportunity_import_csv_fields.model_extra:
            custom_field_data = opportunity_import_csv_fields.model_extra.get(
                CUSTOM_FIELD_DATA
            )
        else:
            custom_field_data = None

        return await self.pipeline_service.create_pipeline(
            organization_id=organization_id,
            user_id=user_id,
            req=CreatePipelineRequest(
                display_name=pipeline_display_name,
                amount=cast_decimal_or_none(
                    opportunity_import_csv_fields.opportunity_deal_amount
                ),
                account_id=account_id,
                type_id=None,
                stage_id=pipeline_stage_slv_id,
                owner_user_id=owner_user_id,
                next_step_details=opportunity_import_csv_fields.opportunity_next_step_details,
                next_step_due_at=cast_datetime_or_none(
                    opportunity_import_csv_fields.opportunity_next_step_due_at,
                    local_timezone,
                ),
                anticipated_closing_at=cast_datetime_or_none(
                    opportunity_import_csv_fields.opportunity_anticipated_closing_at,
                    local_timezone,
                ),
                expires_at=cast_datetime_or_none(
                    opportunity_import_csv_fields.opportunity_expires_at,
                    local_timezone,
                ),
                contact_pipeline_associations=contact_pipeline_associations,
                custom_field_data=custom_field_data,
                created_at=cast_datetime_or_none(
                    opportunity_import_csv_fields.opportunity_created_at,
                    local_timezone,
                ),
                closed_at=cast_datetime_or_none(
                    opportunity_import_csv_fields.opportunity_closed_at,
                    local_timezone,
                ),
                closed_reason_select_list_value_ids=closed_reason_ids or None,
                closed_reason_custom_detail=opportunity_import_csv_fields.opportunity_closed_reason_custom_details,
                created_source=CreatedSource.CSV_IMPORT,
            ),
        )

    # see https://docs.pydantic.dev/latest/errors/errors/
    def _has_address_no_field_defined_error(
        self, validation_error: ValidationError
    ) -> bool:
        for err in validation_error.errors():
            if (
                (err_ctx := err.get("ctx"))
                and (err_ctx_error := err_ctx.get("error"))
                and isinstance(err_ctx_error, AddressNoFieldDefinedError)
            ):
                return True
        return False

    def _create_address_obj_or_none(
        self,
        street_one: str | None,
        street_two: str | None,
        zip_code: str | None,
        city: str | None,
        state: str | None,
        country: str | None,
    ) -> Address | None:
        try:
            return Address(
                street_one=street_one,
                street_two=street_two,
                zip_code=zip_code,
                city=city,
                state=state,
                country=country,
            )
        except ValidationError as validation_error:
            if self._has_address_no_field_defined_error(validation_error):
                return None
            raise

    def _create_address_create_request_or_none(
        self,
        street_one: str | None,
        street_two: str | None,
        zip_code: str | None,
        city: str | None,
        state: str | None,
        country: str | None,
        created_by_user_id: uuid.UUID,
        organization_id: uuid.UUID,
    ) -> AddressCreateRequest | None:
        try:
            return AddressCreateRequest(
                street_one=street_one,
                street_two=street_two,
                zip_code=zip_code,
                city=city,
                state=state,
                country=country,
                created_by_user_id=created_by_user_id,
                organization_id=organization_id,
            )
        except ValidationError as validation_error:
            if self._has_address_no_field_defined_error(validation_error):
                return None
            raise

    async def find_or_create_account(
        self,
        account_import_csv_fields: AccountImportCsvFields,
        user_id: uuid.UUID,
        organization_id: uuid.UUID,
        local_timezone: TimeZoneName | None,
        owner_user_id: uuid.UUID | None,
    ) -> tuple[AccountV2, bool]:
        """
        Find or creates an account.
        Returns a tuple of:
        - AccountV2: The account that was found or created
        - bool (is_newly_created): True if the account was newly created, False if it was found
        """
        account_unique_identifier = account_import_csv_fields.unique_identifier()
        if not account_unique_identifier:
            raise ImportEntityDependencyNotFoundError(
                entity_type="account",
                message=f"Must has account_unique_identifier to create/get account: {account_import_csv_fields}",
            )
        self.logger.bind(
            account_unique_identifier=account_unique_identifier,
            account_import_csv_fields=account_import_csv_fields.model_dump_json(),
        ).info("[CrmSyncService] [find_or_create_account], request value map.")
        existing_account = one_row_or_none(
            await self.account_service.account_repository._find_by_column_values(  # noqa: SLF001
                DbAccount,
                organization_id=organization_id,
                domain_name=account_unique_identifier,
                exclude_deleted_or_archived=False,
            )
        )
        if existing_account:
            self.logger.bind(
                existing_account=existing_account,
                account_unique_identifier=account_unique_identifier,
            ).info("[CrmSyncService] [find_or_create_account], found existing account.")
            if existing_account.archived_at:
                await self.account_service.reactivate_by_id(
                    user_id=user_id,
                    organization_id=organization_id,
                    account_id=existing_account.id,
                )
                self.logger.bind(account_id=existing_account.id).info(
                    "[CrmSyncService] [find_or_create_account] Reactivated archived account."
                )
            return await self.account_service.get_account_v2(
                account_id=existing_account.id, organization_id=organization_id
            ), False
        self.logger.bind(
            account_unique_identifier=account_unique_identifier,
            account_import_csv_fields=account_import_csv_fields.model_dump_json(),
        ).info(
            "[CrmSyncService] [find_or_create_account], no existing account found, creating new account"
        )

        csv_type = (
            account_import_csv_fields.model_extra.get(CSV_TYPE)
            if account_import_csv_fields.model_extra
            else None
        )
        self.logger.info(
            f"[crm_sync_service] [find_or_create_account] csv_type: {csv_type}"
        )
        try:
            return await self.account_service.create_account_v2(
                organization_id=organization_id,
                user_id=user_id,
                create_account_request=CreateAccountRequest(
                    display_name=account_import_csv_fields.company_display_name
                    or "UNKNOWN",
                    owner_user_id=owner_user_id or user_id,
                    official_website=account_import_csv_fields.get_official_website(),
                    domain_name=account_unique_identifier,
                    description=account_import_csv_fields.company_description,
                    keyword_list=none_or_split(
                        account_import_csv_fields.model_extra.get("company_keywords")
                        if account_import_csv_fields.model_extra
                        else None,
                    ),
                    category_list=none_or_split(
                        account_import_csv_fields.company_industry
                    ),
                    technology_list=none_or_split(
                        account_import_csv_fields.company_technologies
                    ),
                    estimated_annual_revenue=cast_decimal_or_none(
                        account_import_csv_fields.company_estimated_annual_revenue
                    ),
                    estimated_employee_count=cast_int_or_none(
                        account_import_csv_fields.company_estimated_employee_count
                    ),
                    linkedin_url=strip_str_or_none(
                        account_import_csv_fields.company_linkedin_url
                    ),
                    facebook_url=strip_str_or_none(
                        account_import_csv_fields.company_facebook_url
                    ),
                    zoominfo_url=strip_str_or_none(
                        account_import_csv_fields.model_extra.get(
                            "company_zoominfo_url"
                        )
                    )
                    if account_import_csv_fields.model_extra
                    else None,
                    x_url=strip_str_or_none(account_import_csv_fields.company_x_url),
                    custom_field_data=account_import_csv_fields.model_extra.get(
                        CUSTOM_FIELD_DATA
                    )
                    if account_import_csv_fields.model_extra
                    else None,
                    address=self._create_address_obj_or_none(
                        street_one=strip_str_or_none(
                            account_import_csv_fields.model_extra.get(
                                "company_address_street_one"
                            )
                        ),
                        street_two=strip_str_or_none(
                            account_import_csv_fields.model_extra.get(
                                "company_address_street_two"
                            )
                        ),
                        zip_code=strip_str_or_none(
                            account_import_csv_fields.model_extra.get(
                                "company_address_zip_code"
                            )
                        ),
                        city=strip_str_or_none(
                            account_import_csv_fields.model_extra.get(
                                "company_address_city"
                            )
                        ),
                        state=strip_str_or_none(
                            account_import_csv_fields.model_extra.get(
                                "company_address_state"
                            )
                        ),
                        country=strip_str_or_none(
                            account_import_csv_fields.model_extra.get(
                                "company_address_country"
                            )
                        ),
                    )
                    if account_import_csv_fields.model_extra
                    else None,
                    status=AccountStatus(
                        account_import_csv_fields.company_status or AccountStatus.TARGET
                    )
                    or AccountStatus.TARGET,
                    created_at=cast_datetime_or_none(
                        account_import_csv_fields.company_created_at,
                        local_timezone,
                    ),
                    # todo: REEVO-1857
                    # create_account_lead=True,
                    created_source=CreatedSource.CSV_IMPORT,
                    csv_type=csv_type,
                ),
            ), True

        except ConflictResourceError as e:
            if e.additional_error_details and (
                account_id := cast_uuid_or_none(e.additional_error_details.reference_id)
            ):
                self.logger.bind(
                    field_values_map=account_import_csv_fields,
                    organization_id=organization_id,
                    account_id=account_id,
                ).info(
                    "_process_account_import_row self.account_service.create_account_v2 got ConflictResourceError."
                )
                return await self.account_service.get_account_v2(
                    account_id=account_id,
                    organization_id=organization_id,
                    include_custom_object=True,
                ), False
        raise ResourceNotFoundError("find_or_create_account failed.")

    async def _process_account_import_row(
        self,
        job_id: uuid.UUID,
        organization_id: uuid.UUID,
        user_id: uuid.UUID,
        account_import_csv_fields: AccountImportCsvFields,
        owner_user_id: uuid.UUID,
        # If is_required==true, account must be present and will mark as error
        #   if not found.  If is_required==false and no account identifier is
        #   specified, then instead of error, the record will be marked as
        #   SKIPPED.
        is_required: bool,
        local_timezone: TimeZoneName | None,
        reference_id: str | None = None,
        upsert_mode: bool = False,
    ) -> DbImportRecord:
        if owner_email := account_import_csv_fields.company_owner_email:
            owner_user_id = await self.get_owner_user_id_v2(
                organization_id=organization_id,
                default_user_id=owner_user_id,
                user_email=owner_email,
            )

        self.logger.bind(
            account_import_csv_fields=account_import_csv_fields.model_dump_json()
        ).info("_process_account_import_row started.")
        status = ImportRecordStatus.SUCCESS
        status_detail = None
        account = None
        new_created = True
        try:
            account, new_created = await self.find_or_create_account(
                user_id=user_id,
                organization_id=organization_id,
                account_import_csv_fields=account_import_csv_fields,
                local_timezone=local_timezone,
                owner_user_id=owner_user_id,
            )
        except ImportEntityDependencyNotFoundError as e:
            if is_required:
                status = ImportRecordStatus.VALIDATION_ERROR
                status_detail = "Account identifier not specified but is required."
                self.logger.bind(
                    exception=e,
                    status_detail=status_detail,
                    job_id=job_id,
                    reference_id=reference_id,
                ).warning(
                    f"[CrmSyncService] [_process_account_import_row], {status_detail}"
                )
            else:
                status = ImportRecordStatus.SKIPPED
                status_detail = (
                    "Account not required and identifier not specified. Skipping."
                )
                self.logger.bind(
                    exception=e, job_id=job_id, reference_id=reference_id
                ).info(
                    f"[CrmSyncService] [_process_account_import_row], {status_detail}"
                )
        except ValueError as e:
            status = ImportRecordStatus.VALIDATION_ERROR
            status_detail = f"Account validation failed: {e}"
            self.logger.bind(
                exception=e,
                status_detail=status_detail,
                job_id=job_id,
                reference_id=reference_id,
            ).warning(
                "[CrmSyncService] [_process_account_import_row], validation_error"
            )
        except Exception as e:
            status = ImportRecordStatus.FAILED
            status_detail = _get_except_msg(e)
            self.logger.bind(
                exception=e,
                account_import_csv_fields=account_import_csv_fields.model_dump_json(),
                job_id=job_id,
                reference_id=reference_id,
            ).opt(exception=e).exception(
                "[CrmSyncService] [_process_account_import_row], other exception."
            )

        # NOTICE that we are not comparing existing_record and incoming_record for conflict resolution flow.
        # Conflict will be determined by `new_created` flag
        # In import records, conflicting_record and existing_record won't have custom fields.
        # Re-visit these two columns when needed.
        existing_record = (
            ImportAccountRecord.from_account(account=account) if account else None
        )
        incoming_record = ImportAccountRecord.from_import_account_dict(
            account_import_csv_fields=account_import_csv_fields,
            owner_user_id=owner_user_id,
        )
        if not new_created and account is not None:
            self.logger.bind(
                db_account=existing_record, request_account=incoming_record
            ).warning("_process_account_import_row got conflict record.")
            status = ImportRecordStatus.CONFLICT
            status_detail = "Account already exists, please resolve conflicts."
            if upsert_mode:
                self.logger.bind(
                    db_account=existing_record,
                    request_account=incoming_record,
                ).info(
                    "Applying upsert mode to update existing account with import data"
                )

                # Call update method to apply changes from import data to existing account
                updated_account = await self.update_account_from_import_fields(
                    organization_id=organization_id,
                    user_id=user_id,
                    existing_account=not_none(account),
                    account_import_csv_fields=account_import_csv_fields,
                    owner_user_id=owner_user_id,
                )

                # Update status to success if the update operation worked
                if updated_account:
                    status = ImportRecordStatus.SUCCESS
                    status_detail = "Account updated via upsert"
                    # Update existing_record with latest data
                    existing_record = ImportAccountRecord.from_account(
                        account=updated_account
                    )
                else:
                    self.logger.bind(
                        db_account=existing_record,
                        request_account=incoming_record,
                    ).error("Failed updating account for upsert mode")
        return DbImportRecord(
            id=uuid.uuid4(),
            import_entity_type=ImportEntityType.ACCOUNT,
            organization_id=organization_id,
            created_at=datetime.now(pytz.utc),
            job_id=job_id,
            row_reference_id=reference_id,
            status=status,
            entity_id=account.id if account else None,
            status_detail=status_detail,
            existing_record=json.loads(existing_record.model_dump_json())
            if existing_record
            else None,
            conflicting_record=json.loads(incoming_record.model_dump_json()),
            external_id=account_import_csv_fields.model_extra.get("external_id")
            if account_import_csv_fields.model_extra
            else None,
        )

    async def _process_contact_import_role_row(
        self,
        job: ImportJob,
        reference_id: str,
        account_id: uuid.UUID,
        contact_id: uuid.UUID,
        car_import_csv_fields: ContactAccountRoleImportCsvFields,
    ) -> DbImportRecord:
        status = ImportRecordStatus.SUCCESS
        status_detail = None
        contact_account_role = None
        try:
            existing_associations = await self.contact_query_service.list_active_contact_account_associations(
                organization_id=job.organization_id,
                contact_id=contact_id,
                account_id=account_id,
            )
            if len(existing_associations) >= 1:
                contact_account_role = existing_associations[0]
                if len(existing_associations) > 1:
                    self.logger.bind(
                        existing_associations=existing_associations, job=job
                    ).error(
                        "_process_contact_import_role_row found multiple existing_associations, default 1st one."
                    )

            # Check if we need to create/update the association
            if contact_account_role is None or (
                job.configuration
                and job.configuration.object_import_mode == ObjectImportMode.UPSERT
            ):
                # Either no existing association or we're in upsert mode
                created_contact_account_role_result = await self.contact_service.upsert_contact_account_association(
                    organization_id=job.organization_id,
                    user_id=not_none(job.created_by_user_id),
                    contact_id=contact_id,
                    req=UpsertContactAccountRoleRequest(
                        account_id=account_id,
                        is_primary=True,
                        title=car_import_csv_fields.contact_account_role_title,
                        department=car_import_csv_fields.contact_account_role_department,
                    ),
                )
                contact_account_role = created_contact_account_role_result.upserted
            # Check if existing contact_account_role matches the incoming fields
            elif (
                contact_account_role.title
                != car_import_csv_fields.contact_account_role_title
                or contact_account_role.department
                != car_import_csv_fields.contact_account_role_department
            ):
                status = ImportRecordStatus.CONFLICT
                status_detail = (
                    "Existing contact account role has different title or department"
                )
                self.logger.bind(
                    existing_role=contact_account_role,
                    incoming_title=car_import_csv_fields.contact_account_role_title,
                    incoming_department=car_import_csv_fields.contact_account_role_department,
                ).info("Contact account role conflict detected")

        except Exception as e:
            status = ImportRecordStatus.FAILED
            status_detail = _get_except_msg(e)
            self.logger.bind(
                exception=e, contact_import_csv_fields=car_import_csv_fields
            ).opt(exception=e).exception(
                "[CrmSyncService] [_process_contact_import_role_row], failed with exception."
            )

        return DbImportRecord(
            id=uuid.uuid4(),
            import_entity_type=ImportEntityType.CONTACT_ACCOUNT_ROLE,
            organization_id=job.organization_id,
            created_at=datetime.now(pytz.utc),
            job_id=job.id,
            row_reference_id=reference_id,
            status=status,
            entity_id=contact_account_role.id if contact_account_role else None,
            status_detail=status_detail,
            existing_record=json.loads(contact_account_role.model_dump_json())
            if contact_account_role
            else None,
        )

    async def _process_contact_pipeline_role_row(
        self,
        job: ImportJob,
        reference_id: str,
        pipeline_id: uuid.UUID,
        contact_id: uuid.UUID,
        cor_import_csv_fields: ContactOpportunityRoleImportCsvFields,
    ) -> DbImportRecord:
        status = ImportRecordStatus.SUCCESS
        status_detail = None
        contact_pipeline_role = None
        try:
            # First check for existing associations
            existing_association = (
                await self.pipeline_service.find_contact_pipeline_association(
                    organization_id=job.organization_id,
                    pipeline_id=pipeline_id,
                    contact_id=contact_id,
                )
            )

            if existing_association:
                contact_pipeline_role = existing_association

            # Check if we need to create/update the association
            if contact_pipeline_role is None or (
                job.configuration
                and job.configuration.object_import_mode == ObjectImportMode.UPSERT
            ):
                # Either no existing association or we're in upsert mode
                created_contact_pipeline_role_result = (
                    await self.pipeline_service.upsert_contact_pipeline_association(
                        organization_id=job.organization_id,
                        user_id=not_none(job.created_by_user_id),
                        pipeline_id=pipeline_id,
                        req=UpsertContactPipelineAssociationRequests(
                            primary=ContactPipelineAssociationRequest(
                                contact_id=contact_id,
                                note=cor_import_csv_fields.contact_opportunity_note,
                                role_types=self._populate_contact_pipeline_roles(
                                    cor_import_csv_fields.contact_opportunity_role_types
                                ),
                            ),
                        ),
                    )
                )
                contact_pipeline_role = created_contact_pipeline_role_result.upserted
            else:
                status = ImportRecordStatus.CONFLICT
                status_detail = "Found existing contact pipeline role"
                self.logger.bind(
                    existing_role=contact_pipeline_role,
                    incoming_note=cor_import_csv_fields.contact_opportunity_note,
                ).info("Contact pipeline role conflict detected")

        except Exception as e:
            status = ImportRecordStatus.FAILED
            status_detail = _get_except_msg(e)
            self.logger.bind(
                exception=e,
                cor_import_csv_fields=cor_import_csv_fields,
                pipeline_id=pipeline_id,
                contact_id=contact_id,
            ).opt(exception=e).exception(
                "[CrmSyncService] [_process_contact_pipeline_role_row], failed with exception."
            )

        return DbImportRecord(
            id=uuid.uuid4(),
            import_entity_type=ImportEntityType.CONTACT_PIPELINE_ROLE,
            organization_id=job.organization_id,
            created_at=datetime.now(pytz.utc),
            job_id=job.id,
            row_reference_id=reference_id,
            status=status,
            entity_id=contact_pipeline_role.id if contact_pipeline_role else None,
            status_detail=status_detail,
            existing_record=json.loads(contact_pipeline_role.model_dump_json())
            if contact_pipeline_role
            else None,
        )

    async def _process_contact_import_row_v2(
        self,
        job: ImportJob,
        reference_id: str,
        owner_user_id: uuid.UUID,
        contact_import_csv_fields: ContactImportCsvFields,
    ) -> DbImportRecord:
        self.logger.info(
            f"[CrmSyncService] [_process_contact_import_row_v2], mapped_fields: {contact_import_csv_fields}"
        )

        if owner_email := contact_import_csv_fields.contact_owner_email:
            owner_user_id = await self.get_owner_user_id_v2(
                organization_id=job.organization_id,
                default_user_id=owner_user_id,
                user_email=owner_email,
            )

        contact_stage_id = await self.contact_service.get_default_stage_id_for_contact(
            job.organization_id
        )
        self.logger.bind(contact_stage_id=contact_stage_id).info(
            "[CrmSyncService] [_process_contact_import_row], default contact_stage_id"
        )
        if contact_stage_value := contact_import_csv_fields.contact_stage_value:  # noqa: SIM102
            if (
                contact_stage_dto
                := await self.contact_service.select_list_service.find_default_select_list_dto_by_application_code_name(
                    organization_id=job.organization_id,
                    application_code_name=StdSelectListIdentifier.contact_stage,
                )
            ):
                for select_list_value in contact_stage_dto.select_list_values:
                    if not select_list_value.deactivated_at and eq_str_value(
                        select_list_value.display_value, contact_stage_value
                    ):
                        self.logger.bind(contact_stage_value=contact_stage_value).info(
                            "[CrmSyncService] [_process_contact_import_row], contact_stage_id found "
                        )
                        contact_stage_id = select_list_value.id
        created_contact, status, status_detail = await self._create_contact(
            user_id=not_none(job.created_by_user_id),
            owner_user_id=owner_user_id,
            organization_id=job.organization_id,
            contact_stage_id=contact_stage_id,
            contact_import_csv_fields=contact_import_csv_fields,
            local_timezone=job.configuration.timezone if job.configuration else None,
            primary_account_id=None,
            upsert_mode=(
                job.configuration.object_import_mode == ObjectImportMode.UPSERT
            )
            if job.configuration
            else False,
        )
        return DbImportRecord(
            id=uuid.uuid4(),
            import_entity_type=ImportEntityType.CONTACT,
            organization_id=job.organization_id,
            created_at=datetime.now(pytz.utc),
            job_id=job.id,
            row_reference_id=reference_id,
            status=status,
            entity_id=created_contact.id if created_contact else None,
            status_detail=status_detail,
            existing_record=json.loads(
                ImportContactRecord.from_domain_contact(
                    contact_response=created_contact
                ).model_dump_json()
            )
            if created_contact
            else None,
            conflicting_record=json.loads(
                ImportContactRecord.from_import_dict(
                    contact_import_csv_fields=contact_import_csv_fields,
                    user_id=not_none(job.created_by_user_id),
                    owner_user_id=owner_user_id,
                    stage_id=contact_stage_id,
                ).model_dump_json()
            ),
            external_id=contact_import_csv_fields.model_extra.get("external_id")
            if contact_import_csv_fields.model_extra
            else None,
        )

    async def _process_contact_import_row(
        self,
        job_id: uuid.UUID,
        organization_id: uuid.UUID,
        user_id: uuid.UUID,
        contact_import_csv_fields: ContactImportCsvFields,  # dict[str, str],
        local_timezone: TimeZoneName | None,
        owner_user_id: uuid.UUID,
        reference_id: str | None = None,
        account_id: uuid.UUID | None = None,
    ) -> DbImportRecord:
        self.logger.info(
            f"[CrmSyncService] [_process_contact_import_row], mapped_fields: {contact_import_csv_fields}"
        )
        primary_account_id = (
            cast_uuid_or_none(
                contact_import_csv_fields.model_extra.get("account_id")
                if contact_import_csv_fields.model_extra is not None
                else None
            )
            or account_id
        )
        if not primary_account_id:
            account_import_csv_fields = (
                contact_import_csv_fields.to_associated_account_import_csv_fields()
            )
            account, _ = (
                await self.find_or_create_account(
                    account_import_csv_fields=account_import_csv_fields,
                    organization_id=organization_id,
                    user_id=user_id,
                    local_timezone=local_timezone,
                    owner_user_id=owner_user_id,
                )
                if account_import_csv_fields.company_display_name
                or account_import_csv_fields.company_domain_name
                else (None, False)
            )
            primary_account_id = account.id if account else None
        self.logger.bind(primary_account_id=primary_account_id).info(
            "[CrmSyncService] [_process_contact_import_row], primary_account_id set"
        )
        contact_stage_id = await self.contact_service.get_default_stage_id_for_contact(
            organization_id
        )
        self.logger.bind(contact_stage_id=contact_stage_id).info(
            "[CrmSyncService] [_process_contact_import_row], default contact_stage_id"
        )
        if contact_stage_value := contact_import_csv_fields.contact_stage_value:  # noqa: SIM102
            if (
                contact_stage_dto
                := await self.contact_service.select_list_service.find_default_select_list_dto_by_application_code_name(
                    organization_id=organization_id,
                    application_code_name=StdSelectListIdentifier.contact_stage,
                )
            ):
                for select_list_value in contact_stage_dto.select_list_values:
                    if not select_list_value.deactivated_at and eq_str_value(
                        select_list_value.display_value, contact_stage_value
                    ):
                        self.logger.bind(contact_stage_value=contact_stage_value).info(
                            "[CrmSyncService] [_process_contact_import_row], contact_stage_id found "
                        )
                        contact_stage_id = select_list_value.id
        created_contact, status, status_detail = await self._create_contact(
            user_id=user_id,
            owner_user_id=owner_user_id,
            organization_id=organization_id,
            contact_stage_id=contact_stage_id,
            contact_import_csv_fields=contact_import_csv_fields,
            local_timezone=local_timezone,
            primary_account_id=primary_account_id,
        )

        return DbImportRecord(
            id=uuid.uuid4(),
            import_entity_type=ImportEntityType.CONTACT,
            organization_id=organization_id,
            created_at=datetime.now(pytz.utc),
            job_id=job_id,
            row_reference_id=reference_id,
            status=status,
            entity_id=created_contact.id if created_contact else None,
            status_detail=status_detail,
            existing_record=json.loads(
                ImportContactRecord.from_domain_contact(
                    contact_response=created_contact
                ).model_dump_json()
            )
            if created_contact
            else None,
            conflicting_record=json.loads(
                ImportContactRecord.from_import_dict(
                    contact_import_csv_fields=contact_import_csv_fields,
                    user_id=user_id,
                    owner_user_id=owner_user_id,
                    stage_id=contact_stage_id,
                ).model_dump_json()
            ),
            external_id=contact_import_csv_fields.model_extra.get("external_id")
            if contact_import_csv_fields.model_extra
            else None,
        )

    async def _create_contact(
        self,
        user_id: uuid.UUID,
        owner_user_id: uuid.UUID,
        organization_id: uuid.UUID,
        contact_stage_id: uuid.UUID,
        contact_import_csv_fields: ContactImportCsvFields,  # dict[str, str],
        local_timezone: TimeZoneName | None,
        primary_account_id: uuid.UUID | None,
        upsert_mode: bool = False,
    ) -> tuple[ContactV2 | None, ImportRecordStatus, str | None]:
        created_contact = None
        status = ImportRecordStatus.SUCCESS
        status_detail = None
        create_contact_request = None
        try:
            create_contact_request = await self._build_contact_with_email_db_objects(
                user_id=user_id,
                owner_user_id=owner_user_id,
                organization_id=organization_id,
                stage_id=contact_stage_id,
                contact_import_csv_fields=contact_import_csv_fields,
                local_timezone=local_timezone,
                primary_account_id=primary_account_id,
            )
            self.logger.info(
                f"[CrmSyncService] [_process_contact_import_row], creating contact: {create_contact_request}"
            )
            created_contact = (
                await self.contact_service.create_contact_with_contact_channels(
                    organization_id=organization_id,
                    user_id=user_id,
                    create_contact_with_contact_channel_request=create_contact_request,
                )
            )
        except ValidationError as e:
            status = ImportRecordStatus.VALIDATION_ERROR
            status_detail = f"Validation failed: {e}"
            self.logger.bind(exception=e, status_detail=status_detail).warning(
                "[CrmSyncService] [_process_contact_import_row], validation_error"
            )
        except ConflictResourceError as e:
            status = ImportRecordStatus.CONFLICT
            status_detail = (
                e.additional_error_details.details
                if e.additional_error_details
                else None
            )
            if e.additional_error_details and e.additional_error_details.reference_id:  # noqa: SIM102
                if contact_id := uuid.UUID(e.additional_error_details.reference_id):
                    existing_contact = await self.contact_query_service.get_contact_v2(
                        contact_id=contact_id,
                        organization_id=organization_id,
                    )
                    if existing_contact.archived_at:
                        await self.contact_service.unarchive_contact(
                            organization_id=organization_id,
                            contact_id=existing_contact.id,
                            unarchived_by_user_id=user_id,
                        )
                        self.logger.bind(contact_id=existing_contact.id).info(
                            "[CrmSyncService] [_process_contact_import_row] reactivating archived contact."
                        )
                    if upsert_mode:
                        # In upsert mode, update the existing contact with new data
                        self.logger.bind(
                            existing_contact=existing_contact.id,
                            request=not_none(create_contact_request),
                        ).info(
                            "Applying upsert mode to update existing contact with import data"
                        )

                        try:
                            # Update contact with new data from import
                            updated_contact = (
                                await self.update_contact_from_import_fields(
                                    organization_id=organization_id,
                                    user_id=user_id,
                                    existing_contact=existing_contact,
                                    contact_import_csv_fields=contact_import_csv_fields,
                                )
                            )

                            status = ImportRecordStatus.SUCCESS
                            status_detail = "Contact updated via upsert"
                            created_contact = updated_contact
                        except Exception as update_error:
                            self.logger.bind(
                                existing_contact=existing_contact.id,
                            ).opt(exception=e).error(
                                "Failed updating contact for upsert mode"
                            )
                            status = ImportRecordStatus.FAILED
                            status_detail = f"Failed updating contact: {update_error!s}"

                    else:
                        created_contact = existing_contact
                    self.logger.info(
                        f"[CrmSyncService] [_process_contact_import_row], ConflictResourceError: {e!s}, existing_record: {existing_contact}"
                    )
        except Exception as e:
            status = ImportRecordStatus.FAILED
            status_detail = _get_except_msg(e)
            self.logger.info(
                f"[CrmSyncService] [_process_contact_import_row], creating contact, error: {status_detail}"
            )
        return created_contact, status, status_detail

    async def _process_task_import_row(
        self,
        job: Job,
        field_values_map: dict[str, str],
        owner_user_id: uuid.UUID,
        row_number: int | None = None,
    ) -> DbImportRecord:
        status = ImportRecordStatus.SUCCESS
        status_detail = None
        due_at = zoned_utc_now()
        if due_at_time_string := field_values_map.get("Activity date"):
            due_at = pytz.timezone("America/Los_Angeles").localize(
                datetime.strptime(due_at_time_string, "%Y-%m-%d %H:%M")  # noqa: DTZ007
            )
        contact_id = None
        account_id = None

        if contact_raw_string := field_values_map.get("Associated Contacts"):
            email_pattern = r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
            if match := re.search(email_pattern, contact_raw_string):
                contact_id_by_email = (
                    await self.contact_service.find_contact_ids_by_primary_emails(
                        organization_id=job.organization_id,
                        primary_emails={match[0]},
                    )
                )
                if match[0] in contact_id_by_email:
                    contact_id = contact_id_by_email[match[0]]

        if company_raw_string := field_values_map.get("Associated Companies"):
            account, _is_newly_created = await self.find_or_create_account(
                account_import_csv_fields=AccountImportCsvFields(
                    company_display_name=company_raw_string,
                    company_domain_name=None,
                    company_official_website=None,
                    company_description=None,
                    company_industry=None,
                    company_technologies=None,
                    company_estimated_annual_revenue=None,
                    company_estimated_employee_count=None,
                    company_linkedin_url=None,
                    company_facebook_url=None,
                    company_x_url=None,
                    company_owner_email=None,
                    company_created_at=None,
                    company_status=None,
                ),
                user_id=job.user_id,
                organization_id=job.organization_id,
                local_timezone=None,
                owner_user_id=owner_user_id,
            )
            account_id = account.id
        create_task = await self.task_v2_service.insert_task_v2(
            created_by_user_id=job.user_id,
            organization_id=job.organization_id,
            request=CreateTaskRequest(
                title=field_values_map.get("title") or "UNKNOWN",
                note=field_values_map.get("Details")
                or field_values_map.get("details")
                or "UNKNOWN",
                status=TaskStatus.OPEN,
                priority=TaskPriority.MEDIUM,
                type=TaskType.ACTION_ITEM,
                contact_ids=[] if not contact_id else [contact_id],
                account_id=account_id,
                owner_user_id=owner_user_id,
                due_at=due_at,
                source_type=TaskSourceType.USER,
                created_source=JobType(
                    job.type
                ).to_created_source(),  # type could be str.
            ),
        )
        entity_id = create_task.id

        return DbImportRecord(
            id=uuid.uuid4(),
            import_entity_type=ImportEntityType.TASK,
            organization_id=job.organization_id,
            created_at=datetime.now(pytz.utc),
            job_id=job.id,
            row_reference_id=str(row_number) if row_number else None,
            status=status,
            entity_id=entity_id,
            status_detail=status_detail,
            existing_record=None,
            conflicting_record=json.loads(create_task.model_dump_json())
            if create_task
            else None,
            external_id=field_values_map.get("external_id"),
        )

    async def _process_custom_object_import_row(  # noqa: C901, PLR0912, PLR0915
        self,
        job: Job
        | None,  # Either job or import_job must be defined.  Job takes precedence
        import_job: ImportJob | None,
        reference_id: str | None,
        mapped_fields: CustomObjectImportCsvFields,
        custom_object_dto: CustomObjectDto,
    ) -> DbImportRecord:
        """Process a single custom object import row respecting ObjectImportMode."""
        if job:
            job_id = job.id
            user_id = job.user_id
            organization_id = job.organization_id
        elif import_job:
            job_id = import_job.id
            if not import_job.created_by_user_id:
                # Should not happen if import job created correctly
                raise ValueError("ImportJob missing created_by_user_id")
            user_id = import_job.created_by_user_id
            organization_id = import_job.organization_id
        else:
            raise ValueError("Either job or import_job must be defined")

        if not job_id or not user_id or not organization_id:
            raise ValueError("job_id, user_id, and organization_id must be defined")

        object_import_mode = ObjectImportMode.CREATE_ONLY  # Default
        if (
            import_job
            and import_job.configuration
            and import_job.configuration.object_import_mode
        ):
            object_import_mode = import_job.configuration.object_import_mode

        status = ImportRecordStatus.SUCCESS
        status_detail = None
        custom_object_data_id: uuid.UUID | None = None
        conflicting_record_dict: dict[str, object] | None = None

        try:
            self.logger.info(
                f"[process_custom_object_import_row] mapped_fields: {mapped_fields}"
            )

            display_name = mapped_fields.custom_object_display_name
            if display_name is None:
                raise ValueError("custom_object_display_name is required")

            # Map the field UUIDs to their data record values
            field_data_with_uuid: dict[uuid.UUID, FieldValueOrAny] = {}
            if (
                mapped_fields.model_extra
                and CUSTOM_FIELD_DATA in mapped_fields.model_extra
            ):
                custom_field_data: dict[uuid.UUID, FieldValueOrAny] = (
                    mapped_fields.model_extra.get(CUSTOM_FIELD_DATA, {})
                )
                for (
                    key_uuid,
                    value,
                ) in custom_field_data.items():  # Renamed key_str to key
                    try:
                        # Further validation/transformation might be needed based on field type
                        # For now, assume value is directly usable by custom_object_service
                        field_data_with_uuid[key_uuid] = value
                    except ValueError:
                        self.logger.error(
                            f"Invalid UUID format for field key string:({key_uuid}), Skipping field."
                        )
                    except Exception as field_err:
                        # Catch any other errors during field processing
                        self.logger.error(
                            f"Error processing field with key {key_uuid}:({field_err})",
                            exc_info=True,
                        )
                        raise ValueError(
                            f"Error processing field with key {key_uuid}"
                        ) from field_err

            self.logger.info(f"Processed field_data_with_uuid:({field_data_with_uuid})")
            created_or_updated_dto: CustomObjectDataDto | None = None

            if object_import_mode == ObjectImportMode.UPSERT:
                existing_data_dto = await self.custom_object_service.find_custom_object_data_by_display_name_or_none(
                    organization_id=organization_id,
                    custom_object_id=custom_object_dto.custom_object_id,
                    display_name=display_name,
                )

                if existing_data_dto:
                    self.logger.info(
                        f"Found existing custom object data (id={existing_data_dto.custom_object_data.id}), updating."
                    )
                    created_or_updated_dto = await self.custom_object_service.update_custom_object_data_by_id_v2(
                        user_id=user_id,
                        organization_id=organization_id,
                        custom_object_id=custom_object_dto.custom_object_id,
                        custom_object_data_id=existing_data_dto.custom_object_data.id,
                        custom_field_data_by_field_id_or_any=field_data_with_uuid,
                        display_name=display_name,
                    )
                    # Note: We don't populate existing_record/conflicting_record on successful update

            # If no update happened (either CREATE_ONLY mode or UPSERT didn't find existing)
            if created_or_updated_dto is None:
                log_message = (
                    "CREATE_ONLY mode, creating new custom object data."
                    if object_import_mode == ObjectImportMode.CREATE_ONLY
                    else "No existing custom object data found during UPSERT, creating new."
                )
                self.logger.info(log_message)
                created_or_updated_dto = (
                    await self.custom_object_service.create_custom_object_data_v2(
                        user_id=user_id,
                        organization_id=organization_id,
                        custom_object_dto_or_id=custom_object_dto.custom_object_id,
                        custom_field_data_by_field_id=field_data_with_uuid,
                        display_name=display_name,
                    )
                )

            # Extract the ID after creation or update
            if created_or_updated_dto:
                custom_object_data_id = created_or_updated_dto.custom_object_data.id
            else:
                # This case should ideally not be reached if logic is correct
                # but adding for safety to avoid UnboundLocalError later
                raise ValueError(
                    "Failed to create or update custom object data record."
                )

        except ConflictResourceError as e:
            # Specifically for CREATE_ONLY mode when a duplicate display name is found
            self.logger.warning(
                f"Conflict creating custom object data: {e}", exc_info=True
            )
            status = ImportRecordStatus.FAILED
            status_detail = (
                f"Conflict: A record with this display name already exists. {e}"
            )
            conflicting_record_dict = self._convert_mapped_fields_to_dict(mapped_fields)

        except ValueError as e:
            # Catch validation errors like missing display_name or bad field data
            self.logger.warning(
                f"Validation error processing custom object row: {e}", exc_info=True
            )
            status = ImportRecordStatus.VALIDATION_ERROR
            status_detail = f"Validation Error: {e}"
            conflicting_record_dict = self._convert_mapped_fields_to_dict(mapped_fields)

        except Exception as e:
            # Catch all other errors during create/update
            self.logger.error(f"Error processing custom object row: {e}", exc_info=True)
            status = ImportRecordStatus.FAILED
            status_detail = _get_except_msg(e)
            conflicting_record_dict = self._convert_mapped_fields_to_dict(mapped_fields)

        # Create the final import record based on the outcome
        return DbImportRecord(
            id=uuid.uuid4(),
            import_entity_type=ImportEntityType.CUSTOM_OBJECT,
            organization_id=organization_id,
            created_at=datetime.now(pytz.utc),
            job_id=job_id,
            row_reference_id=reference_id,
            status=status,
            entity_id=custom_object_data_id,
            status_detail=status_detail,
            existing_record=None,  # Not populated in this logic
            conflicting_record=conflicting_record_dict,
            external_id=None,  # Assuming external_id is not handled here
        )

    async def _process_pipeline_import_row(
        self,
        job_id: uuid.UUID,
        organization_id: uuid.UUID,
        user_id: uuid.UUID,
        opportunity_import_csv_fields: OpportunityImportCsvFields,
        local_timezone: TimeZoneName | None,
        owner_user_id: uuid.UUID,
        reference_id: str | None = None,
        import_pipeline_record: ImportPipelineRecord | None = None,
        account_id: uuid.UUID | None = None,
        create_contact_pipeline_role: bool = True,
        upsert_mode: bool = False,
        pipeline_select_list_id: uuid.UUID | None = None,
    ) -> DbImportRecord:
        if owner_email := opportunity_import_csv_fields.opportunity_owner_email:
            owner_user_id = await self.get_owner_user_id_v2(
                organization_id=organization_id,
                default_user_id=owner_user_id,
                user_email=owner_email,
            )

        # find account_id
        account_id = (
            cast_uuid_or_none(
                opportunity_import_csv_fields.model_extra.get("account_id")
                if opportunity_import_csv_fields.model_extra
                else None
            )
            or account_id
        )
        if not account_id:
            account, _ = await self.find_or_create_account(
                account_import_csv_fields=opportunity_import_csv_fields.to_associated_account_import_csv_fields(),
                user_id=user_id,
                organization_id=organization_id,
                local_timezone=local_timezone,
                owner_user_id=owner_user_id,
            )
            account_id = account.id

        status = ImportRecordStatus.SUCCESS
        status_detail = None
        pipeline = None
        try:
            pipeline = await self._create_pipeline(
                user_id=user_id,
                organization_id=organization_id,
                account_id=account_id,
                opportunity_import_csv_fields=opportunity_import_csv_fields,
                local_timezone=local_timezone,
                owner_user_id=owner_user_id,
                create_contact_pipeline_role=create_contact_pipeline_role,
                pipeline_select_list_id=pipeline_select_list_id,
            )
        except ImportEntityDependencyNotFoundError as e:
            status = ImportRecordStatus.VALIDATION_ERROR
            status_detail = e.message
            self.logger.bind(
                entity_type=e.entity_type,
                entity_value=e.entity_value,
                status_detail=status_detail,
            ).opt(exception=e).exception(
                "[CrmSyncService] [_process_deal_import_row], ImportEntityDependencyNotFoundError"
            )
        except ValueError as e:
            status = ImportRecordStatus.VALIDATION_ERROR
            status_detail = str(e)
            self.logger.bind(
                exception=e,
                status_detail=status_detail,
                opportunity_import_csv_fields=opportunity_import_csv_fields,
            ).opt(exception=e).exception(
                "[CrmSyncService] [_process_deal_import_row], validation_error"
            )
        except ConflictResourceError as e:
            status = ImportRecordStatus.CONFLICT
            status_detail = (
                e.additional_error_details.details
                if e.additional_error_details
                else None
            )
            if (
                e.additional_error_details
                and e.additional_error_details.reference_id
                and (pipeline_id := uuid.UUID(e.additional_error_details.reference_id))
            ):
                existing_pipeline = await self.pipeline_service.get_pipeline_by_id(
                    pipeline_id=pipeline_id,
                    organization_id=organization_id,
                    include_custom_object=True,
                )
                if upsert_mode:
                    # In upsert mode, update the existing pipeline with new data
                    self.logger.bind(
                        existing_pipeline=existing_pipeline.id,
                        opportunity_import_csv_fields=opportunity_import_csv_fields,
                    ).info(
                        "Applying upsert mode to update existing pipeline with import data"
                    )

                    try:
                        # Update pipeline with new data from import
                        updated_pipeline = await self._update_pipeline_from_import_fields(
                            organization_id=organization_id,
                            user_id=user_id,
                            pipeline=existing_pipeline,
                            opportunity_import_csv_fields=opportunity_import_csv_fields,
                            local_timezone=local_timezone,
                        )

                        status = ImportRecordStatus.SUCCESS
                        status_detail = "Pipeline updated via upsert"
                        pipeline = updated_pipeline
                    except Exception as update_error:
                        self.logger.bind(
                            existing_pipeline=existing_pipeline.id,
                        ).opt(exception=update_error).error(
                            "Failed updating pipeline for upsert mode"
                        )
                        status = ImportRecordStatus.FAILED
                        status_detail = f"Failed updating pipeline: {update_error!s}"

                else:
                    pipeline = existing_pipeline
                self.logger.info(
                    f"[CrmSyncService] [_process_pipeline_import_row], ConflictResourceError: {e!s}, existing_record: {existing_pipeline}"
                )

        except Exception as e:
            status = ImportRecordStatus.FAILED
            status_detail = _get_except_msg(e)
            self.logger.bind(
                status_detail=status_detail,
                opportunity_import_csv_fields=opportunity_import_csv_fields,
            ).opt(exception=e).exception(
                "[CrmSyncService] [_process_deal_import_row], general error"
            )

        return DbImportRecord(
            id=uuid.uuid4(),
            import_entity_type=ImportEntityType.PIPELINE,
            organization_id=organization_id,
            created_at=datetime.now(pytz.utc),
            job_id=job_id,
            row_reference_id=reference_id,
            status=status,
            entity_id=pipeline.id if pipeline else None,
            status_detail=status_detail,
            existing_record=json.loads(
                ImportPipelineRecord.from_pipeline_v2(pipeline).model_dump_json()
            )
            if pipeline
            else None,
            conflicting_record=json.loads(import_pipeline_record.model_dump_json())
            if import_pipeline_record
            else json.loads(opportunity_import_csv_fields.model_dump_json()),
            external_id=opportunity_import_csv_fields.model_extra.get("external_id")
            if opportunity_import_csv_fields.model_extra
            else None,
        )

    async def _find_pipeline_stage_slv_id(  # noqa: C901
        self,
        organization_id: uuid.UUID,
        pipeline_id: uuid.UUID | None,
        pipeline_name: str | None = None,
        pipeline_stage_select_list_value: str | None = None,
    ) -> uuid.UUID:
        # find stage select list
        target_stage_select_list_dto = None
        if pipeline_id:
            target_stage_select_list_dto = (
                await self.pipeline_stage_service.get_pipeline_stage_select_list_dto(
                    select_list_id=pipeline_id, organization_id=organization_id
                )
            )
        else:
            for (
                stage_dto
            ) in await self.pipeline_stage_service.list_pipeline_stage_select_list_dtos(
                organization_id=organization_id
            ):
                if stage_dto.select_list_dto.list_metadata.is_default:
                    target_stage_select_list_dto = stage_dto
                if eq_str_value(
                    pipeline_name,
                    stage_dto.select_list_dto.select_list.display_name,
                ):
                    target_stage_select_list_dto = stage_dto
                    break
        if not target_stage_select_list_dto:
            raise ImportEntityDependencyNotFoundError(
                entity_value=f"{pipeline_id}",
                entity_type="pipeline_stage_dtos",
                message=f"Must has existing pipeline stage list to create pipeline: {organization_id}",
            )

        # find stage select list value
        target_stage_select_list_value_dto = None
        lowest_rank_stage_select_list_value_dto = None
        for stage_value_dto in target_stage_select_list_dto.select_list_value_dtos:
            if stage_value_dto.select_list_value.is_default:
                target_stage_select_list_value_dto = stage_value_dto
            if eq_str_value(
                pipeline_stage_select_list_value,
                stage_value_dto.select_list_value.display_value,
            ):
                target_stage_select_list_value_dto = stage_value_dto
                break
            if (
                lowest_rank_stage_select_list_value_dto is None
                or stage_value_dto.select_list_value.rank
                < lowest_rank_stage_select_list_value_dto.select_list_value.rank
            ):
                lowest_rank_stage_select_list_value_dto = stage_value_dto

        if not target_stage_select_list_value_dto:
            # There were not default select list values, so use the lowest rank one.
            target_stage_select_list_value_dto = lowest_rank_stage_select_list_value_dto

        if not target_stage_select_list_value_dto:
            raise ImportEntityDependencyNotFoundError(
                entity_value=f"{pipeline_stage_select_list_value}",
                entity_type="target_stage_select_list_value",
                message=f"Must has existing pipeline stage value to create pipeline: {pipeline_stage_select_list_value}",
            )

        return target_stage_select_list_value_dto.select_list_value_id

    def _clean_phone_number(self, phone: str | None) -> str | None:
        international_format_length = 11
        american_format_length = 10

        if not phone:
            return None

        # Remove extensions (anything after 'x')
        phone = phone.split("x")[0].strip()

        # Remove all non-digit characters
        digits_only = "".join(filter(str.isdigit, phone))

        # Handle international format
        if (
            digits_only.startswith("1")
            and len(digits_only) == international_format_length
        ):
            digits_only = digits_only[1:]  # Remove leading '1'

        # If we don't have exactly 10 digits, return None
        if len(digits_only) != american_format_length:
            return None

        # Format as XXX-XXX-XXXX
        return f"{digits_only[:3]}-{digits_only[3:6]}-{digits_only[6:]}"

    async def _build_contact_with_email_db_objects(
        self,
        user_id: uuid.UUID,
        organization_id: uuid.UUID,
        stage_id: uuid.UUID,
        primary_account_id: uuid.UUID | None,
        local_timezone: TimeZoneName | None,
        owner_user_id: uuid.UUID | None,
        contact_import_csv_fields: ContactImportCsvFields,
    ) -> CreateContactRequest:
        self.logger.bind(contact_import_csv_fields=contact_import_csv_fields).info(
            "_build_contact_with_email_db_objects"
        )
        email_to_create = strip_and_lower(
            contact_import_csv_fields.contact_primary_email
        )

        # Clean phone number
        contact_primary_phone_number = self._clean_phone_number(
            strip_and_lower(contact_import_csv_fields.contact_primary_phone_number)
        )
        csv_type = (
            contact_import_csv_fields.model_extra.get(CSV_TYPE)
            if contact_import_csv_fields.model_extra
            else None
        )
        self.logger.info(
            f"[crm_sync_service] [_build_contact_with_email_db_objects] csv_type: {csv_type}"
        )

        display_name, first_name, last_name = await self._populate_proper_contact_names(
            contact_import_csv_fields
        )

        return CreateContactRequest(
            contact=CreateDbContactRequest(
                display_name=display_name,
                owner_user_id=owner_user_id or user_id,
                created_by_user_id=user_id,
                stage_id=stage_id,
                first_name=first_name,
                last_name=last_name,
                middle_name=contact_import_csv_fields.model_extra.get("middle_name")
                if contact_import_csv_fields.model_extra is not None
                else None,
                primary_phone_number=contact_primary_phone_number,
                linkedin_url=contact_import_csv_fields.contact_linkedin_url,
                zoominfo_url=contact_import_csv_fields.model_extra.get("zoominfo_url")
                if contact_import_csv_fields.model_extra is not None
                else None,
                facebook_url=contact_import_csv_fields.contact_facebook_url,
                x_url=contact_import_csv_fields.contact_x_url,
                title=contact_import_csv_fields.contact_job_title,
                department=contact_import_csv_fields.contact_department,
                csv_type=csv_type,
                custom_field_data=contact_import_csv_fields.model_extra.get(
                    CUSTOM_FIELD_DATA
                )
                if contact_import_csv_fields.model_extra is not None
                else None,
                address=self._create_address_create_request_or_none(
                    street_one=strip_str_or_none(
                        contact_import_csv_fields.model_extra.get("street_one")
                    ),
                    street_two=strip_str_or_none(
                        contact_import_csv_fields.model_extra.get("street_two")
                    ),
                    zip_code=strip_str_or_none(
                        contact_import_csv_fields.model_extra.get("zip_code")
                    ),
                    city=strip_str_or_none(
                        contact_import_csv_fields.model_extra.get("city")
                    ),
                    state=strip_str_or_none(
                        contact_import_csv_fields.model_extra.get("state")
                    ),
                    country=strip_str_or_none(
                        contact_import_csv_fields.model_extra.get("country")
                    ),
                    created_by_user_id=user_id,
                    organization_id=organization_id,
                )
                if contact_import_csv_fields.model_extra
                else None,
                created_source=CreatedSource.CSV_IMPORT,
                created_at=cast_datetime_or_none(
                    contact_import_csv_fields.contact_created_at,
                    local_timezone,
                ),
            ),
            contact_emails=[
                CreateDbContactEmailRequest(
                    email=email_to_create,
                    is_contact_primary=True,
                    email_account_associations=[
                        CreateDbContactEmailAccountAssociationRequest(
                            account_id=primary_account_id,
                            is_contact_account_primary=True,
                        )
                    ]
                    if primary_account_id
                    else [],
                )
            ]
            if email_to_create
            else [],
            contact_account_roles=[
                CreateContactAccountRoleRequest(
                    account_id=primary_account_id, is_primary_account=True
                )
            ]
            if primary_account_id
            else [],
        )

    async def resolve_import_conflict(
        self,
        import_entity_type: ImportEntityType,
        import_record_id: uuid.UUID,
        organization_id: uuid.UUID,
        contact: ContactV2 | None = None,
        account: AccountV2 | None = None,
    ) -> bool:
        import_records = await self.import_repository.get_by_id(
            organization_id=organization_id,
            record_id=import_record_id,
        )

        if len(import_records) != 1:
            self.logger.error(
                f"Tries to resolve_import_conflict but no record found, entity id: {import_record_id}"
            )
            return False

        import_record = import_records[0]

        if (
            import_record.status
            not in (
                ImportRecordStatus.CONFLICT,
                ImportRecordStatus.VALIDATION_ERROR,
                ImportRecordStatus.FAILED,
            )
            or import_record.import_entity_type != import_entity_type
        ):
            self.logger.warning(
                f"Tries to resolve_import_conflict but import_record status is not CONFLICT, entity record id: {import_record_id}"
            )
            return False

        conflicting_record = None
        entity_id = None
        if contact and import_entity_type == ImportEntityType.CONTACT:
            conflicting_record = json.loads(
                ImportContactRecord.from_domain_contact(contact).model_dump_json()
            )
            entity_id = contact.id
        if account and import_entity_type == ImportEntityType.ACCOUNT:
            conflicting_record = json.loads(
                ImportAccountRecord.from_account(account).model_dump_json()
            )
            entity_id = account.id
        if await self.import_repository.update_by_tenanted_primary_key(
            table_model=DbImportRecord,
            primary_key_to_value={"id": import_record_id},
            organization_id=organization_id,
            column_to_update={
                "updated_at": zoned_utc_now(),
                "status": ImportRecordStatus.SUCCESS,
                "status_detail": "Conflict Resolved",
                "entity_id": entity_id,
                "conflicting_record": conflicting_record,
            },
        ):
            return True
        return False

    async def _map_pipeline_field_values(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        organization_id: uuid.UUID,
        field_values_map: dict[str, Any],
        already_mapped_csv_fields: OpportunityImportCsvFields | None,
        custom_object_dto: CustomObjectDto | None = None,
    ) -> OpportunityImportCsvFields:
        if already_mapped_csv_fields:
            to_return = already_mapped_csv_fields
            # Map custom field values
            if custom_object_dto and to_return.model_extra:
                to_return.model_extra[CSV_TYPE] = "pipeline"

                custom_field_data = await self.map_custom_field_data(
                    organization_id=organization_id,
                    custom_object_dto=custom_object_dto,
                    model_extra=to_return.model_extra,
                    local_timezone=None,
                    use_deprecated_custom_display_name_mapping=True,
                )
                to_return.model_extra[CUSTOM_FIELD_DATA] = custom_field_data
                self.logger.info(
                    f"[CrmSyncService] Final value of custom_field_data: {custom_field_data}"
                )
        else:  # derived by looking at usages, a hold-over until we only use types.
            # fall-through handles case JobType.HUBSPOT_DEAL_SYNC as well
            to_return = OpportunityImportCsvFields(
                opportunity_display_name=field_values_map.get("display_name"),
                opportunity_deal_amount=field_values_map.get("deal_amount"),
                opportunity_stage_value=field_values_map.get("stage_list_value"),
                opportunity_pipeline_id=None,
                opportunity_next_step_details=field_values_map.get("next_step_details"),
                opportunity_next_step_due_at=field_values_map.get("next_step_due_at"),
                opportunity_anticipated_closing_at=field_values_map.get(
                    "anticipated_closing_at"
                ),
                opportunity_expires_at=field_values_map.get("expires_at"),
                opportunity_created_at=field_values_map.get("created_at"),
                opportunity_closed_at=field_values_map.get("closed_at"),
                opportunity_owner_email=None,
                opportunity_closed_reason_custom_details=None,
                opportunity_closed_reasons=None,
                contact_primary_email=field_values_map.get("primary_contact_email"),
                contact_pipeline_role=field_values_map.get("contact_opportunity_role"),
                contact_pipeline_note=field_values_map.get("contact_opportunity_note"),
                company_display_name=None,
                company_domain_name=None,
                company_official_website=None,
            )
            if to_return.model_extra is not None:
                self._set_opportunity_model_extra(field_values_map, to_return)
        return to_return

    def _set_opportunity_model_extra(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, field_values_map: dict[str, Any], to_return: OpportunityImportCsvFields
    ) -> OpportunityImportCsvFields:
        if to_return.model_extra is not None:
            to_return.model_extra["stage_list_name"] = cast_str_or_none(
                field_values_map.get("stage_list_name")
            )
            to_return.model_extra["account_id"] = cast_str_or_none(
                field_values_map.get("account_id")
            )
            to_return.model_extra["external_id"] = cast_str_or_none(
                field_values_map.get("external_id")
            )
            to_return.model_extra["contact_ids"] = field_values_map.get("contact_ids")
            to_return.model_extra["pipeline_stage_select_list_value_id"] = (
                cast_str_or_none(
                    field_values_map.get("pipeline_stage_select_list_value_id")
                )
            )
            to_return.model_extra["stage_list_name"] = cast_str_or_none(
                field_values_map.get("stage_list_name")
            )
            to_return.model_extra["contact_job_title"] = cast_str_or_none(
                field_values_map.get("contact_job_title")
            )
            to_return.model_extra["contact_department"] = cast_str_or_none(
                field_values_map.get("contact_department")
            )
        return to_return

    async def _map_contact_field_values(  # type: ignore[explicit-any]
        self,
        organization_id: uuid.UUID,
        field_values_map: dict[str, Any],
        already_mapped_csv_fields: ContactImportCsvFields | None,
        custom_object_dto: CustomObjectDto | None = None,
    ) -> ContactImportCsvFields:  # dict[str, str]:
        if already_mapped_csv_fields:
            to_return = already_mapped_csv_fields
            # Map custom field values
            if custom_object_dto and to_return.model_extra:
                to_return.model_extra[CSV_TYPE] = "contact"

                custom_field_data = await self.map_custom_field_data(
                    organization_id=organization_id,
                    custom_object_dto=custom_object_dto,
                    model_extra=to_return.model_extra,
                    local_timezone=None,
                    use_deprecated_custom_display_name_mapping=True,
                )
                to_return.model_extra[CUSTOM_FIELD_DATA] = custom_field_data
                self.logger.info(
                    f"[CrmSyncService] Final value of custom_field_data: {custom_field_data}"
                )

            if (
                to_return.contact_display_name is None
                or to_return.contact_display_name == ""
            ):
                to_return.contact_display_name = " ".join(
                    [
                        to_return.contact_first_name
                        if to_return.contact_first_name
                        else "",
                        to_return.contact_last_name
                        if to_return.contact_last_name
                        else "",
                    ]
                )
            if (
                to_return.contact_first_name is None
                or to_return.contact_last_name == ""
            ):
                to_return.contact_first_name = "UNKNOWN"
            if to_return.contact_last_name is None or to_return.contact_last_name == "":
                to_return.contact_last_name = "UNKNOWN"
            if (
                not to_return.contact_display_name
                or not to_return.contact_display_name.strip()
            ):
                to_return.contact_display_name = to_return.contact_primary_email
        else:
            # Optimization: Aim to use AccountImportCsvFields.model_validate(field_values_map)
            #   For now, tracking variable usage.
            to_return = ContactImportCsvFields(
                contact_primary_email=field_values_map.get(
                    "primary_contact_email"
                ),  # note the word order swap
                contact_display_name=field_values_map.get("display_name"),
                contact_first_name=field_values_map.get("first_name"),
                contact_last_name=field_values_map.get("last_name"),
                contact_primary_phone_number=field_values_map.get(
                    "primary_phone_number"
                ),
                contact_linkedin_url=field_values_map.get("linkedin_url"),
                contact_facebook_url=field_values_map.get("facebook_url"),
                contact_x_url=field_values_map.get("x_url"),
                contact_job_title=field_values_map.get("title"),
                contact_department=field_values_map.get("department"),
                contact_created_at=field_values_map.get("created_at"),
                # Setting to None until usage is seen
                contact_stage_value=None,
                contact_owner_email=None,
                company_display_name=None,
                company_domain_name=None,
                company_official_website=None,
            )
            if to_return.model_extra is not None:
                to_return.model_extra["middle_name"] = cast_str_or_none(
                    field_values_map.get("middle_name")
                )
                to_return.model_extra["zoominfo_url"] = cast_str_or_none(
                    field_values_map.get("zoominfo_url")
                )
                to_return.model_extra["street_one"] = cast_str_or_none(
                    field_values_map.get("street_one")
                )
                to_return.model_extra["street_two"] = cast_str_or_none(
                    field_values_map.get("street_two")
                )
                to_return.model_extra["zip_code"] = cast_str_or_none(
                    field_values_map.get("zip_code")
                )
                to_return.model_extra["city"] = cast_str_or_none(
                    field_values_map.get("city")
                )
                to_return.model_extra["state"] = cast_str_or_none(
                    field_values_map.get("state")
                )
                to_return.model_extra["country"] = cast_str_or_none(
                    field_values_map.get("country")
                )
                to_return.model_extra["created_source"] = cast_str_or_none(
                    field_values_map.get("created_source")
                )
                to_return.model_extra["external_id"] = cast_str_or_none(
                    field_values_map.get("external_id")
                )
                to_return.model_extra["account_id"] = cast_str_or_none(
                    field_values_map.get("account_id")
                )
        return to_return

    def _rename_model_extra_key(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, model_extra: dict[str, Any], key_src: str, key_dest: str
    ) -> None:
        if model_extra.get(key_src) is not None and model_extra.get(key_dest) is None:
            model_extra[key_dest] = cast_str_or_none(model_extra.pop(key_src))

    def _convert_date_to_proper_format(
        self, date_str: str, local_timezone: TimeZoneName | None
    ) -> TimestampFieldValue:
        try:
            # Convert to datetime
            converted_date_as_datetime = cast_datetime_or_none(date_str, local_timezone)
            if not converted_date_as_datetime:
                raise ValueError(f"Invalid date string: {date_str}")

            # Convert to TimestampFieldValue and return
            timestamp = TimestampFieldValue(timestamp=converted_date_as_datetime)
            logger.info(f"timestamp: {timestamp}")
            return timestamp

        except (ValueError, TypeError) as e:
            logger.opt(exception=e).exception(
                "Failed to parse date string",
                extra={"date_str": date_str, "error": str(e)},
            )
            raise ValueError(f"Invalid date string: {date_str}")

    async def map_custom_field_data(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        organization_id: uuid.UUID,
        custom_object_dto: CustomObjectDto,
        model_extra: dict[str, Any],
        local_timezone: TimeZoneName | None,
        use_deprecated_custom_display_name_mapping: bool = False,  # True if you want to use the old import flow
        info_currently_processing: CurrentlyProcessing | None = None,
    ) -> dict[uuid.UUID, FieldValueOrAny]:
        custom_field_data: dict[uuid.UUID, FieldValueOrAny] = {}
        for custom_field in custom_object_dto.custom_fields:
            if isinstance(custom_field, CustomField):
                value = (
                    model_extra.get(str(custom_field.id))
                    if not use_deprecated_custom_display_name_mapping
                    else model_extra.get(custom_field.field_display_name)
                )
                field_id_info = (
                    f"({custom_field.id}, {custom_field.field_display_name})"
                )
                if info_currently_processing:
                    info_currently_processing.field_name = field_id_info
                    info_currently_processing.field_value = value
                # Note: Users can pass in fewer custom fields than the number of custom fields defined in the object
                # Do NOT add custom fields if there are no values passed in.
                if value is None or str(value).strip() == "":
                    logger.info(
                        f"[_map_custom_field_data] value is None for field {field_id_info}. Skipping."
                    )
                    continue

                if custom_field is not None and isinstance(
                    custom_field.properties, SingleSelectFieldProperty
                ):  # custom field is single-select
                    select_list_values = await self.custom_object_service.select_list_service.list_select_list_value(
                        select_list_id=custom_field.properties.select_list_id,
                        organization_id=organization_id,
                    )

                    for select_value in select_list_values:
                        if (
                            str(select_value.display_value).lower()
                            == str(value).lower()
                        ):
                            custom_field_data[custom_field.id] = str(select_value.id)
                            break
                    else:
                        raise ValueError(
                            f"Invalid select list value: {value} for field {custom_field.properties.field_display_name}"
                        )
                elif custom_field is not None and isinstance(
                    custom_field.properties, TimestampFieldProperty
                ):
                    custom_field_data[custom_field.id] = (
                        self._convert_date_to_proper_format(value, local_timezone)
                    )
                else:  # custom field is primitive type
                    custom_field_data[custom_field.id] = value

                self.logger.bind(
                    field_display_name=f"{custom_field.field_display_name}",
                    field_id=f"{custom_field.id}",
                    value=f"{value}",
                    field_data=f"{custom_field_data}",
                ).info("Custom Field Info")
        self.logger.info(
            f"[CrmSyncService] Final value of custom_field_data: {custom_field_data}"
        )
        return custom_field_data

    def _normalize_csv_account_import_model_extra(
        self,
        account_csv_fields: AccountImportCsvFields,
    ) -> None:
        if account_csv_fields.model_extra is not None:
            self._rename_model_extra_key(
                account_csv_fields.model_extra,
                "street_one",
                "company_address_street_one",
            )
            self._rename_model_extra_key(
                account_csv_fields.model_extra,
                "street_two",
                "company_address_street_two",
            )
            self._rename_model_extra_key(
                account_csv_fields.model_extra,
                "zip_code",
                "company_address_zip_code",
            )
            self._rename_model_extra_key(
                account_csv_fields.model_extra, "city", "company_address_city"
            )
            self._rename_model_extra_key(
                account_csv_fields.model_extra, "state", "company_address_state"
            )
            self._rename_model_extra_key(
                account_csv_fields.model_extra, "country", "company_address_country"
            )
            self._rename_model_extra_key(
                account_csv_fields.model_extra, "zoominfo_url", "company_zoominfo_url"
            )

    async def _map_account_field_values(  # type: ignore[explicit-any]
        self,
        organization_id: uuid.UUID,
        field_values_map: dict[str, Any],
        already_mapped_csv_fields: AccountImportCsvFields | None,
        custom_object_dto: CustomObjectDto | None = None,
    ) -> AccountImportCsvFields:
        if already_mapped_csv_fields:
            to_return = already_mapped_csv_fields
            if to_return.model_extra is not None:
                self._normalize_csv_account_import_model_extra(to_return)

            if custom_object_dto and to_return.model_extra:
                to_return.model_extra[CSV_TYPE] = "account"

                custom_field_data = await self.map_custom_field_data(
                    organization_id=organization_id,
                    custom_object_dto=custom_object_dto,
                    model_extra=to_return.model_extra,
                    local_timezone=None,
                    use_deprecated_custom_display_name_mapping=True,
                )
                to_return.model_extra[CUSTOM_FIELD_DATA] = custom_field_data
                self.logger.info(
                    f"[CrmSyncService] Final value of custom_field_data: {custom_field_data}"
                )

            if to_return.company_official_website and not to_return.company_domain_name:
                to_return.company_domain_name = validate_domain_name(
                    to_return.company_official_website
                )
        else:  # Map fields that are needed to create an account or import account_dict
            # Generic mapping from a dictionary, try and avoid and map from entry input
            # Aim to use AccountImportCsvFields.model_validate(field_values_map)
            #   after value str casting, but can do that change in another step.
            #   However, there are currently 2 values to 1 mappings in model_extra, so cannnot do it yet.
            to_return = AccountImportCsvFields(
                company_display_name=field_values_map.get("company_name"),
                company_domain_name=field_values_map.get("company_domain_name"),
                company_official_website=field_values_map.get(
                    "company_official_website"
                ),
                company_description=field_values_map.get("company_description"),
                company_technologies=field_values_map.get("company_technologies"),
                company_estimated_annual_revenue=field_values_map.get(
                    "estimated_annual_revenue"
                ),
                company_estimated_employee_count=field_values_map.get(
                    "estimated_employee_count"
                ),
                company_linkedin_url=field_values_map.get("company_linkedin_url")
                or field_values_map.get("linkedin_url"),
                company_facebook_url=field_values_map.get("company_facebook_url")
                or field_values_map.get("facebook_url"),
                company_x_url=field_values_map.get("company_x_url")
                or field_values_map.get("x_url"),
                company_owner_email=None,  # Setting to none until usage is seen
                company_created_at=field_values_map.get("created_at"),
                company_status=field_values_map.get("status"),
                company_industry=field_values_map.get("company_categories"),
            )
            if to_return.model_extra is not None:
                to_return.model_extra["owner_user_id"] = cast_str_or_none(
                    field_values_map.get("owner_user_id")
                )
                to_return.model_extra["company_keywords"] = cast_str_or_none(
                    field_values_map.get("company_keywords")
                ) or cast_str_or_none(field_values_map.get("keyword_list"))
                to_return.model_extra["zoominfo_url"] = cast_str_or_none(
                    field_values_map.get("zoominfo_url")
                )
                to_return.model_extra["company_address_street_one"] = cast_str_or_none(
                    field_values_map.get("company_address_street_one")
                )
                to_return.model_extra["company_address_street_two"] = cast_str_or_none(
                    field_values_map.get("company_address_street_two")
                )
                to_return.model_extra["company_address_zip_code"] = cast_str_or_none(
                    field_values_map.get("company_address_zip_code")
                )
                to_return.model_extra["company_address_city"] = cast_str_or_none(
                    field_values_map.get("company_address_city")
                )
                to_return.model_extra["company_address_state"] = cast_str_or_none(
                    field_values_map.get("company_address_state")
                )
                to_return.model_extra["company_address_country"] = cast_str_or_none(
                    field_values_map.get("company_address_country")
                )
                to_return.model_extra["external_id"] = (
                    cast_str_or_none(  # for HubSpot import record
                        field_values_map.get("external_id")
                    )
                )
        return to_return

    async def _find_or_create_pipeline_stage_select_list(
        self,
        organization_id: uuid.UUID,
        user_id: uuid.UUID,
        pipeline_stages: list[PipelineStageRequest],
    ) -> PipelineStageSelectListDetails:
        pipeline_stage_dto: PipelineStageDto | None = None

        summaries = (
            await self.pipeline_stage_service.list_pipeline_stage_select_list_summary(
                organization_id=organization_id
            )
        )

        # if summaries has multiple, get the default one
        if len(summaries) > 1:
            # Start of Selection
            for summary in summaries:
                if summary.is_default:
                    pipeline_stage_dto = await self.pipeline_stage_service.get_pipeline_stage_select_list_dto(
                        organization_id=organization_id, select_list_id=summary.id
                    )

        # if no summaries, create a new one
        if (
            not summaries
            or not pipeline_stage_dto
            or not self._is_pipeline_stages_match(pipeline_stages, pipeline_stage_dto)
        ):
            existing_pipeline_stage_dto = pipeline_stage_dto
            pipeline_stage_dto = (
                await self.pipeline_stage_service.create_pipeline_stage_select_list(
                    organization_id=organization_id,
                    actioning_user_id=user_id,
                    req=PipelineStageSelectListCreateRequest(
                        is_default=True,
                        display_name="Default(Sync from Hubspot)",
                        value_reqs=tuple(
                            PipelineStageSelectListValueCreateRequest(
                                default_closing_probability=Decimal(
                                    pipeline_stage.probability
                                ),
                                outcome_state=pipeline_stage.outcome_state(),
                                pipeline_status=pipeline_stage.pipeline_status(),
                                select_list_value_req=SelectListValueCreateRequest(
                                    display_value=pipeline_stage.display_name,
                                    api_name=pipeline_stage.display_name,
                                    is_default=False,
                                ),
                            )
                            for pipeline_stage in pipeline_stages
                        ),
                    ),
                )
            )

            # deactivate pipeline_stage_dto
            if existing_pipeline_stage_dto and pipeline_stage_dto:
                await self.pipeline_stage_service.deactivate_pipeline_stage_select_list(
                    organization_id=organization_id,
                    actioning_user_id=user_id,
                    pipeline_stage_select_list_id=existing_pipeline_stage_dto.select_list_id,
                )
        if not pipeline_stage_dto:
            raise ValueError("Error found or creating pipeline stage dto")
        return PipelineStageSelectListDetails.from_pipeline_stage_dto(
            pipeline_stage_dto=pipeline_stage_dto
        )

    def _is_pipeline_stages_match(
        self,
        pipeline_stages: list[PipelineStageRequest],
        pipeline_stage_dto: PipelineStageDto | None,
    ) -> bool:
        if not pipeline_stage_dto:
            return False
        pipeline_stage_details = PipelineStageSelectListDetails.from_pipeline_stage_dto(
            pipeline_stage_dto=pipeline_stage_dto
        )
        self.logger.bind(
            pipeline_stage_dto=pipeline_stage_dto,
            pipeline_stages=pipeline_stages,
            pipeline_stage_details=pipeline_stage_details,
        ).info("_is_pipeline_stages_match")
        if len(pipeline_stage_details.values) != len(pipeline_stages):
            return False
        for pipeline_stage in pipeline_stages:
            if pipeline_stage.display_name not in [
                stage.display_value for stage in pipeline_stage_details.values
            ]:
                return False
        return True

    def _cast_probability(self, probability: str) -> int:
        try:
            decim = cast_decimal_or_none(probability)
            return int(decim * 100) if decim is not None else 0
        except (ValueError, TypeError):
            return 0

    async def upsert_org_external_sync(
        self,
        organization_id: uuid.UUID,
        user_id: uuid.UUID,
        sync_mode: SyncMode,
        sync_period_minutes: int,
        user_integration_id: uuid.UUID,
    ) -> OrganizationExternalSync:
        if existing_instance := await self.crm_sync_repository.get_org_external_sync(
            organization_id=organization_id
        ):
            sync_setting = existing_instance.sync_setting.model_copy(
                update={
                    "sync_mode": sync_mode,
                    "sync_period_minutes": sync_period_minutes,
                }
            )

            return await self.crm_sync_repository.update_org_external_sync(
                sync_id=existing_instance.id,
                user_id=user_id,
                user_integration_id=user_integration_id,
                sync_setting=sync_setting,
            )
        else:
            return await self.crm_sync_repository.insert(
                OrganizationExternalSync(
                    id=uuid.uuid4(),
                    organization_id=organization_id,
                    user_integration_id=user_integration_id,
                    status=OrganizationExternalSyncStatus.ACTIVE,
                    created_at=zoned_utc_now(),
                    created_by_user_id=user_id,
                    sync_setting=OrganizationExternalSyncSetting(
                        crm=CrmProvider.HUBSPOT,
                        sync_mode=sync_mode,
                        sync_period_minutes=sync_period_minutes,
                    ),
                )
            )

    async def map_org_import_records(
        self, organization_id: uuid.UUID, status: list[ImportRecordStatus]
    ) -> dict[str, list[ImportRecord]]:
        all_records = await self.import_repository.list_all(
            organization_id=organization_id
        )
        results: dict[str, list[ImportRecord]] = {}
        for record in all_records:
            if record.status in status:
                if not results.get(record.import_entity_type):
                    results[record.import_entity_type] = []
                results[record.import_entity_type].append(record)
        return results

    async def update_account_from_import_fields(  # noqa: PLR0915,C901, PLR0912
        self,
        *,
        organization_id: uuid.UUID,
        user_id: uuid.UUID,
        existing_account: AccountV2,
        account_import_csv_fields: AccountImportCsvFields,
        owner_user_id: uuid.UUID,
    ) -> AccountV2:
        """
        Updates an existing account with data from import fields using an upsert model.
        Merges new values with existing ones, prioritizing non-empty existing data.
        Only adds new values without replacing existing ones.

        Args:
            organization_id: The organization ID
            user_id: The user performing the update
            existing_account: The existing account to update
            account_import_csv_fields: The import data containing new values

        Returns:
            The updated account
        """

        # Track if we have any changes to make
        has_changes = False

        # Initialize variables with UNSET values
        display_name: UnsetAware[str] = UNSET
        official_website: UnsetAware[SchemeOptionalHttpUrlStr | None] = UNSET
        description: UnsetAware[str | None] = UNSET
        category_list: UnsetAware[list[str] | None] = UNSET
        linkedin_url: UnsetAware[SchemeOptionalHttpUrlStr | None] = UNSET
        facebook_url: UnsetAware[SchemeOptionalHttpUrlStr | None] = UNSET
        x_url: UnsetAware[SchemeOptionalHttpUrlStr | None] = UNSET
        keyword_list: UnsetAware[list[str] | None] = UNSET
        technology_list: UnsetAware[list[str] | None] = UNSET
        estimated_annual_revenue: UnsetAware[NonNegativeDecimal | None] = UNSET
        estimated_employee_count: UnsetAware[NonNegativeInt | None] = UNSET
        custom_field_data: UnsetAware[dict[uuid.UUID, FieldValueOrAny] | None] = UNSET

        # Display name - only update if empty in existing account
        if (
            account_import_csv_fields.company_display_name
            and not existing_account.display_name
        ):
            display_name = account_import_csv_fields.company_display_name
            has_changes = True

        # Linkedlin - add if doesn't already exist
        if account_import_csv_fields.company_linkedin_url:
            linkedin_url = account_import_csv_fields.company_linkedin_url
            has_changes = True

        # Facebook - add if doesn't already exist
        if account_import_csv_fields.company_facebook_url:
            facebook_url = account_import_csv_fields.company_facebook_url
            has_changes = True

        # X (Twitter) - add if doesn't already exist
        if account_import_csv_fields.company_x_url:
            x_url = account_import_csv_fields.company_x_url
            has_changes = True

        # Website - only update if empty in existing account
        # Official website - only update if empty in existing account
        if (
            account_import_csv_fields.company_official_website
            and not existing_account.official_website
        ):
            official_website = account_import_csv_fields.company_official_website
            has_changes = True

        # Description - only update if empty in existing account
        if (
            account_import_csv_fields.company_description
            and not existing_account.description
        ):
            description = account_import_csv_fields.company_description
            has_changes = True

        # Industry (category_list) - add new categories without removing existing ones
        if account_import_csv_fields.company_industry:
            import_categories = [
                c.strip()
                for c in account_import_csv_fields.company_industry.split(",")
                if c.strip()
            ]
            existing_categories = existing_account.category_list or []

            # Find categories that don't exist in the current list
            new_categories = [
                c for c in import_categories if c not in existing_categories
            ]

            if new_categories:
                # Combine existing categories with new ones
                merged_categories = existing_categories + new_categories
                category_list = merged_categories
                has_changes = True

        # Keywords - add new keywords without removing existing ones
        # Check both model_extra fields for company_keywords/keywords
        company_keywords = None
        if (
            account_import_csv_fields.model_extra
            and "company_keywords" in account_import_csv_fields.model_extra
        ):
            company_keywords = account_import_csv_fields.model_extra.get(
                "company_keywords"
            )
        elif (
            account_import_csv_fields.model_extra
            and "keywords" in account_import_csv_fields.model_extra
        ):
            company_keywords = account_import_csv_fields.model_extra.get("keywords")

        if company_keywords:
            import_keywords = [
                kw.strip() for kw in company_keywords.split(",") if kw.strip()
            ]
            existing_keywords = existing_account.keyword_list or []

            # Find keywords that don't exist in the current list
            new_keywords = [kw for kw in import_keywords if kw not in existing_keywords]

            if new_keywords:
                # Combine existing keywords with new ones
                merged_keywords = existing_keywords + new_keywords
                keyword_list = merged_keywords
                has_changes = True

        # Technologies - add new technologies without removing existing ones
        if account_import_csv_fields.company_technologies:
            import_technologies = [
                tech.strip()
                for tech in account_import_csv_fields.company_technologies.split(",")
                if tech.strip()
            ]
            existing_technologies = existing_account.technology_list or []

            # Find technologies that don't exist in the current list
            new_technologies = [
                tech
                for tech in import_technologies
                if tech not in existing_technologies
            ]

            if new_technologies:
                # Combine existing technologies with new ones
                merged_technologies = existing_technologies + new_technologies
                technology_list = merged_technologies
                has_changes = True

        # Annual Revenue - add if it doesn't already exist
        if (
            account_import_csv_fields.company_estimated_annual_revenue
            and not existing_account.estimated_annual_revenue
        ):
            try:
                # Clean value and convert to Decimal
                revenue_str = (
                    account_import_csv_fields.company_estimated_annual_revenue.strip()
                )
                # Remove any currency symbols and commas
                revenue_str = re.sub(r"[^\d.]", "", revenue_str)
                revenue = TypeAdapter(NonNegativeDecimal).validate_python(revenue_str)
                estimated_annual_revenue = revenue
                has_changes = True
            except (ValueError, TypeError):
                # Log invalid value but continue processing
                self.logger.warning(
                    f"Invalid annual revenue value: {account_import_csv_fields.company_estimated_annual_revenue}"
                )

        # Employee Count - add if it doesn't already exist
        if (
            account_import_csv_fields.company_estimated_employee_count
            and not existing_account.estimated_employee_count
        ):
            try:
                # Clean value and convert to integer
                count_str = (
                    account_import_csv_fields.company_estimated_employee_count.strip()
                )
                # Remove any non-numeric characters
                count_str = re.sub(r"[^\d]", "", count_str)
                count = TypeAdapter(NonNegativeInt).validate_python(count_str)
                estimated_employee_count = count
                has_changes = True
            except (ValueError, TypeError):
                # Log invalid value but continue processing
                self.logger.warning(
                    f"Invalid employee count value: {account_import_csv_fields.company_estimated_employee_count}"
                )

        # Custom field data
        if (
            account_import_csv_fields.model_extra
            and account_import_csv_fields.model_extra.get(CUSTOM_FIELD_DATA)
        ):
            custom_field_data = account_import_csv_fields.model_extra.get(
                CUSTOM_FIELD_DATA
            )
            has_changes = True

        if owner_user_id_changed := (owner_user_id != existing_account.owner_user_id):
            has_changes = True

        # If we have changes, update the account
        if has_changes:
            # Log what we're updating
            self.logger.bind(
                organization_id=organization_id,
                account_id=existing_account.id,
                display_name=display_name if display_name is not UNSET else "UNSET",
                linkedin_url=linkedin_url if linkedin_url is not UNSET else "UNSET",
                facebook_url=facebook_url if facebook_url is not UNSET else "UNSET",
                x_url=x_url if x_url is not UNSET else "UNSET",
                official_website=official_website
                if official_website is not UNSET
                else "UNSET",
                description=description if description is not UNSET else "UNSET",
                category_list=category_list if category_list is not UNSET else "UNSET",
                technology_list=technology_list
                if technology_list is not UNSET
                else "UNSET",
                keyword_list=keyword_list if keyword_list is not UNSET else "UNSET",
                estimated_annual_revenue=estimated_annual_revenue
                if estimated_annual_revenue is not UNSET
                else "UNSET",
                estimated_employee_count=estimated_employee_count
                if estimated_employee_count is not UNSET
                else "UNSET",
                custom_field_data=custom_field_data
                if custom_field_data is not UNSET
                else "UNSET",
                owner_user_id=owner_user_id if owner_user_id_changed else UNSET,
            ).info("Updating existing account from import data using merge model")

            return await self.account_service.patch_by_id_v2(
                organization_id=organization_id,
                account_id=existing_account.id,
                user_id=user_id,
                request=PatchAccountRequest(
                    display_name=display_name,
                    official_website=official_website,
                    description=description,
                    category_list=category_list,
                    linkedin_url=linkedin_url,
                    facebook_url=facebook_url,
                    x_url=x_url,
                    technology_list=technology_list,
                    estimated_annual_revenue=estimated_annual_revenue,
                    estimated_employee_count=estimated_employee_count,
                    keyword_list=keyword_list,
                    custom_field_data=custom_field_data,
                    owner_user_id=owner_user_id,
                ),
            )

        return existing_account

    def _convert_mapped_fields_to_dict(
        self, mapped_fields: CustomObjectImportCsvFields
    ) -> dict[str, object]:
        """Helper to convert mapped fields to a serializable dict for logging/storage."""
        record_dict = mapped_fields.model_dump(mode="json")

        # Clean up the dict if necessary (e.g., stringify UUID keys if model_dump doesn't handle it)
        if record_dict.get(CUSTOM_FIELD_DATA):
            try:
                # Attempt to convert keys if they are UUIDs
                record_dict[CUSTOM_FIELD_DATA] = {
                    str(k): v for k, v in record_dict[CUSTOM_FIELD_DATA].items()
                }
            except Exception:
                # Log error if conversion fails, but proceed
                self.logger.warning(
                    "Failed to stringify UUID keys in custom_field_data for conflicting_record"
                )
                # Keep the original dict structure if conversion fails

        return record_dict

    async def update_contact_from_import_fields(
        self,
        *,
        organization_id: uuid.UUID,
        user_id: uuid.UUID,
        existing_contact: ContactV2,
        contact_import_csv_fields: ContactImportCsvFields,
    ) -> ContactV2:
        """
        Updates an existing contact with data from import fields using an upsert model.
        Merges new values with existing ones, prioritizing non-empty existing data.
        Only adds new values without replacing existing ones.

        Args:
            organization_id: The organization ID
            user_id: The user performing the update
            existing_contact: The existing contact to update
            contact_import_csv_fields: The import data containing new values

        Returns:
            The updated contact
        """
        # Track if we have any changes to make

        d_name, f_name, l_name = await self._populate_proper_contact_names(
            contact_import_csv_fields
        )

        has_changes = False

        # Initialize variables with UNSET values
        first_name: UnsetAware[str | None] = UNSET
        last_name: UnsetAware[str | None] = UNSET
        display_name: UnsetAware[str] = UNSET
        primary_phone_number: UnsetAware[str | None] = UNSET
        linkedin_url: UnsetAware[str | None] = UNSET
        facebook_url: UnsetAware[str | None] = UNSET
        custom_field_data: UnsetAware[dict[uuid.UUID, FieldValueOrAny] | None] = UNSET

        # First name - only update if empty in existing contact
        if f_name:
            first_name = f_name
            has_changes = True

        # Last name - only update if empty in existing contact
        if l_name:
            last_name = l_name
            has_changes = True

        # display_name - only update if empty in existing contact
        if d_name:
            display_name = d_name
            has_changes = True

        # LinkedIn URL - add if doesn't already exist
        if contact_import_csv_fields.contact_linkedin_url:
            linkedin_url = contact_import_csv_fields.contact_linkedin_url
            has_changes = True

        # Facebook URL - add if doesn't already exist
        if contact_import_csv_fields.contact_facebook_url:
            facebook_url = contact_import_csv_fields.contact_facebook_url
            has_changes = True

        # Primary phone number - only update if empty in existing contact
        if contact_import_csv_fields.contact_primary_phone_number:
            primary_phone_number = (
                contact_import_csv_fields.contact_primary_phone_number
            )
            has_changes = True

        # Custom field data
        if (
            contact_import_csv_fields.model_extra
            and contact_import_csv_fields.model_extra.get(CUSTOM_FIELD_DATA)
        ):
            custom_field_data = contact_import_csv_fields.model_extra.get(
                CUSTOM_FIELD_DATA
            )
            has_changes = True

        # If we have changes, update the contact
        if has_changes:
            # Log what we're updating
            self.logger.bind(
                organization_id=organization_id,
                contact_id=existing_contact.id,
                first_name=first_name if first_name is not UNSET else "UNSET",
                last_name=last_name if last_name is not UNSET else "UNSET",
                display_name=display_name if display_name is not UNSET else "UNSET",
                linkedin_url=linkedin_url if linkedin_url is not UNSET else "UNSET",
                facebook_url=facebook_url if facebook_url is not UNSET else "UNSET",
                primary_phone_number=primary_phone_number
                if primary_phone_number is not UNSET
                else "UNSET",
                custom_field_data=custom_field_data
                if custom_field_data is not UNSET
                else "UNSET",
            ).info("Updating existing contact from import data using merge model")

            return await self.contact_service.patch_by_id(
                organization_id=organization_id,
                contact_id=existing_contact.id,
                user_id=user_id,
                request=PatchContactRequest(
                    first_name=first_name,
                    last_name=last_name,
                    display_name=display_name,
                    linkedin_url=linkedin_url,
                    facebook_url=facebook_url,
                    primary_phone_number=primary_phone_number,
                    custom_field_data=custom_field_data,
                ),
            )

        return existing_contact

    async def _update_pipeline_from_import_fields(  # noqa: C901
        self,
        *,
        organization_id: uuid.UUID,
        user_id: uuid.UUID,
        pipeline: PipelineV2,
        opportunity_import_csv_fields: OpportunityImportCsvFields,
        local_timezone: TimeZoneName | None,
    ) -> PipelineV2:
        """
        Update an existing pipeline with data from imported fields.
        Similar to update_contact_from_import_fields or update_account_from_import_fields.
        """

        # Track if we have any changes to make
        has_changes = False

        try:
            # Prepare fields for the patch request
            display_name: UnsetAware[str] = UNSET
            if opportunity_import_csv_fields.opportunity_display_name:
                display_name = opportunity_import_csv_fields.opportunity_display_name
                has_changes = True

            next_step_details: UnsetAware[str | None] = UNSET
            if opportunity_import_csv_fields.opportunity_next_step_details:
                next_step_details = (
                    opportunity_import_csv_fields.opportunity_next_step_details
                )
                has_changes = True

            anticipated_closing_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
            if opportunity_import_csv_fields.opportunity_anticipated_closing_at:
                try:
                    timestamp_value = self._convert_date_to_proper_format(
                        date_str=opportunity_import_csv_fields.opportunity_anticipated_closing_at,
                        local_timezone=local_timezone,
                    )
                    # Extract the datetime from the TimestampFieldValue
                    if (
                        isinstance(timestamp_value, TimestampFieldValue)
                        and timestamp_value.timestamp
                    ):
                        anticipated_closing_at = timestamp_value.timestamp
                except Exception as e:
                    self.logger.bind(
                        date=opportunity_import_csv_fields.opportunity_anticipated_closing_at,
                        exception=e,
                    ).warning(
                        "Failed to parse anticipated_closing_at for pipeline update"
                    )
                has_changes = True

            amount: UnsetAware[NonNegativeDecimal | None] = UNSET
            if opportunity_import_csv_fields.opportunity_deal_amount:
                try:
                    amount_str = (
                        opportunity_import_csv_fields.opportunity_deal_amount.strip()
                        .replace("$", "")
                        .replace(",", "")
                    )
                    amount = TypeAdapter(NonNegativeDecimal).validate_python(amount_str)
                except ValueError:
                    self.logger.bind(
                        amount=opportunity_import_csv_fields.opportunity_deal_amount
                    ).warning("Invalid amount value for pipeline update")
                has_changes = True

            owner_user_id: UnsetAware[uuid.UUID] = UNSET
            if opportunity_import_csv_fields.opportunity_owner_email:
                try:
                    owner_user_id = await self.get_owner_user_id_v2(
                        organization_id=organization_id,
                        default_user_id=user_id,
                        user_email=opportunity_import_csv_fields.opportunity_owner_email,
                    )
                except Exception as e:
                    self.logger.bind(
                        owner_email=opportunity_import_csv_fields.opportunity_owner_email,
                        exception=e,
                    ).warning("Failed to get owner_user_id for pipeline update")
                has_changes = True

            # Custom field data
            custom_field_data: UnsetAware[
                Mapping[uuid.UUID, FieldValueOrAny] | None
            ] = UNSET
            if (
                opportunity_import_csv_fields.model_extra
                and opportunity_import_csv_fields.model_extra.get(CUSTOM_FIELD_DATA)
            ):
                custom_field_data = opportunity_import_csv_fields.model_extra.get(
                    CUSTOM_FIELD_DATA
                )
                has_changes = True

            if has_changes:
                # Create the patch request with all fields
                patch_pipeline_request = InternalPatchPipelineRequest(
                    display_name=display_name,
                    amount=amount,
                    next_step_details=next_step_details,
                    anticipated_closing_at=anticipated_closing_at,
                    owner_user_id=owner_user_id,
                    custom_field_data=custom_field_data,
                )

                # Update the pipeline
                return await self.pipeline_service.patch_pipeline(
                    organization_id=organization_id,
                    user_id=user_id,
                    pipeline_id=pipeline.id,
                    req=patch_pipeline_request,
                )

        except Exception as e:
            self.logger.bind(
                organization_id=organization_id,
                pipeline_id=pipeline.id,
                opportunity_import_csv_fields=opportunity_import_csv_fields,
            ).opt(exception=e).error("Failed to update pipeline from import fields")
            raise
        return pipeline

    async def _populate_proper_contact_names(  # noqa:PLR0911,PLR0912,C901
        self, contact_import_csv_fields: ContactImportCsvFields
    ) -> tuple[str, str, str | None]:
        """
        Extracts and normalizes contact name fields from import data.

        Returns:
            tuple[str, str, str | None]: (display_name, first_name, last_name)
        """
        display_name = contact_import_csv_fields.contact_display_name
        first_name = contact_import_csv_fields.contact_first_name
        last_name = contact_import_csv_fields.contact_last_name

        # Case 1: All three values are None
        if not display_name and not first_name and not last_name:
            fallback_name = contact_import_csv_fields.contact_primary_email or "UNKNOWN"
            return fallback_name, fallback_name, None

        # Case 2: first_name exists, but last_name is missing
        if first_name and not last_name:
            # Use first_name as display_name if no display_name
            display_name = display_name or first_name
            return display_name, first_name, None

        # Case 3: display_name and first_name both exist, no need to modify
        if display_name and first_name:
            return display_name, first_name, last_name

        # Case 4: display_name missing, but have first/last name
        if not display_name and first_name and last_name:
            display_name = f"{first_name} {last_name}"
            return display_name, first_name, last_name

        # Case 5: first_name and last_name missing, but have display_name
        if display_name and not first_name and not last_name:
            name_parts = display_name.split(" ", 1)
            if len(name_parts) > 1:
                first_name = name_parts[0]
                last_name = name_parts[1]
            else:
                first_name = display_name
                last_name = None
            return display_name, first_name, last_name

        # Case 6: display_name exists, first_name is missing, but last_name exists
        if display_name and not first_name and last_name:
            # Extract first name from display name
            name_parts = display_name.split(" ", 1)
            if len(name_parts) > 1 and name_parts[1].strip() == last_name:  # noqa: SIM108
                # If the last part of display_name matches last_name, use first part as first_name
                first_name = name_parts[0]
            else:
                # Otherwise, just use the first part of display_name as first_name
                first_name = name_parts[0]
            return display_name, first_name, last_name

        # Default fallback case
        if not display_name and first_name:
            display_name = first_name
        elif not display_name and last_name:
            display_name = last_name
        elif not display_name:
            display_name = "UNKNOWN"

        if not first_name:
            first_name = "UNKNOWN"

        return display_name, first_name, last_name

    def _populate_contact_pipeline_roles(
        self, contact_opportunity_role_types: str | None
    ) -> list[ContactPipelineRoleType] | None:
        """
        Convert comma-separated string of role types to a list of ContactPipelineRoleType enum values.

        Args:
            contact_opportunity_role_types: Comma-separated string of role types

        Returns:
            List of valid ContactPipelineRoleType enum values or None if no valid values found
        """

        types = none_or_split(contact_opportunity_role_types)
        if not contact_opportunity_role_types or not types:
            return None
        # Convert valid types to enum values, filtering out invalid ones
        result: list[ContactPipelineRoleType] = []
        for role_type in types:
            upper_role = role_type.upper()
            if upper_role in ContactPipelineRoleType.__members__:
                result.append(ContactPipelineRoleType[upper_role])
            else:
                self.logger.debug(
                    f"Skipped invalid contact pipeline role type: {role_type}"
                )

        return result if result else None


def get_crm_sync_service_from_db_engine(engine: DatabaseEngine) -> CrmSyncService:
    import_repository = ImportRepository(engine=engine)
    contact_service = get_contact_service(db_engine=engine)
    contact_query_service = get_contact_query_service(db_engine=engine)
    pipeline_service = get_pipeline_service(db_engine=engine)
    pipeline_stage_service = get_pipeline_stage_select_list_service(engine=engine)
    account_service = get_account_service(
        db_engine=engine,
    )
    hubspot_service = get_hubspot_service_by_db_engine(db_engine=engine)
    user_auth_service = get_user_auth_service_with_engine(db_engine=engine)
    task_v2_service = get_task_v2_service_general(db_engine=engine)
    meeting_service = meeting_service_factory_general(db_engine=engine)
    file_service = file_service_from_engine(engine=engine)
    domain_object_list_service = get_domain_object_list_service(db_engine=engine)
    feature_flag_service = get_feature_flag_service()
    crm_sync_repository = CRMSyncRepository(engine=engine)
    import_job_repository = ImportJobRepository(engine=engine)
    custom_object_service = get_custom_object_service(db_engine=engine)
    association_service = get_association_service(db_engine=engine)
    select_list_service = get_select_list_service(engine=engine)
    return CrmSyncService(
        import_repository=import_repository,
        contact_service=contact_service,
        contact_query_service=contact_query_service,
        pipeline_service=pipeline_service,
        pipeline_stage_service=pipeline_stage_service,
        account_service=account_service,
        user_auth_service=user_auth_service,
        task_v2_service=task_v2_service,
        meeting_service=meeting_service,
        file_service=file_service,
        domain_object_list_service=domain_object_list_service,
        feature_flag_service=feature_flag_service,
        crm_sync_repository=crm_sync_repository,
        import_job_repository=import_job_repository,
        hubspot_service=hubspot_service,
        custom_object_service=custom_object_service,
        association_service=association_service,
        select_list_service=select_list_service,
    )


def get_crm_sync_service(request: Request) -> CrmSyncService:
    return get_crm_sync_service_from_db_engine(engine=get_db_engine(request))
