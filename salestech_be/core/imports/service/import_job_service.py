from datetime import timed<PERSON><PERSON>
from heapq import nlargest
from typing import Annotated
from uuid import UUID, uuid4

import temporalio
from fastapi import Depends
from temporalio.client import Client, WorkflowExecutionStatus
from temporalio.common import RetryPolicy
from temporalio.exceptions import WorkflowAlreadyStartedError

from salestech_be.common.error_code import <PERSON>rrorCode
from salestech_be.common.exception import ErrorDetails, InvalidArgumentError
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.metadata.common import (
    CustomObjectIdentifier,
    StandardObjectIdentifier,
    object_id_or_name,
)
from salestech_be.core.files.service.file_service import (
    FileService,
    file_service_from_engine,
)
from salestech_be.core.imports.models.import_job import (
    FileMetadata,
    ImportConfiguration,
    ImportCsvJobStatus,
    ImportJob,
    ImportMetadata,
)
from salestech_be.core.imports.repository.import_job_repository import (
    ImportJobRepository,
)
from salestech_be.core.imports.types import (
    ImportCsvJobDetail,
    ImportEntityDetail,
    ImportJobFilter,
    ImportResultStats,
)
from salestech_be.db.dao.import_repository import ImportRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.import_record import ImportEntityType, ImportRecordStatus
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import (
    CRM_SYNC_TASK_QUEUE,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.pydantic_types.copy_util import strict_model_copy
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.imports.workflows.entity_Import_workflow import (
    EntityImportWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger()


def _import_objects_identifier(
    object_identifiers: list[StandardObjectIdentifier | CustomObjectIdentifier],
) -> str:
    if not object_identifiers:
        raise InvalidArgumentError(
            additional_error_details=ErrorDetails(
                code=ErrorCode.INVALID_REQUEST,
                details="object_identifiers are missing.",
            )
        )

    # Convert to set first to remove duplicates
    unique_identifiers = {str(object_id_or_name(oi)) for oi in object_identifiers}

    if len(unique_identifiers) != len(object_identifiers):
        raise InvalidArgumentError(
            additional_error_details=ErrorDetails(
                code=ErrorCode.INVALID_REQUEST,
                details="Duplicate object identifiers are not allowed.",
            )
        )

    return "__".join(sorted(unique_identifiers))


class ImportJobService:
    def __init__(
        self,
        repository: ImportJobRepository,
        import_repository: ImportRepository,
        file_service: FileService,
        temporal_client: Client,
    ):
        self._repository = repository
        self.import_repository = import_repository
        self.file_service = file_service
        self._temporal_client = temporal_client
        # Maybe in the future support concurrent imports per org
        self.import_job_max_global_num_concurrent = (
            settings.import_job_max_global_num_concurrent
        )
        # Ideally, workflows will complete and update their own status.
        #   But workflows don't do that when they fail or time out.
        #   So we check for that here and update the status.
        self.import_job_grace_time_started_check_sec = (
            settings.import_job_grace_time_started_check_sec
        )
        self.import_job_activity_workflow_margin = timedelta(minutes=20)

    async def create_job(
        self,
        organization_id: UUID,
        user_id: UUID,
        job_name: str,
        object_identifier: str,
        configuration: ImportConfiguration | None = None,
        file_metadata: FileMetadata | None = None,
        status: ImportCsvJobStatus = ImportCsvJobStatus.QUEUED,
    ) -> ImportJob:
        return await self._repository.create(
            job=ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                display_name=job_name,
                status=status,
                configuration=configuration,
                metadata=file_metadata,
                object_identifier=object_identifier,
                created_by_user_id=user_id,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )

    async def create_job_draft(
        self,
        organization_id: UUID,
        user_id: UUID,
        import_metadata: ImportMetadata,
    ) -> ImportJob:
        import_objects_identifier = _import_objects_identifier(
            import_metadata.import_configuration.object_identifiers
        )
        return await self.create_job(
            organization_id=organization_id,
            user_id=user_id,
            job_name=import_metadata.display_name,
            object_identifier=import_objects_identifier,
            configuration=import_metadata.import_configuration,
            file_metadata=import_metadata.file_metadata[0],
            status=ImportCsvJobStatus.DRAFT,
        )

    async def _start_job_workflow(self, job: ImportJob) -> ImportJob | None:
        """Starts the temporal workflow for a given job and updates its status."""
        if job.status != ImportCsvJobStatus.QUEUED:
            logger.bind(job_id=job.id, status=job.status).warning(
                "Attempted to start a workflow for a job not in QUEUED state."
            )
            return None

        workflow_id = f"csv-import-{job.object_identifier}-{job.id}"
        try:
            workflow_handle = await self._temporal_client.start_workflow(
                EntityImportWorkflow.run,
                args=[job.id, job.organization_id],
                id=workflow_id,
                task_queue=CRM_SYNC_TASK_QUEUE,
                # Timeout of a single workflow run.
                run_timeout=timedelta(days=2)
                + self.import_job_activity_workflow_margin,
                # Timeout of a single workflow execution.
                execution_timeout=timedelta(days=3)
                + self.import_job_activity_workflow_margin,
                retry_policy=RetryPolicy(
                    initial_interval=timedelta(seconds=10),
                    backoff_coefficient=2,
                    maximum_interval=timedelta(minutes=1),
                    maximum_attempts=5,
                    non_retryable_error_types=[
                        "ValueError",
                        "ResourceNotFoundError",
                    ],
                ),
            )
            logger.bind(
                workflow_id=workflow_id,
                run_id=workflow_handle.run_id,
                job_id=job.id,
                organization_id=job.organization_id,
            ).info("Successfully started workflow for import job.")
            return await self._repository.update_status_from_queued(
                job_id=job.id,
                organization_id=job.organization_id,
                new_status=ImportCsvJobStatus.STARTED,
                new_workflow_id=workflow_id,
            )
        except WorkflowAlreadyStartedError as e_already_started:
            logger.bind(
                job_id=job.id,
                workflow_id=e_already_started.workflow_id,
                actual_run_id=e_already_started.run_id,
                organization_id=job.organization_id,
            ).info(
                "Temporal workflow for import job already started by another process."
            )
            return await self._repository.update_status_from_queued(
                job_id=job.id,
                organization_id=job.organization_id,
                new_status=ImportCsvJobStatus.STARTED,
                new_workflow_id=workflow_id,
            )
        except temporalio.service.RPCError as e:
            logger.bind(
                job_id=job.id,
                workflow_id=workflow_id,
                error=str(e),
                grpc_status_code=e.grpc_status.code if e.grpc_status else None,
            ).error("Failed to start temporal workflow for import job.")
            return await self._repository.update_status_from_queued(
                job_id=job.id,
                organization_id=job.organization_id,
                new_status=ImportCsvJobStatus.FAILED,
                new_workflow_id=workflow_id,
            )

    async def _try_start_queued_jobs_global(self, max_jobs_start: int) -> None:  # noqa: C901
        """
        Checks for running jobs and starts queued jobs if under the concurrency limit.
        max_jobs_start is the maximum number of jobs to start, and could potentially
          cause unacceptable latency if set too high.
        """
        logger.info("Queued Jobs loop STARTED max_jobs_start({max_jobs_start})")
        # 1) Check currently running jobs for accurate status
        running_jobs_count = 0
        started_jobs = await self._repository.global_list_by_status(
            ImportCsvJobStatus.STARTED,
            limit=max_jobs_start,
        )
        for job in started_jobs:
            if not job.workflow_id:
                logger.bind(job_id=job.id).warning(
                    "Job marked as STARTED but has no workflow_id. Set FAILED status."
                )
                await self._repository.update_by_primary_key(
                    ImportJob,
                    primary_key_to_value={"id": job.id},
                    column_to_update={
                        "status": ImportCsvJobStatus.FAILED,
                        "updated_at": zoned_utc_now(),
                    },
                )
                continue

            # Only check workflows that have been running for more than 10 minutes
            if job.updated_at > zoned_utc_now() - timedelta(
                seconds=self.import_job_grace_time_started_check_sec
            ):
                running_jobs_count += 1
                continue

            try:
                handle = self._temporal_client.get_workflow_handle(job.workflow_id)
                workflow_state = await handle.describe()
                if workflow_state.status == WorkflowExecutionStatus.RUNNING:
                    running_jobs_count += 1
                    continue
                logger.bind(
                    job_id=job.id,
                    workflow_id=job.workflow_id,
                    expected_status=WorkflowExecutionStatus.RUNNING,
                    actual_status=workflow_state.status,
                ).warning(
                    "Workflow for STARTED job is not RUNNING. Updating DB status."
                )
                final_status = ImportCsvJobStatus.from_temporal_workflow_status(
                    workflow_state.status
                )
                await self._repository.update_by_primary_key(
                    ImportJob,
                    primary_key_to_value={"id": job.id},
                    column_to_update={
                        "status": final_status,
                        "completed_at": zoned_utc_now()
                        if workflow_state.status == WorkflowExecutionStatus.COMPLETED
                        else job.completed_at,
                        "updated_at": zoned_utc_now(),
                    },
                )
            except temporalio.service.RPCError as e:
                logger.bind(
                    job_id=job.id, workflow_id=job.workflow_id, error=str(e)
                ).warning(
                    "Error describing workflow for STARTED job. Assuming not running."
                )
                await self._repository.update_by_primary_key(
                    ImportJob,
                    primary_key_to_value={"id": job.id},
                    column_to_update={
                        "status": ImportCsvJobStatus.FAILED,
                        "updated_at": zoned_utc_now(),
                    },
                )
        logger.bind(
            running_jobs_count=running_jobs_count,
            limit=self.import_job_max_global_num_concurrent,
        ).info("Checked running import jobs count.")

        available_slots = self.import_job_max_global_num_concurrent - running_jobs_count
        if available_slots <= 0:
            logger.bind(
                running_jobs_count=running_jobs_count, available_slots=available_slots
            ).info(
                f"Concurrency limit reached ({self.import_job_max_global_num_concurrent}). "
                f"No queued import jobs will be started. Queued Jobs loop finished"
            )
            return

        max_jobs_to_start = min(
            max_jobs_start, self.import_job_max_global_num_concurrent
        )  # sanity infinite loop prevention
        while available_slots > 0 and max_jobs_to_start > 0:
            # refetch everything each loop to have best effort multi-pod concurrency.
            # each loop iteration will handle at most a single job.
            max_jobs_to_start -= 1
            queued_jobs = await self._repository.global_list_by_status(
                ImportCsvJobStatus.QUEUED,
                limit=available_slots,
            )

            if not queued_jobs:
                logger.info("No QUEUED import jobs. Queued Jobs loop finished")
                return

            is_new_job_started = False
            for queued_job_to_start in queued_jobs:
                updated_job = await self._start_job_workflow(queued_job_to_start)
                if updated_job:
                    logger.bind(
                        job_id=updated_job.id,
                        workflow_id=updated_job.workflow_id,
                        organization_id=queued_job_to_start.organization_id,
                        available_slots=available_slots,
                        queued_jobs_count=len(queued_jobs),
                        updated_job_status=updated_job.status,
                    ).info("Handled queued job")
                    if updated_job.status == ImportCsvJobStatus.STARTED:
                        is_new_job_started = True
                        break
            if not is_new_job_started:
                break
            available_slots = (
                self.import_job_max_global_num_concurrent
                - await self._repository.global_count_by_status(
                    ImportCsvJobStatus.STARTED
                )
            )
        logger.info("Queued Jobs loop finished")

    async def process_file_import(
        self, organization_id: UUID, user_id: UUID, import_metadata: ImportMetadata
    ) -> ImportJob:
        import_objects_identifier = _import_objects_identifier(
            import_metadata.import_configuration.object_identifiers
        )
        job = await self.create_job(
            organization_id=organization_id,
            user_id=user_id,
            job_name=import_metadata.display_name,
            configuration=import_metadata.import_configuration,
            file_metadata=import_metadata.file_metadata[0],
            object_identifier=import_objects_identifier,
        )
        await self._try_start_queued_jobs_global(max_jobs_start=20)
        return not_none(await self._repository.get_by_id(job.id, organization_id))

    async def process_draft_file_import(
        self, organization_id: UUID, csv_import_job_id: UUID
    ) -> ImportJob:
        draft_job = await self._repository.get_by_id(csv_import_job_id, organization_id)
        if not draft_job:
            raise ValueError(
                f"Draft job({csv_import_job_id}) from organization({organization_id}) not found"
            )
        if draft_job.status != ImportCsvJobStatus.DRAFT:
            raise ValueError(
                f"Draft job({csv_import_job_id}) from organization({organization_id}) is not in DRAFT status({draft_job.status})"
            )

        cloned_job = strict_model_copy(
            draft_job,
            status=ImportCsvJobStatus.QUEUED,
            updated_at=zoned_utc_now(),
        )
        queued_job = await self._repository.update(cloned_job)
        if not queued_job:
            raise ValueError(
                f"Failed to update draft job ({csv_import_job_id}) to QUEUED status."
            )

        await self._try_start_queued_jobs_global(max_jobs_start=20)

        return not_none(
            await self._repository.get_by_id(queued_job.id, organization_id)
        )

    async def create_and_start_import_csv_job(
        self, organization_id: UUID, user_id: UUID
    ) -> ImportJob:
        job = await self.create_job(
            organization_id=organization_id,
            user_id=user_id,
            job_name="deprecated_job_flow",
            object_identifier="single_entity",
        )

        # piggyback call, remove after we have a temporal cron to call this
        await self._try_start_queued_jobs_global(max_jobs_start=10)

        return not_none(await self._repository.get_by_id(job.id, organization_id))

    async def get_job(self, job_id: UUID, organization_id: UUID) -> ImportJob | None:
        return await self._repository.get_by_id(job_id, organization_id)

    async def filter_jobs(
        self, organization_id: UUID, job_filter: ImportJobFilter
    ) -> list[ImportCsvJobDetail]:
        object_identifier = _import_objects_identifier(job_filter.object_identifiers)
        db_jobs = await self._repository.tenanted_list_by_object_identifier(
            organization_id, object_identifier
        )

        # If there's a limit, only sort the number we need
        if job_filter.limit:
            # Use heapq.nlargest which is more efficient for getting top N items
            sorted_jobs = nlargest(
                job_filter.limit, db_jobs, key=lambda job: job.created_at
            )
        else:
            # If no limit, sort all
            sorted_jobs = sorted(db_jobs, key=lambda job: job.created_at, reverse=True)

        return [ImportCsvJobDetail.from_db(job) for job in sorted_jobs]

    # TODO: Danger, there is no limit in this query.
    async def list_jobs(
        self,
        organization_id: UUID,
    ) -> list[ImportCsvJobDetail]:
        # piggyback call, remove after we have a temporal cron to call this
        await self._try_start_queued_jobs_global(max_jobs_start=5)
        db_jobs = await self._repository.list_by_organization(organization_id)
        result: list[ImportCsvJobDetail] = []
        for db_job in db_jobs:
            job_details = ImportCsvJobDetail.from_db(db_job)
            if job_details.status == ImportCsvJobStatus.STARTED and db_job.workflow_id:
                try:
                    handle = self._temporal_client.get_workflow_handle(
                        db_job.workflow_id
                    )
                    workflow_state = await handle.describe()
                    if workflow_state.status != WorkflowExecutionStatus.RUNNING:
                        final_status = ImportCsvJobStatus.from_temporal_workflow_status(
                            workflow_state.status
                        )
                        if (
                            db_job.status == ImportCsvJobStatus.STARTED
                            and final_status != ImportCsvJobStatus.STARTED
                        ):
                            logger.bind(
                                job_id=db_job.id,
                                old_status=db_job.status,
                                new_status=final_status,
                            ).info("Updating job status based on non-running workflow.")
                            await self._repository.update_by_tenanted_primary_key(
                                ImportJob,
                                primary_key_to_value={"id": db_job.id},
                                organization_id=organization_id,
                                column_to_update={
                                    "status": final_status,
                                    "completed_at": zoned_utc_now()
                                    if workflow_state.status
                                    == WorkflowExecutionStatus.COMPLETED
                                    else db_job.completed_at,
                                    "updated_at": zoned_utc_now(),
                                },
                            )
                except temporalio.service.RPCError as e:
                    logger.bind(
                        organization_id=organization_id,
                        db_job=db_job,
                        rpc_error_code=e.grpc_status.code,
                    ).error("Error getting workflow state.")

            if job_details.status != ImportCsvJobStatus.DRAFT:
                result.append(job_details)
        return result

    async def get_job_details(  # noqa:C901, PLR0912, PLR0915
        self, job_id: UUID, organization_id: UUID, lightweight_mode: bool = False
    ) -> ImportCsvJobDetail:
        job = await self._repository.get_by_id(job_id, organization_id)
        if not job:
            raise InvalidArgumentError("Import job not found")
        if not job.workflow_id:
            raise ValueError("Workflow not started for this job")

        db_import_records_map = await self.import_repository.map_type_by_job_id(
            organization_id=organization_id, job_id=job_id
        )
        job_details = ImportCsvJobDetail.from_db(job)
        handle = None
        try:
            if (
                not lightweight_mode
                and job_details.status == ImportCsvJobStatus.STARTED
            ):
                # piggyback call, remove after we have a temporal cron to call this
                await self._try_start_queued_jobs_global(max_jobs_start=2)
                handle = self._temporal_client.get_workflow_handle(job.workflow_id)
                workflow_state = await handle.describe()
                new_status = ImportCsvJobStatus.from_temporal_workflow_status(
                    workflow_state.status
                )
                if (
                    job.status == ImportCsvJobStatus.STARTED
                    and new_status != ImportCsvJobStatus.STARTED
                ):
                    logger.bind(
                        job_id=job.id, old_status=job.status, new_status=new_status
                    ).info(
                        "Updating job status based on non-running workflow in get_job_details."
                    )
                    await self._repository.update_by_tenanted_primary_key(
                        ImportJob,
                        primary_key_to_value={"id": job.id},
                        organization_id=organization_id,
                        column_to_update={
                            "status": new_status,
                            "completed_at": zoned_utc_now()
                            if workflow_state.status
                            == WorkflowExecutionStatus.COMPLETED
                            else job.completed_at,
                            "updated_at": zoned_utc_now(),
                        },
                    )
                    job_details.status = new_status
                elif job.status != new_status:
                    job_details.status = new_status
        except temporalio.service.RPCError as e:
            logger.bind(
                db_job=job, rpc_error_code=e.grpc_status.code, error_message=e.message
            ).error("Error querying workflow.")

        # populate original_filename from the existing file_metadata
        file_metadata = job_details.file_metadata
        if file_metadata and file_metadata.original_filename is None:
            file_metadata.original_filename = (
                await self.file_service.get_original_filename(
                    file_id=file_metadata.file_id, organization_id=organization_id
                )
            )

        # old import flow with multiple steps. TODO: deprecate this if flow after importV2 rollout
        if (
            not job_details.object_identifier
            or job_details.object_identifier == "single_entity"
        ):
            db_import_records_map = (
                await self.import_repository.map_type_by_reference_ids(
                    organization_id=organization_id, reference_id=str(job_id)
                )
            )
            for import_entity_type in [
                ImportEntityType.ACCOUNT,
                ImportEntityType.CONTACT,
                ImportEntityType.PIPELINE,
                ImportEntityType.MEETING,
            ]:
                job_details.import_entity_details[import_entity_type] = (
                    ImportEntityDetail(
                        import_records=db_import_records_map.get(import_entity_type, [])
                    )
                )
        else:
            if (
                not job_details.import_configuration
                or not job_details.import_configuration.object_identifiers
            ):
                raise ValueError(
                    "Missing import_configuration or import_configuration.object_identifiers"
                )
            logger.bind(
                job_details=job_details, db_import_records_map=db_import_records_map
            ).info("Start enriching job_details.")
            for (
                object_identifier
            ) in job_details.import_configuration.object_identifiers:
                # Convert object identifier to import entity type
                # Cast to ObjectIdentifier to satisfy type checker
                import_entity_type = ImportEntityType.from_object_identifier(
                    object_identifier
                )

                # Get import records for this type
                import_records = db_import_records_map.get(import_entity_type, [])

                # Initialize counters and non-success records
                success_count = 0
                conflict_count = 0
                failed_count = 0
                non_success_records = []

                # Single loop to count all statuses and collect non-success records
                for record in import_records:
                    match record.status:
                        case ImportRecordStatus.SUCCESS:
                            success_count += 1
                        case ImportRecordStatus.CONFLICT:
                            conflict_count += 1
                            non_success_records.append(record)
                        case ImportRecordStatus.FAILED:
                            failed_count += 1
                            non_success_records.append(record)
                        case ImportRecordStatus.VALIDATION_ERROR:
                            failed_count += 1
                            non_success_records.append(record)
                        case _:
                            # Include any other non-SUCCESS status
                            non_success_records.append(record)

                # Add import entity details for this type with only non-success records
                object_list_id = None
                if job.result_metadata and (
                    domain_object_list := job.result_metadata.created_list_map.get(
                        import_entity_type
                    )
                ):
                    object_list_id = domain_object_list.id

                job_details.import_entity_details[import_entity_type] = (
                    ImportEntityDetail(
                        object_identifier=object_identifier,
                        object_import_results={
                            ImportResultStats.TOTAL_RECORDS_ATTEMPTED: len(
                                import_records
                            ),
                            ImportResultStats.TOTAL_SUCCESS_RECORDS: success_count,
                            ImportResultStats.TOTAL_CONFLICT_RECORDS: conflict_count,
                            ImportResultStats.TOTAL_FAILED_RECORDS: failed_count,
                        },
                        object_list_id=object_list_id,
                        import_records=non_success_records
                        if not lightweight_mode
                        else [],
                    )
                )

            # Initialize aggregate import results
            job_details.aggregate_import_results = {
                ImportResultStats.TOTAL_RECORDS_ATTEMPTED: 0,
                ImportResultStats.TOTAL_SUCCESS_RECORDS: 0,
                ImportResultStats.TOTAL_CONFLICT_RECORDS: 0,
                ImportResultStats.TOTAL_FAILED_RECORDS: 0,
            }

            # Aggregate results from all import entity details
            for entity_detail in job_details.import_entity_details.values():
                for stat_key, stat_value in entity_detail.object_import_results.items():
                    if stat_key in job_details.aggregate_import_results:
                        job_details.aggregate_import_results[stat_key] += stat_value
            logger.bind(
                job_details=job_details, db_import_records_map=db_import_records_map
            ).info("Finished enriching job_details.")
        return job_details

    STANDARD_OBJECTS_PROCESS_ORDER: tuple[ImportEntityType, ...] = (
        ImportEntityType.ACCOUNT,
        ImportEntityType.CONTACT,
        ImportEntityType.PIPELINE,
    )


class SingletonImportJobService(Singleton, ImportJobService):
    pass


def get_import_job_service(
    db_engine: Annotated[DatabaseEngine, Depends(get_or_init_db_engine)],
    temporal_client: Annotated[Client, Depends(get_temporal_client)],
) -> ImportJobService:
    if SingletonImportJobService.has_instance():
        return SingletonImportJobService.get_singleton_instance()
    import_csv_job_repository = ImportJobRepository(engine=db_engine)
    import_repository = ImportRepository(engine=db_engine)
    file_service = file_service_from_engine(engine=db_engine)
    return SingletonImportJobService(
        import_csv_job_repository, import_repository, file_service, temporal_client
    )
