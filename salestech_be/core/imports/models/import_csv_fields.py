from typing import Any

from pydantic import AliasChoices, BaseModel, ConfigDict, Field, field_validator

from salestech_be.util.pydantic_types.str import (
    extract_facebook_handle,
    extract_linkedin_handle,
    extract_twitter_handle,
    validate_domain_name,
)
from salestech_be.util.validation import strip_str_or_none

# From variable aliases ordered from most cananical to least canonical
# Variable choices originally come from
#   https://www.notion.so/reevo/Importing-experience-and-polish-plan-1997019cb5b180a39b3ed61bffeaea6c#19d7019cb5b1804397b0d05efcfd1745
# FE 2025-03-04: Std field list are here
#   https://github.com/ReevoAI/frontend-monorepo/blob/33cca82c50514036121abcf5d871162b08df1bd3/apps/reevo-webapp/src/modules/imports/client/constants/importWizardConstants.ts#L50
# Using extra=Extra.allow to allow for broader field support, although ideally we remove this.


def _copy_model_extra(src: dict[str, Any] | None, dst: dict[str, Any] | None) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    if src is not None and dst is not None:
        for key, value in src.items():
            if dst.get(key) is None:
                dst[key] = value


# Internal usage of model_extra keys
# a custom field data in model_extra representing a dict of custom field UUID to value
CUSTOM_FIELD_DATA = "custom_field_data"
# This CSV_TYPE field is currently necessary because our CSV import flow allows
#   multiple reevo objects per row, and the code is not strict when a new entity
#   is created to create only that entity. For example, an account object may be
#   created during the course of creating a pipeline object.)  See
#   "_copy_model_extra" below.  For these cases, the entire model_extra is
#   just blindly copied and available during the creation of each of these objects and
#   it is necessary to determine under which context the custom field data is being
#   created under, because custom_objects also get blindly created (call it multiple
#   times and you may have multiple attached custom objects.)
CSV_TYPE = "csv_type"


class AccountImportCsvFields(BaseModel):
    model_config = ConfigDict(
        # Allowing extra fields for custom field support
        # Historically handled by: https://github.com/ReevoAI/salestech-be/blob/d6c2058fe6b73942ad238d7bf74b24b2bdd598cf/salestech_be/core/imports/service/crm_sync_service.py#L2458-L2493
        extra="allow",
        # Allow populating fields by both name and alias
        validate_by_name=True,
        validate_by_alias=True,
    )

    # This converts empty string fields to None. But DOES NOT do so for model_extra fields.
    @field_validator("*", mode="before")
    @classmethod
    def empty_str_to_none(cls, v: str | None) -> str | None:
        if v == "":
            return None
        return v

    company_domain_name: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_domain_name",
            "domain_name",
        ),
    )
    company_official_website: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_official_website",
            "official_website",
            "official_website_url",  # FE 2025-03-04
            "company_official_website_url",  # FE 2025-03-04
        ),
    )
    company_display_name: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_display_name",  # AliasChoices must include the original field name
            "display_name",
        ),
    )
    company_description: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_description",
            "description",
        ),
    )
    company_industry: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_industry",
            "industry",
            "company_categories",  # Historical field name, backwards compatibility
            "company_category",
            "industries",
            "category_list",
        ),
    )
    company_technologies: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_technologies",
            "technologies",
            "technology_list",  # FE 2025-04-11
        ),
    )
    company_estimated_annual_revenue: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_estimated_annual_revenue",
            "estimated_annual_revenue",
        ),
    )
    company_estimated_employee_count: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_estimated_employee_count",
            "estimated_employee_count",
        ),
    )
    company_linkedin_url: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_linkedin_url",
            "linkedin_url",
        ),
    )
    company_facebook_url: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_facebook_url",
            "facebook_url",
        ),
    )
    company_x_url: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_x_url",
            "x_url",
        ),
    )
    company_owner_email: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_owner_email",
            "owner_user_id",
            "owner_email",
        ),
    )
    company_created_at: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_created_at",
            "created_at",
        ),
    )
    company_status: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_status",
            "status",
            "account_state",  # FE 2025-03-04
            "company_account_state",  # FE 2025-03-04
        ),
    )

    def unique_identifier(self) -> str | None:  # noqa: C901,PLR0911
        if self.company_domain_name:  # noqa: SIM102
            if domain_name := validate_domain_name(self.company_domain_name):
                return domain_name
        if self.company_official_website:  # noqa: SIM102
            if domain_name := validate_domain_name(self.company_official_website):
                return domain_name
        if self.company_linkedin_url:  # noqa: SIM102
            if linkedin_handle := extract_linkedin_handle(self.company_linkedin_url):
                return f"linkedin.{linkedin_handle}"
        if self.company_facebook_url:  # noqa: SIM102
            if facebook_handle := extract_facebook_handle(self.company_facebook_url):
                return f"facebook.{facebook_handle}"
        if self.company_x_url:  # noqa: SIM102
            if x_handle := extract_twitter_handle(self.company_x_url):
                return f"x.{x_handle}"
        if self.company_display_name:
            return f"display_name.{self.company_display_name.strip()}"
        return None

    def get_official_website(self) -> str | None:
        to_return = self.company_official_website or self.company_domain_name
        return strip_str_or_none(to_return)


class ContactImportCsvFields(BaseModel):
    model_config = ConfigDict(
        # Historically handled by: https://github.com/ReevoAI/salestech-be/blob/d6c2058fe6b73942ad238d7bf74b24b2bdd598cf/salestech_be/core/imports/service/crm_sync_service.py#L2393-L2420
        extra="allow",
        # Allow populating fields by both name and alias
        validate_by_name=True,
        validate_by_alias=True,
    )

    # This converts empty string fields to None. But DOES NOT do so for model_extra fields.
    @field_validator("*", mode="before")
    @classmethod
    def empty_str_to_none(cls, v: str | None) -> str | None:
        if v == "":
            return None
        return v

    contact_primary_email: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_primary_email",
            "primary_email",
            "primary_contact_email",  # Important legacy field mapping
        ),
    )
    contact_display_name: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_display_name",  # AliasChoices must include the original field name
            "display_name",
        ),
    )
    contact_first_name: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_first_name",
            "first_name",
        ),
    )
    contact_last_name: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_last_name",
            "last_name",
        ),
    )
    company_display_name: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_display_name",
            "company_name",  # FE 2025-03-04
            "contact_company_name",  # FE 2025-03-04
        ),
    )
    company_domain_name: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_domain_name",
            "contact_company_domain_name",  # FE 2025-03-04
        ),
    )
    company_official_website: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_official_website",
            "contact_company_official_website",  # FE 2025-03-04
        ),
    )
    contact_primary_phone_number: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_primary_phone_number",
            "primary_phone_number",
        ),
    )
    # Represents the name of the stage of a Contact, example stage values being
    # "Cold", "Reached Out", "Replied", and "Disqualified"
    contact_stage_value: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_stage_value",
            "stage_value",
            "stage_id",  # FE 2025-04-11, This is technically wrong, leave this here until FE stops using it.
            "stage",  # FE 2025-04-18
        ),
    )
    contact_owner_email: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_owner_email",
            "owner_user_id",
            "owner_email",
        ),
    )
    contact_job_title: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_job_title",
            "job_title",
            "title",  # FE 2025-03-04
            "contact_title",  # FE 2025-03-04
        ),
    )
    contact_created_at: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_created_at",
            "created_at",
        ),
    )
    # deprecated 2025-04-17, prefer Department from account_contact_role
    #   Remove this field once original old import flow is gone.
    contact_department: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_department",
            "department",
        ),
    )
    contact_linkedin_url: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_linkedin_url",
            "linkedin_url",
        ),
    )
    contact_facebook_url: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_facebook_url",
            "facebook_url",
        ),
    )
    contact_x_url: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_x_url",
            "x_url",
        ),
    )

    def to_associated_account_import_csv_fields(self) -> AccountImportCsvFields:
        to_return = AccountImportCsvFields(
            company_display_name=self.company_display_name,
            company_domain_name=self.company_domain_name,
            company_official_website=self.company_official_website,
            company_description=None,
            company_industry=None,
            company_technologies=None,
            company_estimated_annual_revenue=None,
            company_estimated_employee_count=None,
            company_linkedin_url=None,
            company_facebook_url=None,
            company_x_url=None,
            company_owner_email=None,
            company_created_at=None,
            company_status=None,
        )
        _copy_model_extra(src=self.model_extra, dst=to_return.model_extra)
        return to_return
        # Any model_extra fields to include?  Ie. Address?


class OpportunityImportCsvFields(BaseModel):
    # Historically handled by: https://github.com/ReevoAI/salestech-be/blob/d6c2058fe6b73942ad238d7bf74b24b2bdd598cf/salestech_be/core/imports/service/crm_sync_service.py#L2357-L2378
    model_config = ConfigDict(
        # Allowing extra fields for custom field support
        # Historically handled by: https://github.com/ReevoAI/salestech-be/blob/d6c2058fe6b73942ad238d7bf74b24b2bdd598cf/salestech_be/core/imports/service/crm_sync_service.py#L2458-L2493
        extra="allow",
        # Allow populating fields by both name and alias
        validate_by_name=True,
        validate_by_alias=True,
    )

    @field_validator("*", mode="before")
    @classmethod
    def empty_str_to_none(cls, v: str | None) -> str | None:
        if v == "":
            return None
        return v

    opportunity_display_name: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "opportunity_display_name",  # AliasChoices must include the original field name
            "display_name",
            "pipeline_display_name",  # FE 2025-03-04
        ),
    )
    # stage_value represents the name of each stage, example values are "New", and "Nurturing"
    # Reevo currently only has "Default" Stage List Names, which represents the name of the entire
    # stage list, but the backend is prepared to support more Stage Lists in the future.
    opportunity_stage_value: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "opportunity_stage_value",
            "stage_list_value",
            "stage_value",
            "pipeline_stage_value",  # FE 2025-03-04
            "stage_id",  # FE 2025-04-11
            "stage",  # FE 2025-04-18
        ),
    )
    opportunity_pipeline_id: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "opportunity_pipeline_id",
            "stage_pipeline_id",
            "pipeline_id",
        ),
    )
    opportunity_deal_amount: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "opportunity_deal_amount",
            "deal_amount",
            "pipeline_deal_amount",  # FE 2025-03-04
            "amount",  # FE 2025-04-11
        ),
    )
    contact_primary_email: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_primary_email",
            "primary_contact_email",  # FE 2025-03-04
            "pipeline_primary_contact_email",  # FE 2025-03-04
        ),
    )
    company_display_name: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_display_name",
            "company_name",  # FE 2025-03-04
            "pipeline_company_name",  # FE 2025-03-04
        ),
    )
    company_domain_name: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "company_domain_name",
            "pipeline_company_domain_name",  # FE 2025-03-04
        ),
    )
    company_official_website: str | None = Field(
        None,
    )
    opportunity_owner_email: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "opportunity_owner_email",
            "owner_user_id",
            "owner_email",
        ),
    )
    opportunity_created_at: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "opportunity_created_at",
            "created_at",
        ),
    )
    opportunity_closed_reasons: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "closed_reason_select_list_values",
            "opportunity_closed_reasons",
        ),
    )
    opportunity_closed_reason_custom_details: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "opportunity_closed_reason_custom_details",
            "closed_reason_custom_details",
        ),
    )
    opportunity_next_step_details: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "opportunity_next_step_details",
            "next_step_details",
        ),
    )
    opportunity_next_step_due_at: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "opportunity_next_step_due_at",
            "next_step_due_at",
        ),
    )
    opportunity_anticipated_closing_at: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "opportunity_anticipated_closing_at",
            "anticipated_closing_at",
        ),
    )
    opportunity_expires_at: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "opportunity_expires_at",
            "expires_at",
        ),
    )
    opportunity_closed_at: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "opportunity_closed_at",
            "closed_at",
        ),
    )
    # deprecated 2025-04-17, prefer ContactOpportunityRoleImportCsvFields
    #   Remove this field once original old import flow is gone.
    contact_pipeline_role: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_opportunity_role",
            "contact_pipeline_role",
        ),
    )
    # deprecated 2025-04-17, prefer ContactOpportunityRoleImportCsvFields
    #   Remove this field once original old import flow is gone.
    contact_pipeline_note: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_opportunity_note",
            "contact_pipeline_note",
        ),
    )

    def to_associated_account_import_csv_fields(self) -> AccountImportCsvFields:
        to_return = AccountImportCsvFields(
            company_display_name=self.company_display_name,
            company_domain_name=self.company_domain_name,
            company_official_website=self.company_official_website,
            company_description=None,
            company_industry=None,
            company_technologies=None,
            company_estimated_annual_revenue=None,
            company_estimated_employee_count=None,
            company_linkedin_url=None,
            company_facebook_url=None,
            company_x_url=None,
            company_owner_email=None,
            company_created_at=None,
            company_status=None,
        )
        _copy_model_extra(src=self.model_extra, dst=to_return.model_extra)
        return to_return

    def to_associated_contact_import_csv_fields(self) -> ContactImportCsvFields:
        to_return = ContactImportCsvFields(
            contact_primary_email=self.contact_primary_email,
            contact_first_name=None,
            contact_last_name=None,
            company_display_name=None,
            company_domain_name=None,
            company_official_website=None,
            contact_stage_value=None,
            contact_display_name=None,
            contact_primary_phone_number=None,
            contact_owner_email=None,
            contact_job_title=None,
            contact_created_at=None,
            contact_department=None,
            contact_linkedin_url=None,
            contact_facebook_url=None,
            contact_x_url=None,
        )
        _copy_model_extra(src=self.model_extra, dst=to_return.model_extra)
        return to_return


class ContactAccountRoleImportCsvFields(BaseModel):
    model_config = ConfigDict(
        # Allowing extra fields for custom field support
        # Historically handled by: https://github.com/ReevoAI/salestech-be/blob/d6c2058fe6b73942ad238d7bf74b24b2bdd598cf/salestech_be/core/imports/service/crm_sync_service.py#L2458-L2493
        extra="allow",
        # Allow populating fields by both name and alias
        validate_by_name=True,
        validate_by_alias=True,
    )

    @field_validator("*", mode="before")
    @classmethod
    def empty_str_to_none(cls, v: str | None) -> str | None:
        if v == "":
            return None
        return v

    contact_account_role_title: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_account_role_title",
            "job_title",
            "title",
        ),
    )
    contact_account_role_department: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_account_role_department",
            "department",
        ),
    )


class ContactOpportunityRoleImportCsvFields(BaseModel):
    model_config = ConfigDict(
        # Allowing extra fields for custom field support
        # Historically handled by: https://github.com/ReevoAI/salestech-be/blob/d6c2058fe6b73942ad238d7bf74b24b2bdd598cf/salestech_be/core/imports/service/crm_sync_service.py#L2458-L2493
        extra="allow",
        # Allow populating fields by both name and alias
        validate_by_name=True,
        validate_by_alias=True,
    )

    @field_validator("*", mode="before")
    @classmethod
    def empty_str_to_none(cls, v: str | None) -> str | None:
        if v == "":
            return None
        return v

    contact_opportunity_note: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_opportunity_note",
            "contact_pipeline_note",
            "note",
        ),
    )
    contact_opportunity_role_types: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "contact_opportunity_role_types",
            "contact_pipeline_role_types",
            "role_types",
        ),
    )


class UserImportCsvFields(BaseModel):
    model_config = ConfigDict(
        # Allowing extra fields for custom field support
        # Historically handled by: https://github.com/ReevoAI/salestech-be/blob/d6c2058fe6b73942ad238d7bf74b24b2bdd598cf/salestech_be/core/imports/service/crm_sync_service.py#L2458-L2493
        extra="allow",
        # Allow populating fields by both name and alias
        validate_by_name=True,
        validate_by_alias=True,
    )

    # This converts empty string fields to None. But DOES NOT do so for model_extra fields.
    @field_validator("*", mode="before")
    @classmethod
    def empty_str_to_none(cls, v: str | None) -> str | None:
        if v == "":
            return None
        return v

    user_email_address: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "user_email_address",  # AliasChoices must include the original field name
            "email_address",
        ),
    )


class CustomObjectImportCsvFields(BaseModel):
    model_config = ConfigDict(
        # Allowing extra fields for custom field support
        # Historically handled by: https://github.com/ReevoAI/salestech-be/blob/d6c2058fe6b73942ad238d7bf74b24b2bdd598cf/salestech_be/core/imports/service/crm_sync_service.py#L2458-L2493
        extra="allow",
        # Allow populating fields by both name and alias
        validate_by_name=True,
        validate_by_alias=True,
    )

    # This converts empty string fields to None. But DOES NOT do so for model_extra fields.
    @field_validator("*", mode="before")
    @classmethod
    def empty_str_to_none(cls, v: str | None) -> str | None:
        if v == "":
            return None
        return v

    custom_object_display_name: str | None = Field(
        None,
        validation_alias=AliasChoices(
            "custom_object_display_name",  # AliasChoices must include the original field name
            "display_name",
        ),
    )
