from __future__ import annotations

import uuid
from decimal import Decimal
from enum import StrEnum, auto
from typing import Any
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.type.formatted_string import strip_and_lower
from salestech_be.common.type.metadata.common import (
    CustomObjectIdentifier,
    StandardObjectIdentifier,
)
from salestech_be.common.type.patch_request import UNSET, BasePatchRequest, UnsetAware
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.files.type.models import S3BucketType
from salestech_be.core.imports.models.import_csv_fields import (
    AccountImportCsvFields,
    ContactImportCsvFields,
    OpportunityImportCsvFields,
)
from salestech_be.core.imports.models.import_job import (
    CSVImportState,
    FileMetadata,
    ImportConfiguration,
    ImportCsvJobStatus,
    ImportJob,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.db.models.account import Account
from salestech_be.db.models.address import Address
from salestech_be.db.models.contact import (
    Contact,
    CreateContactRequest,
    CreateDbContactRequest,
)
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.db.models.import_record import (
    ImportEntityType,
    ImportRecordStatus,
)
from salestech_be.db.models.import_record import (
    ImportRecord as DbImportRecord,
)
from salestech_be.db.models.pipeline import Pipeline as PipelineDb
from salestech_be.db.models.pipeline import PipelineStatus
from salestech_be.db.models.pipeline_stage_select_list_value_metadata import (
    PipelineOutcomeState,
)
from salestech_be.db.models.task import Task as TaskDb
from salestech_be.integrations.hubspot.model import HubSpotDeal
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.validation import (
    cast_datetime_or_none,
    cast_decimal_or_none,
    cast_int_or_none,
    none_or_split,
    not_none,
    strip_str_or_none,
)


class ImportMetricsName(StrEnum):
    IMPORT_UPLOAD_OUTPUT_FILE = "import_upload_output_file"
    IMPORT_CREATE_DOMAIN_OBJECT_LIST = "import_create_domain_object_list"
    IMPORT_PROCESSING_ERROR = "import_processing_error"
    IMPORT_UNKNOWN_ERROR = "import_unknown_error"
    IMPORT_ACCOUNT = "import_account"
    IMPORT_CONTACT = "import_contact"
    IMPORT_PIPELINE = "import_pipeline"
    IMPORT_CONTACT_ACCOUNT_ROLE = "import_contact_account_role"
    IMPORT_CONTACT_PIPELINE_ROLE = "import_contact_pipeline_role"
    IMPORT_CUSTOM_OBJECT = "import_custom_object"
    IMPORT_CUSTOM_OBJECT_ASSOCIATION = "import_custom_object_association"
    IMPORT_V0_RECORDS_ERROR = "import_v0_records_error"
    IMPORT_V0_ACCOUNT = "import_v0_account"
    IMPORT_V0_CONTACT = "import_v0_contact"
    IMPORT_V0_PIPELINE = "import_v0_pipeline"
    IMPORT_V0_MEETING = "import_v0_meeting"
    IMPORT_V0_CUSTOM_OBJECT = "import_v0_custom_object"


class EnrichType(StrEnum):
    CONTACT_WITH_PHONE_NUMBER = "CONTACT_WITH_PHONE_NUMBER"
    CONTACT_BASIC = "CONTACT_BASIC"


class PipelineStageRequest(BaseModel):
    id: str | None = None
    display_name: str
    probability: int
    is_closed: bool = False
    label: str | None = None

    def outcome_state(self) -> PipelineOutcomeState | None:
        if not self.is_closed:
            return None
        return (
            PipelineOutcomeState.CLOSED_WON
            if self.probability == 100  # noqa: PLR2004
            else PipelineOutcomeState.CLOSED_LOST
        )

    def pipeline_status(self) -> PipelineStatus:
        return PipelineStatus.DEAL if self.is_closed else PipelineStatus.PROSPECT


class ImportRecord(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    id: uuid.UUID
    import_entity_type: ImportEntityType
    status: ImportRecordStatus
    status_detail: str | None
    created_at: ZoneRequiredDateTime
    updated_at: ZoneRequiredDateTime | None = None
    job_id: uuid.UUID | None = None
    row_reference_id: str | None = None
    entity_id: uuid.UUID | None
    existing_record: dict[str, Any] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
    incoming_record: dict[str, Any] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
    external_id: str | None = None

    @staticmethod
    def map_from_db(db_import_record: DbImportRecord) -> ImportRecord:
        return ImportRecord(
            id=db_import_record.id,
            import_entity_type=ImportEntityType(db_import_record.import_entity_type),
            status=ImportRecordStatus(db_import_record.status),
            status_detail=db_import_record.status_detail,
            created_at=db_import_record.created_at,
            updated_at=db_import_record.updated_at,
            job_id=db_import_record.job_id,
            row_reference_id=db_import_record.row_reference_id,
            entity_id=db_import_record.entity_id,
            existing_record=db_import_record.existing_record,
            incoming_record=db_import_record.conflicting_record,
            external_id=db_import_record.external_id,
        )

    def __eq__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self is other or self.id == other.id

    def __hash__(self) -> int:
        return hash(self.id)


class ImportAccountRecord(CreateAccountRequest):
    @classmethod
    def from_account(cls, account: AccountV2) -> ImportAccountRecord:
        return ImportAccountRecord(
            display_name=account.display_name,
            owner_user_id=account.owner_user_id,
            official_website=account.official_website,
            description=account.description,
            keyword_list=account.keyword_list,
            category_list=account.category_list,
            technology_list=account.technology_list,
            estimated_annual_revenue=cast_decimal_or_none(
                account.estimated_annual_revenue
            ),
            estimated_employee_count=account.estimated_employee_count,
            linkedin_url=account.linkedin_url,
            facebook_url=account.facebook_url,
            x_url=account.x_url,
        )

    @classmethod
    def from_account_db_and_address(
        cls, account_db: Account, address: Address | None
    ) -> ImportAccountRecord:
        address_dict = address.model_dump(exclude_unset=True) if address else {}
        account_db_dict = account_db.model_dump(exclude_unset=True)
        shared_keys = address_dict.keys() & account_db_dict.keys()
        address_dict_no_shared_keys = (
            address.model_dump(exclude_unset=True, exclude=shared_keys)
            if address
            else {}
        )
        return ImportAccountRecord(
            **address_dict_no_shared_keys,
            **account_db_dict,
        )

    @classmethod
    def from_import_account_dict(
        cls,
        account_import_csv_fields: AccountImportCsvFields,
        owner_user_id: uuid.UUID,
    ) -> ImportAccountRecord:
        owner_user_id_model_extra = (
            account_import_csv_fields.model_extra.get("owner_user_id")
            if account_import_csv_fields.model_extra
            else None
        )
        return ImportAccountRecord(
            display_name=account_import_csv_fields.company_display_name or "UNKNOWN",
            owner_user_id=uuid.UUID(owner_user_id_model_extra)
            if owner_user_id_model_extra
            else owner_user_id,
            official_website=account_import_csv_fields.get_official_website(),
            description=account_import_csv_fields.company_description,
            keyword_list=none_or_split(
                account_import_csv_fields.model_extra.get("company_keywords")
            )
            if account_import_csv_fields.model_extra
            else None,
            category_list=none_or_split(account_import_csv_fields.company_industry),
            technology_list=none_or_split(
                account_import_csv_fields.company_technologies
            ),
            estimated_annual_revenue=cast_decimal_or_none(
                account_import_csv_fields.company_estimated_annual_revenue
            ),
            estimated_employee_count=cast_int_or_none(
                account_import_csv_fields.company_estimated_employee_count
            ),
            linkedin_url=strip_str_or_none(
                account_import_csv_fields.company_linkedin_url
            ),
            facebook_url=strip_str_or_none(
                account_import_csv_fields.company_facebook_url
            ),
            x_url=strip_str_or_none(account_import_csv_fields.company_x_url),
        )


class ImportContactRecord(CreateContactRequest):
    def __eq__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        contact = self.contact
        other_contact = other.contact
        return (
            strip_and_lower(contact.display_name)
            == strip_and_lower(other_contact.display_name)
            and strip_and_lower(contact.first_name)
            == strip_and_lower(other_contact.first_name)
            and strip_and_lower(contact.last_name)
            == strip_and_lower(other_contact.last_name)
            and strip_and_lower(contact.title) == strip_and_lower(other_contact.title)
            and strip_and_lower(contact.x_url) == strip_and_lower(other_contact.x_url)
            and strip_and_lower(contact.primary_phone_number)
            == strip_and_lower(other_contact.primary_phone_number)
            and strip_and_lower(contact.linkedin_url)
            == strip_and_lower(other_contact.linkedin_url)
            and strip_and_lower(contact.zoominfo_url)
            == strip_and_lower(other_contact.zoominfo_url)
            and strip_and_lower(contact.owner_user_id)
            == strip_and_lower(other_contact.owner_user_id)
            and strip_and_lower(contact.department)
            == strip_and_lower(other_contact.department)
            and strip_and_lower(contact.facebook_url)
            == strip_and_lower(other_contact.facebook_url)
            and self.contact_emails == other.contact_emails
        )

    @classmethod
    def from_domain_contact(cls, contact_response: ContactV2) -> ImportContactRecord:
        return ImportContactRecord(
            contact=CreateDbContactRequest(
                display_name=contact_response.display_name,
                first_name=contact_response.first_name,
                last_name=contact_response.last_name,
                middle_name=contact_response.middle_name,
                primary_phone_number=contact_response.primary_phone_number,
                linkedin_url=contact_response.linkedin_url,
                zoominfo_url=contact_response.zoominfo_url,
                owner_user_id=contact_response.owner_user_id,
                address=None,
                title=contact_response.title,
                department=contact_response.department,
                facebook_url=contact_response.facebook_url,
                x_url=contact_response.x_url,
                stage_id=contact_response.stage_id,
                created_by_user_id=contact_response.created_by_user_id,
            )
        )

    @classmethod
    def from_create_contact_request(
        cls, create_contact_request: CreateContactRequest
    ) -> ImportContactRecord:
        return ImportContactRecord(contact=create_contact_request.contact)

    @classmethod
    def from_db_contact(
        cls,
        db_contact: Contact,
    ) -> ImportContactRecord:
        return ImportContactRecord(
            contact=CreateDbContactRequest(
                display_name=db_contact.display_name,
                first_name=db_contact.first_name,
                last_name=db_contact.last_name,
                middle_name=db_contact.middle_name,
                primary_phone_number=db_contact.primary_phone_number,
                linkedin_url=db_contact.linkedin_url,
                zoominfo_url=db_contact.zoominfo_url,
                owner_user_id=db_contact.owner_user_id,
                created_source=db_contact.created_source,
                address=None,
                title=db_contact.title,
                department=db_contact.department,
                facebook_url=db_contact.facebook_url,
                x_url=db_contact.x_url,
                stage_id=db_contact.stage_id,
                created_by_user_id=db_contact.created_by_user_id,
            )
        )

    @classmethod
    def from_import_dict(
        cls,
        contact_import_csv_fields: ContactImportCsvFields,
        user_id: uuid.UUID,
        owner_user_id: uuid.UUID,
        stage_id: uuid.UUID,
    ) -> ImportContactRecord:
        return ImportContactRecord(
            contact=CreateDbContactRequest(
                display_name=contact_import_csv_fields.contact_display_name or "",
                created_by_user_id=user_id,
                owner_user_id=owner_user_id,
                first_name=contact_import_csv_fields.contact_first_name,
                last_name=contact_import_csv_fields.contact_last_name,
                primary_phone_number=contact_import_csv_fields.contact_primary_phone_number,
                linkedin_url=contact_import_csv_fields.contact_linkedin_url,
                facebook_url=contact_import_csv_fields.contact_facebook_url,
                x_url=contact_import_csv_fields.contact_x_url,
                department=contact_import_csv_fields.contact_department,
                stage_id=stage_id,
                title=contact_import_csv_fields.contact_job_title,
                created_source=CreatedSource.CSV_IMPORT,
            )
        )


class ImportPipelineRecord(BaseModel):
    model_config = {
        "json_encoders": {
            Decimal: lambda d: format(d, "f")  # fixed-point notation (i.e. "50000000")
        }
    }

    display_name: str
    amount: str | None = None
    company_external_ids: list[str] = []
    contact_external_ids: list[str] = []
    stage: str | None = None
    owner_external_id: str | None = None
    anticipated_closing_at: ZoneRequiredDateTime | None = None

    @classmethod
    def from_hubspot_deal(cls, deal: HubSpotDeal) -> ImportPipelineRecord:
        # Extract external company IDs from deal associations if available
        companies = deal.associations.get("companies") if deal.associations else None
        company_results = companies.results if companies else []
        company_external_ids = [
            item.id for item in company_results if item.type == "deal_to_company"
        ]

        # Extract external contact IDs from deal associations if available
        contacts = deal.associations.get("contacts") if deal.associations else None
        contact_results = contacts.results if contacts else []
        contact_external_ids = [
            item.id for item in contact_results if item.type == "deal_to_contact"
        ]

        # anticipated_closing_at is taken from the closedate field
        anticipated_closing_at = (
            deal.properties.closedate if deal.properties.closedate else None
        )

        return ImportPipelineRecord(
            display_name=deal.properties.display_name,
            amount=deal.properties.deal_amount,
            stage=deal.properties.deal_stage,
            owner_external_id=deal.properties.hubspot_owner_id,
            company_external_ids=company_external_ids,
            contact_external_ids=contact_external_ids,
            anticipated_closing_at=anticipated_closing_at,
        )

    @classmethod
    def from_dict(cls, json_dict: dict[str, Any]) -> ImportPipelineRecord:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        def split_or_empty(value: str | list[str] | None) -> list[str]:
            if value:
                if isinstance(value, str):
                    return [item.strip() for item in value.split(",") if item.strip()]
                else:
                    return value
            return []

        return ImportPipelineRecord(
            display_name=json_dict.get("display_name", ""),
            amount=json_dict.get("amount", "") if json_dict.get("amount") else None,
            company_external_ids=split_or_empty(json_dict.get("company_external_ids")),
            contact_external_ids=split_or_empty(json_dict.get("contact_external_ids")),
            stage=json_dict.get("stage"),
            owner_external_id=json_dict.get("owner_external_id"),
            anticipated_closing_at=cast_datetime_or_none(
                json_dict.get("anticipated_closing_at"),
                local_timezone=None,
            ),
        )

    @classmethod
    def from_pipeline_db(cls, pipeline_db: PipelineDb) -> ImportPipelineRecord:
        return ImportPipelineRecord(
            display_name=pipeline_db.display_name,
            amount=str(pipeline_db.amount) if pipeline_db.amount else None,
            # company_external_ids
            # contact_external_ids
            # stage=pipeline_db.stage_id,
            owner_external_id=str(pipeline_db.owner_user_id)
            if pipeline_db.owner_user_id
            else None,
            anticipated_closing_at=pipeline_db.anticipated_closing_at,
        )

    @classmethod
    def from_pipeline_v2(cls, pipeline: PipelineV2) -> ImportPipelineRecord:
        return ImportPipelineRecord(
            display_name=pipeline.display_name,
            amount=str(pipeline.amount) if pipeline.amount else None,
            anticipated_closing_at=pipeline.anticipated_closing_at,
            stage=pipeline.stage.display_value,
        )

    @classmethod
    def from_opportunity_import_csv_fields(
        cls, fields: OpportunityImportCsvFields
    ) -> ImportPipelineRecord:
        return ImportPipelineRecord(
            display_name=not_none(fields.opportunity_display_name),
            amount=fields.opportunity_deal_amount,
            anticipated_closing_at=cast_datetime_or_none(
                fields.opportunity_anticipated_closing_at,
                local_timezone=None,
            ),
            stage=fields.opportunity_stage_value,
        )

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, ImportPipelineRecord):
            return False
        return (
            strip_and_lower(self.display_name) == strip_and_lower(other.display_name)
            and strip_and_lower(self.amount) == strip_and_lower(other.amount)
            and strip_and_lower(self.anticipated_closing_at)
            == strip_and_lower(other.anticipated_closing_at)
            and strip_and_lower(self.stage) == strip_and_lower(other.stage)
        )


class ImportTaskRecord(BaseModel):
    title: str
    due_at: str | None = None
    note: str | None = None

    @classmethod
    def from_task_db(cls, task_db: TaskDb) -> ImportTaskRecord:
        return ImportTaskRecord(
            title=task_db.title,
            due_at=str(task_db.due_at) if task_db.due_at else None,
            note=task_db.note,
        )

    @classmethod
    def from_dict(cls, json_dict: dict[str, Any]) -> ImportTaskRecord:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return ImportTaskRecord(
            title=json_dict.get("title", ""),
            due_at=json_dict.get("due_at"),
            note=json_dict.get("note"),
        )


class CSVImportSignal(NameValueStrEnum):
    CANCEL_IMPORT = auto()

    SKIP_ACCOUNT_IMPORT = auto()
    SET_ACCOUNT_FILE_ID = auto()
    CHECK_ACCOUNT_UPLOAD_ERRORS = auto()

    SKIP_CONTACT_IMPORT = auto()
    SET_CONTACT_FILE_ID = auto()
    CHECK_CONTACT_UPLOAD_ERRORS = auto()

    SKIP_PIPELINE_IMPORT = auto()
    SET_PIPELINE_FILE_ID = auto()
    CHECK_PIPELINE_UPLOAD_ERRORS = auto()

    SKIP_MEETING_IMPORT = auto()
    SET_MEETING_FILE_ID = auto()
    CHECK_MEETING_UPLOAD_ERRORS = auto()


class ImportResultStats(NameValueStrEnum):
    TOTAL_RECORDS_ATTEMPTED = auto()
    TOTAL_SUCCESS_RECORDS = auto()
    TOTAL_CONFLICT_RECORDS = auto()
    TOTAL_FAILED_RECORDS = auto()


class ImportEntityDetail(BaseModel):
    object_identifier: StandardObjectIdentifier | CustomObjectIdentifier | None = None
    object_import_results: dict[ImportResultStats, int] = {}
    object_list_id: UUID | None = None
    errors: list[str] = []
    import_records: list[ImportRecord] = []


class ImportOutputFile(BaseModel):
    s3_key: str
    bucket_type: S3BucketType = S3BucketType.IMPORT_CSV
    direct_link: str | None = None


class ImportCsvJobDetail(BaseModel):
    id: UUID
    workflow_id: str
    organization_id: UUID
    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID | None = None
    updated_at: ZoneRequiredDateTime
    completed_at: ZoneRequiredDateTime | None = None
    state: CSVImportState | None = Field(
        default=None,
        description="Deprecated: Use status instead, coordinate with FE to remove",
    )
    import_entity_details: dict[ImportEntityType, ImportEntityDetail] = {}
    output_file: ImportOutputFile | None = None
    aggregate_import_results: dict[ImportResultStats, int] = {}
    status: ImportCsvJobStatus | None = None
    display_name: str | None = None
    object_identifier: str | None = None
    import_configuration: ImportConfiguration | None = None
    file_metadata: FileMetadata | None = None

    @staticmethod
    def from_db(job: ImportJob) -> ImportCsvJobDetail:
        if job.status not in {
            ImportCsvJobStatus.DRAFT,
            ImportCsvJobStatus.QUEUED,
        }:  # All stages have a workflow_id other than DRAFT and QUEUED.
            workflow_id = not_none(job.workflow_id)
        else:  # DRAFT and QUEUED jobs (do not have a workflow_id)
            workflow_id = job.workflow_id or ""

        return ImportCsvJobDetail(
            id=job.id,
            workflow_id=workflow_id,
            organization_id=job.organization_id,
            created_at=job.created_at,
            created_by_user_id=job.created_by_user_id,
            updated_at=job.updated_at,
            completed_at=job.completed_at,
            status=job.status,
            display_name=job.display_name,
            object_identifier=job.object_identifier,
            import_configuration=job.configuration,
            file_metadata=job.metadata,
            output_file=ImportOutputFile(
                s3_key=job.result_metadata.output_file_s3_key,
                direct_link=job.result_metadata.output_file_link,
            )
            if job.result_metadata and job.result_metadata.output_file_s3_key
            else None,
        )


class ImportCsvJobList(BaseModel):
    jobs: list[ImportCsvJobDetail]


class ImportJobFilter(BaseModel):
    object_identifiers: list[StandardObjectIdentifier | CustomObjectIdentifier]
    limit: int = 10


class WorkflowSignalMetadata(BasePatchRequest):
    create_contact_list_on_import: UnsetAware[bool] = UNSET
    create_account_list_on_import: UnsetAware[bool] = UNSET


class WorkflowSignalRequest(BaseModel):
    signal_name: CSVImportSignal
    signal_arg: UUID | None = None


class SignalResponse(BaseModel):
    success: bool = True
    error: str | None = None


class ImportMeetingRequest(BaseModel):
    account_id: UUID | None = None
    pipeline_id: UUID | None = None
    pipeline_display_name: str | None = None
    user_names: list[str] | None = None
    contact_emails: list[str]
    title: str
    media_url: str
    date_of_meeting: str  # expected format: MM/DD/YYYY


class ImportMeetingResponse(BaseModel):
    import_record: ImportRecord


class ImportCsvSampleRequest(BaseModel):
    object_identifiers: list[StandardObjectIdentifier | CustomObjectIdentifier]


class ImportCsvSampleResponse(BaseModel):
    file_link: str
    file_name: str
