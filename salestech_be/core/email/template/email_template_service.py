import uuid
from uuid import UUID

from fastapi import Request

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ErrorDeta<PERSON>,
    ForbiddenError,
    InvalidArgumentError,
    ResourceNotFoundError,
    ServiceError,
)
from salestech_be.common.exception.exception import ConflictResourceError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.type.patch_request import specified
from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.attachment.email_attachment_service import (
    EmailAttachmentService,
    get_email_attachment_service_by_db_engine,
)
from salestech_be.core.email.render.email_rendering_service import EmailRenderingService
from salestech_be.core.email.template.email_template_query_service import (
    EmailTemplateQueryService,
    get_email_template_query_service_from_engine,
)
from salestech_be.core.email.template.types import EmailTemplate
from salestech_be.core.email.type.email import (
    EmailHydratedParticipant,
    PreviewTemplateBodyParticipant,
)
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.db.dao.email_template_repository import EmailTemplateRepository
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dao.sequence_repository import (
    SequenceStepRepository,
    SequenceStepVariant,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.email_template import (
    EmailTemplate as DbEmailTemplate,
)
from salestech_be.db.models.email_template import (
    EmailTemplateCategory,
    EmailTemplateHistory,
    EmailTemplatePermissions,
    EmailTemplateType,
)
from salestech_be.db.models.user import UserRole
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.email.template.schema import (
    CreateEmailTemplateCategoryRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CreateEmailTemplateRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    EmailTemplateCategoryResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchEmailTemplateCategoryRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchEmailTemplateRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PreviewEmailTemplateBodyResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PreviewEmailTemplateResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class EmailTemplateService:
    def __init__(
        self,
        email_template_repository: EmailTemplateRepository,
        email_attachment_service: EmailAttachmentService,
        message_repository: MessageRepository,
        email_rendering_service: EmailRenderingService,
        email_account_service: EmailAccountServiceExt,
        user_service: UserService,
        email_template_query_service: EmailTemplateQueryService,
        sequence_step_repository: SequenceStepRepository,
    ):
        self.email_template_repository = email_template_repository
        self.email_attachment_service = email_attachment_service
        self._message_repository = message_repository
        self._email_rendering_service = email_rendering_service
        self.email_account_service = email_account_service
        self.user_service = user_service
        self.email_template_query_service = email_template_query_service
        self.sequence_step_repository = sequence_step_repository

    async def create_email_template(
        self,
        user_id: UUID | None,
        organization_id: UUID,
        request: CreateEmailTemplateRequest,
    ) -> EmailTemplate:
        if request.attachment_ids:
            await self.email_attachment_service.find_attachment_by_ids(
                organization_id=organization_id,
                attachment_ids=request.attachment_ids,
            )
        categories = request.categories
        if categories:
            categories = self._clean_categories(categories)
            await self._create_category_if_not_exist(
                user_id=user_id,
                organization_id=organization_id,
                categories=categories,
            )
        template_to_insert = DbEmailTemplate(
            id=uuid.uuid4(),
            name=request.name,
            type=request.type,
            subject=request.subject,
            body_html=request.body_html,
            categories=categories,
            attachment_ids=request.attachment_ids,
            version=1,
            permissions=request.permissions,
            include_email_signature=request.include_email_signature,
            organization_id=organization_id,
            created_by_user_id=user_id,
            updated_by_user_id=user_id,
            deleted_by_user_id=None,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=None,
        )

        db_template = await self.email_template_repository.insert(template_to_insert)
        return EmailTemplate.email_template_from_db_model(db_template)

    async def delete_email_template(
        self, user_id: UUID, template_id: UUID, organization_id: UUID
    ) -> EmailTemplate:
        db_template = (
            await self.email_template_repository._find_unique_by_column_values(  # noqa: SLF001
                DbEmailTemplate,
                exclude_deleted_or_archived=True,
                id=template_id,
                organization_id=organization_id,
            )
        )
        if not db_template:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"Template {template_id} not found.",
                )
            )

        # check permissions
        if db_template.permissions == EmailTemplatePermissions.VIEW_ONLY:
            raise ForbiddenError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.FORBIDDEN,
                    details="Not allowed to delete",
                )
            )
        # check if admin and if not the creator of the template
        user_roles = await self.user_service.get_user_organization_role(
            user_id=user_id, organization_id=organization_id
        )
        if (
            UserRole.ADMIN not in user_roles
            and db_template.created_by_user_id != user_id
        ):
            raise ForbiddenError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.FORBIDDEN,
                    details="Not allowed to delete",
                )
            )
        # update db
        deleted_db_template = (
            await self.email_template_repository._update_unique_by_column_values(  # noqa: SLF001
                DbEmailTemplate,
                column_value_to_query={
                    "id": template_id,
                },
                column_to_update={
                    "deleted_at": zoned_utc_now(),
                    "updated_at": zoned_utc_now(),
                    "deleted_by_user_id": user_id,
                    "updated_by_user_id": user_id,
                },
            )
        )

        if not deleted_db_template:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DB_UPDATE_ERROR,
                    details=f"Template {template_id} failed to delete.",
                )
            )

        await self.email_attachment_service.delete_attachment_by_ids(
            organization_id=organization_id,
            attachment_ids=db_template.attachment_ids or [],
        )
        logger.bind(deleted_db_template=deleted_db_template).info("Deleted template")
        return EmailTemplate.email_template_from_db_model(deleted_db_template)

    async def patch_email_template(  # noqa: C901
        self,
        user_id: UUID,
        template_id: UUID,
        organization_id: UUID,
        request: PatchEmailTemplateRequest,
        update_sequence_step_variants: bool = True,
    ) -> EmailTemplate:
        existing_db_template = (
            await self.email_template_repository._find_unique_by_column_values(  # noqa: SLF001
                DbEmailTemplate,
                exclude_deleted_or_archived=True,
                id=template_id,
                organization_id=organization_id,
            )
        )
        if not existing_db_template:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"Template {template_id} not found.",
                )
            )
        fields_to_update = request.model_dump()

        if not fields_to_update:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_PATCH_REQUEST,
                    details="The patch request is empty",
                )
            )

        if specified(request.include_email_signature):
            fields_to_update["include_email_signature"] = (
                request.include_email_signature
            )

        # validate template
        if (
            ("subject" in fields_to_update and fields_to_update["subject"] is None)
            or (
                "body_html" in fields_to_update
                and fields_to_update["body_html"] is None
            )
            or (
                "include_email_signature" in fields_to_update
                and fields_to_update["include_email_signature"] is None
            )
        ):
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_PATCH_REQUEST,
                    details="subject or body_html cannot be empty.",
                )
            )
        if not await self.check_email_patch_permissions(
            template=existing_db_template,
            user_id=user_id,
            organization_id=organization_id,
            request=request,
        ):
            raise ForbiddenError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.FORBIDDEN,
                    details="You are not allowed to modify this template.",
                )
            )
        if specified(request.attachment_ids) and request.attachment_ids:
            await self.email_attachment_service.find_attachment_by_ids(
                organization_id=organization_id,
                attachment_ids=request.attachment_ids,
            )

        fields_to_update["updated_at"] = zoned_utc_now()

        if specified(request.categories) and request.categories:
            categories = self._clean_categories(request.categories)
            await self._create_category_if_not_exist(
                user_id=user_id,
                organization_id=organization_id,
                categories=categories,
            )
            fields_to_update["categories"] = categories

        if await self._should_increment_version(existing_db_template, request):
            fields_to_update["version"] = existing_db_template.version + 1
            await self._create_template_history(existing_db_template)

        updated_db_template = (
            await self.email_template_repository._update_unique_by_column_values(  # noqa: SLF001
                DbEmailTemplate,
                column_value_to_query={
                    "id": template_id,
                    "organization_id": organization_id,
                    "version": existing_db_template.version,
                },
                column_to_update=fields_to_update,
            )
        )
        if not updated_db_template:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DB_UPDATE_ERROR,
                    details=f"Template {template_id} failed to update.",
                )
            )

        # Update associated sequence step variants if content was changed
        if (
            update_sequence_step_variants
            and existing_db_template.type == EmailTemplateType.SEQUENCE
        ):
            await self._update_sequence_step_variants(
                user_id=user_id,
                organization_id=organization_id,
                template_id=template_id,
                subject=updated_db_template.subject or "",
                body_html=updated_db_template.body_html,
                attachment_ids=updated_db_template.attachment_ids,
            )

        return EmailTemplate.email_template_from_db_model(updated_db_template)

    async def _should_increment_version(
        self, email_template: DbEmailTemplate, request: PatchEmailTemplateRequest
    ) -> bool:
        return any(
            [
                specified(request.subject)
                and request.subject != email_template.subject,
                specified(request.body_html)
                and request.body_html != email_template.body_html,
                specified(request.attachment_ids)
                and request.attachment_ids != email_template.attachment_ids,
            ]
        )

    async def _create_template_history(
        self, email_template: DbEmailTemplate
    ) -> EmailTemplateHistory:
        template_history = EmailTemplateHistory(
            id=uuid.uuid4(),
            organization_id=email_template.organization_id,
            email_template_id=email_template.id,
            version=email_template.version,
            name=email_template.name,
            subject=email_template.subject,
            body_html=email_template.body_html,
            attachment_ids=email_template.attachment_ids,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            created_by_user_id=not_none(
                email_template.created_by_user_id
            ),  # history must have created user,
            updated_by_user_id=not_none(email_template.updated_by_user_id),
        )
        return await self.email_template_repository.insert(template_history)

    async def preview_email_template_by_id(
        self,
        user_id: UUID,
        template_id: UUID,
        organization_id: UUID,
        send_from: EmailHydratedParticipant,
        send_to: EmailHydratedParticipant | None,
    ) -> PreviewEmailTemplateResponse:
        existing_email_template_list = (
            await self.email_template_query_service.list_email_templates(
                organization_id=organization_id,
                user_id=user_id,
                only_include_template_ids={template_id},
            )
        )
        if not existing_email_template_list:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"Template {template_id} not found.",
                )
            )
        existing_email_template = existing_email_template_list[0]

        merged_subject = existing_email_template.subject
        merged_body_html = existing_email_template.body_html
        unresolved_variables: set[str] = set()
        (
            merged_subject,
            merged_body_html,
            unresolved_variables,
        ) = await self._email_rendering_service.render_email_with_domain_models(
            sender_email_account_id=not_none(send_from.email_account_id),
            recipient_contact_id=send_to.contact_id if send_to else None,
            pre_render_subject=existing_email_template.subject,
            pre_render_body_html=existing_email_template.body_html,
            include_email_signature=existing_email_template.include_email_signature,
            organization_id=organization_id,
            use_default=False,
            user_id=user_id,
        )

        logger.bind(
            merged_subject=merged_subject,
            merged_body_html=merged_body_html,
            unresolved_variables=unresolved_variables,
        ).info("Rendered email")

        return PreviewEmailTemplateResponse.from_email_template(
            email_template=existing_email_template.model_copy(
                update={
                    "subject": merged_subject,
                    "body_html": merged_body_html,
                }
            ),
            unresolved_variables=unresolved_variables,
        )

    async def preview_email_template_body(
        self,
        user_id: UUID,
        organization_id: UUID,
        body_html: str,
        subject: str,
        include_email_signature: bool,
        use_default: bool = True,
        send_from: EmailHydratedParticipant
        | PreviewTemplateBodyParticipant
        | None = None,
        send_to: EmailHydratedParticipant
        | PreviewTemplateBodyParticipant
        | None = None,
    ) -> PreviewEmailTemplateBodyResponse:
        (
            merged_subject,
            merged_body_html,
            unresolved_variables,
        ) = await self._email_rendering_service.render_email_with_domain_models(
            sender_email_account_id=send_from.email_account_id if send_from else None,
            recipient_contact_id=send_to.contact_id if send_to else None,
            pre_render_subject=subject,
            pre_render_body_html=body_html,
            include_email_signature=include_email_signature,
            organization_id=organization_id,
            use_default=use_default,
            user_id=user_id,
        )

        return PreviewEmailTemplateBodyResponse(
            subject=merged_subject,
            body_html=merged_body_html,
            unresolved_variables=list(unresolved_variables)
            if unresolved_variables
            else None,
        )

    async def check_email_patch_permissions(
        self,
        template: DbEmailTemplate,
        user_id: UUID,
        organization_id: UUID,
        request: PatchEmailTemplateRequest,
    ) -> bool:
        # Can't edit VIEW_ONLY templates no matter what
        if template.permissions == EmailTemplatePermissions.VIEW_ONLY:
            return False
        # Owner -> True
        if user_id == template.created_by_user_id:
            return True
        # Admin -> True
        user_roles = await self.user_service.get_user_organization_role(
            user_id, organization_id
        )
        if UserRole.ADMIN in user_roles:
            return True
        # Others -> Permission changed? + TEAM_EDITABLE
        if (
            specified(request.permissions)
            and template.permissions != request.permissions
        ):
            return False
        if template.permissions == EmailTemplatePermissions.TEAM_EDITABLE:
            return True
        return False

    async def create_email_template_category(
        self,
        user_id: UUID,
        organization_id: UUID,
        request: CreateEmailTemplateCategoryRequest,
    ) -> EmailTemplateCategoryResponse:
        existing_category = (
            await self.email_template_repository.get_email_template_category_by_name(
                request.name, organization_id=organization_id
            )
        )
        if existing_category:
            if not existing_category.deleted_at:
                raise ConflictResourceError(
                    f"Category with name {request.name} already exists."
                )
            else:
                # restore the deleted category
                updated_category = await self.email_template_repository._update_unique_by_column_values(  # noqa: SLF001
                    EmailTemplateCategory,
                    exclude_deleted_or_archived=False,
                    column_value_to_query={
                        "id": existing_category.id,
                    },
                    column_to_update={
                        "deleted_at": None,
                        "deleted_by_user_id": None,
                        "updated_at": zoned_utc_now(),
                        "updated_by_user_id": user_id,
                        "color": request.color,
                    },
                )
                if not updated_category:
                    raise ServiceError(
                        additional_error_details=ErrorDetails(
                            code=ErrorCode.DB_UPDATE_ERROR,
                            details=f"Category '{existing_category.name}' failed to create.",
                        )
                    )
                return EmailTemplateCategoryResponse.from_db_category(updated_category)
        else:
            email_template_category = EmailTemplateCategory(
                id=uuid.uuid4(),
                organization_id=organization_id,
                name=request.name,
                color=request.color,
                created_by_user_id=user_id,
                updated_by_user_id=user_id,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
            inserted_category = await self.email_template_repository.insert(
                email_template_category
            )
            return EmailTemplateCategoryResponse.from_db_category(inserted_category)

    async def delete_email_template_category(
        self,
        user_id: UUID,
        organization_id: UUID,
        category_id: UUID,
    ) -> EmailTemplateCategoryResponse:
        existing_category = (
            await self.email_template_repository._find_unique_by_column_values_or_fail(  # noqa: SLF001
                EmailTemplateCategory,
                exclude_deleted_or_archived=True,
                id=category_id,
                organization_id=organization_id,
            )
        )
        if not existing_category:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"Category {category_id} not found.",
                )
            )
        updated_category = (
            await self.email_template_repository._update_unique_by_column_values(  # noqa: SLF001
                EmailTemplateCategory,
                column_value_to_query={
                    "id": category_id,
                    "organization_id": organization_id,
                },
                column_to_update={
                    "deleted_at": zoned_utc_now(),
                    "deleted_by_user_id": user_id,
                },
            )
        )
        if not updated_category:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DB_UPDATE_ERROR,
                    details=f"Category '{existing_category.name}' failed to delete.",
                )
            )
        # remove the category from all templates
        await self.email_template_repository.remove_category_from_all_templates(
            category_name=existing_category.name,
            organization_id=organization_id,
        )
        return EmailTemplateCategoryResponse.from_db_category(updated_category)

    async def patch_email_template_category(
        self,
        user_id: UUID,
        organization_id: UUID,
        category_id: UUID,
        request: PatchEmailTemplateCategoryRequest,
    ) -> EmailTemplateCategoryResponse:
        existing_category = (
            await self.email_template_repository._find_unique_by_column_values_or_fail(  # noqa: SLF001
                EmailTemplateCategory,
                exclude_deleted_or_archived=True,
                id=category_id,
                organization_id=organization_id,
            )
        )
        if not existing_category:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"Category {category_id} not found.",
                )
            )
        fields_to_update = request.model_dump()
        if not fields_to_update:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_PATCH_REQUEST,
                    details="The patch request is empty",
                )
            )
        fields_to_update = {
            "updated_at": zoned_utc_now(),
            "updated_by_user_id": user_id,
        }
        if specified(request.color):
            fields_to_update["color"] = request.color
        updated_category = (
            await self.email_template_repository._update_unique_by_column_values(  # noqa: SLF001
                EmailTemplateCategory,
                column_value_to_query={
                    "id": category_id,
                    "organization_id": organization_id,
                },
                column_to_update=fields_to_update,
            )
        )
        if not updated_category:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DB_UPDATE_ERROR,
                    details=f"Category '{existing_category.name}' failed to update.",
                )
            )
        return EmailTemplateCategoryResponse.from_db_category(updated_category)

    async def list_email_template_categories(
        self,
        organization_id: UUID,
    ) -> list[EmailTemplateCategory]:
        return await self.email_template_repository.get_email_template_categories_by_organization_id(
            organization_id=organization_id,
        )

    def _clean_categories(self, categories: list[str]) -> list[str]:
        return list(
            {category for category in categories if category and category.strip()}
        )

    async def _create_category_if_not_exist(
        self,
        user_id: UUID | None,
        organization_id: UUID,
        categories: list[str],
    ) -> None:
        existing_categories = await self.email_template_repository.get_email_template_categories_by_organization_id(
            organization_id=organization_id,
        )
        existing_categories_names = {category.name for category in existing_categories}
        category_to_create = []
        for category in categories:
            if category not in existing_categories_names:
                category_to_create.append(category)
        # use batch insert
        await self.email_template_repository.transactional_batch_insert_email_template_categories(
            organization_id=organization_id,
            user_id=user_id,
            categories=category_to_create,
        )

    async def get_email_template_by_id(
        self,
        organization_id: UUID,
        template_id: UUID,
    ) -> EmailTemplate:
        db_template = (
            await self.email_template_repository.find_by_tenanted_primary_key_or_fail(
                table_model=DbEmailTemplate,
                organization_id=organization_id,
                id=template_id,
            )
        )
        return EmailTemplate.email_template_from_db_model(db_template)

    async def _update_sequence_step_variants(
        self,
        user_id: UUID,
        organization_id: UUID,
        template_id: UUID,
        subject: str,
        body_html: str,
        attachment_ids: list[UUID] | None,
    ) -> None:
        """
        Find and update any sequence step variants using the template that was updated.
        This ensures that sequence steps using the template have their content in sync.
        """
        logger.bind(template_id=template_id, organization_id=organization_id).info(
            "Updating sequence step variants using email template"
        )

        existing_variant = (
            await self.sequence_step_repository._find_unique_by_column_values(  # noqa: SLF001
                table_model=SequenceStepVariant,
                template_id=template_id,
                organization_id=organization_id,
                exclude_deleted_or_archived=True,
            )
        )

        if not existing_variant:
            logger.bind(template_id=template_id, organization_id=organization_id).info(
                "No sequence step variants found for template"
            )
            return

        updated_content = existing_variant.content.model_copy(
            update={
                "subject": subject,
                "body": body_html,
                "attachment_ids": attachment_ids or [],
            }
        )

        await self.sequence_step_repository.update_by_tenanted_primary_key(
            table_model=SequenceStepVariant,
            primary_key_to_value={"id": existing_variant.id},
            organization_id=organization_id,
            column_to_update={
                "content": updated_content,
                "updated_at": zoned_utc_now(),
                "updated_by_user_id": user_id,
            },
        )


def get_email_template_service_from_engine(
    db_engine: DatabaseEngine,
) -> EmailTemplateService:
    return EmailTemplateService(
        email_template_repository=EmailTemplateRepository(engine=db_engine),
        email_attachment_service=get_email_attachment_service_by_db_engine(
            db_engine=db_engine
        ),
        message_repository=MessageRepository(engine=db_engine),
        email_rendering_service=EmailRenderingService(db_engine=db_engine),
        email_account_service=EmailAccountServiceExt(engine=db_engine),
        user_service=get_user_service_general(db_engine=db_engine),
        email_template_query_service=get_email_template_query_service_from_engine(
            db_engine=db_engine
        ),
        sequence_step_repository=SequenceStepRepository(engine=db_engine),
    )


def get_email_template_service(
    request: Request,
) -> EmailTemplateService:
    db_engine = get_db_engine(request=request)
    return get_email_template_service_from_engine(db_engine=db_engine)
