import re
from typing import Any, ClassVar
from uuid import UUID

import phonenumbers
from fastapi import HTTPException

from salestech_be.common.exception import ResourceNotFoundError
from salestech_be.core.account.service.account_query_service import (
    get_account_query_service,
)
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.service.contact_query_service import (
    get_contact_query_service,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.email.account.service_v2 import (
    get_email_account_service_v2_by_db_engine,
)
from salestech_be.core.email.account.types import EmailAccountV2
from salestech_be.core.email.copywriter.types import (
    CustomizedOpenerCommand,
)
from salestech_be.core.email.unsubscription_group.unsubscription_group_ext import (
    UnsuscriptionGroupServiceExt,
)
from salestech_be.core.organization.service.organization_preference_service import (
    organization_preference_service_from_engine,
)
from salestech_be.core.organization.service.organization_service_v2 import (
    get_organization_service_v2_from_engine,
)
from salestech_be.core.organization.types import OrganizationV2
from salestech_be.core.user.service.user_service import get_user_service_general
from salestech_be.core.user.signature.service import get_signature_service
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.core.variable.types import (
    CurrentDateTimeBinding,
    EmailAccountBinding,
    EmailContactBinding,
    EmailSenderEmailAccountBinding,
    EmailSenderOrganizationBinding,
    EmailSenderUserBinding,
)
from salestech_be.core.variable.variable_service import VariableService
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.organization_info import OrganizationPreferenceKeys
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now


class EmailRenderingService:
    AI_PERSONALIZATION_OPENER_VARIABLES: ClassVar[dict[str, Any]] = {  # type: ignore[explicit-any] # TODO: fix-any-annotation
        "ai_personalized_opener.job_focus": CustomizedOpenerCommand.JOB_FOCUS,
        "ai_personalized_opener.company_version": CustomizedOpenerCommand.COMPANY_MISSION,
    }

    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.user_service = get_user_service_general(db_engine=db_engine)
        self.organization_service_v2 = get_organization_service_v2_from_engine(
            db_engine=db_engine
        )
        self._variable_service = VariableService(
            user_service=self.user_service,
            organization_service=self.organization_service_v2,
        )
        self._email_account_service_v2 = get_email_account_service_v2_by_db_engine(
            db_engine=db_engine
        )
        self._signature_service = get_signature_service(db_engine=db_engine)
        self._unsubscription_group_service = UnsuscriptionGroupServiceExt(
            engine=db_engine
        )
        self.FAKE_GLOBAL_MESSAGE_UNSUBSCRIBE_LINK = f"{settings.unsubscribe_link_base_url}?group_id=********-1111-1111-1111-************&email=<EMAIL>&global_message_id=********-1111-1111-1111-************"

        self.contact_query_service = get_contact_query_service(db_engine=db_engine)
        self.account_query_service = get_account_query_service(
            db_engine=db_engine,
        )

        self.organization_preference_service = (
            organization_preference_service_from_engine(db_engine=db_engine)
        )
        self._logger = get_logger()

    @staticmethod
    def _convert_http_to_https(content: str) -> str:
        return content.replace("http://", "https://")

    async def render_email_with_domain_models(  # noqa: C901
        self,
        organization_id: UUID,
        user_id: UUID,
        sender_email_account_id: UUID | None = None,
        recipient_contact_id: UUID | None = None,
        include_email_signature: bool = False,
        use_default: bool = False,
        pre_render_subject: str | None = None,
        pre_render_body_html: str | None = None,
        unsubscribe_link: str | None = None,
    ) -> tuple[str, str, set[str]]:
        variable_context: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        unresolved_vars: set[str] = set()

        # Step 1: Construct all variable context
        # 1.1 Current Datetime
        variable_context[CurrentDateTimeBinding.object_display_name.lower()] = (
            CurrentDateTimeBinding(
                date=zoned_utc_now().strftime("%m/%d/%Y"),
                time=zoned_utc_now().strftime("%I:%M %p").lower(),
                day=zoned_utc_now().strftime("%d"),
                month=zoned_utc_now().strftime("%m"),
                year=zoned_utc_now().strftime("%Y"),
            )
        )

        # 1.2 Contact & Account
        (
            contact_variable,
            account_variable,
        ) = await self.create_contact_variable_and_account_variable(
            organization_id=organization_id,
            recipient_contact_id=recipient_contact_id,
            use_default=use_default,
        )

        if contact_variable:
            variable_context[contact_variable.object_display_name.lower()] = (
                contact_variable
            )

        if account_variable:
            variable_context[account_variable.object_display_name.lower()] = (
                account_variable
            )

        # 1.3 Sender
        sender_variable: EmailSenderEmailAccountBinding | None = None
        if sender_email_account_id:
            email_account: (
                EmailAccountV2 | None
            ) = await self._email_account_service_v2.get_email_account_v2(
                email_account_id=sender_email_account_id,
                organization_id=organization_id,
                user_id=user_id,
            )
            sender_variable = await self.create_sender_variable(
                organization_id=organization_id,
                email_account=email_account,
                use_default=use_default,
            )

        if sender_variable:
            variable_context[sender_variable.object_display_name.lower()] = (
                sender_variable
            )

        context, exposed_fields = self._variable_service.parse_context(
            variable_context, {}
        )

        # Temp workaround to support {{sender.company}}. The parse_context need to
        # be refactored later.
        sender_organization_variable = await self.create_sender_organization_variable(
            organization_id=organization_id,
            use_default=use_default,
        )

        if sender_organization_variable:
            context, exposed_fields = await self.update_context_fields_by_variable(
                context=context,
                exposed_fields=exposed_fields,
                sender_variable=sender_organization_variable,
            )

        sender_phone_number_variable: EmailSenderUserBinding | None = None
        if user_id:
            sender_phone_number_variable = (
                await self.create_sender_phone_number_variable(
                    organization_id=organization_id,
                    user_id=user_id,
                    use_default=use_default,
                )
            )
        if sender_phone_number_variable:
            context, exposed_fields = await self.update_context_fields_by_variable(
                context=context,
                exposed_fields=exposed_fields,
                sender_variable=sender_phone_number_variable,
            )

        merged_subject = pre_render_subject or ""
        merged_body_html = pre_render_body_html or ""

        if pre_render_subject:
            merged_subject = self._variable_service.register_variables(
                template=pre_render_subject,
                context=context,
                exposed_fields=exposed_fields,
            )
            # Add any remaining variables in subject directly from placeholders
            unresolved_vars.update(
                self._variable_service.get_variable_placeholders(merged_subject)
            )

        if pre_render_body_html:
            merged_body_html = self._variable_service.register_variables(
                template=pre_render_body_html,
                context=context,
                exposed_fields=exposed_fields,
            )

            # Append signature to the end of the email if include_email_signature is true
            if (
                include_email_signature
                and email_account
                and (
                    signature := await self._signature_service.get_signature_by_id(
                        organization_id=organization_id,
                        signature_id=email_account.signature_id,
                    )
                )
            ):
                signature_html = f'<div class="reevo-signature-container">{signature.signature_html}</div>'
                merged_body_html += signature_html

            # Add sequence unsubscription signature
            sequence_unsubscription_signature_html = (
                await self.get_sequence_unsubscription_signature_html(
                    organization_id=organization_id, unsubscribe_link=unsubscribe_link
                )
            )
            if sequence_unsubscription_signature_html:
                merged_body_html += sequence_unsubscription_signature_html

            # Add any remaining variables in body directly from placeholders
            unresolved_vars.update(
                self._variable_service.get_variable_placeholders(merged_body_html)
            )

        return (
            self._convert_http_to_https(merged_subject),
            self._convert_http_to_https(merged_body_html),
            unresolved_vars,
        )

    async def create_sender_phone_number_variable(
        self,
        organization_id: UUID,
        user_id: UUID,
        use_default: bool = False,
    ) -> EmailSenderUserBinding | None:
        sender_phone_number_variable: EmailSenderUserBinding | None = None
        user_v2: OrganizationUserV2 | None = None
        try:
            user_v2 = await self.user_service.get_user_v2(
                user_id=user_id,
                organization_id=organization_id,
            )
        except ResourceNotFoundError:
            self._logger.bind(user_id=user_id).error("User not found")

        if user_v2:
            sender_phone_number_variable = EmailSenderUserBinding.from_domain_object(
                user_v2, use_default=use_default
            )
        if not user_v2 and use_default:
            sender_phone_number_variable = EmailSenderUserBinding.from_field_examples()
        # Convert phone numbers to RFC 3966 format before creating the binding
        if not sender_phone_number_variable:
            return None
        phone_number = sender_phone_number_variable.phone_number
        reevo_phone_number = sender_phone_number_variable.reevo_phone_number
        phone_number_rfc_3966 = None
        reevo_phone_number_rfc_3966 = None

        if phone_number:
            try:
                phone_number_rfc_3966 = await self._convert_phone_number_to_rfc_3966(
                    phone_number
                )
            except Exception as e:
                self._logger.bind(phone_number=phone_number).error(
                    f"Failed to convert phone number to RFC 3966: {e}"
                )

        if reevo_phone_number:
            try:
                reevo_phone_number_rfc_3966 = (
                    await self._convert_phone_number_to_rfc_3966(reevo_phone_number)
                )
            except Exception as e:
                self._logger.bind(phone_number=reevo_phone_number).error(
                    f"Failed to convert reevo phone number to RFC 3966: {e}"
                )

        # Create a copy of the user with the converted phone numbers
        sender_phone_number_variable_formatted = (
            sender_phone_number_variable.model_dump()
        )
        sender_phone_number_variable_formatted["phone_number"] = phone_number
        sender_phone_number_variable_formatted["reevo_phone_number"] = (
            reevo_phone_number
        )
        sender_phone_number_variable_formatted["phone_number_rfc_3966"] = (
            phone_number_rfc_3966
        )
        sender_phone_number_variable_formatted["reevo_phone_number_rfc_3966"] = (
            reevo_phone_number_rfc_3966
        )
        return EmailSenderUserBinding(**sender_phone_number_variable_formatted)

    async def _convert_phone_number_to_rfc_3966(self, phone_number: str) -> str:
        # Convert phone number to RFC 3966 format
        # Example: +1234567890 -> tel:+1234567890
        # Example: +1234567890x1234 -> tel:+1234567890;ext=1234
        # Example: +1234567890 (1234) -> tel:+1234567890;ext=1234
        # Example: +1234567890 (1234) x1234 -> tel:+1234567890;ext=1234;ext-call-id=1234
        # Example: +1234567890 (1234) x1234 -> tel:+1234567890;ext=1234;ext-call-id=1234
        parsed_number = phonenumbers.parse(phone_number)
        return phonenumbers.format_number(
            parsed_number, phonenumbers.PhoneNumberFormat.RFC3966
        )

    async def update_context_fields_by_variable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        context: dict[str, Any],
        exposed_fields: list[str],
        sender_variable: EmailSenderUserBinding | EmailSenderOrganizationBinding,
    ) -> tuple[dict[str, Any], list[str]]:
        if sender_variable:
            custom_context = {
                field_info.alias or field: sender_variable.model_dump().get(field)
                for field, field_info in sender_variable.model_fields.items()
            }
            sender_variable_context, variable_exposed_fields = (
                self._variable_service.parse_context(
                    {},
                    {sender_variable.object_display_name.lower(): custom_context},
                )
            )
            context.get(sender_variable.object_display_name.lower(), {}).update(
                sender_variable_context.get(sender_variable.object_display_name.lower())
            )
            exposed_fields += variable_exposed_fields
        return context, exposed_fields

    async def create_contact_variable_and_account_variable(
        self,
        organization_id: UUID,
        recipient_contact_id: UUID | None,
        use_default: bool = False,
    ) -> tuple[EmailContactBinding | None, EmailAccountBinding | None]:
        contact_v2: ContactV2 | None = None
        if recipient_contact_id:
            try:
                contact_v2 = await self.contact_query_service.get_contact_v2(
                    organization_id=organization_id, contact_id=recipient_contact_id
                )
            except ResourceNotFoundError:
                self._logger.bind(contact_id=recipient_contact_id).error(
                    "Contact not found"
                )
        account_v2: AccountV2 | None = None
        if contact_v2 and contact_v2.primary_account_id:
            try:
                account_v2 = await self.account_query_service.get_account_v2(
                    account_id=contact_v2.primary_account_id,
                    organization_id=organization_id,
                )
            except ResourceNotFoundError:
                self._logger.bind(account_id=contact_v2.primary_account_id).error(
                    "Account not found"
                )

        contact_variable = None
        account_variable = None
        if contact_v2:
            contact_variable = EmailContactBinding.from_domain_object(
                contact_v2, use_default=use_default
            )
        if not contact_v2 and use_default:
            contact_variable = EmailContactBinding.from_field_examples()

        if account_v2:
            account_variable = EmailAccountBinding.from_domain_object(
                account_v2, use_default=use_default
            )

        if not account_v2 and use_default:
            account_variable = EmailAccountBinding.from_field_examples()

        self._logger.bind(
            contact_variable=contact_variable,
            account_variable=account_variable,
        ).info("Contact and account variables")

        return contact_variable, account_variable

    async def create_sender_variable(
        self,
        organization_id: UUID,
        email_account: EmailAccountV2 | None,
        use_default: bool = False,
    ) -> EmailSenderEmailAccountBinding | None:
        if email_account:
            # construct sender variable , display_name will fallback to user's, then default (if set to True)
            # 1. use Email Account V2
            sender_variable = EmailSenderEmailAccountBinding.from_domain_object(
                email_account, use_default=False
            )
            # 2. Fallback to user's display name
            if not sender_variable.display_name:
                try:
                    user = await self.user_service.get_user_v2(
                        user_id=email_account.owner_user_id,
                        organization_id=organization_id,
                    )
                    sender_variable = sender_variable.model_copy(
                        update={
                            "display_name": user.display_name,
                            "first_name": user.first_name,
                        }
                    )
                except HTTPException:
                    self._logger.bind(user_id=email_account.owner_user_id).error(
                        "User not found"
                    )

            # 3. Fallback to sue default
            if not sender_variable.display_name and use_default:
                sender_variable = EmailSenderEmailAccountBinding.from_domain_object(
                    email_account, use_default=True
                )
            return sender_variable

        if not email_account and use_default:
            return EmailSenderEmailAccountBinding.from_field_examples()

        return None

    async def create_sender_organization_variable(
        self,
        organization_id: UUID,
        use_default: bool = False,
    ) -> EmailSenderOrganizationBinding | None:
        organization_v2: OrganizationV2 | None = None
        try:
            organization_v2 = await self.organization_service_v2.get_organization_by_id(
                organization_id=organization_id,
            )
        except ResourceNotFoundError:
            self._logger.bind(organization_id=organization_id).error(
                "Organization not found"
            )

        if organization_v2:
            return EmailSenderOrganizationBinding.from_domain_object(
                organization_v2, use_default=use_default
            )
        if not organization_v2 and use_default:
            return EmailSenderOrganizationBinding.from_field_examples()

        return None

    async def get_sequence_unsubscription_signature(
        self,
        organization_id: UUID,
    ) -> str | None:
        organization_preference = (
            await self.organization_preference_service.get_organization_preference(
                organization_id=organization_id,
                key=OrganizationPreferenceKeys.SEQUENCE_SETTINGS,
            )
        )
        sequence_settings = organization_preference.sequence_settings
        if not sequence_settings:
            return None
        return (
            sequence_settings.opt_out_message_template
            if sequence_settings.append_opt_out_after_signature
            else None
        )

    async def get_sequence_unsubscription_signature_html(
        self,
        organization_id: UUID,
        unsubscribe_link: str | None = None,
    ) -> str | None:
        """
        This function is used to get the sequence unsubscription signature html
        """
        signature = await self.get_sequence_unsubscription_signature(
            organization_id=organization_id
        )
        if not signature:
            return None
        # signature is an string contains variable, example format: If you don't want to hear from me again, please <%let me know%>
        # we need to replace the variable with the unsubscribe link and wrap it with link tag
        link_url = unsubscribe_link or self.FAKE_GLOBAL_MESSAGE_UNSUBSCRIBE_LINK

        # Find the placeholder and replace it with an HTML link
        match = re.search(r"<%(.+?)%>", signature)
        processed_signature = signature

        if match:
            placeholder = match.group(0)  # The full placeholder, e.g., <%let me know%>
            link_text = match.group(1)  # Just the text, e.g., let me know
            processed_signature = signature.replace(
                placeholder,
                f'<a id="{settings.unsubscribe_link_html_id}" href="{link_url}">{link_text}</a>',
            )

        return f'<div class="reevo-unsubscribe-signature-container">{processed_signature}</div>'
