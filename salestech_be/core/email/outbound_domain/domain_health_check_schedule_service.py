from datetime import timedel<PERSON>
from uuid import UUID

import grpc
import temporalio
from temporalio.client import (
    Client,
    Schedule,
    ScheduleActionStartWorkflow,
    ScheduleIntervalSpec,
    ScheduleSpec,
)
from temporalio.common import RetryPolicy

from salestech_be.common.singleton import Singleton
from salestech_be.db.dao.outbound_repository import (
    DomainHealthRepository,
    OutboundDomainRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import TemporalTaskQueue
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.workflows.email.domain_health_check_per_domain_workflow import (
    DomainHealthCheckPerDomainWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    DomainHealthCheckPerDomainWorkflowInput,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


class DomainHealthCheckScheduleService:
    def __init__(self, engine: DatabaseEngine):
        self.engine = engine
        self.outbound_domain_repository = OutboundDomainRepository(engine=engine)
        self.domain_health_repository = DomainHealthRepository(engine=engine)

    async def create_domain_health_check_per_domain_schedule(
        self,
        domain_id: UUID,
        organization_id: UUID,
        domain_name: str,
    ) -> None:
        try:
            client = await get_temporal_client()
            await self._create_domain_health_check_per_domain_schedule(
                client=client,
                domain_id=domain_id,
                organization_id=organization_id,
                domain_name=domain_name,
            )
        except Exception as e:
            logger.bind(domain_id=domain_id).error(
                f"Failed to create domain health check per domain schedule: {e}"
            )

    async def _create_domain_health_check_per_domain_schedule(
        self, client: Client, domain_id: UUID, organization_id: UUID, domain_name: str
    ) -> str:
        schedule_id = await self._get_domain_health_check_per_domain_schedule_id(
            domain_id=domain_id, domain_name=domain_name
        )
        try:
            handle = client.get_schedule_handle(schedule_id)
            await handle.describe()
            logger.bind(domain_id=domain_id, schedule_id=schedule_id).info(
                "Domain health check per domain schedule already exists for this domain, skipping creation"
            )
            return schedule_id
        except temporalio.service.RPCError as e:
            if e.grpc_status.code == grpc.StatusCode.NOT_FOUND.value[0]:
                logger.bind(domain_id=domain_id, schedule_id=schedule_id).info(
                    "Creating new domain health check per domain schedule"
                )
        offset = timedelta(hours=zoned_utc_now().hour)
        await client.create_schedule(
            id=schedule_id,
            schedule=Schedule(
                action=ScheduleActionStartWorkflow(
                    workflow=DomainHealthCheckPerDomainWorkflow.run,
                    args=[
                        DomainHealthCheckPerDomainWorkflowInput(
                            domain_id=domain_id, organization_id=organization_id
                        )
                    ],
                    id=schedule_id,
                    task_queue=TemporalTaskQueue.EMAIL_TASK_QUEUE,
                    execution_timeout=timedelta(minutes=20),
                    retry_policy=RetryPolicy(
                        initial_interval=timedelta(minutes=2),
                        backoff_coefficient=2,
                        maximum_interval=timedelta(minutes=20),
                        maximum_attempts=3,
                        non_retryable_error_types=[
                            "ValueError",
                            "ResourceNotFoundError",
                        ],
                    ),
                ),
                spec=ScheduleSpec(
                    intervals=[
                        ScheduleIntervalSpec(every=timedelta(days=1), offset=offset)
                    ],
                    jitter=timedelta(seconds=60),
                ),
            ),
            trigger_immediately=False,
        )
        return schedule_id

    async def delete_domain_health_check_per_domain_schedule(
        self, domain_id: UUID, organization_id: UUID, domain_name: str
    ) -> None:
        try:
            schedule_id = await self._get_domain_health_check_per_domain_schedule_id(
                domain_id, domain_name=domain_name
            )
            client = await get_temporal_client()
            handle = client.get_schedule_handle(schedule_id)
            await handle.delete()
            logger.bind(domain_id=domain_id, organization_id=organization_id).info(
                "Domain health check per domain schedule stopped"
            )
        except Exception as e:
            logger.bind(domain_id=domain_id, organization_id=organization_id).error(
                "Failed to stop domain health check per domain schedule", exc_info=e
            )

    async def _get_domain_health_check_per_domain_schedule_id(
        self, domain_id: UUID, domain_name: str
    ) -> str:
        return f"domain_health_check_per_domain_{domain_id}_{domain_name}"


class SingletonDomainHealthCheckScheduleService(
    Singleton, DomainHealthCheckScheduleService
):
    pass


def get_domain_health_check_schedule_service(
    engine: DatabaseEngine,
) -> DomainHealthCheckScheduleService:
    if SingletonDomainHealthCheckScheduleService.has_instance():
        return SingletonDomainHealthCheckScheduleService.get_singleton_instance()
    return SingletonDomainHealthCheckScheduleService(engine=engine)
