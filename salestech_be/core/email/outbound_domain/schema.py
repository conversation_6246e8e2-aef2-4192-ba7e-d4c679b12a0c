from __future__ import annotations

from enum import StrEnum
from typing import Any
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception.exception import ErrorDetails, InvalidArgumentError
from salestech_be.core.email.type.email import EmailAccountArchiveSequenceHandling
from salestech_be.core.email.utils import is_valid_web_url
from salestech_be.db.models.core.types import EntityDnsRecord
from salestech_be.db.models.outbound import OutboundDomain, OutboundDomainStatus
from salestech_be.integrations.infraforge.type import (
    ContactDetailsForSetup,
    ExternalCheckDomainAvailabilityResponse,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class DomainPaymentMethod(StrEnum):  # These are all differentiated by domain price
    INCLUDED_IN_PLAN = "INCLUDED_IN_PLAN"  # Reevo covered domains are paid for by <PERSON>evo in the base plan
    ACCOUNTS_RECEIVABLE = "ACCOUNTS_RECEIVABLE"  # Customer payment domains are paid for by the customer via invoice
    CONTACT_SUPPORT = "CONTACT_SUPPORT"  # Contact support domains require the customer to contact support to purchase


class DomainBillingCycle(StrEnum):
    MONTHLY = "MONTHLY"
    ANNUALLY = "ANNUALLY"


# Check Domain Availability
class CheckDomainAvailabilityRequest(BaseModel):
    workspace_id: UUID
    domain: str


class CheckDomainAvailabilityResponse(BaseModel):
    available: bool
    domain: str
    price: float


class DomainResponse(BaseModel):
    id: UUID
    organization_id: UUID
    domain: str
    external_id: str
    status: OutboundDomainStatus
    workspace_id: UUID
    created_at: ZoneRequiredDateTime
    updated_at: ZoneRequiredDateTime

    @classmethod
    def from_db_model(cls, domain: OutboundDomain) -> DomainResponse:
        return cls(
            id=domain.id,
            organization_id=domain.organization_id,
            domain=domain.domain,
            external_id=domain.external_id,
            status=domain.status,
            workspace_id=domain.workspace_id,
            created_at=domain.created_at,
            updated_at=domain.updated_at,
        )


class FindDomainRequest(BaseModel):
    query: str
    suggest_alternative: int = 10  # this represents how many alternatives we want to show (we want it to be 10 max backend)


class FindDomainResponse(BaseModel):
    domain: str
    price_cents: int
    available: bool
    billing_cycle: DomainBillingCycle
    payment_method: DomainPaymentMethod = (
        DomainPaymentMethod.INCLUDED_IN_PLAN
    )  # TODO: change this once we actually pass down from find_domain

    @classmethod
    def from_check_availability_response(
        cls,
        availability: ExternalCheckDomainAvailabilityResponse,
        payment_method: DomainPaymentMethod | None = None,
    ) -> FindDomainResponse:
        return cls(
            domain=availability.domain,
            available=availability.available,
            price_cents=int(
                availability.price * 100
            ),  # Note: checking is in dollars, but purchase invoice is in cents...
            billing_cycle=DomainBillingCycle.ANNUALLY,  # TODO: discuss pricing w/ gtm
            payment_method=payment_method or DomainPaymentMethod.INCLUDED_IN_PLAN,
        )


# Buy Domain
class ContactDetails(BaseModel):
    address_1: str
    address_2: str
    city: str
    country: str
    email: str
    first_name: str
    last_name: str
    organization: str
    phone: str
    postal_code: str
    province: str
    job_title: str | None

    @classmethod
    def get_reevo_default_contact(cls) -> ContactDetails:
        """Get default Reevo contact details used for domain registration."""
        return cls(
            address_1="2445 Augustine Dr",
            address_2="Suite 150",
            city="Santa Clara",
            country="US",
            email="<EMAIL>",
            first_name="Curtis",
            last_name="Tan",
            organization="Reevo",
            phone="*************",
            postal_code="95054",
            province="CA",
            job_title="Builder",
        )

    def to_infraforge_model(self, primary_domain: str | None) -> ContactDetailsForSetup:
        return ContactDetailsForSetup(
            address1=self.address_1,
            address2=self.address_2,
            city=self.city,
            country=self.country,
            email=self.email,
            firstName=self.first_name,
            lastName=self.last_name,
            organization=self.organization,
            phone=self.phone,
            postalCode=self.postal_code,
            province=self.province,
            jobTitle=self.job_title,
            forwardToDomain=primary_domain,
            dmarcEmail="<EMAIL>",
        )


class BuyDomainRequest(BaseModel):
    domains: list[str]
    forward_to_domain: str | None = None
    is_mock_record: bool = False

    def model_post_init(self, __context: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().model_post_init(__context)
        if self.forward_to_domain:
            # Add https:// prefix if the URL doesn't have a scheme already
            if not self.forward_to_domain.startswith(("http://", "https://")):
                self.forward_to_domain = f"https://{self.forward_to_domain}"

            # Validate the URL after potential modification
            if not is_valid_web_url(self.forward_to_domain):
                raise InvalidArgumentError(
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.INVALID_REQUEST,
                        details="Forward to domain is not valid, please provide a valid domain or None.",
                    )
                )
        if len(self.domains) > 1:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_REQUEST,
                    details="Only one domain can be purchased at a time.",
                )
            )


# Generate Alternative Domains
class GenerateAlternativeDomainsRequest(BaseModel):
    workspace_id: UUID
    input_sld: str
    output_tld: str
    count: int = Field(..., ge=1)


class AlternativeDomain(BaseModel):
    domain: str
    available: bool
    price: float


class ArchiveDomainRequest(BaseModel):
    sequence_handling: EmailAccountArchiveSequenceHandling


class DNSCheckResult(BaseModel):
    mx_records: list[EntityDnsRecord]
    spf_records: list[EntityDnsRecord]
    dmarc_record: list[EntityDnsRecord]
    dkim_record: list[EntityDnsRecord]

    @property
    def is_complete(self) -> bool:
        return bool(
            self.mx_records
            and self.spf_records
            and self.dkim_record
            and self.dmarc_record
        )


class GenerateAlternativeDomainsResponse(BaseModel):
    alternatives: list[AlternativeDomain]
