import asyncio
from datetime import UTC, datetime
from uuid import UUID

import dns.asyncresolver

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import ServiceError
from salestech_be.common.exception.exception import ErrorDetails
from salestech_be.common.singleton import Singleton
from salestech_be.core.email.outbound_domain.schema import DNSCheckResult
from salestech_be.db.dao.outbound_repository import (
    DomainHealthRepository,
    OutboundDomainRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.core.types import EntityDnsRecord
from salestech_be.db.models.outbound import OutboundDomainStatus
from salestech_be.ree_logging import get_logger
from salestech_be.util.validation import not_none

logger = get_logger(__name__)

MAX_ATTEMPTS = 3
INITIAL_RETRY_DELAY = 10  # seconds
BACKOFF_FACTOR = 2  # exponential backoff multiplier

resolver = dns.asyncresolver.Resolver()
resolver.cache = None
resolver.timeout = 1.0
resolver.lifetime = 2.0


class DnsCheckService:
    def __init__(self, engine: DatabaseEngine):
        self.outbound_domain_repository = OutboundDomainRepository(engine=engine)
        self.domain_health_repository = DomainHealthRepository(engine=engine)

    async def check_domain_health_per_domain(
        self, domain_id: UUID, organization_id: UUID
    ) -> None:
        domain = await self.outbound_domain_repository.get_domain_by_id_and_org_id(
            outbound_domain_id=domain_id, organization_id=organization_id
        )
        if not domain:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.SERVER_ERROR,
                    details="Failed to find domain that should exist in database",
                )
            )
        now = datetime.now(tz=UTC)
        fields_to_update: dict[str, datetime | list[EntityDnsRecord]] = {
            "last_check_at": now
        }
        if domain.is_mock_record:  # if the domain is a mock record, we don't need to check the health,just update the last_check_at
            await self.domain_health_repository.update_domain_health(
                outbound_domain_id=domain_id, column_to_update=fields_to_update
            )
            return
        domain_health = (
            await self.domain_health_repository.get_domain_health_by_outbound_domain_id(
                outbound_domain_id=domain_id
            )
        )
        formatted_domain = (
            domain.domain + "." if not domain.domain.endswith(".") else domain.domain
        )
        dns_records = await check_dns(formatted_domain)
        updated = False
        if domain_health:
            for field_name in dns_records.model_fields:
                current_value = getattr(dns_records, field_name)
                stored_value = getattr(domain_health, field_name)
                if set(current_value) != set(
                    stored_value
                ):  # set makes the comparison order-independent, mostly for mx and spf
                    fields_to_update[field_name] = current_value
                    logger.bind(
                        domain_id=domain_id,
                        field_name=field_name,
                        current_value=current_value,
                        stored_value=stored_value,
                    ).info("New DNS record found")
                    updated = True
            if updated:
                fields_to_update["updated_at"] = now
            await self.domain_health_repository.update_domain_health(
                outbound_domain_id=domain_id, column_to_update=fields_to_update
            )
        else:
            logger.info(f"Inserting domain health for domain {domain.domain}")
            await self.domain_health_repository.insert_domain_health(
                outbound_domain_id=domain_id,
                mx_records=dns_records.mx_records,
                spf_records=dns_records.spf_records,
                dkim_record=dns_records.dkim_record,
                dmarc_record=dns_records.dmarc_record,
            )
        if (
            dns_records.is_complete and domain.status == OutboundDomainStatus.PENDING
        ):  # If the domain is pending, update the status to active, we only do this for pre-existing healths
            await self.outbound_domain_repository.update_outbound_domain(
                outbound_domain_id=domain_id,
                column_to_update={"status": OutboundDomainStatus.ACTIVE},
            )
            # TODO: potentially check if is_complete is false and domain is older than 1 day, if so, set to FAILED?


async def check_txt(domain: str, record_type: str) -> list[str]:
    for attempt in range(MAX_ATTEMPTS):
        if attempt > 0:
            delay = INITIAL_RETRY_DELAY * (BACKOFF_FACTOR ** (attempt - 1))
            logger.info(
                f"Retry attempt {attempt + 1} for domain {domain}, waiting {delay} seconds"
            )
            await asyncio.sleep(delay)
        try:
            answers = await resolver.resolve(domain, dns.rdatatype.TXT)
            result = [
                answer.to_text().strip('"').replace('\\"', '"') for answer in answers
            ]
            records = [r for r in result if r.startswith(record_type)]
            if records:  # If we found matching records, return immediately
                return records
            logger.info(f"No matching {record_type} records found for {domain}")
        except dns.asyncresolver.NXDOMAIN:
            logger.info(f"NXDOMAIN for domain {domain} and record type {record_type}")
        except dns.asyncresolver.NoAnswer:
            logger.info(f"NoAnswer for domain {domain} and record type {record_type}")
        except Exception as e:
            logger.info(f"Error for domain {domain} and record type {record_type}: {e}")
    logger.warning(f"All retry attempts exhausted for {domain} {record_type}")
    return []


async def check_mx(domain: str) -> list[str]:
    for attempt in range(MAX_ATTEMPTS):
        if attempt > 0:
            delay = INITIAL_RETRY_DELAY * (BACKOFF_FACTOR ** (attempt - 1))
            logger.info(
                f"Retry attempt {attempt + 1} for domain {domain}, waiting {delay} seconds"
            )
            await asyncio.sleep(delay)
        try:
            answers = await resolver.resolve(domain, dns.rdatatype.MX)
            result = [
                rdata.to_text() for rdata in answers
            ]  # trying different list comprehension to clean up
            if result:
                logger.info(f"Found {len(result)} MX records for {domain}")
                return result
            logger.info(f"No MX records found for {domain}")
        except dns.asyncresolver.NXDOMAIN:
            logger.info(f"MX NXDOMAIN for domain {domain}")
        except dns.asyncresolver.NoAnswer:
            logger.info(f"MX NoAnswer for domain {domain}")
        except Exception as e:
            logger.info(f"Error for domain {domain} and record type MX: {e}")
    logger.warning(f"All retry attempts exhausted for MX records of {domain}")
    return []


async def is_domain_available(domain: str) -> bool:
    async with asyncio.TaskGroup() as tg:
        ns_record_check = tg.create_task(is_nxdomain_from_ns_record(domain))
        mx_record_check = tg.create_task(is_nxdomain_from_mx(domain))
        a_record_check = tg.create_task(is_nxdomain_from_a_record(domain))

    return (
        ns_record_check.result()
        and mx_record_check.result()
        and a_record_check.result()
    )


async def is_nxdomain_from_a_record(domain: str) -> bool:
    # Try to resolve A record
    try:
        await resolver.resolve(domain, "A")
        return False
    except (dns.asyncresolver.NXDOMAIN, dns.asyncresolver.NoAnswer):
        return True
    except Exception as e:
        logger.warning(f"unanticipated error while attempting to resolve A record: {e}")
        return True


async def is_nxdomain_from_ns_record(domain: str) -> bool:
    # Try to resolve NS record
    try:
        await resolver.resolve(domain, "NS")
        return False
    except (dns.asyncresolver.NXDOMAIN, dns.asyncresolver.NoAnswer):
        return True
    except Exception as e:
        logger.warning(
            f"unanticipated error while attempting to resolve NS record: {e}"
        )
        return True


async def is_nxdomain_from_mx(domain: str) -> bool:
    # Try to resolve MX record
    try:
        await resolver.resolve(domain, "MX")
        return False
    except (dns.asyncresolver.NXDOMAIN, dns.asyncresolver.NoAnswer):
        return True
    except Exception as e:
        logger.warning(
            f"unanticipated error while attempting to resolve MX record: {e}"
        )
        return True


async def check_dmarc(
    domain: str,
) -> list[
    str
]:  # only expected to ever be == 1 since each domain should only have 1 DMARC object.
    return await check_txt(f"_dmarc.{domain}", "v=DMARC1")


async def check_dkim(
    domain: str, selector: str
) -> list[str]:  # there should only be 1 DKIM per selector as well
    return await check_txt(f"{selector}._domainkey.{domain}", "v=DKIM1")


# Consolidate all the DNS records into a single dictionary
async def check_dns(domain: str) -> DNSCheckResult:
    mx_records = not_none(
        EntityDnsRecord.list_from_request_field(await check_mx(domain))
    )
    dmarc_record = not_none(
        EntityDnsRecord.list_from_request_field(await check_dmarc(domain))
    )
    spf_records = not_none(
        EntityDnsRecord.list_from_request_field(await check_txt(domain, "v=spf1"))
    )
    dkim_record = not_none(
        EntityDnsRecord.list_from_request_field(
            await check_dkim(domain=domain, selector="default")
        )
    )
    return DNSCheckResult(
        mx_records=mx_records,
        dmarc_record=dmarc_record,
        spf_records=spf_records,
        dkim_record=dkim_record,
    )


class SingletonDnsCheckService(Singleton, DnsCheckService):
    pass


def get_dns_check_service(engine: DatabaseEngine) -> DnsCheckService:
    if SingletonDnsCheckService.has_instance():
        return SingletonDnsCheckService.get_singleton_instance()
    return DnsCheckService(engine=engine)
