from uuid import UUID

from salestech_be.core.crm_integrity.event_processors.abstract_processor import (
    AbstractDataOperationProcessor,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
    DataArchivalParam,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationGlobalThreadAccountPreviewIdentifier,
    IntegrityAssociatedDataOperationPreviewIdentifiers,
)
from salestech_be.core.email.service.email_data_integrity_service import (
    EmailDataIntegrityService,
    FetchAffectedEmailObjectsResult,
)
from salestech_be.db.models.crm_integrity import (
    AssociatedEntityType,
    CRMIntegrityJobUserOption,
)


class EmailAccountArchiveEventProcessor(
    AbstractDataOperationProcessor[DataArchivalParam, CrmIdReplacementTrackParam]
):
    """
    Event processor for Account Archive within Email domain.

    """

    def __init__(
        self,
        email_data_integrity_service: EmailDataIntegrityService,
    ) -> None:
        self.email_data_integrity_service = email_data_integrity_service

    async def fetch_affected_email_objects(
        self,
        *,
        organization_id: UUID,
        account_id: UUID,
    ) -> FetchAffectedEmailObjectsResult:
        return await self.email_data_integrity_service.find_email_objects_by_account_id(
            account_id=account_id,
            organization_id=organization_id,
        )

    async def process(
        self,
        *,
        param: DataArchivalParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[CrmIdReplacementTrackParam]:
        """
        No-op for email processing in this job
        """
        return []

    async def preview(
        self,
        *,
        param: DataArchivalParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[IntegrityAssociatedDataOperationPreviewIdentifiers]:
        affected_email_objects = await self.fetch_affected_email_objects(
            account_id=param.archive_entity_id,
            organization_id=param.organization_id,
        )
        return [
            IntegrityAssociatedDataOperationPreviewIdentifiers(
                entity_type=AssociatedEntityType.EMAIL,
                entity_ids=[
                    IntegrityAssociatedDataOperationGlobalThreadAccountPreviewIdentifier(
                        id=global_thread.id,
                        account_id=param.archive_entity_id,
                        subject=global_thread.subject,
                        global_message_ids=affected_email_objects.global_thread_to_global_message_map.get(
                            global_thread.id, []
                        ),
                    )
                    for global_thread in affected_email_objects.global_threads
                ],
            )
        ]
