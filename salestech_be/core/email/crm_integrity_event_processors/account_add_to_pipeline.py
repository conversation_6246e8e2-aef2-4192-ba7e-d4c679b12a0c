from uuid import UUID

from salestech_be.core.activity.service.activity_service import (
    ActivityService,
)
from salestech_be.core.activity.types import (
    Activity,
    ActivityPatchRequest,
)
from salestech_be.core.crm_integrity.event_processors.abstract_processor import (
    AbstractDataOperationProcessor,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
    DataAdditionParam,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationGlobalThreadAccountPreviewIdentifier,
    IntegrityAssociatedDataOperationPreviewIdentifiers,
)
from salestech_be.core.email.crm_integrity_event_processors.email_common_processor import (
    EmailCommonProcessor,
)
from salestech_be.core.email.global_email.global_thread_resolve_service import (
    GlobalThreadResolveService,
)
from salestech_be.core.email.service.email_data_integrity_service import (
    EmailDataIntegrityService,
    FetchAffectedEmailObjectsResult,
)
from salestech_be.db.models.activity import ActivityType
from salestech_be.db.models.crm_integrity import (
    AssociatedEntityField,
    AssociatedEntityFieldType,
    AssociatedEntityOperation,
    AssociatedEntityType,
    CRMIntegrityJobUserOption,
)
from salestech_be.db.models.global_thread import GlobalThread
from salestech_be.db.models.thread import Thread


class EmailAccountAddToPipelineEventProcessor(
    AbstractDataOperationProcessor[DataAdditionParam, CrmIdReplacementTrackParam]
):
    """
    Event processor for Account Add to Pipeline within Email domain.

    """

    def __init__(
        self,
        email_data_integrity_service: EmailDataIntegrityService,
        global_thread_resolve_service: GlobalThreadResolveService,
        activity_service: ActivityService,
    ) -> None:
        self.email_data_integrity_service = email_data_integrity_service
        self.activity_service = activity_service
        self.email_common_processor = EmailCommonProcessor(
            email_data_integrity_service=email_data_integrity_service,
            global_thread_resolve_service=global_thread_resolve_service,
        )

    async def fetch_affected_email_objects(
        self,
        *,
        organization_id: UUID,
        account_id: UUID,
    ) -> FetchAffectedEmailObjectsResult:
        return await self.email_data_integrity_service.find_email_objects_by_account_id(
            account_id=account_id,
            organization_id=organization_id,
        )

    async def fetch_affected_activity_records(
        self,
        *,
        organization_id: UUID,
        threads: list[Thread],
        global_threads: list[GlobalThread],
    ) -> tuple[dict[UUID, list[Activity]], dict[UUID, list[Activity]]]:
        """
        return
        a global_thread_id -> list of global_thread activities map
        and a global_thread_id -> list of thread activities map
        """
        thread_relevant_activities = (
            await self.activity_service.list_activities_by_reference_ids_and_type(
                organization_id=organization_id,
                reference_ids=[str(thread.id) for thread in threads],
                activity_type=ActivityType.EMAIL_THREAD,
            )
        )

        thread_to_activities_map: dict[UUID, list[Activity]] = {}
        for activity in thread_relevant_activities:
            thread_to_activities_map.setdefault(UUID(activity.reference_id), []).append(
                activity
            )

        global_thread_relevant_activities = (
            await self.activity_service.list_activities_by_reference_ids_and_type(
                organization_id=organization_id,
                reference_ids=[
                    str(global_thread.id) for global_thread in global_threads
                ],
                activity_type=ActivityType.EMAIL_GLOBAL_THREAD,
            )
        )

        global_thread_to_global_thread_activities_map: dict[UUID, list[Activity]] = {}
        for activity in global_thread_relevant_activities:
            global_thread_to_global_thread_activities_map.setdefault(
                UUID(activity.reference_id), []
            ).append(activity)

        global_thread_to_thread_activities_map: dict[UUID, list[Activity]] = {}
        for global_thread in global_threads:
            for thread_id in global_thread.thread_ids:
                global_thread_to_thread_activities_map.setdefault(
                    global_thread.id, []
                ).extend(thread_to_activities_map.get(thread_id, []))

        return (
            global_thread_to_global_thread_activities_map,
            global_thread_to_thread_activities_map,
        )

    async def process(
        self,
        *,
        param: DataAdditionParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[CrmIdReplacementTrackParam]:
        track_params = []
        affected_email_objects = await self.fetch_affected_email_objects(
            account_id=param.src_entity_id,
            organization_id=param.organization_id,
        )

        (
            global_thread_to_global_thread_activities_map,
            global_thread_to_thread_activities_map,
        ) = await self.fetch_affected_activity_records(
            organization_id=param.organization_id,
            threads=affected_email_objects.threads,
            global_threads=affected_email_objects.global_threads,
        )

        for global_thread in affected_email_objects.global_threads:
            if not global_thread.pipeline_id:
                # try resolve pipeline id again if it's not set
                track_pipeline_change = (
                    await self.email_common_processor.refresh_global_thread_pipeline_id(
                        global_thread=global_thread,
                        organization_id=param.organization_id,
                    )
                )
                track_params.extend(track_pipeline_change)

                if track_pipeline_change:
                    # if pipeline is changed, update related activities accordingly
                    for activity in global_thread_to_global_thread_activities_map.get(
                        global_thread.id, []
                    ) + global_thread_to_thread_activities_map.get(
                        global_thread.id, []
                    ):
                        await self.activity_service.patch_activity_by_id(
                            activity_id=activity.id,
                            organization_id=param.organization_id,
                            req=ActivityPatchRequest(
                                pipeline_id=param.dest_entity_id,
                            ),
                        )
                        track_params.append(
                            CrmIdReplacementTrackParam(
                                entity_type=AssociatedEntityType.ACTIVITY,
                                entity_id=activity.id,
                                entity_field_name=AssociatedEntityField.PIPELINE_ID,
                                entity_field_type=AssociatedEntityFieldType.UUID,
                                entity_operation=AssociatedEntityOperation.UPDATE,
                                before_value=str(activity.pipeline_id)
                                if activity.pipeline_id
                                else None,
                                after_value=str(param.dest_entity_id),
                            )
                        )

        return track_params

    async def preview(
        self,
        *,
        param: DataAdditionParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[IntegrityAssociatedDataOperationPreviewIdentifiers]:
        affected_email_objects = await self.fetch_affected_email_objects(
            account_id=param.src_entity_id,
            organization_id=param.organization_id,
        )
        return [
            IntegrityAssociatedDataOperationPreviewIdentifiers(
                entity_type=AssociatedEntityType.EMAIL,
                entity_ids=[
                    IntegrityAssociatedDataOperationGlobalThreadAccountPreviewIdentifier(
                        id=global_thread.id,
                        account_id=param.src_entity_id,
                        subject=global_thread.subject,
                        global_message_ids=affected_email_objects.global_thread_to_global_message_map.get(
                            global_thread.id, []
                        ),
                    )
                    for global_thread in affected_email_objects.global_threads
                    if not global_thread.pipeline_id
                ],
            )
        ]
