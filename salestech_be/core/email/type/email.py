from __future__ import annotations

import uuid
from enum import StrEnum
from typing import Any

from email_validator.rfc_constants import LOCAL_PART_MAX_LENGTH
from nylas.models.events import EmailName
from pydantic import BaseModel, EmailStr, field_validator

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import ErrorDetails, InvalidArgumentError
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.db.models.email_account import EmailAccount
from salestech_be.integrations.nylas.model import NylasEmailName
from salestech_be.util.enum_util import NameValueStrEnum


class EmailHydratedParticipant(BaseModel):
    email_account_id: uuid.UUID | None = None
    account_id: uuid.UUID | None = None
    contact_id: uuid.UUID | None = None
    email: EmailStr
    name: str | None = None

    def validate_request(self, validate_email_account_id: bool = False) -> None:
        if self.email is None:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_REQUEST, details="Email address is required."
                )
            )

        if not self.email_account_id and not self.contact_id:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_REQUEST,
                    details="Email Participant must be either a email_account or a "
                    "contact, one of the ids must be set.",
                )
            )
        if (
            sum(
                [
                    bool(self.email_account_id),
                    bool(self.contact_id),
                ],
            )
            > 1
        ):
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_REQUEST,
                    details="Only one of recipient's email_account_id "
                    " and contact_id can be set.",
                )
            )

        if validate_email_account_id and not self.email_account_id:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_REQUEST,
                    details="email_account_id is required.",
                )
            )

    @classmethod
    def from_nylas_email_name_and_person(
        cls,
        nylas_email_name: NylasEmailName,
        person: ContactV2 | EmailAccount | None = None,
        contact_account_map: dict[uuid.UUID, uuid.UUID | None] | None = None,
    ) -> EmailHydratedParticipant:
        if isinstance(person, EmailAccount):
            return EmailHydratedParticipant(
                email=nylas_email_name.email,
                name=nylas_email_name.name,
                email_account_id=person.id,
            )
        if isinstance(person, ContactV2):
            return EmailHydratedParticipant(
                account_id=contact_account_map.get(person.id)
                if contact_account_map
                else None,
                email=nylas_email_name.email,
                name=nylas_email_name.name,
                contact_id=person.id,
            )
        return EmailHydratedParticipant(
            email=nylas_email_name.email,
            name=nylas_email_name.name,
        )

    def to_nylas_email_name(self) -> EmailName:
        return EmailName(name=self.name, email=self.email)

    # convert to serialized dict
    def to_serializable_dict(self) -> dict[str, str | None]:
        return {
            "email": self.email if self.email else None,
            "name": self.name if self.name else None,
            "email_account_id": str(self.email_account_id)
            if self.email_account_id
            else None,
            "contact_id": str(self.contact_id) if self.contact_id else None,
        }

    # EmailStr has a 64-character limit for the part before the '@' symbol. Most real
    # user emails should not exceed this limit. However, some non-deliverable email
    # addresses, like <EMAIL>, can exceed this limit. Therefore, we add a
    # custom validator to truncate the local part to 100 characters to ensure it passes
    # validation.
    @field_validator("email", mode="before")
    @classmethod
    def truncate_email_local_part(cls, value: str | None) -> str | None:
        if value is None:
            return value
        # The ".invalid" suffix is automatically appended to an email address when a bounce occurs,
        # indicating that the email could not be delivered due to a permanent issue.
        # In this case, we revert to the original email address instead of raising a validation error.
        if value.endswith(".invalid"):
            value = value.removesuffix(".invalid")

        max_length = LOCAL_PART_MAX_LENGTH
        local_part, domain = value.split("@")
        if len(local_part) > max_length:
            local_part = local_part[:max_length]
        return f"{local_part}@{domain}"

    def __hash__(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return hash(self.email)

    def __eq__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self is other or self.email == other.email


class EmailTag(StrEnum):
    OUTBOUND = "outbound"
    DIALER_FOLLOWUP = "dialer_followup"


class ReevoEmailHeader(StrEnum):
    REEVO_TAG_HEADER = "X-Reevo-Tag"


class TrackingEventType(StrEnum):
    EMAIL_OPENED = "email_opened"
    LINK_CLICKED = "link_clicked"


class TrackingReferenceType(StrEnum):
    GLOBAL_MESSAGE_ID = "global_message_id"


class TrackingEventShortenedKeys(StrEnum):
    ID = "id"
    ORIGINAL_URL = "ou"


class EmailAccountArchiveSequenceHandling(NameValueStrEnum):
    STOP = "STOP"  # behavior 1: completely stop the sequence/s
    CONTINUE = "CONTINUE"  # behavior 2: choose other mailbox/es to continue sequence/s
    RESTART = "RESTART"  # behavior 3: restart with new mailbox in same sequence/s


class OptionalEmailMixin(BaseModel):
    email: EmailStr | None = None


class PreviewTemplateBodyParticipant(OptionalEmailMixin, EmailHydratedParticipant):
    def validate_request(self, validate_email_account_id: bool = False) -> None:
        if validate_email_account_id and not self.email_account_id:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_REQUEST,
                    details="email_account_id is required.",
                )
            )

        if self.email_account_id and self.contact_id:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_REQUEST,
                    details="Only one of email_account_id and contact_id can be set.",
                )
            )
