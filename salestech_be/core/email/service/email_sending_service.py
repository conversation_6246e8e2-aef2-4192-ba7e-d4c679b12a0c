import base64
from datetime import datetime
from uuid import UUID

import pytz
from nylas.utils.file_utils import MAXIMUM_JSON_ATTACHMENT_SIZE

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ErrorDetails,
    ExternalServiceError,
    ServiceError,
)
from salestech_be.common.singleton import Singleton
from salestech_be.core.contact.service.contact_resolve_service import (
    get_contact_resolve_service,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.event.email_event_service import EmailEventService
from salestech_be.core.email.participant.email_participant_service import (
    EmailParticipantService,
)
from salestech_be.core.email.service.smtp_sending_service import SmtpSendingService
from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.core.quota.service.quota_service import QuotaService
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.email_dto import EmailDto, GlobalEmailDto, MessageDto
from salestech_be.db.models.email_account import EmailAccount, EmailAccountType
from salestech_be.db.models.message import (
    EmailProvider,
    MessageStatus,
    MessageUpdate,
)
from salestech_be.db.models.quota import QuotaConsumerEntityType, QuotaConsumingResource
from salestech_be.db.models.thread import ThreadUpdate
from salestech_be.integrations.nylas.async_nylas_client import AsyncNylasClient
from salestech_be.integrations.nylas.model import (
    NylasCreateAttachmentRequest,
)
from salestech_be.integrations.s3.s3_bucket_manager import (
    get_s3_bucket_manager_by_bucket_name,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger()


class EmailSendingService:
    def __init__(
        self,
        db_engine: DatabaseEngine,
    ):
        self._async_nylas_client = AsyncNylasClient()
        self._s3_bucket_manager = get_s3_bucket_manager_by_bucket_name(
            settings.email_attachment_bucket_name,
            aws_access_key_id=settings.s3_external_share_user_access_key_id,
            aws_secret_access_key=settings.s3_external_share_user_access_key_secret,
        )
        self._thread_repository = ThreadRepository(engine=db_engine)
        self._email_participant_service = EmailParticipantService(db_engine=db_engine)
        self._email_quota_service = QuotaService(db_engine=db_engine)
        self._email_account_service = EmailAccountServiceExt(engine=db_engine)
        self.email_event_service = EmailEventService(db_engine=db_engine)
        self._smtp_service = SmtpSendingService(db_engine=db_engine)
        self.contact_resolve_service = get_contact_resolve_service(db_engine=db_engine)

    async def send_messages_to_provider(
        self,
        organization_id: UUID,
        global_email_dto: GlobalEmailDto,
        reply_to_provider_message_id: str | None = None,
    ) -> GlobalEmailDto:
        if len(global_email_dto.message_dtos) != 1:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INCORRECT_MESSAGE_NUMBER_TO_SEND,
                    details="Only one message can be sent at a time.",
                )
            )
        email_account_id = global_email_dto.message_dtos[0].message.email_account_id
        email_account = (
            await self._email_account_service.get_decrypted_email_account_by_id(
                email_account_id=email_account_id,
            )
        )
        if not email_account:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"Email account {email_account_id} not found.",
                )
            )

        try:
            # Send the message through the provider
            if email_account.type == EmailAccountType.REGULAR:
                sent_email_dto = await self.send_message_through_nylas(
                    organization_id=organization_id,
                    global_email_dto=global_email_dto,
                    reply_to_provider_message_id=reply_to_provider_message_id,
                    email_account_id=email_account_id,
                )
            else:
                sent_email_dto = await self.send_message_through_smtp(
                    global_email_dto=global_email_dto,
                    decrypted_email_account=email_account,
                )

            # Post-send processing
            # Only track the usage when it's an immediate sent(status CREATED),
            # the scheduled message quota is recorded when create the message
            message_dto_to_send = global_email_dto.message_dtos[0]
            if message_dto_to_send.message.status == MessageStatus.CREATED:
                await self._email_quota_service.increase_usage(
                    organization_id=organization_id,
                    entity_id=email_account_id,
                    entity_type=QuotaConsumerEntityType.EMAIL_ACCOUNT,
                    resource=QuotaConsumingResource.EMAIL,
                    usage=1,
                    timestamp=zoned_utc_now(),
                )

            return sent_email_dto
        except Exception as e:
            logger.bind(
                message_id=global_email_dto.message_dtos[0].message.id,
                exc_info=e,
            ).error("message failed to send to provider")
            raise e

    async def send_message_through_nylas(
        self,
        organization_id: UUID,
        global_email_dto: GlobalEmailDto,
        email_account_id: UUID,
        reply_to_provider_message_id: str | None = None,
    ) -> GlobalEmailDto:
        message_dto_to_send = global_email_dto.message_dtos[0]
        (
            grant_id,
            integration_provider,
        ) = await self._email_account_service.get_nylas_grant_id_by_email_account_id(
            email_account_id=email_account_id,
            organization_id=organization_id,
        )
        nylas_send_message_request = message_dto_to_send.to_nylas_send_message_request(
            reply_to_nylas_message_id=reply_to_provider_message_id
        )
        nylas_attachments = []
        total_size = sum(
            attachment.attachment.size for attachment in message_dto_to_send.attachments
        )

        for attachment_dto in message_dto_to_send.attachments:
            attachment = attachment_dto.attachment
            binary_content = self._s3_bucket_manager.read_object_buffer(
                key=not_none(attachment.s3_key)
            )
            nylas_attachments.append(
                NylasCreateAttachmentRequest(
                    filename=attachment.file_name,
                    content_type=attachment.content_type,
                    content=base64.b64encode(binary_content).decode()
                    if total_size < MAXIMUM_JSON_ATTACHMENT_SIZE
                    else binary_content,
                    size=attachment.size,
                    is_inline=attachment.is_inline,
                    content_id=str(attachment.id) if attachment.is_inline else None,
                )
            )
        if nylas_attachments:
            nylas_send_message_request = nylas_send_message_request.model_copy(
                update={"attachments": nylas_attachments}
            )
        sent_nylas_message = await self._async_nylas_client.send_message(
            grant_id=grant_id, nylas_send_message_request=nylas_send_message_request
        )

        # Fetch the message again with headers
        nylas_message = await self._async_nylas_client.get_message_by_id(
            grant_id=grant_id,
            nylas_message_id=sent_nylas_message.id,
            include_headers=True,
        )

        nylas_thread = await self._async_nylas_client.get_thread_by_id(
            grant_id=grant_id,
            nylas_thread_id=not_none(nylas_message.thread_id),
        )
        if not nylas_thread:
            raise ExternalServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.MISSING_PROVIDER_ENTITY,
                    details=f"Can not find thread {nylas_message.thread_id} on Nylas.",
                )
            )
        if nylas_thread.participants:
            emails_in_nylas_thread = [
                email.email for email in nylas_thread.participants
            ]
            email_person_map = await self._email_participant_service.find_contact_or_email_account_by_emails(
                organization_id=organization_id, emails=emails_in_nylas_thread
            )
        else:
            email_person_map = {}

        contact_email_pairs: list[tuple[UUID | None, str | None]] = []
        for participant in nylas_thread.participants or []:
            person = email_person_map.get(participant.email)
            if isinstance(person, ContactV2):
                contact_email_pairs.append((person.id, participant.email))
        contact_account_map = await self.contact_resolve_service.resolve_relevant_account_by_contact_and_email_pairs(
            organization_id=organization_id,
            contact_email_pairs=contact_email_pairs,
        )
        thread_update_fields = ThreadUpdate(
            subject=nylas_thread.subject or global_email_dto.thread.subject,
            snippet=nylas_thread.snippet or global_email_dto.thread.snippet,
            unread=nylas_thread.unread,
            starred=nylas_thread.starred,
            has_attachments=nylas_thread.has_attachments,
            has_drafts=nylas_thread.has_drafts,
            folders=nylas_thread.folders,
            earliest_message_date=datetime.fromtimestamp(
                nylas_thread.earliest_message_date, pytz.UTC
            )
            if nylas_thread.earliest_message_date
            else global_email_dto.thread.earliest_message_date,
            latest_message_sent_date=datetime.fromtimestamp(
                nylas_thread.latest_message_sent_date, pytz.UTC
            )
            if nylas_thread.latest_message_sent_date
            else None,
            latest_message_received_date=datetime.fromtimestamp(
                nylas_thread.latest_message_received_date, pytz.UTC
            )
            if nylas_thread.latest_message_received_date
            else None,
            participants=[
                EmailHydratedParticipant.from_nylas_email_name_and_person(
                    nylas_email_name=nylas_email_name,
                    person=email_person_map.get(nylas_email_name.email, None),
                    contact_account_map=contact_account_map,
                )
                for nylas_email_name in nylas_thread.participants
            ]
            if nylas_thread.participants
            else [],
            updated_at=zoned_utc_now(),
        )

        if not global_email_dto.thread.provider_id:
            thread_update_fields.provider_id = nylas_thread.id
        if not global_email_dto.thread.provider:
            thread_update_fields.provider = EmailProvider.NYLAS

        updated_incomplete_email_dto = (
            await self._thread_repository.update_thread_and_message(
                thread_id=global_email_dto.thread.id,
                message_id=message_dto_to_send.message.id,
                thread_update_fields=thread_update_fields,
                message_update_fields=MessageUpdate(
                    subject=nylas_message.subject
                    or message_dto_to_send.message.subject,
                    status=MessageStatus.SENT,
                    provider=EmailProvider.NYLAS,
                    provider_id=nylas_message.id,
                    snippet=nylas_message.snippet or "",
                    folders=nylas_message.folders,
                    starred=nylas_message.starred,
                    unread=nylas_message.unread,
                    send_at=zoned_utc_now(),
                    received_at=datetime.fromtimestamp(nylas_message.date, pytz.UTC)
                    if nylas_message.date
                    else None,
                    headers=nylas_message.message_headers,
                ),
            )
        )
        email_dto = EmailDto(
            thread=updated_incomplete_email_dto.thread,
            message_dtos=[
                MessageDto(
                    message=updated_incomplete_email_dto.message_dtos[0].message,
                    attachments=message_dto_to_send.attachments,
                )
            ],
        )
        return GlobalEmailDto.from_email_dto(
            email_dto=email_dto,
            global_thread_id=global_email_dto.global_thread_id,
            global_message_mapping=global_email_dto.global_message_mapping,
        )

    async def send_message_through_smtp(
        self,
        global_email_dto: GlobalEmailDto,
        decrypted_email_account: EmailAccount,
    ) -> GlobalEmailDto:
        updated_email_dto = await self._smtp_service.send_message(
            global_email_dto=global_email_dto,
            decrypted_email_account=decrypted_email_account,
        )
        sent_message_dto = updated_email_dto.message_dtos[0]
        all_message_participants = set(
            sent_message_dto.message.send_from
            + sent_message_dto.message.send_to
            + (sent_message_dto.message.cc if sent_message_dto.message.cc else [])
            + (sent_message_dto.message.bcc if sent_message_dto.message.bcc else [])
        )
        thread_provider_id = (
            updated_email_dto.thread.provider_id
            or not_none(sent_message_dto.message.headers).message_id
        )
        thread_update_fields = ThreadUpdate(
            latest_message_sent_date=zoned_utc_now(),
            participants=list(
                set(updated_email_dto.thread.participants) | all_message_participants
            ),
            provider=EmailProvider.INFRAFORGE,
            provider_id=thread_provider_id,
            updated_at=zoned_utc_now(),
        )
        email_dto = await self._thread_repository.update_thread_and_message(
            thread_id=global_email_dto.thread.id,
            message_id=sent_message_dto.message.id,
            thread_update_fields=thread_update_fields,
            message_update_fields=MessageUpdate(
                status=MessageStatus.SENT,
                provider=EmailProvider.INFRAFORGE,
                provider_id=sent_message_dto.message.provider_id,
                folders=sent_message_dto.message.folders,
                send_at=zoned_utc_now(),
                headers=sent_message_dto.message.headers,
            ),
        )
        return GlobalEmailDto.from_email_dto(
            email_dto=email_dto,
            global_thread_id=global_email_dto.global_thread_id,
            global_message_mapping=global_email_dto.global_message_mapping,
        )


class SingletonEmailSendingService(Singleton, EmailSendingService):
    pass


def get_email_sending_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> EmailSendingService:
    return SingletonEmailSendingService(db_engine=db_engine)
