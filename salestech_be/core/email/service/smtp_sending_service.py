import imaplib
import quopri
import smtplib
import time
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import formatdate
from uuid import UUID

from bs4 import BeautifulSoup

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception.exception import ErrorDetails, ServiceError
from salestech_be.core.email.service.email_utils import generate_email_hash
from salestech_be.core.email.type.email import (
    EmailHydratedParticipant,
    EmailTag,
    ReevoEmailHeader,
    TrackingEventType,
    TrackingReferenceType,
)
from salestech_be.core.email.unsubscription_group.unsubscription_group_ext import (
    get_unsubscription_ext_service_by_db_engine,
)
from salestech_be.core.email.utils import is_valid_web_url
from salestech_be.core.event_tracking.event_tracking_service import (
    get_event_tracking_service,
)
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.email_dto import GlobalEmailDto
from salestech_be.db.models.email_account import EmailAccount
from salestech_be.db.models.event_tracking import CreateDbEventTrackingRequest
from salestech_be.db.models.message import Message
from salestech_be.integrations.nylas.model import MessageHeaders
from salestech_be.integrations.s3.s3_bucket_manager import (
    get_s3_bucket_manager_by_bucket_name,
)
from salestech_be.ree_logging import get_logger, log_context
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.str import MessageId
from salestech_be.util.validation import not_none

logger = get_logger()

# Constants
SMTP_SSL_PORT = 465


def starttls_setup(server: smtplib.SMTP | smtplib.SMTP_SSL) -> None:
    if isinstance(server, smtplib.SMTP):
        server.starttls()


class SmtpSendingService:
    def __init__(self, db_engine: DatabaseEngine):
        self.message_repository = MessageRepository(engine=db_engine)
        self._s3_bucket_manager = get_s3_bucket_manager_by_bucket_name(
            settings.email_attachment_bucket_name,
            aws_access_key_id=settings.s3_external_share_user_access_key_id,
            aws_secret_access_key=settings.s3_external_share_user_access_key_secret,
        )
        self.event_tracking_service = get_event_tracking_service(db_engine=db_engine)
        self.unsubscription_group_service = get_unsubscription_ext_service_by_db_engine(
            engine=db_engine
        )

    @staticmethod
    def _generate_message_id(
        message_mime: MIMEMultipart, domain: str | None = None
    ) -> MessageId:
        """
        Generate a unique Message ID compliant with RFC 5322.
        The Message-ID format should be: "<" id-left "@" id-right ">"

        :param message_mime: The MIME message to generate the hash from.
        :param domain: The domain to use in the Message ID.
        :return: A string containing the generated Message ID that complies with RFC 5322.
        """
        message_hash = generate_email_hash(message_mime)
        domain_part = domain if domain else "reevo.ai"
        return f"<{message_hash}@{domain_part}>"

    async def _thread_headers(
        self,
        thread_id: UUID,
        organization_id: UUID,
        new_message_id: UUID,
        in_reply_to_message_id: UUID | None = None,
    ) -> dict[str, str]:
        messages = await self.message_repository.list_by_thread_id(
            thread_id=thread_id, organization_id=organization_id
        )
        previous_messages = [
            message for message in messages if message.id != new_message_id
        ]
        headers = {
            "References": " ".join(
                [
                    message.provider_id
                    for message in previous_messages
                    if message.provider_id
                ]
            ),
        }
        # Use the in_reply_to if it exists, otherwise use the most recent message id
        if in_reply_to_message_id:
            in_reply_to_message = await self.message_repository.get_by_message_id(
                message_id=in_reply_to_message_id, organization_id=organization_id
            )
            if in_reply_to_message.provider_id:
                headers["In-Reply-To"] = in_reply_to_message.provider_id
        else:
            most_recent_message_provider_id = (
                previous_messages[-1].provider_id if previous_messages else None
            )
            if most_recent_message_provider_id:
                headers["In-Reply-To"] = most_recent_message_provider_id

        # Add outbound tag to the headers
        headers[ReevoEmailHeader.REEVO_TAG_HEADER] = EmailTag.OUTBOUND

        return headers

    def _format_email_participant(self, participant: EmailHydratedParticipant) -> str:
        return (
            f"{participant.name} <{participant.email}>"
            if participant.name
            else participant.email
        )

    def _from_to_headers(self, message_db: Message) -> dict[str, str]:
        send_from = message_db.send_from[0]
        headers = {
            "From": f"{send_from.name} <{send_from.email}>"
            if send_from.name
            else send_from.email,
            "To": ", ".join(
                [
                    self._format_email_participant(participant)
                    for participant in message_db.send_to
                ]
            ),
        }
        if message_db.cc:
            headers["Cc"] = ", ".join(
                [
                    self._format_email_participant(participant)
                    for participant in (message_db.cc or [])
                ]
            )
        if message_db.bcc:
            headers["Bcc"] = ", ".join(
                [
                    self._format_email_participant(participant)
                    for participant in (message_db.bcc or [])
                ]
            )
        return headers

    @log_context(from_kwargs=["email_dto"])
    async def send_message(
        self, global_email_dto: GlobalEmailDto, decrypted_email_account: EmailAccount
    ) -> GlobalEmailDto:
        message_dto = global_email_dto.message_dtos[0]
        message_to_send = message_dto.message
        sender_email_domain = message_to_send.send_from[0].email.split("@")[1]

        # Determine if custom tracking domains should be used
        use_custom_domain = settings.enable_purchased_domain_cname or (
            str(decrypted_email_account.organization_id)
            in settings.enable_purchased_domain_cname_org_ids
        )

        custom_cname_exists = False
        if use_custom_domain:
            # TODO: This is currently hardcoded to False. In the future, implement proper validation
            # of the custom domain to determine if a CNAME record exists.
            pass

        validated_sender_domain = (
            sender_email_domain if (use_custom_domain and custom_cname_exists) else None
        )

        message_mime = MIMEMultipart()
        message_mime["Subject"] = message_to_send.subject
        message_mime["Date"] = formatdate()
        unsubscription_link = (
            await self.unsubscription_group_service.generate_unsubscription_link(
                organization_id=decrypted_email_account.organization_id,
                global_message_id=not_none(
                    global_email_dto.get_global_message_id_by_message_id(
                        global_email_dto.message_dtos[0].message.id
                    )
                ),
                recipient_email=global_email_dto.message_dtos[0]
                .message.send_to[0]
                .email,
            )
        )
        message_mime["List-Unsubscribe"] = f"<{unsubscription_link}>"
        thread_headers = await self._thread_headers(
            thread_id=message_to_send.thread_id,
            organization_id=message_to_send.organization_id,
            new_message_id=message_to_send.id,
            in_reply_to_message_id=message_to_send.reply_to_message_id,
        )
        for header, value in thread_headers.items():
            message_mime[header] = value
        from_to_headers = self._from_to_headers(message_to_send)
        for header, value in from_to_headers.items():
            message_mime[header] = value

        logger.bind(message_mime=message_mime).info("[smtp] sending MIME message...")
        # Embed tracking url
        global_message_id = not_none(
            global_email_dto.get_global_message_id_by_message_id(
                message_id=message_dto.message.id
            )
        )
        body_html = await self.embed_tracking_url(
            organization_id=decrypted_email_account.organization_id,
            global_message_id=global_message_id,
            message_html=message_to_send.body_html,
            sender_domain=validated_sender_domain,
        )

        # Encode the HTML body in quoted-printable with utf-8
        encoded_html = quopri.encodestring(body_html.encode("utf-8")).decode("utf-8")

        # Create MIMEText without a charset so we don't get automatic base64 encoding
        html_part = MIMEText("", _subtype="html")
        html_part.set_payload(encoded_html)

        # Now set the charset and encoding manually
        html_part.set_charset("utf-8")
        html_part.replace_header("Content-Transfer-Encoding", "quoted-printable")

        message_mime.attach(html_part)
        for attachment_dto in message_dto.attachments:
            attachment = attachment_dto.attachment
            binary_content = self._s3_bucket_manager.read_object_buffer(
                key=not_none(attachment.s3_key)
            )

            if attachment.is_inline:
                mime_attachment = MIMEApplication(binary_content)
                mime_attachment.add_header(
                    "Content-ID", f"<{attachment.provider_content_id}>"
                )
                mime_attachment.add_header(
                    "Content-Disposition", "inline", filename=attachment.file_name
                )
            else:
                mime_attachment = MIMEApplication(binary_content)
                mime_attachment.add_header(
                    "Content-Disposition", "attachment", filename=attachment.file_name
                )

            message_mime.attach(mime_attachment)

        message_id = self._generate_message_id(
            message_mime=message_mime, domain=sender_email_domain
        )
        # Update the message DTO with the new provider ID
        updated_message = message_to_send.model_copy(
            update={
                "provider_id": message_id,
                "headers": MessageHeaders(
                    message_id=message_id,
                    in_reply_to=thread_headers.get("In-Reply-To"),
                    references=thread_headers.get("References", "").split(),
                ),
            }
        )
        message_mime["Message-ID"] = message_id

        # Send outbound email.
        await self._send_smtp(message_mime, decrypted_email_account)

        # Append to IMAP INBOX.
        with imaplib.IMAP4_SSL(
            host=not_none(decrypted_email_account.imap_host),
            port=not_none(decrypted_email_account.imap_port),
        ) as imap_server:
            imap_server.login(
                user=not_none(decrypted_email_account.imap_username),
                password=not_none(decrypted_email_account.imap_password),
            )
            result = imap_server.append(
                mailbox="INBOX",
                flags="\\Seen",
                date_time=imaplib.Time2Internaldate(time.time()),
                message=message_mime.as_string().encode(),
            )
            if result[0] != "OK":
                logger.error("[smtp] failed to append message to IMAP server")

        # TODO: update folder
        return GlobalEmailDto(
            email_dto=global_email_dto.email_dto.model_copy(
                update={
                    "message_dtos": [
                        message_dto.model_copy(update={"message": updated_message})
                    ]
                }
            ),
            global_thread_id=global_email_dto.global_thread_id,
            global_message_mapping=global_email_dto.global_message_mapping,
        )

    async def _send_smtp(
        self, message_mime: MIMEMultipart, email_account: EmailAccount
    ) -> None:
        """
        Only intended to perform sending of an email given a MIME message, and an object containing SMTP credentials.

        Make any modifications to the email content prior to calling this method.

        This is only intended to be called from send_message(), or for debugging purposes.

        :param message_mime: The MIME message to send.
        :param email_account: The email account to use to send the message.
        """
        smtp_host = email_account.smtp_host
        smtp_port = email_account.smtp_port
        smtp_username = email_account.smtp_username
        smtp_password = email_account.smtp_password
        if not smtp_host or not smtp_port or not smtp_username or not smtp_password:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.EMAIL_ACCOUNT_CONFIGURATION_ERROR,
                    details=f"Email account {email_account.id} configuration is missing.",
                )
            )

        # Send email via SMTP
        # Port 465 uses implicit SSL/TLS from the start
        # Other ports (typically 587) use STARTTLS to upgrade the connection
        with (
            smtplib.SMTP_SSL(host=not_none(smtp_host), port=not_none(smtp_port))
            if smtp_port == SMTP_SSL_PORT
            else smtplib.SMTP(host=not_none(smtp_host), port=not_none(smtp_port))
        ) as server:
            # For non-SSL connections, upgrade to TLS
            if smtp_port != SMTP_SSL_PORT:
                server.starttls()

            server.login(user=not_none(smtp_username), password=not_none(smtp_password))
            server.send_message(message_mime)

    async def embed_tracking_url(
        self,
        organization_id: UUID,
        global_message_id: UUID,
        message_html: str,
        sender_domain: str | None = None,
    ) -> str:
        """Embeds tracking elements in the HTML content of an email.

        This method performs two key tracking modifications to the email content:
        1. Replaces all valid web links with tracking URLs that point to the event tracking service
        2. Adds an invisible 1x1 tracking pixel at the end of the email body

        When a recipient opens the email or clicks a link, these tracking elements will
        send a request to the event tracking service, which records the event.

        If sender_domain is provided and the custom tracking domains feature is enabled
        for the organization, the tracking URLs will be generated using the custom domain
        with the configured subdomain and path (e.g., m.customer.com/e).

        Args:
            organization_id: The UUID of the organization sending the email
            global_message_id: The UUID of the email message being tracked
            message_html: The original HTML content of the email
            sender_domain: Optional domain of the sender (e.g., "customer.com")
                           Used to generate custom tracking domains when enabled

        Returns:
            str: The modified HTML content with tracking elements embedded
                 Returns the original content unchanged if an error occurs
        """
        try:
            # Parse HTML and inject tracking pixel
            soup = BeautifulSoup(message_html, "html.parser")

            # Replace all links with tracking links
            for link in soup.find_all("a", href=True):
                original_url = link["href"]
                # Skip empty links, anchors, javascript, and unsubscribe links
                if (
                    is_valid_web_url(original_url)
                    and link.get("id") != settings.unsubscribe_link_html_id
                ):
                    link_req = CreateDbEventTrackingRequest(
                        organization_id=organization_id,
                        reference_id=str(global_message_id),
                        reference_type=TrackingReferenceType.GLOBAL_MESSAGE_ID,
                        event_type=TrackingEventType.LINK_CLICKED,
                        original_url=original_url,
                    )
                    link_tracker = (
                        await self.event_tracking_service.get_or_create_tracker(
                            link_req, sender_domain=sender_domain
                        )
                    )
                    link["href"] = link_tracker.tracking_url

            # Create tracking pixel
            pixel_req = CreateDbEventTrackingRequest(
                organization_id=organization_id,
                reference_id=str(global_message_id),
                reference_type=TrackingReferenceType.GLOBAL_MESSAGE_ID,
                event_type=TrackingEventType.EMAIL_OPENED,
            )
            pixel_tracker = await self.event_tracking_service.get_or_create_tracker(
                pixel_req, sender_domain=sender_domain
            )
            tracking_pixel = BeautifulSoup(
                f'<img src="{pixel_tracker.tracking_url}" alt="" '
                'width="1" height="1" '
                'style="display:none!important;opacity:0!important;'
                "width:1px!important;height:1px!important;border:0!important;"
                'margin:0!important;padding:0!important" '
                'aria-hidden="true" tabindex="-1">',
                "html.parser",
            )

            # Make sure everything ends up inside one <body> tag
            if soup.body:
                soup.body.append(tracking_pixel)
                return str(soup)
            else:
                # Wrap existing content and pixel inside a new <body>
                wrapper = BeautifulSoup("<html><body></body></html>", "html.parser")
                wrapper.body.extend(soup.contents)
                wrapper.body.append(tracking_pixel)
                return str(wrapper)

        except Exception as e:
            logger.error(
                "[embed_tracking_url] failed to embed tracking url", exc_info=e
            )

        return message_html
