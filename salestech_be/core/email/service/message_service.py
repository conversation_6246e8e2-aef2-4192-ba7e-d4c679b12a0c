import uuid
from datetime import <PERSON><PERSON><PERSON>
from typing import Annotated
from uuid import UUID

from fastapi import Depends, Request
from temporalio.common import WorkflowIDReusePolicy

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ErrorDetails,
    IllegalStateError,
    ResourceNotFoundError,
    ServiceError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.core.ai.email.activities.classify_message_and_update_metadata import (
    classify_message_and_update_metadata,
)
from salestech_be.core.ai.email.activities.parse_main_body_text_and_persist import (
    parse_main_body_text_and_persist,
)
from salestech_be.core.ai.tasks.workflow_triggers.start_close_existing_tasks import (
    start_close_existing_tasks,
)
from salestech_be.core.ai.workflows.schema import IntelTriggerObjectType
from salestech_be.core.contact.service.contact_resolve_service import (
    get_contact_resolve_service,
)
from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.activity.email_activity_service import (
    get_email_activity_service_by_db_engine,
)
from salestech_be.core.email.attachment.email_attachment_service import (
    get_email_attachment_service_by_db_engine,
)
from salestech_be.core.email.event.email_event_service import (
    get_email_event_service_by_db_engine,
)
from salestech_be.core.email.global_email.global_thread_service import (
    get_global_thread_service_by_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.core.email.message.schema import (
    EmailOperationStatus,
    EmailSendingCancelRequest,
    EmailSendingErrorCode,
    EmailSendingMessageLevelError,
    EmailSendingResponse,
    MessageOperationSuccessData,
    MessageUpdateResult,
)
from salestech_be.core.email.participant.email_participant_service import (
    EmailParticipantService,
)
from salestech_be.core.email.service.email_sending_service import (
    get_email_sending_service_by_db_engine,
)
from salestech_be.core.email.service.message_service_ext import MessageServiceExt
from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.core.email.unsubscription_group.unsubscription_group_ext import (
    get_unsubscription_ext_service_by_db_engine,
)
from salestech_be.core.ff.feature_flag_service import get_feature_flag_service
from salestech_be.core.ff.types import FeatureFlagRequest
from salestech_be.core.quota.service.quota_service import QuotaService
from salestech_be.db.dao.attachment_repository import AttachmentRepository
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.email_attchment_dto import AttachmentDto
from salestech_be.db.dto.email_dto import EmailDto, GlobalEmailDto, MessageDto
from salestech_be.db.models.email_account import EmailAccount
from salestech_be.db.models.global_thread import (
    GlobalMessage,
    GlobalMessageAssociation,
    GlobalMessageAssociationUpdate,
    GlobalMessageUpdate,
    GlobalThread,
    GlobalThreadMessagesAssociation,
    GlobalThreadMessagesAssociationUpdate,
    GlobalThreadUpdate,
)
from salestech_be.db.models.message import (
    Message,
    MessageHeaders,
    MessageSource,
    MessageStatus,
    MessageUpdate,
    _compress_body_html,
)
from salestech_be.db.models.quota import QuotaConsumerEntityType, QuotaConsumingResource
from salestech_be.db.models.sequence import EmailEventType
from salestech_be.db.models.thread import Thread, ThreadUpdate
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import TemporalTaskQueue
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.workflows.email.email_insight_workflow import (
    EmailInsightWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.pydantic_types.copy_util import strict_model_copy
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.email.message.schema import (
    SendMessageRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger()


class MessageService:
    def __init__(
        self,
        db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
    ):
        self.message_repository = MessageRepository(engine=db_engine)
        self.thread_repository = ThreadRepository(engine=db_engine)
        self.attachment_repository = AttachmentRepository(engine=db_engine)
        self._email_activity_service = get_email_activity_service_by_db_engine(
            db_engine=db_engine
        )
        self._email_attachment_service = get_email_attachment_service_by_db_engine(
            db_engine=db_engine
        )
        self._email_participant_service = EmailParticipantService(db_engine=db_engine)
        self._email_sending_service = get_email_sending_service_by_db_engine(
            db_engine=db_engine
        )
        self._message_service_ext = MessageServiceExt(db_engine=db_engine)
        self._email_account_service = EmailAccountServiceExt(engine=db_engine)
        self.contact_resolve_service = get_contact_resolve_service(db_engine=db_engine)
        self.global_thread_service = get_global_thread_service_by_db_engine(
            db_engine=db_engine
        )
        self.email_event_service = get_email_event_service_by_db_engine(
            db_engine=db_engine
        )
        self._email_quota_service = QuotaService(db_engine=db_engine)
        self.unsubscription_group_service_ext = (
            get_unsubscription_ext_service_by_db_engine(db_engine)
        )

    async def _validate_send_message_request(
        self,
        organization_id: UUID,
        send_message_request: SendMessageRequest,
    ) -> tuple[EmailAccount, Message | None, Thread | None, list[AttachmentDto]]:
        # 1.1 Validate attachment if given
        attachments = await self._email_attachment_service.find_attachment_by_ids(
            organization_id=organization_id,
            attachment_ids=send_message_request.attachment_ids
            if send_message_request.attachment_ids
            else [],
        )

        # 1.2 Validate reply to message id
        reply_to_db_message = (
            await self.message_repository._find_unique_by_column_values(
                Message,
                exclude_deleted_or_archived=True,
                id=send_message_request.reply_to_message_id,
                organization_id=organization_id,
            )
            if send_message_request.reply_to_message_id
            else None
        )

        # 1.3 validate email_account_id
        if not (
            email_account := await self._email_account_service.get_email_account_by_id(
                email_account_id=not_none(
                    send_message_request.send_from[0].email_account_id
                ),
                organization_id=organization_id,
            )
        ):
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details="Email account not found for the sender.",
                )
            )

        if send_message_request.reply_to_message_id and reply_to_db_message is None:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"Reply to message {send_message_request.reply_to_message_id} not found.",
                )
            )

        # 1.3 Validate if existing thread exists
        existing_db_thread = None
        if reply_to_db_message:
            existing_db_thread = (
                await self.thread_repository._find_unique_by_column_values(
                    Thread,
                    id=reply_to_db_message.thread_id,
                    organization_id=organization_id,
                )
            )

        if reply_to_db_message and not existing_db_thread:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"Thread {reply_to_db_message.thread_id} is missing.",
                )
            )
        return email_account, reply_to_db_message, existing_db_thread, attachments

    async def create_message_and_thread(
        self,
        organization_id: UUID,
        send_message_request: SendMessageRequest,
    ) -> tuple[GlobalEmailDto, Message | None]:
        (
            email_account,
            reply_to_db_message,
            existing_db_thread,
            attachments,
        ) = await self._validate_send_message_request(
            organization_id=organization_id,
            send_message_request=send_message_request,
        )
        send_now = (
            send_message_request.send_at is None
            or send_message_request.send_at <= zoned_utc_now()
        )

        if send_message_request.source == MessageSource.SEQUENCE:
            status = (
                MessageStatus.DRAFT
                if send_message_request.use_draft
                else MessageStatus.SCHEDULED
            )
        elif send_now:
            status = MessageStatus.CREATED
        else:
            status = MessageStatus.SCHEDULED

        message_id = uuid.uuid4()
        message_to_insert = Message(
            id=message_id,
            thread_id=uuid.uuid4(),  # fake id
            email_account_id=email_account.id,
            organization_id=organization_id,
            email_template_id=send_message_request.email_template_id,
            # Must use original message's subject.
            subject=send_message_request.subject
            if not reply_to_db_message
            else reply_to_db_message.subject,
            send_from=send_message_request.send_from,
            send_to=send_message_request.to,
            cc=send_message_request.cc,
            bcc=send_message_request.bcc,
            reply_to=send_message_request.reply_to,
            reply_to_message_id=send_message_request.reply_to_message_id,
            status=status,
            snippet=send_message_request.snippet or "",
            body_text=send_message_request.body_text,
            body_html=_compress_body_html(send_message_request.body_html),
            attachment_ids=send_message_request.attachment_ids,
            folders=None,
            starred=None,
            unread=None,
            use_draft=None,
            send_at=send_message_request.send_at,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            received_at=None,
            deleted_at=None,
            headers=MessageHeaders(
                message_id=str(message_id),
                in_reply_to=None,
                references=None,
            ),
            source=send_message_request.source,
        )

        thread_id = None
        if not existing_db_thread:
            # 2. Create init thread and message
            thread_id = uuid.uuid4()
            # Message created location
            (
                incomplete_email_dto,
                inserted_db_message,
            ) = await self.thread_repository.insert_thread_and_message(
                thread=Thread(
                    id=thread_id,
                    email_account_id=email_account.id,
                    organization_id=organization_id,
                    subject=send_message_request.subject,
                    snippet="",
                    unread=None,
                    starred=None,
                    has_attachments=None,
                    has_drafts=None,
                    folders=None,
                    earliest_message_date=zoned_utc_now(),
                    latest_message_sent_date=None,
                    latest_message_received_date=None,
                    participants=[],
                    tags=send_message_request.tags,
                    created_at=zoned_utc_now(),
                    updated_at=zoned_utc_now(),
                    deleted_at=None,
                ),
                message=message_to_insert.model_copy(update={"thread_id": thread_id}),
            )
        else:
            thread_id = existing_db_thread.id
            # Message created location
            inserted_db_message = await self.message_repository.insert(
                message_to_insert.model_copy(update={"thread_id": thread_id})
            )
            incomplete_email_dto = EmailDto(
                thread=existing_db_thread,
                message_dtos=[MessageDto(message=inserted_db_message, attachments=[])],
            )

        await parse_main_body_text_and_persist(
            message=inserted_db_message,
        )
        await classify_message_and_update_metadata(
            message=inserted_db_message,
        )

        email_dto_without_global_data = incomplete_email_dto.model_copy(
            update={
                "message_dtos": [
                    MessageDto(
                        message=incomplete_email_dto.message_dtos[0].message,
                        attachments=attachments,
                    )
                ]
            }
        )
        (
            global_thread,
            global_message_and_message_mappings,
        ) = await self.global_thread_service.construct_global_thread_and_messages(
            email_dto=email_dto_without_global_data,
            organization_id=organization_id,
            account_ids=send_message_request.account_ids,
            pipeline_id=send_message_request.pipeline_id,
            sequence_id=send_message_request.sequence_id,
        )
        global_email_dto = GlobalEmailDto.from_email_dto(
            email_dto=email_dto_without_global_data,
            global_thread_id=global_thread.id,
            global_message_mapping=global_message_and_message_mappings,
        )
        # embed generated unsubscription link
        if send_message_request.source == MessageSource.SEQUENCE:
            body_html_with_unsubscription_link = (
                await self.unsubscription_group_service_ext.embed_unsubscription_link(
                    organization_id=organization_id,
                    body_html=email_dto_without_global_data.message_dtos[
                        0
                    ].message.body_html,
                    global_message_id=not_none(
                        global_email_dto.get_global_message_id_by_message_id(message_id)
                    ),
                    recipient_email=send_message_request.to[0].email,
                )
            )
            updated_message = not_none(
                await self.message_repository.update_by_tenanted_primary_key(
                    Message,
                    organization_id=organization_id,
                    primary_key_to_value={"id": message_id},
                    column_to_update=MessageUpdate(
                        body_html=body_html_with_unsubscription_link,
                    ),
                )
            )
            global_email_dto = GlobalEmailDto.from_email_dto(
                email_dto=EmailDto(
                    thread=email_dto_without_global_data.thread,
                    message_dtos=[
                        MessageDto(
                            message=updated_message,
                            attachments=email_dto_without_global_data.message_dtos[
                                0
                            ].attachments,
                        )
                    ],
                ),
                global_thread_id=global_thread.id,
                global_message_mapping=global_message_and_message_mappings,
            )

        # Increase quota usage for scheduled email
        if (
            global_email_dto.email_dto.message_dtos[0].message.status
            == MessageStatus.SCHEDULED
        ):
            await self._email_quota_service.increase_usage(
                organization_id=organization_id,
                entity_id=email_account.id,
                entity_type=QuotaConsumerEntityType.EMAIL_ACCOUNT,
                resource=QuotaConsumingResource.EMAIL,
                usage=1,
                timestamp=global_email_dto.email_dto.message_dtos[0].message.send_at
                or zoned_utc_now(),
            )

        return global_email_dto, reply_to_db_message

    async def get_global_email_dto_from_draft_or_scheduled_message(
        self,
        organization_id: UUID,
        send_message_request: SendMessageRequest,
    ) -> tuple[GlobalEmailDto, Message | None]:
        # Step 1: Get the email DTO
        global_message_associations = await self.thread_repository.find_message_associations_by_global_message_ids(
            global_message_ids=[not_none(send_message_request.global_message_id)],
            organization_id=organization_id,
        )
        if len(global_message_associations) != 1:
            raise ResourceNotFoundError(
                f"Expected exactly one global message association for draft message {send_message_request.global_message_id}, got {len(global_message_associations)}"
            )
        message = await self.get_message_by_id(
            organization_id=organization_id,
            message_id=global_message_associations[0].message_id,
        )
        if message.status not in [MessageStatus.DRAFT, MessageStatus.SCHEDULED]:
            raise IllegalStateError(
                f"Expected message {message.id} to be in DRAFT/SCHEDULED state, got {message.status}"
            )
        (
            email_account,
            reply_to_db_message,
            existing_db_thread,
            attachments,
        ) = await self._validate_send_message_request(
            organization_id=organization_id,
            send_message_request=send_message_request,
        )
        # update draft message from request
        message = not_none(
            await self.message_repository.update_by_tenanted_primary_key(
                Message,
                organization_id=organization_id,
                primary_key_to_value={"id": message.id},
                column_to_update=MessageUpdate(
                    email_account_id=email_account.id,
                    subject=send_message_request.subject,
                    send_from=send_message_request.send_from,
                    send_to=send_message_request.to,
                    cc=send_message_request.cc,
                    bcc=send_message_request.bcc,
                    reply_to=reply_to_db_message.reply_to
                    if reply_to_db_message
                    else None,
                    body_text=send_message_request.body_text,
                    body_html=send_message_request.body_html,
                    attachment_ids=[a.attachment.id for a in attachments],
                    send_at=None,  # update to None to send immediately
                    status=MessageStatus.SCHEDULED,  # update to SCHEDULED to send immediately
                    updated_at=zoned_utc_now(),
                ),
            )
        )

        # Step 2: Get the global email DTO
        return await self._message_service_ext.get_email_dto_for_a_message(
            organization_id=organization_id,
            db_message=message,
        ), reply_to_db_message

    async def send_message(
        self,
        organization_id: UUID,
        send_message_request: SendMessageRequest,
    ) -> GlobalEmailDto:
        # Step 1: Create message and thread
        send_message_request = (
            await self.resolve_and_fill_fields_for_send_message_request(
                organization_id=organization_id,
                send_message_request=send_message_request,
            )
        )
        if send_message_request.global_message_id:
            (
                global_email_dto,
                reply_to_db_message,
            ) = await self.get_global_email_dto_from_draft_or_scheduled_message(
                organization_id=organization_id,
                send_message_request=send_message_request,
            )
        else:
            (
                global_email_dto,
                reply_to_db_message,
            ) = await self.create_message_and_thread(
                organization_id=organization_id,
                send_message_request=send_message_request,
            )

        # Step 2: Send message
        send_now = (
            send_message_request.send_at is None
            or send_message_request.send_at <= zoned_utc_now()
        )
        if send_now:
            global_email_dto = await self.send_email_dto(
                organization_id=organization_id,
                global_email_dto=global_email_dto,
                reply_to_db_message=reply_to_db_message,
            )
            global_thread_id = global_email_dto.global_thread_id

            # Step 3: Persist activities
            await self._email_activity_service.create_activities_for_global_messages(
                global_thread_id=global_thread_id,
                global_message_and_message_mappings=global_email_dto.new_global_message_mappings,
            )

            # Step 4: Close existing tasks
            ff_service = get_feature_flag_service()
            enable_auto_close = await ff_service.is_enabled(
                request=FeatureFlagRequest(
                    flag_key="task-auto-completion-vfeb2025",
                    organization_id=organization_id,
                )
            )
            if enable_auto_close:
                await start_close_existing_tasks(
                    organization_id=organization_id,
                    object_id=global_thread_id,
                    object_type=IntelTriggerObjectType.GLOBAL_THREAD,
                    account_ids=send_message_request.account_ids
                    if send_message_request.account_ids is not None
                    and len(send_message_request.account_ids) > 0
                    else None,
                    pipeline_id=send_message_request.pipeline_id,
                )
            # Step 5: trigger insight generation
            if send_message_request.source != MessageSource.SEQUENCE:
                client = await get_temporal_client()
                await client.start_workflow(
                    EmailInsightWorkflow.run,
                    args=[[global_thread_id], organization_id],
                    id=f"generate_email_insight::{global_thread_id}",
                    task_queue=TemporalTaskQueue.EMAIL_TASK_QUEUE,
                    id_reuse_policy=WorkflowIDReusePolicy.TERMINATE_IF_RUNNING,
                    execution_timeout=timedelta(minutes=10),
                )

        return global_email_dto

    async def resolve_and_fill_fields_for_participant_inplace(
        self, organization_id: UUID, participants: list[EmailHydratedParticipant]
    ) -> list[EmailHydratedParticipant]:
        to_resolve_participants: list[EmailHydratedParticipant] = []
        need_resolve_contact_and_emails: list[
            tuple[UUID | None, EmailStrLower | None]
        ] = []
        need_resolve_emails: set[EmailStrLower] = set()
        for participant in participants:
            if participant.email_account_id or (
                participant.contact_id and participant.account_id
            ):
                continue
            to_resolve_participants.append(participant)
            if participant.contact_id:
                need_resolve_contact_and_emails.append(
                    (participant.contact_id, participant.email)
                )
            else:
                need_resolve_emails.add(participant.email)

        contact_account_map = await self.contact_resolve_service.resolve_relevant_account_by_contact_and_email_pairs(
            organization_id=organization_id,
            contact_email_pairs=need_resolve_contact_and_emails,
        )
        email_res_map = await self.contact_resolve_service.batch_resolve_relevant_contact_info_by_email(
            organization_id=organization_id,
            emails=list(need_resolve_emails),
        )
        for participant in to_resolve_participants:
            if participant.contact_id:
                participant.account_id = contact_account_map.get(participant.contact_id)
            else:
                if not (email_res := email_res_map.get(participant.email)):
                    continue
                participant.contact_id = email_res.contact_id
                if not participant.account_id:
                    participant.account_id = email_res.account_id
        return participants

    async def resolve_and_fill_fields_for_send_message_request(
        self, organization_id: UUID, send_message_request: SendMessageRequest
    ) -> SendMessageRequest:
        updated_send_message_request = strict_model_copy(send_message_request)
        participants = list(
            updated_send_message_request.send_from
            + updated_send_message_request.to
            + (updated_send_message_request.cc or [])
            + (updated_send_message_request.bcc or [])
            + (updated_send_message_request.reply_to or [])
        )
        await self.resolve_and_fill_fields_for_participant_inplace(
            organization_id=organization_id,
            participants=participants,
        )
        return updated_send_message_request

    async def send_email_dto(
        self,
        organization_id: UUID,
        global_email_dto: GlobalEmailDto,
        reply_to_db_message: Message | None,
    ) -> GlobalEmailDto:
        try:
            sent_email_dto = (
                await self._email_sending_service.send_messages_to_provider(
                    organization_id=organization_id,
                    global_email_dto=global_email_dto,
                    reply_to_provider_message_id=reply_to_db_message.provider_id
                    if reply_to_db_message
                    else None,
                )
            )
            # If message sent successfully, process the email event
            message_id = sent_email_dto.message_dtos[0].message.id
            global_message_id = sent_email_dto.get_global_message_id_by_message_id(
                message_id
            )
            provider_id = sent_email_dto.message_dtos[0].message.provider_id
            logger.bind(
                global_message_id=global_message_id,
                global_thread_id=global_email_dto.global_thread_id,
                external_event_id=provider_id,
            ).info("[send_email_dto] processing email event")
            await self.email_event_service.process_global_email_event(
                email_account_id=global_email_dto.thread.email_account_id,
                organization_id=organization_id,
                event_type=EmailEventType.SEND_ATTEMPTED,
                event_time=zoned_utc_now(),
                global_message_id=not_none(global_message_id),
                global_thread_id=global_email_dto.global_thread_id,
                external_event_id=not_none(provider_id),
                event_details=None,
            )
            return sent_email_dto
        except Exception as e:
            logger.bind(
                global_email_dto=global_email_dto,
            ).error("[send_email_dto] sending email failed", exc_info=e)
            message_status = global_email_dto.message_dtos[0].message.status
            # Only delete the message if it was created/scheduled and failed to send
            if message_status in [MessageStatus.CREATED, MessageStatus.SCHEDULED]:
                (
                    global_message_id,
                    global_thread_id,
                ) = not_none(
                    await self.thread_repository.delete_failed_message_and_global_message(
                        organization_id=organization_id,
                        global_email_dto=global_email_dto,
                        is_new_thread=reply_to_db_message is None,
                    )
                )
                # If message sent failed, process the email event
                external_event_id = str(global_email_dto.message_dtos[0].message.id)
                await self.email_event_service.process_global_email_event(
                    email_account_id=global_email_dto.thread.email_account_id,
                    organization_id=organization_id,
                    event_type=EmailEventType.SEND_ATTEMPTED_FAILED,
                    event_time=zoned_utc_now(),
                    global_message_id=global_message_id,
                    global_thread_id=global_thread_id,
                    external_event_id=external_event_id,
                    event_details=None,
                )

            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.MESSAGE_SEND_FAILURE,
                    details="Failed to send email, please try again later.",
                )
            ) from e

    async def get_message_by_id(
        self,
        organization_id: UUID,
        message_id: UUID,
    ) -> Message:
        db_message = await self.message_repository._find_unique_by_column_values(
            Message,
            exclude_deleted_or_archived=True,
            id=message_id,
            organization_id=organization_id,
        )

        if not db_message:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"Message {message_id} not found.",
                )
            )

        return db_message

    async def send_scheduled_message_by_global_message_id(
        self,
        organization_id: UUID,
        global_message_id: UUID,
    ) -> GlobalEmailDto:
        # Step 1: Get the email DTO
        global_message_associations = await self.thread_repository.find_message_associations_by_global_message_ids(
            global_message_ids=[global_message_id],
            organization_id=organization_id,
        )
        if len(global_message_associations) != 1:
            raise IllegalStateError(
                f"Expected exactly one global message association for scheduled message {global_message_id}, got {len(global_message_associations)}"
            )
        message = await self.get_message_by_id(
            organization_id=organization_id,
            message_id=global_message_associations[0].message_id,
        )
        if message.status != MessageStatus.SCHEDULED:
            raise IllegalStateError(
                f"Expected message {message.id} to be in SCHEDULED state, got {message.status}"
            )
        global_email_dto = await self._message_service_ext.get_email_dto_for_a_message(
            organization_id=organization_id,
            db_message=message,
        )

        # Step 2: Get reply_to_message if needed
        reply_to_db_message = None
        if message.reply_to_message_id:
            reply_to_db_message = (
                await self.message_repository.find_by_tenanted_primary_key(
                    Message,
                    exclude_deleted_or_archived=True,
                    id=message.reply_to_message_id,
                    organization_id=organization_id,
                )
            )

        # Step 3: Send the message
        global_email_dto = await self.send_email_dto(
            organization_id=organization_id,
            global_email_dto=global_email_dto,
            reply_to_db_message=reply_to_db_message,
        )

        # Step 4: Persist activities
        await self._email_activity_service.create_activities_for_global_messages(
            global_thread_id=global_email_dto.global_thread_id,
            global_message_and_message_mappings=global_email_dto.new_global_message_mappings,
        )
        return global_email_dto

    async def cancel_scheduled_message_by_global_message_id(
        self,
        organization_id: UUID,
        global_message_id: UUID,
    ) -> GlobalEmailDto:
        global_message_associations = await self.thread_repository.find_message_associations_by_global_message_ids(
            global_message_ids=[global_message_id],
            organization_id=organization_id,
        )
        if len(global_message_associations) != 1:
            raise ResourceNotFoundError(
                f"Global message {global_message_id} not found or has been deleted."
            )

        message = await self.get_message_by_id(
            organization_id=organization_id,
            message_id=global_message_associations[0].message_id,
        )

        if message.status == MessageStatus.CANCELLED:
            return await self._message_service_ext.get_email_dto_for_a_message(
                organization_id=organization_id,
                db_message=message,
            )

        if message.status not in [MessageStatus.SCHEDULED, MessageStatus.DRAFT]:
            raise IllegalStateError(
                f"Expected message {message.id} to be in SCHEDULED/CANCELLED state, got {message.status}"
            )
        global_email_dto, _ = await self.cancel_scheduled_message_by_message(
            organization_id=organization_id,
            message=message,
        )
        return not_none(global_email_dto)

    async def cancel_scheduled_message_by_message(
        self,
        organization_id: UUID,
        message: Message,
    ) -> tuple[GlobalEmailDto | None, MessageUpdateResult]:
        cancelled_message = (
            await self.message_repository.update_by_tenanted_primary_key(
                Message,
                organization_id=organization_id,
                primary_key_to_value={
                    "id": message.id,
                },
                column_to_update={
                    "status": MessageStatus.CANCELLED,
                    "updated_at": zoned_utc_now(),
                },
            )
        )

        if not cancelled_message:
            return None, MessageUpdateResult(
                status=EmailOperationStatus.ERROR,
                message_id=message.id,
                error_code=EmailSendingErrorCode.MESSAGE_CANCEL_ERROR,
                error_detail=f"Failed to cancel message {message.id}.",
            )

        await self._email_activity_service.create_activity_for_cancelled_messages(
            message=cancelled_message,
        )

        cancelled_email_dto = (
            await self._message_service_ext.get_email_dto_for_a_message(
                organization_id=organization_id,
                db_message=cancelled_message,
            )
        )

        # Cancel related associations
        try:
            await self._cancel_message_associations(
                organization_id=organization_id,
                message=cancelled_message,
            )
        except Exception:
            return cancelled_email_dto, MessageUpdateResult(
                status=EmailOperationStatus.ERROR,
                message_id=cancelled_message.id,
                error_code=EmailSendingErrorCode.MESSAGE_CANCEL_ERROR,
                error_detail=f"Failed to cancel message associations for message {cancelled_message.id}.",
            )

        # create a new global message mapping with the cancelled message because
        # new_global_message_mappings only contains the messages that were created
        global_message_mappings = {
            global_message_id: message
            for global_message_id, (
                message,
                _,
            ) in cancelled_email_dto.global_message_mapping.items()
            if message.id == cancelled_message.id
        }

        await self._email_activity_service.create_activities_for_global_messages(
            global_thread_id=not_none(cancelled_email_dto.global_thread_id),
            global_message_and_message_mappings=global_message_mappings,
        )

        await self._email_quota_service.decrease_usage(
            organization_id=organization_id,
            entity_id=cancelled_message.email_account_id,
            entity_type=QuotaConsumerEntityType.EMAIL_ACCOUNT,
            resource=QuotaConsumingResource.EMAIL,
            usage=1,
            timestamp=cancelled_message.send_at
            or zoned_utc_now(),  # For backward compatibility
        )

        return cancelled_email_dto, MessageUpdateResult(
            message_id=cancelled_message.id,
            message=cancelled_message,
            status=EmailOperationStatus.SUCCESS,
        )

    async def _cancel_message_associations(
        self,
        organization_id: UUID,
        message: Message,
    ) -> None:
        """Cancel all associations related to a message.

        This includes:
        - The global message association
        - The global message
        - The global thread messages association
        - The thread and global thread (if not a reply)

        Args:
            organization_id: The organization ID
            message: The message to cancel associations for
        """
        # 1. Cancel the global message association
        global_message_association = not_none(
            await self.thread_repository._update_unique_by_column_values(
                GlobalMessageAssociation,
                column_value_to_query={
                    "message_id": message.id,
                    "organization_id": organization_id,
                },
                column_to_update=GlobalMessageAssociationUpdate(
                    cancelled_at=zoned_utc_now(),
                ),
            )
        )

        # 2. Cancel the global message
        cancelled_global_message = not_none(
            await self.thread_repository.update_by_tenanted_primary_key(
                GlobalMessage,
                organization_id=organization_id,
                primary_key_to_value={
                    "id": global_message_association.global_message_id
                },
                column_to_update=GlobalMessageUpdate(
                    cancelled_at=zoned_utc_now(),
                ),
                exclude_deleted_or_archived=True,
            )
        )

        # 3. Cancel the global thread messages association
        await self.thread_repository._update_unique_by_column_values(
            GlobalThreadMessagesAssociation,
            column_value_to_query={
                "original_message_id": str(message.id),
                "organization_id": organization_id,
            },
            column_to_update=GlobalThreadMessagesAssociationUpdate(
                cancelled_at=zoned_utc_now(),
            ),
        )

        # 4. Cancel the thread and global thread if it's not a reply
        if not message.reply_to_message_id:
            await self.thread_repository.update_by_tenanted_primary_key(
                Thread,
                organization_id=organization_id,
                primary_key_to_value={"id": message.thread_id},
                column_to_update=ThreadUpdate(
                    cancelled_at=zoned_utc_now(),
                    updated_at=zoned_utc_now(),
                ),
            )
            await self.thread_repository.update_by_tenanted_primary_key(
                GlobalThread,
                organization_id=organization_id,
                primary_key_to_value={"id": cancelled_global_message.global_thread_id},
                column_to_update=GlobalThreadUpdate(
                    cancelled_at=zoned_utc_now(),
                ),
            )

    async def cancel_email_sending(
        self, email_sending_cancel_request: EmailSendingCancelRequest
    ) -> EmailSendingResponse:
        response: EmailSendingResponse = EmailSendingResponse(
            data=[],
        )
        for message in email_sending_cancel_request.scheduled_messages:
            _, cancellation_result = await self.cancel_scheduled_message_by_message(
                organization_id=email_sending_cancel_request.organization_id,
                message=message,
            )
            if cancellation_result.status == EmailOperationStatus.ERROR:
                response.data.append(
                    EmailSendingMessageLevelError.from_message_update_result(
                        message_update_error=cancellation_result,
                    )
                )
            else:
                response.data.append(
                    MessageOperationSuccessData.from_message_update_result(
                        message_update_result=cancellation_result
                    )
                )

        return response


class SingletonMessageService(Singleton, MessageService):
    pass


def get_message_service(
    request: Request,
) -> MessageService:
    db_engine = get_db_engine(request=request)
    return SingletonMessageService(
        db_engine=db_engine,
    )


def get_message_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> MessageService:
    return SingletonMessageService(
        db_engine=db_engine,
    )
