import email
import imaplib
import re
from dataclasses import dataclass
from datetime import timed<PERSON><PERSON>
from email.header import decode_header
from email.message import Message as EmailMessage
from email.utils import getaddresses, parsedate_to_datetime
from enum import StrEnum
from typing import Annotated, Any, Final
from uuid import UUID, uuid4

import pytz
from pydantic import BaseModel, EmailStr, StringConstraints

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception.exception import (
    ErrorDetails,
    ResourceNotFoundError,
)
from salestech_be.common.singleton import Singleton
from salestech_be.core.ai.email.activities.classify_message_and_update_metadata import (
    classify_message_and_update_metadata,
)
from salestech_be.core.ai.email.activities.parse_main_body_text_and_persist import (
    parse_main_body_text_and_persist,
)
from salestech_be.core.contact.service.contact_resolve_service import (
    get_contact_resolve_service,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.attachment.email_attachment_service import (
    get_email_attachment_service_by_db_engine,
)
from salestech_be.core.email.event.email_event_service import EmailEventService
from salestech_be.core.email.global_email.global_thread_service import (
    get_global_thread_service_by_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.core.email.parser.email_reply_parser import EmailReplyParser
from salestech_be.core.email.participant.email_participant_service import (
    EmailParticipantService,
)
from salestech_be.core.email.service.email_utils import generate_email_hash
from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.email_dto import (
    EmailDto,
    GlobalEmailDto,
    MessageDto,
    ShortDBMessage,
    ShortDBThread,
)
from salestech_be.db.models.attachment import Attachment
from salestech_be.db.models.email_account import EmailAccount
from salestech_be.db.models.message import (
    EmailProvider,
    Message,
    MessageSource,
    MessageUpdate,
)
from salestech_be.db.models.sequence import EmailEventType
from salestech_be.db.models.thread import Thread, ThreadUpdate
from salestech_be.integrations.nylas.model import MessageHeaders
from salestech_be.integrations.s3.s3_bucket_manager import (
    get_s3_bucket_manager_by_bucket_name,
)
from salestech_be.ree_logging import get_logger, log_context
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.str import MessageId
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger()

ImapUid = Annotated[str, StringConstraints(min_length=1)]


class MessageIdWrapper(BaseModel):
    message_id: MessageId


class ImapAttachment(BaseModel):
    filename: str
    content_type: str
    payload: bytes
    is_inline: bool
    content_id: str | None


class DsnStatus(StrEnum):
    UNDELIVERED = "UNDELIVERED"


@dataclass
class DBMessage:
    id: UUID
    provider_id: str
    thread_id: UUID


class ImapMessage:
    # These were generated before we started validating message ids
    KNOWN_INVALID_MESSAGE_IDS: Final[set[str]] = {
        "067058a30799479d8a1e2bf5fd813732",
        "7db2e723d03e4d1e9a3532907e39c43c",
        "e6c8332c7fec4ae98975d04c19e5228f",
        "e3e0d137ea34464da05bd767da173525",
        "ecc-671bff00-1f-27ff4b80@92914706",
    }

    def __init__(
        self,
        imap_uid: ImapUid,
        email_message: EmailMessage,
        is_unread: bool,
        folder: str,
        internal_date: ZoneRequiredDateTime,
        preview: str,
        thread_imap_uid: ImapUid,
        parent_imap_uid: ImapUid | None,
        visible_text: str | None,
        html_body: str | None,
    ):
        self.imap_uid = imap_uid
        self.email_message = email_message
        self.is_unread = is_unread
        self.folders = {folder}
        self.internal_date = internal_date
        self.thread_imap_uid = thread_imap_uid
        self.parent_imap_uid = parent_imap_uid
        self.preview = preview
        self.visible_text = visible_text
        self.send_from_email_addresses = self.get_header_email_addresses("From")
        self.send_to_email_addresses = self.get_header_email_addresses("To")
        self.cc_email_addresses = self.get_header_email_addresses("Cc")
        self.bcc_email_addresses = self.get_header_email_addresses("Bcc")
        self.dsn_status = self.get_dsn_status()
        self.original_message_id = self.get_original_message_id()
        self.html_body = html_body

    def get_header_email_addresses(self, header: str) -> list[EmailStr]:
        header_value = self.email_message.get(header) or self.email_message.get(
            header.lower()
        )
        if not header_value:
            return []

        return [t[1] for t in getaddresses([header_value]) if t[1] and t[1] != "None"]

    def get_unique_email_addresses(self) -> list[EmailStr]:
        return list(
            set(
                self.send_from_email_addresses
                + self.send_to_email_addresses
                + self.cc_email_addresses
                + self.bcc_email_addresses
            )
        )

    def get_message_date(self) -> ZoneRequiredDateTime:
        for header in ["Date", "Received", "X-Date", "Resent-Date"]:
            date_str = self.email_message.get(header)
            if date_str:
                try:
                    if header == "Received":
                        # The date is typically at the end of the 'Received' header
                        date_str = date_str.split(";")[-1].strip()
                    parsed_datetime = parsedate_to_datetime(date_str)
                    if parsed_datetime.tzinfo is None:
                        # If no timezone info, assume UTC
                        parsed_datetime = parsed_datetime.replace(tzinfo=pytz.UTC)
                    return parsed_datetime
                except (TypeError, ValueError, IndexError):
                    logger.bind(header=header, date_str=date_str).warning(
                        "[imap] failed to parse date"
                    )

        logger.warning("[imap] no date found, falling back to imap internaldate")
        return self.internal_date

    async def get_thread_id(
        self,
        messages: dict[MessageId, "CombinedMessage"],
        threads_by_id: dict[UUID, ShortDBThread],
        imap_uid_to_message_id: dict[ImapUid, MessageId],
        thread_repository: ThreadRepository,
        threads_by_provider_id: dict[MessageId, ShortDBThread],
    ) -> MessageId:
        # Regular message
        thread_message_id = imap_uid_to_message_id[self.thread_imap_uid]
        if thread_message_id in messages:
            message = messages[thread_message_id].message
            if isinstance(message, Message):
                thread = threads_by_id[message.thread_id]
                if thread.provider_id:
                    return thread.provider_id
                else:
                    await thread_repository.update_by_primary_key(
                        Thread,
                        primary_key_to_value={"id": thread.id},
                        column_to_update={
                            "provider": EmailProvider.INFRAFORGE,
                            "provider_id": thread_message_id,
                            "updated_at": zoned_utc_now(),
                        },
                    )
                    threads_by_provider_id[thread_message_id] = thread
            # For bounced message, we use the original message id as the thread id
            # There could be cases that the original-message-id is invalid and can't
            # be parsed. In this case we return the original thread_message_id
            elif isinstance(message, ImapMessage):
                if (
                    message.dsn_status
                    and message.dsn_status == DsnStatus.UNDELIVERED
                    and message.original_message_id
                ):
                    thread_message_id = message.original_message_id

        return thread_message_id

    def get_message_id(self) -> MessageId:
        message_id = self.email_message.get("message-id")
        if message_id:
            result = message_id.strip()
        else:
            if self.send_from_email_addresses:
                primary_sender = self.send_from_email_addresses[0]
                domain = primary_sender.split("@")[-1]
            else:
                domain = "reevo.ai"
            message_hash = generate_email_hash(self.email_message)
            result = f"<{message_hash}@{domain}>"
        try:
            return MessageIdWrapper(message_id=result).message_id
        except ValueError:
            if message_id not in self.KNOWN_INVALID_MESSAGE_IDS:
                logger.bind(
                    message_id=message_id, result=result, imap_uid=self.imap_uid
                ).error("[imap] invalid message_id")
            return result

    """
    Sample DSN (Delivery Status Notification) Message structure:

    Human Readable Part:
        [('Content-Description', 'Notification'),
        ('Content-Type', 'text/plain; charset=utf-8'),
        ('Content-Transfer-Encoding', '8bit')]

    DSN Report Part:
        [('Content-Description', 'Delivery report'),
        ('Content-Type', 'message/delivery-status')]

    Undelivered Message Part - can be in one of two formats:
    1. Full message format:
        [('Content-Description', 'Undelivered Message'),
        ('Content-Type', 'message/rfc822'),
        ('Content-Transfer-Encoding', '8bit')]
    2. Headers-only format:
        [('Content-Description', 'Undelivered Message Headers'),
        ('Content-Type', 'text/rfc822-headers')]
    """

    def get_dsn_status(self) -> DsnStatus | None:
        if self.email_message.is_multipart():
            for part in self.email_message.walk():
                content_type = part.get_content_type()
                content_description = part.get("Content-Description")
                if (
                    # Standard DSN format containing the full undelivered message
                    # This is the most common format used by many mail servers
                    content_type == "message/rfc822"
                    and content_description == "Undelivered Message"
                ) or (
                    # Alternative DSN format containing only the headers of the undelivered message
                    # This format is commonly used by Microsoft Exchange and some other mail systems
                    # to reduce the size of bounce notifications
                    content_type == "text/rfc822-headers"
                    and content_description == "Undelivered Message Headers"
                ):
                    return DsnStatus.UNDELIVERED

            # check for standard bounce mail headers
            # Use getaddresses to properly parse the From header
            from_addresses = getaddresses([self.email_message.get("From", "")])
            if from_addresses and len(from_addresses) > 0:
                # format: [(display_name, email_address)]
                _, email_address = from_addresses[0]
                if email_address:
                    email_address = email_address.lower()
                    if "@" in email_address:
                        local_part, _ = email_address.split("@", 1)

                        # check if the local part of the email address is a common system account
                        system_accounts = {"postmaster", "mailer-daemon"}

                        if local_part in system_accounts:
                            return DsnStatus.UNDELIVERED

            # check for Exchange NDR header
            if "X-MS-Exchange-Message-Is-Ndr" in self.email_message:
                return DsnStatus.UNDELIVERED

        return None

    def get_original_message_id(self) -> MessageId | None:  # noqa: C901 PLR0911 PLR0912
        if self.email_message.is_multipart():
            # Standard format: extract from nested EmailMessage in message/rfc822
            for part in self.email_message.walk():
                content_type = part.get_content_type()
                content_description = part.get("Content-Description")

                # Handle message/rfc822 format
                if (
                    content_type == "message/rfc822"
                    and content_description == "Undelivered Message"
                ):
                    payload = part.get_payload()
                    if isinstance(payload, list | EmailMessage):
                        payload = payload[0] if isinstance(payload, list) else payload
                        if isinstance(payload, EmailMessage):
                            message_id = payload.get("Message-ID")
                            if not message_id:
                                logger.bind(
                                    payload=payload,
                                ).warning(
                                    "[imap] no message-id found in undelivered message"
                                )
                                return None
                        else:
                            return None
                    else:
                        return None
                    stripped_message_id = message_id.strip()
                    try:
                        return MessageIdWrapper(
                            message_id=stripped_message_id
                        ).message_id
                    except ValueError:
                        if stripped_message_id not in self.KNOWN_INVALID_MESSAGE_IDS:
                            logger.bind(
                                message_id=message_id,
                                result=message_id,
                                imap_uid=self.imap_uid,
                            ).error("[imap] invalid Original Message ID")
                        return None

                # Handle text/rfc822-headers format
                if (
                    content_type == "text/rfc822-headers"
                    and content_description == "Undelivered Message Headers"
                ):
                    # Extract message ID from header text
                    payload = part.get_payload(decode=True)
                    if isinstance(payload, bytes):
                        header_text = payload.decode(errors="ignore")

                        # Handle multi-line Message-ID headers with folding whitespace (RFC 2822)
                        # First join any folded header lines
                        lines = header_text.splitlines()
                        joined_lines = []
                        current_line = ""

                        for line in lines:
                            # If line starts with whitespace, it's a continuation of the previous header
                            if line and (line[0].isspace() or not current_line):
                                current_line = (current_line + line).strip()
                            else:
                                if current_line:
                                    joined_lines.append(current_line)
                                current_line = line.strip()

                        # Don't forget the last line
                        if current_line:
                            joined_lines.append(current_line)

                        # Now search for Message-ID in the properly joined headers
                        for line in joined_lines:
                            if line.startswith(("Message-ID:", "Message-Id:")):
                                message_id = line.split(":", 1)[1].strip()
                                if message_id:  # Make sure we actually got a value
                                    try:
                                        return MessageIdWrapper(
                                            message_id=message_id
                                        ).message_id
                                    except ValueError:
                                        if (
                                            message_id
                                            not in self.KNOWN_INVALID_MESSAGE_IDS
                                        ):
                                            logger.bind(
                                                message_id=message_id,
                                                imap_uid=self.imap_uid,
                                            ).error(
                                                "[imap] invalid Original Message ID from headers"
                                            )
                                        return None

                        logger.bind(
                            header_text=header_text,
                            joined_lines=joined_lines,
                        ).warning(
                            "[imap] no message-id found in undelivered message headers"
                        )

            # fallback to extract from headers
            if (
                self.dsn_status == DsnStatus.UNDELIVERED
                and "In-Reply-To" in self.email_message
            ):
                in_reply_to_raw = self.email_message["In-Reply-To"]
                if in_reply_to_raw:
                    in_reply_to = in_reply_to_raw.strip()
                    try:
                        return MessageIdWrapper(message_id=in_reply_to).message_id
                    except ValueError:
                        logger.bind(
                            in_reply_to=in_reply_to, imap_uid=self.imap_uid
                        ).warning("[imap] invalid Original Message ID from headers")
        return None


class CombinedMessage:
    def __init__(self, message: ShortDBMessage | ImapMessage):
        self.message = message


class EmailBody:
    ATTACHMENT_CONTENT_DISPOSITIONS: Final[set[str]] = {"attachment", "inline"}

    def __init__(self, email_message: EmailMessage):
        body_text, body_html = self._get_email_body(email_message)
        self.body_text = body_text
        self.body_html = body_html
        self.attachments = self._get_email_attachments(email_message)

    def get_body_text(self) -> str:
        return self.body_text

    def get_body_html(self) -> str:
        return self.body_html

    @classmethod
    def _get_email_attachments(
        cls, email_message: EmailMessage
    ) -> list[ImapAttachment]:
        attachments = []
        for part in email_message.walk():
            content_disposition = part.get_content_disposition()
            if content_disposition in cls.ATTACHMENT_CONTENT_DISPOSITIONS:
                payload = part.get_payload(decode=True)
                filename = cls._get_filename(part=part)
                if filename and isinstance(payload, bytes):
                    content_id = cls._get_content_id(part)
                    attachments.append(
                        ImapAttachment(
                            filename=filename,
                            content_type=part.get_content_type(),
                            payload=payload,
                            is_inline=content_disposition == "inline",
                            content_id=content_id,
                        )
                    )
        return attachments

    @staticmethod
    def _get_content_id(part: EmailMessage) -> str | None:
        raw_content_id = part.get("content-id")
        if raw_content_id:
            return raw_content_id.strip("<>")
        else:
            return None

    @staticmethod
    @log_context(from_kwargs=["part"])
    def _get_filename(part: EmailMessage) -> str | None:
        filename = part.get_filename()
        if not filename:
            logger.warning("[imap] no filename found for attachment")
            return None

        decoded_parts = decode_header(filename)
        if not decoded_parts:
            logger.warning("[imap] no filename found for attachment")
            return None

        f, encoding = decoded_parts[0]
        if encoding:
            filename = f.decode(encoding)
        return filename

    @classmethod
    def _get_email_body(cls, email_message: EmailMessage) -> tuple[str, str]:
        if email_message.is_multipart():
            return cls._get_multipart_body(email_message)
        else:
            return cls._get_single_part_body(email_message)

    @classmethod
    def _get_multipart_body(cls, email_message: EmailMessage) -> tuple[str, str]:
        body_text = ""
        body_html = ""
        is_bounce_dsn_message = False
        for part in email_message.walk():
            content_type = part.get_content_type()
            content_description = part.get("Content-Description")
            if (
                content_type == "message/rfc822"
                and content_description == "Undelivered Message"
            ) or (
                content_type == "text/rfc822-headers"
                and content_description == "Undelivered Message Headers"
            ):
                is_bounce_dsn_message = True
                break

        # This parsing logic might not be right for messages with multiple parts as
        # it takes the last part as the body
        for part in email_message.walk():
            if "attachment" in str(part.get("Content-Disposition", "")):
                continue
            content_type = part.get_content_type()
            if content_type == "text/plain":
                body_text = cls._decode_payload(part.get_payload(decode=True))
                # If bounced messages, return the first part
                if is_bounce_dsn_message:
                    body_html = body_text
                return body_text, body_html
            elif content_type == "text/html":
                body_html = cls._decode_payload(part.get_payload(decode=True))
        return body_text, body_html

    @classmethod
    def _get_single_part_body(cls, email_message: EmailMessage) -> tuple[str, str]:
        content_type = email_message.get_content_type()
        payload = cls._decode_payload(email_message.get_payload(decode=True))
        if content_type == "text/plain":
            return payload, ""
        elif content_type == "text/html":
            return "", payload
        else:
            return "", ""

    @staticmethod
    def _decode_payload(payload: Any) -> str:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if isinstance(payload, bytes):
            return payload.decode(errors="ignore")
        elif isinstance(payload, str):
            return payload
        else:
            return ""


class MessageNode:
    def __init__(self, imap_uid: ImapUid):
        self.imap_uid = imap_uid
        self.children: list[MessageNode] = []

    def add_child(self, child: "MessageNode") -> None:
        self.children.append(child)

    def __repr__(self) -> str:
        return f"MessageNode(id={self.imap_uid}, children={self.children})"


class ImapSyncingService:
    def __init__(
        self,
        db_engine: DatabaseEngine,
    ):
        self.message_repository = MessageRepository(engine=db_engine)
        self.thread_repository = ThreadRepository(engine=db_engine)
        self.email_participant_service = EmailParticipantService(db_engine=db_engine)
        self.email_event_service = EmailEventService(db_engine=db_engine)
        self.s3_bucket_manager = get_s3_bucket_manager_by_bucket_name(
            settings.email_attachment_bucket_name
        )
        self.email_account_service_ext = EmailAccountServiceExt(engine=db_engine)
        self.email_attachment_service = get_email_attachment_service_by_db_engine(
            db_engine=db_engine
        )
        self.global_thread_service = get_global_thread_service_by_db_engine(
            db_engine=db_engine
        )
        self.email_reply_parser = EmailReplyParser()
        self.contact_resolve_service = get_contact_resolve_service(db_engine=db_engine)

    def _search_criteria(self, email_account: EmailAccount) -> str:
        if email_account.imap_last_synced_at:
            # We are relying on IMAP for threading entirely, so we always sync the last 14d
            # Note that this means any replies that exceed this time window will not be threaded
            since_date = email_account.imap_last_synced_at - timedelta(days=14)
            date_string = since_date.strftime("%d-%b-%Y")
            logger.bind(date_string=date_string).info("[imap] syncing emails since")
            return f'(SINCE "{date_string}")'

        logger.info("[imap] syncing all emails")
        return "ALL"

    async def _update_imap_last_synced_at(self, email_account_id: UUID) -> None:
        await self.message_repository.update_by_primary_key(
            EmailAccount,
            primary_key_to_value={"id": email_account_id},
            column_to_update={
                "imap_last_synced_at": zoned_utc_now(),
                "updated_at": zoned_utc_now(),
            },
        )

    async def _build_message_threads(
        self,
        imap_messages: dict[MessageId, ImapMessage],
        combined_messages: dict[MessageId, CombinedMessage],
        threads_by_id: dict[UUID, ShortDBThread],
        imap_uid_to_message_id: dict[ImapUid, MessageId],
        threads_by_provider_id: dict[MessageId, ShortDBThread],
    ) -> dict[MessageId, list[MessageId]]:
        threads: dict[MessageId, list[MessageId]] = {}
        for imap_message_id, imap_message in imap_messages.items():
            thread_id = await imap_message.get_thread_id(
                combined_messages,
                threads_by_id,
                imap_uid_to_message_id,
                self.thread_repository,
                threads_by_provider_id,
            )
            if thread_id not in threads:
                threads[thread_id] = []
            threads[thread_id].append(imap_message_id)
        return threads

    async def _get_existing_threads(
        self, email_account_id: UUID, organization_id: UUID
    ) -> list[ShortDBThread]:
        return await self.thread_repository.get_short_threads_by_email_account_id(
            email_account_id=email_account_id,
            organization_id=organization_id,
        )

    async def _get_existing_messages(
        self, email_account_id: UUID, organization_id: UUID
    ) -> list[ShortDBMessage]:
        return await self.message_repository.get_short_messages_by_email_account_id(
            email_account_id=email_account_id,
            organization_id=organization_id,
        )

    @log_context(from_kwargs=["provider_id"])
    async def _upsert_thread(
        self,
        provider_id: MessageId,
        email_account_id: UUID,
        organization_id: UUID,
        threads_by_provider_id: dict[MessageId, ShortDBThread],
    ) -> Thread:
        if provider_id not in threads_by_provider_id:
            thread_id = uuid4()
            thread = Thread(
                id=thread_id,
                email_account_id=email_account_id,
                organization_id=organization_id,
                provider=EmailProvider.INFRAFORGE,
                provider_id=provider_id,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
                # These will be updated during _update_thread_with_messages
                subject="",
                snippet="",
                participants=[],
            )
            logger.bind(
                thread_id=thread_id,
                provider_id=provider_id,
            ).info("[imap] inserting new thread")
            return await self.message_repository.insert(thread)
        else:
            short_thread = threads_by_provider_id[provider_id]
            logger.bind(thread_id=short_thread.id, provider_id=provider_id).info(
                "[imap] thread already exists"
            )
            return not_none(
                await self.message_repository.update_by_primary_key(
                    Thread,
                    exclude_deleted_or_archived=False,
                    primary_key_to_value={"id": short_thread.id},
                    column_to_update={"deleted_at": None},
                )
            )

    @log_context(from_kwargs=["thread", "messages"])
    async def _get_or_update_thread(  # noqa: C901
        self,
        thread: Thread,
        organization_id: UUID,
        subject: str,
        snippet: str,
        unread: bool,
        starred: bool,
        has_attachments: bool,
        folders: list[str],
        earliest_message_date: ZoneRequiredDateTime | None,
        latest_message_received_date: ZoneRequiredDateTime | None,
        participants: list[EmailHydratedParticipant],
    ) -> Thread:
        updates = ThreadUpdate()

        if subject != thread.subject:
            updates.subject = subject
        if snippet != thread.snippet:
            updates.snippet = snippet
        if unread != thread.unread:
            updates.unread = unread
        if starred != thread.starred:
            updates.starred = starred
        if has_attachments != thread.has_attachments:
            updates.has_attachments = has_attachments
        if folders != thread.folders:
            updates.folders = folders
        if earliest_message_date != thread.earliest_message_date:
            updates.earliest_message_date = earliest_message_date
        if latest_message_received_date != thread.latest_message_received_date:
            updates.latest_message_received_date = latest_message_received_date

        # Compare participants by value instead of reference
        if self._compare_participants_changed(participants, thread.participants):
            updates.participants = participants

        if updates.model_dump(exclude_unset=True):
            updates.updated_at = zoned_utc_now()
            updates.deleted_at = None
            logger.bind(
                updates=updates,
                thread_id=thread.id,
            ).info("[imap] updating thread")
            return not_none(
                await self.message_repository.update_by_primary_key(
                    Thread,
                    primary_key_to_value={"id": thread.id},
                    exclude_deleted_or_archived=False,
                    column_to_update=updates,
                )
            )
        else:
            logger.bind(
                thread_id=thread.id,
            ).info("[imap] no updates to thread")
            return thread

    def _compare_participants_changed(
        self,
        new_participants: list[EmailHydratedParticipant],
        old_participants: list[EmailHydratedParticipant],
    ) -> bool:
        """Compare two lists of EmailHydratedParticipant objects by their attributes."""
        if len(new_participants) != len(old_participants):
            return True

        # Convert to dictionaries using email as key
        new_dict = {}
        old_dict = {}

        for p in new_participants:
            # Use email as key or stringified model if email is None
            key = p.email or str(p.model_dump())
            new_dict[key] = p.model_dump()

        for p in old_participants:
            key = p.email or str(p.model_dump())
            old_dict[key] = p.model_dump()

        # Check if the keys match between both dictionaries
        if set(new_dict.keys()) != set(old_dict.keys()):
            return True

        # Compare each participant's attributes
        for key, new_attrs in new_dict.items():
            if key in old_dict:
                old_attrs = old_dict[key]
                if new_attrs != old_attrs:
                    return True
            else:
                return True  # Key exists in new but not in old

        return False

    @log_context(from_kwargs=["thread", "messages"])
    async def _update_thread_with_messages(
        self,
        thread: Thread,
        messages: list[Message],
    ) -> GlobalEmailDto:
        organization_id = messages[0].organization_id
        subject = messages[0].subject
        snippet = messages[0].snippet
        unread = any(message.unread for message in messages)
        starred = any(message.starred for message in messages)
        has_attachments = any(message.attachment_ids for message in messages)
        folders = list(
            {folder for message in messages for folder in (message.folders or [])}
        )
        received_dates = [
            message.received_at for message in messages if message.received_at
        ]

        earliest_message_date = min(received_dates) if received_dates else None
        latest_message_received_date = max(received_dates) if received_dates else None
        participants = list(
            {
                participant
                for message in messages
                for participant in message.unique_email_participants
            }
        )

        updated_thread = await self._get_or_update_thread(
            thread=thread,
            organization_id=organization_id,
            subject=subject,
            snippet=snippet,
            unread=unread,
            starred=starred,
            has_attachments=has_attachments,
            folders=folders,
            earliest_message_date=earliest_message_date,
            latest_message_received_date=latest_message_received_date,
            participants=participants,
        )

        message_dtos: list[MessageDto] = []
        for message in messages:
            message_dtos.append(
                MessageDto(
                    message=message,
                    attachments=[],  # we don't need to construct attachments here
                )
            )

        # First create the basic email_dto
        email_dto = EmailDto(
            thread=updated_thread,
            message_dtos=message_dtos,
        )
        account_ids = [
            participant.account_id
            for participant in updated_thread.participants
            if participant.account_id
        ]
        # Then create global thread and messages
        (
            global_thread,
            global_message_map,
        ) = await self.global_thread_service.construct_global_thread_and_messages(
            organization_id=organization_id,
            email_dto=email_dto,
            account_ids=account_ids if account_ids else None,
        )

        # Finally update the email_dto with global thread information
        return GlobalEmailDto.from_email_dto(
            email_dto=email_dto,
            global_thread_id=global_thread.id,
            global_message_mapping=global_message_map,
        )

    async def _update_parent(
        self,
        message_id: UUID,
        parent_provider_id: MessageId,
        email_account_id: UUID,
    ) -> None:
        await self.message_repository.update_reply_to_message_id(
            message_id=message_id,
            parent_provider_id=parent_provider_id,
            provider=EmailProvider.INFRAFORGE,
            email_account_id=email_account_id,
        )

    def _extract_message_headers(self, imap_message: ImapMessage) -> MessageHeaders:
        """
        get message_id, in_reply_to, references from imap_message
        """
        message_id = imap_message.get_message_id()

        in_reply_to = None
        if "In-Reply-To" in imap_message.email_message:
            in_reply_to_raw = imap_message.email_message["In-Reply-To"]
            if in_reply_to_raw:
                in_reply_to = in_reply_to_raw.strip()

        references = None
        if "References" in imap_message.email_message:
            references_raw = imap_message.email_message["References"]
            if references_raw:
                references = [
                    ref.strip() for ref in references_raw.split() if ref.strip()
                ]
        return MessageHeaders(
            message_id=message_id, in_reply_to=in_reply_to, references=references
        )

    @log_context(from_kwargs=["imap_message"])
    async def _upsert_message(  # noqa: C901
        self,
        provider_id: ImapUid,
        imap_message: ImapMessage,
        thread_id: UUID,
        email_account_id: UUID,
        organization_id: UUID,
        messages_by_provider_id: dict[ImapUid, ShortDBMessage],
    ) -> Message:
        email_message = imap_message.email_message
        logger.bind(
            email_message=email_message,
        ).debug("[imap] upserting message")
        email_body = EmailBody(email_message)
        body_text = email_body.get_body_text()
        body_html = email_body.get_body_html()

        # Use pre-parsed visible text from ImapMessage or fallback to body_text
        visible_text = (
            imap_message.visible_text if imap_message.visible_text else body_text
        )

        body_html = imap_message.html_body if imap_message.html_body else body_html

        snippet = imap_message.preview

        headers = self._extract_message_headers(imap_message)

        email_person_map = await self.email_participant_service.find_contact_or_email_account_by_emails(
            organization_id=organization_id,
            emails=imap_message.get_unique_email_addresses(),
        )
        contact_email_pairs: list[tuple[UUID | None, EmailStr | None]] = []
        for email_str, person in email_person_map.items():
            if isinstance(person, ContactV2):
                contact_email_pairs.append((person.id, email_str))
        contact_account_map = await self.contact_resolve_service.resolve_relevant_account_by_contact_and_email_pairs(
            organization_id=organization_id,
            contact_email_pairs=contact_email_pairs,
        )

        def get_hydrated_participant(email: EmailStr) -> EmailHydratedParticipant:
            person = email_person_map.get(email)
            if person:
                if isinstance(person, EmailAccount):
                    return EmailHydratedParticipant(
                        email=email, email_account_id=person.id
                    )
                if isinstance(person, ContactV2):
                    account_id = contact_account_map.get(person.id)
                    return EmailHydratedParticipant(
                        email=email, contact_id=person.id, account_id=account_id
                    )
            return EmailHydratedParticipant(email=email)

        if provider_id not in messages_by_provider_id:
            logger.bind(
                headers=headers,
            ).info("[imap] inserting new message")
            message_id = uuid4()
            attachments = await self._upload_attachments(
                message_id=message_id,
                organization_id=organization_id,
                email_account_id=email_account_id,
                email_body=email_body,
            )
            message = Message(
                id=message_id,
                thread_id=thread_id,
                email_account_id=email_account_id,
                organization_id=organization_id,
                idempotency_key=provider_id,
                source=MessageSource.INBOX,
                subject=email_message["Subject"],
                send_from=[
                    get_hydrated_participant(e)
                    for e in imap_message.send_from_email_addresses
                ],
                send_to=[
                    get_hydrated_participant(e)
                    for e in imap_message.send_to_email_addresses
                ],
                cc=[
                    get_hydrated_participant(e) for e in imap_message.cc_email_addresses
                ],
                bcc=[
                    get_hydrated_participant(e)
                    for e in imap_message.bcc_email_addresses
                ],
                folders=list(imap_message.folders),
                snippet=snippet,
                body_text=visible_text,  # Use pre-parsed visible text
                body_html=body_html,
                unread=imap_message.is_unread,
                provider=EmailProvider.INFRAFORGE,
                provider_id=provider_id,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
                received_at=imap_message.get_message_date(),
                attachment_ids=[attachment.id for attachment in attachments],
                headers=headers,
            )
            if imap_message.dsn_status == DsnStatus.UNDELIVERED:
                # do not insert bounce notification messages into the database
                return message
            else:
                # Message created location
                inserted_message = await self.message_repository.insert(message)

                await parse_main_body_text_and_persist(
                    message=inserted_message,
                )
                await classify_message_and_update_metadata(
                    message=inserted_message,
                )
                return inserted_message
        else:
            short_message = messages_by_provider_id[provider_id]
            updates = MessageUpdate()
            if list(imap_message.folders) != short_message.folders:
                updates.folders = list(imap_message.folders)
            if imap_message.is_unread != short_message.unread:
                updates.unread = imap_message.is_unread
            if headers != short_message.headers:
                updates.headers = headers
            if imap_message.get_message_date() != short_message.received_at:
                updates.received_at = imap_message.get_message_date()
            if updates.model_dump(exclude_unset=True):
                logger.bind(
                    updates=updates,
                    message_id=short_message.id,
                ).info("[imap] updating message")
                updates.updated_at = zoned_utc_now()
                return not_none(
                    await self.message_repository.update_by_primary_key(
                        Message,
                        primary_key_to_value={"id": short_message.id},
                        column_to_update=updates,
                    )
                )
            else:
                return not_none(
                    await self.message_repository.find_by_primary_key(
                        Message,
                        id=short_message.id,
                    )
                )

    async def _upload_attachments(
        self,
        message_id: UUID,
        organization_id: UUID,
        email_account_id: UUID,
        email_body: EmailBody,
    ) -> list[Attachment]:
        db_attachments = []
        for attachment in email_body.attachments:
            attachment_id = uuid4()
            s3_key = f"{organization_id}/{email_account_id}/{message_id}/{attachment_id}/{attachment.filename}"
            self.s3_bucket_manager.write_object(key=s3_key, data=attachment.payload)
            db_attachment = Attachment(
                id=attachment_id,
                file_name=attachment.filename,
                content_type=attachment.content_type,
                size=len(attachment.payload),
                is_inline=attachment.is_inline,
                s3_key=s3_key,
                provider=EmailProvider.INFRAFORGE,
                provider_content_id=attachment.content_id,
                email_account_id=email_account_id,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
            await self.message_repository.insert(db_attachment)
            db_attachments.append(db_attachment)

        return db_attachments

    async def _combine_messages(
        self,
        messages_by_provider_id: dict[MessageId, ShortDBMessage],
        imap_messages: dict[MessageId, ImapMessage],
    ) -> dict[MessageId, CombinedMessage]:
        combined: dict[ImapUid, CombinedMessage] = {}
        for provider_id, message in messages_by_provider_id.items():
            combined[provider_id] = CombinedMessage(message)
        for provider_id, imap_message in imap_messages.items():
            if provider_id not in combined:
                combined[provider_id] = CombinedMessage(imap_message)
        return combined

    @log_context(from_kwargs=["folder"])
    def _process_folder(
        self, folder: str, email_account: EmailAccount
    ) -> tuple[list[ImapMessage], dict[ImapUid, MessageId]]:
        with imaplib.IMAP4_SSL(
            host=not_none(email_account.imap_host),
            port=not_none(email_account.imap_port),
        ) as imap_server:
            imap_server.login(
                not_none(email_account.imap_username),
                not_none(email_account.imap_password),
            )

            select_result, _ = imap_server.select(folder, readonly=True)
            if select_result != "OK":
                return ([], {})
            search_criteria = self._search_criteria(email_account)
            _, threads_data = imap_server.thread("REFERENCES", "UTF-8", search_criteria)
            thread_trees = self._build_thread_trees(threads_data[0])

            imap_messages: list[ImapMessage] = []
            imap_uid_to_message_id: dict[ImapUid, MessageId] = {}
            for thread_tree in thread_trees:
                imap_thread_id = thread_tree.imap_uid
                self._traverse_thread_tree(
                    thread_tree,
                    imap_thread_id,
                    None,
                    folder,
                    imap_messages,
                    imap_uid_to_message_id,
                    imap_server,
                )

            return imap_messages, imap_uid_to_message_id

    def _traverse_thread_tree(
        self,
        node: MessageNode,
        imap_thread_id: ImapUid,
        imap_parent_id: ImapUid | None,
        folder: str,
        imap_messages: list[ImapMessage],
        imap_uid_to_message_id: dict[ImapUid, MessageId],
        imap_server: imaplib.IMAP4_SSL,
    ) -> None:
        # Process the current node
        imap_message = self._process_message(
            node.imap_uid, imap_thread_id, imap_parent_id, folder, imap_server
        )
        if imap_message:
            imap_messages.append(imap_message)
            imap_uid_to_message_id[imap_message.imap_uid] = (
                imap_message.get_message_id()
            )

        # Recursively process children
        for child in node.children:
            self._traverse_thread_tree(
                child,
                imap_thread_id,
                node.imap_uid,
                folder,
                imap_messages,
                imap_uid_to_message_id,
                imap_server,
            )

    def _process_message(  # noqa: PLR0912, C901 PLR0915
        self,
        imap_uid: ImapUid,
        thread_imap_uid: ImapUid,
        parent_imap_uid: ImapUid | None,
        folder: str,
        imap_server: imaplib.IMAP4_SSL,
    ) -> ImapMessage | None:
        _, data = imap_server.fetch(imap_uid, "(RFC822 FLAGS INTERNALDATE PREVIEW)")
        if not data or len(data) < 2:  # noqa: PLR2004
            logger.error(f"[imap] insufficient data for message {imap_uid}")
            return None

        # Extract the email content
        email_content = None
        for item in data:
            if isinstance(item, tuple) and len(item) > 1 and b"RFC822" in item[0]:
                email_content = item[1]
                break

        if not email_content:
            logger.error(f"[imap] no RFC822 data found for message {imap_uid}")
            return None

        email_message = email.message_from_bytes(email_content)

        # Parse email content using EmailReplyParser
        visible_text = None
        preview = ""
        html_body = None

        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                if content_type == "text/plain":
                    payload = part.get_payload(decode=True)
                    if isinstance(payload, bytes):
                        text_content = payload.decode(errors="ignore")
                        visible_text = self.email_reply_parser.parse_reply(text_content)
                        if visible_text:
                            preview = visible_text
                elif content_type == "text/html":
                    payload = part.get_payload(decode=True)
                    if isinstance(payload, bytes):
                        html_body = payload.decode(errors="ignore")
        else:
            content = email_message.get_payload(decode=True)
            if isinstance(content, bytes):
                text_content = content.decode(errors="ignore")
                visible_text = self.email_reply_parser.parse_reply(text_content)
                if visible_text:
                    preview = visible_text
                if email_message.get_content_type() == "text/html":
                    html_body = text_content

        # Extract flags, preview, and internal date from the response
        flags: tuple[bytes, ...] = ()
        internal_date = None
        for item in data:
            if isinstance(item, tuple) and isinstance(item[0], bytes):
                item_str = item[0].decode("utf-8")
                if "FLAGS" in item_str:
                    flags = imaplib.ParseFlags(item[0])
                if "PREVIEW" in item_str and not preview:
                    preview_match = re.search(r'PREVIEW "([^"]+)"', item_str)
                    if preview_match:
                        preview = preview_match.group(1)
                    else:
                        preview = item[1].decode("utf-8", errors="ignore").strip()
                if "INTERNALDATE" in item_str:
                    internal_date_match = re.search(r'INTERNALDATE "([^"]+)"', item_str)
                    if internal_date_match:
                        internal_date_str = internal_date_match.group(1)
                        internal_date = parsedate_to_datetime(internal_date_str)
                        if internal_date.tzinfo is None:
                            internal_date = internal_date.replace(tzinfo=pytz.UTC)

        if internal_date is None:
            logger.warning(
                f"[imap] no internal date found for message {imap_uid}, using current time"
            )
            internal_date = zoned_utc_now()

        is_unread = b"\\Seen" not in flags

        return ImapMessage(
            imap_uid=imap_uid,
            email_message=email_message,
            is_unread=is_unread,
            folder=folder,
            internal_date=internal_date,
            preview=preview,
            thread_imap_uid=thread_imap_uid,
            parent_imap_uid=parent_imap_uid,
            visible_text=visible_text,
            html_body=html_body,
        )

    def _build_thread_trees(self, thread_data: bytes) -> list[MessageNode]:
        """
        Parses the thread data into a list of MessageNode trees.

        Example:
            Input: (2)(3 6 (4 23)(44 7 96))
            Output: [
                MessageNode(id='2'),
                MessageNode(id='3', children=[
                    MessageNode(id='6', children=[
                        MessageNode(id='4', children=[
                            MessageNode(id='23')
                        ]),
                        MessageNode(id='44', children=[
                            MessageNode(id='7', children=[
                                MessageNode(id='96')
                            ])
                        ])
                    ])
                ])
            ]
        """
        decoded_data = thread_data.decode("utf-8").strip()
        tokens = self._tokenize(decoded_data)
        return self._build_trees(tokens)

    def _tokenize(self, data: str) -> list[str]:
        tokens = []
        i = 0
        while i < len(data):
            char = data[i]
            if char in ("(", ")"):
                tokens.append(char)
                i += 1
            elif char.isdigit():
                num = ""
                while i < len(data) and data[i].isdigit():
                    num += data[i]
                    i += 1
                tokens.append(num)
            else:
                i += 1
        return tokens

    def _build_trees(self, tokens: list[str]) -> list[MessageNode]:
        trees = []
        stack: list[str | MessageNode] = []

        for token in tokens:
            if token == "(":  # noqa: S105
                # Push a marker to indicate the start of a new subtree
                stack.append("(")
            elif token == ")":  # noqa: S105
                # Pop elements until the marker '(' is found
                while stack and stack[-1] != "(":
                    stack.pop()
                if stack and stack[-1] == "(":
                    stack.pop()
            else:
                # It's a message ID; create a new node
                new_node = MessageNode(imap_uid=token)

                # Find the most recent parent node (last node in the stack that's not a marker)
                parent = None
                for item in reversed(stack):
                    if item != "(" and isinstance(item, MessageNode):
                        parent = item
                        break

                if parent:
                    parent.add_child(new_node)
                else:
                    # If no parent is found, this node is a root node
                    trees.append(new_node)

                # Push the new node onto the stack as the current node
                stack.append(new_node)

        return trees

    def _process_all_folders(
        self, email_account: EmailAccount
    ) -> tuple[dict[MessageId, ImapMessage], dict[ImapUid, MessageId]]:
        imap_messages: dict[MessageId, ImapMessage] = {}

        # Due to global ID conflicts across folders, we only support the INBOX folder.
        folders = ["INBOX"]

        for folder in folders:
            folder_messages, imap_uid_to_message_id = self._process_folder(
                folder=folder, email_account=email_account
            )
            for imap_message in folder_messages:
                imap_uid = imap_message.imap_uid
                if imap_uid not in imap_messages:
                    imap_messages[imap_uid_to_message_id[imap_uid]] = imap_message
                else:
                    imap_messages[imap_uid_to_message_id[imap_uid]].folders.update(
                        imap_message.folders
                    )

        return imap_messages, imap_uid_to_message_id

    def _is_self_reply(self, parent_message: Message, current_message: Message) -> bool:
        """
        Check if the current message is a self-reply (same sender replying to their own message)

        Args:
            parent_message: The parent message being replied to
            current_message: The current message (reply)

        Returns:
            bool: True if it's a self-reply, False otherwise
        """
        # If either sender list is empty, it can't be a self-reply
        if not parent_message.send_from or not current_message.send_from:
            return False

        # Extract parent message sender emails
        parent_sender_emails: set[EmailStr] = set()
        for sender in parent_message.send_from:
            if hasattr(sender, "email") and sender.email:
                parent_sender_emails.add(sender.email)

        # Extract current message sender emails
        current_sender_emails: set[EmailStr] = set()
        for sender in current_message.send_from:
            if hasattr(sender, "email") and sender.email:
                current_sender_emails.add(sender.email)

        # Check if there's an overlap between sender emails
        return bool(parent_sender_emails.intersection(current_sender_emails))

    async def sync(  # noqa: C901 PLR0915
        self,
        email_account_id: UUID,
    ) -> None:
        with logger.contextualize(email_account_id=email_account_id):
            if not (
                email_account := (
                    await self.email_account_service_ext.get_decrypted_email_account_by_id(
                        email_account_id=email_account_id
                    )
                )
            ):
                raise ResourceNotFoundError(
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.RESOURCE_NOT_FOUND,
                        details=f"Email account {email_account_id} not found",
                    )
                )

            organization_id = email_account.organization_id
            logger.info("[imap] Starting sync process")

            imap_messages, imap_uid_to_message_id = self._process_all_folders(
                email_account
            )
            logger.bind(
                imap_messages_count=len(imap_messages),
                imap_uid_mapping_count=len(imap_uid_to_message_id),
            ).info("[imap] Processed IMAP folders")

            existing_threads = await self._get_existing_threads(
                email_account_id, organization_id
            )
            threads_by_provider_id = {
                t.provider_id: t for t in existing_threads if t.provider_id
            }
            threads_by_id = {t.id: t for t in existing_threads}
            existing_messages = await self._get_existing_messages(
                email_account_id, organization_id
            )
            messages_by_provider_id = {
                m.provider_id: m for m in existing_messages if m.provider_id
            }
            logger.bind(
                existing_threads_count=len(existing_threads),
                threads_by_provider_id_count=len(threads_by_provider_id),
                existing_messages_count=len(existing_messages),
                messages_by_provider_id_count=len(messages_by_provider_id),
            ).info("[imap] Got existing threads and messages")

            combined_messages = await self._combine_messages(
                messages_by_provider_id, imap_messages
            )
            logger.bind(combined_messages_count=len(combined_messages)).info(
                "[imap] Combined messages"
            )

            threads = await self._build_message_threads(
                imap_messages,
                combined_messages,
                threads_by_id,
                imap_uid_to_message_id,
                threads_by_provider_id,
            )
            logger.bind(threads_count=len(threads)).info("[imap] Built message threads")

            for thread_provider_id, message_provider_ids in threads.items():
                logger.bind(
                    thread_provider_id=thread_provider_id,
                    message_provider_ids_count=len(message_provider_ids),
                ).info("[imap] Processing thread")

                thread = await self._upsert_thread(
                    provider_id=thread_provider_id,
                    email_account_id=email_account_id,
                    organization_id=organization_id,
                    threads_by_provider_id=threads_by_provider_id,
                )
                messages = {}
                for provider_id in message_provider_ids:
                    imap_message = imap_messages[provider_id]
                    upserted_message = await self._upsert_message(
                        provider_id=provider_id,
                        imap_message=imap_message,
                        thread_id=thread.id,
                        email_account_id=email_account_id,
                        organization_id=organization_id,
                        messages_by_provider_id=messages_by_provider_id,
                    )
                    messages[provider_id] = upserted_message

                logger.bind(messages_count=len(messages)).info(
                    "[imap] Upserted messages for thread"
                )

                email_dto = await self._update_thread_with_messages(
                    thread=thread,
                    messages=list(messages.values()),
                )
                logger.bind(email_dto=email_dto).info(
                    "imap_syncing_service: sync: email_dto"
                )
                for provider_id, message in messages.items():
                    imap_message = imap_messages[provider_id]
                    parent_imap_uid = imap_message.parent_imap_uid

                    logger.bind(
                        message_id=message.id,
                        provider_id=provider_id,
                        parent_imap_uid=parent_imap_uid,
                    ).info("[imap] Processing message relationship")
                    if parent_imap_uid:
                        parent_provider_id = imap_uid_to_message_id[parent_imap_uid]
                        await self._update_parent(
                            message_id=message.id,
                            parent_provider_id=parent_provider_id,
                            email_account_id=email_account_id,
                        )
                        # Process reply events for the parent message
                        parent_message: Message | None = messages[parent_provider_id]
                        if not parent_message:
                            raise ResourceNotFoundError(
                                additional_error_details=ErrorDetails(
                                    code=ErrorCode.RESOURCE_NOT_FOUND,
                                    details=f"Parent message with provider_id {parent_provider_id} not found",
                                )
                            )

                        # Check if this is a self-reply
                        is_self_reply = self._is_self_reply(parent_message, message)

                        if is_self_reply:
                            logger.bind(
                                parent_message_id=parent_message.id,
                            ).info(
                                "[imap] Skipped creating REPLIED event for self-reply"
                            )

                        # Only create REPLIED event if it's not a self-reply and the message is not bounce
                        if not is_self_reply and not imap_message.dsn_status:
                            await self.email_event_service.process_global_email_event(
                                email_account_id=email_account_id,
                                organization_id=organization_id,
                                event_type=EmailEventType.REPLIED,
                                event_time=imap_message.get_message_date(),
                                global_message_id=not_none(
                                    email_dto.get_global_message_id_by_message_id(
                                        parent_message.id
                                    )
                                ),
                                global_thread_id=not_none(email_dto.global_thread_id),
                                event_details=None,
                                # use parent message id as external event id as it's reply to this message
                                external_event_id=str(parent_message.id),
                            )

                            logger.bind(
                                parent_message_id=parent_message.id,
                            ).info("[imap] Created REPLIED event for parent message")

                    # Process bounced messages email event
                    if (
                        imap_message.dsn_status
                        and imap_message.dsn_status == DsnStatus.UNDELIVERED
                        and imap_message.original_message_id
                    ):
                        original_message_provider_id = imap_message.original_message_id
                        logger.bind(
                            original_message_provider_id=original_message_provider_id
                        ).info("[imap] Processing bounce message")

                        if original_message_provider_id in messages:
                            original_message = messages[original_message_provider_id]
                            await self.email_event_service.process_global_email_event(
                                email_account_id=email_account_id,
                                organization_id=organization_id,
                                event_type=EmailEventType.BOUNCE_DETECTED,
                                event_time=imap_message.get_message_date(),
                                global_message_id=not_none(
                                    email_dto.get_global_message_id_by_message_id(
                                        original_message.id
                                    )
                                ),
                                global_thread_id=not_none(email_dto.global_thread_id),
                                event_details=None,
                                # use original message id as external event id as it's bounce to this message
                                external_event_id=str(original_message.id),
                            )
                            logger.bind(
                                original_message_id=original_message.id,
                            ).info("[imap] Created BOUNCE event for original message")
                        else:
                            logger.bind(
                                original_message_provider_id=original_message_provider_id,
                            ).info("[imap] original message not found in the thread")

            await self._update_imap_last_synced_at(email_account_id)
            logger.info("[imap] Sync completed successfully")


class SingletonImapSyncingService(Singleton, ImapSyncingService):
    pass


def get_imap_syncing_service_general(engine: DatabaseEngine) -> ImapSyncingService:
    return SingletonImapSyncingService(db_engine=engine)
