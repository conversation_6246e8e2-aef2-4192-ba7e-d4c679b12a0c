import time
from typing import cast

from salestech_be.common.error_code import <PERSON>rror<PERSON>ode
from salestech_be.common.events import (
    DomainEnrichedCDCEvent,
    EnrichedCDCEventProcessor,
)
from salestech_be.common.exception.exception import ErrorDetails, ResourceNotFoundError
from salestech_be.common.stats.metric import custom_metric
from salestech_be.core.ai.event_handlers.close_tasks import close_tasks_handler
from salestech_be.core.ai.event_handlers.pipeline_intel import pipeline_intel_handler
from salestech_be.core.ai.event_handlers.pipeline_update import (
    start_pipeline_update_workflow,
)
from salestech_be.core.ai.workflows.schema import PipelineUpdateReason
from salestech_be.core.crm_ai_rec.crm_ai_rec_service import CrmAIRecService
from salestech_be.core.ff.feature_flag_service import get_feature_flag_service
from salestech_be.core.ff.types import FeatureFlagRequest
from salestech_be.core.meeting.meeting_bot_service import MeetingBotService
from salestech_be.core.meeting.meeting_cdc_service import (
    meeting_research_handler,
    sequence_exit_handler,
)
from salestech_be.core.meeting.service.meeting_query_service import MeetingQueryService
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
from salestech_be.db.models.meeting import Meeting
from salestech_be.integrations.kafka.types import (
    CDCObject,
    CDCObjectState,
    MeetingView,
)
from salestech_be.ree_logging import get_logger
from salestech_be.search.indexing.cdc_event_handlers import (
    index_meeting_handler,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.settings import settings

logger = get_logger(__name__)


class MeetingCDCEventProcessor(EnrichedCDCEventProcessor[Meeting, MeetingV2]):
    def __init__(
        self,
        meeting_query_service: MeetingQueryService,
        meeting_bot_service: MeetingBotService,
        crm_ai_rec_service: CrmAIRecService,
    ):
        self.meeting_query_service = meeting_query_service
        self.meeting_bot_service = meeting_bot_service
        self.ff_service = get_feature_flag_service()
        self.crm_ai_rec_service = crm_ai_rec_service

    def get_table_model(self, event: CDCObject) -> Meeting:
        return Meeting.model_validate(event.model_dump(exclude={"view_model"}))

    async def get_domain_model(
        self, event: CDCObject, event_state: CDCObjectState
    ) -> MeetingV2:
        start_time = time.perf_counter()
        meeting_view = cast(MeetingView, event)

        try:
            if event_state == CDCObjectState.BEFORE:
                # We do not have associated data (e.g. bot) for point in time for the before meeting,
                # default to empty.
                meeting = self.get_table_model(event)
                meeting_bot = (
                    await self.meeting_bot_service.get_latest_meeting_bot_by_meeting_id(
                        meeting_id=meeting.id,
                        organization_id=meeting.organization_id,
                    )
                )
                return await self.meeting_query_service.meeting_v2_from_db_objects(
                    organization_id=meeting.organization_id,
                    meeting=meeting,
                    meeting_bot=meeting_bot,
                    user_id=None,
                )

            meeting_v2 = await self.meeting_query_service.get_meeting_v2(
                meeting_id=meeting_view.id,
                organization_id=meeting_view.organization_id,
            )

            if not meeting_v2:
                raise ResourceNotFoundError(
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.MEETING_NOT_FOUND,
                        details="Meeting not found",
                        reference_id=str(meeting_view.id),
                    )
                )
            return meeting_v2
        finally:
            end_time = time.perf_counter()
            custom_metric.timing(
                metric_name="cdc_event_processor_get_domain_model_meeting",
                value=(end_time - start_time) * 1000,
                tags=[
                    f"{event_state}:{event_state}",
                ],
            )

    async def process_domain_enriched_event(
        self, event: DomainEnrichedCDCEvent[Meeting, MeetingV2]
    ) -> MeetingV2:
        logger.bind(event_before=event.before, event_after=event.after).info(
            "Processing meeting event"
        )
        # Check feature flag for auto close tasks
        if event.after and event.after.organization_id:
            enable_auto_close = await self.ff_service.is_enabled(
                request=FeatureFlagRequest(
                    flag_key="task-auto-completion-vfeb2025",
                    organization_id=event.after.organization_id,
                )
            )
            if enable_auto_close:
                await close_tasks_handler(event)

        pipeline_update_enabled = (
            settings.enable_pipeline_update_multi_meeting_run
            and (
                settings.enable_pipeline_update_multi_meeting_run_org_ids == []
                or event.after.organization_id
                in settings.enable_pipeline_update_multi_meeting_run_org_ids
            )
        )

        if pipeline_update_enabled and "sales_action_types" in event.modified_fields:
            logger.info(
                "Sales action types modified" + str(event.after.sales_action_types)
            )
            last_change_record = (
                await self.crm_ai_rec_service.find_crm_property_metadata(
                    organization_id=event.after.organization_id,
                    sobject_name="meeting",
                    record_id=event.after.id,
                    sobject_field_path="sales_action_types",
                )
            )
            if (
                last_change_record
                and last_change_record.field_last_updated_source == "user"
                and event.after.pipeline_id
            ):
                # Run the pipeline update workflow
                await start_pipeline_update_workflow(
                    organization_id=event.after.organization_id,
                    pipeline_id=event.after.pipeline_id,
                    reason=PipelineUpdateReason.SALES_ACTION_ROLE_CLASSIFICATION,
                    meeting_id=event.after.id,
                )

        if settings.enable_research_agent_on_meeting:
            await meeting_research_handler(event)
        if settings.enable_search_indexing:
            await index_meeting_handler(event)
        if settings.enable_pipeline_intel_on_meeting:
            await pipeline_intel_handler(event)
        if settings.enable_sequence_exit_on_meeting:
            await sequence_exit_handler(event)

        return event.current_domain_model
