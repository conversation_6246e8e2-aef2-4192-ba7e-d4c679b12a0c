import asyncio
import json
from uuid import UUID

from pydantic_ai import Agent
from pydantic_ai.models.anthropic import AnthropicModel
from pydantic_ai.models.gemini import GeminiModel
from pydantic_ai.providers.anthropic import AnthropicProvider
from pydantic_ai.providers.google_vertex import GoogleVertexProvider

from salestech_be.core.meeting.meeting_service import (
    MeetingService,
    meeting_service_factory_general,
)
from salestech_be.core.meeting.types.meeting_query_types import (
    MeetingFilter,
    MeetingQueryResponse,
    MeetingQueryResult,
    MeetingSummaryWithSource,
)
from salestech_be.core.transcript.transcript_service import (
    TranscriptService,
    transcript_service_from_engine,
)
from salestech_be.core.transcript.types import TranscriptContainer
from salestech_be.db.dao.meeting_repository import MeetingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.meeting import Meeting, MeetingStatus
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.web.api.meeting.schema import (
    TranscriptSentenceWithLineNum,  # fmt: skip  # tach-ignore
)

logger = get_logger(__name__)


class MeetingTranscriptAgentDeps:
    """Dependencies for the meeting transcript agent"""

    def __init__(
        self,
        transcript_service: TranscriptService,
        meeting_repository: MeetingRepository,
        organization_id: UUID,
    ):
        self.transcript_service = transcript_service
        self.meeting_repository = meeting_repository
        self.organization_id = organization_id


class MeetingTranscriptAgentService:
    """Service for querying meeting transcripts using AI agents"""

    def __init__(
        self,
        transcript_service: TranscriptService,
        meeting_repository: MeetingRepository,
        meeting_service: MeetingService,
    ):
        self.transcript_service = transcript_service
        self.meeting_repository = meeting_repository
        self.meeting_service = meeting_service

        # Create the query agent
        self.query_agent = Agent[MeetingTranscriptAgentDeps, MeetingQueryResult](
            model=GeminiModel(
                model_name="gemini-2.0-flash",
                provider=GoogleVertexProvider(
                    service_account_info=json.loads(
                        settings.vertex_ai_cred_json.get_secret_value()
                    )
                ),
            ),
            system_prompt="""You are an expert at analyzing meeting transcripts and extracting relevant information.
            When analyzing a transcript:
            1. Focus on extracting information relevant to the user's question
            2. Provide clear, concise answers with specific quotes when relevant
            3. Assign confidence scores based on how directly the transcript answers the question
            4. Only include information explicitly stated in the transcript""",
            deps_type=MeetingTranscriptAgentDeps,
            result_type=MeetingQueryResult,
            name="meeting-transcript-query-agent",
            instrument=True,
        )

        # Create the summary agent
        self.summary_agent = Agent[None, MeetingSummaryWithSource](
            model=AnthropicModel(
                "claude-3-7-sonnet-20250219",
                provider=AnthropicProvider(
                    api_key=settings.anthropic_api_key.get_secret_value()
                ),
            ),
            system_prompt="""You are an expert at synthesizing information from multiple meeting transcripts.
            When creating summaries:
            1. Identify key themes and patterns across meetings
            2. Highlight important agreements or disagreements
            3. Note any evolution of topics over time
            4. Be clear about the strength of evidence for each conclusion""",
            name="meeting-transcript-summary-agent",
            result_type=MeetingSummaryWithSource,
            instrument=True,
        )

    async def query_meetings(
        self,
        question: str,
        filters: MeetingFilter | None = None,
    ) -> MeetingQueryResponse:
        """Query multiple meeting transcripts and synthesize the results"""
        # Get meetings matching filters
        meetings = await self._get_filtered_meetings(filters or MeetingFilter())
        logger.debug(f"found {len(meetings)} meetings")
        logger.debug(f"meetings details {meetings}")

        # Query each meeting in parallel
        async def process_meeting(meeting: Meeting) -> MeetingQueryResult | None:
            # Meetings that are completed are the ones that have been transcribed and analyzed
            if meeting.status != MeetingStatus.COMPLETED:
                return None

            transcript_container = await self._get_meeting_transcript(meeting)
            if not transcript_container:
                return None

            # Create deps for this query
            deps = MeetingTranscriptAgentDeps(
                transcript_service=self.transcript_service,
                meeting_repository=self.meeting_repository,
                organization_id=meeting.organization_id,
            )

            transcript_sentences = []

            # Construct meeting transcript with line numbers
            for line_number, sentence in enumerate(
                transcript_container.transcript.sentences
            ):
                transcript_sentences.append(
                    TranscriptSentenceWithLineNum(
                        **sentence.model_dump(),
                        line_number=line_number,
                    )
                )

            # Run query agent
            prompt = f"""
            Question: {question}

            Meeting ID: {meeting.id} {meeting.title}
            Attendees: {meeting.attendees}

            Meeting Transcript:
            {transcript_sentences}

            Analyze this transcript and extract relevant information to answer the question.
            Provide specific quotes when possible and assign a confidence score based on how directly the transcript answers the question.
            Also provide some identifying information on the meeting so that the I can refer back to specific meetings.
            Include citations that are only from the transcript's raw text, do not include information from the meeting data or transcript sentence metadata such as speaker names, and only include meaningful sentences.
            Each citation should be the exact relevant text from the transcript and the corresponding line number. Ideally, there should be no small 1-2 line gaps in between citations, if the sentences are close to one another, include the intermediate sentences for continuity.
            """

            result = await self.query_agent.run(
                prompt,
                deps=deps,
                message_history=[],  # Fresh conversation for each meeting
            )
            return result.data

        tasks = [process_meeting(meeting) for meeting in meetings]
        results = [r for r in await asyncio.gather(*tasks) if r is not None]

        # Generate summary
        summary_prompt = f"""
        Question: {question}

        Individual Meeting Results:
        {results}

        Please synthesize these results into a coherent summary that answers the question.
        Identify key themes, patterns, and any evolution of topics across meetings.
        Note areas of agreement and disagreement.
        Be clear about the strength of evidence for each conclusion.
        From the citations field in the individual meeting results, pick the most relevant citations, if any, and include them in the summary. Only keep the most significant citations, there should be 1-3 citations at most in the summary.
        """

        summary_result = await self.summary_agent.run(summary_prompt)

        return MeetingQueryResponse(
            results=results,
            summary=summary_result.data,
        )

    async def _get_filtered_meetings(self, filters: MeetingFilter) -> list[Meeting]:
        """Get meetings matching the given filters"""
        if not filters.organization_id:
            logger.debug("No organization ID provided in filters")
            return []

        if filters.pipeline_id and not filters.meeting_ids:
            return await self.meeting_repository.list_meetings_by_pipeline_id(
                organization_id=filters.organization_id,
                pipeline_id=filters.pipeline_id,
            )

        # TODO: filter for meeting ids by start or end date and participant id
        return await self.meeting_repository.list_meetings_by_organization_id(
            organization_id=filters.organization_id, meeting_ids=filters.meeting_ids
        )

    async def _get_meeting_transcript(
        self, meeting: Meeting
    ) -> TranscriptContainer | None:
        try:
            _, transcript_container = await self.meeting_service.get_meeting_transcript(
                meeting=meeting,
                organization_id=meeting.organization_id,
            )
            return transcript_container
        except Exception as e:
            logger.bind(
                meeting_id=meeting.id,
                organization_id=meeting.organization_id,
                reference_id=meeting.reference_id,
                reference_id_type=meeting.reference_id_type,
            ).error(f"Failed to get meeting transcript: {e}", exc_info=e)
            return None


def meeting_transcript_agent_service_from_engine(
    engine: DatabaseEngine,
) -> MeetingTranscriptAgentService:
    """Create MeetingTranscriptAgentService from database engine"""
    return MeetingTranscriptAgentService(
        transcript_service=transcript_service_from_engine(engine),
        meeting_repository=MeetingRepository(engine=engine),
        meeting_service=meeting_service_factory_general(engine),
    )
