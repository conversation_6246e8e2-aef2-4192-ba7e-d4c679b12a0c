from uuid import UUID

from salestech_be.core.activity.service.activity_service import (
    ActivityService,
)
from salestech_be.core.activity.types import Activity
from salestech_be.core.crm_integrity.event_processors.abstract_processor import (
    AbstractDataOperationProcessor,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
    DataArchivalParam,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationCommonPreviewIdentifier,
    IntegrityAssociatedDataOperationPreviewIdentifiers,
)
from salestech_be.core.meeting.meeting_data_integrity_service import (
    MeetingDataIntegrityService,
)
from salestech_be.db.models.activity import ActivityType
from salestech_be.db.models.crm_integrity import (
    AssociatedEntityType,
    CRMIntegrityJobUserOption,
)
from salestech_be.db.models.meeting import Meeting, MeetingReferenceIdType


class MeetingAccountArchiveEventProcessor(
    AbstractDataOperationProcessor[DataArchivalParam, CrmIdReplacementTrackParam]
):
    """
    Event processor for Account Archive within Meeting domain and associated activities.
    """

    def __init__(
        self,
        meeting_data_integrity_service: MeetingDataIntegrityService,
        activity_service: ActivityService,
    ) -> None:
        self.meeting_data_integrity_service = meeting_data_integrity_service
        self.activity_service = activity_service

    async def fetch_affected_meeting_and_activitiese(
        self,
        account_id: UUID,
        organization_id: UUID,
    ) -> tuple[list[Meeting], list[Activity]]:
        relevant_meetings = (
            await self.meeting_data_integrity_service.find_meetings_by_account_id(
                account_id=account_id,
                organization_id=organization_id,
            )
        )

        relevant_activities = (
            await self.activity_service.list_activities_by_reference_ids_and_type(
                organization_id=organization_id,
                reference_ids=[str(meeting.id) for meeting in relevant_meetings],
                activity_type=ActivityType.MEETING,
            )
        )

        return relevant_meetings, relevant_activities

    async def process(
        self,
        *,
        param: DataArchivalParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[CrmIdReplacementTrackParam]:
        """
        No-op for meeting processing in this job
        """
        return []

    async def preview(
        self,
        *,
        param: DataArchivalParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[IntegrityAssociatedDataOperationPreviewIdentifiers]:
        (
            meetings,
            _,
        ) = await self.fetch_affected_meeting_and_activitiese(
            account_id=param.archive_entity_id,
            organization_id=param.organization_id,
        )

        # only preview video meetings in this domain, voice meetings are in voice_call domain
        return [
            IntegrityAssociatedDataOperationPreviewIdentifiers(
                entity_type=AssociatedEntityType.MEETING,
                entity_ids=[
                    IntegrityAssociatedDataOperationCommonPreviewIdentifier(
                        id=meeting.id,
                    )
                    for meeting in meetings
                    if meeting.reference_id_type
                    == MeetingReferenceIdType.USER_CALENDAR_EVENT
                ],
            )
        ]
