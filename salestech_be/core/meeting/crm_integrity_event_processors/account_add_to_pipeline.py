from uuid import UUID

from salestech_be.core.activity.service.activity_service import (
    ActivityService,
)
from salestech_be.core.activity.types import (
    Activity,
    ActivityPatchRequest,
)
from salestech_be.core.crm_integrity.event_processors.abstract_processor import (
    AbstractDataOperationProcessor,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
    DataAdditionParam,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationCommonPreviewIdentifier,
    IntegrityAssociatedDataOperationPreviewIdentifiers,
)
from salestech_be.core.meeting.crm_integrity_event_processors.meeting_common_processor import (
    MeetingCommonProcessor,
)
from salestech_be.core.meeting.meeting_data_integrity_service import (
    MeetingDataIntegrityService,
)
from salestech_be.core.meeting.meeting_service import MeetingService
from salestech_be.db.models.activity import ActivityType
from salestech_be.db.models.crm_integrity import (
    AssociatedEntityField,
    AssociatedEntityFieldType,
    AssociatedEntityOperation,
    AssociatedEntityType,
    CRMIntegrityJobUserOption,
)
from salestech_be.db.models.meeting import Meeting, MeetingReferenceIdType


class MeetingAccountAddToPipelineEventProcessor(
    AbstractDataOperationProcessor[DataAdditionParam, CrmIdReplacementTrackParam]
):
    """
    Event processor for Account Add to Pipeline within Meeting domain and associated activities.
    """

    def __init__(
        self,
        meeting_data_integrity_service: MeetingDataIntegrityService,
        meeting_service: MeetingService,
        activity_service: ActivityService,
    ) -> None:
        self.meeting_data_integrity_service = meeting_data_integrity_service
        self.activity_service = activity_service
        self.meeting_common_processor = MeetingCommonProcessor(
            meeting_data_integrity_service=meeting_data_integrity_service,
            meeting_service=meeting_service,
        )

    async def fetch_affected_meetings(
        self,
        account_id: UUID,
        organization_id: UUID,
    ) -> list[Meeting]:
        return [
            meeting
            for meeting in await self.meeting_data_integrity_service.find_meetings_by_account_id(
                account_id=account_id,
                organization_id=organization_id,
            )
            if not meeting.pipeline_id
        ]

    async def fetch_affected_activity_records(
        self,
        meetings: list[Meeting],
        organization_id: UUID,
    ) -> dict[UUID, list[Activity]]:
        """
        return a meeting_id -> list[activity] map
        """
        affected_activities = (
            await self.activity_service.list_activities_by_reference_ids_and_type(
                organization_id=organization_id,
                reference_ids=[str(meeting.id) for meeting in meetings],
                activity_type=ActivityType.MEETING,
            )
        )
        result_map: dict[UUID, list[Activity]] = {}
        for activity in affected_activities:
            result_map.setdefault(UUID(activity.reference_id), []).append(activity)
        return result_map

    async def process(
        self,
        *,
        param: DataAdditionParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[CrmIdReplacementTrackParam]:
        track_params = []
        meetings = await self.fetch_affected_meetings(
            account_id=param.src_entity_id,
            organization_id=param.organization_id,
        )

        meeting_to_activities_map = await self.fetch_affected_activity_records(
            meetings=meetings,
            organization_id=param.organization_id,
        )

        for meeting in meetings:
            # try resolve pipeline id again for non pipeline_id meeting
            track_pipeline_change = (
                await self.meeting_common_processor.refresh_pipeline_id_in_meeting(
                    meeting_id=meeting.id,
                    organization_id=param.organization_id,
                )
            )
            track_params.extend(track_pipeline_change)

            if track_pipeline_change:
                # if pipeline is changed, update related activities accordingly
                for activity in meeting_to_activities_map.get(meeting.id, []):
                    await self.activity_service.patch_activity_by_id(
                        activity_id=activity.id,
                        organization_id=param.organization_id,
                        req=ActivityPatchRequest(pipeline_id=param.dest_entity_id),
                    )

                    track_params.append(
                        CrmIdReplacementTrackParam(
                            entity_type=AssociatedEntityType.ACTIVITY,
                            entity_id=activity.id,
                            entity_field_name=AssociatedEntityField.PIPELINE_ID,
                            entity_field_type=AssociatedEntityFieldType.UUID,
                            entity_operation=AssociatedEntityOperation.UPDATE,
                            before_value=str(activity.pipeline_id)
                            if activity.pipeline_id
                            else None,
                            after_value=str(param.dest_entity_id),
                        )
                    )
        return track_params

    async def preview(
        self,
        *,
        param: DataAdditionParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[IntegrityAssociatedDataOperationPreviewIdentifiers]:
        meetings = await self.fetch_affected_meetings(
            account_id=param.src_entity_id,
            organization_id=param.organization_id,
        )

        # only preview video meetings in this domain, voice meetings are in voice_call domain
        return [
            IntegrityAssociatedDataOperationPreviewIdentifiers(
                entity_type=AssociatedEntityType.MEETING,
                entity_ids=[
                    IntegrityAssociatedDataOperationCommonPreviewIdentifier(
                        id=meeting.id,
                    )
                    for meeting in meetings
                    if meeting.reference_id_type
                    == MeetingReferenceIdType.USER_CALENDAR_EVENT
                ],
            )
        ]
