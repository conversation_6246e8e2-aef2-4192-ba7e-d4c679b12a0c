from collections.abc import Callable, Coroutine
from typing import Any, cast

from fastapi import Request
from svix.webhooks import Webhook, WebhookVerificationError

from salestech_be.core.meeting.meeting_realtime_service import (
    MeetingRealtimeService,
    meeting_realtime_service_by_request,
)
from salestech_be.core.meeting.meeting_service import (
    MeetingService,
    meeting_service_factory,
)
from salestech_be.core.meeting.service_type import RecallStatusCode
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import MEETING_TASK_QUEUE
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.workflows.meeting.meeting_completion import (
    MeetingCompletionWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    MeetingCompletionWorkflowData,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.meeting.schema import (
    RecallBotEvent,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    RecallBotEventData,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    RecallBotEventTypes,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    RecallBotStatusChangeEvent,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    RecallRealtimeParticipantJoinEvent,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    RecallRealtimeTranscriptionEvent,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class MeetingWebhookService:
    Handler = Callable[[str, RecallBotEventData], Coroutine[Any, Any, None]]  # type: ignore[explicit-any] # TODO: fix-any-annotation

    def __init__(
        self,
        meeting_service: MeetingService,
        meeting_realtime_service: MeetingRealtimeService,
    ) -> None:
        self.meeting_service = meeting_service
        self.meeting_realtime_service = meeting_realtime_service
        self.handlers: dict[str, MeetingWebhookService.Handler] = {
            RecallStatusCode.JOINING_CALL: self.joining_call_status,
            RecallStatusCode.IN_WAITING_ROOM: self.in_waiting_room_status,
            RecallStatusCode.IN_CALL_RECORDING: self.in_call_recording_status,
            RecallStatusCode.RECORDING_PERMISSION_DENIED: self.recording_permission_denied_status,
            RecallStatusCode.CALL_ENDED: self.call_ended_status,
            RecallStatusCode.DONE: self.done_status,
            RecallStatusCode.ANALYSIS_FAILED: self.analysis_failed_status,
            RecallStatusCode.ANALYSIS_DONE: self.analysis_done_status,
        }

    @staticmethod
    async def is_signature_valid(
        recall_event: RecallBotEvent, request: Request
    ) -> bool:
        """
        Validates the signature of a recall event.  Returns true also in the case that
        the event has no signature and cannot be verified, or if verification is
        disabled.
        """

        if not settings.enable_recall_signature_verification:
            logger.info("Recall signature verification disabled")
            return True

        if recall_event.event != RecallBotEventTypes.BOT_STATUS_CHANGE:
            # Signature verification is done only for bot status events.  Other webhooks
            # (such as realtime events) instead rely on query parameters and are handled
            # in meeting service.
            logger.bind(event_type=recall_event.event, headers=request.headers).info(
                "Event does not have signature headers, skipping signature validation"
            )
            return True
        else:
            logger.info("Validating recall signature")

        request_body = await request.body()
        try:
            svix_webhook = Webhook(settings.recall_webhook_signing_secret)
            svix_webhook.verify(request_body, dict(request.headers))
        except WebhookVerificationError as e:
            logger.warning("Invalid recall webhook", exc_info=e)
            return False

        logger.info("Recall signature verification passed")
        return True

    async def handle_event(
        self,
        event: str,
        recall_bot_event: RecallBotEvent,
        query_params: dict[str, list[str]],
    ) -> None:
        # Logic to choose the correct handler based on the event or data attributes
        match event:
            case RecallBotEventTypes.BOT_OUTPUT_LOG.value:
                logger.info(f"Output log event: {recall_bot_event.model_dump_json()}")
            case RecallBotEventTypes.BOT_STATUS_CHANGE.value:
                recall_status_change_event: RecallBotStatusChangeEvent = cast(
                    RecallBotStatusChangeEvent, recall_bot_event
                )
                handler = self.handlers.get(
                    recall_status_change_event.data.status.code,
                    self.default_handler,
                )
                await handler(event, recall_status_change_event.data)
            case RecallBotEventTypes.BOT_REALTIME_TRANSCRIPTION.value:
                # Realtime transcription has a different convention than the rest of the event status handlers:
                # There are query parameters used for validation, and event data type is different.
                recall_bot_event = cast(
                    RecallRealtimeTranscriptionEvent, recall_bot_event
                )
                await self.meeting_realtime_service.bot_realtime_transcript_event(
                    event, recall_bot_event.data, query_params
                )
            case RecallBotEventTypes.BOT_REALTIME_PARTICIPANT_JOIN:
                recall_bot_event = cast(
                    RecallRealtimeParticipantJoinEvent, recall_bot_event
                )
                await self.meeting_realtime_service.bot_realtime_participant_join_event(
                    event, recall_bot_event.data, query_params
                )
            case RecallBotEventTypes.BOT_REALTIME_ACTIVE_SPEAKER_NOTIFY.value:
                # We intentionally ignore active speaker events
                pass
            case _:
                logger.info(f"Unhandled event type: {event}")

    async def joining_call_status(self, event: str, data: RecallBotEventData) -> None:
        await self.meeting_service.bot_joining_event(event=event, data=data)

    async def in_waiting_room_status(
        self, event: str, data: RecallBotEventData
    ) -> None:
        await self.meeting_service.bot_in_waiting_room_event(event, data)

    async def in_call_recording_status(
        self, event: str, data: RecallBotEventData
    ) -> None:
        await self.meeting_service.bot_in_call_recording_event(event, data)

    async def recording_permission_denied_status(
        self, event: str, data: RecallBotEventData
    ) -> None:
        await self.meeting_service.bot_recording_permission_denied(
            event=event, data=data
        )

    async def call_ended_status(self, event: str, data: RecallBotEventData) -> None:
        await self.meeting_service.bot_call_ended_event(event, data)

    async def done_status(self, event: str, data: RecallBotEventData) -> None:
        await self.meeting_service.bot_done_event(event, data)

    async def analysis_done_status(self, event: str, data: RecallBotEventData) -> None:
        meeting_dto = await self.meeting_service.bot_analysis_done_event(event, data)

        if settings.enable_insights_job_invocation and meeting_dto:
            try:
                logger.bind(meeting_id=meeting_dto.meeting_id).info(
                    "Triggering meeting analysis job"
                )
                client = await get_temporal_client()
                await client.start_workflow(
                    MeetingCompletionWorkflow.run,
                    args=[
                        MeetingCompletionWorkflowData(
                            meeting_reference_id=meeting_dto.meeting.reference_id,
                            meeting_reference_id_type=meeting_dto.meeting.reference_id_type,
                            organization_id=meeting_dto.meeting.organization_id,
                        ),
                    ],
                    id=f"meeting_completion_workflow_{meeting_dto.meeting.id}",
                    task_queue=MEETING_TASK_QUEUE,
                )
            except Exception as e:
                logger.bind(external_bot_id=data.bot_id).error(
                    "Failed to trigger meeting insight analysis", exc_info=e
                )

    async def analysis_failed_status(
        self, event: str, data: RecallBotEventData
    ) -> None:
        await self.meeting_service.bot_analysis_failed_event(event, data)

    async def default_handler(self, event: str, data: RecallBotEventData) -> None:
        await self.meeting_service.bot_default_event_handler(event, data)


def meeting_webhook_service_api_factory(request: Request) -> MeetingWebhookService:
    meeting_service = meeting_service_factory(request)
    meeting_realtime_service = meeting_realtime_service_by_request(request)
    return MeetingWebhookService(
        meeting_service=meeting_service,
        meeting_realtime_service=meeting_realtime_service,
    )
