from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field


class MeetingFilter(BaseModel):
    """Filter criteria for meetings"""

    start_date: datetime | None = Field(
        None, description="Filter meetings after this date"
    )
    end_date: datetime | None = Field(
        None, description="Filter meetings before this date"
    )
    organization_id: UUID | None = Field(None, description="Filter by organization ID")
    participant_email: str | None = Field(
        None, description="Filter by participant email"
    )
    account_id: UUID | None = Field(None, description="Filter meetings by account ID")
    contact_id: UUID | None = Field(None, description="Filter meetings by contact ID")
    pipeline_id: UUID | None = Field(None, description="Filter meetings by pipeline ID")
    meeting_ids: list[UUID] | None = Field(
        None, description="Filter by specific meeting IDs"
    )


class TranscriptCitation(BaseModel):
    """A citation from a transcript with its source and location"""

    text: str = Field(..., description="The cited text from the transcript")
    speaker: str | None = Field(None, description="The speaker of the cited text")
    line_number: int = Field(..., description="The line number in the transcript")
    meeting_id: UUID = Field(..., description="ID of the source meeting")


class MeetingQueryResult(BaseModel):
    """Result from querying a single meeting transcript"""

    meeting_id: UUID = Field(..., description="ID of the meeting")
    transcript_id: UUID = Field(..., description="ID of the transcript")
    answer: str = Field(
        ..., description="Answer extracted from this meeting's transcript"
    )
    confidence: float = Field(
        ..., description="Confidence score for this answer", ge=0, le=1
    )
    citations: list[TranscriptCitation] = Field(
        ..., description="Citations from the meeting transcript supporting this answer"
    )


class MeetingSummaryWithSource(BaseModel):
    """Summary of a meeting with citations from source transcripts"""

    summary: str = Field(..., description="Summary of the meeting")
    citations: list[TranscriptCitation] = Field(
        ..., description="Citations from meeting transcripts supporting this summary"
    )


class MeetingQueryResponse(BaseModel):
    """Combined response from querying multiple meetings"""

    results: list[MeetingQueryResult] = Field(
        ..., description="Results from each meeting"
    )
    summary: MeetingSummaryWithSource = Field(
        ..., description="Overall summary combining insights from all meetings"
    )
