import json
import uuid
from datetime import datetime
from enum import StrEnum
from typing import Any, assert_never, cast, override
from uuid import UUID

from fastapi import Request

from salestech_be.common.exception import (
    InvalidArgumentError,
    ResourceNotFoundError,
    UnprocessableEntity,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    acompletion,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import UNSET, BasePatchRequest
from salestech_be.core.account.service.account_query_service import (
    AccountQueryService,
    get_account_query_service,
)
from salestech_be.core.ai.insights.llm_calls.generate_objection_insights_from_conversation_llm_call import (
    generate_objection_insights_from_conversation_llm_call,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest
from salestech_be.core.ai.workflows.schema import IntelTriggerObjectType
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.extraction_prompt.service.config_extraction_service import (
    ExtractionConfigService,
    extraction_config_service_factory_general,
)
from salestech_be.core.extraction_prompt.type.shared_type import ExtractionType, Feature
from salestech_be.core.ff.feature_flag_service import get_feature_flag_service
from salestech_be.core.ff.types import FeatureFlagRequest
from salestech_be.core.meeting.dto.meeting_insight_dto import InsightDTO
from salestech_be.core.meeting.meeting_subdomain_service import MeetingSubDomainService
from salestech_be.core.meeting.types.meeting_types_v2 import (
    SubServiceMeetingPermission,
)
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaExtractionSourceObjectId,
    CriteriaExtractionSourceObjectType,
)
from salestech_be.core.organization.service.organization_service import (
    OrganizationService,
    get_organization_service_general,
)
from salestech_be.core.prompt.types import PromptUseCase
from salestech_be.core.task.service.task_v2_service import (
    TaskV2Service,
    get_task_v2_service_general,
)
from salestech_be.core.transcript.transcript_extraction_service_v2 import (
    TranscriptExtractionServiceV2,
    transcript_extraction_from_engine_v2,
)
from salestech_be.core.transcript.types import TranscriptContainer
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.db.dao.insight_repository import InsightRepository
from salestech_be.db.dao.meeting_repository import MeetingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.extraction_config import (
    ExtractionConfigDTO,
    ExtractionSection,
)
from salestech_be.db.models.insight import (
    Insight,
    InsightAuthorType,
    InsightReferenceIdType,
    InsightSection,
    InsightSourceType,
)
from salestech_be.db.models.meeting import (
    Meeting,
)
from salestech_be.integrations.langchain.insights.schema import (
    ActionableInsight,
    InsightListFromLLM,
    InsightType,
    SentimentInsight,
    TInsight,
    TypedInsight,
)
from salestech_be.integrations.langchain.insights.schema import (
    Insight as InsightSchema,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.common.container import (
    DeleteEntityResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.meeting.schema import (
    CreateInsightsFieldRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchInsightRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger("meeting_insight_service")

MINIMUM_TRANSCRIPT_SENTENCES_FOR_INSIGHTS = 5

langfuse_prompt_service = get_langfuse_prompt_service()


class InsightNamingPolicy(StrEnum):
    EXTRACTION_SECTION_NAME = "extraction_section_name"
    EXTRACTED_INSIGHT_NAME = "extracted_insight_name"


class MeetingInsightService(MeetingSubDomainService[InsightDTO]):
    def __init__(
        self,
        config_extraction_service: ExtractionConfigService,
        transcript_extraction_service_v2: TranscriptExtractionServiceV2,
        insight_repository: InsightRepository,
        meeting_repository: MeetingRepository,
        task_v2_service: TaskV2Service,
        contact_query_service: ContactQueryService,
        user_service: UserService,
        organization_service: OrganizationService,
        account_query_service: AccountQueryService,
    ) -> None:
        super().__init__(
            feature_flag_service=get_feature_flag_service(),
            meeting_repository=meeting_repository,
        )
        self.config_extraction_service = config_extraction_service
        self.transcript_extraction_service_v2 = transcript_extraction_service_v2
        self.insight_repository = insight_repository
        self.meeting_repository = meeting_repository
        self.task_v2_service = task_v2_service
        self.contact_query_service = contact_query_service
        self.user_service = user_service
        self.organization_service = organization_service
        self.account_query_service = account_query_service

    async def create_meeting_insights_from_extraction_config(
        self, organization_id: UUID, meeting_id: UUID, feature: Feature
    ) -> list[InsightDTO]:
        """
        Wrapper that creates individual insights based on extraction config.
        """
        extraction_config_dtos = (
            await self.config_extraction_service.get_extraction_config_by_feature(
                organization_id=organization_id, feature=feature
            )
        )
        results = []
        for config in extraction_config_dtos:
            results.append(
                await self._create_meeting_insights_from_extraction_config(
                    organization_id=organization_id,
                    meeting_id=meeting_id,
                    extraction_config_dto=config,
                )
            )
        return results

    async def _create_meeting_insights_from_extraction_config(
        self,
        organization_id: UUID,
        meeting_id: UUID,
        extraction_config_dto: ExtractionConfigDTO,
    ) -> InsightDTO:
        """
        Creates insights based on extraction config.  This results in empty
        insight_sections, with no fields.  This is used for use cases where we have no
        analysis to use as the basis for insights, e.g. unrecorded meeting (no
        transcript to rely on).
        """
        logger.bind(organization_id=organization_id, meeting_id=meeting_id).info(
            "Creating default insights from extraction config"
        )
        insight_dto = self.map_extraction_config_to_insight_section(
            organization_id=organization_id,
            source_type=InsightSourceType.from_extraction_type(
                extraction_config_dto.extraction_section.type
            ),
            meeting_id=meeting_id,
            extraction_config_dto=extraction_config_dto,
            user_id=None,
        )
        return await self._persist_insight_dto(insight_dto)

    async def _persist_insight_dto(self, insight_dto: InsightDTO) -> InsightDTO:
        persisted_insight_section = await self.insight_repository.insert(
            insight_dto.insight_section
        )
        persisted_insights: list[Insight] = []
        for insight in insight_dto.insights:
            persisted_insights.append(await self.insight_repository.insert(insight))
        return InsightDTO(
            insight_section=persisted_insight_section,
            insights=persisted_insights,
        )

    async def _create_empty_insight_sections(
        self,
        meeting: Meeting,
        organization_id: UUID,
        transcript_container: TranscriptContainer,
    ) -> list[InsightDTO]:
        existing_insights = await self.insight_repository.get_insight_sections_by_reference_id_and_organization_id(
            reference_id=meeting.id,
            organization_id=organization_id,
            reference_type=InsightReferenceIdType.MEETING,
        )
        if not existing_insights:
            logger.bind(
                meeting_id=meeting.id,
                organization_id=organization_id,
                transcript_sentence_count=len(
                    transcript_container.transcript.sentences
                ),
            ).info(
                "Transcript insufficient for insights, creating empty sections instead of llm analysis"
            )
            return await self.create_meeting_insights_from_extraction_config(
                organization_id=organization_id,
                meeting_id=meeting.id,
                feature=Feature.POST_CALL_ANALYTICS,
            )
        else:
            logger.info(
                "Transcript is sufficient for insights, insights already exist - skipping empty section creation"
            )
            return []

    async def extract_insights_from_transcript(
        self,
        organization_id: UUID,
        meeting: Meeting,
        transcript_container: TranscriptContainer,
    ) -> list[InsightDTO]:
        logger.bind(meeting_id=meeting.id, organization_id=organization_id).info(
            "Extracting insights from meeting transcript"
        )

        if (
            len(transcript_container.transcript.sentences)
            < MINIMUM_TRANSCRIPT_SENTENCES_FOR_INSIGHTS
        ):
            return await self._create_empty_insight_sections(
                meeting=meeting,
                organization_id=organization_id,
                transcript_container=transcript_container,
            )

        existing_insights = await self.insight_repository.get_insight_sections_by_reference_id_and_organization_id(
            reference_id=meeting.id,
            organization_id=organization_id,
            reference_type=InsightReferenceIdType.MEETING,
        )
        existing_by_config_section_id = {
            i.extraction_section_id: i for i in existing_insights
        }

        # Collect all insights from different extractors
        all_insights = []
        # Add meeting brief insights extraction
        meeting_brief_insights = (
            await self._extract_meeting_brief_insights_from_transcript(
                organization_id=organization_id,
                meeting=meeting,
                transcript_container=transcript_container,
                existing_insights_by_config_section_id=existing_by_config_section_id,
            )
        )
        all_insights.extend(meeting_brief_insights)

        # Add sentiment insights extraction
        sentiment_insights = await self._extract_sentiment_insights_from_transcript(
            organization_id=organization_id,
            meeting=meeting,
            transcript_container=transcript_container,
            existing_insights_by_config_section_id=existing_by_config_section_id,
        )
        all_insights.extend(sentiment_insights)

        # Add summary insights extraction
        summary_insights = await self._extract_summary_insights_from_transcript(
            organization_id=organization_id,
            meeting=meeting,
            transcript_container=transcript_container,
            existing_insights_by_config_section_id=existing_by_config_section_id,
        )
        all_insights.extend(summary_insights)

        # Add outline insights extraction
        outline_insights = await self._extract_topic_outline_insights_from_transcript(
            organization_id=organization_id,
            meeting=meeting,
            transcript_container=transcript_container,
            existing_insights_by_config_section_id=existing_by_config_section_id,
        )
        all_insights.extend(outline_insights)

        # Add field extraction insights
        field_extraction_insights = (
            await self._extract_field_extraction_insights_from_transcript(
                organization_id=organization_id,
                meeting=meeting,
                transcript_container=transcript_container,
                existing_insights_by_config_section_id=existing_by_config_section_id,
            )
        )
        all_insights.extend(field_extraction_insights)

        # Add objection insights extraction
        if settings.enable_generate_objections_from_conversation:
            objection_insights = await self._extract_objection_insights_from_transcript(
                organization_id=organization_id,
                meeting=meeting,
                transcript_container=transcript_container,
                existing_insights_by_config_section_id=existing_by_config_section_id,
            )

            all_insights.extend(objection_insights)

            # Start intel workflow for each objection insight
            if (
                settings.enable_generate_tasks_from_objection_insights
                or str(organization_id)
                in settings.enable_generate_tasks_from_objection_insights_org_ids
            ):
                for dto in objection_insights:
                    for persisted_insight in dto.insights:
                        try:
                            now = zoned_utc_now()
                            workflow_id = f"intel-{persisted_insight.id}-{IntelTriggerObjectType.OBJECTION.value}-{organization_id}-{now.strftime('%Y%m%d%H%M%S')}"
                            await start_intel_workflow(
                                organization_id=organization_id,
                                object_id=persisted_insight.id,
                                object_type=IntelTriggerObjectType.OBJECTION,
                                account_ids=[meeting.account_id]
                                if meeting.account_id
                                else None,
                                pipeline_id=meeting.pipeline_id,
                                workflow_id=workflow_id,
                            )
                        except Exception as e:
                            logger.error(
                                "[meeting_insight_service] Error starting intel workflow for objection insight",
                                error=e,
                            )

        # Start task generation workflow
        try:
            await start_intel_workflow(
                organization_id=organization_id,
                object_id=meeting.id,
                object_type=IntelTriggerObjectType.MEETING,
                account_ids=[meeting.account_id] if meeting.account_id else None,
                pipeline_id=meeting.pipeline_id,
            )
        except Exception as e:
            logger.bind(organization_id=organization_id, meeting_id=meeting.id).error(
                "Error starting intel workflow from insights extraction", exc_info=e
            )

        ff_service = get_feature_flag_service()
        is_stage_criteria_enabled = await ff_service.is_enabled(
            request=FeatureFlagRequest(
                flag_key="enable-stage-criteria-generation",
                organization_id=organization_id,
            ),
        )

        if meeting.pipeline_id and is_stage_criteria_enabled:
            await start_stage_criteria_workflow(
                organization_id=organization_id,
                pipeline_id=meeting.pipeline_id,
                user_id=UUID(settings.intel_hardcoded_user_id),
                source_object=CriteriaExtractionSourceObjectId(
                    object_type=CriteriaExtractionSourceObjectType.MEETING,
                    object_id=meeting.id,
                ),
            )
        return all_insights

    def _create_insight_section(
        self,
        extraction_section: ExtractionSection,
        meeting_id: UUID,
        organization_id: UUID,
        now: datetime,
    ) -> InsightSection:
        return InsightSection(
            id=uuid.uuid4(),
            source_type=InsightSourceType.TRANSCRIPTION,
            reference_type=InsightReferenceIdType.MEETING,
            reference_id=meeting_id,
            name=extraction_section.name,
            description=extraction_section.description,
            extraction_section_version=extraction_section.version,
            extraction_section_id=extraction_section.id,
            insight_type=extraction_section.type,
            created_at=now,
            updated_at=now,
            organization_id=organization_id,
            created_by_user_id=None,
            updated_by_user_id=None,
            deleted_by_user_id=None,
            deleted_at=None,
            user_feedback=None,
        )

    async def _persist_insight_dtos(
        self,
        organization_id: UUID,
        meeting_id: UUID,
        extraction_section: ExtractionSection,
        extracted_insights: list[TInsight],
        insight_naming_policy: InsightNamingPolicy,
    ) -> list[InsightDTO]:
        now = zoned_utc_now()

        # Check if there's an existing empty insight section for this extraction section
        existing_sections = await self.insight_repository.get_insight_sections_by_reference_id_and_organization_id(
            reference_id=meeting_id,
            organization_id=organization_id,
            reference_type=InsightReferenceIdType.MEETING,
        )
        existing_section = next(
            (
                section
                for section in existing_sections
                if section.extraction_section_id == extraction_section.id
            ),
            None,
        )

        # Determine which section to use
        if existing_section:
            # Verify the section is empty
            existing_insights = (
                await self.insight_repository.get_insights_by_section_id(
                    organization_id=organization_id,
                    insight_section_id=existing_section.id,
                )
            )
            if not existing_insights:
                # Reuse the existing empty section
                insight_section = existing_section
            else:
                # Create a new section if the existing one has insights
                insight_section = self._create_insight_section(
                    extraction_section=extraction_section,
                    meeting_id=meeting_id,
                    organization_id=organization_id,
                    now=now,
                )
        else:
            # Create a new section if none exists
            insight_section = self._create_insight_section(
                extraction_section=extraction_section,
                meeting_id=meeting_id,
                organization_id=organization_id,
                now=now,
            )

        # Only upsert if it's a new section
        inserted_section = (
            insight_section
            if existing_section and insight_section.id == existing_section.id
            else not_none(await self.insight_repository.upsert_pk(insight_section))
        )

        # Create insights with the correct section ID
        dtos = [
            MeetingInsightService.map_to_insight_dto(
                insight=extracted_insight,
                rank=rank,
                insight_name=extracted_insight.insight_name
                if insight_naming_policy == InsightNamingPolicy.EXTRACTED_INSIGHT_NAME
                and extracted_insight.insight_name
                else extraction_section.name,
                insight_description=extraction_section.description,
                organization_id=organization_id,
                source_type=InsightSourceType.TRANSCRIPTION,
                insight_section=inserted_section,
                meeting_id=meeting_id,
                user_id=None,
            )
            for rank, extracted_insight in enumerate(extracted_insights)
        ]

        # Insert the insights
        inserted_dtos = []
        for dto in dtos:
            inserted_insights = []
            for insight in dto.insights:
                # Create a new insight with the correct section ID
                new_insight = Insight(
                    id=insight.id,
                    insight_section_id=inserted_section.id,
                    organization_id=insight.organization_id,
                    reference_type=insight.reference_type,
                    reference_id=insight.reference_id,
                    contact_id=insight.contact_id,
                    insight_name=insight.insight_name,
                    insight_description=insight.insight_description,
                    created_by_user_id=insight.created_by_user_id,
                    updated_by_user_id=insight.updated_by_user_id,
                    deleted_by_user_id=insight.deleted_by_user_id,
                    source_type=insight.source_type,
                    tags=insight.tags,
                    brief_values=insight.brief_values,
                    detailed_explanation=insight.detailed_explanation,
                    transcript_locations=insight.transcript_locations,
                    source_locations=insight.source_locations,
                    created_at=insight.created_at,
                    updated_at=insight.updated_at,
                    deleted_at=insight.deleted_at,
                    author_type=insight.author_type,
                    user_feedback=insight.user_feedback,
                    approved_at=insight.approved_at,
                    approved_by_user_id=insight.approved_by_user_id,
                    metadata=insight.metadata,
                    rank=insight.rank,
                    version=insight.version,
                )
                inserted_insights.append(
                    await self.insight_repository.insert(new_insight)
                )
            inserted_dtos.append(
                InsightDTO(
                    insight_section=inserted_section,
                    insights=inserted_insights,
                )
            )
        return inserted_dtos

    async def _requires_extraction(
        self,
        organization_id: UUID,
        meeting_id: UUID,
        extraction_section: ExtractionSection | None,
        existing_insights_by_config_section_id: dict[UUID, InsightSection],
    ) -> bool:
        if not extraction_section:
            logger.bind(meeting_id=meeting_id, organization_id=organization_id).info(
                "No extraction section found, skipping extraction"
            )
            return False

        existing_section = existing_insights_by_config_section_id.get(
            extraction_section.id
        )
        if settings.enable_existing_insight_check and existing_section:
            # Check if there are any non-deleted insights for this section
            insights = await self.insight_repository.get_insights_by_section_id(
                organization_id=organization_id,
                insight_section_id=existing_section.id,
            )
            if insights:
                logger.bind(
                    meeting_id=meeting_id, organization_id=organization_id
                ).info(
                    "Non-empty insights already exist for meeting for section, skipping extraction"
                )
                return False
            else:
                logger.bind(
                    meeting_id=meeting_id, organization_id=organization_id
                ).info(
                    "Empty insights exist for meeting for section, proceeding with extraction"
                )

        return True

    async def generate_seller_contacts_prompt_input(
        self, organization_id: UUID, meeting: Meeting
    ) -> str:
        user_ids_from_attendees = {
            attendee.user_id
            for attendee in meeting.attendee_or_empty_list()
            if attendee.user_id
        }
        seller_contacts = ""
        if user_ids_from_attendees:
            users = await self.user_service.list_users_v2(
                organization_id=organization_id,
                only_include_user_ids=user_ids_from_attendees,
                active_users_only=True,
            )
            if users:
                seller_contacts = "\n".join(
                    [
                        f"""<seller_contact>
                <first_name>{c.first_name}</first_name>
                <last_name>{c.last_name}</last_name>
                <phone>{c.phone_number}</phone>
                <email>{c.email}</email>
                <id>{c.id}</id>
                </seller_contact>"""
                        for c in users
                    ]
                )
        return seller_contacts

    async def _generate_customer_contacts_prompt_input(
        self, organization_id: UUID, meeting: Meeting
    ) -> str:
        contact_ids_from_attendees = {
            attendee.contact_id
            for attendee in meeting.attendee_or_empty_list()
            if attendee.contact_id
        }

        # get customer contacts
        customer_contacts = ""
        if contact_ids_from_attendees:
            # for meetings, use attendees to get contacts first and fallback to contacts from pipeline
            meeting_contacts = await self.contact_query_service.list_contacts_v2(
                organization_id=organization_id,
                only_include_contact_ids=contact_ids_from_attendees,
            )
            if meeting_contacts:
                contacts = meeting_contacts

                customer_contacts = "\n".join(
                    [
                        f"""<customer_contact>
                <first_name>{c.first_name}</first_name>
                <last_name>{c.last_name}</last_name>
                <phone>{c.primary_phone_number}</phone>
                <email>{c.primary_email}</email>
                <id>{c.id}</id>
                </customer_contact>"""
                        for c in contacts
                    ]
                )
        return customer_contacts

    async def _extract_meeting_brief_insights_from_transcript(
        self,
        organization_id: UUID,
        meeting: Meeting,
        transcript_container: TranscriptContainer,
        existing_insights_by_config_section_id: dict[UUID, InsightSection],
    ) -> list[InsightDTO]:
        meeting_brief_extraction_section = await self.config_extraction_service.get_extraction_section_by_feature_and_name(
            feature=Feature.POST_CALL_ANALYTICS,
            name="Meeting Brief",
            organization_id=organization_id,
        )

        if not await self._requires_extraction(
            organization_id,
            meeting.id,
            meeting_brief_extraction_section,
            existing_insights_by_config_section_id,
        ):
            return []

        logger.bind(meeting_id=meeting.id, organization_id=organization_id).info(
            "Extracting meeting brief insights"
        )

        if settings.enable_langfuse_for_meeting_insights:
            langfuse_session_id = (
                f"insights:{IntelTriggerObjectType.MEETING}:{meeting.id}"
            )

            # get customer contacts
            customer_contacts = await self._generate_customer_contacts_prompt_input(
                organization_id=organization_id, meeting=meeting
            )

            # get seller contacts
            seller_contacts = await self.generate_seller_contacts_prompt_input(
                organization_id=organization_id, meeting=meeting
            )

            organization = await self.organization_service.get_organization_by_id(
                organization_id
            )

            account = None
            if meeting.account_id:
                account = await self.account_query_service.get_account_v2(
                    account_id=meeting.account_id,
                    organization_id=organization_id,
                )

            prompt_variables = {
                "transcript": transcript_container.transcript.compact(),
                "seller_contacts": seller_contacts,
                "seller_company_name": organization.display_name
                if organization
                else "",
                "customer_contacts": customer_contacts,
                "customer_company_name": account.display_name if account else "",
                "ends_at": str(meeting.ended_at) or str(meeting.ends_at),
                "num_insights": "1",
            }

            prompt_obj = await langfuse_prompt_service.get_prompt(
                request=PromptRequest(
                    prompt_name=PromptEnum.GENERATE_BRIEF_INSIGHTS_FROM_CONVERSATION,
                    variables=prompt_variables,
                )
            )

            response = await acompletion(
                model=prompt_obj.get_model(),
                messages=prompt_obj.messages,
                temperature=0,
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "create_brief_insights",
                            "description": "A tool that creates one or more meeting brief insights with descriptions.",
                            "parameters": InsightListFromLLM.model_json_schema(),
                        },
                    }
                ],
                tool_choice="create_brief_insights",
                metadata=LLMTraceMetadata(
                    trace_name="insights.meeting_brief",
                    session_id=langfuse_session_id,
                    custom_fields=ReeTraceMetadata(
                        organization_id=str(organization_id),
                        prompt_use_case=PromptUseCase.BRIEF_INSIGHTS,
                    ),
                ),
            )

            if not response.choices:
                return []
            tool_calls = response.tool_calls
            if not tool_calls:
                return []

            meeting_brief_insights = []

            for tool_call in tool_calls:
                if tool_call.function and tool_call.function.arguments:
                    json_args = json.loads(tool_call.function.arguments)
                    try:
                        for insight in json_args.get("insights", []):
                            meeting_brief_insights.append(
                                InsightSchema(
                                    insight_name=insight.get("insight_name", ""),
                                    insight=insight.get("insight", ""),
                                    line_numbers=insight.get("line_numbers", []),
                                    keyword_tags=insight.get("keyword_tags", []),
                                    start_timestamp=insight.get(
                                        "start_timestamp", None
                                    ),
                                )
                            )
                    except Exception as e:
                        logger.error(
                            "[meeting_insight_service] Error parsing tool call arguments",
                            error=e,
                        )
        else:
            meeting_brief_insights = (
                await self.transcript_extraction_service_v2.extract_meeting_brief(
                    organization_id=organization_id,
                    compacted_transcript=transcript_container.transcript.compact(),
                    meeting=meeting,
                )
            )

        return await self._persist_insight_dtos(
            organization_id=organization_id,
            meeting_id=meeting.id,
            extraction_section=not_none(meeting_brief_extraction_section),
            extracted_insights=meeting_brief_insights,
            insight_naming_policy=InsightNamingPolicy.EXTRACTION_SECTION_NAME,
        )

    async def _extract_sentiment_insights_from_transcript(
        self,
        organization_id: UUID,
        meeting: Meeting,
        transcript_container: TranscriptContainer,
        existing_insights_by_config_section_id: dict[UUID, InsightSection],
    ) -> list[InsightDTO]:
        sentiment_extraction_section = await self.config_extraction_service.get_extraction_section_by_feature_and_name(
            feature=Feature.POST_CALL_ANALYTICS,
            name="Sentiment",
            organization_id=organization_id,
        )

        if not await self._requires_extraction(
            organization_id,
            meeting.id,
            sentiment_extraction_section,
            existing_insights_by_config_section_id,
        ):
            return []

        if settings.enable_langfuse_for_meeting_insights:
            langfuse_session_id = (
                f"insights:{IntelTriggerObjectType.MEETING}:{meeting.id}"
            )

            # get customer contacts
            customer_contacts = await self._generate_customer_contacts_prompt_input(
                organization_id=organization_id, meeting=meeting
            )

            # get seller contacts
            seller_contacts = await self.generate_seller_contacts_prompt_input(
                organization_id=organization_id, meeting=meeting
            )

            organization = await self.organization_service.get_organization_by_id(
                organization_id
            )

            account = None
            if meeting.account_id:
                account = await self.account_query_service.get_account_v2(
                    account_id=meeting.account_id,
                    organization_id=organization_id,
                )

            prompt_variables = {
                "transcript": transcript_container.transcript.compact(),
                "seller_contacts": seller_contacts,
                "seller_company_name": organization.display_name
                if organization
                else "",
                "customer_contacts": customer_contacts,
                "customer_company_name": account.display_name if account else "",
                "ends_at": str(meeting.ended_at) or str(meeting.ends_at),
                "num_insights": "1",
            }

            prompt_obj = await langfuse_prompt_service.get_prompt(
                request=PromptRequest(
                    prompt_name=PromptEnum.GENERATE_SENTIMENT_INSIGHTS_FROM_CONVERSATION,
                    variables=prompt_variables,
                )
            )

            response = await acompletion(
                model=prompt_obj.get_model(),
                messages=prompt_obj.messages,
                temperature=0,
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "create_sentiment_insights",
                            "description": "A tool that creates one or more meeting sentiment insights with descriptions.",
                            "parameters": SentimentInsight.model_json_schema(),
                        },
                    }
                ],
                tool_choice="create_sentiment_insights",
                metadata=LLMTraceMetadata(
                    trace_name="insights.meeting_sentiment",
                    session_id=langfuse_session_id,
                    custom_fields=ReeTraceMetadata(
                        organization_id=str(organization_id),
                        prompt_use_case=PromptUseCase.SENTIMENT_INSIGHTS,
                    ),
                ),
            )

            if not response.choices:
                return []
            tool_calls = response.tool_calls
            if not tool_calls:
                return []

            sentiments = []

            for tool_call in tool_calls:
                if tool_call.function and tool_call.function.arguments:
                    json_args = json.loads(tool_call.function.arguments)
                    try:
                        sentiments.append(
                            SentimentInsight(
                                insight_name=json_args.get("insight_name", ""),
                                insight=json_args.get("insight", ""),
                                polarity=json_args.get("polarity", None),
                                emotion=json_args.get("emotion", []),
                                line_numbers=json_args.get("line_numbers", []),
                                keyword_tags=json_args.get("keyword_tags", []),
                            )
                        )
                    except Exception as e:
                        logger.error(
                            "[meeting_insight_service] Error parsing tool call arguments",
                            error=e,
                        )

        else:
            sentiments = await self.transcript_extraction_service_v2.extract_sentiment(
                organization_id=organization_id,
                compacted_transcript=transcript_container.transcript.compact(),
                meeting=meeting,
            )

        return await self._persist_insight_dtos(
            organization_id=organization_id,
            meeting_id=meeting.id,
            extraction_section=not_none(sentiment_extraction_section),
            extracted_insights=sentiments,
            insight_naming_policy=InsightNamingPolicy.EXTRACTION_SECTION_NAME,
        )

    async def _extract_summary_insights_from_transcript(
        self,
        organization_id: UUID,
        meeting: Meeting,
        transcript_container: TranscriptContainer,
        existing_insights_by_config_section_id: dict[UUID, InsightSection],
    ) -> list[InsightDTO]:
        summary_extraction_section = await self.config_extraction_service.get_extraction_section_by_feature_and_name(
            feature=Feature.POST_CALL_ANALYTICS,
            name="Summary",
            organization_id=organization_id,
        )

        if not await self._requires_extraction(
            organization_id,
            meeting.id,
            summary_extraction_section,
            existing_insights_by_config_section_id,
        ):
            return []

        logger.bind(meeting_id=meeting.id, organization_id=organization_id).info(
            "Extracting summary insights"
        )

        if settings.enable_langfuse_for_meeting_insights:
            langfuse_session_id = (
                f"insights:{IntelTriggerObjectType.MEETING}:{meeting.id}"
            )

            # get customer contacts
            customer_contacts = await self._generate_customer_contacts_prompt_input(
                organization_id=organization_id, meeting=meeting
            )

            # get seller contacts
            seller_contacts = await self.generate_seller_contacts_prompt_input(
                organization_id=organization_id, meeting=meeting
            )

            organization = await self.organization_service.get_organization_by_id(
                organization_id
            )

            account = None
            if meeting.account_id:
                account = await self.account_query_service.get_account_v2(
                    account_id=meeting.account_id,
                    organization_id=organization_id,
                )

            prompt_variables = {
                "transcript": transcript_container.transcript.compact(),
                "seller_contacts": seller_contacts,
                "seller_company_name": organization.display_name
                if organization
                else "",
                "customer_contacts": customer_contacts,
                "customer_company_name": account.display_name if account else "",
                "ends_at": str(meeting.ended_at) or str(meeting.ends_at),
                "num_insights": "5 to 10",
            }

            prompt_obj = await langfuse_prompt_service.get_prompt(
                request=PromptRequest(
                    prompt_name=PromptEnum.GENERATE_SUMMARY_INSIGHTS_FROM_CONVERSATION,
                    variables=prompt_variables,
                )
            )

            response = await acompletion(
                model=prompt_obj.get_model(),
                messages=prompt_obj.messages,
                temperature=0,
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "create_summary_insights",
                            "description": "A tool that creates one or more meeting summary insights with descriptions.",
                            "parameters": InsightListFromLLM.model_json_schema(),
                        },
                    }
                ],
                tool_choice="create_summary_insights",
                metadata=LLMTraceMetadata(
                    trace_name="insights.meeting_summary",
                    session_id=langfuse_session_id,
                    custom_fields=ReeTraceMetadata(
                        organization_id=str(organization_id),
                        prompt_use_case=PromptUseCase.SUMMARY_INSIGHTS,
                    ),
                ),
            )

            if not response.choices:
                return []
            tool_calls = response.tool_calls
            if not tool_calls:
                return []

            summary_insights = []

            for tool_call in tool_calls:
                if tool_call.function and tool_call.function.arguments:
                    json_args = json.loads(tool_call.function.arguments)
                    try:
                        for insight in json_args.get("insights", []):
                            summary_insights.append(
                                InsightSchema(
                                    insight_name=insight.get("insight_name", ""),
                                    insight=insight.get("insight", ""),
                                    line_numbers=insight.get("line_numbers", []),
                                    keyword_tags=insight.get("keyword_tags", []),
                                    start_timestamp=insight.get(
                                        "start_timestamp", None
                                    ),
                                )
                            )
                    except Exception as e:
                        logger.error(
                            "[meeting_insight_service] Error parsing tool call arguments",
                            error=e,
                        )
        else:
            summary_insights = (
                await self.transcript_extraction_service_v2.extract_summary(
                    organization_id=organization_id,
                    compacted_transcript=transcript_container.transcript.compact(),
                    meeting=meeting,
                )
            )

        return await self._persist_insight_dtos(
            organization_id=organization_id,
            meeting_id=meeting.id,
            extraction_section=not_none(summary_extraction_section),
            extracted_insights=summary_insights,
            insight_naming_policy=InsightNamingPolicy.EXTRACTION_SECTION_NAME,
        )

    async def _extract_topic_outline_insights_from_transcript(
        self,
        organization_id: UUID,
        meeting: Meeting,
        transcript_container: TranscriptContainer,
        existing_insights_by_config_section_id: dict[UUID, InsightSection],
    ) -> list[InsightDTO]:
        outline_extraction_section = await self.config_extraction_service.get_extraction_section_by_feature_and_name(
            feature=Feature.POST_CALL_ANALYTICS,
            name="Outline",
            organization_id=organization_id,
        )

        if not await self._requires_extraction(
            organization_id,
            meeting.id,
            outline_extraction_section,
            existing_insights_by_config_section_id,
        ):
            return []

        logger.bind(meeting_id=meeting.id, organization_id=organization_id).info(
            "Extracting outline insights"
        )

        if settings.enable_langfuse_for_meeting_insights:
            langfuse_session_id = (
                f"insights:{IntelTriggerObjectType.MEETING}:{meeting.id}"
            )

            # get customer contacts
            customer_contacts = await self._generate_customer_contacts_prompt_input(
                organization_id=organization_id, meeting=meeting
            )

            # get seller contacts
            seller_contacts = await self.generate_seller_contacts_prompt_input(
                organization_id=organization_id, meeting=meeting
            )

            organization = await self.organization_service.get_organization_by_id(
                organization_id
            )

            account = None
            if meeting.account_id:
                account = await self.account_query_service.get_account_v2(
                    account_id=meeting.account_id,
                    organization_id=organization_id,
                )

            prompt_variables = {
                "transcript": transcript_container.transcript.compact(),
                "seller_contacts": seller_contacts,
                "seller_company_name": organization.display_name
                if organization
                else "",
                "customer_contacts": customer_contacts,
                "customer_company_name": account.display_name if account else "",
                "ends_at": str(meeting.ended_at) or str(meeting.ends_at),
                "num_insights": "1 to 30",
            }

            prompt_obj = await langfuse_prompt_service.get_prompt(
                request=PromptRequest(
                    prompt_name=PromptEnum.GENERATE_TOPIC_OUTLINE_INSIGHTS_FROM_CONVERSATION,
                    variables=prompt_variables,
                )
            )

            response = await acompletion(
                model=prompt_obj.get_model(),
                messages=prompt_obj.messages,
                temperature=0,
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "create_outline_insights",
                            "description": "A tool that creates one or more meeting outline insights with descriptions.",
                            "parameters": InsightListFromLLM.model_json_schema(),
                        },
                    }
                ],
                tool_choice="create_outline_insights",
                metadata=LLMTraceMetadata(
                    trace_name="insights.meeting_outline",
                    session_id=langfuse_session_id,
                    custom_fields=ReeTraceMetadata(
                        organization_id=str(organization_id),
                        prompt_use_case=PromptUseCase.OUTLINE_INSIGHTS,
                    ),
                ),
            )

            if not response.choices:
                return []
            tool_calls = response.tool_calls
            if not tool_calls:
                return []

            outline = []
            for tool_call in tool_calls:
                if tool_call.function and tool_call.function.arguments:
                    json_args = json.loads(tool_call.function.arguments)
                    try:
                        for insight in json_args.get("insights", []):
                            outline.append(
                                InsightSchema(
                                    insight_name=insight.get("insight_name", ""),
                                    insight=insight.get("insight", ""),
                                    line_numbers=insight.get("line_numbers", []),
                                    keyword_tags=insight.get("keyword_tags", []),
                                    start_timestamp=insight.get(
                                        "start_timestamp", None
                                    ),
                                )
                            )
                    except Exception as e:
                        logger.error(
                            "[meeting_insight_service] Error parsing tool call arguments",
                            error=e,
                        )

        else:
            outline = (
                await self.transcript_extraction_service_v2.extract_outline_topics(
                    organization_id=organization_id,
                    compacted_transcript=transcript_container.transcript.compact(),
                    meeting=meeting,
                )
            )

        return await self._persist_insight_dtos(
            organization_id=organization_id,
            meeting_id=meeting.id,
            extraction_section=not_none(outline_extraction_section),
            extracted_insights=outline,
            insight_naming_policy=InsightNamingPolicy.EXTRACTED_INSIGHT_NAME,
        )

    async def _extract_field_extraction_insights_from_transcript(  # noqa: C901, PLR0912, PLR0915
        self,
        organization_id: UUID,
        meeting: Meeting,
        transcript_container: TranscriptContainer,
        existing_insights_by_config_section_id: dict[UUID, InsightSection],
    ) -> list[InsightDTO]:
        # Exclude EMAIL_ANALYSIS for XSCRIPT_FIELD_EXTRACTION section
        field_sections = await self.transcript_extraction_service_v2.extraction_config_repository.get_extraction_section_by_organization_and_feature_and_extraction_type(
            organization_id=organization_id,
            extraction_type=ExtractionType.XSCRIPT_FIELD_EXTRACTION,
            feature=Feature.POST_CALL_ANALYTICS,
        )
        typed_insights: list[TypedInsight] = []

        for field_section in field_sections:
            if not await self._requires_extraction(
                organization_id,
                meeting.id,
                field_section,
                existing_insights_by_config_section_id,
            ):
                continue

            logger.bind(
                meeting_id=meeting.id,
                organization_id=organization_id,
                section=field_section.name,
            ).info(
                "Extracting insights for meeting for section",
                extraction_section_id=field_section.id,
            )

            if settings.enable_langfuse_for_meeting_insights:
                langfuse_session_id = (
                    f"insights:{IntelTriggerObjectType.MEETING}:{meeting.id}"
                )

                # get customer contacts
                customer_contacts = await self._generate_customer_contacts_prompt_input(
                    organization_id=organization_id, meeting=meeting
                )

                # get seller contacts
                seller_contacts = await self.generate_seller_contacts_prompt_input(
                    organization_id=organization_id, meeting=meeting
                )

                organization = await self.organization_service.get_organization_by_id(
                    organization_id
                )

                account = None
                if meeting.account_id:
                    account = await self.account_query_service.get_account_v2(
                        account_id=meeting.account_id,
                        organization_id=organization_id,
                    )

                fields = await self.transcript_extraction_service_v2.extraction_config_repository.get_extraction_fields_v2_by_extraction_section_id(
                    organization_id=organization_id,
                    extraction_section_id=field_section.id,
                )
                insight_type_bullets = "\n".join(
                    [f"  - {f.name}: {f.description}" for f in fields]
                )

                prompt_variables = {
                    "transcript": transcript_container.transcript.compact(),
                    "seller_contacts": seller_contacts,
                    "seller_company_name": organization.display_name
                    if organization
                    else "",
                    "customer_contacts": customer_contacts,
                    "customer_company_name": account.display_name if account else "",
                    "ends_at": str(meeting.ended_at) or str(meeting.ends_at),
                    "num_insights": "1 to 8",
                    "insight_type_bullets": insight_type_bullets,
                }

                prompt_obj = await langfuse_prompt_service.get_prompt(
                    request=PromptRequest(
                        prompt_name=PromptEnum.GENERATE_FIELD_EXTRACTION_INSIGHTS_FROM_CONVERSATION,
                        variables=prompt_variables,
                    )
                )

                response = await acompletion(
                    model=prompt_obj.get_model(),
                    messages=prompt_obj.messages,
                    temperature=0,
                    tools=[
                        {
                            "type": "function",
                            "function": {
                                "name": "create_field_extraction_insights",
                                "description": "A tool that creates one or more meeting field extraction insights with descriptions.",
                                "parameters": TypedInsight.model_json_schema(),
                            },
                        }
                    ],
                    tool_choice="create_field_extraction_insights",
                    metadata=LLMTraceMetadata(
                        trace_name="insights.meeting_field_extraction",
                        session_id=langfuse_session_id,
                        custom_fields=ReeTraceMetadata(
                            organization_id=str(organization_id),
                            prompt_use_case=PromptUseCase.FIELD_EXTRACTION_INSIGHTS,
                        ),
                    ),
                )

                if not response.choices:
                    continue
                tool_calls = response.tool_calls
                if not tool_calls:
                    continue

                for tool_call in tool_calls:
                    if tool_call.function and tool_call.function.arguments:
                        json_args = json.loads(tool_call.function.arguments)
                        try:
                            typed_insights.append(
                                TypedInsight(
                                    insight_name=json_args.get("insight_name", ""),
                                    insight=json_args.get("insight", ""),
                                    insight_type=InsightType(
                                        json_args.get("insight_type", "")
                                    ),
                                    line_numbers=json_args.get("line_numbers", []),
                                    keyword_tags=json_args.get("keyword_tags", []),
                                    start_timestamp=json_args.get(
                                        "start_timestamp", None
                                    ),
                                )
                            )
                        except Exception as e:
                            logger.error(
                                "[meeting_insight_service] Error parsing tool call arguments",
                                error=e,
                            )

            else:
                typed_insights.extend(
                    await self.transcript_extraction_service_v2.extract_insights(
                        organization_id=organization_id,
                        compacted_transcript=transcript_container.transcript.compact(),
                        meeting=meeting,
                        extraction_section=field_section,
                    )
                )

        if not typed_insights:
            return []

        types: list[InsightType] = list({i.insight_type for i in typed_insights})
        is_by_es: dict[ExtractionSection, InsightSection] = {}

        # Get existing sections for this meeting
        existing_sections = await self.insight_repository.get_insight_sections_by_reference_id_and_organization_id(
            reference_id=meeting.id,
            organization_id=organization_id,
            reference_type=InsightReferenceIdType.MEETING,
        )
        existing_by_config_section_id = {
            section.extraction_section_id: section for section in existing_sections
        }

        async def build_insight_section(
            extraction_section: ExtractionSection,
        ) -> InsightSection:
            nonlocal is_by_es
            if extraction_section in is_by_es:
                return is_by_es[extraction_section]

            # Check if there's an existing empty section for this extraction section
            existing_section = existing_by_config_section_id.get(extraction_section.id)
            if existing_section:
                # Verify the section is empty
                existing_insights = (
                    await self.insight_repository.get_insights_by_section_id(
                        organization_id=organization_id,
                        insight_section_id=existing_section.id,
                    )
                )
                if not existing_insights:
                    # Reuse the existing empty section
                    is_by_es[extraction_section] = existing_section
                    return existing_section

            # Create a new section if none exists or if existing section has insights
            new_section = InsightSection(
                id=uuid.uuid4(),
                source_type=InsightSourceType.TRANSCRIPTION,
                reference_type=InsightReferenceIdType.MEETING,
                reference_id=meeting.id,
                name=extraction_section.name,
                description=extraction_section.description,
                extraction_section_version=extraction_section.version,
                extraction_section_id=extraction_section.id,
                insight_type=extraction_section.type,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
                organization_id=organization_id,
                created_by_user_id=None,
                updated_by_user_id=None,
                deleted_by_user_id=None,
                deleted_at=None,
                user_feedback=None,
            )
            is_by_es[extraction_section] = new_section
            return new_section

        # Create sections for each type
        insight_sections = {}
        for t in types:
            extraction_section = await self.transcript_extraction_service_v2.extraction_config_repository.get_extraction_section_by_feature(
                organization_id=organization_id,
                insight_type=t,
                feature=Feature.POST_CALL_ANALYTICS,
            )
            insight_sections[t] = await build_insight_section(extraction_section)

        extraction_fields = await self.transcript_extraction_service_v2.extraction_config_repository.get_extraction_fields_v2_by_feature(
            organization_id=organization_id, feature=Feature.POST_CALL_ANALYTICS
        )
        ef = {f.insight_type: f for f in extraction_fields if f.insight_type}

        dtos = [
            MeetingInsightService.map_to_insight_dto(
                insight=insight,
                rank=rank,
                insight_name=ef[not_none(insight.insight_type)].name,
                insight_description=ef[not_none(insight.insight_type)].description,
                organization_id=organization_id,
                source_type=InsightSourceType.TRANSCRIPTION,
                insight_section=insight_sections[not_none(insight.insight_type)],
                meeting_id=meeting.id,
                user_id=None,
            )
            for rank, insight in enumerate(typed_insights)
        ]

        inserted_dtos = []
        for dto in dtos:
            # Only upsert if it's a new section
            inserted_section = (
                dto.insight_section
                if dto.insight_section.id in {s.id for s in existing_sections}
                else not_none(
                    await self.insight_repository.upsert_pk(dto.insight_section)
                )
            )
            inserted_insights = []
            for i in dto.insights:
                inserted_insights.append(await self.insight_repository.insert(i))

            inserted_dtos.append(
                InsightDTO(
                    insight_section=inserted_section,
                    insights=inserted_insights,
                )
            )
        return inserted_dtos

    async def _extract_objection_insights_from_transcript(
        self,
        organization_id: UUID,
        meeting: Meeting,
        transcript_container: TranscriptContainer,
        existing_insights_by_config_section_id: dict[UUID, InsightSection],
    ) -> list[InsightDTO]:
        objection_extraction_section = await self.config_extraction_service.get_extraction_section_by_feature_and_name(
            feature=Feature.POST_CALL_ANALYTICS,
            name="Objections",
            organization_id=organization_id,
        )

        if not await self._requires_extraction(
            organization_id,
            meeting.id,
            objection_extraction_section,
            existing_insights_by_config_section_id,
        ):
            return []

        logger.bind(meeting_id=meeting.id, organization_id=organization_id).info(
            "Extracting objection insights"
        )

        langfuse_session_id = f"insights:{IntelTriggerObjectType.MEETING}:{meeting.id}"

        # get customer contacts
        customer_contacts = await self._generate_customer_contacts_prompt_input(
            organization_id=organization_id, meeting=meeting
        )

        # get seller contacts
        seller_contacts = await self.generate_seller_contacts_prompt_input(
            organization_id=organization_id, meeting=meeting
        )

        organization = await self.organization_service.get_organization_by_id(
            organization_id
        )

        account = None
        if meeting.account_id:
            account = await self.account_query_service.get_account_v2(
                account_id=meeting.account_id,
                organization_id=organization_id,
            )

        objections = await generate_objection_insights_from_conversation_llm_call(
            transcript=transcript_container.transcript.compact(),
            seller_contacts=seller_contacts,
            seller_company_name=organization.display_name if organization else "",
            customer_contacts=customer_contacts,
            customer_company_name=account.display_name if account else "",
            ends_at=str(meeting.ended_at) or str(meeting.ends_at),
            min_num_insights=0,
            max_num_insights=10,
            organization_id=organization_id,
            langfuse_session_id=langfuse_session_id,
        )

        return await self._persist_insight_dtos(
            organization_id=organization_id,
            meeting_id=meeting.id,
            extraction_section=not_none(objection_extraction_section),
            extracted_insights=objections,
            insight_naming_policy=InsightNamingPolicy.EXTRACTED_INSIGHT_NAME,
        )

    # Get all Insights by contact_id and organization_id
    async def get_insights_by_contact_id(
        self, contact_id: UUID, organization_id: UUID
    ) -> list[Insight]:
        return await self.insight_repository.get_insights_by_contact_id(
            contact_id=contact_id,
            organization_id=organization_id,
            reference_type=InsightReferenceIdType.MEETING,
        )

    # Get Insight Section along with Individual Insights from database
    async def get_insight_section_by_id(
        self, insight_section_id: UUID, organization_id: UUID
    ) -> InsightDTO:
        insight_section = await self._find_existing_insight_section_or_error(
            insight_section_id=insight_section_id, organization_id=organization_id
        )

        return await self._get_dto_for_section(insight_section=insight_section)

    async def authed_get_insight_sections_by_upcoming_meeting_id_and_organization_id(
        self,
        user_auth_context: UserAuthContext,
        upcoming_meeting_id: UUID,
    ) -> list[InsightDTO]:
        await self.error_if_no_meeting_access(
            user_auth_context=user_auth_context,
            meeting_id=upcoming_meeting_id,
            permission=SubServiceMeetingPermission.READ,
        )
        return (
            await self.get_insight_sections_by_upcoming_meeting_id_and_organization_id(
                upcoming_meeting_id=upcoming_meeting_id,
                organization_id=user_auth_context.organization_id,
            )
        )

    async def get_insight_sections_by_upcoming_meeting_id_and_organization_id(
        self, upcoming_meeting_id: UUID, organization_id: UUID
    ) -> list[InsightDTO]:
        meeting = await self.meeting_repository.find_by_tenanted_primary_key(
            Meeting, organization_id=organization_id, id=upcoming_meeting_id
        )
        if not meeting:
            raise ResourceNotFoundError(
                f"Meeting {upcoming_meeting_id} for organization {organization_id} not found"
            )

        if not meeting.pipeline_id:
            return []

        all_pipeline_meetings = (
            await self.meeting_repository.list_meetings_by_pipeline_id(
                organization_id=organization_id,
                pipeline_id=meeting.pipeline_id,
            )
        )
        other_meetings = sorted(
            [m for m in all_pipeline_meetings if m.id != upcoming_meeting_id],
            key=lambda x: x.starts_at,
            reverse=True,
        )
        last_meeting = other_meetings[0] if other_meetings else None
        if not last_meeting:
            return []

        return await self.get_insight_sections_by_meeting_id_and_organization_id(
            meeting_id=last_meeting.id, organization_id=organization_id
        )

    async def authed_get_insight_sections_by_meeting_id_and_organization_id(
        self,
        user_auth_context: UserAuthContext,
        meeting_id: UUID,
    ) -> list[InsightDTO]:
        await self.error_if_no_meeting_access(
            user_auth_context=user_auth_context,
            meeting_id=meeting_id,
            permission=SubServiceMeetingPermission.READ,
        )
        return await self.get_insight_sections_by_meeting_id_and_organization_id(
            meeting_id=meeting_id,
            organization_id=user_auth_context.organization_id,
        )

    # Get all Insight Sections along with Individual Insights using meeting_id and organization_id
    async def get_insight_sections_by_meeting_id_and_organization_id(
        self, meeting_id: UUID, organization_id: UUID
    ) -> list[InsightDTO]:
        logger.bind(meeting_id=meeting_id, organization_id=organization_id).info(
            "List insights by meeting and organization"
        )
        insight_sections = await self.insight_repository.get_insight_sections_by_reference_id_and_organization_id(
            reference_id=meeting_id,
            organization_id=organization_id,
            reference_type=InsightReferenceIdType.MEETING,
        )
        insights = await self.insight_repository.get_insights_by_reference_id_and_organization_id(
            reference_id=meeting_id,
            organization_id=organization_id,
            reference_type=InsightReferenceIdType.MEETING,
        )
        return self._get_dto_list(insight_sections=insight_sections, insights=insights)

    async def patch_insight(  # noqa: C901
        self,
        insight_id: UUID,
        organization_id: UUID,
        user_id: UUID,
        request: PatchInsightRequest,
    ) -> InsightDTO:
        logger.bind(insight_id=insight_id, request=request).info(
            "Patch insight request"
        )
        insight = await self._find_existing_insight_or_error(
            insight_id=insight_id, organization_id=organization_id
        )

        fields_to_update: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if request.contact_id not in (UNSET, insight.contact_id):
            fields_to_update["contact_id"] = request.contact_id
        if request.field_values not in (UNSET, insight.brief_values):
            fields_to_update["brief_values"] = request.field_values
        if request.user_feedback not in (UNSET, insight.user_feedback):
            fields_to_update["user_feedback"] = request.user_feedback
        if request.transcript_offset_locations not in (
            UNSET,
            insight.transcript_locations,
        ):
            fields_to_update["transcript_locations"] = (
                request.transcript_offset_locations
            )
        if request.tags not in (UNSET, insight.tags):
            fields_to_update["tags"] = request.tags
        if request.approved_at != UNSET and not insight.approved_at:
            fields_to_update["approved_at"] = request.approved_at
            fields_to_update["approved_by_user_id"] = user_id

        if not fields_to_update:
            return await self._get_dto_for_section_id(
                insight_section_id=insight.insight_section_id,
                organization_id=organization_id,
            )

        time_now = zoned_utc_now()
        fields_to_update["updated_at"] = time_now
        fields_to_update["updated_by_user_id"] = user_id

        match insight.author_type:
            case InsightAuthorType.SYSTEM:
                fields_to_update["author_type"] = InsightAuthorType.SYSTEM_USER_UPDATED
            case InsightAuthorType.SYSTEM_USER_UPDATED:
                fields_to_update["author_type"] = InsightAuthorType.SYSTEM_USER_UPDATED
            case InsightAuthorType.USER:
                fields_to_update["author_type"] = InsightAuthorType.USER
            case _:
                assert_never(insight.author_type)

        updated_insight = not_none(
            await self.insight_repository.update_by_tenanted_primary_key(
                table_model=Insight,
                organization_id=organization_id,
                primary_key_to_value={
                    "id": insight_id,
                },
                column_to_update=fields_to_update,
            )
        )
        return await self._get_dto_for_section_id(
            insight_section_id=updated_insight.insight_section_id,
            organization_id=organization_id,
        )

    async def authed_create_insight_field(
        self,
        user_auth_context: UserAuthContext,
        request: CreateInsightsFieldRequest,
    ) -> InsightDTO:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=request.meeting_insight_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.create_insight_field(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            request=request,
        )

    async def create_insight_field(
        self, organization_id: UUID, user_id: UUID, request: CreateInsightsFieldRequest
    ) -> InsightDTO:
        logger.bind(request=request).info("Create insights field request")
        insight_section = await self._find_existing_insight_section_or_error(
            insight_section_id=request.meeting_insight_id,
            organization_id=organization_id,
        )
        time_now = zoned_utc_now()
        rank = request.rank
        if not rank:
            other_insights = await self.insight_repository.get_insights_by_section_id(
                organization_id=organization_id,
                insight_section_id=insight_section.id,
            )
            rank = (
                0
                if not other_insights
                else max(other_insights, key=lambda x: x.rank).rank + 1
            )
        await self.insight_repository.insert(
            Insight(
                id=uuid.uuid4(),
                insight_section_id=insight_section.id,
                organization_id=organization_id,
                reference_type=InsightReferenceIdType.MEETING,
                reference_id=insight_section.reference_id,
                insight_name=request.name,
                insight_description=request.description,
                contact_id=request.contact_id,
                created_by_user_id=user_id,
                updated_by_user_id=user_id,
                deleted_by_user_id=None,
                source_type=InsightSourceType.TRANSCRIPTION,  # Only current use case
                tags=None,
                brief_values=request.field_values,
                detailed_explanation=None,
                transcript_locations=None,
                source_locations=None,
                created_at=time_now,
                updated_at=time_now,
                deleted_at=None,
                author_type=InsightAuthorType.USER,
                user_feedback=None,
                approved_at=time_now,
                approved_by_user_id=user_id,
                metadata={},
                rank=rank,
                version=0,
            )
        )
        # Return back fully populated list of fields for this section
        return await self._get_dto_for_section(insight_section=insight_section)

    async def authed_remove_insight_section_by_id_and_org_id(
        self,
        user_auth_context: UserAuthContext,
        insight_section_id: UUID,
    ) -> InsightDTO:
        await self.error_if_no_insight_section_access(
            user_auth_context=user_auth_context,
            insight_section_id=insight_section_id,
            permission=SubServiceMeetingPermission.UPDATE,
        )
        return await self.remove_insight_section_by_id_and_org_id(
            insight_section_id=insight_section_id,
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
        )

    async def remove_insight_section_by_id_and_org_id(
        self, insight_section_id: UUID, organization_id: UUID, user_id: UUID
    ) -> InsightDTO:
        logger.bind(insight_section_id=insight_section_id).info(
            "Delete meeting insight section request"
        )
        insight_section = await self.insight_repository.find_by_tenanted_primary_key(
            table_model=InsightSection,
            organization_id=organization_id,
            id=insight_section_id,
        )
        if not insight_section:
            raise ResourceNotFoundError(
                f"Insight section {insight_section_id} for organization {organization_id} not found"
            )
        time_now = zoned_utc_now()
        return await self.insight_repository.remove_insight_section_and_insights(
            organization_id=organization_id,
            insight_section_id=insight_section_id,
            deleted_by_user_id=user_id,
            deleted_at=time_now,
        )

    async def remove_insight(
        self, insight_id: UUID, organization_id: UUID, user_id: UUID
    ) -> InsightDTO:
        logger.bind(insight_id=insight_id).info("Remove insight request")
        time_now = zoned_utc_now()
        result = await self.insight_repository.update_by_tenanted_primary_key(
            table_model=Insight,
            organization_id=organization_id,
            primary_key_to_value={
                "id": insight_id,
            },
            column_to_update={
                "updated_at": time_now,
                "updated_by_user_id": user_id,
                "deleted_at": time_now,
                "deleted_by_user_id": user_id,
            },
        )
        if not result:
            raise ResourceNotFoundError(
                f"Insight {insight_id} for organization {organization_id} not found"
            )
        return await self._get_dto_for_section_id(
            insight_section_id=result.insight_section_id,
            organization_id=organization_id,
        )

    async def remove_all_insight_by_insight_section_id(
        self, insight_section_id: UUID, organization_id: UUID, user_id: UUID
    ) -> None:
        results = await self.insight_repository.remove_all_insights_by_section_id(
            insight_section_id=insight_section_id,
            organization_id=organization_id,
            deleted_at=zoned_utc_now(),
            deleted_by_user_id=user_id,
        )
        if not results:
            raise ResourceNotFoundError(
                f"Insight by section id {insight_section_id} for organization {organization_id} not found"
            )

    async def authed_re_rank_insight(
        self,
        user_auth_context: UserAuthContext,
        insight_id: UUID,
        organization_id: UUID,
        predecessor_id: UUID | None,
        successor_id: UUID | None,
    ) -> None:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=insight_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        await self.re_rank_insight(
            insight_id=insight_id,
            organization_id=organization_id,
            predecessor_id=predecessor_id,
            successor_id=successor_id,
        )

    async def re_rank_insight(  # noqa: PLR0912, C901
        self,
        insight_id: UUID,
        organization_id: UUID,
        predecessor_id: UUID | None,
        successor_id: UUID | None,
    ) -> None:
        if not predecessor_id and not successor_id:
            raise InvalidArgumentError(
                "One of predecessor_id or successor_id must be present"
            )

        if predecessor_id == successor_id:
            raise InvalidArgumentError("Predecessor must not be the same as successor")

        if insight_id in (predecessor_id, successor_id):
            raise InvalidArgumentError(
                "Target must not be equal to predecessor or successor"
            )

        insight = await self.insight_repository.find_by_tenanted_primary_key(
            Insight, organization_id=organization_id, id=insight_id
        )

        if not insight:
            raise ResourceNotFoundError(
                f"Insight {insight_id} for organization {organization_id} not found"
            )

        async def _neighbor_insight(_insight: Insight, neighbor_id: UUID) -> Insight:
            neighbor = await self.insight_repository.find_by_tenanted_primary_key(
                Insight,
                organization_id=organization_id,
                id=neighbor_id,
                insight_section_id=_insight.insight_section_id,
            )

            if not neighbor:
                raise ResourceNotFoundError(
                    f"Insight {predecessor_id} for organization {organization_id} not found"
                )

            return neighbor

        if predecessor_id:
            predecessor = await _neighbor_insight(insight, predecessor_id)
        else:
            predecessor = None

        if successor_id:
            successor = await _neighbor_insight(insight, successor_id)
        else:
            successor = None

        if predecessor and successor:
            if predecessor.rank == successor.rank:
                raise UnprocessableEntity(
                    f"Predecessor rank {predecessor.rank} is equal to successor rank {successor.rank}"
                )
            new_rank = (predecessor.rank + successor.rank) / 2
        elif predecessor:
            new_rank = predecessor.rank + 1
        elif successor:
            new_rank = successor.rank - 1
        else:
            raise ValueError("One of predecessor_id or successor_id must be present")

        if new_rank == insight.rank:
            raise UnprocessableEntity(
                f"Calculated rank {new_rank} is the same as existing rank"
            )

        should_not_exist = (
            await self.insight_repository.get_insight_by_section_id_and_rank(
                organization_id=organization_id,
                rank=new_rank,
                insight_section_id=insight.insight_section_id,
            )
        )
        if should_not_exist:
            raise UnprocessableEntity(
                f"Calculated rank {new_rank} already exists for the insight section"
            )

        await self.insight_repository.update_by_tenanted_primary_key(
            Insight,
            organization_id=organization_id,
            primary_key_to_value={"id": insight_id},
            column_to_update={
                "rank": new_rank,
                "updated_at": zoned_utc_now(),
            },
        )

    async def _find_existing_insight_section_or_error(
        self, insight_section_id: UUID, organization_id: UUID
    ) -> InsightSection:
        insight_section = await self.insight_repository.find_by_tenanted_primary_key(
            table_model=InsightSection,
            organization_id=organization_id,
            id=insight_section_id,
        )
        if not insight_section:
            raise ResourceNotFoundError(
                f"No insight section found for id {insight_section_id}, organization {organization_id}"
            )
        return insight_section

    async def _find_existing_insight_or_error(
        self, insight_id: UUID, organization_id: UUID
    ) -> Insight:
        insight = await self.insight_repository.find_by_tenanted_primary_key(
            table_model=Insight,
            organization_id=organization_id,
            id=insight_id,
        )
        if insight is None:
            raise ResourceNotFoundError(f"No insight found with id {insight_id}")
        return insight

    def _get_dto_list(
        self, insight_sections: list[InsightSection], insights: list[Insight]
    ) -> list[InsightDTO]:
        # Helper function for list functions that forms expected DTO results
        section_to_dtos: dict[UUID, InsightDTO] = {}
        for insight_section in insight_sections:
            section_to_dtos[insight_section.id] = InsightDTO(
                insight_section=insight_section,
                insights=[],
            )

        for insight in insights:
            section_to_dtos[insight.insight_section_id].insights.append(insight)

        insight_dtos = list(section_to_dtos.values())
        insight_dtos.sort(
            key=lambda insight_dto: (
                insight_dto.insight_section.created_at,
                insight_dto.insight_section.name,
            ),
        )
        for insight_dto in insight_dtos:
            insight_dto.insights.sort(
                key=lambda insight: (
                    insight.rank,
                    insight.created_at,
                    insight.insight_name,
                ),
            )
        return insight_dtos

    async def _get_dto_for_section(self, insight_section: InsightSection) -> InsightDTO:
        # Helper function to create fully hydrated DTO from an insight_section.
        # Used to form result when we have a modified insight_section on hand.
        insights = sorted(
            await self.insight_repository.get_insights_by_section_id(
                organization_id=insight_section.organization_id,
                insight_section_id=insight_section.id,
            ),
            key=lambda insight: (insight.created_at, insight.insight_name),
        )
        return InsightDTO(
            insight_section=insight_section,
            insights=insights,
        )

    async def _get_dto_for_section_id(
        self, insight_section_id: UUID, organization_id: UUID
    ) -> InsightDTO:
        # Helper function to create fully hydrated InisghtDTO from an insight ID.
        # Used to form expected contract results after individual insight operation.
        insight_section = not_none(
            await self.insight_repository.find_by_tenanted_primary_key(
                table_model=InsightSection,
                organization_id=organization_id,
                id=insight_section_id,
            )
        )
        insights = sorted(
            await self.insight_repository.get_insights_by_section_id(
                insight_section_id=insight_section.id,
                organization_id=organization_id,
            ),
            key=lambda insight: (insight.created_at, insight.insight_name),
        )
        return InsightDTO(
            insight_section=insight_section,
            insights=insights,
        )

    @staticmethod
    def map_extraction_config_to_insight_section(
        organization_id: UUID,
        source_type: InsightSourceType,
        meeting_id: UUID,
        extraction_config_dto: ExtractionConfigDTO,
        user_id: UUID | None,
    ) -> InsightDTO:
        zoned_time = zoned_utc_now()
        insight_section = InsightSection(
            id=uuid.uuid4(),
            source_type=source_type,
            reference_type=InsightReferenceIdType.MEETING,
            reference_id=meeting_id,
            name=extraction_config_dto.extraction_section.name,
            description=extraction_config_dto.extraction_section.description,
            extraction_section_version=extraction_config_dto.extraction_section.version,
            extraction_section_id=extraction_config_dto.extraction_section.id,
            insight_type=extraction_config_dto.extraction_section.type,
            created_at=zoned_time,
            updated_at=zoned_time,
            organization_id=organization_id,
            created_by_user_id=user_id,
            updated_by_user_id=user_id,
            deleted_by_user_id=None,
            deleted_at=None,
            user_feedback=None,
        )
        return InsightDTO(
            insight_section=insight_section,
            insights=[],
        )

    @staticmethod
    def map_to_insight_dto(
        insight: TInsight,
        rank: float,
        insight_name: str,
        insight_description: str,
        insight_section: InsightSection,
        organization_id: UUID,
        source_type: InsightSourceType,
        meeting_id: UUID,
        user_id: UUID | None,
        author_type: InsightAuthorType = InsightAuthorType.SYSTEM,
    ) -> InsightDTO:
        metadata: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if isinstance(insight, ActionableInsight):
            metadata = {
                "task_title": insight.title,
                "task_note": insight.insight,
                "task_priority": insight.priority,
                "task_type": insight.action_type,
                "task_associated_contact_ids": [
                    str(c.id) for c in insight.associated_contacts
                ],
                "task_due_date": insight.due_date,
            }
        if isinstance(insight, SentimentInsight):
            metadata = {
                "polarity": insight.polarity,
                "emotion": insight.emotion,
            }
        zoned_time = zoned_utc_now()
        insights = [
            Insight(
                id=uuid.uuid4(),
                insight_section_id=insight_section.id,
                organization_id=organization_id,
                reference_type=InsightReferenceIdType.MEETING,
                reference_id=meeting_id,
                contact_id=None,
                insight_name=insight_name,
                insight_description=insight_description,
                created_by_user_id=user_id,
                updated_by_user_id=user_id,
                deleted_by_user_id=None,
                source_type=source_type,
                tags=insight.keyword_tags,
                brief_values=[insight.insight],
                detailed_explanation="",
                transcript_locations=insight.line_numbers,
                source_locations=[
                    {
                        "line_number": insight.line_numbers,
                        "start_timestamp": insight.start_timestamp,
                    }
                ],
                created_at=zoned_time,
                updated_at=zoned_time,
                deleted_at=None,
                author_type=author_type,
                user_feedback=None,
                approved_at=None,
                approved_by_user_id=None,
                metadata=metadata,
                rank=rank,
                version=0,
            )
        ]

        return InsightDTO(
            insight_section=insight_section,
            insights=insights,
        )

    @override
    async def get_meeting_id_for_entity(self, entity: InsightDTO) -> UUID:
        if not entity.insights:
            raise ResourceNotFoundError("No insights found for InsightDTO")
        insight = entity.insights[0]
        insight_section = entity.insight_section
        if (
            not insight_section.reference_type
            or insight_section.reference_type != InsightReferenceIdType.MEETING
        ):
            raise ResourceNotFoundError(f"No meeting found for insight {insight.id}")
        if insight_section.reference_id is None:
            raise ResourceNotFoundError(f"No meeting found for insight {insight.id}")
        return insight_section.reference_id

    async def get_entity(
        self, entity_id: UUID, organization_id: UUID, user_id: UUID
    ) -> InsightDTO:
        insight = await self.insight_repository.get_insight_by_id(
            organization_id=organization_id,
            insight_id=entity_id,
        )
        if not insight:
            raise ResourceNotFoundError(f"No insight found for id {entity_id}")
        insight_section = (
            await self.insight_repository.get_insight_section_by_insight_id(
                organization_id=organization_id,
                insight_id=entity_id,
            )
        )
        if not insight_section:
            raise ResourceNotFoundError(
                f"No insight section found for insight {entity_id}"
            )
        return InsightDTO(
            insight_section=insight_section,
            insights=[insight],
        )

    async def update_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        entity: InsightDTO,
        request: BasePatchRequest,
    ) -> InsightDTO:
        if not entity.insights:
            raise ResourceNotFoundError("No insights found for InsightDTO")
        insight = entity.insights[0]
        patch_request = cast(PatchInsightRequest, request)
        return await self.patch_insight(
            organization_id=organization_id,
            user_id=user_id,
            insight_id=insight.id,
            request=patch_request,
        )

    async def create_entity(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        organization_id: UUID,
        user_id: UUID,
        request: Any,
    ) -> InsightDTO:
        if not isinstance(request, CreateInsightsFieldRequest):
            raise ValueError("Request must be a CreateInsightsFieldRequest")
        return await self.create_insight_field(
            organization_id=organization_id,
            user_id=user_id,
            request=request,
        )

    async def remove_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        entity_id: UUID,
    ) -> DeleteEntityResponse:
        removed_insight = await self.remove_insight(
            insight_id=entity_id,
            organization_id=organization_id,
            user_id=user_id,
        )
        return DeleteEntityResponse(id=removed_insight.insights[0].id)

    async def error_if_no_insight_section_access(
        self,
        user_auth_context: UserAuthContext,
        insight_section_id: UUID,
        permission: SubServiceMeetingPermission,
    ) -> None:
        insight_section = await self.insight_repository.find_by_tenanted_primary_key(
            table_model=InsightSection,
            organization_id=user_auth_context.organization_id,
            id=insight_section_id,
        )
        if not insight_section:
            raise ResourceNotFoundError(
                f"No insight section found for id {insight_section_id}"
            )
        if (
            insight_section.reference_type != InsightReferenceIdType.MEETING
            or not insight_section.reference_id
        ):
            raise ResourceNotFoundError(
                f"No meeting found for insight section {insight_section_id}"
            )

        meeting_id = insight_section.reference_id
        await self.error_if_no_meeting_access(
            user_auth_context=user_auth_context,
            meeting_id=meeting_id,
            permission=permission,
        )


class SingletonMeetingInsightService(Singleton, MeetingInsightService):
    pass


def meeting_insight_service_factory_general(
    db_engine: DatabaseEngine,
) -> MeetingInsightService:
    return SingletonMeetingInsightService(
        config_extraction_service=extraction_config_service_factory_general(
            db_engine=db_engine
        ),
        transcript_extraction_service_v2=transcript_extraction_from_engine_v2(
            engine=db_engine
        ),
        insight_repository=InsightRepository(engine=db_engine),
        meeting_repository=MeetingRepository(engine=db_engine),
        task_v2_service=get_task_v2_service_general(db_engine=db_engine),
        contact_query_service=get_contact_query_service(db_engine=db_engine),
        user_service=get_user_service_general(db_engine=db_engine),
        organization_service=get_organization_service_general(db_engine=db_engine),
        account_query_service=get_account_query_service(
            db_engine=db_engine,
        ),
    )


def meeting_insight_service_factory(request: Request) -> MeetingInsightService:
    db_engine = get_db_engine(request=request)
    return meeting_insight_service_factory_general(db_engine=db_engine)


# Necessary due to a circular dependency
async def start_intel_workflow(
    organization_id: UUID,
    object_id: UUID,
    object_type: IntelTriggerObjectType,
    account_ids: list[UUID] | None = None,
    pipeline_id: UUID | None = None,
    workflow_id: str | None = None,
) -> None:
    from salestech_be.core.ai.event_handlers.intel import (
        start_intel_workflow as _start_intel,
    )

    await _start_intel(
        organization_id=organization_id,
        object_id=object_id,
        object_type=object_type,
        account_ids=account_ids,
        pipeline_id=pipeline_id,
        workflow_id=workflow_id,
    )


async def start_stage_criteria_workflow(
    organization_id: UUID,
    pipeline_id: UUID,
    user_id: UUID,
    source_object: CriteriaExtractionSourceObjectId,
) -> None:
    from salestech_be.core.ai.event_handlers.stage_criteria import (
        start_stage_criteria_workflow as _start_stage_criteria_workflow,
    )

    await _start_stage_criteria_workflow(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        source_object=source_object,
    )
