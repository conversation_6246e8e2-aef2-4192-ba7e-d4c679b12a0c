from uuid import UUID

from fastapi import Request
from pydantic import ValidationError

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.db.dao.organization_preference_repository import (
    OrganizationPreferenceRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.organization_info import (
    OrganizationPreferenceKeys,
)
from salestech_be.ree_logging import get_logger
from salestech_be.web.api.organization.schema import (
    ActivityCaptureMode,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    MeetingRecordingPreference,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    OrganizationActivityCapturePreference,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    OrganizationPreferenceRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    OrganizationPreferenceResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    SettingsType,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class OrganizationPreferenceService:
    """Organization preference service."""

    def __init__(
        self,
        organization_preference_repository: OrganizationPreferenceRepository,
    ):
        self.organization_preference_repository = organization_preference_repository

    async def upsert_organization_preference(
        self,
        user_id: UUID,
        organization_id: UUID,
        data: OrganizationPreferenceRequest,
    ) -> None:
        logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            request=data,
        ).info("Upsert organization preference request")
        organization_preference = (
            await self.organization_preference_repository.find_by_organization(
                organization_id=organization_id,
                key=data.key,
            )
        )

        if not organization_preference:
            organization_preference = await self.organization_preference_repository.create_organization_preference(
                user_id=user_id,
                organization_id=organization_id,
                key=data.key,
            )

        await self.organization_preference_repository.update_organization_preference(
            organization_preference_id=organization_preference.id,
            user_id=user_id,
            request=data,
        )

    async def get_organization_preference(
        self,
        organization_id: UUID,
        key: str,
    ) -> OrganizationPreferenceResponse:
        """Get organization preference by organization ID and key."""

        # Some specific settings can have default values or special handling
        # You can put them here
        match key:
            case OrganizationPreferenceKeys.MEETING_RECORDING:
                meeting_recording_preference = (
                    await self.get_organization_meeting_recording_preference_or_default(
                        organization_id=organization_id
                    )
                )
                return OrganizationPreferenceResponse(
                    # response model will filter out unset, specify fields here
                    meeting_recording=MeetingRecordingPreference(
                        sales_meetings=meeting_recording_preference.sales_meetings,
                        internal_meetings=meeting_recording_preference.internal_meetings,
                        recurring_meetings=meeting_recording_preference.recurring_meetings,
                    )
                )
            case OrganizationPreferenceKeys.ACTIVITY_CAPTURE:
                activity_capture_preference = (
                    await self.get_organization_activity_capture_preference_or_default(
                        organization_id=organization_id
                    )
                )
                return OrganizationPreferenceResponse(
                    # response model will filter out unset, specify fields here
                    activity_capture=OrganizationActivityCapturePreference(
                        capture_mode=activity_capture_preference.capture_mode,
                        blocked_domains=activity_capture_preference.blocked_domains,
                        blocked_emails=activity_capture_preference.blocked_emails,
                    )
                )
            case _:
                # Handle all other keys using the generic approach
                pass

        # Default/Generic handling for the preference
        organization_preference = (
            await self.organization_preference_repository.find_by_organization(
                organization_id=organization_id,
                key=key,
            )
        )
        preference_data: dict[str, SettingsType] = {key: {}}

        if (
            organization_preference is not None
            and organization_preference.value is not None
        ):
            preference_data = {key: organization_preference.value}

        try:
            response = OrganizationPreferenceResponse.model_validate(preference_data)
        except ValidationError as e:
            raise ValueError(f"Invalid data: {e}")

        return response

    async def get_organization_meeting_recording_preference_or_default(
        self,
        organization_id: UUID,
    ) -> MeetingRecordingPreference:
        organization_preference = (
            await self.organization_preference_repository.find_by_organization(
                organization_id=organization_id,
                key=OrganizationPreferenceKeys.MEETING_RECORDING,
            )
        )
        if organization_preference is None or organization_preference.value is None:
            # use the default
            return MeetingRecordingPreference()
        return MeetingRecordingPreference.model_validate(organization_preference.value)

    async def get_organization_activity_capture_preference_or_default(
        self,
        organization_id: UUID,
    ) -> OrganizationActivityCapturePreference:
        organization_preference = (
            await self.organization_preference_repository.find_by_organization(
                organization_id=organization_id,
                key=OrganizationPreferenceKeys.ACTIVITY_CAPTURE,
            )
        )

        if organization_preference is None or organization_preference.value is None:
            # use the default
            return OrganizationActivityCapturePreference()
        return OrganizationActivityCapturePreference.model_validate(
            organization_preference.value
        )

    async def get_organization_activity_capture_mode(
        self,
        organization_id: UUID,
    ) -> ActivityCaptureMode:
        activity_capture_preference = (
            await self.get_organization_activity_capture_preference_or_default(
                organization_id
            )
        )
        return activity_capture_preference.capture_mode

    async def get_organization_activity_capture_blocked_domains(
        self,
        organization_id: UUID,
    ) -> list[str]:
        activity_capture_preference = (
            await self.get_organization_activity_capture_preference_or_default(
                organization_id
            )
        )
        return activity_capture_preference.blocked_domains

    async def get_organization_activity_capture_blocked_emails(
        self,
        organization_id: UUID,
    ) -> list[EmailStrLower]:
        activity_capture_preference = (
            await self.get_organization_activity_capture_preference_or_default(
                organization_id
            )
        )
        return activity_capture_preference.blocked_emails


class SingletonOrganizationPreferenceService(Singleton, OrganizationPreferenceService):
    pass


def organization_preference_service_from_engine(
    db_engine: DatabaseEngine,
) -> OrganizationPreferenceService:
    return SingletonOrganizationPreferenceService(
        organization_preference_repository=OrganizationPreferenceRepository(
            engine=db_engine
        ),
    )


def organization_preference_service_factory(
    request: Request,
) -> OrganizationPreferenceService:
    return organization_preference_service_from_engine(
        db_engine=get_db_engine(request=request)
    )
