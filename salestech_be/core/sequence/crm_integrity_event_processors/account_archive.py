from uuid import UUID

from salestech_be.core.crm_integrity.event_processors.abstract_processor import (
    AbstractDataOperationProcessor,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
    DataArchivalParam,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationPreviewIdentifiers,
    IntegrityAssociatedDataOperationSequencePreviewIdentifier,
)
from salestech_be.core.sequence.service.sequence_enrollment_query_service import (
    SequenceEnrollmentQueryService,
)
from salestech_be.core.sequence.type.sequence_enrollment_types import SequenceEnrollment
from salestech_be.db.models.crm_integrity import (
    AssociatedEntityType,
    CRMIntegrityJobUserOption,
)


class SequenceAccountArchiveEventProcessor(
    AbstractDataOperationProcessor[DataArchivalParam, CrmIdReplacementTrackParam]
):
    """
    Event processor for Account Archive within SequenceV2 domain.
    """

    def __init__(
        self,
        sequence_enrollment_query_service: SequenceEnrollmentQueryService,
    ) -> None:
        self.sequence_enrollment_query_service = sequence_enrollment_query_service

    async def fetch_sequence_enrollments(
        self,
        *,
        account_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceEnrollment]:
        return await self.sequence_enrollment_query_service.find_sequence_enrollments_by_account_id(
            account_id=account_id,
            organization_id=organization_id,
        )

    async def process(
        self,
        *,
        param: DataArchivalParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[CrmIdReplacementTrackParam]:
        """
        No-op for sequence processing in this job
        """
        return []

    async def preview(
        self,
        *,
        param: DataArchivalParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[IntegrityAssociatedDataOperationPreviewIdentifiers]:
        affected_sequence_enrollments = await self.fetch_sequence_enrollments(
            account_id=param.archive_entity_id,
            organization_id=param.organization_id,
        )

        affected_sequence_to_enrollments_map: dict[UUID, list[UUID]] = {}
        for sequence_enrollment in affected_sequence_enrollments:
            affected_sequence_to_enrollments_map.setdefault(
                sequence_enrollment.sequence_id, []
            ).append(sequence_enrollment.id)

        return [
            IntegrityAssociatedDataOperationPreviewIdentifiers(
                entity_type=AssociatedEntityType.SEQUENCE,
                entity_ids=[
                    IntegrityAssociatedDataOperationSequencePreviewIdentifier(
                        id=sequence_id,
                        enrollment_ids=enrollment_ids,
                    )
                    for sequence_id, enrollment_ids in affected_sequence_to_enrollments_map.items()
                ],
            )
        ]
