from __future__ import annotations

import enum
from typing import Annotated, Any, Literal

from pydantic import BaseModel, Field, field_validator


class DatasetConfig(BaseModel):
    """Configuration for a dataset"""

    dataset_id: str
    dataset_name: str
    alias: str | None = None

    def to_sql(self) -> str:
        """Convert the dataset to SQL"""
        return (
            f"{self.dataset_name} AS {self.alias}" if self.alias else self.dataset_name
        )

    def to_alias_sql(self) -> str:
        """Convert the dataset to SQL"""
        return f"{self.alias}" if self.alias else self.dataset_name


class FieldConfig(BaseModel):
    """Configuration for a field"""

    field_id: str
    field_name: str

    def to_sql(self) -> str:
        """Convert the field to SQL"""
        return f"{self.field_name}"


class DatasetFieldConfig(BaseModel):
    """Configuration for a field in the dataset"""

    dataset: DatasetConfig
    field: FieldConfig

    def to_sql(self) -> str:
        """Convert the field reference to SQL"""
        return f"{self.dataset.to_alias_sql()}.{self.field.to_sql()}"


class ExpressionNodeType(str, enum.Enum):
    """Types of nodes in an expression tree"""

    FUNCTION = "function"
    FIELD = "field"
    LITERAL = "literal"
    OPERATOR = "operator"
    CASE = "case"


class BaseExpressionNode(BaseModel):
    """Base class for all expression tree nodes"""

    type: ExpressionNodeType

    def to_sql(self) -> str:
        """Convert the expression node to SQL"""
        raise NotImplementedError("Subclasses must implement to_sql")


class LiteralExpressionNode(BaseExpressionNode):
    """A literal value in an expression"""

    type: Literal["literal"]
    value: Any
    value_type: str | None = None  # e.g., "string", "number", "date", etc.

    def to_sql(self) -> str:
        """Convert the literal to SQL"""
        if isinstance(self.value, str):
            return f"'{self.value}'"
        return str(self.value)


class FieldExpressionNode(BaseExpressionNode):
    """A reference to a field in a dataset"""

    type: Literal["field"]
    dataset_field: DatasetFieldConfig

    def to_sql(self) -> str:
        """Convert the field reference to SQL"""
        return self.dataset_field.to_sql()


class FunctionExpressionNode(BaseExpressionNode):
    """A function call in an expression"""

    type: Literal["function"]
    function_name: str
    arguments: list[ExpressionNode]

    def to_sql(self) -> str:
        """Convert the function to SQL"""
        args_sql = ", ".join(arg.to_sql() for arg in self.arguments)
        return f"{self.function_name}({args_sql})"


class OperatorExpressionNode(BaseExpressionNode):
    """An operator in an expression (e.g., +, -, *, /)"""

    type: Literal["operator"]
    operator: str
    left: ExpressionNode
    right: ExpressionNode

    def to_sql(self) -> str:
        """Convert the operator expression to SQL"""
        return f"({self.left.to_sql()} {self.operator} {self.right.to_sql()})"


class CaseWhenCondition(BaseModel):
    """A WHEN condition in a CASE expression"""

    condition: ExpressionNode
    result: ExpressionNode


class CaseExpressionNode(BaseExpressionNode):
    """A CASE expression"""

    type: Literal["case"]
    conditions: list[CaseWhenCondition]
    else_result: ExpressionNode | None = None

    def to_sql(self) -> str:
        """Convert the CASE expression to SQL"""
        when_clauses = " ".join(
            f"WHEN {cond.condition.to_sql()} THEN {cond.result.to_sql()}"
            for cond in self.conditions
        )
        else_clause = f" ELSE {self.else_result.to_sql()}" if self.else_result else ""
        return f"CASE {when_clauses}{else_clause} END"


ExpressionNode = Annotated[
    LiteralExpressionNode
    | FieldExpressionNode
    | FunctionExpressionNode
    | OperatorExpressionNode
    | CaseExpressionNode,
    Field(discriminator="type"),
]


class FieldColumnConfig(BaseModel):
    """Configuration for a field in the dataset"""

    type: Literal["field"]
    dataset_field: DatasetFieldConfig
    alias: str | None = None

    def to_sql(self) -> str:
        """Convert the field to SQL"""
        if self.alias:
            return f"{self.dataset_field.to_sql()} AS {self.alias}"
        return self.dataset_field.to_sql()


class ExpressionColumnConfig(BaseModel):
    """Configuration for an expression column in the dataset"""

    type: Literal["expression"]
    expression: str | ExpressionNode  # Support both string and tree structure
    alias: str | None = None

    def to_sql(self) -> str:
        """Convert the expression to SQL"""
        if isinstance(self.expression, str):
            expression_sql = self.expression
        else:
            expression_sql = self.expression.to_sql()
        if self.alias:
            return f"{expression_sql} AS {self.alias}"
        return expression_sql


ColumnConfig = Annotated[
    FieldColumnConfig | ExpressionColumnConfig, Field(discriminator="type")
]


class JoinOperator(str, enum.Enum):
    EQUALS = "="

    def to_sql(self) -> str:
        """Convert the join operator to SQL"""
        return self.value


class JoinType(str, enum.Enum):
    LEFT = "left"
    INNER = "inner"

    def to_sql(self) -> str:
        """Convert the join type to SQL"""
        return self.value.upper()


class JoinCondition(BaseModel):
    """Defines how two tables should be joined"""

    left_dataset_field: DatasetFieldConfig
    operator: JoinOperator
    right_dataset_field: DatasetFieldConfig


class JoinConfig(BaseModel):
    """Configuration for a table join"""

    dataset: DatasetConfig
    join_type: JoinType
    condition: JoinCondition

    def __iter__(self):
        """Allow unpacking like: table, join_type, condition = join_config"""
        yield self.dataset
        yield self.join_type
        yield self.condition


class FilterOperator(str, enum.Enum):
    EQUALS = "="
    NOT_EQUALS = "!="
    GREATER_THAN = ">"
    LESS_THAN = "<"
    GREATER_THAN_EQUALS = ">="
    LESS_THAN_EQUALS = "<="
    LIKE = "LIKE"
    IN = "IN"
    NOT_IN = "NOT IN"
    IS_NULL = "IS NULL"
    IS_NOT_NULL = "IS NOT NULL"

    def to_sql(self) -> str:
        """Convert the filter operator to SQL"""
        return self.value


class FilterConfig(BaseModel):
    """Configuration for a WHERE clause filter condition"""

    dataset_field: DatasetFieldConfig
    operator: FilterOperator
    value: Any | None = None

    @field_validator("value")
    def validate_value(cls, v, info):
        values = info.data
        if "operator" in values:
            operator = values["operator"]
            # For IS NULL and IS NOT NULL, value should be None
            if (
                operator in [FilterOperator.IS_NULL, FilterOperator.IS_NOT_NULL]
                and v is not None
            ):
                raise ValueError(f"Value must be None for {operator.to_sql()} operator")
            # For all other operators, value should not be None
            elif (
                operator not in [FilterOperator.IS_NULL, FilterOperator.IS_NOT_NULL]
                and v is None
            ):
                raise ValueError(
                    f"Value cannot be None for {operator.to_sql()} operator"
                )
            # For IN and NOT IN, value should be a list
            if operator in [
                FilterOperator.IN,
                FilterOperator.NOT_IN,
            ] and not isinstance(v, list):
                raise ValueError(
                    f"Value must be a list for {operator.to_sql()} operator"
                )
        return v

    def to_sql(self) -> str:
        """Convert the filter to a SQL WHERE clause fragment"""
        if self.operator in [FilterOperator.IS_NULL, FilterOperator.IS_NOT_NULL]:
            return f"{self.dataset_field.to_sql()} {self.operator.to_sql()}"
        elif self.operator in [FilterOperator.IN, FilterOperator.NOT_IN]:
            values = ", ".join(
                [f"'{v}'" if isinstance(v, str) else str(v) for v in self.value]
            )
            return f"{self.dataset_field.to_sql()} {self.operator.to_sql()} ({values})"
        elif self.operator == FilterOperator.LIKE:
            return (
                f"{self.dataset_field.to_sql()} {self.operator.to_sql()} '{self.value}'"
            )
        else:
            # Handle string values with quotes
            value_str = (
                f"'{self.value}'" if isinstance(self.value, str) else str(self.value)
            )
            return f"{self.dataset_field.to_sql()} {self.operator.to_sql()} {value_str}"


class FilterLogic(str, enum.Enum):
    AND = "AND"
    OR = "OR"

    def to_sql(self) -> str:
        """Convert the filter logic to SQL"""
        return self.value


class FilterGroup(BaseModel):
    """Group of filter conditions with a specific logic operator"""

    filters: list[FilterGroup | FilterConfig]
    logic: FilterLogic = FilterLogic.AND

    def to_sql(self) -> str:
        """Convert the filter group to a SQL WHERE clause fragment"""
        if not self.filters:
            return ""

        filter_clauses = []
        for filter_item in self.filters:
            filter_clauses.append(filter_item.to_sql())

        # Join the clauses with the appropriate logic operator
        joined_clauses = f" {self.logic.to_sql()} ".join(filter_clauses)

        # Wrap in parentheses if there's more than one filter
        if len(self.filters) > 1:
            return f"({joined_clauses})"
        return joined_clauses


class OrderByConfig(BaseModel):
    """Configuration for an ORDER BY clause"""

    dataset_field: DatasetFieldConfig
    direction: Literal["ASC", "DESC"] = "ASC"

    def __iter__(self):
        """Allow unpacking like: column, direction = order_by_config"""
        yield self.dataset_field
        yield self.direction

    def to_sql(self) -> str:
        """Convert the order by to a SQL ORDER BY clause fragment"""
        return f"{self.dataset_field.to_sql()} {self.direction}"


class QueryConfig(BaseModel):
    """Complete configuration for a query dataset"""

    columns: list[ColumnConfig] = Field(default_factory=list)
    primary_dataset: DatasetConfig
    additional_datasets: list[JoinConfig] = Field(default_factory=list)
    filter_group: FilterGroup | None = None
    group_bys: list[ColumnConfig] = Field(default_factory=list)
    order_bys: list[OrderByConfig] = Field(default_factory=list)
    limit: int | None = None

    @field_validator("limit")
    def validate_limit(cls, v):
        if v is not None and v <= 0:
            raise ValueError("limit must be a positive integer")
        return v
