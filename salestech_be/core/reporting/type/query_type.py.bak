import enum
from typing import List, Optional, Union, Any
from pydantic import BaseModel, Field, field_validator


class JoinType(enum.Enum):
    LEFT = "left"
    INNER = "inner"


class JoinCondition(BaseModel):
    """Defines how two tables should be joined"""
    left_table: str
    left_column: str
    right_table: str
    right_column: str


class TableConfig(BaseModel):
    """Configuration for a table in the dataset"""
    table_name: str
    alias: Optional[str] = None
    columns: List[str] = ["*"]  # Default to all columns
    
    def __init__(self, **data: Any):
        super().__init__(**data)
        if self.alias is None:
            self.alias = self.table_name


class JoinConfig(BaseModel):
    """Configuration for a table join"""
    table: TableConfig
    join_type: JoinType
    condition: JoinCondition

    def __iter__(self):
        """Allow unpacking like: table, join_type, condition = join_config"""
        yield self.table
        yield self.join_type
        yield self.condition


class FilterOperator(enum.Enum):
    EQUALS = "="
    NOT_EQUALS = "!="
    GREATER_THAN = ">"
    LESS_THAN = "<"
    GREATER_THAN_EQUALS = ">="
    LESS_THAN_EQUALS = "<="
    LIKE = "LIKE"
    IN = "IN"
    NOT_IN = "NOT IN"
    IS_NULL = "IS NULL"
    IS_NOT_NULL = "IS NOT NULL"


class FilterConfig(BaseModel):
    """Configuration for a WHERE clause filter condition"""
    table: str
    column: str
    operator: FilterOperator
    value: Optional[Any] = None
    
    @field_validator('value')
    def validate_value(cls, v, info):
        values = info.data
        if 'operator' in values:
            operator = values['operator']
            # For IS NULL and IS NOT NULL, value should be None
            if operator in [FilterOperator.IS_NULL, FilterOperator.IS_NOT_NULL] and v is not None:
                raise ValueError(f"Value must be None for {operator.value} operator")
            # For all other operators, value should not be None
            elif operator not in [FilterOperator.IS_NULL, FilterOperator.IS_NOT_NULL] and v is None:
                raise ValueError(f"Value cannot be None for {operator.value} operator")
            # For IN and NOT IN, value should be a list
            if operator in [FilterOperator.IN, FilterOperator.NOT_IN] and not isinstance(v, list):
                raise ValueError(f"Value must be a list for {operator.value} operator")
        return v
    
    def to_sql(self) -> str:
        """Convert the filter to a SQL WHERE clause fragment"""
        if self.operator in [FilterOperator.IS_NULL, FilterOperator.IS_NOT_NULL]:
            return f"{self.table}.{self.column} {self.operator.value}"
        elif self.operator in [FilterOperator.IN, FilterOperator.NOT_IN]:
            values = ", ".join([f"'{v}'" if isinstance(v, str) else str(v) for v in self.value])
            return f"{self.table}.{self.column} {self.operator.value} ({values})"
        elif self.operator == FilterOperator.LIKE:
            return f"{self.table}.{self.column} {self.operator.value} '{self.value}'"
        else:
            # Handle string values with quotes
            value_str = f"'{self.value}'" if isinstance(self.value, str) else str(self.value)
            return f"{self.table}.{self.column} {self.operator.value} {value_str}"


class FilterLogic(enum.Enum):
    AND = "AND"
    OR = "OR"


class FilterGroup(BaseModel):
    """Group of filter conditions with a specific logic operator"""
    filters: List[Union['FilterGroup', FilterConfig]]
    logic: FilterLogic = FilterLogic.AND

    def to_sql(self) -> str:
        """Convert the filter group to a SQL WHERE clause fragment"""
        if not self.filters:
            return ""

        filter_clauses = []
        for filter_item in self.filters:
            filter_clauses.append(filter_item.to_sql())
        
        # Join the clauses with the appropriate logic operator
        joined_clauses = f" {self.logic.value} ".join(filter_clauses)
        
        # Wrap in parentheses if there's more than one filter
        if len(self.filters) > 1:
            return f"({joined_clauses})"
        return joined_clauses



class TimeGranularity(enum.Enum):
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    QUARTER = "quarter"
    YEAR = "year"


class FieldType(enum.Enum):
    TEXT = "text"
    NUMBER = "number"
    DATE = "date"
    DATETIME = "datetime"
    BOOLEAN = "boolean"


class GroupByConfig(BaseModel):
    """Configuration for a GROUP BY clause with time granularity support"""
    table: str
    column: str
    field_type: FieldType
    time_granularity: Optional[TimeGranularity] = None
    
    @field_validator('time_granularity')
    def validate_time_granularity(cls, v, info):
        values = info.data
        if 'field_type' in values:
            field_type = values['field_type']
            # Time granularity only applies to date/datetime fields
            if v is not None and field_type not in [FieldType.DATE, FieldType.DATETIME]:
                raise ValueError(f"Time granularity can only be applied to DATE or DATETIME fields, not {field_type.value}")
        return v
    
    def to_sql(self) -> str:
        """Convert the group by to a SQL GROUP BY clause fragment"""
        if self.field_type in [FieldType.DATE, FieldType.DATETIME] and self.time_granularity:
            # Different databases have different date truncation functions
            # This example uses PostgreSQL's date_trunc
            return f"date_trunc('{self.time_granularity.value}', {self.table}.{self.column})"
        else:
            return f"{self.table}.{self.column}"


class OrderByConfig(BaseModel):
    """Configuration for an ORDER BY clause"""
    column: str
    direction: str = "ASC"
    
    @field_validator('direction')
    def validate_direction(cls, v):
        if v.upper() not in ["ASC", "DESC"]:
            raise ValueError('direction must be either "ASC" or "DESC"')
        return v.upper()

    def __iter__(self):
        """Allow unpacking like: column, direction = order_by_config"""
        yield self.column
        yield self.direction


class QueryConfig(BaseModel):
    """Complete configuration for a custom dataset"""
    name: str
    base_table: TableConfig
    additional_tables: List[JoinConfig] = Field(default_factory=list)
    filters: List[FilterConfig] = Field(default_factory=list)
    filter_group: FilterGroup | None = None
    columns: List[str] = Field(default_factory=list)
    group_bys: List[GroupByConfig] = Field(default_factory=list)
    order_bys: List[OrderByConfig] = Field(default_factory=list)
    limit: Optional[int] = None
    
    @field_validator('limit')
    def validate_limit(cls, v):
        if v is not None and v <= 0:
            raise ValueError('limit must be a positive integer')
        return v
    
    @field_validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('name cannot be empty')
        return v
