from uuid import UUID

import jinja2

from salestech_be.common.exception.exception import InvalidArgumentError
from salestech_be.core.reporting.type.filter import Filter
from salestech_be.core.reporting.type.query_config import (
    FilterConfig,
    FilterGroup,
    FilterLogic,
    QueryConfig,
)
from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.reporting import (
    ReportingDataset,
    ReportingDatasetType,
)
from salestech_be.ree_logging import get_logger


class QueryBuilderService:
    def __init__(self, db_engine: DatabaseEngine):
        self.reporting_repository = ReportingRepository(engine=db_engine)
        self.logger = get_logger()

    def merge_filters(
        self,
        filter_group: FilterGroup | None,
        filters: list[Filter] | None,
        dataset_name: str | None = None,
    ) -> FilterGroup | None:
        """
        Merge a list of Filter objects with an existing FilterGroup.

        Args:
            filter_group: Existing filter group to merge with (can be None)
            filters: List of Filter objects to add (can be None or empty)
            dataset_name: Optional dataset name to use for new filters

        Returns:
            FilterGroup: The merged filter group, or None if no filters provided
        """
        # If no new filters to add, return the existing filter group
        if not filters:
            return filter_group

        # Convert Filter objects to FilterConfig objects
        filter_configs = []
        for filter_obj in filters:
            try:
                filter_config = FilterConfig.from_filter(filter_obj, dataset_name)
                filter_configs.append(filter_config)
            except ValueError as e:
                self.logger.warning(f"Skipping invalid filter (col={filter_obj.col}, op={filter_obj.op}): {e}")
                continue

        # If no valid filters were created, return the existing filter group
        if not filter_configs:
            return filter_group

        # If no existing filter group, create a new one with AND logic
        if not filter_group:
            return FilterGroup(
                filters=filter_configs,
                logic=FilterLogic.AND,
            )

        # Merge with existing filter group based on its logic
        if filter_group.logic == FilterLogic.AND:
            # For AND logic, we can simply add the new filters to the existing group
            # This maintains the AND relationship: (existing filters) AND (new filters)
            merged_filters = list(filter_group.filters) + filter_configs
            return FilterGroup(
                filters=merged_filters,
                logic=FilterLogic.AND,
            )
        else:  # FilterLogic.OR
            # For OR logic, we need to be more careful about precedence
            # We want: (existing OR filters) AND (new filters)
            # So we wrap the existing OR group and add new filters with AND logic
            return FilterGroup(
                filters=[filter_group] + filter_configs,
                logic=FilterLogic.AND,
            )


    def build_query(
        self,
        *,
        query_config: QueryConfig,
        organization_id: UUID,
    ) -> str:
        """
        Builds a SQL query from a QueryConfig object.

        Args:
            config (QueryConfig): The configuration to build a query from

        Returns:
            str: The SQL query
        """
        # Build the SELECT clause
        query = f"SELECT {', '.join(column.to_sql() for column in query_config.columns)}\n"

        # Build the FROM clause
        query += f"FROM {query_config.primary_dataset.to_sql()}\n"

        # Build the JOIN clauses
        for join_config in query_config.additional_datasets:
            dataset_config, join_type, join_condition = join_config
            query += f"{join_type.to_sql()} JOIN {dataset_config.to_sql()} ON "
            query += f"{join_condition.left_dataset_field.to_sql()} "
            query += f"{join_condition.operator.to_sql()} "
            query += f"{join_condition.right_dataset_field.to_sql()}\n"

        # Build the WHERE clause if filters exist
        if query_config.filter_group:
            query += f"WHERE {query_config.filter_group.to_sql()}\n"

        # Build the GROUP BY clause if group_bys exist
        if query_config.group_bys:
            group_by_clauses = [group_by.to_sql() for group_by in query_config.group_bys]
            query += f"GROUP BY {', '.join(group_by_clauses)}\n"

        # Build the ORDER BY clause if order_bys exist
        if query_config.order_bys:
            order_clauses = [order_by.to_sql() for order_by in query_config.order_bys]
            query += f"ORDER BY {', '.join(order_clauses)}\n"

        # Add LIMIT clause if specified
        if query_config.limit is not None:
            query += f"LIMIT {query_config.limit}\n"

        return query.strip()

    async def build_dataset_query(
        self,
        *,
        dataset_id: UUID,
        filters: list[Filter] | None = None,
        organization_id: UUID,
    ) -> str:
        """
        Builds a SQL query for a dataset.

        Args:
            dataset_id (UUID): The ID of the dataset to build a query for
            filters (Optional[List[Filter]]): Optional filters to apply to the query

        Returns:
            str: The SQL query
        """
        # Get the dataset
        dataset = await self.reporting_repository.find_dataset_by_id(
            dataset_id=dataset_id,
            organization_id=organization_id,
        )

        # Build the query based on the dataset type
        if dataset.type == ReportingDatasetType.TABLE:
            return self._build_table_dataset_query(
                dataset=dataset,
                filters=filters,
                organization_id=organization_id,
            )
        elif dataset.type == ReportingDatasetType.QUERY:
            return self._build_query_dataset_query(
                dataset=dataset,
                filters=filters,
                organization_id=organization_id,
            )
        elif dataset.type == ReportingDatasetType.SQL:
            return self._build_sql_dataset_query(
                dataset=dataset,
                filters=filters,
                organization_id=organization_id,
            )
        else:
            raise InvalidArgumentError(f"Unsupported dataset type: {dataset.type}")

    def _build_table_dataset_query(
        self,
        *,
        dataset: ReportingDataset,
        filters: list[Filter] | None = None,
        organization_id: UUID,
    ) -> str:
        """
        Builds a SQL query for a table dataset.

        Args:
            dataset (ReportingDataset): The dataset to build a query for
            filters (Optional[List[Filter]]): Optional filters to apply to the query

        Returns:
            str: The SQL query
        """
        if not dataset.table_reference:
            raise ValueError(f"Dataset {dataset.id} has no table_reference")

        # Start with a simple SELECT * query
        query = f"SELECT * FROM {dataset.table_reference}"

        # Add filters if provided
        if filters:
            where_clauses = []
            for filter_obj in filters:
                try:
                    where_clauses.append(filter_obj.to_sql())
                except Exception as e:
                    self.logger.warning(f"Skipping invalid filter {filter_obj}: {e}")
                    continue

            if where_clauses:
                query += f" WHERE {' AND '.join(where_clauses)}"

        return query

    def _build_query_dataset_query(
        self,
        *,
        dataset: ReportingDataset,
        filters: list[Filter] | None = None,
        organization_id: UUID,
    ) -> str:
        """
        Builds a SQL query for a query dataset.

        Args:
            dataset (ReportingDataset): The dataset to build a query for
            filters (Optional[List[Filter]]): Optional filters to apply to the query

        Returns:
            str: The SQL query
        """
        if not dataset.query_config:
            raise ValueError(f"Dataset {dataset.id} has no query_config")

        # Parse the query config
        query_config = QueryConfig.model_validate(dataset.query_config)

        # Apply additional filters if provided
        if filters:
            # Use the merge_filters method to properly combine filters
            query_config.filter_group = self.merge_filters(
                filter_group=query_config.filter_group,
                filters=filters,
                dataset_name=dataset.name,
            )

        # Build the query
        return self.build_query(
            query_config=query_config,
            organization_id=organization_id,
        )

    def _build_sql_dataset_query(
        self,
        *,
        dataset: ReportingDataset,
        filters: list[Filter] | None = None,
        organization_id: UUID,
    ) -> str:
        """
        Builds a SQL query for a SQL dataset.

        Args:
            dataset (ReportingDataset): The dataset to build a query for
            filters (Optional[Dict[str, Any]]): Optional filters to apply to the query

        Returns:
            str: The SQL query
        """
        if not dataset.sql_statement:
            raise ValueError(f"Dataset {dataset.id} has no sql_statement")

        # If there are no filters, return the SQL as is
        if not filters:
            return dataset.sql_statement

        def get_filters(col=None):
            if col is None:
                return filters
            return [f for f in filters if f.col == col]

        def first_filter(col=None):
            for f in filters:
                if col is None or f.col == col:
                    return f
            return None

        def filter_values(col, col_alias=None):
            matches = [f for f in filters if f.col == col]
            if not matches:
                return None
            conditions = []
            for m in matches:
                conditions.append(m.to_sql(col_alias=col_alias))
            return " AND ".join(conditions)

        def and_filter_values(col, col_alias=None):
            matches = [f for f in filters if f.col == col]
            if not matches:
                return ""
            conditions = []
            for m in matches:
                conditions.append("AND " + m.to_sql(col_alias=col_alias))
            return "\n".join(conditions)

        def or_filter_values(col, col_alias=None):
            matches = [f for f in filters if f.col == col]
            if not matches:
                return ""
            conditions = []
            for m in matches:
                conditions.append("OR " + m.to_sql(col_alias=col_alias))
            return "\n".join(conditions)

        def filter_lower_boundary(col):
            for f in filters:
                if f.col != col:
                    continue
                if f.val is None:
                    continue
                if f.op.upper() == "BETWEEN":
                    return f.val[0]
                elif f.op.upper() in [">", ">="]:
                    return f.val
            return None

        def filter_upper_boundary(col):
            for f in filters:
                if f.col != col:
                    continue
                if f.val is None:
                    continue
                if f.op.upper() == "BETWEEN":
                    return f.val[1]
                elif f.op.upper() in ["<", "<="]:
                    return f.val
            return None

        # Use Jinja2 to render the template with filters
        template = jinja2.Template(dataset.sql_statement)
        template.globals["get_filters"] = get_filters
        template.globals["first_filter"] = first_filter
        template.globals["filter_values"] = filter_values
        template.globals["and_filter_values"] = and_filter_values
        template.globals["or_filter_values"] = or_filter_values
        template.globals["filter_lower_boundary"] = filter_lower_boundary
        template.globals["filter_upper_boundary"] = filter_upper_boundary
        raw_sql = template.render()
        return raw_sql.strip().rstrip(";")

    async def build_report_query(
        self,
        *,
        report_id: UUID,
        filters: list[Filter] | None = None,
        organization_id: UUID,
    ) -> str:
        """
        Builds a SQL query for a report.

        Args:
            report_id (UUID): The ID of the report to build a query for
            filters (Optional[List[Filter]]): Optional filters to apply to the query
            organization_id (UUID): The ID of the organization to build the query for

        Returns:
            str: The SQL query
        """
        # Get the report
        self.logger.info(
            f"Building query for report {report_id} with filters {filters}"
        )

        # Get the report from the repository
        report = await self.reporting_repository.find_report_by_id(
            report_id=report_id,
            organization_id=organization_id,
        )

        # Build a query for the dataset
        return await self.build_dataset_query(
            dataset_id=report.dataset_id,
            filters=filters,
            organization_id=organization_id,
        )
