"""
Test the query execution service.

Usage:
    python test_query_execution_service.py

Requirements:
    pip install asyncpg
"""

import asyncio
import datetime
import logging
from uuid import UUID

from salestech_be.core.reporting.connection.materialize_connection import (
    AsyncMaterializeConnection,
)
from salestech_be.core.reporting.service.query_execution_service import (
    QueryExecutionService,
)
from salestech_be.core.reporting.type.filter import Filter

# from salestech_be.core.reporting.type.query_type import (
#     TableConfig, JoinType, Join<PERSON>ondition, Join<PERSON>onfig, FilterOperator,
#     FilterConfig, FilterLogic, FilterGroup, TimeGranularity, FieldType,
#     GroupByConfig, OrderByConfig, QueryConfig
# )
from salestech_be.core.reporting.type.query_config import (
    DatasetConfig,
    DatasetFieldConfig,
    FieldColumnConfig,
    FieldConfig,
    QueryConfig,
)
from salestech_be.db.dbengine.core import DatabaseEngine

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("test_query_execution_service")

connection_params = {
    "user": "<EMAIL>",
    "password": "mzp_367ade2bd3064688ab3aadea72a4e59a2500c93285f646e186e4a6f5d4c71ead",
    "database": "mz_db_reevo_main_dev",
    "host": "c87i8hd8mkmx0djk0b9qk6w2n.lb.us-west-2.aws.materialize.cloud",
    "port": 6875,
    "ssl": True,
}


async def test_simple_list_tables():
    dwh_connection = AsyncMaterializeConnection(**connection_params)
    await dwh_connection.connect()
    print(await dwh_connection.list_tables())
    print(await dwh_connection.get_table_schema("contact"))
    print(await dwh_connection.execute_query("select * from contact limit 10"))


async def test_simple_query_config():
    """Run a test of the query execution service"""
    query_execution_service = QueryExecutionService(
        db_engine=DatabaseEngine(url="postgresql://localhost:5432/reevo_dev"),
        dwh_connection=AsyncMaterializeConnection(**connection_params),
    )
    dataset_config = DatasetConfig(dataset_id="123", dataset_name="contacts", alias="c")

    field_configs = [
        FieldColumnConfig(
            type="field",
            dataset_field=DatasetFieldConfig(
                dataset=dataset_config,
                field=FieldConfig(field_id="display_name", field_name="display_name"),
            ),
        )
    ]

    config = QueryConfig(
        primary_dataset=dataset_config, columns=field_configs, limit=10
    )
    results = await query_execution_service.execute_query(config)
    logger.info(f"Query results size: {len(results)}")
    for row in results:
        logger.info(f"{row}")


async def test_dataset_sql():
    """Run a test of the query execution service"""
    from salestech_be.settings import settings

    query_execution_service = QueryExecutionService(
        db_engine=DatabaseEngine(url=str(settings.db_url)),
        dwh_connection=AsyncMaterializeConnection(**connection_params),
    )
    dataset_id = UUID("141cc6c7-219e-4235-8ac0-d1f41753d7cd")
    filters = [
        # Filter(col="owner_user_id", op="in", val=["36ad3654-0978-41f6-b25d-51915d47a401"]),
        Filter(col="pipeline_amount", op=">=", val=1000),
        Filter(col="created_at", op=">=", val=datetime.datetime(2025, 1, 1)),
        Filter(col="some_column_not_exists", op="<=", val=12000),
        Filter(
            col="organization_id",
            op="=",
            val=UUID("c6734584-b3f3-4608-b62e-a993f4703a8e"),
        ),
        Filter(col="date_bucket", op="=", val="week"),
    ]
    results = await query_execution_service.execute_dataset(
        dataset_id=dataset_id, filters=filters
    )
    logger.info(f"Query results size: {len(results)}")
    for row in results:
        print(row)


if __name__ == "__main__":
    asyncio.run(test_dataset_sql())
