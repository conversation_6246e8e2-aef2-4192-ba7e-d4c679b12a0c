from uuid import UUID

from salestech_be.core.activity.service.activity_service import (
    ActivityService,
)
from salestech_be.core.activity.types import (
    ActivityPatchRequest,
)
from salestech_be.core.crm_integrity.event_processors.abstract_processor import (
    AbstractDataOperationProcessor,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
    DataAdditionParam,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationPreviewIdentifiers,
    IntegrityAssociatedDataOperationVoiceCallPreviewIdentifier,
)
from salestech_be.core.pipeline.service.pipeline_query_service import (
    PipelineQueryService,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.voice.v2.voice_call_data_integrity_service import (
    VoiceCallDataIntegrityService,
)
from salestech_be.db.models.activity import ActivityType
from salestech_be.db.models.crm_integrity import (
    AssociatedEntityField,
    AssociatedEntityFieldType,
    AssociatedEntityOperation,
    AssociatedEntityType,
    CRMIntegrityJobUserOption,
)
from salestech_be.db.models.voice_v2 import Call


class VoiceCallAccountAddToPipelineEventProcessor(
    AbstractDataOperationProcessor[DataAdditionParam, CrmIdReplacementTrackParam]
):
    """
    Event processor for Account Add to Pipeline within VoiceCall domain.
    """

    def __init__(
        self,
        voice_call_data_integrity_service: VoiceCallDataIntegrityService,
        pipeline_query_service: PipelineQueryService,
        activity_service: ActivityService,
    ) -> None:
        self.voice_call_data_integrity_service = voice_call_data_integrity_service
        self.pipeline_query_service = pipeline_query_service
        self.activity_service = activity_service

    async def fetch_pipeline(
        self, *, pipeline_id: UUID, organization_id: UUID
    ) -> PipelineV2:
        return await self.pipeline_query_service.get_pipeline_by_id(
            pipeline_id=pipeline_id,
            organization_id=organization_id,
        )

    async def fetch_affected_voice_calls(
        self, *, account_id: UUID, pipeline_id: UUID, organization_id: UUID
    ) -> tuple[list[Call], PipelineV2]:
        calls = (
            await self.voice_call_data_integrity_service.find_voice_calls_by_account_id(
                account_id=account_id,
                organization_id=organization_id,
            )
        )
        pipeline = await self.fetch_pipeline(
            pipeline_id=pipeline_id,
            organization_id=organization_id,
        )
        return [
            call
            for call in calls
            if (
                (call.contact_id and call.contact_id in pipeline.all_contact_ids)
                or (call.caller_id and call.caller_id in pipeline.all_contact_ids)
                or (call.recipient_id and call.recipient_id in pipeline.all_contact_ids)
            )
            and not call.pipeline_id
        ], pipeline

    async def process(
        self,
        *,
        param: DataAdditionParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[CrmIdReplacementTrackParam]:
        track_params = []

        calls, pipeline = await self.fetch_affected_voice_calls(
            account_id=param.src_entity_id,
            pipeline_id=param.dest_entity_id,
            organization_id=param.organization_id,
        )

        for call in calls:
            await self.voice_call_data_integrity_service.replace_pipeline_id_in_voice_call(
                call_id=call.id,
                pipeline_id=param.dest_entity_id,
                pipeline_stage_id=pipeline.stage_id,
            )

            track_params.append(
                CrmIdReplacementTrackParam(
                    entity_type=AssociatedEntityType.VOICE_CALL,
                    entity_id=call.id,
                    entity_field_name=AssociatedEntityField.PIPELINE_ID,
                    entity_field_type=AssociatedEntityFieldType.UUID,
                    entity_operation=AssociatedEntityOperation.UPDATE,
                    before_value=str(call.pipeline_id) if call.pipeline_id else None,
                    after_value=str(param.dest_entity_id),
                )
            )

        activities = (
            await self.activity_service.list_activities_by_reference_ids_and_type(
                organization_id=param.organization_id,
                reference_ids=[str(call.id) for call in calls],
                activity_type=ActivityType.VOICE_CALL,
            )
        )

        for activity in activities:
            await self.activity_service.patch_activity_by_id(
                activity_id=activity.id,
                organization_id=param.organization_id,
                req=ActivityPatchRequest(pipeline_id=param.dest_entity_id),
            )
            track_params.append(
                CrmIdReplacementTrackParam(
                    entity_type=AssociatedEntityType.ACTIVITY,
                    entity_id=activity.id,
                    entity_field_name=AssociatedEntityField.PIPELINE_ID,
                    entity_field_type=AssociatedEntityFieldType.UUID,
                    entity_operation=AssociatedEntityOperation.UPDATE,
                    before_value=str(activity.pipeline_id)
                    if activity.pipeline_id
                    else None,
                    after_value=str(param.dest_entity_id),
                )
            )

        return track_params

    async def preview(
        self,
        *,
        param: DataAdditionParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[IntegrityAssociatedDataOperationPreviewIdentifiers]:
        calls, _ = await self.fetch_affected_voice_calls(
            account_id=param.src_entity_id,
            pipeline_id=param.dest_entity_id,
            organization_id=param.organization_id,
        )
        call_to_meeting_map = (
            await self.voice_call_data_integrity_service.find_meetings_by_call_ids(
                call_ids=[call.id for call in calls],
                organization_id=param.organization_id,
            )
        )
        return [
            IntegrityAssociatedDataOperationPreviewIdentifiers(
                entity_type=AssociatedEntityType.VOICE_CALL,
                entity_ids=[
                    IntegrityAssociatedDataOperationVoiceCallPreviewIdentifier(
                        id=call.id,
                        meeting_id=call_to_meeting_map[call.id],
                    )
                    for call in calls
                ],
            )
        ]
