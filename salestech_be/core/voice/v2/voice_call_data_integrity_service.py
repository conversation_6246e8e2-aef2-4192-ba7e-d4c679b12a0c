from typing import Annotated
from uuid import UUID

from fastapi import Depends

from salestech_be.db.dao.meeting_repository import MeetingRepository
from salestech_be.db.dao.voice_call_repository import VoiceCallRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.meeting import MeetingReferenceIdType
from salestech_be.db.models.voice_v2 import Call
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none


class VoiceCallDataIntegrityService:
    def __init__(
        self,
        voice_call_repository: Annotated[VoiceCallRepository, Depends()],
        meeting_repository: Annotated[MeetingRepository, Depends()],
    ) -> None:
        self.voice_call_repository = voice_call_repository
        self.meeting_repository = meeting_repository

    async def find_voice_calls_by_account_id(
        self,
        *,
        account_id: UUID,
        organization_id: UUID,
    ) -> list[Call]:
        return await self.voice_call_repository.find_voice_calls_by_account_id(
            account_id=account_id,
            organization_id=organization_id,
        )

    async def find_voice_calls_by_contact_id(
        self,
        *,
        contact_id: UUID,
        organization_id: UUID,
    ) -> list[Call]:
        return await self.voice_call_repository.find_voice_calls_by_contact_id(
            contact_id=contact_id,
            organization_id=organization_id,
        )

    async def find_meetings_by_call_ids(
        self,
        *,
        call_ids: list[UUID],
        organization_id: UUID,
    ) -> dict[UUID, UUID | None]:
        meetings = await self.meeting_repository.list_meetings_by_reference_id_and_type(
            organization_id=organization_id,
            reference_ids=[str(call_id) for call_id in call_ids],
            reference_id_type=MeetingReferenceIdType.VOICE_V2,
        )
        call_to_meeting_map = {
            UUID(meeting.reference_id): meeting.id for meeting in meetings
        }
        return {call_id: call_to_meeting_map.get(call_id) for call_id in call_ids}

    async def find_voice_calls_by_contact_id_and_account_id(
        self,
        *,
        contact_id: UUID,
        account_id: UUID,
        organization_id: UUID,
    ) -> list[Call]:
        return await self.voice_call_repository.find_voice_calls_by_contact_id_and_account_id(
            contact_id=contact_id,
            account_id=account_id,
            organization_id=organization_id,
        )

    async def replace_account_id_in_voice_call(
        self,
        *,
        call_id: UUID,
        account_id: UUID | None,
    ) -> Call:
        return not_none(
            await self.voice_call_repository.update_by_id(
                call_id=call_id,
                column_to_update={
                    "account_id": account_id,
                    "updated_at": zoned_utc_now(),
                },
            )
        )

    async def replace_contact_id_in_voice_call(
        self,
        *,
        call_id: UUID,
        contact_id: UUID,
    ) -> Call:
        return not_none(
            await self.voice_call_repository.update_by_id(
                call_id=call_id,
                column_to_update={
                    "contact_id": contact_id,
                    "updated_at": zoned_utc_now(),
                },
            )
        )

    async def replace_pipeline_id_in_voice_call(
        self,
        *,
        call_id: UUID,
        pipeline_id: UUID,
        pipeline_stage_id: UUID,
    ) -> Call:
        return not_none(
            await self.voice_call_repository.update_by_id(
                call_id=call_id,
                column_to_update={
                    "pipeline_id": pipeline_id,
                    "pipeline_select_list_value_id": pipeline_stage_id,
                    "updated_at": zoned_utc_now(),
                },
            )
        )


def get_voice_call_data_integrity_service_from_engine(
    db_engine: DatabaseEngine,
) -> VoiceCallDataIntegrityService:
    return VoiceCallDataIntegrityService(
        voice_call_repository=VoiceCallRepository(engine=db_engine),
        meeting_repository=MeetingRepository(engine=db_engine),
    )
