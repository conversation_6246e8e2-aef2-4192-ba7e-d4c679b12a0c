from dataclasses import dataclass
from enum import Enum
from typing import Ann<PERSON><PERSON>, <PERSON><PERSON>, Type<PERSON><PERSON>s, TypeVar
from uuid import UUID

from anthropic.types import TextCitation
from pydantic import BaseModel, Discriminator

from salestech_be.core.meeting.types.meeting_query_types import TranscriptCitation
from salestech_be.core.opportunity_stage_criteria.base import BaseCriteriaContext
from salestech_be.core.opportunity_stage_criteria.standard_criteria.champion import (
    Champion,
    ChampionContext,
    ChampionItemUpdate,
    ChampionItemUpdateMulti,
    ChampionItemUpdateWithRecTypes,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.competition import (
    CompetitionContext,
    CompetitionContextUpdate,
    CompetitionContextUpdateWithRecTypes,
    Competitor,
    CompetitorItemUpdate,
    CompetitorItemUpdateMulti,
    CompetitorItemUpdateWithRecTypes,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.decision_criteria import (
    DecisionCriteriaContext,
    DecisionCriteriaContextUpdate,
    DecisionCriteriaContextUpdateWithRecTypes,
    DecisionCriteriaItem,
    DecisionCriteriaItemUpdate,
    DecisionCriteriaItemUpdateMulti,
    DecisionCriteriaItemUpdateWithRecTypes,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.decision_process import (
    DecisionProcessContext,
    DecisionProcessContextUpdate,
    DecisionProcessContextUpdateWithRecTypes,
    DecisionProcessItem,
    DecisionProcessItemUpdate,
    DecisionProcessItemUpdateMulti,
    DecisionProcessItemUpdateWithRecTypes,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.economic_buyer import (
    EconomicBuyerContext,
    EconomicBuyerItemUpdate,
    EconomicBuyerItemUpdateMulti,
    EconomicBuyerItemUpdateWithRecTypes,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.identified_pain import (
    IdentifiedPainItem,
    IdentifiedPainItemUpdate,
    IdentifiedPainItemUpdateMulti,
    IdentifiedPainItemUpdateWithRecTypes,
    IdentifiedPainPointContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.metric import (
    MetricContext,
    MetricContextUpdate,
    MetricContextUpdateWithRecTypes,
    MetricItem,
    MetricItemUpdate,
    MetricItemUpdateMulti,
    MetricItemUpdateWithRecTypes,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.paper_process import (
    PaperProcess,
    PaperProcessContext,
    PaperProcessItemUpdate,
    PaperProcessItemUpdateMulti,
    PaperProcessItemUpdateWithRecTypes,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.whys import (
    WhyAnyContext,
    WhyAnyContextUpdate,
    WhyAnyContextUpdateWithRecTypes,
    WhyNowContext,
    WhyNowContextUpdate,
    WhyNowContextUpdateWithRecTypes,
    WhyUsContext,
    WhyUsContextUpdate,
    WhyUsContextUpdateWithRecTypes,
)
from salestech_be.util.enum_util import NameValueStrEnum

# Define CriteriaContext as a Union of all context types

CriteriaContext: TypeAlias = Annotated[
    WhyAnyContext
    | WhyNowContext
    | WhyUsContext
    | DecisionCriteriaContext
    | DecisionProcessContext
    | PaperProcessContext
    | IdentifiedPainPointContext
    | ChampionContext
    | CompetitionContext
    | EconomicBuyerContext
    | MetricContext,
    Discriminator("context_type"),
]

CriteriaItem: TypeAlias = (
    Champion
    | Competitor
    | IdentifiedPainItem
    | MetricItem
    | DecisionCriteriaItem
    | DecisionProcessItem
    | PaperProcess
)

CriteriaContextUpdate: TypeAlias = (
    WhyAnyContextUpdate
    | WhyNowContextUpdate
    | WhyUsContextUpdate
    | DecisionCriteriaContextUpdate
    | DecisionProcessContextUpdate
    | CompetitionContextUpdate
    | MetricContextUpdate
)

CriteriaContextUpdateWithRecTypes: TypeAlias = (
    WhyAnyContextUpdateWithRecTypes
    | WhyNowContextUpdateWithRecTypes
    | WhyUsContextUpdateWithRecTypes
    | DecisionCriteriaContextUpdateWithRecTypes
    | CompetitionContextUpdateWithRecTypes
    | MetricContextUpdateWithRecTypes
    | DecisionProcessContextUpdateWithRecTypes
)

CriteriaItemUpdate: TypeAlias = (
    ChampionItemUpdate
    | CompetitorItemUpdate
    | EconomicBuyerItemUpdate
    | IdentifiedPainItemUpdate
    | MetricItemUpdate
    | PaperProcessItemUpdate
    | DecisionCriteriaItemUpdate
    | DecisionProcessItemUpdate
)

CriteriaItemUpdateWithRecTypes: TypeAlias = (
    ChampionItemUpdateWithRecTypes
    | CompetitorItemUpdateWithRecTypes
    | EconomicBuyerItemUpdateWithRecTypes
    | IdentifiedPainItemUpdateWithRecTypes
    | MetricItemUpdateWithRecTypes
    | PaperProcessItemUpdateWithRecTypes
    | DecisionCriteriaItemUpdateWithRecTypes
    | DecisionProcessItemUpdateWithRecTypes
)

CriteriaItemsUpdate: TypeAlias = (
    ChampionItemUpdateMulti
    | CompetitorItemUpdateMulti
    | EconomicBuyerItemUpdateMulti
    | IdentifiedPainItemUpdateMulti
    | MetricItemUpdateMulti
    | PaperProcessItemUpdateMulti
    | DecisionCriteriaItemUpdateMulti
    | DecisionProcessItemUpdateMulti
)

CriteriaContextT = TypeVar("CriteriaContextT", bound=BaseCriteriaContext)
CriteriaItemT = TypeVar("CriteriaItemT", bound=BaseModel)
CriteriaContextUpdateT = TypeVar("CriteriaContextUpdateT", bound=BaseModel)
CriteriaContextUpdateWithRecTypesT = TypeVar(
    "CriteriaContextUpdateWithRecTypesT", bound=BaseModel
)
CriteriaItemUpdateT = TypeVar("CriteriaItemUpdateT", bound=BaseModel)
CriteriaItemUpdateWithRecTypesT = TypeVar(
    "CriteriaItemUpdateWithRecTypesT", bound=BaseModel
)
CriteriaItemsUpdateT = TypeVar("CriteriaItemsUpdateT", bound=BaseModel)


class CriteriaExtractionSourceObjectType(NameValueStrEnum):
    MEETING = "MEETING"
    EMAIL = "EMAIL"
    CALL = "CALL"


class CriteriaExtractionSourceObjectId(BaseModel):
    """
    Identifies a source object from which criteria can be extracted.

    Attributes:
        object_type: The type of source object (meeting, email, call)
        object_id: The unique identifier of the source object
    """

    object_type: CriteriaExtractionSourceObjectType
    object_id: UUID


SourceObject: TypeAlias = CriteriaExtractionSourceObjectId | list[TranscriptCitation]


class CriteriaExtractionRequest(BaseModel, Generic[CriteriaContextT]):
    """
    Request model for extracting criteria from source objects.

    Attributes:
        expected_type: The expected type of criteria context to extract
        previous_context: The previous criteria context to use for extraction
    """

    expected_type: type[CriteriaContextT]
    previous_context: CriteriaContextT | None = None


class FieldInfo(BaseModel):
    """
    A field info for a criteria item.
    """

    field_name: str
    field_item_id: UUID | None = None


@dataclass
class FieldCitationPair:
    field: FieldInfo
    citation: TextCitation


class CriteriaCitation(BaseModel):
    """
    A citation for a criteria item.
    """

    id: UUID
    field_name: str
    field_item_id: UUID | None = None


class KeepCriteriaChange(BaseModel):
    """
    A model to determine if a criteria change should be kept.
    """

    should_keep_change: bool
    reason: str | None = None


class CriteriaExtractionResult(BaseModel, Generic[CriteriaContextT]):
    """
    Result model containing extracted criteria from source objects.

    Attributes:
        extracted_value: The extracted criteria context value
    """

    extracted_value: CriteriaContextT
    citations: list[CriteriaCitation] | None = None


class CriteriaExtractionMultiRequest(BaseModel):
    """
    Request model for extracting multiple criteria in a single request.

    Attributes:
        organization_id: The unique identifier of the organization
        pipeline_id: The unique identifier of the pipeline (opportunity)
        source_objects: list of source objects from which criteria can be extracted
        requests: Tuple of individual criteria extraction requests
    """

    organization_id: UUID
    pipeline_id: UUID
    source_objects: list[CriteriaExtractionSourceObjectId]
    requests: tuple[CriteriaExtractionRequest[CriteriaContext], ...]


class CriteriaExtractionMultiResult(BaseModel):
    """
    Result model containing multiple criteria extraction results.
    The order of the results is the same as the order of the requests.

    Attributes:
        results: Tuple of individual criteria extraction results
    """

    results: tuple[CriteriaExtractionResult[CriteriaContext], ...]


class CriteriaUpdateRequest(
    BaseModel,
    Generic[CriteriaContextT, CriteriaContextUpdateWithRecTypesT, CriteriaItemsUpdateT],
):
    """
    Request model for updating a criteria context.

    Attributes:
        update_type: The type of criteria context to update
        previous_context: The previous criteria context to use for updating
    """

    context_update_type: type[CriteriaContextUpdateWithRecTypesT] | None = None
    items_update_type: type[CriteriaItemsUpdateT] | None = None
    previous_context: str | None = None


class CriteriaUpdateResult(
    BaseModel, Generic[CriteriaContextUpdateWithRecTypesT, CriteriaItemsUpdateT]
):
    """
    Result model containing the updated criteria context.
    """

    updated_context: CriteriaContextUpdateWithRecTypesT | None = None
    updated_items: CriteriaItemsUpdateT | None = None
    citations: list[CriteriaCitation] | None = None


class LLMCriteriaUpdateRequest(BaseModel):
    """
    Request model for updating a criteria context in a single request.
    """

    organization_id: UUID
    pipeline_id: UUID
    source_object: SourceObject
    request: CriteriaUpdateRequest[
        CriteriaContext, CriteriaContextUpdateWithRecTypes, CriteriaItemsUpdate
    ]


class LLMCriteriaUpdateResult(BaseModel):
    """
    Result model containing a single criteria update result.
    """

    result: CriteriaUpdateResult[CriteriaContextUpdateWithRecTypes, CriteriaItemsUpdate]


class CriteriaVerificationRequest(BaseModel, Generic[CriteriaContextT]):
    """
    Request model for verifying an existing criteria context by extracting a reference criteria from source objects.

    Attributes:
        extraction_request: The request for extracting a reference criteria from source objects
        context_to_verify: The criteria context to verify against the reference criteria
    """

    extraction_request: CriteriaExtractionRequest[CriteriaContextT]
    context_to_verify: CriteriaContextT


class CriteriaVerificationResultStatus(NameValueStrEnum):
    """
    The status of the criteria verification result.
    """

    # The value is valid.
    VALID = "VALID"
    # The value is invalid.
    INVALID = "INVALID"
    # The value is inconclusive due to missing information.
    INCONCLUSIVE = "INCONCLUSIVE"


class CriteriaVerificationResult(BaseModel, Generic[CriteriaContextT]):
    """
    Result model containing the verification result for criteria.

    Attributes:
        extraction_result: The result of extracting criteria from source objects
        status: The verification status (valid, invalid, or inconclusive)
        explanation: A textual explanation of the verification result
    """

    extraction_result: CriteriaExtractionResult[CriteriaContextT]
    status: CriteriaVerificationResultStatus
    explanation: str | None = None


class CriteriaVerificationMultiRequest(BaseModel):
    """
    Request model for verifying multiple criteria in a single request.

    Attributes:
        organization_id: The unique identifier of the organization
        pipeline_id: The unique identifier of the pipeline (opportunity)
        source_objects: The list of source objects from which criteria are extracted
        requests: Tuple of individual criteria verification requests
    """

    organization_id: UUID
    pipeline_id: UUID
    source_objects: list[CriteriaExtractionSourceObjectId]
    requests: tuple[CriteriaVerificationRequest[CriteriaContext], ...]


class CriteriaVerificationMultiResult(BaseModel):
    """
    Result model containing multiple criteria verification results.
    The order of the results is the same as the order of the requests.

    Attributes:
        results: Tuple of individual criteria verification results
    """

    results: tuple[CriteriaVerificationResult[CriteriaContext], ...]


class ContextUpdateTypeEnum(Enum):
    DECISION_CRITERIA = (DecisionCriteriaContextUpdateWithRecTypes, "decision_criteria")
    DECISION_PROCESS = (DecisionProcessContextUpdateWithRecTypes, "decision_process")
    METRIC = (MetricContextUpdateWithRecTypes, "metric")
    WHY_ANY = (WhyAnyContextUpdateWithRecTypes, "why_any")
    WHY_NOW = (WhyNowContextUpdateWithRecTypes, "why_now")
    WHY_US = (WhyUsContextUpdateWithRecTypes, "why_us")
    COMPETITION = (CompetitionContextUpdateWithRecTypes, "competition")

    def __init__(
        self, update_type: type[CriteriaContextUpdateWithRecTypesT], context_type: str
    ):
        self.update_type = update_type
        self.context_type = context_type

    @classmethod
    def from_update_type(
        cls, update_type: type[CriteriaContextUpdateWithRecTypesT]
    ) -> str:
        for enum_value in cls:
            if enum_value.update_type == update_type:
                return enum_value.context_type
        raise ValueError(f"No enum value found for update type {update_type}")

    @classmethod
    def from_context_type(
        cls, context_type: str
    ) -> type[CriteriaContextUpdateWithRecTypesT]:
        for enum_value in cls:
            if enum_value.context_type == context_type:
                return enum_value.update_type  # type: ignore[return-value]
        raise ValueError(f"No enum value found for context type {context_type}")


class ItemsUpdateTypeEnum(Enum):
    DECISION_CRITERIA = (DecisionCriteriaItemUpdateMulti, "decision_criteria")
    DECISION_PROCESS = (DecisionProcessItemUpdateMulti, "decision_process")
    COMPETITION = (CompetitorItemUpdateMulti, "competition")
    ECONOMIC_BUYER = (EconomicBuyerItemUpdateMulti, "economic_buyer")
    IDENTIFIED_PAIN = (IdentifiedPainItemUpdateMulti, "identified_pain")
    METRIC = (MetricItemUpdateMulti, "metric")
    PAPER_PROCESS = (PaperProcessItemUpdateMulti, "paper_process")
    CHAMPION = (ChampionItemUpdateMulti, "champion")

    def __init__(self, update_type: type[CriteriaItemsUpdateT], context_type: str):
        self.update_type = update_type
        self.context_type = context_type

    @classmethod
    def from_update_type(cls, update_type: type[CriteriaItemsUpdateT]) -> str:
        for enum_value in cls:
            if enum_value.update_type == update_type:
                return enum_value.context_type
        raise ValueError(f"No enum value found for update type {update_type}")

    @classmethod
    def from_context_type(cls, context_type: str) -> type[CriteriaItemsUpdateT]:
        for enum_value in cls:
            if enum_value.context_type == context_type:
                return enum_value.update_type  # type: ignore[return-value]
        raise ValueError(f"No enum value found for context type {context_type}")
