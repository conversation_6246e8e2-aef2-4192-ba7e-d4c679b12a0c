from collections.abc import Sequence
from uuid import uuid4

from pydantic import BaseModel

from salestech_be.common.type.patch_request import UNSET
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaContextT,
    CriteriaContextUpdateT,
    CriteriaItemsUpdateT,
    FieldInfo,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.champion import (
    ChampionItemUpdateMulti,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.competition import (
    CompetitorItemUpdateMulti,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.decision_criteria import (
    DecisionCriteriaItemUpdateMulti,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.decision_process import (
    DecisionProcessItemUpdateMulti,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.economic_buyer import (
    EconomicBuyerItemUpdateMulti,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.identified_pain import (
    IdentifiedPainItemUpdateMulti,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.metric import (
    MetricItemUpdateMulti,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.paper_process import (
    PaperProcessItemUpdateMulti,
)

_ITEMS_FIELD_NAME_MAP = {
    DecisionCriteriaItemUpdateMulti: "decision_criteria_items",
    DecisionProcessItemUpdateMulti: "decision_process_items",
    ChampionItemUpdateMulti: "champions",
    CompetitorItemUpdateMulti: "competitors",
    EconomicBuyerItemUpdateMulti: "economic_buyers",
    IdentifiedPainItemUpdateMulti: "ordered_pain_items",
    MetricItemUpdateMulti: "metrics",
    PaperProcessItemUpdateMulti: "ordered_paper_processes",
}


def parse_criteria_context[T: BaseModel](
    context: T,
) -> list[FieldInfo]:
    result = []

    # Get attribute dictionary (use __fields__ for Pydantic v1, model_fields for v2)
    fields_dict = getattr(context, "model_fields", {})

    # Get all field names from model
    for field_name in fields_dict:
        # Skip specific fields
        if field_name in {"raw_details", "organization_id", "context_type"}:
            continue

        # Use model_dump() to safely get field values
        field_values = context.model_dump()
        field_value = field_values.get(field_name)

        # If the field is None, skip it
        if field_value is None:
            continue

        # Check if it's a sequence (list or tuple) but not a string
        if isinstance(field_value, Sequence) and not isinstance(
            field_value, (str, bytes)
        ):
            # For sequence fields, add entries with indices
            for index in range(len(field_value)):
                field_item = field_value[index]
                # Handle both dictionary and object cases
                item_id = (
                    field_item.get("id")
                    if isinstance(field_item, dict)
                    else getattr(field_item, "id", None)
                )
                result.append(FieldInfo(field_name=field_name, field_item_id=item_id))
        else:
            # For non-sequence fields, just add the field name
            result.append(FieldInfo(field_name=field_name, field_item_id=None))

    return result


def parse_update_criteria_items(
    items_result: BaseModel, items_update_type: type[CriteriaItemsUpdateT]
) -> list[FieldInfo]:
    result = []
    # Go through create, delete, update
    create = getattr(items_result, "create", None)
    delete = getattr(items_result, "delete", None)
    update = getattr(items_result, "update", None)
    if create is not None:
        for created_item in create:
            result.append(
                FieldInfo(
                    field_name=get_original_items_field_name(items_update_type),
                    field_item_id=created_item.id,
                )
            )
    if delete is not None:
        for deleted_item in delete:
            result.append(
                FieldInfo(
                    field_name=get_original_items_field_name(items_update_type),
                    field_item_id=deleted_item,
                )
            )
    if update is not None:
        for updated_item in update:
            result.append(
                FieldInfo(
                    field_name=get_original_items_field_name(items_update_type),
                    field_item_id=updated_item.updated_item.id,
                )
            )

    return result


def populate_update_create_item_ids(
    items_result: CriteriaItemsUpdateT,
) -> CriteriaItemsUpdateT:
    items_result_dict = items_result.model_dump()
    create = items_result_dict.get("create", [])
    if create is None:
        return items_result
    for created_item in create:
        created_item["id"] = uuid4()
    return items_result.model_validate(items_result_dict)


def get_original_items_field_name(items_update_type: type[BaseModel]) -> str:
    if items_update_type not in _ITEMS_FIELD_NAME_MAP:
        raise ValueError(f"Unsupported items update type: {items_update_type}")

    return _ITEMS_FIELD_NAME_MAP[items_update_type]


def populate_list_field_item_ids(
    context: CriteriaContextT, criteria_type: type[CriteriaContextT]
) -> CriteriaContextT:
    context_dict = context.model_dump()
    fields_dict = getattr(context, "model_fields", {})

    for field_name in fields_dict:
        field_value = context_dict.get(field_name)
        if isinstance(field_value, Sequence) and not isinstance(
            field_value, (str, bytes)
        ):
            # For each sequence field, update the items with new UUIDs
            updated_items = []
            for item in field_value:
                item_dict = item if isinstance(item, dict) else item.model_dump()
                item_dict["id"] = uuid4()
                updated_items.append(item_dict)
            context_dict[field_name] = updated_items

    return criteria_type.model_validate(context_dict)


def set_criteria_field_null_to_unset(
    field_update: CriteriaContextUpdateT,
) -> CriteriaContextUpdateT:
    model_dict = field_update.model_dump()
    for field_name, field_value in model_dict.items():
        if field_value is None:
            model_dict[field_name] = UNSET
    return field_update.model_validate(model_dict)


def set_criteria_item_field_null_to_unset(
    item_update: CriteriaItemsUpdateT,
) -> CriteriaItemsUpdateT:
    model_dict = item_update.model_dump()
    for field_name, field_value in model_dict.items():
        if field_value is None:
            model_dict[field_name] = UNSET
    return item_update.model_validate(model_dict)
