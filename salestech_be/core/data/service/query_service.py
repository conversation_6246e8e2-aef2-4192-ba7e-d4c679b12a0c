import asyncio
import dataclasses
import time
from collections import defaultdict
from collections.abc import Awaitable, Callable, Mapping, Sequence
from decimal import Decimal
from typing import (
    Annotated,
    Any,
    Literal,
    assert_never,
    cast,
)
from uuid import UUID

from fastapi import Depends
from frozendict import frozendict
from sentry_sdk import start_span

from salestech_be.common.exception import (
    IllegalStateError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.exception.exception import ForbiddenError, ServiceError
from salestech_be.common.query_util.baseline_filter_extraction import (
    BaselineFilterGroup,
    BaselineFilters,
    baseline_filter_extractor,
)
from salestech_be.common.query_util.domain_fetch_hints import DomainFetchHints
from salestech_be.common.query_util.filter_schema import (
    CompositeFilter,
    FilterSpec,
    FilterType,
    SingularFilterValue,
    ValueFilter,
)
from salestech_be.common.query_util.legacy.sort_schema import OrderEnum
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.query_util.sort_schema import (
    MultiPageTokenV1,
    NonRelationalSorter,
    NonRelationalSortingSpec,
    SinglePageTokenV1,
    Sorter,
    SortingSpec,
    as_non_relational_sorter_or_none,
    as_non_relational_sorting_spec_or_default,
)
from salestech_be.common.results import Cursor
from salestech_be.common.schema_manager.std_object_field_identifier import (
    AccountField,
    ContactField,
    PipelineField,
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    AccountRelationship,
    CommentRelationship,
    ContactAccountRoleRelationship,
    ContactPipelineRoleRelationship,
    ContactRelationship,
    CustomRecordRelationship,
    DomainObjectListItemRelationship,
    EmailAccountPoolRelationship,
    EmailAccountRelationship,
    EmailTemplateRelationship,
    GlobalThreadRelationship,
    MeetingRelationship,
    OutboundDomainRelationship,
    PipelineRelationship,
    ProspectingRunRelationship,
    ProspectingRunResultRelationship,
    SequenceEnrollmentContactRelationship,
    SequenceEnrollmentRelationship,
    SequenceEnrollmentRunRelationship,
    SequenceRelationship,
    SequenceStepExecutionRelationship,
    SignatureRelationship,
    TaskRelationship,
    UserGoalRelationship,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.stats.metric import custom_metric
from salestech_be.common.type.metadata.common import (
    CustomObjectIdentifier,
    ObjectIdentifier,
    ObjectKind,
    RelationshipId,
    StandardFieldIdentifier,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.schema import (
    FieldReference,
    QualifiedField,
)
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.core.account.service.account_query_service import (
    AccountQueryService,
    get_account_query_service,
)
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.activity.types import Activity
from salestech_be.core.citation.service.citation_query_service import (
    CitationQueryService,
    get_citation_query_service,
)
from salestech_be.core.citation.types import Citation
from salestech_be.core.comment.service.comment_query_service import (
    CommentQueryService,
    get_comment_query_service,
    get_comment_query_service_from_engine,
)
from salestech_be.core.comment.types import Comment, CommentReferenceIdType
from salestech_be.core.common.types import CustomizableDomainModel, UserAuthContext
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.conversation.service.conversation_query_service import (
    ConversationQueryService,
    get_conversation_query_service,
    get_conversation_query_service_from_engine,
)
from salestech_be.core.conversation.types.conversation_types import Conversation
from salestech_be.core.custom_object.service.association_service import (
    AssociationService,
    get_association_service,
)
from salestech_be.core.custom_object.service.custom_object_query_service import (
    CustomObjectQueryService,
    get_custom_object_query_service,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.data.service.falkor_lib import FalkorLib
from salestech_be.core.data.service.resolvers.activity_resolver import (
    ActivityResolver,
    get_activity_resolver,
)
from salestech_be.core.data.types import (
    CustomRecord,
    CustomRecordData,
    FetchContext,
    ModeledObjectRecord,
    ObjectRecord,
    ObjectRecordPrimaryDataUpdater,
    ObjectRecordT,
    StandardRecord,
    new_fetch_context,
)
from salestech_be.core.data.util import (
    ObjectRecordFetchConditions,
    extract_field,
    extract_field_references,
    extract_field_references_from_field_set,
    filter_record_sequence,
    sort_record_sequence,
)
from salestech_be.core.domain_object_list.service.domain_object_list_query_service import (
    DomainObjectListQueryService,
    get_domain_object_list_query_service,
    get_domain_object_list_query_service_by_db_engine,
)
from salestech_be.core.domain_object_list.types import (
    DomainObjectList,
    DomainObjectListItem,
    DomainObjectListItemType,
)
from salestech_be.core.email.account.email_account_query_service import (
    EmailAccountQueryService,
    get_email_account_query_service,
    get_email_account_query_service_by_db_engine,
)
from salestech_be.core.email.account.types import EmailAccountV2
from salestech_be.core.email.attachment.email_attachment_query_service import (
    EmailAttachmentQueryService,
    get_email_attachment_query_service,
    get_email_attachment_query_service_by_db_engine,
)
from salestech_be.core.email.global_email.attachment_details_type_v2 import (
    AttachmentDetailsV2,
)
from salestech_be.core.email.global_email.global_message_type import GlobalMessage
from salestech_be.core.email.global_email.global_thread_query_service import (
    GlobalThreadQueryService,
    get_global_thread_query_service,
)
from salestech_be.core.email.global_email.global_thread_type import GlobalThread
from salestech_be.core.email.outbound_domain.outbound_domain_query_service import (
    OutboundDomainQueryService,
    get_outbound_domain_query_service,
    get_outbound_domain_query_service_by_db_engine,
)
from salestech_be.core.email.outbound_domain.types_v2 import (
    OutboundDomainV2,
)
from salestech_be.core.email.pool.email_account_pool_query_service import (
    EmailAccountPoolQueryService,
    get_email_account_pool_query_service,
    get_email_account_pool_query_service_from_engine,
)
from salestech_be.core.email.pool.types_v2 import EmailAccountPoolV2
from salestech_be.core.email.template.email_template_query_service import (
    EmailTemplateQueryService,
    get_email_template_query_service,
    get_email_template_query_service_from_engine,
)
from salestech_be.core.email.template.types import EmailTemplate
from salestech_be.core.ff import feature_flag_service
from salestech_be.core.ff.types import FeatureFlagRequest
from salestech_be.core.goal.service.goal_query_service import (
    GoalQueryService,
    goal_query_service_by_db_engine,
    goal_query_service_by_request,
)
from salestech_be.core.goal.types import UserGoal
from salestech_be.core.meeting.service.meeting_query_service import (
    MeetingQueryService,
    get_meeting_query_service,
)
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
    get_select_list_service,
)
from salestech_be.core.metadata.service.metadata_service import (
    MetadataService,
    get_metadata_service,
)
from salestech_be.core.metadata.types import ContactAccountRole, ContactPipelineRole
from salestech_be.core.metadata.value_operators.extract import (
    ObjectRecordValueRetriever,
)
from salestech_be.core.metadata.value_operators.predicate import (
    FilterResult,
)
from salestech_be.core.metadata.value_operators.predicate_v2 import (
    ObjectRecordFilterEvaluatorV2,
)
from salestech_be.core.pipeline.service.pipeline_query_service import (
    PipelineQueryService,
    get_pipeline_query_service,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.prospecting.prospecting_credit_query_service import (
    ProspectingCreditUsageQueryService,
    get_prospecting_credit_usage_query_service,
    get_prospecting_credit_usage_query_service_by_db,
)
from salestech_be.core.prospecting.prospecting_filter_field_query_service import (
    ProspectingFilterFieldQueryService,
    get_prospecting_filter_field_query_service,
    get_prospecting_filter_field_query_service_by_db,
)
from salestech_be.core.prospecting.prospecting_run_query_service import (
    ProspectingRunQueryService,
    get_prospecting_run_query_service,
    get_prospecting_run_query_service_by_db,
)
from salestech_be.core.prospecting.prospecting_run_result_query_service import (
    ProspectingRunResultQueryService,
    get_prospecting_run_result_query_service,
    get_prospecting_run_result_query_service_by_db,
)
from salestech_be.core.prospecting.prospecting_saved_search_query_query_service import (
    ProspectingSavedSearchQueryQueryService,
    get_prospecting_saved_search_query_query_service,
    get_prospecting_saved_search_query_query_service_by_db,
)
from salestech_be.core.prospecting.type.credit import (
    ProspectingCreditUsagePointV2,
    ProspectingCreditUsageSummary,
)
from salestech_be.core.prospecting.type.filter_field import FilterFieldOptionsFacetV2
from salestech_be.core.prospecting.type.query import ProspectingSavedSearchQueryV2
from salestech_be.core.prospecting.type.run import ProspectingRunV2, RunResultV2
from salestech_be.core.schedule.event_schedule_query_service import (
    EventScheduleQueryService,
    get_event_schedule_query_service,
    get_event_schedule_query_service_from_engine,
)
from salestech_be.core.schedule.types import (
    EventSchedule,
)
from salestech_be.core.sequence.service.sequence_enrollment_query_service import (
    SequenceEnrollmentQueryService,
    get_sequence_enrollment_query_service,
    get_sequence_enrollment_query_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_execution_query_service import (
    SequenceExecutionQueryService,
    get_sequence_execution_query_service,
    get_sequence_execution_query_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_query_service import (
    SequenceQueryService,
    get_sequence_query_service,
    get_sequence_query_service_by_db,
)
from salestech_be.core.sequence.service.sequence_step_execution_query_service import (
    SequenceStepExecutionQueryService,
    get_sequence_step_execution_query_service,
    get_sequence_step_execution_query_service_by_db,
)
from salestech_be.core.sequence.service.sequence_step_query_service import (
    SequenceStepQueryService,
    get_sequence_step_query_service,
    get_sequence_step_query_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_step_variant_query_service import (
    SequenceStepVariantQueryService,
    get_sequence_step_variant_query_service,
    get_sequence_step_variant_query_service_by_db_engine,
)
from salestech_be.core.sequence.type.sequence_enrollment_contact_type import (
    SequenceEnrollmentContact,
)
from salestech_be.core.sequence.type.sequence_enrollment_run_type import (
    SequenceEnrollmentRun,
)
from salestech_be.core.sequence.type.sequence_enrollment_types import SequenceEnrollment
from salestech_be.core.sequence.type.sequence_step_execution_types import (
    SequenceStepExecution,
)
from salestech_be.core.sequence.type.sequence_step_types import SequenceStepV2
from salestech_be.core.sequence.type.sequence_step_variant_types import (
    SequenceStepVariantV2,
)
from salestech_be.core.sequence.type.sequence_v2 import SequenceV2
from salestech_be.core.task.service.task_query_service import (
    TaskQueryService,
    get_task_query_service,
    get_task_query_service_from_engine,
)
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.core.user.signature.signature_query_service import (
    SignatureQueryService,
    get_signature_query_service,
    get_signature_query_service_by_db_engine,
)
from salestech_be.core.user.signature.types_v2 import SignatureV2
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.db.dao.quota_repository import ProspectingCreditUsageAggregation
from salestech_be.db.dao.saved_domain_object_filter_repository import (
    SavedDomainObjectFilterRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.core.utils import cursor_to_offset_limit_with_overflow
from salestech_be.db.models.custom_field import CustomField
from salestech_be.db.models.custom_object_association import (
    CustomObjectAssociation,
)
from salestech_be.db.models.custom_object_data import CustomObjectData
from salestech_be.db.models.message import MessageStatus
from salestech_be.db.models.saved_domain_object_filter import (
    FilterError,
    SavedDomainObjectFilter,
    SavedDomainObjectFilterUpdate,
)
from salestech_be.db.models.sequence import EmailEventType
from salestech_be.falkordb.falkordb_factory import FalkorDBConnectionManager
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.copy_util import strict_model_copy
from salestech_be.util.uuid import safe_parse_uuid_or_none
from salestech_be.util.validation import not_none

logger = get_logger(__name__)


class DomainObjectQueryService:
    def __init__(
        self,
        metadata_service: Annotated[MetadataService, Depends()],
        saved_domain_object_filter_repository: Annotated[
            SavedDomainObjectFilterRepository, Depends()
        ],
        feature_flag_service: Annotated[
            feature_flag_service.FeatureFlagService,
            Depends(),
        ],
        falkordb_conn_mgr: Annotated[FalkorDBConnectionManager, Depends()],
        account_query_service: Annotated[AccountQueryService, Depends()],
        meeting_query_service: Annotated[MeetingQueryService, Depends()],
        contact_query_service: Annotated[ContactQueryService, Depends()],
        select_list_service: Annotated[InternalSelectListService, Depends()],
        user_service: Annotated[UserService, Depends()],
        goal_query_service: Annotated[
            GoalQueryService, Depends(goal_query_service_by_request)
        ],
        comment_query_service: Annotated[
            CommentQueryService, Depends(get_comment_query_service)
        ],
        pipeline_query_service: Annotated[PipelineQueryService, Depends()],
        task_query_service: Annotated[
            TaskQueryService, Depends(get_task_query_service)
        ],
        global_thread_query_service: Annotated[GlobalThreadQueryService, Depends()],
        activity_resolver: Annotated[ActivityResolver, Depends()],
        event_schedule_query_service: Annotated[
            EventScheduleQueryService, Depends(get_event_schedule_query_service)
        ],
        email_template_query_service: Annotated[
            EmailTemplateQueryService, Depends(get_email_template_query_service)
        ],
        domain_object_list_query_service: Annotated[
            DomainObjectListQueryService, Depends(get_domain_object_list_query_service)
        ],
        citation_query_service: Annotated[CitationQueryService, Depends()],
        conversation_query_service: Annotated[
            ConversationQueryService, Depends(get_conversation_query_service)
        ],
        custom_object_query_service: Annotated[
            CustomObjectQueryService, Depends(get_custom_object_query_service)
        ],
        association_service: Annotated[
            AssociationService, Depends(get_association_service)
        ],
        email_account_query_service: Annotated[
            EmailAccountQueryService, Depends(get_email_account_query_service)
        ],
        email_attachment_query_service: Annotated[
            EmailAttachmentQueryService, Depends(get_email_attachment_query_service)
        ],
        email_account_pool_query_service: Annotated[
            EmailAccountPoolQueryService, Depends(get_email_account_pool_query_service)
        ],
        sequence_query_service: Annotated[
            SequenceQueryService, Depends(get_sequence_query_service)
        ],
        prospecting_run_query_service: Annotated[
            ProspectingRunQueryService, Depends(get_prospecting_run_query_service)
        ],
        prospecting_run_result_query_service: Annotated[
            ProspectingRunResultQueryService,
            Depends(get_prospecting_run_result_query_service),
        ],
        outbound_domain_query_service: Annotated[
            OutboundDomainQueryService, Depends(get_outbound_domain_query_service)
        ],
        prospecting_credit_usage_query_service: Annotated[
            ProspectingCreditUsageQueryService,
            Depends(get_prospecting_credit_usage_query_service),
        ],
        prospecting_filter_field_query_service: Annotated[
            ProspectingFilterFieldQueryService,
            Depends(get_prospecting_filter_field_query_service),
        ],
        sequence_enrollment_query_service: Annotated[
            SequenceEnrollmentQueryService,
            Depends(get_sequence_enrollment_query_service),
        ],
        sequence_step_query_service: Annotated[
            SequenceStepQueryService, Depends(get_sequence_step_query_service)
        ],
        sequence_execution_query_service: Annotated[
            SequenceExecutionQueryService, Depends(get_sequence_execution_query_service)
        ],
        sequence_step_variant_query_service: Annotated[
            SequenceStepVariantQueryService,
            Depends(get_sequence_step_variant_query_service),
        ],
        prospecting_saved_search_query_query_service: Annotated[
            ProspectingSavedSearchQueryQueryService,
            Depends(get_prospecting_saved_search_query_query_service),
        ],
        signature_query_service: Annotated[
            SignatureQueryService, Depends(get_signature_query_service)
        ],
        sequence_step_execution_query_service: Annotated[
            SequenceStepExecutionQueryService,
            Depends(get_sequence_step_execution_query_service),
        ],
    ):
        self.metadata_service = metadata_service
        self.feature_flag_service = feature_flag_service
        self.saved_domain_object_filter_repository = (
            saved_domain_object_filter_repository
        )
        self.meeting_query_service = meeting_query_service
        self.account_query_service = account_query_service
        self.outbound_domain_query_service = outbound_domain_query_service
        self.contact_query_service = contact_query_service
        self.user_service = user_service
        self.goal_query_service = goal_query_service
        self.comment_query_service = comment_query_service
        self.pipeline_query_service = pipeline_query_service
        self.task_query_service = task_query_service
        self.global_thread_query_service = global_thread_query_service
        self.activity_resolver = activity_resolver
        self.select_list_service = select_list_service
        self.event_schedule_query_service = event_schedule_query_service
        self.email_template_query_service = email_template_query_service
        self.email_account_pool_query_service = email_account_pool_query_service
        self.email_account_query_service = email_account_query_service
        self.domain_object_list_query_service = domain_object_list_query_service
        self.citation_query_service = citation_query_service
        self.conversation_query_service = conversation_query_service
        self.custom_object_query_service = custom_object_query_service
        self.association_service = association_service
        self.email_attachment_query_service = email_attachment_query_service
        self.sequence_query_service = sequence_query_service
        self.prospecting_run_query_service = prospecting_run_query_service
        self.prospecting_run_result_query_service = prospecting_run_result_query_service
        self.prospecting_credit_usage_query_service = (
            prospecting_credit_usage_query_service
        )
        self.prospecting_filter_field_query_service = (
            prospecting_filter_field_query_service
        )
        self.sequence_enrollment_query_service = sequence_enrollment_query_service
        self.sequence_step_query_service = sequence_step_query_service
        self.sequence_execution_query_service = sequence_execution_query_service
        self.sequence_step_variant_query_service = sequence_step_variant_query_service
        self.prospecting_saved_search_query_query_service = (
            prospecting_saved_search_query_query_service
        )
        self.signature_query_service = signature_query_service
        self.sequence_step_execution_query_service = (
            sequence_step_execution_query_service
        )
        self.falkordb_conn_mgr = falkordb_conn_mgr

    async def evaluate_filter_results(
        self,
        *,
        primary_object_identifier: ObjectIdentifier,
        organization_id: UUID,
        record_id: UUID,
        filters: set[UUID] | list[SavedDomainObjectFilter],
        object_record_primary_data_updater: (
            ObjectRecordPrimaryDataUpdater | None
        ) = None,
    ) -> frozendict[UUID, FilterResult]:
        """
        Evaluate filter results for a given record against a set of filters.
        """

        # Get filters either from ids or use provided filters
        if isinstance(filters, set):
            filters_by_id = (
                await self.saved_domain_object_filter_repository.map_filter_by_id(
                    organization_id=organization_id,
                    filter_ids=filters,
                )
            )
        else:
            filters_by_id = frozendict(
                {filter_.id: filter_ for filter_ in not_none(filters)}
            )

        # Extract field references from all filters
        field_references: list[FieldReference] = []
        for filter_ in filters_by_id.values():
            field_references.extend(
                extract_field_references(
                    fetch_conditions=ObjectRecordFetchConditions(
                        filter_spec=filter_.filter_spec, sorting_spec=None, fields=None
                    )
                )
            )

        # Get the record to evaluate
        object_record = await self.get_generic_record(
            organization_id=organization_id,
            record_id=record_id,
            object_identifier=primary_object_identifier,
            field_references=field_references,
            include_custom_object=True,
        )
        if object_record_primary_data_updater:
            object_record = object_record.copy_with_update(
                primary_data_updater=object_record_primary_data_updater
            )

        # Evaluate each filter
        result_map: dict[UUID, FilterResult] = {}
        for filter_id, filter_ in filters_by_id.items():
            if filter_.filter_spec is None:
                result_map[filter_id] = FilterResult(is_match=True)
                continue
            try:
                filter_result = ObjectRecordFilterEvaluatorV2.evaluate_filter_spec(
                    object_record=object_record,
                    filter_spec=filter_.filter_spec,
                )
                result_map[filter_id] = FilterResult(
                    is_match=filter_result.is_match,
                    error_details=None,
                    object_record_values=filter_result.object_record_values or [],
                )
            except Exception as e:
                result_map[filter_id] = FilterResult(
                    is_match=False,
                    error_details=FilterError(
                        error_type=e.__class__.__name__, error_message=str(e)[:255]
                    ),
                )

        return frozendict(result_map)

    async def map_saved_filter_by_id(
        self, *, organization_id: UUID, filter_ids: set[UUID]
    ) -> frozendict[UUID, SavedDomainObjectFilter]:
        return await self.saved_domain_object_filter_repository.map_filter_by_id(
            organization_id=organization_id,
            filter_ids=filter_ids,
        )

    async def create_saved_filter(
        self,
        *,
        user_id: UUID,
        organization_id: UUID,
        filter_spec: FilterSpec,
        sorting_spec: SortingSpec | None = None,
        display_name: str | None = None,
        description: str | None = None,
    ) -> SavedDomainObjectFilter:
        object_identifier = filter_spec.primary_object_identifier
        if sorting_spec and sorting_spec.primary_object_identifier != object_identifier:
            raise InvalidArgumentError(
                "Sorting spec primary object identifier must match filter spec primary object identifier"
            )
        await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=object_identifier,
            fetch_conditions=ObjectRecordFetchConditions(
                filter_spec=filter_spec,
                sorting_spec=sorting_spec,
                fields=None,
            ),
        )
        return await self.saved_domain_object_filter_repository.create_filter(
            user_id=user_id,
            organization_id=organization_id,
            object_identifier=object_identifier,
            filter_spec=filter_spec,
            sorting_spec=sorting_spec,
            display_name=display_name,
            description=description,
        )

    async def patch_saved_filter(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        filter_id: UUID,
        filter_spec: UnsetAware[FilterSpec | None] = UNSET,
        sorting_spec: UnsetAware[SortingSpec | None] = UNSET,
        display_name: UnsetAware[str | None] = UNSET,
        description: UnsetAware[str | None] = UNSET,
    ) -> SavedDomainObjectFilter:
        saved_filter_update = SavedDomainObjectFilterUpdate(
            filter_spec=filter_spec,
            sorting_spec=sorting_spec,
            display_name=display_name,
            description=description,
            updated_by_user_id=user_id,
        )
        existing_filter: SavedDomainObjectFilter = (
            await self.map_saved_filter_by_id(
                organization_id=organization_id, filter_ids={filter_id}
            )
        )[filter_id]

        await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=existing_filter.object_identifier,
            fetch_conditions=ObjectRecordFetchConditions(
                filter_spec=(
                    saved_filter_update.filter_spec
                    if specified(saved_filter_update.filter_spec)
                    else existing_filter.filter_spec
                ),
                sorting_spec=(
                    saved_filter_update.sorting_spec
                    if specified(saved_filter_update.sorting_spec)
                    else existing_filter.sorting_spec
                ),
                fields=None,
            ),
        )

        return await self.saved_domain_object_filter_repository.patch_filter(
            organization_id=organization_id,
            filter_id=filter_id,
            saved_filter_update=saved_filter_update,
        )

    async def delete_saved_filter(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        filter_id: UUID,
    ) -> SavedDomainObjectFilter:
        return await self.saved_domain_object_filter_repository.delete_filter(
            organization_id=organization_id,
            filter_id=filter_id,
            user_id=user_id,
        )

    async def get_filter_by_id(
        self,
        *,
        filter_id: UUID,
        organization_id: UUID,
    ) -> SavedDomainObjectFilter:
        if (
            db_filter
            := await self.saved_domain_object_filter_repository.find_by_tenanted_primary_key(
                SavedDomainObjectFilter, id=filter_id, organization_id=organization_id
            )
        ):
            return db_filter
        raise ResourceNotFoundError(
            f"SavedDomainObjectFilter is not found in database. "
            f"organization_id: {organization_id}   filter_id: {filter_id}"
        )

    async def list_generic_records(
        self,
        *,
        organization_id: UUID,
        only_include_record_ids: UnsetAware[set[UUID]] = UNSET,
        object_identifier: ObjectIdentifier,
        field_references: list[FieldReference],
        include_custom_object: bool = False,
    ) -> Sequence[ModeledObjectRecord]:
        # Early return
        if specified(only_include_record_ids) and not only_include_record_ids:
            return []

        if not isinstance(object_identifier, StandardObjectIdentifier):
            raise NotImplementedError(
                f"Unsupported object identifier: {object_identifier}"
            )
        match object_identifier.object_name:
            case AccountV2.object_id.object_name:
                return await self.list_account_records(
                    organization_id=organization_id,
                    include_custom_object=include_custom_object,
                    only_include_account_ids=only_include_record_ids,
                    fetch_conditions=field_references,
                )
            case ContactV2.object_id.object_name:
                return await self.list_contact_records(
                    organization_id=organization_id,
                    only_include_contact_ids=only_include_record_ids,
                    fetch_conditions=field_references,
                    include_custom_object=include_custom_object,
                )
            case PipelineV2.object_id.object_name:
                return await self.list_pipeline_records(
                    organization_id=organization_id,
                    only_include_pipeline_ids=only_include_record_ids,
                    fetch_conditions=field_references,
                    include_custom_object=include_custom_object,
                )
            case _:
                raise NotImplementedError(
                    f"Unsupported object identifier: {object_identifier}"
                )

    async def get_generic_record(
        self,
        *,
        organization_id: UUID,
        record_id: UUID,
        object_identifier: ObjectIdentifier,
        field_references: list[FieldReference],
        include_custom_object: bool = False,
    ) -> ObjectRecord:
        object_records = await self.list_generic_records(
            organization_id=organization_id,
            only_include_record_ids={record_id},
            object_identifier=object_identifier,
            field_references=field_references,
            include_custom_object=include_custom_object,
        )
        if not object_records:
            raise ResourceNotFoundError(
                f"No object record found for {object_identifier} {record_id}"
            )
        if len(object_records) > 1:
            raise IllegalStateError(
                f"Expected 1 object record for {object_identifier} {record_id}, but got {len(object_records)}"
            )
        return object_records[0]

    async def filter_record_sequence(
        self,
        organization_id: UUID,
        filter_spec: FilterSpec,
        records: Sequence[ObjectRecordT],
    ) -> Sequence[ObjectRecordT]:
        return await filter_record_sequence(
            filter_spec=filter_spec,
            records=records,
        )

    async def sort_record_sequence(
        self,
        sorting_spec: SortingSpec,
        records: Sequence[ObjectRecordT],
    ) -> tuple[ObjectRecordT, ...]:
        return await sort_record_sequence(
            sorting_spec=sorting_spec,
            records=records,
        )

    async def validate_field_and_fetch_specs(
        self,
        organization_id: UUID,
        object_identifier: ObjectIdentifier,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> tuple[set[FieldReference], FilterSpec | None, SortingSpec | None, bool]:
        field_set = extract_field(fetch_conditions=fetch_conditions)
        field_references = extract_field_references_from_field_set(field_set=field_set)
        filter_spec = (
            fetch_conditions.filter_spec
            if isinstance(fetch_conditions, ObjectRecordFetchConditions)
            else None
        )
        sorting_spec = (
            fetch_conditions.sorting_spec
            if isinstance(fetch_conditions, ObjectRecordFetchConditions)
            else None
        )
        sorting_fields = extract_field(
            fetch_conditions=ObjectRecordFetchConditions(
                sorting_spec=sorting_spec, fields=None, filter_spec=None
            )
        )
        non_sorting_fields = field_set - sorting_fields
        await self.metadata_service.validate_field_set(
            organization_id=organization_id,
            object_identifier=object_identifier,
            fields=non_sorting_fields,
            validate_sortable=False,
        )
        await self.metadata_service.validate_field_set(
            organization_id=organization_id,
            object_identifier=object_identifier,
            fields=sorting_fields,
            validate_sortable=True,
        )
        should_include_custom_object = bool(
            any(field.has_custom_field() for field in field_set)
        )
        return field_references, filter_spec, sorting_spec, should_include_custom_object

    async def get_organization_user_record(
        self,
        record_id: UUID,
        organization_id: UUID,
        ordered_object_fields: (
            tuple[QualifiedField | FieldReference, ...] | None
        ) = None,
    ) -> Sequence[StandardRecord[OrganizationUserV2]]:
        organization_users = await self.list_organization_user_records(
            organization_id=organization_id,
            only_include_user_ids={record_id},
            fetch_conditions=ObjectRecordFetchConditions(
                fields=ordered_object_fields,
                filter_spec=None,
                sorting_spec=None,
            ),
        )
        if not organization_users:
            raise ResourceNotFoundError(
                f"No organization user record found for {record_id}"
            )
        return organization_users

    async def list_organization_user_records(
        self,
        organization_id: UUID,
        only_include_user_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        output_converter: (
            Callable[[OrganizationUserV2], Awaitable[OrganizationUserV2]] | None
        ) = None,
    ) -> Sequence[StandardRecord[OrganizationUserV2]]:
        # Early return
        if specified(only_include_user_ids) and not only_include_user_ids:
            return []

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=OrganizationUserV2.object_id.object_name,
                object_identifier=OrganizationUserV2.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            _,  # field_references (unused)
            filter_spec,
            sorting_spec,
            _,  # should_include_custom_object (unused)
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=OrganizationUserV2.object_id,
            fetch_conditions=fetch_conditions,
        )
        result = await self._list_organization_user_records(
            organization_id=organization_id,
            only_include_user_ids=only_include_user_ids,
            fetch_context=fetch_context,
            output_converter=output_converter,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def list_activity_records(
        self,
        organization_id: UUID,
        fetch_conditions: ObjectRecordFetchConditions,
        cursor: Cursor | None = None,
    ) -> tuple[Sequence[StandardRecord[Activity]], Cursor]:
        # Experimental pattern, don't follow this example!!!
        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=Activity.object_id.object_name,
                object_identifier=Activity.object_id,
                select_list_service=self.select_list_service,
                metadata_service=self.metadata_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        return await self.activity_resolver.list_activity_records(
            organization_id=organization_id,
            fetch_conditions=fetch_conditions,
            cursor=cursor,
        )

    # @log_timing(logger=logger)
    async def get_account_record(
        self,
        record_id: UUID,
        organization_id: UUID,
        include_custom_object: bool,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
        ordered_object_fields: (
            tuple[QualifiedField | FieldReference, ...] | None
        ) = None,
    ) -> Sequence[StandardRecord[AccountV2]]:
        accounts = await self.list_account_records(
            organization_id=organization_id,
            include_custom_object=include_custom_object,
            only_include_account_ids={record_id},
            user_id=user_id,
            user_auth_context=user_auth_context,
            fetch_conditions=ObjectRecordFetchConditions(
                fields=ordered_object_fields,
                filter_spec=None,
                sorting_spec=None,
            ),
        )
        if not accounts:
            raise ResourceNotFoundError(f"No account record found for {record_id}")
        return accounts

    # @log_timing(logger=logger)
    async def list_account_records(
        self,
        organization_id: UUID,
        include_custom_object: bool,
        only_include_account_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
    ) -> Sequence[StandardRecord[AccountV2]]:
        # Early return
        if specified(only_include_account_ids) and not only_include_account_ids:
            return []

        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=AccountV2.object_id,
            fetch_conditions=fetch_conditions,
        )
        if not sorting_spec:
            sorting_spec = SortingSpec(
                primary_object_identifier=AccountV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(
                            path=(AccountField.created_at,),
                        ),
                        order=OrderEnum.DESC,
                    ),
                ),
            )
        result = await self._list_account_records(
            user_id=user_id,
            organization_id=organization_id,
            user_auth_context=user_auth_context,
            only_include_account_ids=only_include_account_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def list_account_record_ids(
        self,
        *,
        organization_id: UUID,
        include_custom_object: bool,
        cursor: Cursor,
        only_include_account_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: ObjectRecordFetchConditions,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
    ) -> tuple[list[UUID], Cursor]:
        # Early return
        if specified(only_include_account_ids) and not only_include_account_ids:
            return ([], Cursor(total_number=0))

        if isinstance(
            fetch_conditions, ObjectRecordFetchConditions
        ) and FalkorLib.should_run_falkor_bg(enable_query_optimization_v3=True):
            try:
                falkor_result = await FalkorLib.try_compile_query(
                    organization_id=organization_id,
                    fetch_conditions=fetch_conditions,
                    entity_type=AccountV2.object_id.object_name,
                    object_identifier=AccountV2.object_id,
                    metadata_service=self.metadata_service,
                    select_list_service=self.select_list_service,
                    cursor=cursor,
                    falkordb_conn_mgr=self.falkordb_conn_mgr,
                )
                if not falkor_result:
                    return [], Cursor(total_number=0)
                return falkor_result
            except Exception as e:
                logger.opt(exception=e).warning(
                    "Error when trying to use falkordb index",
                )
        return [], Cursor(total_number=0)

    # @log_timing(logger=logger)
    async def list_account_records_v2(  # noqa: C901, PLR0912, PLR0915
        self,
        *,
        organization_id: UUID,
        include_custom_object: bool,
        cursor: Cursor,
        only_include_account_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
        enable_query_optimization_v3: bool = False,
    ) -> tuple[Sequence[StandardRecord[AccountV2]], Cursor]:
        # Early return
        if specified(only_include_account_ids) and not only_include_account_ids:
            return ([], Cursor(total_number=0))

        falkor_result = None
        if isinstance(
            fetch_conditions, ObjectRecordFetchConditions
        ) and FalkorLib.should_run_falkor_bg(
            enable_query_optimization_v3=enable_query_optimization_v3
        ):
            try:
                falkor_result = await FalkorLib.try_compile_query(
                    organization_id=organization_id,
                    fetch_conditions=fetch_conditions,
                    entity_type=AccountV2.object_id.object_name,
                    object_identifier=AccountV2.object_id,
                    metadata_service=self.metadata_service,
                    select_list_service=self.select_list_service,
                    cursor=cursor,
                    falkordb_conn_mgr=self.falkordb_conn_mgr,
                )
            except Exception as e:
                logger.opt(exception=e).warning(
                    "Error when trying to use falkordb index",
                )

        is_falkor_enabled = await self.feature_flag_service.is_enabled(
            request=FeatureFlagRequest(
                flag_key="filter_sort_paginate",
                organization_id=organization_id,
            )
        )
        logger.debug(
            "[list account records v2] is_falkor_enabled",
            organization_id=organization_id,
            user_id=user_id,
            enable_query_optimization_v3=enable_query_optimization_v3,
            is_falkor_enabled=is_falkor_enabled,
        )
        if is_falkor_enabled and enable_query_optimization_v3 and falkor_result:
            only_include_account_ids = set(falkor_result[0])
            postprocessed_result = await self.list_account_records(
                organization_id=organization_id,
                include_custom_object=include_custom_object,
                only_include_account_ids=only_include_account_ids,
                fetch_conditions=fetch_conditions,
                user_id=user_id,
                user_auth_context=user_auth_context,
            )
            if len(postprocessed_result) != len(only_include_account_ids):
                logger.warning(
                    "[list account records v2] result records lost in post-filtering",
                    len_postprocessed_result=len(postprocessed_result),
                    len_falkor_result=len(falkor_result[0]),
                    organization_id=organization_id,
                    user_id=user_id,
                    only_include_account_ids=only_include_account_ids,
                    fetch_conditions=fetch_conditions,
                    result_cursor=falkor_result[1],
                )
                failure_tags = [
                    "result_size_mismatch:True",
                    "entity_type:account",
                ]
                custom_metric.increment(
                    metric_name="graph.index.result_size_mismatch",
                    tags=failure_tags,
                )
                if len(postprocessed_result) == 0:
                    failure_tags.append("post_filter_result_size_zero:True")
                    custom_metric.increment(
                        metric_name="graph.index.post_filter_result_size_zero",
                        tags=failure_tags,
                    )
            return postprocessed_result, falkor_result[1]

        if specified(only_include_account_ids):
            if not only_include_account_ids:
                return [], Cursor(
                    total_number=0,
                    total_page_number=0,
                    page_size=cursor.page_size,
                    page_index=1,
                    page_tokens={},
                    requested_page_token=None,
                    has_more=False,
                )
            else:
                postprocessed_result = await self.list_account_records(
                    organization_id=organization_id,
                    include_custom_object=include_custom_object,
                    only_include_account_ids=only_include_account_ids,
                    fetch_conditions=fetch_conditions,
                    user_id=user_id,
                    user_auth_context=user_auth_context,
                )
                return postprocessed_result, Cursor(
                    total_number=len(postprocessed_result),
                    total_page_number=1,
                    page_size=len(postprocessed_result),
                    page_index=1,
                    page_tokens={},
                    requested_page_token=None,
                    has_more=False,
                )

        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=AccountV2.object_id,
            fetch_conditions=fetch_conditions,
        )

        last_record: StandardRecord[AccountV2] | None = None
        single_page_token: SinglePageTokenV1 | None = None
        if cursor.requested_page_token and (
            single_page_token := SinglePageTokenV1.from_b64str(
                cursor.requested_page_token
            )
        ):
            with logger.contextualize(
                action="find_offset_record",
            ):
                offset_records = await self._list_account_records(
                    user_id=user_id,
                    organization_id=organization_id,
                    user_auth_context=user_auth_context,
                    only_include_account_ids={single_page_token.record_id},
                    field_references=[],
                    fetch_context=fetch_context,
                    include_custom_object=False,
                )
                last_record = offset_records[0] if offset_records else last_record

        tie_breaker_sorters = (
            Sorter(
                field=QualifiedField(
                    path=(AccountField.created_at,),
                ),
                order=OrderEnum.DESC,
            ),
            Sorter(
                field=QualifiedField(
                    path=(AccountField.id,),
                ),
                order=OrderEnum.DESC,
            ),
        )
        if not sorting_spec:
            sorting_spec = SortingSpec(
                primary_object_identifier=AccountV2.object_id,
                ordered_sorters=tie_breaker_sorters,
            )
        else:
            sorting_spec = strict_model_copy(
                sorting_spec,
                ordered_sorters=(
                    *sorting_spec.ordered_sorters,
                    *tie_breaker_sorters,
                ),
            )

        baseline_filter_group = (
            baseline_filter_extractor.extract_baseline_filter_group(
                filter_spec=filter_spec,
                # todo(xw): add cached call for metadata or do we really need this? probably
                # just need to do the first layer without relational push down for now.
                # organization_schema_descriptor=await self.metadata_service.get_organization_domain_object_schema(
                #     organization_id=organization_id,
                # ),
            )
            if filter_spec
            else BaselineFilterGroup(baseline_filters_by_object_identifier={})
        )

        non_relational_sorting_spec: NonRelationalSortingSpec = (
            as_non_relational_sorting_spec_or_default(
                sorting_spec=sorting_spec,
                fallback=NonRelationalSortingSpec(
                    primary_object_identifier=AccountV2.object_id,
                    ordered_sorters=tuple(
                        not_none(as_non_relational_sorter_or_none(sorter=_sorter))
                        for _sorter in tie_breaker_sorters
                    ),
                ),
            )
        )

        single_page_size = cursor.page_size if cursor and cursor.page_size else 20
        effective_page_count = 5
        target_over_fetch_page_count = effective_page_count + 1
        target_over_fetch_total_items = single_page_size * target_over_fetch_page_count
        fetch_page_limit = target_over_fetch_total_items
        max_page_depth = target_over_fetch_total_items * 20

        initial_baseline_filters = (
            baseline_filter_group.baseline_filters_by_object_identifier.get(
                AccountV2.object_id,
                BaselineFilters(
                    must_filters=[],
                    must_not_filters=[],
                    at_least_one_filters=[],
                ),
            )
        )

        final_result: list[StandardRecord[AccountV2]] = []

        fetch_more = True
        while fetch_more:
            offset_selections: list[ValueFilter] = []
            if last_record and non_relational_sorting_spec.ordered_sorters:
                first_sorter = non_relational_sorting_spec.ordered_sorters[0]
                offset_value = ObjectRecordValueRetriever.get_representative_value_from_object_record__pending_rewrite(
                    object_record=last_record,
                    field=first_sorter.field,
                )
                if isinstance(offset_value, SingularFilterValue | Decimal):
                    offset_selections.append(
                        ValueFilter(
                            field=first_sorter.field,
                            operator=(
                                MatchOperator.GTE
                                if first_sorter.order == OrderEnum.ASC
                                else MatchOperator.LTE
                            ),
                            value=(
                                float(offset_value)
                                if isinstance(offset_value, Decimal)
                                else offset_value
                            ),
                        )
                    )

            if offset_selections:
                baseline_filters = dataclasses.replace(
                    initial_baseline_filters,
                    must_filters=initial_baseline_filters.must_filters
                    + offset_selections,
                )
            else:
                baseline_filters = initial_baseline_filters

            hint = DomainFetchHints(
                baseline_filters=baseline_filters,
                non_relational_sorting_spec=non_relational_sorting_spec,
                limit=fetch_page_limit,
            )

            with logger.contextualize(
                action="paginated_list_account_records",
                hint=hint,
            ):
                preprocessed_result = await self._list_account_records(
                    user_id=user_id,
                    organization_id=organization_id,
                    user_auth_context=user_auth_context,
                    only_include_account_ids=only_include_account_ids,
                    field_references=list(field_references),
                    fetch_context=fetch_context,
                    include_custom_object=include_custom_object
                    or should_include_custom_object,
                    hint=hint,
                )

            take_index = 0
            if last_record:
                for i, record in enumerate(preprocessed_result):
                    if record.data.id == last_record.data.id:
                        take_index = i + 1
                        break
            postprocessed_result = preprocessed_result[take_index:]

            if filter_spec and postprocessed_result:
                logger.debug("filter spec", filter_spec=filter_spec)
                postprocessed_result = await self.filter_record_sequence(
                    organization_id=organization_id,
                    filter_spec=filter_spec,
                    records=postprocessed_result,
                )
            if sorting_spec and postprocessed_result:
                postprocessed_result = await self.sort_record_sequence(
                    sorting_spec=sorting_spec,
                    records=postprocessed_result,
                )

            final_result.extend(postprocessed_result)
            fetch_more = (len(final_result) < target_over_fetch_total_items) and len(
                preprocessed_result
            ) >= fetch_page_limit

            if not fetch_more:
                break

            if last_record and preprocessed_result[-1].data.id == last_record.data.id:
                if fetch_page_limit == max_page_depth:
                    fetch_more = False
                    logger.critical(
                        "[list contact records] fetch_page_limit == max_page_depth, circuit breaking",
                        fetch_page_limit=fetch_page_limit,
                        max_page_depth=max_page_depth,
                        organization_id=organization_id,
                        user_id=user_id,
                        last_record=last_record,
                        hint=hint,
                    )

                fetch_page_limit = min(fetch_page_limit * 2, max_page_depth)

            last_record = preprocessed_result[-1]

        del fetch_context

        # chunk the total result into single pages and create tokens per each page
        over_fetched_page_results = [
            final_result[i : i + single_page_size]
            for i in range(0, len(final_result), single_page_size)
        ]
        effective_page_results = over_fetched_page_results[0:effective_page_count]
        has_more = len(over_fetched_page_results) > 1
        initial_page_number = single_page_token.page_number if single_page_token else 1

        total_number = None
        total_page_number = None
        if len(over_fetched_page_results) < target_over_fetch_page_count:
            total_page_number = initial_page_number + len(over_fetched_page_results) - 1
            total_number = (initial_page_number - 1) * single_page_size + len(
                final_result
            )

        token = MultiPageTokenV1(
            single_page_tokens=[
                SinglePageTokenV1(
                    record_id=page_result[-1].data.id,
                    page_number=initial_page_number + i + 1,
                )
                for i, page_result in enumerate(effective_page_results[:-1])
                if page_result
            ]
        )
        return (
            effective_page_results[0] if effective_page_results else [],
            Cursor(
                total_number=total_number,
                total_page_number=total_page_number,
                page_size=single_page_size,
                page_index=initial_page_number,
                page_tokens=token.to_generic_token(),
                has_more=has_more,
            ),
        )

    async def get_contact_record(
        self,
        record_id: UUID,
        organization_id: UUID,
        include_custom_object: bool,
        ordered_object_fields: (
            tuple[QualifiedField | FieldReference, ...] | None
        ) = None,
        user_id: UUID | None = None,
        contextual_account_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
    ) -> Sequence[StandardRecord[ContactV2]]:
        contacts = await self.list_contact_records(
            organization_id=organization_id,
            include_custom_object=include_custom_object,
            only_include_contact_ids={record_id},
            fetch_conditions=ObjectRecordFetchConditions(
                fields=ordered_object_fields,
                filter_spec=None,
                sorting_spec=None,
            ),
            user_id=user_id,
            contextual_account_id=contextual_account_id,
            user_auth_context=user_auth_context,
        )

        if not contacts:
            raise ResourceNotFoundError(f"No contact record found for {record_id}")
        return contacts

    async def list_contact_records(
        self,
        organization_id: UUID,
        include_custom_object: bool,
        only_include_contact_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        user_id: UUID | None = None,
        contextual_account_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
    ) -> Sequence[StandardRecord[ContactV2]]:
        # Early return
        if specified(only_include_contact_ids) and not only_include_contact_ids:
            return []

        logger.debug(
            "[list contact records v1] start",
            organization_id=organization_id,
            user_id=user_id,
            only_include_contact_ids=only_include_contact_ids,
        )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=ContactV2.object_id,
            fetch_conditions=fetch_conditions,
        )
        if not sorting_spec:
            sorting_spec = SortingSpec(
                primary_object_identifier=ContactV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(
                            path=(ContactField.created_at,),
                        ),
                        order=OrderEnum.DESC,
                    ),
                ),
            )
        result = await self._list_contact_records(
            user_id=user_id,
            organization_id=organization_id,
            contextual_account_id=contextual_account_id,
            user_auth_context=user_auth_context,
            only_include_contact_ids=only_include_contact_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        logger.debug(
            "[list contact records v1] end",
            organization_id=organization_id,
            user_id=user_id,
            only_include_contact_ids=only_include_contact_ids,
        )

        return result

    async def list_contact_record_ids(
        self,
        *,
        organization_id: UUID,
        include_custom_object: bool,
        cursor: Cursor,
        only_include_contact_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: ObjectRecordFetchConditions,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
    ) -> tuple[list[UUID], Cursor]:
        # Early return
        if specified(only_include_contact_ids) and not only_include_contact_ids:
            return ([], Cursor(total_number=0))

        if isinstance(
            fetch_conditions, ObjectRecordFetchConditions
        ) and FalkorLib.should_run_falkor_bg(enable_query_optimization_v3=True):
            try:
                falkor_result = await FalkorLib.try_compile_query(
                    organization_id=organization_id,
                    fetch_conditions=fetch_conditions,
                    entity_type=ContactV2.object_id.object_name,
                    object_identifier=ContactV2.object_id,
                    metadata_service=self.metadata_service,
                    select_list_service=self.select_list_service,
                    cursor=cursor,
                    falkordb_conn_mgr=self.falkordb_conn_mgr,
                )
                if not falkor_result:
                    return [], Cursor(total_number=0)
                return falkor_result
            except Exception as e:
                logger.opt(exception=e).warning(
                    "Error when trying to use falkordb index",
                )
        return [], Cursor(total_number=0)

    def inject_filterspec_perms(
        self,
        user_auth_context: UserAuthContext | None,
        filter_spec: FilterSpec | None,
        entity_type: type[CustomizableDomainModel],
    ) -> FilterSpec | None:
        """
        Injects permissions if the user is not an admin or super admin.

        If a FilterSpec is not provided, a new one will be created.
        Otherwise an AND operation will be performed against any existing filter.
        """

        # If there is no user auth context, we cannot filter by user specific fields.
        if not user_auth_context:
            logger.debug("no user auth context, skipping permissions FilterSpec")
            return filter_spec

        # No need to inject an additional filter if ADMIN or SUPER_ADMIN.
        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            logger.debug(
                "user is admin or super admin, skipping permissions FilterSpec"
            )
            return filter_spec

        # Create a new FilterSpec if one does not exist.
        if not filter_spec:
            logger.debug("no existing FilterSpec, creating new one")
            filter_spec = FilterSpec(
                primary_object_identifier=entity_type.object_id,
                filter=CompositeFilter(),
            )

        # Wrap an existing ValueFilter into a CompositeFilter since we need to AND it with permissions.
        # This is effectively a no-op if no FilterSpec was provided to this method.
        if filter_spec.filter.filter_type == FilterType.VALUE:
            logger.bind(filter_spec=filter_spec).debug(
                "existing FilterSpec is a ValueFilter, wrapping in a CompositeFilter"
            )
            filter_spec = FilterSpec(
                primary_object_identifier=entity_type.object_id,
                filter=CompositeFilter(
                    all_of=[filter_spec.filter],
                ),
            )

        logger.bind(filter_spec=filter_spec).debug("creating permissions FilterSpec")
        perms_filter_spec = CompositeFilter()

        owner_user_id_field = None
        participants_field = None

        match entity_type.__name__:
            case ContactV2.__name__:
                owner_user_id_field = QualifiedField(path=(ContactField.owner_user_id,))
                participants_field = QualifiedField(
                    path=(ContactField.participant_user_id_list,)
                )
            case _:
                # This should never happen. Due to the type narrowing above, we cannot use assert_never() here.
                logger.bind(entity_type=entity_type.__name__).error(
                    "Unexpected invocation of FilterSpec injection. This means this method was called with an entity type that is not supported."
                )
                raise ServiceError("Unexpected invocation of FilterSpec injection")

        if owner_user_id_field:
            perms_filter_spec.any_of.append(
                ValueFilter(
                    field=owner_user_id_field,
                    operator=MatchOperator.EQ,
                    value=user_auth_context.user_id,
                )
            )

        if participants_field:
            perms_filter_spec.any_of.append(
                ValueFilter(
                    field=participants_field,
                    operator=MatchOperator.CONTAINS,
                    value=user_auth_context.user_id,
                )
            )

        if filter_spec.filter.filter_type == FilterType.VALUE:
            raise ValueError(
                "unable to operate against a FilterSpec of type ValueFilter"
            )

        filter_spec.filter.all_of.append(perms_filter_spec)
        logger.bind(filter_spec=filter_spec).debug("injected permissions FilterSpec")
        return filter_spec

    async def list_contact_records_v2(  # noqa: C901, PLR0912, PLR0915
        self,
        organization_id: UUID,
        include_custom_object: bool,
        cursor: Cursor,
        only_include_contact_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
        enable_query_optimization_v3: bool | None = None,
    ) -> tuple[Sequence[StandardRecord[ContactV2]], Cursor]:
        # Early return
        if specified(only_include_contact_ids) and not only_include_contact_ids:
            return ([], Cursor(total_number=0))

        falkor_result = None
        if isinstance(
            fetch_conditions, ObjectRecordFetchConditions
        ) and FalkorLib.should_run_falkor_bg(
            enable_query_optimization_v3=enable_query_optimization_v3
        ):
            if (
                settings.enable_filterspec_perms
                or str(organization_id) in settings.enable_filterspec_perms_org_ids
            ):
                fetch_conditions = ObjectRecordFetchConditions(
                    filter_spec=self.inject_filterspec_perms(
                        filter_spec=fetch_conditions.filter_spec,
                        user_auth_context=user_auth_context,
                        entity_type=ContactV2,
                    ),
                    sorting_spec=fetch_conditions.sorting_spec,
                    fields=fetch_conditions.fields,
                )

            try:
                falkor_result = await FalkorLib.try_compile_query(
                    organization_id=organization_id,
                    fetch_conditions=fetch_conditions,
                    entity_type=ContactV2.object_id.object_name,
                    object_identifier=ContactV2.object_id,
                    metadata_service=self.metadata_service,
                    select_list_service=self.select_list_service,
                    cursor=cursor,
                    falkordb_conn_mgr=self.falkordb_conn_mgr,
                )
            except Exception as e:
                logger.opt(exception=e).warning(
                    "Error when trying to use falkordb index",
                )

        is_falkor_enabled = await self.feature_flag_service.is_enabled(
            request=FeatureFlagRequest(
                flag_key="filter_sort_paginate",
                organization_id=organization_id,
            )
        )
        logger.debug(
            "[list contact records v2] is_falkor_enabled",
            organization_id=organization_id,
            user_id=user_id,
            enable_query_optimization_v3=enable_query_optimization_v3,
            is_falkor_enabled=is_falkor_enabled,
        )
        if is_falkor_enabled and enable_query_optimization_v3 and falkor_result:
            only_include_contact_ids = set(falkor_result[0])
            postprocessed_result = await self.list_contact_records(
                organization_id=organization_id,
                include_custom_object=include_custom_object,
                only_include_contact_ids=only_include_contact_ids,
                fetch_conditions=fetch_conditions,
                user_id=user_id,
                user_auth_context=user_auth_context,
            )
            if len(postprocessed_result) != len(only_include_contact_ids):
                logger.warning(
                    "[list contact records v2] result records lost in post-filtering",
                    len_postprocessed_result=len(postprocessed_result),
                    len_falkor_result=len(falkor_result[0]),
                    organization_id=organization_id,
                    user_id=user_id,
                    only_include_contact_ids=only_include_contact_ids,
                    fetch_conditions=fetch_conditions,
                    result_cursor=falkor_result[1],
                )
                failure_tags = [
                    "result_size_mismatch:True",
                    "entity_type:contact",
                ]
                custom_metric.increment(
                    metric_name="graph.index.result_size_mismatch",
                    tags=failure_tags,
                )
                if len(postprocessed_result) == 0:
                    failure_tags.append("post_filter_result_size_zero:True")
                    custom_metric.increment(
                        metric_name="graph.index.post_filter_result_size_zero",
                        tags=failure_tags,
                    )
            return postprocessed_result, falkor_result[1]

        if specified(only_include_contact_ids):
            if not only_include_contact_ids:
                return [], Cursor(
                    total_number=0,
                    total_page_number=0,
                    page_size=cursor.page_size,
                    page_index=1,
                    page_tokens={},
                    requested_page_token=None,
                    has_more=False,
                )
            else:
                postprocessed_result = await self.list_contact_records(
                    organization_id=organization_id,
                    include_custom_object=include_custom_object,
                    only_include_contact_ids=only_include_contact_ids,
                    fetch_conditions=fetch_conditions,
                    user_id=user_id,
                    user_auth_context=user_auth_context,
                )
                return postprocessed_result, Cursor(
                    total_number=len(postprocessed_result),
                    total_page_number=1,
                    page_size=len(postprocessed_result),
                    page_index=1,
                    page_tokens={},
                    requested_page_token=None,
                    has_more=False,
                )

        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=ContactV2.object_id,
            fetch_conditions=fetch_conditions,
        )

        last_record: StandardRecord[ContactV2] | None = None
        single_page_token: SinglePageTokenV1 | None = None
        if cursor.requested_page_token and (
            single_page_token := SinglePageTokenV1.from_b64str(
                cursor.requested_page_token
            )
        ):
            offset_records = await self._list_contact_records(
                user_id=user_id,
                organization_id=organization_id,
                user_auth_context=user_auth_context,
                only_include_contact_ids={single_page_token.record_id},
                field_references=[],
                fetch_context=fetch_context,
                include_custom_object=False,
            )
            last_record = offset_records[0] if offset_records else last_record

        tie_breaker_sorters = (
            Sorter(
                field=QualifiedField(
                    path=(ContactField.created_at,),
                ),
                order=OrderEnum.DESC,
            ),
            Sorter(
                field=QualifiedField(
                    path=(ContactField.id,),
                ),
                order=OrderEnum.DESC,
            ),
        )
        if not sorting_spec:
            sorting_spec = SortingSpec(
                primary_object_identifier=ContactV2.object_id,
                ordered_sorters=tie_breaker_sorters,
            )
        else:
            sorting_spec = strict_model_copy(
                sorting_spec,
                ordered_sorters=(
                    *sorting_spec.ordered_sorters,
                    *tie_breaker_sorters,
                ),
            )

        baseline_filter_group = (
            baseline_filter_extractor.extract_baseline_filter_group(
                filter_spec=filter_spec,
                # todo(xw): add cached call for metadata or do we really need this? probably
                # just need to do the first layer without relational push down for now.
                # organization_schema_descriptor=await self.metadata_service.get_organization_domain_object_schema(
                #     organization_id=organization_id,
                # ),
            )
            if filter_spec
            else BaselineFilterGroup(baseline_filters_by_object_identifier={})
        )

        non_relational_sorting_spec: NonRelationalSortingSpec = (
            as_non_relational_sorting_spec_or_default(
                sorting_spec=sorting_spec,
                fallback=NonRelationalSortingSpec(
                    primary_object_identifier=ContactV2.object_id,
                    ordered_sorters=tuple(
                        not_none(as_non_relational_sorter_or_none(sorter=_sorter))
                        for _sorter in tie_breaker_sorters
                    ),
                ),
            )
        )

        single_page_size = cursor.page_size if cursor and cursor.page_size else 20
        effective_page_count = 5
        target_over_fetch_page_count = effective_page_count + 1
        target_over_fetch_total_items = single_page_size * target_over_fetch_page_count
        fetch_page_limit = target_over_fetch_total_items
        max_page_depth = target_over_fetch_total_items * 20

        initial_baseline_filters = (
            baseline_filter_group.baseline_filters_by_object_identifier.get(
                ContactV2.object_id,
                BaselineFilters(
                    must_filters=[],
                    must_not_filters=[],
                    at_least_one_filters=[],
                ),
            )
        )

        final_result: list[StandardRecord[ContactV2]] = []

        fetch_more = True
        _iteration_count = 0
        while fetch_more:
            _iteration_count += 1
            offset_selections: list[ValueFilter] = []
            logger.debug(
                "[list contact records] last_record",
                last_record=last_record,
                iteration_count=_iteration_count,
                organization_id=organization_id,
                user_id=user_id,
            )
            if last_record and non_relational_sorting_spec.ordered_sorters:
                first_sorter = non_relational_sorting_spec.ordered_sorters[0]
                """
                (= s1 and >s2) or (>s1)
                """
                offset_value = ObjectRecordValueRetriever.get_representative_value_from_object_record__pending_rewrite(
                    object_record=last_record,
                    field=first_sorter.field,
                )
                if isinstance(offset_value, SingularFilterValue | Decimal):
                    offset_selections.append(
                        ValueFilter(
                            field=first_sorter.field,
                            operator=(
                                MatchOperator.GTE
                                if first_sorter.order == OrderEnum.ASC
                                else MatchOperator.LTE
                            ),
                            value=(
                                float(offset_value)
                                if isinstance(offset_value, Decimal)
                                else offset_value
                            ),
                        )
                    )

            if offset_selections:
                baseline_filters = dataclasses.replace(
                    initial_baseline_filters,
                    must_filters=initial_baseline_filters.must_filters
                    + offset_selections,
                )
            else:
                baseline_filters = initial_baseline_filters

            hint = DomainFetchHints(
                baseline_filters=baseline_filters,
                non_relational_sorting_spec=non_relational_sorting_spec,
                limit=fetch_page_limit,
            )

            preprocessed_result = await self._list_contact_records(
                user_id=user_id,
                organization_id=organization_id,
                user_auth_context=user_auth_context,
                only_include_contact_ids=only_include_contact_ids,
                field_references=list(field_references),
                fetch_context=fetch_context,
                include_custom_object=include_custom_object
                or should_include_custom_object,
                hint=hint,
            )
            logger.debug(
                "[list contact records] preprocessed_result",
                hint=hint,
                preprocessed_result_0=(
                    {
                        "id": preprocessed_result[0].data.id,
                        "created_at": preprocessed_result[0].data.created_at,
                        "display_name": preprocessed_result[0].data.display_name,
                    }
                    if preprocessed_result
                    else None
                ),
                preprocessed_result_last=(
                    {
                        "id": preprocessed_result[-1].data.id,
                        "created_at": preprocessed_result[-1].data.created_at,
                        "display_name": preprocessed_result[-1].data.display_name,
                    }
                    if preprocessed_result
                    else None
                ),
                iteration_count=_iteration_count,
                organization_id=organization_id,
                user_id=user_id,
            )

            take_index = 0
            if last_record:
                for i, record in enumerate(preprocessed_result):
                    if record.data.id == last_record.data.id:
                        take_index = i + 1
                        break
            postprocessed_result = preprocessed_result[take_index:]
            logger.debug(
                "[list contact records] postprocessed_result before in-app filtering and sorting",
                postprocessed_result_0=(
                    {
                        "id": postprocessed_result[0].data.id,
                        "created_at": postprocessed_result[0].data.created_at,
                        "display_name": postprocessed_result[0].data.display_name,
                    }
                    if postprocessed_result
                    else None
                ),
                postprocessed_result_last=(
                    {
                        "id": postprocessed_result[-1].data.id,
                        "created_at": postprocessed_result[-1].data.created_at,
                        "display_name": postprocessed_result[-1].data.display_name,
                    }
                    if postprocessed_result
                    else None
                ),
                iteration_count=_iteration_count,
                organization_id=organization_id,
                user_id=user_id,
            )
            if filter_spec and postprocessed_result:
                logger.debug("filter spec", filter_spec=filter_spec)
                postprocessed_result = await self.filter_record_sequence(
                    organization_id=organization_id,
                    filter_spec=filter_spec,
                    records=postprocessed_result,
                )
            if sorting_spec and postprocessed_result:
                postprocessed_result = await self.sort_record_sequence(
                    sorting_spec=sorting_spec,
                    records=postprocessed_result,
                )

            final_result.extend(postprocessed_result)
            fetch_more = (len(final_result) < target_over_fetch_total_items) and len(
                preprocessed_result
            ) >= fetch_page_limit
            logger.debug(
                "[list contact records] postprocessed_result after in-app filtering and sorting",
                postprocessed_result_0=(
                    {
                        "id": postprocessed_result[0].data.id,
                        "created_at": postprocessed_result[0].data.created_at,
                        "display_name": postprocessed_result[0].data.display_name,
                    }
                    if postprocessed_result
                    else None
                ),
                postprocessed_result_last=(
                    {
                        "id": postprocessed_result[-1].data.id,
                        "created_at": postprocessed_result[-1].data.created_at,
                        "display_name": postprocessed_result[-1].data.display_name,
                    }
                    if postprocessed_result
                    else None
                ),
                iteration_count=_iteration_count,
                fetch_more=fetch_more,
                organization_id=organization_id,
                user_id=user_id,
            )

            if not fetch_more:
                break

            if last_record and preprocessed_result[-1].data.id == last_record.data.id:
                if fetch_page_limit == max_page_depth:
                    fetch_more = False
                    logger.critical(
                        "[list contact records] fetch_page_limit == max_page_depth, circuit breaking",
                        fetch_page_limit=fetch_page_limit,
                        max_page_depth=max_page_depth,
                        organization_id=organization_id,
                        user_id=user_id,
                        last_record=last_record,
                        hint=hint,
                    )

                fetch_page_limit = min(fetch_page_limit * 2, max_page_depth)
            # else:
            # fetch_page_limit = target_over_fetch_total_items

            last_record = preprocessed_result[-1]

        del fetch_context

        # chunk the total result into single pages and create tokens per each page
        over_fetched_page_results = [
            final_result[i : i + single_page_size]
            for i in range(0, len(final_result), single_page_size)
        ]
        effective_page_results = over_fetched_page_results[0:effective_page_count]
        has_more = len(over_fetched_page_results) > 1
        initial_page_number = single_page_token.page_number if single_page_token else 1

        total_number = None
        total_page_number = None
        if len(over_fetched_page_results) < target_over_fetch_page_count:
            total_page_number = initial_page_number + len(over_fetched_page_results) - 1
            total_number = (initial_page_number - 1) * single_page_size + len(
                final_result
            )

        token = MultiPageTokenV1(
            single_page_tokens=[
                SinglePageTokenV1(
                    record_id=page_result[-1].data.id,
                    page_number=initial_page_number + i + 1,
                )
                for i, page_result in enumerate(effective_page_results[:-1])
                if page_result
            ]
        )
        return (
            effective_page_results[0] if effective_page_results else [],
            Cursor(
                total_number=total_number,
                total_page_number=total_page_number,
                page_size=single_page_size,
                page_index=initial_page_number,
                page_tokens=token.to_generic_token(),
                has_more=has_more,
            ),
        )

    async def list_conversation_records(
        self,
        organization_id: UUID,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[Conversation]]:
        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=Conversation.object_id.object_name,
                object_identifier=Conversation.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=Conversation.object_id,
            fetch_conditions=fetch_conditions,
        )
        result = await self._list_conversation_records(
            organization_id=organization_id,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=should_include_custom_object,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def list_meeting_records(
        self,
        organization_id: UUID,
        user_id: UUID | None,
        include_custom_object: bool,
        only_include_meeting_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        only_include_meeting_for_user: UnsetAware[UUID] = UNSET,
        user_auth_context: UserAuthContext | None = None,
        cursor: Cursor | None = None,
        include_property_metadata: bool = False,
    ) -> tuple[Sequence[StandardRecord[MeetingV2]], Cursor | None]:
        # Early return
        if specified(only_include_meeting_ids) and not only_include_meeting_ids:
            return ([], Cursor(total_number=0))

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=MeetingV2.object_id.object_name,
                object_identifier=MeetingV2.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=MeetingV2.object_id,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_meeting_records(
            organization_id=organization_id,
            user_id=user_id,
            user_auth_context=user_auth_context,
            only_include_meeting_ids=only_include_meeting_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
            only_include_meeting_for_user=only_include_meeting_for_user,
            filter_spec=filter_spec,
            sorting_spec=sorting_spec,
            cursor=cursor,
            include_property_metadata=include_property_metadata,
        )

        del fetch_context
        new_cursor: Cursor | None = None
        if cursor:
            has_more: bool = len(result) > cursor.page_size if cursor else False
            new_cursor = Cursor(
                page_size=cursor.page_size,
                page_index=cursor.page_index,
                has_more=has_more,
            )
            result = result[0 : cursor.page_size]

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result, new_cursor

    async def list_pipeline_record_ids(
        self,
        *,
        organization_id: UUID,
        include_custom_object: bool,
        cursor: Cursor,
        only_include_pipeline_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: ObjectRecordFetchConditions,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
    ) -> tuple[list[UUID], Cursor]:
        # Early return
        if specified(only_include_pipeline_ids) and not only_include_pipeline_ids:
            return ([], Cursor(total_number=0))

        if isinstance(
            fetch_conditions, ObjectRecordFetchConditions
        ) and FalkorLib.should_run_falkor_bg(enable_query_optimization_v3=True):
            try:
                falkor_result = await FalkorLib.try_compile_query(
                    organization_id=organization_id,
                    fetch_conditions=fetch_conditions,
                    entity_type=PipelineV2.object_id.object_name,
                    object_identifier=PipelineV2.object_id,
                    metadata_service=self.metadata_service,
                    select_list_service=self.select_list_service,
                    cursor=cursor,
                    falkordb_conn_mgr=self.falkordb_conn_mgr,
                )
                if not falkor_result:
                    return [], Cursor(total_number=0)
                return falkor_result
            except Exception as e:
                logger.opt(exception=e).warning(
                    "Error when trying to use falkordb index",
                )
        return [], Cursor(total_number=0)

    async def list_pipeline_records_v2(
        self,
        organization_id: UUID,
        include_custom_object: bool,
        only_include_pipeline_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        exclude_archived: bool = True,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
        cursor: Cursor | None = None,
        enable_query_optimization_v3: bool | None = None,
    ) -> tuple[Sequence[StandardRecord[PipelineV2]], Cursor]:
        falkor_result = None
        if isinstance(
            fetch_conditions, ObjectRecordFetchConditions
        ) and FalkorLib.should_run_falkor_bg(
            enable_query_optimization_v3=enable_query_optimization_v3
        ):
            try:
                falkor_result = await FalkorLib.try_compile_query(
                    organization_id=organization_id,
                    fetch_conditions=fetch_conditions,
                    entity_type=PipelineV2.object_id.object_name,
                    object_identifier=PipelineV2.object_id,
                    metadata_service=self.metadata_service,
                    select_list_service=self.select_list_service,
                    cursor=cursor,
                    falkordb_conn_mgr=self.falkordb_conn_mgr,
                )
            except Exception as e:
                logger.opt(exception=e).warning(
                    "Error when trying to use falkordb index",
                )

        is_falkor_enabled = await self.feature_flag_service.is_enabled(
            request=FeatureFlagRequest(
                flag_key="filter_sort_paginate",
                organization_id=organization_id,
            )
        )
        logger.debug(
            "[list pipeline records v2] is_falkor_enabled",
            organization_id=organization_id,
            user_id=user_id,
            enable_query_optimization_v3=enable_query_optimization_v3,
            is_falkor_enabled=is_falkor_enabled,
        )
        if is_falkor_enabled and enable_query_optimization_v3 and falkor_result:
            only_include_pipeline_ids = set(falkor_result[0])
            postprocessed_result = await self.list_pipeline_records(
                organization_id=organization_id,
                include_custom_object=include_custom_object,
                only_include_pipeline_ids=only_include_pipeline_ids,
                fetch_conditions=fetch_conditions,
                user_id=user_id,
                user_auth_context=user_auth_context,
            )
            if len(postprocessed_result) != len(only_include_pipeline_ids):
                logger.warning(
                    "[list pipeline records v2] result records lost in post-filtering",
                    len_postprocessed_result=len(postprocessed_result),
                    len_falkor_result=len(falkor_result[0]),
                    organization_id=organization_id,
                    user_id=user_id,
                    only_include_pipeline_ids=only_include_pipeline_ids,
                    fetch_conditions=fetch_conditions,
                    result_cursor=falkor_result[1],
                )
                failure_tags = [
                    "result_size_mismatch:True",
                    "entity_type:pipeline",
                ]
                custom_metric.increment(
                    metric_name="graph.index.result_size_mismatch",
                    tags=failure_tags,
                )
                if len(postprocessed_result) == 0:
                    failure_tags.append("post_filter_result_size_zero:True")
                    custom_metric.increment(
                        metric_name="graph.index.post_filter_result_size_zero",
                        tags=failure_tags,
                    )
            return postprocessed_result, falkor_result[1]

        return [], Cursor()

    async def list_pipeline_records(
        self,
        organization_id: UUID,
        include_custom_object: bool,
        only_include_pipeline_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        exclude_archived: bool = True,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
    ) -> Sequence[StandardRecord[PipelineV2]]:
        # Early return
        if specified(only_include_pipeline_ids) and not only_include_pipeline_ids:
            return []

        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=PipelineV2.object_id,
            fetch_conditions=fetch_conditions,
        )
        if not sorting_spec:
            sorting_spec = SortingSpec(
                primary_object_identifier=PipelineV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(
                            path=(PipelineField.created_at,),
                        ),
                        order=OrderEnum.DESC,
                    ),
                ),
            )
        result = await self._list_pipeline_records(
            user_id=user_id,
            organization_id=organization_id,
            user_auth_context=user_auth_context,
            only_include_pipeline_ids=only_include_pipeline_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
            exclude_archived=exclude_archived,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def get_pipeline_record(
        self,
        record_id: UUID,
        organization_id: UUID,
        include_custom_object: bool,
        ordered_object_fields: (
            tuple[QualifiedField | FieldReference, ...] | None
        ) = None,
        user_auth_context: UserAuthContext | None = None,
    ) -> StandardRecord[PipelineV2]:
        pipelines = await self.list_pipeline_records(
            organization_id=organization_id,
            include_custom_object=include_custom_object,
            only_include_pipeline_ids={record_id},
            fetch_conditions=ObjectRecordFetchConditions(
                fields=ordered_object_fields,
                filter_spec=None,
                sorting_spec=None,
            ),
            exclude_archived=False,
            user_auth_context=user_auth_context,
        )
        if not pipelines:
            raise ResourceNotFoundError(f"No pipeline record found for {record_id}")
        return pipelines[0]

    # @log_timing(logger=logger)
    async def _list_pipeline_records(  # noqa: C901, PLR0912, PLR0915
        self,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        exclude_archived: bool = True,
        only_include_pipeline_ids: UnsetAware[set[UUID]] = UNSET,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
    ) -> Sequence[StandardRecord[PipelineV2]]:
        # Early return
        if specified(only_include_pipeline_ids) and not only_include_pipeline_ids:
            return []

        cached_pipelines: dict[UUID, PipelineV2] = fetch_context.pipeline_by_id
        pipeline_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_pipeline_ids - set(cached_pipelines.keys())
            if specified(only_include_pipeline_ids)
            else UNSET
        )
        fetched_pipelines = await self.pipeline_query_service.list_pipelines(
            organization_id=organization_id,
            only_include_pipeline_ids=pipeline_ids_to_fetch,
            include_custom_object=include_custom_object,
            exclude_archived=exclude_archived,
        )

        if user_auth_context:
            fetched_pipelines = (
                await self.pipeline_query_service.filter_viewable_records(
                    records=fetched_pipelines,
                    user_auth_context=user_auth_context,
                )
            )

        fetched_pipeline_by_id = {
            pipeline.id: pipeline for pipeline in fetched_pipelines
        }
        cached_pipelines.update(fetched_pipeline_by_id)

        pipeline_by_id: dict[UUID, PipelineV2] = (
            {
                pipeline_id: cached_pipelines[pipeline_id]
                for pipeline_id in only_include_pipeline_ids
                if pipeline_id in cached_pipelines
            }
            if specified(only_include_pipeline_ids)
            else fetched_pipeline_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_pipeline_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        for (
            _relationship_id,
            _field_references,
        ) in field_references_by_relationship_id.items():
            next_layer_field_references: list[FieldReference] = [
                fr.field
                for fr in _field_references
                if isinstance(fr.field, FieldReference)
            ]
            if _relationship_id in PipelineRelationship:
                _pipeline_relationship_id = PipelineRelationship(str(_relationship_id))
                match _pipeline_relationship_id:
                    case PipelineRelationship.pipeline__to__account:
                        account_id_to_pipeline_ids: dict[UUID, set[UUID]] = {}
                        for pipeline in pipeline_by_id.values():
                            account_id_to_pipeline_ids.setdefault(
                                pipeline.account_id, set()
                            ).add(pipeline.id)
                        account_records = await self._list_account_records(
                            organization_id=organization_id,
                            only_include_account_ids=set(
                                account_id_to_pipeline_ids.keys()
                            ),
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                            user_auth_context=user_auth_context,
                        )
                        for account_record in account_records:
                            for _pipeline_id in account_id_to_pipeline_ids[
                                account_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_pipeline_id[
                                    _pipeline_id
                                ][_relationship_id].append(account_record)
                    case (
                        PipelineRelationship.pipeline__to__owner_user
                        | PipelineRelationship.pipeline__to__created_by_user
                        | PipelineRelationship.pipeline__to__updated_by_user
                        | PipelineRelationship.pipeline__to__archived_by_user
                        | PipelineRelationship.pipeline__to__closed_by_user
                    ):
                        user_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in PipelineV2.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        user_id_to_pipeline_ids: dict[UUID, set[UUID]] = {}
                        for pipeline in pipeline_by_id.values():
                            if user_id := getattr(pipeline, user_id_field, None):
                                user_id_to_pipeline_ids.setdefault(user_id, set()).add(
                                    pipeline.id
                                )
                        user_records = await self._list_organization_user_records(
                            organization_id=organization_id,
                            only_include_user_ids=set(user_id_to_pipeline_ids.keys()),
                            fetch_context=fetch_context,
                        )
                        for user_record in user_records:
                            for _pipeline_id in user_id_to_pipeline_ids[
                                user_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_pipeline_id[
                                    _pipeline_id
                                ][_relationship_id].append(user_record)

                    case PipelineRelationship.pipeline__to__participant_users:
                        # map pipeline to participants.
                        pipeline_id_to_participant_user_ids: dict[UUID, set[UUID]] = {}
                        for pipeline in pipeline_by_id.values():
                            if _participant_user_ids := getattr(
                                pipeline, "participant_user_id_list", None
                            ):
                                pipeline_id_to_participant_user_ids.setdefault(
                                    pipeline.id, set()
                                ).update(_participant_user_ids)

                        # flatten list of sets to get the unique set of user IDs.
                        users: list[UUID] = [
                            item
                            for set_ in pipeline_id_to_participant_user_ids.values()
                            for item in set_
                        ]

                        participant_user_records: dict[
                            UUID, StandardRecord[OrganizationUserV2]
                        ] = {
                            user_record.data.id: user_record
                            for user_record in await self._list_organization_user_records(
                                organization_id=organization_id,
                                only_include_user_ids=set(users),
                                fetch_context=fetch_context,
                            )
                        }

                        # reassociate participant user IDs back to the pipeline via related_records.
                        for (
                            pipeline_id,
                            participant_user_ids,
                        ) in pipeline_id_to_participant_user_ids.items():
                            for participant_user_id in participant_user_ids:
                                related_objects_by_relationship_id_by_pipeline_id[
                                    pipeline_id
                                ][_relationship_id].append(
                                    participant_user_records[participant_user_id]
                                )

                    case PipelineRelationship.pipeline__to__primary_contact:
                        contact_id_to_pipeline_ids: dict[UUID, set[UUID]] = {}
                        for pipeline in pipeline_by_id.values():
                            if (
                                pipeline.primary_contact_id
                                and _relationship_id
                                == PipelineRelationship.pipeline__to__primary_contact
                            ):
                                contact_id_to_pipeline_ids.setdefault(
                                    pipeline.primary_contact_id, set()
                                ).add(pipeline.id)

                        contact_records = await self._list_contact_records(
                            organization_id=organization_id,
                            only_include_contact_ids=set(
                                contact_id_to_pipeline_ids.keys()
                            ),
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                            user_auth_context=user_auth_context,
                        )
                        for contact_record in contact_records:
                            for _pipeline_id in contact_id_to_pipeline_ids[
                                contact_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_pipeline_id[
                                    _pipeline_id
                                ][_relationship_id].append(contact_record)

                    case PipelineRelationship.pipeline__from__next_due_task:
                        task_id_to_pipeline_ids: dict[UUID, set[UUID]] = {}
                        next_due_task_by_pipeline_id: dict[
                            UUID, TaskV2
                        ] = await self.task_query_service.get_next_due_task_by_pipeline_id(
                            pipeline_ids=[
                                pipeline.id for pipeline in pipeline_by_id.values()
                            ],
                            organization_id=organization_id,
                        )
                        for (
                            pipeline_id,
                            next_due_task,
                        ) in next_due_task_by_pipeline_id.items():
                            task_id_to_pipeline_ids.setdefault(
                                next_due_task.id, set()
                            ).add(pipeline_id)

                        task_records = await self._list_task_records(
                            organization_id=organization_id,
                            only_include_task_ids=set(task_id_to_pipeline_ids.keys()),
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                            filter_spec=None,
                            sorting_spec=None,
                        )

                        for task_record in task_records:
                            for _pipeline_id in task_id_to_pipeline_ids[
                                task_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_pipeline_id[
                                    _pipeline_id
                                ][_relationship_id].append(task_record)
                    case PipelineRelationship.pipeline__from__contact_pipeline_role:
                        contact_pipeline_role_records = await self._list_contact_pipeline_role_record_by_contact_or_pipeline_ids(
                            organization_id=organization_id,
                            entity_ids=set(pipeline_by_id.keys()),
                            entity_type="pipeline",
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                            user_auth_context=user_auth_context,
                        )
                        for (
                            contact_pipeline_role_record
                        ) in contact_pipeline_role_records:
                            pipeline_id = contact_pipeline_role_record.data.pipeline_id
                            related_objects_by_relationship_id_by_pipeline_id[
                                pipeline_id
                            ][_relationship_id].append(contact_pipeline_role_record)
                    case _ as unreachable:
                        assert_never(unreachable)
            elif association_id := safe_parse_uuid_or_none(_relationship_id):
                # Handle custom associations
                association = await self.association_service.get_association_by_id(
                    organization_id=organization_id,
                    association_id=association_id,
                )
                related_records_from_association = await self.fetch_related_records(
                    organization_id=organization_id,
                    fetch_context=fetch_context,
                    association=association,
                    source_data_ids=list(pipeline_by_id.keys()),
                    field_references=next_layer_field_references,
                    current_object_name="pipeline",
                )
                for (
                    pipeline_id,
                    custom_records,
                ) in related_records_from_association.items():
                    related_objects_by_relationship_id_by_pipeline_id[pipeline_id][
                        _relationship_id
                    ].extend(custom_records)

        is_editable_by_pipeline_id_tasks: dict[UUID, asyncio.Task[bool]] = {}
        async with asyncio.TaskGroup() as tg:
            for pipeline_id, pipeline in pipeline_by_id.items():
                is_editable_by_pipeline_id_tasks[pipeline_id] = tg.create_task(
                    self.pipeline_query_service.is_editable(
                        user_auth_context=user_auth_context,
                        domain_object=pipeline,
                    )
                )

        result: list[StandardRecord[PipelineV2]] = []
        for pipeline_id, pipeline in pipeline_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_pipeline_id[
                    pipeline_id
                ].items()
            }
            is_editable = is_editable_by_pipeline_id_tasks[pipeline_id].result()
            result.append(
                StandardRecord[PipelineV2](
                    object_id=PipelineV2.object_id,
                    is_editable=is_editable,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=pipeline,
                )
            )
        return result

    async def _list_contact_pipeline_role_record_by_contact_or_pipeline_ids(  # noqa: C901, PLR0912
        self,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        entity_ids: set[UUID],
        entity_type: Literal["contact", "pipeline"],
        user_auth_context: UserAuthContext | None = None,
    ) -> Sequence[StandardRecord[ContactPipelineRole]]:
        """
        Query for ContactPipelineRole records, delegating to appropriate method based on filters.
        Similar to how ContactAccountRole queries are handled.
        """
        if not entity_ids:
            return []

        if entity_type == "contact":
            roles_by_entity_id = await self.pipeline_query_service.map_contact_pipeline_roles_by_contact_ids(
                organization_id=organization_id,
                contact_ids=entity_ids,
            )
        elif entity_type == "pipeline":
            roles_by_entity_id = await self.pipeline_query_service.map_contact_pipeline_roles_by_pipeline_ids(
                organization_id=organization_id,
                pipeline_ids=entity_ids,
            )
        else:
            assert_never(entity_type)

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_role_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for (
            _relationship_id,
            _field_references,
        ) in field_references_by_relationship_id.items():
            if _relationship_id not in ContactPipelineRoleRelationship:
                raise NotImplementedError(
                    f"Unsupported relationship in ContactPipelineRole: {_relationship_id}"
                )
            next_layer_field_references: list[FieldReference] = [
                fr.field
                for fr in _field_references
                if isinstance(fr.field, FieldReference)
            ]
            match ContactPipelineRoleRelationship(str(_relationship_id)):
                case ContactPipelineRoleRelationship.contact_pipeline_role__to__contact:
                    contact_ids = {
                        _role.contact_id
                        for _roles in roles_by_entity_id.values()
                        for _role in _roles
                    }
                    role_ids_by_contact_id: defaultdict[UUID, set[UUID]] = defaultdict(
                        set
                    )
                    for _roles in roles_by_entity_id.values():
                        for _role in _roles:
                            role_ids_by_contact_id[_role.contact_id].add(_role.id)
                    contact_records = await self._list_contact_records(
                        organization_id=organization_id,
                        only_include_contact_ids=contact_ids,
                        field_references=next_layer_field_references,
                        fetch_context=fetch_context,
                        include_custom_object=include_custom_object,
                        user_auth_context=user_auth_context,
                    )
                    for contact_record in contact_records:
                        for role_id in role_ids_by_contact_id[contact_record.data.id]:
                            related_objects_by_relationship_id_by_role_id[role_id][
                                _relationship_id
                            ].append(contact_record)
                case (
                    ContactPipelineRoleRelationship.contact_pipeline_role__to__pipeline
                ):
                    pipeline_ids = {
                        _role.pipeline_id
                        for _roles in roles_by_entity_id.values()
                        for _role in _roles
                    }
                    role_ids_by_pipeline_id: defaultdict[UUID, set[UUID]] = defaultdict(
                        set
                    )
                    for _roles in roles_by_entity_id.values():
                        for _role in _roles:
                            role_ids_by_pipeline_id[_role.pipeline_id].add(_role.id)
                    pipeline_records = await self._list_pipeline_records(
                        organization_id=organization_id,
                        only_include_pipeline_ids=pipeline_ids,
                        field_references=next_layer_field_references,
                        fetch_context=fetch_context,
                        include_custom_object=include_custom_object,
                        user_auth_context=user_auth_context,
                    )
                    for pipeline_record in pipeline_records:
                        for role_id in role_ids_by_pipeline_id[pipeline_record.data.id]:
                            related_objects_by_relationship_id_by_role_id[role_id][
                                _relationship_id
                            ].append(pipeline_record)
                case _ as unreachable:
                    assert_never(unreachable)

        result: list[StandardRecord[ContactPipelineRole]] = []
        for roles in roles_by_entity_id.values():
            for role in roles:
                related_records: dict[
                    RelationshipId, tuple[ModeledObjectRecord, ...]
                ] = {
                    relationship_id: tuple(related)
                    for relationship_id, related in related_objects_by_relationship_id_by_role_id[
                        role.id
                    ].items()
                }
                result.append(
                    StandardRecord[ContactPipelineRole](
                        object_id=ContactPipelineRole.object_id,
                        requested_relationships=set(
                            field_references_by_relationship_id.keys()
                        ),
                        related_records=related_records,
                        data=role,
                    )
                )
        return result

    async def list_user_goal_records(
        self,
        organization_id: UUID,
        include_custom_object: bool,
        only_include_user_goal_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[UserGoal]]:
        # Early return
        if specified(only_include_user_goal_ids) and not only_include_user_goal_ids:
            return []

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=UserGoal.object_id.object_name,
                object_identifier=UserGoal.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=UserGoal.object_id,
            fetch_conditions=fetch_conditions,
        )
        result = await self._list_user_goal_records(
            organization_id=organization_id,
            only_include_user_goal_ids=only_include_user_goal_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def _list_user_goal_records(
        self,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_user_goal_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> Sequence[StandardRecord[UserGoal]]:
        # Early return
        if specified(only_include_user_goal_ids) and not only_include_user_goal_ids:
            return []

        cached_user_goals: dict[UUID, UserGoal] = fetch_context.user_goal_by_id
        user_goal_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_user_goal_ids - set(cached_user_goals.keys())
            if specified(only_include_user_goal_ids)
            else UNSET
        )
        fetched_user_goals = await self.goal_query_service.list_user_goals(
            organization_id=organization_id,
            only_include_user_goal_ids=user_goal_ids_to_fetch,
        )
        fetched_user_goal_by_id = {
            user_goal.id: user_goal for user_goal in fetched_user_goals
        }
        cached_user_goals.update(fetched_user_goal_by_id)

        user_goals_by_id: dict[UUID, UserGoal] = (
            {
                user_goal_id: cached_user_goals[user_goal_id]
                for user_goal_id in only_include_user_goal_ids
                if user_goal_id in cached_user_goals
            }
            if specified(only_include_user_goal_ids)
            else fetched_user_goal_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_user_goal_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for _relationship_id in field_references_by_relationship_id:
            if _relationship_id in UserGoalRelationship:
                _user_goal_relationship_id = UserGoalRelationship(str(_relationship_id))
                match _user_goal_relationship_id:
                    case (
                        UserGoalRelationship.user_goal__to__archived_by_user
                        | UserGoalRelationship.user_goal__to__created_by_user
                        | UserGoalRelationship.user_goal__to__assigned_to_user
                        | UserGoalRelationship.user_goal__to__updated_by_user
                    ):
                        user_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in UserGoal.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        user_id_to_user_goal_ids: dict[UUID, set[UUID]] = {}
                        for goal in user_goals_by_id.values():
                            if user_id := getattr(goal, user_id_field, None):
                                user_id_to_user_goal_ids.setdefault(user_id, set()).add(
                                    goal.id
                                )
                        user_records = await self._list_organization_user_records(
                            organization_id=organization_id,
                            only_include_user_ids=set(user_id_to_user_goal_ids.keys()),
                            fetch_context=fetch_context,
                        )
                        for user_record in user_records:
                            for _goal_id in user_id_to_user_goal_ids[
                                user_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_user_goal_id[
                                    _goal_id
                                ][_relationship_id].append(user_record)
                    case _ as unreachable:
                        assert_never(unreachable)
            else:
                raise NotImplementedError(
                    f"Unsupported custom object relationship: {_relationship_id}"
                )
        result: list[StandardRecord[UserGoal]] = []
        for user_goal_id, user_goal in user_goals_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_user_goal_id[
                    user_goal_id
                ].items()
            }
            result.append(
                StandardRecord[UserGoal](
                    object_id=UserGoal.object_id,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=user_goal,
                )
            )
        return result

    async def get_task_record(
        self,
        user_id: UUID,
        record_id: UUID,
        organization_id: UUID,
        user_auth_context: UserAuthContext,
        include_custom_object: bool,
        ordered_object_fields: (
            tuple[QualifiedField | FieldReference, ...] | None
        ) = None,
    ) -> Sequence[StandardRecord[TaskV2]]:
        tasks = await self.list_task_records(
            user_id=user_id,
            organization_id=organization_id,
            user_auth_context=user_auth_context,
            include_custom_object=include_custom_object,
            only_include_task_ids={record_id},
            fetch_conditions=ObjectRecordFetchConditions(
                fields=ordered_object_fields,
                filter_spec=None,
                sorting_spec=None,
            ),
        )
        if not tasks:
            raise ResourceNotFoundError(f"No task record found for {record_id}")
        return tasks

    # @log_timing(logger=logger)
    async def list_task_records(
        self,
        organization_id: UUID,
        include_custom_object: bool,
        only_include_task_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
    ) -> Sequence[StandardRecord[TaskV2]]:
        # Early return
        if specified(only_include_task_ids) and not only_include_task_ids:
            return []

        # if isinstance(fetch_conditions, ObjectRecordFetchConditions):
        #     await self.try_compile_query(
        #         organization_id=organization_id,
        #         fetch_conditions=fetch_conditions,
        #         entity_type=TaskV2.object_id.object_name,
        #         object_identifier=TaskV2.object_id,
        #     )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=TaskV2.object_id,
            fetch_conditions=fetch_conditions,
        )
        result = await self._list_task_records(
            user_id=user_id,
            organization_id=organization_id,
            user_auth_context=user_auth_context,
            only_include_task_ids=only_include_task_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
            filter_spec=filter_spec,
            sorting_spec=sorting_spec,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    # @log_timing(logger=logger)
    async def _list_organization_user_records(
        self,
        organization_id: UUID,
        fetch_context: FetchContext,
        only_include_user_ids: UnsetAware[set[UUID]] = UNSET,
        # no custom object for user now
        # include_custom_object: bool,
        # no field reference for user now, it doesn't support outbound relationship yet
        # field_references: list[FieldReference],
        output_converter: (
            Callable[[OrganizationUserV2], Awaitable[OrganizationUserV2]] | None
        ) = None,
    ) -> Sequence[StandardRecord[OrganizationUserV2]]:
        # Early return
        if specified(only_include_user_ids) and not only_include_user_ids:
            return []

        cached_organization_users: dict[UUID, OrganizationUserV2] = (
            fetch_context.organization_users_by_id
        )
        user_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_user_ids - set(cached_organization_users.keys())
            if specified(only_include_user_ids)
            else UNSET
        )
        fetched_organization_users: list[
            OrganizationUserV2
        ] = await self.user_service.list_users_v2(
            organization_id=organization_id,
            only_include_user_ids=user_ids_to_fetch,
            active_users_only=False,
        )
        fetched_organization_user_by_id: dict[UUID, OrganizationUserV2] = {
            user.id: user for user in fetched_organization_users
        }
        cached_organization_users.update(fetched_organization_user_by_id)

        user_id_to_return = (
            only_include_user_ids
            if specified(only_include_user_ids)
            else fetched_organization_user_by_id.keys()
        )
        return [
            StandardRecord[OrganizationUserV2](
                object_id=OrganizationUserV2.object_id,
                requested_relationships=set(),
                related_records={},
                data=(
                    await output_converter(cached_organization_users[user_id])
                    if output_converter
                    else cached_organization_users[user_id]
                ),
            )
            for user_id in user_id_to_return
            if user_id in cached_organization_users
        ]

    # Note: This is a public method that is used by the indexing library
    async def list_organization_user_records_untenanted(
        self,
        fetch_context: FetchContext,
        only_include_user_ids: UnsetAware[set[UUID]] = UNSET,
        # no custom object for user now
        # include_custom_object: bool,
        # no field reference for user now, it doesn't support outbound relationship yet
        # field_references: list[FieldReference],
        output_converter: (
            Callable[[OrganizationUserV2], Awaitable[OrganizationUserV2]] | None
        ) = None,
    ) -> Sequence[OrganizationUserV2]:
        # Early return
        if specified(only_include_user_ids) and not only_include_user_ids:
            return []

        cached_organization_users: dict[UUID, OrganizationUserV2] = (
            fetch_context.organization_users_by_id
        )
        user_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_user_ids - set(cached_organization_users.keys())
            if specified(only_include_user_ids)
            else UNSET
        )
        fetched_organization_users: list[
            OrganizationUserV2
        ] = await self.user_service.list_users_v2_untenanted(
            only_include_user_ids=user_ids_to_fetch,
            active_users_only=False,
        )

        fetched_organization_user_by_id: dict[UUID, OrganizationUserV2] = {
            user.id: user for user in fetched_organization_users
        }
        cached_organization_users.update(fetched_organization_user_by_id)

        user_id_to_return = (
            only_include_user_ids
            if specified(only_include_user_ids)
            else fetched_organization_user_by_id.keys()
        )

        return [
            await output_converter(cached_organization_users[user_id])
            if output_converter
            else cached_organization_users[user_id]
            for user_id in user_id_to_return
            if user_id in cached_organization_users
        ]

    # @log_timing(logger=logger)
    async def _list_account_records(  # noqa: C901, PLR0912, PLR0915
        self,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_account_ids: UnsetAware[set[UUID]] = UNSET,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
        hint: UnsetAware[DomainFetchHints | None] = UNSET,
    ) -> Sequence[StandardRecord[AccountV2]]:
        logger.debug(
            "[list account records internal] start",
            organization_id=organization_id,
            user_id=user_id,
            only_include_account_ids=only_include_account_ids,
        )

        # Early return
        if specified(only_include_account_ids) and not only_include_account_ids:
            return []

        cached_accounts: dict[UUID, AccountV2] = fetch_context.account_by_id
        account_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_account_ids - set(cached_accounts.keys())
            if specified(only_include_account_ids)
            else UNSET
        )
        fetched_accounts = await self.account_query_service.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids=account_ids_to_fetch,
            include_custom_object=include_custom_object,
            domain_fetch_hints=hint,
        )

        if user_auth_context:
            fetched_accounts = await self.account_query_service.filter_viewable_records(
                records=fetched_accounts,
                user_auth_context=user_auth_context,
            )

        fetched_account_by_id = {account.id: account for account in fetched_accounts}
        cached_accounts.update(fetched_account_by_id)

        account_by_id: dict[UUID, AccountV2] = (
            {
                account_id: cached_accounts[account_id]
                for account_id in only_include_account_ids
                if account_id in cached_accounts
            }
            if specified(only_include_account_ids)
            else fetched_account_by_id
        )

        retrieved_account_ids: set[UUID] = set(account_by_id.keys())

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_account_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for (
            _relationship_id,
            _field_references,
        ) in field_references_by_relationship_id.items():
            with logger.contextualize(
                action="list_account_records_handling_relationships",
                relationship_id=_relationship_id,
            ):
                next_layer_field_references: list[FieldReference] = [
                    fr.field
                    for fr in _field_references
                    if isinstance(fr.field, FieldReference)
                ]
                if _relationship_id in AccountRelationship:
                    _account_relationship_id = AccountRelationship(
                        str(_relationship_id)
                    )
                    match _account_relationship_id:
                        case AccountRelationship.account__from__contact_account_role:
                            contextual_account_id = None
                            if (
                                specified(only_include_account_ids)
                                and len(only_include_account_ids) == 1
                            ):
                                contextual_account_id = next(
                                    iter(only_include_account_ids)
                                )
                            contact_account_role_records = (
                                await self._list_contact_account_role_record(
                                    organization_id=organization_id,
                                    only_include_account_ids=retrieved_account_ids,
                                    field_references=next_layer_field_references,
                                    fetch_context=fetch_context,
                                    include_custom_object=include_custom_object,
                                    contextual_account_id=contextual_account_id,
                                )
                            )
                            for (
                                contact_account_role_record
                            ) in contact_account_role_records:
                                account_id = contact_account_role_record.data.account_id
                                related_objects_by_relationship_id_by_account_id[
                                    account_id
                                ][_relationship_id].append(contact_account_role_record)
                        case (
                            AccountRelationship.account__to__archived_by_user
                            | AccountRelationship.account__to__created_by_user
                            | AccountRelationship.account__to__updated_by_user
                            | AccountRelationship.account__to__owner_user
                        ):
                            # todo(xw): super hack way to get the user id field, refactor will blow all these away.
                            user_id_field = next(
                                outbound.ordered_self_field_identifiers[0].field_name
                                for outbound in AccountV2.outbound_relationships
                                if outbound.id == _relationship_id
                                and outbound.ordered_self_field_identifiers
                                and isinstance(
                                    outbound.ordered_self_field_identifiers[0],
                                    StandardFieldIdentifier,
                                )
                            )
                            user_id_to_account_ids: dict[UUID, set[UUID]] = {}
                            for account in account_by_id.values():
                                if user_id := getattr(account, user_id_field, None):
                                    user_id_to_account_ids.setdefault(
                                        user_id, set()
                                    ).add(account.id)
                            user_records = await self._list_organization_user_records(
                                organization_id=organization_id,
                                only_include_user_ids=set(
                                    user_id_to_account_ids.keys()
                                ),
                                fetch_context=fetch_context,
                            )
                            for user_record in user_records:
                                for _account_id in user_id_to_account_ids[
                                    user_record.data.id
                                ]:
                                    related_objects_by_relationship_id_by_account_id[
                                        _account_id
                                    ][_relationship_id].append(user_record)

                        case AccountRelationship.account__to__participant_users:
                            # map accounts to participants.
                            account_id_to_participant_user_ids: dict[
                                UUID, set[UUID]
                            ] = {}
                            for account in account_by_id.values():
                                if _participant_user_ids := getattr(
                                    account, "participant_user_id_list", None
                                ):
                                    account_id_to_participant_user_ids.setdefault(
                                        account.id, set()
                                    ).update(_participant_user_ids)

                            # flatten list of sets to get the unique set of user IDs.
                            users: list[UUID] = [
                                item
                                for set_ in account_id_to_participant_user_ids.values()
                                for item in set_
                            ]

                            participant_user_records: dict[
                                UUID, StandardRecord[OrganizationUserV2]
                            ] = {
                                user_record.data.id: user_record
                                for user_record in await self._list_organization_user_records(
                                    organization_id=organization_id,
                                    only_include_user_ids=set(users),
                                    fetch_context=fetch_context,
                                )
                            }

                            # reassociate participant user IDs back to the account via related_records.
                            for (
                                account_id,
                                participant_user_ids,
                            ) in account_id_to_participant_user_ids.items():
                                for participant_user_id in participant_user_ids:
                                    related_objects_by_relationship_id_by_account_id[
                                        account_id
                                    ][_relationship_id].append(
                                        participant_user_records[participant_user_id]
                                    )
                        case AccountRelationship.account__from__pipeline:
                            pipeline_records_by_account_id = (
                                await self._map_pipeline_records_by_account_ids(
                                    organization_id=organization_id,
                                    account_ids=retrieved_account_ids,
                                    field_references=next_layer_field_references,
                                    fetch_context=fetch_context,
                                    include_custom_object=include_custom_object,
                                    user_auth_context=user_auth_context,
                                )
                            )
                            for (
                                account_id,
                                pipeline_records,
                            ) in pipeline_records_by_account_id.items():
                                related_objects_by_relationship_id_by_account_id[
                                    account_id
                                ][_relationship_id].extend(pipeline_records)
                        case AccountRelationship.domain_object_lists__from__account:
                            domain_object_list_records_by_account_id = (
                                await self._map_list_records_by_account_ids(
                                    organization_id=organization_id,
                                    account_ids=retrieved_account_ids,
                                )
                            )
                            for (
                                account_id,
                                domain_object_list_records,
                            ) in domain_object_list_records_by_account_id.items():
                                related_objects_by_relationship_id_by_account_id[
                                    account_id
                                ][_relationship_id].extend(domain_object_list_records)
                        case _ as unreachable:
                            assert_never(unreachable)
                elif association_id := safe_parse_uuid_or_none(_relationship_id):
                    association = await self.association_service.get_association_by_id(
                        organization_id=organization_id,
                        association_id=association_id,
                    )
                    related_records_from_association = await self.fetch_related_records(
                        organization_id=organization_id,
                        fetch_context=fetch_context,
                        association=association,
                        source_data_ids=list(retrieved_account_ids),
                        field_references=next_layer_field_references,
                        current_object_name="account",
                    )
                    for (
                        account_id,
                        custom_records,
                    ) in related_records_from_association.items():
                        related_objects_by_relationship_id_by_account_id[account_id][
                            _relationship_id
                        ].extend(custom_records)
        result: list[StandardRecord[AccountV2]] = []
        for account_id, account in account_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_account_id[
                    account_id
                ].items()
            }
            result.append(
                StandardRecord[AccountV2](
                    object_id=AccountV2.object_id,
                    is_editable=await self.account_query_service.is_editable(
                        user_auth_context=user_auth_context,
                        domain_object=account,
                    ),
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=account,
                )
            )
        logger.debug(
            "[list account records internal] end",
            organization_id=organization_id,
            user_id=user_id,
            only_include_account_ids=only_include_account_ids,
        )
        return result

    # @log_timing(logger=logger)
    async def fetch_related_records(
        self,
        organization_id: UUID,
        fetch_context: FetchContext,
        association: CustomObjectAssociation,
        source_data_ids: list[UUID],
        field_references: list[FieldReference],
        current_object_name: str,
    ) -> dict[UUID, list[ModeledObjectRecord]]:
        """
        Fetch related records for any standard object based on association direction.

        Args:
            organization_id: Organization ID
            fetch_context: Context for the current fetch operation
            association: Association defining the relationship
            source_data_ids: IDs of the source records
            field_references: Fields to fetch from related records
            current_object_name: Name of the object making the query

        Returns:
            List of related records (either StandardRecord or CustomRecord)
        """
        # Determine if current object is source of the association.
        # If it is, we need to fetch the target records.
        # If it is not, we need to fetch the source records.
        is_target = self._determine_relationship_direction(
            association=association,
            current_object_name=current_object_name,
        )
        related_object_identifier = association.source_object_identifier

        if is_target:
            related_object_identifier = association.target_object_identifier

        # Fetch appropriate record type based on target object kind
        if related_object_identifier.object_kind == ObjectKind.CUSTOM:
            logger.debug("Fetching related custom records")
            logger.debug(related_object_identifier)
            return await self.fetch_related_custom_records(
                organization_id=organization_id,
                fetch_context=fetch_context,
                association=association,
                source_data_ids=source_data_ids,
                field_references=field_references,
                is_target=is_target,
            )
        else:
            logger.debug("Fetching related standard records")
            logger.debug(related_object_identifier)
            return await self.fetch_related_standard_records(
                organization_id=organization_id,
                fetch_context=fetch_context,
                association=association,
                source_data_ids=source_data_ids,
                field_references=field_references,
                is_target=is_target,
            )

    def _determine_relationship_direction(
        self,
        association: CustomObjectAssociation,
        current_object_name: str,
    ) -> bool:
        """Determines if the current object is the standard object source in the association.

        This function checks if:
        1. The source object in the association is a standard object (not custom)
        2. The current object name matches that standard object's name

        Args:
            current_object_name: Name of the object being checked
            association: Association between a standard and custom object

        Returns:
            bool: True if the current object is the standard source object in the association,
                False otherwise
        """
        if association.source_object_identifier.object_kind == ObjectKind.STANDARD:
            return (
                current_object_name == association.source_object_identifier.object_name
            )

        return False

    # @log_timing(logger=logger)
    async def _map_pipeline_records_by_account_ids(
        self,
        organization_id: UUID,
        account_ids: set[UUID],
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        user_auth_context: UserAuthContext | None = None,
    ) -> Mapping[UUID, list[StandardRecord[PipelineV2]]]:
        if not account_ids:
            return frozendict[UUID, list[StandardRecord[PipelineV2]]]()
        cached_pipelines_by_account_id: defaultdict[UUID, list[PipelineV2]] = (
            fetch_context.pipelines_by_account_id
        )
        account_ids_to_fetch: set[UUID] = account_ids - set(
            cached_pipelines_by_account_id.keys()
        )
        fetched_pipelines: frozendict[
            UUID, list[PipelineV2]
        ] = await self.pipeline_query_service.map_pipeline_v2_by_account_ids(
            organization_id=organization_id,
            account_ids=account_ids_to_fetch,
            include_custom_object=include_custom_object,
        )
        cached_pipelines_by_account_id.update(fetched_pipelines)

        pipeline_by_id: dict[UUID, PipelineV2] = {}
        for account_id in account_ids:
            if account_id in cached_pipelines_by_account_id:
                for pipeline in cached_pipelines_by_account_id[account_id]:
                    pipeline_by_id[pipeline.id] = pipeline
        pipeline_records = await self._list_pipeline_records(
            organization_id=organization_id,
            only_include_pipeline_ids=set(pipeline_by_id.keys()),
            field_references=field_references,
            fetch_context=fetch_context,
            include_custom_object=include_custom_object,
            user_auth_context=user_auth_context,
        )
        result: dict[UUID, list[StandardRecord[PipelineV2]]] = defaultdict(list)
        for pipeline_record in pipeline_records:
            result[pipeline_record.data.account_id].append(pipeline_record)
        return result

    # @log_timing(logger=logger)
    async def _map_list_records_by_account_ids(
        self,
        organization_id: UUID,
        account_ids: set[UUID],
    ) -> Mapping[UUID, list[StandardRecord[DomainObjectList]]]:
        if not account_ids or len(account_ids) == 0:
            return frozendict[UUID, list[StandardRecord[DomainObjectList]]]()
        # Skip caching because there is currently no other path to list objects
        lists_by_account_id = await self.domain_object_list_query_service.map_domain_object_lists_by_account_id(
            organization_id=organization_id,
            account_ids=account_ids,
        )
        return {
            account_id: [
                StandardRecord[DomainObjectList](
                    object_id=DomainObjectList.object_id,
                    requested_relationships=set(),
                    related_records={},
                    data=domain_object_list,
                )
                for domain_object_list in domain_object_lists
            ]
            for account_id, domain_object_lists in lists_by_account_id.items()
        }

    async def _map_list_records_by_contact_ids(
        self,
        organization_id: UUID,
        contact_ids: set[UUID],
    ) -> Mapping[UUID, list[StandardRecord[DomainObjectList]]]:
        if not contact_ids or len(contact_ids) == 0:
            return frozendict[UUID, list[StandardRecord[DomainObjectList]]]()
        # Skip caching because there is currently no other path to list objects
        lists_by_contact_id = await self.domain_object_list_query_service.map_domain_object_lists_by_contact_id(
            organization_id=organization_id,
            contact_ids=contact_ids,
        )
        return {
            contact_id: [
                StandardRecord[DomainObjectList](
                    object_id=DomainObjectList.object_id,
                    requested_relationships=set(),
                    related_records={},
                    data=domain_object_list,
                )
                for domain_object_list in domain_object_lists
            ]
            for contact_id, domain_object_lists in lists_by_contact_id.items()
        }

    async def _process_meeting_relationship_contact(
        self,
        *,
        organization_id: UUID,
        _relationship_id: Literal[
            MeetingRelationship.meeting__to__invitee_contact,
            MeetingRelationship.meeting__to__attendee_contact,
        ],
        fetch_context: FetchContext,
        meeting_by_id: dict[UUID, MeetingV2],
        include_custom_object: bool,
        next_layer_field_references: list[FieldReference],
        related_objects_by_relationship_id_by_meeting_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ],
    ) -> None:
        with start_span(
            op="meeting.process_contact", description=f"Process {_relationship_id}"
        ) as span:
            span.set_data("organization_id", str(organization_id))
            span.set_data("relationship_id", str(_relationship_id))
            span.set_data("meeting_count", len(meeting_by_id))

            contact_id_to_meeting_ids: dict[UUID, set[UUID]] = {}
            for meeting in meeting_by_id.values():
                contact_id_list: list[UUID] = []
                if (
                    meeting.invitee_contact_id_list
                    and _relationship_id
                    == MeetingRelationship.meeting__to__invitee_contact
                ):
                    contact_id_list = meeting.invitee_contact_id_list
                elif (
                    meeting.attendee_contact_id_list
                    and _relationship_id
                    == MeetingRelationship.meeting__to__attendee_contact
                ):
                    contact_id_list = meeting.attendee_contact_id_list

                for contact_id in contact_id_list:
                    contact_id_to_meeting_ids.setdefault(contact_id, set()).add(
                        meeting.id
                    )

            contact_records = await self._list_contact_records(
                organization_id=organization_id,
                only_include_contact_ids=set(contact_id_to_meeting_ids.keys()),
                field_references=next_layer_field_references,
                fetch_context=fetch_context,
                include_custom_object=include_custom_object,
            )
            for contact_record in contact_records:
                for _meeting_id in contact_id_to_meeting_ids[contact_record.data.id]:
                    related_objects_by_relationship_id_by_meeting_id[_meeting_id][
                        _relationship_id
                    ].append(contact_record)

    async def _process_meeting_relationship_user(
        self,
        *,
        organization_id: UUID,
        _relationship_id: Literal[
            MeetingRelationship.meeting__to__attendee_user,
            MeetingRelationship.meeting__to__organizer_user,
            MeetingRelationship.meeting__to__invitee_user,
            MeetingRelationship.meeting__to__created_by_user,
        ],
        fetch_context: FetchContext,
        meeting_by_id: dict[UUID, MeetingV2],
        related_objects_by_relationship_id_by_meeting_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ],
    ) -> None:
        with start_span(
            op="meeting.process_user", description=f"Process {_relationship_id}"
        ) as span:
            span.set_data("organization_id", str(organization_id))
            span.set_data("relationship_id", str(_relationship_id))
            span.set_data("meeting_count", len(meeting_by_id))

            user_id_field = next(
                outbound.ordered_self_field_identifiers[0].field_name
                for outbound in MeetingV2.outbound_relationships
                if outbound.id == _relationship_id
                and outbound.ordered_self_field_identifiers
                and isinstance(
                    outbound.ordered_self_field_identifiers[0],
                    StandardFieldIdentifier,
                )
            )
            user_id_to_meeting_ids: dict[UUID, set[UUID]] = {}
            for meeting in meeting_by_id.values():
                if user_id_value := getattr(meeting, user_id_field, None):
                    user_id_list: list[UUID] = (
                        user_id_value
                        if isinstance(user_id_value, list)
                        else [user_id_value]
                    )
                    for user_id in user_id_list:
                        user_id_to_meeting_ids.setdefault(user_id, set()).add(
                            meeting.id
                        )
            user_records = await self._list_organization_user_records(
                organization_id=organization_id,
                only_include_user_ids=set(user_id_to_meeting_ids.keys()),
                fetch_context=fetch_context,
            )
            for user_record in user_records:
                for _meeting_id in user_id_to_meeting_ids[user_record.data.id]:
                    related_objects_by_relationship_id_by_meeting_id[_meeting_id][
                        _relationship_id
                    ].append(user_record)

    async def _process_meeting_relationship_pipeline(
        self,
        *,
        organization_id: UUID,
        _relationship_id: RelationshipId,
        fetch_context: FetchContext,
        meeting_by_id: dict[UUID, MeetingV2],
        include_custom_object: bool,
        next_layer_field_references: list[FieldReference],
        related_objects_by_relationship_id_by_meeting_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ],
    ) -> None:
        with start_span(
            op="meeting.process_pipeline", description=f"Process {_relationship_id}"
        ) as span:
            span.set_data("organization_id", str(organization_id))
            span.set_data("relationship_id", str(_relationship_id))
            span.set_data("meeting_count", len(meeting_by_id))

            pipeline_id_to_meeting_ids: dict[UUID, set[UUID]] = {}
            for meeting in meeting_by_id.values():
                if meeting.pipeline_id:
                    pipeline_id_to_meeting_ids.setdefault(
                        meeting.pipeline_id, set()
                    ).add(meeting.id)

            if not pipeline_id_to_meeting_ids:
                logger.info("No pipeline id to meeting ids")
                return

            pipeline_records = await self._list_pipeline_records(
                organization_id=organization_id,
                only_include_pipeline_ids=set(pipeline_id_to_meeting_ids.keys()),
                field_references=next_layer_field_references,
                fetch_context=fetch_context,
                include_custom_object=include_custom_object,
            )

            for pipeline_record in pipeline_records:
                for _meeting_id in pipeline_id_to_meeting_ids[pipeline_record.data.id]:
                    related_objects_by_relationship_id_by_meeting_id[_meeting_id][
                        _relationship_id
                    ].append(pipeline_record)

    async def _process_meeting_relationship_account(
        self,
        *,
        organization_id: UUID,
        _relationship_id: RelationshipId,
        fetch_context: FetchContext,
        meeting_by_id: dict[UUID, MeetingV2],
        include_custom_object: bool,
        next_layer_field_references: list[FieldReference],
        related_objects_by_relationship_id_by_meeting_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ],
    ) -> None:
        with start_span(
            op="meeting.process_account", description=f"Process {_relationship_id}"
        ) as span:
            span.set_data("organization_id", str(organization_id))
            span.set_data("relationship_id", str(_relationship_id))
            span.set_data("meeting_count", len(meeting_by_id))

            # map account id to meeting id
            account_id_to_meeting_ids: dict[UUID, set[UUID]] = {}
            for meeting in meeting_by_id.values():
                if meeting.account_id:
                    account_id_to_meeting_ids.setdefault(meeting.account_id, set()).add(
                        meeting.id
                    )

            if not account_id_to_meeting_ids:
                logger.info("No account id to meeting ids")
                return

            # associate account records to meeting
            account_records = await self._list_account_records(
                organization_id=organization_id,
                only_include_account_ids=set(account_id_to_meeting_ids.keys()),
                field_references=next_layer_field_references,
                fetch_context=fetch_context,
                include_custom_object=include_custom_object,
            )

            for account_record in account_records:
                for _meeting_id in account_id_to_meeting_ids[account_record.data.id]:
                    related_objects_by_relationship_id_by_meeting_id[_meeting_id][
                        _relationship_id
                    ].append(account_record)

    async def _list_conversation_records(
        self,
        *,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
    ) -> Sequence[StandardRecord[Conversation]]:
        start_time = time.perf_counter()
        fetched_conversations = await self.conversation_query_service.list_conversation(
            organization_id=organization_id,
            include_custom_object=include_custom_object,
        )
        fetched_conversation_by_id = {
            conversation.id: conversation for conversation in fetched_conversations
        }

        elapsed_time = (time.perf_counter() - start_time) * 1000
        logger.bind(elapsed_time=elapsed_time).info("Initial conversations fetch")

        # TODO: what are the related objects we want to expose for the genral list call?
        result: list[StandardRecord[Conversation]] = []

        for conversation in fetched_conversation_by_id.values():
            result.append(
                StandardRecord[Conversation](
                    object_id=Conversation.object_id,
                    requested_relationships=set(),
                    related_records={},
                    data=conversation,
                )
            )
        return result

    def _extract_meeting_contact_ids_in_filter_spec(
        self,
        *,
        filter_spec: FilterSpec | None = None,
    ) -> set[UUID]:
        if not filter_spec:
            return set()
        value_filters: list[ValueFilter] = (
            baseline_filter_extractor.extract_must_filter_by_field_reference(
                filter_spec=filter_spec,
                field=FieldReference(
                    relationship_id=MeetingRelationship.meeting__to__invitee_contact,
                    field=QualifiedField(path=(ContactField.id,)),
                ),
            )
        )
        if not value_filters:
            return set()
        value_filter = value_filters[0]
        if value_filter.operator == MatchOperator.EQ and isinstance(
            value_filter.value, UUID
        ):
            return {value_filter.value}
        elif value_filter.operator == MatchOperator.IN and isinstance(
            value_filter.value, set
        ):
            return {v for v in value_filter.value if isinstance(v, UUID)}
        else:
            return set()

    def _extract_meeting_account_ids_in_filter_spec(
        self,
        *,
        filter_spec: FilterSpec | None = None,
    ) -> set[UUID]:
        if not filter_spec:
            return set()
        value_filters: list[ValueFilter] = (
            baseline_filter_extractor.extract_must_filter_by_field_reference(
                filter_spec=filter_spec,
                field=FieldReference(
                    relationship_id=MeetingRelationship.meeting__to__invitee_contact,
                    field=FieldReference(
                        relationship_id=ContactRelationship.contact__to__primary_account,
                        field=QualifiedField(path=(ContactField.id,)),
                    ),
                ),
            )
        )
        if not value_filters:
            return set()
        value_filter = value_filters[0]
        if value_filter.operator == MatchOperator.EQ and isinstance(
            value_filter.value, UUID
        ):
            return {value_filter.value}
        elif value_filter.operator == MatchOperator.IN and isinstance(
            value_filter.value, set
        ):
            return {v for v in value_filter.value if isinstance(v, UUID)}
        else:
            return set()

    async def _list_meeting_records(  # noqa: C901, PLR0912
        self,
        *,
        organization_id: UUID,
        user_id: UUID | None,
        user_auth_context: UserAuthContext | None = None,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_meeting_ids: UnsetAware[set[UUID]] = UNSET,
        only_include_meeting_for_user: UnsetAware[UUID] = UNSET,
        filter_spec: FilterSpec | None = None,
        sorting_spec: SortingSpec | None = None,
        cursor: Cursor | None = None,
        include_property_metadata: bool = False,
    ) -> Sequence[StandardRecord[MeetingV2]]:
        # Early return
        if specified(only_include_meeting_ids) and not only_include_meeting_ids:
            return []

        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            include_custom_object=include_custom_object,
        ).info("entering _list_meeting_records")
        start_time = time.perf_counter()
        cached_meetings: dict[UUID, MeetingV2] = fetch_context.meeting_by_id
        meeting_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_meeting_ids - set(cached_meetings.keys())
            if specified(only_include_meeting_ids)
            else UNSET
        )
        contact_ids_to_fetch: list[UUID] = list(
            self._extract_meeting_contact_ids_in_filter_spec(
                filter_spec=filter_spec,
            )
        )
        account_ids_to_fetch: list[UUID] = list(
            self._extract_meeting_account_ids_in_filter_spec(
                filter_spec=filter_spec,
            )
        )

        offset, limit = (
            cursor_to_offset_limit_with_overflow(cursor) if cursor else (None, None)
        )

        hint = self.domain_fetch_hints_from_filter_and_sorting_spec(
            filter_spec=filter_spec,
            sorting_spec=sorting_spec,
            limit=limit,
            standard_object_identifier=MeetingV2.object_id,
            only_include_object_ids=only_include_meeting_ids,
            offset=offset,
        )

        logger.bind(
            domain_fetch_hints=hint,
        ).info("domain fetch hints")

        if specified(meeting_ids_to_fetch):
            fetched_meetings = await self.meeting_query_service.list_meetings_v2_with_domain_fetch_hints(
                organization_id=organization_id,
                user_id=user_id,
                only_include_meeting_ids=meeting_ids_to_fetch,
                include_custom_object=include_custom_object,
                domain_fetch_hints=hint,
                include_property_metadata=include_property_metadata,
            )

        elif contact_ids_to_fetch:
            fetched_meetings = (
                await self.meeting_query_service.list_meetings_by_contact_ids(
                    organization_id=organization_id,
                    contact_ids=contact_ids_to_fetch,
                    user_id=user_id,
                    include_custom_object=include_custom_object,
                    include_property_metadata=include_property_metadata,
                )
            )
        elif account_ids_to_fetch:
            fetched_meetings = (
                await self.meeting_query_service.list_meetings_by_primary_account_ids(
                    organization_id=organization_id,
                    primary_account_ids=account_ids_to_fetch,
                    user_id=user_id,
                    include_custom_object=include_custom_object,
                    include_property_metadata=include_property_metadata,
                )
            )
        else:
            fetched_meetings = await self.meeting_query_service.list_meetings_v2_with_domain_fetch_hints(
                organization_id=organization_id,
                user_id=user_id,
                only_include_meeting_ids=meeting_ids_to_fetch,
                include_custom_object=include_custom_object,
                domain_fetch_hints=hint,
                include_property_metadata=include_property_metadata,
            )

        # if user_auth_context:
        #    fetched_meetings = await self.meeting_query_service.filter_viewable_records(
        #        records=fetched_meetings,
        #        user_auth_context=user_auth_context,
        #    )

        fetched_meeting_by_id = {meeting.id: meeting for meeting in fetched_meetings}
        cached_meetings.update(fetched_meeting_by_id)

        elapsed_time = (time.perf_counter() - start_time) * 1000
        logger.bind(elapsed_time=elapsed_time).info("Initial meeting fetch")

        meeting_by_id: dict[UUID, MeetingV2] = (
            {
                meeting_id: cached_meetings[meeting_id]
                for meeting_id in only_include_meeting_ids
                if meeting_id in cached_meetings
            }
            if specified(only_include_meeting_ids)
            else fetched_meeting_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        process_tasks = []
        related_objects_by_relationship_id_by_meeting_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for (
            _relationship_id,
            _field_references,
        ) in field_references_by_relationship_id.items():
            next_layer_field_references: list[FieldReference] = [
                fr.field
                for fr in _field_references
                if isinstance(fr.field, FieldReference)
            ]
            if _relationship_id not in MeetingRelationship:
                raise NotImplementedError(
                    f"Unsupported custom object relationship: {_relationship_id}"
                )

            _meeting_relationship_id = MeetingRelationship(str(_relationship_id))
            match _meeting_relationship_id:
                case (
                    MeetingRelationship.meeting__to__attendee_user
                    | MeetingRelationship.meeting__to__organizer_user
                    | MeetingRelationship.meeting__to__invitee_user
                    | MeetingRelationship.meeting__to__created_by_user
                ):
                    process_tasks.append(
                        self._process_meeting_relationship_user(
                            organization_id=organization_id,
                            _relationship_id=_meeting_relationship_id,
                            fetch_context=fetch_context,
                            meeting_by_id=meeting_by_id,
                            related_objects_by_relationship_id_by_meeting_id=related_objects_by_relationship_id_by_meeting_id,
                        )
                    )
                case (
                    MeetingRelationship.meeting__to__invitee_contact
                    | MeetingRelationship.meeting__to__attendee_contact
                ):
                    process_tasks.append(
                        self._process_meeting_relationship_contact(
                            organization_id=organization_id,
                            _relationship_id=_meeting_relationship_id,
                            fetch_context=fetch_context,
                            meeting_by_id=meeting_by_id,
                            include_custom_object=include_custom_object,
                            next_layer_field_references=next_layer_field_references,
                            related_objects_by_relationship_id_by_meeting_id=related_objects_by_relationship_id_by_meeting_id,
                        )
                    )
                case MeetingRelationship.meeting__to__account:
                    process_tasks.append(
                        self._process_meeting_relationship_account(
                            organization_id=organization_id,
                            _relationship_id=_relationship_id,
                            fetch_context=fetch_context,
                            meeting_by_id=meeting_by_id,
                            include_custom_object=include_custom_object,
                            next_layer_field_references=next_layer_field_references,
                            related_objects_by_relationship_id_by_meeting_id=related_objects_by_relationship_id_by_meeting_id,
                        )
                    )
                case MeetingRelationship.meeting__to__pipeline:
                    process_tasks.append(
                        self._process_meeting_relationship_pipeline(
                            organization_id=organization_id,
                            _relationship_id=_relationship_id,
                            fetch_context=fetch_context,
                            meeting_by_id=meeting_by_id,
                            include_custom_object=include_custom_object,
                            next_layer_field_references=next_layer_field_references,
                            related_objects_by_relationship_id_by_meeting_id=related_objects_by_relationship_id_by_meeting_id,
                        )
                    )
                case _ as unreachable:
                    assert_never(unreachable)

        await asyncio.gather(*process_tasks)

        result: list[StandardRecord[MeetingV2]] = []
        for meeting_id, meeting in meeting_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_meeting_id[
                    meeting_id
                ].items()
            }
            result.append(
                StandardRecord[MeetingV2](
                    object_id=MeetingV2.object_id,
                    is_editable=await self.meeting_query_service.is_editable(
                        user_auth_context=user_auth_context,
                        domain_object=meeting,
                    ),
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=meeting,
                )
            )
        return result

    async def get_meeting_record(
        self,
        record_id: UUID,
        organization_id: UUID,
        user_id: UUID | None,
        include_custom_object: bool,
        ordered_object_fields: (
            tuple[QualifiedField | FieldReference, ...] | None
        ) = None,
    ) -> Sequence[StandardRecord[MeetingV2]]:
        meetings, _ = await self.list_meeting_records(
            organization_id=organization_id,
            user_id=user_id,
            include_custom_object=include_custom_object,
            only_include_meeting_ids={record_id},
            fetch_conditions=ObjectRecordFetchConditions(
                fields=ordered_object_fields,
                filter_spec=None,
                sorting_spec=None,
            ),
        )
        if not meetings:
            raise ResourceNotFoundError(f"No meeting record found for {record_id}")
        return meetings

    # @log_timing(logger=logger)
    async def _list_contact_records(  # noqa: C901, PLR0912, PLR0915
        self,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_contact_ids: UnsetAware[set[UUID]] = UNSET,
        user_id: UUID | None = None,
        contextual_account_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
        hint: UnsetAware[DomainFetchHints | None] = UNSET,
    ) -> Sequence[StandardRecord[ContactV2]]:
        # Early return
        if specified(only_include_contact_ids) and not only_include_contact_ids:
            return []

        logger.debug(
            "[list contact records internal] start",
            organization_id=organization_id,
            user_id=user_id,
            only_include_contact_ids=only_include_contact_ids,
        )
        cached_contacts: dict[UUID, ContactV2] = fetch_context.contact_by_id
        contact_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_contact_ids - set(cached_contacts.keys())
            if specified(only_include_contact_ids)
            else UNSET
        )
        fetched_contacts = await self.contact_query_service.list_contacts_v2(
            organization_id=organization_id,
            only_include_contact_ids=contact_ids_to_fetch,
            include_custom_object=include_custom_object,
            domain_fetch_hints=hint,
            contextual_account_id=contextual_account_id,
        )

        if user_auth_context:
            fetched_contacts = await self.contact_query_service.filter_viewable_records(
                records=fetched_contacts,
                user_auth_context=user_auth_context,
            )

        fetched_contact_by_id = {contact.id: contact for contact in fetched_contacts}
        cached_contacts.update(fetched_contact_by_id)

        contact_by_id: dict[UUID, ContactV2] = (
            {
                contact_id: cached_contacts[contact_id]
                for contact_id in only_include_contact_ids
                if contact_id in cached_contacts
            }
            if specified(only_include_contact_ids)
            else fetched_contact_by_id
        )

        set(contact_by_id.keys())

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_contact_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for (
            _relationship_id,
            _field_references,
        ) in field_references_by_relationship_id.items():
            logger.debug(
                "[list contact record] relationship start",
                organization_id=organization_id,
                user_id=user_id,
                relationship_id=_relationship_id,
            )
            next_layer_field_references: list[FieldReference] = [
                fr.field
                for fr in _field_references
                if isinstance(fr.field, FieldReference)
            ]
            if _relationship_id in ContactRelationship:
                _contact_relationship_id = ContactRelationship(str(_relationship_id))
                match _contact_relationship_id:
                    case ContactRelationship.contact__to__primary_account:
                        account_id_to_contact_ids: dict[UUID, set[UUID]] = {}
                        for contact in contact_by_id.values():
                            if contact.primary_account_id:
                                account_id_to_contact_ids.setdefault(
                                    contact.primary_account_id, set()
                                ).add(contact.id)
                        account_records = await self._list_account_records(
                            organization_id=organization_id,
                            only_include_account_ids=set(
                                account_id_to_contact_ids.keys()
                            ),
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                            user_auth_context=user_auth_context,
                        )
                        for account_record in account_records:
                            for contact_id in account_id_to_contact_ids[
                                account_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_contact_id[
                                    contact_id
                                ][_relationship_id].append(account_record)
                    case ContactRelationship.contact__from__contact_account_role:
                        contact_account_role_records = (
                            await self._list_contact_account_role_record(
                                organization_id=organization_id,
                                only_include_contact_ids=set(contact_by_id.keys()),
                                field_references=next_layer_field_references,
                                fetch_context=fetch_context,
                                include_custom_object=include_custom_object,
                            )
                        )
                        for contact_account_role_record in contact_account_role_records:
                            contact_id = contact_account_role_record.data.contact_id
                            related_objects_by_relationship_id_by_contact_id[
                                contact_id
                            ][_relationship_id].append(contact_account_role_record)
                    case (
                        ContactRelationship.contact__to__archived_by_user
                        | ContactRelationship.contact__to__created_by_user
                        | ContactRelationship.contact__to__updated_by_user
                        | ContactRelationship.contact__to__owner_user
                    ):
                        # todo(xw): super hack way to get the user id field, refactor will blow all these away.
                        user_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in ContactV2.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        user_id_to_contact_ids: dict[UUID, set[UUID]] = {}
                        for contact in contact_by_id.values():
                            if user_id := getattr(contact, user_id_field, None):
                                user_id_to_contact_ids.setdefault(user_id, set()).add(
                                    contact.id
                                )
                        user_records = await self._list_organization_user_records(
                            organization_id=organization_id,
                            only_include_user_ids=set(user_id_to_contact_ids.keys()),
                            fetch_context=fetch_context,
                        )
                        for user_record in user_records:
                            for _contact_id in user_id_to_contact_ids[
                                user_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_contact_id[
                                    _contact_id
                                ][_relationship_id].append(user_record)

                    case ContactRelationship.contact__to__participant_users:
                        # map contacts to participants.
                        contact_id_to_participant_user_ids: dict[UUID, set[UUID]] = {}
                        for contact in contact_by_id.values():
                            if _participant_user_ids := getattr(
                                contact, "participant_user_id_list", None
                            ):
                                contact_id_to_participant_user_ids.setdefault(
                                    contact.id, set()
                                ).update(_participant_user_ids)

                        # flatten list of sets to get the unique set of user IDs.
                        users: list[UUID] = [
                            item
                            for set_ in contact_id_to_participant_user_ids.values()
                            for item in set_
                        ]

                        participant_user_records: dict[
                            UUID, StandardRecord[OrganizationUserV2]
                        ] = {
                            user_record.data.id: user_record
                            for user_record in await self._list_organization_user_records(
                                organization_id=organization_id,
                                only_include_user_ids=set(users),
                                fetch_context=fetch_context,
                            )
                        }

                        # reassociate participant user IDs back to the contact via related_records.
                        for (
                            contact_id,
                            participant_user_ids,
                        ) in contact_id_to_participant_user_ids.items():
                            for participant_user_id in participant_user_ids:
                                related_objects_by_relationship_id_by_contact_id[
                                    contact_id
                                ][_relationship_id].append(
                                    participant_user_records[participant_user_id]
                                )
                    case ContactRelationship.domain_object_lists__from__contact:
                        domain_object_list_records_by_contact_id = (
                            await self._map_list_records_by_contact_ids(
                                organization_id=organization_id,
                                contact_ids=set(contact_by_id.keys()),
                            )
                        )
                        for (
                            contact_id,
                            domain_object_list_records,
                        ) in domain_object_list_records_by_contact_id.items():
                            related_objects_by_relationship_id_by_contact_id[
                                contact_id
                            ][_relationship_id].extend(domain_object_list_records)

                    case ContactRelationship.contact__from__contact_pipeline_role:
                        contact_pipeline_role_records = await self._list_contact_pipeline_role_record_by_contact_or_pipeline_ids(
                            organization_id=organization_id,
                            entity_ids=set(contact_by_id.keys()),
                            entity_type="contact",
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                            user_auth_context=user_auth_context,
                        )
                        for (
                            contact_pipeline_role_record
                        ) in contact_pipeline_role_records:
                            contact_id = contact_pipeline_role_record.data.contact_id
                            related_objects_by_relationship_id_by_contact_id[
                                contact_id
                            ][_relationship_id].append(contact_pipeline_role_record)
                    case _ as unreachable:
                        assert_never(unreachable)
            elif association_id := safe_parse_uuid_or_none(_relationship_id):
                # Replace debug log with actual custom association handling
                association = await self.association_service.get_association_by_id(
                    organization_id=organization_id,
                    association_id=association_id,
                )
                related_records_from_association = await self.fetch_related_records(
                    organization_id=organization_id,
                    fetch_context=fetch_context,
                    association=association,
                    source_data_ids=list(contact_by_id.keys()),
                    field_references=next_layer_field_references,
                    current_object_name="contact",
                )
                for (
                    contact_id,
                    custom_records,
                ) in related_records_from_association.items():
                    related_objects_by_relationship_id_by_contact_id[contact_id][
                        _relationship_id
                    ].extend(custom_records)
            logger.debug(
                "[list contact record] relationship end",
                organization_id=organization_id,
                user_id=user_id,
                relationship_id=_relationship_id,
            )

        result: list[StandardRecord[ContactV2]] = []
        for contact_id, contact in contact_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_contact_id[
                    contact_id
                ].items()
            }
            result.append(
                StandardRecord[ContactV2](
                    object_id=ContactV2.object_id,
                    is_editable=await self.contact_query_service.is_editable(
                        user_auth_context=user_auth_context,
                        domain_object=contact,
                    ),
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=contact,
                )
            )
        logger.debug(
            "[list contact records internal] end",
            organization_id=organization_id,
            user_id=user_id,
            only_include_contact_ids=only_include_contact_ids,
        )
        return result

    async def _map_pipeline_records_by_contact_ids(
        self,
        *,
        organization_id: UUID,
        contact_ids: set[UUID],
        primary_contact_filter: Literal["primary", "additional", "all"],
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        user_auth_context: UserAuthContext | None = None,
    ) -> frozendict[UUID, list[StandardRecord[PipelineV2]]]:
        if not contact_ids:
            return frozendict[UUID, list[StandardRecord[PipelineV2]]]()
        cached_pipelines: dict[UUID, list[PipelineV2]] = {}
        match primary_contact_filter:
            case "primary":
                cached_pipelines = fetch_context.pipeline_by_primary_contact_id
            case "additional":
                cached_pipelines = fetch_context.pipeline_by_additional_contact_id
            case "all":
                cached_pipelines = fetch_context.pipeline_by_any_contact_id
            case _ as unreachable:
                assert_never(unreachable)
        contact_ids_to_fetch: set[UUID] = contact_ids - set(cached_pipelines.keys())
        fetched_pipelines: frozendict[
            UUID, list[PipelineV2]
        ] = await self.pipeline_query_service.map_pipeline_v2_by_contact_ids(
            organization_id=organization_id,
            contact_ids=contact_ids_to_fetch,
            primary_contact_filter=primary_contact_filter,
            include_custom_object=include_custom_object,
        )
        cached_pipelines.update(fetched_pipelines)
        pipeline_by_id: dict[UUID, PipelineV2] = {}
        for _contact_id in contact_ids:
            for _pipeline in cached_pipelines.get(_contact_id, []):
                pipeline_by_id[_pipeline.id] = _pipeline
        pipeline_records = await self._list_pipeline_records(
            organization_id=organization_id,
            only_include_pipeline_ids=set(pipeline_by_id.keys()),
            field_references=field_references,
            fetch_context=fetch_context,
            include_custom_object=include_custom_object,
            user_auth_context=user_auth_context,
        )
        pipeline_records_by_id: dict[UUID, StandardRecord[PipelineV2]] = {
            pipeline_record.data.id: pipeline_record
            for pipeline_record in pipeline_records
        }
        result: dict[UUID, list[StandardRecord[PipelineV2]]] = defaultdict(list)
        for _contact_id in contact_ids:
            for _pipeline in cached_pipelines.get(_contact_id, []):
                if _pipeline.id in pipeline_records_by_id:
                    result[_contact_id].append(pipeline_records_by_id[_pipeline.id])
        return frozendict[UUID, list[StandardRecord[PipelineV2]]](result)

    # @log_timing(logger=logger)
    async def _map_contact_records_by_primary_account_id(
        self,
        organization_id: UUID,
        account_ids: set[UUID],
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        user_auth_context: UserAuthContext | None = None,
    ) -> Mapping[UUID, Sequence[StandardRecord[ContactV2]]]:
        cached_contacts: defaultdict[UUID, list[ContactV2]] = (
            fetch_context.contacts_by_primary_account_id
        )
        account_ids_to_fetch = account_ids - set(cached_contacts.keys())
        fetched_contacts_by_account_id = (
            await self.contact_query_service.map_contact_v2_by_primary_account_ids(
                organization_id=organization_id,
                account_ids=account_ids_to_fetch,
                include_custom_object=include_custom_object,
            )
        )
        cached_contacts.update(fetched_contacts_by_account_id)
        contact_by_primary_account_id: dict[UUID, list[ContactV2]] = {
            account_id: cached_contacts[account_id]
            for account_id in account_ids
            if account_id in cached_contacts
        }
        contact_by_id: dict[UUID, ContactV2] = {}
        for contacts in contact_by_primary_account_id.values():
            for contact in contacts:
                contact_by_id[contact.id] = contact

        contact_records = await self._list_contact_records(
            organization_id=organization_id,
            only_include_contact_ids=set(contact_by_id.keys()),
            field_references=field_references,
            fetch_context=fetch_context,
            include_custom_object=include_custom_object,
            user_auth_context=user_auth_context,
        )

        result: dict[UUID, list[StandardRecord[ContactV2]]] = defaultdict(list)
        for contact_record in contact_records:
            if contact_record.data.primary_account_id:
                result[contact_record.data.primary_account_id].append(contact_record)

        return result

    # @log_timing(logger=logger)
    async def _list_task_records(  # noqa: C901, PLR0912, PLR0915
        self,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_task_ids: UnsetAware[set[UUID]] = UNSET,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
        filter_spec: FilterSpec | None = None,
        sorting_spec: SortingSpec | None = None,
    ) -> Sequence[StandardRecord[TaskV2]]:
        # Early return
        if specified(only_include_task_ids) and not only_include_task_ids:
            return []

        logger.bind(organization_id=organization_id, user_id=user_id).info(
            "[_list_task_records] start"
        )
        cached_tasks: dict[UUID, TaskV2] = fetch_context.task_by_id
        task_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_task_ids - set(cached_tasks.keys())
            if specified(only_include_task_ids)
            else UNSET
        )

        hint = self.domain_fetch_hints_from_filter_and_sorting_spec(
            filter_spec=filter_spec,
            sorting_spec=sorting_spec,
            limit=None,
            standard_object_identifier=TaskV2.object_id,
            only_include_object_ids=task_ids_to_fetch,
        )

        fetched_tasks = (
            await self.task_query_service.list_tasks_with_domain_fetch_hints(
                organization_id=organization_id,
                only_include_task_ids=task_ids_to_fetch,
                include_custom_object=include_custom_object,
                domain_fetch_hints=hint,
            )
        )

        if user_auth_context:
            fetched_tasks = await self.task_query_service.filter_viewable_records(
                records=fetched_tasks,
                user_auth_context=user_auth_context,
            )

        fetched_task_by_id = {task.id: task for task in fetched_tasks}
        cached_tasks.update(fetched_task_by_id)

        task_by_id: dict[UUID, TaskV2] = (
            {
                task_id: cached_tasks[task_id]
                for task_id in only_include_task_ids
                if task_id in cached_tasks
            }
            if specified(only_include_task_ids)
            else fetched_task_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_task_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        sequence_step_execution_by_ids = await self.sequence_execution_query_service.map_sequence_step_executions_by_ids(
            organization_id=organization_id,
            sequence_step_execution_ids=[
                task.sequence_step_execution_id
                for task in task_by_id.values()
                if task.sequence_step_execution_id
            ],
        )

        sequence_step_execution_by_task_id: dict[UUID, SequenceStepExecution | None] = {
            task.id: sequence_step_execution_by_ids.get(
                task.sequence_step_execution_id, None
            )
            for task in task_by_id.values()
            if task.sequence_step_execution_id
        }

        sequence_steps_by_id = (
            await self.sequence_step_query_service.map_sequence_steps_by_ids(
                organization_id=organization_id,
                step_ids={
                    sse.sequence_step_id
                    for sse in sequence_step_execution_by_ids.values()
                },
            )
        )

        sequence_steps_by_task_id: dict[UUID, SequenceStepV2 | None] = {
            task_id: sequence_steps_by_id.get(sse.sequence_step_id, None)
            for task_id, sse in sequence_step_execution_by_task_id.items()
            if sse
        }

        sequence_step_variant_by_ids = await self.sequence_step_variant_query_service.map_sequence_step_variants_by_ids(
            organization_id=organization_id,
            sequence_step_variant_ids=[
                sse.sequence_step_variant_id
                for sse in sequence_step_execution_by_ids.values()
            ],
        )

        sequence_step_variant_by_task_id: dict[UUID, SequenceStepVariantV2 | None] = {
            task_id: sequence_step_variant_by_ids.get(
                sse.sequence_step_variant_id, None
            )
            for task_id, sse in sequence_step_execution_by_task_id.items()
            if sse
        }

        for (
            _relationship_id,
            _field_references,
        ) in field_references_by_relationship_id.items():
            next_layer_field_references: list[FieldReference] = [
                fr.field
                for fr in _field_references
                if isinstance(fr.field, FieldReference)
            ]
            if _relationship_id in TaskRelationship:
                _task_relationship_id = TaskRelationship(str(_relationship_id))
                with logger.contextualize(
                    task_relationship_id=_task_relationship_id,
                    next_layer_field_references=next_layer_field_references,
                ):
                    match _task_relationship_id:
                        case TaskRelationship.task__to__account:
                            account_id_to_task_ids: dict[UUID, set[UUID]] = {}
                            for task in task_by_id.values():
                                if task.account_id:
                                    account_id_to_task_ids.setdefault(
                                        task.account_id, set()
                                    ).add(task.id)
                            account_records = await self._list_account_records(
                                organization_id=organization_id,
                                only_include_account_ids=set(
                                    account_id_to_task_ids.keys()
                                ),
                                field_references=next_layer_field_references,
                                fetch_context=fetch_context,
                                include_custom_object=include_custom_object,
                            )
                            for account_record in account_records:
                                for _task_id in account_id_to_task_ids[
                                    account_record.data.id
                                ]:
                                    related_objects_by_relationship_id_by_task_id[
                                        _task_id
                                    ][_relationship_id].append(account_record)

                        case TaskRelationship.task__to__pipeline:
                            pipeline_id_to_task_ids: dict[UUID, set[UUID]] = {}
                            for task in task_by_id.values():
                                if pipeline_id := task.pipeline_id:
                                    pipeline_id_to_task_ids.setdefault(
                                        pipeline_id, set()
                                    ).add(task.id)
                            pipeline_records = await self._list_pipeline_records(
                                organization_id=organization_id,
                                only_include_pipeline_ids=set(
                                    pipeline_id_to_task_ids.keys()
                                ),
                                field_references=next_layer_field_references,
                                fetch_context=fetch_context,
                                include_custom_object=include_custom_object,
                            )
                            for pipeline_record in pipeline_records:
                                for _task_id in pipeline_id_to_task_ids[
                                    pipeline_record.data.id
                                ]:
                                    related_objects_by_relationship_id_by_task_id[
                                        _task_id
                                    ][_relationship_id].append(pipeline_record)

                        case TaskRelationship.task__to__contacts:
                            contact_id_to_task_ids: dict[UUID, set[UUID]] = {}
                            for task in task_by_id.values():
                                if task.contact_ids:
                                    for contact_id in task.contact_ids:
                                        contact_id_to_task_ids.setdefault(
                                            contact_id, set()
                                        ).add(task.id)
                            contact_records = await self._list_contact_records(
                                organization_id=organization_id,
                                only_include_contact_ids=set(
                                    contact_id_to_task_ids.keys()
                                ),
                                field_references=next_layer_field_references,
                                fetch_context=fetch_context,
                                include_custom_object=include_custom_object,
                            )
                            for contact_record in contact_records:
                                for _task_id in contact_id_to_task_ids[
                                    contact_record.data.id
                                ]:
                                    related_objects_by_relationship_id_by_task_id[
                                        _task_id
                                    ][_relationship_id].append(contact_record)

                        case (
                            TaskRelationship.task__to__archived_by_user
                            | TaskRelationship.task__to__created_by_user
                            | TaskRelationship.task__to__updated_by_user
                            | TaskRelationship.task__to__owner_user
                        ):
                            user_id_field = next(
                                outbound.ordered_self_field_identifiers[0].field_name
                                for outbound in TaskV2.outbound_relationships
                                if outbound.id == _relationship_id
                                and outbound.ordered_self_field_identifiers
                                and isinstance(
                                    outbound.ordered_self_field_identifiers[0],
                                    StandardFieldIdentifier,
                                )
                            )
                            user_id_to_task_ids: dict[UUID, set[UUID]] = {}
                            for task in task_by_id.values():
                                if user_id_field_value := getattr(
                                    task, user_id_field, None
                                ):
                                    user_id_to_task_ids.setdefault(
                                        user_id_field_value, set()
                                    ).add(task.id)
                            user_records = await self._list_organization_user_records(
                                organization_id=organization_id,
                                only_include_user_ids=set(user_id_to_task_ids.keys()),
                                fetch_context=fetch_context,
                            )
                            for user_record in user_records:
                                for _deal_id in user_id_to_task_ids[
                                    user_record.data.id
                                ]:
                                    related_objects_by_relationship_id_by_task_id[
                                        _deal_id
                                    ][_relationship_id].append(user_record)

                        case TaskRelationship.task__to__participant_users:
                            # map task to participants.
                            task_id_to_participant_user_ids: dict[UUID, set[UUID]] = {}
                            for task in task_by_id.values():
                                if _participant_user_ids := getattr(
                                    task, "participant_user_id_list", None
                                ):
                                    task_id_to_participant_user_ids.setdefault(
                                        task.id, set()
                                    ).update(_participant_user_ids)

                            # flatten list of sets to get the unique set of user IDs.
                            users: list[UUID] = [
                                item
                                for set_ in task_id_to_participant_user_ids.values()
                                for item in set_
                            ]

                            participant_user_records: dict[
                                UUID, StandardRecord[OrganizationUserV2]
                            ] = {
                                user_record.data.id: user_record
                                for user_record in await self._list_organization_user_records(
                                    organization_id=organization_id,
                                    only_include_user_ids=set(users),
                                    fetch_context=fetch_context,
                                )
                            }

                            # reassociate participant user IDs back to the task via related_records.
                            for (
                                task_id,
                                participant_user_ids,
                            ) in task_id_to_participant_user_ids.items():
                                for participant_user_id in participant_user_ids:
                                    related_objects_by_relationship_id_by_task_id[
                                        task_id
                                    ][_relationship_id].append(
                                        participant_user_records[participant_user_id]
                                    )

                        case TaskRelationship.task__to__comment:
                            comment_records = await self._list_comment_records(
                                organization_id=organization_id,
                                field_references=next_layer_field_references,
                                fetch_context=fetch_context,
                                include_custom_object=include_custom_object,
                            )
                            task_comments = await self.filter_record_sequence(
                                organization_id=organization_id,
                                filter_spec=FilterSpec(
                                    primary_object_identifier=Comment.object_id,
                                    filter=ValueFilter(
                                        field=QualifiedField(
                                            path=("reference_id_type",),
                                        ),
                                        operator=MatchOperator.EQ,
                                        value=CommentReferenceIdType.TASK,
                                    ),
                                ),
                                records=comment_records,
                            )
                            for comment_record in task_comments:
                                comment_task_id = comment_record.data.reference_id
                                if comment_task_id in task_by_id:
                                    related_objects_by_relationship_id_by_task_id[
                                        comment_task_id
                                    ][_relationship_id].append(comment_record)

                        case TaskRelationship.task__to__email_threads:
                            email_thread_ids_to_task_ids: dict[UUID, set[UUID]] = {}
                            for task in task_by_id.values():
                                if task.email_thread_ids:
                                    for email_thread_id in task.email_thread_ids:
                                        email_thread_ids_to_task_ids.setdefault(
                                            email_thread_id, set()
                                        ).add(task.id)

                            email_thread_records: Sequence[
                                StandardRecord[GlobalThread]
                            ] = []
                            if user_id:
                                logger.bind(
                                    organization_id=organization_id,
                                    user_id=user_id,
                                ).info("Fetching email thread records")
                                email_thread_records = (
                                    await self.list_global_thread_records(
                                        user_id=user_id,
                                        organization_id=organization_id,
                                        only_include_thread_ids=set(
                                            email_thread_ids_to_task_ids.keys()
                                        ),
                                        include_custom_object=include_custom_object,
                                    )
                                )
                            else:
                                logger.bind(
                                    organization_id=organization_id,
                                    user_id=user_id,
                                ).warning(
                                    "No user ID provided, skipping email thread records"
                                )
                                email_thread_records = []

                            for email_thread_record in email_thread_records:
                                for _task_id in email_thread_ids_to_task_ids[
                                    email_thread_record.data.id
                                ]:
                                    related_objects_by_relationship_id_by_task_id[
                                        _task_id
                                    ][_relationship_id].append(email_thread_record)

                        case TaskRelationship.task__to__meeting:
                            meeting_id_to_task_ids: dict[UUID, set[UUID]] = {}
                            for task in task_by_id.values():
                                if task.meeting_id:
                                    meeting_id_to_task_ids.setdefault(
                                        task.meeting_id, set()
                                    ).add(task.id)

                            meeting_records = await self._list_meeting_records(
                                user_id=user_id,
                                organization_id=organization_id,
                                only_include_meeting_ids=set(
                                    meeting_id_to_task_ids.keys()
                                ),
                                field_references=next_layer_field_references,
                                fetch_context=fetch_context,
                                include_custom_object=include_custom_object,
                                filter_spec=None,
                                sorting_spec=None,
                            )
                            for meeting_record in meeting_records:
                                for _task_id in meeting_id_to_task_ids[
                                    meeting_record.data.id
                                ]:
                                    related_objects_by_relationship_id_by_task_id[
                                        _task_id
                                    ][_relationship_id].append(meeting_record)

                        case TaskRelationship.task__to__result_meetings:
                            result_meeting_id_to_task_ids: dict[UUID, set[UUID]] = {}
                            for task in task_by_id.values():
                                if task.result_meeting_ids:
                                    for result_meeting_id in task.result_meeting_ids:
                                        result_meeting_id_to_task_ids.setdefault(
                                            result_meeting_id, set()
                                        ).add(task.id)

                            meeting_records = await self._list_meeting_records(
                                user_id=user_id,
                                organization_id=organization_id,
                                only_include_meeting_ids=set(
                                    result_meeting_id_to_task_ids.keys()
                                ),
                                field_references=next_layer_field_references,
                                fetch_context=fetch_context,
                                include_custom_object=include_custom_object,
                                filter_spec=None,
                                sorting_spec=None,
                            )
                            for meeting_record in meeting_records:
                                for _task_id in result_meeting_id_to_task_ids[
                                    meeting_record.data.id
                                ]:
                                    related_objects_by_relationship_id_by_task_id[
                                        _task_id
                                    ][_relationship_id].append(meeting_record)

                        case TaskRelationship.task__to__citation:
                            citation_id_to_task_ids: dict[UUID, set[UUID]] = {}
                            for task in task_by_id.values():
                                if task.citation_id:
                                    citation_id_to_task_ids.setdefault(
                                        task.citation_id, set()
                                    ).add(task.id)
                            citations = (
                                await self.citation_query_service.list_citations(
                                    organization_id=organization_id,
                                    only_include_citation_ids=set(
                                        citation_id_to_task_ids.keys()
                                    ),
                                )
                            )
                            for citation in citations:
                                for _task_id in citation_id_to_task_ids[citation.id]:
                                    related_objects_by_relationship_id_by_task_id[
                                        _task_id
                                    ][_relationship_id].append(
                                        StandardRecord[Citation](
                                            object_id=Citation.object_id,
                                            requested_relationships=set(),
                                            related_records={},
                                            data=citation,
                                        )
                                    )

                        case TaskRelationship.task__to__sequence:
                            sequence_id_to_task_ids: dict[UUID, set[UUID]] = {}
                            for task in task_by_id.values():
                                if task.sequence_id:
                                    sequence_id_to_task_ids.setdefault(
                                        task.sequence_id, set()
                                    ).add(task.id)
                            sequences = (
                                await self.sequence_query_service.list_sequences(
                                    organization_id=organization_id,
                                    only_include_sequence_ids=set(
                                        sequence_id_to_task_ids.keys()
                                    ),
                                )
                            )
                            for sequence in sequences:
                                for _task_id in sequence_id_to_task_ids[sequence.id]:
                                    related_objects_by_relationship_id_by_task_id[
                                        _task_id
                                    ][_relationship_id].append(
                                        StandardRecord[SequenceV2](
                                            object_id=SequenceV2.object_id,
                                            requested_relationships=set(),
                                            related_records={},
                                            data=sequence,
                                        )
                                    )

                        case TaskRelationship.task__to__sequence_enrollment:
                            sequence_enrollment_id_to_task_ids: dict[
                                UUID, set[UUID]
                            ] = {}
                            for task in task_by_id.values():
                                if task.sequence_enrollment_id:
                                    sequence_enrollment_id_to_task_ids.setdefault(
                                        task.sequence_enrollment_id, set()
                                    ).add(task.id)

                            sequence_enrollments = (
                                await self._list_sequence_enrollment_records(
                                    organization_id=organization_id,
                                    fetch_context=fetch_context,
                                    field_references=next_layer_field_references,
                                    only_include_sequence_enrollment_ids=set(
                                        sequence_enrollment_id_to_task_ids.keys()
                                    ),
                                )
                            )
                            for sequence_enrollment in sequence_enrollments:
                                for _task_id in sequence_enrollment_id_to_task_ids[
                                    sequence_enrollment.data.id
                                ]:
                                    related_objects_by_relationship_id_by_task_id[
                                        _task_id
                                    ][_relationship_id].append(sequence_enrollment)

                        case TaskRelationship.task__to__sequence_step:
                            for (
                                task_id,
                                sequence_step,
                            ) in sequence_steps_by_task_id.items():
                                if sequence_step is None:
                                    continue

                                related_objects_by_relationship_id_by_task_id[task_id][
                                    _relationship_id
                                ].append(
                                    StandardRecord[SequenceStepV2](
                                        object_id=SequenceStepV2.object_id,
                                        requested_relationships=set(),
                                        related_records={},
                                        data=sequence_step,
                                    )
                                )

                        case TaskRelationship.task__to__sequence_step_execution:
                            for (
                                task_id,
                                sequence_step_execution,
                            ) in sequence_step_execution_by_task_id.items():
                                if sequence_step_execution is None:
                                    continue

                                related_objects_by_relationship_id_by_task_id[task_id][
                                    _relationship_id
                                ].append(
                                    StandardRecord[SequenceStepExecution](
                                        object_id=SequenceStepExecution.object_id,
                                        requested_relationships=set(),
                                        related_records={},
                                        data=sequence_step_execution,
                                    )
                                )

                        case TaskRelationship.task__to__sequence_step_variant:
                            for (
                                task_id,
                                sequence_step_variant,
                            ) in sequence_step_variant_by_task_id.items():
                                if sequence_step_variant is None:
                                    continue

                                related_objects_by_relationship_id_by_task_id[task_id][
                                    _relationship_id
                                ].append(
                                    StandardRecord[SequenceStepVariantV2](
                                        object_id=SequenceStepVariantV2.object_id,
                                        requested_relationships=set(),
                                        related_records={},
                                        data=sequence_step_variant,
                                    )
                                )

                        case _ as unreachable:
                            assert_never(unreachable)
            else:
                raise NotImplementedError(
                    f"Unsupported custom object relationship: {_relationship_id}"
                )
        result: list[StandardRecord[TaskV2]] = []
        for task_id, task in task_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_task_id[
                    task_id
                ].items()
            }
            result.append(
                StandardRecord[TaskV2](
                    object_id=TaskV2.object_id,
                    is_editable=await self.task_query_service.is_editable(
                        user_auth_context=user_auth_context,
                        domain_object=task,
                    ),
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=task,
                )
            )
        return result

    @staticmethod
    def _group_field_references_by_relationship_id(
        *,
        field_references: list[FieldReference],
    ) -> dict[RelationshipId, list[FieldReference]]:
        field_references_by_relationship_id: dict[
            RelationshipId, list[FieldReference]
        ] = defaultdict(list)
        for field_reference in field_references:
            if field_reference.relationship_id:
                field_references_by_relationship_id[
                    field_reference.relationship_id
                ].append(field_reference)
        return field_references_by_relationship_id

    async def get_comment_record(
        self,
        record_id: UUID,
        organization_id: UUID,
        include_custom_object: bool,
        ordered_object_fields: (
            tuple[QualifiedField | FieldReference, ...] | None
        ) = None,
    ) -> Sequence[StandardRecord[Comment]]:
        comments = await self.list_comment_records(
            organization_id=organization_id,
            include_custom_object=include_custom_object,
            only_include_comment_ids={record_id},
            fetch_conditions=ObjectRecordFetchConditions(
                fields=ordered_object_fields,
                filter_spec=None,
                sorting_spec=None,
            ),
        )
        if not comments:
            raise ResourceNotFoundError(f"No comment record found for {record_id}")
        return comments

    async def list_comment_records(
        self,
        organization_id: UUID,
        include_custom_object: bool,
        only_include_comment_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[Comment]]:
        # Early return
        if specified(only_include_comment_ids) and not only_include_comment_ids:
            return []

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=Comment.object_id.object_name,
                object_identifier=Comment.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=Comment.object_id,
            fetch_conditions=fetch_conditions,
        )
        result = await self._list_comment_records(
            organization_id=organization_id,
            only_include_comment_ids=only_include_comment_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    # @log_timing(logger=logger)
    async def _list_comment_records(  # noqa: C901, PLR0912
        self,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_comment_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> Sequence[StandardRecord[Comment]]:
        # Early return
        if specified(only_include_comment_ids) and not only_include_comment_ids:
            return []

        cached_comments: dict[UUID, Comment] = fetch_context.comment_by_id
        comment_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_comment_ids - set(cached_comments.keys())
            if specified(only_include_comment_ids)
            else UNSET
        )

        fetched_comments: list[
            Comment
        ] = await self.comment_query_service.list_comments(
            organization_id=organization_id,
            only_include_comment_ids=comment_ids_to_fetch,
        )

        fetched_comment_by_id = {comment.id: comment for comment in fetched_comments}
        cached_comments.update(fetched_comment_by_id)

        comment_by_id: dict[UUID, Comment] = (
            {
                comment_id: cached_comments[comment_id]
                for comment_id in only_include_comment_ids
                if comment_id in cached_comments
            }
            if specified(only_include_comment_ids)
            else fetched_comment_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_comment_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for (
            _relationship_id,
            _field_references,
        ) in field_references_by_relationship_id.items():
            next_layer_field_references: list[FieldReference] = [
                fr.field
                for fr in _field_references
                if isinstance(fr.field, FieldReference)
            ]
            if _relationship_id in CommentRelationship:
                _comment_relationship_id = CommentRelationship(str(_relationship_id))
                match _comment_relationship_id:
                    case CommentRelationship.comment__to__created_by_user:
                        user_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in Comment.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        user_id_to_comment_ids: dict[UUID, set[UUID]] = {}
                        for comment in comment_by_id.values():
                            if user_id := getattr(comment, user_id_field, None):
                                user_id_to_comment_ids.setdefault(user_id, set()).add(
                                    comment.id
                                )
                        user_records = await self._list_organization_user_records(
                            organization_id=organization_id,
                            only_include_user_ids=set(user_id_to_comment_ids.keys()),
                            fetch_context=fetch_context,
                        )
                        for user_record in user_records:
                            for _comment_id in user_id_to_comment_ids[
                                user_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_comment_id[
                                    _comment_id
                                ][_relationship_id].append(user_record)
                    case CommentRelationship.comment__to__meeting:
                        meeting_id_to_comment_ids: dict[UUID, set[UUID]] = {}
                        for comment in comment_by_id.values():
                            if (
                                comment.reference_id_type
                                == CommentReferenceIdType.MEETING
                            ):
                                meeting_id_to_comment_ids.setdefault(
                                    comment.reference_id, set()
                                ).add(comment.id)
                        meeting_records = await self._list_meeting_records(
                            organization_id=organization_id,
                            user_id=None,
                            only_include_meeting_ids=set(
                                meeting_id_to_comment_ids.keys()
                            ),
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                        )
                        for meeting_record in meeting_records:
                            for _comment_id in meeting_id_to_comment_ids[
                                meeting_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_comment_id[
                                    _comment_id
                                ][_relationship_id].append(meeting_record)
                    case CommentRelationship.comment__to__task:
                        task_id_to_comment_ids: dict[UUID, set[UUID]] = {}
                        for comment in comment_by_id.values():
                            if comment.reference_id_type == CommentReferenceIdType.TASK:
                                task_id_to_comment_ids.setdefault(
                                    comment.reference_id, set()
                                ).add(comment.id)
                        task_records = await self._list_task_records(
                            organization_id=organization_id,
                            only_include_task_ids=set(task_id_to_comment_ids.keys()),
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                            filter_spec=None,
                            sorting_spec=None,
                        )
                        for task_record in task_records:
                            for _comment_id in task_id_to_comment_ids[
                                task_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_comment_id[
                                    _comment_id
                                ][_relationship_id].append(task_record)

                    case CommentRelationship.comment__to__parent_comment:
                        # comments: parent_ids
                        parent_comment_id_to_comment_ids: dict[UUID, set[UUID]] = {}
                        for comment in comment_by_id.values():
                            if parent_id := comment.parent_comment_id:
                                parent_comment_id_to_comment_ids.setdefault(
                                    parent_id, set()
                                ).add(comment.id)

                        # fetch parent comments from parent_ids
                        parent_comment_records = await self._list_comment_records(
                            organization_id=organization_id,
                            only_include_comment_ids=set(
                                parent_comment_id_to_comment_ids.keys()
                            ),
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                        )

                        # associate parent comments back to child comments
                        for parent_comment_record in parent_comment_records:
                            for _comment_id in parent_comment_id_to_comment_ids[
                                parent_comment_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_comment_id[
                                    _comment_id
                                ][_relationship_id].append(parent_comment_record)

                    case _ as unreachable:
                        assert_never(unreachable)
            else:
                raise NotImplementedError(
                    f"Unsupported custom object relationship: {_relationship_id}"
                )

        result: list[StandardRecord[Comment]] = []
        for comment_id, comment in comment_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_comment_id[
                    comment_id
                ].items()
            }
            result.append(
                StandardRecord[Comment](
                    object_id=Comment.object_id,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=comment,
                )
            )
        return result

    # @log_timing(logger=logger)
    async def list_global_thread_records(
        self,
        user_id: UUID,
        organization_id: UUID,
        include_custom_object: bool,
        fetch_conditions: ObjectRecordFetchConditions | None = None,
        only_include_thread_ids: UnsetAware[set[UUID]] = UNSET,
        only_include_thread_for_contacts: UnsetAware[set[UUID]] = UNSET,
        only_include_thread_for_accounts: UnsetAware[set[UUID]] = UNSET,
        only_include_thread_for_pipeline: UnsetAware[UUID] = UNSET,
        only_include_thread_for_contacts_email: UnsetAware[set[str]] = UNSET,
        only_include_thread_for_sequence: UnsetAware[UUID] = UNSET,
    ) -> Sequence[StandardRecord[GlobalThread]]:
        # Early return
        if specified(only_include_thread_ids) and not only_include_thread_ids:
            return []
        if (
            specified(only_include_thread_for_contacts)
            and not only_include_thread_for_contacts
        ):
            return []
        if (
            specified(only_include_thread_for_accounts)
            and not only_include_thread_for_accounts
        ):
            return []
        if (
            specified(only_include_thread_for_contacts_email)
            and not only_include_thread_for_contacts_email
        ):
            return []

        fetch_context = new_fetch_context()
        field_references: list[FieldReference] = []
        filter_spec = None
        sorting_spec = None
        should_include_custom_object = False

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=GlobalThread.object_id.object_name,
                object_identifier=GlobalThread.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )

        if fetch_conditions:
            (
                field_references_set,
                filter_spec,
                sorting_spec,
                should_include_custom_object,
            ) = await self.validate_field_and_fetch_specs(
                organization_id=organization_id,
                object_identifier=GlobalThread.object_id,
                fetch_conditions=fetch_conditions,
            )
            field_references = list(field_references_set)

        result = await self._list_global_thread_records(
            user_id=user_id,
            organization_id=organization_id,
            only_include_thread_ids=only_include_thread_ids,
            field_references=field_references,
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
            only_include_thread_for_contacts=only_include_thread_for_contacts,
            only_include_thread_for_accounts=only_include_thread_for_accounts,
            only_include_thread_for_pipeline=only_include_thread_for_pipeline,
            only_include_thread_for_contacts_email=only_include_thread_for_contacts_email,
            only_include_thread_for_sequence=only_include_thread_for_sequence,
        )
        del fetch_context

        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def _list_event_schedule_records(
        self,
        user_id: UUID,
        organization_id: UUID,
        fetch_context: FetchContext,
        field_references: list[FieldReference],
        only_include_event_schedule_ids: UnsetAware[set[UUID]] = UNSET,
        include_shared: bool = False,
    ) -> Sequence[StandardRecord[EventSchedule]]:
        # Early return
        if (
            specified(only_include_event_schedule_ids)
            and not only_include_event_schedule_ids
        ):
            return []

        cached_event_schedules: dict[UUID, EventSchedule] = (
            fetch_context.event_schedule_by_id
        )
        event_schedule_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_event_schedule_ids - set(cached_event_schedules.keys())
            if specified(only_include_event_schedule_ids)
            else UNSET
        )
        event_schedule_dtos = (
            await self.event_schedule_query_service.list_event_schedules(
                user_id=user_id,
                organization_id=organization_id,
                only_include_event_schedule_ids=event_schedule_ids_to_fetch,
                include_shared=include_shared,
            )
        )
        fetched_event_schedules: list[EventSchedule] = [
            EventSchedule.from_dto(event_schedule_dto)
            for event_schedule_dto in event_schedule_dtos
        ]

        fetched_event_schedule_by_id = {
            event_schedule.id: event_schedule
            for event_schedule in fetched_event_schedules
        }
        cached_event_schedules.update(fetched_event_schedule_by_id)

        event_schedule_by_id: dict[UUID, EventSchedule] = (
            {
                event_schedule_id: cached_event_schedules[event_schedule_id]
                for event_schedule_id in only_include_event_schedule_ids
                if event_schedule_id in cached_event_schedules
            }
            if specified(only_include_event_schedule_ids)
            else fetched_event_schedule_by_id
        )

        result: list[StandardRecord[EventSchedule]] = []
        for event_schedule in event_schedule_by_id.values():
            result.append(
                StandardRecord[EventSchedule](
                    object_id=EventSchedule.object_id,
                    requested_relationships=set(),
                    related_records={},
                    data=event_schedule,
                )
            )
        return result

    async def list_event_schedule_records(
        self,
        user_id: UUID,
        organization_id: UUID,
        fetch_conditions: ObjectRecordFetchConditions | None = None,
        only_include_event_schedule_ids: UnsetAware[set[UUID]] = UNSET,
        include_shared: bool = False,
    ) -> Sequence[StandardRecord[EventSchedule]]:
        # Early return
        if (
            specified(only_include_event_schedule_ids)
            and not only_include_event_schedule_ids
        ):
            return []

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=EventSchedule.object_id.object_name,
                object_identifier=EventSchedule.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )

        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            _,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=EventSchedule.object_id,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_event_schedule_records(
            user_id=user_id,
            organization_id=organization_id,
            only_include_event_schedule_ids=only_include_event_schedule_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_shared=include_shared,
        )

        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )

        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def _list_global_thread_records(  # noqa: C901
        self,
        user_id: UUID,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_thread_ids: UnsetAware[set[UUID]] = UNSET,
        only_include_thread_for_contacts: UnsetAware[set[UUID]] = UNSET,
        only_include_thread_for_accounts: UnsetAware[set[UUID]] = UNSET,
        only_include_thread_for_pipeline: UnsetAware[UUID] = UNSET,
        only_include_thread_for_contacts_email: UnsetAware[set[str]] = UNSET,
        only_include_thread_for_sequence: UnsetAware[UUID] = UNSET,
    ) -> Sequence[StandardRecord[GlobalThread]]:
        """Internal method to list global thread records with related objects."""

        # Early return
        if specified(only_include_thread_ids) and not only_include_thread_ids:
            return []
        if (
            specified(only_include_thread_for_contacts)
            and not only_include_thread_for_contacts
        ):
            return []
        if (
            specified(only_include_thread_for_accounts)
            and not only_include_thread_for_accounts
        ):
            return []
        if (
            specified(only_include_thread_for_contacts_email)
            and not only_include_thread_for_contacts_email
        ):
            return []

        cached_threads: dict[UUID, GlobalThread] = fetch_context.global_thread_by_id
        thread_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_thread_ids - set(cached_threads.keys())
            if specified(only_include_thread_ids)
            else UNSET
        )

        fetched_threads = await self.global_thread_query_service.list_global_threads(
            user_id=user_id,
            organization_id=organization_id,
            only_include_thread_ids=thread_ids_to_fetch,
            include_custom_object=include_custom_object,
            only_include_thread_for_contacts=only_include_thread_for_contacts,
            only_include_thread_for_accounts=only_include_thread_for_accounts,
            only_include_thread_for_pipeline=only_include_thread_for_pipeline,
            only_include_thread_for_contacts_email=only_include_thread_for_contacts_email,
            only_include_thread_for_sequence=only_include_thread_for_sequence,
        )
        fetched_thread_by_id = {thread.id: thread for thread in fetched_threads}
        cached_threads.update(fetched_thread_by_id)

        thread_by_id: dict[UUID, GlobalThread] = (
            {
                thread_id: cached_threads[thread_id]
                for thread_id in only_include_thread_ids
                if thread_id in cached_threads
            }
            if specified(only_include_thread_ids)
            else fetched_thread_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_thread_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for (
            _relationship_id,
            _field_references,
        ) in field_references_by_relationship_id.items():
            next_layer_field_references: list[FieldReference] = [
                fr.field
                for fr in _field_references
                if isinstance(fr.field, FieldReference)
            ]
            if _relationship_id in GlobalThreadRelationship:
                _thread_relationship_id = GlobalThreadRelationship(
                    str(_relationship_id)
                )
                match _thread_relationship_id:
                    case (
                        GlobalThreadRelationship.global_message__to__participant_contact
                    ):
                        await self._process_global_thread_relationship_contact(
                            organization_id=organization_id,
                            _relationship_id=_thread_relationship_id,
                            fetch_context=fetch_context,
                            thread_by_id=thread_by_id,
                            include_custom_object=include_custom_object,
                            next_layer_field_references=next_layer_field_references,
                            related_objects_by_relationship_id_by_thread_id=related_objects_by_relationship_id_by_thread_id,
                        )
                    case GlobalThreadRelationship.global_message__to__account:
                        await self._process_global_thread_relationship_account(
                            organization_id=organization_id,
                            _relationship_id=_thread_relationship_id,
                            fetch_context=fetch_context,
                            thread_by_id=thread_by_id,
                            include_custom_object=include_custom_object,
                            next_layer_field_references=next_layer_field_references,
                            related_objects_by_relationship_id_by_thread_id=related_objects_by_relationship_id_by_thread_id,
                        )
                    case GlobalThreadRelationship.global_message__to__pipeline:
                        await self._process_global_thread_relationship_pipeline(
                            organization_id=organization_id,
                            _relationship_id=_thread_relationship_id,
                            fetch_context=fetch_context,
                            thread_by_id=thread_by_id,
                            include_custom_object=include_custom_object,
                            next_layer_field_references=next_layer_field_references,
                            related_objects_by_relationship_id_by_thread_id=related_objects_by_relationship_id_by_thread_id,
                        )
                    case _ as unreachable:
                        assert_never(unreachable)
            else:
                raise NotImplementedError(
                    f"Unsupported custom object relationship: {_relationship_id}"
                )

        result: list[StandardRecord[GlobalThread]] = []
        for thread_id, thread in thread_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_thread_id[
                    thread_id
                ].items()
            }
            result.append(
                StandardRecord[GlobalThread](
                    object_id=GlobalThread.object_id,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=thread,
                )
            )
        return result

    async def _process_global_thread_relationship_contact(
        self,
        *,
        organization_id: UUID,
        _relationship_id: RelationshipId,
        fetch_context: FetchContext,
        thread_by_id: dict[UUID, GlobalThread],
        include_custom_object: bool,
        next_layer_field_references: list[FieldReference],
        related_objects_by_relationship_id_by_thread_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ],
    ) -> None:
        contact_id_to_thread_ids: dict[UUID, set[UUID]] = {}
        for thread in thread_by_id.values():
            if thread.participant_contact_id_list:
                for contact_id in thread.participant_contact_id_list:
                    contact_id_to_thread_ids.setdefault(contact_id, set()).add(
                        thread.id
                    )

        contact_records = await self._list_contact_records(
            organization_id=organization_id,
            only_include_contact_ids=set(contact_id_to_thread_ids.keys()),
            field_references=next_layer_field_references,
            fetch_context=fetch_context,
            include_custom_object=include_custom_object,
        )

        for contact_record in contact_records:
            for thread_id in contact_id_to_thread_ids[contact_record.data.id]:
                related_objects_by_relationship_id_by_thread_id[thread_id][
                    _relationship_id
                ].append(contact_record)

    async def _process_global_thread_relationship_account(
        self,
        *,
        organization_id: UUID,
        _relationship_id: RelationshipId,
        fetch_context: FetchContext,
        thread_by_id: dict[UUID, GlobalThread],
        include_custom_object: bool,
        next_layer_field_references: list[FieldReference],
        related_objects_by_relationship_id_by_thread_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ],
    ) -> None:
        account_id_to_thread_ids: dict[UUID, set[UUID]] = defaultdict(set)
        for thread in thread_by_id.values():
            for account_id in thread.account_ids:
                account_id_to_thread_ids[account_id].add(thread.id)

        if not account_id_to_thread_ids:
            logger.info("No account id to thread ids")
            return

        account_records = await self._list_account_records(
            organization_id=organization_id,
            only_include_account_ids=set(account_id_to_thread_ids.keys()),
            field_references=next_layer_field_references,
            fetch_context=fetch_context,
            include_custom_object=include_custom_object,
        )

        for account_record in account_records:
            for thread_id in account_id_to_thread_ids[account_record.data.id]:
                related_objects_by_relationship_id_by_thread_id[thread_id][
                    _relationship_id
                ].append(account_record)

    async def _process_global_thread_relationship_pipeline(
        self,
        *,
        organization_id: UUID,
        _relationship_id: RelationshipId,
        fetch_context: FetchContext,
        thread_by_id: dict[UUID, GlobalThread],
        include_custom_object: bool,
        next_layer_field_references: list[FieldReference],
        related_objects_by_relationship_id_by_thread_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ],
    ) -> None:
        pipeline_id_to_thread_ids: dict[UUID, set[UUID]] = {}
        for thread in thread_by_id.values():
            if thread.pipeline_id:
                pipeline_id_to_thread_ids.setdefault(thread.pipeline_id, set()).add(
                    thread.id
                )

        if not pipeline_id_to_thread_ids:
            logger.info("No pipeline id to thread ids")
            return

        pipeline_records = await self._list_pipeline_records(
            organization_id=organization_id,
            only_include_pipeline_ids=set(pipeline_id_to_thread_ids.keys()),
            field_references=next_layer_field_references,
            fetch_context=fetch_context,
            include_custom_object=include_custom_object,
        )

        for pipeline_record in pipeline_records:
            for thread_id in pipeline_id_to_thread_ids[pipeline_record.data.id]:
                related_objects_by_relationship_id_by_thread_id[thread_id][
                    _relationship_id
                ].append(pipeline_record)

    async def get_global_thread_record(
        self,
        record_id: UUID,
        user_id: UUID,
        organization_id: UUID,
        include_custom_object: bool,
        ordered_object_fields: (
            tuple[QualifiedField | FieldReference, ...] | None
        ) = None,
    ) -> Sequence[StandardRecord[GlobalThread]]:
        """Get a global thread record by ID.

        Args:
            record_id: The ID of the global thread to fetch
            user_id: The ID of the user making the request
            organization_id: Organization ID to filter threads by
            include_custom_object: Whether to include custom object data
            ordered_object_fields: Optional tuple of fields to include in the response

        Returns:
            Sequence containing the single matching GlobalThread standard record

        Raises:
            ResourceNotFoundError: If no thread is found with the given ID
        """
        threads = await self.list_global_thread_records(
            user_id=user_id,
            organization_id=organization_id,
            include_custom_object=include_custom_object,
            only_include_thread_ids={record_id},
            fetch_conditions=ObjectRecordFetchConditions(
                fields=ordered_object_fields,
                filter_spec=None,
                sorting_spec=None,
            ),
        )
        if not threads:
            raise ResourceNotFoundError(
                f"No global thread record found for {record_id}"
            )
        return threads

    async def list_email_template_records(
        self,
        user_id: UUID,
        organization_id: UUID,
        include_custom_object: bool,
        only_include_template_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[EmailTemplate]]:
        # Early return
        if specified(only_include_template_ids) and not only_include_template_ids:
            return []

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=EmailTemplate.object_id.object_name,
                object_identifier=EmailTemplate.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=EmailTemplate.object_id,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_email_template_records(
            user_id=user_id,
            organization_id=organization_id,
            only_include_template_ids=only_include_template_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def _list_email_template_records(  # noqa: C901, PLR0912
        self,
        user_id: UUID,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_template_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> Sequence[StandardRecord[EmailTemplate]]:
        # Early return
        if specified(only_include_template_ids) and not only_include_template_ids:
            return []

        # Get cached templates
        cached_templates: dict[UUID, EmailTemplate] = fetch_context.email_template_by_id

        # Determine which template IDs need to be fetched
        template_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_template_ids - set(cached_templates.keys())
            if specified(only_include_template_ids)
            else UNSET
        )

        # Fetch templates from service
        fetched_templates = (
            await self.email_template_query_service.list_email_templates(
                organization_id=organization_id,
                user_id=user_id,
                only_include_template_ids=template_ids_to_fetch,
            )
        )

        # Update cache with fetched templates
        fetched_template_by_id = {
            template.id: template for template in fetched_templates
        }
        cached_templates.update(fetched_template_by_id)

        # Get final set of templates to return
        template_by_id: dict[UUID, EmailTemplate] = (
            {
                template_id: cached_templates[template_id]
                for template_id in only_include_template_ids
                if template_id in cached_templates
            }
            if specified(only_include_template_ids)
            else fetched_template_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_template_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for (
            _relationship_id,
            _field_references,
        ) in field_references_by_relationship_id.items():  # noqa: PERF102
            if _relationship_id in EmailTemplateRelationship:
                _template_relationship_id = EmailTemplateRelationship(
                    str(_relationship_id)
                )
                match _template_relationship_id:
                    case (
                        EmailTemplateRelationship.email_template__to__created_by_user
                        | EmailTemplateRelationship.email_template__to__updated_by_user
                    ):
                        user_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in EmailTemplate.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        user_id_to_template_ids: dict[UUID, set[UUID]] = {}
                        for template in template_by_id.values():
                            if user_id_field_value := getattr(
                                template, user_id_field, None
                            ):
                                user_id_to_template_ids.setdefault(
                                    user_id_field_value, set()
                                ).add(template.id)
                        user_records = await self._list_organization_user_records(
                            organization_id=organization_id,
                            only_include_user_ids=set(user_id_to_template_ids.keys()),
                            fetch_context=fetch_context,
                        )
                        for user_record in user_records:
                            for template_id in user_id_to_template_ids[
                                user_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_template_id[
                                    template_id
                                ][_relationship_id].append(user_record)

                    case (
                        EmailTemplateRelationship.email_template__to__attachment_details
                    ):
                        for template in template_by_id.values():
                            attachment_ids = template.attachment_ids
                            if not attachment_ids:
                                continue
                            attachment_details = (
                                await self.list_email_attachment_details(
                                    organization_id=organization_id,
                                    attachment_ids=attachment_ids,
                                )
                            )
                            attachment_detail_id_to_template_ids: dict[
                                UUID, set[UUID]
                            ] = {}
                            for attachment_detail in attachment_details:
                                attachment_detail_id_to_template_ids.setdefault(
                                    attachment_detail.data.id, set()
                                ).add(template.id)

                            for attachment_detail in attachment_details:
                                for template_id in attachment_detail_id_to_template_ids[
                                    attachment_detail.data.id
                                ]:
                                    related_objects_by_relationship_id_by_template_id[
                                        template_id
                                    ][_relationship_id].append(
                                        StandardRecord[AttachmentDetailsV2](
                                            object_id=AttachmentDetailsV2.object_id,
                                            requested_relationships=attachment_detail.requested_relationships,
                                            related_records=attachment_detail.related_records,
                                            data=attachment_detail.data,
                                        )
                                    )
                    case _ as unreachable:
                        assert_never(unreachable)
            else:
                raise NotImplementedError(
                    f"Unsupported custom object relationship: {_relationship_id}"
                )

        result: list[StandardRecord[EmailTemplate]] = []
        for template_id, template in template_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_template_id[
                    template_id
                ].items()
            }
            result.append(
                StandardRecord[EmailTemplate](
                    object_id=EmailTemplate.object_id,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=template,
                )
            )
        return result

    async def list_email_template_history_records(
        self,
        user_id: UUID,
        organization_id: UUID,
        include_custom_object: bool,
        email_template_id: UUID,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[EmailTemplate]]:
        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=EmailTemplate.object_id.object_name,
                object_identifier=EmailTemplate.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=EmailTemplate.object_id,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_email_template_history_records(
            organization_id=organization_id,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
            email_template_id=email_template_id,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )
        return result

    async def _list_email_template_history_records(
        self,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        email_template_id: UUID,
    ) -> Sequence[StandardRecord[EmailTemplate]]:
        # Fetch templates history from service
        fetched_templates = (
            await self.email_template_query_service.list_email_template_histories(
                organization_id=organization_id,
                email_template_id=email_template_id,
            )
        )

        # Build result records
        result: list[StandardRecord[EmailTemplate]] = []
        for template_history in fetched_templates:
            result.append(
                StandardRecord[EmailTemplate](
                    object_id=EmailTemplate.object_id,
                    requested_relationships=set(),
                    related_records={},
                    data=template_history,
                )
            )
        return result

    async def list_domain_object_list_items(
        self,
        user_id: UUID,
        organization_id: UUID,
        list_id: UUID,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[DomainObjectListItem]]:
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            _should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=DomainObjectListItem.object_id,
            fetch_conditions=fetch_conditions,
        )
        result = await self._list_domain_object_list_items(
            user_id=user_id,
            organization_id=organization_id,
            field_references=list(field_references),
            fetch_context=fetch_context,
            list_id=list_id,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )
        return result

    async def _list_domain_object_list_items(  # noqa: C901
        self,
        user_id: UUID,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        list_id: UUID,
    ) -> Sequence[StandardRecord[DomainObjectListItem]]:
        item_records = (
            await self.domain_object_list_query_service.list_domain_object_list_items(
                organization_id=organization_id,
                list_id=list_id,
            )
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_item_id: defaultdict[
            tuple[UUID, str, UUID],
            defaultdict[RelationshipId, list[ModeledObjectRecord]],
        ] = defaultdict(lambda: defaultdict(list))

        # Create tasks list for parallel processing
        process_tasks = []

        for _relationship_id in field_references_by_relationship_id:
            if _relationship_id in DomainObjectListItemRelationship:
                _list_relationship_id = DomainObjectListItemRelationship(
                    str(_relationship_id)
                )
                # Extract the field references for the next layer
                next_layer_field_references: list[FieldReference] = [
                    fr.field
                    for fr in field_references_by_relationship_id[_relationship_id]
                    if isinstance(fr.field, FieldReference)
                ]

                match _list_relationship_id:
                    case DomainObjectListItemRelationship.domain_object_list_item__to__account:
                        # Group account IDs for batch processing
                        account_items = [
                            (item, item.data.reference_id)
                            for item in item_records
                            if item.data.reference_type
                            == DomainObjectListItemType.ACCOUNT
                            and item.data.reference_id
                        ]
                        if account_items:
                            account_ids = {ref_id for _, ref_id in account_items}
                            process_tasks.append(
                                self._process_domain_object_list_account_items(
                                    organization_id=organization_id,
                                    list_id=list_id,
                                    account_ids=account_ids,
                                    account_items=account_items,
                                    field_references=next_layer_field_references,
                                    fetch_context=fetch_context,
                                    related_objects_map=related_objects_by_relationship_id_by_item_id,
                                    relationship_id=_relationship_id,
                                )
                            )

                    case DomainObjectListItemRelationship.domain_object_list_item__to__contact:
                        # Group contact IDs for batch processing
                        contact_items = [
                            (item, item.data.reference_id)
                            for item in item_records
                            if item.data.reference_type
                            == DomainObjectListItemType.CONTACT
                            and item.data.reference_id
                        ]
                        if contact_items:
                            contact_ids = {ref_id for _, ref_id in contact_items}
                            process_tasks.append(
                                self._process_domain_object_list_contact_items(
                                    organization_id=organization_id,
                                    list_id=list_id,
                                    contact_ids=contact_ids,
                                    contact_items=contact_items,
                                    field_references=next_layer_field_references,
                                    fetch_context=fetch_context,
                                    related_objects_map=related_objects_by_relationship_id_by_item_id,
                                    relationship_id=_relationship_id,
                                )
                            )

                    case DomainObjectListItemRelationship.domain_object_list_item__to__pipeline:
                        # Group pipeline IDs for batch processing
                        pipeline_items = [
                            (item, item.data.reference_id)
                            for item in item_records
                            if item.data.reference_type
                            == DomainObjectListItemType.PIPELINE
                            and item.data.reference_id
                        ]
                        if pipeline_items:
                            pipeline_ids = {ref_id for _, ref_id in pipeline_items}
                            process_tasks.append(
                                self._process_domain_object_list_pipeline_items(
                                    organization_id=organization_id,
                                    list_id=list_id,
                                    pipeline_ids=pipeline_ids,
                                    pipeline_items=pipeline_items,
                                    field_references=next_layer_field_references,
                                    fetch_context=fetch_context,
                                    related_objects_map=related_objects_by_relationship_id_by_item_id,
                                    relationship_id=_relationship_id,
                                )
                            )

                    case _ as unreachable:
                        assert_never(unreachable)
            else:
                raise NotImplementedError(
                    f"Unsupported custom object relationship: {_relationship_id}"
                )

        # Run all tasks in parallel
        await asyncio.gather(*process_tasks)

        logger.bind(
            related_objects_by_relationship_id_by_item_id=related_objects_by_relationship_id_by_item_id
        ).info("Related objects by relationship ID by item ID")

        result: list[StandardRecord[DomainObjectListItem]] = []
        for item in item_records:
            key = (list_id, item.data.reference_type, item.data.reference_id)
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {}

            if item.data.reference_id:  # Only process items with valid reference_id
                related_records = {
                    relationship_id: tuple(related)
                    for relationship_id, related in related_objects_by_relationship_id_by_item_id[
                        key
                    ].items()
                }

            result.append(
                StandardRecord[DomainObjectListItem](
                    object_id=DomainObjectListItem.object_id,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=item.data,
                )
            )

        return result

    async def _process_domain_object_list_account_items(
        self,
        organization_id: UUID,
        list_id: UUID,
        account_ids: set[UUID],
        account_items: list[tuple[StandardRecord[DomainObjectListItem], UUID]],
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        related_objects_map: defaultdict[
            tuple[UUID, str, UUID],
            defaultdict[RelationshipId, list[ModeledObjectRecord]],
        ],
        relationship_id: RelationshipId,
    ) -> None:
        # Fetch all accounts in a single call
        account_records = await self._list_account_records(
            organization_id=organization_id,
            only_include_account_ids=account_ids,
            field_references=field_references,
            fetch_context=fetch_context,
            include_custom_object=False,
        )

        # Map account records by ID for quick lookup
        account_records_by_id = {record.data.id: record for record in account_records}

        # Assign records to their respective list items
        for item, account_id in account_items:
            if account_id in account_records_by_id:
                related_objects_map[(list_id, item.data.reference_type, account_id)][
                    relationship_id
                ].append(account_records_by_id[account_id])

    async def _process_domain_object_list_contact_items(
        self,
        organization_id: UUID,
        list_id: UUID,
        contact_ids: set[UUID],
        contact_items: list[tuple[StandardRecord[DomainObjectListItem], UUID]],
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        related_objects_map: defaultdict[
            tuple[UUID, str, UUID],
            defaultdict[RelationshipId, list[ModeledObjectRecord]],
        ],
        relationship_id: RelationshipId,
    ) -> None:
        # Fetch all contacts in a single call
        contact_records = await self._list_contact_records(
            organization_id=organization_id,
            only_include_contact_ids=contact_ids,
            field_references=field_references,
            fetch_context=fetch_context,
            include_custom_object=False,
        )

        # Map contact records by ID for quick lookup
        contact_records_by_id = {record.data.id: record for record in contact_records}

        # Assign records to their respective list items
        for item, contact_id in contact_items:
            if contact_id in contact_records_by_id:
                related_objects_map[(list_id, item.data.reference_type, contact_id)][
                    relationship_id
                ].append(contact_records_by_id[contact_id])

    async def _process_domain_object_list_pipeline_items(
        self,
        organization_id: UUID,
        list_id: UUID,
        pipeline_ids: set[UUID],
        pipeline_items: list[tuple[StandardRecord[DomainObjectListItem], UUID]],
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        related_objects_map: defaultdict[
            tuple[UUID, str, UUID],
            defaultdict[RelationshipId, list[ModeledObjectRecord]],
        ],
        relationship_id: RelationshipId,
    ) -> None:
        # Fetch all pipelines in a single call
        pipeline_records = await self._list_pipeline_records(
            organization_id=organization_id,
            only_include_pipeline_ids=pipeline_ids,
            field_references=field_references,
            fetch_context=fetch_context,
            include_custom_object=False,
        )

        # Map pipeline records by ID for quick lookup
        pipeline_records_by_id = {record.data.id: record for record in pipeline_records}

        # Assign records to their respective list items
        for item, pipeline_id in pipeline_items:
            if pipeline_id in pipeline_records_by_id:
                related_objects_map[(list_id, item.data.reference_type, pipeline_id)][
                    relationship_id
                ].append(pipeline_records_by_id[pipeline_id])

    async def list_custom_object_data_v2(
        self,
        organization_id: UUID,
        custom_object_id_or_parent_object_name: UUID | ExtendableStandardObject,
        record_id: UUID | None = None,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        enable_query_optimization_v3: bool | None = None,
    ) -> list[CustomRecord]:
        custom_object_dto = await self.custom_object_query_service.get_custom_object(
            custom_object_id_or_parent_object_name=custom_object_id_or_parent_object_name,
            organization_id=organization_id,
            include_fields=False,
        )
        if not fetch_conditions or (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and fetch_conditions.sorting_spec is None
        ):
            if not fetch_conditions:
                fetch_conditions = ObjectRecordFetchConditions(
                    fields=(),
                    filter_spec=None,
                    sorting_spec=None,
                )
            if isinstance(fetch_conditions, ObjectRecordFetchConditions):
                fetch_conditions = ObjectRecordFetchConditions(
                    fields=fetch_conditions.fields,
                    filter_spec=fetch_conditions.filter_spec,
                    sorting_spec=SortingSpec(
                        primary_object_identifier=CustomObjectIdentifier(
                            object_id=custom_object_dto.custom_object_id,
                            organization_id=organization_id,
                        ),
                        ordered_sorters=(
                            Sorter(
                                field=QualifiedField(path=("created_at",)),
                                order=OrderEnum.DESC,
                            ),
                        ),
                    ),
                )

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=str(custom_object_id_or_parent_object_name),
                object_identifier=CustomObjectIdentifier(
                    object_id=custom_object_dto.custom_object_id,
                    organization_id=organization_id,
                ),
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )

        return await self._list_custom_object_data(
            organization_id=organization_id,
            custom_object_id_or_parent_object_name=custom_object_id_or_parent_object_name,
            fetch_conditions=fetch_conditions,
            fetch_context=new_fetch_context(),
            record_id=record_id,
        )

    async def _list_custom_object_data(  # noqa: C901, PLR0912, PLR0915
        self,
        organization_id: UUID,
        custom_object_id_or_parent_object_name: UUID | ExtendableStandardObject,
        record_id: UUID | None = None,
        fetch_context: FetchContext | None = None,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> list[CustomRecord]:
        """List custom object data with related records.

        Args:
            organization_id: Organization ID
            custom_object_id_or_parent_object_name: Custom object ID or parent object name
            fetch_conditions: Conditions for fetching data

        Returns:
            CustomObjectDataListDto containing the results
        """
        # Initialize visited relationships if not provided
        fetch_context = (
            fetch_context if fetch_context is not None else new_fetch_context()
        )
        visited_relationships = fetch_context.visited_cust_relationship_ids

        # Get the base custom object with its fields
        custom_object_dto = await self.custom_object_query_service.get_custom_object(
            custom_object_id_or_parent_object_name=custom_object_id_or_parent_object_name,
            organization_id=organization_id,
            include_fields=True,
        )

        # If this is a recursive call and we don't need related records, return early
        if visited_relationships and not fetch_conditions:
            custom_object_datas = await self.custom_object_query_service.list_custom_object_data_by_custom_object_id(
                custom_object_id=custom_object_dto.custom_object_id,
                organization_id=organization_id,
                record_id=record_id,
            )
            return [
                CustomRecord(
                    object_id=CustomObjectIdentifier(
                        object_kind=ObjectKind.CUSTOM,
                        organization_id=organization_id,
                        object_id=custom_object_dto.custom_object_id,
                    ),
                    data=CustomRecordData(
                        id=data.id,
                        custom_field_data=create_custom_object_record_data(
                            data=data,
                            custom_fields=custom_object_dto.custom_fields,
                        ),
                        display_name=data.display_name_computed,
                        created_at=data.created_at,
                        updated_at=data.updated_at,
                        deleted_at=data.deleted_at,
                        created_by_user_id=data.created_by_user_id,
                        updated_by_user_id=data.updated_by_user_id,
                        deleted_by_user_id=data.deleted_by_user_id,
                    ),
                    related_records={},
                    requested_relationships=set(),
                )
                for data in custom_object_datas
            ]

        # Validate and extract field references, filter_spec, and sorting_spec
        (
            field_references,
            filter_spec,
            sorting_spec,
            _should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=CustomObjectIdentifier(
                organization_id=organization_id,
                object_id=custom_object_dto.custom_object_id,
            ),
            fetch_conditions=fetch_conditions,
        )
        logger.debug(f"Validated field references: {field_references}")

        # Get the base list of records
        custom_object_datas = await self.custom_object_query_service.list_custom_object_data_by_custom_object_id(
            custom_object_id=custom_object_dto.custom_object_id,
            organization_id=organization_id,
            record_id=record_id,
        )
        logger.debug(f"Base custom object datas: {custom_object_datas}")

        # Group field references by relationship_id
        field_references_list = list(field_references) if field_references else []
        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references_list
            )
        )
        logger.debug(
            f"Field references by relationship: {field_references_by_relationship_id}"
        )  # Initialize mapping for related records
        related_objects_by_relationship_id_by_data_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for relationship_id, refs in field_references_by_relationship_id.items():
            if relationship_id in [
                CustomRecordRelationship.custom_record__to__created_by_user,
                CustomRecordRelationship.custom_record__to__updated_by_user,
                CustomRecordRelationship.custom_record__to__deleted_by_user,
            ]:
                user_id_field_by_user_relationship: dict[RelationshipId, str] = {
                    CustomRecordRelationship.custom_record__to__created_by_user: "created_by_user_id",
                    CustomRecordRelationship.custom_record__to__updated_by_user: "updated_by_user_id",
                    CustomRecordRelationship.custom_record__to__deleted_by_user: "deleted_by_user_id",
                }
                user_id_field: str = user_id_field_by_user_relationship[relationship_id]
                user_id_to_custom_object_ids: dict[UUID, set[UUID]] = {}
                for data in custom_object_datas:
                    if user_id := getattr(data, user_id_field, None):
                        user_id_to_custom_object_ids.setdefault(user_id, set()).add(
                            data.id
                        )
                logger.debug(
                    f"user_id_to_custom_object_ids: {user_id_to_custom_object_ids}"
                )
                # Fetch user records for all relevant user IDs
                user_records = await self._list_organization_user_records(
                    organization_id=organization_id,
                    only_include_user_ids=set(user_id_to_custom_object_ids.keys()),
                    fetch_context=fetch_context,
                )
                logger.debug(f"user_records: {user_records}")
                # Map user records back to their custom objects
                for user_record in user_records:
                    for custom_object_id in user_id_to_custom_object_ids[
                        user_record.data.id
                    ]:
                        related_objects_by_relationship_id_by_data_id[custom_object_id][
                            relationship_id
                        ].append(user_record)
                logger.debug(
                    f"related_objects_by_relationship_id_by_data_id: {related_objects_by_relationship_id_by_data_id}"
                )
                continue
            next_layer_field_references: list[FieldReference] = [
                fr.field for fr in refs if isinstance(fr.field, FieldReference)
            ]
            logger.debug(f"\nProcessing relationship_id: {relationship_id}")
            logger.debug(f"Field references: {refs}")

            association = await self.association_service.get_association_by_id(
                organization_id=organization_id,
                association_id=UUID(str(relationship_id)),
            )
            logger.debug(f"Association: {association}")

            # Determine if current object is source or target of the association
            current_object_identifier = CustomObjectIdentifier(
                object_kind=ObjectKind.CUSTOM,
                organization_id=organization_id,
                object_id=custom_object_dto.custom_object.id,
            )

            is_target = (
                association.source_object_identifier == current_object_identifier
            )
            related_object_identifier = (
                association.target_object_identifier
                if is_target
                else association.source_object_identifier
            )

            if related_object_identifier.object_kind == ObjectKind.CUSTOM:
                logger.debug("Related object is custom")
                # Get related custom object with its fields
                related_custom_object = (
                    await self.custom_object_query_service.get_custom_object(
                        organization_id=organization_id,
                        custom_object_id_or_parent_object_name=UUID(
                            str(related_object_identifier.object_id)
                        ),
                    )
                )
                logger.debug(
                    f"Related custom object fields: {related_custom_object.custom_fields}"
                )

                related_cust_records = await self.fetch_related_custom_records(
                    organization_id=organization_id,
                    association=association,
                    source_data_ids=[data.id for data in custom_object_datas],
                    field_references=next_layer_field_references,
                    fetch_context=fetch_context,
                    is_target=is_target,
                )
                logger.debug(f"Related records: {related_cust_records}")

                # Map related records and include target object fields
                for source_id, related in related_cust_records.items():
                    related_objects_by_relationship_id_by_data_id[source_id][
                        str(relationship_id)
                    ].extend(related)
                    logger.debug(f"Mapped records for source_id {source_id}: {related}")

                # Add target object fields to result DTO if not already present
                for field in related_custom_object.custom_fields:
                    if field not in custom_object_dto.custom_fields:
                        custom_object_dto.custom_fields.append(field)
                logger.debug(
                    f"Updated custom_object_dto fields: {custom_object_dto.custom_fields}"
                )

            # Handle standard object relationships
            elif related_object_identifier.object_kind == ObjectKind.STANDARD:
                logger.debug("Processing standard object relationship")
                source_ids = [data.id for data in custom_object_datas]
                # Get associations between source and target
                (
                    await self.association_service.list_associations_by_source_ids(
                        organization_id=organization_id,
                        association_id=association.id,
                        source_ids=source_ids,
                    )
                )
                related_standard_records: dict[
                    UUID, list[ModeledObjectRecord]
                ] = await self.fetch_related_standard_records(
                    organization_id=organization_id,
                    association=association,
                    source_data_ids=source_ids,
                    field_references=next_layer_field_references,
                    is_target=is_target,
                    fetch_context=fetch_context,
                )
                # Map related records to their source records
                for source_id, related in related_standard_records.items():
                    related_objects_by_relationship_id_by_data_id[source_id][
                        str(relationship_id)
                    ].extend(related)
                    logger.debug(
                        f"Mapped standard records for source_id {source_id}: {related}"
                    )

        # Create records with their related data
        records: list[CustomRecord] = []
        original_by_ids: dict[tuple[UUID, UUID], CustomObjectData] = {}

        for data in custom_object_datas:
            logger.debug(f"\nProcessing data record: {data.id}")
            data_dict = create_custom_object_record_data(
                data=data,
                custom_fields=custom_object_dto.custom_fields,
            )
            logger.debug(f"Created data_dict: {data_dict}")

            # Debug related records before mapping
            logger.debug(f"Related records by relationship for data {data.id}:")
            for rel_id, rel_records in related_objects_by_relationship_id_by_data_id[
                data.id
            ].items():
                logger.debug(f"  Relationship {rel_id}: {len(rel_records)} records")
                for rec in rel_records:
                    logger.debug(f"    Record data: {rec.data}")

            related_records = {
                rel_id: cast(list[ModeledObjectRecord], list(records))
                for rel_id, records in related_objects_by_relationship_id_by_data_id[
                    data.id
                ].items()
            }
            logger.debug(f"Mapped related_records: {related_records}")
            logger.debug(
                f"Requested relationships: {field_references_by_relationship_id.keys()}"
            )

            record = CustomRecord(
                object_id=CustomObjectIdentifier(
                    object_kind=ObjectKind.CUSTOM,
                    organization_id=organization_id,
                    object_id=custom_object_dto.custom_object_id,
                ),
                data=CustomRecordData(
                    id=data.id,
                    custom_field_data=data_dict,
                    display_name=data.display_name_computed,
                    created_at=data.created_at,
                    updated_at=data.updated_at,
                    deleted_at=data.deleted_at,
                    created_by_user_id=data.created_by_user_id,
                    updated_by_user_id=data.updated_by_user_id,
                    deleted_by_user_id=data.deleted_by_user_id,
                ),
                related_records=cast(
                    Mapping[RelationshipId, tuple[ModeledObjectRecord, ...]],
                    related_records,
                ),
                requested_relationships=set(field_references_by_relationship_id),
            )
            logger.debug(f"Created CustomRecord: {record}")
            logger.debug(f"CustomRecord related_records: {record.related_records}")

            records.append(record)
            original_by_ids[(custom_object_dto.custom_object_id, data.id)] = data

        result: Sequence[CustomRecord] = records
        if filter_spec:
            logger.debug(f"Pre-filter records: {[rec.data.id for rec in result]}")
            logger.debug(
                f"Pre-filter data: {[(rec.data.id, rec.data, rec.related_records) for rec in result]}"
            )

            result = await filter_record_sequence(
                filter_spec=filter_spec,
                records=result,
            )

            logger.debug(f"Post-filter records: {[rec.data.id for rec in result]}")
            logger.debug(
                f"Post-filter data: {[(rec.data.id, rec.data, rec.related_records) for rec in result]}"
            )

        if sorting_spec:
            result = await sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return list(result)

    async def fetch_related_standard_records(
        self,
        organization_id: UUID,
        association: CustomObjectAssociation,
        source_data_ids: list[UUID],
        field_references: list[FieldReference],
        is_target: bool,
        fetch_context: FetchContext,
    ) -> dict[UUID, list[ModeledObjectRecord]]:
        # Use target or source based on direction
        associations = await self.association_service.get_records(association)
        related_object_identifier = cast(
            StandardObjectIdentifier,
            (
                association.target_object_identifier
                if is_target
                else association.source_object_identifier
            ),
        )
        # Group target IDs by source ID
        related_ids_by_source_id: defaultdict[UUID, list[UUID]] = defaultdict(list)
        for assoc in associations:
            source_id = assoc.source_record_id if is_target else assoc.target_record_id
            related_id = assoc.target_record_id if is_target else assoc.source_record_id
            related_ids_by_source_id[source_id].append(related_id)

        all_related_ids = {
            rid for ids in related_ids_by_source_id.values() for rid in ids
        }
        if not all_related_ids:
            return {}

        target_records: Sequence[StandardRecord[Any]]  # type: ignore[explicit-any] # TODO: fix-any-annotation
        # Use appropriate query method based on related object type
        match related_object_identifier.object_name:
            case StdObjectIdentifiers.account:
                target_records = await self._list_account_records(
                    organization_id=organization_id,
                    only_include_account_ids=all_related_ids,
                    field_references=field_references,
                    fetch_context=fetch_context,
                    include_custom_object=True,
                )
            case StdObjectIdentifiers.contact:
                target_records = await self._list_contact_records(
                    organization_id=organization_id,
                    only_include_contact_ids=all_related_ids,
                    field_references=field_references,
                    fetch_context=fetch_context,
                    include_custom_object=True,
                )
            case StdObjectIdentifiers.pipeline:
                target_records = await self._list_pipeline_records(
                    organization_id=organization_id,
                    only_include_pipeline_ids=all_related_ids,
                    field_references=field_references,
                    fetch_context=fetch_context,
                    include_custom_object=True,
                )
            case StdObjectIdentifiers.user:
                target_records = await self._list_organization_user_records(
                    organization_id=organization_id,
                    only_include_user_ids=all_related_ids,
                    fetch_context=fetch_context,
                )
            case _:
                raise NotImplementedError(
                    f"Unsupported related object type: {related_object_identifier.object_name}"
                )

        # Map target records back to source IDs
        result: defaultdict[UUID, list[ModeledObjectRecord]] = defaultdict(list)
        target_records_by_id = {record.data.id: record for record in target_records}

        for source_id, related_ids in related_ids_by_source_id.items():
            for related_id in related_ids:
                if related_record := target_records_by_id.get(related_id):
                    result[source_id].append(related_record)

        return dict(result)

    async def fetch_related_custom_records(
        self,
        organization_id: UUID,
        association: CustomObjectAssociation,
        source_data_ids: list[UUID],
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        is_target: bool,
    ) -> dict[UUID, list[ModeledObjectRecord]]:
        """Fetch related custom records while preventing infinite loops."""
        visited_relationships = fetch_context.visited_cust_relationship_ids

        logger.debug(f"Fetching related records for association {association.id}")
        logger.debug(f"Source data IDs: {source_data_ids}")
        logger.debug(f"Field references: {field_references}")
        logger.debug(f"Visited relationships: {visited_relationships}")
        logger.debug(f"Is source direction: {is_target}")

        if association.id in visited_relationships:
            logger.debug(
                f"Already visited relationship {association.id}, returning empty"
            )
            return {}

        visited_relationships.add(association.id)

        # Get the target object identifier based on direction
        target_identifier = (
            association.target_object_identifier
            if is_target
            else association.source_object_identifier
        )

        target_id = get_identifier_id(target_identifier)
        if target_id is None:
            raise ValueError(
                f"Expected CustomObjectIdentifier but got: {target_identifier}"
            )

        target_custom_object = await self.custom_object_query_service.get_custom_object(
            organization_id=organization_id,
            custom_object_id_or_parent_object_name=target_id,
        )
        logger.debug(f"Target custom object: {target_custom_object.custom_object_id}")

        # Get associations
        association_records = await self.association_service.get_records(association)
        logger.debug(f"Found {len(association_records)} associations")
        for assoc in association_records:
            logger.debug(
                f"Association: source={assoc.source_record_id}, target={assoc.target_record_id}"
            )

        target_ids_by_source_id: defaultdict[UUID, list[UUID]] = defaultdict(list)
        for assoc in association_records:
            if is_target:
                # When traversing in source direction, swap source/target
                if assoc.source_record_id in source_data_ids:
                    target_ids_by_source_id[assoc.source_record_id].append(
                        assoc.target_record_id
                    )
            elif assoc.target_record_id in source_data_ids:
                target_ids_by_source_id[assoc.target_record_id].append(
                    assoc.source_record_id
                )

        logger.debug(f"Target IDs by source ID: {dict(target_ids_by_source_id)}")

        all_target_ids = {
            tid for ids in target_ids_by_source_id.values() for tid in ids
        }
        if not all_target_ids:
            logger.debug("No target IDs found, returning empty")
            return {}

        logger.debug(f"Fetching records for target IDs: {all_target_ids}")
        target_records = await self._list_custom_object_data(
            organization_id=organization_id,
            custom_object_id_or_parent_object_name=target_custom_object.custom_object_id,
            fetch_conditions=field_references,
            fetch_context=fetch_context,
        )
        logger.debug(
            f"Found {len(target_records) if target_records else 0} target records"
        )

        result = create_record_mapping(
            target_ids_by_source_id=target_ids_by_source_id,
            target_records=target_records,
        )
        logger.debug(f"Final result mapping: {result}")
        return cast(dict[UUID, list[ModeledObjectRecord]], result)

    async def list_email_attachment_details(
        self,
        organization_id: UUID,
        attachment_ids: list[UUID],
    ) -> Sequence[StandardRecord[AttachmentDetailsV2]]:
        if isinstance(attachment_ids, ObjectRecordFetchConditions):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=attachment_ids,
                entity_type=AttachmentDetailsV2.object_id.object_name,
                object_identifier=AttachmentDetailsV2.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        attachment_details = (
            await self.email_attachment_query_service.find_attachment_by_ids(
                organization_id=organization_id, attachment_ids=attachment_ids
            )
        )
        result: list[StandardRecord[AttachmentDetailsV2]] = []
        for attachment_detail in attachment_details:
            result.append(
                StandardRecord(
                    object_id=AttachmentDetailsV2.object_id,
                    requested_relationships=set(),
                    related_records={},
                    data=attachment_detail,
                )
            )
        return result

    async def list_email_account_pool_records(
        self,
        user_auth_context: UserAuthContext,
        include_custom_object: bool = False,
        only_include_email_account_pool_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[EmailAccountPoolV2]]:
        # Early return
        if (
            specified(only_include_email_account_pool_ids)
            and not only_include_email_account_pool_ids
        ):
            return []

        # This domain model is not used and hence is not in schema and cannot be compiled to cypher.
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=user_auth_context.organization_id,
            object_identifier=EmailAccountPoolV2.object_id,
            fetch_conditions=fetch_conditions,
        )
        result = await self._list_email_account_pool_records(
            user_auth_context=user_auth_context,
            only_include_email_account_pool_ids=only_include_email_account_pool_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=user_auth_context.organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def _list_email_account_pool_records(
        self,
        user_auth_context: UserAuthContext,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_email_account_pool_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> Sequence[StandardRecord[EmailAccountPoolV2]]:
        # Early return
        if (
            specified(only_include_email_account_pool_ids)
            and not only_include_email_account_pool_ids
        ):
            return []

        cached_email_account_pools: dict[UUID, EmailAccountPoolV2] = (
            fetch_context.email_account_pool_by_id
        )
        email_account_pool_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_email_account_pool_ids - set(cached_email_account_pools.keys())
            if specified(only_include_email_account_pool_ids)
            else UNSET
        )
        fetched_email_account_pools = (
            await self.email_account_pool_query_service.list_email_account_pools_v2(
                user_auth_context=user_auth_context,
                only_include_email_account_pool_ids=email_account_pool_ids_to_fetch,
            )
        )
        fetched_email_account_pool_by_id = {
            email_account_pool.id: email_account_pool
            for email_account_pool in fetched_email_account_pools
        }
        cached_email_account_pools.update(fetched_email_account_pool_by_id)

        email_account_pools_by_id: dict[UUID, EmailAccountPoolV2] = (
            {
                email_account_pool_id: cached_email_account_pools[email_account_pool_id]
                for email_account_pool_id in only_include_email_account_pool_ids
                if email_account_pool_id in cached_email_account_pools
            }
            if specified(only_include_email_account_pool_ids)
            else fetched_email_account_pool_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_email_account_pool_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for _relationship_id in field_references_by_relationship_id:
            if _relationship_id in EmailAccountPoolRelationship:
                _email_account_pool_relationship_id = EmailAccountPoolRelationship(
                    str(_relationship_id)
                )
                match _email_account_pool_relationship_id:
                    case (
                        EmailAccountPoolRelationship.email_account_pool__to__created_by_user
                        | EmailAccountPoolRelationship.email_account_pool__to__updated_by_user
                        | EmailAccountPoolRelationship.email_account_pool__to__deleted_by_user
                    ):
                        user_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in EmailAccountPoolV2.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        user_id_to_email_account_pool_ids: dict[UUID, set[UUID]] = {}
                        for email_account_pool in email_account_pools_by_id.values():
                            if user_id := getattr(
                                email_account_pool, user_id_field, None
                            ):
                                user_id_to_email_account_pool_ids.setdefault(
                                    user_id, set()
                                ).add(email_account_pool.id)
                        user_records = await self._list_organization_user_records(
                            organization_id=user_auth_context.organization_id,
                            only_include_user_ids=set(
                                user_id_to_email_account_pool_ids.keys()
                            ),
                            fetch_context=fetch_context,
                        )
                        for user_record in user_records:
                            for (
                                _email_account_pool_id
                            ) in user_id_to_email_account_pool_ids[user_record.data.id]:
                                related_objects_by_relationship_id_by_email_account_pool_id[
                                    _email_account_pool_id
                                ][_relationship_id].append(user_record)
                    case _ as unreachable:
                        assert_never(unreachable)
            else:
                raise NotImplementedError(
                    f"Unsupported custom object relationship: {_relationship_id}"
                )
        result: list[StandardRecord[EmailAccountPoolV2]] = []
        for (
            email_account_pool_id,
            email_account_pool,
        ) in email_account_pools_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_email_account_pool_id[
                    email_account_pool_id
                ].items()
            }
            result.append(
                StandardRecord[EmailAccountPoolV2](
                    object_id=EmailAccountPoolV2.object_id,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=email_account_pool,
                )
            )
        return result

    async def list_sequence_records(
        self,
        user_id: UUID,
        organization_id: UUID,
        include_custom_object: bool,
        only_include_sequence_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        user_auth_context: UserAuthContext | None = None,
    ) -> Sequence[StandardRecord[SequenceV2]]:
        # Early return
        if specified(only_include_sequence_ids) and not only_include_sequence_ids:
            return []

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=SequenceV2.object_id.object_name,
                object_identifier=SequenceV2.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=SequenceV2.object_id,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_sequence_records(
            user_id=user_id,
            organization_id=organization_id,
            only_include_sequence_ids=only_include_sequence_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
            user_auth_context=user_auth_context,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def _list_sequence_records(  # noqa: C901, PLR0912
        self,
        user_id: UUID,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_sequence_ids: UnsetAware[set[UUID]] = UNSET,
        user_auth_context: UserAuthContext | None = None,
    ) -> Sequence[StandardRecord[SequenceV2]]:
        # Early return
        if specified(only_include_sequence_ids) and not only_include_sequence_ids:
            return []

        # Get cached sequences
        cached_sequences: dict[UUID, SequenceV2] = fetch_context.sequence_by_id
        sequence_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_sequence_ids - set(cached_sequences.keys())
            if specified(only_include_sequence_ids)
            else UNSET
        )

        # Fetch sequences from service
        fetched_sequences = await self.sequence_query_service.list_sequences(
            organization_id=organization_id,
            only_include_sequence_ids=sequence_ids_to_fetch,
        )

        # Apply permissions filtering if enabled
        if user_auth_context:
            fetched_sequences = (
                await self.sequence_query_service.filter_viewable_records(
                    records=fetched_sequences,
                    user_auth_context=user_auth_context,
                )
            )

        # Update cache with fetched sequences
        fetched_sequence_by_id = {
            sequence.id: sequence for sequence in fetched_sequences
        }
        cached_sequences.update(fetched_sequence_by_id)

        # Determine which sequences to include in the result
        sequence_by_id: dict[UUID, SequenceV2] = (
            {
                sequence_id: cached_sequences[sequence_id]
                for sequence_id in only_include_sequence_ids
                if sequence_id in cached_sequences
            }
            if specified(only_include_sequence_ids)
            else fetched_sequence_by_id
        )

        # Group field references by relationship ID
        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        # Initialize data structure for related objects
        related_objects_by_relationship_id_by_sequence_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        # Process relationships
        for _relationship_id in field_references_by_relationship_id:
            if _relationship_id in SequenceRelationship:
                _sequence_relationship_id = SequenceRelationship(str(_relationship_id))

                # Process different relationship types
                match _sequence_relationship_id:
                    case (
                        SequenceRelationship.sequence__to__created_by_user
                        | SequenceRelationship.sequence__to__owner_user
                    ):
                        # Process user relationships
                        user_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in SequenceV2.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        user_id_to_sequence_ids: dict[UUID, set[UUID]] = {}
                        for sequence in sequence_by_id.values():
                            user_id_value = getattr(sequence, user_id_field, None)
                            if isinstance(user_id_value, UUID):
                                user_id_to_sequence_ids.setdefault(
                                    user_id_value, set()
                                ).add(sequence.id)
                        user_records = await self._list_organization_user_records(
                            organization_id=organization_id,
                            only_include_user_ids=set(user_id_to_sequence_ids.keys()),
                            fetch_context=fetch_context,
                        )
                        for user_record in user_records:
                            for _sequence_id in user_id_to_sequence_ids[
                                user_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_sequence_id[
                                    _sequence_id
                                ][_relationship_id].append(user_record)
                    case SequenceRelationship.sequence__to__owner_user:
                        # Process owner user relationships
                        owner_user_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in SequenceV2.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        owner_user_id_to_sequence_ids: dict[UUID, set[UUID]] = {}
                        for sequence in sequence_by_id.values():
                            if owner_user_id := getattr(
                                sequence, owner_user_id_field, None
                            ):
                                owner_user_id_to_sequence_ids.setdefault(
                                    owner_user_id, set()
                                ).add(sequence.id)
                        owner_user_records = await self._list_organization_user_records(
                            organization_id=organization_id,
                            only_include_user_ids=set(
                                owner_user_id_to_sequence_ids.keys()
                            ),
                            fetch_context=fetch_context,
                        )
                        for owner_user_record in owner_user_records:
                            for _sequence_id in owner_user_id_to_sequence_ids[
                                owner_user_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_sequence_id[
                                    _sequence_id
                                ][_relationship_id].append(owner_user_record)

                    case _:
                        logger.warning(
                            f"Unhandled sequence relationship: {_sequence_relationship_id}"
                        )
            else:
                # Handle custom relationships
                logger.warning(
                    f"Custom relationship not implemented: {_relationship_id}"
                )

        # Build result records
        result: list[StandardRecord[SequenceV2]] = []
        for sequence_id, sequence in sequence_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_sequence_id[
                    sequence_id
                ].items()
            }
            result.append(
                StandardRecord[SequenceV2](
                    object_id=SequenceV2.object_id,
                    is_editable=await self.sequence_query_service.is_editable(
                        user_auth_context=user_auth_context,
                        domain_object=sequence,
                    ),
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=sequence,
                )
            )
        return result

    async def list_sequence_step_execution_records(
        self,
        user_id: UUID,
        organization_id: UUID,
        sequence_id: UUID,
        include_custom_object: bool,
        only_include_sequence_step_execution_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        user_auth_context: UserAuthContext | None = None,
        cursor: Cursor | None = None,
        step_ids: list[UUID] | None = None,
        messages_statuses_include: list[MessageStatus] | None = None,
        messages_statuses_exclude: list[MessageStatus] | None = None,
        email_event_type: EmailEventType | None = None,
        require_global_message_id: bool = False,
    ) -> tuple[Sequence[StandardRecord[SequenceStepExecution]], Cursor | None]:
        # Early return
        if (
            specified(only_include_sequence_step_execution_ids)
            and not only_include_sequence_step_execution_ids
        ):
            return ([], Cursor(total_number=0))

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=SequenceStepExecution.object_id.object_name,
                object_identifier=SequenceStepExecution.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=SequenceStepExecution.object_id,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_sequence_step_execution_records(
            user_id=user_id,
            organization_id=organization_id,
            sequence_id=sequence_id,
            only_include_sequence_step_execution_ids=only_include_sequence_step_execution_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            user_auth_context=user_auth_context,
            cursor=cursor,
            include_custom_object=include_custom_object,
            filter_spec=filter_spec,
            sorting_spec=sorting_spec,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        new_cursor = None
        return result, new_cursor

    async def list_sequence_step_execution_records_v2(
        self,
        user_id: UUID,
        organization_id: UUID,
        sequence_id: UUID,
        include_custom_object: bool,
        only_include_sequence_step_execution_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        user_auth_context: UserAuthContext | None = None,
        cursor: Cursor | None = None,
        step_ids: list[UUID] | None = None,
        messages_statuses_include: list[MessageStatus] | None = None,
        messages_statuses_exclude: list[MessageStatus] | None = None,
        email_event_types_include: list[EmailEventType] | None = None,
        require_global_message_id: bool = False,
    ) -> tuple[Sequence[StandardRecord[SequenceStepExecution]], Cursor | None]:
        # Early return
        if (
            specified(only_include_sequence_step_execution_ids)
            and not only_include_sequence_step_execution_ids
        ):
            return ([], Cursor(total_number=0))

        # if (
        #     isinstance(fetch_conditions, ObjectRecordFetchConditions)
        #     and FalkorLib.should_run_falkor_bg()
        # ):
        #     await FalkorLib.try_compile_query(
        #         organization_id=organization_id,
        #         fetch_conditions=fetch_conditions,
        #         entity_type=SequenceStepExecution.object_id.object_name,
        #         object_identifier=SequenceStepExecution.object_id,
        #         metadata_service=self.metadata_service,
        #         select_list_service=self.select_list_service,
        #         falkordb_conn_mgr=self.falkordb_conn_mgr,
        #     )

        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=SequenceStepExecution.object_id,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_sequence_step_execution_records_v2(
            user_id=user_id,
            organization_id=organization_id,
            sequence_id=sequence_id,
            only_include_sequence_step_execution_ids=only_include_sequence_step_execution_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            user_auth_context=user_auth_context,
            cursor=cursor,
            step_ids=step_ids,
            messages_statuses_include=messages_statuses_include,
            messages_statuses_exclude=messages_statuses_exclude,
            email_event_types_include=email_event_types_include,
            require_global_message_id=require_global_message_id,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        new_cursor = None
        return result, new_cursor

    async def _list_sequence_step_execution_records_v2(  # noqa: C901, PLR0912
        self,
        user_id: UUID,
        organization_id: UUID,
        sequence_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        step_ids: list[UUID] | None = None,
        messages_statuses_include: list[MessageStatus] | None = None,
        messages_statuses_exclude: list[MessageStatus] | None = None,
        email_event_types_include: list[EmailEventType] | None = None,
        require_global_message_id: bool = False,
        only_include_sequence_step_execution_ids: UnsetAware[set[UUID]] = UNSET,
        user_auth_context: UserAuthContext | None = None,
        cursor: Cursor | None = None,
    ) -> Sequence[StandardRecord[SequenceStepExecution]]:
        # Early return
        if (
            specified(only_include_sequence_step_execution_ids)
            and not only_include_sequence_step_execution_ids
        ):
            return []

        offset, limit = 0, 100
        if cursor:
            offset, limit = cursor_to_offset_limit_with_overflow(
                cursor=cursor,
            )

        # Get cached sequences
        cached_sse: dict[UUID, SequenceStepExecution] = (
            fetch_context.sequence_step_execution_by_id
        )
        sequence_step_execution_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_sequence_step_execution_ids - set(cached_sse.keys())
            if specified(only_include_sequence_step_execution_ids)
            else UNSET
        )

        fetched_sse = await self.sequence_step_execution_query_service.list_sequence_step_executions(
            organization_id=organization_id,
            sequence_id=sequence_id,
            only_include_sequence_step_execution_ids=sequence_step_execution_ids_to_fetch,
        )

        # Update cache with fetched sequences
        fetched_sse_by_id = {sse.id: sse for sse in fetched_sse}
        cached_sse.update(fetched_sse_by_id)

        # Apply permissions filtering if enabled
        filtered_sse = list(cached_sse.values())
        if user_auth_context:
            filtered_sse = await self.sequence_step_execution_query_service.filter_viewable_records(
                records=list(cached_sse.values()),
                user_auth_context=user_auth_context,
            )

        filtered_sse_by_id = {sse.id: sse for sse in filtered_sse}

        # Determine which sequences to include in the result
        sequence_step_execution_by_id: dict[UUID, SequenceStepExecution] = (
            {
                sse_id: cached_sse[sse_id]
                for sse_id in only_include_sequence_step_execution_ids
                if sse_id in filtered_sse_by_id
            }
            if specified(only_include_sequence_step_execution_ids)
            else filtered_sse_by_id
        )

        global_message_ids_to_hydrate = await self.sequence_step_execution_query_service.get_global_message_ids_by_sequence_step_execution_id_and_email_event_type(
            organization_id=organization_id,
            sequence_step_execution_ids=list(sequence_step_execution_by_id.keys()),
            step_ids=step_ids,
            messages_statuses_include=messages_statuses_include,
            messages_statuses_exclude=messages_statuses_exclude,
            email_event_types_include=email_event_types_include,
        )

        logger.info(f"global_message_ids_to_hydrate: {global_message_ids_to_hydrate}")

        # Group field references by relationship ID
        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        # Initialize data structure for related objects
        related_objects_by_relationship_id_by_sequence_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        # Process relationships
        for _relationship_id in field_references_by_relationship_id:
            if _relationship_id in SequenceStepExecutionRelationship:
                _sequence_step_execution_relationship_id = (
                    SequenceStepExecutionRelationship(str(_relationship_id))
                )

                # Process different relationship types
                match _sequence_step_execution_relationship_id:
                    case SequenceStepExecutionRelationship.sequence_step_execution__to__global_message:
                        # Process global message relationships
                        global_message_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in SequenceStepExecution.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        global_message_id_to_sequence_step_execution_ids: dict[
                            UUID, set[UUID]
                        ] = {}
                        for (
                            sequence_step_execution
                        ) in sequence_step_execution_by_id.values():
                            global_message_id_value = getattr(
                                sequence_step_execution, global_message_id_field, None
                            )
                            if isinstance(global_message_id_value, UUID):
                                global_message_id_to_sequence_step_execution_ids.setdefault(
                                    global_message_id_value, set()
                                ).add(sequence_step_execution.id)
                        global_message_records = (
                            await self.list_organization_global_message_records(
                                user_id=user_id,
                                organization_id=organization_id,
                                only_include_global_message_ids=set(
                                    global_message_ids_to_hydrate
                                ),
                                fetch_context=fetch_context,
                            )
                        )

                        for global_message_record in global_message_records:
                            for (
                                _sequence_step_execution_id
                            ) in global_message_id_to_sequence_step_execution_ids[
                                global_message_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_sequence_id[
                                    _sequence_step_execution_id
                                ][_relationship_id].append(global_message_record)
                    case _:
                        logger.warning(
                            f"Unhandled sequence relationship: {_sequence_step_execution_relationship_id}"
                        )
            else:
                # Handle custom relationships
                logger.warning(
                    f"Custom relationship not implemented: {_relationship_id}"
                )

        # Build result records
        result: list[StandardRecord[SequenceStepExecution]] = []
        for sse_id, sse in sequence_step_execution_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_sequence_id[
                    sse_id
                ].items()
            }
            result.append(
                StandardRecord[SequenceStepExecution](
                    object_id=SequenceStepExecution.object_id,
                    is_editable=await self.sequence_step_execution_query_service.is_editable(
                        user_auth_context=user_auth_context,
                        domain_object=sse,
                    ),
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=sse,
                )
            )
        return result

    async def _list_sequence_step_execution_records(  # noqa: C901, PLR0912
        self,
        user_id: UUID,
        organization_id: UUID,
        sequence_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_sequence_step_execution_ids: UnsetAware[set[UUID]] = UNSET,
        user_auth_context: UserAuthContext | None = None,
        filter_spec: FilterSpec | None = None,
        sorting_spec: SortingSpec | None = None,
        cursor: Cursor | None = None,
    ) -> Sequence[StandardRecord[SequenceStepExecution]]:
        # Early return
        if (
            specified(only_include_sequence_step_execution_ids)
            and not only_include_sequence_step_execution_ids
        ):
            return []

        offset, limit = 0, 100
        if cursor:
            offset, limit = cursor_to_offset_limit_with_overflow(
                cursor=cursor,
            )

        # Get cached sequences
        cached_sse: dict[UUID, SequenceStepExecution] = (
            fetch_context.sequence_step_execution_by_id
        )
        sequence_step_execution_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_sequence_step_execution_ids - set(cached_sse.keys())
            if specified(only_include_sequence_step_execution_ids)
            else UNSET
        )

        fetched_sse = await self.sequence_step_execution_query_service.list_sequence_step_executions(
            organization_id=organization_id,
            sequence_id=sequence_id,
            only_include_sequence_step_execution_ids=sequence_step_execution_ids_to_fetch,
        )

        # Update cache with fetched sequences
        fetched_sse_by_id = {sse.id: sse for sse in fetched_sse}
        cached_sse.update(fetched_sse_by_id)

        # Apply permissions filtering if enabled
        filtered_sse = list(cached_sse.values())
        if user_auth_context:
            filtered_sse = await self.sequence_step_execution_query_service.filter_viewable_records(
                records=list(cached_sse.values()),
                user_auth_context=user_auth_context,
            )

        filtered_sse_by_id = {sse.id: sse for sse in filtered_sse}

        # Determine which sequences to include in the result
        sequence_step_execution_by_id: dict[UUID, SequenceStepExecution] = (
            {
                sse_id: cached_sse[sse_id]
                for sse_id in only_include_sequence_step_execution_ids
                if sse_id in filtered_sse_by_id
            }
            if specified(only_include_sequence_step_execution_ids)
            else filtered_sse_by_id
        )

        # Group field references by relationship ID
        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        # Initialize data structure for related objects
        related_objects_by_relationship_id_by_sequence_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        # Process relationships
        for _relationship_id in field_references_by_relationship_id:
            if _relationship_id in SequenceStepExecutionRelationship:
                _sequence_step_execution_relationship_id = (
                    SequenceStepExecutionRelationship(str(_relationship_id))
                )

                # Process different relationship types
                match _sequence_step_execution_relationship_id:
                    case SequenceStepExecutionRelationship.sequence_step_execution__to__global_message:
                        # Process global message relationships
                        global_message_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in SequenceStepExecution.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        global_message_id_to_sequence_step_execution_ids: dict[
                            UUID, set[UUID]
                        ] = {}
                        for (
                            sequence_step_execution
                        ) in sequence_step_execution_by_id.values():
                            global_message_id_value = getattr(
                                sequence_step_execution, global_message_id_field, None
                            )
                            if isinstance(global_message_id_value, UUID):
                                global_message_id_to_sequence_step_execution_ids.setdefault(
                                    global_message_id_value, set()
                                ).add(sequence_step_execution.id)
                        global_message_records = await self.list_organization_global_message_records(
                            user_id=user_id,
                            organization_id=organization_id,
                            only_include_global_message_ids=set(
                                global_message_id_to_sequence_step_execution_ids.keys()
                            ),
                            fetch_context=fetch_context,
                        )
                        for global_message_record in global_message_records:
                            for (
                                _sequence_step_execution_id
                            ) in global_message_id_to_sequence_step_execution_ids[
                                global_message_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_sequence_id[
                                    _sequence_step_execution_id
                                ][_relationship_id].append(global_message_record)
                    case _:
                        logger.warning(
                            f"Unhandled sequence relationship: {_sequence_step_execution_relationship_id}"
                        )
            else:
                # Handle custom relationships
                logger.warning(
                    f"Custom relationship not implemented: {_relationship_id}"
                )

        # Build result records
        result: list[StandardRecord[SequenceStepExecution]] = []
        for sse_id, sse in sequence_step_execution_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_sequence_id[
                    sse_id
                ].items()
            }
            result.append(
                StandardRecord[SequenceStepExecution](
                    object_id=SequenceStepExecution.object_id,
                    is_editable=await self.sequence_step_execution_query_service.is_editable(
                        user_auth_context=user_auth_context,
                        domain_object=sse,
                    ),
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=sse,
                )
            )
        return result

    def domain_fetch_hints_from_filter_and_sorting_spec(
        self,
        filter_spec: FilterSpec | None,
        sorting_spec: SortingSpec | None,
        limit: int | None,
        standard_object_identifier: StandardObjectIdentifier,
        only_include_object_ids: UnsetAware[set[UUID]] | None = None,
        offset: int | None = None,
    ) -> DomainFetchHints:
        """
        Creates a DomainFetchHints object from filter and sorting specifications.

        Args:
            filter_spec: The filter specification to convert
            sorting_spec: The sorting specification to convert
            standard_object_identifier: The standard object identifier for the domain object

        Returns:
            A DomainFetchHints object or None if no hints can be created
        """

        # Convert sorting_spec to non_relational_sorting_spec
        non_relational_sorting_spec = as_non_relational_sorting_spec_or_default(
            sorting_spec=sorting_spec
            or SortingSpec(
                primary_object_identifier=standard_object_identifier,
                ordered_sorters=(),
            ),
            fallback=NonRelationalSortingSpec(
                primary_object_identifier=standard_object_identifier,
                ordered_sorters=(
                    NonRelationalSorter(
                        field=QualifiedField(
                            path=("created_at",)
                        ),  # default to chronologically sorted
                        order=OrderEnum.DESC,
                    ),
                ),
            ),
        )

        # Extract baseline filters if filter_spec is provided
        baseline_filters = BaselineFilters(
            must_filters=[],
            must_not_filters=[],
            at_least_one_filters=[],
        )
        if filter_spec:
            baseline_filter_group = (
                baseline_filter_extractor.extract_baseline_filter_group(
                    filter_spec=filter_spec,
                )
            )
            baseline_filters = (
                baseline_filter_group.baseline_filters_by_object_identifier.get(
                    standard_object_identifier,
                    BaselineFilters(
                        must_filters=[],
                        must_not_filters=[],
                        at_least_one_filters=[],
                    ),
                )
            )

        # Add only_include_object_ids to baseline_filters if provided
        if specified(only_include_object_ids):
            baseline_filters.must_filters.append(
                ValueFilter(
                    field=QualifiedField(path=("id",)),
                    operator=MatchOperator.IN,
                    value=only_include_object_ids,
                )
            )

        return DomainFetchHints(
            non_relational_sorting_spec=non_relational_sorting_spec,
            baseline_filters=baseline_filters,
            limit=limit,
            exclude_invisible=True,
            exclude_locked_by_integrity_jobs=True,
            offset=offset,
        )

    async def list_meeting_records_simple(
        self,
        organization_id: UUID,
        user_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
        fetch_context: FetchContext | None = None,
        include_custom_object: bool = False,
        only_include_meeting_ids: set[UUID] | None = None,
        only_include_meeting_for_user: UUID | None = None,
    ) -> Sequence[StandardRecord[MeetingV2]]:
        """
        Simplified function that fetches meetings and their immediate relationships without traversing further.
        This function loads all standard meeting relationships (account, pipeline, contacts, users) automatically
        without requiring field references.

        Args:
            query_service: The query service instance
            organization_id: The organization ID
            user_id: Optional user ID filter
            user_auth_context: Optional user authentication context for permissions
            fetch_context: Context for fetching and caching objects
            include_custom_object: Whether to include custom object data
            only_include_meeting_ids: Optional set of meeting IDs to filter by
            only_include_meeting_for_user: Optional user ID to filter meetings by

        Returns:
            Sequence of MeetingV2 objects with their immediate relationships loaded
        """
        start_time = time.perf_counter()
        fetch_context = fetch_context or new_fetch_context()
        cached_meetings: dict[UUID, MeetingV2] = fetch_context.meeting_by_id
        meeting_ids_to_fetch: UnsetAware[set[UUID]] = UNSET

        if only_include_meeting_ids:
            meeting_ids_to_fetch = only_include_meeting_ids - set(
                cached_meetings.keys()
            )
            if not meeting_ids_to_fetch:
                # All meetings are already cached
                return [
                    StandardRecord[MeetingV2](
                        object_id=MeetingV2.object_id,
                        requested_relationships=set(),
                        related_records={},
                        data=cached_meetings[meeting_id],
                    )
                    for meeting_id in only_include_meeting_ids
                ]

        # Fetch the meetings
        fetched_meetings = await self.meeting_query_service.list_meeting_v2(
            organization_id=organization_id,
            user_id=user_id,
            only_include_meeting_ids=meeting_ids_to_fetch,
            include_custom_object=include_custom_object,
            only_include_meeting_for_user=(
                only_include_meeting_for_user
                if only_include_meeting_for_user
                else UNSET
            ),
        )

        # Apply permissions filtering if enabled
        if user_auth_context:
            fetched_meetings = await self.meeting_query_service.filter_viewable_records(
                records=fetched_meetings,
                user_auth_context=user_auth_context,
            )

        # Update cache and prepare final list
        fetched_meeting_by_id = {meeting.id: meeting for meeting in fetched_meetings}
        cached_meetings.update(fetched_meeting_by_id)

        meeting_by_id: dict[UUID, MeetingV2] = (
            {
                meeting_id: cached_meetings[meeting_id]
                for meeting_id in only_include_meeting_ids
                if meeting_id in cached_meetings
            }
            if only_include_meeting_ids
            else fetched_meeting_by_id
        )

        # Process all standard meeting relationships
        all_relationship_ids = [
            MeetingRelationship.meeting__to__organizer_user,
            MeetingRelationship.meeting__to__created_by_user,
            MeetingRelationship.meeting__to__invitee_user,
            MeetingRelationship.meeting__to__attendee_user,
            MeetingRelationship.meeting__to__invitee_contact,
            MeetingRelationship.meeting__to__attendee_contact,
            MeetingRelationship.meeting__to__account,
            MeetingRelationship.meeting__to__pipeline,
        ]

        # Container for related objects
        related_objects_by_relationship_id_by_meeting_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        # Process all relationships
        for relationship_id in all_relationship_ids:
            start_relation_time = time.perf_counter()

            # Process each type of relationship
            match relationship_id:
                case (
                    MeetingRelationship.meeting__to__attendee_user
                    | MeetingRelationship.meeting__to__organizer_user
                    | MeetingRelationship.meeting__to__invitee_user
                    | MeetingRelationship.meeting__to__created_by_user
                ):
                    await self._process_meeting_relationship_user(
                        organization_id=organization_id,
                        _relationship_id=relationship_id,
                        fetch_context=fetch_context,
                        meeting_by_id=meeting_by_id,
                        related_objects_by_relationship_id_by_meeting_id=related_objects_by_relationship_id_by_meeting_id,
                    )
                case (
                    MeetingRelationship.meeting__to__invitee_contact
                    | MeetingRelationship.meeting__to__attendee_contact
                ):
                    await self._process_meeting_relationship_contact(
                        organization_id=organization_id,
                        _relationship_id=relationship_id,
                        fetch_context=fetch_context,
                        meeting_by_id=meeting_by_id,
                        include_custom_object=include_custom_object,
                        next_layer_field_references=[],
                        # Empty list to prevent further traversal
                        related_objects_by_relationship_id_by_meeting_id=related_objects_by_relationship_id_by_meeting_id,
                    )
                case MeetingRelationship.meeting__to__account:
                    await self._process_meeting_relationship_account(
                        organization_id=organization_id,
                        _relationship_id=relationship_id,
                        fetch_context=fetch_context,
                        meeting_by_id=meeting_by_id,
                        include_custom_object=include_custom_object,
                        next_layer_field_references=[],
                        # Empty list to prevent further traversal
                        related_objects_by_relationship_id_by_meeting_id=related_objects_by_relationship_id_by_meeting_id,
                    )
                case MeetingRelationship.meeting__to__pipeline:
                    await self._process_meeting_relationship_pipeline(
                        organization_id=organization_id,
                        _relationship_id=relationship_id,
                        fetch_context=fetch_context,
                        meeting_by_id=meeting_by_id,
                        include_custom_object=include_custom_object,
                        next_layer_field_references=[],
                        # Empty list to prevent further traversal
                        related_objects_by_relationship_id_by_meeting_id=related_objects_by_relationship_id_by_meeting_id,
                    )

            elapsed_relation_time = (time.perf_counter() - start_relation_time) * 1000
            logger.bind(
                elapsed_time=elapsed_relation_time,
                relationship=relationship_id,
            ).info(f"Meeting relationship {relationship_id} fetch")

        elapsed_time = (time.perf_counter() - start_time) * 1000
        logger.bind(elapsed_time=elapsed_time).info(
            "Simple meeting records fetch completed"
        )

        result: list[StandardRecord[MeetingV2]] = []
        for meeting_id, meeting in meeting_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_meeting_id[
                    meeting_id
                ].items()
            }
            result.append(
                StandardRecord[MeetingV2](
                    object_id=MeetingV2.object_id,
                    is_editable=True,
                    requested_relationships=set(all_relationship_ids),
                    related_records=related_records,
                    data=meeting,
                )
            )
        return result

    async def list_organization_user_records_simple(
        self,
        organization_id: UUID,
        fetch_context: FetchContext | None = None,
        only_include_user_ids: set[UUID] | None = None,
    ) -> Sequence[StandardRecord[OrganizationUserV2]]:
        """
        Simplified function that fetches organization users and their immediate relationships without traversing further.
        This function loads all standard user relationships automatically without requiring field references.

        Args:
            organization_id: The organization ID
            fetch_context: Context for fetching and caching objects
            include_custom_object: Whether to include custom object data
            only_include_user_ids: Optional set of user IDs to filter by

        Returns:
            Sequence of OrganizationUserV2 objects with their immediate relationships loaded
        """
        time.perf_counter()
        fetch_context = fetch_context or new_fetch_context()
        user_ids_to_fetch: UnsetAware[set[UUID]] = only_include_user_ids or UNSET

        # Fetch the users
        return await self._list_organization_user_records(
            organization_id=organization_id,
            only_include_user_ids=user_ids_to_fetch,
            fetch_context=fetch_context,
        )

    async def _list_prospecting_run_records(
        self,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_prospecting_run_ids: UnsetAware[set[UUID]] = UNSET,
        user_auth_context: UserAuthContext | None = None,
    ) -> Sequence[StandardRecord[ProspectingRunV2]]:
        # Early return
        if (
            specified(only_include_prospecting_run_ids)
            and not only_include_prospecting_run_ids
        ):
            return []

        # Get cached prospecting_run
        cached_prospecting_runs: dict[UUID, ProspectingRunV2] = (
            fetch_context.prospecting_run_by_id
        )
        prospecting_run_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_prospecting_run_ids - set(cached_prospecting_runs.keys())
            if specified(only_include_prospecting_run_ids)
            else UNSET
        )

        # Fetch prospecting_run from service
        fetched_prospecting_runs = (
            await self.prospecting_run_query_service.list_prospecting_runs(
                organization_id=organization_id,
                only_include_prospecting_run_ids=prospecting_run_ids_to_fetch,
            )
        )

        fetched_prospecting_run_by_id = {
            prospecting_run.id: prospecting_run
            for prospecting_run in fetched_prospecting_runs
        }
        cached_prospecting_runs.update(fetched_prospecting_run_by_id)

        prospecting_run_by_id: dict[UUID, ProspectingRunV2] = (
            {
                prospecting_run_id: cached_prospecting_runs[prospecting_run_id]
                for prospecting_run_id in only_include_prospecting_run_ids
                if prospecting_run_id in cached_prospecting_runs
            }
            if specified(only_include_prospecting_run_ids)
            else fetched_prospecting_run_by_id
        )

        # Group field references by relationship ID
        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        # Initialize data structure for related objects
        related_objects_by_relationship_id_by_prospecting_run_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        # Process relationships
        for _relationship_id in field_references_by_relationship_id:
            if _relationship_id in ProspectingRunRelationship:
                _prospecting_run_relationship_id = ProspectingRunRelationship(
                    str(_relationship_id)
                )

                # Process different relationship types
                match _prospecting_run_relationship_id:
                    case ProspectingRunRelationship.prospecting_run__to__user:
                        # Process user relationships
                        user_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in ProspectingRunV2.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        user_id_to_prospecting_run_ids: dict[UUID, set[UUID]] = {}
                        for prospecting_run in prospecting_run_by_id.values():
                            user_id_value = getattr(
                                prospecting_run, user_id_field, None
                            )
                            if isinstance(user_id_value, UUID):
                                user_id_to_prospecting_run_ids.setdefault(
                                    user_id_value, set()
                                ).add(prospecting_run.id)
                        user_records = await self._list_organization_user_records(
                            organization_id=organization_id,
                            only_include_user_ids=set(
                                user_id_to_prospecting_run_ids.keys()
                            ),
                            fetch_context=fetch_context,
                        )
                        for user_record in user_records:
                            for _prospecting_run_id in user_id_to_prospecting_run_ids[
                                user_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_prospecting_run_id[
                                    _prospecting_run_id
                                ][_relationship_id].append(user_record)
                    case _:
                        logger.warning(
                            f"Unhandled prospecting run relationship: {_prospecting_run_relationship_id}"
                        )
            else:
                # Handle custom relationships
                logger.warning(
                    f"Custom relationship not implemented: {_relationship_id}"
                )

        # Build result records
        result: list[StandardRecord[ProspectingRunV2]] = []
        for (
            prospecting_run_id,
            prospecting_run,
        ) in prospecting_run_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_prospecting_run_id[
                    prospecting_run_id
                ].items()
            }
            result.append(
                StandardRecord[ProspectingRunV2](
                    object_id=ProspectingRunV2.object_id,
                    is_editable=await self.prospecting_run_query_service.is_editable(
                        user_auth_context=user_auth_context,
                        domain_object=prospecting_run,
                    ),
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=prospecting_run,
                )
            )
        return result

    async def _list_prospecting_run_result_records(  # noqa: C901 PLR0912
        self,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        prospecting_run_id: UUID,
        user_auth_context: UserAuthContext | None = None,
    ) -> Sequence[StandardRecord[RunResultV2]]:
        # Fetch prospecting_run from service
        fetched_prospecting_run_results = await self.prospecting_run_result_query_service.list_prospecting_run_results(
            organization_id=organization_id,
            prospecting_run_id=prospecting_run_id,
        )

        prospecting_run_result_by_id = {
            prospecting_run_result.id: prospecting_run_result
            for prospecting_run_result in fetched_prospecting_run_results
        }

        # Group field references by relationship ID
        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        # Initialize data structure for related objects
        related_objects_by_relationship_id_by_prospecting_run_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        # Process relationships
        for _relationship_id in field_references_by_relationship_id:
            if _relationship_id in ProspectingRunResultRelationship:
                _prospecting_run_result_relationship_id = (
                    ProspectingRunResultRelationship(str(_relationship_id))
                )

                # Process different relationship types
                match _prospecting_run_result_relationship_id:
                    case ProspectingRunResultRelationship.prospecting_run_result__to__contact:
                        # Process contact relationships
                        contact_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in RunResultV2.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        contact_id_to_prospecting_run_result_ids: dict[
                            UUID, set[UUID]
                        ] = {}
                        for (
                            prospecting_run_result
                        ) in prospecting_run_result_by_id.values():
                            contact_id_value = getattr(
                                prospecting_run_result, contact_id_field, None
                            )
                            if isinstance(contact_id_value, UUID):
                                contact_id_to_prospecting_run_result_ids.setdefault(
                                    contact_id_value, set()
                                ).add(prospecting_run_result.id)
                        contact_records = await self._list_contact_records(
                            organization_id=organization_id,
                            only_include_contact_ids=set(
                                contact_id_to_prospecting_run_result_ids.keys()
                            ),
                            field_references=[],
                            fetch_context=fetch_context,
                            include_custom_object=False,
                        )
                        for contact_record in contact_records:
                            for (
                                _prospecting_run_result_id
                            ) in contact_id_to_prospecting_run_result_ids[
                                contact_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_prospecting_run_id[
                                    _prospecting_run_result_id
                                ][_relationship_id].append(contact_record)
                    case ProspectingRunResultRelationship.prospecting_run_result__to__account:
                        # Process account relationships
                        account_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in RunResultV2.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        account_id_to_prospecting_run_result_ids: dict[
                            UUID, set[UUID]
                        ] = {}
                        for (
                            prospecting_run_result
                        ) in prospecting_run_result_by_id.values():
                            account_id_value = getattr(
                                prospecting_run_result, account_id_field, None
                            )
                            if isinstance(account_id_value, UUID):
                                account_id_to_prospecting_run_result_ids.setdefault(
                                    account_id_value, set()
                                ).add(prospecting_run_result.id)
                        account_records = await self._list_account_records(
                            organization_id=organization_id,
                            only_include_account_ids=set(
                                account_id_to_prospecting_run_result_ids.keys()
                            ),
                            field_references=[],
                            fetch_context=fetch_context,
                            include_custom_object=False,
                        )
                        for account_record in account_records:
                            for (
                                _prospecting_run_result_id
                            ) in account_id_to_prospecting_run_result_ids[
                                account_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_prospecting_run_id[
                                    _prospecting_run_result_id
                                ][_relationship_id].append(account_record)
                    case _:
                        logger.warning(
                            f"Unhandled prospecting run relationship: {_prospecting_run_result_relationship_id}"
                        )
            else:
                # Handle custom relationships
                logger.warning(
                    f"Custom relationship not implemented: {_relationship_id}"
                )

        # Build result records
        result: list[StandardRecord[RunResultV2]] = []
        for (
            prospecting_run_result_id,
            prospecting_run_result,
        ) in prospecting_run_result_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_prospecting_run_id[
                    prospecting_run_result_id
                ].items()
            }
            result.append(
                StandardRecord[RunResultV2](
                    object_id=RunResultV2.object_id,
                    is_editable=await self.prospecting_run_result_query_service.is_editable(
                        user_auth_context=user_auth_context,
                        domain_object=prospecting_run_result,
                    ),
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=prospecting_run_result,
                )
            )
        return result

    async def list_prospecting_run_result_records(
        self,
        organization_id: UUID,
        user_id: UUID,
        prospecting_run_id: UUID,
        include_custom_object: bool = False,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[RunResultV2]]:
        if isinstance(fetch_conditions, ObjectRecordFetchConditions):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=RunResultV2.object_id.object_name,
                object_identifier=RunResultV2.object_id,
                metadata_service=self.metadata_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
                select_list_service=self.select_list_service,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=RunResultV2.object_id,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_prospecting_run_result_records(
            organization_id=organization_id,
            field_references=list(field_references),
            fetch_context=fetch_context,
            prospecting_run_id=prospecting_run_id,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def list_prospecting_run_records(
        self,
        organization_id: UUID,
        include_custom_object: bool = False,
        only_include_prospecting_run_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[ProspectingRunV2]]:
        # Early return
        if (
            specified(only_include_prospecting_run_ids)
            and not only_include_prospecting_run_ids
        ):
            return []

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=ProspectingRunV2.object_id.object_name,
                object_identifier=ProspectingRunV2.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=ProspectingRunV2.object_id,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_prospecting_run_records(
            organization_id=organization_id,
            field_references=list(field_references),
            fetch_context=fetch_context,
            only_include_prospecting_run_ids=only_include_prospecting_run_ids,
            include_custom_object=include_custom_object or should_include_custom_object,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    # @log_timing(logger=logger)
    async def _list_contact_account_role_record(
        self,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_contact_ids: set[UUID] | None = None,
        only_include_account_ids: set[UUID] | None = None,
        user_id: UUID | None = None,
        contextual_account_id: UUID | None = None,
        user_auth_context: UserAuthContext | None = None,
        hint: UnsetAware[DomainFetchHints | None] = UNSET,
    ) -> Sequence[StandardRecord[ContactAccountRole]]:
        logger.debug(
            "[list contact account role record] start",
            organization_id=organization_id,
            user_id=user_id,
            only_include_contact_ids=only_include_contact_ids,
            only_include_account_ids=only_include_account_ids,
        )
        all_contact_account_roles = (
            await self.contact_query_service.list_contact_account_roles_by_contact_id(
                organization_id=organization_id,
                contact_ids=only_include_contact_ids,
                account_ids=only_include_account_ids,
            )
        )
        logger.debug(
            "[list contact account role record] fetched all contact account roles",
            organization_id=organization_id,
            user_id=user_id,
            only_include_contact_ids=only_include_contact_ids,
            only_include_account_ids=only_include_account_ids,
            contact_account_roles_count=len(all_contact_account_roles),
        )
        contact_account_roles_map_by_contact: dict[UUID, list[ContactAccountRole]] = (
            defaultdict(list)
        )
        contact_account_roles_map_by_account: dict[UUID, list[ContactAccountRole]] = (
            defaultdict(list)
        )
        for contact_account_role in all_contact_account_roles:
            contact_account_roles_map_by_contact[
                contact_account_role.contact_id
            ].append(contact_account_role)
            contact_account_roles_map_by_account[
                contact_account_role.account_id
            ].append(contact_account_role)

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_association_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for (
            _relationship_id,
            _field_references,
        ) in field_references_by_relationship_id.items():
            logger.debug(
                "[list contact account role record] relationship - start",
                organization_id=organization_id,
                user_id=user_id,
                relationship_id=_relationship_id,
            )
            next_layer_field_references: list[FieldReference] = [
                fr.field
                for fr in _field_references
                if isinstance(fr.field, FieldReference)
            ]
            _contact_account_role_relationship_id = (
                ContactAccountRoleRelationship(str(_relationship_id))
                if _relationship_id in ContactAccountRoleRelationship
                else None
            )
            match _contact_account_role_relationship_id:
                case ContactAccountRoleRelationship.contact_account_role__to__account:
                    if contact_account_roles_map_by_account:
                        account_records = await self._list_account_records(
                            organization_id=organization_id,
                            only_include_account_ids=set(
                                contact_account_roles_map_by_account.keys()
                            ),
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                        )
                        for account_record in account_records:
                            account_id = account_record.data.id
                            relevant_contact_account_roles = (
                                contact_account_roles_map_by_account.get(account_id)
                                or []
                            )
                            self._set_contact_account_role_related_objects(
                                relationship_id=_relationship_id,
                                record=account_record,
                                relevant_contact_account_roles=relevant_contact_account_roles,
                                related_objects=related_objects_by_relationship_id_by_association_id,
                            )
                case ContactAccountRoleRelationship.contact_account_role__to__contact:
                    if contact_account_roles_map_by_contact:
                        contact_records = await self._list_contact_records(
                            organization_id=organization_id,
                            only_include_contact_ids=set(
                                contact_account_roles_map_by_contact.keys()
                            ),
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                            contextual_account_id=contextual_account_id,
                        )
                        for contact_record in contact_records:
                            contact_id = contact_record.data.id
                            relevant_contact_account_roles = (
                                contact_account_roles_map_by_contact.get(contact_id)
                                or []
                            )
                            self._set_contact_account_role_related_objects(
                                relationship_id=_relationship_id,
                                record=contact_record,
                                relevant_contact_account_roles=relevant_contact_account_roles,
                                related_objects=related_objects_by_relationship_id_by_association_id,
                            )
            logger.debug(
                "[list contact account role record] relationship - end",
                organization_id=organization_id,
                user_id=user_id,
                relationship_id=_relationship_id,
            )
        result: list[StandardRecord[ContactAccountRole]] = []
        for contact_account_role in all_contact_account_roles:
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_association_id[
                    contact_account_role.contact_account_association_id
                ].items()
            }
            result.append(
                StandardRecord[ContactAccountRole](
                    object_id=ContactAccountRole.object_id,
                    is_editable=True,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=contact_account_role,
                )
            )
        return result

    async def _list_prospecting_credit_usage_records(
        self,
        organization_id: UUID,
        aggregation: ProspectingCreditUsageAggregation,
    ) -> tuple[
        Sequence[StandardRecord[ProspectingCreditUsagePointV2]],
        ProspectingCreditUsageSummary,
    ]:
        (
            fetched_prospecting_credit_usages,
            credit_usage_summary,
        ) = await self.prospecting_credit_usage_query_service.list_credit_usages(
            organization_id=organization_id,
            aggregation=aggregation,
        )

        return [
            StandardRecord[ProspectingCreditUsagePointV2](
                object_id=ProspectingCreditUsagePointV2.object_id,
                requested_relationships=set(),
                related_records={},
                data=prospecting_credit_usage,
            )
            for prospecting_credit_usage in fetched_prospecting_credit_usages
        ], credit_usage_summary

    async def list_prospecting_credit_usage_records(
        self,
        organization_id: UUID,
        aggregation: ProspectingCreditUsageAggregation,
        include_custom_object: bool = False,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> tuple[
        Sequence[StandardRecord[ProspectingCreditUsagePointV2]],
        ProspectingCreditUsageSummary,
    ]:
        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=ProspectingCreditUsagePointV2.object_id.object_name,
                object_identifier=ProspectingCreditUsagePointV2.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=ProspectingCreditUsagePointV2.object_id,
            fetch_conditions=fetch_conditions,
        )
        (
            result,
            credit_usage_summary,
        ) = await self._list_prospecting_credit_usage_records(
            organization_id=organization_id,
            aggregation=aggregation,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result, credit_usage_summary

    async def _list_prospecting_filter_fields(
        self,
    ) -> Sequence[StandardRecord[FilterFieldOptionsFacetV2]]:
        filter_fields = await self.prospecting_filter_field_query_service.list_prospecting_filter_fields()
        return [
            StandardRecord[FilterFieldOptionsFacetV2](
                object_id=FilterFieldOptionsFacetV2.object_id,
                requested_relationships=set(),
                related_records={},
                data=filter_field,
            )
            for filter_field in filter_fields
        ]

    async def list_prospecting_filter_fields(
        self,
        organization_id: UUID,
        include_custom_object: bool = False,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[FilterFieldOptionsFacetV2]]:
        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=FilterFieldOptionsFacetV2.object_id.object_name,
                object_identifier=FilterFieldOptionsFacetV2.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=FilterFieldOptionsFacetV2.object_id,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_prospecting_filter_fields()
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )
        return result

    async def list_sequence_enrollment_contact_records(
        self,
        user_auth_context: UserAuthContext,
        organization_id: UUID,
        enrollment_run_id: UUID,
        only_include_contact_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        include_custom_object: bool = False,
    ) -> Sequence[StandardRecord[SequenceEnrollmentContact]]:
        # Early return
        if specified(only_include_contact_ids) and not only_include_contact_ids:
            return []

        if user_auth_context and (
            settings.enable_sequence_perms
            or str(organization_id) in settings.enable_sequence_perms_org_ids
        ):
            can_access_sequence = await self.sequence_enrollment_query_service.can_access_sequence_enrollment_run_by_id_for_read(
                user_auth_context=user_auth_context,
                enrollment_run_id=enrollment_run_id,
            )
            if not can_access_sequence:
                raise ForbiddenError(
                    "You do not have permission to access this sequence enrollment run"
                )

        # Add FalkorLib integration for query compilation in background
        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=StdObjectIdentifiers.sequence_enrollment_contact.identifier.object_name,
                object_identifier=StdObjectIdentifiers.sequence_enrollment_contact.identifier,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )

        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=StdObjectIdentifiers.sequence_enrollment_contact.identifier,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_sequence_enrollment_contact_records(
            organization_id=organization_id,
            enrollment_run_id=enrollment_run_id,
            only_include_contact_ids=only_include_contact_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def _list_sequence_enrollment_contact_records(  # noqa: C901, PLR0912
        self,
        organization_id: UUID,
        enrollment_run_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_contact_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> Sequence[StandardRecord[SequenceEnrollmentContact]]:
        # Early return
        if specified(only_include_contact_ids) and not only_include_contact_ids:
            return []

        cached_sequence_enrollment_contacts: dict[UUID, SequenceEnrollmentContact] = (
            fetch_context.sequence_enrollment_contact_by_id
        )

        sequence_enrollment_contact_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_contact_ids - set(cached_sequence_enrollment_contacts.keys())
            if specified(only_include_contact_ids)
            else UNSET
        )

        fetched_sequence_enrollment_contacts = (
            await self.sequence_enrollment_query_service.list_enrollment_contacts(
                organization_id=organization_id,
                enrollment_run_id=enrollment_run_id,
                only_include_contact_ids=sequence_enrollment_contact_ids_to_fetch,
            )
        )

        fetched_sequence_enrollment_contact_by_id = {
            contact.id: contact for contact in fetched_sequence_enrollment_contacts
        }
        cached_sequence_enrollment_contacts.update(
            fetched_sequence_enrollment_contact_by_id
        )

        sequence_enrollment_contact_by_id: dict[UUID, SequenceEnrollmentContact] = (
            {
                contact_id: cached_sequence_enrollment_contacts[contact_id]
                for contact_id in only_include_contact_ids
                if contact_id in cached_sequence_enrollment_contacts
            }
            if specified(only_include_contact_ids)
            else fetched_sequence_enrollment_contact_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_enrollment_contact_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for _relationship_id in field_references_by_relationship_id:
            if _relationship_id in SequenceEnrollmentContactRelationship:
                _enrollment_contact_relationship_id = (
                    SequenceEnrollmentContactRelationship(str(_relationship_id))
                )
                match _enrollment_contact_relationship_id:
                    case SequenceEnrollmentContactRelationship.sequence_enrollment_contact__to__contact:
                        contact_id_to_enrollment_contact_ids: dict[UUID, set[UUID]] = {}
                        for (
                            enrollment_contact
                        ) in sequence_enrollment_contact_by_id.values():
                            if enrollment_contact.contact_id:
                                contact_id_to_enrollment_contact_ids.setdefault(
                                    enrollment_contact.contact_id, set()
                                ).add(enrollment_contact.id)

                        contact_records = await self._list_contact_records(
                            organization_id=organization_id,
                            only_include_contact_ids=set(
                                contact_id_to_enrollment_contact_ids.keys()
                            ),
                            field_references=[],
                            fetch_context=fetch_context,
                            include_custom_object=False,
                        )

                        for contact_record in contact_records:
                            for (
                                enrollment_contact_id
                            ) in contact_id_to_enrollment_contact_ids[
                                contact_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_enrollment_contact_id[
                                    enrollment_contact_id
                                ][_relationship_id].append(contact_record)

                    case SequenceEnrollmentContactRelationship.sequence_enrollment_contact__to__account:
                        account_id_to_enrollment_contact_ids: dict[UUID, set[UUID]] = {}
                        for (
                            enrollment_contact
                        ) in sequence_enrollment_contact_by_id.values():
                            if enrollment_contact.account_id:
                                account_id_to_enrollment_contact_ids.setdefault(
                                    enrollment_contact.account_id, set()
                                ).add(enrollment_contact.id)

                        account_records = await self._list_account_records(
                            organization_id=organization_id,
                            only_include_account_ids=set(
                                account_id_to_enrollment_contact_ids.keys()
                            ),
                            field_references=[],
                            fetch_context=fetch_context,
                            include_custom_object=False,
                        )

                        for account_record in account_records:
                            for (
                                enrollment_contact_id
                            ) in account_id_to_enrollment_contact_ids[
                                account_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_enrollment_contact_id[
                                    enrollment_contact_id
                                ][_relationship_id].append(account_record)

            else:
                raise NotImplementedError(
                    f"Unsupported relationship: {_relationship_id}"
                )

        # Build result records with relationships
        result: list[StandardRecord[SequenceEnrollmentContact]] = []
        for enrollment_contact in sequence_enrollment_contact_by_id.values():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_enrollment_contact_id[
                    enrollment_contact.id
                ].items()
            }
            result.append(
                StandardRecord[SequenceEnrollmentContact](
                    object_id=StdObjectIdentifiers.sequence_enrollment_contact.identifier,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=enrollment_contact,
                )
            )

        return result

    async def list_sequence_enrollment_records(
        self,
        user_auth_context: UserAuthContext,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        only_include_sequence_enrollment_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> Sequence[StandardRecord[SequenceEnrollment]]:
        # Early return
        if (
            specified(only_include_sequence_enrollment_ids)
            and not only_include_sequence_enrollment_ids
        ):
            return []

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=user_auth_context.organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=SequenceEnrollment.object_id.object_name,
                object_identifier=SequenceEnrollment.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=user_auth_context.organization_id,
            object_identifier=SequenceEnrollment.object_id,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_sequence_enrollment_records(
            user_auth_context=user_auth_context,
            organization_id=user_auth_context.organization_id,
            fetch_context=fetch_context,
            field_references=list(field_references),
            only_include_sequence_enrollment_ids=only_include_sequence_enrollment_ids,
            filter_spec=filter_spec,
            sorting_spec=sorting_spec,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=user_auth_context.organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )
        return result

    # @log_timing(logger=logger)
    async def _list_sequence_enrollment_records(  # noqa: PLR0912, C901
        self,
        organization_id: UUID,
        fetch_context: FetchContext,
        field_references: list[FieldReference],
        user_auth_context: UserAuthContext | None = None,
        only_include_sequence_enrollment_ids: UnsetAware[set[UUID]] = UNSET,
        filter_spec: FilterSpec | None = None,
        sorting_spec: SortingSpec | None = None,
    ) -> Sequence[StandardRecord[SequenceEnrollment]]:
        # Early return
        if (
            specified(only_include_sequence_enrollment_ids)
            and not only_include_sequence_enrollment_ids
        ):
            return []

        cached_sequence_enrollments: dict[UUID, SequenceEnrollment] = (
            fetch_context.sequence_enrollment_by_id
        )
        sequence_enrollment_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_sequence_enrollment_ids
            - set(cached_sequence_enrollments.keys())
            if specified(only_include_sequence_enrollment_ids)
            else UNSET
        )

        hint = self.domain_fetch_hints_from_filter_and_sorting_spec(
            filter_spec=filter_spec,
            sorting_spec=sorting_spec,
            limit=None,
            standard_object_identifier=SequenceEnrollment.object_id,
            only_include_object_ids=sequence_enrollment_ids_to_fetch,
        )

        fetched_sequence_enrollments = await self.sequence_enrollment_query_service.list_sequence_enrollments_with_domain_fetch_hints(
            organization_id=organization_id,
            domain_fetch_hints=hint,
        )

        if settings.enable_sequence_perms and user_auth_context:
            fetched_sequence_enrollments = (
                await self.sequence_enrollment_query_service.filter_viewable_records(
                    user_auth_context=user_auth_context,
                    records=fetched_sequence_enrollments,
                )
            )

        fetched_sequence_enrollments_by_id = {
            sequence_enrollment.id: sequence_enrollment
            for sequence_enrollment in fetched_sequence_enrollments
        }
        cached_sequence_enrollments.update(fetched_sequence_enrollments_by_id)

        sequence_enrollments_by_id: dict[UUID, SequenceEnrollment] = (
            {
                sequence_enrollment_id: cached_sequence_enrollments[
                    sequence_enrollment_id
                ]
                for sequence_enrollment_id in only_include_sequence_enrollment_ids
                if sequence_enrollment_id in cached_sequence_enrollments
            }
            if specified(only_include_sequence_enrollment_ids)
            else fetched_sequence_enrollments_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_enrollment_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        enrollment_id_to_sequence_step_executions = await self.sequence_execution_query_service.map_enrollment_id_to_sequence_step_executions(
            organization_id=organization_id,
            enrollment_ids=list(sequence_enrollments_by_id.keys()),
        )

        for _relationship_id in field_references_by_relationship_id:
            if _relationship_id in SequenceEnrollmentRelationship:
                _enrollment_relationship_id = SequenceEnrollmentRelationship(
                    str(_relationship_id)
                )
                match _enrollment_relationship_id:
                    case SequenceEnrollmentRelationship.sequence_enrollment__to__enrolled_by_user:
                        # Map enrollments to enrolled_by users
                        user_id_to_enrollment_ids: dict[UUID, set[UUID]] = {}
                        for enrollment in sequence_enrollments_by_id.values():
                            if enrollment.enrolled_by_user_id:
                                user_id_to_enrollment_ids.setdefault(
                                    enrollment.enrolled_by_user_id, set()
                                ).add(enrollment.id)

                        user_records = await self._list_organization_user_records(
                            organization_id=organization_id,
                            only_include_user_ids=set(user_id_to_enrollment_ids.keys()),
                            fetch_context=fetch_context,
                        )

                        for user_record in user_records:
                            for enrollment_id in user_id_to_enrollment_ids[
                                user_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_enrollment_id[
                                    enrollment_id
                                ][_relationship_id].append(user_record)

                    case (
                        SequenceEnrollmentRelationship.sequence_enrollment__to__contact
                    ):
                        contact_id_to_enrollment_ids: dict[UUID, set[UUID]] = {}
                        for enrollment in sequence_enrollments_by_id.values():
                            if enrollment.contact_id:
                                contact_id_to_enrollment_ids.setdefault(
                                    enrollment.contact_id, set()
                                ).add(enrollment.id)

                        contact_records = await self._list_contact_records(
                            organization_id=organization_id,
                            only_include_contact_ids=set(
                                contact_id_to_enrollment_ids.keys()
                            ),
                            field_references=[],
                            fetch_context=fetch_context,
                            include_custom_object=False,
                        )

                        for contact_record in contact_records:
                            for enrollment_id in contact_id_to_enrollment_ids[
                                contact_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_enrollment_id[
                                    enrollment_id
                                ][_relationship_id].append(contact_record)

                    case (
                        SequenceEnrollmentRelationship.sequence_enrollment__to__account
                    ):
                        account_id_to_enrollment_ids: dict[UUID, set[UUID]] = {}
                        for enrollment in sequence_enrollments_by_id.values():
                            if enrollment.account_id:
                                account_id_to_enrollment_ids.setdefault(
                                    enrollment.account_id, set()
                                ).add(enrollment.id)

                        account_records = await self._list_account_records(
                            organization_id=organization_id,
                            only_include_account_ids=set(
                                account_id_to_enrollment_ids.keys()
                            ),
                            field_references=[],
                            fetch_context=fetch_context,
                            include_custom_object=False,
                        )

                        for account_record in account_records:
                            for enrollment_id in account_id_to_enrollment_ids[
                                account_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_enrollment_id[
                                    enrollment_id
                                ][_relationship_id].append(account_record)

                    case SequenceEnrollmentRelationship.sequence_enrollment__to__sequence_step:
                        all_step_ids: set[UUID] = set()
                        enrollment_id_to_sequence_step_ids: dict[UUID, set[UUID]] = {}
                        for (
                            enrollment_id,
                            step_executions,
                        ) in enrollment_id_to_sequence_step_executions.items():
                            for step_execution in step_executions:
                                enrollment_id_to_sequence_step_ids.setdefault(
                                    enrollment_id, set()
                                ).add(step_execution.sequence_step_id)

                                all_step_ids.add(step_execution.sequence_step_id)

                        sequence_steps_by_ids = await self.sequence_step_query_service.map_sequence_steps_by_ids(
                            organization_id=organization_id,
                            step_ids=all_step_ids,
                        )

                        for (
                            enrollment_id,
                            ss_ids,
                        ) in enrollment_id_to_sequence_step_ids.items():
                            for ss_id in ss_ids:
                                sequence_step = sequence_steps_by_ids[ss_id]
                                related_objects_by_relationship_id_by_enrollment_id[
                                    enrollment_id
                                ][_relationship_id].append(
                                    StandardRecord[SequenceStepV2](
                                        object_id=SequenceStepV2.object_id,
                                        requested_relationships=set(),
                                        related_records={},
                                        data=sequence_step,
                                    )
                                )

                    case SequenceEnrollmentRelationship.sequence_enrollment__to__sequence_step_execution:
                        for (
                            enrollment_id,
                            sequence_step_executions,
                        ) in enrollment_id_to_sequence_step_executions.items():
                            for sse in sequence_step_executions:
                                related_objects_by_relationship_id_by_enrollment_id[
                                    enrollment_id
                                ][_relationship_id].append(
                                    StandardRecord[SequenceStepExecution](
                                        object_id=SequenceStepExecution.object_id,
                                        requested_relationships=set(),
                                        related_records={},
                                        data=sse,
                                    )
                                )

            else:
                raise NotImplementedError(
                    f"Unsupported relationship: {_relationship_id}"
                )

        # Build result records with relationships
        result: list[StandardRecord[SequenceEnrollment]] = []
        for enrollment in sequence_enrollments_by_id.values():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_enrollment_id[
                    enrollment.id
                ].items()
            }
            result.append(
                StandardRecord[SequenceEnrollment](
                    object_id=SequenceEnrollment.object_id,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=enrollment,
                )
            )

        return result

    async def list_sequence_enrollment_run_records(
        self,
        user_auth_context: UserAuthContext,
        organization_id: UUID,
        only_include_run_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
        only_include_runs_for_sequence: UnsetAware[UUID] = UNSET,
    ) -> Sequence[StandardRecord[SequenceEnrollmentRun]]:
        logger.info("Listing sequence enrollment runs")

        # Early return
        if specified(only_include_run_ids) and not only_include_run_ids:
            return []

        # Add FalkorLib integration for query compilation in background
        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            # TODO (colin): anything to do here?
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=StdObjectIdentifiers.sequence_enrollment_run.identifier.object_name,
                object_identifier=StdObjectIdentifiers.sequence_enrollment_run.identifier,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )

        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=StdObjectIdentifiers.sequence_enrollment_run.identifier,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_sequence_enrollment_run_records(
            user_auth_context=user_auth_context,
            organization_id=organization_id,
            only_include_run_ids=only_include_run_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            only_include_runs_for_sequence=only_include_runs_for_sequence,
            filter_spec=filter_spec,
            sorting_spec=sorting_spec,
            limit=None,
            offset=None,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def _list_sequence_enrollment_run_records(  # noqa: C901
        self,
        user_auth_context: UserAuthContext,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        only_include_run_ids: UnsetAware[set[UUID]] = UNSET,
        only_include_runs_for_sequence: UnsetAware[UUID] = UNSET,
        filter_spec: FilterSpec | None = None,
        sorting_spec: SortingSpec | None = None,
        limit: int | None = None,
        offset: int | None = None,
    ) -> Sequence[StandardRecord[SequenceEnrollmentRun]]:
        cached_sequence_enrollment_runs: dict[UUID, SequenceEnrollmentRun] = (
            fetch_context.sequence_enrollment_run_by_id
        )

        domain_fetch_hints = self.domain_fetch_hints_from_filter_and_sorting_spec(
            filter_spec=filter_spec,
            sorting_spec=sorting_spec,
            limit=limit,
            offset=offset,
            only_include_object_ids=only_include_run_ids,
            standard_object_identifier=SequenceEnrollmentRun.object_id,
        )

        fetched_sequence_enrollment_runs = await self.sequence_enrollment_query_service.list_sequence_enrollment_runs_with_domain_fetch_hints(
            organization_id=organization_id,
            domain_fetch_hints=domain_fetch_hints,
        )

        if settings.enable_sequence_perms and user_auth_context:
            fetched_sequence_enrollment_runs = (
                await self.sequence_enrollment_query_service.filter_viewable_runs(
                    user_auth_context=user_auth_context,
                    runs=fetched_sequence_enrollment_runs,
                )
            )

        fetched_sequence_enrollment_run_by_id = {
            run.id: run for run in fetched_sequence_enrollment_runs
        }
        cached_sequence_enrollment_runs.update(fetched_sequence_enrollment_run_by_id)

        if not cached_sequence_enrollment_runs:
            return []

        sequence_enrollment_run_by_id: dict[UUID, SequenceEnrollmentRun] = (
            {
                run_id: cached_sequence_enrollment_runs[run_id]
                for run_id in only_include_run_ids
                if run_id in cached_sequence_enrollment_runs
            }
            if specified(only_include_run_ids)
            else fetched_sequence_enrollment_run_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_enrollment_run_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for _relationship_id in field_references_by_relationship_id:
            if _relationship_id in SequenceEnrollmentRunRelationship:
                _enrollment_run_relationship_id = SequenceEnrollmentRunRelationship(
                    str(_relationship_id)
                )
                match _enrollment_run_relationship_id:
                    case SequenceEnrollmentRunRelationship.sequence_enrollment_run__to__user:
                        user_id_to_enrollment_run_ids: dict[UUID, set[UUID]] = {}
                        for enrollment_run in sequence_enrollment_run_by_id.values():
                            if enrollment_run.created_by_user_id:
                                user_id_to_enrollment_run_ids.setdefault(
                                    enrollment_run.created_by_user_id, set()
                                ).add(enrollment_run.id)

                        user_records = await self._list_organization_user_records(
                            organization_id=organization_id,
                            only_include_user_ids=set(
                                user_id_to_enrollment_run_ids.keys()
                            ),
                            fetch_context=fetch_context,
                        )

                        for user_record in user_records:
                            for enrollment_run_id in user_id_to_enrollment_run_ids[
                                user_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_enrollment_run_id[
                                    enrollment_run_id
                                ][_relationship_id].append(user_record)
            else:
                raise NotImplementedError(
                    f"Unsupported relationship: {_relationship_id}"
                )

        # Build result records with relationships
        result: list[StandardRecord[SequenceEnrollmentRun]] = []
        for enrollment_run in sequence_enrollment_run_by_id.values():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_enrollment_run_id[
                    enrollment_run.id
                ].items()
            }
            result.append(
                StandardRecord[SequenceEnrollmentRun](
                    object_id=StdObjectIdentifiers.sequence_enrollment_run.identifier,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=enrollment_run,
                )
            )

        return result

    async def _list_prospecting_saved_query_records(
        self,
        organization_id: UUID,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_prospecting_saved_query_ids: UnsetAware[set[UUID]] = UNSET,
        user_auth_context: UserAuthContext | None = None,
    ) -> Sequence[StandardRecord[ProspectingSavedSearchQueryV2]]:
        # Early return
        if (
            specified(only_include_prospecting_saved_query_ids)
            and not only_include_prospecting_saved_query_ids
        ):
            return []

        # Get cached prospecting_saved_query
        cached_prospecting_saved_queries: dict[UUID, ProspectingSavedSearchQueryV2] = (
            fetch_context.prospecting_saved_query_by_id
        )
        prospecting_saved_query_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_prospecting_saved_query_ids
            - set(cached_prospecting_saved_queries.keys())
            if specified(only_include_prospecting_saved_query_ids)
            else UNSET
        )

        # Fetch prospecting_saved_query from service
        fetched_prospecting_saved_queries = await self.prospecting_saved_search_query_query_service.list_prospecting_saved_queries(
            organization_id=organization_id,
            only_include_prospecting_saved_query_ids=prospecting_saved_query_ids_to_fetch,
        )

        fetched_prospecting_saved_query_by_id = {
            prospecting_saved_query.id: prospecting_saved_query
            for prospecting_saved_query in fetched_prospecting_saved_queries
        }
        cached_prospecting_saved_queries.update(fetched_prospecting_saved_query_by_id)

        prospecting_saved_query_by_id: dict[UUID, ProspectingSavedSearchQueryV2] = (
            {
                prospecting_saved_query_id: cached_prospecting_saved_queries[
                    prospecting_saved_query_id
                ]
                for prospecting_saved_query_id in only_include_prospecting_saved_query_ids
                if prospecting_saved_query_id in cached_prospecting_saved_queries
            }
            if specified(only_include_prospecting_saved_query_ids)
            else fetched_prospecting_saved_query_by_id
        )

        return [
            StandardRecord[ProspectingSavedSearchQueryV2](
                object_id=ProspectingSavedSearchQueryV2.object_id,
                requested_relationships=set(),
                related_records={},
                data=prospecting_saved_query,
            )
            for prospecting_saved_query in prospecting_saved_query_by_id.values()
        ]

    async def list_prospecting_saved_query_records(
        self,
        organization_id: UUID,
        include_custom_object: bool = False,
        only_include_prospecting_saved_query_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[ProspectingSavedSearchQueryV2]]:
        # Early return
        if (
            specified(only_include_prospecting_saved_query_ids)
            and not only_include_prospecting_saved_query_ids
        ):
            return []

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=ProspectingSavedSearchQueryV2.object_id.object_name,
                object_identifier=ProspectingSavedSearchQueryV2.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=organization_id,
            object_identifier=ProspectingSavedSearchQueryV2.object_id,
            fetch_conditions=fetch_conditions,
        )

        result = await self._list_prospecting_saved_query_records(
            organization_id=organization_id,
            field_references=list(field_references),
            fetch_context=fetch_context,
            only_include_prospecting_saved_query_ids=only_include_prospecting_saved_query_ids,
            include_custom_object=include_custom_object or should_include_custom_object,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    def _set_contact_account_role_related_objects(
        self,
        relationship_id: RelationshipId,
        record: ModeledObjectRecord,
        relevant_contact_account_roles: list[ContactAccountRole],
        related_objects: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ],
    ) -> None:
        for contact_account_role in relevant_contact_account_roles:
            related_objects[contact_account_role.contact_account_association_id][
                relationship_id
            ].append(record)

    async def get_email_account_record(
        self,
        record_id: UUID,
        user_auth_context: UserAuthContext,
        include_custom_object: bool,
        ordered_object_fields: (
            tuple[QualifiedField | FieldReference, ...] | None
        ) = None,
    ) -> Sequence[StandardRecord[EmailAccountV2]]:
        email_account = await self.list_email_account_records_v2(
            user_auth_context=user_auth_context,
            include_custom_object=include_custom_object,
            only_include_email_account_ids={record_id},
            fetch_conditions=ObjectRecordFetchConditions(
                fields=ordered_object_fields,
                filter_spec=None,
                sorting_spec=None,
            ),
        )
        if not email_account:
            raise ResourceNotFoundError(f"No account record found for {record_id}")
        return email_account

    async def list_email_account_records_v2(
        self,
        user_auth_context: UserAuthContext,
        include_custom_object: bool,
        only_include_email_account_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[EmailAccountV2]]:
        # Early return
        if (
            specified(only_include_email_account_ids)
            and not only_include_email_account_ids
        ):
            return []

        if (
            isinstance(fetch_conditions, ObjectRecordFetchConditions)
            and FalkorLib.should_run_falkor_bg()
        ):
            await FalkorLib.try_compile_query(
                organization_id=user_auth_context.organization_id,
                fetch_conditions=fetch_conditions,
                entity_type=EmailAccountV2.object_id.object_name,
                object_identifier=EmailAccountV2.object_id,
                metadata_service=self.metadata_service,
                select_list_service=self.select_list_service,
                falkordb_conn_mgr=self.falkordb_conn_mgr,
            )
        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=user_auth_context.organization_id,
            object_identifier=EmailAccountV2.object_id,
            fetch_conditions=fetch_conditions,
        )
        result = await self._list_email_account_records_v2(
            user_auth_context=user_auth_context,
            only_include_email_account_ids=only_include_email_account_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=user_auth_context.organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def _list_email_account_records_v2(  # noqa: PLR0912, C901
        self,
        user_auth_context: UserAuthContext,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_email_account_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> Sequence[StandardRecord[EmailAccountV2]]:
        # Early return
        if (
            specified(only_include_email_account_ids)
            and not only_include_email_account_ids
        ):
            return []

        cached_email_accounts: dict[UUID, EmailAccountV2] = (
            fetch_context.email_account_by_id
        )
        email_account_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_email_account_ids - set(cached_email_accounts.keys())
            if specified(only_include_email_account_ids)
            else UNSET
        )
        fetched_email_accounts = (
            await self.email_account_query_service.list_email_accounts(
                user_auth_context=user_auth_context,
                only_include_email_account_ids=email_account_ids_to_fetch,
                include_archived=True,
            )
        )
        fetched_email_accounts = (
            await self.email_account_query_service.filter_viewable_records(
                records=fetched_email_accounts,
                user_auth_context=user_auth_context,
            )
        )
        fetched_email_account_by_id = {
            email_account.id: email_account for email_account in fetched_email_accounts
        }
        cached_email_accounts.update(fetched_email_account_by_id)

        email_accounts_by_id: dict[UUID, EmailAccountV2] = (
            {
                email_account_id: cached_email_accounts[email_account_id]
                for email_account_id in only_include_email_account_ids
                if email_account_id in cached_email_accounts
            }
            if specified(only_include_email_account_ids)
            else fetched_email_account_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_email_account_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for (
            _relationship_id,
            _field_references,
        ) in field_references_by_relationship_id.items():
            next_layer_field_references: list[FieldReference] = [
                fr.field
                for fr in _field_references
                if isinstance(fr.field, FieldReference)
            ]
            if _relationship_id in EmailAccountRelationship:
                _email_account_relationship_id = EmailAccountRelationship(
                    str(_relationship_id)
                )
                match _email_account_relationship_id:
                    case (
                        EmailAccountRelationship.email_account__to__owner_user
                        | EmailAccountRelationship.email_account__to__created_by_user
                        | EmailAccountRelationship.email_account__to__updated_by_user
                        | EmailAccountRelationship.email_account__to__deleted_by_user
                    ):
                        user_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in EmailAccountV2.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        user_id_to_email_account_ids: dict[UUID, set[UUID]] = {}
                        for email_account in email_accounts_by_id.values():
                            if user_id := getattr(email_account, user_id_field, None):
                                user_id_to_email_account_ids.setdefault(
                                    user_id, set()
                                ).add(email_account.id)
                        user_records = await self._list_organization_user_records(
                            organization_id=user_auth_context.organization_id,
                            only_include_user_ids=set(
                                user_id_to_email_account_ids.keys()
                            ),
                            fetch_context=fetch_context,
                        )
                        for user_record in user_records:
                            for _email_account_id in user_id_to_email_account_ids[
                                user_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_email_account_id[
                                    _email_account_id
                                ][_relationship_id].append(user_record)
                    case EmailAccountRelationship.email_account__to__sequence:
                        email_account_id_to_sequence_ids: dict[UUID, set[UUID]] = (
                            defaultdict(set)
                        )
                        all_sequence_ids: set[UUID] = set()

                        (
                            email_account_id_to_sequence_ids,
                            all_sequence_ids,
                        ) = await self.sequence_enrollment_query_service.find_email_account_sequence_associations_by_email_account_ids(
                            email_account_ids=list(email_accounts_by_id.keys()),
                            organization_id=user_auth_context.organization_id,
                        )

                        sequence_records = await self._list_sequence_records(
                            user_id=user_auth_context.user_id,
                            organization_id=user_auth_context.organization_id,
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                            only_include_sequence_ids=all_sequence_ids,
                        )

                        sequence_by_id = {
                            sequence_record.data.id: sequence_record
                            for sequence_record in sequence_records
                        }

                        for (
                            email_account_id,
                            sequence_ids_set,
                        ) in email_account_id_to_sequence_ids.items():
                            for sequence_id in sequence_ids_set:
                                if sequence_id in sequence_by_id:
                                    related_objects_by_relationship_id_by_email_account_id[
                                        email_account_id
                                    ][_relationship_id].append(
                                        sequence_by_id[sequence_id]
                                    )
                    case EmailAccountRelationship.email_account__to__signature:
                        signature_id_to_email_account_ids: dict[UUID, set[UUID]] = {}
                        for email_account in email_accounts_by_id.values():
                            if email_account.signature_id:
                                signature_id_to_email_account_ids.setdefault(
                                    email_account.signature_id, set()
                                ).add(email_account.id)
                        signature_records = await self._list_signature_records(
                            user_auth_context=user_auth_context,
                            only_include_signature_ids=set(
                                signature_id_to_email_account_ids.keys()
                            ),
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                        )
                        for signature_record in signature_records:
                            for email_account_id in signature_id_to_email_account_ids[
                                signature_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_email_account_id[
                                    email_account_id
                                ][_relationship_id].append(signature_record)

                    case _ as unreachable:
                        assert_never(unreachable)
            else:
                raise NotImplementedError(
                    f"Unsupported custom object relationship: {_relationship_id}"
                )
        result: list[StandardRecord[EmailAccountV2]] = []
        for email_account_id, email_account in email_accounts_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_email_account_id[
                    email_account_id
                ].items()
            }
            result.append(
                StandardRecord[EmailAccountV2](
                    object_id=EmailAccountV2.object_id,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=email_account,
                )
            )
        return result

    async def list_outbound_domain_records(
        self,
        user_auth_context: UserAuthContext,
        include_custom_object: bool,
        only_include_outbound_domain_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[OutboundDomainV2]]:
        # Early return
        if (
            specified(only_include_outbound_domain_ids)
            and not only_include_outbound_domain_ids
        ):
            return []

        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=user_auth_context.organization_id,
            object_identifier=OutboundDomainV2.object_id,
            fetch_conditions=fetch_conditions,
        )
        result = await self._list_outbound_domain_records(
            user_auth_context=user_auth_context,
            only_include_outbound_domain_ids=only_include_outbound_domain_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=user_auth_context.organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )
        return result

    async def _list_outbound_domain_records(  # noqa: C901, PLR0912
        self,
        user_auth_context: UserAuthContext,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_outbound_domain_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> Sequence[StandardRecord[OutboundDomainV2]]:
        # Early return
        if (
            specified(only_include_outbound_domain_ids)
            and not only_include_outbound_domain_ids
        ):
            return []

        cached_outbound_domains: dict[UUID, OutboundDomainV2] = (
            fetch_context.outbound_domain_by_id
        )
        outbound_domain_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_outbound_domain_ids - set(cached_outbound_domains.keys())
            if specified(only_include_outbound_domain_ids)
            else UNSET
        )
        fetched_outbound_domains = (
            await self.outbound_domain_query_service.list_outbound_domains(
                user_auth_context=user_auth_context,
                only_include_outbound_domain_ids=outbound_domain_ids_to_fetch,
            )
        )
        fetched_outbound_domain_by_id = {
            outbound_domain.id: outbound_domain
            for outbound_domain in fetched_outbound_domains
        }
        cached_outbound_domains.update(fetched_outbound_domain_by_id)

        outbound_domain_by_id: dict[UUID, OutboundDomainV2] = (
            {
                outbound_domain_id: cached_outbound_domains[outbound_domain_id]
                for outbound_domain_id in only_include_outbound_domain_ids
                if outbound_domain_id in cached_outbound_domains
            }
            if specified(only_include_outbound_domain_ids)
            else fetched_outbound_domain_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_outbound_domain_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for (
            _relationship_id,
            _field_references,
        ) in field_references_by_relationship_id.items():
            next_layer_field_references: list[FieldReference] = [
                fr.field
                for fr in _field_references
                if isinstance(fr.field, FieldReference)
            ]
            if _relationship_id in OutboundDomainRelationship:
                _outbound_domain_relationship_id = OutboundDomainRelationship(
                    str(_relationship_id)
                )
                match _outbound_domain_relationship_id:
                    case (
                        OutboundDomainRelationship.outbound_domain__to__created_by_user
                    ):
                        user_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in OutboundDomainV2.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        user_id_to_outbound_domain_ids: dict[UUID, set[UUID]] = {}
                        for outbound_domain in outbound_domain_by_id.values():
                            if user_id := getattr(outbound_domain, user_id_field, None):
                                user_id_to_outbound_domain_ids.setdefault(
                                    user_id, set()
                                ).add(outbound_domain.id)
                        user_records = await self._list_organization_user_records(
                            organization_id=user_auth_context.organization_id,
                            only_include_user_ids=set(
                                user_id_to_outbound_domain_ids.keys()
                            ),
                            fetch_context=fetch_context,
                        )
                        for user_record in user_records:
                            for _outbound_domain_id in user_id_to_outbound_domain_ids[
                                user_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_outbound_domain_id[
                                    _outbound_domain_id
                                ][_relationship_id].append(user_record)
                    case OutboundDomainRelationship.outbound_domain__to__sequence:
                        outbound_domain_id_to_sequence_ids: dict[UUID, set[UUID]] = (
                            defaultdict(set)
                        )
                        all_sequence_ids: set[UUID] = set()

                        (
                            outbound_domain_id_to_sequence_ids,
                            all_sequence_ids,
                        ) = await self.sequence_enrollment_query_service.find_outbound_domain_sequence_associations_by_outbound_domain_ids(
                            outbound_domain_ids=list(outbound_domain_by_id.keys()),
                            organization_id=user_auth_context.organization_id,
                        )

                        sequence_records = await self._list_sequence_records(
                            user_id=user_auth_context.user_id,
                            organization_id=user_auth_context.organization_id,
                            field_references=next_layer_field_references,
                            fetch_context=fetch_context,
                            include_custom_object=include_custom_object,
                            only_include_sequence_ids=all_sequence_ids,
                        )

                        sequence_by_id = {
                            sequence_record.data.id: sequence_record
                            for sequence_record in sequence_records
                        }

                        for (
                            outbound_domain_id,
                            sequence_ids_set,
                        ) in outbound_domain_id_to_sequence_ids.items():
                            for sequence_id in sequence_ids_set:
                                if sequence_id in sequence_by_id:
                                    related_objects_by_relationship_id_by_outbound_domain_id[
                                        outbound_domain_id
                                    ][_relationship_id].append(
                                        sequence_by_id[sequence_id]
                                    )

                    case _ as unreachable:
                        assert_never(unreachable)
            else:
                raise NotImplementedError(
                    f"Unsupported custom object relationship: {_relationship_id}"
                )

        result: list[StandardRecord[OutboundDomainV2]] = []
        for outbound_domain_id, outbound_domain in outbound_domain_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_outbound_domain_id[
                    outbound_domain_id
                ].items()
            }
            result.append(
                StandardRecord[OutboundDomainV2](
                    object_id=OutboundDomainV2.object_id,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=outbound_domain,
                )
            )

        return result

    async def get_signature_record(
        self,
        record_id: UUID,
        user_auth_context: UserAuthContext,
        include_custom_object: bool,
        ordered_object_fields: (
            tuple[QualifiedField | FieldReference, ...] | None
        ) = None,
    ) -> Sequence[StandardRecord[SignatureV2]]:
        signature = await self.list_signature_records(
            user_auth_context=user_auth_context,
            include_custom_object=include_custom_object,
            only_include_signature_ids={record_id},
            fetch_conditions=ObjectRecordFetchConditions(
                fields=ordered_object_fields,
                filter_spec=None,
                sorting_spec=None,
            ),
        )
        if not signature:
            raise ResourceNotFoundError(f"No signature record found for {record_id}")
        return signature

    async def list_signature_records(
        self,
        user_auth_context: UserAuthContext,
        include_custom_object: bool,
        only_include_signature_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_conditions: (
            ObjectRecordFetchConditions | list[FieldReference] | None
        ) = None,
    ) -> Sequence[StandardRecord[SignatureV2]]:
        # Early return
        if specified(only_include_signature_ids) and not only_include_signature_ids:
            return []

        fetch_context = new_fetch_context()
        (
            field_references,
            filter_spec,
            sorting_spec,
            should_include_custom_object,
        ) = await self.validate_field_and_fetch_specs(
            organization_id=user_auth_context.organization_id,
            object_identifier=SignatureV2.object_id,
            fetch_conditions=fetch_conditions,
        )
        result = await self._list_signature_records(
            user_auth_context=user_auth_context,
            only_include_signature_ids=only_include_signature_ids,
            field_references=list(field_references),
            fetch_context=fetch_context,
            include_custom_object=include_custom_object or should_include_custom_object,
        )
        del fetch_context

        if filter_spec:
            result = await self.filter_record_sequence(
                organization_id=user_auth_context.organization_id,
                filter_spec=filter_spec,
                records=result,
            )
        if sorting_spec:
            result = await self.sort_record_sequence(
                sorting_spec=sorting_spec,
                records=result,
            )

        return result

    async def _list_signature_records(  # noqa: C901, PLR0912
        self,
        user_auth_context: UserAuthContext,
        field_references: list[FieldReference],
        fetch_context: FetchContext,
        include_custom_object: bool,
        only_include_signature_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> Sequence[StandardRecord[SignatureV2]]:
        # Early return
        if specified(only_include_signature_ids) and not only_include_signature_ids:
            return []

        cached_signatures: dict[UUID, SignatureV2] = fetch_context.signature_by_id
        signature_ids_to_fetch: UnsetAware[set[UUID]] = (
            only_include_signature_ids - set(cached_signatures.keys())
            if specified(only_include_signature_ids)
            else UNSET
        )
        fetched_signatures = await self.signature_query_service.list_signatures(
            user_auth_context=user_auth_context,
            only_include_signature_ids=signature_ids_to_fetch,
        )
        fetched_signature = await self.signature_query_service.filter_viewable_records(
            records=fetched_signatures,
            user_auth_context=user_auth_context,
        )
        fetched_signature_by_id = {
            signature.id: signature for signature in fetched_signature
        }
        cached_signatures.update(fetched_signature_by_id)

        signatures_by_id: dict[UUID, SignatureV2] = (
            {
                signature_id: cached_signatures[signature_id]
                for signature_id in only_include_signature_ids
                if signature_id in cached_signatures
            }
            if specified(only_include_signature_ids)
            else fetched_signature_by_id
        )

        field_references_by_relationship_id = (
            self._group_field_references_by_relationship_id(
                field_references=field_references
            )
        )

        related_objects_by_relationship_id_by_signature_id: defaultdict[
            UUID, defaultdict[RelationshipId, list[ModeledObjectRecord]]
        ] = defaultdict(lambda: defaultdict(list))

        for (
            _relationship_id,
            _field_references,
        ) in field_references_by_relationship_id.items():
            next_layer_field_references: list[FieldReference] = [
                fr.field
                for fr in _field_references
                if isinstance(fr.field, FieldReference)
            ]
            if _relationship_id in SignatureRelationship:
                _signature_relationship_id = SignatureRelationship(
                    str(_relationship_id)
                )
                match _signature_relationship_id:
                    case (
                        SignatureRelationship.signature__to__created_by_user
                        | SignatureRelationship.signature__to__updated_by_user
                        | SignatureRelationship.signature__to__deleted_by_user
                    ):
                        user_id_field = next(
                            outbound.ordered_self_field_identifiers[0].field_name
                            for outbound in SignatureV2.outbound_relationships
                            if outbound.id == _relationship_id
                            and outbound.ordered_self_field_identifiers
                            and isinstance(
                                outbound.ordered_self_field_identifiers[0],
                                StandardFieldIdentifier,
                            )
                        )
                        user_id_to_signature_ids: dict[UUID, set[UUID]] = {}
                        for signature in signatures_by_id.values():
                            if user_id := getattr(signature, user_id_field, None):
                                user_id_to_signature_ids.setdefault(user_id, set()).add(
                                    signature.id
                                )
                        user_records = await self._list_organization_user_records(
                            organization_id=user_auth_context.organization_id,
                            only_include_user_ids=set(user_id_to_signature_ids.keys()),
                            fetch_context=fetch_context,
                        )
                        for user_record in user_records:
                            for _signature_id in user_id_to_signature_ids[
                                user_record.data.id
                            ]:
                                related_objects_by_relationship_id_by_signature_id[
                                    _signature_id
                                ][_relationship_id].append(user_record)
                    case SignatureRelationship.signature__to__email_account:
                        signature_id_to_email_account_ids: dict[UUID, set[UUID]] = (
                            defaultdict(set)
                        )
                        all_email_account_ids: set[UUID] = set()

                        (
                            signature_id_to_email_account_ids,
                            all_email_account_ids,
                        ) = await self.email_account_query_service.find_signature_email_account_associations_by_signature_ids(
                            user_auth_context=user_auth_context,
                            signature_ids=list(signatures_by_id.keys()),
                        )

                        email_account_records = (
                            await self._list_email_account_records_v2(
                                user_auth_context=user_auth_context,
                                field_references=next_layer_field_references,
                                fetch_context=fetch_context,
                                include_custom_object=include_custom_object,
                                only_include_email_account_ids=all_email_account_ids,
                            )
                        )

                        email_account_by_id = {
                            email_account_record.data.id: email_account_record
                            for email_account_record in email_account_records
                        }

                        for (
                            signature_id,
                            email_account_ids_set,
                        ) in signature_id_to_email_account_ids.items():
                            for email_account_id in email_account_ids_set:
                                if email_account_id in email_account_by_id:
                                    related_objects_by_relationship_id_by_signature_id[
                                        signature_id
                                    ][_relationship_id].append(
                                        email_account_by_id[email_account_id]
                                    )
                    case _ as unreachable:
                        assert_never(unreachable)
            else:
                raise NotImplementedError(
                    f"Unsupported custom object relationship: {_relationship_id}"
                )
        result: list[StandardRecord[SignatureV2]] = []
        for signature_id, signature in signatures_by_id.items():
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {
                relationship_id: tuple(related)
                for relationship_id, related in related_objects_by_relationship_id_by_signature_id[
                    signature_id
                ].items()
            }
            result.append(
                StandardRecord[SignatureV2](
                    object_id=SignatureV2.object_id,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=signature,
                )
            )
        return result

    async def list_organization_global_message_records(
        self,
        user_id: UUID,
        organization_id: UUID,
        only_include_global_message_ids: set[UUID],
        fetch_context: FetchContext,
    ) -> Sequence[StandardRecord[GlobalMessage]]:
        cached_global_messages: dict[UUID, GlobalMessage] = (
            fetch_context.global_message_by_id
        )
        global_message_ids_to_fetch: set[UUID] = only_include_global_message_ids - set(
            cached_global_messages.keys()
        )
        fetched_global_messages: list[
            GlobalMessage
        ] = await self.global_thread_query_service.list_global_messages(
            user_id=user_id,
            organization_id=organization_id,
            only_include_global_message_ids=global_message_ids_to_fetch,
        )
        fetched_global_message_by_id: dict[UUID, GlobalMessage] = {
            global_message.id: global_message
            for global_message in fetched_global_messages
        }
        cached_global_messages.update(fetched_global_message_by_id)

        global_message_id_to_return = (
            only_include_global_message_ids
            if specified(only_include_global_message_ids)
            else fetched_global_message_by_id.keys()
        )

        logger.info(f"Returning {len(global_message_id_to_return)} global messages")
        return [
            StandardRecord[GlobalMessage](
                object_id=GlobalMessage.object_id,
                requested_relationships=set(),
                related_records={},
                data=(cached_global_messages[global_message_id]),
            )
            for global_message_id in global_message_id_to_return
            if global_message_id in cached_global_messages
        ]

    async def get_global_message_by_id(
        self,
        global_message_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> StandardRecord[GlobalMessage]:
        global_message_records = await self.list_organization_global_message_records(
            user_id=user_id,
            organization_id=organization_id,
            only_include_global_message_ids={global_message_id},
            fetch_context=new_fetch_context(),
        )
        if not global_message_records:
            raise ResourceNotFoundError(
                f"Global message {global_message_id} not found or has been deleted."
            )
        return global_message_records[0]


def create_custom_object_record_data(  # type: ignore[explicit-any] # TODO: fix-any-annotation
    data: CustomObjectData,
    custom_fields: list[CustomField],
) -> dict[UUID, Any]:
    result = {}
    for field in custom_fields:
        if field.deleted_at:
            continue
        value = data.get_value(
            slot_number=field.slot_number,
            custom_field_id=field.id,
        )
        if value is not None:
            result[field.id] = value.to_generic_value()
    return result


def create_record_mapping(
    target_ids_by_source_id: dict[UUID, list[UUID]],
    target_records: list[CustomRecord],
) -> dict[UUID, list[CustomRecord]]:
    result: defaultdict[UUID, list[CustomRecord]] = defaultdict(list)
    target_records_by_id = {record.data.id: record for record in target_records}

    for source_id, target_ids in target_ids_by_source_id.items():
        for target_id in target_ids:
            if target_record := target_records_by_id.get(target_id):
                result[source_id].append(target_record)

    return dict(result)


def get_identifier_id(
    identifier: StandardObjectIdentifier | CustomObjectIdentifier,
) -> UUID | None:
    """Get object_id from a CustomObjectIdentifier."""
    if isinstance(identifier, CustomObjectIdentifier):
        return identifier.object_id
    return None


def get_identifier_name(
    identifier: StandardObjectIdentifier | CustomObjectIdentifier,
) -> str | None:
    """Get object_name from a StandardObjectIdentifier."""
    if isinstance(identifier, StandardObjectIdentifier):
        return identifier.object_name
    return None


class SingletonDomainObjectQueryService(Singleton, DomainObjectQueryService):
    pass


# @log_timing(logger=logger)
def get_domain_object_query_service(
    *,
    db_engine: DatabaseEngine,
    falkordb_conn_mgr: FalkorDBConnectionManager | None = None,
) -> DomainObjectQueryService:
    if SingletonDomainObjectQueryService.has_instance():
        return SingletonDomainObjectQueryService.get_singleton_instance()
    return SingletonDomainObjectQueryService(
        falkordb_conn_mgr=falkordb_conn_mgr,
        metadata_service=get_metadata_service(db_engine=db_engine),
        feature_flag_service=feature_flag_service.get_feature_flag_service(),
        saved_domain_object_filter_repository=SavedDomainObjectFilterRepository(
            engine=db_engine
        ),
        meeting_query_service=get_meeting_query_service(db_engine=db_engine),
        account_query_service=get_account_query_service(
            db_engine=db_engine,
        ),
        outbound_domain_query_service=get_outbound_domain_query_service_by_db_engine(
            db_engine=db_engine
        ),
        contact_query_service=get_contact_query_service(
            db_engine=db_engine,
        ),
        pipeline_query_service=get_pipeline_query_service(db_engine=db_engine),
        user_service=get_user_service_general(db_engine=db_engine),
        comment_query_service=get_comment_query_service_from_engine(db_engine),
        goal_query_service=goal_query_service_by_db_engine(db_engine=db_engine),
        task_query_service=get_task_query_service_from_engine(db_engine=db_engine),
        global_thread_query_service=get_global_thread_query_service(
            db_engine=db_engine
        ),
        activity_resolver=get_activity_resolver(db_engine=db_engine),
        event_schedule_query_service=get_event_schedule_query_service_from_engine(
            db_engine=db_engine
        ),
        email_template_query_service=get_email_template_query_service_from_engine(
            db_engine=db_engine
        ),
        select_list_service=get_select_list_service(engine=db_engine),
        citation_query_service=get_citation_query_service(db_engine=db_engine),
        conversation_query_service=get_conversation_query_service_from_engine(
            db_engine=db_engine
        ),
        custom_object_query_service=get_custom_object_query_service(
            db_engine=db_engine
        ),
        association_service=get_association_service(db_engine=db_engine),
        email_account_query_service=get_email_account_query_service_by_db_engine(
            db_engine=db_engine
        ),
        domain_object_list_query_service=get_domain_object_list_query_service_by_db_engine(
            db_engine=db_engine
        ),
        email_attachment_query_service=get_email_attachment_query_service_by_db_engine(
            db_engine=db_engine
        ),
        email_account_pool_query_service=get_email_account_pool_query_service_from_engine(
            db_engine=db_engine
        ),
        sequence_query_service=get_sequence_query_service_by_db(db_engine=db_engine),
        prospecting_run_query_service=get_prospecting_run_query_service_by_db(
            db_engine=db_engine
        ),
        prospecting_run_result_query_service=get_prospecting_run_result_query_service_by_db(
            db_engine=db_engine
        ),
        prospecting_credit_usage_query_service=get_prospecting_credit_usage_query_service_by_db(
            db_engine=db_engine
        ),
        prospecting_filter_field_query_service=get_prospecting_filter_field_query_service_by_db(
            db_engine=db_engine
        ),
        sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
            db_engine=db_engine
        ),
        sequence_step_query_service=get_sequence_step_query_service_by_db_engine(
            db_engine=db_engine
        ),
        sequence_execution_query_service=get_sequence_execution_query_service_by_db_engine(
            db_engine=db_engine
        ),
        sequence_step_variant_query_service=get_sequence_step_variant_query_service_by_db_engine(
            db_engine=db_engine
        ),
        prospecting_saved_search_query_query_service=get_prospecting_saved_search_query_query_service_by_db(
            db_engine=db_engine
        ),
        signature_query_service=get_signature_query_service_by_db_engine(
            db_engine=db_engine
        ),
        sequence_step_execution_query_service=get_sequence_step_execution_query_service_by_db(
            db_engine=db_engine
        ),
    )
