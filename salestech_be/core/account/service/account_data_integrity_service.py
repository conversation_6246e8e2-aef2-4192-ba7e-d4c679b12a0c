from typing import assert_never
from uuid import UUID

from salestech_be.common.exception.exception import (
    ConcurrentModificationError,
    ResourceNotFoundError,
)
from salestech_be.common.type.metadata.common import ObjectAccessStatus
from salestech_be.common.type.patch_request import UNSET
from salestech_be.core.account.service.account_service import (
    get_account_service,
)
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityDataOperationValidationError,
    IntegrityOperationValidation,
)
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.crm_integrity_repository import CRMIntegrityJobRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.account import (
    Account,
    AccountUpdate,
    AccountUpdateCondition,
)
from salestech_be.db.models.crm_integrity import (
    EntityType,
    IntegrityJobType,
    IntegrityOperation,
    JobStatus,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now

logger = get_logger()


class AccountDataIntegrityService:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.account_service = get_account_service(
            db_engine=db_engine,
        )
        self.account_repository = AccountRepository(engine=db_engine)
        self.crm_integrity_job_repository = CRMIntegrityJobRepository(
            engine=db_engine,
        )

    async def _is_dest_account_in_move_contact_to_account_job(
        self,
        account_id: UUID,
        job_ids: list[UUID],
        organization_id: UUID,
    ) -> bool:
        """
        [first-time association special case handling]

        if the account is the destination account in a move contact to account job,
        we allow the integrity job to proceed as long as all currently running jobs
        locking this account are also "move contact to account" jobs where this
        account is the destination.
        """
        running_integrity_jobs = (
            await self.crm_integrity_job_repository.find_integrity_jobs_by_job_ids(
                job_ids=job_ids,
                organization_id=organization_id,
                job_status=[JobStatus.RUNNING],
            )
        )

        return all(
            job.type == IntegrityJobType.MOVE
            and job.src_entity_type == EntityType.CONTACT
            and job.dest_entity_type == EntityType.ACCOUNT
            and job.dest_entity_id == account_id
            for job in running_integrity_jobs
        )

    async def validate_account_availability(
        self,
        account_id: UUID,
        organization_id: UUID,
        is_dest_account_in_move_contact_to_account_job: bool = False,
    ) -> list[IntegrityOperationValidation]:
        validations: list[IntegrityOperationValidation] = []
        accounts = await self.account_service.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids={account_id},
        )
        if not accounts:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.object_not_found,
                    error_message=f"account {account_id} not found",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.ACCOUNT,
                    src_entity_id=account_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=account_id,
                )
            )
            return validations

        if accounts[0].access_status == ObjectAccessStatus.INTEGRITY_JOB_RUNNING:
            if not (
                is_dest_account_in_move_contact_to_account_job
                and accounts[0].integrity_job_started_by_job_ids
                and await self._is_dest_account_in_move_contact_to_account_job(
                    account_id=account_id,
                    job_ids=accounts[0].integrity_job_started_by_job_ids,
                    organization_id=organization_id,
                )
            ):
                validations.append(
                    IntegrityOperationValidation(
                        violation=IntegrityDataOperationValidationError.object_has_running_integrity_job,
                        error_message=f"account {account_id} has running integrity job",
                        type=IntegrityOperation.VALIDATE,
                        src_entity_type=EntityType.ACCOUNT,
                        src_entity_id=account_id,
                        dest_entity_type=EntityType.ACCOUNT,
                        dest_entity_id=account_id,
                    )
                )
                return validations
        elif accounts[0].access_status != ObjectAccessStatus.ACTIVE:
            validations.append(
                IntegrityOperationValidation(
                    violation=IntegrityDataOperationValidationError.object_not_in_active_state,
                    error_message=f"account {account_id} is not in active state",
                    type=IntegrityOperation.VALIDATE,
                    src_entity_type=EntityType.ACCOUNT,
                    src_entity_id=account_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=account_id,
                )
            )
            return validations

        return validations

    async def acquire_lock_for_integrity_job(
        self,
        *,
        job_id: UUID,
        account_id: UUID,
        user_id: UUID,
        organization_id: UUID,
        is_dest_account_in_move_contact_to_account_job: bool = False,
    ) -> AccountV2:
        account = await self.account_service.get_account_v2(
            account_id=account_id, organization_id=organization_id
        )
        match account.access_status:
            case ObjectAccessStatus.ACTIVE:
                column_to_update = AccountUpdate(
                    integrity_job_started_at=zoned_utc_now(),
                    integrity_job_started_by_user_id=user_id,
                    integrity_job_started_by_job_ids=[job_id],
                    updated_by_user_id=user_id,
                )
                column_condition = AccountUpdateCondition(
                    integrity_job_started_at=None,
                    integrity_job_started_by_user_id=None,
                    integrity_job_started_by_job_ids=[],
                )
                exclude_locked_by_integrity_jobs = True
            case ObjectAccessStatus.INTEGRITY_JOB_RUNNING:
                if (
                    is_dest_account_in_move_contact_to_account_job
                    and account.integrity_job_started_by_job_ids
                    and await self._is_dest_account_in_move_contact_to_account_job(
                        account_id=account_id,
                        job_ids=account.integrity_job_started_by_job_ids,
                        organization_id=organization_id,
                    )
                ):
                    column_to_update = AccountUpdate(
                        integrity_job_started_by_job_ids=list(
                            set(account.integrity_job_started_by_job_ids) | {job_id}
                        ),
                        updated_by_user_id=user_id,
                    )
                    column_condition = AccountUpdateCondition(
                        integrity_job_started_by_job_ids=account.integrity_job_started_by_job_ids,
                    )
                    exclude_locked_by_integrity_jobs = False
                else:
                    raise ResourceNotFoundError(
                        f"account was locked by another job, account_id: {account_id}, organization_id: {organization_id}"
                    )
            case ObjectAccessStatus.DELETED | ObjectAccessStatus.ARCHIVED:
                raise ResourceNotFoundError(
                    f"account is not in active state, account_id: {account_id}, organization_id: {organization_id}"
                )
            case _ as never:
                assert_never(never)

        updated_account = await self.account_repository.update_by_tenanted_primary_key(
            table_model=Account,
            organization_id=organization_id,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            primary_key_to_value={
                "id": account_id,
            },
            column_to_update=column_to_update,
            column_condition=column_condition,
        )

        if not updated_account:
            raise ConcurrentModificationError(
                f"account: {account_id}, organization_id: {organization_id} is being modified by another process"
            )

        return await self.account_service.get_account_v2(
            account_id=account_id, organization_id=organization_id
        )

    async def free_lock_for_integrity_job(
        self, *, job_id: UUID, account_id: UUID, user_id: UUID, organization_id: UUID
    ) -> AccountV2:
        account = await self.account_service.get_account_v2(
            account_id=account_id, organization_id=organization_id
        )

        updated_job_ids = list(set(account.integrity_job_started_by_job_ids) - {job_id})
        column_to_update = AccountUpdate(
            integrity_job_started_at=None if not updated_job_ids else UNSET,
            integrity_job_started_by_user_id=None if not updated_job_ids else UNSET,
            integrity_job_started_by_job_ids=updated_job_ids,
            updated_by_user_id=user_id,
        )

        updated_account = await self.account_repository.update_by_tenanted_primary_key(
            table_model=Account,
            organization_id=organization_id,
            exclude_deleted_or_archived=False,
            exclude_locked_by_integrity_jobs=False,
            primary_key_to_value={
                "id": account_id,
            },
            column_to_update=column_to_update,
            column_condition=AccountUpdateCondition(
                integrity_job_started_at=account.integrity_job_started_at,
                integrity_job_started_by_user_id=account.integrity_job_started_by_user_id,
                integrity_job_started_by_job_ids=account.integrity_job_started_by_job_ids,
            ),
        )

        if not updated_account:
            raise ConcurrentModificationError(
                f"account: {account_id}, organization_id: {organization_id} is being modified by another process"
            )
        return await self.account_service.get_account_v2(
            account_id=account_id, organization_id=organization_id
        )


def get_account_data_integrity_service(
    db_engine: DatabaseEngine,
) -> AccountDataIntegrityService:
    return AccountDataIntegrityService(db_engine=db_engine)
