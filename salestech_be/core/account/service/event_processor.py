from typing import cast
from uuid import uuid4

from salestech_be.common.events import (
    DomainEnrichedCDCEvent,
    EnrichedCDCEventProcessor,
)
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.core.account.service.account_query_service import AccountQueryService
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.ai.event_handlers.pipeline_intel import pipeline_intel_handler
from salestech_be.core.logical_propagation.service.trigger import (
    LogicalPropagationTriggerService,
)
from salestech_be.core.logical_propagation.types import CoreObjectChangeEvent
from salestech_be.core.research_agent.research_cdc_service import (
    company_research_handler,
)
from salestech_be.db.models.account import Account
from salestech_be.integrations.kafka.types import (
    AccountView,
    CDCObject,
    CDCObjectState,
)
from salestech_be.ree_logging import get_logger
from salestech_be.search.indexing.cdc_event_handlers import (
    index_account_handler,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import one_row_only

logger = get_logger(__name__)


class AccountCDCEventProcessor(EnrichedCDCEventProcessor[Account, AccountV2]):
    def __init__(
        self,
        account_service: AccountService,
        logical_propagation_trigger_service: LogicalPropagationTriggerService,
        account_query_service: AccountQueryService,
    ):
        self.account_service = account_service
        self.logical_propagation_trigger_service = logical_propagation_trigger_service
        self.account_query_service = account_query_service

    def get_table_model(self, event: CDCObject) -> Account:
        return Account.model_validate(event.model_dump(exclude={"view_model"}))

    async def get_domain_model(
        self, event: CDCObject, event_state: CDCObjectState
    ) -> AccountV2:
        account_view = cast(AccountView, event)
        if event_state == CDCObjectState.BEFORE:
            return one_row_only(
                await self.account_query_service._enrich_account_v2(  # noqa: SLF001
                    db_accounts=[self.get_table_model(event)],
                    organization_id=account_view.organization_id,
                )
            )

        return await self.account_query_service.get_account_v2(
            account_id=account_view.id,
            organization_id=account_view.organization_id,
        )

    async def process_domain_enriched_event(
        self, event: DomainEnrichedCDCEvent[Account, AccountV2]
    ) -> AccountV2:
        logger.bind(event_before=event.before, event_after=event.after).info(
            "Processing account event"
        )

        await (
            self.logical_propagation_trigger_service.process_core_object_changes_event(
                event=CoreObjectChangeEvent(
                    idempotent_key=str(uuid4()),
                    event_time=zoned_utc_now(),
                    organization_id=event.after.organization_id,
                    entity_type=StdObjectIdentifiers.account,
                    entity_id=event.after.id,
                    before_domain_model=event.before_domain_model,
                    current_domain_model=event.current_domain_model,
                )
            )
        )

        if settings.enable_research_agent_on_company:
            await company_research_handler(event)
        if settings.enable_search_indexing:
            await index_account_handler(event)
        if event.before_domain_model:
            await self.account_service.send_account_change_notification(
                event.before_domain_model, event.current_domain_model
            )

        await pipeline_intel_handler(event)

        return event.current_domain_model
