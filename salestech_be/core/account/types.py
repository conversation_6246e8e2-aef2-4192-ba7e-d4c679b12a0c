from __future__ import annotations

import uuid
from collections.abc import Mapping

from pydantic import BaseModel, NonNegativeInt

from salestech_be.common.type.metadata.field.field_value import (
    FieldValueOrAny,
)
from salestech_be.common.type.numbers import NonNegativeDecimal
from salestech_be.core.common.types import Address
from salestech_be.db.models.account import AccountStatus
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.util.pydantic_types.str import SchemeOptionalHttpUrlStr
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class CreateAccountRequest(BaseModel):
    display_name: str
    owner_user_id: uuid.UUID
    official_website: SchemeOptionalHttpUrlStr | None = None
    domain_name: str | None = None
    description: str | None = None
    keyword_list: list[str] | None = None
    category_list: list[str] | None = None  # This is displayed in FE as "industry"
    technology_list: list[str] | None = None
    estimated_annual_revenue: NonNegativeDecimal | None = None
    estimated_employee_count: NonNegativeInt | None = None
    linkedin_url: SchemeOptionalHttpUrlStr | None = None
    facebook_url: SchemeOptionalHttpUrlStr | None = None
    zoominfo_url: SchemeOptionalHttpUrlStr | None = None
    x_url: SchemeOptionalHttpUrlStr | None = None
    address: Address | None = None
    custom_field_data: Mapping[uuid.UUID, FieldValueOrAny] | None = None
    status: AccountStatus = AccountStatus.TARGET
    participant_user_id_list: list[uuid.UUID] | None = None
    created_at: ZoneRequiredDateTime | None = None
    created_source: CreatedSource | None = None
    # TODO: Make this more type specific later using Object Identifiers
    csv_type: str | None = None
    company_id: uuid.UUID | None = None

    def has_official_website(self) -> bool:
        """Check if the account has an official website and is not a LinkedIn URL"""
        if not self.official_website:
            return False

        # Normalize the URL by removing protocol and www
        url = self.official_website.lower()
        if url.startswith(("http://", "https://")):
            url = url.split("://", 1)[1]
        url = url.removeprefix("www.")

        # Check if it starts with linkedin.com
        return not url.startswith("linkedin.com")


class ImportAccountRequest(BaseModel):
    file_id: uuid.UUID
