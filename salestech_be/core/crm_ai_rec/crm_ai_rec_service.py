from asyncio import Task, TaskGroup
from collections import defaultdict
from collections.abc import Mapping
from typing import Annotated, Any, Literal
from uuid import UUID

from fastapi.params import Depends
from pydantic import BaseModel

from salestech_be.common.exception.exception import (
    IllegalStateError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.metadata.common import StandardObjectIdentifier
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.common.util import BoundedTaskGroup
from salestech_be.core.common.ai_rec_aware_domain_object_service import (
    BaseAIRecAwareCreateService,
)
from salestech_be.core.crm_ai_rec.service_api_schema import (
    DomainObjectIdWIthPreview,
    DomainObjectPatchPreview,
    DomainObjectPreview,
    FieldLevelPatchPreview,
    ListActiveCrmAiRecsBySourceRequest,
    ListActiveCrmAiRecsBySourceResponse,
    ListCrmObjectCreationRecsMultiRequest,
    ListCrmObjectCreationRecsRequest,
)
from salestech_be.core.pipeline.service.contact_pipeline_role_ai_rec_service import (
    ContactPipelineRoleAIRecService,
    get_contact_pipeline_role_ai_rec_service,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_services.competitor_service import (
    CompetitorService,
    get_competitor_service,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_services.decision_criteria_item_service import (
    DecisionCriteriaItemService,
    get_decision_criteria_item_service,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_services.decision_process_item_service import (
    DecisionProcessItemService,
    get_decision_process_item_service,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_services.identifed_pain_service import (
    IdentifiedPainItemService,
    get_identified_pain_item_service,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_services.metric_item_service import (
    MetricItemService,
    get_metric_item_service,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_services.paper_process_item_service import (
    PaperProcessItemService,
    get_paper_process_item_service,
)
from salestech_be.db.dao.crm_ai_rec_repository import CrmAIRecRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_ai_rec import (
    CrmObjectAiRec,
    CrmPropertyAiRec,
    CrmPropertyMetadata,
    ParentRecordIds,
)
from salestech_be.ree_logging import get_logger

logger = get_logger()


class CrmAIRecService:
    def __init__(
        self,
        crm_ai_rec_repository: Annotated[CrmAIRecRepository, Depends()],
        identified_pain_item_service: Annotated[IdentifiedPainItemService, Depends()],
        decision_criteria_item_service: Annotated[
            DecisionCriteriaItemService, Depends()
        ],
        decision_process_item_service: Annotated[DecisionProcessItemService, Depends()],
        paper_process_item_service: Annotated[PaperProcessItemService, Depends()],
        competitor_service: Annotated[CompetitorService, Depends()],
        metric_item_service: Annotated[MetricItemService, Depends()],
        contact_pipeline_role_ai_rec_service: Annotated[
            ContactPipelineRoleAIRecService, Depends()
        ],
    ):
        self.crm_ai_rec_repository = crm_ai_rec_repository
        self._sobject_name_to_service_map: Mapping[  # type: ignore[explicit-any]
            StdObjectIdentifiers,
            BaseAIRecAwareCreateService[Any, Any, Any],
        ] = {
            identified_pain_item_service.std_object_identifier(): identified_pain_item_service,
            decision_criteria_item_service.std_object_identifier(): decision_criteria_item_service,
            decision_process_item_service.std_object_identifier(): decision_process_item_service,
            paper_process_item_service.std_object_identifier(): paper_process_item_service,
            competitor_service.std_object_identifier(): competitor_service,
            metric_item_service.std_object_identifier(): metric_item_service,
            contact_pipeline_role_ai_rec_service.std_object_identifier(): contact_pipeline_role_ai_rec_service,
        }

    def ai_rec_create_service_for_sobject_name(  # type: ignore[explicit-any]
        self,
        sobject_name: StdObjectIdentifiers,
    ) -> BaseAIRecAwareCreateService[Any, Any, Any] | None:
        return self._sobject_name_to_service_map.get(sobject_name)

    async def list_active_crm_ai_recs_by_source(  # noqa: C901
        self,
        *,
        organization_id: UUID,
        request: ListActiveCrmAiRecsBySourceRequest,
    ) -> ListActiveCrmAiRecsBySourceResponse:
        db_source: Literal["EMAIL", "MEETING"]
        match request.source_object_identifier.object_name:
            case StdObjectIdentifiers.global_message:
                db_source = "EMAIL"
            case StdObjectIdentifiers.meeting:
                db_source = "MEETING"
            case _:
                raise InvalidArgumentError(
                    f"Invalid source object type: {request.source_object_identifier.object_name}"
                )

        async with TaskGroup() as tg:
            creation_recs_task = tg.create_task(
                self.crm_ai_rec_repository.list_active_crm_object_ai_recs_by_source(
                    organization_id=organization_id,
                    source=db_source,
                    source_id=request.source_record_id,
                )
            )
            patch_recs_task = tg.create_task(
                self.crm_ai_rec_repository.list_active_crm_property_ai_recs_by_source(
                    organization_id=organization_id,
                    source=db_source,
                    source_id=request.source_record_id,
                )
            )

        db_creation_recs: list[CrmObjectAiRec] = creation_recs_task.result()
        db_patch_recs: list[CrmPropertyAiRec] = patch_recs_task.result()

        pending_creation_preview: list[DomainObjectPreview] = []
        accepted_creation_preview: list[DomainObjectIdWIthPreview] = []
        for db_creation_rec in db_creation_recs:
            if db_creation_rec.created_record_id is None:
                pending_creation_preview.append(
                    self.convert_crm_object_ai_rec_to_domain_object_preview(
                        db_creation_rec
                    )
                )
            else:
                accepted_creation_preview.append(
                    DomainObjectIdWIthPreview(
                        object_identifier=StandardObjectIdentifier(
                            object_name=db_creation_rec.sobject_name
                        ),
                        record_id=db_creation_rec.created_record_id,
                        ai_rec_preview=self.convert_crm_object_ai_rec_to_domain_object_preview(
                            db_creation_rec
                        ),
                    )
                )

        db_patch_recs_by_object_name_record_id_field_path: dict[
            str, dict[UUID, dict[str, CrmPropertyAiRec]]
        ] = {}
        for db_patch_rec in db_patch_recs:
            object_group: dict[UUID, dict[str, CrmPropertyAiRec]] = (
                db_patch_recs_by_object_name_record_id_field_path.setdefault(
                    db_patch_rec.sobject_name, {}
                )
            )
            field_path_group: dict[str, CrmPropertyAiRec] = object_group.setdefault(
                db_patch_rec.record_id, {}
            )
            if (
                existing := field_path_group.get(db_patch_rec.sobject_field_path)
            ) and existing.created_at > db_patch_rec.created_at:
                continue
            field_path_group[db_patch_rec.sobject_field_path] = db_patch_rec

        db_patch_recs_by_object_name_record_id: dict[
            str, dict[UUID, list[CrmPropertyAiRec]]
        ] = {
            object_name: {
                record_id: list(field_path_group.values())
                for record_id, field_path_group in object_group.items()
            }
            for object_name, object_group in db_patch_recs_by_object_name_record_id_field_path.items()
        }

        pending_patch_preview: list[DomainObjectPatchPreview] = []
        accepted_patch_preview: list[DomainObjectPatchPreview] = []
        for (
            object_name,
            record_id_group,
        ) in db_patch_recs_by_object_name_record_id.items():
            for record_id, ai_recs in record_id_group.items():
                object_identifier = StandardObjectIdentifier(object_name=object_name)
                pending_recs = [
                    ai_rec for ai_rec in ai_recs if ai_rec.accepted_at is None
                ]
                accepted_recs = [
                    ai_rec for ai_rec in ai_recs if ai_rec.accepted_at is not None
                ]
                if pending_recs:
                    pending_patch_preview.append(
                        DomainObjectPatchPreview(
                            object_identifier=object_identifier,
                            record_id=record_id,
                            preview_data=CrmPropertyAiRec.merge_to_generic_map(
                                pending_recs
                            ),
                            field_level_preview=[
                                FieldLevelPatchPreview(
                                    field_path=ai_rec.sobject_field_path_tuple,
                                    value=ai_rec.rec_value.to_generic_value()
                                    if ai_rec.rec_value
                                    else None,
                                    ai_rec_id=ai_rec.id,
                                    citation_ids=ai_rec.rec_citation_ids or [],
                                    rec_type=ai_rec.rec_type,
                                )
                                for ai_rec in pending_recs
                            ],
                        )
                    )
                if accepted_recs:
                    accepted_patch_preview.append(
                        DomainObjectPatchPreview(
                            object_identifier=object_identifier,
                            record_id=record_id,
                            preview_data=CrmPropertyAiRec.merge_to_generic_map(
                                accepted_recs
                            ),
                            field_level_preview=[
                                FieldLevelPatchPreview(
                                    field_path=ai_rec.sobject_field_path_tuple,
                                    value=ai_rec.rec_value.to_generic_value()
                                    if ai_rec.rec_value
                                    else None,
                                    ai_rec_id=ai_rec.id,
                                    citation_ids=ai_rec.rec_citation_ids or [],
                                    rec_type=ai_rec.rec_type,
                                )
                                for ai_rec in accepted_recs
                            ],
                        )
                    )

        return ListActiveCrmAiRecsBySourceResponse(
            preview_from_pending_patch_rec=pending_patch_preview,
            preview_from_accepted_patch_rec=accepted_patch_preview,
            preview_from_pending_creation_rec=pending_creation_preview,
            preview_from_accepted_creation_rec=accepted_creation_preview,
        )

    async def create_object_ai_rec_from_create_request(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        sobject_name: StdObjectIdentifiers,
        create_request: BaseModel,
        parent_record_ids: ParentRecordIds | None,
        citation_ids: list[UUID] | None,
    ) -> CrmObjectAiRec:
        ai_rec_create_service = self.ai_rec_create_service_for_sobject_name(
            sobject_name
        )
        if ai_rec_create_service is None:
            raise ValueError(f"No service found for sobject name: {sobject_name}")
        return await ai_rec_create_service.create_object_ai_rec_from_create_request(
            organization_id=organization_id,
            user_id=user_id,
            parent_record_ids=parent_record_ids,
            create_request=create_request,
            citation_ids=citation_ids,
        )

    async def list_crm_object_creation_recs(
        self,
        *,
        organization_id: UUID,
        request: ListCrmObjectCreationRecsMultiRequest,
    ) -> list[DomainObjectPreview]:
        tasks: list[Task[list[DomainObjectPreview]]] = []
        async with BoundedTaskGroup(max_parallelism=5) as tg:
            for single_request in request.requests:
                tasks.append(
                    tg.create_task(
                        self.list_crm_object_creation_recs_by_single_request(
                            organization_id=organization_id,
                            request=single_request,
                            max_result=request.max_result_per_request,
                        )
                    )
                )
        return [result for task in tasks for result in task.result()]

    async def list_crm_object_creation_recs_by_single_request(
        self,
        organization_id: UUID,
        request: ListCrmObjectCreationRecsRequest,
        max_result: int,
    ) -> list[DomainObjectPreview]:
        standard_object_identifier = request.object_identifier
        if standard_object_identifier.object_name not in StdObjectIdentifiers:
            raise ResourceNotFoundError(
                f"Invalid sobject name: {standard_object_identifier.object_name}"
            )
        sobject = StdObjectIdentifiers(standard_object_identifier.object_name)
        sobject_name_and_parent_record_ids: dict[
            StdObjectIdentifiers, set[ParentRecordIds]
        ] = defaultdict(set)
        sobject_name_and_parent_record_ids[sobject].update(request.parent_record_ids)

        ai_recs = await self.crm_ai_rec_repository.list_crm_object_ai_rec_by_sobject_name_and_parent_record_ids(
            organization_id=organization_id,
            sobject_name_and_parent_record_ids=sobject_name_and_parent_record_ids,
            limit=max_result,
        )
        return [
            self.convert_crm_object_ai_rec_to_domain_object_preview(ai_rec)
            for ai_rec in ai_recs
            if (ai_rec.created_record_id is None) and (ai_rec.rejected_at is None)
        ]

    async def reject_crm_object_creation_rec(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        standard_object_identifier: StandardObjectIdentifier,
        ai_rec_ids: set[UUID],
    ) -> None:
        await self.crm_ai_rec_repository.reject_crm_object_ai_rec(
            organization_id=organization_id,
            user_id=user_id,
            sobject_name=StdObjectIdentifiers(standard_object_identifier.object_name),
            ai_rec_ids=ai_rec_ids,
        )

    def convert_crm_object_ai_rec_to_domain_object_preview(
        self,
        ai_rec: CrmObjectAiRec,
    ) -> DomainObjectPreview:
        citation_ids = ai_rec.rec_citation_ids or []
        parent_record_ids = ai_rec.parent_record_ids
        sobject_name = StdObjectIdentifiers(ai_rec.sobject_name)
        ai_rec_create_service = self.ai_rec_create_service_for_sobject_name(
            sobject_name
        )
        if ai_rec_create_service is None:
            raise ValueError(f"No service found for sobject name: {sobject_name}")
        preview = ai_rec_create_service.convert_create_req_to_domain_model_preview(
            create_req=ai_rec_create_service.create_request_type().model_validate(
                ai_rec.rec_value
            ),
        )
        if not isinstance(preview, ShapeConstrainedModel):
            raise IllegalStateError(
                f"Preview is not a ShapeConstrainedModel: {preview}"
            )
        return DomainObjectPreview(
            object_identifier=sobject_name.identifier,
            ai_rec_id=ai_rec.id,
            citation_ids=citation_ids,
            parent_record_ids=parent_record_ids,
            preview_data=preview.model_dump(mode="json"),
            created_at=ai_rec.created_at,
        )

    async def find_crm_property_metadata(
        self,
        *,
        organization_id: UUID,
        sobject_name: str,
        record_id: UUID,
        sobject_field_path: str,
    ) -> CrmPropertyMetadata | None:
        return await self.crm_ai_rec_repository.find_crm_property_metadata(
            organization_id=organization_id,
            sobject_name=sobject_name,
            record_id=record_id,
            sobject_field_path=sobject_field_path,
        )


class SingletonCrmAIRecService(Singleton, CrmAIRecService):
    pass


def get_crm_ai_rec_service(db_engine: DatabaseEngine) -> CrmAIRecService:
    if SingletonCrmAIRecService.has_instance():
        return SingletonCrmAIRecService.get_singleton_instance()
    else:
        return SingletonCrmAIRecService(
            crm_ai_rec_repository=CrmAIRecRepository(engine=db_engine),
            identified_pain_item_service=get_identified_pain_item_service(db_engine),
            decision_criteria_item_service=get_decision_criteria_item_service(
                db_engine=db_engine
            ),
            decision_process_item_service=get_decision_process_item_service(
                db_engine=db_engine
            ),
            paper_process_item_service=get_paper_process_item_service(
                db_engine=db_engine
            ),
            competitor_service=get_competitor_service(db_engine=db_engine),
            metric_item_service=get_metric_item_service(db_engine=db_engine),
            contact_pipeline_role_ai_rec_service=get_contact_pipeline_role_ai_rec_service(
                db_engine=db_engine
            ),
        )
