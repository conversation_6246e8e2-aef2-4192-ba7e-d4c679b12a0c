from temporalio import activity, workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    from datetime import timed<PERSON><PERSON>
    from uuid import UUID

    from salestech_be.core.ai.opportuntity_stages.activities.generate_criteria import (
        generate_criteria,
    )
    from salestech_be.core.ai.opportuntity_stages.activities.generate_sales_action_role_classification import (
        generate_contact_pipeline_role_classification,
        generate_sales_action_classification,
    )
    from salestech_be.core.ai.workflows.schema import (
        PipelineUpdateInput,
        PipelineUpdateReason,
    )
    from salestech_be.core.meeting.service.meeting_transcript_agent_service import (
        MeetingTranscriptAgentService,
        meeting_transcript_agent_service_from_engine,
    )
    from salestech_be.core.meeting.types.meeting_query_types import (
        Meeting<PERSON><PERSON>er,
        MeetingQueryResponse,
        TranscriptCitation,
    )
    from salestech_be.core.stage_criteria.constants import (
        MEETING_DELIMITER,
        MEETING_DELIMITER_END,
    )
    from salestech_be.integrations.temporal.config import AI_TASK_QUEUE
    from salestech_be.settings import settings
    from salestech_be.temporal.database import (
        get_or_init_db_engine,  # fmt: skip  # tach-ignore
    )


@workflow.defn
class PipelineUpdateWorkflow:
    def __init__(self) -> None:
        self.__input_queue: list[PipelineUpdateInput] = []

    @workflow.signal
    async def enqueue_input(self, workflow_input: PipelineUpdateInput) -> None:
        self.__input_queue.append(workflow_input)

    async def _process_single_input(
        self,
        workflow_input: PipelineUpdateInput,
    ) -> None:
        reason = workflow_input.reason
        if (
            reason == PipelineUpdateReason.SALES_ACTION_ROLE_CLASSIFICATION
            and workflow_input.source_object
        ):
            await workflow.execute_activity(
                generate_sales_action_classification,
                args=(
                    workflow_input.organization_id,
                    workflow_input.pipeline_id,
                    workflow_input.source_object,
                ),
                task_queue=AI_TASK_QUEUE,
                start_to_close_timeout=timedelta(minutes=15),
                schedule_to_close_timeout=timedelta(minutes=20),
                retry_policy=RetryPolicy(
                    maximum_attempts=3,
                    initial_interval=timedelta(seconds=60),
                    backoff_coefficient=3.0,
                ),
            )
            return

        multi_meeting_transcript = await workflow.execute_activity(
            generate_multi_meeting_transcript,
            args=(
                workflow_input.organization_id,
                workflow_input.pipeline_id,
            ),
            task_queue=AI_TASK_QUEUE,
            start_to_close_timeout=timedelta(minutes=5),
            schedule_to_close_timeout=timedelta(minutes=10),
            retry_policy=RetryPolicy(
                maximum_attempts=3,
                initial_interval=timedelta(seconds=60),
                backoff_coefficient=3.0,
            ),
        )
        if not multi_meeting_transcript:
            return
        if reason == PipelineUpdateReason.CONTACT_PIPELINE_ROLE_CLASSIFICATION:
            await workflow.execute_activity(
                generate_contact_pipeline_role_classification,
                args=(
                    workflow_input.organization_id,
                    workflow_input.pipeline_id,
                    multi_meeting_transcript,
                ),
                task_queue=AI_TASK_QUEUE,
                start_to_close_timeout=timedelta(minutes=15),
                schedule_to_close_timeout=timedelta(minutes=20),
                retry_policy=RetryPolicy(
                    maximum_attempts=3,
                    initial_interval=timedelta(seconds=60),
                    backoff_coefficient=3.0,
                ),
            )
            return

        # Last reason is criteria update
        await workflow.execute_activity(
            generate_criteria,
            args=(
                workflow_input.organization_id,
                workflow_input.pipeline_id,
                settings.intel_hardcoded_user_id,
                multi_meeting_transcript,
                workflow_input.config_type,
            ),
            task_queue=AI_TASK_QUEUE,
            start_to_close_timeout=timedelta(minutes=15),
            schedule_to_close_timeout=timedelta(minutes=20),
            retry_policy=RetryPolicy(
                maximum_attempts=3,
                initial_interval=timedelta(seconds=60),
                backoff_coefficient=3.0,
            ),
        )

    async def _process_queue(self) -> None:
        while len(self.__input_queue) > 0:
            next_input = self.__input_queue.pop(0)
            await self._process_single_input(next_input)

    @workflow.run
    async def run(self) -> None:
        # Wait for the first input to be enqueued from signal_with_start
        await workflow.wait_condition(lambda: len(self.__input_queue) > 0)

        # Start processing the queue
        await self._process_queue()

    @staticmethod
    def get_workflow_id(
        pipeline_id: UUID,
        reason: PipelineUpdateReason,
    ) -> str:
        return f"pipeline-update-{reason.value}-{pipeline_id}"


@activity.defn
async def generate_multi_meeting_transcript(
    organization_id: UUID,
    pipeline_id: UUID,
) -> list[TranscriptCitation]:
    db_engine = await get_or_init_db_engine()
    meeting_transcript_agent_service: MeetingTranscriptAgentService = (
        meeting_transcript_agent_service_from_engine(engine=db_engine)
    )
    question = """
    What parts of this meeting transcript do you believe will be useful when trying to extract some criteria about the sales opportunity,
    classifying the sales action types of the meeting, or assigning some roles to the contacts associated with the sales opportunity?
    Include these parts in the citations field in order and explain why you believe these are the most important parts.
    """
    response: MeetingQueryResponse = (
        await meeting_transcript_agent_service.query_meetings(
            question=question,
            filters=MeetingFilter(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
            ),
        )
    )
    multi_meeting_transcript: list[TranscriptCitation] = []
    for query_result in response.results:
        multi_meeting_transcript.append(
            TranscriptCitation(
                text=f"{MEETING_DELIMITER} {query_result.meeting_id} ",
                speaker=None,
                line_number=-1,
                meeting_id=query_result.meeting_id,
            )
        )
        multi_meeting_transcript.extend(query_result.citations)
        multi_meeting_transcript.append(
            TranscriptCitation(
                text=MEETING_DELIMITER_END,
                speaker=None,
                line_number=-1,
                meeting_id=query_result.meeting_id,
            )
        )
    return multi_meeting_transcript
