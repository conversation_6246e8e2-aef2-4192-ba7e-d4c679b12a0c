import uuid
from enum import Str<PERSON>num
from typing import Any

from pydantic import BaseModel
from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.opportunity_stage_criteria.criteria_types import (
        CriteriaExtractionSourceObjectId,
    )


class MeetingTriggerPipelineIntelInput(BaseModel):
    organization_id: uuid.UUID
    pipeline_id: uuid.UUID
    account_id: uuid.UUID
    meeting_id: uuid.UUID | None  # The meeting that triggered the pipeline intel if any
    contact_id: uuid.UUID | None  # The contact that triggered the pipeline intel if any
    global_thread_id: (
        uuid.UUID | None
    )  # The global thread that triggered the pipeline intel if any
    langfuse_session_id: str


class IntelTriggerObjectType(StrEnum):
    """Possible object types that can trigger intel."""

    GLOBAL_THREAD = "global_thread"
    MEETING = "meeting"
    CONTACT = "contact"
    CALL = "call"
    OBJECTION = "objection"


class IntelInput(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    organization_id: uuid.UUID
    object_id: uuid.UUID  # The object that triggered the pipeline intel if any
    object_type: IntelTriggerObjectType
    pipeline_id: uuid.UUID | None
    account_ids: list[uuid.UUID] | None
    langfuse_session_id: str
    bypass_dedup: bool = False
    non_temporal: bool = False
    test_stub: None | Any = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
    output_path: str | None = None


class StageCriteriaType(StrEnum):
    """Possible criteria types."""

    DECISION_CRITERIA = "DECISION_CRITERIA"
    IDENTIFIED_PAIN = "IDENTIFIED_PAIN"
    METRIC = "METRIC"
    PAPER_PROCESS = "PAPER_PROCESS"
    COMPETITOR = "COMPETITOR"
    DECISION_PROCESS = "DECISION_PROCESS"


class StageCriteriaWorkflowInput(BaseModel):
    organization_id: uuid.UUID
    pipeline_id: uuid.UUID
    user_id: uuid.UUID
    source_object: CriteriaExtractionSourceObjectId


class SalesActionRoleClassificationInput(BaseModel):
    organization_id: uuid.UUID
    pipeline_id: uuid.UUID
    source_object: CriteriaExtractionSourceObjectId


class PipelineUpdateReason(StrEnum):
    """Reason for updating the pipeline."""

    CONTACT_PIPELINE_ROLE_CLASSIFICATION = "contact_pipeline_role_update"
    SALES_ACTION_ROLE_CLASSIFICATION = "sales_actions_update"
    CRITERIA_UPDATE = "criteria_update"


class PipelineUpdateInput(BaseModel):
    organization_id: uuid.UUID
    pipeline_id: uuid.UUID
    source_object: CriteriaExtractionSourceObjectId | None = None
    reason: PipelineUpdateReason
    config_type: StageCriteriaType | None = None


class CDCEventEnum(StrEnum):
    GLOBAL_THREAD = "global_thread"


class GlobalThreadCDCEventWorkflowInput(BaseModel):
    global_thread_id: uuid.UUID
    organization_id: uuid.UUID
