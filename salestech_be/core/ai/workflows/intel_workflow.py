from temporalio import workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    import uuid
    from datetime import timedelta

    from salestech_be.core.ai.tasks.activities.generate_intel_tasks import (
        generate_intel_tasks,
    )
    from salestech_be.core.ai.workflows.schema import (
        IntelInput,
        IntelTriggerObjectType,
    )
    from salestech_be.integrations.temporal.config import AI_TASK_QUEUE


@workflow.defn
class IntelWorkflow:
    @workflow.run
    async def run(self, workflow_input: IntelInput) -> None:
        intel_tasks_task = None
        object_id = workflow_input.object_id
        object_type = workflow_input.object_type

        langfuse_session_id = f"intel:{object_type.value}:{object_id}"

        intel_input = IntelInput(
            organization_id=workflow_input.organization_id,
            pipeline_id=workflow_input.pipeline_id,
            account_ids=workflow_input.account_ids,
            object_id=object_id,
            object_type=object_type,
            langfuse_session_id=langfuse_session_id,
        )

        intel_tasks_task = workflow.execute_activity(
            generate_intel_tasks,
            intel_input,
            task_queue=AI_TASK_QUEUE,
            retry_policy=RetryPolicy(
                maximum_attempts=3,
                initial_interval=timedelta(seconds=60),
                backoff_coefficient=3.0,
            ),
            start_to_close_timeout=timedelta(seconds=900),
        )

        # Make sure the task generation is complete before we return
        if intel_tasks_task:
            await intel_tasks_task

    @staticmethod
    def get_workflow_id(
        organization_id: uuid.UUID,
        object_id: uuid.UUID,
        object_type: IntelTriggerObjectType,
    ) -> str:
        return f"intel-{object_id}-{object_type.value}-{organization_id}"
