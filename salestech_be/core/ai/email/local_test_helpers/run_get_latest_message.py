import asyncio
import uuid
from datetime import datetime

import pytz

from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.models.message import Message
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)


async def run_test_get_latest_message_from_global_thread(
    global_thread_id: uuid.UUID,
    organization_id: uuid.UUID,
) -> Message | None:
    engine = await get_or_init_db_engine()
    thread_repository = ThreadRepository(engine=engine)
    messages = await thread_repository.list_messages_by_global_thread_id(
        global_thread_id=global_thread_id,
        organization_id=organization_id,
    )

    messages = sorted(
        messages,
        key=lambda x: x.send_at.replace(tzinfo=pytz.UTC)
        if x.send_at
        else datetime.min.replace(tzinfo=pytz.UTC),
        reverse=True,
    )

    # Get latest message from global thread
    return messages[0] if len(messages) > 0 else None


if __name__ == "__main__":
    asyncio.run(
        run_test_get_latest_message_from_global_thread(
            global_thread_id=uuid.UUID("1e6ca277-40bd-4f43-a9ee-b9471b391795"),
            organization_id=uuid.UUID("f79f8210-d6f3-44ea-b510-62da85ab273d"),
        )
    )
