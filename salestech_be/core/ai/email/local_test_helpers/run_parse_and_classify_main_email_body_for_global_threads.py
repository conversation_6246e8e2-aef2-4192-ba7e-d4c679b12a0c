#!/usr/bin/env python3

import asyncio
import uuid

from salestech_be.core.ai.email.activities import (
    parse_and_classify_main_email_body_for_global_threads,
)
from salestech_be.core.email.global_email.global_thread_query_service import (
    get_global_thread_query_service,
)
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


async def main() -> None:
    # Initialize DB engine
    db_engine = await get_or_init_db_engine()

    # Create input
    organization_id = uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5")
    global_thread_query_service = get_global_thread_query_service(db_engine=db_engine)
    global_threads = await global_thread_query_service.list_global_threads(
        user_id=uuid.UUID("3b2f46a7-e4b3-42f1-a7f4-a232eb5dcfaa"),
        organization_id=organization_id,
        only_include_thread_ids={uuid.UUID("32e1a9c7-96cf-4604-8403-da1f9d2ef2c1")},
    )

    global_thread = global_threads[0]

    # Run the activity directly
    logger.info(f"Global Thread: {global_thread}")
    logger.info(f"Latest message id: {global_thread.messages[0].id}")
    logger.info(f"Latest message: {global_thread.messages[0].body_text}")
    await parse_and_classify_main_email_body_for_global_threads(
        global_thread_id_list=[global_thread.id],
        organization_id=organization_id,
    )


if __name__ == "__main__":
    asyncio.run(main())
