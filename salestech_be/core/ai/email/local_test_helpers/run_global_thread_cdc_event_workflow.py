from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    import asyncio
    import uuid

    from salestech_be.core.ai.email.workflows.global_thread_cdc_event_workflow import (
        GlobalThreadCDCEventWorkflow,
        GlobalThreadCDCEventWorkflowInput,
    )
    from salestech_be.integrations.temporal.client import get_temporal_client
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_QUEUE
    from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


async def run_workflow(workflow_input: GlobalThreadCDCEventWorkflowInput) -> str:
    """Execute the close existing tasks workflow."""
    temporal_client = await get_temporal_client()
    await temporal_client.start_workflow(
        GlobalThreadCDCEventWorkflow.run,
        id=GlobalThreadCDCEventWorkflow.get_workflow_id(
            workflow_input.global_thread_id,
            workflow_input.organization_id,
        ),
        task_queue=DEFAULT_TASK_QUEUE,
        start_signal="enqueue_input",
        start_signal_args=[workflow_input],
    )
    return "running workflow..."


if __name__ == "__main__":
    logger.info("Starting global thread cdc event workflow")
    # Test organization ID
    workflow_input = GlobalThreadCDCEventWorkflowInput(
        global_thread_id=uuid.UUID("1e6ca277-40bd-4f43-a9ee-b9471b391795"),
        organization_id=uuid.UUID("f79f8210-d6f3-44ea-b510-62da85ab273d"),
    )

    asyncio.run(run_workflow(workflow_input))
