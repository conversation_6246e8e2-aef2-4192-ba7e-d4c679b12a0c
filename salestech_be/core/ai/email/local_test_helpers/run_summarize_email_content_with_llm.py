#!/usr/bin/env python3

import asyncio
import uuid
from datetime import UTC, datetime

from markdownify import markdownify as md

from salestech_be.core.ai.email.summarize_email_content_with_llm import (
    summarize_email_content_with_llm,
)
from salestech_be.core.email.global_email.global_thread_query_service import (
    get_global_thread_query_service,
)
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


async def main() -> None:
    # Initialize DB engine
    db_engine = await get_or_init_db_engine()

    organization_id = uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5")
    global_thread_query_service = get_global_thread_query_service(db_engine=db_engine)
    global_threads = await global_thread_query_service.list_global_threads(
        user_id=uuid.UUID("3b2f46a7-e4b3-42f1-a7f4-a232eb5dcfaa"),
        organization_id=organization_id,
        only_include_thread_ids={uuid.UUID("9783a042-044d-48a4-b850-ab8ba6e8759d")},
    )

    global_thread = global_threads[0]
    messages = sorted(
        global_thread.messages,
        key=lambda x: x.send_at if x.send_at else datetime.min.replace(tzinfo=UTC),
        reverse=True,
    )

    all_messages = "\n\n".join(
        [
            f"""
        Message:
        Subject: {m.subject}
        From: {m.send_from}
        To: {m.to}
        CC: {m.cc}
        Body: {md(m.body_html if m.body_html else m.body_text)}
        Received At: {m.received_date}
    """
            for m in messages
        ]
    )

    result = await summarize_email_content_with_llm(
        email_content=all_messages,
        organization_id=organization_id,
    )
    logger.info(f"Activity result: {result}")


if __name__ == "__main__":
    asyncio.run(main())
