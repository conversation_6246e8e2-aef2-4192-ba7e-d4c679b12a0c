from uuid import UUID

from temporalio import activity

from salestech_be.core.ai.email.activities.classify_message_and_update_metadata import (
    classify_message_and_update_metadata,
)
from salestech_be.core.ai.email.activities.get_latest_message_from_global_thread import (
    get_latest_message_from_global_thread_id,
)
from salestech_be.core.ai.email.activities.parse_main_body_text_and_persist import (
    parse_main_body_text_and_persist,
)
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


@activity.defn
async def parse_and_classify_main_email_body_for_global_threads(
    global_thread_id_list: list[UUID],
    organization_id: UUID,
) -> None:
    for global_thread_id in global_thread_id_list:
        logger.info(f"Global Thread ID: {global_thread_id}")
        latest_message = await get_latest_message_from_global_thread_id(
            global_thread_id=global_thread_id,
            organization_id=organization_id,
        )
        if latest_message is None:
            continue

        logger.info(f"Latest Message: {latest_message}")
        logger.info(f"Latest Message ID: {latest_message.id}")
        logger.info(f"Latest Message Body Text: {latest_message.body_text}")

        await parse_main_body_text_and_persist(
            message=latest_message,
        )
        await classify_message_and_update_metadata(
            message=latest_message,
        )
