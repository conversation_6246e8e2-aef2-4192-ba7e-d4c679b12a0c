from datetime import datetime
from uuid import UUID

import pytz
from temporalio import activity

from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.workflows.schema import GlobalThreadCDCEventWorkflowInput
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.models.message import (
    Message,
)
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

langfuse_prompt_service = get_langfuse_prompt_service()


async def get_latest_message(
    messages: list[Message],
) -> Message | None:
    """Get the latest message from the list of messages."""
    messages = sorted(
        messages,
        key=lambda x: x.send_at.replace(tzinfo=pytz.utc)
        if x.send_at
        else datetime.min.replace(tzinfo=pytz.UTC),
        reverse=True,
    )

    return messages[0] if len(messages) > 0 else None


@activity.defn
async def get_latest_message_from_global_thread_id(
    global_thread_id: UUID,
    organization_id: UUID,
) -> Message | None:
    """Get the latest message from the global thread."""
    engine = await get_or_init_db_engine()
    thread_repository = ThreadRepository(engine=engine)
    messages = await thread_repository.list_messages_by_global_thread_id(
        global_thread_id=global_thread_id,
        organization_id=organization_id,
    )
    # Get latest message from global thread
    return await get_latest_message(
        messages=messages,
    )


@activity.defn
async def get_latest_message_from_global_thread(
    workflow_input: GlobalThreadCDCEventWorkflowInput,
) -> Message | None:
    """Get the latest message from the global thread."""
    return await get_latest_message_from_global_thread_id(
        global_thread_id=workflow_input.global_thread_id,
        organization_id=workflow_input.organization_id,
    )
