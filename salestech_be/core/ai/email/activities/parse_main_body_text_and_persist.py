import re

from bs4 import BeautifulSoup, Comment
from temporalio import activity

from salestech_be.core.ai.email.extract_main_email_content_with_llm import (
    extract_main_email_content_with_llm,
)
from salestech_be.core.ai.email.strip_html_tags_with_llm import strip_html_tags_with_llm
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.models.message import (
    Message,
)
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

langfuse_prompt_service = get_langfuse_prompt_service()

logger = get_logger(__name__)


@activity.defn
async def parse_main_body_text_and_persist(
    message: Message,
) -> str | None:
    """Parse the main body text and persist it."""
    engine = await get_or_init_db_engine()
    message_repository = MessageRepository(engine=engine)

    result = ""
    # if body_html is None, use body_text and just extract the main body text
    if len(message.body_text) > 0 and len(message.body_html) == 0:
        preprocessed_body_text = _preprocess_html(message.body_text)
        parsed_email_body_text = await extract_main_email_content_with_llm(
            email_content=preprocessed_body_text,
            organization_id=message.organization_id,
            langfuse_session_id=f"cdc:EMAIL:{message.id}",
        )
        await message_repository.update_email_message_main_body_text(
            message_id=message.id,
            main_body_text=parsed_email_body_text,
            organization_id=message.organization_id,
        )
        result = parsed_email_body_text

    # if body_html exists, use body_html and extract the main body html then strip the html tags
    # by default, we use body_html to extract the main body text as body html is source of truth
    elif len(message.body_html) > 0:
        preprocessed_body_html = _preprocess_html(message.body_html)
        parsed_email_body_html = await extract_main_email_content_with_llm(
            email_content=preprocessed_body_html,
            organization_id=message.organization_id,
            langfuse_session_id=f"cdc:EMAIL:{message.id}",
        )
        await message_repository.update_email_message_main_body_html(
            message_id=message.id,
            main_body_html=parsed_email_body_html,
            organization_id=message.organization_id,
        )
        parsed_email_body_text = await strip_html_tags_with_llm(
            email_content=parsed_email_body_html,
            organization_id=message.organization_id,
            langfuse_session_id=f"cdc:EMAIL:{message.id}",
        )
        await message_repository.update_email_message_main_body_text(
            message_id=message.id,
            main_body_text=parsed_email_body_text,
            organization_id=message.organization_id,
        )
        result = parsed_email_body_text
    else:
        # if body_html is empty, set main_body_text and main_body_html to empty string
        await message_repository.update_email_message_main_body_text(
            message_id=message.id,
            main_body_text=result,
            organization_id=message.organization_id,
        )
        await message_repository.update_email_message_main_body_html(
            message_id=message.id,
            main_body_html=result,
            organization_id=message.organization_id,
        )

    return result


def _preprocess_html(html_content: str) -> str:
    try:
        if not html_content:
            return ""

        # Handle base64 content first (only this specific case is regex)
        html_content = re.sub(
            r"data:[^;]+;base64,[a-zA-Z0-9+/]+=*", "[IMAGE]", html_content
        )
        html_content = re.sub(
            r"[a-zA-Z0-9+/]{500,}={0,2}", "[ENCODED_CONTENT]", html_content
        )

        # Parse HTML
        soup = BeautifulSoup(html_content, "html.parser")

        # Remove unwanted elements
        for element in soup.find_all(["script", "style", "meta", "link"]):
            element.decompose()

        # Remove comments
        for element in soup.find_all(string=lambda text: isinstance(text, Comment)):
            element.extract()

        # Remove tracking pixels and small images
        for img in soup.find_all("img"):
            if img.attrs.get("width") or img.attrs.get("height"):
                img.decompose()

        # Clean up whitespace
        return " ".join(str(soup).split()).strip()

    except Exception as e:
        logger.info(f"Error in _preprocess_html: {e!s}")
        return html_content or ""
