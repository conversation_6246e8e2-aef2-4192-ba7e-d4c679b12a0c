from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    import time

    from salestech_be.common.events import (
        DomainEnrichedCDCEvent,
    )
    from salestech_be.common.stats.metric import custom_metric
    from salestech_be.core.ai.email.workflows.global_thread_cdc_event_workflow import (
        GlobalThreadCDCEventWorkflow,
        GlobalThreadCDCEventWorkflowInput,
    )
    from salestech_be.core.email.global_email.global_thread_type import (
        GlobalThread as GlobalThreadDomainModel,
    )
    from salestech_be.db.models.global_thread import GlobalThread
    from salestech_be.integrations.temporal.client import get_temporal_client
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_QUEUE
    from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


async def start_global_thread_cdc_event_workflow(
    event: DomainEnrichedCDCEvent[GlobalThread, GlobalThreadDomainModel],
) -> None:
    """Start the workflow to process a global thread CDC event.

    Args:
        organization_id: The organization ID
        object_id: The ID of the object (meeting, email, etc)
        object_type: The type of object triggering the workflow
        account_id: Optional account ID
        pipeline_id: Optional pipeline ID
    """
    workflow_input = GlobalThreadCDCEventWorkflowInput(
        global_thread_id=event.after.id,
        organization_id=event.after.organization_id,
    )

    process_start_time = time.perf_counter()
    client = await get_temporal_client()
    custom_metric.timing(
        metric_name="cdc_event_processor_get_temporal_client",
        value=(time.perf_counter() - process_start_time) * 1000,
        tags=[
            "cdc_flow:start_process_global_thread_cdc_event_workflow",
        ],
    )

    workflow_id = GlobalThreadCDCEventWorkflow.get_workflow_id(
        global_thread_id=event.after.id,
        organization_id=event.after.organization_id,
    )

    logger.info(
        "Starting process global thread cdc event workflow",
        extra={
            "workflow_id": workflow_id,
            "global_thread_id": event.after.id,
            "organization_id": event.after.organization_id,
        },
    )

    process_start_time = time.perf_counter()
    try:
        await client.start_workflow(
            GlobalThreadCDCEventWorkflow.run,
            id=workflow_id,
            task_queue=DEFAULT_TASK_QUEUE,
            start_signal="enqueue_input",
            start_signal_args=[workflow_input],
        )
        custom_metric.timing(
            metric_name="process_global_thread_cdc_event_start_workflow",
            value=(time.perf_counter() - process_start_time) * 1000,
            tags=[
                "workflow:process_global_thread_cdc_event",
                "status:success",
            ],
        )
    except Exception as e:
        custom_metric.timing(
            metric_name="process_global_thread_cdc_event_start_workflow",
            value=(time.perf_counter() - process_start_time) * 1000,
            tags=[
                "workflow:process_global_thread_cdc_event",
                "status:error",
            ],
        )
        logger.error(
            "Failed to start process global thread cdc event workflow",
            exc_info=e,
            extra={
                "workflow_id": workflow_id,
                "global_thread_id": event.after.id,
                "organization_id": event.after.organization_id,
            },
        )
        raise
