import json
from uuid import UUID

from pydantic import BaseModel

from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    acompletion,
)
from salestech_be.core.ai.common.helpers.ree_llm_retry import (
    ReeLLMRetryError,
    ree_llm_retry,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest, PromptRole
from salestech_be.core.prompt.types import PromptUseCase
from salestech_be.db.models.message_metadata import MessageClassification
from salestech_be.ree_logging import get_logger

RETURN_ON_ATTEMPT_NUM = 2


class EmailClassification(BaseModel):
    """Response model for email classification."""

    category: MessageClassification
    confidence: float
    explanation: str


langfuse_prompt_service = get_langfuse_prompt_service()
logger = get_logger(__name__)


@ree_llm_retry(retry_delay=2)
async def classify_email_content_with_llm(
    email_content: str,
    organization_id: UUID,
    langfuse_session_id: str,
    *,
    attempt_num: int = 0,
    last_error: ReeLLMRetryError | None = None,
    all_errors: list[ReeLLMRetryError] | None = None,
) -> EmailClassification:
    """Classifies email content using LLM.
    Args:
        email_content: The email content to classify
        organization_id: The organization ID
        langfuse_session_id: Session ID for tracing

    Returns:
        EmailClassification containing category, confidence and explanation
    """
    prompt_variables = {
        "email": email_content,
    }

    prompt_obj = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.CLASSIFY_EMAIL,
            variables=prompt_variables,
        )
    )

    messages = prompt_obj.messages.copy()
    if last_error is not None:
        messages.append(
            {
                "role": PromptRole.USER,
                "content": f"The previous attempt failed with the following error: {last_error.message}. Please be extra cautious and ensure the response has the correct structure.",
            }
        )

    llm_response = await acompletion(
        model=prompt_obj.get_model(),
        messages=prompt_obj.messages,
        temperature=0,
        max_completion_tokens=1024,
        response_format=EmailClassification,
        metadata=LLMTraceMetadata(
            trace_name=PromptEnum.CLASSIFY_EMAIL,
            session_id=langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(organization_id),
                prompt_use_case=PromptUseCase.EMAIL_CLASSIFICATION,
            ),
        ),
    )

    if (
        not llm_response.choices
        or not llm_response.choices[0].message
        or not llm_response.choices[0].message.content
    ):
        if attempt_num < RETURN_ON_ATTEMPT_NUM:
            raise ReeLLMRetryError(message="No response from LLM")
        return EmailClassification(
            category=MessageClassification.OTHER,
            confidence=0,
            explanation="",
        )

    try:
        content = json.loads(llm_response.choices[0].message.content)
    except Exception as e:
        raise ReeLLMRetryError(message="Invalid response structure") from e

    classification = content.get("category")

    if classification not in [e.value for e in MessageClassification]:
        classification = MessageClassification.OTHER

    return EmailClassification(
        category=classification,
        confidence=content["confidence"],
        explanation=content["explanation"],
    )
