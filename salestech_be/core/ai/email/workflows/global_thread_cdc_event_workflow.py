from temporalio import workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    import uuid
    from datetime import timedelta

    from salestech_be.core.ai.email.activities import (
        classify_message_and_update_metadata,
        get_latest_message_from_global_thread,
        parse_main_body_text_and_persist,
    )
    from salestech_be.core.ai.workflows.schema import GlobalThreadCDCEventWorkflowInput
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_QUEUE
    from salestech_be.settings import settings


@workflow.defn
class GlobalThreadCDCEventWorkflow:
    def __init__(self) -> None:
        self._input_queue: list[GlobalThreadCDCEventWorkflowInput] = []

    @workflow.signal
    async def enqueue_input(
        self, workflow_input: GlobalThreadCDCEventWorkflowInput
    ) -> None:
        self._input_queue.append(workflow_input)

    async def _process_single_input(
        self, workflow_input: GlobalThreadCDCEventWorkflowInput
    ) -> None:
        message = await workflow.execute_activity(
            get_latest_message_from_global_thread,
            workflow_input,
            task_queue=DEFAULT_TASK_QUEUE,
            retry_policy=RetryPolicy(maximum_attempts=3),
            start_to_close_timeout=timedelta(seconds=60),
        )

        if message is None:
            return

        if settings.enable_email_body_llm_parsing:
            await workflow.execute_activity(
                parse_main_body_text_and_persist,
                message,
                task_queue=DEFAULT_TASK_QUEUE,
                retry_policy=RetryPolicy(maximum_attempts=3),
                start_to_close_timeout=timedelta(seconds=60),
            )

        if settings.enable_email_classification:
            await workflow.execute_activity(
                classify_message_and_update_metadata,
                message,
                task_queue=DEFAULT_TASK_QUEUE,
                retry_policy=RetryPolicy(maximum_attempts=3),
                start_to_close_timeout=timedelta(seconds=60),
            )

    async def _process_queue(self) -> None:
        # Process the queue until it's empty, which allows us to queue up jobs that are enqueued in parallel as a lock on pipeline_intel
        while len(self._input_queue) > 0:
            next_input = self._input_queue.pop(0)
            await self._process_single_input(next_input)

    @workflow.run
    async def run(self) -> None:
        # Wait for the first input to be enqueued from signal_with_start
        await workflow.wait_condition(lambda: len(self._input_queue) > 0)

        # Start processing the queue
        await self._process_queue()

    @staticmethod
    def get_workflow_id(
        global_thread_id: uuid.UUID,
        organization_id: uuid.UUID,
    ) -> str:
        return f"global-thread-cdc-event-workflow-{global_thread_id}::{organization_id}"
