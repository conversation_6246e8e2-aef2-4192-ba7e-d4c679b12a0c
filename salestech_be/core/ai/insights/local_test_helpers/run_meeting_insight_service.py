import asyncio
from uuid import UUID

from salestech_be.common.exception import (
    IllegalStateError,
)
from salestech_be.core.meeting.meeting_insight_service import (
    meeting_insight_service_factory_general,
)
from salestech_be.core.meeting.meeting_service import (
    meeting_service_factory_general,
)
from salestech_be.core.meeting.service.meeting_reference_type_strategy import (
    get_meeting_reference_type_strategy_factory_db_engine,
)
from salestech_be.core.transcript.transcript_service import (
    transcript_service_from_engine,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

logger = get_logger(__name__)


async def run_meeting_insight_service(organization_id: UUID, meeting_id: UUID) -> str:
    engine = DatabaseEngine(url=str(settings.db_url), pool_size=100, max_overflow=500)
    meeting_service = meeting_service_factory_general(engine)
    meeting_dto = await meeting_service.get_meeting(meeting_id, organization_id)

    meeting_insight_service = meeting_insight_service_factory_general(db_engine=engine)

    transcript_service = transcript_service_from_engine(engine)
    reference_type_strategy_factory = (
        get_meeting_reference_type_strategy_factory_db_engine(engine)
    )

    (
        transcript,
        transcript_container,
    ) = await transcript_service.process_completed_transcript(
        organization_id=organization_id,
        request=await reference_type_strategy_factory.get_instance(
            meeting_dto.meeting.reference_id_type
        ).get_process_transcript_request(meeting=meeting_dto.meeting),
    )
    if not transcript or not transcript_container:
        raise IllegalStateError("No processed transcript for meeting")

    await meeting_insight_service.extract_insights_from_transcript(
        organization_id,
        meeting=meeting_dto.meeting,
        transcript_container=transcript_container,
    )
    return "done"


if __name__ == "__main__":
    asyncio.run(
        run_meeting_insight_service(
            organization_id=UUID("e309ff95-96ba-4cf5-b65b-5facd6f54ca5"),
            meeting_id=UUID("7c0445df-e964-4148-99c9-25e1a6519258"),
        )
    )
