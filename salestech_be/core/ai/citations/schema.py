from typing import Literal
from uuid import UUID

from pydantic import BaseModel

from salestech_be.core.ai.common.types import IntelContext


class MeetingTaskCitationDTO(BaseModel):
    task_id: str
    cited_text: str
    turn_id: int | None = None
    start_turn_id: int | None = None
    end_turn_id: int | None = None


class EmailTaskCitationDTO(BaseModel):
    task_id: str
    cited_text: str
    global_message_id: str
    started_at_char_index: int
    ended_at_char_index: int
    generated_citation: str


class ObjectionTaskCitationDTO(BaseModel):
    task_id: UUID
    insight_id: UUID
    cited_insight_values: list[str] | None


class CitationBlockContext(BaseModel):
    start_idx: int
    end_idx: int
    context: IntelContext
    context_type: Literal["meeting", "email"]


class CitationContextResponse(BaseModel):
    context: IntelContext
    context_type: Literal["meeting", "email"]
    local_start_index: int
    local_end_index: int
