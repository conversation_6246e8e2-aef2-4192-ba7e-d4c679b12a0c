import contextlib
from uuid import UUID

from markdownify import markdownify as md
from pydantic import BaseModel
from temporalio import activity

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.query_util.filter_schema import FilterSpec, ValueFilter
from salestech_be.common.query_util.legacy.sort_schema import OrderEnum
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.query_util.sort_schema import Sorter, SortingSpec
from salestech_be.common.ree_llm import LLMTraceMetadata, acompletion
from salestech_be.common.schema_manager.std_object_field_identifier import TaskField
from salestech_be.common.type.metadata.schema import QualifiedField
from salestech_be.core.account.service.account_query_service import (
    get_account_query_service,
)
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.ai.workflows.schema import (
    MeetingTriggerPipelineIntelInput,
)
from salestech_be.core.contact.service.contact_query_service import (
    get_contact_query_service,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.data.service.query_service import get_domain_object_query_service
from salestech_be.core.data.util import ObjectRecordFetchConditions
from salestech_be.core.meeting.meeting_insight_service import (
    meeting_insight_service_factory_general,
)
from salestech_be.core.meeting.meeting_service import meeting_service_factory_general
from salestech_be.core.pipeline.service.pipeline_intel_service import (
    get_pipeline_intel_service,
)
from salestech_be.core.pipeline.service.pipeline_service import get_pipeline_service
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.task.service.task_v2_service import get_task_v2_service_general
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.models.meeting import MeetingStatus
from salestech_be.db.models.message import Message
from salestech_be.db.models.pipeline_intel import PipelineIntel
from salestech_be.db.models.task import (
    TaskPriority,
    TaskSourceType,
    TaskStatus,
    TaskType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.validation import not_none
from salestech_be.web.api.task.schema import (
    CreateTaskRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class GlobalThreadContext(BaseModel):
    thread_id: UUID
    subject: str
    messages: list[Message]


class MeetingContext(BaseModel):
    transcript: str
    meeting_id: UUID


class PipelineContext(BaseModel):
    meeting_context: MeetingContext | None
    threads: list[GlobalThreadContext]
    pipeline: PipelineV2
    account: AccountV2
    contacts: list[ContactV2]


def _construct_emails_from_threads(
    threads: list[GlobalThreadContext],
) -> str:
    """Construct emails for LLM prompt from meeting context."""

    def get_message_body(message: Message) -> str:
        """Get message body using fallback hierarchy."""
        if message.main_body_html:
            return message.main_body_html
        if message.main_body_text:
            return message.main_body_text
        if message.body_html:
            return message.body_html
        return message.body_text

    return "\n".join(
        [
            f"""
            Thread ID: {t.thread_id}
            Thread Title: {t.subject}
            Thread Messages:
            {
                "\n\n".join(
                    [
                        f'''
                    Message:
                    Subject: {m.subject}
                    From: {m.send_from}
                    To: {m.send_to}
                    CC: {m.cc}
                    Body: {md(get_message_body(m))}
                    Received At: {m.received_at}
                    Status: {m.status}
                    '''
                        for m in t.messages
                    ]
                )
            }
            """
            for t in threads
        ]
    )


async def get_context_since_last_meeting(
    context_input: MeetingTriggerPipelineIntelInput,
) -> PipelineContext:
    """Get emails since last meeting."""
    db_engine = await get_or_init_db_engine()
    meeting_insight_service = meeting_insight_service_factory_general(db_engine)
    thread_repository = ThreadRepository(engine=db_engine)
    meeting_repository = meeting_insight_service.meeting_repository
    meeting_service = meeting_service_factory_general(db_engine)
    pipeline_service = get_pipeline_service(db_engine)
    global_thread_to_messages: list[GlobalThreadContext] = []

    pipeline = await pipeline_service.get_pipeline_by_id(
        context_input.pipeline_id, context_input.organization_id
    )
    account_query_service = get_account_query_service(db_engine)
    account = await account_query_service.get_account_v2(
        account_id=pipeline.account_id,
        organization_id=context_input.organization_id,
    )

    contact_query_service = get_contact_query_service(db_engine)
    contacts = await contact_query_service.list_contacts_by_pipeline_id(
        pipeline_id=context_input.pipeline_id,
        organization_id=context_input.organization_id,
    )

    # Get all meetings for this pipeline
    meetings = await meeting_repository.list_meetings_by_pipeline_id(
        pipeline_id=context_input.pipeline_id,
        organization_id=context_input.organization_id,
    )

    completed_meetings = sorted(
        [m for m in meetings if m.status == MeetingStatus.COMPLETED and m.ended_at],
        key=lambda m: not_none(m.ended_at),
        reverse=True,
    )

    last_completed_meeting = completed_meetings[0] if completed_meetings else None

    threads = await thread_repository.list_global_threads_by_pipeline_id(
        pipeline_id=context_input.pipeline_id,
        organization_id=context_input.organization_id,
    )

    latest_threads = sorted(
        [t for t in threads if t.latest_message_received_date],
        key=lambda t: not_none(t.latest_message_received_date),
    )
    if last_completed_meeting and last_completed_meeting.ended_at:
        # if we have a meeting all global threads for this pipeline since the last meeting
        latest_threads = sorted(
            [
                t
                for t in threads
                if t.latest_message_received_date
                and t.latest_message_received_date >= last_completed_meeting.ended_at
            ],
            key=lambda t: not_none(t.latest_message_received_date),
        )

    for thread in latest_threads:
        message_list = await thread_repository.list_messages_by_global_thread_id(
            global_thread_id=thread.id,
            organization_id=context_input.organization_id,
        )
        if last_completed_meeting and last_completed_meeting.ended_at:
            latest_messages = sorted(
                [
                    m
                    for m in message_list
                    if m.received_at
                    and m.received_at >= last_completed_meeting.ended_at
                ],
                key=lambda m: not_none(m.received_at),
                reverse=True,
            )
        else:
            latest_messages = sorted(
                [m for m in message_list if m.received_at],
                key=lambda m: not_none(m.received_at),
                reverse=True,
            )

        global_thread_to_messages.append(
            GlobalThreadContext(
                thread_id=thread.id,
                messages=latest_messages,
                subject=thread.subject,
            )
        )

    transcript_container = None
    if last_completed_meeting:
        with contextlib.suppress(ResourceNotFoundError):
            (
                _,
                transcript_container,
                _meeting,
            ) = await meeting_service.get_transcript_by_meeting_id(
                meeting_id=last_completed_meeting.id,
                organization_id=context_input.organization_id,
            )

    transcript = (
        transcript_container.transcript.compact() if transcript_container else None
    )

    meeting_context = None
    if transcript and last_completed_meeting:
        meeting_context = MeetingContext(
            transcript=transcript,
            meeting_id=last_completed_meeting.id,
        )

    return PipelineContext(
        meeting_context=meeting_context,
        threads=global_thread_to_messages,
        pipeline=pipeline,
        account=account,
        contacts=contacts,
    )


@activity.defn
async def generate_meeting_activity_summary(
    activity_input: MeetingTriggerPipelineIntelInput,
) -> str:
    """Generate a summary of the meeting activities."""
    context = await get_context_since_last_meeting(activity_input)

    prompt = f"""Based on the meeting transcript and notes, provide a concise summary of the key activities and discussions.

Account:
Account Name: {context.account.display_name}
Account Domain: {context.account.domain_name}
Account Description: {context.account.description}

Pipeline:
Pipeline ID: {context.pipeline.id}
Pipeline Name: {context.pipeline.display_name}
Owner: {context.pipeline.owner_user_id}
Status: {context.pipeline.status}
Stage: {context.pipeline.stage}
Amount: {context.pipeline.amount}

{f"Meeting Details:\n{context.meeting_context.transcript}" if context.meeting_context else ""}

Emails since last meeting:
{_construct_emails_from_threads(context.threads)}

Provide a brief, professional summary in markdown format. Keep it short and concise. Do not use H1. Feel free to use H2, H3, lists, etc. Do not include a title since we did that separately.

## OUTPUT SPEC:
- DO NOT INCLUDE IDENTIFIER THAT IS NOT USER-FRIENDLY, LIKE UUID, ETC
- USE PRECISE TENSE IN YOUR SUMMARY
"""

    response = await acompletion(
        model="gpt-4o",
        messages=[{"role": "user", "content": prompt}],
        metadata=LLMTraceMetadata(
            trace_name="meeting_pipeline_intel.meeting_activity_summary",
            session_id=activity_input.langfuse_session_id,
            custom_fields={
                "organization_id": str(activity_input.organization_id),
                "meeting_id": str(activity_input.meeting_id),
            },
        ),
    )

    try:
        return response.message_content.strip()
    except ValueError:
        return "No response generated"


@activity.defn
async def generate_meeting_how_to_win(
    activity_input: MeetingTriggerPipelineIntelInput,
) -> str:
    """Generate recommendations on how to win the deal based on the meeting."""
    context = await get_context_since_last_meeting(activity_input)

    prompt = f"""Based on the meeting discussion, what are the key actions and strategies to win this deal?

Account:
Account Name: {context.account.display_name}
Account Domain: {context.account.domain_name}
Account Description: {context.account.description}

Pipeline:
Pipeline ID: {context.pipeline.id}
Pipeline Name: {context.pipeline.display_name}
Owner: {context.pipeline.owner_user_id}
Status: {context.pipeline.status}
Stage: {context.pipeline.stage}
Amount: {context.pipeline.amount}

{f"Meeting Details:\n{context.meeting_context.transcript}" if context.meeting_context else ""}

Emails since last meeting:
{_construct_emails_from_threads(context.threads)}

Contacts:
{"\n".join([c.model_dump_json() for c in context.contacts])}


Here's some examples of how the output should look:

Example 1 (Complete Information):
``` markdown
## Immediate steps

Send quote:
* Prepare and send an accurate and compelling quote via email as soon as possible.
* Ensure the quote highlights the unique value propositions and competitive pricing.

Schedule & Prepare for a Call on 1/9:
* Confirm the upcoming call scheduled for January 9th with the contact.
* Prepare a detailed agenda for the call to guide the discussion effectively.
* Highlight previous successful client collaborations similar to ChefJoy.

Facilitate Contact with Michael:
* Arrange for a conversation between Michael and the ChefJoy representative to build rapport and address any specific concerns or questions.

## Relationship building

Nurture Communication:
* Keep communication open and responsive. Regularly check in with the primary contact to maintain interest and engagement.

Tailored Engagement:
* Personalize interactions by understanding ChefJoy's mission and goals, aligning offerings to meet their specific needs.

## Strategic Considerations

Present Success Stories:
* Share testimonials or case studies from past clients in the food industry to establish credibility and trust.

Value Proposition Focus:
* Emphasize features that align with ChefJoy's business model and drive their operational efficiency.

Competitive Analysis:
* Highlight advantages over potential competitors, focusing on pricing, quality, and service support.

## Long-Term Strategy

Post-Sale Engagement Plan:
* Develop a plan for ongoing engagement after the deal is closed to strengthen the business relationship.

Customer Feedback Mechanism:
* Establish channels for feedback to continuously improve and adapt offerings to ChefJoy's evolving needs.
```

Example 2 (Email discussions exist but no meeting):
``` markdown
## Immediate steps

Follow-up Email:
* Send targeted follow-up within 2-3 business days if no response
* Include specific value points relevant to their industry
* Propose 2-3 concrete meeting time slots

Prepare Materials:
* Draft preliminary pricing based on available information
* Customize pitch deck for initial meeting

## Relationship building

Multi-threaded Approach:
* Identify and reach out to additional stakeholders
* Connect with decision makers on LinkedIn
* Monitor email open/click rates for engagement

## Strategic Considerations

Value Focus:
* Emphasize ROI and quick implementation timeline
* Reference similar successful implementations
* Address any concerns raised in initial email

## Next Steps

Meeting Preparation:
* Draft clear agenda focused on their needs
* Prepare relevant case studies and demos
* Plan discovery questions based on initial email context
```

Example 3 (No email discussions and no meeting):
``` markdown
## Immediate steps

Send quote:
* Prepare standard pricing overview document.
* Include basic service tier descriptions.

Schedule & Prepare for a Call:
* Request initial discovery meeting.
* Prepare general company overview presentation.
```

Provide specific, actionable recommendations in markdown format. Keep it short and concise. Do not use H1. Feel free to use H2, H3, lists, etc. Do not include a title since we did that separately.
Your markdown output:
"""

    response = await acompletion(
        model="gpt-4o",
        messages=[{"role": "user", "content": prompt}],
        metadata=LLMTraceMetadata(
            trace_name="meeting_pipeline_intel.meeting_how_to_win",
            session_id=activity_input.langfuse_session_id,
            custom_fields={
                "organization_id": str(activity_input.organization_id),
                "meeting_id": str(activity_input.meeting_id),
            },
        ),
    )

    try:
        return response.message_content.strip()
    except ValueError:
        return "No response generated"


@activity.defn
async def generate_meeting_risks(
    activity_input: MeetingTriggerPipelineIntelInput,
) -> str:
    """Identify potential risks based on the meeting discussion."""
    context = await get_context_since_last_meeting(activity_input)

    prompt = f"""Based on the meeting discussion, what are the potential risks and concerns for this deal?

Account:
Account Name: {context.account.display_name}
Account Domain: {context.account.domain_name}
Account Description: {context.account.description}

Pipeline:
Pipeline ID: {context.pipeline.id}
Pipeline Name: {context.pipeline.display_name}
Owner: {context.pipeline.owner_user_id}
Status: {context.pipeline.status}
Stage: {context.pipeline.stage}
Amount: {context.pipeline.amount}

{f"Meeting Details:\n{context.meeting_context.transcript}" if context.meeting_context else ""}

Emails since last meeting:
{_construct_emails_from_threads(context.threads)}

Contacts:
{"\n".join([c.model_dump_json() for c in context.contacts])}

List the key risks identified in markdown format. Keep it short and concise. Do not use H1. Feel free to use H2, H3, lists, etc. Do not include a title since we did that separately:"""

    response = await acompletion(
        model="gpt-4o",
        messages=[{"role": "user", "content": prompt}],
        metadata=LLMTraceMetadata(
            trace_name="meeting_pipeline_intel.meeting_risks",
            session_id=activity_input.langfuse_session_id,
            custom_fields={
                "organization_id": str(activity_input.organization_id),
                "meeting_id": str(activity_input.meeting_id),
            },
        ),
    )

    try:
        return response.message_content.strip()
    except ValueError:
        return "No response generated"


@activity.defn
async def generate_meeting_objections(
    activity_input: MeetingTriggerPipelineIntelInput,
) -> str:
    """Identify objections raised during the meeting."""
    context = await get_context_since_last_meeting(activity_input)

    prompt = f"""Based on the meeting discussion, what objections or concerns were raised by the client?

Account:
Account Name: {context.account.display_name}
Account Domain: {context.account.domain_name}
Account Description: {context.account.description}

Pipeline:
Pipeline ID: {context.pipeline.id}
Pipeline Name: {context.pipeline.display_name}
Owner: {context.pipeline.owner_user_id}
Status: {context.pipeline.status}
Stage: {context.pipeline.stage}
Amount: {context.pipeline.amount}

{f"Meeting Details:\n{context.meeting_context.transcript}" if context.meeting_context else ""}

Emails since last meeting:
{_construct_emails_from_threads(context.threads)}

Contacts:
{"\n".join([c.model_dump_json() for c in context.contacts])}

List the key objections raised in markdown format. Keep it short and concise. Do not use H1. Feel free to use H2, H3, lists, etc. Do not include a title since we did that separately:"""

    response = await acompletion(
        model="gpt-4o",
        messages=[{"role": "user", "content": prompt}],
        metadata=LLMTraceMetadata(
            trace_name="meeting_pipeline_intel.meeting_objections",
            session_id=activity_input.langfuse_session_id,
            custom_fields={
                "organization_id": str(activity_input.organization_id),
                "meeting_id": str(activity_input.meeting_id),
            },
        ),
    )

    try:
        return response.message_content.strip()
    except ValueError:
        return "No response generated"


class TaskRequestFromLLM(BaseModel):
    title: str
    priority: TaskPriority
    type: TaskType
    note: str
    email_thread_id: str | None = None
    meeting_id: str | None = None


class TaskRequestListFromLLM(BaseModel):
    tasks: list[TaskRequestFromLLM]


class DedupTaskFromLLM(BaseModel):
    is_duplicate: bool
    reason: str | None = None


class DedupRequestListFromLLM(BaseModel):
    tasks: list[DedupTaskFromLLM]


@activity.defn
async def generate_meeting_pipeline_intel_tasks(
    activity_input: MeetingTriggerPipelineIntelInput,
) -> str:
    """Generate the tasks for the meeting pipeline intel."""
    db_engine = await get_or_init_db_engine()
    task_v2_service = get_task_v2_service_general(db_engine=db_engine)
    domain_object_query_service = get_domain_object_query_service(db_engine=db_engine)
    context = await get_context_since_last_meeting(activity_input)
    existing_task_records = await domain_object_query_service.list_task_records(
        organization_id=activity_input.organization_id,
        fetch_conditions=ObjectRecordFetchConditions(
            fields=(
                QualifiedField(path=(TaskField.pipeline_id,)),
                QualifiedField(path=(TaskField.title_,)),
                QualifiedField(path=(TaskField.created_at,)),
                QualifiedField(path=(TaskField.note,)),
            ),
            filter_spec=FilterSpec(
                filter=ValueFilter(
                    field=QualifiedField(path=(TaskField.pipeline_id,)),
                    operator=MatchOperator.EQ,
                    value=activity_input.pipeline_id,
                ),
                primary_object_identifier=TaskV2.object_id,
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=TaskV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(TaskField.created_at,)),
                        order=OrderEnum.DESC,
                    ),
                ),
            ),
            # fields=get_task_summary_request.ordered_object_fields,
        ),
        include_custom_object=False,
    )
    existing_tasks = [record.data for record in existing_task_records]

    prompt = f"""Based on the meeting discussion and follow-up emails, create new actionable tasks for the sales team.

Account:
Account Name: {context.account.display_name}
Account Domain: {context.account.domain_name}
Account Description: {context.account.description}

Pipeline:
Pipeline ID: {context.pipeline.id}
Pipeline Name: {context.pipeline.display_name}
Owner: {context.pipeline.owner_user_id}
Status: {context.pipeline.status}
Stage: {context.pipeline.stage}
Amount: {context.pipeline.amount}

{
        f"Meeting Details:\n{context.meeting_context.transcript}"
        if context.meeting_context
        else ""
    }

Emails since last meeting:
{_construct_emails_from_threads(context.threads)}

Existing tasks (DO NOT CREATE TASKS THAT OVERLAP WITH THESE):
{
        "\n".join(
            [
                f'''<task>
  <title>{t.title}</title>
  <notes>{t.note}</notes>
</task>'''
                for t in existing_tasks
            ]
        )
    }

Contacts:
{
        "\n".join(
            [
                f'''<contact>
  <first_name>{c.first_name}</first_name>
  <last_name>{c.last_name}</last_name>
  <phone>{c.primary_phone_number}</phone>
  <email>{c.primary_email}</email>
</contact>'''
                for c in context.contacts
            ]
        )
    }

Create new tasks that are not duplicates of existing ones. For each task include:
- A clear title describing the action needed
- Priority level ({", ".join(TaskPriority.__members__.keys())})
- Task type ({", ".join(TaskType.__members__.keys())})
- Detailed notes with context and next steps

Do not create tasks that overlap with existing tasks. Focus on new action items identified from the meeting and follow-up communications.

ONLY CREATE TASKS FROM ACTION ITEMS THAT ARE EXTRACTED FROM THE MEETING TRANSCRIPT OR EMAIL DISCUSSIONS. DO NOT CREATE TASKS THAT ARE NOT EXPLICITLY REQUESTED.
It is okay to create zero tasks if there are no explicit actionable items that are not already covered by existing tasks.
"""

    # Note gpt-4o can take response_format=TaskRequestListFromLLM, but anthropic cannot so assisting through tool use
    response = await acompletion(
        model="anthropic/claude-3-5-sonnet-20241022",
        messages=[{"role": "user", "content": prompt}],
        temperature=0,
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "create_tasks",
                    "description": "A tool that creates one or more tasks with descriptions.",
                    "parameters": TaskRequestListFromLLM.model_json_schema(),
                },
            }
        ],
        # response_format={"type": "json_object"},
        metadata=LLMTraceMetadata(
            trace_name="meeting_pipeline_intel.meeting_tasks",
            session_id=activity_input.langfuse_session_id,
            custom_fields={
                "organization_id": str(activity_input.organization_id),
                "meeting_id": str(activity_input.meeting_id),
            },
        ),
        tool_choice="any",  # Force at least one tool call. Not sure why a specific tool call isn't working based on docs
    )

    if not response.choices:
        return "No tasks generated"

    tool_calls = response.tool_calls
    if not tool_calls:
        return "No tasks generated"

    tool_call = tool_calls[0]
    if not isinstance(tool_call.function.name, str):
        raise ValueError("Tool call name is not a string")

    task_json = tool_call.function.arguments
    if not isinstance(task_json, str):
        raise ValueError("Tool call arguments is not a string")

    task_request_list = TaskRequestListFromLLM.model_validate_json(task_json)

    for task_request in task_request_list.tasks:
        email_thread_id = (
            UUID(task_request.email_thread_id)
            if task_request.email_thread_id
            else activity_input.global_thread_id
        )
        meeting_id = (
            UUID(task_request.meeting_id)
            if task_request.meeting_id
            else activity_input.meeting_id
        )
        await task_v2_service.insert_task_v2(
            created_by_user_id=context.pipeline.owner_user_id,
            organization_id=activity_input.organization_id,
            request=CreateTaskRequest(
                owner_user_id=context.pipeline.owner_user_id,
                title=task_request.title,
                pipeline_id=activity_input.pipeline_id,
                meeting_id=meeting_id,
                account_id=activity_input.account_id,
                contact_ids=[c.id for c in context.contacts],
                email_thread_ids=[email_thread_id] if email_thread_id else [],
                status=TaskStatus.OPEN,
                priority=task_request.priority,
                type=task_request.type,
                note=task_request.note,
                source_type=TaskSourceType.SYSTEM,
            ),
        )

    return "\n".join([t.title for t in task_request_list.tasks])


@activity.defn
async def upsert_pipeline_intel(pipeline_intel: PipelineIntel) -> None:
    """Create or update pipeline intelligence data."""
    db_engine = await get_or_init_db_engine()
    service = get_pipeline_intel_service(db_engine)
    await service.upsert_pipeline_intel_by_pipeline_id(pipeline_intel)
