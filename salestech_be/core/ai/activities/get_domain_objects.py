import json
from uuid import UUID

from pydantic import BaseModel, Field
from temporalio import activity

from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.ai.tools.domain_objects.account import (
    fetch_account_domain_object_from_db,
    fetch_account_research_from_db,
)
from salestech_be.core.ai.tools.domain_objects.contact import (
    fetch_contact_domain_object_from_db,
    fetch_contact_research_from_db,
)
from salestech_be.core.ai.tools.domain_objects.meeting import (
    fetch_meeting_domain_object_from_db,
)
from salestech_be.core.ai.tools.domain_objects.pipeline import (
    fetch_pipeline_domain_object_from_db,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.db.dto.research_dto import AccountResearchDto, ContactResearchDto
from salestech_be.integrations.s3.s3_bucket_manager import (
    get_s3_bucket_manager_by_bucket_name,
)
from salestech_be.settings import settings
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

AI_ACTIVITIES_S3_RESULTS_BUCKET_NAME = (
    f"reevo-{settings.environment}-ai-activities-results"
)

s3_bucket_manager = get_s3_bucket_manager_by_bucket_name(
    bucket_name=AI_ACTIVITIES_S3_RESULTS_BUCKET_NAME
)


def get_s3_url(key: str) -> str:
    return f"https://{AI_ACTIVITIES_S3_RESULTS_BUCKET_NAME}.s3.{settings.aws_region}.amazonaws.com/{key}"


class DomainObjectRequest(BaseModel):
    """Base domain object request model."""

    object_id: UUID = Field(..., description="Unique identifier for the domain object")
    organization_id: UUID = Field(..., description="Organization identifier")

    # store_in_s3 will be set to True if the object is to be stored in S3 (to overcome the temporal payload size limit)
    store_in_s3: bool = Field(
        default=False, description="Whether to store the object in S3"
    )


class S3DomainObjectResponse(BaseModel):
    """Base S3-backed domain object response model."""

    s3_url: str | None = Field(default=None, description="S3 URL of stored object")

    store_in_s3: bool = Field(
        default=False, description="Whether the object was stored in S3"
    )


class AccountDomainObjectResponse(S3DomainObjectResponse):
    """Account domain object response model."""

    account: AccountV2 | None = Field(
        default=None, description="Account domain object details"
    )
    research: AccountResearchDto | None = Field(
        default=None, description="Account research details"
    )


class ContactDomainObjectResponse(S3DomainObjectResponse):
    """Contact domain object response model."""

    contact: ContactV2 | None = Field(
        default=None, description="Contact domain object details"
    )
    research: ContactResearchDto | None = Field(
        default=None, description="Contact research details"
    )


class MeetingDomainObjectResponse(S3DomainObjectResponse):
    """Meeting domain object response model."""

    meeting: MeetingV2 | None = Field(
        default=None, description="Meeting domain object details"
    )


class PipelineDomainObjectResponse(S3DomainObjectResponse):
    """Pipeline domain object response model."""

    pipeline: PipelineV2 | None = Field(
        default=None, description="Pipeline domain object details"
    )


@activity.defn
async def get_account_domain_object_activity(
    request: DomainObjectRequest,
) -> AccountDomainObjectResponse:
    """
    Retrieve account domain object and optional research

    Args:
        request: Domain object request with account_id and organization_id

    Returns:
        AccountDomainObjectResponse with account and research details
    """
    db_engine = await get_or_init_db_engine()

    # Fetch account domain object
    account = await fetch_account_domain_object_from_db(
        account_id=request.object_id,
        organization_id=request.organization_id,
        db_engine=db_engine,
    )

    # Fetch account research
    research = await fetch_account_research_from_db(
        account_id=request.object_id,
        organization_id=request.organization_id,
        db_engine=db_engine,
    )

    if request.store_in_s3:
        # Get activity execution info to generate s3 key
        activity_info = activity.info()
        s3_file_key = f"{activity_info.workflow_id}-{activity_info.activity_id}-{request.object_id}-{request.organization_id}.json"

        # Store in S3
        s3_bucket_manager.write_object(
            key=s3_file_key,
            data=json.dumps(
                {
                    "account": account.model_dump_json() if account else None,
                    "research": research.model_dump_json() if research else None,
                }
            ),
        )

        return AccountDomainObjectResponse(
            store_in_s3=True, s3_url=get_s3_url(s3_file_key)
        )

    return AccountDomainObjectResponse(
        account=account,
        research=research,
    )


@activity.defn
async def get_contact_domain_object_activity(
    request: DomainObjectRequest,
) -> ContactDomainObjectResponse:
    """
    Retrieve contact domain object and optional research

    Args:
        request: Domain object request with contact_id and organization_id

    Returns:
        ContactDomainObjectResponse with contact and research details
    """
    db_engine = await get_or_init_db_engine()

    # Fetch contact domain object
    contact = await fetch_contact_domain_object_from_db(
        contact_id=request.object_id,
        organization_id=request.organization_id,
        db_engine=db_engine,
    )

    # Fetch contact research
    research = await fetch_contact_research_from_db(
        contact_id=request.object_id,
        organization_id=request.organization_id,
        db_engine=db_engine,
    )

    if request.store_in_s3:
        # Get activity execution info to generate s3 key
        activity_info = activity.info()
        s3_file_key = f"{activity_info.workflow_id}-{activity_info.activity_id}-{request.object_id}-{request.organization_id}.json"

        # Store in S3
        s3_bucket_manager.write_object(
            key=s3_file_key,
            data=json.dumps(
                {
                    "contact": contact.model_dump_json() if contact else None,
                    "research": research.model_dump_json() if research else None,
                }
            ),
        )

        return ContactDomainObjectResponse(
            store_in_s3=True, s3_url=get_s3_url(s3_file_key)
        )

    return ContactDomainObjectResponse(
        contact=contact,
        research=research,
    )


@activity.defn
async def get_meeting_domain_object_activity(
    request: DomainObjectRequest,
) -> MeetingDomainObjectResponse:
    """
    Retrieve meeting domain object

    Args:
        request: Domain object request with meeting_id and organization_id

    Returns:
        MeetingDomainObjectResponse with meeting details
    """
    db_engine = await get_or_init_db_engine()

    # Fetch meeting domain object
    meeting = await fetch_meeting_domain_object_from_db(
        meeting_id=request.object_id,
        organization_id=request.organization_id,
        db_engine=db_engine,
    )

    if request.store_in_s3:
        # Get activity execution info to generate s3 key
        activity_info = activity.info()
        s3_file_key = f"{activity_info.workflow_id}-{activity_info.activity_id}-{request.object_id}-{request.organization_id}.json"

        # Store in S3
        s3_bucket_manager.write_object(
            key=s3_file_key,
            data=json.dumps(
                {
                    "meeting": meeting.model_dump_json() if meeting else None,
                }
            ),
        )

        return MeetingDomainObjectResponse(
            store_in_s3=True, s3_url=get_s3_url(s3_file_key)
        )

    return MeetingDomainObjectResponse(
        meeting=meeting,
    )


@activity.defn
async def get_pipeline_domain_object_activity(
    request: DomainObjectRequest,
) -> PipelineDomainObjectResponse:
    """
    Retrieve pipeline domain object

    Args:
        request: Domain object request with pipeline_id and organization_id

    Returns:
        PipelineDomainObjectResponse with pipeline details
    """
    db_engine = await get_or_init_db_engine()

    # Fetch pipeline domain object
    pipeline = await fetch_pipeline_domain_object_from_db(
        pipeline_id=request.object_id,
        organization_id=request.organization_id,
        db_engine=db_engine,
    )

    if request.store_in_s3:
        # Get activity execution info to generate s3 key
        activity_info = activity.info()
        s3_file_key = f"{activity_info.workflow_id}-{activity_info.activity_id}-{request.object_id}-{request.organization_id}.json"

        # Store in S3
        s3_bucket_manager.write_object(
            key=s3_file_key,
            data=json.dumps(
                {
                    "pipeline": pipeline.model_dump_json() if pipeline else None,
                }
            ),
        )

        return PipelineDomainObjectResponse(
            store_in_s3=True, s3_url=get_s3_url(s3_file_key)
        )

    return PipelineDomainObjectResponse(
        pipeline=pipeline,
    )
