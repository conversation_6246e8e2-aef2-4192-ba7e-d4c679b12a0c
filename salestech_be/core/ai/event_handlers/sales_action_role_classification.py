from uuid import UUID

from temporalio.common import WorkflowIDReusePolicy

from salestech_be.common.events import DomainEnrichedCDCEvent
from salestech_be.core.ai.workflows.sales_action_role_classification_workflow import (
    SalesActionRoleClassificationWorkflow,
)
from salestech_be.core.ai.workflows.schema import SalesActionRoleClassificationInput
from salestech_be.core.email.global_email.global_thread_type import (
    GlobalThread as GlobalThreadDomainModel,
)
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaExtractionSourceObjectId,
    CriteriaExtractionSourceObjectType,
)
from salestech_be.db.models.global_thread import GlobalThread
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import AI_TASK_QUEUE


async def start_sales_action_role_classification_workflow_from_global_thread_cdc_event(
    event: DomainEnrichedCDCEvent[GlobalThread, GlobalThreadDomainModel],
) -> None:
    if not event.after.pipeline_id:
        return
    await start_sales_action_role_classification_workflow(
        organization_id=event.after.organization_id,
        pipeline_id=event.after.pipeline_id,
        source_object=CriteriaExtractionSourceObjectId(
            object_id=event.after.id,
            object_type=CriteriaExtractionSourceObjectType.EMAIL,
        ),
    )


async def start_sales_action_role_classification_workflow(
    organization_id: UUID,
    pipeline_id: UUID,
    source_object: CriteriaExtractionSourceObjectId,
    workflow_id: str | None = None,
) -> None:
    if workflow_id is None:
        workflow_id = SalesActionRoleClassificationWorkflow.get_workflow_id(
            organization_id, pipeline_id, source_object
        )

    workflow_input = SalesActionRoleClassificationInput(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        source_object=source_object,
    )

    client = await get_temporal_client()
    await client.start_workflow(
        SalesActionRoleClassificationWorkflow.run,
        id=workflow_id,
        task_queue=AI_TASK_QUEUE,
        args=[workflow_input],
        id_reuse_policy=WorkflowIDReusePolicy.ALLOW_DUPLICATE_FAILED_ONLY,
    )
