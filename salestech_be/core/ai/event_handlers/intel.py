import time
import uuid
from uuid import UUID

from temporalio.common import WorkflowIDReusePolicy

from salestech_be.common.stats.metric import custom_metric
from salestech_be.core.ai.workflows.intel_workflow import IntelWorkflow
from salestech_be.core.ai.workflows.schema import IntelInput, IntelTriggerObjectType
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import AI_TASK_QUEUE
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


async def start_intel_workflow(
    organization_id: UUID,
    object_id: UUID,
    object_type: IntelTriggerObjectType,
    account_ids: list[UUID] | None = None,
    pipeline_id: UUID | None = None,
    workflow_id: str | None = None,
) -> None:
    workflow_input = IntelInput(
        organization_id=organization_id,
        object_id=object_id,
        object_type=object_type,
        pipeline_id=pipeline_id,
        account_ids=account_ids,
        langfuse_session_id=str(uuid.uuid4()),
    )

    process_start_time = time.perf_counter()
    client = await get_temporal_client()
    custom_metric.timing(
        metric_name="cdc_event_processor_get_temporal_client",
        value=(time.perf_counter() - process_start_time) * 1000,
        tags=[
            "cdc_flow:start_meeting_intel_workflow",
        ],
    )

    if workflow_id is None:
        workflow_id = IntelWorkflow.get_workflow_id(
            organization_id, object_id, object_type
        )

    process_start_time = time.perf_counter()
    await client.start_workflow(
        IntelWorkflow.run,
        id=workflow_id,
        task_queue=AI_TASK_QUEUE,
        args=[workflow_input],
        id_reuse_policy=WorkflowIDReusePolicy.ALLOW_DUPLICATE_FAILED_ONLY,
    )
    custom_metric.timing(
        metric_name="cdc_event_processor_start_workflow",
        value=(time.perf_counter() - process_start_time) * 1000,
        tags=[
            "cdc_flow:start_intel_workflow",
        ],
    )
