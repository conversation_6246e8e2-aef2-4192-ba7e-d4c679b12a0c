from uuid import UUID

from salestech_be.core.ai.workflows.pipeline_update_workflow import (
    PipelineUpdateWorkflow,
)
from salestech_be.core.ai.workflows.schema import (
    PipelineUpdateInput,
    PipelineUpdateReason,
    StageCriteriaType,
)
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaExtractionSourceObjectId,
    CriteriaExtractionSourceObjectType,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import AI_TASK_QUEUE


async def start_pipeline_update_workflow(
    organization_id: UUID,
    pipeline_id: UUID,
    reason: PipelineUpdateReason,
    meeting_id: UUID | None = None,
    workflow_id: str | None = None,
    config_type: StageCriteriaType | None = None,
) -> None:
    if workflow_id is None:
        workflow_id = PipelineUpdateWorkflow.get_workflow_id(pipeline_id, reason)

    source_object = None
    if meeting_id:
        source_object = CriteriaExtractionSourceObjectId(
            object_id=meeting_id,
            object_type=CriteriaExtractionSourceObjectType.MEETING,
        )

    workflow_input = PipelineUpdateInput(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        source_object=source_object,
        reason=reason,
        config_type=config_type,
    )

    client = await get_temporal_client()
    await client.start_workflow(
        PipelineUpdateWorkflow.run,
        id=workflow_id,
        task_queue=AI_TASK_QUEUE,
        start_signal="enqueue_input",
        start_signal_args=[workflow_input],
    )
