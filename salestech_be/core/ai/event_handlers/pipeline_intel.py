import time
import uuid
from uuid import UUID

from temporalio.client import Workflow<PERSON>andle

from salestech_be.common.events import DomainEnrichedCDCEvent
from salestech_be.common.stats.metric import custom_metric
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.ai.workflows.schema import MeetingTriggerPipelineIntelInput
from salestech_be.core.ai.workflows.trigger_pipeline_intel import (
    MeetingTriggerPipelineIntelWorkflow,
)
from salestech_be.core.email.insight.events import NewEmailEvent
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
from salestech_be.core.pipeline.service.pipeline_service import (
    PipelineService,
    get_pipeline_service,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.db.models.account import Account
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.meeting import Meeting
from salestech_be.db.models.pipeline import Pipeline
from salestech_be.event.database import (
    get_db_engine_for_event_handling,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import AI_TASK_QUEUE
from salestech_be.ree_logging import get_logger

PipelineIntelTriggerEvent = (
    DomainEnrichedCDCEvent[Meeting, MeetingV2]
    | DomainEnrichedCDCEvent[Pipeline, PipelineV2]
    | DomainEnrichedCDCEvent[Account, AccountV2]
    | NewEmailEvent
)
logger = get_logger(__name__)


async def start_meeting_intel_workflow(
    pipeline_id: UUID,
    organization_id: UUID,
    account_id: UUID,
    meeting_id: UUID | None = None,
    contact_id: UUID | None = None,
    global_thread_id: UUID | None = None,
) -> WorkflowHandle[MeetingTriggerPipelineIntelWorkflow, None]:
    workflow_input = MeetingTriggerPipelineIntelInput(
        meeting_id=meeting_id,
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
        contact_id=contact_id,
        global_thread_id=global_thread_id,
        langfuse_session_id=str(uuid.uuid4()),
    )

    process_start_time = time.perf_counter()
    client = await get_temporal_client()
    custom_metric.timing(
        metric_name="cdc_event_processor_get_temporal_client",
        value=(time.perf_counter() - process_start_time) * 1000,
        tags=[
            "cdc_flow:start_meeting_intel_workflow",
        ],
    )
    workflow_id = MeetingTriggerPipelineIntelWorkflow.get_workflow_id(
        meeting_id, organization_id
    )
    process_start_time = time.perf_counter()
    handle = await client.start_workflow(
        MeetingTriggerPipelineIntelWorkflow.run,
        id=workflow_id,
        task_queue=AI_TASK_QUEUE,
        start_signal="enqueue_input",
        start_signal_args=[workflow_input],
    )
    custom_metric.timing(
        metric_name="cdc_event_processor_start_workflow",
        value=(time.perf_counter() - process_start_time) * 1000,
        tags=[
            "cdc_flow:start_meeting_intel_workflow",
        ],
    )
    return handle


async def _fetch_account_id_by_pipeline_service(
    pipeline_service: PipelineService,
    pipeline_id: UUID,
    organization_id: UUID,
) -> UUID:
    pipeline = await pipeline_service.get_pipeline_by_id(pipeline_id, organization_id)
    return pipeline.account_id


async def pipeline_intel_handler(  # noqa: C901
    event: PipelineIntelTriggerEvent,
) -> None:
    db_engine = await get_db_engine_for_event_handling()

    start_time = time.perf_counter()
    custom_metric.timing(
        metric_name="pipeline_intel_handler_get_meeting_service",
        value=(time.perf_counter() - start_time) * 1000,
    )

    start_time = time.perf_counter()
    pipeline_service = get_pipeline_service(db_engine)
    custom_metric.timing(
        metric_name="pipeline_intel_handler_get_pipeline_service",
        value=(time.perf_counter() - start_time) * 1000,
    )

    match event:
        # Handle Meeting
        case DomainEnrichedCDCEvent() if isinstance(
            event, DomainEnrichedCDCEvent
        ) and isinstance(event.after, Meeting):
            start_time = time.perf_counter()
            meeting = event.after
            if not meeting.pipeline_id or not meeting.organization_id:
                logger.bind(meeting_id=meeting.id).info(
                    f"Meeting {meeting.id} missing required pipeline or account info, fields are {meeting.pipeline_id}, {meeting.organization_id}"
                )
                return
            account_id = (
                meeting.account_id
                or await _fetch_account_id_by_pipeline_service(
                    # temporary fix, pipeline guaranteed to have account_id
                    pipeline_service,
                    meeting.pipeline_id,
                    meeting.organization_id,
                )
            )
            custom_metric.timing(
                metric_name="pipeline_intel_handler_fetch_account_id_by_pipeline_service",
                value=(time.perf_counter() - start_time) * 1000,
            )

            start_time = time.perf_counter()
            await start_meeting_intel_workflow(
                pipeline_id=meeting.pipeline_id,
                organization_id=meeting.organization_id,
                account_id=account_id,
                meeting_id=meeting.id,
            )
            custom_metric.timing(
                metric_name="pipeline_intel_handler_start_meeting_intel_workflow",
                value=(time.perf_counter() - start_time) * 1000,
            )
        case NewEmailEvent():
            email_event = event
            if (
                not email_event.pipeline_id
                or not email_event.organization_id
                or not email_event.account_id
            ):
                logger.warning(
                    f"Email event {email_event} missing required pipeline info"
                )
                return
            await start_meeting_intel_workflow(
                pipeline_id=email_event.pipeline_id,
                organization_id=email_event.organization_id,
                account_id=email_event.account_id,
                global_thread_id=email_event.global_thread_id,
            )

        # Handle Pipeline
        case DomainEnrichedCDCEvent() if isinstance(
            event, DomainEnrichedCDCEvent
        ) and isinstance(event.after, Pipeline):
            pipeline = event.after
            if not pipeline.organization_id or not pipeline.account_id:
                logger.warning(
                    f"Pipeline {pipeline.id} missing required organization info"
                )
                return
            await start_meeting_intel_workflow(
                pipeline_id=pipeline.id,
                organization_id=pipeline.organization_id,
                account_id=pipeline.account_id,
            )

        # Handle Account
        case DomainEnrichedCDCEvent() if isinstance(
            event, DomainEnrichedCDCEvent
        ) and isinstance(event.after, Account):
            account = event.after
            if not account.organization_id:
                logger.warning(
                    f"Account {account.id} missing required organization info"
                )
                return

            # Get all pipelines for this account
            org_pipelines_v2 = await pipeline_service.list_pipelines(
                account.organization_id
            )
            pipelines_v2 = [p for p in org_pipelines_v2 if p.account_id == account.id]

            for pipeline_v2 in pipelines_v2:
                await start_meeting_intel_workflow(
                    pipeline_id=pipeline_v2.id,
                    organization_id=account.organization_id,
                    account_id=account.id,
                )

        case DomainEnrichedCDCEvent() if isinstance(
            event, DomainEnrichedCDCEvent
        ) and isinstance(event.after, Contact):
            contact = event.after
            if not contact.organization_id or not contact.account_id:
                logger.warning(
                    f"Contact {contact.id} missing required organization info"
                )
                return

            await start_meeting_intel_workflow(
                pipeline_id=contact.pipeline_id,
                organization_id=contact.organization_id,
                account_id=contact.account_id,
                contact_id=contact.id,
            )

        case _:
            logger.warning(f"Unsupported event type: {type(event)}")
