from uuid import UUID

from temporalio.common import WorkflowIDReusePolicy

from salestech_be.core.ai.workflows.schema import StageCriteriaWorkflowInput
from salestech_be.core.ai.workflows.stage_criteria_workflow import StageCriteriaWorkflow
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaExtractionSourceObjectId,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import AI_TASK_QUEUE


async def start_stage_criteria_workflow(
    organization_id: UUID,
    pipeline_id: UUID,
    user_id: UUID,
    source_object: CriteriaExtractionSourceObjectId,
    workflow_id: str | None = None,
) -> None:
    if workflow_id is None:
        workflow_id = StageCriteriaWorkflow.get_workflow_id(
            organization_id, pipeline_id, source_object
        )

    workflow_input = StageCriteriaWorkflowInput(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        source_object=source_object,
    )

    client = await get_temporal_client()
    await client.start_workflow(
        StageCriteriaWorkflow.run,
        id=workflow_id,
        task_queue=AI_TASK_QUEUE,
        args=[workflow_input],
        id_reuse_policy=WorkflowIDReusePolicy.ALLOW_DUPLICATE_FAILED_ONLY,
    )
