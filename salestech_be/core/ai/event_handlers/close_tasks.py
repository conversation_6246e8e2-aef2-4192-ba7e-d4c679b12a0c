from salestech_be.common.events import DomainEnrichedCDCEvent
from salestech_be.core.ai.event_handlers.types import PipelineIntelTriggerEvent
from salestech_be.core.ai.tasks.workflow_triggers.start_close_existing_tasks import (
    start_close_existing_tasks,
)
from salestech_be.core.ai.workflows.schema import IntelTriggerObjectType
from salestech_be.core.email.insight.events import NewEmailEvent
from salestech_be.db.models.meeting import Meeting
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


async def close_tasks_handler(
    event: PipelineIntelTriggerEvent,
) -> None:
    """Handle closing tasks based on meeting and email events."""

    match event:
        # Handle Meeting events
        case DomainEnrichedCDCEvent() if isinstance(event.after, Meeting):
            meeting = event.after
            if not meeting.organization_id:
                logger.bind(meeting_id=meeting.id).info(
                    "Meeting missing required organization info"
                )
                return

            await start_close_existing_tasks(
                organization_id=meeting.organization_id,
                object_id=meeting.id,
                object_type=IntelTriggerObjectType.MEETING,
                account_ids=[meeting.account_id] if meeting.account_id else None,
                pipeline_id=meeting.pipeline_id,
            )

        # Handle Email events
        case NewEmailEvent():
            if not event.organization_id:
                logger.warning(
                    f"Email event {event} missing required organization info"
                )
                return

            if not event.global_thread_id:
                logger.warning(f"Email event {event} missing required global thread id")
                return

            await start_close_existing_tasks(
                organization_id=event.organization_id,
                object_id=event.global_thread_id,
                object_type=IntelTriggerObjectType.GLOBAL_THREAD,
                account_ids=[event.account_id] if event.account_id else None,
                pipeline_id=event.pipeline_id,
            )

        case _:
            # Silently ignore other event types
            pass
