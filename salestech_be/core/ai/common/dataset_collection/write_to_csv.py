import json
from typing import Any

import pandas as pd

from salestech_be.ree_logging import get_logger

logger = get_logger()


def write_to_csv(  # type: ignore[explicit-any]  # TODO: fix-any-annotation
    prompt_variables: dict[str, str],
    output_response: Any,
    output_path: str,
) -> None:
    try:
        data = {
            "input": json.dumps(prompt_variables),
            "output": json.dumps(output_response),
        }
        logger.info(f"Writing data {data} to {output_path}")
        df = pd.DataFrame(data, index=[0])
        df.to_csv(output_path, mode="a", header=False, index=False)
    except Exception as e:
        logger.error(f"Error writing data to {output_path}: {e}")
