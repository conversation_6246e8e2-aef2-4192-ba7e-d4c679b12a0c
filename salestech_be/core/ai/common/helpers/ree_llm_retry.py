import asyncio
from collections.abc import Callable, Coroutine
from functools import wraps
from typing import Any, ParamSpec, TypeVar, cast

from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)

ParamSpecT = ParamSpec("ParamSpecT")
ReturnTypeT = TypeVar("ReturnTypeT")


class ReeLLMRetryError(Exception):
    """Custom error for LLM validation failures that includes feedback for retry"""

    message: str
    metadata: dict[str, Any] | None = None  # type: ignore[explicit-any]

    def __init__(self, message: str, metadata: dict[str, Any] | None = None):  # type: ignore[explicit-any]
        self.message = message
        self.metadata = metadata


def ree_llm_retry(  # type: ignore[explicit-any]
    max_retries: int = 3, retry_delay: float = 1.0
) -> Callable[
    [Callable[ParamSpecT, Coroutine[Any, Any, ReturnTypeT]]],
    Callable[ParamSpecT, Coroutine[Any, Any, ReturnTypeT]],
]:
    """
    Decorator for LLM calls that modifies the prompt on retries based on validation errors.

    When an LLMValidationError is raised, its error message will be incorporated into
    the next retry attempt to help guide the model to a valid response.
    """

    def decorator(  # type: ignore[explicit-any]
        func: Callable[ParamSpecT, Coroutine[Any, Any, ReturnTypeT]],
    ) -> Callable[ParamSpecT, Coroutine[Any, Any, ReturnTypeT]]:
        @wraps(func)
        async def wrapper(
            *args: ParamSpecT.args, **kwargs: ParamSpecT.kwargs
        ) -> ReturnTypeT:
            attempt = 0
            last_error = None

            while attempt < max_retries:
                try:
                    if attempt > 0:
                        kwargs["attempt_num"] = attempt
                        kwargs["last_error"] = last_error
                        existing_errors = cast(
                            list[ReeLLMRetryError], kwargs.get("all_errors", [])
                        )
                        kwargs["all_errors"] = (
                            [*existing_errors, last_error]
                            if last_error
                            else existing_errors
                        )
                    return await func(*args, **kwargs)

                except ReeLLMRetryError as e:
                    last_error = e
                    attempt += 1

                    logger.warning(
                        f"LLM validation failed on attempt {attempt}",
                        extra={
                            "error": str(e),
                            "attempt": attempt,
                            "max_retries": max_retries,
                            "function": func.__name__,
                        },
                    )

                    if attempt < max_retries:
                        await asyncio.sleep(retry_delay * attempt)

                except Exception:
                    # For non-validation errors, fail immediately
                    raise

            raise ReeLLMRetryError(
                f"Failed to get valid response after {max_retries} attempts. Last error: {last_error.message if last_error else 'Unknown error'}"
            )

        return wrapper

    return decorator
