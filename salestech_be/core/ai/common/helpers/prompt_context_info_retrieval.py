from uuid import UUID

from salestech_be.core.ai.common.types import (
    EmailAugmented,
    MeetingAugmented,
    SourceContext,
    VoiceCallAugmented,
)
from salestech_be.core.email.insight.email_insight_service import (
    get_email_insight_service_by_db,
)
from salestech_be.core.meeting.meeting_insight_service import (
    meeting_insight_service_factory_general,
)
from salestech_be.core.user.service.user_service import get_user_service_general
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore
)

logger = get_logger(__name__)


async def get_seller_contacts_prompt_input(
    source_context: SourceContext,
    organization_id: UUID,
) -> str:
    db_engine = await get_or_init_db_engine()
    if isinstance(source_context, list):
        # Get all the users of the organization as the seller contacts
        user_service = get_user_service_general(db_engine)
        users = await user_service.list_users_v2(
            organization_id=organization_id, active_users_only=True
        )
        return "\n".join(
            [
                f"""<seller_contact>
                <first_name>{c.first_name}</first_name>
                <last_name>{c.last_name}</last_name>
                <phone>{c.phone_number}</phone>
                <email>{c.email}</email>
                <id>{c.id}</id>
                </seller_contact>"""
                for c in users
            ]
        )
    if isinstance(
        source_context.primary_object, (MeetingAugmented, VoiceCallAugmented)
    ):
        meeting_insight_service = meeting_insight_service_factory_general(db_engine)
        if not source_context.organization:
            logger.warning(
                "No organization found for prompt input",
                object_id=source_context.primary_object.id,
            )
            return ""
        return await meeting_insight_service.generate_seller_contacts_prompt_input(
            organization_id=source_context.organization.id,
            meeting=source_context.primary_object
            if isinstance(source_context.primary_object, MeetingAugmented)
            else source_context.primary_object.meeting,
        )
    elif isinstance(source_context.primary_object, EmailAugmented):
        email_insight_service = get_email_insight_service_by_db(db_engine)
        return await email_insight_service.generate_seller_contacts_prompt_input(
            organization=source_context.organization,
            global_thread=source_context.primary_object,
        )
    logger.warning(
        "No seller contacts found for prompt input",
        object_id=source_context.primary_object.id,
    )
    return ""
