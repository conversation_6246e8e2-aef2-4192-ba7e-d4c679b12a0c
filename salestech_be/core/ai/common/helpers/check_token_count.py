import inspect
from collections.abc import Callable, Coroutine
from functools import wraps
from typing import Any, ParamSpec, TypeVar, cast

import anthropic
from google import genai
from litellm import token_counter

from salestech_be.common.ree_voyage import get_async_voyage_client
from salestech_be.core.ai.common.llm_types import ModelTypes
from salestech_be.settings import settings

ParamSpecT = ParamSpec("ParamSpecT")
ReturnTypeT = TypeVar("ReturnTypeT")


class ReeLLMTokenCountException(Exception):
    """
    Exception raised when if token count does not behave as expected.
    """

    def __init__(self, message: str):
        self.message = message


def get_token_count(message_content: str, model: str) -> int:
    """Helper function to get token count for different model types."""
    match model:
        case "voyage-3" | _ if "rerank" in model:
            vo = get_async_voyage_client()
            return vo.count_tokens([message_content], model=model)
        case _ if "claude" in model:
            cleaned_model = model.replace("anthropic/", "")
            message_token_count = anthropic.Anthropic(
                api_key=settings.anthropic_api_key.get_secret_value()
            ).messages.count_tokens(
                messages=[{"role": "user", "content": message_content}],
                model=cleaned_model,
            )
            return message_token_count.input_tokens
        case _ if "gemini" in model:
            cleaned_model = model.replace("vertex_ai/", "")
            gemini_client = genai.Client(
                api_key=settings.gemini_api_key.get_secret_value()
            )
            gemini_message_token_count = gemini_client.models.count_tokens(
                model=cleaned_model, contents=message_content
            )
            return gemini_message_token_count.total_tokens  # type: ignore
        case _:
            return token_counter(model=model, text=message_content)


def _validate_model(model: str) -> None:
    """Validate input parameters."""
    if model not in ModelTypes:
        raise ReeLLMTokenCountException(
            f"Provided model {model} is not currently supported. Please add it to ModelTypes in llm_types.py. You may also need to support is in get_max_tokens and check_token_count."
        )


def _process_token_count(  # type: ignore[explicit-any]
    payload: Any,
    model: str,
    token_limit: int,
    to_prompt: Callable[[Any], str] | None = None,
) -> tuple[int, int]:
    """Process token count and return token metrics."""
    message_content = to_prompt(payload) if to_prompt else cast(str, payload)
    token_count = get_token_count(message_content, model)
    tokens_to_remove = token_count - token_limit if token_count > token_limit else 0
    return token_count, tokens_to_remove


def check_token_count(  # type: ignore[explicit-any]
    token_limit: int,
    model: str,
    to_prompt: Callable[[Any], str] | None = None,
    max_attempts: int = 200000,
) -> Callable[[Callable[ParamSpecT, ReturnTypeT]], Callable[ParamSpecT, ReturnTypeT]]:
    """Decorator to check token count of a payload and provide token usage information."""

    def decorator(
        func: Callable[ParamSpecT, ReturnTypeT],
    ) -> Callable[ParamSpecT, ReturnTypeT]:
        if inspect.iscoroutinefunction(func):

            @wraps(func)
            async def async_wrapper(*args: Any, **kwargs: Any) -> ReturnTypeT:  # type: ignore[explicit-any]
                try:
                    _validate_model(model)
                    payload = args[0]
                    attempt = cast(int, kwargs.get("attempt", 0))

                    token_count, tokens_to_remove = _process_token_count(
                        payload, model, token_limit, to_prompt
                    )

                    kwargs["token_count"] = token_count
                    kwargs["tokens_to_remove"] = tokens_to_remove

                    async_func = cast(  # type: ignore[explicit-any]
                        Callable[..., Coroutine[Any, Any, ReturnTypeT]], func
                    )
                    result = await async_func(payload, *args[1:], **kwargs)

                    if (
                        tokens_to_remove > 0
                        and result is not None
                        and attempt < max_attempts
                    ):
                        new_args = (result, *args[1:])
                        kwargs["attempt"] = attempt + 1
                        return await async_wrapper(*new_args, **kwargs)

                    return result

                except Exception as e:
                    raise ReeLLMTokenCountException(
                        f"Error in token budget checking: {e!s}"
                    )

            return async_wrapper  # type: ignore[return-value] # TODO: fix-return-type
        else:

            @wraps(func)
            def sync_wrapper(*args: Any, **kwargs: Any) -> ReturnTypeT:  # type: ignore[explicit-any]
                try:
                    _validate_model(model)
                    payload = args[0]
                    attempt = cast(int, kwargs.get("attempt", 0))

                    token_count, tokens_to_remove = _process_token_count(
                        payload, model, token_limit, to_prompt
                    )

                    kwargs["token_count"] = token_count
                    kwargs["tokens_to_remove"] = tokens_to_remove

                    result = func(payload, *args[1:], **kwargs)
                    if (
                        tokens_to_remove > 0
                        and result is not None
                        and attempt < max_attempts
                    ):
                        new_args = (result, *args[1:])
                        kwargs["attempt"] = attempt + 1
                        return sync_wrapper(*new_args, **kwargs)

                    return result

                except Exception as e:
                    raise ReeLLMTokenCountException(
                        f"Error in token budget checking: {e!s}"
                    )

            return sync_wrapper

    return decorator
