import contextlib
from datetime import timedelta
from uuid import UUID

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.query_util.filter_schema import (
    CompositeFilter,
    Filter,
    FilterSpec,
    ValueFilter,
)
from salestech_be.common.query_util.legacy.sort_schema import OrderEnum
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.query_util.sort_schema import Sorter, SortingSpec
from salestech_be.common.schema_manager.std_object_field_identifier import TaskField
from salestech_be.common.type.metadata.schema import QualifiedField
from salestech_be.core.account.service.account_query_service import (
    get_account_query_service,
)
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.ai.common.types import (
    EmailAugmented,
    IntelContext,
    MeetingAugmented,
    VoiceCallAugmented,
)
from salestech_be.core.ai.workflows.schema import (
    IntelInput,
    IntelTriggerObjectType,
)
from salestech_be.core.contact.service.contact_query_service import (
    get_contact_query_service,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.data.service.query_service import get_domain_object_query_service
from salestech_be.core.data.util import ObjectRecordFetchConditions
from salestech_be.core.meeting.meeting_insight_service import (
    meeting_insight_service_factory_general,
)
from salestech_be.core.meeting.meeting_service import meeting_service_factory_general
from salestech_be.core.organization.service.organization_service import (
    get_organization_service_general,
)
from salestech_be.core.pipeline.service.pipeline_service import get_pipeline_service
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.core.user.service.user_service import (
    get_user_service_general,
)
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.db.dao.email_account import EmailAccountRepository
from salestech_be.db.dao.insight_repository import InsightRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dao.voice_call_repository import VoiceCallRepository
from salestech_be.db.models.email_account import EmailAccount
from salestech_be.db.models.meeting import (
    MeetingReferenceIdType,
)
from salestech_be.db.models.task import (
    TaskStatus,
)
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.organization.schema import (
    OrganizationResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger()


async def get_organization(organization_id: UUID) -> OrganizationResponse | None:
    db_engine = await get_or_init_db_engine()
    organization_service = get_organization_service_general(db_engine)
    return await organization_service.get_organization_by_id(
        organization_id=organization_id,
    )


async def get_existing_tasks(
    organization_id: UUID, pipeline_id: UUID | None, account_ids: list[UUID] | None
) -> list[TaskV2]:
    id_field_to_filter_on: TaskField | None = None
    ids_field_to_filter_on: TaskField | None = None
    # Filter on pipeline_id, if pipeline_id doesn't exist, filter on account_ids
    if pipeline_id:
        id_field_to_filter_on = TaskField.pipeline_id
        id_value_to_filter_on = pipeline_id
    elif account_ids:
        ids_field_to_filter_on = TaskField.account_id
        ids_value_to_filter_on = account_ids

    filters: list[Filter] = [
        ValueFilter(
            field=QualifiedField(path=(TaskField.created_at,)),
            operator=MatchOperator.GTE,
            value=zoned_utc_now() - timedelta(days=30),
        ),
        CompositeFilter(
            all_of=[],
            none_of=[],
            any_of=[
                ValueFilter(
                    field=QualifiedField(path=(TaskField.status,)),
                    operator=MatchOperator.EQ,
                    value=TaskStatus.OPEN,
                ),
                ValueFilter(
                    field=QualifiedField(path=(TaskField.status,)),
                    operator=MatchOperator.EQ,
                    value=TaskStatus.IN_PROGRESS,
                ),
            ],
        ),
    ]

    if id_field_to_filter_on is not None:
        filters.append(
            ValueFilter(
                field=QualifiedField(path=(id_field_to_filter_on,)),
                operator=MatchOperator.EQ,
                value=id_value_to_filter_on,
            )
        )

    if ids_field_to_filter_on is not None:
        any_of: list[CompositeFilter | ValueFilter] = [
            ValueFilter(
                field=QualifiedField(path=(ids_field_to_filter_on,)),
                operator=MatchOperator.EQ,
                value=id_value,
            )
            for id_value in ids_value_to_filter_on
        ]

        filters.append(
            CompositeFilter(
                any_of=any_of,
            )
        )
    db_engine = await get_or_init_db_engine()
    domain_object_query_service = get_domain_object_query_service(db_engine=db_engine)
    existing_task_records = await domain_object_query_service.list_task_records(
        organization_id=organization_id,
        fetch_conditions=ObjectRecordFetchConditions(
            fields=(
                QualifiedField(path=(TaskField.pipeline_id,)),
                QualifiedField(path=(TaskField.title_,)),
                QualifiedField(path=(TaskField.created_at,)),
                QualifiedField(path=(TaskField.note,)),
            ),
            filter_spec=FilterSpec(
                filter=CompositeFilter(
                    all_of=filters,
                    any_of=[],
                    none_of=[],
                ),
                primary_object_identifier=TaskV2.object_id,
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=TaskV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(TaskField.created_at,)),
                        order=OrderEnum.DESC,
                    ),
                ),
            ),
            # fields=get_task_summary_request.ordered_object_fields,
        ),
        include_custom_object=False,
    )
    return [record.data for record in existing_task_records]


async def get_all_users(
    organization_id: UUID,
) -> list[OrganizationUserV2]:
    db_engine = await get_or_init_db_engine()
    user_service = get_user_service_general(db_engine)
    return await user_service.list_users_v2(
        organization_id=organization_id,
        active_users_only=True,
    )


async def get_specific_active_users(
    organization_id: UUID, user_ids: set[UUID]
) -> list[OrganizationUserV2]:
    db_engine = await get_or_init_db_engine()
    user_service = get_user_service_general(db_engine)
    return await user_service.list_users_v2(
        organization_id=organization_id,
        only_include_user_ids=user_ids,
        active_users_only=True,
    )


async def get_specific_active_user(
    user_id: UUID, organization_id: UUID
) -> OrganizationUserV2 | None:
    users = await get_specific_active_users(organization_id, {user_id})
    if users:
        return users[0]
    return None


async def get_pipeline(pipeline_id: UUID, organization_id: UUID) -> PipelineV2:
    db_engine = await get_or_init_db_engine()
    pipeline_service = get_pipeline_service(db_engine)
    return await pipeline_service.get_pipeline_by_id(pipeline_id, organization_id)


async def get_contacts(pipeline_id: UUID, organization_id: UUID) -> list[ContactV2]:
    db_engine = await get_or_init_db_engine()
    contact_query_service = get_contact_query_service(db_engine)
    return await contact_query_service.list_contacts_by_pipeline_id(
        pipeline_id=pipeline_id,
        organization_id=organization_id,
    )


async def get_accounts(
    account_ids: list[UUID], organization_id: UUID
) -> list[AccountV2]:
    db_engine = await get_or_init_db_engine()
    account_query_service = get_account_query_service(db_engine)
    return await account_query_service.list_accounts_v2(
        only_include_account_ids=set(account_ids),
        organization_id=organization_id,
    )


def filter_existing_tasks(
    existing_tasks: list[TaskV2], contacts: list[ContactV2]
) -> list[TaskV2]:
    contact_ids = [contact.id for contact in contacts]
    return [
        task
        for task in existing_tasks
        if not task.contact_ids or set(task.contact_ids) == set(contact_ids)
    ]


async def get_email_accounts(
    email_account_ids: list[UUID], organization_id: UUID
) -> list[EmailAccount]:
    db_engine = await get_or_init_db_engine()
    email_account_repository = EmailAccountRepository(engine=db_engine)
    return await email_account_repository.find_accounts_by_ids(
        email_account_ids=email_account_ids,
        organization_id=organization_id,
    )


async def get_meeting_intel_context(intel_input: IntelInput) -> IntelContext:  # noqa: PLR0915, PLR0912, C901
    db_engine = await get_or_init_db_engine()
    meeting_service = meeting_service_factory_general(db_engine)
    meeting_insight_service = meeting_insight_service_factory_general(db_engine)
    meeting_repository = meeting_insight_service.meeting_repository
    voice_call_repository = VoiceCallRepository(engine=db_engine)

    organization = await get_organization(organization_id=intel_input.organization_id)

    existing_tasks = await get_existing_tasks(
        organization_id=intel_input.organization_id,
        pipeline_id=intel_input.pipeline_id,
        account_ids=intel_input.account_ids,
    )
    if intel_input.pipeline_id:
        pipeline = await get_pipeline(
            intel_input.pipeline_id, intel_input.organization_id
        )
        contacts = await get_contacts(
            intel_input.pipeline_id, intel_input.organization_id
        )
    else:
        pipeline = None
        contacts = None

    if intel_input.account_ids:
        accounts = await get_accounts(
            intel_input.account_ids, intel_input.organization_id
        )
    else:
        accounts = None

    meeting = await meeting_repository.get_meeting_by_id(
        meeting_id=intel_input.object_id,
        organization_id=intel_input.organization_id,
    )
    if not meeting:
        raise ValueError("No meeting found")

    # Users
    users = []
    user_ids_from_attendees = {
        attendee.user_id
        for attendee in meeting.attendee_or_empty_list()
        if attendee.user_id
    }
    if user_ids_from_attendees:
        # for meetings, use attendees to get contacts first and fallback to contacts from pipeline
        meeting_users = await get_specific_active_users(
            organization_id=intel_input.organization_id,
            user_ids=user_ids_from_attendees,
        )
        if meeting_users:
            users = meeting_users

    # Contacts
    contact_ids_from_attendees = {
        attendee.contact_id
        for attendee in meeting.attendee_or_empty_list()
        if attendee.contact_id
    }
    if contact_ids_from_attendees:
        contact_query_service = get_contact_query_service(db_engine)
        # for meetings, use attendees to get contacts first and fallback to contacts from pipeline
        meeting_contacts = await contact_query_service.list_contacts_v2(
            organization_id=intel_input.organization_id,
            only_include_contact_ids=contact_ids_from_attendees,
        )
        if meeting_contacts:
            contacts = meeting_contacts

    # Get account owner
    if pipeline:
        account_owner = await get_specific_active_user(
            organization_id=intel_input.organization_id,
            user_id=pipeline.owner_user_id,
        )
    elif accounts is not None and len(accounts) > 0:
        account_owner = await get_specific_active_user(
            organization_id=intel_input.organization_id,
            user_id=accounts[0].owner_user_id,
        )
    elif meeting.organizer_user_id is not None:
        account_owner = await get_specific_active_user(
            organization_id=intel_input.organization_id,
            user_id=meeting.organizer_user_id,
        )
    else:
        account_owner = None

    transcript_container = None
    with contextlib.suppress(ResourceNotFoundError):
        (
            _,
            transcript_container,
            _,
        ) = await meeting_service.get_transcript_by_meeting_id(
            meeting_id=intel_input.object_id,
            organization_id=intel_input.organization_id,
        )

    transcript = (
        transcript_container.transcript.compact() if transcript_container else None
    )

    transcript_sentences = None

    logger.info(f"intel_input: {intel_input}")

    primary_object: MeetingAugmented | VoiceCallAugmented | None = None

    if intel_input.test_stub:
        transcript_sentences = intel_input.test_stub
        transcript = "\n".join([sentence.text for sentence in intel_input.test_stub])
    elif transcript_container and transcript_container.transcript:
        transcript_sentences = transcript_container.transcript.sentences

    if meeting.reference_id_type == MeetingReferenceIdType.VOICE_V2:
        call = await voice_call_repository.find_by_id(
            call_id=UUID(meeting.reference_id)
        )
        if not call:
            logger.warning(
                f"No call {meeting.reference_id} found for meeting {meeting.id}"
            )

            primary_object = MeetingAugmented(
                **meeting.model_dump(),
                transcript=transcript,
                transcript_sentences=transcript_sentences,
            )
        else:
            primary_object = VoiceCallAugmented(
                id=call.id,
                call=call,
                meeting=meeting,
                transcript=transcript,
                transcript_sentences=transcript_sentences,
            )
    else:
        primary_object = MeetingAugmented(
            **meeting.model_dump(),
            transcript=transcript,
            transcript_sentences=transcript_sentences,
        )

    # If there's no pipeline or accounts, filter out existing tasks that don't match the contacts
    if not intel_input.pipeline_id and not intel_input.account_ids and contacts:
        # Filter tasks to only include those where contact_ids exactly match our contacts list
        existing_tasks = filter_existing_tasks(existing_tasks, contacts)

    if primary_object and organization:
        logger.debug("Primary object and organization found, returning intel context")
        logger.debug(f"Primary object: {primary_object}")
        logger.debug(f"Organization: {organization}")
        return IntelContext(
            contacts=contacts,
            accounts=accounts,
            pipeline=pipeline,
            primary_object=primary_object,
            existing_tasks=existing_tasks,
            organization=organization,
            users=users,
            account_owner=account_owner,
        )
    else:
        raise ValueError("No primary object found")


async def get_email_intel_context(intel_input: IntelInput) -> IntelContext:  # noqa: PLR0912, C901
    db_engine = await get_or_init_db_engine()
    thread_repository = ThreadRepository(engine=db_engine)

    organization = await get_organization(organization_id=intel_input.organization_id)
    existing_tasks = await get_existing_tasks(
        intel_input.organization_id, intel_input.pipeline_id, intel_input.account_ids
    )

    if intel_input.pipeline_id:
        pipeline = await get_pipeline(
            intel_input.pipeline_id, intel_input.organization_id
        )
        contacts = await get_contacts(
            pipeline_id=intel_input.pipeline_id,
            organization_id=intel_input.organization_id,
        )
    else:
        pipeline = None
        contacts = None

    if intel_input.account_ids:
        accounts = await get_accounts(
            intel_input.account_ids, intel_input.organization_id
        )
    else:
        accounts = None

    thread = await thread_repository.get_global_thread_by_id(
        global_thread_id=intel_input.object_id,
        organization_id=intel_input.organization_id,
    )
    if not thread:
        raise ValueError("No global thread found")

    messages = await thread_repository.list_messages_with_global_message_id_by_global_thread_id(
        global_thread_id=intel_input.object_id,
        organization_id=intel_input.organization_id,
    )

    if intel_input.test_stub:
        messages = intel_input.test_stub

    # If there are no messages, set the last message to None
    if not messages:
        messages = []
        last_message = None
    else:
        last_message = messages[-1]

    # If last message exists, only allow user options to be from participants in last message
    # else set the users to all users in organization
    if last_message:
        last_message_participants_email_account_ids = (
            last_message.unique_email_account_ids
        )
        email_accounts = await get_email_accounts(
            email_account_ids=last_message_participants_email_account_ids,
            organization_id=intel_input.organization_id,
        )
        user_ids = [email_account.owner_user_id for email_account in email_accounts]

        users = await get_specific_active_users(
            organization_id=intel_input.organization_id,
            user_ids=set(user_ids),
        )
    else:
        users = await get_all_users(organization_id=intel_input.organization_id)

    primary_object = EmailAugmented(**thread.model_dump(), messages=messages)

    if pipeline:
        account_owner = await get_specific_active_user(
            organization_id=intel_input.organization_id,
            user_id=pipeline.owner_user_id,
        )
    elif accounts is not None and len(accounts) > 0:
        account_owner = await get_specific_active_user(
            organization_id=intel_input.organization_id,
            user_id=accounts[0].owner_user_id,
        )
    else:
        account_owner = None

    # If there's no pipeline or accounts, filter out existing tasks that don't match the contacts
    if not intel_input.pipeline_id and not intel_input.account_ids and contacts:
        # Filter tasks to only include those where contact_ids exactly match our contacts list
        existing_tasks = filter_existing_tasks(existing_tasks, contacts)

    if primary_object and organization:
        logger.debug("Primary object and organization found, returning intel context")
        logger.debug(f"Primary object: {primary_object}")
        logger.debug(f"Organization: {organization}")
        return IntelContext(
            contacts=contacts,
            accounts=accounts,
            pipeline=pipeline,
            primary_object=primary_object,
            existing_tasks=existing_tasks,
            organization=organization,
            users=users,
            account_owner=account_owner,
        )
    else:
        raise ValueError("No primary object found")


async def get_objection_intel_context(intel_input: IntelInput) -> IntelContext:
    db_engine = await get_or_init_db_engine()
    insight_repository = InsightRepository(engine=db_engine)

    organization = await get_organization(organization_id=intel_input.organization_id)

    users = await get_all_users(organization_id=intel_input.organization_id)

    existing_tasks = await get_existing_tasks(
        organization_id=intel_input.organization_id,
        pipeline_id=intel_input.pipeline_id,
        account_ids=intel_input.account_ids,
    )

    if intel_input.pipeline_id:
        pipeline = await get_pipeline(
            pipeline_id=intel_input.pipeline_id,
            organization_id=intel_input.organization_id,
        )
        contacts = await get_contacts(
            pipeline_id=intel_input.pipeline_id,
            organization_id=intel_input.organization_id,
        )
    else:
        pipeline = None
        contacts = None

    if intel_input.account_ids:
        accounts = await get_accounts(
            account_ids=intel_input.account_ids,
            organization_id=intel_input.organization_id,
        )
    else:
        accounts = None

    logger.debug(f"Getting objection by id: {intel_input.object_id}")
    logger.debug(f"Organization id: {intel_input.organization_id}")
    # Objections are a type of insight
    primary_object = await insight_repository.get_insight_by_id(
        insight_id=intel_input.object_id,
        organization_id=intel_input.organization_id,
    )
    if not primary_object:
        raise ValueError("No insight found")

    # Get account owner
    if pipeline:
        account_owner = await get_specific_active_user(
            organization_id=intel_input.organization_id,
            user_id=pipeline.owner_user_id,
        )
    elif accounts is not None and len(accounts) > 0:
        account_owner = await get_specific_active_user(
            organization_id=intel_input.organization_id,
            user_id=accounts[0].owner_user_id,
        )
    else:
        account_owner = None

    # If there's no pipeline or accounts, filter out existing tasks that don't match the contacts
    if not intel_input.pipeline_id and not intel_input.account_ids and contacts:
        # Filter tasks to only include those where contact_ids exactly match our contacts list
        existing_tasks = filter_existing_tasks(existing_tasks, contacts)

    if primary_object and organization:
        logger.debug("Primary object and organization found, returning intel context")
        logger.debug(f"Primary object: {primary_object}")
        logger.debug(f"Organization: {organization}")
        return IntelContext(
            contacts=contacts,
            accounts=accounts,
            pipeline=pipeline,
            primary_object=primary_object,
            existing_tasks=existing_tasks,
            organization=organization,
            users=users,
            account_owner=account_owner,
        )
    else:
        raise ValueError("No primary object found")


async def get_intel_context(intel_input: IntelInput) -> IntelContext:
    if intel_input.object_type == IntelTriggerObjectType.MEETING:
        return await get_meeting_intel_context(intel_input=intel_input)
    elif intel_input.object_type == IntelTriggerObjectType.GLOBAL_THREAD:
        return await get_email_intel_context(intel_input=intel_input)
    elif intel_input.object_type == IntelTriggerObjectType.OBJECTION:
        return await get_objection_intel_context(intel_input=intel_input)
    else:
        raise ValueError("Invalid object type")
