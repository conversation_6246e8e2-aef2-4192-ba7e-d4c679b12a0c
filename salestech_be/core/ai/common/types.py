from datetime import UTC, datetime
from typing import TypeA<PERSON>s
from uuid import UUID

from langfuse.api.resources.prompts.types.prompt import Prompt
from markdownify import markdownify as md
from pydantic import BaseModel

from salestech_be.common.ree_llm import get_max_tokens
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.ai.activities.meeting_pipeline_intel_activities import (
    TaskRequestFromLLM,
)
from salestech_be.core.ai.common.helpers.check_token_count import (
    check_token_count,
    get_token_count,
)
from salestech_be.core.ai.email.summarize_email_content_with_llm import (
    summarize_email_content_with_llm,
)
from salestech_be.core.ai.prompt.schema import PromptEnum
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.meeting.types.meeting_query_types import TranscriptCitation
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.db.models.global_thread import GlobalThread
from salestech_be.db.models.insight import Insight
from salestech_be.db.models.meeting import Meeting
from salestech_be.db.models.message import MessageWithGlobalMessageId
from salestech_be.db.models.task import (
    TaskPriority,
)
from salestech_be.db.models.voice_v2 import Call
from salestech_be.web.api.meeting.schema import (
    TranscriptSentence,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.organization.schema import (
    OrganizationResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)


class MeetingAugmented(Meeting):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    transcript: str | None
    transcript_sentences: list[TranscriptSentence] | None


class VoiceCallAugmented(BaseModel):
    id: UUID
    call: Call
    meeting: Meeting
    transcript: str | None
    transcript_sentences: list[TranscriptSentence] | None


class EmailAugmented(GlobalThread):
    messages: list[MessageWithGlobalMessageId]


class TaskPriorityResponse(BaseModel):
    priority: TaskPriority
    explanation: str


class TaskOwnershipResponse(BaseModel):
    ownership_id: str
    explanation: str


class IntelContext(BaseModel):
    primary_object: (
        GlobalThread
        | MeetingAugmented
        | Meeting
        | EmailAugmented
        | VoiceCallAugmented
        | Call
        | Insight
    )
    organization: OrganizationResponse | None
    accounts: list[AccountV2] | None
    contacts: list[ContactV2] | None
    pipeline: PipelineV2 | None
    existing_tasks: list[TaskV2] | None
    users: list[OrganizationUserV2] | None
    account_owner: OrganizationUserV2 | None

    async def to_task_prompt_variables(
        self,
        prompt: Prompt | None = None,
    ) -> dict[str, str]:
        base_vars: dict[str, str] = {
            "additional_context": "",
            "contacts": "",
            "object_content": "",
            "existing_tasks": "",
        }

        if self.pipeline is not None:
            base_vars["additional_context"] += f"""
                Pipeline ID: {self.pipeline.id}
                Pipeline Name: {self.pipeline.display_name}
                Owner: {self.pipeline.owner_user_id}
                Status: {self.pipeline.status}
                Stage: {self.pipeline.stage}
                Amount: {self.pipeline.amount}
                \n\n
            """

        if self.accounts is not None:
            base_vars["additional_context"] += f"""
                Accounts:
                {
                "\n".join(
                    [
                        f'''
                            Account ID: {account.id}
                            Account Name: {account.display_name}
                            Account Domain: {account.domain_name}
                            Account Description: {account.description}
                            \n\n
                            '''
                        for account in self.accounts
                    ]
                )
            }
            """

        if self.contacts is not None:
            base_vars["contacts"] = "\n".join(
                [
                    f"""<contact>
            <first_name>{c.first_name}</first_name>
            <last_name>{c.last_name}</last_name>
            <phone>{c.primary_phone_number}</phone>
            <email>{c.primary_email}</email>
            <id>{c.id}</id>
            </contact>"""
                    for c in self.contacts
                ]
            )

        if self.existing_tasks is not None:
            base_vars["existing_tasks"] = "\n".join(
                [
                    f"""<task>
            <title>{t.title}</title>
            <notes>{t.note}</notes>
            </task>"""
                    for t in self.existing_tasks
                ]
            )

        if isinstance(self.primary_object, EmailAugmented):
            return {
                **base_vars,
                "object_content": await self._process_email_content(prompt),
            }

        elif isinstance(self.primary_object, (MeetingAugmented, VoiceCallAugmented)):
            if not self.primary_object:
                raise ValueError("No primary object found")

            return {
                **base_vars,
                "object_content": self.primary_object.transcript
                if self.primary_object.transcript
                else "",
            }

        elif isinstance(self.primary_object, Insight):
            object_content = self.serialize_primary_object()

            return {
                **base_vars,
                "object_content": object_content,
            }

        raise ValueError(f"Invalid primary object type: {type(self.primary_object)}")

    async def _process_email_content(self, prompt: Prompt | None = None) -> str:
        messages = sorted(
            self.primary_object.messages,  # type: ignore
            key=lambda x: x.received_at if x.received_at else datetime.now(tz=UTC),
            reverse=True,
        )

        def construct_email_content(
            messages: list[MessageWithGlobalMessageId], summary: str | None = None
        ) -> str:
            if isinstance(self.primary_object, EmailAugmented):
                thread_title = self.primary_object.subject
            else:
                thread_title = "No subject"

            email_content = f"""
                Thread ID: {self.primary_object.id}  # type: ignore
                Thread Title: {thread_title}
                Messages:
                {
                "\n\n".join(
                    [
                        f'''
                            Subject: {m.subject}
                            From: {m.send_from}
                            To: {m.send_to}
                            CC: {m.cc}
                            Body: {
                            md(
                                m.main_body_html
                                if m.main_body_html
                                else m.body_html
                                if m.body_html
                                else m.body_text
                                if m.body_text
                                else ""
                            )
                        }
                            Received At: {m.received_at}
                        '''
                        for m in messages
                    ]
                )
            }
                """
            if summary is not None:
                email_content += f"\n\nOlder messages summary:\n{summary}"
            return email_content

        # First try with last 5 messages
        summary = None
        message_cutoff = 5
        if len(messages) > message_cutoff:
            older_messages = self._format_messages_for_summary(
                messages[message_cutoff:]
            )
            summary = await summarize_email_content_with_llm(
                email_content=older_messages,
                organization_id=self.organization.id,  # type: ignore
                langfuse_session_id=f"intel:email:{self.primary_object.id}",
            )
            messages = messages[:5]

        email_content = construct_email_content(messages, summary)

        # Check token count if prompt is provided
        if prompt:
            model_name = prompt.config.get("model", "gpt-4o")
            max_tokens = get_max_tokens(model=model_name)

            if get_token_count(email_content, model=model_name) > max_tokens:
                if len(messages) > 1:
                    # Summarize all but the last message
                    older_messages = self._format_messages_for_summary(messages[1:])
                    summary_recent_messages = await summarize_email_content_with_llm(
                        email_content=older_messages,
                        organization_id=self.organization.id,  # type: ignore
                        langfuse_session_id=f"intel:email:{self.primary_object.id}",
                    )
                    combined_summary = (
                        f"{summary}\n\n{summary_recent_messages}"
                        if summary
                        else summary_recent_messages
                    )
                    email_content = construct_email_content(
                        messages[:1], combined_summary
                    )
                else:
                    raise ValueError("Token count is too high for last sent email")

        return email_content

    def _format_messages_for_summary(
        self, messages: list[MessageWithGlobalMessageId]
    ) -> str:
        return "\n\n".join(
            [
                f"""
                Message:
                Subject: {m.subject}
                From: {m.send_from}
                To: {m.send_to}
                CC: {m.cc}
                Body: {md(m.body_html if m.body_html else m.body_text)}
                Received At: {m.received_at}
            """
                for m in messages
            ]
        )

    def to_check_dup_tasks_prompt_variables(
        self,
        prompt: Prompt | None = None,
        generated_tasks: list[TaskRequestFromLLM] | None = None,
    ) -> dict[str, str]:
        if prompt:

            def vars_to_content(variables: dict[str, str]) -> str:
                template_content = ""
                if len(prompt.prompt) > 1:
                    template_content = (
                        prompt.prompt[0].content + prompt.prompt[1].content
                    )
                return (
                    template_content
                    + "\n"
                    + "\n".join([f"{k}: {v}" for k, v in variables.items()])
                )

            model_name = prompt.config.get("model", "gpt-4o")
            max_tokens = get_max_tokens(model=model_name)

            remaining_tasks = self.existing_tasks.copy() if self.existing_tasks else []
            remaining_tasks.sort(key=lambda t: t.updated_at)

            @check_token_count(
                model=model_name,
                to_prompt=vars_to_content,
                max_attempts=1000,
                token_limit=max_tokens,
            )
            def fit_prompt(
                variables: dict[str, str],
                *,
                token_count: int | None = None,
                tokens_to_remove: int = 0,
            ) -> dict[str, str]:
                if tokens_to_remove > 0 and remaining_tasks:
                    remaining_tasks.pop(0)
                    # Regenerate the existing_tasks string with the updated list
                    variables["existing_tasks"] = "\n".join(
                        [
                            f"""<task>
                <title>{t.title}</title>
                <notes>{t.note}</notes>
                </task>"""
                            for t in remaining_tasks
                        ]
                    )
                return variables

        base_vars: dict[str, str] = {
            "existing_tasks": "",
            "generated_tasks": "",
        }

        if self.existing_tasks is not None:
            base_vars["existing_tasks"] = "\n".join(
                [
                    f"""<task>
            <title>{t.title}</title>
            <notes>{t.note}</notes>
            </task>"""
                    for t in self.existing_tasks
                ]
            )

        if generated_tasks is not None:
            base_vars["generated_tasks"] = "\n".join(
                [
                    f"""<task>
            <title>{t.title}</title>
            <notes>{t.note}</notes>
            </task>"""
                    for t in generated_tasks
                ]
            )
        if prompt:
            base_vars = fit_prompt(base_vars)
        return base_vars

    def to_brief_insights_prompt_variables(self) -> dict[str, str]:
        if isinstance(self.primary_object, Call):
            base_vars: dict[str, str] = {
                "ends_at": str(self.primary_object.ended_at),
                "seller_company_name": self.organization.display_name
                if self.organization
                else "",
                "transcript": "",
            }

            if self.contacts is not None:
                base_vars["contacts"] = "\n".join(
                    [
                        f"""<contact>
                <first_name>{c.first_name}</first_name>
                <last_name>{c.last_name}</last_name>
                <phone>{c.primary_phone_number}</phone>
                <email>{c.primary_email}</email>
                </contact>"""
                        for c in self.contacts
                    ]
                )

            if self.existing_tasks is not None:
                base_vars["existing_tasks"] = "\n".join(
                    [
                        f"""<task>
                <title>{t.title}</title>
                <notes>{t.note}</notes>
                </task>"""
                        for t in self.existing_tasks
                    ]
                )

            return base_vars
        else:
            raise ValueError("Invalid primary object")

    def to_close_existing_tasks_prompt_variables(self) -> dict[str, str]:
        base_vars: dict[str, str] = {
            "object_content": "",
            "existing_tasks": "",
        }
        if self.existing_tasks is not None:
            base_vars["existing_tasks"] = "\n".join(
                [
                    f"""<task>
            <id>{t.id}</id>
            <title>{t.title}</title>
            <notes>{t.note}</notes>
            </task>"""
                    for t in self.existing_tasks
                ]
            )

        if isinstance(self.primary_object, EmailAugmented):
            email_content = self.serialize_primary_object()

            return {
                **base_vars,
                "object_content": email_content if email_content else "",
            }

        elif isinstance(self.primary_object, MeetingAugmented):
            if not self.primary_object:
                raise ValueError("No primary object found")
            object_content = self.serialize_primary_object()

            return {
                **base_vars,
                "object_content": object_content,
            }

        raise ValueError(f"Invalid primary object type: {type(self.primary_object)}")

    def serialize_primary_object(self) -> str:
        if isinstance(self.primary_object, MeetingAugmented):
            return f"""
                Meeting:
                Meeting ID: {self.primary_object.id}
                Meeting Title: {self.primary_object.title}
                Meeting Transcript:
                {self.primary_object.transcript if self.primary_object.transcript else "No transcript available"}
            """
        elif isinstance(self.primary_object, EmailAugmented):
            return f"""
                Email Thread:
                Thread ID: {self.primary_object.id}
                Thread Title: {self.primary_object.subject}
                Thread Messages:
                {
                "\n\n".join(
                    [
                        f'''
                        Message:
                        Subject: {m.subject}
                        From: {m.send_from}
                        To: {m.send_to}
                        CC: {m.cc}
                        Body: {md(m.body_html if m.body_html else m.body_text)}
                        Received At: {m.received_at}
                    '''
                        for m in self.primary_object.messages
                    ]
                )
            }
            """
        elif isinstance(self.primary_object, Insight):
            return f"""<objection>
                <description>{". ".join(self.primary_object.brief_values) if self.primary_object.brief_values else ""}</description>
                <id>{self.primary_object.id}</id>
            </objection>"""

        else:
            raise ValueError(
                f"Invalid primary object type: {type(self.primary_object)}"
            )

    async def to_prompt_variables(
        self,
        prompt_name: PromptEnum,
        prompt: Prompt | None = None,
        generated_tasks: list[TaskRequestFromLLM] | None = None,
    ) -> dict[str, str]:
        if prompt_name in [
            PromptEnum.GENERATE_TASKS_FROM_CONVERSATION,
            PromptEnum.GENERATE_TASKS_FROM_EMAIL,
            PromptEnum.GENERATE_TASK_PRIORITY,
            PromptEnum.GENERATE_TASK_DUE_DATE,
            PromptEnum.GENERATE_TASKS_FROM_OBJECTION,
            PromptEnum.GENERATE_TASK_OWNERSHIP,
        ]:
            return await self.to_task_prompt_variables(
                prompt=prompt,
            )
        # elif prompt_name == PromptEnum.GENERATE_TASK_OWNERSHIP:
        #     return await self.to_task_ownership_prompt_variables()
        elif prompt_name == PromptEnum.GENERATE_BRIEF_INSIGHTS_FROM_CONVERSATION:
            return self.to_brief_insights_prompt_variables()
        elif prompt_name == PromptEnum.CLOSE_EXISTING_TASKS:
            return self.to_close_existing_tasks_prompt_variables()
        elif prompt_name == PromptEnum.CHECK_GENERATED_TASKS_ALREADY_EXIST:
            return self.to_check_dup_tasks_prompt_variables(
                prompt=prompt, generated_tasks=generated_tasks
            )
        else:
            raise ValueError(f"Invalid prompt name: {prompt_name}")


SourceContext: TypeAlias = IntelContext | list[TranscriptCitation]
