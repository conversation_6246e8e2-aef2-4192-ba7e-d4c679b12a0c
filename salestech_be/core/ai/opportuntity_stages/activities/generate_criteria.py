from uuid import UUID

from temporalio import activity

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.core.ai.opportuntity_stages.criteria_configs import (
    CRITERIA_CONFIG_MAP,
)
from salestech_be.core.ai.opportuntity_stages.llm_calls.criteria_update_with_llm import (
    criteria_update_with_llm,
)
from salestech_be.core.ai.opportuntity_stages.utils.extract_criteria_citations import (
    extract_criteria_citations,
)
from salestech_be.core.ai.workflows.schema import StageCriteriaType
from salestech_be.core.common.types import DomainModel
from salestech_be.core.crm_ai_rec.crm_ai_rec_service import get_crm_ai_rec_service
from salestech_be.core.opportunity_stage_criteria.criteria_parser import (
    set_criteria_field_null_to_unset,
    set_criteria_item_field_null_to_unset,
)
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaContextUpdateWithRecTypes,
    CriteriaItemsUpdate,
    CriteriaUpdateRequest,
    CriteriaUpdateResult,
    LLMCriteriaUpdateRequest,
    SourceObject,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_service import (
    get_pipeline_qualification_property_service,
)
from salestech_be.db.models.crm_ai_rec import CrmAIRecType, ParentRecordIds
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore
)

logger = get_logger(__name__)


@activity.defn
async def generate_criteria(  # noqa: C901, PLR0912
    organization_id: UUID,
    pipeline_id: UUID,
    user_id: UUID,
    source_object: SourceObject,
    config_type: StageCriteriaType,
) -> (
    CriteriaUpdateResult[CriteriaContextUpdateWithRecTypes, CriteriaItemsUpdate] | None
):
    config = CRITERIA_CONFIG_MAP.get(config_type)
    if not config:
        raise ValueError(f"Invalid config type: {config_type}")

    db_engine = await get_or_init_db_engine()
    pipeline_qual_prop_service = get_pipeline_qualification_property_service(
        db_engine=db_engine
    )
    crm_ai_rec_service = get_crm_ai_rec_service(db_engine=db_engine)

    # Get previous criteria using the configured method
    previous_criteria = await config.get_previous_criteria(
        pipeline_qual_prop_service,
        organization_id,
        pipeline_id,
        user_id,
    )

    request = LLMCriteriaUpdateRequest(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        source_object=source_object,
        request=CriteriaUpdateRequest(
            context_update_type=config.context_update_type,
            items_update_type=config.items_update_type,
            previous_context=previous_criteria.model_dump_json(),
        ),
    )
    criteria_update_result = await criteria_update_with_llm(request)
    if not criteria_update_result or not criteria_update_result.result:
        return None
    result = criteria_update_result.result
    criteria_citations = extract_criteria_citations(result)
    updated_context = result.updated_context
    if (
        updated_context
        and config.context_update_type
        and isinstance(updated_context, config.context_update_type)
    ):
        updated_context_fields = set_criteria_field_null_to_unset(
            updated_context.updated_context
        )
        if config.patch_request_type and updated_context_fields:
            patch_request = config.patch_request_type(
                **updated_context_fields.model_dump()
            )
            if config.create_ai_recs_from_patch_request:
                await config.create_ai_recs_from_patch_request(
                    pipeline_qual_prop_service,
                    organization_id,
                    pipeline_id,
                    user_id,
                    CrmAIRecType.enhancement,
                    patch_request,
                    criteria_citations.field_citations,
                    updated_context.field_update_types,
                )
    updated_items = result.updated_items
    if not updated_items:
        return result
    if updated_items.create and config.criteria_item_type:
        for item in updated_items.create:
            if not isinstance(item, config.criteria_item_type):
                logger.warning(
                    "Item is not an instance of criteria item type",
                    item=item,
                    criteria_item_type=config.criteria_item_type,
                )
                continue
            if not config.create_item_request_type:
                logger.warning(
                    "No create item request type",
                    item=item,
                    criteria_item_type=config.criteria_item_type,
                )
                continue
            if not issubclass(
                request_type := config.create_item_request_type, ShapeConstrainedModel
            ):
                logger.warning(
                    "Create item request type is not a subclass of ShapeConstrainedModel",
                    item=item,
                    criteria_item_type=config.criteria_item_type,
                )
                continue
            if not issubclass(
                domain_constraint := request_type.constraint(),
                DomainModel,
            ):
                logger.warning(
                    "Create item request type constraint is not a subclass of DomainModel",
                    item=item,
                    criteria_item_type=config.criteria_item_type,
                )
                continue
            create_req = request_type(**item.model_dump())
            object_name = StdObjectIdentifiers(domain_constraint.object_id.object_name)

            logger.info(
                "Creating AI object rec",
                object_name=object_name,
                create_req=create_req,
            )

            await crm_ai_rec_service.create_object_ai_rec_from_create_request(
                organization_id=organization_id,
                user_id=user_id,
                sobject_name=object_name,
                create_request=create_req,
                parent_record_ids=ParentRecordIds(pipeline_id=pipeline_id),
                citation_ids=[
                    citation.id
                    for citation in criteria_citations.added_item_citations
                    if citation.field_item_id == item.id
                ],
            )

    # if updated_items.delete:
    #     for item_id in updated_items.delete:
    #         if config.delete_criteria_item:
    #             await config.delete_criteria_item(
    #                 pipeline_qual_prop_service,
    #                 organization_id,
    #                 pipeline_id,
    #                 user_id,
    #                 item_id,
    #                 criteria_citations.removed_item_citations,
    #             )
    if updated_items.update and config.update_item_type:
        for update_item in updated_items.update:
            if not isinstance(update_item, config.update_item_type):
                continue
            update_item_cleaned = set_criteria_item_field_null_to_unset(
                update_item.updated_item
            )
            if (
                config.create_item_ai_recs_from_patch_request
                and config.update_item_request_type
            ):
                patch_req = config.update_item_request_type(
                    **update_item_cleaned.model_dump()
                )
                await config.create_item_ai_recs_from_patch_request(
                    pipeline_qual_prop_service,
                    organization_id,
                    pipeline_id,
                    user_id,
                    CrmAIRecType.enhancement,
                    update_item_cleaned.id,
                    patch_req,
                    criteria_citations.updated_item_citations,
                    update_item.field_update_types,
                )
    return result
