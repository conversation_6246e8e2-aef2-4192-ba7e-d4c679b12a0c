from dataclasses import dataclass
from uuid import UUID

from anthropic.types import CitationContentBlockLocation

from salestech_be.core.citation.service.citation_service import CitationService
from salestech_be.core.meeting.types.meeting_query_types import TranscriptCitation
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaCitation,
    FieldCitationPair,
)
from salestech_be.db.models.citation import (
    CitationForObjectType,
    CitationSourceType,
    MeetingCitationMetadata,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

logger = get_logger(__name__)


@dataclass
class MultiMeetingTranscriptRange:
    meeting_id: UUID
    start_turn_id: int
    end_turn_id: int
    source_text: str


async def add_criteria_multi_meeting_citation(
    organization_id: UUID,
    citation_service: CitationService,
    object_type: CitationForObjectType,
    multi_meeting_transcript: list[TranscriptCitation],
    field_citation_pairs: list[FieldCitationPair],
) -> list[CriteriaCitation]:
    criteria_citations: list[CriteriaCitation] = []
    for pair in field_citation_pairs:
        citation = pair.citation
        cited_field = pair.field

        if not isinstance(citation, CitationContentBlockLocation):
            logger.warning(f"Invalid citation type: {type(citation)}")
            continue

        # First collect all the ranges
        ranges: list[MultiMeetingTranscriptRange] = []
        current_range: MultiMeetingTranscriptRange | None = None

        for index in range(citation.start_block_index, citation.end_block_index):
            original_transcript_citation = multi_meeting_transcript[index]

            # Exclude if it is a separator block
            if (
                original_transcript_citation.text == "--- END OF MEETING ---"
                or "--- MEETING" in original_transcript_citation.text
            ):
                continue

            # If this is a new meeting or first line, start a new range
            if (
                current_range is None
                or current_range.meeting_id != original_transcript_citation.meeting_id
            ):
                # Save previous range if exists
                if current_range is not None:
                    ranges.append(current_range)

                # Start new range
                current_range = MultiMeetingTranscriptRange(
                    meeting_id=original_transcript_citation.meeting_id,
                    start_turn_id=original_transcript_citation.line_number,
                    end_turn_id=original_transcript_citation.line_number + 1,
                    source_text=original_transcript_citation.text,
                )
            # Continue current range if lines are consecutive
            elif original_transcript_citation.line_number == current_range.end_turn_id:
                current_range.end_turn_id = original_transcript_citation.line_number + 1
                current_range.source_text += original_transcript_citation.text
            else:
                # Save current range and start new one
                ranges.append(current_range)
                current_range = MultiMeetingTranscriptRange(
                    meeting_id=original_transcript_citation.meeting_id,
                    start_turn_id=original_transcript_citation.line_number,
                    end_turn_id=original_transcript_citation.line_number + 1,
                    source_text=original_transcript_citation.text,
                )

        # Save final range if exists
        if current_range is not None:
            ranges.append(current_range)

        # Now create citations for each range
        for curr_range in ranges:
            db_citation = await citation_service.create_citation(
                organization_id=organization_id,
                for_object_id=UUID("00000000-0000-0000-0000-000000000000"),
                for_object_type=object_type,
                source_type=CitationSourceType.MEETING,
                source_id=curr_range.meeting_id,
                created_by_user_id=UUID(settings.intel_hardcoded_user_id),
                metadata=MeetingCitationMetadata(
                    source_text=curr_range.source_text,
                    start_turn_id=curr_range.start_turn_id,
                    end_turn_id=curr_range.end_turn_id,
                ),
                field_name=cited_field.field_name,
                field_item_id=cited_field.field_item_id,
            )
            criteria_citations.append(
                CriteriaCitation(
                    id=db_citation.id,
                    field_name=cited_field.field_name,
                    field_item_id=cited_field.field_item_id,
                )
            )

    return criteria_citations
