from uuid import UUID

from anthropic.types import (
    ContentBlockParam,
    Message,
    MessageParam,
    TextCitation,
)
from langfuse import Langfuse

from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    anthropic_completion,
)
from salestech_be.core.ai.common.helpers.prompt_context_info_retrieval import (
    get_seller_contacts_prompt_input,
)
from salestech_be.core.ai.common.llm_types import ModelTypes
from salestech_be.core.ai.common.types import SourceContext
from salestech_be.core.ai.opportuntity_stages.criteria_definitions import (
    MEDDPICC_DEFINITIONS,
)
from salestech_be.core.ai.opportuntity_stages.llm_calls.criteria_extraction_with_llm import (
    add_criteria_citations_to_db,
    build_block_messages,
    convert_source_object_to_intel_context,
)
from salestech_be.core.citation.service.citation_service import (
    get_citation_service,
)
from salestech_be.core.opportunity_stage_criteria.criteria_parser import (
    parse_criteria_context,
    parse_update_criteria_items,
    populate_update_create_item_ids,
)
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    ContextUpdateTypeEnum,
    CriteriaCitation,
    CriteriaContextUpdateWithRecTypesT,
    CriteriaExtractionSourceObjectId,
    CriteriaItemsUpdateT,
    CriteriaUpdateResult,
    FieldCitationPair,
    FieldInfo,
    ItemsUpdateTypeEnum,
    KeepCriteriaChange,
    LLMCriteriaUpdateRequest,
    LLMCriteriaUpdateResult,
)
from salestech_be.core.prompt.types import PromptUseCase
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore
)

logger = get_logger(__name__)


async def criteria_update_with_llm(
    criteria_update_request: LLMCriteriaUpdateRequest,
) -> LLMCriteriaUpdateResult:
    source_object = criteria_update_request.source_object
    source_context: SourceContext = []
    if isinstance(source_object, CriteriaExtractionSourceObjectId):
        langfuse_session_id = f"opportunity_stage_criteria:{source_object.object_type}:{source_object.object_id}"
        source_context = await convert_source_object_to_intel_context(
            organization_id=criteria_update_request.organization_id,
            pipeline_id=criteria_update_request.pipeline_id,
            langfuse_session_id=langfuse_session_id,
            source_object=source_object,
        )
    else:
        langfuse_session_id = f"opportunity_stage_criteria:multi_meeting_transcript:{criteria_update_request.pipeline_id}"
        source_context = source_object
    request = criteria_update_request.request
    result = await update_criteria_with_llm(
        source_context=source_context,
        previous_context=request.previous_context if request.previous_context else "",
        context_update_type=request.context_update_type,
        items_update_type=request.items_update_type,
        organization_id=criteria_update_request.organization_id,
        langfuse_session_id=langfuse_session_id,
    )

    return LLMCriteriaUpdateResult(result=result)  # type: ignore[arg-type]


async def update_criteria_with_llm(
    source_context: SourceContext,
    previous_context: str,
    context_update_type: type[CriteriaContextUpdateWithRecTypesT] | None,
    items_update_type: type[CriteriaItemsUpdateT] | None,
    organization_id: UUID,
    langfuse_session_id: str,
) -> CriteriaUpdateResult[CriteriaContextUpdateWithRecTypesT, CriteriaItemsUpdateT]:
    db_engine: DatabaseEngine = await get_or_init_db_engine()
    citation_service = get_citation_service(db_engine)
    citations: list[CriteriaCitation] = []
    messages = build_block_messages(source_context)
    seller_contacts = await get_seller_contacts_prompt_input(
        source_context, organization_id
    )

    context_result: CriteriaContextUpdateWithRecTypesT | None = None
    if context_update_type:
        context_llm_response = await make_criteria_context_update_llm_call(
            messages=messages,
            previous_context=previous_context,
            seller_contacts=seller_contacts,
            langfuse_session_id=langfuse_session_id,
            organization_id=organization_id,
            context_name=ContextUpdateTypeEnum.from_update_type(context_update_type),
        )

        if context_llm_response and context_llm_response.content[-1].type == "tool_use":
            context_result = context_update_type.model_validate(
                context_llm_response.content[-1].input
            )
            context = context_result.updated_context  # type: ignore[attr-defined] #TODO: bound by something other than BaseModel
            field_info = parse_criteria_context(context)
            field_citation_pairs: list[FieldCitationPair] = []

            for field in list(field_info):
                field_citation = await create_citation_for_field_update(
                    messages=messages,
                    previous_context=previous_context,
                    field_updated_value=f"({field.field_name}: {getattr(context, field.field_name)})",
                    seller_contacts=seller_contacts,
                    langfuse_session_id=langfuse_session_id,
                    organization_id=organization_id,
                )
                if field_citation:
                    field_citation_pairs.append(
                        FieldCitationPair(field=field, citation=field_citation)
                    )
                else:
                    # Set field to None on the instance
                    logger.warning(
                        "No citation found for updated field, discarding",
                        field_name=field.field_name,
                    )
                    context_dict = context.model_dump()
                    context_dict[field.field_name] = None
                    context_result.updated_context = context.__class__(  # type: ignore[attr-defined]
                        **context_dict
                    )

            citations.extend(
                await add_criteria_citations_to_db(
                    source_context=source_context,
                    field_citation_pairs=field_citation_pairs,
                    organization_id=organization_id,
                    citation_service=citation_service,
                )
            )

    items_result: CriteriaItemsUpdateT | None = None
    if items_update_type:
        items_llm_response = await make_criteria_items_update_llm_call(
            messages=messages,
            previous_context=previous_context,
            seller_contacts=seller_contacts,
            langfuse_session_id=langfuse_session_id,
            organization_id=organization_id,
            items_name=ItemsUpdateTypeEnum.from_update_type(items_update_type),
        )

        if items_llm_response and items_llm_response.content[-1].type == "tool_use":
            items_result = items_update_type.model_validate(
                items_llm_response.content[-1].input
            )
            items_result = populate_update_create_item_ids(items_result=items_result)
            items_field_info = parse_update_criteria_items(
                items_result, items_update_type
            )

            field_citation_pairs = await generate_citations_for_items(
                items_result=items_result,
                items_field_info=items_field_info,
                messages=messages,
                previous_context=previous_context,
                seller_contacts=seller_contacts,
                langfuse_session_id=langfuse_session_id,
                organization_id=organization_id,
            )

            citations.extend(
                await add_criteria_citations_to_db(
                    source_context=source_context,
                    field_citation_pairs=field_citation_pairs,
                    organization_id=organization_id,
                    citation_service=citation_service,
                )
            )

    return CriteriaUpdateResult(
        updated_context=context_result,
        updated_items=items_result,
        citations=citations,
    )


async def make_criteria_context_update_llm_call(
    messages: list[str],
    previous_context: str,
    seller_contacts: str,
    langfuse_session_id: str,
    organization_id: UUID,
    context_name: str,
) -> Message | None:
    try:
        context_update_type = ContextUpdateTypeEnum.from_context_type(context_name)  # type: ignore[var-annotated]
    except ValueError:
        logger.error(f"Invalid context update type: {context_name}")
        return None
    context_dataset_name = f"stage_criteria_context_{context_name}_raw"
    langfuse = Langfuse(
        public_key=settings.langfuse_public_key.get_secret_value(),
        secret_key=settings.langfuse_secret_key.get_secret_value(),
        host=settings.langfuse_host,
    )
    context_definition = MEDDPICC_DEFINITIONS[context_name]
    llm_context_messages = await build_meddpicc_prompt(
        messages, previous_context, context_definition, seller_contacts
    )
    context_trace_name = (
        f"opportunity_stages.update_exit_criteria_context_{context_name}"
    )
    context_llm_response = await anthropic_completion(
        model=ModelTypes.CLAUDE_3_5_SONNET_20241022,
        max_tokens=settings.max_citation_output_tokens,
        messages=llm_context_messages,
        tools=[
            {
                "input_schema": context_update_type.model_json_schema(),
                "name": "update_criteria_context",
                "description": "A tool that updates the criteria context from the given provided context.",
            }
        ],
        metadata=LLMTraceMetadata(
            trace_name=context_trace_name,
            session_id=langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(organization_id),
                prompt_use_case=PromptUseCase.UPDATE_OPPORTUNITY_STAGE_CRITERIA,
            ),
        ),
    )
    try:
        langfuse.create_dataset_item(
            dataset_name=context_dataset_name,
            input={
                "messages": messages,
                "previous_context": previous_context,
                "seller_contacts": seller_contacts,
                "langfuse_session_id": langfuse_session_id,
                "organization_id": organization_id,
                "context_name": context_name,
            },
            expected_output=context_llm_response,
        )
    except Exception as e:
        logger.bind(dataset_name=context_dataset_name).warning(
            f"Error creating dataset item: {e}"
        )
    return context_llm_response


async def make_criteria_items_update_llm_call(
    messages: list[str],
    previous_context: str,
    seller_contacts: str,
    langfuse_session_id: str,
    organization_id: UUID,
    items_name: str,
) -> Message | None:
    try:
        items_update_type = ItemsUpdateTypeEnum.from_context_type(items_name)  # type: ignore[var-annotated]
    except ValueError:
        logger.error(f"Invalid items update type: {items_name}")
        return None
    items_dataset_name = f"stage_criteria_items_{items_name}_raw"
    langfuse = Langfuse(
        public_key=settings.langfuse_public_key.get_secret_value(),
        secret_key=settings.langfuse_secret_key.get_secret_value(),
        host=settings.langfuse_host,
    )
    item_definition = MEDDPICC_DEFINITIONS[items_name]
    llm_items_messages = await build_meddpicc_prompt(
        messages, previous_context, item_definition, seller_contacts, for_items=True
    )
    items_trace_name = f"opportunity_stages.update_exit_criteria_items_{items_name}"
    items_llm_response = await anthropic_completion(
        model=ModelTypes.CLAUDE_3_5_SONNET_20241022,
        max_tokens=settings.max_citation_output_tokens,
        messages=llm_items_messages,
        tools=[
            {
                "input_schema": items_update_type.model_json_schema(),
                "name": "update_criteria_items",
                "description": "A tool that updates the criteria items by being able to create, delete, and update the criteria items.",
            }
        ],
        metadata=LLMTraceMetadata(
            trace_name=items_trace_name,
            session_id=langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(organization_id),
                prompt_use_case=PromptUseCase.UPDATE_OPPORTUNITY_STAGE_CRITERIA,
            ),
        ),
    )
    try:
        langfuse.create_dataset_item(
            dataset_name=items_dataset_name,
            input={
                "messages": messages,
                "previous_context": previous_context,
                "seller_contacts": seller_contacts,
                "langfuse_session_id": langfuse_session_id,
                "organization_id": organization_id,
                "items_name": items_name,
            },
            expected_output=items_llm_response,
        )
    except Exception as e:
        logger.bind(dataset_name=items_dataset_name).warning(
            f"Error creating dataset item: {e}"
        )
    return items_llm_response


async def build_meddpicc_prompt(
    messages: list[str],
    previous_context: str,
    context_definition: str,
    seller_contacts: str,
    for_items: bool = False,
) -> list[MessageParam]:
    message_content: list[ContentBlockParam] = [
        {
            "type": "document",
            "source": {
                "type": "content",
                "content": [
                    {
                        "type": "text",
                        "text": text,
                    }
                    for text in messages
                ],
            },
            "title": "Context",
            "context": "This is the source content to extract criteria from.",
            "citations": {"enabled": True},
        },
    ]
    if seller_contacts:
        message_content.append(
            {
                "type": "text",
                "text": f"This the information about the seller contacts: {seller_contacts}. Anyone in the source content that is not in this list is a buyer contact.",
            },
        )
    if context_definition:
        message_content.append(
            {
                "type": "text",
                "text": f"""
                This is the definition of the current MEDDPICC criteria we are updating: {context_definition}.
                Since MEDDPICC is a framwork for understanding the buyer's needs, focus only on what the buyer said, only using the seller's information to fill in the blanks, not to update MEDDPICC from the seller's perspective.
                The buyer just mentioning something briefly is not enough to update the criteria, instead focus on direct statements or questions the buyer made. Don't make assumptions about the buyer's needs, and only make updates based on the information provided from the buyer directly in the source content.
                Take into account not just the raw content, but also the overall context of the source content, it is not guaranteed that the content is sales oriented, there may only be small parts of it that are sales oriented or not at all, and only those parts should be used to update the criteria.
                """,
            },
        )
    if previous_context:
        message_content.append(
            {
                "type": "text",
                "text": f"This is the current existing value for that MEDDPICC criteria, {previous_context}, please update the criteria from the provided source content. If there is no update, then don't include the field in your response. Keep in mind that other MEDDPICC criteria are being updated as well, so you should not include information related to any other criteria in your response.",
            },
        )
    if for_items:
        message_content.append(
            {
                "type": "text",
                "text": """
                First, check which criteria items need to be added, and add an explanation with a citation for each individual item you are adding. Ensure the generated UUID is 00000000-0000-0000-0000-000000000000.
                Then, check which criteria items need to be deleted, and add an explanation with a citation for each individual item you are deleting.
                Finally, check which criteria items need to be updated, and add an explanation with a citation for each individual item you are updating. For each field in an item you are updating, add what type of update this is in the field_update_types field.
                So, every change you make should have a citation. For example, if you added 2 new items, deleted 1 item, and updated 1 item, you should have 4 citations.
                If there are no provided criteria, then that means this is a new criteria, so you need to create new criteria items. Please ensure the generated UUID is 00000000-0000-0000-0000-000000000000.
                """,
            },
        )
    else:
        message_content.append(
            {
                "type": "text",
                "text": "For each field your are going to update, explain why you are updating it, and provide a citation for your explanation. Only one citation per field is needed.",
            },
        )

    message_content.append(
        {
            "type": "text",
            "text": "Only make changes if they are meaningful and necessary, if the semantic meaning doesn't change, then don't update it.",
        },
    )

    message_params: list[MessageParam] = [
        {
            "role": "user",
            "content": message_content,
        }
    ]
    return message_params


async def create_citation_for_field_update(
    messages: list[str],
    previous_context: str,
    seller_contacts: str,
    field_updated_value: str,
    langfuse_session_id: str,
    organization_id: UUID,
) -> TextCitation | None:
    """
    Create a citation for a field update.
    """
    result = await anthropic_completion(
        model=ModelTypes.CLAUDE_3_5_SONNET_20241022,
        max_tokens=settings.max_citation_output_tokens,
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "document",
                        "source": {
                            "type": "content",
                            "content": [
                                {
                                    "type": "text",
                                    "text": text,
                                }
                                for text in messages
                            ],
                        },
                        "title": "Context",
                        "context": "This is the source content to extract criteria from.",
                        "citations": {"enabled": True},
                    },
                    {
                        "type": "text",
                        "text": f"""
                        This is the information about the seller contacts: {seller_contacts}. Anyone in the source content that is not in this list is a buyer contact.
                        This is the current existing MEDDPICC criteria, {previous_context}.
                        There has been a change to the MEDDPICC criteria, {field_updated_value}.
                        Please provide one citation for where this change could have come from, only from the buyer's words, in the source content. Only one citation should be provided, the most relevant one.
                        Don't make assumptions about the buyer's needs, and only cite information provided from the buyer directly in the source content.
                        Focus on the overall context of the source content, it is not guaranteed that the content is sales oriented, there may only be small parts of it that are sales oriented or not at all.
                        So, a passing mention or comment is not enough to provide a citation, instead focus on direct statements or questions the buyer made within the context of sales only.
                        If the seller's information is used to fill in the blank, then that is not a valid citation.
                        If you cannot find a citation from the buyer, that means this update is not valid, and you should return None.
                        """,
                    },
                ],
            }
        ],
        tools=[
            {
                "input_schema": KeepCriteriaChange.model_json_schema(),
                "name": "keep_criteria_change",
                "description": "A tool that determines if a criteria change should be kept.",
            }
        ],
        metadata=LLMTraceMetadata(
            trace_name="opportunity_stages.citation_for_field_update",
            session_id=langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(organization_id),
                prompt_use_case=PromptUseCase.UPDATE_OPPORTUNITY_STAGE_CRITERIA,
            ),
        ),
    )
    if not result.content:
        return None
    if result.content[-1].type == "tool_use":
        keep_criteria_change = KeepCriteriaChange.model_validate(
            result.content[-1].input
        )
        if not keep_criteria_change.should_keep_change:
            return None
    for content_block in result.content:
        if content_block.type == "text" and content_block.citations:
            return content_block.citations[0]
    return None


async def generate_citations_for_items(
    items_result: CriteriaItemsUpdateT,
    items_field_info: list[FieldInfo],
    messages: list[str],
    previous_context: str,
    seller_contacts: str,
    langfuse_session_id: str,
    organization_id: UUID,
) -> list[FieldCitationPair]:
    """
    Generate citations for all items in create, delete, and update operations.
    Returns a list of FieldCitationPair for items that have valid citations.
    Removes items that don't have valid citations from both items_result and items_field_info.
    """
    field_citation_pairs: list[FieldCitationPair] = []
    groups = [
        ("Created", items_result.create or []),  # type: ignore[attr-defined]
        ("Deleted", items_result.delete or []),  # type: ignore[attr-defined]
        ("Updated", items_result.update or []),  # type: ignore[attr-defined]
    ]

    item_field_info_by_field_item_id = {
        f.field_item_id: f for f in items_field_info if f.field_item_id
    }
    for label, items in groups:
        # Use a copy of the list to allow safe removal
        for item in list(items):
            field = item_field_info_by_field_item_id.get(item.id)
            if not field:
                continue

            field_citation = await create_citation_for_field_update(
                messages=messages,
                previous_context=previous_context,
                field_updated_value=f"({label} item: {item})",
                seller_contacts=seller_contacts,
                langfuse_session_id=langfuse_session_id,
                organization_id=organization_id,
            )
            if field_citation:
                field_citation_pairs.append(
                    FieldCitationPair(field=field, citation=field_citation)
                )
            else:
                logger.warning(
                    "No citation found for updated item, discarding",
                    item=item,
                )
                items.remove(item)
                items_field_info.remove(field)

    return field_citation_pairs
