from salestech_be.core.ai.opportuntity_stages.types import (
    CriteriaConfig,
)
from salestech_be.core.ai.workflows.schema import StageCriteriaType
from salestech_be.core.opportunity_stage_criteria.standard_criteria.competition import (
    CompetitionContextUpdateWithRecTypes,
    Competitor,
    CompetitorItemUpdateMulti,
    CompetitorItemUpdateWithRecTypes,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.decision_criteria import (
    DecisionCriteriaContextUpdateWithRecTypes,
    DecisionCriteriaItem,
    DecisionCriteriaItemUpdateMulti,
    DecisionCriteriaItemUpdateWithRecTypes,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.decision_process import (
    DecisionProcessContextUpdateWithRecTypes,
    DecisionProcessItem,
    DecisionProcessItemUpdateMulti,
    DecisionProcessItemUpdateWithRecTypes,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.identified_pain import (
    IdentifiedPainItem,
    IdentifiedPainItemUpdateMulti,
    IdentifiedPainItemUpdateWithRecTypes,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.metric import (
    MetricContextUpdateWithRecTypes,
    MetricItem,
    MetricItemUpdateMulti,
    MetricItemUpdateWithRecTypes,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.paper_process import (
    PaperProcess,
    PaperProcessItemUpdateMulti,
    PaperProcessItemUpdateWithRecTypes,
)
from salestech_be.core.pipeline.service_api_schema import (
    CreateCompetitorRequest,
    CreateDecisionCriteriaItemRequest,
    CreateDecisionProcessItemRequest,
    CreateIdentifiedPainItemRequest,
    CreateMetricItemRequest,
    CreatePaperProcessItemRequest,
    PatchCompetitionRequest,
    PatchCompetitorRequest,
    PatchDecisionCriteriaItemRequest,
    PatchDecisionCriteriaRequest,
    PatchDecisionProcessItemRequest,
    PatchDecisionProcessRequest,
    PatchIdentifiedPainItemRequest,
    PatchMetricItemRequest,
    PatchMetricRequest,
    PatchPaperProcessItemRequest,
)

DECISION_CRITERIA_CONFIG = CriteriaConfig[
    DecisionCriteriaContextUpdateWithRecTypes,
    DecisionCriteriaItemUpdateMulti,
    DecisionCriteriaItem,
    DecisionCriteriaItemUpdateWithRecTypes,
    PatchDecisionCriteriaRequest,
    CreateDecisionCriteriaItemRequest,
    PatchDecisionCriteriaItemRequest,
](
    get_previous_criteria=lambda service,
    organization_id,
    pipeline_id,
    user_id: service.get_or_create_default_decision_criteria(
        organization_id=organization_id, pipeline_id=pipeline_id, user_id=user_id
    ),
    context_update_type=DecisionCriteriaContextUpdateWithRecTypes,
    items_update_type=DecisionCriteriaItemUpdateMulti,
    patch_request_type=PatchDecisionCriteriaRequest,
    create_ai_recs_from_patch_request=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    rec_type,
    patch_req,
    citations,
    ai_rec_types: service.create_decision_criteria_ai_recs_from_patch_request(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        rec_type=rec_type,
        patch_req=patch_req,
        citations=citations,
        ai_rec_types=ai_rec_types,
    ),
    criteria_item_type=DecisionCriteriaItem,
    create_criteria_item=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    create_req,
    citations: service.create_decision_criteria_item(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        create_req=create_req,
        citations=citations,
    ),
    create_item_request_type=CreateDecisionCriteriaItemRequest,
    delete_criteria_item=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    item_id,
    citations: service.delete_decision_criteria_item(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        item_id=item_id,
        citations=citations,
    ),
    update_item_type=DecisionCriteriaItemUpdateWithRecTypes,
    create_item_ai_recs_from_patch_request=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    rec_type,
    item_id,
    patch_req,
    citations,
    ai_rec_types: service.create_decision_criteria_item_ai_recs_from_patch_request(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        rec_type=rec_type,
        item_id=item_id,
        patch_req=patch_req,
        citations=citations,
        ai_rec_types=ai_rec_types,
    ),
    update_item_request_type=PatchDecisionCriteriaItemRequest,
)

IDENTIFIED_PAIN_CONFIG = CriteriaConfig[
    None,
    IdentifiedPainItemUpdateMulti,
    IdentifiedPainItem,
    IdentifiedPainItemUpdateWithRecTypes,
    None,
    CreateIdentifiedPainItemRequest,
    PatchIdentifiedPainItemRequest,
](
    get_previous_criteria=lambda service,
    organization_id,
    pipeline_id,
    user_id: service.get_or_create_default_identified_pain(
        organization_id=organization_id, pipeline_id=pipeline_id, user_id=user_id
    ),
    items_update_type=IdentifiedPainItemUpdateMulti,
    criteria_item_type=IdentifiedPainItem,
    create_criteria_item=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    create_req,
    citations: service.create_identified_pain_item(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        create_req=create_req,
        citations=citations,
    ),
    create_item_request_type=CreateIdentifiedPainItemRequest,
    delete_criteria_item=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    item_id,
    citations: service.delete_identified_pain_item(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        item_id=item_id,
        citations=citations,
    ),
    update_item_type=IdentifiedPainItemUpdateWithRecTypes,
    create_item_ai_recs_from_patch_request=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    rec_type,
    item_id,
    patch_req,
    citations,
    ai_rec_types: service.create_identified_pain_item_ai_recs_from_patch_request(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        rec_type=rec_type,
        item_id=item_id,
        patch_req=patch_req,
        citations=citations,
        ai_rec_types=ai_rec_types,
    ),
    update_item_request_type=PatchIdentifiedPainItemRequest,
)

METRIC_CONFIG = CriteriaConfig[
    MetricContextUpdateWithRecTypes,
    MetricItemUpdateMulti,
    MetricItem,
    MetricItemUpdateWithRecTypes,
    PatchMetricRequest,
    CreateMetricItemRequest,
    PatchMetricItemRequest,
](
    get_previous_criteria=lambda service,
    organization_id,
    pipeline_id,
    user_id: service.get_or_create_default_metric(
        organization_id=organization_id, pipeline_id=pipeline_id, user_id=user_id
    ),
    context_update_type=MetricContextUpdateWithRecTypes,
    items_update_type=MetricItemUpdateMulti,
    patch_request_type=PatchMetricRequest,
    create_ai_recs_from_patch_request=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    rec_type,
    patch_req,
    citations,
    ai_rec_types: service.create_metric_ai_recs_from_patch_request(
        organization_id=organization_id,
        user_id=user_id,
        pipeline_id=pipeline_id,
        rec_type=rec_type,
        patch_req=patch_req,
        citations=citations,
        ai_rec_types=ai_rec_types,
    ),
    criteria_item_type=MetricItem,
    create_criteria_item=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    create_req,
    citations: service.create_metric_item(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        create_req=create_req,
        citations=citations,
    ),
    create_item_request_type=CreateMetricItemRequest,
    delete_criteria_item=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    item_id,
    citations: service.delete_metric_item(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        item_id=item_id,
        citations=citations,
    ),
    update_item_type=MetricItemUpdateWithRecTypes,
    create_item_ai_recs_from_patch_request=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    rec_type,
    item_id,
    patch_req,
    citations,
    ai_rec_types: service.create_metric_item_ai_recs_from_patch_request(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        rec_type=rec_type,
        item_id=item_id,
        patch_req=patch_req,
        citations=citations,
        ai_rec_types=ai_rec_types,
    ),
    update_item_request_type=PatchMetricItemRequest,
)

PAPER_PROCESS_CONFIG = CriteriaConfig[
    None,
    PaperProcessItemUpdateMulti,
    PaperProcess,
    PaperProcessItemUpdateWithRecTypes,
    None,
    CreatePaperProcessItemRequest,
    PatchPaperProcessItemRequest,
](
    get_previous_criteria=lambda service,
    organization_id,
    pipeline_id,
    user_id: service.get_or_create_default_paper_process(
        organization_id=organization_id, pipeline_id=pipeline_id, user_id=user_id
    ),
    items_update_type=PaperProcessItemUpdateMulti,
    criteria_item_type=PaperProcess,
    create_criteria_item=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    create_req,
    citations: service.create_paper_process_item(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        create_req=create_req,
        citations=citations,
    ),
    create_item_request_type=CreatePaperProcessItemRequest,
    delete_criteria_item=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    item_id,
    citations: service.delete_paper_process_item(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        item_id=item_id,
        citations=citations,
    ),
    update_item_type=PaperProcessItemUpdateWithRecTypes,
    create_item_ai_recs_from_patch_request=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    rec_type,
    item_id,
    patch_req,
    citations,
    ai_rec_types: service.create_paper_process_item_ai_recs_from_patch_request(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        rec_type=rec_type,
        item_id=item_id,
        patch_req=patch_req,
        citations=citations,
        ai_rec_types=ai_rec_types,
    ),
    update_item_request_type=PatchPaperProcessItemRequest,
)

COMPETITOR_CONFIG = CriteriaConfig[
    CompetitionContextUpdateWithRecTypes,
    CompetitorItemUpdateMulti,
    Competitor,
    CompetitorItemUpdateWithRecTypes,
    PatchCompetitionRequest,
    CreateCompetitorRequest,
    PatchCompetitorRequest,
](
    get_previous_criteria=lambda service,
    organization_id,
    pipeline_id,
    user_id: service.get_or_create_default_competition(
        organization_id=organization_id, pipeline_id=pipeline_id, user_id=user_id
    ),
    context_update_type=CompetitionContextUpdateWithRecTypes,
    items_update_type=CompetitorItemUpdateMulti,
    patch_request_type=PatchCompetitionRequest,
    create_ai_recs_from_patch_request=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    rec_type,
    patch_req,
    citations,
    ai_rec_types: service.create_competition_ai_recs_from_patch_request(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        rec_type=rec_type,
        patch_req=patch_req,
        citations=citations,
        ai_rec_types=ai_rec_types,
    ),
    criteria_item_type=Competitor,
    create_criteria_item=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    create_req,
    citations: service.create_competitor(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        create_req=create_req,
        citations=citations,
    ),
    create_item_request_type=CreateCompetitorRequest,
    delete_criteria_item=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    item_id,
    citations: service.delete_competitor(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        competitor_id=item_id,
        citations=citations,
    ),
    update_item_type=CompetitorItemUpdateWithRecTypes,
    create_item_ai_recs_from_patch_request=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    rec_type,
    item_id,
    patch_req,
    citations,
    ai_rec_types: service.create_competitor_ai_recs_from_patch_request(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        rec_type=rec_type,
        competitor_id=item_id,
        patch_req=patch_req,
        citations=citations,
        ai_rec_types=ai_rec_types,
    ),
    update_item_request_type=PatchCompetitorRequest,
)

DECISION_PROCESS_CONFIG = CriteriaConfig[
    DecisionProcessContextUpdateWithRecTypes,
    DecisionProcessItemUpdateMulti,
    DecisionProcessItem,
    DecisionProcessItemUpdateWithRecTypes,
    PatchDecisionProcessRequest,
    CreateDecisionProcessItemRequest,
    PatchDecisionProcessItemRequest,
](
    get_previous_criteria=lambda service,
    organization_id,
    pipeline_id,
    user_id: service.get_or_create_default_decision_process(
        organization_id=organization_id, pipeline_id=pipeline_id, user_id=user_id
    ),
    context_update_type=DecisionProcessContextUpdateWithRecTypes,
    items_update_type=DecisionProcessItemUpdateMulti,
    patch_request_type=PatchDecisionProcessRequest,
    create_ai_recs_from_patch_request=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    rec_type,
    patch_req,
    citations,
    ai_rec_types: service.create_decision_process_ai_recs_from_patch_request(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        rec_type=rec_type,
        patch_req=patch_req,
        citations=citations,
        ai_rec_types=ai_rec_types,
    ),
    criteria_item_type=DecisionProcessItem,
    create_criteria_item=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    create_req,
    citations: service.create_decision_process_item(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        create_req=create_req,
        citations=citations,
    ),
    create_item_request_type=CreateDecisionProcessItemRequest,
    delete_criteria_item=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    item_id,
    citations: service.delete_decision_process_item(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        item_id=item_id,
        citations=citations,
    ),
    update_item_type=DecisionProcessItemUpdateWithRecTypes,
    create_item_ai_recs_from_patch_request=lambda service,
    organization_id,
    pipeline_id,
    user_id,
    rec_type,
    item_id,
    patch_req,
    citations,
    ai_rec_types: service.create_decision_process_item_ai_recs_from_patch_request(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        user_id=user_id,
        rec_type=rec_type,
        item_id=item_id,
        patch_req=patch_req,
        citations=citations,
        ai_rec_types=ai_rec_types,
    ),
    update_item_request_type=PatchDecisionProcessItemRequest,
)

CRITERIA_CONFIG_MAP: dict[StageCriteriaType, CriteriaConfig] = {  # type: ignore[type-arg] #TODO: Add strict typing
    StageCriteriaType.DECISION_CRITERIA: DECISION_CRITERIA_CONFIG,
    StageCriteriaType.IDENTIFIED_PAIN: IDENTIFIED_PAIN_CONFIG,
    StageCriteriaType.METRIC: METRIC_CONFIG,
    StageCriteriaType.PAPER_PROCESS: PAPER_PROCESS_CONFIG,
    StageCriteriaType.COMPETITOR: COMPETITOR_CONFIG,
    StageCriteriaType.DECISION_PROCESS: DECISION_PROCESS_CONFIG,
}
