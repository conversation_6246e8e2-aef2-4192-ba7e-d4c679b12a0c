#!/usr/bin/env python3

import asyncio
import uuid

from salestech_be.core.ai.activities.meeting_pipeline_intel_activities import (
    TaskRequestFromLLM,
)
from salestech_be.core.ai.common.helpers.get_intel_context import get_intel_context
from salestech_be.core.ai.workflows.schema import IntelInput, IntelTriggerObjectType
from salestech_be.db.models.task import (
    TaskPriority,
    TaskType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


async def main() -> None:
    # Initialize DB engine
    await get_or_init_db_engine()

    # Create input
    # activity_input = IntelInput(
    #     object_id=uuid.UUID("dae63e26-e2b7-478b-84a2-d38fc1dbee79"),
    #     object_type=IntelTriggerObjectType.GLOBAL_THREAD,
    #     organization_id=uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
    #     pipeline_id=uuid.UUID("7980a8fd-1066-4ccc-ba48-ee8702d215f8"),
    #     account_ids=[uuid.UUID("fdd3aed2-308a-45c5-b287-9f9ac7c26735")],
    #     langfuse_session_id="test_session_id",
    # )
    activity_input = IntelInput(
        object_id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        object_type=IntelTriggerObjectType.GLOBAL_THREAD,
        organization_id=uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
        pipeline_id=uuid.UUID("e69b6614-f53c-43ab-b651-1e96e0390939"),
        account_ids=[uuid.UUID("d21ebf0e-333b-4d49-86a5-f47c03291a26")],
        langfuse_session_id="test_session_id",
    )

    # activity_input = IntelInput(
    #     object_id=uuid.UUID("7fe73821-3a53-47f9-9928-98a6ddcc1ef4"),
    #     object_type=IntelTriggerObjectType.MEETING,
    #     organization_id=uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
    #     pipeline_id=uuid.UUID("74e79f27-d5fd-4b9f-a128-41feb180bf8d"),
    #     account_ids=[uuid.UUID("53a784b5-cb44-4598-8dca-2fb08a480753")],
    #     langfuse_session_id="test_session_id",
    # )

    intel_context = await get_intel_context(activity_input)
    logger.info(f"intel context {intel_context}")

    task = TaskRequestFromLLM(  # noqa: F841
        title="Reschedule meeting",
        priority=TaskPriority.HIGH,
        type=TaskType.EMAIL,
        note="Test Note",
        email_thread_id=None,
        meeting_id=None,
    )

    # Deprecated (Can no longer run separately from generate_intel_tasks)
    # result = await generate_task_ownership(
    #     intel_input=activity_input,
    #     task=task,
    #     intel_context=intel_context,
    #     workflow_id="test_workflow_id-rodaan1",
    # )
    # logger.info(f"Activity result: {result}")


if __name__ == "__main__":
    asyncio.run(main())
