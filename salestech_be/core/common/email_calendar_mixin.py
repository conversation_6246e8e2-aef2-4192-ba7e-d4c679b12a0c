from sqlite3 import IntegrityError
from uuid import UUID, uuid4

from asyncpg import UniqueViolationError

from salestech_be.common.exception import ConflictResourceError, InvalidArgumentError
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdSelectListIdentifier,
)
from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.ff.feature_flag_service import (
    get_feature_flag_service,
)
from salestech_be.core.ff.types import FeatureFlagRequest
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.organization.service.organization_preference_service import (
    OrganizationPreferenceService,
)
from salestech_be.core.user.service.user_service import UserService
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.db.models.account import Account, AccountStatus
from salestech_be.db.models.contact import (
    CreateContactAccountRoleRequest,
    CreateContactRequest,
    CreateDbContactEmailAccountAssociationRequest,
    CreateDbContactEmailRequest,
    CreateDbContactRequest,
)
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.ree_logging import get_logger
from salestech_be.util.email_utils import (
    format_email_username_as_display_name,
    generate_first_and_last_name,
)
from salestech_be.util.pydantic_types.str import (
    check_same_email_domain,
    validate_domain_name,
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.email.common.utils import (  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PUBLIC_EMAIL_DOMAIN,
    is_system_email,
)
from salestech_be.web.api.organization.schema import (
    ActivityCaptureMode,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class EmailCalendarMixin:
    def __init__(
        self,
        contact_service: ContactService,
        user_service: UserService,
        select_list_service: InternalSelectListService,
        organization_preference_service: OrganizationPreferenceService,
    ):
        self.contact_service = contact_service
        self.user_service = user_service
        self.select_list_service = select_list_service
        self.organization_preference_service = organization_preference_service
        self.ff_service = get_feature_flag_service()

    async def try_create_account_and_contact_for_emails(  # noqa: C901, PLR0912, PLR0915
        self,
        *,
        emails: list[EmailStrLower],
        user_id: UUID,
        organization_id: UUID,
        is_event_sent_by_user: bool,
        capture_mode_override: ActivityCaptureMode | None = None,
    ) -> tuple[list[ContactV2], list[Account]]:
        """
        Based on the organization's config, try to create contact and account if applicable.

        Args:
            emails: List of emails to process
            user_id: User ID
            organization_id: Organization ID
            is_event_sent_by_user: Whether the event is sent by the user

        Returns:
            Tuple of list of contacts and list of accounts that were created, if any
        """
        created_accounts = []
        created_contacts = []

        if not await self.ff_service.is_enabled(
            request=FeatureFlagRequest(
                flag_key="activity-capture-vMay2025",
                organization_id=organization_id,
            ),
            default_value=True,
        ):
            logger.bind(
                emails=emails,
                user_id=user_id,
                organization_id=organization_id,
            ).info("Activity capture feature flag is disabled, skipping")
            return ([], [])

        org_activity_capture_mode = (
            capture_mode_override
            or await self.organization_preference_service.get_organization_activity_capture_mode(
                organization_id=organization_id
            )
        )

        # Early return if activity capture mode is NONE
        if org_activity_capture_mode == ActivityCaptureMode.NONE:
            logger.bind(
                emails=emails,
                user_id=user_id,
                organization_id=organization_id,
            ).info("Activity capture mode is NONE, skipping")
            return ([], [])

        # Early return if activity capture mode is SENT but event is not sent by user
        if (
            org_activity_capture_mode == ActivityCaptureMode.SENT
            and not is_event_sent_by_user
        ):
            logger.bind(
                emails=emails,
                user_id=user_id,
                organization_id=organization_id,
            ).info(
                "Activity capture mode is SENT, but event is not sent by user, skipping"
            )
            return ([], [])

        # For other activity capture modes, need some prep work, to get list of domains and emails to process
        emails = [email for email in emails if not is_system_email(email)]
        if not emails:
            logger.bind(
                emails=emails,
                user_id=user_id,
                organization_id=organization_id,
            ).info("No emails to process")
            return ([], [])

        user = (
            await self.user_service.user_repository.find_user_by_id_and_organization_id(
                user_id=user_id,
                organization_id=organization_id,
            )
        )
        if not user:
            logger.bind(
                emails=emails,
                user_id=user_id,
                organization_id=organization_id,
            ).info("User not found")
            return ([], [])

        external_domains: list[str] = []
        external_domain_emails = []
        blocked_domains = await self.organization_preference_service.get_organization_activity_capture_blocked_domains(
            organization_id=organization_id
        )
        blocked_emails = await self.organization_preference_service.get_organization_activity_capture_blocked_emails(
            organization_id=organization_id
        )
        for email in emails:
            domain_name = validate_domain_name(email.split("@")[1])
            if domain_name in PUBLIC_EMAIL_DOMAIN:
                # only public domain names, skip
                continue

            if domain_name in blocked_domains:
                # blocked domains, skip
                logger.bind(
                    email=email,
                ).info("Activity capture: Skipping blocked domain")
                continue

            if check_same_email_domain([email, user.email]):
                # internal user emails, skip
                continue

            external_domains.append(email.split("@")[1])
            external_domain_emails.append(email)

        # Given the external domains and email addresses, record accounts if they don't exist yet
        if await self.ff_service.is_enabled(
            request=FeatureFlagRequest(
                flag_key="activity-capture-vMay2025-fetch-archive-account",
                organization_id=organization_id,
            ),
            default_value=False,
        ):
            # no interface from account service to use yet, use contact service instead
            existing_accounts = await self.contact_service.account_repository.find_accounts_by_domain_names(
                organization_id=organization_id,
                domain_names=external_domains,
                exclude_locked_by_integrity_jobs=True,
                exclude_deleted_or_archived=False,
            )
        else:
            # no interface from account service to use yet, use contact service instead
            existing_accounts = await self.contact_service.account_repository.find_accounts_by_domain_names(
                organization_id=organization_id,
                domain_names=external_domains,
                exclude_locked_by_integrity_jobs=True,
            )

        existing_domains_for_account = {
            account.domain_name for account in existing_accounts if account.domain_name
        }
        logger.bind(
            user_id=user_id,
            user_email=user.email,
            organization_id=organization_id,
            emails=emails,
            org_activity_capture_mode=org_activity_capture_mode,
            is_event_sent_by_user=is_event_sent_by_user,
            external_domains=external_domains,
            existing_accounts=existing_accounts,
            existing_domains_for_account=existing_domains_for_account,
        ).info("Activity capture account processing")
        for external_domain in external_domains:
            if external_domain not in existing_domains_for_account:
                if org_activity_capture_mode == ActivityCaptureMode.ALL or (
                    org_activity_capture_mode == ActivityCaptureMode.SENT
                    and is_event_sent_by_user
                ):
                    # create new account if activity capture mode is ALL or SENT and event is sent by user
                    try:
                        account_created = await self.contact_service.account_repository.insert_account(
                            db_account=Account(
                                id=uuid4(),
                                organization_id=organization_id,
                                display_name=external_domain,
                                domain_name=external_domain,
                                status=AccountStatus.TARGET,
                                owner_user_id=user_id,
                                created_at=zoned_utc_now(),
                                created_by_user_id=user_id,
                                created_source=CreatedSource.ACTIVITY_CAPTURE,
                            ),
                            db_address=None,
                        )
                        logger.bind(
                            account_id=account_created.id,
                            user_id=user_id,
                            organization_id=organization_id,
                        ).info("Activity capture account created")
                        existing_accounts.append(account_created)
                        created_accounts.append(account_created)
                    except (UniqueViolationError, IntegrityError) as e:
                        account_found = await self.contact_service.account_repository.find_account_by_domain_name(
                            domain_name=external_domain,
                            organization_id=organization_id,
                            exclude_locked_by_integrity_jobs=True,
                        )
                        if account_found:
                            # covers potential race condition where multiple threads try to create the same account
                            existing_accounts.append(account_found)
                            logger.bind(
                                account_id=account_found.id,
                                user_id=user_id,
                                organization_id=organization_id,
                            ).info("Activity capture account found")
                        else:
                            # covers `locked_by_integrity_jobs` case where we should raise the error for caller to handle
                            # for email/calendar sync activities, it's handled by retrying
                            raise e
                elif org_activity_capture_mode == ActivityCaptureMode.DOMAIN:
                    # noop, for code readability
                    # domain mode will not create new account
                    continue
                else:
                    raise ValueError("Invalid condition")

        # build domain to account mapping for contact creation later
        domain_to_account = {
            account.domain_name: account
            for account in existing_accounts
            if account.domain_name
        }

        # Given the external domains and email addresses, record contacts if they don't exist yet
        # Because account will be created depending on activity capture mode, we use account existence to gate contact creation
        # Basically, every contact creation is now effectively a DOMAIN mode.
        existing_contact_emails = (
            await self.contact_service.find_contact_email_by_emails(
                emails=external_domain_emails,
                organization_id=organization_id,
            )
        )
        existing_contact_email_list = [
            contact.email for contact in existing_contact_emails if contact.email
        ]
        logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            emails=emails,
            external_domain_emails=external_domain_emails,
            existing_contact_emails=existing_contact_emails,
            existing_contact_email_list=existing_contact_email_list,
        ).info("Activity capture contact processing")
        for external_domain_email in external_domain_emails:
            if (
                external_domain_email not in existing_contact_email_list
                and external_domain_email not in blocked_emails
            ):
                # assume the first email / contact creation will set the primary email too
                domain_name = external_domain_email.split("@")[1]
                existing_account = domain_to_account.get(domain_name)
                if existing_account:
                    try:
                        contact_created = (
                            await self.get_or_create_contact_with_primary_email(
                                email=external_domain_email,
                                existing_account=existing_account,
                                organization_id=organization_id,
                                user_id=user_id,
                            )
                        )
                        created_contacts.append(contact_created)
                        logger.bind(
                            contact_id=contact_created.id,
                            user_id=user_id,
                            organization_id=organization_id,
                        ).info("Activity capture contact created")
                    except Exception as e:
                        # Fail-open for contact creation
                        # This can happen (ie ResourceNotFound) if the account is deleted or archived
                        # Revisit once contact_service is refactored and allows creation
                        # for archived accounts
                        logger.bind(
                            email=external_domain_email,
                            domain_name=domain_name,
                            organization_id=organization_id,
                            user_id=user_id,
                            exception=e,
                        ).warning("Failed to create contact for domain name")
                else:
                    logger.bind(
                        email=external_domain_email,
                        domain_name=domain_name,
                    ).warning("No account found for domain name, consistency issue?")

        return (created_contacts, created_accounts)

    async def try_create_placeholder_user_for_emails(
        self, *, emails: list[EmailStrLower], user_id: UUID, organization_id: UUID
    ) -> list[OrganizationUserV2]:
        """
        Try to create placeholder user for emails from the same org domain.

        Same logic from the legacy `create_stranger_contact_and_placeholder_user`

        Args:
            emails: List of emails to process
            user_id: User ID
            organization_id: Organization ID

        Returns:
            List of OrganizationUserV2 if created or found
        """

        logger.bind(
            emails=emails,
            user_id=user_id,
            organization_id=organization_id,
        ).info("[try_create_placeholder_user_for_emails] Start processing")

        organization_user_results: list[OrganizationUserV2] = []

        emails = [email for email in emails if not is_system_email(email)]
        if not emails:
            # Early return to save DB lookup
            return organization_user_results

        user = (
            await self.user_service.user_repository.find_user_by_id_and_organization_id(
                user_id=user_id,
                organization_id=organization_id,
            )
        )
        if not user:
            return []

        # Create hidden user for same domain emails
        same_domain_emails = [
            email
            for email in emails
            if check_same_email_domain([email, user.email])
            and validate_domain_name(email.split("@")[1]) not in PUBLIC_EMAIL_DOMAIN
        ]
        for email in same_domain_emails:
            org_user = await self.user_service.create_hidden_user_and_organization_association_v2(
                email=email, organization_id=organization_id
            )
            organization_user_results.append(org_user)

        return organization_user_results

    async def create_stranger_contact_and_placeholder_user(
        self, emails: list[EmailStrLower], user_id: UUID, organization_id: UUID
    ) -> tuple[list[ContactV2], list[OrganizationUserV2]]:
        logger.bind(
            emails=emails,
            user_id=user_id,
            organization_id=organization_id,
        ).info("[create_stranger_contact_and_placeholder_user] Start processing")
        contact_results = []
        organization_user_results = []

        emails = [email for email in emails if not is_system_email(email)]

        user = (
            await self.user_service.user_repository.find_user_by_id_and_organization_id(
                user_id=user_id,
                organization_id=organization_id,
            )
        )
        if not user:
            return [], []

        # 1. create hidden user for same domain emails
        same_domain_emails = [
            email
            for email in emails
            if check_same_email_domain([email, user.email])
            and validate_domain_name(email.split("@")[1]) not in PUBLIC_EMAIL_DOMAIN
        ]

        for email in same_domain_emails:
            org_user = await self.user_service.create_hidden_user_and_organization_association_v2(
                email=email, organization_id=organization_id
            )
            organization_user_results.append(org_user)

        # 2. get or create contacts for the others
        not_same_domain_emails = list(set(emails) - set(same_domain_emails))
        domain_name_list = [
            domain
            for email in not_same_domain_emails
            if (domain := validate_domain_name(email.split("@")[1]))
        ]

        existing_accounts = (
            await self.contact_service.account_repository.find_accounts_by_domain_names(
                organization_id=organization_id,
                domain_names=domain_name_list,
            )
        )

        for email in not_same_domain_emails:
            domain_name = validate_domain_name(email.split("@")[1])
            existing_account = None
            if domain_name and domain_name not in PUBLIC_EMAIL_DOMAIN:
                existing_account = next(
                    (
                        account
                        for account in existing_accounts
                        if account.domain_name and account.domain_name == domain_name
                    ),
                    None,
                )
            if existing_account:
                logger.info(
                    f"[create_stranger_contact_and_placeholder_user] found existing account {existing_account} with domain_name: {domain_name}"
                )
            default_stage_list = await self.select_list_service.find_default_select_list_dto_by_application_code_name(
                organization_id=organization_id,
                application_code_name=StdSelectListIdentifier.contact_stage,
            )
            try:
                if (
                    not default_stage_list
                    or not default_stage_list.default_or_initial_active_value
                ):
                    raise InvalidArgumentError(
                        "Stage is required for creating a contact, however "
                        "the organization does not have a default stage set."
                    )
                default_value = default_stage_list.default_or_initial_active_value

                display_name = format_email_username_as_display_name(
                    email.split("@")[0]
                )
                first_name, last_name = generate_first_and_last_name(display_name)

                contact = await self.contact_service.create_contact_with_contact_channels(
                    organization_id=organization_id,
                    user_id=user_id,
                    create_contact_with_contact_channel_request=CreateContactRequest(
                        contact=CreateDbContactRequest(
                            display_name=display_name,
                            first_name=first_name if first_name else None,
                            last_name=last_name if last_name else None,
                            owner_user_id=user_id,
                            created_by_user_id=user_id,
                            stage_id=default_value.id,
                        ),
                        contact_emails=[
                            CreateDbContactEmailRequest(
                                email=email,
                                is_contact_primary=True,
                            )
                        ],
                        contact_account_roles=[
                            CreateContactAccountRoleRequest(
                                account_id=existing_account.id, is_primary_account=True
                            )
                        ]
                        if existing_account
                        else [],
                        overwrite_archived_emails=True,
                    ),
                )
            except ConflictResourceError:
                email_to_contacts = (
                    await self.contact_service.find_contacts_by_primary_emails(
                        primary_emails={email.lower()},
                        organization_id=organization_id,
                    )
                )
                contact = email_to_contacts[email.lower()]

            contact_results.append(contact)
        return contact_results, organization_user_results

    async def get_or_create_contact_with_primary_email(
        self,
        *,
        email: EmailStrLower,
        existing_account: Account,
        organization_id: UUID,
        user_id: UUID,
    ) -> ContactV2:
        """
        Get or create contact with primary email for a given account (company)

        Args:
            email: Email address
            existing_account: Account
            organization_id: Organization ID
            user_id: User ID

        Returns:
            ContactV2
        """
        default_stage_list = await self.select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        try:
            if (
                not default_stage_list
                or not default_stage_list.default_or_initial_active_value
            ):
                raise InvalidArgumentError(
                    "Stage is required for creating a contact, however "
                    "the organization does not have a default stage set."
                )
            default_value = default_stage_list.default_or_initial_active_value
            display_name = format_email_username_as_display_name(email.split("@")[0])
            first_name, last_name = generate_first_and_last_name(display_name)
            contact = await self.contact_service.create_contact_with_contact_channels(
                organization_id=organization_id,
                user_id=user_id,
                create_contact_with_contact_channel_request=CreateContactRequest(
                    contact=CreateDbContactRequest(
                        display_name=display_name,
                        first_name=first_name if first_name else None,
                        last_name=last_name if last_name else None,
                        owner_user_id=user_id,
                        created_by_user_id=user_id,
                        stage_id=default_value.id,
                        created_source=CreatedSource.ACTIVITY_CAPTURE,
                    ),
                    contact_emails=[
                        CreateDbContactEmailRequest(
                            email=email,
                            is_contact_primary=True,
                            email_account_associations=[
                                CreateDbContactEmailAccountAssociationRequest(
                                    account_id=existing_account.id,
                                    is_contact_account_primary=True,
                                )
                            ],
                        )
                    ],
                    contact_account_roles=[
                        CreateContactAccountRoleRequest(
                            account_id=existing_account.id, is_primary_account=True
                        )
                    ]
                    if existing_account
                    else [],
                    overwrite_archived_emails=True,
                ),
            )
        except ConflictResourceError:
            email_to_contacts = (
                await self.contact_service.find_contacts_by_primary_emails(
                    primary_emails={email.lower()},
                    organization_id=organization_id,
                )
            )
            contact = email_to_contacts[email.lower()]

        return contact
