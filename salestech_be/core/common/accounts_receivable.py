from enum import StrEnum


class AccountsReceivable(StrEnum):
    """
    INCLUDED_IN_PLAN is what <PERSON><PERSON> will purchase on behalf a the customer, as part of their plan offering.

    INVOICED_TO_CUSTOMER is what <PERSON><PERSON> will purchase on behalf of a customer, and invoice them either as a pass-through, or value-add.
    """

    INCLUDED_IN_PLAN = "INCLUDED_IN_PLAN"
    INVOICED_TO_CUSTOMER = "INVOICED_TO_CUSTOMER"
