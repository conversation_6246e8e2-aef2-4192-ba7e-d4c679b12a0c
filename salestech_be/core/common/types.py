from __future__ import annotations

import dataclasses
import inspect
from abc import ABC
from collections.abc import Mapping
from dataclasses import dataclass
from datetime import datetime
from types import NoneType
from typing import Annotated, Any, ClassVar, Self, assert_never
from uuid import UUID

import pytz
from frozendict import frozendict
from pydantic import BaseModel, ConfigDict, Field, computed_field, model_validator
from pydantic.fields import FieldInfo
from pydantic.json_schema import SkipJsonSchema
from pydantic_extra_types.timezone_name import TimeZoneName

from salestech_be.common.schema_manager.std_object_field_identifier import (
    AddressField,
    AISuggestedValueField,
    MeetingBotStatusEventField,
    MeetingParticipantField,
    StdFieldIdentifierEnum,
    StdObjectIdentifiers,
    UserOrganizationProfileField,
)
from salestech_be.common.type.metadata.common import (
    CustomObjectIdentifier,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    BooleanCheckboxFieldProperty,
    CustomFieldTypeProperty,
    StandardFieldTypeProperty,
    TextFieldProperty,
    TimestampFieldProperty,
)
from salestech_be.common.type.metadata.field.field_value import (
    PresentableFieldValue,
)
from salestech_be.common.type.metadata.schema import (
    InboundRelationship,
    ObjectTypeProperty,
    OutboundRelationship,
    SkipDescriptor,
    StandardFieldDescriptor,
    StandardObjectDescriptor,
)
from salestech_be.core.common.property_metadata import PropertyMetadata
from salestech_be.db.models.address import (
    Address as DbAddress,
)
from salestech_be.db.models.address import (
    AddressCreateRequest,
    AddressNoFieldDefinedError,
    AddressUpdate,
)
from salestech_be.db.models.user_organization_association import (
    UserOrganizationProfile as DbUserOrganizationProfile,
)
from salestech_be.util.pydantic_types.parser import (
    CollectionType,
    ParsedType,
    parse_pydantic_field_type,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime

GenericDataMap = Mapping[UUID, Any]  # type: ignore[explicit-any] # TODO: fix-any-annotation
CustomFieldDataMap = Mapping[UUID, PresentableFieldValue]


@dataclass(frozen=True, kw_only=True)
class FieldMetadata:
    type_property: StandardFieldTypeProperty
    # This field is used to indicate that the field is enriched at runtime.
    # It is optional and defaults to False.
    is_enriched_field: bool = False


class DomainModel(BaseModel, ABC):
    model_config = ConfigDict(frozen=True)

    object_id: ClassVar[StandardObjectIdentifier]
    object_display_name: ClassVar[str] = ""
    is_junction_object: ClassVar[bool] = False
    field_name_provider: ClassVar[type[StdFieldIdentifierEnum]]
    inbound_relationships: ClassVar[tuple[InboundRelationship, ...]] = ()
    outbound_relationships: ClassVar[tuple[OutboundRelationship, ...]] = ()
    field_metadata_by_name: ClassVar[Mapping[StdFieldIdentifierEnum, FieldMetadata]] = (
        frozendict()
    )
    parsed_type_info_by_name: ClassVar[Mapping[StdFieldIdentifierEnum, ParsedType]] = (
        frozendict()
    )

    _is_base_domain_model: ClassVar[bool] = False

    property_metadata: Annotated[
        list[PropertyMetadata] | None,
        Field(
            description="Metadata about the property",
            deprecated="Do not use this field yet, under development",
        ),
        SkipDescriptor(
            reason="container for property metadata",
        ),
    ] = None

    @classmethod
    def __pydantic_init_subclass__(cls, **kwargs: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().__pydantic_init_subclass__(**kwargs)
        if not cls._is_base_domain_model:
            cls._validate_subclass_configs()
            cls.field_metadata_by_name, cls.parsed_type_info_by_name = (
                cls._ensure_field_metadata()
            )
            cls._ensure_all_field_metadata_populated()
            # Ensure the object descriptor is valid by invoking it once during subclassing.
            _ = cls.object_descriptor()
        else:
            cls._is_base_domain_model = False

    @classmethod
    def object_descriptor(cls) -> StandardObjectDescriptor:
        return StandardObjectDescriptor(
            object_identifier=cls.object_id,
            object_display_name=cls.object_display_name,
            is_junction_object=cls.is_junction_object,
            fields=tuple(
                StandardFieldDescriptor(
                    field_identifier=f_name.identifier,
                    field_type_property=f_meta.type_property,
                )
                for f_name, f_meta in cls.field_metadata_by_name.items()
            ),
            inbound_relationships=cls.inbound_relationships,
            outbound_relationships=cls.outbound_relationships,
            object_type_property=ObjectTypeProperty(),
        )

    @classmethod
    def get_std_field_parsed_type(cls, field_name: str) -> ParsedType:
        if field_name not in cls.field_name_provider:
            raise ValueError(
                f"Field {field_name} not found in field_name_provider {cls.field_name_provider}"
            )
        return cls.parsed_type_info_by_name[cls.field_name_provider(field_name)]

    @classmethod
    def get_field_metadata(cls, field_name: str) -> FieldMetadata:
        if field_name not in cls.field_name_provider:
            raise ValueError(
                f"Field {field_name} not found in field_name_provider {cls.field_name_provider}"
            )
        return cls.field_metadata_by_name[cls.field_name_provider(field_name)]

    @classmethod
    def _validate_subclass_configs(cls) -> None:
        if not cls.object_display_name:
            raise TypeError(
                f"Class {cls.__name__} must define a `object_display_name` "
                "attribute of type `str`."
            )
        if not isinstance(getattr(cls, "object_id", None), StandardObjectIdentifier):
            raise TypeError(
                f"Class {cls.__name__} must define a `object_id` "
                "attribute of type `StandardObjectIdentifier`."
            )
        if not issubclass(
            getattr(cls, "field_name_provider", NoneType),
            StdFieldIdentifierEnum,
        ):
            raise TypeError(
                f"Class {cls.__name__} must define a `field_name_provider` "
                "attribute of type `StdFieldIdentifierEnum`."
            )
        if not isinstance(
            in_r := getattr(cls, "inbound_relationships", None), tuple
        ) or not all(
            isinstance(relationship, InboundRelationship) for relationship in in_r
        ):
            raise TypeError(
                f"Class {cls.__name__} must define a `inbound_relationships` "
                "attribute of type `tuple[InboundRelationship, ...]`."
            )
        if not isinstance(
            out_r := getattr(cls, "outbound_relationships", None), tuple
        ) or not all(
            isinstance(relationship, OutboundRelationship) for relationship in out_r
        ):
            raise TypeError(
                f"Class {cls.__name__} must define a `outbound_relationships` "
                "attribute of type `tuple[OutboundRelationship, ...]`."
            )

    @classmethod
    def _ensure_field_metadata(
        cls,
    ) -> tuple[
        Mapping[StdFieldIdentifierEnum, FieldMetadata],
        Mapping[StdFieldIdentifierEnum, ParsedType],
    ]:
        field_metadata_by_name: dict[StdFieldIdentifierEnum, FieldMetadata] = {}
        parsed_type_by_field_name: dict[StdFieldIdentifierEnum, ParsedType] = {}

        model_field_to_check: set[str] = set()

        for f_name, f_info in cls.model_fields.items():
            all_metadata = f_info.metadata
            if not all_metadata:
                raise TypeError(
                    f"Field {f_name} has no metadata defined for {cls.__name__}"
                )
            if any(isinstance(am, SkipDescriptor) for am in all_metadata):
                # Allow to use skip descriptor intentionally skip field metadata for cerntain "internal" or "beta" fields
                continue
            model_field_to_check.add(f_name)
            field_metadata = next(
                (am for am in all_metadata if isinstance(am, FieldMetadata)),
                None,
            )
            if not field_metadata:
                raise TypeError(
                    f"Field {f_name} has no FieldMetadata defined for {cls.__name__}"
                )

            if f_name not in cls.field_name_provider:
                raise TypeError(
                    f"Field {f_name} has a FieldMetadata for {cls.__name__}, but the field_name is not defined in {cls.field_name_provider}."
                )
            field_metadata, field_parsed_type = cls._ensure_field_metadata_correctness(
                field_name=f_name, field_info=f_info, field_metadata=field_metadata
            )
            field_metadata_by_name[cls.field_name_provider(str(f_name))] = (
                field_metadata
            )
            parsed_type_by_field_name[cls.field_name_provider(str(f_name))] = (
                field_parsed_type
            )

        field_provider_fields = set(cls.field_name_provider)
        redundant_model_fields: set[Any] = (  # type: ignore[explicit-any] # TODO: fix-any-annotation
            model_field_to_check.difference(field_provider_fields) or set()
        )
        redundant_provider_fields: set[Any] = (  # type: ignore[explicit-any] # TODO: fix-any-annotation
            field_provider_fields.difference(model_field_to_check) or set()
        )

        if redundant_provider_fields or redundant_model_fields:
            raise TypeError(
                "A DomainModel subclass and its field_name_provider must have the same "
                "set of fields defined WITHOUT SkipDescriptor() "
                f"[Model: {cls.__name__}] [FieldProviderEnum: {cls.field_name_provider}] "
                f"[Redundant Model Fields: {redundant_model_fields}] "
                f"[Redundant Provider Enum Fields: {redundant_provider_fields}]"
            )
        return frozendict[StdFieldIdentifierEnum, FieldMetadata](
            field_metadata_by_name
        ), frozendict[StdFieldIdentifierEnum, ParsedType](parsed_type_by_field_name)

    @classmethod
    def _ensure_field_metadata_correctness(
        cls, *, field_name: str, field_info: FieldInfo, field_metadata: FieldMetadata
    ) -> tuple[FieldMetadata, ParsedType]:
        parsed_type = parse_pydantic_field_type(cls, field_name=field_name)
        is_required = field_info.is_required()
        is_nullable = parsed_type.nullable

        _field_is_required = "is_required"
        _field_is_nullable = "is_nullable"
        if _field_is_required not in field_metadata.type_property.model_fields:
            raise TypeError(
                f"{type(field_metadata.type_property)} doesn't define {cls.__name__}.{_field_is_required}"
            )
        if _field_is_nullable not in field_metadata.type_property.model_fields:
            raise TypeError(
                f"{type(field_metadata.type_property)} doesn't define {cls.__name__}.{_field_is_nullable}"
            )

        if singular_type := parsed_type.type_if_singular_or_nullable_singular:
            cls._validate_pydantic_model_fields(field_name=field_name, tp=singular_type)

        if parsed_type.collection_type:
            match parsed_type.collection_type:
                case CollectionType.LIST | CollectionType.SEQUENCE | CollectionType.SET:
                    cls._validate_pydantic_model_fields(
                        field_name=field_name,
                        tp=parsed_type.collection_element_type.type_if_singular_or_nullable_singular
                        if parsed_type.collection_element_type
                        else None,
                    )
                case CollectionType.DICT | CollectionType.MAPPING:
                    cls._validate_pydantic_model_fields(
                        field_name=field_name,
                        tp=parsed_type.value_type.type_if_singular_or_nullable_singular
                        if parsed_type.value_type
                        else None,
                    )
                case CollectionType.TUPLE:
                    for tuple_type in parsed_type.tuple_element_types:
                        cls._validate_pydantic_model_fields(
                            field_name=field_name,
                            tp=tuple_type.type_if_singular_or_nullable_singular
                            if tuple_type
                            else None,
                        )
                case _ as unreachable:
                    assert_never(unreachable)

        return dataclasses.replace(
            field_metadata,
            type_property=field_metadata.type_property.model_copy(
                update={
                    _field_is_required: is_required,
                    _field_is_nullable: is_nullable,
                }
            ),
        ), parsed_type

    @classmethod
    def _validate_pydantic_model_fields(
        cls, *, field_name: str, tp: type | None
    ) -> None:
        if not tp:
            return
        if not inspect.isclass(tp):
            return
        if not issubclass(tp, BaseModel):
            return
        if not issubclass(tp, DomainModel):
            raise TypeError(
                f"{cls.__name__}.{field_name} is a pydantic model {tp.__name__} but not a subclass of {DomainModel.__name__}, this is not allowed"
            )

    @classmethod
    def _ensure_all_field_metadata_populated(cls) -> None:
        if not cls.field_metadata_by_name:
            raise TypeError(
                f"field_metadata hasn't been initialized for {cls.__name__}"
            )


class CustomizableDomainModel(DomainModel, ABC):
    _is_base_domain_model = True

    custom_field_data: Annotated[
        GenericDataMap | None,
        SkipDescriptor(
            reason="envelop to contain custom field, hence no descriptor needed"
        ),
    ] = None

    # SkipJsonSchema
    custom_field_data_v2: Annotated[
        SkipJsonSchema[CustomFieldDataMap | None],
        Field(exclude=True),
        SkipDescriptor(
            reason="envelop to contain custom field, hence no descriptor needed"
        ),
    ] = None

    @computed_field(  # type: ignore[prop-decorator]
        title="Custom Field Requested",
        description="""Indicates if custom field data was requested.
        When this value is `True`:
            the `custom_field_data` field will be populated as a ["field_id" : "field_value"] map.
        When this value is `False`:
            the `custom_field_data` field will be set to `None`.
        """,
        return_type=bool,
        repr=True,
    )
    @property
    def custom_field_requested(self) -> bool:
        return self.custom_field_data is not None

    def get_presentable_custom_field_data(
        self, field_id: UUID
    ) -> PresentableFieldValue | None:
        return (
            self.custom_field_data_v2.get(field_id)
            if self.custom_field_data_v2
            else None
        )

    def get_generic_custom_field_data(self, field_id: UUID) -> Any | None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.custom_field_data.get(field_id) if self.custom_field_data else None


class Address(DomainModel):
    """Address Object."""

    object_id = StdObjectIdentifiers.address.identifier
    object_display_name = "Address"
    field_name_provider = AddressField

    street_one: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Street One",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ] = None

    street_two: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Street Two",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ] = None
    zip_code: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Zip Code",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ] = None
    city: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="City",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ] = None
    state: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="State",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ] = None
    country: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Country",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ] = None

    @model_validator(mode="after")
    def validate_at_least_one_field(self) -> Self:
        """Ensure at least one address field is not None."""
        address_fields = [
            self.street_one,
            self.street_two,
            self.zip_code,
            self.city,
            self.state,
            self.country,
        ]

        if all(field is None for field in address_fields):
            raise AddressNoFieldDefinedError(
                "At least one address field must be provided"
            )

        return self

    @staticmethod
    def map_from_db(db_address: DbAddress) -> Address:
        return Address(
            street_one=db_address.street_one,
            street_two=db_address.street_two,
            zip_code=db_address.zip_code,
            city=db_address.city,
            state=db_address.state,
            country=db_address.country,
        )

    @staticmethod
    def update_database_fields(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        address: Address,
        user_id: UUID,
    ) -> dict[str, Any]:
        return {
            "street_one": address.street_one,
            "street_two": address.street_two,
            "zip_code": address.zip_code,
            "city": address.city,
            "state": address.state,
            "country": address.country,
            "updated_at": datetime.now(pytz.utc),
            "updated_by_user_id": user_id,
        }

    def to_db_address_create_request(
        self, created_by_user_id: UUID, organization_id: UUID
    ) -> AddressCreateRequest:
        return AddressCreateRequest(
            street_one=self.street_one,
            street_two=self.street_two,
            zip_code=self.zip_code,
            city=self.city,
            state=self.state,
            country=self.country,
            created_by_user_id=created_by_user_id,
            organization_id=organization_id,
        )

    def to_db_address_update(self, user_id: UUID) -> AddressUpdate:
        return AddressUpdate(
            street_one=self.street_one,
            street_two=self.street_two,
            zip_code=self.zip_code,
            city=self.city,
            state=self.state,
            country=self.country,
            updated_by_user_id=user_id,
        )


class UserOrganizationProfile(DomainModel):
    """
    User Organization Profile Object.
    """

    object_id = StdObjectIdentifiers.user_organization_profile.identifier
    object_display_name = "User Organization Profile"
    field_name_provider = UserOrganizationProfileField

    timezone: Annotated[
        TimeZoneName | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Timezone",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    location: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Location",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    title: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Title",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    keywords: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Keywords",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    is_task_digest_enabled: Annotated[
        bool | None,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Is Task Digest Enabled",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    meeting_bot_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Meeting Bot Name",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ] = None

    @staticmethod
    def map_from_db(db_profile: DbUserOrganizationProfile) -> UserOrganizationProfile:
        return UserOrganizationProfile(
            timezone=db_profile.timezone,
            location=db_profile.location,
            title=db_profile.title,
            keywords=db_profile.keywords,
            is_task_digest_enabled=db_profile.is_task_digest_enabled,
            meeting_bot_name=db_profile.meeting_bot_name,
        )


class MeetingParticipant(DomainModel):
    """
    Meeting Participant Field
    """

    object_id = StdObjectIdentifiers.meeting_participant.identifier
    object_display_name = "Meeting Participant"
    field_name_provider = MeetingParticipantField

    name: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    is_host: Annotated[
        bool,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Is Host",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    platform: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Platform",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]


class MeetingBotStatusEvent(DomainModel):
    object_id = StdObjectIdentifiers.meeting_bot_status_event.identifier
    object_display_name = "Meeting Bot Status Event"
    field_name_provider = MeetingBotStatusEventField

    status: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Status",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    status_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Status At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    sub_code: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Status Sub Code",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None


@dataclass(frozen=True, kw_only=True)
class CustomFieldMetadata:
    type_property: CustomFieldTypeProperty


class CustomDomainModel(BaseModel, ABC):
    model_config = ConfigDict(frozen=True)

    object_id: ClassVar[CustomObjectIdentifier]
    object_display_name: ClassVar[str] = ""
    field_metadata_by_name: ClassVar[Mapping[UUID, CustomFieldMetadata]] = frozendict()
    inbound_relationships: ClassVar[tuple[InboundRelationship, ...]] = ()
    outbound_relationships: ClassVar[tuple[OutboundRelationship, ...]] = ()
    _is_base_domain_model: ClassVar[bool] = False


class UserAuthContext(BaseModel):
    """
    User Authorization Context.  Provides information about claims and groups.
    """

    organization_id: UUID
    user_id: UUID
    groups: list[str] = []

    @property
    def is_admin(self) -> bool:
        return "admin" in self.groups or self.is_super_admin

    @property
    def is_super_admin(self) -> bool:
        return "super_admin" in self.groups


# Generic base class for AI suggested values
class BaseAISuggestedValue(DomainModel):
    # TODO: revisit this class for the future AI suggested values
    """Base class for AI-suggested values."""

    object_id = StandardObjectIdentifier(object_name="ai_suggested_value")
    object_display_name = "AI Suggested Value"
    field_name_provider = AISuggestedValueField

    updated_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Updated At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None

    # Generic field that will be overridden by subclasses with specific types
    value: Annotated[
        object | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Value",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None

    # More fields e.g: original_value, confidence_score, etc.
