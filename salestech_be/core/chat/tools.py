import textwrap
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Final, Literal
from uuid import UUID

from pydantic_ai import RunContext
from pydantic_ai.tools import ToolDefinition

from salestech_be.common import ree_llm
from salestech_be.common.ree_llm import LLMTraceMetadata
from salestech_be.common.results import Cursor
from salestech_be.core.chat.agent_deps import ChatAgentDeps
from salestech_be.core.chat.types import (
    AnalyzeMeetingTranscriptsResult,
    GetMeetingTranscriptsResult,
    GetUserTasksResult,
    MeetingSearchHit,
    MeetingSearchResult,
    UserTask,
)
from salestech_be.core.meeting.types.meeting_query_types import (
    MeetingFilter,
)
from salestech_be.db.models.task import TaskPriority, TaskStatus
from salestech_be.ree_logging import get_logger
from salestech_be.search.common.type import (
    DocumentType,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.search.search.meeting_filters import (
    create_account_filter,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    create_contact_filters,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.search.search.service_api_schema import (
    FilterInput,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    FilterOperator,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    SearchRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.search.search.service_api_schema_legacy import (
    TranscriptionQueryOpts,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)

# This should be tuned as a last resort to avoid scenarios where the agent is making
# little progress. Set lower to avoid exhausting token rate limits, but should not interfere
# with normal tool call patterns.
TOTAL_TOOL_CALL_LIMIT: Final[int] = 10
ES_SEARCH_PAGE_SIZE: Final[int] = 50
ES_SEARCH_MAX_SEARCHES: Final[int] = 6


async def limit_tool_calls(
    ctx: RunContext[ChatAgentDeps], tool_def: ToolDefinition
) -> ToolDefinition | None:
    if ctx.deps.num_tool_calls < TOTAL_TOOL_CALL_LIMIT:
        return tool_def
    logger.bind(tool_def=tool_def).debug(
        f"Tool call limit reached. `{tool_def.name}` tool being disabled."
    )
    return None


async def tool_call_limit_outlet(
    ctx: RunContext[ChatAgentDeps], tool_def: ToolDefinition
) -> ToolDefinition | None:
    """pydantic-ai prepare methods get executed after previous step reflection and before the next tool call.
    This provides a workaround for the case where the tool call limit is reached and the agent decides it needs to
    keep calling tools during reflection, then all tools are de-registered next step, it will simply
    stop early.

    This works by registering a new tool that can be called, allowing the agent
    to give a final response to the user instead of abruptly stopping.
    """
    if ctx.deps.num_tool_calls >= TOTAL_TOOL_CALL_LIMIT:
        logger.info("Tool call limit reached for current chat-agent run.")
        return tool_def

    return None


async def review_results(_ctx: RunContext[ChatAgentDeps]) -> str:
    return "Tool call limit reached. Please respond to the user, but do not mention the tool call limit."


async def get_meeting_transcripts(
    ctx: RunContext[ChatAgentDeps],
    transcript_ids: list[UUID],
) -> GetMeetingTranscriptsResult:
    """Get meeting transcripts by transcript_ids.

    This tool should be used to get the full transcript of a meeting by providing the transcript_ids
    from meeting data found in search. Use this to freely enrich meeting data without
    needing to ask the user for confirmation.

    Args:
        ctx: The run context
        transcript_ids: transcript IDs to get transcripts for

    Returns:
        An array of complete transcripts. Invalid transcript IDs will be ignored.
        Each line of the transcript is prefixed with the speaker and start timestamp (in HH:MM:SS).
    """
    await ctx.deps.increment_tool_call_count()
    logger.bind(transcript_ids=transcript_ids).info("Getting meeting transcripts")
    start_time = time.perf_counter()
    transcripts_by_id = (
        await ctx.deps.transcript_service.get_processed_transcripts_by_transcript_id(
            organization_id=ctx.deps.organization_id,
            transcript_ids=transcript_ids,
            limit=20,
        )
    )
    logger.info(f"get_meeting_transcripts took {time.perf_counter() - start_time:.2f}s")

    def ms_to_duration_str(ms: int) -> str:
        seconds = ms // 1000
        minutes, seconds = divmod(seconds, 60)
        hours, minutes = divmod(minutes, 60)
        return f"{hours:02}:{minutes:02}:{seconds:02}"

    return GetMeetingTranscriptsResult(
        transcripts=[
            {
                "transcript_id": transcript_id,
                "transcript_lines": [
                    f"{sentence.speaker} [{ms_to_duration_str(sentence.start_timestamp)}]: {sentence.text}"
                    for sentence in transcript.transcript.sentences
                ],
            }
            for transcript_id, transcript in transcripts_by_id.items()
        ]
    )


async def analyze_meeting_transcripts(
    ctx: RunContext[ChatAgentDeps],
    prompt: str,
    transcript_ids: list[UUID],
) -> AnalyzeMeetingTranscriptsResult:
    """Analyze meeting/call transcripts.

    This tool should be used to analyze the content of a meeting/call by providing a prompt
    describing the analysis task or question to answer.

    Args:
        ctx: The run context
        prompt: Prompt describing the analysis task or question to answer per transcript
        transcript_ids: transcript IDs to analyze

    Returns:
        Analyses results per transcript. Invalid transcript IDs will be ignored.
    """
    await ctx.deps.increment_tool_call_count()
    logger.bind(prompt=prompt, transcript_ids=transcript_ids).info(
        "Getting meeting transcripts for analysis"
    )
    start_time = time.perf_counter()
    transcripts_by_id = (
        await ctx.deps.transcript_service.get_processed_transcripts_by_transcript_id(
            organization_id=ctx.deps.organization_id,
            transcript_ids=transcript_ids,
            limit=len(transcript_ids),
        )
    )
    if not transcripts_by_id:
        logger.debug("No transcripts found for analysis")
        return AnalyzeMeetingTranscriptsResult(analyses=[])

    transcripts_by_id_result = GetMeetingTranscriptsResult(
        transcripts=[
            {
                "transcript_id": transcript_id,
                "transcript_lines": [
                    sentence.compact(index)
                    for index, sentence in enumerate(transcript.transcript.sentences)
                ],
            }
            for transcript_id, transcript in transcripts_by_id.items()
        ]
    )

    logger.info(
        f"Getting {len(transcripts_by_id)} transcript(s) took {time.perf_counter() - start_time:.2f}s"
    )
    system_prompt = """\
You are a professional meeting/call transcript analysis agent in the sales domain.
Your goal is to analyze the content of the given transcripts and provide a detailed answer to the analysis prompt.

Think through your analysis step by step and validate your reasoning.

Include one answer for each transcript provided.
If a provided transcript has no relevant information to answer the prompt, please answer with "No relevant information found" for that transcript.

<analysis_prompt>
{prompt}
</analysis_prompt>
    """
    try:
        result = await ree_llm.acompletion(
            model="gpt-4.1-mini",
            metadata=LLMTraceMetadata(
                trace_name="chat.tools.analyze_meeting_transcripts",
                user_id=ctx.deps.user_id,
                custom_fields={
                    "organization_id": str(ctx.deps.organization_id),
                },
            ),
            messages=[
                {
                    "role": "system",
                    "content": system_prompt.format(prompt=prompt),
                },
                {
                    "role": "user",
                    "content": transcripts_by_id_result.model_dump_json(),
                },
            ],
            response_format=AnalyzeMeetingTranscriptsResult,
        )
        logger.info(
            f"analyze_meeting_transcripts took {time.perf_counter() - start_time:.2f}s"
        )
        return result.message_content
    except Exception:
        logger.exception("Unexpected error while analyzing meeting transcripts")
        return AnalyzeMeetingTranscriptsResult(analyses=[])


async def meeting_semantic_search(
    ctx: RunContext[ChatAgentDeps],
    query: str | None = None,
    start_after_datetime: datetime | None = None,
    start_before_datetime: datetime | None = None,
    account_id: UUID | None = None,
    contact_id: UUID | None = None,
) -> MeetingSearchResult:
    """Search meetings by semantic query.

    Use this tool to find meeting data semantically related to the query string,
    which can be a topic, question, or keywords, usually extracted from the
    user's input message.

    This tool is effective for retrieving knowledge from completed meetings,
    useful for answering general questions. The returned data only includes meeting
    metadata (e.g., associated `transcript_id`)

    Args:
        ctx: The run context
        query: Semantic query to rank meetings against. Use None if you want to just filter
        start_after_datetime: Filter meetings with start time after this date
        start_before_datetime: Filter meetings with start time before this date
        account_id: Filter meetings by account ID. Only returns meetings associated with this account.
        contact_id: Filter meetings by contact ID. Only returns meetings where this contact was an invitee.

    Returns:
        Meeting search results matching the parameters. Includes all meetings across
        the organization, which may include meetings the user was not invited to or attend.
    """
    await ctx.deps.increment_tool_call_count()
    logger.bind(
        query=query,
        start_after_datetime=start_after_datetime,
        start_before_datetime=start_before_datetime,
        account_id=account_id,
        contact_id=contact_id,
    ).info("Performing semantic search on meetings")
    filters = []
    if start_after_datetime:
        filters.append(
            FilterInput(
                field="starts_at",
                operator=FilterOperator.GREATER_THAN_EQUALS,
                value=start_after_datetime,
            )
        )
    if start_before_datetime:
        filters.append(
            FilterInput(
                field="starts_at",
                operator=FilterOperator.LESS_THAN,
                value=start_before_datetime,
            )
        )

    # Add account filter if provided
    if account_id:
        filters.append(create_account_filter(account_id))

    # Add contact filter if provided
    if contact_id:
        filters.extend(create_contact_filters(contact_id))
    request = SearchRequest(
        query=query,
        # TODO: Filter by user-accessible meetings
        filters=filters,
        indexes=[DocumentType.MEETING],
        included_fields=[
            "sentences_embedding",
            "title",
            "description",
            "key_talking_points",
            "attendees",
            "invitees",
        ],
        size=20,
        timeout_secs=30,
    )
    search_start = time.perf_counter()
    hits = await ctx.deps.search_service.search_v0(
        organization_id=ctx.deps.organization_id,
        search_request=request,
        transcription_query_options=TranscriptionQueryOpts(
            use_match_query=False,
            use_knn_query=True,
            knn_top_k=3,
            knn_num_candidates=20,
            inner_hits_size=1,
        ),
    )
    logger.info(
        f"meeting_semantic_search took {time.perf_counter() - search_start:.2f}s"
    )
    return MeetingSearchResult(
        total_hits=hits.total,
        last_sort=hits.last_sort,
        meetings=[MeetingSearchHit.from_search_hit(hit.source) for hit in hits.hits],
    )


async def meeting_search(
    ctx: RunContext[ChatAgentDeps],
    start_after_datetime: datetime | None = None,
    start_before_datetime: datetime | None = None,
    user_id: UUID | None = None,
    meeting_id: UUID | None = None,
    account_id: UUID | None = None,
    contact_id: UUID | None = None,
    start_time_sort: Literal["asc", "desc"] = "desc",
) -> MeetingSearchResult:
    """Search meetings by filtering criteria, ordered by meeting start time.

    WHEN TO USE:
    - Use this tool when you need to filter meetings by specific dates, IDs, or ownership
    - Use this tool to find the most recent meetings by setting start_before_datetime to current time
    - Use this tool when you need meetings sorted by start time

    DO NOT USE:
    - Do not use this tool for semantic/meaning-based searches (use meeting_semantic_search instead)

    Examples:
    - To find user's most recent meeting: set start_before_datetime=end_of_today, user_id=UUID
    - To find meetings for a specific account: set account_id=UUID

    Args:
        ctx: The run context
        start_after_datetime: Only include meetings that started after this datetime (inclusive)
        start_before_datetime: Only include meetings that started before this datetime (inclusive)
        user_id: Only include meetings associated with this user
        meeting_id: Find a specific meeting by its UUID
        account_id: Only include meetings associated with this account UUID
        contact_id: Only include meetings where this contact UUID was an invitee
        start_time_sort: Order results by meeting start time - "asc" for oldest first, "desc" for newest first (default: "desc")

    Returns:
        MeetingSearchResult
        - total_hits: Total number of meetings matching the criteria
        - meetings: List of matching meetings with their metadata
    """
    await ctx.deps.increment_tool_call_count()
    logger.bind(
        start_after_datetime=start_after_datetime,
        start_before_datetime=start_before_datetime,
        user_id=user_id,
        meeting_id=meeting_id,
        start_time_sort=start_time_sort,
        account_id=account_id,
        contact_id=contact_id,
    ).info("Searching meetings")

    start_time = time.perf_counter()
    filters = []
    if start_after_datetime:
        filters.append(
            FilterInput(
                field="starts_at",
                operator=FilterOperator.GREATER_THAN_EQUALS,
                value=start_after_datetime,
            )
        )
    if start_before_datetime:
        filters.append(
            FilterInput(
                field="starts_at",
                operator=FilterOperator.LESS_THAN_EQUALS,
                value=start_before_datetime,
            )
        )
    if user_id:
        filters.append(
            FilterInput(
                field="invitees.user_id",
                operator=FilterOperator.EQUALS,
                value=str(user_id),
            )
        )
    if meeting_id:
        filters.append(
            FilterInput(
                field="id",
                operator=FilterOperator.EQUALS,
                value=str(meeting_id),
            )
        )

    # Add account filter if provided
    if account_id:
        filters.append(create_account_filter(account_id))

    # Add contact filter if provided
    if contact_id:
        filters.extend(create_contact_filters(contact_id))

    request = SearchRequest(
        filters=filters,
        indexes=[DocumentType.MEETING],
        sort=[{"starts_at": start_time_sort}],
        size=50,
        timeout_secs=30,
    )

    search_results = await ctx.deps.search_service.search_v0(
        organization_id=ctx.deps.organization_id,
        search_request=request,
    )
    logger.info(f"search_meetings took {time.perf_counter() - start_time:.2f}s")
    return MeetingSearchResult(
        total_hits=search_results.total,
        meetings=[
            MeetingSearchHit.from_search_hit(hit.source) for hit in search_results.hits
        ],
        last_sort=search_results.last_sort,
    )


async def get_user_tasks(
    ctx: RunContext[ChatAgentDeps],
    include_completed: bool = False,
    due_datetime_sort: Literal["asc", "desc"] = "asc",
    meeting_id: UUID | None = None,
    account_id: UUID | None = None,
    contact_id: UUID | None = None,
    due_after: datetime | None = None,
    due_before: datetime | None = None,
    page_size: int = 100,
    page_index: int = 1,
) -> GetUserTasksResult:
    """Get Tasks assigned to the current user or filtered by various criteria.
    Internally tracked action items, follow-ups, and other work are tracked as tasks.
    Tasks are associated with meetings, accounts, contacts, and other entities, but may also be standalone.
    If no filters are provided, will return tasks due from today onwards.
    Results are paginated - if the number of tasks returned equals page_size, there are likely more pages available.

    Args:
        ctx: Run context
        include_completed: Optionally include completed Tasks
        due_datetime_sort: Sort order for Tasks by when due ("asc" or "desc")
        meeting_id: Filter tasks by meeting ID
        account_id: Filter tasks by account ID
        contact_id: Filter tasks by contact ID
        due_after: Filter tasks due after this datetime (defaults to start of today if no filters provided)
        due_before: Filter tasks due before this datetime
        page_size: Number of tasks to return per page (defaults to 100)
        page_index: Page number to return (defaults to 1)

    Returns:
        A list of Task objects matching the filter criteria. If the number of tasks equals page_size,
        there are likely more results available on subsequent pages.
    """
    await ctx.deps.increment_tool_call_count()

    # Set default filters if none provided
    if not any([meeting_id, account_id, contact_id, due_after, due_before]):
        # Default to tasks due from today onwards
        due_after = zoned_utc_now().replace(hour=0, minute=0, second=0, microsecond=0)

    # Convert contact_id to contact_ids list if provided
    contact_ids = [contact_id] if contact_id else None

    # Set up status filter - exclude completed tasks unless explicitly included
    status_ne = None if include_completed else TaskStatus.COMPLETED

    logger.bind(
        user_id=ctx.deps.user_id,
        account_id=account_id,
        contact_ids=contact_ids,
        meeting_id=meeting_id,
        include_completed=include_completed,
        due_datetime_sort=due_datetime_sort,
        due_after=due_after,
        due_before=due_before,
    ).info("Getting tasks with filters")

    start_time = time.perf_counter()

    # Use list_giant_tasks from task repository
    giant_tasks = await ctx.deps.task_repository.list_giant_tasks(
        cursor=Cursor(page_size=page_size, page_index=page_index),
        organization_id=ctx.deps.organization_id,
        owner_user_id=ctx.deps.user_id,
        status_ne=status_ne,
        due_at_gt=due_after,
        due_at_lt=due_before,
        account_id=account_id,
        contact_ids=contact_ids,
        meeting_id=meeting_id,
        sorting_field="due_at",
        sorting_direction=due_datetime_sort,
    )
    logger.info(f"Found {len(giant_tasks)} giant tasks")

    logger.bind(num_tasks=len(giant_tasks)).info(
        f"get_user_tasks took {time.perf_counter() - start_time:.2f}s"
    )

    return GetUserTasksResult(
        tasks=[
            UserTask(
                title=task.task_title,
                status=TaskStatus(task.task_status),
                priority=TaskPriority(task.task_priority),
                due_datetime=task.task_due_at,
                description=task.task_note,
                meeting_id=task.meeting_id,
                account_id=task.account_id,
                contact_ids=[
                    c.contact_id
                    for c in (task.contacts_for_giant_task or [])
                    if c.contact_id
                ],
            )
            for task in giant_tasks
        ],
    )


async def ask_multi_meeting_transcripts(
    ctx: RunContext[ChatAgentDeps],
    question: str,
    start_date: datetime | None = None,
    end_date: datetime | None = None,
    account_id: UUID | None = None,
    contact_id: UUID | None = None,
) -> str:
    """Ask questions across multiple meeting transcripts and get a summarized answer.

    This tool allows you to ask questions about information contained in multiple meeting
    transcripts. It searches for relevant meetings based on the provided filters,
    retrieves their transcripts, and generates a comprehensive answer by analyzing all
    the relevant content.

    The process involves:
    1. Finding relevant meetings using semantic search based on your question
    2. Retrieving and analyzing the transcripts from those meetings
    3. Generating a consolidated answer that synthesizes information across all meetings
    4. Providing both a summary and detailed results from individual meetings

    Args:
        ctx: The run context
        question: The question to ask about the meeting transcripts
        start_date: Optional filter for meetings after this date
        end_date: Optional filter for meetings before this date
        account_id: Optional filter for meetings associated with a specific account
        contact_id: Optional filter for meetings where a specific contact was an invitee

    Returns:
        A formatted string containing a summary of findings across all meetings and
        detailed results from individual meetings that contributed to the answer
    """
    await ctx.deps.increment_tool_call_count()
    # Initialize filters with the provided parameters
    filters = MeetingFilter(
        organization_id=ctx.deps.organization_id,
        start_date=start_date,
        end_date=end_date,
        account_id=account_id,
        contact_id=contact_id,
    )

    logger.info("Asking meeting transcript agent service")
    logger.info(f"args: question={question}, filters={filters}")

    # Step 1: Find relevant meetings using semantic search
    # Use the original question as the semantic query
    meetings_result: list[MeetingSearchHit] = []
    last_sort = None
    num_searches = 0
    while True:
        result = await es_meeting_search(
            ctx=ctx,
            # query=question,  # Use the question as the semantic query
            start_after_datetime=start_date,
            start_before_datetime=end_date,
            account_id=account_id,
            contact_id=contact_id,
            search_after=last_sort,
        )
        if not result.meetings:
            break
        meetings_result.extend(result.meetings)
        if len(result.meetings) < ES_SEARCH_PAGE_SIZE:
            break
        last_sort = result.last_sort
        num_searches += 1
        # Avoid infinite loop
        if num_searches >= ES_SEARCH_MAX_SEARCHES:
            logger.warning(
                f"Max searches reached using elastic search pagination: {num_searches}"
            )
            break

    if not meetings_result:
        return "No relevant meetings found matching the criteria."

    # Step 2: Get meeting IDs from the search results
    meeting_ids = [meeting.id for meeting in meetings_result]

    if not meeting_ids:
        return "No relevant meetings found matching the criteria."

    # Step 3: Process the question across all transcripts
    # Use the multi_meeting_service to process the question across all transcripts
    # Pass the meeting IDs to the filter so it only processes those specific meetings
    filters.meeting_ids = meeting_ids

    meeting_response = await ctx.deps.multi_meeting_service.query_meetings(
        question=question, filters=filters
    )
    logger.info(f"Meetings response: {meeting_response}")

    # Step 4: Return the formatted answer
    return textwrap.dedent(
        f"""
        Summary
        <summary>
        {meeting_response.summary}
        </summary>

        Results
        <results>
        {meeting_response.results}
        </results>
        """
    )


# TODO: test that this works with last sort and pagination over all meetings within multiple windows of data
async def es_meeting_search(  # type: ignore[explicit-any]
    ctx: RunContext[ChatAgentDeps],
    query: str | None = None,
    start_after_datetime: datetime | None = None,
    start_before_datetime: datetime | None = None,
    account_id: UUID | None = None,
    contact_id: UUID | None = None,
    search_after: list[Any] | None = None,
) -> MeetingSearchResult:
    """Search meetings by semantic query.

    Use this tool to find meeting data semantically related to the query string,
    which can be a topic, question, or keywords, usually extracted from the
    user's input message.

    This tool is effective for retrieving knowledge from completed meetings,
    useful for answering general questions. The returned data only includes meeting
    metadata, and should be enriched by fetching the full transcript using the
    `get_meeting_transcripts` tool.

    Args:
        ctx: The run context
        query: Semantic query to rank meetings against. Use None if you want to just filter
        start_after_datetime: Filter meetings with start time after this date
        start_before_datetime: Filter meetings with start time before this date
        account_id: Filter meetings by account ID. Only returns meetings associated with this account.
        contact_id: Filter meetings by contact ID. Only returns meetings where this contact was an invitee.

    Returns:
        Meeting search results matching the parameters. Includes all meetings across
        the organization, which may include meetings the user was not invited to or attend.
    """
    logger.bind(
        query=query,
        start_after_datetime=start_after_datetime,
        start_before_datetime=start_before_datetime,
        account_id=account_id,
        contact_id=contact_id,
    ).info("Performing semantic search on meetings")
    filters = []
    if start_after_datetime:
        filters.append(
            FilterInput(
                field="starts_at",
                operator=FilterOperator.GREATER_THAN_EQUALS,
                value=start_after_datetime,
            )
        )
    if start_before_datetime:
        filters.append(
            FilterInput(
                field="starts_at",
                operator=FilterOperator.LESS_THAN,
                value=start_before_datetime,
            )
        )

    # Add account filter if provided
    if account_id:
        filters.append(create_account_filter(account_id))

    # Add contact filter if provided
    if contact_id:
        filters.extend(create_contact_filters(contact_id))

    request = SearchRequest(
        query=query,
        filters=filters,
        indexes=[DocumentType.MEETING],
        included_fields=[
            "sentences_embedding",
            "title",
            "description",
            "key_talking_points",
            "attendees",
            "invitees",
        ],
        size=ES_SEARCH_PAGE_SIZE,
        timeout_secs=30,
        search_after=search_after,
    )
    search_start = time.perf_counter()
    hits = await ctx.deps.search_service.search_v0(
        organization_id=ctx.deps.organization_id,
        search_request=request,
        transcription_query_options=TranscriptionQueryOpts(
            use_match_query=False,
            use_knn_query=True,
            knn_top_k=3,
            knn_num_candidates=20,
            inner_hits_size=1,
        ),
    )
    logger.info(f"es_meeting_search took {time.perf_counter() - search_start:.2f}s")
    return MeetingSearchResult(
        total_hits=hits.total,
        last_sort=hits.last_sort,
        meetings=[MeetingSearchHit.from_search_hit(hit.source) for hit in hits.hits],
    )


@dataclass
class Recipient:
    contact_id: UUID
    email: str


async def compose_email(
    ctx: RunContext[ChatAgentDeps],
    recipients: list[Recipient],
    email_body_html: str,
    subject: str | None = None,
) -> str:
    """Compose an email draft based on the specified parameters.

    Use this for drafting or composing an email on the User's behalf.

    Args:
        ctx: The run context containing dependencies
        recipients: List of recipients, each with a contact ID and email address
        email_body_html: The body of the email in HTML format. This content MUST be pure HTML.
        subject: Subject line for the email (optional)
    """
    await ctx.deps.increment_tool_call_count()
    # This tool is executed async so this implementation is a stub
    logger.bind(
        recipient_emails=recipients,
        email_body_html=email_body_html,
        subject=subject,
    ).info("compose_email tool called")

    return "Draft email sent to User's email composer for review"
