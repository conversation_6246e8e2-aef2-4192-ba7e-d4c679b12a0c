import json
import zoneinfo
from datetime import datetime
from functools import lru_cache

from anthropic import APIStatusError, AsyncAnthropic, AsyncAnthropicVertex
from google.oauth2.service_account import Credentials
from pydantic_ai import Agent, ModelHTTPError, Tool
from pydantic_ai.messages import ModelMessage, ModelRequest, SystemPromptPart
from pydantic_ai.models.anthropic import AnthropicModel, AnthropicModelSettings
from pydantic_ai.models.fallback import FallbackModel
from pydantic_ai.models.instrumented import InstrumentationSettings
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.anthropic import AnthropicProvider
from pydantic_ai.providers.openai import OpenAIProvider

from salestech_be.common.langfuse_otel import tracer_provider
from salestech_be.core.chat.agent_deps import ChatAgentDeps
from salestech_be.core.chat.agent_prompt import (
    SYS_PROMPT_TEMPLATE,
    Colleague,
    UserInfoContext,
)
from salestech_be.core.chat.entity_tools import (
    get_account_by_id,
    get_contact_by_id,
    search_accounts,
    search_contacts,
)
from salestech_be.core.chat.tools import (
    analyze_meeting_transcripts,
    ask_multi_meeting_transcripts,
    compose_email,
    get_meeting_transcripts,
    get_user_tasks,
    limit_tool_calls,
    meeting_search,
    meeting_semantic_search,
    review_results,
    tool_call_limit_outlet,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

logger = get_logger(__name__)

# Assume raw OTEL tracing: https://ai.pydantic.dev/logfire/#otel-without-logfire
Agent.instrument_all(
    instrument=InstrumentationSettings(
        event_mode="attributes", tracer_provider=tracer_provider
    )
)

base_tools = [
    Tool[ChatAgentDeps](
        review_results,
        prepare=tool_call_limit_outlet,
    ),
    Tool[ChatAgentDeps](
        meeting_search,
        prepare=limit_tool_calls,
        require_parameter_descriptions=True,
    ),
    Tool[ChatAgentDeps](
        meeting_semantic_search,
        prepare=limit_tool_calls,
        require_parameter_descriptions=True,
    ),
    Tool[ChatAgentDeps](
        get_user_tasks,
        prepare=limit_tool_calls,
        require_parameter_descriptions=True,
    ),
    # Entity tools for contacts and accounts
    Tool[ChatAgentDeps](
        search_contacts,
        prepare=limit_tool_calls,
        require_parameter_descriptions=True,
    ),
    Tool[ChatAgentDeps](
        get_contact_by_id,
        prepare=limit_tool_calls,
        require_parameter_descriptions=True,
    ),
    Tool[ChatAgentDeps](
        search_accounts,
        prepare=limit_tool_calls,
        require_parameter_descriptions=True,
    ),
    Tool[ChatAgentDeps](
        get_account_by_id,
        prepare=limit_tool_calls,
        require_parameter_descriptions=True,
    ),
]
# Additional variant-specific tools
primary_tools = [
    Tool[ChatAgentDeps](
        get_meeting_transcripts,
        prepare=limit_tool_calls,
        require_parameter_descriptions=True,
    ),
]
cake_tools = [
    Tool[ChatAgentDeps](
        compose_email,
        prepare=limit_tool_calls,
        require_parameter_descriptions=True,
    )
]
tao_tools = [
    Tool[ChatAgentDeps](
        analyze_meeting_transcripts,
        prepare=limit_tool_calls,
        require_parameter_descriptions=True,
    )
]
mamm_tools = [
    Tool[ChatAgentDeps](
        ask_multi_meeting_transcripts,
        prepare=limit_tool_calls,
        require_parameter_descriptions=False,
    ),
]

base_model_settings = AnthropicModelSettings(
    temperature=0.0, max_tokens=8192, parallel_tool_calls=True, anthropic_metadata={}
)
anthropic_client = AsyncAnthropic(api_key=settings.anthropic_api_key.get_secret_value())
anthropic_model = AnthropicModel(
    "claude-3-7-sonnet-********",
    provider=AnthropicProvider(
        anthropic_client=anthropic_client,
    ),
)
claude_sonnet_4_model = AnthropicModel(
    "claude-sonnet-4-********",
    provider=AnthropicProvider(
        anthropic_client=anthropic_client,
    ),
)

claude_3_7_agent = Agent(
    model=anthropic_model,
    model_settings=base_model_settings,
    tools=[*base_tools, *primary_tools],
    deps_type=ChatAgentDeps,
    name="chat-agent",
)

claude_sonnet_4_agent = Agent(
    model=claude_sonnet_4_model,
    model_settings=base_model_settings,
    tools=[*base_tools, *primary_tools],
    deps_type=ChatAgentDeps,
    name="chat-agent",
)

# Variant implementing client-action email composer (caec/cake)
claude_3_7_cake_agent = Agent(
    model=anthropic_model,
    model_settings=base_model_settings,
    tools=[*base_tools, *primary_tools, *cake_tools],
    deps_type=ChatAgentDeps,
    name="chat-agent",
)
claude_sonnet_4_cake_agent = Agent(
    model=claude_sonnet_4_model,
    model_settings=base_model_settings,
    tools=[*base_tools, *primary_tools, *cake_tools],
    deps_type=ChatAgentDeps,
    name="chat-agent",
)

# Offloads transcript analysis to sub-LLM - transcript analysis offload (tao)
claude_3_7_tao_agent = Agent(
    model=anthropic_model,
    model_settings=base_model_settings,
    tools=[*base_tools, *tao_tools],
    deps_type=ChatAgentDeps,
    name="chat-agent",
)

# This variant experiments w/ using a multi-agent multi-meeting tool (mamm)
claude_3_7_mamm_agent = Agent(
    model=anthropic_model,
    model_settings=base_model_settings,
    tools=[*base_tools, *mamm_tools],
    deps_type=ChatAgentDeps,
    name="chat-agent",
)


def render_system_prompt(
    ctx: ChatAgentDeps,
) -> ModelMessage:
    """
    Explicitly call to render the system prompt. Preferred over the @agent.system_prompt
    decorator because we have to explicitly build the system prompt when there is
    chat history, resulting in duplicated code
    """

    timezone = "America/Los_Angeles"
    if (profile := ctx.user.organization_association_profile) and profile.timezone:
        timezone = str(profile.timezone)

    tzinfo = zoneinfo.ZoneInfo(timezone)
    system_prompt = SYS_PROMPT_TEMPLATE.format(
        current_date=datetime.now(tz=tzinfo).date().strftime("%B %d, %Y"),
        user_info=UserInfoContext(
            user_id=str(ctx.user.id),
            name=ctx.user.display_name,
            timezone=timezone,
            organization_name=ctx.organization.display_name,
            view_contexts=ctx.view_contexts,
            colleagues=[
                Colleague(user_id=str(u.id), name=u.display_name or u.email)
                for u in ctx.org_users
                if u.id != ctx.user.id
            ],
        ).model_dump_json(),
    )
    return ModelRequest(
        parts=[SystemPromptPart(content=system_prompt)],
    )


def select_variant(variant_name: str | None = None) -> Agent[ChatAgentDeps, str]:  # noqa: PLR0911
    match variant_name:
        case "claude-3-7-tao":
            return claude_3_7_tao_agent
        case "claude-3-7-mamm":
            return claude_3_7_mamm_agent
        case "claude-3-7-cake":
            return claude_3_7_cake_agent
        case "claude-3-7" | "claude-3-7-sonnet":
            return claude_3_7_agent
        case "claude-sonnet-4":
            return claude_sonnet_4_agent
        case "claude-sonnet-4-cake":
            return claude_sonnet_4_cake_agent
        case _:
            logger.warning(
                f"Unhandled variant: {variant_name}. Falling back to mainline agent."
            )
            return claude_3_7_agent


@lru_cache
def chat_fallback_models(agent_variant: str | None = None) -> FallbackModel:
    """
    This is a workaround for Claude on Vertex AI as pydantic-ai does not have native support yet.
    It is instantiated here instead of being set up in the agent instance to avoid initializing during import time.
    See https://github.com/pydantic/pydantic-ai/issues/960
    """
    anthropic_vertex = AsyncAnthropicVertex(
        project_id="reevo-gemini",
        # Available regions: https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/use-claude#regions
        region="us-east5",
        credentials=Credentials.from_service_account_info(
            # type: ignore[no-untyped-call]
            json.loads(settings.vertex_ai_cred_json.get_secret_value()),
            scopes=["https://www.googleapis.com/auth/cloud-platform"],
        ),
    )
    if agent_variant is None or agent_variant.startswith("claude-3-7"):
        vertex_model_name = "claude-3-7-sonnet@********"
        primary_model = anthropic_model
    else:
        vertex_model_name = "claude-sonnet-4@********"
        primary_model = claude_sonnet_4_model

    anthropic_vertex_model = AnthropicModel(
        vertex_model_name,
        provider=AnthropicProvider(
            anthropic_client=anthropic_vertex,  # type: ignore
        ),
    )
    # Primarily used as a fallback when we violate context limits with Claude
    openai_model = OpenAIModel(
        model_name="gpt-4.1-2025-04-14",
        provider=OpenAIProvider(
            api_key=settings.openai_api_key.get_secret_value(),
        ),
    )
    return FallbackModel(
        primary_model,
        anthropic_vertex_model,
        openai_model,
        # Anthropic sometimes throws APIStatusError with no status_code, which was not triggering fallbacks
        fallback_on=(ModelHTTPError, APIStatusError),
    )
