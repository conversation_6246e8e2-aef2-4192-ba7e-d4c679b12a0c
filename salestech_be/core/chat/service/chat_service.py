import json
import time
from collections.abc import AsyncGenerator
from datetime import datetime
from uuid import UUID, uuid4

import urllib3.util
from fastapi import HTTPException
from pydantic_ai import Agent
from pydantic_ai.agent import AgentRunResult
from pydantic_ai.messages import (
    FinalResultEvent,
    FunctionToolCallEvent,
    FunctionToolResultEvent,
    ModelMessage,
    PartDeltaEvent,
    PartStartEvent,
    TextPartDelta,
)
from pydantic_ai.models.anthropic import AnthropicModelSettings

from salestech_be.common import ree_llm
from salestech_be.common.ai_stream import (
    BaseDataStreamEvent,
    DataEvent,
    FinishMessageEvent,
    FinishStepEvent,
    StartStepEvent,
    TextDeltaEvent,
    ToolCallEvent,
    ToolResultEvent,
)
from salestech_be.common.langfuse_otel import langfuse_otel_trace
from salestech_be.common.ree_llm import LLMTraceMetadata
from salestech_be.common.singleton import Singleton
from salestech_be.core.account.service.account_service import (
    AccountService,
    get_account_service,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    LangfusePromptService,
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptRequest
from salestech_be.core.chat.agent_deps import ChatAgentDeps
from salestech_be.core.chat.chat_agent import (
    chat_fallback_models,
    render_system_prompt,
    select_variant,
)
from salestech_be.core.chat.types import Chat
from salestech_be.core.contact.service.contact_service import (
    ContactService,
    get_contact_service,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.ff.types import FeatureFlagRequest
from salestech_be.core.meeting.service.meeting_transcript_agent_service import (
    MeetingTranscriptAgentService,
    meeting_transcript_agent_service_from_engine,
)
from salestech_be.core.organization.service.organization_service import (
    OrganizationService,
    get_organization_service_general,
)
from salestech_be.core.transcript.transcript_service import (
    TranscriptService,
    transcript_service_from_engine,
)
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.db.dao.chat_repository import ChatRepository, get_chat_repository
from salestech_be.db.dao.task_repository import TaskRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.chat import (
    ChatMessage,
    ChatMessageSender,
    MessageContentBlock,
    TextContentBlock,
    ToolCallContentBlock,
)
from salestech_be.ree_logging import get_logger
from salestech_be.search.es.search.client_utils import (
    get_es_search_client,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.search.search.search_service import (  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    SearchService,
    get_search_service,
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.chat.schema import (
    ChatRequest,  # tach-ignore
    ViewContext,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)

CHAT_AGENT_SELECTION_FF_KEY = "chat-agent-model"
TITLE_GEN_SYSTEM_PROMPT_NAME = "chat.title"


class ChatService:
    def __init__(
        self,
        search_service: SearchService,
        transcript_service: TranscriptService,
        user_service: UserService,
        langfuse_prompt_service: LangfusePromptService,
        ff_service: FeatureFlagService,
        organization_service: OrganizationService,
        task_repository: TaskRepository,
        multi_meeting_service: MeetingTranscriptAgentService,
        contact_service: ContactService,
        account_service: AccountService,
        chat_repo: ChatRepository,
    ) -> None:
        self.search_service = search_service
        self.transcript_service = transcript_service
        self.user_service = user_service
        self.langfuse_prompt_service = langfuse_prompt_service
        self.ff_service = ff_service
        self.organization_service = organization_service
        self.task_repository = task_repository
        self.multi_meeting_service = multi_meeting_service
        self.contact_service = contact_service
        self.account_service = account_service
        self.chat_repo = chat_repo

    async def process_user_message(  # noqa: C901 PLR0912 PLR0915
        self,
        organization_id: UUID,
        user_id: UUID,
        chat_request: ChatRequest,
    ) -> AsyncGenerator[BaseDataStreamEvent, None]:
        start_time = time.perf_counter()
        client_messages = chat_request.messages
        chat_id = chat_request.chat_id

        if not client_messages or client_messages[-1].role == "assistant":
            return

        last_message = client_messages[-1]
        user = await self.user_service.get_user_v2(
            organization_id=organization_id, user_id=user_id
        )
        org = await self.organization_service.get_organization_by_id(
            organization_id=organization_id
        )
        if org is None:
            raise ValueError(f"Organization with ID {organization_id} not found")

        org_users = await self.user_service.list_all_users_in_organization(
            organization_id=org.id,
            active_users_only=True,
        )
        view_contexts = (
            chat_request.view_contexts
            if chat_request.view_contexts
            else self._contexts_from_url(chat_request.meeting_url)
        )
        deps = ChatAgentDeps(
            search_service=self.search_service,
            transcript_service=self.transcript_service,
            task_repository=self.task_repository,
            user_id=user_id,
            organization_id=organization_id,
            user=user,
            organization=org,
            org_users=org_users,
            multi_meeting_service=self.multi_meeting_service,
            contact_service=self.contact_service,
            account_service=self.account_service,
            view_contexts=view_contexts,
        )

        is_first_token = True
        message_history = [render_system_prompt(ctx=deps)]
        if len(client_messages) > 1:
            message_history.extend([m.to_pai_message() for m in client_messages[:-1]])

        process_start_ts = zoned_utc_now()
        trace_metadata = LLMTraceMetadata(
            trace_name="chat-agent",
            user_id=user_id,
            session_id=f"chat:{org.display_name}:{chat_id!s}" if chat_id else None,
            custom_fields={
                "organization_id": str(organization_id),
                "organization_name": org.display_name,
                "user_display_name": user.display_name or user.email or "",
            },
        )

        with langfuse_otel_trace(trace_metadata=trace_metadata) as parent_span:
            parent_span.set_attribute("input.value", last_message.content)
            chat_agent, variant_name = await self._select_chat_agent(
                user_id=user_id, organization_id=organization_id
            )
            response_message_id = uuid4()

            async with chat_agent.iter(
                last_message.content,
                model=chat_fallback_models(variant_name),
                deps=deps,
                message_history=message_history,
                model_settings=AnthropicModelSettings(
                    anthropic_metadata={
                        "user_id": str(user_id),
                    },
                ),
            ) as run:
                async for node in run:
                    if Agent.is_user_prompt_node(node):
                        logger.debug(f"User prompt: {node.user_prompt}")
                        # Vercel's AI SDK uses this to identify the message
                        yield StartStepEvent(message_id=str(response_message_id))
                    elif Agent.is_model_request_node(node):
                        async with node.stream(run.ctx) as request_stream:
                            async for event in request_stream:
                                logger.debug(f"Received model_request event: {event}")
                                if isinstance(event, PartStartEvent):
                                    if event.part.part_kind == "text":
                                        if is_first_token:
                                            logger.info(
                                                f"Time to first token: {time.perf_counter() - start_time:.2f}s"
                                            )
                                            is_first_token = False
                                        yield TextDeltaEvent(text=event.part.content)
                                elif isinstance(event, PartDeltaEvent):
                                    if isinstance(event.delta, TextPartDelta):
                                        yield TextDeltaEvent(
                                            text=event.delta.content_delta
                                        )
                                elif isinstance(event, FinalResultEvent):
                                    ...
                    elif Agent.is_call_tools_node(node):
                        async with node.stream(run.ctx) as handle_stream:
                            # This is a hack to separate chain-of-thought text that
                            # precede tool calls. They normally do not have surrounding
                            # whitespace.
                            yield TextDeltaEvent(text="\n\n")
                            async for event in handle_stream:  # type: ignore[assignment]
                                logger.debug(f"Received call_tools event: {event}")
                                if isinstance(event, FunctionToolCallEvent):
                                    yield ToolCallEvent(
                                        tool_call_id=event.part.tool_call_id
                                        or event.part.tool_name,
                                        tool_name=event.part.tool_name,
                                        args=event.part.args_as_dict(),
                                    )
                                if isinstance(event, FunctionToolResultEvent):
                                    # We probably don't want to return real tool results for most tools
                                    yield ToolResultEvent(
                                        tool_call_id=event.tool_call_id
                                        or event.result.tool_call_id
                                        or event.result.tool_name,
                                        result=None,
                                    )
                    elif Agent.is_end_node(node):
                        usage = None
                        if run.result:
                            usage = run.result.usage()
                            parent_span.set_attribute("output.value", run.result.output)
                            if not chat_id:
                                chat = await self._create_chat(
                                    organization_id=organization_id,
                                    user_id=user_id,
                                    initial_message=last_message.content,
                                    response=run.result.output,
                                    timestamp=process_start_ts,
                                )
                                chat_id = chat.id
                                yield DataEvent(
                                    data=[{"event": "chat.created", "data": chat}]
                                )
                                parent_span.set_attribute(
                                    "langfuse.session.id",
                                    f"chat:{org.display_name}:{chat_id!s}",
                                )

                            await self._persist_user_message(
                                chat_id=chat_id,
                                message_content=last_message.content,
                                timestamp=process_start_ts,
                            )
                            await self._persist_response_message(
                                chat_id=chat_id,
                                response_message_id=response_message_id,
                                run_result=run.result,
                            )

                        usage_response = {
                            "promptTokens": usage.request_tokens
                            if usage and usage.request_tokens
                            else -1,
                            "completionTokens": usage.response_tokens
                            if usage and usage.response_tokens
                            else -1,
                        }

                        yield FinishStepEvent(
                            finish_reason="stop",
                            is_continued=False,
                            usage=usage_response,
                        )
                        yield FinishMessageEvent(
                            finish_reason="stop", usage=usage_response
                        )
                    else:
                        raise RuntimeError("Unexpected agent node")

    async def _create_chat(
        self,
        organization_id: UUID,
        user_id: UUID,
        initial_message: str,
        response: str,
        timestamp: datetime,
    ) -> Chat:
        db_chat = await self.chat_repo.create_chat(
            organization_id=organization_id,
            owner_user_id=user_id,
            title=await self._generate_chat_title(
                organization_id=organization_id,
                user_id=user_id,
                initial_message=initial_message,
                assistant_response=response,
            ),
            timestamp=timestamp,
        )
        return Chat.from_db(db_chat)

    async def _persist_user_message(
        self,
        chat_id: UUID | None,
        message_content: str,
        timestamp: datetime,
    ) -> ChatMessage:
        if chat_id is None:
            raise AssertionError("Unable to persist chat messages: chat_id is None")

        logger.bind(chat_id=chat_id, message_content=message_content).debug(
            "Persisting user message"
        )
        return await self.chat_repo.create_message(
            message_id=uuid4(),
            chat_id=chat_id,
            content=[TextContentBlock(text=message_content)],
            sender=ChatMessageSender.USER,
            timestamp=timestamp,
        )

    async def _persist_response_message(
        self,
        chat_id: UUID | None,
        response_message_id: UUID,
        run_result: AgentRunResult | None = None,
    ) -> ChatMessage:
        """Persist the user message and assistant response"""
        if chat_id is None:
            raise AssertionError("Unable to persist chat messages: chat_id is None")

        if run_result is None:
            raise AssertionError("Unable to persist chat messages: run_result is None")

        content_blocks = self._pai_to_response_content_blocks(run_result.new_messages())
        logger.bind(
            chat_id=chat_id,
            response_message_id=response_message_id,
            content=content_blocks,
        ).debug("Persisting response message")

        return await self.chat_repo.create_message(
            message_id=response_message_id,
            chat_id=chat_id,
            content=content_blocks,
            sender=ChatMessageSender.ASSISTANT,
        )

    @staticmethod
    def _pai_to_response_content_blocks(
        model_messages: list[ModelMessage],
    ) -> list[MessageContentBlock]:
        response_content_blocks: list[MessageContentBlock] = []
        for message in model_messages:
            if message.kind == "response":
                for part in message.parts:
                    if part.part_kind == "text":
                        response_content_blocks.append(
                            TextContentBlock(text=part.content)
                        )
                    elif part.part_kind == "tool-call":
                        # Keep this consistent how we handle COT text in streaming
                        response_content_blocks.append(TextContentBlock(text="\n\n"))
                        response_content_blocks.append(
                            ToolCallContentBlock(
                                tool_name=part.tool_name,
                                tool_id=part.tool_call_id,
                                args=part.args_as_dict(),
                                result=None,
                            )
                        )
                    else:
                        logger.error(
                            f"Dropping unknown message part kind: {part.part_kind}"
                        )
        return response_content_blocks

    async def _generate_chat_title(
        self,
        organization_id: UUID,
        user_id: UUID,
        initial_message: str,
        assistant_response: str,
    ) -> str:
        title_gen_prompt = await self.langfuse_prompt_service.get_prompt(
            PromptRequest(
                prompt_name=TITLE_GEN_SYSTEM_PROMPT_NAME,
                variables={
                    "chat_messages": json.dumps(
                        [
                            {"role": "user", "content": initial_message},
                            {"role": "assistant", "content": assistant_response},
                        ]
                    ),
                },
                label="latest",
            )
        )
        response = await ree_llm.acompletion(
            model="vertex_ai/gemini-2.0-flash-001",
            messages=title_gen_prompt.messages,
            max_completion_tokens=15,
            temperature=0.2,
            metadata=ree_llm.LLMTraceMetadata(
                trace_name="chat.title",
                user_id=user_id,
                custom_fields={
                    "organization_id": str(organization_id),
                },
                prompt=title_gen_prompt.prompt_client,
            ),
        )
        return response.message_content.strip()

    async def _select_chat_agent(
        self, user_id: UUID, organization_id: UUID
    ) -> tuple[Agent[ChatAgentDeps, str], str]:
        flag = await self.ff_service.get_feature_flag(
            request=FeatureFlagRequest(
                flag_key=CHAT_AGENT_SELECTION_FF_KEY,
                user_id=user_id,
                organization_id=organization_id,
            )
        )
        return select_variant(flag), flag

    @staticmethod
    def _contexts_from_url(url: str | None) -> list[ViewContext]:
        if url is None:
            return [ViewContext(type="meeting")]
        path = urllib3.util.parse_url(url).path
        if not path:
            return [ViewContext(type="meeting")]
        path_parts = path.split("/")
        return [
            ViewContext(
                type=path_parts[1],
                id=UUID(path_parts[2])
                if len(path_parts) > 2 and path_parts[2]  # noqa: PLR2004
                else None,
            )
        ]

    async def update_chat(
        self,
        chat_id: UUID,
        user_id: UUID,
        organization_id: UUID,
        title: str | None = None,
    ) -> Chat:
        db_chat = await self.chat_repo.update_chat(
            chat_id=chat_id,
            user_id=user_id,
            organization_id=organization_id,
            title=title,
        )
        if db_chat is None:
            raise HTTPException(status_code=404, detail="Chat not found")
        return Chat.from_db(db_chat)

    async def delete_chat(
        self,
        chat_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> Chat:
        db_chat = await self.chat_repo.delete_chat(
            chat_id=chat_id, user_id=user_id, organization_id=organization_id
        )
        if db_chat is None:
            raise HTTPException(status_code=404, detail="Chat not found")
        return Chat.from_db(db_chat)


class SingletonChatService(ChatService, Singleton):
    pass


def get_chat_service(db_engine: DatabaseEngine) -> ChatService:
    return SingletonChatService(
        search_service=get_search_service(search_client=get_es_search_client()),
        transcript_service=transcript_service_from_engine(engine=db_engine),
        user_service=get_user_service_general(db_engine=db_engine),
        langfuse_prompt_service=get_langfuse_prompt_service(),
        ff_service=get_feature_flag_service(),
        organization_service=get_organization_service_general(db_engine=db_engine),
        task_repository=TaskRepository(engine=db_engine),
        multi_meeting_service=meeting_transcript_agent_service_from_engine(
            engine=db_engine
        ),
        contact_service=get_contact_service(db_engine=db_engine),
        account_service=get_account_service(db_engine=db_engine),
        chat_repo=get_chat_repository(db_engine=db_engine),
    )
