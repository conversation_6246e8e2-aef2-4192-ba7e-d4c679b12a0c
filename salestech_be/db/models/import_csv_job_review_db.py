from __future__ import annotations

from enum import StrEnum
from uuid import UUID

from salestech_be.db.models.core.base import Column, TableModel
from salestech_be.db.models.core.constants import TableName
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class ImportCsvJobReviewDb(TableModel):
    table_name = TableName.import_csv_job_review
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    organization_id: Column[UUID]
    display_name: Column[str | None]
    submitter_user_id: Column[UUID]

    # import_csv_job_id FK should not be null, but not enforced by DB.
    #   Allows operational flexibility. None will be treated as archived.
    import_csv_job_id: Column[UUID | None]
    reviewer_user_id: Column[UUID | None]
    reviewer_notes: Column[str | None]

    status: Column[str]  # INITIAL, PENDING_REVIEW, APPROVED
    status_changed_at: Column[ZoneRequiredDateTime | None]

    created_at: Column[ZoneRequiredDateTime]
    updated_at: Column[ZoneRequiredDateTime]
    archived_at: Column[ZoneRequiredDateTime | None]


class ImportCsvJobReviewExtraInfoDb(ImportCsvJobReviewDb):
    # extra info, not part of import_csv_job_review table
    info_organization_display_name: Column[
        str | None
    ]  # Informational, best effort populated


class ImportCsvJobReviewStatusDb(StrEnum):
    PENDING_REVIEW = "PENDING_REVIEW"
    APPROVED = "APPROVED"
    REJECTED = "REJECTED"
