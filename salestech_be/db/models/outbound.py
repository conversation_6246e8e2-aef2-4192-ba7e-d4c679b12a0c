from dataclasses import field
from enum import StrEnum
from uuid import UUID, uuid4

from pydantic import Field

from salestech_be.core.common.accounts_receivable import (
    AccountsReceivable,
)
from salestech_be.db.models.core.base import Column, JsonColumn, TableModel
from salestech_be.db.models.core.constants import TableName
from salestech_be.db.models.core.types import EntityDnsRecord
from salestech_be.integrations.infraforge.type import (
    InfraforgeDomainStatus,
    OneTimeDomainPurchaseInvoice,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now


class OutboundDomainStatus(StrEnum):
    ACTIVE = "ACTIVE"
    PENDING = "PENDING"
    INACTIVE = "INACTIVE"
    FAILED = "FAILED"

    @classmethod
    def from_infraforge_model(
        cls, status: InfraforgeDomainStatus
    ) -> "OutboundDomainStatus":
        status_map = {
            "draft": cls.PENDING,
            "active": cls.ACTIVE,
            "pending": cls.PENDING,
            "inactive": cls.INACTIVE,
            "failed": cls.FAILED,
        }
        return status_map.get(status, cls.PENDING)


class OutboundVendor(StrEnum):
    INFRAFORGE = "INFRAFORGE"


class OutboundWorkspace(TableModel):
    """Workspace model for infraforge."""

    table_name = TableName.outbound_workspace
    ordered_primary_keys = ("id",)

    id: Column[UUID] = Field(default_factory=uuid4)
    name: Column[str]
    vendor: Column[OutboundVendor]
    external_id: Column[str]
    organization_id: Column[UUID]
    created_at: Column[ZoneRequiredDateTime] = Field(default_factory=zoned_utc_now)
    updated_at: Column[ZoneRequiredDateTime] = Field(default_factory=zoned_utc_now)
    is_mock_record: Column[bool] = False


class OutboundDomain(TableModel):
    """Outbound Domain table model."""

    table_name = TableName.outbound_domain
    ordered_primary_keys = ("id",)

    id: Column[UUID] = Field(default_factory=uuid4)
    organization_id: Column[UUID]
    domain: Column[str]
    external_id: Column[str]
    status: Column[OutboundDomainStatus]
    workspace_id: Column[UUID]
    forward_to_domain: Column[str | None] = None
    masking: Column[bool] = False
    vendor: Column[OutboundVendor] = OutboundVendor.INFRAFORGE
    created_by_user_id: Column[UUID]
    created_at: Column[ZoneRequiredDateTime] = Field(default_factory=zoned_utc_now)
    updated_at: Column[ZoneRequiredDateTime] = Field(default_factory=zoned_utc_now)
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    archived_at: Column[ZoneRequiredDateTime | None] = None
    invoice: JsonColumn[OneTimeDomainPurchaseInvoice | None] = None
    transaction_type: Column[AccountsReceivable | None] = None
    is_mock_record: Column[bool] = False
    custom_cname_exists: Column[bool] = False


class DomainHealth(TableModel):
    """Domain Health table model."""

    table_name = TableName.domain_health
    ordered_primary_keys = ("id",)

    id: Column[UUID] = Field(default_factory=uuid4)
    domain_id: Column[UUID]
    last_check_at: Column[ZoneRequiredDateTime] = Field(default_factory=zoned_utc_now)
    created_at: Column[ZoneRequiredDateTime] = Field(default_factory=zoned_utc_now)
    updated_at: Column[ZoneRequiredDateTime] = Field(default_factory=zoned_utc_now)
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    smtp_connectable: Column[bool] = False
    is_mock_record: Column[bool] = False

    # JSONB columns for storing lists of strings
    spf_records: JsonColumn[list[EntityDnsRecord]] = field(default_factory=list)
    dmarc_record: JsonColumn[list[EntityDnsRecord]] = field(default_factory=list)
    dkim_record: JsonColumn[list[EntityDnsRecord]] = field(default_factory=list)
    mx_records: JsonColumn[list[EntityDnsRecord]] = field(default_factory=list)
