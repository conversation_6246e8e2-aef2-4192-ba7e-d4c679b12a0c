from __future__ import annotations

from collections.abc import Mapping
from typing import Any, Self
from uuid import UUID, uuid4

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    PrivateAttr,
    model_validator,
)

from salestech_be.common.type.contact import ContactChannelLabel
from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.common.type.metadata.common import ObjectAccessStatus
from salestech_be.common.type.metadata.field.field_value import (
    FieldValueOrAny,
)
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.db.models.address import Address, AddressCreateRequest
from salestech_be.db.models.contact_account_association import ContactAccountAssociation
from salestech_be.db.models.contact_email import (
    ContactEmail,
    ContactEmailAccountAssociation,
    ContactEmailAccountAssociationUpdate,
    ContactEmailUpdate,
)
from salestech_be.db.models.contact_phone_number import (
    ContactPhoneNumber,
    ContactPhoneNumberAccountAssociation,
)
from salestech_be.db.models.core.base import (
    Column,
    DBModel,
    JsonColumn,
    SysTableModel,
    TableBoundedModel,
)
from salestech_be.db.models.core.constants import TableName
from salestech_be.db.models.core.types import CreatedSource, EntityParticipant
from salestech_be.util.pydantic_types.str import PhoneNumber, PhoneNumberWithExtension
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none


class Contact(SysTableModel):
    """Contact table model."""

    table_name = TableName.contact
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    display_name: Column[str]
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    owner_user_id: Column[UUID]
    organization_id: Column[UUID]
    first_name: Column[str | None] = None
    last_name: Column[str | None] = None
    middle_name: Column[str | None] = None
    primary_email: Column[EmailStrLower | None] = None
    primary_phone_number: Column[PhoneNumber | None] = None
    linkedin_url: Column[str | None] = None
    zoominfo_url: Column[str | None] = None
    facebook_url: Column[str | None] = None
    x_url: Column[str | None] = None
    address_id: Column[UUID | None] = None
    title: Column[str | None] = None
    department: Column[str | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    archived_at: Column[ZoneRequiredDateTime | None] = None
    archived_by_user_id: Column[UUID | None] = None
    integrity_job_finished_at: Column[ZoneRequiredDateTime | None] = None
    integrity_job_finished_by_user_id: Column[UUID | None] = None
    integrity_job_started_at: Column[ZoneRequiredDateTime | None] = None
    integrity_job_started_by_user_id: Column[UUID | None] = None
    integrity_job_started_by_job_ids: Column[list[UUID]] = Field(default_factory=list)
    primary_account_id: Column[UUID | None] = None
    person_id: Column[UUID | None] = None
    stage_id: Column[UUID]
    participants: JsonColumn[list[EntityParticipant] | None] = None
    created_source: Column[CreatedSource | None] = None
    phone_enriched_at: Column[ZoneRequiredDateTime | None] = None
    email_enriched_at: Column[ZoneRequiredDateTime | None] = None

    @property
    def access_status(self) -> ObjectAccessStatus:
        if not any(
            [
                self.archived_at,
                self.integrity_job_started_at,
                self.integrity_job_finished_at,
            ]
        ):
            return ObjectAccessStatus.ACTIVE
        elif self.integrity_job_started_at:
            return ObjectAccessStatus.INTEGRITY_JOB_RUNNING
        elif self.archived_at:
            return ObjectAccessStatus.ARCHIVED

        raise ValueError(f"account {self.id} has invalid access status")


class CreateDbContactRequest(BaseModel):
    display_name: str
    created_by_user_id: UUID
    owner_user_id: UUID
    first_name: str | None = None
    last_name: str | None = None
    middle_name: str | None = None
    primary_phone_number: PhoneNumberWithExtension | None = None
    linkedin_url: str | None = None
    zoominfo_url: str | None = None
    facebook_url: str | None = None
    x_url: str | None = None
    address: AddressCreateRequest | None = None
    # TODO: Make this more type specific later using Object Identifiers
    csv_type: str | None = None
    custom_field_data: Mapping[UUID, FieldValueOrAny] | None = None
    address_id: UUID | None = None
    title: str | None = None
    department: str | None = None
    person_id: UUID | None = None
    stage_id: UUID
    created_source: CreatedSource | None = None
    created_at: ZoneRequiredDateTime | None = None

    def to_db_inserts(
        self,
        organization_id: UUID,
        address_id_override: UUID | None = None,
    ) -> Contact:
        now = zoned_utc_now()
        if not self.display_name:
            if self.first_name and self.last_name:
                self.display_name = f"{self.first_name} {self.last_name}"
            elif self.first_name:
                self.display_name = self.first_name
            elif self.last_name:
                self.display_name = self.last_name
            elif self.middle_name:
                self.display_name = self.middle_name
            elif self.primary_phone_number:
                self.display_name = self.primary_phone_number
            else:
                self.display_name = ""
        return Contact(
            id=uuid4(),
            organization_id=organization_id,
            display_name=self.display_name,
            created_by_user_id=self.created_by_user_id,
            updated_by_user_id=self.created_by_user_id,
            created_at=self.created_at or now,
            updated_at=now,
            owner_user_id=self.owner_user_id,
            first_name=self.first_name,
            last_name=self.last_name,
            middle_name=self.middle_name,
            primary_phone_number=self.primary_phone_number,
            linkedin_url=self.linkedin_url,
            zoominfo_url=self.zoominfo_url,
            facebook_url=self.facebook_url,
            x_url=self.x_url,
            address_id=address_id_override or self.address_id,
            title=self.title,
            department=self.department,
            person_id=self.person_id,
            stage_id=not_none(self.stage_id),
            created_source=self.created_source,
        )

    def to_address_db_insert(
        self,
    ) -> Address | None:
        if self.address:
            now = zoned_utc_now()
            return self.address.to_db_insert(created_at=now)
        return None


class UpdateContactEmailRequest(BaseModel):
    id: UUID

    is_contact_primary: bool
    labels: list[ContactChannelLabel] | None = None
    alternative_email_id: UUID | None = None

    def to_db_inserts(
        self,
        user_id: UUID,
    ) -> ContactEmailUpdate:
        return ContactEmailUpdate(
            updated_by_user_id=user_id,
            updated_at=zoned_utc_now(),
            labels=UNSET if self.labels is None else self.labels,
            is_contact_primary=UNSET
            if self.is_contact_primary is None
            else self.is_contact_primary,
        )


class UpdateContactEmailAccountAssociationRequest(BaseModel):
    id: UUID

    is_contact_account_primary: bool
    alternative_email_account_association_id: UUID | None = None

    def to_db_inserts(
        self,
        user_id: UUID,
    ) -> ContactEmailAccountAssociationUpdate:
        return ContactEmailAccountAssociationUpdate(
            updated_by_user_id=user_id,
            updated_at=zoned_utc_now(),
            is_contact_account_primary=UNSET
            if self.is_contact_account_primary is None
            else self.is_contact_account_primary,
        )


class CreateDbContactEmailAccountAssociationRequest(BaseModel):
    id: UUID = uuid4()
    account_id: UUID
    is_contact_account_primary: bool

    def __eq__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(other, type(self)):
            return NotImplemented
        return (
            self.account_id == other.account_id
            and self.is_contact_account_primary == other.is_contact_account_primary
        )


class CreateDbContactEmailRequest(BaseModel):
    email: EmailStrLower
    is_contact_primary: bool
    labels: list[ContactChannelLabel] | None = None
    email_account_associations: list[CreateDbContactEmailAccountAssociationRequest] = (
        Field(default_factory=list)
    )

    def __eq__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(other, type(self)):
            return NotImplemented
        return (
            self.email == other.email
            and self.is_contact_primary == other.is_contact_primary
            and self.email_account_associations == other.email_account_associations
        )

    @model_validator(mode="after")
    def _validate_email_account_associations(self) -> Self:
        account_ids = [assoc.account_id for assoc in self.email_account_associations]
        if len(account_ids) != len(set(account_ids)):
            raise ValueError(
                "email_account_associations must contain unique account_ids"
            )
        return self

    @model_validator(mode="after")
    def _validate_labels(self) -> Self:
        if self.labels and len(self.labels) != 1:
            raise ValueError(
                "only a single label in labels setting is supported for now."
            )
        return self

    def to_db_inserts(
        self,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
    ) -> tuple[ContactEmail, list[ContactEmailAccountAssociation]]:
        now = zoned_utc_now()
        contact_email_id = uuid4()
        contact_email = ContactEmail(
            id=contact_email_id,
            organization_id=organization_id,
            contact_id=contact_id,
            email=self.email,
            is_contact_primary=self.is_contact_primary,
            # set default labels
            labels=self.labels if self.labels else [ContactChannelLabel.PERSONAL],
            created_by_user_id=user_id,
            updated_by_user_id=user_id,
            created_at=now,
            updated_at=now,
        )

        contact_email_account_associations = [
            ContactEmailAccountAssociation(
                id=uuid4(),
                organization_id=organization_id,
                contact_email_id=contact_email_id,
                contact_id=contact_id,
                account_id=caa.account_id,
                is_contact_account_primary=caa.is_contact_account_primary,
                created_by_user_id=user_id,
                updated_by_user_id=user_id,
                created_at=now,
                updated_at=now,
            )
            for caa in self.email_account_associations
        ]

        return contact_email, contact_email_account_associations


class CreateDbContactPhoneNumberAccountAssociationRequest(BaseModel):
    id: UUID = uuid4()
    account_id: UUID
    is_contact_account_primary: bool

    def __eq__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(other, type(self)):
            return NotImplemented
        return (
            self.account_id == other.account_id
            and self.is_contact_account_primary == other.is_contact_account_primary
        )


class CreateDbContactPhoneNumberRequest(BaseModel):
    phone_number: PhoneNumberWithExtension
    is_contact_primary: bool
    phone_number_account_associations: list[
        CreateDbContactPhoneNumberAccountAssociationRequest
    ] = Field(default_factory=list)

    def __eq__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(other, type(self)):
            return NotImplemented
        return (
            self.phone_number == other.phone_number
            and self.is_contact_primary == other.is_contact_primary
            and self.phone_number_account_associations
            == other.phone_number_account_associations
        )

    @model_validator(mode="after")
    def _validate_phone_number_account_associations(self) -> Self:
        account_ids = [
            assoc.account_id for assoc in self.phone_number_account_associations
        ]
        if len(account_ids) != len(set(account_ids)):
            raise ValueError(
                "phone_number_account_associations must contain unique account_ids"
            )
        return self

    def to_db_inserts(
        self,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
    ) -> tuple[ContactPhoneNumber, list[ContactPhoneNumberAccountAssociation]]:
        now = zoned_utc_now()
        contact_phone_number_id = uuid4()
        contact_phone_number = ContactPhoneNumber(
            id=contact_phone_number_id,
            organization_id=organization_id,
            contact_id=contact_id,
            phone_number=self.phone_number,
            is_contact_primary=self.is_contact_primary,
            created_by_user_id=user_id,
            updated_by_user_id=user_id,
            created_at=now,
            updated_at=now,
        )

        contact_phone_number_account_associations = [
            ContactPhoneNumberAccountAssociation(
                id=uuid4(),
                organization_id=organization_id,
                contact_phone_number_id=contact_phone_number_id,
                contact_id=contact_id,
                account_id=cpa.account_id,
                is_contact_account_primary=cpa.is_contact_account_primary,
                created_by_user_id=user_id,
                updated_by_user_id=user_id,
                created_at=now,
                updated_at=now,
            )
            for cpa in self.phone_number_account_associations
        ]

        return contact_phone_number, contact_phone_number_account_associations


class CreateContactAccountRoleRequest(BaseModel):
    account_id: UUID
    is_primary_account: bool
    title: str | None = None
    department: str | None = None

    def __eq__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(other, type(self)):
            return NotImplemented
        return (
            self.account_id == other.account_id
            and self.is_primary_account == other.is_primary_account
        )

    def to_db_inserts(
        self,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        contact_title: str | None = None,
    ) -> ContactAccountAssociation:
        now = zoned_utc_now()
        return ContactAccountAssociation(
            organization_id=organization_id,
            contact_id=contact_id,
            account_id=self.account_id,
            is_primary=self.is_primary_account,
            title=contact_title if not self.title else self.title,
            department=self.department,
            created_by_user_id=user_id,
            updated_by_user_id=user_id,
            created_at=now,
            updated_at=now,
        )


class CreateContactRequest(BaseModel):
    model_config = ConfigDict(frozen=True)

    """
    Request to create a contact.
    Optionally:
    - contact_emails: list of contact emails to create
    - contact_account_associations: list of contact account associations to create

    The request will be processed in a transaction and failed if any of the
    contact emails or contact account associations fail to be created.
    """
    contact: CreateDbContactRequest
    contact_account_roles: list[CreateContactAccountRoleRequest] = Field(
        default_factory=list
    )

    overwrite_archived_emails: bool = False
    contact_emails: list[CreateDbContactEmailRequest] = Field(default_factory=list)
    contact_phone_numbers: list[CreateDbContactPhoneNumberRequest] = Field(
        default_factory=list
    )

    _contact_email_by_email: dict[EmailStrLower, CreateDbContactEmailRequest] = (
        PrivateAttr(default_factory=dict)
    )
    _contact_phone_number_by_phone_number: dict[
        PhoneNumberWithExtension, CreateDbContactPhoneNumberRequest
    ] = PrivateAttr(default_factory=dict)
    _contact_account_role_by_account_id: dict[UUID, CreateContactAccountRoleRequest] = (
        PrivateAttr(default_factory=dict)
    )

    @model_validator(mode="after")
    def _validate_create_contact_request(self) -> Self:
        self._populate_private_attributes()
        if self.contact_emails:
            self._validate_contact_emails()
        if self.contact_phone_numbers:
            self._validate_contact_emails()
        if self.contact_account_roles:
            self._validate_contact_account_roles()
        return self

    @property
    def primary_account_role(
        self,
    ) -> CreateContactAccountRoleRequest | None:
        return next(
            (ra for ra in self.contact_account_roles if ra.is_primary_account), None
        )

    @property
    def additional_account_roles(
        self,
    ) -> list[CreateContactAccountRoleRequest]:
        return [ra for ra in self.contact_account_roles if not ra.is_primary_account]

    @property
    def primary_contact_email(self) -> CreateDbContactEmailRequest | None:
        return next((ce for ce in self.contact_emails if ce.is_contact_primary), None)

    @property
    def primary_contact_phone_number(self) -> CreateDbContactPhoneNumberRequest | None:
        return next(
            (cp for cp in self.contact_phone_numbers if cp.is_contact_primary), None
        )

    @property
    def additional_contact_emails(
        self,
    ) -> list[CreateDbContactEmailRequest]:
        return [ce for ce in self.contact_emails if not ce.is_contact_primary]

    @property
    def additional_contact_phone_numbers(
        self,
    ) -> list[CreateDbContactPhoneNumberRequest]:
        return [cp for cp in self.contact_phone_numbers if not cp.is_contact_primary]

    def _populate_private_attributes(self) -> None:
        self._contact_account_role_by_account_id = (
            {car.account_id: car for car in self.contact_account_roles}
            if self.contact_account_roles
            else {}
        )
        self._contact_email_by_email = (
            {ce.email: ce for ce in self.contact_emails} if self.contact_emails else {}
        )
        self._contact_phone_number_by_phone_number = (
            {cp.phone_number: cp for cp in self.contact_phone_numbers}
            if self.contact_phone_numbers
            else {}
        )

    def _validate_contact_emails(self) -> Self:
        _contact_primary: CreateDbContactEmailRequest | None = None
        _contact_email_account_primary_id_set: set[UUID] = set()

        if len(self._contact_email_by_email) != len(self.contact_emails):
            _duplicates = [
                ce.email
                for ce in self.contact_emails
                if self._contact_email_by_email[ce.email] != ce
            ]
            raise ValueError(
                f"all_contact_emails must contain unique emails, but found duplicates: {_duplicates}"
            )

        for contact_email in self.contact_emails:
            if contact_email.is_contact_primary:
                if _contact_primary:
                    raise ValueError("only one contact_email can be primary")
                _contact_primary = contact_email

            for account_association in contact_email.email_account_associations:
                if (
                    account_association.account_id
                    not in self._contact_account_role_by_account_id
                ):
                    raise ValueError(
                        "contact_email.account_id must be in contact_account_associations"
                    )

                if account_association.is_contact_account_primary:
                    if (
                        account_association.account_id
                        in _contact_email_account_primary_id_set
                    ):
                        raise ValueError(
                            "only one contact_email_account_association can be primary"
                        )
                    _contact_email_account_primary_id_set.add(
                        account_association.account_id
                    )

        return self

    def _validate_contact_phone_numbers(self) -> Self:
        _contact_primary: CreateDbContactPhoneNumberRequest | None = None
        _contact_phone_number_account_primary_id_set: set[UUID] = set()

        if len(self._contact_phone_number_by_phone_number) != len(
            self.contact_phone_numbers
        ):
            _duplicates = [
                cp.phone_number
                for cp in self.contact_phone_numbers
                if self._contact_phone_number_by_phone_number[cp.phone_number] != cp
            ]
            raise ValueError(
                f"all_contact_phone_numbers must contain unique emails, but found duplicates: {_duplicates}"
            )

        for contact_phone_number in self.contact_phone_numbers:
            if contact_phone_number.is_contact_primary:
                if _contact_primary:
                    raise ValueError("only one contact_phone_number can be primary")
                _contact_primary = contact_phone_number

            for (
                account_association
            ) in contact_phone_number.phone_number_account_associations:
                if (
                    account_association.account_id
                    not in self._contact_account_role_by_account_id
                ):
                    raise ValueError(
                        "contact_phone_number.account_id must be in contact_account_associations"
                    )

                if account_association.is_contact_account_primary:
                    if (
                        account_association.account_id
                        in _contact_phone_number_account_primary_id_set
                    ):
                        raise ValueError(
                            "only one contact_phone_number_account_association can be primary"
                        )
                    _contact_phone_number_account_primary_id_set.add(
                        account_association.account_id
                    )

        return self

    def _validate_contact_account_roles(self) -> Self:
        _primary_account_role: CreateContactAccountRoleRequest | None = None
        for contact_account_role in self.contact_account_roles:
            if contact_account_role.is_primary_account:
                if _primary_account_role:
                    raise ValueError(
                        f"only one contact_account_role can be primary, {self.contact_account_roles}"
                    )
                _primary_account_role = contact_account_role
        if len(self._contact_account_role_by_account_id) != len(
            self.contact_account_roles
        ):
            raise ValueError("contact_account_roles must contain unique account_ids")
        return self


class ContactName(BaseModel):
    id: UUID
    display_name: str
    primary_email: EmailStrLower | None = None
    title: str | None = None
    role: str | None = None


class PipelineAccountEngagementContact(DBModel):
    id: UUID
    pipeline_id: UUID
    organization_id: UUID
    contact_id: UUID
    is_primary: bool
    display_name: str
    primary_email: EmailStrLower | None = None
    title: str | None = None
    role: str | None = None
    archived_at: ZoneRequiredDateTime | None = None
    primary_phone_number: str | None = None
    department: str | None = None


class ContactUpdate(TableBoundedModel[Contact]):
    first_name: UnsetAware[str | None] = UNSET
    last_name: UnsetAware[str | None] = UNSET
    middle_name: UnsetAware[str | None] = UNSET
    display_name: UnsetAware[str] = UNSET
    primary_email: UnsetAware[EmailStrLower | None] = UNSET
    primary_phone_number: UnsetAware[PhoneNumberWithExtension | None] = UNSET
    stage_id: UnsetAware[UUID] = UNSET
    linkedin_url: UnsetAware[str | None] = UNSET
    zoominfo_url: UnsetAware[str | None] = UNSET
    owner_user_id: UnsetAware[UUID] = UNSET
    title: UnsetAware[str | None] = UNSET
    department: UnsetAware[str | None] = UNSET
    person_id: UnsetAware[UUID | None] = UNSET
    facebook_url: UnsetAware[str | None] = UNSET
    participants: UnsetAware[list[EntityParticipant] | None] = UNSET
    # created_source: UnsetAware[str | None] = UNSET
    address_id: UnsetAware[UUID | None] = UNSET
    archived_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    archived_by_user_id: UnsetAware[UUID | None] = UNSET
    updated_by_user_id: UUID
    updated_at: ZoneRequiredDateTime = Field(default_factory=zoned_utc_now)
    integrity_job_started_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    integrity_job_started_by_user_id: UnsetAware[UUID | None] = UNSET
    integrity_job_started_by_job_ids: UnsetAware[list[UUID]] = UNSET


class ContactPrimaryAccountIdUpdate(TableBoundedModel[Contact]):
    primary_account_id: UUID | None
    updated_at: ZoneRequiredDateTime = Field(default_factory=zoned_utc_now)
    updated_by_user_id: UUID


class ContactUpdateCondition(TableBoundedModel[Contact]):
    primary_email: UnsetAware[EmailStrLower | None] = UNSET
    primary_phone_number: UnsetAware[PhoneNumber | None] = UNSET
    updated_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    primary_account_id: UnsetAware[UUID | None] = UNSET
    integrity_job_started_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    integrity_job_started_by_user_id: UnsetAware[UUID | None] = UNSET
    integrity_job_started_by_job_ids: UnsetAware[list[UUID]] = UNSET


class UpdateContactRequest(BaseModel):
    model_config = ConfigDict(frozen=True)
    contact_update: ContactUpdate
    contact_id: UUID
    organization_id: UUID
    primary_email: EmailStrLower | None = None
    remove_current_primary_email: bool | None = False
    address_request: AddressCreateRequest | None = None
    participants: list[EntityParticipant] | None = None

    @model_validator(mode="after")
    def validate_self(self) -> Self:
        if (
            specified(self.contact_update.address_id)
            and (not self.contact_update.address_id)
            and self.address_request
        ):
            raise ValueError(
                "request to set address_id to None while providing address_request"
            )
        return self
