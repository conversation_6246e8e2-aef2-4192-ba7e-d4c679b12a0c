from typing import <PERSON>A<PERSON><PERSON>
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.db.models.core.base import (
    Column,
    JsonColumn,
    TableBoundedModel,
    TableModel,
)
from salestech_be.db.models.core.constants import TableName
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now


class CRMSubDomain(NameValueStrEnum):
    TASK = "TASK"
    CALENDAR = "CALENDAR"
    EMAIL = "EMAIL"
    NOTE = "NOTE"
    SEQUENCE = "SEQUENCE"
    MEETING = "MEETING"
    INTELLIGENCE = "INTELLIGENCE"
    VOICE_CALL = "VOICE_CALL"
    DOMAIN_OBJECT_LIST = "DOMAIN_OBJECT_LIST"


class EntityType(NameValueStrEnum):
    CONTACT = "CONTACT"
    CONTACT_EMAIL = "CONTACT_EMAIL"
    CONTACT_PHONE_NUMBER = "CONTACT_PHONE_NUMBER"
    ACCOUNT = "ACCOUNT"
    PIPELINE = "PIPELINE"


class IntegrityJobType(NameValueStrEnum):
    MOVE = "MOVE"
    MERGE = "MERGE"
    REMOVE = "REMOVE"
    DELETE = "DELETE"
    ARCHIVE = "ARCHIVE"
    ADD = "ADD"


class IntegrityJobName(NameValueStrEnum):
    MOVE_CONTACT_TO_ACCOUNT = "MOVE_CONTACT_TO_ACCOUNT"
    REMOVE_CONTACT_FROM_ACCOUNT = "REMOVE_CONTACT_FROM_ACCOUNT"

    MERGE_CONTACTS = "MERGE_CONTACTS"
    MERGE_ACCOUNTS = "MERGE_ACCOUNTS"

    ARCHIVE_CONTACT = "ARCHIVE_CONTACT"
    ARCHIVE_ACCOUNT = "ARCHIVE_ACCOUNT"

    MOVE_CONTACT_EMAIL_TO_CONTACT = "MOVE_CONTACT_EMAIL_TO_CONTACT"
    REMOVE_CONTACT_EMAIL_FROM_CONTACT = "REMOVE_CONTACT_EMAIL_FROM_CONTACT"

    MOVE_CONTACT_EMAIL_TO_ACCOUNT = "MOVE_CONTACT_EMAIL_TO_ACCOUNT"
    REMOVE_CONTACT_EMAIL_FROM_ACCOUNT = "REMOVE_CONTACT_EMAIL_FROM_ACCOUNT"

    ADD_ACCOUNT_TO_PIPELINE = "ADD_ACCOUNT_TO_PIPELINE"


def get_integrity_job_name(  # noqa: PLR0911, C901
    integrity_job_type: IntegrityJobType,
    src_entity_type: EntityType,
    dest_entity_type: EntityType | None,
) -> IntegrityJobName:
    if (
        src_entity_type == EntityType.CONTACT
        and dest_entity_type == EntityType.ACCOUNT
        and integrity_job_type == IntegrityJobType.MOVE
    ):
        return IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT
    elif (
        src_entity_type == EntityType.CONTACT
        and dest_entity_type == EntityType.ACCOUNT
        and integrity_job_type == IntegrityJobType.REMOVE
    ):
        return IntegrityJobName.REMOVE_CONTACT_FROM_ACCOUNT
    elif (
        src_entity_type == EntityType.CONTACT
        and dest_entity_type == EntityType.CONTACT
        and integrity_job_type == IntegrityJobType.MERGE
    ):
        return IntegrityJobName.MERGE_CONTACTS
    elif (
        src_entity_type == EntityType.ACCOUNT
        and dest_entity_type == EntityType.ACCOUNT
        and integrity_job_type == IntegrityJobType.MERGE
    ):
        return IntegrityJobName.MERGE_ACCOUNTS
    elif (
        src_entity_type == EntityType.CONTACT
        and dest_entity_type is None
        and integrity_job_type == IntegrityJobType.ARCHIVE
    ):
        return IntegrityJobName.ARCHIVE_CONTACT
    elif (
        src_entity_type == EntityType.ACCOUNT
        and dest_entity_type is None
        and integrity_job_type == IntegrityJobType.ARCHIVE
    ):
        return IntegrityJobName.ARCHIVE_ACCOUNT
    elif (
        src_entity_type == EntityType.CONTACT_EMAIL
        and dest_entity_type == EntityType.CONTACT
        and integrity_job_type == IntegrityJobType.REMOVE
    ):
        return IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_CONTACT
    elif (
        src_entity_type == EntityType.CONTACT_EMAIL
        and dest_entity_type == EntityType.ACCOUNT
        and integrity_job_type == IntegrityJobType.MOVE
    ):
        return IntegrityJobName.MOVE_CONTACT_EMAIL_TO_ACCOUNT
    elif (
        src_entity_type == EntityType.CONTACT_EMAIL
        and dest_entity_type == EntityType.ACCOUNT
        and integrity_job_type == IntegrityJobType.REMOVE
    ):
        return IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_ACCOUNT
    elif (
        src_entity_type == EntityType.CONTACT_EMAIL
        and dest_entity_type == EntityType.CONTACT
        and integrity_job_type == IntegrityJobType.MOVE
    ):
        return IntegrityJobName.MOVE_CONTACT_EMAIL_TO_CONTACT
    elif (
        src_entity_type == EntityType.ACCOUNT
        and dest_entity_type == EntityType.PIPELINE
        and integrity_job_type == IntegrityJobType.ADD
    ):
        return IntegrityJobName.ADD_ACCOUNT_TO_PIPELINE
    else:
        raise NotImplementedError(
            f"Integrity job name not implemented for job_type={integrity_job_type}, "
            f"src_entity_type={src_entity_type}, dest_entity_type={dest_entity_type}"
        )


class JobStatus(NameValueStrEnum):
    SUCCESS = "SUCCESS"
    FAIL = "FAIL"
    RUNNING = "RUNNING"
    QUEUED = "QUEUED"
    NEW = "NEW"


class SubDomainJobStatus(NameValueStrEnum):
    SUCCESS = "SUCCESS"
    FAIL = "FAIL"
    RUNNING = "RUNNING"
    NEW = "NEW"


class JobErrorDetails(BaseModel):
    namespace: str
    message: str
    workflow_id: str
    workflow_type: str
    workflow_run_id: str


class MoveContactToAccountContextualParam(BaseModel):
    src_account_id: UUID | None
    title: str | None = None
    department: str | None = None


class MoveContactEmailToAccountContextualParam(BaseModel):
    src_account_id: UUID
    contact_id: UUID


class RemoveContactEmailFromContactContextualParam(BaseModel):
    remove_contact_account_association_if_last: bool


class MoveContactEmailToContactContextualParam(BaseModel):
    src_contact_id: UUID
    remove_contact_account_association_if_last: bool
    create_contact_account_association_if_missing: bool


class RemoveContactEmailFromAccountContextualParam(BaseModel):
    contact_id: UUID


class MoveContactToAccountUserOption(NameValueStrEnum):
    RETAIN = "RETAIN"
    MOVE = "MOVE"


CRMIntegrityJobContextualParam: TypeAlias = (
    MoveContactToAccountContextualParam
    | MoveContactEmailToAccountContextualParam
    | RemoveContactEmailFromAccountContextualParam
    | RemoveContactEmailFromContactContextualParam
    | MoveContactEmailToContactContextualParam
)

CRMIntegrityJobUserOption: TypeAlias = MoveContactToAccountUserOption


class CRMIntegrityJob(TableModel):
    """CRM Integrity Job."""

    table_name = TableName.crm_integrity_job
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    type: Column[IntegrityJobType]
    status: Column[JobStatus]
    src_entity_type: Column[EntityType]
    src_entity_id: Column[UUID]
    dest_entity_type: Column[EntityType | None] = None
    dest_entity_id: Column[UUID | None] = None
    contextual_param: JsonColumn[CRMIntegrityJobContextualParam | None] = None
    started_at: Column[ZoneRequiredDateTime | None] = None
    ended_at: Column[ZoneRequiredDateTime | None] = None
    retry_count: Column[int] = 0
    error_details: JsonColumn[JobErrorDetails | None] = None

    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None

    organization_id: Column[UUID]


class CRMIntegrityJobUpdate(TableBoundedModel[CRMIntegrityJob]):
    status: UnsetAware[JobStatus] = UNSET
    started_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    ended_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    retry_count: UnsetAware[int] = UNSET
    updated_by_user_id: UUID
    updated_at: ZoneRequiredDateTime = Field(default_factory=zoned_utc_now)
    error_details: UnsetAware[JobErrorDetails | None] = UNSET


class CRMIntegrityJobUpdateCondition(TableBoundedModel[CRMIntegrityJob]):
    status: UnsetAware[JobStatus] = UNSET


class CRMIntegritySubDomainJob(TableModel):
    table_name = TableName.crm_integrity_subdomain_job
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    integrity_job_id: Column[UUID]

    subdomain: Column[CRMSubDomain]
    user_choice: Column[CRMIntegrityJobUserOption | None] = None
    status: Column[SubDomainJobStatus]

    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None

    organization_id: Column[UUID]


class IntegrityOperation(NameValueStrEnum):
    VALIDATE = "VALIDATE"
    ASSOCIATE = "ASSOCIATE"
    UNASSOCIATE = "UNASSOCIATE"
    ADD = "ADD"
    REMOVE = "REMOVE"

    MARK_LOCK = "MARK_LOCK"
    MARK_UNLOCK = "MARK_UNLOCK"
    MARK_MOVE = "MARK_MOVE"

    ARCHIVE = "ARCHIVE"


class CRMIntegrityOperation(TableModel):
    """CRM Integrity fix Operation"""

    table_name = TableName.crm_integrity_operation
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    integrity_job_id: Column[UUID]

    type: Column[IntegrityOperation]
    status: Column[JobStatus]
    src_entity_type: Column[EntityType]
    src_entity_id: Column[UUID]
    dest_entity_type: Column[EntityType]
    dest_entity_id: Column[UUID]

    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None

    organization_id: Column[UUID]


class AssociatedEntityType(NameValueStrEnum):
    ACTIVITY = "ACTIVITY"
    ACTIVITY_SUB_REFERENCE = "ACTIVITY_SUB_REFERENCE"

    EMAIL = "EMAIL"
    THREAD = "THREAD"
    GLOBAL_THREAD = "GLOBAL_THREAD"

    TASK = "TASK"
    TASK_REFERENCE = "TASK_REFERENCE"
    NOTE = "NOTE"
    NOTE_REFERENCE = "NOTE_REFERENCE"
    VOICE_CALL = "VOICE_CALL"

    SEQUENCE = "SEQUENCE"
    SEQUENCE_ENROLLMENT = "SEQUENCE_ENROLLMENT"
    SEQUENCE_STEP_EXECUTION = "SEQUENCE_STEP_EXECUTION"

    INTEL_PERSON_ASSOCIATION = "INTEL_PERSON_ASSOCIATION"
    INTEL_COMPANY_ASSOCIATION = "INTEL_COMPANY_ASSOCIATION"

    MEETING = "MEETING"
    INSIGHT = "INSIGHT"
    USER_CALENDAR_EVENT = "USER_CALENDAR_EVENT"

    DOMAIN_OBJECT_LIST = "DOMAIN_OBJECT_LIST"


class AssociatedEntityField(NameValueStrEnum):
    CC = "CC"
    BCC = "BCC"
    SEND_TO = "SEND_TO"
    REPLY_TO = "REPLY_TO"
    SEND_FROM = "SEND_FROM"
    PARTICIPANTS = "PARTICIPANTS"
    REFERENCE_ID = "REFERENCE_ID"
    ATTENDEES = "ATTENDEES"
    INVITEES = "INVITEES"
    CONTACT_ID = "CONTACT_ID"
    CONTACT_IDS = "CONTACT_IDS"
    ACCOUNT_ID = "ACCOUNT_ID"
    ACCOUNT_IDS = "ACCOUNT_IDS"
    PIPELINE_ID = "PIPELINE_ID"
    PIPELINE_STAGE_ID = "PIPELINE_STAGE_ID"
    STATUS = "STATUS"


class AssociatedEntityFieldType(NameValueStrEnum):
    JSON = "JSON"
    STRING = "STRING"
    UUID = "UUID"
    LIST_OF_UUID = "LIST_OF_UUID"


class AssociatedEntityOperation(NameValueStrEnum):
    CREATE = "CREATE"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    ARCHIVE = "ARCHIVE"


class CRMIntegrityAssociatedEntityOperation(TableModel):
    """CRM Associated Record Operation"""

    table_name = TableName.crm_integrity_associated_entity_operation
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    integrity_job_id: Column[UUID]
    integrity_subdomain_job_id: Column[UUID | None] = None

    type: Column[AssociatedEntityOperation]
    entity_type: Column[AssociatedEntityType]
    entity_id: Column[UUID]
    entity_field: Column[AssociatedEntityField | None] = None
    entity_field_type: Column[AssociatedEntityFieldType | None] = None
    before_value: Column[str | None] = None
    after_value: Column[str | None] = None

    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None

    organization_id: Column[UUID]
