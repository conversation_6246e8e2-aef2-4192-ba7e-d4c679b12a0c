from __future__ import annotations

from typing import Any
from uuid import UUID

from salestech_be.core.reporting.type.layout import (
    DashboardLayoutConfig,
    DashboardReportLayoutConfig,
    ReportLayoutConfig,
)
from salestech_be.core.reporting.type.query_config import QueryConfig
from salestech_be.db.models.core.base import Column, JsonColumn, TableModel
from salestech_be.db.models.core.constants import TableName
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class ReportingDatasetSource(NameValueStrEnum):
    DATABASE = "DATABASE"
    DBT = "DBT"
    CUSTOM_OBJECT = "CUSTOM_OBJECT"
    UPLOADED_SHEET = "UPLOADED_SHEET"
    CUSTOM = "CUSTOM"
    INTERNAL = "INTERNAL"
    TEMPLATE = "TEMPLATE"


class ReportingDatasetType(NameValueStrEnum):
    TABLE = "TABLE"
    QUERY = "QUERY"
    SQL = "SQL"


class ReportingDataset(TableModel):
    table_name = TableName.reporting_dataset
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    description: Column[str | None] = None
    source: Column[ReportingDatasetSource]
    type: Column[ReportingDatasetType]
    table_reference: Column[str | None] = None
    query_config: JsonColumn[QueryConfig | None] = None
    sql_statement: Column[str | None] = None

    organization_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class ReportingDatasetFieldDataType(NameValueStrEnum):
    STRING = "STRING"
    NUMBER = "NUMBER"
    BOOLEAN = "BOOLEAN"
    DATE = "DATE"
    DATETIME = "DATETIME"
    FLOAT = "FLOAT"
    INTEGER = "INTEGER"
    DECIMAL = "DECIMAL"
    TEXT = "TEXT"
    JSON = "JSON"


class ReportingDatasetField(TableModel):
    table_name = TableName.reporting_dataset_field
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    dataset_id: Column[UUID]
    name: Column[str]
    display_name: Column[str | None] = None
    data_type: Column[ReportingDatasetFieldDataType]

    organization_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class ReportingDatasetLineageType(NameValueStrEnum):
    FIELD = "FIELD"
    DATASET = "DATASET"


class ReportingDatasetLineage(TableModel):
    table_name = TableName.reporting_dataset_lineage
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    lineage_type: Column[ReportingDatasetLineageType] = (
        ReportingDatasetLineageType.FIELD
    )
    parent_dataset_id: Column[UUID | None] = None
    parent_field_id: Column[UUID | None] = None
    child_dataset_id: Column[UUID | None] = None
    child_field_id: Column[UUID | None] = None

    organization_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class ReportingDatasetJoinType(NameValueStrEnum):
    LEFT = "LEFT"
    RIGHT = "RIGHT"
    INNER = "INNER"
    OUTER = "OUTER"


class ReportingDatasetRelation(TableModel):
    table_name = TableName.reporting_dataset_relation
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    source_dataset_id: Column[UUID]
    target_dataset_id: Column[UUID]
    source_field_id: Column[UUID]
    target_field_id: Column[UUID]
    join_type: Column[ReportingDatasetJoinType] = ReportingDatasetJoinType.LEFT

    organization_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class ReportingFunction(TableModel):
    table_name = TableName.reporting_function
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    version: Column[int]
    func_name: Column[str]
    display_name: Column[str]
    example: Column[str | None] = None
    description: Column[str | None] = None
    return_type: Column[str]
    is_active: Column[bool] = True

    created_at: Column[ZoneRequiredDateTime]
    updated_at: Column[ZoneRequiredDateTime | None] = None


class ReportingFunctionParameter(TableModel):
    table_name = TableName.reporting_function_parameter
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    function_id: Column[UUID]
    param_order: Column[int]
    param_type: Column[str]
    is_required: Column[bool] = False
    options: JsonColumn[dict[str, Any] | None] = None

    created_at: Column[ZoneRequiredDateTime]
    updated_at: Column[ZoneRequiredDateTime | None] = None


class ReportingReport(TableModel):
    table_name = TableName.reporting_report
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    description: Column[str | None] = None
    dataset_id: Column[UUID]  # Reference to the dataset used by this report
    layout_config: JsonColumn[ReportLayoutConfig | None] = None

    organization_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class ReportingDashboard(TableModel):
    table_name = TableName.reporting_dashboard
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    description: Column[str | None] = None
    layout_config: JsonColumn[DashboardLayoutConfig | None] = None

    organization_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class ReportingDashboardReportAssociation(TableModel):
    table_name = TableName.reporting_dashboard_report_association
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    dashboard_id: Column[UUID]
    report_id: Column[UUID]
    layout_config: JsonColumn[DashboardReportLayoutConfig | None] = None

    organization_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
