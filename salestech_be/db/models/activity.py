from __future__ import annotations

from datetime import datetime
from enum import StrEnum, auto
from typing import Annotated, Any, Literal, Self, cast
from uuid import UUID

from pydantic import BaseModel, Discriminator

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.core.meeting.service_type import MeetingAttendance
from salestech_be.db.models.core.base import (
    Column,
    JsonColumn,
    TableBoundedModel,
    TableModel,
)
from salestech_be.db.models.core.constants import TableName
from salestech_be.db.models.meeting import (
    MeetingCancelReason,
    MeetingProvider,
    MeetingReferenceIdType,
    MeetingStatus,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class ActivityReferenceIdType(StrEnum):
    THREAD_ID = "THREAD_ID"
    GLOBAL_THREAD_ID = "GLOBAL_THREAD_ID"
    CALENDAR_EVENT_GROUP_KEY = "CALENDAR_EVENT_GROUP_KEY"
    NOTE_ID = "NOTE_ID"
    MEETING_ID = "MEETING_ID"
    TASK_ID = "TASK_ID"
    SEQUENCE_ID = "SEQUENCE_ID"


class ActivityType(StrEnum):
    EMAIL_THREAD = "EMAIL_THREAD", ActivityReferenceIdType.THREAD_ID
    EMAIL_GLOBAL_THREAD = (
        "EMAIL_GLOBAL_THREAD",
        ActivityReferenceIdType.GLOBAL_THREAD_ID,
    )
    CALENDAR_EVENT = "CALENDAR_EVENT", ActivityReferenceIdType.CALENDAR_EVENT_GROUP_KEY
    NOTE = "NOTE", ActivityReferenceIdType.NOTE_ID
    MEETING = "MEETING", ActivityReferenceIdType.MEETING_ID
    TASK = "TASK", ActivityReferenceIdType.TASK_ID

    # Voice call will be using Meeting as container
    # that means, the activty row will record reference_id == meeting_id
    VOICE_CALL = "VOICE_CALL", ActivityReferenceIdType.MEETING_ID

    SEQUENCE = "SEQUENCE", ActivityReferenceIdType.SEQUENCE_ID

    def __new__(cls, *values: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        value_len = len(values)
        if value_len != 2:  # noqa: PLR2004
            raise TypeError(
                "strictly require 2 arguments (value: str, reference_id_type: ActivityReferenceIdType) "
            )
        str_value = values[0]
        reference_id_type = values[-1]
        if not isinstance(reference_id_type, ActivityReferenceIdType):
            raise TypeError(
                "last argument must be a ActivityReferenceIdType, to specify reference id type"
            )
        value = str(str_value)
        member = str.__new__(cls, value)
        member._value_ = value
        member._reference_id_type = reference_id_type  # type: ignore[attr-defined]  # noqa: SLF001
        return member

    def __hash__(self) -> int:
        return hash(self.value)

    @property
    def reference_id_type(self) -> ActivityReferenceIdType:
        return cast(ActivityReferenceIdType, self._reference_id_type)  # type: ignore[attr-defined]


class ActivitySubType(StrEnum):
    EMAIL_SCHEDULED = "EMAIL_SCHEDULED"
    EMAIL_SENT = "EMAIL_SENT"
    EMAIL_REPLIED = "EMAIL_REPLIED"
    EMAIL_SCHEDULED_UPDATE = "EMAIL_SCHEDULED_UPDATE"
    EMAIL_SCHEDULED_CANCEL = "EMAIL_SCHEDULED_CANCEL"
    EMAIL_SCHEDULED_ERROR = "EMAIL_SCHEDULED_ERROR"
    EMAIL_SEND_FAILURE = "EMAIL_SEND_FAILURE"

    CALENDAR_EVENT_INVITED = "CALENDAR_EVENT_INVITED"
    CALENDAR_EVENT_UPDATED = "CALENDAR_EVENT_UPDATED"
    CALENDAR_EVENT_ACCEPTED = "CALENDAR_EVENT_ACCEPTED"
    CALENDAR_EVENT_MAYBE = "CALENDAR_EVENT_MAYBE"
    CALENDAR_EVENT_REJECTED = "CALENDAR_EVENT_REJECTED"

    NOTE_CREATED = "NOTE_CREATED"
    NOTE_UPDATED = "NOTE_UPDATED"
    NOTE_DELETED = "NOTE_DELETED"

    MEETING_SCHEDULED = "MEETING_SCHEDULED"
    MEETING_UPDATED = "MEETING_UPDATED"
    MEETING_CANCELED = "MEETING_CANCELED"
    MEETING_COMPLETED = "MEETING_COMPLETED"

    TASK_CREATED = "TASK_CREATED"
    TASK_COMPLETED = "TASK_COMPLETED"

    CALL_OUTBOUND_ANSWERED = "CALL_OUTBOUND_ANSWERED"
    CALL_OUTBOUND_NO_ANSWER = "CALL_OUTBOUND_NO_ANSWER"
    CALL_INBOUND_ANSWERED = "CALL_INBOUND_ANSWERED"
    CALL_INBOUND_NO_ANSWER = "CALL_INBOUND_NO_ANSWER"

    SEQUENCE_CREATED = "SEQUENCE_CREATED"
    SEQUENCE_ACTIVATED = "SEQUENCE_ACTIVATED"
    SEQUENCE_DEACTIVATED = "SEQUENCE_DEACTIVATED"
    SEQUENCE_STEP_CREATED = "SEQUENCE_STEP_CREATED"
    SEQUENCE_STEP_UPDATED = "SEQUENCE_STEP_UPDATED"
    SEQUENCE_STEP_DELETED = "SEQUENCE_STEP_DELETED"
    SEQUENCE_STEP_VARIANT_CREATED = "SEQUENCE_STEP_VARIANT_CREATED"
    SEQUENCE_STEP_VARIANT_UPDATED = "SEQUENCE_STEP_VARIANT_UPDATED"
    SEQUENCE_STEP_VARIANT_DELETED = "SEQUENCE_STEP_VARIANT_DELETED"


class ActivityPriority(StrEnum):
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"


class ActivityStatus(StrEnum):
    HIGHLIGHTED = "HIGHLIGHTED"
    READ = "READ"
    NEW = "NEW"
    HIDDEN = "HIDDEN"


class ActivitySubReferenceType(StrEnum):
    EMAIL_FROM = "EMAIL_FROM"
    EMAIL_TO = "EMAIL_TO"
    EMAIL_CC = "EMAIL_CC"
    EMAIL_BCC = "EMAIL_BCC"
    CALENDER_EVENT_ORGANIZER = "CALENDER_EVENT_ORGANIZER"
    CALENDER_EVENT_PARTICIPANT = "CALENDER_EVENT_PARTICIPANT"
    VIDEO_ORGANIZER = "VIDEO_ORGANIZER"
    VIDEO_PARTICIPANT = "VIDEO_PARTICIPANT"
    TASK_ASSIGNED_TO = "TASK_ASSIGNED_TO"
    NOTE_MENTION = "NOTE_MENTION"
    AUDIO_ORGANIZER = "AUDIO_ORGANIZER"
    AUDIO_PARTICIPANT = "AUDIO_PARTICIPANT"
    CALL_FROM = "CALL_FROM"
    CALL_TO = "CALL_TO"


class ActivityMetadataModel(StrEnum):
    MEETING_SCHEDULED = auto()
    MEETING_UPDATED = auto()
    MEETING_CANCELED = auto()
    MEETING_COMPLETED = auto()
    CALENDAR_EVENT_RSVP = auto()
    NOTES = auto()
    EMAIL_SCHEDULED = auto()
    EMAIL_SCHEDULED_UPDATE = auto()
    EMAIL_SCHEDULED_CANCEL = auto()
    EMAIL_SCHEDULED_ERROR = auto()
    EMAIL_SENT = auto()
    EMAIL_SEND_FAILURE = auto()
    EMAIL_RECEIVED = auto()
    CALL_OUTBOUND_ANSWERED = auto()
    CALL_OUTBOUND_NO_ANSWER = auto()
    CALL_INBOUND_ANSWERED = auto()
    CALL_INBOUND_NO_ANSWER = auto()


class BaseMeetingActivityMetadata(BaseModel):
    id: UUID
    status: MeetingStatus
    reference_id_type: MeetingReferenceIdType | None = None


class BaseEmailActivityMetadata(BaseModel):
    id: UUID
    message_id: UUID | None = None
    subject: str | None = None
    snippet: str


class EmailScheduledActivityMetadata(BaseEmailActivityMetadata):
    metadata_model: Literal[ActivityMetadataModel.EMAIL_SCHEDULED]
    scheduled_at: ZoneRequiredDateTime


class EmailScheduledUpdateActivityMetadata(BaseEmailActivityMetadata):
    metadata_model: Literal[ActivityMetadataModel.EMAIL_SCHEDULED_UPDATE]
    scheduled_at: ZoneRequiredDateTime


class EmailScheduledCancelActivityMetadata(BaseEmailActivityMetadata):
    metadata_model: Literal[ActivityMetadataModel.EMAIL_SCHEDULED_CANCEL]
    canceled_at: ZoneRequiredDateTime


class EmailScheduledErrorActivityMetadata(BaseEmailActivityMetadata):
    metadata_model: Literal[ActivityMetadataModel.EMAIL_SCHEDULED_ERROR]
    error_at: ZoneRequiredDateTime


class EmailSentActivityMetadata(BaseEmailActivityMetadata):
    metadata_model: Literal[ActivityMetadataModel.EMAIL_SENT]
    sent_at: ZoneRequiredDateTime


class EmailSendFailureActivityMetadata(BaseEmailActivityMetadata):
    metadata_model: Literal[ActivityMetadataModel.EMAIL_SEND_FAILURE]
    failure_at: ZoneRequiredDateTime
    failure_reason: str | None = None


class EmailReceivedActivityMetadata(BaseEmailActivityMetadata):
    metadata_model: Literal[ActivityMetadataModel.EMAIL_RECEIVED]
    received_at: ZoneRequiredDateTime


class CalendarEventRsvpStatus(StrEnum):
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    MAYBE = "maybe"


class CalendarEventRsvpActivityMetadata(BaseMeetingActivityMetadata):
    metadata_model: Literal[ActivityMetadataModel.CALENDAR_EVENT_RSVP]
    rsvp_status: CalendarEventRsvpStatus


class MeetingScheduledActivityMetadata(BaseMeetingActivityMetadata):
    metadata_model: Literal[ActivityMetadataModel.MEETING_SCHEDULED]
    title: str | None = None
    meeting_platform: MeetingProvider | None
    starts_at: ZoneRequiredDateTime
    ends_at: ZoneRequiredDateTime | None = None


class MeetingUpdatedActivityMetadata(BaseMeetingActivityMetadata):
    metadata_model: Literal[ActivityMetadataModel.MEETING_UPDATED]
    meeting_platform: MeetingProvider | None
    starts_at: ZoneRequiredDateTime
    ends_at: ZoneRequiredDateTime | None = None
    title: str | None = None
    previous_meeting_platform: MeetingProvider | None
    previous_starts_at: ZoneRequiredDateTime
    previous_ends_at: ZoneRequiredDateTime | None = None
    previous_title: str | None = None


class MeetingCanceledActivityMetadata(BaseMeetingActivityMetadata):
    metadata_model: Literal[ActivityMetadataModel.MEETING_CANCELED]
    title: str | None = None
    canceled_at: ZoneRequiredDateTime
    cancel_reason: MeetingCancelReason | None = None
    starts_at: ZoneRequiredDateTime | None = None
    ends_at: ZoneRequiredDateTime | None = None


class MeetingCompletedActivityMetadata(BaseMeetingActivityMetadata):
    metadata_model: Literal[ActivityMetadataModel.MEETING_COMPLETED]
    title: str | None = None
    meeting_attendance: MeetingAttendance | None = MeetingAttendance.UNKNOWN
    started_at: ZoneRequiredDateTime | None = None
    ended_at: ZoneRequiredDateTime | None = None


class NotesActivityMetadata(BaseModel):
    metadata_model: Literal[ActivityMetadataModel.NOTES]


class CallOutboundAnsweredMetadata(BaseModel):
    metadata_model: Literal[ActivityMetadataModel.CALL_OUTBOUND_ANSWERED]
    starts_at: ZoneRequiredDateTime
    call_duration: int


class CallOutboundNoAnswerMetadata(BaseModel):
    metadata_model: Literal[ActivityMetadataModel.CALL_OUTBOUND_NO_ANSWER]
    starts_at: ZoneRequiredDateTime
    call_duration: int


class CallInboundAnsweredMetadata(BaseModel):
    metadata_model: Literal[ActivityMetadataModel.CALL_INBOUND_ANSWERED]
    starts_at: ZoneRequiredDateTime
    call_duration: int


class CallInboundNoAnswerMetadata(BaseModel):
    metadata_model: Literal[ActivityMetadataModel.CALL_INBOUND_NO_ANSWER]
    starts_at: ZoneRequiredDateTime
    call_duration: int


ActivityMetadata = Annotated[
    MeetingScheduledActivityMetadata
    | MeetingUpdatedActivityMetadata
    | MeetingCanceledActivityMetadata
    | MeetingCompletedActivityMetadata
    | NotesActivityMetadata
    | EmailSentActivityMetadata
    | EmailReceivedActivityMetadata
    | EmailScheduledActivityMetadata
    | EmailScheduledUpdateActivityMetadata
    | EmailScheduledCancelActivityMetadata
    | EmailScheduledErrorActivityMetadata
    | EmailSendFailureActivityMetadata
    | CalendarEventRsvpActivityMetadata
    | CallOutboundAnsweredMetadata
    | CallOutboundNoAnswerMetadata
    | CallInboundAnsweredMetadata
    | CallInboundNoAnswerMetadata,
    Discriminator(discriminator="metadata_model"),
]


class ActivityV2(TableModel):
    """Activity table model."""

    table_name = TableName.activity_v2
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    organization_id: Column[UUID]
    owner_user_id: Column[UUID]
    type: Column[ActivityType]
    reference_id: Column[str]
    sub_type: Column[ActivitySubType]
    priority: Column[ActivityPriority]
    status: Column[ActivityStatus]

    account_id: Column[UUID | None] = None
    sequence_id: Column[UUID | None] = None
    pipeline_id: Column[UUID | None] = None

    display_name: Column[str]
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None

    metadata: JsonColumn[ActivityMetadata | None] = None


class ActivityV2Update(TableBoundedModel[ActivityV2]):
    pipeline_id: UnsetAware[UUID | None] = UNSET
    account_id: UnsetAware[UUID | None] = UNSET
    sub_type: UnsetAware[ActivitySubType] = UNSET
    display_name: UnsetAware[str] = UNSET


class ActivitySubReference(TableModel):
    """ActivityReference table model."""

    table_name = TableName.activity_sub_reference
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    organization_id: Column[UUID]
    activity_id: Column[UUID]
    reference_value: Column[str]
    reference_type: Column[ActivitySubReferenceType]
    contact_id: Column[UUID | None] = None
    email_account_id: Column[UUID | None] = None
    user_id: Column[UUID | None] = None
    display_name: Column[str | None] = None
    created_at: Column[datetime]
    created_by_user_id: Column[UUID]
    updated_at: Column[datetime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[datetime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class ActivitySubReferenceUpdate(TableBoundedModel[ActivitySubReference]):
    contact_id: UnsetAware[UUID | None] = UNSET
    deleted_at: UnsetAware[datetime | None] = UNSET
    deleted_by_user_id: UnsetAware[UUID | None] = UNSET
    updated_at: UnsetAware[datetime] = UNSET
    updated_by_user_id: UnsetAware[UUID] = UNSET
