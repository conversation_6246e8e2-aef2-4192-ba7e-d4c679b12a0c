from enum import StrEnum
from uuid import UUID

from pydantic import EmailStr

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.core.common.accounts_receivable import (
    AccountsReceivable,
)
from salestech_be.db.models.core.base import (
    Column,
    JsonColumn,
    TableBoundedModel,
    TableModel,
)
from salestech_be.db.models.core.constants import TableName
from salestech_be.db.models.sequence import EmailEventType
from salestech_be.integrations.infraforge.type import MailboxPurchaseInvoice
from salestech_be.integrations.mailivery.type import (
    DailyMetrics,
    StatusCode,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class EmailAccountSlotAllocationStatus(StrEnum):
    ALLOCATED = "ALLOCATED"
    CANCELLED = "CANCELLED"


class EmailAccountType(StrEnum):
    REGULAR = "REGULAR"
    OUTBOUND = "OUTBOUND"
    CONNECTED = "CONNECTED"


class EmailProvider(StrEnum):
    NYLAS = "NYLAS"
    INFRAFORGE = "INFRAFORGE"


class EmailAccountUseOverride(StrEnum):
    USE_DESPITE_WARMUP_STATUS = "use_despite_warmup_status"


class EmailAccount(TableModel):
    table_name = TableName.email_account
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    owner_user_id: Column[UUID]
    email: Column[str]
    type: Column[EmailAccountType]
    outbound_domain_id: Column[UUID | None] = None
    external_id: Column[str | None] = None
    vendor: Column[EmailProvider | None] = None
    reply_to_email: Column[EmailStr | None] = None
    aliases: Column[list[str] | None] = None
    active: Column[bool]
    first_name: Column[str | None] = None
    last_name: Column[str | None] = None
    smtp_host: Column[str | None] = None
    smtp_port: Column[int | None] = None
    smtp_username: Column[str | None] = None
    smtp_password: Column[str | None] = None
    imap_host: Column[str | None] = None
    imap_port: Column[int | None] = None
    imap_username: Column[str | None] = None
    imap_password: Column[str | None] = None
    signature_id: Column[UUID | None] = None
    is_default: Column[bool]
    seconds_delay_between_emails: Column[int]
    organization_id: Column[UUID]
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
    archived_at: Column[ZoneRequiredDateTime | None] = None
    archived_by_user_id: Column[UUID | None] = None
    imap_last_synced_at: Column[ZoneRequiredDateTime | None] = None
    invoice: JsonColumn[MailboxPurchaseInvoice | None] = None
    is_mock_record: Column[bool] = False
    use_override: Column[EmailAccountUseOverride | None] = None
    transaction_type: Column[AccountsReceivable | None] = None

    @property
    def display_name(self) -> str | None:
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        if self.first_name:
            return self.first_name
        return None

    @property
    def has_use_override(self) -> bool:
        if not self.use_override:
            return False
        return self.use_override in (EmailAccountUseOverride.USE_DESPITE_WARMUP_STATUS)


class EmailAccountUpdate(TableBoundedModel[EmailAccount]):
    imap_host: UnsetAware[str] = UNSET
    imap_port: UnsetAware[int] = UNSET
    imap_username: UnsetAware[str] = UNSET
    imap_password: UnsetAware[str] = UNSET
    smtp_host: UnsetAware[str] = UNSET
    smtp_port: UnsetAware[int] = UNSET
    smtp_username: UnsetAware[str] = UNSET
    smtp_password: UnsetAware[str] = UNSET
    updated_at: UnsetAware[ZoneRequiredDateTime] = UNSET
    active: UnsetAware[bool] = UNSET
    deleted_at: UnsetAware[ZoneRequiredDateTime] = UNSET
    deleted_by_user_id: UnsetAware[UUID] = UNSET
    archived_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    archived_by_user_id: UnsetAware[UUID | None] = UNSET
    use_override: UnsetAware[EmailAccountUseOverride | None] = UNSET


class EmailAccountSlotAllocation(TableModel):
    table_name = TableName.email_account_slot_allocation
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    email_account_id: Column[UUID]
    organization_id: Column[UUID]
    allocation_status: Column[EmailAccountSlotAllocationStatus]
    allocated_time: Column[ZoneRequiredDateTime]
    created_at: Column[ZoneRequiredDateTime]
    updated_at: Column[ZoneRequiredDateTime]
    cancelled_at: Column[ZoneRequiredDateTime | None] = None


class EmailAccountPool(TableModel):
    table_name = TableName.email_account_pool
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    description: Column[str | None] = None
    is_default: Column[bool]
    organization_id: Column[UUID]
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
    owner_user_id: Column[UUID]


class EmailAccountPoolMembership(TableModel):
    table_name = TableName.email_account_pool_membership
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    email_account_pool_id: Column[UUID]
    email_account_id: Column[UUID]
    organization_id: Column[UUID]
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
    is_mock_record: Column[bool] = False


class EmailAccountWarmUp(TableModel):
    table_name = TableName.email_account_warmup
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    email_account_id: Column[UUID]
    organization_id: Column[UUID]
    external_id: Column[str | None] = None
    vendor: Column[str | None] = None
    status: Column[StatusCode | None] = None
    email_per_day: Column[int | None] = None
    response_rate: Column[int | None] = None
    rampup_enabled: Column[bool | None] = None
    rampup_speed: Column[str | None] = None
    timezone: Column[str | None] = None
    sending_schedule: Column[str | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class WarmupHealth(TableModel):
    table_name = TableName.warmup_health
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    organization_id: Column[UUID]
    email_account_id: Column[UUID]
    warm_up_campaign_id: Column[UUID]
    warmup_health_metrics: JsonColumn[dict[str, str] | None] = None
    created_at: Column[ZoneRequiredDateTime]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None


class EmailAccountHealth(TableModel):
    table_name = TableName.email_account_health
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    organization_id: Column[UUID]
    email_account_id: Column[UUID]
    health_score: Column[int | None] = None
    warmup_health_score: Column[int | None] = None
    deliverability_health_score: Column[int | None] = None
    warmup_health_score_weight: Column[int | None] = None
    deliverability_health_score_weight: Column[int | None] = None
    mailivery_metrics: JsonColumn[list[DailyMetrics] | None] = None
    email_event_metrics: JsonColumn[dict[EmailEventType, int] | None] = None
    created_at: Column[ZoneRequiredDateTime]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None


class EmailAccountHealthHistory(TableModel):
    table_name = TableName.email_account_health_history
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    organization_id: Column[UUID]
    email_account_id: Column[UUID]
    warmup_health_id: Column[UUID | None] = None
    health_score: Column[int | None] = None
    warmup_health_score: Column[int | None] = None
    deliverability_health_score: Column[int | None] = None
    warmup_health_score_weight: Column[int | None] = None
    deliverability_health_score_weight: Column[int | None] = None
    mailivery_metrics: JsonColumn[list[DailyMetrics] | None] = None
    email_event_metrics: JsonColumn[dict[EmailEventType, int] | None] = None
    recorded_at: Column[ZoneRequiredDateTime]
    created_at: Column[ZoneRequiredDateTime]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
