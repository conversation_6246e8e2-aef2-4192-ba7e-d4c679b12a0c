from uuid import UUID

from pydantic import BaseModel

from salestech_be.db.models.core.base import (
    Column,
    TableModel,
)
from salestech_be.db.models.core.constants import TableName
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class AttributionState(NameValueStrEnum):
    NOT_PROCESSED = "NOT_PROCESSED"  # Attribution hasn't occurred yet
    PROCESSED_NOT_SELECTED = (
        "PROCESSED_NOT_SELECTED"  # Attribution occurred but not selected
    )
    PROCESSED_SELECTED = "PROCESSED_SELECTED"  # Attribution occurred and selected


class AttributionSource(NameValueStrEnum):
    DOMAIN_EVENT_CONTEXT = "DOMAIN_EVENT_CONTEXT"
    USER_SELECTION = "USER_SELECTION"
    ATTRIBUTION_MODEL = "ATTRIBUTION_MODEL"


class DomainCRMAssociationRole(NameValueStrEnum):
    ORGANIZER = "ORGANIZER"
    PARTICIPANT = "PARTICIPANT"
    OWNER = "OWNER"


class DomainType(NameValueStrEnum):
    EMAIL = "EMAIL"
    MEETING = "MEETING"
    SEQUENCE = "SEQUENCE"
    NOTE = "NOTE"
    TASK = "TASK"
    VOICE_CALL = "VOICE_CALL"


class AttributionInfo(BaseModel):
    attribution_source: AttributionSource
    attributed_at: ZoneRequiredDateTime | None = None
    attributed_by_user_id: UUID | None = None


class DomainCRMAssociation(TableModel):
    table_name = TableName.domain_crm_association
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    organization_id: Column[UUID]

    # Domain specific tracking info
    domain_type: Column[DomainType]
    global_thread_id: Column[UUID | None] = None
    thread_id: Column[UUID | None] = None
    message_id: Column[UUID | None] = None
    sequence_id: Column[UUID | None] = None
    sequence_step_id: Column[UUID | None] = None
    sequence_step_variant_id: Column[UUID | None] = None
    sequence_step_execution_id: Column[UUID | None] = None
    sequence_enrollment_id: Column[UUID | None] = None
    meeting_id: Column[UUID | None] = None
    note_id: Column[UUID | None] = None
    task_id: Column[UUID | None] = None
    call_id: Column[UUID | None] = None
    user_calendar_event_id: Column[UUID | None] = None

    email: Column[str | None] = None
    phone_number: Column[str | None] = None

    # Contact Attribution
    contact_id: Column[UUID | None] = None
    contact_attribution_state: Column[AttributionState] = AttributionState.NOT_PROCESSED
    contact_attribution_info: Column[AttributionInfo | None] = None

    # Account Attribution
    account_id: Column[UUID | None] = None
    account_attribution_state: Column[AttributionState] = AttributionState.NOT_PROCESSED
    account_attribution_info: Column[AttributionInfo | None] = None

    # Pipepine Attribution
    pipeline_id: Column[UUID | None] = None
    pipeline_attribution_state: Column[AttributionState] = (
        AttributionState.NOT_PROCESSED
    )
    pipeline_attribution_info: Column[AttributionInfo | None] = None

    # User related tracking info
    user_id: Column[UUID | None] = None
    email_account_id: Column[UUID | None] = None

    # Domain specific tracking info
    association_role: Column[DomainCRMAssociationRole | None] = None

    # Audit tracking
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
