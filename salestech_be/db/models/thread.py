import uuid
from datetime import datetime
from uuid import UUID

import pytz
from pydantic import ConfigDict, EmailStr

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.email.type.email import EmailHydratedParticipant, EmailTag
from salestech_be.db.models.core.base import (
    Column,
    JsonColumn,
    TableBoundedModel,
    TableModel,
)
from salestech_be.db.models.core.constants import TableName
from salestech_be.db.models.email_account import EmailAccount, EmailProvider
from salestech_be.integrations.nylas.model import NylasThread
from salestech_be.util.time import zoned_utc_now


class Thread(TableModel):
    """Thread table model."""

    table_name = TableName.thread
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    email_account_id: Column[UUID]
    organization_id: Column[UUID]
    provider: Column[EmailProvider | None] = None
    provider_id: Column[str | None] = None
    subject: Column[str]
    snippet: Column[str]
    unread: Column[bool | None] = None
    starred: Column[bool | None] = None
    has_attachments: Column[bool | None] = None
    has_drafts: Column[bool | None] = None
    folders: Column[list[str] | None] = None
    earliest_message_date: Column[datetime | None] = None
    latest_message_sent_date: Column[datetime | None] = None
    latest_message_received_date: Column[datetime | None] = None
    participants: JsonColumn[list[EmailHydratedParticipant]]
    tags: Column[list[EmailTag] | None] = None
    created_at: Column[datetime]
    updated_at: Column[datetime]
    deleted_at: Column[datetime | None] = None
    cancelled_at: Column[datetime | None] = None

    @classmethod
    def from_nylas_thread(
        cls,
        email_account_id: UUID,
        organization_id: UUID,
        nylas_thread: NylasThread,
        email_person_map: dict[EmailStr, EmailAccount | ContactV2 | None],
        contact_account_map: dict[UUID, UUID | None] | None,
    ) -> "Thread":
        return Thread(
            id=uuid.uuid4(),  # fake id
            email_account_id=email_account_id,
            organization_id=organization_id,
            provider=EmailProvider.NYLAS,
            provider_id=nylas_thread.id,
            subject=nylas_thread.subject or "",
            snippet=nylas_thread.snippet or "",
            folders=nylas_thread.folders,
            unread=nylas_thread.unread,
            starred=nylas_thread.starred,
            has_attachments=nylas_thread.has_attachments,
            has_drafts=nylas_thread.has_drafts,
            earliest_message_date=datetime.fromtimestamp(
                nylas_thread.earliest_message_date, pytz.UTC
            )
            if nylas_thread.earliest_message_date
            else None,
            latest_message_sent_date=datetime.fromtimestamp(
                nylas_thread.latest_message_sent_date, pytz.UTC
            )
            if nylas_thread.latest_message_sent_date
            else None,
            latest_message_received_date=datetime.fromtimestamp(
                nylas_thread.latest_message_received_date, pytz.UTC
            )
            if nylas_thread.latest_message_received_date
            else None,
            participants=[
                EmailHydratedParticipant.from_nylas_email_name_and_person(
                    nylas_email_name=participant,
                    person=email_person_map.get(participant.email),
                    contact_account_map=contact_account_map,
                )
                for participant in nylas_thread.participants
            ]
            if nylas_thread.participants
            else [],
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            deleted_at=None,
        )

    @property
    def participant_emails(self) -> list[EmailStr]:
        return [participant.email for participant in self.participants]


class ThreadUpdate(TableBoundedModel[Thread]):
    model_config = ConfigDict(frozen=False)

    provider: UnsetAware[EmailProvider | None] = UNSET
    provider_id: UnsetAware[str | None] = UNSET
    subject: UnsetAware[str] = UNSET
    snippet: UnsetAware[str] = UNSET
    unread: UnsetAware[bool | None] = UNSET
    starred: UnsetAware[bool | None] = UNSET
    has_attachments: UnsetAware[bool | None] = UNSET
    has_drafts: UnsetAware[bool | None] = UNSET
    folders: UnsetAware[list[str] | None] = UNSET
    earliest_message_date: UnsetAware[datetime | None] = UNSET
    latest_message_sent_date: UnsetAware[datetime | None] = UNSET
    latest_message_received_date: UnsetAware[datetime | None] = UNSET
    participants: UnsetAware[list[EmailHydratedParticipant]] = UNSET
    deleted_at: UnsetAware[datetime | None] = UNSET
    cancelled_at: UnsetAware[datetime | None] = UNSET

    updated_at: UnsetAware[datetime] = UNSET
