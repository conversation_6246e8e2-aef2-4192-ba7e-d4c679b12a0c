from datetime import datetime
from uuid import UUID

from salestech_be.db.models.core.base import (
    Column,
    JsonColumn,
    TableModel,
)
from salestech_be.db.models.core.constants import TableName
from salestech_be.integrations.pdl.model import (
    PeopleDataLabsSearchCompanyRequest,
    PeopleDataLabsSearchCompanyResponse,
)

type PeopleDataLabsSearchCompanyResponseJson = PeopleDataLabsSearchCompanyResponse


class PDLCache(TableModel):
    table_name = TableName.pdl_cache
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    created_at: Column[datetime]
    deleted_at: Column[datetime | None] = None
    operation_name: Column[str]
    query_hash: Column[str]
    request: JsonColumn[PeopleDataLabsSearchCompanyRequest]
    response: JsonColumn[PeopleDataLabsSearchCompanyResponse]
    expires_at: Column[datetime]
