from enum import StrEnum
from typing import Annotated, Any, Literal
from uuid import UUID, uuid4

from pydantic import BaseModel, Field

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.db.models.core.base import (
    Column,
    JsonColumn,
    TableBoundedModel,
    TableModel,
)
from salestech_be.db.models.core.constants import TableName
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now


class Chat(TableModel):
    table_name = TableName.chat
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    organization_id: Column[UUID]
    title: Column[str]
    owner_user_id: Column[UUID]

    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime]
    updated_by_user_id: Column[UUID]
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class ChatUpdate(TableBoundedModel[Chat]):
    title: UnsetAware[str] = UNSET
    updated_at: UnsetAware[ZoneRequiredDateTime] = UNSET
    updated_by_user_id: UnsetAware[UUID] = UNSET
    deleted_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    deleted_by_user_id: UnsetAware[UUID | None] = UNSET


class ChatMessageSender(StrEnum):
    USER = "USER"
    ASSISTANT = "ASSISTANT"


class ContentBlockType(StrEnum):
    TEXT = "TEXT"
    TOOL_CALL = "TOOL_CALL"


class BaseContentBlock(BaseModel):
    type: ContentBlockType


class TextContentBlock(BaseContentBlock):
    type: Literal[ContentBlockType.TEXT] = ContentBlockType.TEXT
    text: str


class ToolCallContentBlock(BaseContentBlock):  # type: ignore[explicit-any]
    type: Literal[ContentBlockType.TOOL_CALL] = ContentBlockType.TOOL_CALL
    tool_name: str
    tool_id: str
    args: dict[str, Any]  # type: ignore[explicit-any]
    result: object | None


MessageContentBlock = Annotated[
    TextContentBlock | ToolCallContentBlock, Field(discriminator="type")
]


class ChatMessage(TableModel):
    table_name = TableName.chat_message
    ordered_primary_keys = ("id",)

    id: Column[UUID] = Field(default_factory=uuid4)
    chat_id: Column[UUID]
    content: JsonColumn[list[MessageContentBlock]]
    sender: Column[ChatMessageSender]

    created_at: Column[ZoneRequiredDateTime] = Field(default_factory=zoned_utc_now)
