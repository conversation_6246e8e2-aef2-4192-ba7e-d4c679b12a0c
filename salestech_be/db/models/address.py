from typing import Self
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, model_validator

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.db.models.core.base import (
    Column,
    ColumnParity,
    TableBoundedModel,
    TableModel,
)
from salestech_be.db.models.core.constants import TableName
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now


class Address(TableModel):
    """Address table model."""

    table_name = TableName.address
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    street_one: Column[str | None] = None
    street_two: Column[str | None] = None
    zip_code: Column[str | None] = None
    city: Column[str | None] = None
    state: Column[str | None] = None
    country: Column[str | None] = None
    description: Column[str | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    organization_id: Column[UUID]

    def concatenate_street(self) -> str:
        """Concatenate street_one and street_two."""
        if self.street_one is None and self.street_two is None:
            return ""
        elif self.street_one is not None:
            return self.street_one
        elif self.street_two is not None:
            return self.street_two
        else:
            return f"{self.street_one} {self.street_two}"


class AddressUpdate(TableBoundedModel[Address]):
    column_parity = ColumnParity(
        excluded_columns=frozenset(
            {
                "created_at",
                "created_by_user_id",
                "id",
                "organization_id",
            }
        )
    )
    street_one: UnsetAware[str | None] = UNSET
    street_two: UnsetAware[str | None] = UNSET
    zip_code: UnsetAware[str | None] = UNSET
    city: UnsetAware[str | None] = UNSET
    state: UnsetAware[str | None] = UNSET
    country: UnsetAware[str | None] = UNSET
    description: UnsetAware[str | None] = UNSET
    updated_at: ZoneRequiredDateTime = Field(default_factory=zoned_utc_now)
    updated_by_user_id: UnsetAware[UUID | None] = UNSET

    def to_db_address_update(self, user_id: UUID) -> Self:
        return self.model_copy(
            update={"updated_by_user_id": user_id, "updated_at": zoned_utc_now()}
        )


class AddressNoFieldDefinedError(ValueError):
    """
    Raised when every address field is None.
    At least one address field must be provided.
    """


class AddressCreateRequest(BaseModel):
    street_one: str | None = None
    street_two: str | None = None
    zip_code: str | None = None
    city: str | None = None
    state: str | None = None
    country: str | None = None
    description: str | None = None
    created_by_user_id: UUID
    organization_id: UUID

    @model_validator(mode="after")
    def validate_at_least_one_field(self) -> Self:
        """Ensure at least one address field is not None."""
        address_fields = [
            self.street_one,
            self.street_two,
            self.zip_code,
            self.city,
            self.state,
            self.country,
        ]

        if all(field is None for field in address_fields):
            raise AddressNoFieldDefinedError(
                "At least one address field must be provided"
            )

        return self

    def to_db_insert(self, created_at: ZoneRequiredDateTime) -> Address:
        return Address(
            id=uuid4(),
            street_one=self.street_one,
            street_two=self.street_two,
            zip_code=self.zip_code,
            city=self.city,
            state=self.state,
            country=self.country,
            description=self.description,
            created_at=created_at,
            created_by_user_id=self.created_by_user_id,
            updated_at=created_at,
            updated_by_user_id=self.created_by_user_id,
            organization_id=self.organization_id,
        )

    def to_db_update(self, updated_at: ZoneRequiredDateTime) -> AddressUpdate:
        return AddressUpdate(
            street_one=self.street_one,
            street_two=self.street_two,
            zip_code=self.zip_code,
            city=self.city,
            state=self.state,
            country=self.country,
            description=self.description,
            updated_at=updated_at,
            updated_by_user_id=self.created_by_user_id,
        )
