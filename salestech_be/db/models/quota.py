from enum import StrEnum
from uuid import UUID

from pydantic import BaseModel

from salestech_be.db.models.core.base import Column, TableModel
from salestech_be.db.models.core.constants import TableName
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class QuotaPeriod(StrEnum):
    HOURLY = "HOURLY"
    DAILY = "DAILY"
    MONTHLY = "MONTHLY"
    ANNUAL = "ANNUAL"
    LIFETIME = "LIFETIME"  # used for when we want to limit the total usage of a resource over the lifetime of the organization.


class QuotaConsumingResource(StrEnum):
    EMAIL = "EMAIL"
    PROSPECTING_EMAIL_ENRICHMENT = "PROSPECTING_EMAIL_ENRICHMENT"
    PROSPECTING_PHONE_NUMBER_ENRICHMENT = "PROSPECTING_PHONE_NUMBER_ENRICHMENT"
    PROSPECTING_ENRICHMENT = "PROSPECTING_ENRICHMENT"
    PROSPECTING_SEARCH = "PROSPECTING_SEARCH"  # to be deprecated
    PROSPECTING_PEOPLE_SEARCH = "PROSPECTING_PEOPLE_SEARCH"
    PROSPECTING_COMPANY_SEARCH = "PROSPECTING_COMPANY_SEARCH"
    PROSPECTING_PEOPLE_ENRICHMENT = "PROSPECTING_PEOPLE_ENRICHMENT"
    DOMAIN = "DOMAIN"
    MAILBOX = "MAILBOX"
    VOICE_SECONDS = "VOICE_SECONDS"
    VOICE_PHONE_NUMBER_UPDATE = "VOICE_PHONE_NUMBER_UPDATE"
    TOTAL_PHONE_LINE = "TOTAL_PHONE_LINE"
    USERS = "USERS"
    PLAN_INCLUDED_COST_MAILBOX = "PLAN_INCLUDED_COST_MAILBOX"
    PLAN_INCLUDED_COST_DOMAIN = "PLAN_INCLUDED_COST_DOMAIN"


class QuotaConsumerEntityType(StrEnum):
    EMAIL_ACCOUNT = "EMAIL_ACCOUNT"
    ORGANIZATION = "ORGANIZATION"
    USER = "USER"


class QuotaSummaryRequestItem(BaseModel):
    resource: QuotaConsumingResource
    period: QuotaPeriod
    entity_type: QuotaConsumerEntityType


class QuotaPolicy(TableModel):
    table_name = TableName.quota_policy
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    quota_limit: Column[int]
    period: Column[QuotaPeriod]
    resource: Column[QuotaConsumingResource]
    entity_type: Column[QuotaConsumerEntityType]
    entity_id: Column[UUID]
    organization_id: Column[UUID]
    applied_sub_entity_types: Column[list[QuotaConsumerEntityType] | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class QuotaUsage(TableModel):
    table_name = TableName.quota_usage
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    resource: Column[QuotaConsumingResource]
    entity_id: Column[UUID]
    entity_type: Column[QuotaConsumerEntityType]
    usage: Column[int]
    hour_start: Column[ZoneRequiredDateTime]
    organization_id: Column[UUID]
    created_at: Column[ZoneRequiredDateTime]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None

    applied_sub_resource: Column[QuotaConsumingResource | None] = None
    created_by_user_id: Column[UUID | None] = None
