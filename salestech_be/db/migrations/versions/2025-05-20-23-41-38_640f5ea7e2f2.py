"""add custom cname col to outbound domain

Revision ID: 640f5ea7e2f2
Revises: d21c9aa8bc1b
Create Date: 2025-05-20 23:41:38.412798+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "640f5ea7e2f2"
down_revision: str | tuple[str, ...] | None = "d21c9aa8bc1b"
branch_labels: set[str] | str | None = None
depends_on: str | None = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE outbound_domain
        ADD COLUMN custom_cname_exists BOOLEAN DEFAULT FALSE;
        """
    )


def downgrade() -> None:
    pass
