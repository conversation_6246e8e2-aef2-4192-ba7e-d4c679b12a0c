"""add transaction_type to email_account and outbound_domain

Revision ID: 415d20cc5070
Revises: de9a9ab68c76
Create Date: 2025-05-19 21:35:42.742825+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "415d20cc5070"
down_revision: str | tuple[str, ...] | None = "de9a9ab68c76"
branch_labels: set[str] | str | None = None
depends_on: str | None = None


def upgrade() -> None:
    op.execute("ALTER TABLE email_account ADD COLUMN transaction_type TEXT NULL;")
    op.execute("ALTER TABLE outbound_domain ADD COLUMN transaction_type TEXT NULL;")


def downgrade() -> None:
    pass
