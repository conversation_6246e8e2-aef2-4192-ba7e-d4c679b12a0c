"""add sequence step variant columns to domain_crm_association

Revision ID: d21c9aa8bc1b
Revises: 415d20cc5070
Create Date: 2025-05-19 11:04:38.000000+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "d21c9aa8bc1b"
down_revision: str | tuple[str, ...] | None = "415d20cc5070"
branch_labels: set[str] | str | None = None
depends_on: str | None = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE domain_crm_association
        ADD COLUMN sequence_step_variant_id UUID,
        ADD COLUMN sequence_step_execution_id UUID;
        """
    )

    # Drop existing sequence index and create updated one with new columns
    op.execute(
        """
        DROP INDEX IF EXISTS idx_domain_crm_assoc_sequence;
        """
    )

    op.execute(
        """
        CREATE INDEX idx_domain_crm_assoc_sequence
        ON domain_crm_association(organization_id, sequence_id, sequence_step_id, sequence_enrollment_id, sequence_step_variant_id, sequence_step_execution_id)
        WHERE deleted_at is null and (sequence_id is not null or sequence_step_id is not null or sequence_enrollment_id is not null or sequence_step_variant_id is not null or sequence_step_execution_id is not null);
        """
    )


def downgrade() -> None:
    # Drop the updated index and recreate original version
    op.execute(
        """
        DROP INDEX IF EXISTS idx_domain_crm_assoc_sequence;
        """
    )

    op.execute(
        """
        CREATE INDEX idx_domain_crm_assoc_sequence
        ON domain_crm_association(organization_id, sequence_id, sequence_step_id, sequence_enrollment_id)
        WHERE deleted_at is null and (sequence_id is not null or sequence_step_id is not null or sequence_enrollment_id is not null);
        """
    )

    # Drop the added columns
    op.execute(
        """
        ALTER TABLE domain_crm_association
        DROP COLUMN sequence_step_variant_id,
        DROP COLUMN sequence_step_execution_id;
        """
    )
