"""Create user_phone_number table

Revision ID: 042ac5d4653a
Revises: 66390f6dd87e
Create Date: 2025-05-22 00:54:01.900957+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "042ac5d4653a"
down_revision: str | tuple[str, ...] | None = "66390f6dd87e"
branch_labels: set[str] | str | None = None
depends_on: str | None = None


def upgrade() -> None:
    op.execute(
        """
        create table user_phone_number
        (
            id                          uuid                     not null primary key,
            organization_id             uuid                     not null,
            user_id                     uuid                     not null,
            phone_number                text                     not null,
            extension                   text                     null,
            is_primary                  boolean                  not null,
            verified                    boolean                  not null default false,
            created_by_user_id          uuid                     not null,
            deleted_by_user_id          uuid                     null,
            created_at                  timestamp with time zone not null,
            deleted_at                  timestamp with time zone null,
            updated_at                  timestamp with time zone not null,
            updated_by_user_id          uuid                     not null
        )
        """
    )

    op.execute("""
        CREATE UNIQUE INDEX user_phone_number_unique ON public.user_phone_number
        (organization_id, phone_number) WHERE (deleted_at IS NULL);
    """)

    op.execute("""
        CREATE UNIQUE INDEX user_phone_number_user_primary_uniq_idx ON public.user_phone_number
        (organization_id, user_id) WHERE ((deleted_at IS NULL) AND is_primary);
    """)


def downgrade() -> None:
    op.execute("""
        DROP TABLE user_phone_number;
    """)
