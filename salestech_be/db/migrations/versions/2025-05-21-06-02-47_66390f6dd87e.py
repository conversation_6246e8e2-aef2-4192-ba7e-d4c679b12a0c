"""pdl_cache

Revision ID: 66390f6dd87e
Revises: 640f5ea7e2f2
Create Date: 2025-05-21 06:02:47.262043+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "66390f6dd87e"
down_revision: str | tuple[str, ...] | None = "640f5ea7e2f2"
branch_labels: set[str] | str | None = None
depends_on: str | None = None


def upgrade() -> None:
    op.execute(
        """
        CREATE TABLE pdl_cache (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            deleted_at TIMESTAMP WITH TIME ZONE,
            operation_name VARCHAR(255) NOT NULL,
            query_hash VARCHAR(64) NOT NULL,
            request JSONB NOT NULL,
            response JSONB NOT NULL,
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL
        );
        """
    )
    op.execute(
        """
        CREATE UNIQUE INDEX ix_pdl_cache_lookup
        ON pdl_cache (operation_name, query_hash)
        WHERE deleted_at IS NULL;
        """
    )


def downgrade() -> None:
    pass
