from uuid import UUID

from sqlalchemy import text
from sqlalchemy.exc import IntegrityError

from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.message import MessageStatus
from salestech_be.db.models.sequence import (
    EmailEventType,
    SequenceEnrollment,
    SequenceEnrollmentStatus,
    SequenceEnrollmentStepVariantAssociation,
    SequenceErrorCode,
    SequenceStatsBySequenceId,
    SequenceStatsBySequenceStepId,
    SequenceStatsBySequenceStepVariantId,
    SequenceStatus,
    SequenceStepExecution,
    SequenceStepExecutionStatus,
    SequenceStepType,
    SequenceStepV2,
    SequenceStepVariant,
    SequenceStepVariantStatus,
    SequenceV2,
    UpsertSequenceStep,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger("sequence.repository")


class SequenceRepository(GenericRepository):
    async def update_sequence_and_enrollments(
        self,
        sequence_id: UUID,
        organization_id: UUID,
        updated_by_user_id: UUID,
        updated_at: ZoneRequiredDateTime,
        new_sequence_status: SequenceStatus,
        new_enrollment_status: SequenceEnrollmentStatus,
    ) -> tuple[SequenceV2, list[SequenceEnrollment]]:
        async with self.engine.begin():
            updated_sequence = not_none(
                await self.update_by_tenanted_primary_key(
                    table_model=SequenceV2,
                    organization_id=organization_id,
                    primary_key_to_value={
                        "id": sequence_id,
                    },
                    column_to_update={
                        "status": new_sequence_status,
                        "updated_at": updated_at,
                        "updated_by_user_id": updated_by_user_id,
                    },
                )
            )
            updated_enrollments = await self._update_by_column_values(
                table_model=SequenceEnrollment,
                column_to_update={
                    "status": new_enrollment_status,
                    # "updated_at": updated_at,
                    # "updated_by_user_id": updated_by_user_id,
                },
                column_value_to_query={
                    "sequence_id": sequence_id,
                    "organization_id": organization_id,
                },
            )
            return updated_sequence, updated_enrollments

    async def transactional_create_sequence_resources_v2(
        self,
        sequence: SequenceV2,
        steps_with_variants: list[tuple[SequenceStepV2, list[SequenceStepVariant]]],
    ) -> SequenceV2:
        async with self.engine.begin():
            new_sequence = await self.insert(sequence)
            for step, variants in steps_with_variants:
                await self.insert(step)
                for variant in variants:
                    await self.insert(variant)
        return new_sequence

    async def find_blueprint_sequences_by_organization_id(
        self,
        organization_id: UUID,
    ) -> list[SequenceV2]:
        return await self._find_by_column_values(
            SequenceV2,
            organization_id=organization_id,
            is_blueprint=True,
        )

    async def archive_blueprint_sequence(
        self,
        organization_id: UUID,
        blueprint_id: UUID,
        step_ids: list[UUID],
    ) -> SequenceV2:
        now = zoned_utc_now()
        async with self.engine.begin():
            updated_sequence = not_none(
                await self.update_by_tenanted_primary_key(
                    table_model=SequenceV2,
                    organization_id=organization_id,
                    primary_key_to_value={"id": blueprint_id},
                    column_to_update={
                        "status": SequenceStatus.TERMINATED,
                        "updated_at": now,
                        "deleted_at": now,
                    },
                )
            )

            for step_id in step_ids:
                await self.update_by_tenanted_primary_key(
                    table_model=SequenceStepV2,
                    organization_id=organization_id,
                    primary_key_to_value={"id": step_id},
                    column_to_update={
                        "updated_at": now,
                        "deleted_at": now,
                    },
                )

                stmt = text(
                    """
                    UPDATE sequence_step_variant
                    SET status = :status,
                    updated_at = :now,
                    deleted_at = :now
                    WHERE sequence_step_id = :sequence_step_id
                    AND organization_id = :organization_id
                    AND deleted_at is null;
                    """
                ).bindparams(
                    status=SequenceStepVariantStatus.INACTIVE,
                    now=now,
                    sequence_step_id=step_id,
                    organization_id=organization_id,
                )
                await self.engine.execute(stmt)

        return updated_sequence

    async def find_sequences_by_ids_and_organization_id(
        self,
        sequence_ids: list[UUID],
        organization_id: UUID,
    ) -> list[SequenceV2]:
        return await self._find_by_column_values(
            SequenceV2,
            id=sequence_ids,
            organization_id=organization_id,
            is_blueprint=False,
        )

    async def get_sequence_by_organization_v2(
        self,
        organization_id: UUID,
    ) -> list[SequenceV2]:
        return await self._find_by_column_values(
            table_model=SequenceV2,
            organization_id=organization_id,
            is_blueprint=False,
        )

    async def get_sequence_stats(
        self,
        organization_id: UUID,
        sequence_ids: list[UUID],
    ) -> list[SequenceStatsBySequenceId]:
        if not sequence_ids:
            return []

        stmt = text(
            """
            SELECT
                sequence_id,
                scheduled_count,
                delivered_count,
                bounced_count,
                reply_count,
                opt_out_count,
                opened_count,
                link_clicked_count
            FROM
                sequence_stats
            WHERE
                sequence_id = ANY(:sequence_ids)
                AND organization_id = :organization_id
            """
        ).bindparams(organization_id=organization_id, sequence_ids=sequence_ids)

        rows = await self.engine.all(stmt)
        return [SequenceStatsBySequenceId.from_row(row) for row in rows]

    async def get_sequence_step_execution_by_global_message_id(
        self,
        global_message_id: UUID,
        organization_id: UUID,
    ) -> SequenceStepExecution | None:
        return await self._find_unique_by_column_values(
            table_model=SequenceStepExecution,
            global_message_id=global_message_id,
            organization_id=organization_id,
        )

    async def find_enrollments_by_sequence_id(
        self,
        sequence_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceEnrollment]:
        return await self._find_by_column_values(
            table_model=SequenceEnrollment,
            sequence_id=sequence_id,
            organization_id=organization_id,
        )


class SequenceStepRepository(GenericRepository):
    async def batch_upsert_sequence_step_in_transaction(
        self,
        upsert_steps: list[UpsertSequenceStep],
    ) -> list[SequenceStepV2]:
        async with self.engine.begin():
            updated_rows = []

            # Create first since the update step may depend on the created ones
            new_steps = [
                upsert_step for upsert_step in upsert_steps if upsert_step.is_new
            ]
            for new_step in new_steps:
                updated_rows.append(await self.insert(new_step.step))

            updated_steps = [
                upsert_step for upsert_step in upsert_steps if not upsert_step.is_new
            ]
            for updated_step in updated_steps:
                updated_rows.append(
                    not_none(
                        await self.update_instance(
                            instance=updated_step.step,
                        )
                    )
                )

            return updated_rows

    async def delete_and_update_sequence_step_in_transaction(
        self,
        user_id: UUID,
        organization_id: UUID,
        update_steps: list[SequenceStepV2],
        delete_variants: list[SequenceStepVariant],
    ) -> tuple[list[SequenceStepV2], list[SequenceStepVariant]]:
        now = zoned_utc_now()
        async with self.engine.begin():
            updated_steps = []
            for updated_step in update_steps:
                updated_steps.append(
                    not_none(await self.update_instance(instance=updated_step))
                )

            deleted_variants = []
            for delete_variant in delete_variants:
                deleted_variant = not_none(
                    await self.update_by_tenanted_primary_key(
                        table_model=SequenceStepVariant,
                        primary_key_to_value={"id": delete_variant.id},
                        organization_id=organization_id,
                        column_to_update={
                            "deleted_at": now,
                            "deleted_by_user_id": user_id,
                            "updated_at": now,
                            "updated_by_user_id": user_id,
                        },
                    )
                )
                await self.delete_variant_and_enrollment_association_by_variant_id(
                    sequence_variant_id=delete_variant.id,
                    organization_id=organization_id,
                )
                deleted_variants.append(deleted_variant)

        return updated_steps, deleted_variants

    async def list_sequence_steps_by_ids(
        self, step_ids: list[UUID], organization_id: UUID
    ) -> list[SequenceStepV2]:
        stmt = text(
            """
            select * from sequence_step_v2
            where id = any(:step_ids)
            and organization_id = :organization_id
            and deleted_at is null
            """
        ).bindparams(step_ids=step_ids, organization_id=organization_id)

        rows = await self.engine.all(stmt)
        return await SequenceStepV2.bulk_from_rows(rows=rows)

    async def list_active_sequence_steps_v2(
        self,
        sequence_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceStepV2]:
        return await self._find_by_column_values(
            table_model=SequenceStepV2,
            sequence_id=sequence_id,
            organization_id=organization_id,
            exclude_deleted_or_archived=True,
        )

    async def list_sequence_step_variants(
        self,
        sequence_step_id: UUID,
        organization_id: UUID,
        exclude_deleted: bool = True,
    ) -> list[SequenceStepVariant]:
        return await self._find_by_column_values(
            table_model=SequenceStepVariant,
            exclude_deleted_or_archived=exclude_deleted,
            sequence_step_id=sequence_step_id,
            organization_id=organization_id,
        )

    async def list_sequence_step_variants_by_step_ids(
        self,
        sequence_step_ids: list[UUID],
        organization_id: UUID,
    ) -> list[SequenceStepVariant]:
        return await self._find_by_column_values(
            table_model=SequenceStepVariant,
            exclude_deleted_or_archived=True,
            sequence_step_id=sequence_step_ids,
            organization_id=organization_id,
        )

    async def list_active_sequence_step_variants(
        self,
        sequence_step_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceStepVariant]:
        return await self._find_by_column_values(
            table_model=SequenceStepVariant,
            exclude_deleted_or_archived=True,
            sequence_step_id=sequence_step_id,
            organization_id=organization_id,
            status=SequenceStepVariantStatus.ACTIVE,
        )

    async def find_enrollment_step_variant_associations_by_step_id(
        self,
        sequence_step_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceEnrollmentStepVariantAssociation]:
        """Find all enrollment-step-variant associations for a given step ID.

        Args:
            sequence_step_id: The ID of the sequence step
            organization_id: The organization ID

        Returns:
            A list of active (non-deleted) associations for the step
        """
        return await self._find_by_column_values(
            table_model=SequenceEnrollmentStepVariantAssociation,
            exclude_deleted_or_archived=True,
            sequence_step_id=sequence_step_id,
            organization_id=organization_id,
        )

    async def insert_step_to_last(
        self, organization_id: UUID, last_step_id: UUID | None, step: SequenceStepV2
    ) -> None:
        async with self.engine.begin():
            await self.insert(step)
            if last_step_id:
                await self.update_by_tenanted_primary_key(
                    table_model=SequenceStepV2,
                    primary_key_to_value={
                        "id": last_step_id,
                    },
                    organization_id=organization_id,
                    column_to_update={
                        "next_step_id": step.id,
                    },
                )

    async def get_sequence_step_stats(
        self,
        sequence_step_ids: list[UUID],
        organization_id: UUID,
    ) -> list[SequenceStatsBySequenceStepId]:
        if not sequence_step_ids:
            return []

        stmt = text(
            """
            SELECT
                sequence_id,
                sequence_step_id,
                scheduled_count,
                delivered_count,
                bounced_count,
                reply_count,
                opt_out_count,
                opened_count,
                link_clicked_count
            FROM
                sequence_step_stats
            WHERE
                sequence_step_id = ANY(:sequence_step_ids)
                AND organization_id = :organization_id
            """
        ).bindparams(
            organization_id=organization_id, sequence_step_ids=sequence_step_ids
        )

        rows = await self.engine.all(stmt)
        return [SequenceStatsBySequenceStepId.from_row(row) for row in rows]

    async def get_sequence_step_variant_stats(
        self,
        sequence_step_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceStatsBySequenceStepVariantId]:
        if not sequence_step_id:
            return []

        stmt = text(
            """
            SELECT
                sequence_id,
                sequence_step_id,
                sequence_step_variant_id,
                scheduled_count,
                delivered_count,
                bounced_count,
                reply_count,
                opt_out_count,
                opened_count,
                link_clicked_count
            FROM
                sequence_step_variant_stats
            WHERE
                sequence_step_id = :sequence_step_id
                AND organization_id = :organization_id
            """
        ).bindparams(organization_id=organization_id, sequence_step_id=sequence_step_id)

        rows = await self.engine.all(stmt)
        return [SequenceStatsBySequenceStepVariantId.from_row(row) for row in rows]

    async def find_variant_by_enrollment_and_step(
        self,
        sequence_enrollment_id: UUID,
        sequence_step_id: UUID,
        organization_id: UUID,
    ) -> SequenceStepVariant | None:
        association = await self._find_unique_by_column_values(
            SequenceEnrollmentStepVariantAssociation,
            sequence_enrollment_id=sequence_enrollment_id,
            sequence_step_id=sequence_step_id,
            organization_id=organization_id,
        )
        if not association:
            return None
        step_variant_id = association.sequence_step_variant_id
        return await self.find_by_tenanted_primary_key(
            SequenceStepVariant,
            organization_id=organization_id,
            id=step_variant_id,
        )

    async def delete_variant_and_enrollment_association_by_variant_id(
        self,
        sequence_variant_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceEnrollmentStepVariantAssociation]:
        return await self._update_by_column_values(
            SequenceEnrollmentStepVariantAssociation,
            column_value_to_query={
                "sequence_step_variant_id": sequence_variant_id,
                "organization_id": organization_id,
            },
            column_to_update={
                "updated_at": zoned_utc_now(),
                "deleted_at": zoned_utc_now(),
            },
        )

    async def delete_variant_and_enrollment_association_by_enrollment_and_step(
        self,
        organization_id: UUID,
        sequence_enrollment_id: UUID,
        sequence_step_id: UUID,
    ) -> list[SequenceEnrollmentStepVariantAssociation]:
        return await self._update_by_column_values(
            SequenceEnrollmentStepVariantAssociation,
            column_value_to_query={
                "sequence_enrollment_id": sequence_enrollment_id,
                "sequence_step_id": sequence_step_id,
                "organization_id": organization_id,
            },
            column_to_update={
                "deleted_at": zoned_utc_now(),
                "updated_at": zoned_utc_now(),
            },
        )

    async def find_execution_by_step_id_and_enrollment_id(
        self,
        sequence_step_id: UUID,
        sequence_enrollment_id: UUID,
        organization_id: UUID,
    ) -> SequenceStepExecution | None:
        return await self._find_unique_by_column_values(
            SequenceStepExecution,
            sequence_step_id=sequence_step_id,
            sequence_enrollment_id=sequence_enrollment_id,
            organization_id=organization_id,
            exclude_deleted_or_archived=True,
        )

    async def insert_or_get_sequence_step_execution(
        self, sequence_step_execution: SequenceStepExecution
    ) -> SequenceStepExecution:
        try:
            step_execution = await self.insert(sequence_step_execution)
        except IntegrityError:
            existing_execution = await self.find_execution_by_step_id_and_enrollment_id(
                sequence_step_id=sequence_step_execution.sequence_step_id,
                sequence_enrollment_id=sequence_step_execution.sequence_enrollment_id,
                organization_id=sequence_step_execution.organization_id,
            )
            if existing_execution:
                step_execution = existing_execution
            else:
                logger.error(
                    "Failed to find existing execution after constraint violation.",
                    extra={
                        "step_id": sequence_step_execution.sequence_step_id,
                        "enrollment_id": sequence_step_execution.sequence_enrollment_id,
                        "organization_id": sequence_step_execution.organization_id,
                    },
                )
                raise
        return step_execution

    async def get_first_step_of_sequence_v2(
        self,
        sequence_id: UUID,
        organization_id: UUID,
    ) -> SequenceStepV2:
        return await self._find_unique_by_column_values_or_fail(
            SequenceStepV2,
            sequence_id=sequence_id,
            is_first_step=True,
            organization_id=organization_id,
        )

    async def get_next_step_of_sequence_v2_with_active_variants(
        self,
        sequence_id: UUID,
        sequence_step_id: UUID,
        organization_id: UUID,
    ) -> SequenceStepV2:
        # Check if the current step has at least one active variant
        active_variants = await self.list_active_sequence_step_variants(
            organization_id=organization_id,
            sequence_step_id=sequence_step_id,
        )

        if not active_variants:
            # Get the step to find its next_step_id
            step = await self.find_by_tenanted_primary_key(
                SequenceStepV2,
                organization_id=organization_id,
                id=sequence_step_id,
                exclude_deleted_or_archived=False,
            )

            if step and step.next_step_id:
                # Recursively try the next step
                return await self.get_next_step_of_sequence_v2_with_active_variants(
                    sequence_id=sequence_id,
                    sequence_step_id=step.next_step_id,
                    organization_id=organization_id,
                )

        # Either the current step has active variants or we're at the end of the sequence
        return not_none(
            await self.find_by_tenanted_primary_key(
                SequenceStepV2,
                id=sequence_step_id,
                organization_id=organization_id,
            )
        )


class SequenceStepVariantRepository(GenericRepository):
    async def list_sequence_step_variants_by_ids(
        self,
        organization_id: UUID,
        sequence_step_variant_ids: list[UUID],
    ) -> list[SequenceStepVariant]:
        return await self._find_by_column_values(
            SequenceStepVariant,
            organization_id=organization_id,
            id=sequence_step_variant_ids,
        )


class SequenceExecutionRepository(GenericRepository):
    async def find_sequence_step_executions_by_enrollment_id(
        self,
        organization_id: UUID,
        enrollment_id: UUID,
        sequence_id: UUID,
    ) -> list[SequenceStepExecution]:
        stmt = text(
            """
            SELECT * FROM sequence_step_execution
            WHERE sequence_enrollment_id = :enrollment_id
            AND organization_id = :organization_id
            AND sequence_id = :sequence_id
            AND global_message_id is not null
            and global_thread_id is not null
            and deleted_at is null
            order by created_at desc
            """
        ).bindparams(
            enrollment_id=enrollment_id,
            organization_id=organization_id,
            sequence_id=sequence_id,
        )
        rows = await self.engine.all(stmt)
        return await SequenceStepExecution.bulk_from_rows(rows=rows)

    async def list_sequence_step_executions_by_enrollment_ids(
        self,
        organization_id: UUID,
        enrollment_ids: list[UUID],
    ) -> list[SequenceStepExecution]:
        stmt = text(
            """
            SELECT * FROM sequence_step_execution
            WHERE sequence_enrollment_id = any(:enrollment_ids)
            AND organization_id = :organization_id
            AND deleted_at is null
            """
        ).bindparams(enrollment_ids=enrollment_ids, organization_id=organization_id)

        rows = await self.engine.all(stmt)
        return await SequenceStepExecution.bulk_from_rows(rows=rows)

    async def terminate_step_execution_status_by_step_id(
        self,
        step_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceStepExecution]:
        return await self._update_by_column_values(
            table_model=SequenceStepExecution,
            column_value_to_query={
                "sequence_step_id": step_id,
                "organization_id": organization_id,
                "status": SequenceStepExecutionStatus.QUEUED,
            },
            column_to_update={
                "status": SequenceStepExecutionStatus.TERMINATED,
                "updated_at": zoned_utc_now(),
            },
        )

    async def terminate_step_execution_status_by_variant_id(
        self,
        sequence_step_variant_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceStepExecution]:
        return await self._update_by_column_values(
            table_model=SequenceStepExecution,
            column_value_to_query={
                "sequence_step_variant_id": sequence_step_variant_id,
                "organization_id": organization_id,
                "status": SequenceStepExecutionStatus.QUEUED,
            },
            column_to_update={
                "status": SequenceStepExecutionStatus.TERMINATED,
                "updated_at": zoned_utc_now(),
            },
        )

    async def terminate_step_execution_by_step_id_and_enrollment_id(
        self,
        sequence_step_id: UUID,
        sequence_enrollment_id: UUID,
        organization_id: UUID,
    ) -> SequenceStepExecution | None:
        return await self._update_unique_by_column_values(
            table_model=SequenceStepExecution,
            column_value_to_query={
                "sequence_step_id": sequence_step_id,
                "sequence_enrollment_id": sequence_enrollment_id,
                "organization_id": organization_id,
                "status": SequenceStepExecutionStatus.QUEUED,
            },
            column_to_update={
                "status": SequenceStepExecutionStatus.TERMINATED,
                "updated_at": zoned_utc_now(),
            },
        )

    async def batch_replace_contact_id_in_sequence_step_execution(
        self,
        sequence_enrollment_id: UUID,
        original_contact_id: UUID,
        replaced_contact_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceStepExecution]:
        return await self._update_by_column_values(
            table_model=SequenceStepExecution,
            column_value_to_query={
                "sequence_enrollment_id": sequence_enrollment_id,
                "contact_id": original_contact_id,
                "organization_id": organization_id,
            },
            column_to_update={
                "contact_id": replaced_contact_id,
                "updated_at": zoned_utc_now(),
            },
        )

    async def update_failed_step_execution(
        self,
        organization_id: UUID,
        step_execution_id: UUID,
        enrollment_id: UUID,
        error_code: SequenceErrorCode,
        error_detail: str,
        execution_status: SequenceStepExecutionStatus
        | None = SequenceStepExecutionStatus.FAILED,
    ) -> tuple[SequenceStepExecution, SequenceEnrollment]:
        execution: SequenceStepExecution
        enrollment: SequenceEnrollment
        async with self.engine.begin():
            execution = not_none(
                await self.update_by_tenanted_primary_key(
                    SequenceStepExecution,
                    organization_id=organization_id,
                    primary_key_to_value={"id": step_execution_id},
                    column_to_update={
                        "is_retry": True,
                        "error_code": error_code,
                        "error_detail": error_detail,
                        "status": execution_status,
                        "executed_at": zoned_utc_now(),
                        "updated_at": zoned_utc_now(),
                    },
                )
            )
            enrollment = not_none(
                await self.update_by_tenanted_primary_key(
                    SequenceEnrollment,
                    organization_id=organization_id,
                    primary_key_to_value={"id": enrollment_id},
                    column_to_update={
                        "status": SequenceEnrollmentStatus.FAILED,
                    },
                )
            )
        return execution, enrollment

    async def list_sequence_step_executions_by_ids(
        self,
        organization_id: UUID,
        sequence_step_execution_ids: list[UUID],
    ) -> list[SequenceStepExecution]:
        return await self._find_by_column_values(
            table_model=SequenceStepExecution,
            id=sequence_step_execution_ids,
            organization_id=organization_id,
        )

    async def find_sequence_step_executions_by_ids_and_organization_id(
        self,
        sequence_step_execution_ids: list[UUID],
        organization_id: UUID,
        sequence_id: UUID,
    ) -> list[SequenceStepExecution]:
        return await self._find_by_column_values(
            table_model=SequenceStepExecution,
            id=sequence_step_execution_ids,
            organization_id=organization_id,
            sequence_id=sequence_id,
        )

    async def get_sequence_step_executions_by_organization_id(
        self,
        organization_id: UUID,
        sequence_id: UUID,
    ) -> list[SequenceStepExecution]:
        return await self._find_by_column_values(
            table_model=SequenceStepExecution,
            organization_id=organization_id,
            sequence_id=sequence_id,
        )

    async def get_sequence_step_execution_by_global_message_id(
        self,
        global_message_id: UUID,
    ) -> SequenceStepExecution | None:
        return await self._find_unique_by_column_values(
            table_model=SequenceStepExecution,
            global_message_id=global_message_id,
        )

    async def get_sequence_step_executions_by_sequence_enrollment_id(
        self,
        sequence_enrollment_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceStepExecution]:
        return await self._find_by_column_values(
            table_model=SequenceStepExecution,
            sequence_enrollment_id=sequence_enrollment_id,
            organization_id=organization_id,
        )

    async def get_global_message_ids_by_sequence_step_execution_id_and_email_event_type(
        self,
        organization_id: UUID,
        sequence_step_execution_ids: list[UUID],
        step_ids: list[UUID] | None = None,
        messages_statuses_include: list[MessageStatus] | None = None,
        messages_statuses_exclude: list[MessageStatus] | None = None,
        email_event_types_include: list[EmailEventType] | None = None,
    ) -> list[UUID]:
        """Get global message IDs associated with the provided sequence step execution IDs.

        Args:
            organization_id: The organization ID
            sequence_step_execution_ids: List of sequence_step_execution IDs to filter by
            email_event_type: Optional email event type to further filter results

        Returns:
            List of global message IDs associated with the sequence step executions
        """
        query = """
            SELECT DISTINCT sse.global_message_id
            FROM sequence_step_execution sse
            INNER JOIN global_message gm ON sse.global_message_id = gm.id
            INNER JOIN global_message_association gma ON gm.id = gma.global_message_id
            INNER JOIN message m ON gma.message_id = m.id
            WHERE sse.organization_id = :organization_id
            AND sse.id = ANY(:sequence_step_execution_ids)
            AND sse.global_message_id IS NOT NULL
            AND gm.deleted_at IS NULL
            AND gma.deleted_at IS NULL
            AND m.deleted_at IS NULL
            AND m.status != 'DRAFT'
        """

        if step_ids:
            query += """
            AND sse.sequence_step_id = ANY(:step_ids)
            """

        # Apply status exclude filter if present, this always applies
        if messages_statuses_exclude:
            query += """
            AND m.status != ANY(:messages_statuses_exclude)
            """

        # Use OR logic between message status and email event type filters
        if messages_statuses_include and email_event_types_include:
            query += """
            AND (
                m.status = ANY(:messages_statuses_include)
                OR
                EXISTS (
                    SELECT 1 FROM email_event ee
                    WHERE ee.global_message_id = sse.global_message_id
                    AND ee.type = ANY(:email_event_types_include)
                    AND ee.organization_id = :organization_id
                )
            )
            """
        elif messages_statuses_include:
            query += """
            AND m.status = ANY(:messages_statuses_include)
            """
        elif email_event_types_include:
            query += """
            AND EXISTS (
                SELECT 1 FROM email_event ee
                WHERE ee.global_message_id = sse.global_message_id
                AND ee.type = ANY(:email_event_types_include)
                AND ee.organization_id = :organization_id
            )
            """

        bind_params = {
            "organization_id": organization_id,
            "sequence_step_execution_ids": sequence_step_execution_ids,
        }

        if step_ids:
            bind_params["step_ids"] = step_ids

        if messages_statuses_include:
            bind_params["messages_statuses_include"] = messages_statuses_include

        if messages_statuses_exclude:
            bind_params["messages_statuses_exclude"] = messages_statuses_exclude

        if email_event_types_include:
            bind_params["email_event_types_include"] = email_event_types_include

        stmt = text(query).bindparams(**bind_params)

        rows = await self.engine.all(stmt)
        return [row[0] for row in rows] if rows else []

    async def find_completed_email_step_executions_by_enrollment_id(
        self,
        organization_id: UUID,
        enrollment_id: UUID,
    ) -> list[SequenceStepExecution]:
        """
        Find all completed email step executions for a specific enrollment.
        These are steps where emails have already been sent.
        """

        stmt = text(
            """
            SELECT * FROM sequence_step_execution as sse
            JOIN sequence_step_v2 as ss ON sse.sequence_step_id = ss.id
            WHERE sse.organization_id = :organization_id
            AND sse.sequence_enrollment_id = :enrollment_id
            AND ss.type IN (:step_types)
            AND sse.status IN (:statuses)
            AND sse.deleted_at IS NULL
            """
        ).bindparams(
            organization_id=organization_id,
            enrollment_id=enrollment_id,
            step_types=[SequenceStepType.AUTO_EMAIL, SequenceStepType.MANUAL_EMAIL],
            statuses=[
                SequenceStepExecutionStatus.SENT,
                SequenceStepExecutionStatus.TASK_COMPLETED,
            ],
        )

        rows = await self.engine.all(stmt)
        return await SequenceStepExecution.bulk_from_rows(rows=rows)

    async def find_active_email_step_executions_by_enrollment_id(
        self,
        organization_id: UUID,
        enrollment_id: UUID,
    ) -> list[SequenceStepExecution]:
        """
        Find all active email step executions for a specific enrollment.
        These are steps where emails are scheduled but not yet sent.
        """

        stmt = text(
            """
            SELECT * FROM sequence_step_execution as sse
            JOIN sequence_step_v2 as ss ON sse.sequence_step_id = ss.id
            WHERE sse.organization_id = :organization_id
            AND sse.sequence_enrollment_id = :enrollment_id
            AND ss.type IN (:step_types)
            AND sse.status IN (:statuses)
            AND sse.deleted_at IS NULL
            """
        ).bindparams(
            organization_id=organization_id,
            enrollment_id=enrollment_id,
            step_types=[SequenceStepType.AUTO_EMAIL, SequenceStepType.MANUAL_EMAIL],
            statuses=[SequenceStepExecutionStatus.QUEUED],
        )

        rows = await self.engine.all(stmt)
        return await SequenceStepExecution.bulk_from_rows(rows=rows)
