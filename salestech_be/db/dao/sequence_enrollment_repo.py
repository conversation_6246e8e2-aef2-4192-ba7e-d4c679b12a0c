from collections import defaultdict
from typing import Annotated
from uuid import UUID

from fastapi import Depends
from sqlalchemy import text

from salestech_be.common.lifespan import get_db_engine
from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dao.sequence_repository import SequenceExecutionRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.global_thread import GlobalMessageAssociation
from salestech_be.db.models.message import Message, MessageUpdate
from salestech_be.db.models.sequence import (
    NumWarningsAndFailuresByEnrollmentRunId,
    SequenceEnrollment,
    SequenceEnrollmentContact,
    SequenceEnrollmentContactStatus,
    SequenceEnrollmentExitReasonCode,
    SequenceEnrollmentRun,
    SequenceEnrollmentStatus,
    SequenceErrorCode,
    SequenceStepExecution,
    SequenceStepExecutionStatus,
)
from salestech_be.db.models.sequence import SequenceEnrollment as DbSequenceEnrollment
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none


class SequenceEnrollmentRepository(GenericRepository):
    def __init__(self, *, engine: Annotated[DatabaseEngine, Depends(get_db_engine)]):
        super().__init__(engine=engine)
        # Initialize other repositories with the same engine
        self.message_repository = MessageRepository(engine=engine)
        self.thread_repository = ThreadRepository(engine=engine)
        self.sequence_execution_repository = SequenceExecutionRepository(engine=engine)

    async def find_all_sequence_enrollments_by_sequence_id_and_organization_id(
        self,
        sequence_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceEnrollment]:
        return await self._find_by_column_values(
            table_model=SequenceEnrollment,
            organization_id=organization_id,
            sequence_id=sequence_id,
        )

    async def find_all_sequence_enrollments_by_sequence_id_and_status(
        self,
        sequence_id: UUID,
        organization_id: UUID,
        status: SequenceEnrollmentStatus,
    ) -> list[SequenceEnrollment]:
        return await self._find_by_column_values(
            table_model=SequenceEnrollment,
            organization_id=organization_id,
            sequence_id=sequence_id,
            status=status,
        )

    async def find_all_sequence_enrollments_by_sequence_id_and_status_list(
        self,
        sequence_id: UUID,
        organization_id: UUID,
        status_list: list[SequenceEnrollmentStatus],
    ) -> list[SequenceEnrollment]:
        return await self._find_by_column_values(
            table_model=SequenceEnrollment,
            organization_id=organization_id,
            sequence_id=sequence_id,
            status=status_list,
        )

    async def find_count_of_sequence_enrollments_by_sequence_id_and_status(
        self,
        sequence_id: UUID,
        organization_id: UUID,
        status: SequenceEnrollmentStatus,
    ) -> int:
        sql_stmt = text(
            """
            SELECT count(*) as enrollment_count
            FROM sequence_enrollment
            WHERE sequence_id = :sequence_id
            AND organization_id = :organization_id
            AND status = :status
            AND deleted_at IS NULL
            """
        ).bindparams(
            sequence_id=sequence_id,
            organization_id=organization_id,
            status=status,
        )
        rows = await self.engine.all(sql_stmt)
        return int(rows[0].enrollment_count)

    async def find_sequence_enrollments_by_email_account_id_and_status(
        self,
        email_account_id: UUID,
        organization_id: UUID,
        status_list: list[SequenceEnrollmentStatus],
    ) -> list[SequenceEnrollment]:
        stmt = text(
            """
            SELECT * FROM sequence_enrollment
            WHERE organization_id = :organization_id
            AND email_account_id = :email_account_id
            AND status = ANY(:status)
            AND deleted_at IS NULL
            """
        ).bindparams(
            email_account_id=email_account_id,
            organization_id=organization_id,
            status=status_list,
        )
        rows = await self.engine.all(stmt)
        return await SequenceEnrollment.bulk_from_rows(rows=rows)

    async def find_sequence_enrollments_by_ids(
        self,
        sequence_enrollment_ids: set[UUID],
        organization_id: UUID,
    ) -> list[SequenceEnrollment]:
        return await self._find_by_column_values(
            table_model=SequenceEnrollment,
            organization_id=organization_id,
            id=sequence_enrollment_ids,
        )

    async def find_sequence_enrollments_by_sequence_id_and_organization_id(
        self,
        sequence_id: UUID,
        organization_id: UUID,
        only_include_sequence_enrollment_ids: set[UUID] | None = None,
    ) -> list[SequenceEnrollment]:
        if not only_include_sequence_enrollment_ids:
            return await self.find_all_sequence_enrollments_by_sequence_id_and_organization_id(
                sequence_id=sequence_id,
                organization_id=organization_id,
            )

        stmt = text(
            """
            SELECT * FROM sequence_enrollment
            WHERE id = ANY(:sequence_enrollment_ids)
            AND sequence_id = :sequence_id
            AND organization_id = :organization_id
            AND deleted_at IS NULL
            """
        ).bindparams(
            sequence_enrollment_ids=only_include_sequence_enrollment_ids,
            sequence_id=sequence_id,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        return await SequenceEnrollment.bulk_from_rows(rows=rows)

    async def find_enrollment_contacts_by_enrollment_run_id(
        self,
        enrollment_run_id: UUID,
        organization_id: UUID,
        only_include_contact_ids: set[UUID] | None = None,
    ) -> list[SequenceEnrollmentContact]:
        if only_include_contact_ids:
            return await self._find_by_column_values(
                table_model=SequenceEnrollmentContact,
                organization_id=organization_id,
                enrollment_run_id=enrollment_run_id,
                contact_id=only_include_contact_ids,
            )
        else:
            return await self._find_by_column_values(
                table_model=SequenceEnrollmentContact,
                organization_id=organization_id,
                enrollment_run_id=enrollment_run_id,
            )

    async def find_enrollment_contacts_by_enrollment_run_id_and_status(
        self,
        enrollment_run_id: UUID,
        organization_id: UUID,
        only_include_contact_ids: set[UUID] | None = None,
        status: SequenceEnrollmentContactStatus | None = None,
    ) -> list[SequenceEnrollmentContact]:
        if only_include_contact_ids:
            return await self._find_by_column_values(
                table_model=SequenceEnrollmentContact,
                organization_id=organization_id,
                enrollment_run_id=enrollment_run_id,
                contact_id=only_include_contact_ids,
                status=status,
            )
        else:
            return await self._find_by_column_values(
                table_model=SequenceEnrollmentContact,
                organization_id=organization_id,
                enrollment_run_id=enrollment_run_id,
                status=status,
            )

    async def find_sequence_enrollment_by_sequence_id_and_contact_id_and_organization_id(
        self,
        contact_id: UUID,
        sequence_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceEnrollment]:
        stmt = text(
            """
            SELECT * FROM sequence_enrollment
            WHERE sequence_id = :sequence_id
            AND contact_id = :contact_id
            AND organization_id = :organization_id
            AND deleted_at IS NULL
            """
        ).bindparams(
            sequence_id=sequence_id,
            contact_id=contact_id,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        return await SequenceEnrollment.bulk_from_rows(rows=rows)

    async def find_sequence_enrollments_by_contact_id(
        self,
        contact_id: UUID,
        organization_id: UUID,
        account_id: UUID | None = None,
        email: str | None = None,
        sequence_id: UUID | None = None,
    ) -> list[SequenceEnrollment]:
        where_filter_account_id = "AND account_id = :account_id" if account_id else ""
        where_filter_email = "AND email = :email" if email else ""
        where_filter_sequence_id = (
            "AND sequence_id = :sequence_id" if sequence_id else ""
        )

        stmt = text(
            f"""
            SELECT * FROM sequence_enrollment
            WHERE contact_id = :contact_id
            AND organization_id = :organization_id
            {where_filter_account_id}
            {where_filter_email}
            {where_filter_sequence_id}
            AND deleted_at IS NULL
            """  # noqa: S608
        ).bindparams(
            contact_id=contact_id,
            organization_id=organization_id,
        )

        if account_id:
            stmt = stmt.bindparams(account_id=account_id)

        if email:
            stmt = stmt.bindparams(email=email)

        if sequence_id:
            stmt = stmt.bindparams(sequence_id=sequence_id)

        rows = await self.engine.all(stmt)
        return await SequenceEnrollment.bulk_from_rows(rows=rows)

    async def find_sequence_enrollments_by_account_id(
        self,
        account_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceEnrollment]:
        return await self._find_by_column_values(
            table_model=SequenceEnrollment,
            organization_id=organization_id,
            account_id=account_id,
        )

    async def update_sequence_enrollment_with_replacement(
        self,
        sequence_enrollment_to_update: SequenceEnrollment,
        replacement_sequence_enrollment: SequenceEnrollment,
    ) -> tuple[SequenceEnrollment, SequenceEnrollment]:
        async with self.engine.begin():
            updated_sequence_enrollment = not_none(
                await self.update_instance(sequence_enrollment_to_update)
            )
            inserted_sequence_enrollment = await self.insert(
                replacement_sequence_enrollment
            )
            return updated_sequence_enrollment, inserted_sequence_enrollment

    async def bulk_update_sequence_enrollment_status(
        self,
        sequence_enrollment_ids: list[UUID],
        organization_id: UUID,
        status: str,
        exited_reason: SequenceEnrollmentExitReasonCode | None = None,
    ) -> list[SequenceEnrollment]:
        if exited_reason:
            stmt = text(
                """
                UPDATE sequence_enrollment
                SET status = :status, exited_reason = :exited_reason
                WHERE id = ANY(:sequence_enrollment_ids)
                AND organization_id = :organization_id
                AND deleted_at IS NULL
                RETURNING *
                """
            ).bindparams(
                sequence_enrollment_ids=sequence_enrollment_ids,
                organization_id=organization_id,
                status=status,
                exited_reason=exited_reason.value,
            )
        else:
            stmt = text(
                """
                UPDATE sequence_enrollment
                SET status = :status
                WHERE id = ANY(:sequence_enrollment_ids)
                AND organization_id = :organization_id
                AND deleted_at IS NULL
                RETURNING *
                """
            ).bindparams(
                sequence_enrollment_ids=sequence_enrollment_ids,
                organization_id=organization_id,
                status=status,
            )

        rows = await self.engine.all(stmt)
        return await SequenceEnrollment.bulk_from_rows(rows=rows)

    async def find_email_account_sequence_associations_by_email_account_ids(
        self,
        email_account_ids: list[UUID],
        organization_id: UUID,
    ) -> tuple[dict[UUID, set[UUID]], set[UUID]]:
        stmt = text(
            """
            SELECT DISTINCT sequence_id, email_account_id
            FROM sequence_enrollment
            WHERE email_account_id = ANY(:email_account_ids)
            AND organization_id = :organization_id
            AND deleted_at IS NULL
            """
        ).bindparams(
            email_account_ids=email_account_ids,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        email_account_id_to_sequence_ids: dict[UUID, set[UUID]] = defaultdict(set)
        all_sequence_ids: set[UUID] = set()
        for row in rows:
            email_account_id_to_sequence_ids[row.email_account_id].add(row.sequence_id)
            all_sequence_ids.add(row.sequence_id)
        return email_account_id_to_sequence_ids, all_sequence_ids

    async def find_outbound_domain_sequence_associations_by_outbound_domain_ids(
        self,
        outbound_domain_ids: list[UUID],
        organization_id: UUID,
    ) -> tuple[dict[UUID, set[UUID]], set[UUID]]:
        stmt = text(
            """
            SELECT DISTINCT se.sequence_id, ea.outbound_domain_id
            FROM sequence_enrollment se
            JOIN email_account ea ON se.email_account_id = ea.id
            WHERE ea.outbound_domain_id = ANY(:domain_ids)
            AND se.organization_id = :organization_id
            AND se.deleted_at IS NULL
            AND ea.deleted_at IS NULL
            AND ea.archived_at is NULL
            """
        ).bindparams(
            domain_ids=outbound_domain_ids,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        domain_id_to_sequence_ids: dict[UUID, set[UUID]] = defaultdict(set)
        all_sequence_ids: set[UUID] = set()
        for row in rows:
            domain_id_to_sequence_ids[row.outbound_domain_id].add(row.sequence_id)
            all_sequence_ids.add(row.sequence_id)
        return domain_id_to_sequence_ids, all_sequence_ids

    async def find_sequence_enrollments_by_contacts_for_sequence(
        self,
        contact_ids: set[UUID],
        sequence_id: UUID,
        organization_id: UUID,
    ) -> dict[UUID, SequenceEnrollment]:
        """
        Batch check which contacts are enrolled in a given sequence.
        Returns a mapping of contact_id to its sequence enrollment.
        """
        stmt = text(
            """
            SELECT * FROM sequence_enrollment
            WHERE contact_id = ANY(:contact_ids)
            AND sequence_id = :sequence_id
            AND organization_id = :organization_id
            AND deleted_at IS NULL
            """
        ).bindparams(
            contact_ids=list(contact_ids),
            sequence_id=sequence_id,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        enrollments = await SequenceEnrollment.bulk_from_rows(rows=rows)

        # result: {contact_id: SequenceEnrollment}
        result: dict[UUID, SequenceEnrollment] = {}
        for enrollment in enrollments:
            result[enrollment.contact_id] = enrollment
        return result

    async def find_sequence_enrollments_by_contacts_and_status(
        self,
        contact_ids: set[UUID],
        organization_id: UUID,
        status_list: list[SequenceEnrollmentStatus],
    ) -> dict[UUID, list[SequenceEnrollment]]:
        """
        Batch check which contacts are enrolled and active in any sequence.
        Returns a mapping of contact_id to its sequence enrollments.
        """
        stmt = text(
            """
            SELECT * FROM sequence_enrollment
            WHERE contact_id = ANY(:contact_ids)
            AND organization_id = :organization_id
            AND status = ANY(:status)
            AND deleted_at IS NULL
            """
        ).bindparams(
            contact_ids=list(contact_ids),
            organization_id=organization_id,
            status=status_list,
        )
        rows = await self.engine.all(stmt)
        enrollments = await SequenceEnrollment.bulk_from_rows(rows=rows)

        result: dict[UUID, list[SequenceEnrollment]] = {}
        for enrollment in enrollments:
            if not result.get(enrollment.contact_id):
                result[enrollment.contact_id] = []
            result[enrollment.contact_id].append(enrollment)
        return result

    async def find_sequence_enrollments_by_sequence_id_and_status(
        self,
        sequence_id: UUID,
        status: str,
        organization_id: UUID,
    ) -> list[SequenceEnrollment]:
        """
        Find all sequence enrollments for a specific sequence with a given status.

        Args:
            sequence_id: The ID of the sequence
            status: The enrollment status to filter by
            organization_id: The organization ID

        Returns:
            A list of matching SequenceEnrollment records
        """
        stmt = text(
            """
            SELECT * FROM sequence_enrollment
            WHERE sequence_id = :sequence_id
            AND status = :status
            AND organization_id = :organization_id
            AND deleted_at IS NULL
            """
        ).bindparams(
            sequence_id=sequence_id,
            status=status,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        return await SequenceEnrollment.bulk_from_rows(rows=rows)

    async def update_enrollment_current_step_id_by_ids(
        self,
        enrollment_ids: list[UUID],
        new_step_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceEnrollment]:
        """
        Update the current_step_id for multiple enrollments by their IDs.

        Args:
            enrollment_ids: List of enrollment IDs to update
            new_step_id: The new step ID to set
            organization_id: The organization ID

        Returns:
            A list of the updated SequenceEnrollment records
        """
        if not enrollment_ids:
            return []

        now = zoned_utc_now()
        updated_enrollments = []
        for enrollment_id in enrollment_ids:
            updated_enrollment = await self.update_by_tenanted_primary_key(
                table_model=SequenceEnrollment,
                primary_key_to_value={"id": enrollment_id},
                organization_id=organization_id,
                column_to_update={
                    "current_step_id": new_step_id,
                    "updated_at": now,
                },
            )
            if updated_enrollment:
                updated_enrollments.append(updated_enrollment)

        return updated_enrollments

    async def list_sequence_enrollment_runs_by_sequence_id(
        self, organization_id: UUID, sequence_id: UUID
    ) -> list[SequenceEnrollmentRun]:
        return await self._find_by_column_values(
            table_model=SequenceEnrollmentRun,
            organization_id=organization_id,
            sequence_id=sequence_id,
        )

    async def count_num_warnings_and_failures_by_enrollment_run_id_for_sequence_id(
        self,
        organization_id: UUID,
        sequence_id: UUID,
    ) -> dict[UUID, tuple[int, int, int]]:
        stmt = text(
            """
            SELECT
                ser.id as enrollment_run_id,
                COUNT(CASE WHEN sec.status = 'ENROLLED' THEN 1 END) as num_successes,
                COUNT(CASE WHEN sec.status = 'WARNING' THEN 1 END) as num_warnings,
                COUNT(CASE WHEN sec.status = 'FAILED' THEN 1 END) as num_failures
            FROM sequence_enrollment_run ser
            LEFT JOIN sequence_enrollment_contact sec
                ON ser.id = sec.enrollment_run_id
            WHERE ser.sequence_id = :sequence_id
            AND ser.organization_id = :organization_id
            GROUP BY ser.id
            """
        ).bindparams(
            organization_id=organization_id,
            sequence_id=sequence_id,
        )
        rows = await self.engine.all(stmt)
        num_warnings_and_failures_by_enrollment_run_id = await (
            NumWarningsAndFailuresByEnrollmentRunId.bulk_from_rows(rows=rows)
        )
        return {
            x.enrollment_run_id: (x.num_successes, x.num_warnings, x.num_failures)
            for x in num_warnings_and_failures_by_enrollment_run_id
        }

    async def find_all_sequence_enrollments_by_ids(
        self,
        enrollment_ids: list[UUID],
        organization_id: UUID,
    ) -> list[SequenceEnrollment]:
        """
        Find all sequence enrollments by their IDs.

        Args:
            enrollment_ids: List of enrollment IDs to find
            organization_id: The organization ID

        Returns:
            A list of matching SequenceEnrollment records
        """
        if not enrollment_ids:
            return []

        stmt = text(
            """
            SELECT * FROM sequence_enrollment
            WHERE id = ANY(:enrollment_ids)
            AND organization_id = :organization_id
            AND deleted_at IS NULL
            """
        ).bindparams(
            enrollment_ids=enrollment_ids,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        return await SequenceEnrollment.bulk_from_rows(rows=rows)

    async def update_failed_enrollment(
        self,
        organization_id: UUID,
        enrollment_id: UUID,
        error_code: SequenceErrorCode,
        error_detail: str,
    ) -> tuple[SequenceStepExecution | None, SequenceEnrollment | None]:
        execution: SequenceStepExecution | None = None
        enrollment: SequenceEnrollment | None = None
        async with self.engine.begin():
            enrollment = await self.update_by_tenanted_primary_key(
                SequenceEnrollment,
                organization_id=organization_id,
                primary_key_to_value={"id": enrollment_id},
                column_to_update={
                    "status": SequenceEnrollmentStatus.FAILED,
                },
            )
            if enrollment and enrollment.current_step_id:
                executions = await self._update_by_column_values(
                    table_model=SequenceStepExecution,
                    column_value_to_query={
                        "sequence_enrollment_id": enrollment.id,
                        "sequence_step_id": enrollment.current_step_id,
                        "organization_id": organization_id,
                    },
                    column_to_update={
                        "is_retry": True,
                        "error_code": error_code,
                        "error_detail": error_detail,
                        "status": SequenceStepExecutionStatus.FAILED,
                        "executed_at": zoned_utc_now(),
                        "updated_at": zoned_utc_now(),
                    },
                )
                execution = executions[0] if executions else None

        return execution, enrollment

    async def patch_enrollment_email(
        self,
        organization_id: UUID,
        enrollment_id: UUID,
        email: str,
        scheduled_executions: list[SequenceStepExecution],
        message_associations: dict[UUID, list[GlobalMessageAssociation]],
        messages: dict[UUID, Message],
    ) -> None:
        async with self.engine.begin():
            # Update the enrollment email
            await self.update_by_tenanted_primary_key(
                DbSequenceEnrollment,
                organization_id=organization_id,
                primary_key_to_value={"id": enrollment_id},
                column_to_update={"email": email},
            )

            # Update each message associated with the global messages
            for execution in scheduled_executions:
                if not execution.global_message_id:
                    continue

                associations = message_associations.get(execution.global_message_id, [])

                for association in associations:
                    message = messages.get(association.message_id)

                    if not message or not message.send_from:
                        continue

                    # Update the message's recipient
                    await self.message_repository.update_by_tenanted_primary_key(
                        Message,
                        organization_id=organization_id,
                        primary_key_to_value={"id": association.message_id},
                        column_to_update=MessageUpdate(
                            send_to=[
                                EmailHydratedParticipant(
                                    email=email,
                                    account_id=message.send_to[0].account_id,
                                    contact_id=message.send_to[0].contact_id,
                                    name=message.send_to[0].name,
                                )
                            ],
                        ),
                    )
