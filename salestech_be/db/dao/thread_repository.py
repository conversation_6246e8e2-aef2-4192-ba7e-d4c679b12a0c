import uuid
from collections import defaultdict
from uuid import UUID, uuid4

from sqlalchemy import bindparam, text

from salestech_be.common.core_crm.sales_action import StandardSalesActionType
from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import ErrorDetails, ServiceError
from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.dto.email_dto import (
    EmailDto,
    GlobalEmailDto,
    GlobalMessageEventSummaryDTO,
    MessageDto,
    ShortDBThread,
)
from salestech_be.db.models.global_thread import (
    GlobalMessage,
    GlobalMessageAssociation,
    GlobalMessageAssociationUpdate,
    GlobalMessageUpdate,
    GlobalThread,
    GlobalThreadMessagesAssociation,
    GlobalThreadMessagesAssociationUpdate,
    GlobalThreadUpdate,
)
from salestech_be.db.models.message import (
    EmailProvider,
    Message,
    MessageStatus,
    MessageUpdate,
    MessageWithGlobalMessageId,
)
from salestech_be.db.models.thread import Thread, ThreadUpdate
from salestech_be.integrations.nylas.model import MessageErrorInfo
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none


class ThreadRepository(GenericRepository):
    async def upsert_threads(self, threads: list[Thread]) -> list[Thread]:
        upsert_thread_list = []
        async with self.engine.begin():
            for thread in threads:
                upsert_thread = await self.upsert_unique_target_columns(
                    thread,
                    on_conflict_target_columns=[
                        "provider",
                        "provider_id",
                        "email_account_id",
                    ],
                    exclude_columns_from_update=[
                        "id",
                        "organization_id",
                        "created_at",
                    ],
                )
                upsert_thread_list.append(upsert_thread)
        return upsert_thread_list

    async def insert_thread_and_message(
        self, thread: Thread, message: Message
    ) -> tuple[EmailDto, Message]:
        async with self.engine.begin():
            inserted_thread = await self.insert(
                thread,
            )
            inserted_message = await self.insert(message)
        return EmailDto(
            thread=inserted_thread,
            message_dtos=[MessageDto(message=inserted_message, attachments=[])],
        ), inserted_message

    async def update_thread_and_message(
        self,
        thread_id: UUID,
        message_id: UUID,
        thread_update_fields: ThreadUpdate,
        message_update_fields: MessageUpdate,
    ) -> EmailDto:
        async with self.engine.begin():
            updated_thread = await self.update_by_primary_key(
                Thread,
                exclude_deleted_or_archived=True,
                primary_key_to_value={"id": thread_id},
                column_to_update=thread_update_fields,
            )
            if not updated_thread:
                raise ServiceError(
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.DB_UPDATE_ERROR,
                        details=f"Thread {thread_id} failed to update.",
                    )
                )
            updated_message = await self.update_by_primary_key(
                Message,
                exclude_deleted_or_archived=True,
                primary_key_to_value={"id": message_id},
                column_to_update=message_update_fields,
            )
            if not updated_message:
                raise ServiceError(
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.DB_UPDATE_ERROR,
                        details=f"Message {message_id} failed to update.",
                    )
                )
        return EmailDto(
            thread=updated_thread,
            message_dtos=[MessageDto(message=updated_message, attachments=[])],
        )

    async def get_by_provider_id(
        self, provider_id: str, email_provider: EmailProvider
    ) -> list[Thread]:
        return await self._find_by_column_values(
            table_model=Thread,
            exclude_deleted_or_archived=False,
            provider=email_provider.name,
            provider_id=provider_id,
        )

    async def list_by_ids(
        self, thread_ids: list[uuid.UUID], organization_id: uuid.UUID
    ) -> list[Thread]:
        stmt = text(
            """
            select * from thread
            where id in :thread_ids and organization_id= :organization_id
            order by greatest(latest_message_received_date, latest_message_sent_date) desc;
            """
        ).bindparams(
            bindparam("thread_ids", thread_ids, expanding=True),
            bindparam("organization_id", organization_id),
        )
        rows = await self.engine.all(stmt)
        return await Thread.bulk_from_rows(rows=rows)

    async def get_by_email_account_id(
        self, email_account_id: UUID, organization_id: UUID
    ) -> list[Thread]:
        stmt = text(
            """
            select * from thread
            where email_account_id = :email_account_id
            and organization_id = :organization_id
            and deleted_at is null
            """
        ).bindparams(email_account_id=email_account_id, organization_id=organization_id)
        rows = await self.engine.all(stmt)
        return await Thread.bulk_from_rows(rows=rows)

    async def get_short_threads_by_email_account_id(
        self, email_account_id: UUID, organization_id: UUID
    ) -> list[ShortDBThread]:
        stmt = text(
            """
            select id, provider_id, email_account_id, organization_id from thread
            where email_account_id = :email_account_id
            and organization_id = :organization_id
            and provider is not null and provider != 'NYLAS' and provider_id is not null and deleted_at is null
            """
        ).bindparams(email_account_id=email_account_id, organization_id=organization_id)
        rows = await self.engine.all(stmt)
        return await ShortDBThread.bulk_from_rows(rows=rows)

    async def get_earliest_synced_thread(
        self, email_account_id: UUID, organization_id: UUID
    ) -> Thread | None:
        stmt = text(
            """
            select * from thread where email_account_id = :email_account_id and organization_id = :organization_id order by earliest_message_date asc limit 1;
            """
        ).bindparams(email_account_id=email_account_id, organization_id=organization_id)
        row = await self.engine.first_or_none(stmt)
        return Thread.from_row(row) if row else None

    async def update_email_account_id_in_batch(
        self, thread_ids: list[UUID], email_account_id: UUID
    ) -> None:
        stmt = (
            text(
                """
            update thread set email_account_id = :email_account_id where id in :thread_ids;
            """
            )
            .bindparams(bindparam("thread_ids", thread_ids, expanding=True))
            .bindparams(email_account_id=email_account_id)
        )
        await self.engine.execute(stmt)

    async def update_thread_participants(
        self, thread_id: UUID, participants: list[EmailHydratedParticipant]
    ) -> Thread:
        updated_thread = await self.update_by_primary_key(
            Thread,
            exclude_deleted_or_archived=True,
            primary_key_to_value={"id": thread_id},
            column_to_update={
                "participants": participants,
                "updated_at": zoned_utc_now(),
            },
        )
        if not updated_thread:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DB_UPDATE_ERROR,
                    details=f"Thread {thread_id} failed to update.",
                )
            )
        return updated_thread

    async def upsert_global_message_association(
        self,
        global_message_association: GlobalMessageAssociation,
    ) -> GlobalMessageAssociation:
        return await self.upsert_unique_target_columns(
            global_message_association,
            on_conflict_target_columns=[
                "global_message_id",
                "message_id",
                "organization_id",
            ],
            exclude_columns_from_update=[
                "id",
                "created_at",
                "deleted_at",
            ],
            exclude_deleted_or_archived=True,
        )

    async def find_by_original_message_id(
        self, original_message_id: str, organization_id: UUID
    ) -> GlobalMessage | None:
        return await self._find_unique_by_column_values(
            GlobalMessage,
            original_message_id=original_message_id,
            organization_id=organization_id,
        )

    async def find_global_thread_by_thread_id(
        self, thread_id: UUID, organization_id: UUID
    ) -> GlobalThread | None:
        """Find GlobalThread by thread_id from its thread_ids array."""
        stmt = text(
            """
            SELECT * FROM global_thread
            WHERE organization_id = :organization_id
            AND :thread_id = ANY(thread_ids);
            """
        ).bindparams(thread_id=thread_id, organization_id=organization_id)

        result = await self.engine.first_or_none(stmt)
        return GlobalThread.from_row(result) if result else None

    async def find_global_thread_by_ids(
        self, global_thread_ids: list[UUID], organization_id: UUID
    ) -> list[GlobalThread]:
        return await self._find_by_column_values(
            GlobalThread,
            id=global_thread_ids,
            organization_id=organization_id,
        )

    async def get_global_message_by_ids(
        self,
        global_message_ids: list[UUID],
        organization_id: UUID,
    ) -> list[GlobalMessage]:
        return await self._find_by_column_values(
            GlobalMessage,
            id=global_message_ids,
            organization_id=organization_id,
        )

    async def get_global_thread_messages_by_original_message_id(
        self, original_message_id: str, organization_id: UUID
    ) -> GlobalThreadMessagesAssociation | None:
        return await self._find_unique_by_column_values(
            GlobalThreadMessagesAssociation,
            original_message_id=original_message_id,
            organization_id=organization_id,
        )

    async def find_global_messages_by_thread_id_order_by_received_at(
        self,
        global_thread_id: UUID,
        organization_id: UUID,
    ) -> list[GlobalMessage]:
        """Find global messages for a thread ordered by received date."""
        stmt = text("""
            SELECT gm.*
            FROM global_message gm
            WHERE gm.global_thread_id = :global_thread_id
            AND gm.organization_id = :organization_id
            AND deleted_at is null
            ORDER BY gm.message_received_at ASC
        """).bindparams(
            global_thread_id=global_thread_id, organization_id=organization_id
        )

        rows = await self.engine.all(stmt)
        return await GlobalMessage.bulk_from_rows(rows=rows)

    async def count_global_messages_by_thread_ids(
        self,
        global_thread_ids: list[UUID],
        organization_id: UUID,
    ) -> dict[UUID, int]:
        if not global_thread_ids:
            return {}
        stmt = (
            text("""
                select global_thread_id, count(*) as cnt from global_message
                    where global_thread_id in :global_thread_ids
                        and organization_id = :organization_id
                        and deleted_at is null
                        group by global_thread_id
                """)
            .bindparams(
                organization_id=organization_id,
            )
            .bindparams(
                bindparam("global_thread_ids", global_thread_ids, expanding=True)
            )
        )
        rows = await self.engine.all(stmt)
        return {row.global_thread_id: row.cnt for row in rows}

    async def find_message_associations_by_global_message_ids(
        self,
        global_message_ids: list[UUID],
        organization_id: UUID,
    ) -> list[GlobalMessageAssociation]:
        return await self._find_by_column_values(
            GlobalMessageAssociation,
            global_message_id=global_message_ids,
            organization_id=organization_id,
        )

    async def find_messages_by_ids(
        self,
        message_ids: list[UUID],
        organization_id: UUID,
    ) -> list[Message]:
        return await self._find_by_column_values(
            Message,
            id=message_ids,
            organization_id=organization_id,
        )

    async def map_messages_by_id(
        self,
        message_ids: list[UUID],
        organization_id: UUID,
    ) -> dict[UUID, Message]:
        ms = await self._find_by_column_values(
            Message, id=message_ids, organization_id=organization_id
        )
        return {m.id: m for m in ms}

    async def map_message_associations_by_global_message_ids(
        self,
        global_message_ids: list[UUID],
        organization_id: UUID,
    ) -> dict[UUID, list[GlobalMessageAssociation]]:
        assocs = await self._find_by_column_values(
            GlobalMessageAssociation,
            global_message_id=global_message_ids,
            organization_id=organization_id,
        )
        assocs_by_global_message_id = defaultdict(list)
        for assoc in assocs:
            assocs_by_global_message_id[assoc.global_message_id].append(assoc)
        return assocs_by_global_message_id

    async def list_global_threads_by_organization_id(
        self,
        organization_id: UUID,
    ) -> list[GlobalThread]:
        return await self._find_by_column_values(
            GlobalThread,
            organization_id=organization_id,
        )

    async def list_global_messages_by_organization_id(
        self,
        organization_id: UUID,
    ) -> list[GlobalMessage]:
        return await self._find_by_column_values(
            GlobalMessage,
            organization_id=organization_id,
        )

    async def list_threads_by_ids(
        self,
        thread_ids: list[UUID],
        organization_id: UUID,
    ) -> list[Thread]:
        if not thread_ids:
            return []
        stmt = text("""
            SELECT * FROM thread
            WHERE id = ANY(:thread_ids)
            AND organization_id = :organization_id
            AND deleted_at IS NULL
        """).bindparams(thread_ids=thread_ids, organization_id=organization_id)
        rows = await self.engine.all(stmt)
        return await Thread.bulk_from_rows(rows=rows)

    async def list_global_threads_by_contact_ids(
        self,
        contact_ids: list[UUID],
        organization_id: UUID,
    ) -> list[GlobalThread]:
        if not contact_ids:
            return []
        sql_stmt = text(
            """
            SELECT * FROM global_thread
            where organization_id = :organization_id and contact_ids && :contact_ids and deleted_at is null
            """
        ).bindparams(organization_id=organization_id, contact_ids=contact_ids)

        rows = await self.engine.all(sql_stmt)
        return await GlobalThread.bulk_from_rows(rows=rows)

    async def list_global_threads_by_account_ids(
        self,
        account_ids: list[UUID],
        organization_id: UUID,
    ) -> list[GlobalThread]:
        if not account_ids:
            return []
        sql_stmt = text(
            """
            SELECT * FROM global_thread
            where organization_id = :organization_id and account_ids && :account_ids and deleted_at is null
            """
        ).bindparams(organization_id=organization_id, account_ids=account_ids)

        rows = await self.engine.all(sql_stmt)
        return await GlobalThread.bulk_from_rows(rows=rows)

    async def get_global_thread_by_id(
        self,
        global_thread_id: UUID,
        organization_id: UUID,
    ) -> GlobalThread | None:
        return await self._find_unique_by_column_values(
            GlobalThread,
            id=global_thread_id,
            organization_id=organization_id,
        )

    async def list_messages_by_global_thread_id(
        self,
        global_thread_id: UUID,
        organization_id: UUID,
    ) -> list[Message]:
        """Find messages associated with a global thread, ensuring one message per global message.

        Args:
            global_thread_id: ID of the global thread
            organization_id: Organization ID for tenancy

        Returns:
            List of Message objects associated with the global thread
        """
        stmt = text("""
            WITH RankedMessages AS (
                SELECT m.*,
                       ROW_NUMBER() OVER (PARTITION BY gm.id ORDER BY m.received_at ASC) as rn
                FROM message m
                INNER JOIN global_message_association gma
                    ON m.id = gma.message_id
                    AND m.organization_id = gma.organization_id
                INNER JOIN global_message gm
                    ON gma.global_message_id = gm.id
                    AND gma.organization_id = gm.organization_id
                WHERE gm.global_thread_id = :global_thread_id
                AND m.organization_id = :organization_id
                AND m.deleted_at IS NULL
                AND gma.deleted_at IS NULL
                AND gm.deleted_at IS NULL
            )
            SELECT * FROM RankedMessages
            WHERE rn = 1
            ORDER BY received_at ASC
        """).bindparams(
            global_thread_id=global_thread_id,
            organization_id=organization_id,
        )

        rows = await self.engine.all(stmt)
        return await Message.bulk_from_rows(rows=rows)

    async def list_messages_with_global_message_id_by_global_thread_id(
        self,
        global_thread_id: UUID,
        organization_id: UUID,
    ) -> list[MessageWithGlobalMessageId]:
        """Find messages associated with a global thread, ensuring one message per global message.
        Also return the global message id for each message.
        Args:
            global_thread_id: ID of the global thread
            organization_id: Organization ID for tenancy

        Returns:
            List of Message objects associated with the global thread
        """
        stmt = text("""
            WITH RankedMessages AS (
                SELECT m.*,
                       gm.id as global_message_id,
                       ROW_NUMBER() OVER (PARTITION BY gm.id ORDER BY m.received_at ASC) as rn
                FROM message m
                INNER JOIN global_message_association gma
                    ON m.id = gma.message_id
                    AND m.organization_id = gma.organization_id
                INNER JOIN global_message gm
                    ON gma.global_message_id = gm.id
                    AND gma.organization_id = gm.organization_id
                WHERE gm.global_thread_id = :global_thread_id
                AND m.organization_id = :organization_id
                AND m.deleted_at IS NULL
                AND gma.deleted_at IS NULL
                AND gm.deleted_at IS NULL
            )
            SELECT * FROM RankedMessages
            WHERE rn = 1
            ORDER BY received_at ASC
        """).bindparams(
            global_thread_id=global_thread_id,
            organization_id=organization_id,
        )

        rows = await self.engine.all(stmt)
        return await MessageWithGlobalMessageId.bulk_from_rows(rows=rows)

    async def list_global_threads_by_pipeline_id(
        self,
        pipeline_id: UUID,
        organization_id: UUID,
    ) -> list[GlobalThread]:
        return await self._find_by_column_values(
            GlobalThread,
            pipeline_id=pipeline_id,
            organization_id=organization_id,
        )

    async def list_global_threads_by_sequence_id(
        self,
        sequence_id: UUID,
        organization_id: UUID,
    ) -> list[GlobalThread]:
        return await self._find_by_column_values(
            GlobalThread,
            sequence_id=sequence_id,
            organization_id=organization_id,
        )

    async def find_global_message_association_by_message_id(
        self,
        message_id: UUID,
        organization_id: UUID,
    ) -> GlobalMessageAssociation | None:
        return await self._find_unique_by_column_values(
            GlobalMessageAssociation,
            message_id=message_id,
            organization_id=organization_id,
        )

    async def find_global_message_associations_by_message_ids(
        self,
        message_ids: list[UUID],
        organization_id: UUID,
    ) -> list[GlobalMessageAssociation]:
        return await self._find_by_column_values(
            GlobalMessageAssociation,
            message_id=message_ids,
            organization_id=organization_id,
        )

    async def upsert_message(self, message: Message) -> Message:
        return await self.upsert_unique_target_columns(
            message,
            on_conflict_target_columns=[
                "provider",
                "provider_id",
                "email_account_id",
            ],
            exclude_columns_from_update=[
                "id",
                "thread_id",
                "organization_id",
                "template_id",
                "email_template_id",
                "email_template_version",
                "status",
                "reply_to_message_id",
                "send_at",
                "created_at",
            ],
        )

    async def find_global_messages_by_original_message_ids(
        self,
        organization_id: UUID,
        original_message_ids: list[str],
    ) -> list[GlobalMessage]:
        return await self._find_by_column_values(
            GlobalMessage,
            organization_id=organization_id,
            original_message_id=original_message_ids,
        )

    async def get_global_thread_by_original_message_ids(
        self, original_message_ids: list[str], organization_id: UUID
    ) -> GlobalThread | None:
        # find global thread id by any original message id
        stmt = text("""
            select distinct global_thread.* from global_thread
            inner join global_thread_messages_association
            on global_thread.id = global_thread_messages_association.global_thread_id
            and global_thread.organization_id = global_thread_messages_association.organization_id
            where global_thread.organization_id = :organization_id
            and global_thread_messages_association.original_message_id = ANY(:original_message_ids)
            and global_thread_messages_association.deleted_at is null
            and global_thread.deleted_at is null
            order by global_thread.created_at desc
        """).bindparams(
            original_message_ids=original_message_ids, organization_id=organization_id
        )
        rows = await self.engine.all(statement=stmt)
        if len(rows) > 1:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DB_UPDATE_ERROR,
                    details=f"Multiple global threads found for original message ids: {original_message_ids}",
                )
            )
        return GlobalThread.from_row(rows[0]) if rows else None

    async def upsert_global_thread_messages_association(
        self,
        organization_id: UUID,
        global_thread_id: UUID,
        original_message_ids: list[str],
    ) -> None:
        async with self.engine.begin():
            for original_message_id in original_message_ids:
                await self.upsert_unique_target_columns(
                    GlobalThreadMessagesAssociation(
                        id=uuid4(),
                        organization_id=organization_id,
                        global_thread_id=global_thread_id,
                        original_message_id=original_message_id,
                        created_at=zoned_utc_now(),
                    ),
                    on_conflict_target_columns=[
                        "original_message_id",
                        "organization_id",
                    ],
                    exclude_columns_from_update=[
                        "id",
                        "deleted_at",
                        "global_thread_id",
                    ],
                    exclude_deleted_or_archived=True,
                )

    async def get_global_message_by_provider_id_and_email_account_ids(
        self,
        provider_id: str,
        email_provider: EmailProvider,
        email_account_ids: list[UUID],
    ) -> tuple[GlobalMessage, UUID] | None:
        """Get global message by provider id and email provider.

        Args:
            provider_id: The provider-specific message ID
            email_provider: The email provider (e.g., NYLAS)
            email_account_ids: The email account ids to filter the messages

        Returns:
            Tuple of (GlobalMessage, email_account_id) or None if not found
        """
        stmt = text("""
            SELECT
                distinct global_message.*, message.email_account_id
            FROM
                message
            INNER JOIN
                global_message_association
                ON message.id = global_message_association.message_id
                AND message.organization_id = global_message_association.organization_id
                AND global_message_association.deleted_at IS NULL
            INNER JOIN
                global_message
                ON global_message_association.global_message_id = global_message.id
                AND global_message_association.organization_id = global_message.organization_id
                AND global_message.deleted_at IS NULL
            WHERE
                message.provider_id = :provider_id
                AND message.provider = :provider
                AND message.deleted_at IS NULL
                AND message.email_account_id = ANY(:email_account_ids)
        """).bindparams(
            provider_id=provider_id,
            provider=email_provider.name,
            email_account_ids=email_account_ids,
        )

        result = await self.engine.all(stmt)
        if not result:
            return None

        if len(result) > 1:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.CONFLICT,
                    details=f"Multiple global messages found for provider id: {provider_id}",
                )
            )

        # Get the row and extract
        row = result[0]
        email_account_id = row._mapping["email_account_id"]

        # Return the tuple
        return (GlobalMessage.from_row(row), email_account_id)

    async def delete_failed_message_and_global_message(
        self,
        organization_id: UUID,
        global_email_dto: GlobalEmailDto,
        is_new_thread: bool,
    ) -> tuple[UUID, UUID] | None:
        """
        Delete a failed message and its thread if it's the new thread
        return global_message_id and global_thread_id
        """
        first_message = global_email_dto.message_dtos[0].message
        if first_message.status not in (MessageStatus.CREATED, MessageStatus.SCHEDULED):
            return None
        await self.delete_failed_message_and_thread(
            organization_id=organization_id,
            message=first_message,
            is_new_thread=is_new_thread,
        )
        (
            global_message_id,
            global_thread_id,
        ) = await self.delete_failed_global_message_and_global_thread(
            organization_id=organization_id,
            message=first_message,
            is_new_thread=is_new_thread,
        )
        return global_message_id, global_thread_id

    async def delete_failed_message_and_thread(
        self,
        organization_id: UUID,
        message: Message,
        is_new_thread: bool,
    ) -> None:
        async with self.engine.begin():
            # 1. Delete the failed message and set status to FAILED
            await self.update_by_tenanted_primary_key(
                Message,
                organization_id=organization_id,
                primary_key_to_value={"id": message.id},
                column_to_update=MessageUpdate(
                    deleted_at=zoned_utc_now(),
                    status=MessageStatus.FAILED,
                    error_info=MessageErrorInfo(
                        error_code="SEND_MESSAGE_ERROR",
                        is_resolvable=False,
                        description="Failed to send email, please try again later.",
                    ),
                ),
            )
            # 2 Delete thread if it's a new thread
            if is_new_thread:
                await self.update_by_tenanted_primary_key(
                    Thread,
                    organization_id=organization_id,
                    primary_key_to_value={"id": message.thread_id},
                    column_to_update=ThreadUpdate(
                        deleted_at=zoned_utc_now(),
                        updated_at=zoned_utc_now(),
                    ),
                )

    async def delete_failed_global_message_and_global_thread(
        self,
        organization_id: UUID,
        message: Message,
        is_new_thread: bool,
    ) -> tuple[UUID, UUID]:
        """
        Delete a global message and its thread if it's the new thread
        return global_message_id and global_thread_id
        """
        async with self.engine.begin():
            # 1. Delete global message association
            global_message_association = not_none(
                await self._update_unique_by_column_values(
                    GlobalMessageAssociation,
                    column_value_to_query={
                        "message_id": message.id,
                        "organization_id": organization_id,
                    },
                    column_to_update=GlobalMessageAssociationUpdate(
                        deleted_at=zoned_utc_now(),
                    ),
                )
            )
            # 2. Delete global message
            global_message = not_none(
                await self.update_by_tenanted_primary_key(
                    GlobalMessage,
                    primary_key_to_value={
                        "id": global_message_association.global_message_id
                    },
                    organization_id=organization_id,
                    column_to_update=GlobalMessageUpdate(
                        deleted_at=zoned_utc_now(),
                    ),
                    exclude_deleted_or_archived=True,
                )
            )
            # 3. Delete global thread messages association
            # For unsent messages, use message id as original_message_id
            original_message_id = str(message.id)
            thread_messages_association = not_none(
                await self._find_unique_by_column_values(
                    GlobalThreadMessagesAssociation,
                    original_message_id=original_message_id,
                    organization_id=organization_id,
                )
            )
            await self.update_by_primary_key(
                GlobalThreadMessagesAssociation,
                primary_key_to_value={"id": thread_messages_association.id},
                column_to_update=GlobalThreadMessagesAssociationUpdate(
                    deleted_at=zoned_utc_now(),
                ),
            )

            # 4. Delete global thread if it's a new thread
            if is_new_thread:
                await self.update_by_tenanted_primary_key(
                    GlobalThread,
                    primary_key_to_value={"id": global_message.global_thread_id},
                    organization_id=organization_id,
                    column_to_update=GlobalThreadUpdate(
                        deleted_at=zoned_utc_now(),
                    ),
                )
            return global_message.id, global_message.global_thread_id

    async def get_global_message_associations_by_global_message_id(
        self,
        global_message_id: UUID,
        organization_id: UUID,
    ) -> list[GlobalMessageAssociation]:
        return await self._find_by_column_values(
            GlobalMessageAssociation,
            global_message_id=global_message_id,
            organization_id=organization_id,
        )

    async def find_email_events_summaries_by_global_message_ids(
        self,
        global_message_ids: list[UUID],
        organization_id: UUID,
    ) -> dict[UUID, list[GlobalMessageEventSummaryDTO]]:
        """
        Find email events summaries for multiple global messages, grouped by event type.
        Returns a list of GlobalMessageEventSummaryDTO with (global_message_id, event_type, latest_event_time).
        """
        stmt = text("""
            SELECT
                global_message_id,
                type as event_type,
                MAX(event_time) as latest_event_time
            FROM email_event
            WHERE global_message_id = ANY(:global_message_ids)
            AND organization_id = :organization_id
            GROUP BY global_message_id, type
            ORDER BY global_message_id, latest_event_time DESC
        """).bindparams(
            global_message_ids=global_message_ids,
            organization_id=organization_id,
        )

        rows = await self.engine.all(stmt)
        dtos = await GlobalMessageEventSummaryDTO.bulk_from_rows(rows=rows)
        dtos_by_global_message_id: dict[UUID, list[GlobalMessageEventSummaryDTO]] = (
            defaultdict(list)
        )
        for dto in dtos:
            dtos_by_global_message_id[dto.global_message_id].append(dto)
        return dtos_by_global_message_id

    async def get_thread_by_id(
        self,
        organization_id: UUID,
        thread_id: UUID,
    ) -> Thread | None:
        return await self._find_unique_by_column_values(
            table_model=Thread,
            organization_id=organization_id,
            id=thread_id,
        )

    async def put_global_message_sales_activity_types(
        self,
        *,
        organization_id: UUID,
        global_message_id: UUID,
        user_id: UUID,
        sales_activity_types: list[StandardSalesActionType] | None,
    ) -> GlobalMessage | None:
        stmt = text(
            """
            update global_message
            set sales_action_types = :sales_activity_types
            where organization_id = :organization_id
              and id = :global_message_id
            returning *
            """
        ).bindparams(
            organization_id=organization_id,
            global_message_id=global_message_id,
            sales_activity_types=sales_activity_types,
        )
        row = await self.engine.at_most_one(stmt)
        return GlobalMessage.from_row(row) if row else None

    async def list_global_messages_by_sales_action_types_for_pipeline(
        self,
        organization_id: UUID,
        sales_action_types_filters: list[list[StandardSalesActionType]],
        pipeline_id: UUID,
    ) -> list[GlobalMessage]:
        """
        List global messages that have any of the specified sales action types.

        Args:
            organization_id: Organization ID to filter by
            sales_action_types_filters: List of sales action types to filter by
                Each list contains a set of sales action types to filter by
                e.g. [[StandardSalesActionType.INTRO], [StandardSalesActionType.FOLLOWUP]]
                will return messages with either intro or followup sales action types
            pipeline_id: Pipeline ID to further filter results

        Returns:
            List of global messages matching the criteria
        """
        if not sales_action_types_filters:
            return []

        if all(
            len(sales_action_types) == 0
            for sales_action_types in sales_action_types_filters
        ):
            return []

        sales_action_type_filter_params: dict[str, list[StandardSalesActionType]] = {}
        sales_action_type_filter_clauses: list[str] = []
        for i, sales_action_types in enumerate(sales_action_types_filters):
            if not sales_action_types:
                continue
            param_name = f"sales_action_types_{i}"
            sales_action_type_filter_params[param_name] = sales_action_types
            sales_action_type_filter_clauses.append(
                f"gm.sales_action_types @> :{param_name}"
            )
        sales_action_type_filter_clause = " OR ".join(sales_action_type_filter_clauses)

        query = f"""
            SELECT gm.* FROM global_message gm
            JOIN global_thread gt ON gm.global_thread_id = gt.id
            WHERE gm.organization_id = :organization_id
            AND gt.pipeline_id = :pipeline_id
            AND gm.deleted_at IS NULL
            AND ({sales_action_type_filter_clause})
        """  # noqa: S608
        stmt = text(query).bindparams(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            **sales_action_type_filter_params,
        )
        rows = await self.engine.all(stmt)
        return await GlobalMessage.bulk_from_rows(rows=rows)
