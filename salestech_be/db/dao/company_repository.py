import uuid
from uuid import UUID

from sqlalchemy import bindparam, text

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import ErrorDetails, ServiceError
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.dto.company_dto import CompanyDto
from salestech_be.db.models.company import Company, CompanyUpdate
from salestech_be.db.models.pdl_company import PDLCompany
from salestech_be.integrations.pdl.model import PeopleDataLabsCompany


class CompanyRepository(GenericRepository):
    async def list_by_ids(
        self, organization_id: UUID, ids: list[UUID]
    ) -> list[Company]:
        if not ids:
            return []
        stmt = (
            text(
                """
            select * from company where id in :ids and organization_id=:organization_id;
            """
            )
            .bindparams(bindparam("ids", ids, expanding=True))
            .bindparams(organization_id=organization_id)
        )

        rows = await self.engine.all(stmt)
        return await Company.bulk_from_rows(rows=rows)

    async def update_by_id(
        self, company_id: UUID, organization_id: UUID, company_update: CompanyUpdate
    ) -> Company:
        if updated_company := await self._update_unique_by_column_values(
            Company,
            column_value_to_query={
                "id": company_id,
                "organization_id": organization_id,
            },
            column_to_update=company_update,
        ):
            return updated_company
        raise ServiceError(
            additional_error_details=ErrorDetails(
                code=ErrorCode.DB_UPDATE_ERROR,
                details=f"Company {company_id} failed to update.",
            )
        )

    async def update_db_company_and_pdl_company(
        self,
        people_data_labs_company_list: list[PeopleDataLabsCompany],
        user_id: UUID,
        organization_id: UUID,
    ) -> list[CompanyDto]:
        results = []
        async with self.engine.begin():
            for people_data_labs_company in people_data_labs_company_list:
                pdl_company = await self.upsert_unique_target_columns(
                    PDLCompany.from_people_data_labs_company(
                        company_id=uuid.uuid4(),
                        organization_id=organization_id,
                        people_data_labs_company=people_data_labs_company,
                    ),
                    on_conflict_target_columns=["organization_id", "ext_id"],
                    exclude_columns_from_update=["id", "company_id", "created_at"],
                )
                updated_db_company = await self.upsert_unique_target_columns(
                    Company.from_people_data_labs_company(
                        people_data_labs_company=people_data_labs_company,
                        user_id=user_id,
                        organization_id=organization_id,
                    ).model_copy(update={"id": pdl_company.company_id}),
                    on_conflict_target_columns=["id"],
                    exclude_columns_from_update=[
                        "user_id",
                        "created_at",
                        "organization_id",
                    ],
                )
                results.append(CompanyDto(db_company=updated_db_company))
        return results
