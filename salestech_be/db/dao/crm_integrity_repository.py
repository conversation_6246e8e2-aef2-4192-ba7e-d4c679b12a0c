from uuid import UUID, uuid4

from salestech_be.common.exception.exception import InvalidArgumentError
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityAssociatedEntityOperation,
    CRMIntegrityJob,
    CRMIntegrityJobContextualParam,
    CRMIntegrityJobUserOption,
    CRMIntegrityOperation,
    CRMIntegritySubDomainJob,
    CRMSubDomain,
    EntityType,
    IntegrityJobType,
    JobStatus,
    SubDomainJobStatus,
)
from salestech_be.util.time import zoned_utc_now


class CRMIntegrityJobRepository(GenericRepository):
    """CRM Integrity Job Repository"""

    async def create_integrity_job(
        self,
        *,
        job_type: IntegrityJobType,
        src_entity_type: EntityType,
        src_entity_id: UUID,
        dest_entity_type: EntityType | None,
        dest_entity_id: UUID | None,
        user_id: UUID,
        organization_id: UUID,
        contextual_param: CRMIntegrityJobContextualParam | None = None,
        user_choices: dict[CRMSubDomain, CRMIntegrityJobUserOption | None],
    ) -> tuple[CRMIntegrityJob, list[CRMIntegritySubDomainJob]]:
        async with self.engine.begin():
            integrity_job = await self.insert(
                CRMIntegrityJob(
                    id=uuid4(),
                    type=job_type,
                    status=JobStatus.NEW,
                    src_entity_type=src_entity_type,
                    src_entity_id=src_entity_id,
                    dest_entity_type=dest_entity_type,
                    dest_entity_id=dest_entity_id,
                    created_at=zoned_utc_now(),
                    created_by_user_id=user_id,
                    organization_id=organization_id,
                    contextual_param=contextual_param,
                )
            )

            if not user_choices:
                return integrity_job, []

            subdomain_jobs = []
            for subdomain, user_choice in user_choices.items():
                subdomain_jobs.append(
                    await self.insert(
                        CRMIntegritySubDomainJob(
                            id=uuid4(),
                            integrity_job_id=integrity_job.id,
                            subdomain=subdomain,
                            user_choice=user_choice,
                            status=SubDomainJobStatus.NEW,
                            created_at=zoned_utc_now(),
                            created_by_user_id=user_id,
                            organization_id=organization_id,
                        )
                    )
                )

            return integrity_job, subdomain_jobs

    async def delete_integrity_job(
        self,
        *,
        job_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> None:
        integrity_job = await self.get_integrity_job(
            organization_id=organization_id,
            job_id=job_id,
        )

        if integrity_job.status in [JobStatus.RUNNING, JobStatus.QUEUED]:
            raise InvalidArgumentError("cannot delete running or queued job")

        async with self.engine.begin():
            await self._update_by_column_values(
                table_model=CRMIntegrityAssociatedEntityOperation,
                column_value_to_query={
                    "integrity_job_id": integrity_job.id,
                    "organization_id": organization_id,
                },
                column_to_update={
                    "deleted_by_user_id": user_id,
                    "deleted_at": zoned_utc_now(),
                },
            )

            await self._update_by_column_values(
                table_model=CRMIntegrityOperation,
                column_value_to_query={
                    "integrity_job_id": integrity_job.id,
                    "organization_id": organization_id,
                },
                column_to_update={
                    "deleted_by_user_id": user_id,
                    "deleted_at": zoned_utc_now(),
                },
            )

            await self._update_by_column_values(
                table_model=CRMIntegritySubDomainJob,
                column_value_to_query={
                    "integrity_job_id": integrity_job.id,
                    "organization_id": organization_id,
                },
                column_to_update={
                    "deleted_by_user_id": user_id,
                    "deleted_at": zoned_utc_now(),
                },
            )

            await self.update_by_primary_key(
                table_model=CRMIntegrityJob,
                primary_key_to_value={"id": integrity_job.id},
                column_to_update={
                    "deleted_by_user_id": user_id,
                    "deleted_at": zoned_utc_now(),
                },
            )

    async def get_integrity_job(
        self,
        organization_id: UUID,
        job_id: UUID,
    ) -> CRMIntegrityJob:
        return await self._find_unique_by_column_values_or_fail(
            table_model=CRMIntegrityJob,
            id=job_id,
            organization_id=organization_id,
        )

    async def list_crm_integrity_jobs(
        self,
        organization_id: UUID,
    ) -> list[CRMIntegrityJob]:
        return await self._find_by_column_values(
            table_model=CRMIntegrityJob,
            organization_id=organization_id,
        )

    async def find_user_choices_by_integrity_job_id(
        self,
        integrity_job_id: UUID,
        organization_id: UUID,
    ) -> dict[CRMSubDomain, CRMIntegrityJobUserOption]:
        subdomain_jobs = await self._find_by_column_values(
            table_model=CRMIntegritySubDomainJob,
            integrity_job_id=integrity_job_id,
            organization_id=organization_id,
        )

        return {
            subdomain_job.subdomain: subdomain_job.user_choice
            for subdomain_job in subdomain_jobs
            if subdomain_job.user_choice
        }

    async def find_integrity_jobs_by_job_ids(
        self,
        job_ids: list[UUID],
        organization_id: UUID,
        job_status: list[JobStatus],
    ) -> list[CRMIntegrityJob]:
        return await self._find_by_column_values(
            table_model=CRMIntegrityJob,
            id=job_ids,
            organization_id=organization_id,
            status=job_status,
        )


class CRMIntegrityOperationRepository(GenericRepository):
    """CRM Integrity Operation Repository"""

    async def find_integrity_operations_by_job_id(
        self,
        integrity_job_id: UUID,
        organization_id: UUID,
    ) -> list[CRMIntegrityOperation]:
        return await self._find_by_column_values(
            table_model=CRMIntegrityOperation,
            integrity_job_id=integrity_job_id,
            organization_id=organization_id,
        )


class CRMIntegritySubDomainJobRepository(GenericRepository):
    """CRM Integrity Sub Domain Job Repository"""

    async def find_sub_domain_jobs_by_integrity_job_id(
        self,
        integrity_job_id: UUID,
        organization_id: UUID,
    ) -> list[CRMIntegritySubDomainJob]:
        return await self._find_by_column_values(
            table_model=CRMIntegritySubDomainJob,
            integrity_job_id=integrity_job_id,
            organization_id=organization_id,
        )

    async def find_sub_domain_job_by_integrity_job_id_and_subdomain(
        self,
        integrity_job_id: UUID,
        subdomain: CRMSubDomain,
        organization_id: UUID,
    ) -> CRMIntegritySubDomainJob | None:
        return await self._find_unique_by_column_values(
            table_model=CRMIntegritySubDomainJob,
            integrity_job_id=integrity_job_id,
            subdomain=subdomain,
            organization_id=organization_id,
        )

    async def list_sub_domain_jobs(
        self,
        organization_id: UUID,
    ) -> list[CRMIntegritySubDomainJob]:
        return await self._find_by_column_values(
            table_model=CRMIntegritySubDomainJob,
            organization_id=organization_id,
        )

    async def update_sub_domain_job_status(
        self,
        sub_domain_job_id: UUID,
        status: SubDomainJobStatus,
    ) -> CRMIntegritySubDomainJob | None:
        return await self.update_by_primary_key(
            table_model=CRMIntegritySubDomainJob,
            primary_key_to_value={"id": sub_domain_job_id},
            column_to_update={"status": status},
        )


class CRMIntegrityAssociatedEntityOperationRepository(GenericRepository):
    """CRM Integrity Associated Entity Operation Repository"""

    async def find_associated_entity_operations_by_integrity_job_id(
        self,
        integrity_job_id: UUID,
        organization_id: UUID,
    ) -> list[CRMIntegrityAssociatedEntityOperation]:
        return await self._find_by_column_values(
            table_model=CRMIntegrityAssociatedEntityOperation,
            integrity_job_id=integrity_job_id,
            organization_id=organization_id,
        )
