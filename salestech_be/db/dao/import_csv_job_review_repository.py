from uuid import UUID

from sqlalchemy import text

from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.import_csv_job_review_db import (
    ImportCsvJobReviewExtraInfoDb,
    ImportCsvJobReviewStatusDb,
)


class ImportCsvJobReviewRepository(GenericRepository):
    async def list_job_reviews(
        self,
        include_organization_id: UUID | None = None,
        include_statuses: list[ImportCsvJobReviewStatusDb] | None = None,
        exclude_is_archived: bool = True,
        exclude_is_orphaned: bool = True,
        limit: int = 800,
        offset: int = 0,
    ) -> list[ImportCsvJobReviewExtraInfoDb]:
        """
        Returns a list of submissions.
        Each select added reduces the number of results by including only the select attribute.
        Each filter added reduces the number of results excluding the filter attribute.
        """
        where_clauses = ["TRUE"]
        params: dict[str, int | UUID | list[ImportCsvJobReviewStatusDb]] = {
            "limit": limit,
            "offset": offset,
        }

        if include_organization_id:
            where_clauses.append("icjr.organization_id = :organization_id")
            params["organization_id"] = include_organization_id

        if include_statuses:
            where_clauses.append("icjr.status = ANY(:statuses)")
            params["statuses"] = include_statuses

        if exclude_is_archived:
            where_clauses.append("icjr.archived_at IS NULL")

        if exclude_is_orphaned:
            where_clauses.append("icjr.import_csv_job_id IS NOT NULL")

        where_clause = " AND ".join(where_clauses)

        stmt = text(
            f"""
            SELECT icjr.*, o.display_name AS info_organization_display_name
            FROM import_csv_job_review icjr
            LEFT JOIN organization o ON icjr.organization_id = o.id
            WHERE {where_clause}
            ORDER BY icjr.created_at DESC
            LIMIT :limit
            OFFSET :offset
            """  # noqa: S608
        ).bindparams(**params)

        rows = await self.engine.all(stmt)
        return await ImportCsvJobReviewExtraInfoDb.bulk_from_rows(rows=rows)
