from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.pdl_cache import PDLCache
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


class PDLCacheRepository(GenericRepository):
    async def find_by_query_hash(
        self, operation_name: str, query_hash: str
    ) -> PDLCache | None:
        try:
            return await self._find_unique_by_column_values_or_fail(
                table_model=PDLCache,
                exclude_deleted_or_archived=False,
                operation_name=operation_name,
                query_hash=query_hash,
            )
        except Exception:
            return None

    async def upsert_cache(self, pdl_cache: PDLCache) -> PDLCache:
        return await self.upsert_unique_target_columns(
            PDLCache(
                id=pdl_cache.id,
                operation_name=pdl_cache.operation_name,
                query_hash=pdl_cache.query_hash,
                request=pdl_cache.request,
                response=pdl_cache.response,
                expires_at=pdl_cache.expires_at,
                created_at=pdl_cache.created_at,
                deleted_at=pdl_cache.deleted_at,
            ),
            columns_to_update=["response", "expires_at"],
            on_conflict_target_columns=["operation_name", "query_hash"],
            exclude_deleted_or_archived=True,
        )
