from typing import Any
from uuid import UUID, uuid4

from asyncpg import UniqueViolationError
from sqlalchemy import text
from sqlalchemy.exc import IntegrityError

from salestech_be.core.common.accounts_receivable import AccountsReceivable
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.core.types import EntityDnsRecord
from salestech_be.db.models.outbound import (
    DomainHealth,
    OutboundDomain,
    OutboundDomainStatus,
    OutboundVendor,
    OutboundWorkspace,
)
from salestech_be.integrations.infraforge.type import OneTimeDomainPurchaseInvoice


class OutboundWorkspaceRepository(GenericRepository):
    async def insert_outbound_workspace(
        self,
        organization_id: UUID,
        name: str,
        vendor: OutboundVendor,
        external_id: str,
        is_mock_record: bool = False,
    ) -> OutboundWorkspace:
        return await self.insert(
            OutboundWorkspace(
                id=uuid4(),
                organization_id=organization_id,
                name=name,
                vendor=vendor,
                external_id=external_id,
                is_mock_record=is_mock_record,
            )
        )

    async def get_outbound_workspace_by_organization_id(
        self, organization_id: UUID
    ) -> OutboundWorkspace | None:
        return await self._find_unique_by_column_values(
            OutboundWorkspace, organization_id=organization_id
        )

    async def get_outbound_workspace_by_id(
        self, workspace_id: UUID
    ) -> OutboundWorkspace | None:
        return await self._find_unique_by_column_values(
            OutboundWorkspace, id=workspace_id
        )

    async def get_workspace_by_org_id_and_id(
        self, organization_id: UUID, workspace_id: UUID
    ) -> OutboundWorkspace | None:
        return await self._find_unique_by_column_values(
            OutboundWorkspace, organization_id=organization_id, id=workspace_id
        )


class OutboundDomainRepository(GenericRepository):
    async def find_many(self, filters: dict[str, Any]) -> list[OutboundDomain]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return await self._find_by_column_values(OutboundDomain, **filters)

    async def find_all(self) -> list[OutboundDomain]:
        return await self._find_all(OutboundDomain)

    async def get_domains_in_workspace(
        self, workspace_id: UUID
    ) -> list[OutboundDomain]:
        return await self._find_by_column_values(
            OutboundDomain, workspace_id=workspace_id
        )

    async def get_all_domains(self) -> list[OutboundDomain]:
        stmt = text(
            """
            SELECT * FROM outbound_domain
            """
        )
        rows = await self.engine.all(stmt)
        return [OutboundDomain.model_validate(row._mapping) for row in rows]

    async def get_total_mailboxes_by_organization_id(
        self, organization_id: UUID
    ) -> dict[str, int]:
        stmt = text(
            """
            SELECT d.domain as dom,
            COUNT(e.id) as mailbox_count
            FROM outbound_domain d
            LEFT JOIN email_account e ON d.id = e.outbound_domain_id
            WHERE d.organization_id = :organization_id
            GROUP BY d.domain;
            """,
        ).bindparams(organization_id=organization_id)
        rows = await self.engine.all(stmt)
        return {row.dom: row.mailbox_count for row in rows}

    async def get_active_mailboxes_by_organization_id(
        self, organization_id: UUID
    ) -> dict[str, int]:
        stmt = text(
            """
            SELECT d.domain as dom,
            COUNT(e.id) as mailbox_count
            FROM outbound_domain d
            LEFT JOIN email_account e ON d.id = e.outbound_domain_id
            WHERE d.organization_id = :organization_id
            AND e.active = true
            GROUP BY d.domain;
            """,
        ).bindparams(organization_id=organization_id)
        rows = await self.engine.all(stmt)
        return {row.dom: row.mailbox_count for row in rows}

    async def get_sequence_count_by_organization_id(
        self, organization_id: UUID
    ) -> dict[str, int]:
        stmt = text(
            """
            SELECT
                d.domain as dom,
                COUNT(DISTINCT se.sequence_id) as sequence_count
            FROM
                outbound_domain d
                LEFT JOIN email_account e ON d.id = e.outbound_domain_id
                LEFT JOIN sequence_enrollment se ON e.id = se.email_account_id
            WHERE
                d.organization_id = :organization_id
                AND e.archived_at IS NULL
                AND se.deleted_at IS NULL
            GROUP BY
                d.domain;
            """
        ).bindparams(organization_id=organization_id)
        rows = await self.engine.all(stmt)
        return {row.dom: row.sequence_count for row in rows}

    async def get_domain_by_id_and_workspace_id(
        self, outbound_domain_id: UUID, workspace_id: UUID
    ) -> OutboundDomain | None:
        return await self._find_unique_by_column_values(
            OutboundDomain,
            id=outbound_domain_id,
            workspace_id=workspace_id,
        )

    async def get_domain_by_id_and_org_id(
        self,
        outbound_domain_id: UUID,
        organization_id: UUID,
        exclude_archived: bool = True,
    ) -> OutboundDomain | None:
        return await self._find_unique_by_column_values(
            OutboundDomain,
            id=outbound_domain_id,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_archived,
        )

    async def get_outbound_domains_by_id_and_org_id(
        self, outbound_domain_id: UUID, organization_id: UUID
    ) -> list[OutboundDomain]:
        return await self._find_by_column_values(
            OutboundDomain,
            id=outbound_domain_id,
            organization_id=organization_id,
        )

    async def get_outbound_domains_by_org_id(
        self, organization_id: UUID, exclude_deleted_or_archived: bool = True
    ) -> list[OutboundDomain]:
        return await self._find_by_column_values(
            OutboundDomain,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
        )

    async def get_number_of_outbound_domains_by_org_id(
        self,
        organization_id: UUID,
    ) -> int:
        # TODO: @Benson if/when we pivot off of Infraforge, we may need to change vendor='INFRAFORGE'.
        stmt = text(
            """
            SELECT COUNT(*)
            FROM outbound_domain
            WHERE organization_id = :organization_id
                AND vendor = :vendor
                AND deleted_at IS NULL
            """
        ).bindparams(organization_id=organization_id, vendor=OutboundVendor.INFRAFORGE)
        rows = await self.engine.one(stmt)
        return int(rows[0])

    async def get_outbound_domains_by_ids_and_org_id(
        self, outbound_domain_ids: list[UUID], organization_id: UUID
    ) -> list[OutboundDomain]:
        """Retrieve multiple outbound domains by their IDs and organization ID in a single query."""
        # Use a SQL query with IN clause, covers archived, deleted, and normal domains
        stmt = text(
            """
            SELECT * FROM outbound_domain
            WHERE id = ANY(:domain_ids)
            AND organization_id = :organization_id
            """,
        ).bindparams(
            domain_ids=outbound_domain_ids,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        return [OutboundDomain.model_validate(row._mapping) for row in rows]

    async def get_domain_by_domain_name(
        self, domain_name: str, is_mock_record: bool = False
    ) -> OutboundDomain | None:
        return await self._find_unique_by_column_values(
            OutboundDomain,
            domain=domain_name,
            is_mock_record=is_mock_record,
        )

    async def bulk_insert_outbound_domains(
        self,
        outbound_domains: list[OutboundDomain],
    ) -> list[OutboundDomain]:
        return await self.bulk_insert(
            table_model=OutboundDomain, instances=outbound_domains
        )

    async def insert_outbound_domain(
        self,
        organization_id: UUID,
        workspace_id: UUID,
        domain: str,
        external_id: str,
        status: OutboundDomainStatus,
        created_by_user_id: UUID,
        invoice: OneTimeDomainPurchaseInvoice,
        forward_to_domain: str | None = None,
        is_mock_record: bool = False,
        transaction_type: AccountsReceivable | None = None,
    ) -> OutboundDomain | None:
        return await self.insert(
            OutboundDomain(
                organization_id=organization_id,
                workspace_id=workspace_id,
                domain=domain,
                status=status,
                created_by_user_id=created_by_user_id,
                external_id=external_id,
                forward_to_domain=forward_to_domain,
                is_mock_record=is_mock_record,
                invoice=invoice,
                transaction_type=transaction_type,
            )
        )

    async def update_outbound_domain(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, outbound_domain_id: UUID, column_to_update: dict[str, Any]
    ) -> OutboundDomain | None:
        return await self.update_by_primary_key(
            OutboundDomain,
            primary_key_to_value={"id": outbound_domain_id},
            column_to_update=column_to_update,
        )

    async def update_outbound_domain_with_organization_id(  # type: ignore[explicit-any]
        self,
        outbound_domain_id: UUID,
        organization_id: UUID,
        column_to_update: dict[str, Any],
    ) -> OutboundDomain | None:
        return await self._update_unique_by_column_values(
            OutboundDomain,
            column_value_to_query={
                "id": outbound_domain_id,
                "organization_id": organization_id,
            },
            column_to_update=column_to_update,
        )

    async def upsert_outbound_domain(
        self,
        outbound_domain: OutboundDomain,
    ) -> OutboundDomain | None:
        """Upsert an outbound domain using domain as unique constraint."""
        # First try to find an existing domain with the same domain name
        existing_domain = await self.get_domain_by_domain_name(
            domain_name=outbound_domain.domain,
            is_mock_record=outbound_domain.is_mock_record,
        )

        # If no existing domain or all existing domains are deleted/archived, insert a new one
        if not existing_domain:
            return await self.insert(outbound_domain)

        # Prepare the update data
        update_data = outbound_domain.model_dump(
            exclude={
                "id",
                "organization_id",
                "workspace_id",
                "created_by_user_id",
                "created_at",
            }
        )

        # Update the existing domain
        return await self.update_by_primary_key(
            OutboundDomain,
            primary_key_to_value={"id": existing_domain.id},
            column_to_update=update_data,
        )

    async def get_number_of_plan_included_outbound_domains_by_org_id(
        self,
        organization_id: UUID,
    ) -> int:
        """
        Get the number of plan included outbound domains for an organization.
        Should be up to 1 for standard plan.
        TODO: @Benson if/when we pivot off of Infraforge, we may need to change vendor='INFRAFORGE'.
        """
        stmt = text(
            """
            SELECT COUNT(*)
            FROM outbound_domain
            WHERE organization_id = :organization_id
                AND vendor = :vendor
                AND transaction_type = :transaction_type
                AND deleted_at IS NULL
                """
        ).bindparams(
            organization_id=organization_id,
            vendor=OutboundVendor.INFRAFORGE,
            transaction_type=AccountsReceivable.INCLUDED_IN_PLAN,
        )
        rows = await self.engine.one(stmt)
        return int(rows[0])


class DomainHealthRepository(GenericRepository):
    async def insert_domain_health(
        self,
        outbound_domain_id: UUID,
        mx_records: list[EntityDnsRecord],
        spf_records: list[EntityDnsRecord],
        dkim_record: list[EntityDnsRecord],
        dmarc_record: list[EntityDnsRecord],
        is_mock_record: bool = False,
    ) -> DomainHealth | None:
        return await self.insert(
            DomainHealth(
                domain_id=outbound_domain_id,
                mx_records=mx_records,
                spf_records=spf_records,
                dkim_record=dkim_record,
                dmarc_record=dmarc_record,
                is_mock_record=is_mock_record,
            )
        )

    async def get_domain_health_by_outbound_domain_id(
        self, outbound_domain_id: UUID
    ) -> DomainHealth | None:
        return await self._find_unique_by_column_values(
            DomainHealth, domain_id=outbound_domain_id
        )

    async def get_domain_health_by_outbound_domain_ids(
        self, outbound_domain_ids: list[UUID]
    ) -> dict[UUID, DomainHealth]:
        stmt = text(
            """
            SELECT * FROM domain_health
            WHERE domain_health.domain_id = ANY(:domain_ids)
            """,
        ).bindparams(
            domain_ids=outbound_domain_ids,
        )
        rows = await self.engine.all(stmt)
        domain_healths = [DomainHealth.model_validate(row._mapping) for row in rows]

        # Create a mapping of domain_id to domain_health
        return {
            domain_health.domain_id: domain_health for domain_health in domain_healths
        }

    async def update_domain_health(  # type: ignore[explicit-any]
        self, outbound_domain_id: UUID, column_to_update: dict[str, Any]
    ) -> DomainHealth | None:
        # First get the record by domain_id to find its primary key
        existing_health = await self.get_domain_health_by_outbound_domain_id(
            outbound_domain_id
        )
        if not existing_health:
            return None

        return await self.update_by_primary_key(
            DomainHealth,
            primary_key_to_value={"id": existing_health.id},
            column_to_update=column_to_update,
        )

    async def upsert_domain_health(
        self,
        outbound_domain_id: UUID,
        mx_records: list[EntityDnsRecord],
        spf_records: list[EntityDnsRecord],
        dkim_record: list[EntityDnsRecord],
        dmarc_record: list[EntityDnsRecord],
        is_mock_record: bool = False,
    ) -> DomainHealth | None:
        try:
            return await self.insert_domain_health(
                outbound_domain_id=outbound_domain_id,
                mx_records=mx_records,
                spf_records=spf_records,
                dkim_record=dkim_record,
                dmarc_record=dmarc_record,
                is_mock_record=is_mock_record,
            )
        except (UniqueViolationError, IntegrityError):
            update_data = {
                "mx_records": mx_records,
                "spf_records": spf_records,
                "dkim_record": dkim_record,
                "dmarc_record": dmarc_record,
            }
            return await self.update_domain_health(outbound_domain_id, update_data)
