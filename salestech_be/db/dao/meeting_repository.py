from typing import Any, Literal
from uuid import UUID, uuid4

from sqlalchemy import BindParameter
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.exc import MultipleResultsFound
from sqlalchemy.sql import text

from salestech_be.common.core_crm.sales_action import StandardSalesActionType
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.meeting import (
    BotProvider,
    BotStatusEvent,
    LiveTranscriptSession,
    Meeting,
    MeetingAnnotation,
    MeetingBot,
    MeetingBotStatus,
    MeetingBotUpdate,
    MeetingClip,
    MeetingReferenceIdentifiers,
    MeetingReferenceIdType,
    MeetingShare,
    MeetingShareVerification,
    RecallRecordingData,
    ScreenShareRange,
    TranscriptProvider,
)
from salestech_be.db.models.transcript import Transcript, TranscriptReferenceIdType
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


class MeetingRepository(GenericRepository):
    async def list_meetings_by_ids_untenanted(
        self, meeting_ids: list[UUID], exclude_deleted_or_archived: bool = True
    ) -> list[Meeting]:
        return await self._find_by_column_values(
            table_model=Meeting,
            id=meeting_ids,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
        )

    async def list_meetings_by_pipeline_id(
        self,
        organization_id: UUID,
        pipeline_id: UUID,
    ) -> list[Meeting]:
        return await self._find_by_column_values(
            table_model=Meeting,
            organization_id=organization_id,
            pipeline_id=pipeline_id,
        )

    async def list_meetings_by_account_id(
        self,
        organization_id: UUID,
        account_id: UUID,
    ) -> list[Meeting]:
        return await self._find_by_column_values(
            table_model=Meeting,
            organization_id=organization_id,
            account_id=account_id,
        )

    async def get_meeting_by_reference_id_and_type(
        self,
        organization_id: UUID,
        reference_id: str,
        reference_id_type: MeetingReferenceIdType,
    ) -> Meeting | None:
        return await self._find_unique_by_column_values(
            table_model=Meeting,
            organization_id=organization_id,
            reference_id=reference_id,
            reference_id_type=reference_id_type,
        )

    async def list_meetings_by_reference_id_and_type(
        self,
        organization_id: UUID,
        reference_ids: list[str],
        reference_id_type: MeetingReferenceIdType,
    ) -> list[Meeting]:
        return await self._find_by_column_values(
            table_model=Meeting,
            organization_id=organization_id,
            reference_id=reference_ids,
            reference_id_type=reference_id_type,
        )

    async def get_meeting_by_id(
        self,
        organization_id: UUID,
        meeting_id: UUID,
    ) -> Meeting | None:
        return await self._find_unique_by_column_values(
            table_model=Meeting,
            organization_id=organization_id,
            id=meeting_id,
        )

    async def get_meeting_by_consent_id(self, consent_id: UUID) -> list[Meeting]:
        return await self._find_by_column_values(
            table_model=Meeting, consent_id=consent_id
        )

    async def get_meeting_for_external_bot_id(
        self, external_meeting_bot_id: str, bot_provider: BotProvider
    ) -> Meeting | None:
        sql_stmt = text(
            """
            SELECT m.*
            FROM meeting_bot as mb
            JOIN meeting as m on mb.meeting_id = m.id
            WHERE mb.external_meeting_bot_id = :external_meeting_bot_id
            AND mb.provider = :bot_provider
            """,
        ).bindparams(
            external_meeting_bot_id=external_meeting_bot_id, bot_provider=bot_provider
        )
        row = await self.engine.first_or_none(sql_stmt)
        return Meeting.from_row(row) if row else None

    async def list_meetings_by_organization_id(
        self, *, organization_id: UUID, meeting_ids: list[UUID] | None
    ) -> list[Meeting]:
        sql_stmt = text(
            """
            SELECT * FROM meeting WHERE organization_id = :organization_id
            AND deleted_at IS NULL
            AND (:meeting_ids :: uuid[] IS NULL OR id = any(:meeting_ids))
            """
        ).bindparams(organization_id=organization_id, meeting_ids=meeting_ids)
        rows = await self.engine.all(sql_stmt)
        return await Meeting.bulk_from_rows(rows=rows)

    async def list_meetings_by_user_id(
        self, *, organization_id: UUID, user_id: UUID
    ) -> list[Meeting]:
        # TODO: (tian): check if we need to have this union and if we need to join with
        # user_calendar_event table in the first place
        sql_stmt = text(
            """
            SELECT m.* FROM meeting m join user_calendar_event e on m.reference_id  = e.group_key
            WHERE e.user_id = :user_id and e.organization_id = :organization_id
            AND m.organization_id = e.organization_id
            AND m.deleted_at is NULL

            UNION

            SELECT * FROM meeting
            WHERE organization_id = :organization_id AND created_by_user_id = :user_id
            AND reference_id_type = 'voice_v2'
            """
        ).bindparams(organization_id=organization_id, user_id=user_id)

        rows = await self.engine.all(sql_stmt)
        return await Meeting.bulk_from_rows(rows=rows)

    async def create_meeting_for_reschedule(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        organization_id: UUID,
        existing_meeting_id: UUID,
        existing_meeting_fields_to_update: dict[str, Any],
        rescheduled_meeting_fields_to_update: dict[str, Any],
    ) -> tuple[Meeting | None, Meeting | None]:
        existing_meeting = await self.find_by_tenanted_primary_key(
            table_model=Meeting, organization_id=organization_id, id=existing_meeting_id
        )
        if not existing_meeting:
            return None, None

        async with self.engine.begin():
            updated_existing_meeting = await self.update_by_tenanted_primary_key(
                table_model=Meeting,
                organization_id=organization_id,
                primary_key_to_value={"id": existing_meeting_id},
                column_to_update=existing_meeting_fields_to_update,
            )
            if not updated_existing_meeting:
                return None, None

            new_meeting_to_insert = existing_meeting.copy(
                update=rescheduled_meeting_fields_to_update
            )
            rescheduled_meeting = await self.insert(new_meeting_to_insert)

            return updated_existing_meeting, rescheduled_meeting

    async def list_meetings_with_calendar_time_mismatch(
        self, days_window: int
    ) -> list[MeetingReferenceIdentifiers]:
        sql_stmt = text(
            """
            SELECT distinct e.group_key, e.organization_id
            FROM meeting m
            JOIN user_calendar_event e ON e.group_key = m.reference_id
            JOIN user_integration_connector c on e.user_id = c.user_id
            WHERE m.reference_id_type = 'user_calendar_event'
            AND m.organization_id = e.organization_id
            AND e.organization_id = m.organization_id
            AND m.status != 'canceled'
            AND e.status != 'cancelled'
            AND m.starts_at != e.starts_at
            AND e.starts_at > now() - make_interval(days => :days_window)
            AND e.meeting_id is not null
            AND m.updated_at < now() - interval '10 minute'
            AND c.connector_provider = 'NYLAS'
            AND c.status = 'CONNECTED'
            AND m.deleted_at is null
            AND c.deleted_at is null
            """
        ).bindparams(days_window=days_window)
        rows = await self.engine.all(sql_stmt)
        return [
            MeetingReferenceIdentifiers(
                reference_id=row.group_key,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                organization_id=row.organization_id,
            )
            for row in rows
        ]

    async def list_meeting_bots_with_meeting_time_mismatch(
        self, days_window: int, join_offset_seconds: int
    ) -> list[MeetingBot]:
        sql_stmt = text(
            """
            SELECT b.*
            FROM meeting_bot b
            JOIN meeting m ON m.id = b.meeting_id
            WHERE m.organization_id = b.organization_id
            AND m.status != 'canceled'
            AND b.status = 'scheduled'
            AND m.starts_at > now() - make_interval(days => :days_window)
            AND ABS(EXTRACT(EPOCH FROM (m.starts_at - b.scheduled_at))) > :join_offset_seconds;
            """
        ).bindparams(days_window=days_window, join_offset_seconds=join_offset_seconds)
        rows = await self.engine.all(sql_stmt)
        return await MeetingBot.bulk_from_rows(rows=rows)

    async def list_meeting_urls_with_extra_scheduled_bots(self) -> dict[str, int]:
        sql_stmt = text(
            """
            SELECT m.reference_id, m.meeting_url, m.organization_id, b.scheduled_at, count(*) as bot_count
            FROM meeting m JOIN meeting_bot b on m.id = b.meeting_id
            WHERE b.status = 'scheduled'
            AND m.starts_at > now()
            GROUP BY 1, 2, 3, 4 having count(*) > 1;
            """
        )
        rows = await self.engine.all(sql_stmt)
        return {row.meeting_url: row.bot_count for row in rows}

    async def list_overdue_analyzing_meetings(
        self, minutes_window: int
    ) -> list[Meeting]:
        sql_stmt = text(
            """
            SELECT m.*
            FROM meeting m
            WHERE
            m.status = 'analyzing'
            AND m.deleted_at IS NULL
            AND m.ended_at < now() - make_interval(mins => :minutes_window)
            """
        ).bindparams(minutes_window=minutes_window)
        rows = await self.engine.all(sql_stmt)
        return await Meeting.bulk_from_rows(rows=rows)

    async def list_overdue_active_meetings(self, minutes_window: int) -> list[Meeting]:
        sql_stmt = text(
            """
            SELECT m.*
            FROM meeting m
            WHERE
            m.status = 'active'
            AND m.deleted_at IS NULL
            AND m.ends_at < now() - make_interval(mins => :minutes_window)
            """
        ).bindparams(minutes_window=minutes_window)
        rows = await self.engine.all(sql_stmt)
        return await Meeting.bulk_from_rows(rows=rows)

    async def get_meeting_bot_by_realtime_event_token(
        self, realtime_event_token: UUID
    ) -> MeetingBot | None:
        return await self._find_unique_by_column_values(
            table_model=MeetingBot,
            realtime_event_token=realtime_event_token,
        )

    async def find_meeting_bots_by_organization_id(
        self, organization_id: UUID, meeting_ids: list[UUID]
    ) -> list[MeetingBot]:
        if not meeting_ids:
            return []
        return await self._find_by_column_values(
            table_model=MeetingBot,
            organization_id=organization_id,
            meeting_id=meeting_ids,
        )

    async def find_future_scheduled_meeting_bots(
        self, organization_id: UUID
    ) -> list[MeetingBot]:
        sql_stmt = text(
            """
            SELECT mb.*
            FROM meeting_bot mb
            WHERE
            mb.organization_id = :organization_id
            AND mb.deleted_at IS NULL
            AND mb.status = 'scheduled'
            AND mb.scheduled_at > now()
            """
        ).bindparams(organization_id=organization_id)
        rows = await self.engine.all(sql_stmt)
        return await MeetingBot.bulk_from_rows(rows=rows)

    async def find_future_meeting_bots_by_meeting_user_id(
        self,
        organization_id: UUID,
        user_id: UUID,
    ) -> list[MeetingBot]:
        sql_stmt = text(
            """
            SELECT mb.*
            FROM meeting m
            JOIN meeting_bot mb on m.id = mb.meeting_id
            WHERE
            m.organization_id = :organization_id
            AND mb.organization_id = :organization_id
            AND m.owner_user_id = :user_id
            AND m.deleted_at IS NULL
            AND mb.deleted_at IS NULL
            AND mb.scheduled_at > now()
            """
        ).bindparams(organization_id=organization_id, user_id=user_id)
        rows = await self.engine.all(sql_stmt)
        return await MeetingBot.bulk_from_rows(rows=rows)

    async def get_meeting_bot_by_external_id(
        self, external_meeting_bot_id: str, provider: BotProvider
    ) -> MeetingBot | None:
        return await self._find_unique_by_column_values(
            table_model=MeetingBot,
            external_meeting_bot_id=external_meeting_bot_id,
            provider=provider,
        )

    async def get_meeting_bot_by_external_id_and_meeting_id(
        self, external_meeting_bot_id: str, meeting_id: UUID
    ) -> MeetingBot | None:
        return await self._find_unique_by_column_values(
            table_model=MeetingBot,
            external_meeting_bot_id=external_meeting_bot_id,
            meeting_id=meeting_id,
        )

    async def find_meeting_bots_by_meeting(
        self, organization_id: UUID, meeting_id: UUID
    ) -> list[MeetingBot]:
        return await self._find_by_column_values(
            MeetingBot, meeting_id=meeting_id, organization_id=organization_id
        )

    async def find_meeting_bots_by_meeting_url_and_time(
        self,
        organization_id: UUID,
        meeting_id: UUID,
        meeting_url: str,
        scheduled_at: ZoneRequiredDateTime,
    ) -> list[MeetingBot]:
        return await self._find_by_column_values(
            MeetingBot,
            meeting_id=meeting_id,
            organization_id=organization_id,
            meeting_url=meeting_url,
            scheduled_at=scheduled_at,
        )

    async def get_live_transcript_session(
        self, organization_id: UUID, meeting_id: UUID, user_id: UUID
    ) -> LiveTranscriptSession | None:
        return await self._find_unique_by_column_values(
            table_model=LiveTranscriptSession,
            meeting_id=meeting_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def update_recording_id_by_bot_id(
        self,
        external_meeting_bot_id: str,
        recording_id: str,
    ) -> MeetingBot | None:
        stmt = await GenericRepository._update_by_column_values_stmt(
            MeetingBot,
            column_value_to_query={"external_meeting_bot_id": external_meeting_bot_id},
            column_to_update={"external_recording_id": recording_id},
        )

        result = await self.engine.execute(stmt)

        try:
            row = result.one_or_none()
        except MultipleResultsFound:
            logger.error(
                f"Multiple meeting bots found with "
                f"external_meeting_bot_id={external_meeting_bot_id}",
            )
            row = result.first()

        return MeetingBot.from_row(row) if row else None

    async def append_bot_status_history_and_update_status(
        self,
        external_meeting_bot_id: str,
        bot_provider: BotProvider,
        status_event: BotStatusEvent,
        status: MeetingBotStatus,
    ) -> MeetingBot | None:
        stmt = text(
            """
            UPDATE meeting_bot
            SET status_history = jsonb_set(
                COALESCE(status_history, '{"status_history": []}'::jsonb),
                '{status_history}',
                COALESCE(status_history->'status_history', '[]'::jsonb) || :status_event ::jsonb
            ),
            status = :status,
            updated_at = now()
            WHERE external_meeting_bot_id = :external_id
            AND provider = :bot_provider
            RETURNING *;
            """
        ).bindparams(
            status_event=status_event.json(),
            status=status,
            external_id=external_meeting_bot_id,
            bot_provider=bot_provider,
        )

        result = await self.engine.execute(stmt)
        try:
            row = result.one_or_none()
        except MultipleResultsFound:
            logger.error(
                f"Multiple meeting bots found with "
                f"external_meeting_bot_id={external_meeting_bot_id}",
            )
            row = result.first()

        return MeetingBot.from_row(row) if row else None

    async def append_bot_status_history(
        self,
        external_meeting_bot_id: str,
        bot_provider: BotProvider,
        status_event: BotStatusEvent,
    ) -> MeetingBot | None:
        stmt = text(
            """
            UPDATE meeting_bot
            SET status_history = jsonb_set(
                COALESCE(status_history, '{"status_history": []}'::jsonb),
                '{status_history}',
                COALESCE(status_history->'status_history', '[]'::jsonb) || :status_event ::jsonb
            ),
            updated_at = now()
            WHERE external_meeting_bot_id = :external_id
            AND provider = :bot_provider
            RETURNING *;
            """
        ).bindparams(
            status_event=status_event.json(),
            external_id=external_meeting_bot_id,
            bot_provider=bot_provider,
        )

        result = await self.engine.execute(stmt)
        try:
            row = result.one_or_none()
        except MultipleResultsFound:
            logger.error(
                f"Multiple meeting bots found with "
                f"external_meeting_bot_id={external_meeting_bot_id}",
            )
            row = result.first()

        return MeetingBot.from_row(row) if row else None

    async def update_meeting_bot_by_external_id(
        self,
        organization_id: UUID,
        external_meeting_bot_id: str,
        meeting_bot_update: MeetingBotUpdate,
    ) -> MeetingBot | None:
        return await self._update_unique_by_column_values(
            MeetingBot,
            column_value_to_query={
                "external_meeting_bot_id": external_meeting_bot_id,
                "organization_id": organization_id,
            },
            column_to_update=meeting_bot_update,
        )

    async def update_meeting_bot_recording_data(
        self,
        organization_id: UUID,
        external_meeting_bot_id: str,
        external_media_url: str | None,
        external_media_retention_ended_at: ZoneRequiredDateTime | None,
        recording_metadata: RecallRecordingData | None,
        screen_share_ranges: list[ScreenShareRange] | None,
        updated_at: ZoneRequiredDateTime,
        completed_at: ZoneRequiredDateTime,
    ) -> MeetingBot | None:
        return await self._update_unique_by_column_values(
            MeetingBot,
            column_value_to_query={
                "external_meeting_bot_id": external_meeting_bot_id,
                "provider": BotProvider.RECALLAI,
                "organization_id": organization_id,
            },
            column_to_update={
                "external_media_url": external_media_url,
                "external_media_retention_ended_at": external_media_retention_ended_at,
                "recording_metadata": recording_metadata,
                "screen_share_ranges": screen_share_ranges,
                "updated_at": updated_at,
                "completed_at": completed_at,
            },
        )

    async def find_transcript_for_meeting_bot(
        self, organization_id: UUID, meeting_bot_id: UUID
    ) -> Transcript | None:
        return await self._find_unique_by_column_values(
            table_model=Transcript,
            organization_id=organization_id,
            reference_id=str(meeting_bot_id),
            reference_id_type=TranscriptReferenceIdType.MEETING_BOT,
        )

    async def create_meeting_bot_transcript_initial_rows(
        self,
        meeting_id: UUID,
        external_bot_id: str,
        bot_provider: BotProvider,
        meeting_bot_name: str | None,
        transcript_provider: TranscriptProvider,
        organization_id: UUID,
        scheduled_at: ZoneRequiredDateTime,
        realtime_event_token: UUID,
        meeting_url: str,
    ) -> tuple[MeetingBot, Transcript]:
        time_now = zoned_utc_now()

        meeting_bot = MeetingBot(
            id=uuid4(),
            meeting_id=meeting_id,
            external_meeting_bot_id=external_bot_id,
            name=meeting_bot_name,
            created_at=time_now,
            updated_at=time_now,
            provider=bot_provider.value,
            organization_id=organization_id,
            scheduled_at=scheduled_at,
            status=MeetingBotStatus.PENDING,
            realtime_event_token=realtime_event_token,
            meeting_url=meeting_url,
        )
        transcript = Transcript(
            id=uuid4(),
            reference_id=str(meeting_bot.id),
            reference_id_type=TranscriptReferenceIdType.MEETING_BOT,
            organization_id=organization_id,
            provider=transcript_provider.value,
            created_at=time_now,
            updated_at=time_now,
        )
        meeting_bot_stmt = self._get_table_model_standard_insert_stmt(meeting_bot)
        transcript_stmt = self._get_table_model_standard_insert_stmt(transcript)
        async with self.engine.begin():
            meeting_bot_result = await self.engine.execute(meeting_bot_stmt)
            transcript_result = await self.engine.execute(transcript_stmt)

        meeting_bot_row = meeting_bot_result.one()
        transcript_row = transcript_result.one()

        return (
            MeetingBot.from_row(meeting_bot_row),
            Transcript.from_row(transcript_row),
        )

    async def find_annotations_by_meeting(
        self, organization_id: UUID, meeting_id: UUID
    ) -> list[MeetingAnnotation]:
        return await self._find_by_column_values(
            table_model=MeetingAnnotation,
            meeting_id=meeting_id,
            organization_id=organization_id,
        )

    async def find_meeting_clips_by_meeting(
        self,
        *,
        organization_id: UUID,
        meeting_id: UUID,
        exclude_deleted_or_archived: bool = True,
    ) -> list[MeetingClip]:
        return await self._find_by_column_values(
            table_model=MeetingClip,
            meeting_id=meeting_id,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
        )

    async def find_meetings_by_participant_contact_id(
        self,
        field_name: Literal["invitees", "attendees"],
        contact_id: UUID,
        organization_id: UUID,
        email: str | None = None,
        account_id: UUID | None = None,
    ) -> list[Meeting]:
        # this is to match contact_id, account_id and email in participants
        account_id_filter = (
            " and (element ->> 'account_id')::UUID = :account_id" if account_id else ""
        )
        email_filter = (
            " and (element ->> 'contact_email')::text = :email" if email else ""
        )
        stmt = text(
            f"""
            SELECT *
            FROM meeting
            WHERE EXISTS (
                SELECT 1
                FROM jsonb_array_elements({field_name}) AS element
                WHERE (element->>'contact_id')::uuid = :contact_id
                {account_id_filter}
                {email_filter}
            )
            AND organization_id = :organization_id
            AND deleted_at IS NULL
        """  # noqa: S608
        ).bindparams(contact_id=contact_id, organization_id=organization_id)

        if account_id:
            stmt = stmt.bindparams(account_id=account_id)
        if email:
            stmt = stmt.bindparams(email=email)

        result = await self.engine.all(stmt)
        return await Meeting.bulk_from_rows(rows=result)

    async def find_meetings_by_account_id(
        self,
        account_id: UUID,
        organization_id: UUID,
    ) -> list[Meeting]:
        # this is to match account_id of meeting object
        return await self._find_by_column_values(
            table_model=Meeting,
            account_id=account_id,
            organization_id=organization_id,
        )

    async def find_meetings_by_participant_account_id(
        self,
        field_name: Literal["invitees", "attendees"],
        account_id: UUID,
        organization_id: UUID,
    ) -> list[Meeting]:
        # this is to match contact_id and account_id in participants
        stmt = text(
            f"""
            SELECT *
            FROM meeting
            WHERE EXISTS (
                SELECT 1
                FROM jsonb_array_elements({field_name}) AS element
                WHERE (element->>'account_id')::UUID = :account_id
            )
            AND organization_id = :organization_id
            AND deleted_at IS NULL
        """  # noqa: S608
        ).bindparams(account_id=account_id, organization_id=organization_id)

        rows = await self.engine.all(stmt)
        return await Meeting.bulk_from_rows(rows=rows)

    async def find_meetings_by_participant_contact_id_and_account_id(
        self,
        contact_id: UUID,
        account_id: UUID,
        organization_id: UUID,
    ) -> list[Meeting]:
        # this is to match contact_id in participants and account_id of meeting object
        stmt = text(
            """
            SELECT *
            FROM meeting
            WHERE EXISTS (
                SELECT 1
                FROM jsonb_array_elements(invitees) AS element
                WHERE (element->>'contact_id')::UUID = :contact_id
            )
            AND organization_id = :organization_id
            AND deleted_at IS NULL
            AND account_id = :account_id
        """
        ).bindparams(
            contact_id=contact_id,
            account_id=account_id,
            organization_id=organization_id,
        )

        rows = await self.engine.all(stmt)
        return await Meeting.bulk_from_rows(rows=rows)

    async def find_meeting_share_by_meeting_id(
        self,
        organization_id: UUID,
        meeting_id: UUID,
        exclude_deleted_or_archived: bool = True,
    ) -> list[MeetingShare]:
        return await self._find_by_column_values(
            table_model=MeetingShare,
            organization_id=organization_id,
            meeting_id=meeting_id,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
        )

    async def find_meeting_share_verification_by_share_id_and_email(
        self, organization_id: UUID, share_id: UUID, email: str
    ) -> MeetingShareVerification | None:
        return await self._find_unique_by_column_values(
            table_model=MeetingShareVerification,
            organization_id=organization_id,
            share_id=share_id,
            email=email,
        )

    async def list_earliest_meeting_id_by_pipeline(
        self, organization_id: UUID, meeting_ids: list[UUID] | None
    ) -> dict[UUID, UUID]:
        """Returns a mapping of pipeline_id to the earliest meeting_id for that pipeline.

        Args:
            organization_id: The organization to query meetings for
            meeting_ids: Optional list of meeting IDs to filter by

        Returns:
            Dictionary mapping pipeline_id to the meeting_id of the earliest meeting in that pipeline
        """
        sql_stmt = text(
            """
            WITH RankedMeetings AS (
                SELECT
                    id,
                    pipeline_id,
                    ROW_NUMBER() OVER (PARTITION BY pipeline_id ORDER BY starts_at) as rn
                FROM meeting m
                WHERE m.organization_id = :organization_id
                AND m.pipeline_id IS NOT NULL
                AND m.deleted_at IS NULL
                AND (:meeting_ids :: uuid[] IS NULL OR m.id = any(:meeting_ids))
            )
            SELECT id, pipeline_id
            FROM RankedMeetings
            WHERE rn = 1
            """
        ).bindparams(organization_id=organization_id, meeting_ids=meeting_ids)

        rows = await self.engine.all(sql_stmt)
        return {row.pipeline_id: row.id for row in rows}

    async def list_meetings_by_contact_id(
        self,
        organization_id: UUID,
        contact_id: UUID,
    ) -> list[Meeting]:
        """List meetings where the given contact ID appears in invitees.
        Args:
            organization_id: Organization ID to filter by
            contact_id: Contact ID to search for in invitees

        Returns:
            List of meetings where the contact appears in invitees
        """
        sql_stmt = text(
            """
            SELECT DISTINCT m.*
            FROM meeting m
            WHERE m.organization_id = :organization_id
            AND m.deleted_at IS NULL
            AND (
                EXISTS (
                    SELECT 1
                    FROM jsonb_array_elements(m.invitees) AS invitee
                    WHERE (invitee->>'contact_id')::uuid = :contact_id
                )
            )
            """
        ).bindparams(
            organization_id=organization_id,
            contact_id=contact_id,
        )

        rows = await self.engine.all(sql_stmt)
        return await Meeting.bulk_from_rows(rows=rows)

    async def list_meetings_by_contact_ids(
        self,
        organization_id: UUID,
        contact_ids: list[UUID],
        exclude_meeting_ids: set[UUID] | None = None,
    ) -> list[Meeting]:
        """List meetings where the given contact ID appears in invitees.
        Args:
            organization_id: Organization ID to filter by
            contact_ids: Contact IDs to search for in invitees
            exclude_meeting_ids: Meeting IDs to exclude

        Returns:
            List of meetings where any of the contact appears in invitees

        SQL should look like this:
        select *
        from meeting
        where organization_id = 'b3c5bc5d-1eae-4586-b9bd-db8486e6b689'
          and deleted_at is null
          and ( invitees @> '[{"contact_id": "91f60c0a-0fc8-43c9-a515-fbee4263149c"}]'::jsonb
                or invitees @> '[{"contact_id": "ff153e65-9cd2-46f9-a45c-b56155b40376"}]'::jsonb
              );

        NOTE: in the sql statement, following piece is the correct way to leverage GIN (PSQL's inverted index on jsonb)
        e.g. `invitees @> '[{"contact_id": "91f60c0a-0fc8-43c9-a515-fbee4263149c"}]'::jsonb`
        This will essentially check if the left hand column CONTAINS the right hand jsonb value.
        """
        if not contact_ids:
            return []
        # chunk contact_ids, in case of exceeding maximum number of parameters
        max_contact_ids = 500
        contact_id_chunks: list[list[UUID]] = [
            contact_ids[i : i + max_contact_ids]
            for i in range(0, len(contact_ids), max_contact_ids)
        ]

        meetings: list[Meeting] = []
        base_stmt = f"""
        select * from meeting
                where organization_id = :organization_id
                  and deleted_at is null
                  {" and id != any(:exclude_meeting_ids)" if exclude_meeting_ids else ""}
        """  # noqa: S608
        common_params: list[BindParameter[Any]] = [  # type: ignore[explicit-any] # TODO: fix-any-annotation
            BindParameter(key="organization_id", value=organization_id),
        ]
        if exclude_meeting_ids:
            common_params.append(
                BindParameter(
                    key="exclude_meeting_ids", value=list(exclude_meeting_ids)
                )
            )
        contact_id_idx = 0
        for _contact_id_chunk in contact_id_chunks:
            if not _contact_id_chunk:
                continue
            _contact_selections: list[str] = []
            _local_params: list[BindParameter[Any]] = [*common_params]  # type: ignore[explicit-any] # TODO: fix-any-annotation
            for _contact_id in _contact_id_chunk:
                _param = f"contact_id_{contact_id_idx}"
                _contact_selections.append(f"invitees @> :{_param}")
                _local_params.append(
                    BindParameter(
                        key=_param, value=[{"contact_id": _contact_id}], type_=JSONB
                    )
                )
                contact_id_idx += 1
            _contact_selection = " or ".join(_contact_selections)
            _sql_stmt = text(f"{base_stmt} and ({_contact_selection})").bindparams(
                *_local_params
            )
            _rows = await self.engine.all(_sql_stmt)
            _meetings = await Meeting.bulk_from_rows(rows=_rows)
            meetings.extend(_meetings)
        return meetings

    async def list_meetings_by_primary_account_ids(
        self, organization_id: UUID, primary_account_ids: list[UUID]
    ) -> list[Meeting]:
        """List meetings where the given account ID appears in invitees.
        Args:
            organization_id: Organization ID to filter by
            primary_account_ids: Primary Account IDs to search for
        Returns:
            List of meetings where any of the primary account's contacts appears in invitees
        """
        if not primary_account_ids:
            return []
        account_match_stmt = text(
            """
            select * from meeting
            where organization_id = :organization_id
            and deleted_at is null
            and account_id = any(:primary_account_ids)
            """
        ).bindparams(
            organization_id=organization_id,
            primary_account_ids=primary_account_ids,
        )
        rows = await self.engine.all(account_match_stmt)
        meetings = await Meeting.bulk_from_rows(rows=rows)
        account_matched_meeting_ids = {meeting.id for meeting in meetings}
        account_match_stmt = text(
            """
            select distinct c.id from contact_account_association caa join contact c
                on caa.contact_id = c.id and c.organization_id = caa.organization_id
                where c.organization_id = :organization_id
                and caa.account_id = any(:primary_account_ids)
                and caa.is_primary
                and c.archived_at is null
                and caa.archived_at is null
            """
        ).bindparams(
            organization_id=organization_id,
            primary_account_ids=primary_account_ids,
        )
        contact_id_result = await self.engine.all(account_match_stmt)
        contact_ids = {
            UUID(row.id) if not isinstance(row.id, UUID) else row.id
            for row in contact_id_result
        }
        contact_matched = await self.list_meetings_by_contact_ids(
            organization_id=organization_id,
            contact_ids=list(contact_ids),
            exclude_meeting_ids=set(account_matched_meeting_ids),
        )
        meetings.extend(contact_matched)
        return meetings

    async def put_meeting_sales_activity_types(
        self,
        *,
        organization_id: UUID,
        meeting_id: UUID,
        user_id: UUID,
        sales_activity_types: list[StandardSalesActionType] | None,
    ) -> Meeting | None:
        stmt = text(
            """
            update meeting
            set sales_action_types = :sales_activity_types,
                updated_at         = now()
            where organization_id = :organization_id
              and id = :meeting_id
            returning *
            """
        ).bindparams(
            organization_id=organization_id,
            meeting_id=meeting_id,
            sales_activity_types=sales_activity_types,
        )
        row = await self.engine.at_most_one(stmt)
        return Meeting.from_row(row) if row else None

    async def list_meetings_by_sales_action_types_for_pipeline(
        self,
        organization_id: UUID,
        sales_action_type_filters: list[list[StandardSalesActionType]],
        pipeline_id: UUID,
    ) -> list[Meeting]:
        """
        List meetings that have any of the specified sales action types.

        Args:
            organization_id: Organization ID to filter by
            sales_action_type_filters: List of sales action types to filter by
                Each list contains a set of sales action types to filter by
                e.g. [[StandardSalesActionType.INTRO], [StandardSalesActionType.FOLLOWUP]]
                will return meetings with either intro or followup sales action types
            pipeline_id: Pipeline ID to further filter results

        Returns:
            List of meetings matching the criteria
        """

        if not sales_action_type_filters:
            return []

        if all(
            len(sales_action_type_filter) == 0
            for sales_action_type_filter in sales_action_type_filters
        ):
            return []

        sales_action_type_filter_clauses: list[str] = []
        sales_action_type_filter_params: dict[str, list[StandardSalesActionType]] = {}
        for i, sales_action_type_filter in enumerate(sales_action_type_filters):
            if not sales_action_type_filter:
                continue
            param_name = f"sales_action_type_filter_{i}"
            sales_action_type_filter_params[param_name] = sales_action_type_filter
            sales_action_type_filter_clauses.append(
                f"sales_action_types @> :{param_name}"
            )
        sales_action_type_filter_clause = " OR ".join(sales_action_type_filter_clauses)

        query = f"""
            SELECT * FROM meeting
            WHERE organization_id = :organization_id
            AND deleted_at IS NULL
            AND pipeline_id = :pipeline_id
            AND ({sales_action_type_filter_clause})
        """  # noqa: S608
        stmt = text(query).bindparams(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            **sales_action_type_filter_params,
        )
        rows = await self.engine.all(stmt)
        return await Meeting.bulk_from_rows(rows=rows)
