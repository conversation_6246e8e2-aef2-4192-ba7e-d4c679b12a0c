import uuid
from typing import List

from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.reporting import (
    ReportingDataset,
    ReportingDatasetField,
    ReportingDatasetLineage,
    ReportingDatasetRelation,
    ReportingFunction,
    ReportingFunctionParameter,
    ReportingReport,
    ReportingDashboard,
)


class ReportingRepository(GenericRepository):
    async def get_reports(self) -> List[ReportingReport]:
        return await self._find_by_column_values(
            table_model=ReportingReport,
            exclude_deleted_or_archived=True,
        )

    async def find_report_by_id(
        self,
        report_id: uuid.UUID,
        organization_id: uuid.UUID,
    ) -> ReportingReport:
        return await self._find_unique_by_column_values_or_fail(
            table_model=ReportingReport,
            id=report_id,
            organization_id=organization_id,
        )

    async def get_dashboards(self) -> List[ReportingDashboard]:
        return await self._find_by_column_values(
            table_model=ReportingDashboard,
            exclude_deleted_or_archived=True,
        )

    # Dataset methods
    async def find_dataset_by_id(
        self,
        dataset_id: uuid.UUID,
        organization_id: uuid.UUID,
    ) -> ReportingDataset:
        return await self._find_unique_by_column_values_or_fail(
            table_model=ReportingDataset,
            id=dataset_id,
            organization_id=organization_id,
        )

    async def get_datasets(
        self,
        organization_id: uuid.UUID,
    ) -> List[ReportingDataset]:
        return await self._find_by_column_values(
            table_model=ReportingDataset,
            organization_id=organization_id,
            exclude_deleted_or_archived=True,
        )

    async def create_dataset(
        self,
        dataset: ReportingDataset,
    ) -> ReportingDataset:
        return await self.insert(dataset)

    async def update_dataset(
        self,
        dataset: ReportingDataset,
    ) -> ReportingDataset:
        return await self.update(dataset)

    # Dataset Field methods
    async def get_dataset_fields(
        self,
        dataset_id: uuid.UUID,
    ) -> List[ReportingDatasetField]:
        return await self._find_by_column_values(
            table_model=ReportingDatasetField,
            dataset_id=dataset_id,
            exclude_deleted_or_archived=True,
        )

    async def create_dataset_field(
        self,
        field: ReportingDatasetField,
    ) -> ReportingDatasetField:
        return await self.insert(field)

    # Dataset Lineage methods
    async def get_dataset_lineage(
        self,
        child_dataset_id: uuid.UUID,
    ) -> List[ReportingDatasetLineage]:
        return await self._find_by_column_values(
            table_model=ReportingDatasetLineage,
            child_dataset_id=child_dataset_id,
            exclude_deleted_or_archived=True,
        )

    async def create_dataset_lineage(
        self,
        lineage: ReportingDatasetLineage,
    ) -> ReportingDatasetLineage:
        return await self.insert(lineage)

    # Dataset Relation methods
    async def get_dataset_relations(
        self,
        source_dataset_id: uuid.UUID,
    ) -> List[ReportingDatasetRelation]:
        return await self._find_by_column_values(
            table_model=ReportingDatasetRelation,
            source_dataset_id=source_dataset_id,
            exclude_deleted_or_archived=True,
        )

    async def create_dataset_relation(
        self,
        relation: ReportingDatasetRelation,
    ) -> ReportingDatasetRelation:
        return await self.insert(relation)

    # Function methods
    async def get_functions(
        self,
        is_active: bool = True,
    ) -> List[ReportingFunction]:
        return await self._find_by_column_values(
            table_model=ReportingFunction,
            is_active=is_active,
        )

    async def get_function_by_id(
        self,
        function_id: uuid.UUID,
    ) -> ReportingFunction | None:
        return await self._find_unique_by_column_values(
            table_model=ReportingFunction,
            id=function_id,
        )

    async def get_function_parameters(
        self,
        function_id: uuid.UUID,
    ) -> List[ReportingFunctionParameter]:
        return await self._find_by_column_values(
            table_model=ReportingFunctionParameter,
            function_id=function_id,
        )
