from typing import Any, Literal, TypeVar
from uuid import UUID

from sqlalchemy import text

from salestech_be.common.results import <PERSON>ursor
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.core.base import DBModel
from salestech_be.db.models.core.utils import cursor_to_offset_limit_with_overflow
from salestech_be.db.models.task import (
    GiantTask,
    Task,
    TaskPriority,
    TaskReference,
    TaskStatus,
    TaskTemplate,
    TaskType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime

logger = get_logger(__name__)
T = TypeVar("T", bound=DBModel)


def create_prefixed_model(
    target_class: type[T], model_mappings: dict[str, DBModel]
) -> T:
    """
    Creates a new model instance with fields from multiple source models with prefixes.

    Args:
        target_class: The target model class to instantiate
        model_mappings: Dict mapping prefixes to model_instance

    Returns:
        Instance of target_class with all prefixed fields from source models
    """
    fields = {}

    for prefix, model in model_mappings.items():
        # Get all fields from the model that have values
        for field_name in model.__dict__:
            if field_name.startswith("_"):
                continue

            if hasattr(model, field_name):
                # Add the field to the result with the prefix
                fields[f"{prefix}_{field_name}"] = getattr(model, field_name)

    # Create instance of target class
    return target_class(**fields)


def generate_prefixed_sql_fields(
    model_class: type[DBModel],
    table_alias: str,
    prefix: str,
    target_model: type[DBModel] | None = None,
) -> str:
    """
    Generates SQL field list with prefixed aliases based on model annotations.

    If target_model is provided, only generates fields that exist in the target model with the given prefix.

    Example:
        fields = generate_prefixed_sql_fields(Task, "t", "task", GiantTask)
        # Produces only the task fields that exist in GiantTask with task_ prefix
    """
    fields = []

    # If target model is provided, get all fields that start with the prefix
    target_field_names = set()
    if target_model:
        target_prefix = f"{prefix}_"
        target_field_names = {
            field_name[len(target_prefix) :]
            for field_name in target_model.__annotations__
            if field_name.startswith(target_prefix) and not field_name.startswith("_")
        }

    for field_name in model_class.__annotations__:
        if field_name.startswith("_"):
            continue

        # Only include fields that exist in the target model with this prefix
        if target_model and field_name not in target_field_names:
            continue

        fields.append(f"{table_alias}.{field_name} AS {prefix}_{field_name}")

    return ", ".join(fields)


def generate_jsonb_object_fields(
    model_class: type[DBModel],
    table_alias: str,
    prefix: str,
    target_model: type[DBModel] | None = None,
) -> str:
    """
    Generates fields suitable for jsonb_build_object from model annotations.
    Example: 'id', a.id, 'name', a.name

    Args:
        model_class: The model class to generate fields from
        table_alias: SQL alias for the table
        prefix: Prefix to look for in target_model fields
        target_model: Optional target model to filter fields

    Returns:
        String with comma-separated field pairs for jsonb_build_object
    """
    fields_list = []

    # If target model is provided, get all fields that start with the prefix
    target_field_names = set()
    if target_model:
        target_prefix = f"{prefix}_"
        target_field_names = {
            field_name[len(target_prefix) :]
            for field_name in target_model.__annotations__
            if field_name.startswith(target_prefix) and not field_name.startswith("_")
        }

    for field_name in model_class.__annotations__:
        if field_name.startswith("_"):
            continue

        # Only include fields that exist in the target model with this prefix
        if target_model and field_name not in target_field_names:
            continue

        # For jsonb_build_object we need 'field_name', table_alias.field_name pairs
        fields_list.append(f"'{field_name}', {table_alias}.{field_name}")

    return ", ".join(fields_list)


class TaskRepository(GenericRepository):
    async def find_all_tasks_by_organization_id(
        self,
        organization_id: UUID,
        exclude_deleted_or_archived: bool | None = True,
    ) -> list[Task]:
        return await self._find_by_column_values(
            Task,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
        )

    async def find_all_tasks_by_organization_id_and_ids(
        self,
        organization_id: UUID,
        task_ids: set[UUID],
    ) -> list[Task]:
        if not task_ids:
            return []
        stmt = text(
            """
        select * from public.task
        where id = any(:task_ids)
        and organization_id = :organization_id
        """,
        ).bindparams(task_ids=task_ids, organization_id=organization_id)

        rows = await self.engine.all(stmt)
        return await Task.bulk_from_rows(rows=rows)

    async def find_task_references_by_task_ids(
        self,
        task_ids: list[UUID],
        organization_id: UUID,
    ) -> list[TaskReference]:
        if len(task_ids) == 0:
            return []
        stmt = text(
            """
        select * from public.task_reference
        where task_id = any(:task_ids)
        and organization_id = :organization_id
        and deleted_at is null
        """,
        ).bindparams(task_ids=task_ids, organization_id=organization_id)

        rows = await self.engine.all(stmt)
        return await TaskReference.bulk_from_rows(rows=rows)

    async def find_tasks_by_reference_ids_and_type(
        self,
        reference_ids: list[str],
        reference_id_type: str,
        organization_id: UUID,
    ) -> list[Task]:
        if len(reference_ids) == 0:
            return []
        stmt = text(
            """
        select distinct t.* from public.task_reference r
        join public.task t on t.id = r.task_id
        where r.reference_id = any(:reference_ids)
        and r.reference_id_type = :reference_id_type
        and t.organization_id = :organization_id
        and r.organization_id = :organization_id
        and t.archived_at is null
        and r.deleted_at is null
        """,
        ).bindparams(
            reference_ids=reference_ids,
            organization_id=organization_id,
            reference_id_type=reference_id_type,
        )
        rows = await self.engine.all(stmt)
        return await Task.bulk_from_rows(rows=rows)

    async def find_user_tasks(
        self,
        user_id: UUID,
        organization_id: UUID,
        include_completed: bool = False,
        due_datetime_sort: Literal["asc", "desc"] | None = None,
    ) -> list[Task]:
        stmt = text(f"""\
        SELECT t.* FROM task t
        LEFT JOIN task_reference r ON t.id = r.task_id
        WHERE t.organization_id = :organization_id
            AND t.owner_user_id = :user_id
            AND t.archived_at IS NULL
            {"AND t.completed_at IS NULL" if not include_completed else ""}
            {"ORDER BY t.due_at " + due_datetime_sort if due_datetime_sort else ""}
        """).bindparams(  # noqa: S608
            organization_id=organization_id,
            user_id=user_id,
        )
        rows = await self.engine.all(stmt)
        return [Task.from_row(row) for row in rows]

    async def delete_task_references(
        self,
        task_id: UUID,
        organization_id: UUID,
        reference_id: str,
        reference_id_type: str,
        deleted_by_user_id: UUID,
        deleted_at: ZoneRequiredDateTime,
    ) -> list[TaskReference]:
        return await self._update_by_column_values(
            table_model=TaskReference,
            column_value_to_query={
                "task_id": task_id,
                "organization_id": organization_id,
                "reference_id": reference_id,
                "reference_id_type": reference_id_type,
            },
            column_to_update={
                "deleted_by_user_id": deleted_by_user_id,
                "deleted_at": deleted_at,
            },
        )

    async def list_giant_tasks(  # noqa: C901, PLR0912, PLR0915
        self,
        only_include_task_ids: list[UUID] | None = None,
        owner_user_id: UUID | None = None,
        owner_user_ids_in: list[UUID] | None = None,
        organization_id: UUID | None = None,
        cursor: Cursor | None = None,
        status_ne: str | None = None,
        due_at_gt: ZoneRequiredDateTime | None = None,
        due_at_lt: ZoneRequiredDateTime | None = None,
        due_at_blank: bool | None = None,
        account_id: UUID | None = None,
        account_ids_in: list[UUID] | None = None,
        contact_ids: list[UUID] | None = None,
        pipeline_id: UUID | None = None,
        pipeline_ids_in: list[UUID] | None = None,
        require_pipeline_id: bool = False,
        pipeline_stage_select_list_value_ids_in: list[UUID] | None = None,
        meeting_id: UUID | None = None,
        email_thread_ids: list[UUID] | None = None,
        sequence_id: UUID | None = None,
        sequence_step_id_in: list[UUID] | None = None,
        priority_in: list[TaskPriority] | None = None,
        type_in: list[TaskType] | None = None,
        status_in: list[TaskStatus] | None = None,
        sorting_field: Literal["due_at", "created_at", "updated_at"] | None = None,
        sorting_direction: Literal["asc", "desc"] | None = None,
        null_first: bool = False,
    ) -> list[GiantTask]:
        offset, limit = (
            cursor_to_offset_limit_with_overflow(cursor) if cursor else (None, None)
        )

        # Build task filtering conditions
        where_clauses = [
            "task.organization_id = :organization_id",
            "task.archived_at IS NULL",
        ]

        if only_include_task_ids:
            where_clauses.append("task.id = any(:only_include_task_ids)")

        # Add owner user id(s) filter.
        if owner_user_id or owner_user_ids_in:
            where_clauses.append("task.owner_user_id = any(:owner_user_ids)")

        # Add status filter (NE - Not Equal)
        if status_ne:
            where_clauses.append("task.status != :status_ne")

        # Add due date filters
        if due_at_gt:
            where_clauses.append("task.due_at > :due_at_gt")

        if due_at_lt:
            where_clauses.append("task.due_at < :due_at_lt")

        if due_at_blank and due_at_blank is True:
            where_clauses.append("task.due_at IS NULL")

        if priority_in:
            where_clauses.append("task.priority = any(:priority_in)")

        if type_in:
            where_clauses.append("task.type = any(:type_in)")

        if status_in:
            where_clauses.append("task.status = any(:status_in)")

        task_where_clause = " AND ".join(where_clauses)

        # Pagination
        limit_clause = f"LIMIT {limit}" if limit else ""
        offset_clause = f"OFFSET {offset}" if offset else ""

        # Sorting
        order_by_clause = ""
        if sorting_field:
            # Determine null handling based on null_first parameter
            null_sort = "NULLS FIRST" if null_first else "NULLS LAST"
            sort_dir = sorting_direction or "asc"
            order_by_clause = (
                f"ORDER BY t.{sorting_field} {sort_dir.upper()} {null_sort}"
            )

        # Generate field list with task_ prefix
        task_fields = generate_prefixed_sql_fields(Task, "t", "task", GiantTask)

        # For account filtering
        if account_id or account_ids_in:
            where_clauses.append("""
                EXISTS (
                    SELECT 1
                    FROM task_reference tr
                    WHERE
                    tr.task_id = task.id
                    AND tr.reference_id_type = 'ACCOUNT_ID'
                    AND tr.reference_id::uuid = ANY(:account_ids)
                    AND tr.deleted_at IS NULL
                )
            """)

        if contact_ids:
            where_clauses.append("""
                EXISTS (
                    SELECT 1
                    FROM task_reference tr
                    WHERE
                    tr.task_id = task.id
                    AND tr.reference_id_type = 'CONTACT_ID'
                    AND tr.reference_id::uuid = ANY(:contact_ids)
                    AND tr.deleted_at IS NULL
                )
            """)

        if pipeline_id or pipeline_ids_in or require_pipeline_id:
            # If specific pipeline IDs are specified, filter by them
            # Otherwise if just require_pipeline_id is True, check for any pipeline
            pipeline_filter = ""
            if pipeline_id or pipeline_ids_in:
                pipeline_filter = "AND tr.reference_id::uuid = ANY(:pipeline_ids)"

            where_clauses.append(f"""
                EXISTS (
                    SELECT 1
                    FROM task_reference tr
                    WHERE
                    tr.task_id = task.id
                    AND tr.reference_id_type = 'PIPELINE_ID'
                    {pipeline_filter}
                    AND tr.deleted_at IS NULL
                )
            """)  # noqa: S608

        if meeting_id:
            where_clauses.append("""
                EXISTS (
                    SELECT 1
                    FROM task_reference tr
                    WHERE
                    tr.task_id = task.id
                    AND tr.reference_id_type = 'MEETING_ID'
                    AND tr.reference_id::uuid = :meeting_id
                    AND tr.deleted_at IS NULL
                )
            """)

        if email_thread_ids:
            where_clauses.append("""
                EXISTS (
                    SELECT 1
                    FROM task_reference tr
                    WHERE
                    tr.task_id = task.id
                    AND tr.reference_id_type = 'EMAIL_THREAD_ID'
                    AND tr.reference_id::uuid = ANY(:email_thread_ids)
                    AND tr.deleted_at IS NULL
                )
            """)

        if sequence_id:
            where_clauses.append("""
                EXISTS (
                    SELECT 1
                    FROM task_reference tr
                    WHERE
                    tr.task_id = task.id
                    AND tr.reference_id_type = 'SEQUENCE_ID'
                    AND tr.reference_id::uuid = :sequence_id
                    AND tr.deleted_at IS NULL
                )
            """)

        if settings.enable_task_sequence_fanout and sequence_step_id_in:
            where_clauses.append("""
                EXISTS (
                    SELECT 1
                    FROM task_reference tr
                    WHERE
                    tr.task_id = task.id
                    AND tr.reference_id_type = 'SEQUENCE_STEP_ID'
                    AND tr.reference_id::uuid = ANY(:sequence_step_id_in)
                    AND tr.deleted_at IS NULL
                )
            """)

        # we may need to rewrite the result set against the respective select-list effective value.
        if (
            pipeline_stage_select_list_value_ids_in
            and len(pipeline_stage_select_list_value_ids_in) > 0
        ):
            where_clauses.append("""
                EXISTS (
                    SELECT 1
                    FROM task_reference tr
                    JOIN pipeline p ON (p.id = tr.reference_id::uuid)
                    WHERE
                    tr.task_id = task.id
                    AND tr.reference_id_type = 'PIPELINE_ID'
                    AND tr.deleted_at IS NULL
                    AND p.stage_id = ANY(:pipeline_stage_select_list_value_ids)
                )
            """)

        task_where_clause = " AND ".join(where_clauses)

        task_sequence_fanout_with_sql = ""
        task_sequence_fanout_join_sql = ""
        task_sequence_fanout_fields_sql = ""

        if settings.enable_task_sequence_fanout:
            ss_where_clause = ""
            ssv_where_clause = ""
            if sequence_step_id_in:
                ss_where_clause = "AND ss.id = ANY(:sequence_step_id_in)"
                ssv_where_clause = (
                    "AND ssv.sequence_step_id = ANY(:sequence_step_id_in)"
                )
            task_sequence_fanout_with_sql = f"""
                t_ss AS (
                    SELECT
                        t.id AS task_id,
                        ss.id AS sequence_step_id,
                        ss.name AS sequence_step_name
                    FROM t
                    JOIN task_reference tr ON tr.task_id = t.id AND tr.reference_id_type = 'SEQUENCE_STEP_ID' AND tr.deleted_at IS NULL
                    JOIN sequence_step_v2 ss ON ss.id = tr.reference_id::uuid
                    WHERE ss.deleted_at IS NULL
                    {ss_where_clause}
                ),
                t_ssv AS (
                    SELECT
                        t.id AS task_id,
                        ssv.id AS sequence_step_variant_id,
                        ssv.name AS sequence_step_variant_name
                    FROM t
                    JOIN task_reference tr ON tr.task_id = t.id AND tr.reference_id_type = 'SEQUENCE_STEP_VARIANT_ID' AND tr.deleted_at IS NULL
                    JOIN sequence_step_variant ssv ON ssv.id = tr.reference_id::uuid
                    WHERE ssv.deleted_at IS NULL
                    {ssv_where_clause}
                ),
            """  # noqa: S608

            task_sequence_fanout_join_sql = """
                LEFT JOIN t_ss ON t.id = t_ss.task_id
                LEFT JOIN t_ssv ON t.id = t_ssv.task_id
            """

            task_sequence_fanout_fields_sql = """
                t_ss.sequence_step_id,
                t_ss.sequence_step_name,
                t_ssv.sequence_step_variant_id,
                t_ssv.sequence_step_variant_name,
            """

        stmt = text(f"""
            WITH t AS (
                SELECT *
                FROM task
                WHERE {task_where_clause}
            ),
            ta AS (
                SELECT
                    t.id AS task_id,
                    a.id AS account_id,
                    a.organization_id AS account_organization_id,
                    a.display_name AS account_display_name,
                    a.official_website AS account_official_website
                FROM t
                JOIN task_reference tr ON tr.task_id = t.id AND tr.reference_id_type = 'ACCOUNT_ID' AND tr.deleted_at IS NULL
                JOIN account a ON a.id = tr.reference_id::uuid
                WHERE a.archived_at IS NULL
            ),
            tc AS (
                SELECT
                    t.id AS task_id,
                    jsonb_agg(
                        jsonb_build_object(
                            'contact_id', c.id,
                            'contact_organization_id', c.organization_id,
                            'contact_display_name', c.display_name
                        )
                        ORDER BY c.last_name
                    ) AS contacts_for_giant_task
                FROM t
                JOIN task_reference tr ON tr.task_id = t.id AND tr.reference_id_type = 'CONTACT_ID' AND tr.deleted_at IS NULL
                JOIN contact c ON c.id = tr.reference_id::uuid
                WHERE c.archived_at IS NULL
                GROUP BY t.id
            ),
            tp AS (
                SELECT
                    t.id AS task_id,
                    p.id AS pipeline_id,
                    p.organization_id AS pipeline_organization_id,
                    p.display_name AS pipeline_display_name,
                    p.stage_id AS pipeline_stage_id
                FROM t
                JOIN task_reference tr ON tr.task_id = t.id AND tr.reference_id_type = 'PIPELINE_ID' AND tr.deleted_at IS NULL
                JOIN pipeline p ON p.id = tr.reference_id::uuid
                WHERE p.archived_at IS NULL
            ),
            tcit AS (
                SELECT
                    t.id AS task_id,
                    jsonb_agg(
                        jsonb_build_object(
                            'citation_id', cit.id,
                            'citation_organization_id', cit.organization_id,
                            'citation_source_id', cit.source_id,
                            'citation_source_type', cit.source_type,
                            'citation_metadata', cit.metadata
                        )
                    ) AS citations_for_giant_task
                FROM t
                JOIN citation cit ON cit.for_object_type = 'TASK' AND cit.for_object_id = t.id
                WHERE cit.deleted_at IS NULL
                GROUP BY t.id
            ),
            tm AS (
                SELECT
                    t.id AS task_id,
                    m.id AS meeting_id,
                    m.title AS meeting_title
                FROM t
                JOIN task_reference tr ON tr.task_id = t.id AND tr.reference_id_type = 'MEETING_ID' AND tr.deleted_at IS NULL
                JOIN meeting m ON m.id = tr.reference_id::uuid
                WHERE m.deleted_at IS NULL
            ),
            tgt AS (
                SELECT
                    t.id AS task_id,
                    jsonb_agg(
                        jsonb_build_object(
                            'global_thread_id', gt.id,
                            'global_thread_organization_id', gt.organization_id,
                            'global_thread_subject', gt.subject
                        )
                        ORDER BY gt.subject
                    ) AS global_threads_for_giant_task
                FROM t
                JOIN task_reference tr ON tr.task_id = t.id AND tr.reference_id_type = 'EMAIL_THREAD_ID' AND tr.deleted_at IS NULL
                JOIN global_thread gt ON gt.id = tr.reference_id::uuid
                WHERE gt.deleted_at IS NULL
                GROUP BY t.id
            ),
            ts AS (
                SELECT
                    t.id AS task_id,
                    s.id AS sequence_id,
                    s.name AS sequence_name
                FROM t
                JOIN task_reference tr ON tr.task_id = t.id AND tr.reference_id_type = 'SEQUENCE_ID' AND tr.deleted_at IS NULL
                JOIN sequence_v2 s ON s.id = tr.reference_id::uuid
                WHERE s.deleted_at IS NULL
            ),
            {task_sequence_fanout_with_sql}
            t_ou AS (
                SELECT
                    t.id AS task_id,
                    u.id AS owner_user_id,
                    u.first_name AS owner_user_first_name,
                    u.last_name AS owner_user_last_name,
                    u.avatar_s3_key AS owner_user_avatar_s3_key
                FROM t
                JOIN public.user u ON u.id = t.owner_user_id
                WHERE u.deactivated_at IS NULL
            ),
            t_cu AS (
                SELECT
                    t.id AS task_id,
                    cb.id AS created_by_user_id,
                    cb.first_name AS created_by_user_first_name,
                    cb.last_name AS created_by_user_last_name,
                    cb.avatar_s3_key AS created_by_user_avatar_s3_key
                FROM t
                JOIN public.user cb ON cb.id = t.created_by_user_id
                WHERE cb.deactivated_at IS NULL
            )
            SELECT
                {task_fields},
                tc.contacts_for_giant_task,
                ta.account_id,
                ta.account_organization_id,
                ta.account_display_name,
                ta.account_official_website,
                tp.pipeline_id,
                tp.pipeline_organization_id,
                tp.pipeline_display_name,
                tp.pipeline_stage_id,
                tcit.citations_for_giant_task,
                tm.meeting_id,
                tm.meeting_title,
                tgt.global_threads_for_giant_task,
                ts.sequence_id,
                ts.sequence_name,
                {task_sequence_fanout_fields_sql}
                t_ou.owner_user_id,
                t_ou.owner_user_first_name,
                t_ou.owner_user_last_name,
                t_ou.owner_user_avatar_s3_key,
                t_cu.created_by_user_id,
                t_cu.created_by_user_first_name,
                t_cu.created_by_user_last_name,
                t_cu.created_by_user_avatar_s3_key,
                COALESCE(t.participants, '[]'::jsonb) AS task_participants
            FROM t
            LEFT JOIN ta ON t.id = ta.task_id
            LEFT JOIN tc ON t.id = tc.task_id
            LEFT JOIN tp ON t.id = tp.task_id
            LEFT JOIN tcit ON t.id = tcit.task_id
            LEFT JOIN tm ON t.id = tm.task_id
            LEFT JOIN tgt ON t.id = tgt.task_id
            LEFT JOIN ts ON t.id = ts.task_id
            {task_sequence_fanout_join_sql}
            LEFT JOIN t_ou ON t.id = t_ou.task_id
            LEFT JOIN t_cu ON t.id = t_cu.task_id
            {order_by_clause}
            {limit_clause}
            {offset_clause}
        """)  # noqa: S608

        bind_params: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        bind_params["organization_id"] = organization_id
        if only_include_task_ids:
            bind_params["only_include_task_ids"] = only_include_task_ids
        if status_ne:
            bind_params["status_ne"] = status_ne
        if due_at_gt:
            bind_params["due_at_gt"] = due_at_gt
        if due_at_lt:
            bind_params["due_at_lt"] = due_at_lt
        if account_id or account_ids_in:
            bind_params["account_ids"] = set([account_id] if account_id else []) | set(
                account_ids_in if account_ids_in else []
            )
        if contact_ids and len(contact_ids) > 0:
            bind_params["contact_ids"] = contact_ids
        if pipeline_id or pipeline_ids_in:
            bind_params["pipeline_ids"] = set(
                [pipeline_id] if pipeline_id else []
            ) | set(pipeline_ids_in if pipeline_ids_in else [])
        if (
            pipeline_stage_select_list_value_ids_in
            and len(pipeline_stage_select_list_value_ids_in) > 0
        ):
            bind_params["pipeline_stage_select_list_value_ids"] = (
                pipeline_stage_select_list_value_ids_in
            )
        if meeting_id:
            bind_params["meeting_id"] = meeting_id
        if email_thread_ids and len(email_thread_ids) > 0:
            bind_params["email_thread_ids"] = email_thread_ids
        if sequence_id:
            bind_params["sequence_id"] = sequence_id
        if settings.enable_task_sequence_fanout and sequence_step_id_in:
            bind_params["sequence_step_id_in"] = sequence_step_id_in
        if owner_user_id or owner_user_ids_in:
            bind_params["owner_user_ids"] = set(
                owner_user_ids_in if owner_user_ids_in else []
            ) | set([owner_user_id] if owner_user_id else [])
        if priority_in:
            bind_params["priority_in"] = priority_in
        if type_in:
            bind_params["type_in"] = type_in
        if status_in:
            bind_params["status_in"] = status_in

        stmt = stmt.bindparams(**bind_params)

        logger.bind(stmt=stmt).info("Giant task query")

        rows = await self.engine.all(stmt)

        return await GiantTask.bulk_from_rows(rows=rows)


class TaskTemplateRepository(GenericRepository):
    async def batch_get_task_templates(
        self,
        template_ids: list[UUID],
        organization_id: UUID,
    ) -> list[TaskTemplate]:
        return await self._find_by_column_values(
            TaskTemplate,
            id=template_ids,
            organization_id=organization_id,
        )
