from datetime import date, datetime
from uuid import UUID

from sqlalchemy import text

from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.dto.prospecting_dto import (
    ProspectingCreditType,
    ProspectingCreditUsagePointDTO,
)
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
    QuotaPeriod,
    QuotaPolicy,
    QuotaSummaryRequestItem,
)
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class ProspectingCreditUsageAggregation(NameValueStrEnum):
    daily = "daily"
    weekly = "weekly"
    monthly = "monthly"


class QuotaPolicyRepository(GenericRepository):
    async def get_entity_relevant_policies(
        self,
        organization_id: UUID,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        period: QuotaPeriod,
    ) -> list[QuotaPolicy]:
        stmt = text(
            """
            SELECT * FROM quota_policy
            where entity_type = :entity_type
            AND resource = :resource
            AND entity_id = :entity_id
            AND organization_id = :organization_id
            AND period = :period
            AND deleted_at is null
            """
        ).bindparams(
            entity_type=entity_type,
            resource=resource,
            entity_id=entity_id,
            organization_id=organization_id,
            period=period,
        )

        rows = await self.engine.all(stmt)
        return await QuotaPolicy.bulk_from_rows(rows=rows)

    async def delete_quota_policy_by_id(self, policy_id: UUID) -> None:
        stmt = text(
            """
            DELETE FROM quota_policy
            WHERE id = :policy_id
            """
        ).bindparams(policy_id=policy_id)

        await self.engine.execute(stmt)

    async def get_organization_total_credits_by_resource(
        self,
        organization_id: UUID,
        resource: QuotaConsumingResource,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
    ) -> int:
        stmt = text(
            """
            SELECT COALESCE(SUM(quota_limit), 0)
            FROM quota_policy
            WHERE organization_id = :organization_id
            AND resource = :resource
            AND entity_type = :entity_type
            AND entity_id = :entity_id
            AND deleted_at IS NULL
            """
        ).bindparams(
            organization_id=organization_id,
            resource=resource,
            entity_id=entity_id,
            entity_type=entity_type,
        )
        row = await self.engine.one(stmt)
        return row[0] if row else 0

    async def get_quota_policies_aggregated_by_resources_and_entity_id(
        self,
        organization_id: UUID,
        resource: QuotaConsumingResource,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        period: QuotaPeriod,
    ) -> int:
        stmt = text(
            """
            SELECT
                COALESCE(SUM(quota_limit), 0) as total_quota_limit
            FROM quota_policy
            WHERE organization_id = :organization_id
            AND resource = :resource
            AND entity_id = :entity_id
            AND entity_type = :entity_type
            AND period = :period
            AND deleted_at IS NULL
            """
        ).bindparams(
            organization_id=organization_id,
            resource=resource,
            entity_id=entity_id,
            entity_type=entity_type,
            period=period,
        )

        row = await self.engine.one(stmt)
        return row[0] if row else 0

    async def bulk_get_quota_policies_aggregated_by_resources_and_entity_id(
        self,
        organization_id: UUID,
        resource: QuotaConsumingResource,
        entity_ids: list[UUID],
        entity_type: QuotaConsumerEntityType,
        period: QuotaPeriod,
    ) -> dict[UUID, int]:
        """
        Get aggregated quota policies for multiple entities in a single query.

        Args:
            organization_id: The organization ID
            resource: The resource being consumed
            entity_ids: List of entity IDs to get quota policies for
            entity_type: The entity type (e.g., USER, ORGANIZATION)
            period: The quota period

        Returns:
            Dictionary mapping entity IDs to their aggregated quota limits
        """
        if not entity_ids:
            return {}

        stmt = text(
            """
            SELECT
                entity_id::uuid,
                COALESCE(SUM(quota_limit), 0) as total_quota_limit
            FROM quota_policy
            WHERE organization_id = :organization_id
            AND resource = :resource
            AND entity_id = ANY(:entity_ids)
            AND entity_type = :entity_type
            AND period = :period
            AND deleted_at IS NULL
            GROUP BY entity_id
            """
        ).bindparams(
            organization_id=organization_id,
            resource=resource,
            entity_type=entity_type,
            period=period,
            entity_ids=entity_ids,
        )

        rows = await self.engine.all(stmt)

        # Create a dictionary mapping entity_id to quota limit
        result = {UUID(str(row[0])): row[1] for row in rows}

        # Add missing entity_ids with default value of 0
        for entity_id in entity_ids:
            if entity_id not in result:
                result[entity_id] = 0

        return result

    async def get_distinct_quota_configurations(
        self,
        organization_id: UUID,
    ) -> list[QuotaSummaryRequestItem]:
        """
        Get all distinct resource/period/entity_type combinations from existing quota policies.

        Args:
            organization_id: The organization ID to get configurations for

        Returns:
            List of QuotaSummaryRequestItem representing unique resource configurations
        """
        stmt = text(
            """
            SELECT DISTINCT resource, period, entity_type
            FROM quota_policy
            WHERE organization_id = :organization_id
            AND deleted_at IS NULL
            """
        ).bindparams(
            organization_id=organization_id,
        )

        rows = await self.engine.all(stmt)

        return [
            QuotaSummaryRequestItem(
                resource=row[0],
                period=row[1],
                entity_type=row[2],
            )
            for row in rows
        ]

    async def bulk_get_entity_relevant_policies(
        self,
        organization_id: UUID,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resources: list[QuotaConsumingResource],
        periods: list[QuotaPeriod],
    ) -> list[QuotaPolicy]:
        """
        Get relevant policies for multiple resources and periods in a single query.

        Args:
            organization_id: The organization ID
            entity_id: The entity ID to get policies for
            entity_type: The entity type (e.g., USER, ORGANIZATION)
            resources: List of resources to get policies for
            periods: List of quota periods to get policies for

        Returns:
            List of QuotaPolicy objects
        """
        if not resources or not periods:
            return []

        stmt = text(
            """
            SELECT * FROM quota_policy
            WHERE entity_type = :entity_type
            AND entity_id = :entity_id
            AND organization_id = :organization_id
            AND resource = ANY(:resources)
            AND period = ANY(:periods)
            AND deleted_at is null
            """
        ).bindparams(
            entity_type=entity_type,
            entity_id=entity_id,
            organization_id=organization_id,
            resources=resources,
            periods=periods,
        )

        rows = await self.engine.all(stmt)
        return await QuotaPolicy.bulk_from_rows(rows=rows)


class QuotaUsageRepository(GenericRepository):
    async def get_aggregate_user_usage_in_period(
        self,
        organization_id: UUID,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        period_start: datetime,
        period_end: datetime,
    ) -> int:
        stmt = text(
            """
            SELECT SUM(usage)
            FROM quota_usage
            WHERE organization_id = :organization_id
            and entity_id = :entity_id
            and entity_type = :entity_type
            and resource = :resource
            and hour_start >= :period_start
            and hour_start < :period_end
            and deleted_at is null
            """
        ).bindparams(
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
            period_start=period_start,
            period_end=period_end,
        )

        rows = await self.engine.all(stmt)
        return rows[0][0] if rows[0][0] else 0

    async def get_aggregate_org_usage_in_period(
        self,
        organization_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        period_start: datetime,
        period_end: datetime,
    ) -> int:
        stmt = text(
            """
            SELECT SUM(usage)
            FROM quota_usage
            WHERE organization_id = :organization_id
            and entity_type = :entity_type
            and resource = :resource
            and hour_start >= :period_start
            and hour_start < :period_end
            and deleted_at is null
            """,
        ).bindparams(
            organization_id=organization_id,
            entity_type=entity_type,
            resource=resource,
            period_start=period_start,
            period_end=period_end,
        )

        rows = await self.engine.all(stmt)
        return rows[0][0] if rows[0][0] else 0

    async def get_aggregated_daily_usage_in_period(
        self,
        organization_id: UUID,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
        period_start: ZoneRequiredDateTime,
        period_end: ZoneRequiredDateTime,
    ) -> dict[date, int]:
        stmt = text(
            """
            SELECT
                DATE(hour_start) AS usage_date,
                SUM(usage) AS total_usage
            FROM quota_usage
            WHERE organization_id = :organization_id
              AND entity_id = :entity_id
              AND entity_type = :entity_type
              AND resource = :resource
              AND hour_start >= :period_start
              AND hour_start < :period_end
              AND deleted_at IS NULL
            GROUP BY DATE(hour_start)
            ORDER BY usage_date;
            """
        ).bindparams(
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
            period_start=period_start,
            period_end=period_end,
        )

        rows = await self.engine.all(stmt)

        # Convert to a dictionary of date: total_usage
        usage_by_date: dict[date, int] = {
            row.usage_date: row.total_usage for row in rows
        }
        return usage_by_date

    async def get_organization_used_credits_by_resource_and_entity(
        self,
        organization_id: UUID,
        resource: QuotaConsumingResource,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        applied_sub_resource: QuotaConsumingResource,
    ) -> int:
        stmt = text(
            """
            SELECT COALESCE(SUM(usage), 0)
            FROM quota_usage
            WHERE organization_id = :organization_id
            AND resource = :resource
            AND entity_id = :entity_id
            AND entity_type = :entity_type
            AND applied_sub_resource = :applied_sub_resource
            AND created_by_user_id IS NOT NULL
            AND deleted_at IS NULL
            """
        ).bindparams(
            organization_id=organization_id,
            resource=resource,
            entity_id=entity_id,
            entity_type=entity_type,
            applied_sub_resource=applied_sub_resource,
        )

        row = await self.engine.one(stmt)
        return row[0] if row else 0

    async def get_organization_aggregated_usages(
        self,
        *,
        organization_id: UUID,
        aggregation: ProspectingCreditUsageAggregation,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        resource: QuotaConsumingResource,
    ) -> list[ProspectingCreditUsagePointDTO]:
        trunc_format = {"daily": "day", "weekly": "week", "monthly": "month"}.get(
            aggregation, "day"
        )

        stmt = text(
            """
            SELECT
                DATE_TRUNC(:trunc_format, qu.created_at) as period_date,
                COALESCE(SUM(qu.usage), 0) as credits_used,
                qu.applied_sub_resource as credit_type,
                u.id as user_id,
                u.first_name || ' ' || u.last_name as user_name

            FROM
                quota_usage qu
            JOIN
                "user" u ON qu.created_by_user_id = u.id
            WHERE
                qu.organization_id = :organization_id
                AND qu.entity_id = :entity_id
                AND qu.entity_type = :entity_type
                AND qu.resource = :resource
                AND qu.created_by_user_id IS NOT NULL
                AND qu.deleted_at IS NULL
            GROUP BY
                period_date, credit_type, user_id, user_name
            ORDER BY
                period_date
            """
        ).bindparams(
            trunc_format=trunc_format,
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resource=resource,
        )

        rows = await self.engine.all(stmt)
        return [
            ProspectingCreditUsagePointDTO(
                date=row[0],
                credits_used=row[1],
                usage_type=ProspectingCreditType.email_enrichment
                if row[2] == QuotaConsumingResource.PROSPECTING_EMAIL_ENRICHMENT
                else ProspectingCreditType.phone_enrichment,
                user_id=row[3],
                user_name=row[4],
            )
            for row in rows
        ]

    async def get_entity_used_credits_by_resource_and_entity_id(
        self,
        organization_id: UUID,
        resource: QuotaConsumingResource,
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        start_time: datetime,
    ) -> int:
        stmt = text(
            """
            SELECT COALESCE(SUM(usage), 0)
            FROM quota_usage
            WHERE organization_id = :organization_id
            AND resource = :resource
            AND entity_id = :entity_id
            AND entity_type = :entity_type
            AND deleted_at IS NULL
            AND hour_start >= :start_time
            """
        ).bindparams(
            organization_id=organization_id,
            resource=resource,
            entity_id=entity_id,
            entity_type=entity_type,
            start_time=start_time,
        )

        row = await self.engine.one(stmt)
        return row[0] if row else 0

    async def bulk_get_entity_used_credits_by_resource_and_entity_id(
        self,
        organization_id: UUID,
        resource: QuotaConsumingResource,
        entity_ids: list[UUID],
        entity_type: QuotaConsumerEntityType,
        start_time: datetime,
    ) -> dict[UUID, int]:
        """
        Get used credits for multiple entities in a single query.

        Args:
            organization_id: The organization ID
            resource: The resource being consumed
            entity_ids: List of entity IDs to get usage for
            entity_type: The entity type (e.g., USER, ORGANIZATION)
            start_time: The start time for the usage period

        Returns:
            Dictionary mapping entity IDs to their used credits
        """
        if not entity_ids:
            return {}

        stmt = text(
            """
            SELECT
                entity_id::uuid,
                COALESCE(SUM(usage), 0) as total_usage
            FROM quota_usage
            WHERE organization_id = :organization_id
            AND resource = :resource
            AND entity_id = ANY(:entity_ids)
            AND entity_type = :entity_type
            AND deleted_at IS NULL
            AND hour_start >= :start_time
            GROUP BY entity_id
            """
        ).bindparams(
            organization_id=organization_id,
            resource=resource,
            entity_type=entity_type,
            start_time=start_time,
            entity_ids=entity_ids,
        )

        rows = await self.engine.all(stmt)

        # Create a dictionary mapping entity_id to usage
        result = {UUID(str(row[0])): row[1] for row in rows}

        # Add missing entity_ids with default value of 0
        for entity_id in entity_ids:
            if entity_id not in result:
                result[entity_id] = 0

        return result

    async def get_organization_used_credits_by_resource_and_user(
        self,
        organization_id: UUID,
        resource: QuotaConsumingResource,
        user_id: UUID,
    ) -> int:
        stmt = text(
            """
            SELECT COALESCE(SUM(usage), 0)
            FROM quota_usage
            WHERE organization_id = :organization_id
            AND resource = :resource
            AND entity_id = :user_id
            AND entity_type = 'USER'
            AND deleted_at IS NULL
            """
        ).bindparams(
            organization_id=organization_id,
            resource=resource,
            user_id=user_id,
        )

        row = await self.engine.one(stmt)
        return row[0] if row else 0

    async def bulk_get_entity_used_credits_by_resources(
        self,
        organization_id: UUID,
        resources: list[QuotaConsumingResource],
        entity_id: UUID,
        entity_type: QuotaConsumerEntityType,
        start_times: dict[QuotaConsumingResource, datetime],
    ) -> dict[QuotaConsumingResource, int]:
        """
        Get used credits for multiple resources in a single query.

        Args:
            organization_id: The organization ID
            resources: List of resources to get usage for
            entity_id: The entity ID to get usage for
            entity_type: The entity type (e.g., USER, ORGANIZATION)
            start_times: Dictionary mapping resources to their start times

        Returns:
            Dictionary mapping resources to their used credits
        """
        if not resources:
            return {}

        # Instead of constructing dynamic SQL with f-strings, use individual queries with proper params
        result = {}

        # Get all usage first with basic filters
        stmt = text(
            """
            SELECT
                resource,
                COALESCE(SUM(usage), 0) as total_usage
            FROM quota_usage
            WHERE organization_id = :organization_id
            AND entity_id = :entity_id
            AND entity_type = :entity_type
            AND resource = ANY(:resources)
            AND deleted_at IS NULL
            GROUP BY resource
            """
        ).bindparams(
            organization_id=organization_id,
            entity_id=entity_id,
            entity_type=entity_type,
            resources=resources,
        )

        rows = await self.engine.all(stmt)

        # Initialize results for all resources
        base_results = {row[0]: row[1] for row in rows}
        for resource in resources:
            result[resource] = base_results.get(resource, 0)

        # For resources with start_times, run an additional filtered query
        for resource, start_time in start_times.items():
            if resource not in resources:
                continue

            resource_stmt = text(
                """
                SELECT
                    COALESCE(SUM(usage), 0) as total_usage
                FROM quota_usage
                WHERE organization_id = :organization_id
                AND entity_id = :entity_id
                AND entity_type = :entity_type
                AND resource = :resource
                AND hour_start >= :start_time
                AND deleted_at IS NULL
                """
            ).bindparams(
                organization_id=organization_id,
                entity_id=entity_id,
                entity_type=entity_type,
                resource=resource,
                start_time=start_time,
            )

            row = await self.engine.one(resource_stmt)
            result[resource] = row[0] if row and row[0] is not None else 0

        return result
