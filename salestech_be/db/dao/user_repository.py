from typing import Annotated, Any
from uuid import UUID, uuid4

from fastapi import Depends
from pydantic import EmailStr
from sqlalchemy import BindParameter, bindparam, text

from salestech_be.common.exception import ResourceNotFoundError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.db.dao.crm_sync_repository import CRMSyncRepository
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.dao.organization_info_repository import OrganizationInfoRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.organization_user_dto import ActiveOrganizationUserDto
from salestech_be.db.models.user import User, UserRole
from salestech_be.db.models.user_organization_association import (
    UserOrganizationAssociation,
    UserOrganizationAssociationStatus,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger("repository.user_organization_repository")


class UserRepository(GenericRepository):
    """Repo methods for user and organization, they are tightly coupled."""

    def __init__(self, *, engine: Annotated[DatabaseEngine, Depends(get_db_engine)]):
        super().__init__(engine=engine)
        self.crm_sync_repo = CRMSyncRepository(engine=engine)
        self.organization_info_repository = OrganizationInfoRepository(engine=engine)

    async def list_by_ids(
        self, organization_id: UUID, user_ids: list[UUID]
    ) -> list[User]:
        stmt = text(
            """
        select u.* from public.user u
        join public.user_organization_association uoa
        on u.id = uoa.user_id
        where uoa.organization_id = :organization_id
        and u.id in :user_ids
        and uoa.status = :status
        """,
        ).bindparams(
            bindparam("user_ids", user_ids, expanding=True),
            organization_id=organization_id,
            status=UserOrganizationAssociationStatus.ACTIVE.name,
        )
        rows = await self.engine.all(stmt)
        return await User.bulk_from_rows(rows=rows)

    async def get_by_id(self, user_id: UUID) -> User | None:
        return await self._find_unique_by_column_values(User, id=user_id)

    async def get_or_insert_user(self, email: str) -> User:
        # check if the user exists or not with the email address
        # create a new user if the user does not exist
        existing_user = await self._find_unique_by_column_values(User, email=email)

        if existing_user:
            # TODO(hao): update user on certain fields..?
            return existing_user

        return not_none(
            await self.insert(
                User(id=uuid4(), email=email, created_at=zoned_utc_now()),
            ),
        )

    async def get_user_with_profile(self, email: str) -> User | None:
        return await self._find_unique_by_column_values(User, email=email)

    async def get_or_insert_user_with_profile(
        self,
        email: str,
        first_name: str | None = None,
        last_name: str | None = None,
        linkedin_url: str | None = None,
    ) -> User:
        existing_user = await self._find_unique_by_column_values(User, email=email)
        if existing_user:
            return existing_user
        return not_none(
            await self.insert(
                User(
                    id=uuid4(),
                    email=email,
                    first_name=first_name,
                    last_name=last_name,
                    linkedin_url=linkedin_url,
                    created_at=zoned_utc_now(),
                ),
            ),
        )

    async def upsert_user_and_associate_to_organization(
        self,
        user_to_upsert: User,
        organization_id: UUID,
        roles: list[UserRole],
    ) -> tuple[User, UserOrganizationAssociation]:
        # todo(xw): unique on email need to be filtered on "deleted_at"
        # todo(xw): need to support pseudo nested transaction on same DB instance.
        # otherwise cannot reliably nest repo calls at all.
        user = await self.insert_or_none(
            user_to_upsert,
            on_conflict_do_nothing_conditional_columns={},
            on_conflict_do_nothing_target_columns=["email"],
        )
        if not user:
            user = not_none(
                await self._find_unique_by_column_values(
                    User,
                    exclude_deleted_or_archived=False,
                    email=user_to_upsert.email,
                )
            )
        asso = await self.associate_user_to_an_organization(
            user_id=user.id, organization_id=organization_id, roles=roles
        )
        return user, asso

    async def find_association_by_user_id_and_organization_id(
        self, user_id: UUID, organization_id: UUID
    ) -> UserOrganizationAssociation | None:
        return await self._find_unique_by_column_values(
            UserOrganizationAssociation,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def find_active_user_orgs_by_user_id(
        self, user_id: UUID
    ) -> list[UserOrganizationAssociation]:
        return await self._find_by_column_values(
            UserOrganizationAssociation,
            user_id=user_id,
            status=UserOrganizationAssociationStatus.ACTIVE,
        )

    async def associate_user_to_an_organization(
        self,
        user_id: UUID,
        organization_id: UUID,
        roles: list[UserRole],
        status: UserOrganizationAssociationStatus = UserOrganizationAssociationStatus.ACTIVE,
    ) -> UserOrganizationAssociation:
        existing_association = await self._find_unique_by_column_values(
            UserOrganizationAssociation,
            user_id=user_id,
            organization_id=organization_id,
        )

        if existing_association:
            # todo(xw): potential bug, what if the new association has different roles?
            logger.info("found existing association, skip")
            return existing_association

        return not_none(
            await self.insert(
                UserOrganizationAssociation(
                    id=uuid4(),
                    user_id=user_id,
                    organization_id=organization_id,
                    roles=roles,
                    created_at=zoned_utc_now(),
                    status=status,
                ),
            ),
        )

    async def deactivate_user_in_organization_by_user_organization_id(
        self, user_id: UUID, organization_id: UUID, updated_by_user_id: UUID
    ) -> UserOrganizationAssociation | None:
        existing_association = await self._find_unique_by_column_values(
            UserOrganizationAssociation,
            user_id=user_id,
            organization_id=organization_id,
        )

        if existing_association:
            # set the user in the org to inactive
            updates = await self._update_by_column_values(
                UserOrganizationAssociation,
                column_value_to_query={
                    "user_id": user_id,
                    "organization_id": organization_id,
                },
                column_to_update={
                    "updated_at": zoned_utc_now(),
                    "updated_by_user_id": updated_by_user_id,
                    "status": UserOrganizationAssociationStatus.DEACTIVATED,
                },
            )

            if len(updates) != 1:
                logger.error(
                    f"should only see 1 updated record: user_id - {user_id}, organization_id - {organization_id}"
                )
            return updates[0]

        logger.info(
            f"user {user_id} not found in org {organization_id}, no actions taken"
        )
        return None

    async def find_active_user_organization_association(
        self, user_id: UUID, organization_id: UUID
    ) -> ActiveOrganizationUserDto | None:
        user_a = "u"
        asso_a = "a"
        user_table_prefix = "u__"
        asso_table_prefix = "a__"

        all_columns = ",".join(
            User.get_column_names_with_alias(
                table_alias=user_a, column_alias_prefix=user_table_prefix
            )
            + UserOrganizationAssociation.get_column_names_with_alias(
                table_alias=asso_a, column_alias_prefix=asso_table_prefix
            )
        )

        stmt = text(f"""
        select {all_columns} from {User.fq_table_name} {user_a}
            join {UserOrganizationAssociation.fq_table_name} {asso_a}
                on {user_a}.id = {asso_a}.user_id
            where
                    {user_a}.id = :user_id
                and {asso_a}.organization_id = :organization_id
        """).bindparams(  # noqa: S608
            user_id=user_id,
            organization_id=organization_id,
        )
        if found := await self.engine.at_most_one(stmt):
            user = User.from_row(found, column_alias_prefix=user_table_prefix)
            user_org_asso = UserOrganizationAssociation.from_row(
                found, column_alias_prefix=asso_table_prefix
            )
            return (
                ActiveOrganizationUserDto(
                    user=user,
                    user_organization_association=user_org_asso,
                )
                if user_org_asso.status == UserOrganizationAssociationStatus.ACTIVE
                else None
            )
        return None

    async def find_user_by_id_and_organization_id(
        self,
        user_id: UUID,
        organization_id: UUID,
        include_inactive: bool = False,
    ) -> User | None:
        if include_inactive:
            stmt = text(
                """
            select u.* from public.user u
            join public.user_organization_association uoa
            on u.id = uoa.user_id
            where uoa.organization_id = :organization_id
            and u.id = :user_id
            """,
            ).bindparams(
                user_id=user_id,
                organization_id=organization_id,
            )
        else:
            stmt = text(
                """
            select u.* from public.user u
            join public.user_organization_association uoa
            on u.id = uoa.user_id
            where uoa.organization_id = :organization_id
            and u.id = :user_id
            and uoa.status = :status
            """,
            ).bindparams(
                user_id=user_id,
                organization_id=organization_id,
                status=UserOrganizationAssociationStatus.ACTIVE,
            )

        rows = await self.engine.all(stmt)
        return User.from_row(rows[0]) if rows else None

    async def find_user_by_email(
        self,
        email: EmailStr,
    ) -> User | None:
        return await self._find_unique_by_column_values(User, email=email)

    async def find_user_by_organization_id_and_email(
        self,
        organization_id: UUID,
        email: EmailStr,
        include_inactive: bool = False,
    ) -> User | None:
        if include_inactive:
            stmt = text(
                """
            select u.* from public.user u
            join public.user_organization_association uoa
            on u.id = uoa.user_id
            where uoa.organization_id = :organization_id
            and u.email = :email
            """,
            ).bindparams(
                email=email,
                organization_id=organization_id,
            )

            rows = await self.engine.all(stmt)
            return User.from_row(rows[0]) if rows else None
        else:
            stmt = text(
                """
            select u.* from public.user u
            join public.user_organization_association uoa
            on u.id = uoa.user_id
            where uoa.organization_id = :organization_id
            and u.email = :email
            and uoa.status = :status
            """,
            ).bindparams(
                email=email,
                organization_id=organization_id,
                status=UserOrganizationAssociationStatus.ACTIVE.name,
            )
            rows = await self.engine.all(stmt)
            return User.from_row(rows[0]) if rows else None

    async def find_user_by_organization_id_and_email_account_id(
        self,
        organization_id: UUID,
        email_account_id: UUID,
    ) -> User | None:
        stmt = text(
            """
        select u.* from public.user u
        join public.user_organization_association uoa
        on u.id = uoa.user_id
        join public.email_account ea
        on u.id = ea.owner_user_id
        where uoa.organization_id = :organization_id
        and uoa.status = :status
        and ea.id = :email_account_id
        """,
        ).bindparams(
            email_account_id=email_account_id,
            organization_id=organization_id,
            status=UserOrganizationAssociationStatus.ACTIVE.name,
        )

        rows = await self.engine.all(stmt)
        return User.from_row(rows[0]) if rows else None

    async def find_active_user_by_organization_id_and_first_last_name(
        self,
        organization_id: UUID,
        first_name: str,
        last_name: str | None,
    ) -> list[User]:
        stmt = text(
            """
            select u.* from public.user u
            join public.user_organization_association uoa
            on u.id = uoa.user_id
            where uoa.organization_id = :organization_id
            and u.first_name = :first_name
            and u.last_name = :last_name
            and uoa.status = :status
            """,
        ).bindparams(
            first_name=first_name,
            last_name=last_name,
            organization_id=organization_id,
            status=UserOrganizationAssociationStatus.ACTIVE.name,
        )
        rows = await self.engine.all(stmt)
        return await User.bulk_from_rows(rows=rows)

    async def find_user_by_organization_id_and_emails(
        self,
        organization_id: UUID,
        emails: list[EmailStr],
    ) -> list[User]:
        stmt = text(
            """
        select u.* from public.user u
        join public.user_organization_association uoa
        on u.id = uoa.user_id
        where uoa.organization_id = :organization_id
        and u.email in :emails
        and uoa.status = :status
        """,
        ).bindparams(
            bindparam("emails", emails, expanding=True),
            organization_id=organization_id,
            status=UserOrganizationAssociationStatus.ACTIVE.name,
        )
        rows = await self.engine.all(stmt)
        return await User.bulk_from_rows(rows=rows)

    async def get_active_org_ids_with_first_admin_user(
        self,
    ) -> dict[UUID, UUID]:
        """
        Each organization with at least one active admin gets the oldest active admin's user id associated to the organization id
        Returns a dictionary mapping organization_id to admin_user_id
        """
        stmt = text("""
        SELECT DISTINCT ON (uoa.organization_id) u.id as admin_user_id, uoa.organization_id
        FROM   public.user u
        JOIN   public.user_organization_association uoa
            ON u.id = uoa.user_id
        WHERE  :role = ANY(uoa.roles)
           AND uoa.status = :status
        ORDER BY uoa.organization_id, uoa.created_at ASC
        """).bindparams(
            status=UserOrganizationAssociationStatus.ACTIVE,
            role=UserRole.ADMIN,
        )
        rows = await self.engine.all(stmt)
        return {row.organization_id: row.admin_user_id for row in rows}

    async def list_org_users_by_organization_id(
        self,
        organization_id: UUID,
        active_users_only: bool = False,
        only_include_user_ids: UnsetAware[set[UUID]] = UNSET,
        include_super_admins: bool = False,
        include_hidden_users: bool = False,
    ) -> list[tuple[User, UserOrganizationAssociation]]:
        """Find users under the given org id.

        This function will return a tuple of user obj and its associated org
        "membership".

        By default, it will return all users in the org except for super admins and
        hidden users.
        """

        if specified(only_include_user_ids) and not only_include_user_ids:
            return []
        user_ids_filter = (
            list(only_include_user_ids) if specified(only_include_user_ids) else None
        )

        stmt = text(
            """
            select
               u.id as u_id,
               u.email as u_email,
               u.created_at as u_created_at,
               u.first_name as u_first_name,
               u.last_name as u_last_name,
               u.phone_number as u_phone_number,
               u.linkedin_url as u_linkedin_url,
               u.avatar_s3_key as u_avatar_s3_key,
               u.created_by_user_id as u_created_by_user_id,
               u.updated_at as u_updated_at,
               u.updated_by_user_id as u_updated_by_user_id,
               u.deactivated_at as u_deactivated_at,
               u.deactivated_by_user_id as u_deactivated_by_user_id,
               uoa.id as uoa_id,
               uoa.user_id as uoa_user_id,
               uoa.organization_id as uoa_organization_id,
               uoa.roles as uoa_roles,
               uoa.created_at as uoa_created_at,
               uoa.status as uoa_status,
               uoa.created_by_user_id as uoa_created_by_user_id,
               uoa.updated_at as uoa_updated_at,
               uoa.updated_by_user_id as uoa_updated_by_user_id,
               COALESCE(up.value->'user_org_profile', uoa.profile) as uoa_profile
            from public.user u
            join public.user_organization_association uoa on u.id = uoa.user_id
            left join public.user_organization_preference up on up.user_id = u.id and up.organization_id = uoa.organization_id and up.key = 'user_org_profile'
            where uoa.organization_id = :organization_id
            """,
        )
        params: list[BindParameter[Any]] = [  # type: ignore[explicit-any] # TODO: fix-any-annotation
            bindparam(key="organization_id", value=organization_id),
        ]

        if active_users_only:
            # add status filter to the SQL to get active users only
            stmt = text(stmt.text + " and uoa.status = :status")
            params.append(
                bindparam(
                    key="status", value=UserOrganizationAssociationStatus.ACTIVE.name
                )
            )

        if not include_super_admins:
            # add user_role filter to exclude super admins
            stmt = text(
                stmt.text + " and (uoa.roles is null OR :role_name != ALL(uoa.roles))"
            )
            params.append(
                bindparam(
                    key="role_name",
                    value=UserRole.SUPER_ADMIN.name,
                )
            )

        if not include_hidden_users:
            # hidden users are special users created for offline sync (like email, crm
            # imports etc). normally they should not be shown, unless explicitly requested
            stmt = text(stmt.text + " and uoa.status != :hidden_status")
            params.append(
                bindparam(
                    key="hidden_status",
                    value=UserOrganizationAssociationStatus.HIDDEN.name,
                )
            )

        if user_ids_filter:
            stmt = text(stmt.text + " and u.id in :user_ids")
            params.append(
                bindparam(key="user_ids", value=user_ids_filter, expanding=True)
            )

        stmt = stmt.bindparams(*params)

        rows = await self.engine.all(stmt)

        return [
            (
                User.from_row(row, column_alias_prefix="u_"),
                UserOrganizationAssociation.from_row(row, column_alias_prefix="uoa_"),
            )
            for row in rows
        ]

    async def list_org_users_by_organization_id_untenanted(
        self,
        active_users_only: bool = False,
        only_include_user_ids: UnsetAware[set[UUID]] = UNSET,
        include_super_admins: bool = False,
        include_hidden_users: bool = False,
    ) -> list[tuple[User, UserOrganizationAssociation]]:
        """Find users under the given org id.

        This function will return a tuple of user obj and its associated org
        "membership".

        By default, it will return all users in the org except for super admins and
        hidden users.
        """

        if specified(only_include_user_ids) and not only_include_user_ids:
            return []
        user_ids_filter = (
            list(only_include_user_ids) if specified(only_include_user_ids) else None
        )

        stmt = text(
            """
            select
               u.id as u_id,
               u.email as u_email,
               u.created_at as u_created_at,
               u.first_name as u_first_name,
               u.last_name as u_last_name,
               u.phone_number as u_phone_number,
               u.linkedin_url as u_linkedin_url,
               u.avatar_s3_key as u_avatar_s3_key,
               u.created_by_user_id as u_created_by_user_id,
               u.updated_at as u_updated_at,
               u.updated_by_user_id as u_updated_by_user_id,
               u.deactivated_at as u_deactivated_at,
               u.deactivated_by_user_id as u_deactivated_by_user_id,
               uoa.id as uoa_id,
               uoa.user_id as uoa_user_id,
               uoa.organization_id as uoa_organization_id,
               uoa.roles as uoa_roles,
               uoa.created_at as uoa_created_at,
               uoa.status as uoa_status,
               uoa.created_by_user_id as uoa_created_by_user_id,
               uoa.updated_at as uoa_updated_at,
               uoa.updated_by_user_id as uoa_updated_by_user_id,
               uoa.profile as uoa_profile
            from public.user u
            join public.user_organization_association uoa
            on u.id = uoa.user_id
            """,
        )
        params: list[BindParameter[Any]] = []  # type: ignore[explicit-any] # TODO: fix-any-annotation

        if active_users_only:
            # add status filter to the SQL to get active users only
            stmt = text(stmt.text + " and uoa.status = :status")
            params.append(
                bindparam(
                    key="status", value=UserOrganizationAssociationStatus.ACTIVE.name
                )
            )

        if not include_super_admins:
            # add user_role filter to exclude super admins
            stmt = text(
                stmt.text + " and (uoa.roles is null OR :role_name != ALL(uoa.roles))"
            )
            params.append(
                bindparam(
                    key="role_name",
                    value=UserRole.SUPER_ADMIN.name,
                )
            )

        if not include_hidden_users:
            # hidden users are special users created for offline sync (like email, crm
            # imports etc). normally they should not be shown, unless explicitly requested
            stmt = text(stmt.text + " and uoa.status != :hidden_status")
            params.append(
                bindparam(
                    key="hidden_status",
                    value=UserOrganizationAssociationStatus.HIDDEN.name,
                )
            )

        if user_ids_filter:
            stmt = text(stmt.text + " and u.id in :user_ids")
            params.append(
                bindparam(key="user_ids", value=user_ids_filter, expanding=True)
            )

        stmt = stmt.bindparams(*params)

        rows = await self.engine.all(stmt)

        return [
            (
                User.from_row(row, column_alias_prefix="u_"),
                UserOrganizationAssociation.from_row(row, column_alias_prefix="uoa_"),
            )
            for row in rows
        ]

    async def can_user_access_organization(
        self,
        user_id: UUID,
        organization_id: UUID,
    ) -> bool:
        existing_association = await self._find_unique_by_column_values(
            UserOrganizationAssociation,
            user_id=user_id,
            organization_id=organization_id,
        )
        return (
            existing_association.status == UserOrganizationAssociationStatus.ACTIVE
            if existing_association
            else False
        )

    async def find_user_by_id_or_error(self, user_id: UUID) -> User:
        user = await self.find_by_primary_key(
            User,
            id=user_id,
        )
        if not user:
            raise ResourceNotFoundError(
                "the user is neither not exist or already deleted.",
            )
        return user

    async def find_user_organization_association_by_user_org_identifier(
        self,
        user_org_identifier: str,
    ) -> UserOrganizationAssociation:
        user_org_association = await self._find_unique_by_column_values(
            UserOrganizationAssociation,
            user_org_identifier=user_org_identifier,
        )
        if not user_org_association:
            raise ResourceNotFoundError(
                f"the user_org_association for user_org_identifier: {user_org_identifier} is neither not exist or already deleted.",
            )
        return user_org_association

    async def update_user_org_association_status_by_user_id_and_org_id(
        self,
        user_id: UUID,
        organization_id: UUID,
        status: UserOrganizationAssociationStatus,
    ) -> UserOrganizationAssociation | None:
        return await self._update_unique_by_column_values(
            UserOrganizationAssociation,
            column_value_to_query={
                "organization_id": organization_id,
                "user_id": user_id,
            },
            column_to_update={"status": status},
        )

    async def find_user_by_linkedin_url(self, linkedin_url: str) -> User:
        queried_user = await self._find_unique_by_column_values(
            User, linkedin_url=linkedin_url
        )
        if not queried_user:
            raise ResourceNotFoundError(
                f"the user with linkedin_url: {linkedin_url} is neither not exist or already deleted."
            )
        return queried_user

    async def update_user_avatar_s3_key(
        self, user_id: UUID, avatar_s3_key: str
    ) -> None:
        # update the specified user with the new avatar_file_id
        await self._update_unique_by_column_values(
            User,
            column_value_to_query={"id": user_id},
            column_to_update={"avatar_s3_key": avatar_s3_key},
        )

        logger.info(f"updated user {user_id} with avatar_s3_key: {avatar_s3_key}")

    async def get_num_active_users_by_organization_id(
        self,
        organization_id: UUID,
    ) -> int:
        """
        This count excludes super admins.
        """
        stmt = text("""
        select  count(*)
         from   public.user u
                join public.user_organization_association uoa
                on u.id = uoa.user_id
         where  uoa.organization_id = :organization_id
          and   uoa.status = :status
          and   :exclude_role <> ANY(uoa.roles)
        """).bindparams(
            organization_id=organization_id,
            status=UserOrganizationAssociationStatus.ACTIVE.name,
            exclude_role=UserRole.SUPER_ADMIN.name,
        )
        count = await self.engine.at_most_one(stmt)
        return count[0] if count else 0
