import uuid
from uuid import UUI<PERSON>

from sqlalchemy import text

from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.signature import Signature
from salestech_be.util.time import zoned_utc_now


class SignatureRepository(GenericRepository):
    async def create_signature(
        self,
        signature_name: str,
        signature_html: str,
        organization_id: UUID,
        user_id: UUID,
        attachment_ids: list[UUID] | None = None,
    ) -> Signature:
        now = zoned_utc_now()
        return await self.insert(
            Signature(
                id=uuid.uuid4(),
                organization_id=organization_id,
                owner_user_id=user_id,
                name=signature_name,
                signature_html=signature_html,
                attachment_ids=attachment_ids,
                created_by_user_id=user_id,
                created_at=now,
                updated_at=now,
            )
        )

    async def find_signatures_by_ids(
        self,
        organization_id: UUID,
        signature_ids: list[UUID],
    ) -> list[Signature]:
        stmt = text(
            """
            SELECT * FROM signature
            WHERE id = ANY(:signature_ids)
            AND organization_id = :organization_id
            AND deleted_at IS NULL
            """
        ).bindparams(
            signature_ids=signature_ids,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        return await Signature.bulk_from_rows(rows=rows)

    async def find_signatures_by_organization_id(
        self,
        organization_id: UUID,
    ) -> list[Signature]:
        return await self._find_by_column_values(
            Signature, organization_id=organization_id
        )

    async def find_signature_by_name(
        self,
        organization_id: UUID,
        name: str,
    ) -> Signature | None:
        return await self._find_unique_by_column_values(
            Signature, organization_id=organization_id, name=name
        )
