from asyncio import TaskGroup
from collections import defaultdict
from collections.abc import Mapping
from contextlib import nullcontext
from typing import Annotated, Named<PERSON><PERSON><PERSON>, assert_never, cast
from uuid import UUID

from fastapi import Depends
from frozendict import frozendict
from pydantic import EmailStr
from sqlalchemy import TextClause, bindparam, text
from sqlalchemy.exc import IntegrityError

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ConcurrentModificationError,
    ConflictResourceError,
    ErrorDetails,
    IllegalStateError,
    ResourceNotFoundError,
)
from salestech_be.common.exception.exception import (
    ConflictErrorDetails,
    InvalidArgumentError,
    ReferentialViolationError,
    ReferentialViolationErrorDetails,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.schema_manager.std_object_field_identifier import (
    ContactField,
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    ContactRelationship,
)
from salestech_be.common.type.contact import (
    Contact<PERSON><PERSON>nel<PERSON>abel,
    ContactChannelType,
)
from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.core.contact.types_v2 import (
    ContactAccountAssociationInfo,
    ContactEmailInfo,
    ContactSearchResult,
)
from salestech_be.core.metadata.converter import contact_account_role_from_db
from salestech_be.core.metadata.types import ContactAccountRole
from salestech_be.db.dao.crm_sync_repository import CRMSyncRepository
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.contact_dto import ContactAccountAssociationJourney, ContactDto
from salestech_be.db.models.account import (
    Account,
)
from salestech_be.db.models.address import Address
from salestech_be.db.models.contact import (
    Contact,
    ContactPrimaryAccountIdUpdate,
    ContactUpdate,
    ContactUpdateCondition,
    CreateContactAccountRoleRequest,
    CreateContactRequest,
    CreateDbContactPhoneNumberRequest,
    UpdateContactEmailAccountAssociationRequest,
    UpdateContactEmailRequest,
    UpdateContactRequest,
)
from salestech_be.db.models.contact_account_association import (
    ContactAccountAssociation,
    ContactAccountAssociationArchivalUpdate,
    ContactAccountAssociationUpdate,
)
from salestech_be.db.models.contact_email import (
    ContactEmail,
    ContactEmailAccountAssociation,
    ContactEmailAccountAssociationUpdate,
    ContactEmailAccountAssociationUpdateCondition,
    ContactEmailUpdate,
    ContactEmailUpdateCondition,
)
from salestech_be.db.models.contact_generic_info import (
    ContactChannelAccountAssociationT,
    ContactChannelT,
    contact_channel_type_to_asso_table_model,
    contact_channel_type_to_table_model,
)
from salestech_be.db.models.contact_phone_number import (
    ContactPhoneNumber,
    ContactPhoneNumberAccountAssociation,
    ContactPhoneNumberAccountAssociationUpdate,
    ContactPhoneNumberAccountAssociationUpdateCondition,
    ContactPhoneNumberUpdate,
    ContactPhoneNumberUpdateCondition,
)
from salestech_be.db.models.contact_pipeline_association import (
    ContactPipelineAssociation,
)
from salestech_be.db.models.core.base import DBModel
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.copy_util import strict_model_copy
from salestech_be.util.pydantic_types.str import (
    PhoneNumber,
    PhoneNumberWithExtension,
    validate_e164,
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none, one_row_or_none

logger = get_logger()


class UpsertContactAccountAssociationResult(NamedTuple):
    updated_contact: Contact | None
    upserted: ContactAccountAssociation
    demoted_sibling: ContactAccountAssociation | None
    promoted_sibling: ContactAccountAssociation | None


class ArchiveContactAccountAssociationResult(NamedTuple):
    updated_contact: Contact | None
    archived: ContactAccountAssociation
    promoted_sibling: ContactAccountAssociation | None


class PatchContactAccountAssociationResult(NamedTuple):
    updated_contact: Contact | None
    updated: ContactAccountAssociation
    promoted_sibling: ContactAccountAssociation | None
    demoted_sibling: ContactAccountAssociation | None


class UpsertContactEmailAccountAssociationResult(NamedTuple):
    upserted_contact_email_account_association: ContactEmailAccountAssociation
    promoted_contact_account_primary_sibling: ContactEmailAccountAssociation | None
    demoted_contact_account_primary_sibling: ContactEmailAccountAssociation | None


class UpsertContactEmailResult(NamedTuple):
    updated_contact: Contact | None
    upserted_contact_email: ContactEmail
    promoted_contact_primary_sibling: ContactEmail | None
    demoted_contact_primary_sibling: ContactEmail | None
    upserted_contact_email_account_association_results: list[
        UpsertContactEmailAccountAssociationResult
    ]


class UpsertContactPhoneNumberAccountAssociationResult(NamedTuple):
    upserted_contact_phone_number_account_association: (
        ContactPhoneNumberAccountAssociation
    )
    promoted_contact_account_primary_sibling: (
        ContactPhoneNumberAccountAssociation | None
    )
    demoted_contact_account_primary_sibling: ContactPhoneNumberAccountAssociation | None


class UpsertContactPhoneNumberResult(NamedTuple):
    updated_contact: Contact | None
    upserted_contact_phone_number: ContactPhoneNumber
    promoted_contact_primary_sibling: ContactPhoneNumber | None
    demoted_contact_primary_sibling: ContactPhoneNumber | None
    upserted_contact_phone_number_account_association_results: list[
        UpsertContactPhoneNumberAccountAssociationResult
    ]


class UpdateContactEmailResult(NamedTuple):
    updated_contact_email: ContactEmail
    updated_alter_contact_email: ContactEmail | None


class UpdateContactEmailAccountAssociationResult(NamedTuple):
    updated_contact_email_account_association: ContactEmailAccountAssociation
    updated_alter_contact_email_account_association: (
        ContactEmailAccountAssociation | None
    )

    contact_email: ContactEmail
    alter_contact_email: ContactEmail | None


class DeleteContactEmailResult(NamedTuple):
    deleted: ContactEmail
    deleted_email_account_associations: list[ContactEmailAccountAssociation]
    promoted: ContactEmail | None


class DeleteContactPhoneNumberResult(NamedTuple):
    deleted: ContactPhoneNumber
    deleted_phone_number_account_associations: list[
        ContactPhoneNumberAccountAssociation
    ]
    promoted: ContactPhoneNumber | None


class DeleteContactEmailAccountAssociationResult(NamedTuple):
    deleted_email_account_association: ContactEmailAccountAssociation
    promoted: ContactEmailAccountAssociation | None


class ArchiveContactEmailResult(NamedTuple):
    archived: ContactEmail
    archived_email_account_associations: list[ContactEmailAccountAssociation]


class UnArchiveContactEmailResult(NamedTuple):
    unarchived: ContactEmail
    unarchived_email_account_associations: list[ContactEmailAccountAssociation]


class ContactIdDisplayNameFromEmail(DBModel):
    organization_id: UUID
    id: UUID
    display_name: str | None
    from_email: EmailStrLower


class ContactRepository(GenericRepository):
    def __init__(self, *, engine: Annotated[DatabaseEngine, Depends(get_db_engine)]):
        super().__init__(engine=engine)
        self.crm_sync_repo = CRMSyncRepository(engine=engine)

    ########################################
    # Contact Repository V3 Methods Starts #
    ########################################

    async def create_contact(
        self,
        *,
        user_id: UUID,
        organization_id: UUID,
        req: CreateContactRequest,
        overwrite_archived_emails: bool = False,
    ) -> ContactDto:
        """
        Create a new contact with emails(optional) in the database.
        - If a contact with a same email and organization_id already exists,
            raise a ConflictResourceError with code CONTACT_ALREADY_EXISTS_WITH_EMAIL.
        - If a contact has multiple primary emails,
            raise a InvalidArgumentError with code CONTACT_HAVE_DUPLICATE_PRIMARY_EMAILS.
        - If a contact set all emails as regular,
            raise a InvalidArgumentError with code CONTACT_HAVE_NO_PRIMARY_EMAIL.
        - If a contact doesn't specify any emails type, set a default primary email.
        """
        contact = req.contact.to_db_inserts(organization_id)

        (
            primary_contact_email,
            primary_contact_email_account_associations,
        ) = await self._create_contact_set_default_primary_email(
            req=req,
            organization_id=organization_id,
            user_id=user_id,
            contact_id=contact.id,
        )

        (
            primary_contact_phone_number,
            primary_contact_phone_number_account_associations,
        ) = await self._create_contact_set_default_primary_phone_number(
            req=req,
            organization_id=organization_id,
            user_id=user_id,
            contact_id=contact.id,
        )
        await self._create_contact_is_contact_unique(
            organization_id=organization_id,
            primary_contact_email=primary_contact_email,
            req=req,
        )

        contact = await self._compatible_contact_fields(
            primary_contact_email=primary_contact_email,
            primary_contact_phone_number=primary_contact_phone_number,
            primary_account_association=req.primary_account_role,
            contact=contact,
        )

        _should_in_txn = bool(req.contact_account_roles or req.contact_emails)
        async with self.engine.begin() if _should_in_txn else nullcontext():
            if address := req.contact.to_address_db_insert():
                await self.insert(address)
            await self.insert(contact)
            if _primary_account_role := req.primary_account_role:
                await self.upsert_contact_account_association(
                    _in_nested_transaction=_should_in_txn,
                    association=_primary_account_role.to_db_inserts(
                        organization_id=organization_id,
                        user_id=user_id,
                        contact_id=contact.id,
                        # Replicate contact title to contact account association title if title is not set
                        # for primary account role
                        contact_title=req.contact.title,
                    ),
                )

            for _non_primary_account_role in req.additional_account_roles:
                await self.upsert_contact_account_association(
                    _in_nested_transaction=_should_in_txn,
                    association=_non_primary_account_role.to_db_inserts(
                        organization_id=organization_id,
                        user_id=user_id,
                        contact_id=contact.id,
                    ),
                )
            try:
                if primary_contact_email:
                    await self.upsert_contact_email(
                        _in_nested_transaction=_should_in_txn,
                        overwrite_archived=overwrite_archived_emails,
                        contact_email=primary_contact_email,
                        contact_email_account_associations=primary_contact_email_account_associations,
                    )

                for _non_primary_contact_email in req.additional_contact_emails:
                    if not _non_primary_contact_email or (
                        primary_contact_email
                        and _non_primary_contact_email.email
                        == primary_contact_email.email
                    ):
                        continue

                    (
                        contact_email_to_upsert,
                        contact_email_account_associations_to_upsert,
                    ) = _non_primary_contact_email.to_db_inserts(
                        organization_id=organization_id,
                        user_id=user_id,
                        contact_id=contact.id,
                    )

                    await self.upsert_contact_email(
                        _in_nested_transaction=_should_in_txn,
                        overwrite_archived=overwrite_archived_emails,
                        contact_email=contact_email_to_upsert,
                        contact_email_account_associations=contact_email_account_associations_to_upsert,
                    )

                await self._create_upsert_contact_phone_number(
                    user_id=user_id,
                    organization_id=organization_id,
                    contact_id=contact.id,
                    _should_in_txn=_should_in_txn,
                    primary_contact_phone_number=primary_contact_phone_number,
                    primary_contact_phone_number_account_associations=primary_contact_phone_number_account_associations,
                    additional_contact_phone_numbers=req.additional_contact_phone_numbers,
                )
            except ConflictResourceError as e:
                additional_error_details = e.additional_error_details
                conflict_error_details = self._rewrite_conflict_error_detail(
                    additional_error_details
                )
                raise ConflictResourceError(
                    additional_error_details=conflict_error_details
                )
            return (
                await self.map_contact_dto_by_contact_id(
                    organization_id=organization_id,
                    contact_ids={contact.id},
                )
            )[contact.id]

    async def _compatible_contact_fields(
        self,
        primary_contact_email: ContactEmail | None,
        primary_contact_phone_number: ContactPhoneNumber | None,
        primary_account_association: CreateContactAccountRoleRequest | None,
        contact: Contact,
    ) -> Contact:
        if primary_contact_email:
            contact = strict_model_copy(
                contact, primary_email=primary_contact_email.email
            )

        if primary_contact_phone_number:
            contact = strict_model_copy(
                contact,
                primary_phone_number=validate_e164(
                    primary_contact_phone_number.phone_number
                ),
            )

        if primary_account_association:
            contact = strict_model_copy(
                contact,
                primary_account_id=primary_account_association.account_id,
            )
        return contact

    async def _create_upsert_contact_phone_number(
        self,
        user_id: UUID,
        organization_id: UUID,
        contact_id: UUID,
        _should_in_txn: bool,
        primary_contact_phone_number: ContactPhoneNumber | None,
        primary_contact_phone_number_account_associations: list[
            ContactPhoneNumberAccountAssociation
        ],
        additional_contact_phone_numbers: list[CreateDbContactPhoneNumberRequest],
    ) -> None:
        if primary_contact_phone_number:
            await self.upsert_contact_phone_number(
                _in_nested_transaction=_should_in_txn,
                contact_phone_number=primary_contact_phone_number,
                contact_phone_number_account_associations=primary_contact_phone_number_account_associations,
            )

        for _non_primary_contact_phone_number in additional_contact_phone_numbers:
            if not _non_primary_contact_phone_number or (
                primary_contact_phone_number
                and _non_primary_contact_phone_number.phone_number
                == primary_contact_phone_number.phone_number
            ):
                continue

            (
                contact_phone_number_to_upsert,
                contact_phone_number_account_associations_to_upsert,
            ) = _non_primary_contact_phone_number.to_db_inserts(
                organization_id=organization_id,
                user_id=user_id,
                contact_id=contact_id,
            )

            await self.upsert_contact_phone_number(
                _in_nested_transaction=_should_in_txn,
                contact_phone_number=contact_phone_number_to_upsert,
                contact_phone_number_account_associations=contact_phone_number_account_associations_to_upsert,
            )

    def _rewrite_conflict_error_detail(
        self,
        additional_error_details: ConflictErrorDetails | None,
    ) -> ConflictErrorDetails | None:
        if not additional_error_details or not additional_error_details.reference_id:
            return additional_error_details
        conflicted_existing_object_attrs = (
            additional_error_details.conflicted_existing_object_attrs
        )
        # archived contact
        if conflicted_existing_object_attrs.get("archived_at"):
            code = "CONTACT_EMAIL_ALREADY_EXISTS_ARCHIVED_CONTACT"
            detail = "try to unarchive the existing contact or overwrite contact email"
        # active contact & archived email
        elif conflicted_existing_object_attrs.get("existing_email_archive_at"):
            code = "CONTACT_EMAIL_ALREADY_EXISTS_ACTIVE_CONTACT_ARCHIVED_EMAIL"
            detail = "contact email conflict, try to update the existing contact or overwrite contact email"
        # active contact & active email
        else:
            code = "CONTACT_EMAIL_ALREADY_EXISTS_ACTIVE_CONTACT_ACTIVE_EMAIL"
            detail = "contact email conflict, try to update the existing contact"
        return ConflictErrorDetails(
            code=code,
            details=detail,
            conflicted_existing_object=StdObjectIdentifiers.contact.identifier,
            reference_id=additional_error_details.reference_id,
            conflicted_existing_object_attrs=conflicted_existing_object_attrs,
        )

    async def _create_contact_is_contact_unique(
        self,
        organization_id: UUID,
        primary_contact_email: ContactEmail | None,
        req: CreateContactRequest,
    ) -> bool:
        if not req.contact_emails or not primary_contact_email:
            return True

        primary_email = primary_contact_email.email
        existing_contact = await self.find_contact_by_primary_email(
            organization_id=organization_id, primary_email=primary_email
        )

        if existing_contact:
            raise ConflictResourceError(
                additional_error_details=ConflictErrorDetails(
                    code="CONTACT_EMAIL_ALREADY_EXISTS_ACTIVE_CONTACT_ACTIVE_EMAIL",
                    details="contact email conflict, try to update the existing contact",
                    conflicted_existing_object=StdObjectIdentifiers.contact.identifier,
                    reference_id=str(existing_contact.id) if existing_contact else None,
                    conflicted_existing_object_attrs={
                        ContactField.organization_id: existing_contact.organization_id,
                        ContactField.id: existing_contact.id,
                        ContactField.display_name: existing_contact.display_name,
                        ContactField.archived_at: None,
                        "existing_email_archive_at": None,
                        "conflict_email": str(primary_email),
                    },
                )
            )
        return True

    async def _create_contact_check_contact_emails_req_valid(
        self, req: CreateContactRequest
    ) -> None:
        pass

    async def _create_contact_set_default_primary_email(
        self,
        organization_id: UUID,
        user_id: UUID,
        req: CreateContactRequest,
        contact_id: UUID,
    ) -> tuple[ContactEmail | None, list[ContactEmailAccountAssociation]]:
        if not req.contact_emails:
            return None, []

        if req.primary_contact_email:
            primary_contact_email, primary_contact_email_account_associations = (
                req.primary_contact_email.to_db_inserts(
                    organization_id=organization_id,
                    user_id=user_id,
                    contact_id=contact_id,
                )
            )
            return primary_contact_email, primary_contact_email_account_associations
        if len(req.additional_contact_emails) == 1:
            primary_contact_email, primary_contact_email_account_associations = (
                strict_model_copy(
                    req.additional_contact_emails[0], is_contact_primary=True
                ).to_db_inserts(
                    organization_id=organization_id,
                    user_id=user_id,
                    contact_id=contact_id,
                )
            )
            return primary_contact_email, primary_contact_email_account_associations
        else:
            # none contact email set as primary
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code="CONTACT_HAVE_NO_PRIMARY_EMAIL",
                    details="contact_emails have no primary email.",
                )
            )

    async def _create_contact_set_default_primary_phone_number(
        self,
        organization_id: UUID,
        user_id: UUID,
        req: CreateContactRequest,
        contact_id: UUID,
    ) -> tuple[ContactPhoneNumber | None, list[ContactPhoneNumberAccountAssociation]]:
        if not req.contact_phone_numbers:
            return None, []

        if req.primary_contact_phone_number:
            (
                primary_contact_phone_number,
                primary_contact_phone_number_account_associations,
            ) = req.primary_contact_phone_number.to_db_inserts(
                organization_id=organization_id,
                user_id=user_id,
                contact_id=contact_id,
            )
            return (
                primary_contact_phone_number,
                primary_contact_phone_number_account_associations,
            )
        if len(req.additional_contact_phone_numbers) == 1:
            (
                primary_contact_phone_number,
                primary_contact_phone_number_account_associations,
            ) = strict_model_copy(
                req.additional_contact_phone_numbers[0], is_contact_primary=True
            ).to_db_inserts(
                organization_id=organization_id,
                user_id=user_id,
                contact_id=contact_id,
            )
            return (
                primary_contact_phone_number,
                primary_contact_phone_number_account_associations,
            )
        else:
            # none contact email set as primary
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code="CONTACT_HAVE_NO_PRIMARY_PHONE_NUMBER",
                    details="contact_emails have no primary phone number.",
                )
            )

    async def list_contacts_by_ids_untenanted(
        self,
        ids: list[UUID],
        exclude_deleted_or_archived: bool = True,
    ) -> list[Contact]:
        if not ids:
            return []
        return await self._find_by_column_values(
            Contact,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            id=ids,
        )

    async def map_contact_dto_by_contact_id(
        self,
        *,
        organization_id: UUID,
        contact_ids: set[UUID],
        exclude_deleted_or_archived: bool = True,
    ) -> frozendict[UUID, ContactDto]:
        list_contacts_stmt = text(
            f"""
            SELECT * FROM contact WHERE
                organization_id = :organization_id
                AND id = any(:contact_ids)
                {"AND archived_at IS NULL" if exclude_deleted_or_archived else ""}
            """  # noqa: S608
        ).bindparams(
            organization_id=organization_id,
            contact_ids=list(contact_ids),
        )
        rows = await self.engine.all(list_contacts_stmt)
        contacts = await Contact.bulk_from_rows(rows=rows)
        _contact_ids = {contact.id for contact in contacts}
        contact_emails_by_contact_id = cast(
            frozendict[UUID, list[ContactEmail]],
            await self.map_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_ids=_contact_ids,
            ),
        )

        contact_email_account_associations_by_contact_id = cast(
            frozendict[UUID, list[ContactEmailAccountAssociation]],
            await self.map_contact_channel_info_account_association_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_ids=_contact_ids,
            ),
        )
        contact_phone_numbers_by_contact_id = cast(
            frozendict[UUID, list[ContactPhoneNumber]],
            await self.map_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.PHONE_NUMBER,
                organization_id=organization_id,
                contact_ids=_contact_ids,
            ),
        )

        contact_phone_number_account_associations_by_contact_id = cast(
            frozendict[UUID, list[ContactPhoneNumberAccountAssociation]],
            await self.map_contact_channel_info_account_association_by_contact_id(
                contact_channel_type=ContactChannelType.PHONE_NUMBER,
                organization_id=organization_id,
                contact_ids=_contact_ids,
            ),
        )

        contact_account_associations_by_contact_id = (
            await self.map_active_contact_account_association_by_contact_ids(
                organization_id=organization_id,
                contact_ids=_contact_ids,
            )
        )

        contact_dto_by_contact_id: dict[UUID, ContactDto] = {}
        for contact in contacts:
            contact_dto_by_contact_id[contact.id] = ContactDto(
                contact=contact,
                emails=contact_emails_by_contact_id.get(contact.id, []),
                email_account_associations=contact_email_account_associations_by_contact_id.get(
                    contact.id, []
                ),
                phone_numbers=contact_phone_numbers_by_contact_id.get(contact.id, []),
                phone_number_account_associations=contact_phone_number_account_associations_by_contact_id.get(
                    contact.id, []
                ),
                active_account_associations=contact_account_associations_by_contact_id.get(
                    contact.id, []
                ),
            )
        return frozendict(contact_dto_by_contact_id)

    async def upsert_contact_account_association(
        self,
        *,
        _in_nested_transaction: bool = False,
        association: ContactAccountAssociation,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> UpsertContactAccountAssociationResult:
        """
        Add a contact account association to the database.
        - If the specified contact is archived, raise an ResourceNotFoundError.
        - If the specified contact account association is already archived, create a new
            association to track the new journey.
        - If the specified contact account association is not archived, update the
          association accordingly.
        - Handling of "primary contact account association" is also included in this method:
            - if the requested association is primary, and the contact's primary account id is not the same as
              the requested association's account id or the contact has no primary account id, then we set the
              requested association as primary
            - if the contact's primary account id is the same as the requested association's account id,
              and the requested association is not primary, then we unset the requested association as primary
              and promote the most recently added association to be the new primary association
        """
        # make sure both contact and account exist (account could ba archived)
        await self.find_by_tenanted_primary_key_or_fail(
            Contact,
            exclude_deleted_or_archived=True,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            organization_id=association.organization_id,
            id=association.contact_id,
        )

        await self.find_by_tenanted_primary_key_or_fail(
            Account,
            exclude_deleted_or_archived=False,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            organization_id=association.organization_id,
            id=association.account_id,
        )

        # if the requested association is primary,
        # or the contact has no primary account id, then we set the requested association as primary
        current_primary_account_id = await self.get_primary_account_id_by_contact_id(
            organization_id=association.organization_id,
            contact_id=association.contact_id,
        )
        set_as_primary = association.is_primary or (not current_primary_account_id)

        # if the contact's primary account id is the same as the requested association's account id,
        # and the requested association is not primary, then we unset the requested association as primary
        unset_from_current_primary = (
            current_primary_account_id == association.account_id
            and not association.is_primary
        )

        # if we are unsetting the requested association as primary,
        # find the candidate primary association after upsert
        candidate_primary_association_after_upsert: ContactAccountAssociation | None = (
            await self._find_candidate_primary_association(
                organization_id=association.organization_id,
                contact_id=association.contact_id,
                excluded_account_ids={association.account_id},
            )
            if unset_from_current_primary
            else None
        )

        if unset_from_current_primary and (
            not candidate_primary_association_after_upsert
        ):
            # when there is no candidate primary association after upsert,
            # we set the requested association as primary to guarantee there
            # is a primary association
            unset_from_current_primary = False
            set_as_primary = True

        updated_contact: Contact | None = (
            None  # updated_contact is None if contact is not updated
        )
        demoted_primary_association: ContactAccountAssociation | None = None
        promoted_association: ContactAccountAssociation | None = None
        upserted_association: ContactAccountAssociation
        _post_update_primary_account_id: UUID | None = current_primary_account_id

        async with (
            self.engine.begin() if (not _in_nested_transaction) else nullcontext()
        ):
            if set_as_primary:
                demoted_primary_association = (
                    ContactAccountAssociation.from_row_or_none(
                        await self.engine.at_most_one(
                            self._get_remove_primary_account_association_statement(
                                organization_id=association.organization_id,
                                contact_id=association.contact_id,
                                user_id=association.updated_by_user_id,
                                excluded_account_id=association.account_id,
                            )
                        )
                    )
                )
            try:
                upserted_association = ContactAccountAssociation.from_row(
                    await self.engine.one(
                        self._get_upsert_contact_account_association_statement(
                            contact_account_association=association,
                            set_as_primary=set_as_primary,
                        )
                    )
                )
            except IntegrityError:
                raise ConcurrentModificationError(
                    f"The contact account association being updated is modified by another process. Please retry: {association}"
                )
            if upserted_association.is_primary:
                _post_update_primary_account_id = upserted_association.account_id

            if candidate_primary_association_after_upsert:
                promoted_association = await self.update_by_tenanted_primary_key(
                    ContactAccountAssociation,
                    exclude_deleted_or_archived=True,
                    organization_id=association.organization_id,
                    primary_key_to_value=candidate_primary_association_after_upsert.primary_key_to_value(),
                    column_to_update=ContactAccountAssociationUpdate(
                        is_primary=True,
                        updated_by_user_id=association.updated_by_user_id,
                    ),
                )
                if not promoted_association:
                    raise ConcurrentModificationError(
                        "The candidate primary contact account association being updated is modified by another process. "
                        f"Please retry: contact account association (id={candidate_primary_association_after_upsert.id})"
                    )
                else:
                    _post_update_primary_account_id = promoted_association.account_id

            if _post_update_primary_account_id != current_primary_account_id:
                updated_contact = await self.update_by_tenanted_primary_key(
                    Contact,
                    exclude_deleted_or_archived=True,
                    exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                    organization_id=association.organization_id,
                    primary_key_to_value={"id": association.contact_id},
                    column_to_update=ContactPrimaryAccountIdUpdate(
                        primary_account_id=_post_update_primary_account_id,
                        updated_at=association.updated_at,
                        updated_by_user_id=association.updated_by_user_id,
                    ),
                    # make sure no one else has updated the primary contact id
                    column_condition=ContactUpdateCondition(
                        primary_account_id=current_primary_account_id
                    )
                    if current_primary_account_id
                    else None,
                )
                if not updated_contact:
                    raise ConcurrentModificationError(
                        "The contact being updated is modified by another process. "
                        f"Please retry: contact (id={association.contact_id})"
                    )
            return UpsertContactAccountAssociationResult(
                upserted=upserted_association,
                demoted_sibling=demoted_primary_association,
                promoted_sibling=promoted_association,
                updated_contact=updated_contact,
            )

    async def archive_contact_account_association(
        self,
        *,
        _in_nested_transaction: bool = False,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        account_id: UUID,
    ) -> ArchiveContactAccountAssociationResult:
        """
        Remove a contact account association from the database.
        - If the specified contact account association does not exist, raise a
          ResourceNotFoundError.
        - If the specified contact account association is already archived, return as-is.
        - If the contact <> account association has an active pipeline association, raise
          an InvalidArgumentError.
        - If the specified contact account association is a primary association, promote
            the most recently added association to be the new primary association.
        """
        # first find the association
        if not (
            association := (
                await self.map_latest_contact_account_association_by_account_id_for_contact(
                    organization_id=organization_id,
                    contact_id=contact_id,
                    account_ids={account_id},
                )
            ).get(account_id)
        ):
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.CONTACT_ACCOUNT_ASSOCIATION_NOT_FOUND,
                    details="contact account association with organization_id, contact_id, and account_id not found.",
                )
            )

        if association.archived_at:
            return ArchiveContactAccountAssociationResult(
                updated_contact=None,
                archived=association,
                promoted_sibling=None,
            )

        if (
            _active_pipeline_associations
            := await self.list_contact_pipeline_associations(
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=account_id,
                exclude_deleted_or_archived=True,
            )
        ):
            _impacted_pipeline_ids = [
                association.pipeline_id for association in _active_pipeline_associations
            ]
            raise ReferentialViolationError(
                "contact <> account association has an active pipeline association",
                additional_error_details=ReferentialViolationErrorDetails(
                    error_code=ErrorCode.CONTACT_ACCOUNT_ASSOCIATION_HAS_ACTIVE_PIPELINE_ASSOCIATION,
                    violating_object=StdObjectIdentifiers.contact.identifier,
                    dependent_object=StdObjectIdentifiers.pipeline.identifier,
                    dependent_object_record_ids=_impacted_pipeline_ids,
                    violating_relationship_id=ContactRelationship.contact__from__contact_pipeline_role,
                ),
            )

        demote_from_primary = association.is_primary

        # check if the association is primary, if so, we need to promote another available association to be the primary
        # in the case we just find the most recently added association and make it primary
        candidate_primary_association = (
            await self._find_candidate_primary_association(
                organization_id=organization_id,
                contact_id=contact_id,
                excluded_account_ids={account_id},
            )
            if demote_from_primary
            else None
        )
        new_primary_account_id: UnsetAware[UUID | None] = UNSET
        if demote_from_primary:
            new_primary_account_id = (
                candidate_primary_association.account_id
                if candidate_primary_association
                else None
            )
        updated_contact: Contact | None = None
        promoted_sibling: ContactAccountAssociation | None = None
        async with (
            self.engine.begin() if (not _in_nested_transaction) else nullcontext()
        ):
            updated_association = not_none(
                await self.update_by_tenanted_primary_key(
                    ContactAccountAssociation,
                    exclude_deleted_or_archived=False,
                    organization_id=organization_id,
                    primary_key_to_value=association.primary_key_to_value(),
                    column_to_update=ContactAccountAssociationArchivalUpdate(
                        archived_by_user_id=user_id,
                        updated_by_user_id=user_id,
                    ),
                )
            )
            if candidate_primary_association:
                promoted_sibling = await self.update_by_tenanted_primary_key(
                    ContactAccountAssociation,
                    # ensure we don't update any archived associations, if it become archived, then
                    # it means another process has already archived this association
                    exclude_deleted_or_archived=True,
                    organization_id=organization_id,
                    primary_key_to_value=candidate_primary_association.primary_key_to_value(),
                    column_to_update=ContactAccountAssociationUpdate(
                        is_primary=True,
                        updated_by_user_id=user_id,
                    ),
                )
                if not promoted_sibling:
                    raise ConcurrentModificationError(
                        "The contact account association being updated is modified by another process. "
                        f"Please retry: contact account association (id={association.id})"
                    )
            if specified(new_primary_account_id):
                updated_contact = await self.update_by_tenanted_primary_key(
                    Contact,
                    # We include archived contacts here, since after archival,
                    # we should still be able to remove the association, as well as update the primary contact id
                    # caused by removal of the primary association
                    exclude_deleted_or_archived=False,
                    exclude_locked_by_integrity_jobs=False,
                    organization_id=organization_id,
                    primary_key_to_value={"id": contact_id},
                    column_to_update=ContactPrimaryAccountIdUpdate(
                        primary_account_id=new_primary_account_id,
                        updated_at=zoned_utc_now(),
                        updated_by_user_id=user_id,
                    ),
                    # make sure no one else has updated the primary contact id
                    column_condition=ContactUpdateCondition(
                        primary_account_id=association.account_id
                    ),
                )
                if not updated_contact:
                    raise ConcurrentModificationError(
                        "The contact being updated is modified by another process. "
                        f"Please retry: contact (id={contact_id})"
                    )
            # By the end of this, we do this pipeline association check again
            # check if the contact <> account association has an active pipeline association,
            # if so, raise an InvalidArgumentError to roll back the transaction.
            # this is to ensure we don't accidentally remove any contact <> account association
            # that is still being used in a pipeline due to a race condition.
            if (
                _active_pipeline_associations
                := await self.list_contact_pipeline_associations(
                    organization_id=organization_id,
                    contact_id=contact_id,
                    account_id=account_id,
                    exclude_deleted_or_archived=True,
                )
            ):
                _impacted_pipeline_ids = [
                    association.pipeline_id
                    for association in _active_pipeline_associations
                ]
                raise ReferentialViolationError(
                    "Contact has been added to another pipeline while being removed from this account, "
                    "cannot archive this contact <> account association now.",
                    additional_error_details=ReferentialViolationErrorDetails(
                        error_code=ErrorCode.CONTACT_ACCOUNT_ASSOCIATION_HAS_ACTIVE_PIPELINE_ASSOCIATION,
                        violating_object=StdObjectIdentifiers.contact.identifier,
                        dependent_object=StdObjectIdentifiers.pipeline.identifier,
                        dependent_object_record_ids=_impacted_pipeline_ids,
                        violating_relationship_id=ContactRelationship.contact__from__contact_pipeline_role,
                    ),
                )
            return ArchiveContactAccountAssociationResult(
                updated_contact=updated_contact,
                archived=updated_association,
                promoted_sibling=promoted_sibling,
            )

    async def patch_contact_account_association(
        self,
        *,
        _in_nested_transaction: bool = False,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID,
        update: ContactAccountAssociationUpdate,
    ) -> PatchContactAccountAssociationResult:
        """
        Update a contact account association in the database.
        - If the specified contact account association is already archived or not found, raise an
          ResourceNotFoundError.
        - If the specified contact <> account association is patched to be primary,
           demote the current primary association to be non-primary if different from the
           specified association.
        - If the specified contact <> account association is patched to be non-primary,
           promote the most recently added association to be the new primary association if the specified association is the currentprimary.
        """
        # find existing association
        if (
            not (
                association := (
                    await self.map_latest_contact_account_association_by_account_id_for_contact(
                        organization_id=organization_id,
                        contact_id=contact_id,
                        account_ids={account_id},
                    )
                ).get(account_id)
            )
            or association.archived_at
        ):
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.CONTACT_ACCOUNT_ASSOCIATION_NOT_FOUND,
                    details="contact account association with organization_id, contact_id, and account_id not found.",
                )
            )

        tentative_association = association.model_copy(
            update=update.flatten_specified_values()
        )

        upsert_result = await self.upsert_contact_account_association(
            _in_nested_transaction=_in_nested_transaction,
            association=tentative_association,
        )
        return PatchContactAccountAssociationResult(
            updated_contact=upsert_result.updated_contact,
            updated=upsert_result.upserted,
            demoted_sibling=upsert_result.demoted_sibling,
            promoted_sibling=upsert_result.promoted_sibling,
        )

    async def upsert_contact_email(
        self,
        *,
        _in_nested_transaction: bool = False,
        overwrite_archived: bool = False,
        contact_email: ContactEmail,
        contact_email_account_associations: list[ContactEmailAccountAssociation],
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> UpsertContactEmailResult:
        """
        Creates or updates a contact_email and contact_email_account_association record while managing primary email designations.

        This function handles complex logic for maintaining primary email states at both
        contact and account levels, including promotion and demotion of existing emails.

        Args:
            _in_nested_transaction (bool): If True, assumes operation is part of a larger transaction.
                If False, creates its own transaction. Defaults to False.
            overwrite_archived (bool): If the same email is found but archived, overwrite it.
            contact_email: The contact email context to create or update.
            contact_email_account_associations: The contact email account association context to create or update.

        Returns:
            UpsertContactEmailResult: A named tuple containing:
                - upserted_contact_email: The created or updated ContactEmail
                - upserted_contact_email_account_association: The created or updated ContactEmailAccountAssociation
                - updated_contact: The updated Contact if primary email changed, None otherwise
                - promoted_contact_primary_sibling: ContactEmail promoted to contact primary, if any
                - promoted_contact_account_primary_sibling: ContactEmailAccountAssociation promoted to account primary, if any
                - demoted_contact_primary_sibling: ContactEmail demoted from contact primary, if any
                - demoted_contact_account_primary_sibling: ContactEmailAccountAssociation demoted from account primary, if any

        Raises:
            ConflictResourceError: If the email already exists for a different contact
            ConcurrentModificationError: If concurrent updates prevent primary email changes
            ResourceNotFoundError: If the contact does not exist
            IllegalStateError: If required fields are missing or in invalid states

        Primary Email Handling:
            1. Contact Primary Email (is_contact_primary):
            - When setting a new contact primary email:
                * Existing primary email (if have) is demoted (is_contact_primary set to False)
                * Contact's primary_email field is updated to the new email
            - When setting a new contact non-primary email:
                * If no other emails exist, keeps current email as primary
            - When removing primary status:
                * Next available email by creation date is promoted to primary
                * Contact's primary_email field is updated to the promoted email
                * If no other emails exist, keeps current email as primary

            2. Contact Account Primary Email (is_contact_account_primary):
            - When setting a new contact account primary email:
                * Existing contact account primary email is demoted (is_contact_account_primary set to False)
                * New email becomes primary for that specific account
            - When removing contact account primary status or changing account:
                * Next available email for that account is promoted to primary
                * If no other emails exist for that account, keeps current as primary

        Transaction Handling:
            - Operations execute in a specific order to prevent deadlocks:
            1. Initial existence checks (within transaction)
            2. Contact updates
            3. Demote existing primaries
            4. Promote new primaries
            5. Final email upsert
            - All operations are atomic within a transaction
            - Uses optimistic locking via updated_at fields

        Concurrency Considerations:
            - Uses transactions to ensure consistency
            - Implements optimistic locking for updates
            - Handles race conditions through conditional updates
            - May retry on concurrent modification errors

        Notes:
            - A contact can have multiple emails but only one primary email
            - An account can have one primary email per contact
            - Primary status changes trigger updates to both email records and contact record
            - All operations are executed atomically within a transaction
            - Maintains referential integrity across all related tables
            - Updates timestamps for audit trail purposes
            - Validates existence of referenced entities
            - Preserves at least one primary email per contact
        """

        # Step 0: check if the email is already existed, raise error if yes but not the same contact
        # here returns an active email or archived email or None
        _matching_contact_email = one_row_or_none(
            [
                ce
                for ce in await self._find_by_column_values(
                    ContactEmail,
                    exclude_deleted_or_archived=False,
                    organization_id=contact_email.organization_id,
                    email=contact_email.email,
                )
                if not ce.deleted_at
            ]
        )

        # When the email is occupied by another contact, we raise conflict error if:
        # 1. the occupied contact email is active ( archived_at is None ) -> can't overwrite active contact_emails
        # 2. the occupied contact email is archived and we are not overwriting it (controlled by overwrite_archived=False)
        if (
            _matching_contact_email
            and _matching_contact_email.contact_id != contact_email.contact_id
            and (
                (_matching_contact_email.archived_at and not overwrite_archived)
                or not _matching_contact_email.archived_at
            )
        ):
            existing_contact = await self._find_unique_by_column_values(
                Contact,
                exclude_deleted_or_archived=False,
                exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                organization_id=contact_email.organization_id,
                id=_matching_contact_email.contact_id,
            )
            raise ConflictResourceError(
                additional_error_details=ConflictErrorDetails(
                    code="CONTACT_EMAIL_ALREADY_EXISTS",
                    error_code=ErrorCode.CONFLICT,
                    details="contact email with organization_id, contact_id, and email already exists.",
                    conflicted_existing_object=StdObjectIdentifiers.contact.identifier,
                    reference_id=str(existing_contact.id) if existing_contact else None,
                    conflicted_existing_object_attrs={
                        ContactField.organization_id: existing_contact.organization_id,
                        ContactField.id: existing_contact.id,
                        ContactField.display_name: existing_contact.display_name,
                        ContactField.archived_at: existing_contact.archived_at,
                        "conflict_email": _matching_contact_email.email,
                        "existing_email_archive_at": _matching_contact_email.archived_at,
                    }
                    if existing_contact
                    else {},
                )
            )

        # Step 1: validate contact existence
        contact = await self.find_by_tenanted_primary_key_or_fail(
            Contact,
            organization_id=contact_email.organization_id,
            id=contact_email.contact_id,
            exclude_deleted_or_archived=False,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )

        # Step 2: list all existing contact emails and potential primary contact email candidates for later use
        _all_existing_contact_emails: list[ContactEmail] = sorted(
            cast(
                list[ContactEmail],
                await self.list_contact_channel_infos_by_contact_id(
                    contact_channel_type=ContactChannelType.EMAIL,
                    organization_id=contact_email.organization_id,
                    contact_id=contact_email.contact_id,
                ),
            ),
            key=lambda x: x.created_at,
            reverse=True,
        )
        _contact_primary_contact_email_candidates: list[ContactEmail] = [
            ce for ce in _all_existing_contact_emails if ce.email != contact_email.email
        ]

        new_primary_contact_email: str | None = None
        updated_contact: Contact | None = None
        promoted_contact_primary_sibling: ContactEmail | None = None
        promoted_contact_account_primary_sibling: (
            ContactEmailAccountAssociation | None
        ) = None
        demoted_contact_primary_sibling: ContactEmail | None = None
        demoted_contact_account_primary_sibling: (
            ContactEmailAccountAssociation | None
        ) = None

        # Step 3: check if we need to unset the existing contact primary email and promote a new primary
        # there is only one case that we need to do this: the email is already existed as primary and we
        # are trying to demote it to non-primary

        _contact_primary_contact_email_candidate: ContactEmail | None = None
        if (
            _matching_contact_email
            and _matching_contact_email.is_contact_primary
            and not contact_email.is_contact_primary
            and _contact_primary_contact_email_candidates
        ):
            _contact_primary_contact_email_candidate = (
                _contact_primary_contact_email_candidates[0]
            )

        # note: it is possible that this email is the only existing email, in this case, we will always
        # set/keep it as primary
        if (
            not contact_email.is_contact_primary
            and not _contact_primary_contact_email_candidates
        ):
            contact_email = strict_model_copy(contact_email, is_contact_primary=True)

        desired_primary_contact_email = (
            contact_email.email
            if contact_email.is_contact_primary
            else (
                _contact_primary_contact_email_candidate.email
                if _contact_primary_contact_email_candidate
                else None
            )
        )
        new_primary_contact_email = (
            desired_primary_contact_email
            if desired_primary_contact_email
            and desired_primary_contact_email != contact.primary_email
            else None
        )

        # Step 4: perform the upsert in one transaction
        async with self.engine.begin() if not _in_nested_transaction else nullcontext():
            if new_primary_contact_email and not (
                updated_contact := await self.update_by_tenanted_primary_key(
                    Contact,
                    organization_id=contact_email.organization_id,
                    exclude_deleted_or_archived=False,
                    exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                    primary_key_to_value=contact.primary_key_to_value(),
                    column_to_update=ContactUpdate(
                        primary_email=new_primary_contact_email,
                        updated_by_user_id=contact_email.updated_by_user_id,
                    ),
                    column_condition=ContactUpdateCondition(
                        primary_email=contact.primary_email,
                    ),
                )
            ):
                raise ConcurrentModificationError(
                    f"failed to update primary_email for contact {contact_email.contact_id} in organization"
                )

            if contact_email.is_contact_primary:
                demoted_contact_primary_sibling = (
                    await self._unset_original_contact_primary_email(
                        organization_id=contact_email.organization_id,
                        contact_id=contact_email.contact_id,
                        new_primary_email=contact_email.email,
                    )
                )

            if _contact_primary_contact_email_candidate:
                demoted_contact_primary_sibling = await self._unset_original_contact_primary_email(
                    organization_id=contact_email.organization_id,
                    contact_id=contact_email.contact_id,
                    new_primary_email=_contact_primary_contact_email_candidate.email,
                )

                promoted_contact_primary_sibling = await self.update_by_tenanted_primary_key(
                    ContactEmail,
                    organization_id=contact_email.organization_id,
                    primary_key_to_value=_contact_primary_contact_email_candidate.primary_key_to_value(),
                    column_to_update=ContactEmailUpdate(
                        is_contact_primary=True,
                        updated_by_user_id=contact_email.updated_by_user_id,
                        updated_at=contact_email.updated_at,
                    ),
                    column_condition=ContactEmailUpdateCondition(
                        deleted_at=None,
                    ),
                )
                if not promoted_contact_primary_sibling:
                    raise ConcurrentModificationError(
                        f"failed to update primary_email for contact {contact_email.contact_id} in organization"
                    )

            if _matching_contact_email:
                upserted_contact_email = not_none(
                    await self.update_instance(
                        strict_model_copy(contact_email, id=_matching_contact_email.id),
                        exclude_deleted_or_archived=False,
                    )
                )
            else:
                upserted_contact_email = await self.insert(contact_email)

            upserted_contact_email_account_association_results: list[
                UpsertContactEmailAccountAssociationResult
            ] = []
            for association in contact_email_account_associations:
                # For a narrow edge case: if we need to unarchive/overwrite a contact_email occupied by another contact,
                # to find its original corresponding association records, we need to pass the original contact_email to target_contact_email
                (
                    upserted_contact_email_account_association,
                    promoted_contact_account_primary_sibling,
                    demoted_contact_account_primary_sibling,
                ) = await self.upsert_contact_email_account_association(
                    _in_nested_transaction=True,
                    target_contact_email=_matching_contact_email
                    or upserted_contact_email,
                    contact_email_account_association=association,
                )
                upserted_contact_email_account_association_results.append(
                    UpsertContactEmailAccountAssociationResult(
                        upserted_contact_email_account_association=upserted_contact_email_account_association,
                        promoted_contact_account_primary_sibling=promoted_contact_account_primary_sibling,
                        demoted_contact_account_primary_sibling=demoted_contact_account_primary_sibling,
                    )
                )
        return UpsertContactEmailResult(
            updated_contact=updated_contact,
            upserted_contact_email=upserted_contact_email,
            promoted_contact_primary_sibling=promoted_contact_primary_sibling,
            demoted_contact_primary_sibling=demoted_contact_primary_sibling,
            upserted_contact_email_account_association_results=upserted_contact_email_account_association_results,
        )

    async def upsert_contact_email_account_association(
        self,
        *,
        _in_nested_transaction: bool = False,
        target_contact_email: ContactEmail,
        contact_email_account_association: ContactEmailAccountAssociation,
    ) -> tuple[
        ContactEmailAccountAssociation,
        ContactEmailAccountAssociation | None,
        ContactEmailAccountAssociation | None,
    ]:
        """
        this method applies a similar logic as the upsert contact email
        """
        no_email_account_association_before = False
        if not await self.list_contact_channel_info_account_associations_by_contact_channel_info_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=target_contact_email.organization_id,
            contact_channel_info_id=target_contact_email.id,
        ):
            no_email_account_association_before = True
        _matching_contact_email_account_association = one_row_or_none(
            [
                cea
                for cea in await self._find_by_column_values(
                    ContactEmailAccountAssociation,
                    exclude_deleted_or_archived=False,
                    organization_id=target_contact_email.organization_id,
                    contact_id=target_contact_email.contact_id,
                    contact_email_id=target_contact_email.id,
                    account_id=contact_email_account_association.account_id,
                )
                if not cea.deleted_at
            ]
        )

        _all_existing_contact_email_account_associations: list[
            ContactEmailAccountAssociation
        ] = sorted(
            cast(
                list[ContactEmailAccountAssociation],
                await self.list_contact_channel_info_account_associations(
                    contact_channel_type=ContactChannelType.EMAIL,
                    organization_id=target_contact_email.organization_id,
                    contact_id=contact_email_account_association.contact_id,
                    account_id=contact_email_account_association.account_id,
                ),
            ),
            key=lambda x: x.created_at,
            reverse=True,
        )

        _contact_account_primary_contact_email_account_association_candidates: list[
            ContactEmailAccountAssociation
        ] = [
            cea
            for cea in _all_existing_contact_email_account_associations
            if cea.contact_email_id != target_contact_email.id
        ]

        upserted_contact_email_account_association: ContactEmailAccountAssociation
        promoted_contact_account_primary_sibling: (
            ContactEmailAccountAssociation | None
        ) = None
        demoted_contact_account_primary_sibling: (
            ContactEmailAccountAssociation | None
        ) = None

        _contact_account_primary_contact_email_account_association_candidate: (
            ContactEmailAccountAssociation | None
        ) = None
        if (
            _matching_contact_email_account_association
            and _matching_contact_email_account_association.is_contact_account_primary
            and not contact_email_account_association.is_contact_account_primary
            and _contact_account_primary_contact_email_account_association_candidates
        ):
            _contact_account_primary_contact_email_account_association_candidate = (
                _contact_account_primary_contact_email_account_association_candidates[0]
            )

        if (
            not contact_email_account_association.is_contact_account_primary
            and not _contact_account_primary_contact_email_account_association_candidates
        ):
            contact_email_account_association = strict_model_copy(
                contact_email_account_association, is_contact_account_primary=True
            )

        async with self.engine.begin() if not _in_nested_transaction else nullcontext():
            if contact_email_account_association.is_contact_account_primary:
                demoted_contact_account_primary_sibling = await self._unset_original_contact_account_primary_email(
                    organization_id=contact_email_account_association.organization_id,
                    contact_id=contact_email_account_association.contact_id,
                    account_id=contact_email_account_association.account_id,
                    new_contact_email_id=target_contact_email.id,
                )

            if _contact_account_primary_contact_email_account_association_candidate:
                demoted_contact_account_primary_sibling = await self._unset_original_contact_account_primary_email(
                    organization_id=contact_email_account_association.organization_id,
                    contact_id=contact_email_account_association.contact_id,
                    account_id=contact_email_account_association.account_id,
                    new_contact_email_id=_contact_account_primary_contact_email_account_association_candidate.contact_email_id,
                )

                promoted_contact_account_primary_sibling = await self.update_by_tenanted_primary_key(
                    ContactEmailAccountAssociation,
                    organization_id=contact_email_account_association.organization_id,
                    primary_key_to_value=_contact_account_primary_contact_email_account_association_candidate.primary_key_to_value(),
                    column_to_update=ContactEmailAccountAssociationUpdate(
                        is_contact_account_primary=True,
                        updated_by_user_id=contact_email_account_association.updated_by_user_id,
                        updated_at=contact_email_account_association.updated_at,
                    ),
                    column_condition=ContactEmailAccountAssociationUpdateCondition(
                        deleted_at=None,
                    ),
                )
                if not promoted_contact_account_primary_sibling:
                    raise ConcurrentModificationError(
                        f"failed to update primary_contact_email_account_association for contact {contact_email_account_association.contact_id} in organization"
                    )

        if _matching_contact_email_account_association:
            upserted_contact_email_account_association = not_none(
                await self.update_instance(
                    strict_model_copy(
                        contact_email_account_association,
                        id=_matching_contact_email_account_association.id,
                        contact_email_id=_matching_contact_email_account_association.contact_email_id,
                    ),
                    exclude_deleted_or_archived=False,
                )
            )
        else:
            upserted_contact_email_account_association = await self.insert(
                strict_model_copy(
                    contact_email_account_association,
                    contact_email_id=target_contact_email.id,
                )
            )

        # update contact email label if there is no account association before
        if no_email_account_association_before:
            await self.update_contact_email_label_by_contact_email_id(
                user_id=contact_email_account_association.updated_by_user_id,
                organization_id=target_contact_email.organization_id,
                contact_email_id=target_contact_email.id,
                label=ContactChannelLabel.WORK,
            )

        return (
            upserted_contact_email_account_association,
            promoted_contact_account_primary_sibling,
            demoted_contact_account_primary_sibling,
        )

    async def upsert_contact_phone_number(
        self,
        *,
        _in_nested_transaction: bool = False,
        contact_phone_number: ContactPhoneNumber,
        contact_phone_number_account_associations: list[
            ContactPhoneNumberAccountAssociation
        ],
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> UpsertContactPhoneNumberResult:
        """
        Creates or updates a contact_phone_number and contact_phone_number_account_association record.

        This function handles complex logic for maintaining primary phone number  states at both
        contact and account levels, including promotion and demotion of existing emails.

        Args:
            _in_nested_transaction (bool): If True, assumes operation is part of a larger transaction.
                If False, creates its own transaction. Defaults to False.
            overwrite_archived (bool): If the same email is found but archived, overwrite it.
            contact_phone: The contact phone number context to create or update.
            contact_phone_number_account_associations: The contact phone number account association context to create or update.

        Returns:
            UpsertContactPhoneNumberResult: A named tuple containing:
                - updated_contact: The created or updated ContactEmail
                - upserted_contact_phone_number: The created or updated ContactEmailAccountAssociation
                - updated_contact: The updated Contact if primary email changed, None otherwise
                - promoted_contact_primary_sibling: ContactEmail promoted to contact primary, if any
                - demoted_contact_primary_sibling: ContactEmail demoted from contact primary, if any
                - upserted_contact_phone_number_account_association_results
                    - promoted_contact_account_primary_sibling: ContactEmailAccountAssociation promoted to account primary, if any
                    - demoted_contact_account_primary_sibling: ContactEmailAccountAssociation demoted from account primary, if any

        Raises:
            ConcurrentModificationError: If concurrent updates prevent primary phone number changes
            ResourceNotFoundError: If the contact does not exist
            IllegalStateError: If required fields are missing or in invalid states

        Primary Email Handling:
            1. Contact Primary Email (is_contact_primary):
            - When setting a new contact primary email:
                * Existing primary email (if have) is demoted (is_contact_primary set to False)
                * Contact's primary_email field is updated to the new email
            - When setting a new contact non-primary email:
                * If no other emails exist, keeps current email as primary
            - When removing primary status:
                * Next available email by creation date is promoted to primary
                * Contact's primary_email field is updated to the promoted email
                * If no other emails exist, keeps current email as primary

            2. Contact Account Primary Email (is_contact_account_primary):
            - When setting a new contact account primary email:
                * Existing contact account primary email is demoted (is_contact_account_primary set to False)
                * New email becomes primary for that specific account
            - When removing contact account primary status or changing account:
                * Next available email for that account is promoted to primary
                * If no other emails exist for that account, keeps current as primary
        """

        # Step 0: check if the phone number is already existed
        _matching_contact_phone_number = one_row_or_none(
            [
                ce
                for ce in await self._find_by_column_values(
                    ContactPhoneNumber,
                    exclude_deleted_or_archived=True,
                    organization_id=contact_phone_number.organization_id,
                    contact_id=contact_phone_number.contact_id,
                    phone_number=contact_phone_number.phone_number,
                )
                if not ce.deleted_at
            ]
        )

        # Step 1: validate contact existence
        contact = await self.find_by_tenanted_primary_key_or_fail(
            Contact,
            organization_id=contact_phone_number.organization_id,
            id=contact_phone_number.contact_id,
            exclude_deleted_or_archived=False,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )

        # Step 2: list all existing contact phone numbers and potential primary contact phone number candidates for later use
        _all_existing_contact_phone_numbers: list[ContactPhoneNumber] = sorted(
            cast(
                list[ContactPhoneNumber],
                await self.list_contact_channel_infos_by_contact_id(
                    contact_channel_type=ContactChannelType.PHONE_NUMBER,
                    organization_id=contact_phone_number.organization_id,
                    contact_id=contact_phone_number.contact_id,
                ),
            ),
            key=lambda x: x.created_at,
            reverse=True,
        )
        _contact_primary_contact_phone_number_candidates: list[ContactPhoneNumber] = [
            cp
            for cp in _all_existing_contact_phone_numbers
            if cp.phone_number != contact_phone_number.phone_number
        ]

        new_primary_contact_phone_number_e164: str | None = None
        updated_contact: Contact | None = None
        promoted_contact_primary_sibling: ContactPhoneNumber | None = None
        promoted_contact_account_primary_sibling: (
            ContactPhoneNumberAccountAssociation | None
        ) = None
        demoted_contact_primary_sibling: ContactPhoneNumber | None = None
        demoted_contact_account_primary_sibling: (
            ContactPhoneNumberAccountAssociation | None
        ) = None

        # Step 3: check if we need to unset the existing contact primary email and promote a new primary
        # there is only one case that we need to do this: the email is already existed as primary and we
        # are trying to demote it to non-primary

        _contact_primary_contact_phone_number_candidate: ContactPhoneNumber | None = (
            None
        )
        if (
            _matching_contact_phone_number
            and _matching_contact_phone_number.is_contact_primary
            and not contact_phone_number.is_contact_primary
            and _contact_primary_contact_phone_number_candidates
        ):
            _contact_primary_contact_phone_number_candidate = (
                _contact_primary_contact_phone_number_candidates[0]
            )

        # note: it is possible that this email is the only existing email, in this case, we will always
        # set/keep it as primary
        if (
            not contact_phone_number.is_contact_primary
            and not _contact_primary_contact_phone_number_candidates
        ):
            contact_phone_number = strict_model_copy(
                contact_phone_number, is_contact_primary=True
            )

        desired_primary_contact_phone_number = (
            contact_phone_number.phone_number
            if contact_phone_number.is_contact_primary
            else (
                _contact_primary_contact_phone_number_candidate.phone_number
                if _contact_primary_contact_phone_number_candidate
                else None
            )
        )
        new_primary_contact_phone_number_e164 = (
            validate_e164(desired_primary_contact_phone_number)
            if desired_primary_contact_phone_number
            and validate_e164(desired_primary_contact_phone_number)
            != contact.primary_phone_number
            else None
        )

        # Step 4: perform the upsert in one transaction
        async with self.engine.begin() if not _in_nested_transaction else nullcontext():
            if new_primary_contact_phone_number_e164 and not (
                updated_contact := await self.update_by_tenanted_primary_key(
                    Contact,
                    organization_id=contact_phone_number.organization_id,
                    exclude_deleted_or_archived=False,
                    exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                    primary_key_to_value=contact.primary_key_to_value(),
                    column_to_update=ContactUpdate(
                        primary_phone_number=new_primary_contact_phone_number_e164,
                        updated_by_user_id=contact_phone_number.updated_by_user_id,
                    ),
                    column_condition=ContactUpdateCondition(
                        primary_phone_number=contact.primary_phone_number,
                    )
                    if contact.primary_phone_number
                    else None,
                )
            ):
                raise ConcurrentModificationError(
                    f"failed to update primary_phone_number for contact {contact_phone_number.contact_id} in organization"
                )

            if contact_phone_number.is_contact_primary:
                demoted_contact_primary_sibling = (
                    await self._unset_original_contact_primary_phone_number(
                        organization_id=contact_phone_number.organization_id,
                        contact_id=contact_phone_number.contact_id,
                        new_primary_phone_number=contact_phone_number.phone_number,
                    )
                )

            if _contact_primary_contact_phone_number_candidate:
                demoted_contact_primary_sibling = await self._unset_original_contact_primary_phone_number(
                    organization_id=contact_phone_number.organization_id,
                    contact_id=contact_phone_number.contact_id,
                    new_primary_phone_number=_contact_primary_contact_phone_number_candidate.phone_number,
                )

                promoted_contact_primary_sibling = await self.update_by_tenanted_primary_key(
                    ContactPhoneNumber,
                    organization_id=contact_phone_number.organization_id,
                    primary_key_to_value=_contact_primary_contact_phone_number_candidate.primary_key_to_value(),
                    column_to_update=ContactPhoneNumberUpdate(
                        is_contact_primary=True,
                        updated_by_user_id=contact_phone_number.updated_by_user_id,
                        updated_at=contact_phone_number.updated_at,
                    ),
                    column_condition=ContactPhoneNumberUpdateCondition(
                        deleted_at=None,
                    ),
                )
                if not promoted_contact_primary_sibling:
                    raise ConcurrentModificationError(
                        f"failed to update primary_phone_number for contact {contact_phone_number.contact_id} in organization"
                    )

            if _matching_contact_phone_number:
                upserted_contact_phone_number = not_none(
                    await self.update_instance(
                        strict_model_copy(
                            contact_phone_number, id=_matching_contact_phone_number.id
                        ),
                        exclude_deleted_or_archived=False,
                    )
                )
            else:
                upserted_contact_phone_number = await self.insert(contact_phone_number)

            upserted_contact_phone_number_account_association_results: list[
                UpsertContactPhoneNumberAccountAssociationResult
            ] = []
            for association in contact_phone_number_account_associations:
                (
                    upserted_contact_phone_number_account_association,
                    promoted_contact_account_primary_sibling,
                    demoted_contact_account_primary_sibling,
                ) = await self.upsert_contact_phone_number_account_association(
                    _in_nested_transaction=True,
                    target_contact_phone_number=_matching_contact_phone_number
                    or upserted_contact_phone_number,
                    contact_phone_number_account_association=association,
                )
                upserted_contact_phone_number_account_association_results.append(
                    UpsertContactPhoneNumberAccountAssociationResult(
                        upserted_contact_phone_number_account_association=upserted_contact_phone_number_account_association,
                        promoted_contact_account_primary_sibling=promoted_contact_account_primary_sibling,
                        demoted_contact_account_primary_sibling=demoted_contact_account_primary_sibling,
                    )
                )
        return UpsertContactPhoneNumberResult(
            updated_contact=updated_contact,
            upserted_contact_phone_number=upserted_contact_phone_number,
            promoted_contact_primary_sibling=promoted_contact_primary_sibling,
            demoted_contact_primary_sibling=demoted_contact_primary_sibling,
            upserted_contact_phone_number_account_association_results=upserted_contact_phone_number_account_association_results,
        )

    async def upsert_contact_phone_number_account_association(
        self,
        *,
        _in_nested_transaction: bool = False,
        target_contact_phone_number: ContactPhoneNumber,
        contact_phone_number_account_association: ContactPhoneNumberAccountAssociation,
    ) -> tuple[
        ContactPhoneNumberAccountAssociation,
        ContactPhoneNumberAccountAssociation | None,
        ContactPhoneNumberAccountAssociation | None,
    ]:
        """
        this method applies a similar logic as the upsert contact email
        """
        _matching_contact_phone_number_account_association = one_row_or_none(
            [
                cpa
                for cpa in await self._find_by_column_values(
                    ContactPhoneNumberAccountAssociation,
                    exclude_deleted_or_archived=False,
                    organization_id=target_contact_phone_number.organization_id,
                    contact_id=target_contact_phone_number.contact_id,
                    contact_phone_number_id=target_contact_phone_number.id,
                    account_id=contact_phone_number_account_association.account_id,
                )
                if not cpa.deleted_at
            ]
        )

        _all_existing_contact_phone_number_account_associations: list[
            ContactPhoneNumberAccountAssociation
        ] = sorted(
            cast(
                list[ContactPhoneNumberAccountAssociation],
                await self.list_contact_channel_info_account_associations(
                    contact_channel_type=ContactChannelType.PHONE_NUMBER,
                    organization_id=target_contact_phone_number.organization_id,
                    contact_id=contact_phone_number_account_association.contact_id,
                    account_id=contact_phone_number_account_association.account_id,
                ),
            ),
            key=lambda x: x.created_at,
            reverse=True,
        )

        _contact_account_primary_contact_phone_number_account_association_candidates: list[
            ContactPhoneNumberAccountAssociation
        ] = [
            cpa
            for cpa in _all_existing_contact_phone_number_account_associations
            if cpa.contact_phone_number_id != target_contact_phone_number.id
        ]

        upserted_contact_phone_number_account_association: (
            ContactPhoneNumberAccountAssociation
        )
        promoted_contact_account_primary_sibling: (
            ContactPhoneNumberAccountAssociation | None
        ) = None
        demoted_contact_account_primary_sibling: (
            ContactPhoneNumberAccountAssociation | None
        ) = None

        _contact_account_primary_contact_phone_number_account_association_candidate: (
            ContactPhoneNumberAccountAssociation | None
        ) = None
        if (
            _matching_contact_phone_number_account_association
            and _matching_contact_phone_number_account_association.is_contact_account_primary
            and not contact_phone_number_account_association.is_contact_account_primary
            and _contact_account_primary_contact_phone_number_account_association_candidates
        ):
            _contact_account_primary_contact_phone_number_account_association_candidate = _contact_account_primary_contact_phone_number_account_association_candidates[
                0
            ]

        if (
            not contact_phone_number_account_association.is_contact_account_primary
            and not _contact_account_primary_contact_phone_number_account_association_candidates
        ):
            contact_phone_number_account_association = strict_model_copy(
                contact_phone_number_account_association,
                is_contact_account_primary=True,
            )

        async with self.engine.begin() if not _in_nested_transaction else nullcontext():
            if contact_phone_number_account_association.is_contact_account_primary:
                demoted_contact_account_primary_sibling = await self._unset_original_contact_account_primary_phone_number(
                    organization_id=contact_phone_number_account_association.organization_id,
                    contact_id=contact_phone_number_account_association.contact_id,
                    account_id=contact_phone_number_account_association.account_id,
                    new_contact_phone_number_id=target_contact_phone_number.id,
                )

            if _contact_account_primary_contact_phone_number_account_association_candidate:
                demoted_contact_account_primary_sibling = await self._unset_original_contact_account_primary_phone_number(
                    organization_id=contact_phone_number_account_association.organization_id,
                    contact_id=contact_phone_number_account_association.contact_id,
                    account_id=contact_phone_number_account_association.account_id,
                    new_contact_phone_number_id=_contact_account_primary_contact_phone_number_account_association_candidate.contact_phone_number_id,
                )

                promoted_contact_account_primary_sibling = await self.update_by_tenanted_primary_key(
                    ContactPhoneNumberAccountAssociation,
                    organization_id=contact_phone_number_account_association.organization_id,
                    primary_key_to_value=_contact_account_primary_contact_phone_number_account_association_candidate.primary_key_to_value(),
                    column_to_update=ContactPhoneNumberAccountAssociationUpdate(
                        is_contact_account_primary=True,
                        updated_by_user_id=contact_phone_number_account_association.updated_by_user_id,
                        updated_at=contact_phone_number_account_association.updated_at,
                    ),
                    column_condition=ContactPhoneNumberAccountAssociationUpdateCondition(
                        deleted_at=None,
                    ),
                )
                if not promoted_contact_account_primary_sibling:
                    raise ConcurrentModificationError(
                        f"failed to update primary_contact_phone_number_account_association for contact {contact_phone_number_account_association.contact_id} in organization"
                    )

        if _matching_contact_phone_number_account_association:
            upserted_contact_phone_number_account_association = not_none(
                await self.update_instance(
                    strict_model_copy(
                        contact_phone_number_account_association,
                        id=_matching_contact_phone_number_account_association.id,
                        contact_phone_number_id=_matching_contact_phone_number_account_association.contact_phone_number_id,
                    ),
                    exclude_deleted_or_archived=False,
                )
            )
        else:
            upserted_contact_phone_number_account_association = await self.insert(
                strict_model_copy(
                    contact_phone_number_account_association,
                    contact_phone_number_id=target_contact_phone_number.id,
                )
            )

        return (
            upserted_contact_phone_number_account_association,
            promoted_contact_account_primary_sibling,
            demoted_contact_account_primary_sibling,
        )

    async def get_contact_account_primary_email(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None,
    ) -> EmailStrLower | None:
        stmt = text(
            """
            select ce.email from contact_email_account_association cea
            join contact_email ce on ce.id = cea.contact_email_id
            where
                cea.organization_id = :organization_id
                and cea.contact_id = :contact_id
                and cea.account_id = :account_id
                and cea.is_contact_account_primary
                and cea.deleted_at is null
                and cea.archived_at is null
                and ce.deleted_at is null
                and ce.archived_at is null
            """
        ).bindparams(
            account_id=account_id,
            contact_id=contact_id,
            organization_id=organization_id,
        )
        row = await self.engine.one(stmt)
        return row[0] if row else None

    async def _unset_original_contact_primary_email(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        new_primary_email: str,
    ) -> ContactEmail | None:
        stmt = text(
            """
            update contact_email set
                is_contact_primary = false
            where
                organization_id = :organization_id
                and contact_id = :contact_id
                and is_contact_primary
                and deleted_at is null
                and email != :new_primary_email
            returning *
            """
        ).bindparams(
            new_primary_email=new_primary_email,
            contact_id=contact_id,
            organization_id=organization_id,
        )
        row = await self.engine.all(stmt)
        return ContactEmail.from_row(row[0]) if row else None

    async def _unset_original_contact_primary_phone_number(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        new_primary_phone_number: PhoneNumberWithExtension,
    ) -> ContactPhoneNumber | None:
        stmt = text(
            """
            update contact_phone_number set
                is_contact_primary = false
            where
                organization_id = :organization_id
                and contact_id = :contact_id
                and is_contact_primary
                and deleted_at is null
                and phone_number != :new_primary_phone_number
            returning *
            """
        ).bindparams(
            new_primary_phone_number=new_primary_phone_number,
            contact_id=contact_id,
            organization_id=organization_id,
        )
        row = await self.engine.all(stmt)
        return ContactPhoneNumber.from_row(row[0]) if row else None

    async def _set_contact_primary_channel_info(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        user_id: UUID,
        info_id: UUID,
    ) -> ContactChannelT | None:
        table_model = contact_channel_type_to_table_model(contact_channel_type)
        return await self.update_by_tenanted_primary_key(
            table_model,
            exclude_deleted_or_archived=True,
            organization_id=organization_id,
            primary_key_to_value={"id": info_id},
            column_to_update={
                "is_contact_primary": True,
                "updated_by_user_id": user_id,
                "updated_at": zoned_utc_now(),
            },
        )

    async def update_contacts_enrichment_timestamp(
        self, organization_id: UUID, contact_ids: list[UUID], phone_enriched: bool
    ) -> None:
        if not contact_ids:
            return
        phone_enrich = " ,phone_enriched_at = :now " if phone_enriched else ""

        stmt = text(
            f"""
            update contact
            set email_enriched_at = :now {phone_enrich}
            where organization_id = :organization_id
              and id = any (:contact_ids)
            returning *;
            """  # noqa: S608
        ).bindparams(
            organization_id=organization_id,
            now=zoned_utc_now(),
            contact_ids=contact_ids,
        )
        await self.engine.all(stmt)

    async def _set_contact_account_primary_info(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        user_id: UUID,
        association_id: UUID,
    ) -> ContactChannelAccountAssociationT | None:
        table_model = contact_channel_type_to_asso_table_model(contact_channel_type)
        return await self.update_by_tenanted_primary_key(
            table_model,
            exclude_deleted_or_archived=True,
            organization_id=organization_id,
            primary_key_to_value={"id": association_id},
            column_to_update={
                "is_contact_account_primary": True,
                "updated_by_user_id": user_id,
                "updated_at": zoned_utc_now(),
            },
        )

    async def _unset_original_contact_account_primary_email(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID,
        new_contact_email_id: UUID,
    ) -> ContactEmailAccountAssociation | None:
        stmt = text(
            """
            update contact_email_account_association set
                is_contact_account_primary = false
            where
                organization_id = :organization_id
                and contact_id = :contact_id
                and account_id = :account_id
                and is_contact_account_primary
                and deleted_at is null
                and contact_email_id != :new_contact_email_id
            returning *
            """
        ).bindparams(
            contact_id=contact_id,
            account_id=account_id,
            organization_id=organization_id,
            new_contact_email_id=new_contact_email_id,
        )
        row = await self.engine.all(stmt)
        return ContactEmailAccountAssociation.from_row(row[0]) if row else None

    async def _unset_original_contact_account_primary_phone_number(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID,
        new_contact_phone_number_id: UUID,
    ) -> ContactPhoneNumberAccountAssociation | None:
        stmt = text(
            """
            update contact_phone_number_account_association set
                is_contact_account_primary = false
            where
                organization_id = :organization_id
                and contact_id = :contact_id
                and account_id = :account_id
                and is_contact_account_primary
                and deleted_at is null
                and contact_phone_number_id != :new_contact_phone_number_id
            returning *
            """
        ).bindparams(
            contact_id=contact_id,
            account_id=account_id,
            organization_id=organization_id,
            new_contact_phone_number_id=new_contact_phone_number_id,
        )
        row = await self.engine.all(stmt)
        return ContactPhoneNumberAccountAssociation.from_row(row[0]) if row else None

    async def map_contact_channel_info_by_contact_id(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        contact_ids: set[UUID],
    ) -> frozendict[UUID, list[ContactChannelT]]:
        table_model = contact_channel_type_to_table_model(contact_channel_type)
        if not contact_ids:
            return frozendict[UUID, list[ContactChannelT]]()
        contact_channel_infos = await self._find_by_column_values(
            table_model,
            exclude_deleted_or_archived=True,
            organization_id=organization_id,
            contact_id=list(contact_ids),
        )

        result: dict[UUID, list[ContactChannelT]] = defaultdict(list)
        for contact_channel_info in contact_channel_infos:
            result[contact_channel_info.contact_id].append(contact_channel_info)
        return frozendict(result)

    async def map_contact_channel_info_account_association_by_contact_id(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        contact_ids: set[UUID],
    ) -> frozendict[UUID, list[ContactChannelAccountAssociationT]]:
        table_model = contact_channel_type_to_asso_table_model(contact_channel_type)
        if not contact_ids:
            return frozendict[UUID, list[ContactChannelAccountAssociationT]]()

        contact_channel_info_account_associations = await self._find_by_column_values(
            table_model,
            exclude_deleted_or_archived=True,
            organization_id=organization_id,
            contact_id=list(contact_ids),
        )
        result: dict[UUID, list[ContactChannelAccountAssociationT]] = defaultdict(list)
        for (
            contact_channel_info_account_association
        ) in contact_channel_info_account_associations:
            result[contact_channel_info_account_association.contact_id].append(
                contact_channel_info_account_association
            )
        return frozendict(result)

    async def get_contact_channel_info_by_id(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        contact_channel_info_id: UUID,
        exclude_deleted_or_archived: bool = True,
    ) -> ContactChannelT | None:
        table_model = contact_channel_type_to_table_model(contact_channel_type)
        return await self.find_by_tenanted_primary_key(
            table_model,
            organization_id=organization_id,
            id=contact_channel_info_id,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
        )

    async def get_contact_channel_info_by_ids(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        contact_channel_info_ids: set[UUID],
        exclude_deleted_or_archived: bool = True,
    ) -> list[ContactChannelT]:
        table_model = contact_channel_type_to_table_model(contact_channel_type)
        return await self._find_by_column_values(
            table_model,
            organization_id=organization_id,
            id=list(contact_channel_info_ids),
            exclude_deleted_or_archived=exclude_deleted_or_archived,
        )

    async def get_primary_contact_channel_info_by_contact_id(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        contact_id: UUID,
    ) -> ContactChannelT | None:
        table_model = contact_channel_type_to_table_model(contact_channel_type)
        contact_channel_infos = await self._find_by_column_values(
            table_model,
            exclude_deleted_or_archived=True,
            organization_id=organization_id,
            contact_id=contact_id,
            is_contact_primary=True,
        )
        return one_row_or_none(contact_channel_infos)

    async def list_primary_contact_channel_infos_by_contact_ids(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        contact_ids: list[UUID],
    ) -> list[ContactChannelT]:
        table_model = contact_channel_type_to_table_model(contact_channel_type)
        return await self._find_by_column_values(
            table_model,
            exclude_deleted_or_archived=True,
            organization_id=organization_id,
            contact_id=contact_ids,
            is_contact_primary=True,
        )

    async def get_primary_channels_by_contact_ids(
        self,
        contact_channel_type: ContactChannelType,
        organization_id: UUID | None,
        contact_ids: set[UUID],
    ) -> dict[UUID, EmailStrLower | PhoneNumberWithExtension]:
        res: dict[UUID, EmailStrLower | PhoneNumberWithExtension] = {}
        table_model = contact_channel_type_to_table_model(contact_channel_type)
        if not contact_ids:
            return res

        contact_channel_infos = await self._find_by_column_values(
            table_model,
            exclude_deleted_or_archived=True,
            organization_id=organization_id,
            contact_id=list(contact_ids),
            is_contact_primary=True,
        )
        if not contact_channel_infos:
            return res
        for row in contact_channel_infos:
            if contact_channel_type == ContactChannelType.EMAIL:
                contact_email = cast(ContactEmail, row)
                res.update({contact_email.contact_id: contact_email.email})
            if contact_channel_type == ContactChannelType.PHONE_NUMBER:
                contact_phone_number = cast(ContactPhoneNumber, row)
                res.update(
                    {contact_phone_number.contact_id: contact_phone_number.phone_number}
                )
        return res

    async def get_primary_channel_by_contact_id(
        self,
        contact_channel_type: ContactChannelType,
        organization_id: UUID | None,
        contact_id: UUID,
    ) -> EmailStrLower | PhoneNumber | None:
        contact_email_map = await self.get_primary_channels_by_contact_ids(
            contact_channel_type=contact_channel_type,
            organization_id=organization_id,
            contact_ids={contact_id},
        )
        return contact_email_map.get(contact_id)

    async def get_primary_account_id_by_contact_id(
        self,
        organization_id: UUID,
        contact_id: UUID,
    ) -> UUID | None:
        account_id_map = await self.get_primary_account_ids_by_contact_ids(
            organization_id=organization_id,
            contact_ids={contact_id},
        )
        return account_id_map.get(contact_id)

    async def get_primary_account_ids_by_contact_ids(
        self,
        organization_id: UUID,
        contact_ids: set[UUID],
    ) -> dict[UUID, UUID]:
        primary_account_associations = await self._find_by_column_values(
            ContactAccountAssociation,
            exclude_deleted_or_archived=True,
            organization_id=organization_id,
            contact_id=list(contact_ids),
            is_primary=True,
        )
        res: dict[UUID, UUID] = defaultdict()
        if not primary_account_associations:
            return res
        for primary_account_association in primary_account_associations:
            res[primary_account_association.contact_id] = (
                primary_account_association.account_id
            )
        return res

    async def list_contact_email_and_email_association_by_contact_id(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
    ) -> tuple[list[ContactEmail], dict[UUID, list[ContactEmailAccountAssociation]]]:
        # validate contact exists
        await self.find_by_tenanted_primary_key_or_fail(
            Contact,
            organization_id=organization_id,
            id=contact_id,
        )

        email_table_alias = "ce"
        association_table_alias = "cea"
        email_column_prefix = "ce_"
        association_column_prefix = "cea_"
        email_columns = ContactEmail.get_column_names_with_alias(
            table_alias=email_table_alias, column_alias_prefix=email_column_prefix
        )
        association_columns = (
            ContactEmailAccountAssociation.get_column_names_with_alias(
                table_alias=association_table_alias,
                column_alias_prefix=association_column_prefix,
            )
        )
        all_columns = ", ".join(email_columns + association_columns)

        stmt = text(
            f"""
            select {all_columns}
            from contact_email {email_table_alias}
            left join contact_email_account_association {association_table_alias}
            on {association_table_alias}.contact_email_id = {email_table_alias}.id
                and {association_table_alias}.organization_id = {email_table_alias}.organization_id
                and {association_table_alias}.deleted_at is null
                and {association_table_alias}.archived_at is null
            where
                {email_table_alias}.organization_id = :organization_id
                and {email_table_alias}.contact_id = :contact_id
                and {email_table_alias}.deleted_at is null
                and {email_table_alias}.archived_at is null
            """  # noqa: S608
        ).bindparams(
            organization_id=organization_id,
            contact_id=contact_id,
        )

        rows = await self.engine.all(stmt)

        contact_email_set: set[ContactEmail] = set()
        association_map: dict[UUID, list[ContactEmailAccountAssociation]] = {}
        for row in rows:
            contact_email = ContactEmail.from_row(
                row, column_alias_prefix=email_column_prefix
            )
            contact_email_set.add(contact_email)

            contact_email_account_association = (
                ContactEmailAccountAssociation.from_row_if_exists(
                    row, column_alias_prefix=association_column_prefix
                )
            )
            if contact_email_account_association:
                association_map.setdefault(
                    contact_email_account_association.contact_email_id, []
                ).append(contact_email_account_association)
        contact_emails = sorted(contact_email_set, key=lambda c: c.id)
        return contact_emails, association_map

    async def list_contact_channel_infos_by_contact_id(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        contact_id: UUID,
    ) -> list[ContactChannelT]:
        return (
            await self.map_contact_channel_info_by_contact_id(
                contact_channel_type=contact_channel_type,
                organization_id=organization_id,
                contact_ids={contact_id},
            )
        ).get(contact_id, [])

    async def list_contact_channel_info_account_associations(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID,
    ) -> list[ContactChannelAccountAssociationT]:
        table_model = contact_channel_type_to_asso_table_model(contact_channel_type)
        return await self._find_by_column_values(
            table_model,
            organization_id=organization_id,
            contact_id=contact_id,
            account_id=account_id,
        )

    async def list_contact_channel_info_account_associations_by_account_id(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        account_id: UUID,
    ) -> list[ContactChannelAccountAssociationT]:
        table_model = contact_channel_type_to_asso_table_model(contact_channel_type)
        return await self._find_by_column_values(
            table_model,
            organization_id=organization_id,
            account_id=account_id,
        )

    async def list_contact_channel_info_account_associations_by_contact_id(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        contact_id: UUID,
    ) -> list[ContactChannelAccountAssociationT]:
        return (
            await self.map_contact_channel_info_account_association_by_contact_id(
                contact_channel_type=contact_channel_type,
                organization_id=organization_id,
                contact_ids={contact_id},
            )
        ).get(contact_id, [])

    async def list_contact_channel_info_account_associations_by_contact_channel_info_id(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        contact_channel_info_id: UUID,
        exclude_deleted_or_archived: bool = True,
    ) -> list[ContactChannelAccountAssociationT]:
        table_model = contact_channel_type_to_asso_table_model(contact_channel_type)
        match contact_channel_type:
            case ContactChannelType.EMAIL:
                return await self._find_by_column_values(
                    table_model,
                    organization_id=organization_id,
                    contact_email_id=contact_channel_info_id,
                    exclude_deleted_or_archived=exclude_deleted_or_archived,
                )
            case ContactChannelType.PHONE_NUMBER:
                return await self._find_by_column_values(
                    table_model,
                    organization_id=organization_id,
                    contact_phone_number_id=contact_channel_info_id,
                    exclude_deleted_or_archived=exclude_deleted_or_archived,
                )
            case _ as unreachable:
                assert_never(unreachable)

    async def list_contact_channel_info_account_associations_by_contact_channel_info_ids(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        contact_channel_info_ids: set[UUID],
        exclude_deleted_or_archived: bool = True,
    ) -> list[ContactChannelAccountAssociationT]:
        table_model = contact_channel_type_to_asso_table_model(contact_channel_type)
        match contact_channel_type:
            case ContactChannelType.EMAIL:
                return await self._find_by_column_values(
                    table_model,
                    organization_id=organization_id,
                    contact_email_id=list(contact_channel_info_ids),
                    exclude_deleted_or_archived=exclude_deleted_or_archived,
                )
            case ContactChannelType.PHONE_NUMBER:
                return await self._find_by_column_values(
                    table_model,
                    organization_id=organization_id,
                    contact_phone_number_id=list(contact_channel_info_ids),
                    exclude_deleted_or_archived=exclude_deleted_or_archived,
                )
            case _ as unreachable:
                assert_never(unreachable)

    async def get_contact_channel_info_account_association_by_id(
        self,
        *,
        contact_channel_type: ContactChannelType,
        organization_id: UUID,
        contact_channel_info_account_association_id: UUID,
    ) -> ContactChannelAccountAssociationT | None:
        table_model = contact_channel_type_to_asso_table_model(contact_channel_type)
        return await self.find_by_tenanted_primary_key(
            table_model,
            organization_id=organization_id,
            id=contact_channel_info_account_association_id,
        )

    async def get_contact_email_account_association(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        contact_email_id: UUID,
        account_id: UUID,
    ) -> ContactEmailAccountAssociation | None:
        return await self._find_unique_by_column_values(
            ContactEmailAccountAssociation,
            organization_id=organization_id,
            contact_id=contact_id,
            contact_email_id=contact_email_id,
            account_id=account_id,
        )

    async def get_preferred_contact_email_of_account(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID,
    ) -> ContactEmail | None:
        stmt = text("""
            select ce.* from contact_email ce
            inner join contact_email_account_association ceaa
            on ce.id = ceaa.contact_email_id
            where ceaa.contact_id = :contact_id
            and ceaa.account_id = :account_id
            and ceaa.organization_id = :organization_id
            and ceaa.is_contact_account_primary
            and ce.archived_at is null and ce.deleted_at is null
            and ceaa.archived_at is null and ceaa.deleted_at is null
            """)
        stmt = stmt.bindparams(
            contact_id=contact_id,
            account_id=account_id,
            organization_id=organization_id,
        )
        row = await self.engine.first_or_none(stmt)
        return ContactEmail.from_row(row) if row else None

    async def get_contact_email_account_associations_by_emails(
        self,
        *,
        organization_id: UUID,
        emails: list[EmailStrLower],
    ) -> dict[EmailStrLower, list[ContactEmailAccountAssociation]]:
        contact_email_association_map: dict[
            EmailStrLower, list[ContactEmailAccountAssociation]
        ] = {}
        if not emails:
            return contact_email_association_map
        stmt = (
            text("""
        select ce.email, ceaa.* from contact_email_account_association ceaa
        left join contact_email ce on ceaa.contact_email_id = ce.id
        where ceaa.organization_id = :organization_id and ce.email in :emails
        and ce.deleted_at is null and ce.archived_at is null
        and ceaa.deleted_at is null and ceaa.archived_at is null
        """)
            .bindparams(bindparam("emails", emails, expanding=True))
            .bindparams(
                organization_id=organization_id,
            )
        )
        rows = list(await self.engine.all(stmt))
        for row in rows:
            if not row[0]:
                continue
            contact_email_associations = contact_email_association_map.setdefault(
                row[0], []
            )
            contact_email_associations.append(
                ContactEmailAccountAssociation.from_row(row)
            )
        return contact_email_association_map

    async def get_primary_contact_email_account_associations_by_emails(
        self,
        *,
        organization_id: UUID,
        emails: list[EmailStrLower],
    ) -> dict[EmailStrLower, list[ContactEmailAccountAssociation]]:
        contact_email_association_map: dict[
            EmailStrLower, list[ContactEmailAccountAssociation]
        ] = {}
        if not emails:
            return contact_email_association_map
        stmt = (
            text("""
        select ce.email, ceaa.* from contact_email_account_association ceaa
        left join contact_email ce on ceaa.contact_email_id = ce.id
        where ceaa.organization_id = :organization_id and ce.email in :emails
        and ceaa.is_contact_account_primary = true
        and ce.deleted_at is null and ce.archived_at is null
        and ceaa.deleted_at is null and ceaa.archived_at is null
        """)
            .bindparams(bindparam("emails", emails, expanding=True))
            .bindparams(
                organization_id=organization_id,
            )
        )
        rows = list(await self.engine.all(stmt))
        for row in rows:
            if not row[0]:
                continue
            contact_email_associations = contact_email_association_map.setdefault(
                row[0], []
            )
            contact_email_associations.append(
                ContactEmailAccountAssociation.from_row(row)
            )
        return contact_email_association_map

    async def list_contact_emails_by_emails(
        self,
        *,
        organization_id: UUID,
        emails: set[EmailStrLower],
    ) -> list[ContactEmail]:
        return list(
            (
                await self.map_contact_emails_by_emails(
                    organization_id=organization_id,
                    emails=emails,
                )
            ).values()
        )

    async def map_contact_emails_by_emails(
        self,
        *,
        organization_id: UUID,
        emails: set[EmailStrLower],
    ) -> frozendict[EmailStr, ContactEmail]:
        if not emails:
            return frozendict({})
        contact_emails = await self._find_by_column_values(
            ContactEmail,
            exclude_deleted_or_archived=True,
            organization_id=organization_id,
            email=list(emails),
        )

        result: dict[EmailStr, ContactEmail] = {}
        for contact_email in contact_emails:
            if contact_email:
                result.update({contact_email.email: contact_email})
        return frozendict(result)

    async def patch_contact_email_by_id(
        self, *, user_id: UUID, organization_id: UUID, req: UpdateContactEmailRequest
    ) -> UpdateContactEmailResult:
        target_contact_email = cast(
            ContactEmail,
            await self.get_contact_channel_info_by_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_channel_info_id=req.id,
            ),
        )
        if not target_contact_email:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code="EMAIL_NOT_FOUND",
                    details="target email not found.",
                )
            )

        new_primary_email: EmailStrLower | None = None
        updated_contact_email: ContactEmail
        updated_alter_contact_email: ContactEmail | None = None

        contact_email_update = req.to_db_inserts(user_id=user_id)

        async with self.engine.begin():
            if (
                target_contact_email.is_contact_primary
                and req.is_contact_primary is False
            ):
                # case 1.
                # unset current primary email (target_contact_email), then promote alternate email
                # always unset primary before set to avoid confilcts

                # alternative email is only required when demoting the target email
                alternative_contact_email = (
                    await self._get_alternative_contact_email_from_req(
                        organization_id=organization_id,
                        target_contact_email=target_contact_email,
                        req=req,
                    )
                )

                updated_contact_email = not_none(
                    await self.update_by_tenanted_primary_key(
                        ContactEmail,
                        exclude_deleted_or_archived=True,
                        organization_id=organization_id,
                        primary_key_to_value={"id": req.id},
                        column_to_update=contact_email_update,
                    )
                )

                updated_alter_contact_email = cast(
                    ContactEmail,
                    await self._set_contact_primary_channel_info(
                        contact_channel_type=ContactChannelType.EMAIL,
                        organization_id=organization_id,
                        user_id=user_id,
                        info_id=alternative_contact_email.id,
                    ),
                )

                new_primary_email = updated_alter_contact_email.email
            elif (
                not target_contact_email.is_contact_primary
                and req.is_contact_primary is True
            ):
                # case 2.
                # set primary email (target_contact_email), then unset original primary email
                # set primary first, or raise unique key error
                updated_alter_contact_email = (
                    await self._unset_original_contact_primary_email(
                        organization_id=organization_id,
                        contact_id=target_contact_email.contact_id,
                        new_primary_email=target_contact_email.email,
                    )
                )

                updated_contact_email = not_none(
                    await self.update_by_tenanted_primary_key(
                        ContactEmail,
                        exclude_deleted_or_archived=True,
                        organization_id=organization_id,
                        primary_key_to_value={"id": req.id},
                        column_to_update=contact_email_update,
                    )
                )

                new_primary_email = updated_contact_email.email
            else:
                updated_contact_email = not_none(
                    await self.update_by_tenanted_primary_key(
                        ContactEmail,
                        exclude_deleted_or_archived=True,
                        organization_id=organization_id,
                        primary_key_to_value={"id": req.id},
                        column_to_update=contact_email_update,
                    )
                )

            if new_primary_email:
                updated_contact: Contact = not_none(
                    await self.update_by_tenanted_primary_key(
                        Contact,
                        exclude_deleted_or_archived=False,
                        organization_id=target_contact_email.organization_id,
                        primary_key_to_value={"id": target_contact_email.contact_id},
                        column_to_update={
                            "primary_email": new_primary_email,
                            "updated_at": zoned_utc_now(),
                        },
                    )
                )

                await self.try_refresh_display_name(
                    user_id=user_id,
                    contact=updated_contact,
                    primary_email=new_primary_email,
                )

        return UpdateContactEmailResult(
            updated_contact_email=updated_contact_email,
            updated_alter_contact_email=updated_alter_contact_email,
        )

    async def _get_alternative_contact_email_from_req(
        self,
        organization_id: UUID,
        target_contact_email: ContactEmail,
        req: UpdateContactEmailRequest,
    ) -> ContactEmail:
        if not req.alternative_email_id:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code="UNSET_PRIMARY_WITHOUT_ALTERNATE_EMAIL",
                    details="alternative email not found.",
                )
            )

        if req.alternative_email_id == target_contact_email.id:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code="ALTERNATE_EMAIL_SAME_AS_TARGET",
                    details="alternative email is the same as target email.",
                )
            )

        alternative_contact_email = cast(
            ContactEmail,
            await self.get_contact_channel_info_by_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_channel_info_id=req.alternative_email_id,
            ),
        )

        if not alternative_contact_email:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code="ALTERNATE_EMAIL_NOT_FOUND",
                    details="alternative email not found.",
                )
            )

        if alternative_contact_email.contact_id != target_contact_email.contact_id:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code="ALTERNATE_EMAIL_CONFLICT",
                    details="alternative email belongs to another contact.",
                )
            )

        return alternative_contact_email

    async def patch_contact_email_account_association(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        req: UpdateContactEmailAccountAssociationRequest,
    ) -> UpdateContactEmailAccountAssociationResult:
        target_contact_email_account_association = cast(
            ContactEmailAccountAssociation,
            await self.get_contact_channel_info_account_association_by_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_channel_info_account_association_id=req.id,
            ),
        )
        if not target_contact_email_account_association:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code="EMAIL_ACCOUNT_ASSOCIATION_NOT_FOUND",
                    details="target email account association not found.",
                )
            )

        updated_contact_email_account_association: ContactEmailAccountAssociation
        updated_alter_contact_email_account_association: (
            ContactEmailAccountAssociation | None
        ) = None
        contact_email: ContactEmail
        alter_contact_email: ContactEmail | None = None

        contact_email_account_association_update = req.to_db_inserts(user_id=user_id)
        contact_email = not_none(
            cast(
                ContactEmail,
                await self.get_contact_channel_info_by_id(
                    contact_channel_type=ContactChannelType.EMAIL,
                    organization_id=organization_id,
                    contact_channel_info_id=target_contact_email_account_association.contact_email_id,
                ),
            )
        )

        if (
            target_contact_email_account_association.is_contact_account_primary
            and req.is_contact_account_primary is False
        ):
            # case 1.
            # unset current primary email (target_contact_email_account_association), then promote alternate email
            # always unset primary before set to avoid confilcts
            alternative_contact_email_account_association = await self._get_alternative_contact_email_account_association_from_req(
                organization_id=organization_id,
                target_contact_email_account_association=target_contact_email_account_association,
                req=req,
            )

            async with self.engine.begin():
                updated_contact_email_account_association = not_none(
                    await self.update_by_tenanted_primary_key(
                        ContactEmailAccountAssociation,
                        exclude_deleted_or_archived=True,
                        organization_id=organization_id,
                        primary_key_to_value={"id": req.id},
                        column_to_update=contact_email_account_association_update,
                    )
                )

                updated_alter_contact_email_account_association = cast(
                    ContactEmailAccountAssociation,
                    await self._set_contact_account_primary_info(
                        contact_channel_type=ContactChannelType.EMAIL,
                        organization_id=organization_id,
                        user_id=user_id,
                        association_id=alternative_contact_email_account_association.id,
                    ),
                )
        elif (
            not target_contact_email_account_association.is_contact_account_primary
            and req.is_contact_account_primary is True
        ):
            # case 2.
            # set primary email (target_contact_email_account_association), then unset original primary email
            # set primary first, or raise unique key error
            async with self.engine.begin():
                updated_alter_contact_email_account_association = await self._unset_original_contact_account_primary_email(
                    organization_id=organization_id,
                    contact_id=target_contact_email_account_association.contact_id,
                    account_id=target_contact_email_account_association.account_id,
                    new_contact_email_id=target_contact_email_account_association.contact_email_id,
                )

                updated_contact_email_account_association = not_none(
                    await self.update_by_tenanted_primary_key(
                        ContactEmailAccountAssociation,
                        exclude_deleted_or_archived=True,
                        organization_id=organization_id,
                        primary_key_to_value={"id": req.id},
                        column_to_update=contact_email_account_association_update,
                    )
                )
        else:
            updated_contact_email_account_association = not_none(
                await self.update_by_tenanted_primary_key(
                    ContactEmailAccountAssociation,
                    exclude_deleted_or_archived=True,
                    organization_id=organization_id,
                    primary_key_to_value={"id": req.id},
                    column_to_update=contact_email_account_association_update,
                )
            )

        if updated_alter_contact_email_account_association:
            alter_contact_email = not_none(
                cast(
                    ContactEmail,
                    await self.get_contact_channel_info_by_id(
                        contact_channel_type=ContactChannelType.EMAIL,
                        organization_id=organization_id,
                        contact_channel_info_id=updated_alter_contact_email_account_association.contact_email_id,
                    ),
                )
            )

        return UpdateContactEmailAccountAssociationResult(
            updated_contact_email_account_association=updated_contact_email_account_association,
            updated_alter_contact_email_account_association=updated_alter_contact_email_account_association,
            contact_email=contact_email,
            alter_contact_email=alter_contact_email,
        )

    async def _get_alternative_contact_email_account_association_from_req(
        self,
        organization_id: UUID,
        target_contact_email_account_association: ContactEmailAccountAssociation,
        req: UpdateContactEmailAccountAssociationRequest,
    ) -> ContactEmailAccountAssociation:
        if not req.alternative_email_account_association_id:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code="UNSET_PRIMARY_WITHOUT_ALTERNATE_EMAIL_ASSOCIATION",
                    details="alternative email account association not found.",
                )
            )

        if (
            req.alternative_email_account_association_id
            == target_contact_email_account_association.id
        ):
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code="ALTERNATE_ACCOUNT_ASSOCIATION_SAME_AS_TARGET",
                    details="alternative email account association is the same as target email account association.",
                )
            )

        alternative_contact_email_account_association = cast(
            ContactEmailAccountAssociation,
            await self.get_contact_channel_info_account_association_by_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_channel_info_account_association_id=req.alternative_email_account_association_id,
            ),
        )

        if not alternative_contact_email_account_association:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code="ALTERNATE_ASSOCIATION_NOT_FOUND",
                    details="alternative email account association not found.",
                )
            )

        if (
            alternative_contact_email_account_association.contact_id
            != target_contact_email_account_association.contact_id
            or alternative_contact_email_account_association.account_id
            != target_contact_email_account_association.account_id
        ):
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code="ALTERNATE_CONTACT_ACCOUNT_ASSOCIATION_CONFLICT",
                    details="alternative email contact account association belongs to another contact or account.",
                )
            )

        return alternative_contact_email_account_association

    async def delete_contact_email(
        self,
        *,
        _in_nested_transaction: bool = False,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        email: EmailStrLower,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> DeleteContactEmailResult:
        """
        Delete a contact email and handle primary email transitions.

        This method delete a contact email while managing the primary email
        designations at both contact and account levels. If the deleted email was primary, raise Error

        Args:
            _in_nested_transaction (bool): If True, assumes operation is part of a larger transaction.
                If False, creates its own transaction.
            organization_id (UUID): The organization ID the contact belongs to
            user_id (UUID): The ID of the user performing the deletion
            contact_id (UUID): The ID of the contact whose email is being deleted
            email (EmailStrLower): The email address to delete (case-insensitive)
        Returns:
            DeleteContactEmailResult: A named tuple containing:
                - deleted: The deleted ContactEmail record
                - deleted_email_account_associations: The related deleted ContactEmailAccountAssociation records
        Raises:
            ResourceNotFoundError: If the contact or email doesn't exist
            ConcurrentModificationError: If concurrent updates prevent the operation
            IllegalStateError: If deleting would leave the contact without any email
            InvalidArgumentError: If attempting to delete the last primary email

        Primary Email Handling:
            1. Contact Primary Email:
                - When deleting a primary email, promotes the next available email by creation date
                - If no other emails exist, contact.primary_email is set to None

            2. Account Primary Email:
                - When deleting an account's primary email, promotes another email for that account
                - Only promotes emails that aren't deleted and belong to the same account

        Transaction Safety:
            - All operations are atomic within a transaction
            - Uses optimistic locking via column conditions
            - Handles race conditions through conditional updates
        """
        sanitized_email = email.lower().strip()

        # it's possible to have multiple deleted and at most one archived contact email in the table
        # we should query them all out and filter out the deleted ones
        contact_email_to_delete = one_row_or_none(
            [
                contact_email
                for contact_email in await self._find_by_column_values(
                    ContactEmail,
                    organization_id=organization_id,
                    contact_id=contact_id,
                    email=sanitized_email,
                    exclude_deleted_or_archived=False,
                )
                if not contact_email.deleted_at
            ]
        )

        if not contact_email_to_delete:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code="CONTACT_EMAIL_NOT_FOUND",
                    details="contact email not found",
                )
            )

        if contact_email_to_delete.archived_at:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code="CONTACT_DELETE_ARCHIVED_CONTACT_EMAIL",
                    details="delete archived contact email",
                    reference_id=str(contact_email_to_delete.email),
                )
            )

        next_primary_contact_email_candidate: ContactEmail | None = None
        if contact_email_to_delete.is_contact_primary:
            # try to promote the next available email by creation date
            # if no other emails exist, contact.primary_email is set to None
            contact_email_candidates = sorted(
                [
                    candidate
                    for candidate in cast(
                        list[ContactEmail],
                        await self.list_contact_channel_infos_by_contact_id(
                            contact_channel_type=ContactChannelType.EMAIL,
                            organization_id=organization_id,
                            contact_id=contact_id,
                        ),
                    )
                    if candidate.id != contact_email_to_delete.id
                ],
                key=lambda x: x.created_at,
                reverse=True,
            )
            next_primary_contact_email_candidate = (
                contact_email_candidates[0] if contact_email_candidates else None
            )

        async with self.engine.begin() if not _in_nested_transaction else nullcontext():
            deleted_contact_email_account_associations = (
                await self._update_by_column_values(
                    ContactEmailAccountAssociation,
                    column_value_to_query={
                        "contact_email_id": contact_email_to_delete.id,
                        "organization_id": organization_id,
                    },
                    column_to_update=ContactEmailAccountAssociationUpdate(
                        deleted_at=zoned_utc_now(),
                        deleted_by_user_id=user_id,
                        updated_by_user_id=user_id,
                    ),
                )
            )

            deleted_contact_email = await self.update_by_tenanted_primary_key(
                ContactEmail,
                organization_id=organization_id,
                exclude_deleted_or_archived=False,
                primary_key_to_value=contact_email_to_delete.primary_key_to_value(),
                column_to_update=ContactEmailUpdate(
                    deleted_at=zoned_utc_now(),
                    deleted_by_user_id=user_id,
                    updated_by_user_id=user_id,
                ),
                column_condition=ContactEmailUpdateCondition(
                    is_contact_primary=contact_email_to_delete.is_contact_primary,
                ),
            )
            if not deleted_contact_email:
                raise ConcurrentModificationError(
                    f"failed to delete contact email {email} in organization"
                )

            if contact_email_to_delete.is_contact_primary:
                if next_primary_contact_email_candidate:
                    await self.update_by_tenanted_primary_key(
                        ContactEmail,
                        organization_id=organization_id,
                        primary_key_to_value=next_primary_contact_email_candidate.primary_key_to_value(),
                        column_to_update=ContactEmailUpdate(
                            is_contact_primary=True,
                            updated_by_user_id=user_id,
                        ),
                        column_condition=ContactEmailUpdateCondition(
                            deleted_at=None,
                        ),
                    )

                    not_none(
                        await self.update_by_tenanted_primary_key(
                            Contact,
                            organization_id=organization_id,
                            primary_key_to_value={"id": contact_id},
                            column_to_update=ContactUpdate(
                                primary_email=next_primary_contact_email_candidate.email,
                                updated_by_user_id=user_id,
                            ),
                            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                        )
                    )
                else:
                    not_none(
                        await self.update_by_tenanted_primary_key(
                            Contact,
                            organization_id=organization_id,
                            primary_key_to_value={"id": contact_id},
                            column_to_update=ContactUpdate(
                                primary_email=None,
                                updated_by_user_id=user_id,
                            ),
                            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                        )
                    )

        return DeleteContactEmailResult(
            deleted=deleted_contact_email,
            deleted_email_account_associations=deleted_contact_email_account_associations,
            promoted=next_primary_contact_email_candidate,
        )

    async def delete_contact_phone_number(
        self,
        *,
        _in_nested_transaction: bool = False,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        phone_number: PhoneNumberWithExtension,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> DeleteContactPhoneNumberResult:
        """
        Delete a contact phone number and handle primary phone number transitions.

        This method delete a contact phone number while managing the primary phone number
        designations at both contact and account levels. If the deleted email was primary, raise Error

        Args:
            _in_nested_transaction (bool): If True, assumes operation is part of a larger transaction.
                If False, creates its own transaction.
            organization_id (UUID): The organization ID the contact belongs to
            user_id (UUID): The ID of the user performing the deletion
            contact_id (UUID): The ID of the contact whose email is being deleted
            phone_number (PhoneNumberExtension): The phone number to delete
        Returns:
            DeleteContactPhoneNumberResult: A named tuple containing:
                - deleted: The deleted ContactPhoneNumber record
                - deleted_phone_number_account_associations: The related deleted ContactPhoneNumberAccountAssociation records
        Raises:
            ResourceNotFoundError: If the contact or email doesn't exist
            ConcurrentModificationError: If concurrent updates prevent the operation
            IllegalStateError: If deleting would leave the contact without any email
            InvalidArgumentError: If attempting to delete the last primary email

        Primary Phone Number Handling:
            1. Contact Primary Phone Number:
                - When deleting a primary phone number, promotes the next available phone number by creation date
                - If no other phone numbers exist, contact.primary_phone_number is set to None

            2. Account Primary Phone Number:
                - When deleting an account's primary phone number, promotes another phone number for that account
                - Only promotes phone numbers that aren't deleted and belong to the same account

        Transaction Safety:
            - All operations are atomic within a transaction
            - Uses optimistic locking via column conditions
            - Handles race conditions through conditional updates
        """
        # it's possible to have multiple deleted and at most one archived contact email in the table
        # we should query them all out and filter out the deleted ones
        contact_phone_number_to_delete = one_row_or_none(
            [
                contact_phone_number
                for contact_phone_number in await self._find_by_column_values(
                    ContactPhoneNumber,
                    organization_id=organization_id,
                    contact_id=contact_id,
                    phone_number=phone_number,
                    exclude_deleted_or_archived=False,
                )
                if not contact_phone_number.deleted_at
            ]
        )

        if not contact_phone_number_to_delete:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code="CONTACT_PHONE_NUMBER_NOT_FOUND",
                    details="contact phone number not found",
                )
            )

        if contact_phone_number_to_delete.archived_at:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code="CONTACT_DELETE_ARCHIVED_CONTACT_PHONE_NUMBER",
                    details="delete archived contact phone number",
                    reference_id=str(contact_phone_number_to_delete.phone_number),
                )
            )

        next_primary_contact_phone_number_candidate: ContactPhoneNumber | None = None
        if contact_phone_number_to_delete.is_contact_primary:
            # try to promote the next available phone number by creation date
            # if no other phone numbers exist, contact.primary_phone_number is set to None
            contact_phone_number_candidates = sorted(
                [
                    candidate
                    for candidate in cast(
                        list[ContactPhoneNumber],
                        await self.list_contact_channel_infos_by_contact_id(
                            contact_channel_type=ContactChannelType.PHONE_NUMBER,
                            organization_id=organization_id,
                            contact_id=contact_id,
                        ),
                    )
                    if candidate.id != contact_phone_number_to_delete.id
                ],
                key=lambda x: x.created_at,
                reverse=True,
            )
            next_primary_contact_phone_number_candidate = (
                contact_phone_number_candidates[0]
                if contact_phone_number_candidates
                else None
            )

        async with self.engine.begin() if not _in_nested_transaction else nullcontext():
            deleted_contact_phone_number_account_associations = (
                await self._update_by_column_values(
                    ContactPhoneNumberAccountAssociation,
                    column_value_to_query={
                        "contact_phone_number_id": contact_phone_number_to_delete.id,
                        "organization_id": organization_id,
                    },
                    column_to_update=ContactPhoneNumberAccountAssociationUpdate(
                        deleted_at=zoned_utc_now(),
                        deleted_by_user_id=user_id,
                        updated_by_user_id=user_id,
                    ),
                )
            )

            deleted_contact_phone_number = await self.update_by_tenanted_primary_key(
                ContactPhoneNumber,
                organization_id=organization_id,
                exclude_deleted_or_archived=False,
                primary_key_to_value=contact_phone_number_to_delete.primary_key_to_value(),
                column_to_update=ContactPhoneNumberUpdate(
                    deleted_at=zoned_utc_now(),
                    deleted_by_user_id=user_id,
                    updated_by_user_id=user_id,
                ),
                column_condition=ContactPhoneNumberUpdateCondition(
                    is_contact_primary=contact_phone_number_to_delete.is_contact_primary,
                ),
            )
            if not deleted_contact_phone_number:
                raise ConcurrentModificationError(
                    f"failed to delete contact phone number {phone_number} in organization"
                )

            if contact_phone_number_to_delete.is_contact_primary:
                if next_primary_contact_phone_number_candidate:
                    await self.update_by_tenanted_primary_key(
                        ContactPhoneNumber,
                        organization_id=organization_id,
                        primary_key_to_value=next_primary_contact_phone_number_candidate.primary_key_to_value(),
                        column_to_update=ContactPhoneNumberUpdate(
                            is_contact_primary=True,
                            updated_by_user_id=user_id,
                        ),
                        column_condition=ContactPhoneNumberUpdateCondition(
                            deleted_at=None,
                        ),
                    )

                    await self.update_by_tenanted_primary_key(
                        Contact,
                        organization_id=organization_id,
                        primary_key_to_value={"id": contact_id},
                        column_to_update=ContactUpdate(
                            primary_phone_number=validate_e164(
                                next_primary_contact_phone_number_candidate.phone_number
                            ),
                            updated_by_user_id=user_id,
                        ),
                        exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                    )
                else:
                    await self.update_by_tenanted_primary_key(
                        Contact,
                        organization_id=organization_id,
                        primary_key_to_value={"id": contact_id},
                        column_to_update=ContactUpdate(
                            primary_phone_number=None,
                            updated_by_user_id=user_id,
                        ),
                        exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                    )

        return DeleteContactPhoneNumberResult(
            deleted=deleted_contact_phone_number,
            deleted_phone_number_account_associations=deleted_contact_phone_number_account_associations,
            promoted=next_primary_contact_phone_number_candidate,
        )

    async def delete_contact_email_account_association(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        account_id: UUID,
        contact_email_id: UUID,
        _in_nested_transaction: bool = False,
    ) -> DeleteContactEmailAccountAssociationResult:
        association_to_delete = one_row_or_none(
            [
                cea
                for cea in await self._find_by_column_values(
                    ContactEmailAccountAssociation,
                    organization_id=organization_id,
                    contact_id=contact_id,
                    account_id=account_id,
                    contact_email_id=contact_email_id,
                    exclude_deleted_or_archived=False,
                )
                if not cea.deleted_at
            ]
        )

        if not association_to_delete:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code="CONTACT_EMAIL_ACCOUNT_ASSOCIATION_NOT_FOUND",
                    details="contact email account association not found",
                )
            )

        if association_to_delete.archived_at:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code="CONTACT_DELETE_ARCHIVED_CONTACT_EMAIL_ACCOUNT_ASSOCIATION",
                    details="delete archived contact email account association",
                )
            )

        next_primary_candidate: ContactEmailAccountAssociation | None = None
        if association_to_delete.is_contact_account_primary:
            candidates = sorted(
                [
                    candidate
                    for candidate in cast(
                        list[ContactEmailAccountAssociation],
                        await self.list_contact_channel_info_account_associations(
                            contact_channel_type=ContactChannelType.EMAIL,
                            organization_id=organization_id,
                            contact_id=contact_id,
                            account_id=account_id,
                        ),
                    )
                    if candidate.id != association_to_delete.id
                ],
                key=lambda x: x.created_at,
                reverse=True,
            )
            next_primary_candidate = candidates[0] if candidates else None

        async with self.engine.begin() if not _in_nested_transaction else nullcontext():
            deleted_association = await self.update_by_tenanted_primary_key(
                ContactEmailAccountAssociation,
                organization_id=organization_id,
                primary_key_to_value=association_to_delete.primary_key_to_value(),
                column_to_update=ContactEmailAccountAssociationUpdate(
                    deleted_at=zoned_utc_now(),
                    deleted_by_user_id=user_id,
                    updated_by_user_id=user_id,
                ),
                column_condition=ContactEmailAccountAssociationUpdateCondition(
                    is_contact_account_primary=association_to_delete.is_contact_account_primary,
                ),
            )
            if not deleted_association:
                raise ConcurrentModificationError(
                    f"failed to delete contact email account association {association_to_delete.id} in organization"
                )

            if next_primary_candidate:
                await self.update_by_tenanted_primary_key(
                    ContactEmailAccountAssociation,
                    organization_id=organization_id,
                    primary_key_to_value=next_primary_candidate.primary_key_to_value(),
                    column_to_update=ContactEmailAccountAssociationUpdate(
                        is_contact_account_primary=True,
                        updated_by_user_id=user_id,
                    ),
                    column_condition=ContactEmailAccountAssociationUpdateCondition(
                        deleted_at=None,
                    ),
                )

        # update contact email label if there is no account association
        if not await self.list_contact_channel_info_account_associations_by_contact_channel_info_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=organization_id,
            contact_channel_info_id=contact_email_id,
        ):
            await self.update_contact_email_label_by_contact_email_id(
                user_id=user_id,
                organization_id=organization_id,
                contact_email_id=contact_email_id,
                label=ContactChannelLabel.PERSONAL,
            )
        return DeleteContactEmailAccountAssociationResult(
            deleted_email_account_association=deleted_association,
            promoted=next_primary_candidate,
        )

    async def archive_contact_email_account_associations_by_account_id(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        account_id: UUID,
    ) -> list[ContactEmailAccountAssociation]:
        return await self._update_by_column_values(
            ContactEmailAccountAssociation,
            column_value_to_query={
                "contact_id": contact_id,
                "account_id": account_id,
                "organization_id": organization_id,
            },
            column_to_update=ContactEmailAccountAssociationUpdate(
                archived_at=zoned_utc_now(),
                archived_by_user_id=user_id,
                updated_by_user_id=user_id,
            ),
        )

    async def update_contact_email_label_by_contact_email_id(
        self,
        user_id: UUID,
        organization_id: UUID,
        contact_email_id: UUID,
        label: ContactChannelLabel,
    ) -> ContactEmail | None:
        return await self._update_unique_by_column_values(
            ContactEmail,
            column_value_to_query={
                "id": contact_email_id,
                "organization_id": organization_id,
            },
            column_to_update={
                "labels": [label],
                "updated_at": zoned_utc_now(),
                "updated_by_user_id": user_id,
            },
        )

    async def _archive_contact_email(
        self, *, organization_id: UUID, user_id: UUID, contact_id: UUID
    ) -> list[ArchiveContactEmailResult]:
        """
        While archiving a contact, archive all email and email account associations of the contact at the same time

        Args:
            organization_id (UUID): The organization ID the contact belongs to
            user_id (UUID): The ID of the user performing the deletion
            contact_id (UUID): The ID of the contact whose email is being deleted
        Returns:
            list[ArchiveContactEmailResult]t: A named tuple containing:
                - archived: The archived ContactEmail record
                - archived_email_account_associations: The related archived ContactEmailAccountAssociation records

        Raises:
            ConcurrentModificationError: If concurrent updates prevent the operation

        Transaction Safety:
            - Uses optimistic locking via column conditions
        """
        archive_result = []
        contact_emails = cast(
            list[ContactEmail],
            await self.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_id=contact_id,
            ),
        )
        for contact_email in contact_emails:
            if contact_email.deleted_at or contact_email.archived_at:
                continue

            archived_contact_email = await self.update_by_tenanted_primary_key(
                ContactEmail,
                organization_id=organization_id,
                exclude_deleted_or_archived=True,
                primary_key_to_value=contact_email.primary_key_to_value(),
                column_to_update=ContactEmailUpdate(
                    archived_at=zoned_utc_now(),
                    archived_by_user_id=user_id,
                    updated_by_user_id=user_id,
                ),
                column_condition=ContactEmailUpdateCondition(
                    updated_at=contact_email.updated_at,
                ),
            )
            if not archived_contact_email:
                raise ConcurrentModificationError(
                    f"failed to archive contact email {contact_email.email} in organization"
                )

            archived_contact_email_account_associations = (
                await self._update_by_column_values(
                    ContactEmailAccountAssociation,
                    column_value_to_query={
                        "contact_id": contact_id,
                        "organization_id": organization_id,
                    },
                    column_to_update=ContactEmailAccountAssociationUpdate(
                        archived_at=zoned_utc_now(),
                        archived_by_user_id=user_id,
                        updated_by_user_id=user_id,
                    ),
                )
            )

            archive_result.append(
                ArchiveContactEmailResult(
                    archived=archived_contact_email,
                    archived_email_account_associations=archived_contact_email_account_associations,
                )
            )

        return archive_result

    async def _unarchive_contact_email(
        self, *, organization_id: UUID, user_id: UUID, contact_id: UUID
    ) -> list[UnArchiveContactEmailResult]:
        """
        While un-archiving a contact, un-archive all email and email account associations of the contact at the same time

        Args:
            organization_id (UUID): The organization ID the contact belongs to
            user_id (UUID): The ID of the user performing the deletion
            contact_id (UUID): The ID of the contact whose email is being deleted
        Returns:
            list[ArchiveContactEmailResult]t: A named tuple containing:
                - unarchived: The unarchived ContactEmail record
                - unarchived_email_account_associations: The related unarchived ContactEmailAccountAssociation records

        Raises:
            ConcurrentModificationError: If concurrent updates prevent the operation

        Transaction Safety:
            - Uses optimistic locking via column conditions
        """
        archive_result = []
        contact_emails = await self._find_by_column_values(
            ContactEmail,
            organization_id=organization_id,
            contact_id=contact_id,
            exclude_deleted_or_archived=False,
        )
        for contact_email in contact_emails:
            if not contact_email.archived_at:
                continue

            unarchived_contact_email = await self.update_by_tenanted_primary_key(
                ContactEmail,
                organization_id=organization_id,
                exclude_deleted_or_archived=False,
                primary_key_to_value=contact_email.primary_key_to_value(),
                column_to_update=ContactEmailUpdate(
                    archived_at=None,
                    archived_by_user_id=None,
                    updated_by_user_id=user_id,
                ),
                column_condition=ContactEmailUpdateCondition(
                    updated_at=contact_email.updated_at,
                ),
            )
            if not unarchived_contact_email:
                raise ConcurrentModificationError(
                    f"failed to un-archive contact email {contact_email.email} in organization"
                )

            unarchived_contact_email_account_associations = (
                await self._update_by_column_values(
                    ContactEmailAccountAssociation,
                    column_value_to_query={
                        "contact_id": contact_id,
                        "organization_id": organization_id,
                    },
                    column_to_update=ContactEmailAccountAssociationUpdate(
                        archived_at=None,
                        archived_by_user_id=None,
                        updated_by_user_id=user_id,
                    ),
                )
            )
            archive_result.append(
                UnArchiveContactEmailResult(
                    unarchived=unarchived_contact_email,
                    unarchived_email_account_associations=unarchived_contact_email_account_associations,
                )
            )

        return archive_result

    async def _check_delete_contact_email(
        self,
        organization_id: UUID,
        contact_id: UUID,
        alternative_email: EmailStrLower | None = None,
        alternative_account_email: EmailStrLower | None = None,
    ) -> tuple[ContactEmail | None, ContactEmail | None]:
        alternative_contact_email, alternative_contact_account_email = None, None
        if alternative_email:
            alternative_contact_email = (
                await self._find_unique_by_column_values_or_fail(
                    ContactEmail,
                    organization_id=organization_id,
                    contact_id=contact_id,
                    email=alternative_email.lower().strip(),
                    exclude_deleted_or_archived=True,
                )
            )
        if alternative_account_email:
            alternative_contact_account_email = (
                await self._find_unique_by_column_values_or_fail(
                    ContactEmail,
                    organization_id=organization_id,
                    contact_id=contact_id,
                    email=alternative_account_email.lower().strip(),
                    exclude_deleted_or_archived=True,
                )
            )
        return alternative_contact_email, alternative_contact_account_email

    async def find_primary_account_company_name_for_contact_ids(
        self,
        contact_ids: set[UUID],
        organization_id: UUID,
    ) -> Mapping[UUID, str]:
        if not contact_ids:
            return {}
        stmt = text(
            """
            select
                caa.contact_id           as contact_id,
                a.display_name as primary_account_company_name
            from contact_account_association caa
            left join account a
                on caa.account_id = a.id and a.organization_id = caa.organization_id
            where
                a.archived_at is null
                and caa.archived_at is null
                and caa.contact_id = any(:contact_ids)
                and caa.organization_id = :organization_id
                and caa.is_primary = true
            """,
        ).bindparams(
            contact_ids=list(contact_ids),
            organization_id=organization_id,
        )

        rows = await self.engine.all(stmt)
        return {
            UUID(row.contact_id)
            if not isinstance(row.contact_id, UUID)
            else row.contact_id: str(row.primary_account_company_name)
            for row in rows
        }

    ########################################
    # Contact Repository V3 Methods Ends #
    ########################################

    ########################################
    # Contact Repository V2 Methods Starts #
    ########################################

    async def create_or_unarchive_contact(  # noqa: C901
        self,
        organization_id: UUID,
        db_contact: Contact,
        db_contact_address: Address | None,
        db_contact_emails: list[
            tuple[ContactEmail, list[ContactEmailAccountAssociation]]
        ],
        db_contact_phone_numbers: list[
            tuple[ContactPhoneNumber, list[ContactPhoneNumberAccountAssociation]]
        ],
        db_contact_account_associations: list[ContactAccountAssociation],
    ) -> Contact:
        """
        Create a new contact in the database.
        - If a contact with the same primary_email and organization_id already exists,
            raise a ConflictResourceError with code CONTACT_ALREADY_EXISTS_WITH_EMAIL.
        - If a contact with the same primary_email and organization_id already exists
            but is archived, update and reactivate it.
        """
        # At most one account association is supported for now in the legacy create_contact method
        if db_contact_account_associations and not (
            await self.find_by_tenanted_primary_key(
                Account,
                organization_id=organization_id,
                id=db_contact_account_associations[0].account_id,
            )
        ):
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code="ACCOUNT_NOT_FOUND",
                    details="associated account with organization_id and id not found.",
                )
            )
        try:
            async with self.engine.begin():
                if db_contact_address:
                    await self.insert(db_contact_address)
                contact: Contact = await self.insert(db_contact)
                # At most one contact email is supported for now in the legacy create_contact method
                if db_contact_emails:
                    # for fluently migrating to new create_contact method, insert into contact_email in sync
                    contact_email_to_add, contact_email_account_associations_to_add = (
                        db_contact_emails[0]
                    )
                    await self.upsert_contact_email(
                        _in_nested_transaction=True,
                        overwrite_archived=True,
                        contact_email=contact_email_to_add,
                        contact_email_account_associations=contact_email_account_associations_to_add,
                    )
                if db_contact_phone_numbers:
                    (
                        contact_phone_number_to_add,
                        contact_phone_number_account_associations_to_add,
                    ) = db_contact_phone_numbers[0]
                    await self.upsert_contact_phone_number(
                        _in_nested_transaction=True,
                        contact_phone_number=contact_phone_number_to_add,
                        contact_phone_number_account_associations=contact_phone_number_account_associations_to_add,
                    )
                if db_contact_account_associations:
                    association_result = await self.upsert_contact_account_association(
                        _in_nested_transaction=True,
                        association=db_contact_account_associations[0],
                    )
                    contact = association_result.updated_contact or contact
            return contact
        except IntegrityError as e:
            primary_contact_email = not_none(
                next(
                    (ce[0] for ce in db_contact_emails if ce[0].is_contact_primary),
                    None,
                )
            )

            if not (
                existing_contact := await self.find_contact_by_primary_email(
                    organization_id=organization_id,
                    primary_email=primary_contact_email.email,
                )
            ):
                raise IllegalStateError(
                    "existing active contact with primary_email and organization_id not found, but failed to insert contact"
                ) from e
            if existing_contact.archived_at:
                logger.info(
                    "Contact with primary_email and organization_id already "
                    "exists but is archived, updating and reactivating it",
                    organization_id=organization_id,
                    existing_contact_id=existing_contact.id,
                )
            else:
                raise ConflictResourceError(
                    additional_error_details=ConflictErrorDetails(
                        code="CONTACT_ALREADY_EXISTS_WITH_EMAIL",
                        error_code=ErrorCode.CONTACT_ALREADY_EXISTS_WITH_EMAIL,
                        details="contact with primary_email and organization_id already exists.",
                        reference_id=str(existing_contact.id),
                        conflicted_existing_object=StdObjectIdentifiers.contact.identifier,
                        conflicted_existing_object_attrs={
                            ContactField.organization_id: existing_contact.organization_id,
                            ContactField.id: existing_contact.id,
                            ContactField.display_name: existing_contact.display_name,
                            ContactField.primary_email: primary_contact_email.email,
                        },
                    )
                )
            async with self.engine.begin():
                unarchived_contact: Contact = not_none(
                    await self.update_instance(
                        strict_model_copy(db_contact, id=existing_contact.id)
                    )
                )
                if db_contact_account_associations:
                    association_result = await self.upsert_contact_account_association(
                        _in_nested_transaction=True,
                        association=strict_model_copy(
                            db_contact_account_associations[0],
                            updates={
                                "user_id": existing_contact.created_by_user_id,
                                "contact_id": existing_contact.id,
                            },
                        ),
                    )
                    unarchived_contact = (
                        association_result.updated_contact or unarchived_contact
                    )
                if db_contact_address:
                    await self.insert(db_contact_address)
                return unarchived_contact

    async def find_contact_account_roles_map_by_association_ids(
        self,
        organization_id: UUID,
        association_ids: list[UUID],
    ) -> list[ContactAccountRole]:
        stmt = text(
            """
            select caa.* from contact_account_association caa
                where caa.organization_id = :organization_id
                  and caa.id = any(:association_ids)
                  and caa.archived_at is null
            """
        ).bindparams(
            organization_id=organization_id,
            association_ids=association_ids,
        )
        rows = await self.engine.all(stmt)
        deserialized_rows = await ContactAccountAssociation.bulk_from_rows(rows=rows)

        data = []
        for row in deserialized_rows:
            data.append(contact_account_role_from_db(contact_account_association=row))

        return data

    async def find_contact_account_roles_map_by_organization_id(
        self,
        organization_id: UUID,
    ) -> list[ContactAccountRole]:
        stmt = text(
            """
            select caa.* from contact_account_association caa
                where caa.organization_id = :organization_id
                  and caa.archived_at is null
                  order by caa.created_at desc
            """
        ).bindparams(
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        deserialized_rows = await ContactAccountAssociation.bulk_from_rows(rows=rows)

        data = []
        for row in deserialized_rows:
            data.append(contact_account_role_from_db(contact_account_association=row))

        return data

    async def map_active_contact_account_association_by_contact_ids(
        self,
        *,
        organization_id: UUID,
        contact_ids: set[UUID],
        is_primary: bool = False,
    ) -> frozendict[UUID, list[ContactAccountAssociation]]:
        if not contact_ids:
            return frozendict[UUID, list[ContactAccountAssociation]]()

        is_primary_filter = " and caa.is_primary " if is_primary else ""
        stmt = text(
            f"""
            select caa.* from contact_account_association caa
                where caa.organization_id = :organization_id
                  and caa.contact_id = any(:contact_ids)
                  and caa.archived_at is null
                  {is_primary_filter}
            """  # noqa: S608
        ).bindparams(
            organization_id=organization_id,
            contact_ids=list(contact_ids),
        )
        rows = await self.engine.all(stmt)
        deserialized_rows = await ContactAccountAssociation.bulk_from_rows(rows=rows)
        association_by_contact_id: dict[UUID, list[ContactAccountAssociation]] = (
            defaultdict(list)
        )
        for association in deserialized_rows:
            association_by_contact_id[association.contact_id].append(association)
        # for those contact_id don't have any contact_account_association, return empty list
        for contact_id in contact_ids:
            if not association_by_contact_id.get(contact_id):
                association_by_contact_id[contact_id] = []
        return frozendict(association_by_contact_id)

    async def map_active_contact_account_association_by_account_ids(
        self, *, organization_id: UUID, account_ids: set[UUID]
    ) -> frozendict[UUID, list[ContactAccountAssociation]]:
        if not account_ids:
            return frozendict[UUID, list[ContactAccountAssociation]]()
        contact_account_associations = await self._find_by_column_values(
            ContactAccountAssociation,
            exclude_deleted_or_archived=True,
            organization_id=organization_id,
            account_id=list(account_ids),
        )
        res: dict[UUID, list[ContactAccountAssociation]] = defaultdict(list)
        for contact_account_association in contact_account_associations:
            res[contact_account_association.account_id].append(
                contact_account_association
            )
        return frozendict(res)

    async def map_contact_account_association_journeys_by_account_id_for_contact(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> frozendict[UUID, ContactAccountAssociationJourney]:
        if specified(account_ids) and not account_ids:
            return frozendict[UUID, ContactAccountAssociationJourney]()
        account_ids_filter = (
            " and caa.account_id = any(:account_ids)" if specified(account_ids) else ""
        )
        # Note: Since we are mapping out association journeys, we intentionally do not filter out archived associations.
        stmt = text(
            f"""
            select caa.* from contact_account_association caa
                where caa.organization_id = :organization_id
                  and caa.contact_id = :contact_id
                  {account_ids_filter}
                  order by caa.created_at desc
            """  # noqa: S608
        ).bindparams(
            organization_id=organization_id,
            contact_id=contact_id,
        )
        if specified(account_ids):
            stmt = stmt.bindparams(account_ids=list(account_ids))
        rows = await self.engine.all(stmt)
        deserialized_rows = await ContactAccountAssociation.bulk_from_rows(rows=rows)
        association_by_account_id: dict[UUID, list[ContactAccountAssociation]] = (
            defaultdict(list)
        )
        for association in deserialized_rows:
            association_by_account_id[association.account_id].append(association)
        return frozendict(
            {
                account_id: ContactAccountAssociationJourney(
                    contact_id=contact_id,
                    account_id=account_id,
                    associations_ordered_by_created_at_desc=tuple(
                        sorted(associations, key=lambda x: x.created_at, reverse=True)
                    ),
                )
                for account_id, associations in association_by_account_id.items()
            }
        )

    async def map_latest_contact_account_association_by_account_id_for_contact(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> frozendict[UUID, ContactAccountAssociation]:
        association_journeys = await self.map_contact_account_association_journeys_by_account_id_for_contact(
            organization_id=organization_id,
            contact_id=contact_id,
            account_ids=account_ids,
        )
        return frozendict(
            {
                account_id: journey.latest_association
                for account_id, journey in association_journeys.items()
                if journey.latest_association
            }
        )

    async def map_active_contact_account_association_by_account_id_for_contact(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> frozendict[UUID, ContactAccountAssociation]:
        if specified(account_ids) and not account_ids:
            return frozendict[UUID, ContactAccountAssociation]()
        account_ids_filter = (
            " and caa.account_id = any(:account_ids)" if specified(account_ids) else ""
        )
        stmt = text(
            f"""
            select caa.* from contact_account_association caa
                where caa.organization_id = :organization_id
                  and caa.contact_id = :contact_id
                  and caa.archived_at is null
                  {account_ids_filter}
                  order by caa.created_at desc
            """  # noqa: S608
        ).bindparams(
            organization_id=organization_id,
            contact_id=contact_id,
        )
        if specified(account_ids):
            stmt = stmt.bindparams(account_ids=list(account_ids))
        rows = await self.engine.all(stmt)
        deserialized_rows = await ContactAccountAssociation.bulk_from_rows(rows=rows)
        return frozendict(
            {association.account_id: association for association in deserialized_rows}
        )

    async def find_active_contact_account_association(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID,
    ) -> ContactAccountAssociation | None:
        return (
            await self.map_active_contact_account_association_by_account_id_for_contact(
                organization_id=organization_id,
                contact_id=contact_id,
                account_ids={account_id},
            )
        ).get(account_id)

    async def list_active_contact_associations_for_account(
        self,
        *,
        organization_id: UUID,
        account_id: UUID,
    ) -> list[ContactAccountAssociation]:
        stmt = text(
            """
            select caa.* from contact_account_association caa
                where caa.organization_id = :organization_id
                  and caa.account_id = :account_id
                  and caa.archived_at is null
            """
        ).bindparams(
            organization_id=organization_id,
            account_id=account_id,
        )
        return [
            ContactAccountAssociation.from_row(row)
            for row in await self.engine.all(stmt)
        ]

    async def list_active_contact_associations_for_contact(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
    ) -> list[ContactAccountAssociation]:
        stmt = text(
            """
            select caa.* from contact_account_association caa
                where caa.organization_id = :organization_id
                  and caa.contact_id = :contact_id
                  and caa.archived_at is null
            """
        ).bindparams(
            organization_id=organization_id,
            contact_id=contact_id,
        )
        return [
            ContactAccountAssociation.from_row(row)
            for row in await self.engine.all(stmt)
        ]

    async def list_contact_pipeline_associations(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None = None,
        exclude_deleted_or_archived: bool = True,
    ) -> list[ContactPipelineAssociation]:
        archived_at_filter = (
            "and cpa.archived_at is null" if exclude_deleted_or_archived else ""
        )
        if account_id:
            stmt = text(
                f"""
                select cpa.* from contact_pipeline_association cpa
                    join pipeline p on cpa.organization_id = p.organization_id and cpa.pipeline_id = p.id
                    where cpa.organization_id = :organization_id
                      and cpa.contact_id = :contact_id
                      and p.account_id = :account_id
                      {archived_at_filter}
                """  # noqa: S608
            ).bindparams(
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=account_id,
            )
        else:
            stmt = text(
                f"""
                select cpa.* from contact_pipeline_association cpa
                    where cpa.organization_id = :organization_id
                      and cpa.contact_id = :contact_id
                      {archived_at_filter}
                """  # noqa: S608
            ).bindparams(
                organization_id=organization_id,
                contact_id=contact_id,
            )

        rows = await self.engine.all(stmt)
        return await ContactPipelineAssociation.bulk_from_rows(rows=rows)

    async def _find_candidate_primary_association(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        excluded_account_ids: set[UUID],
    ) -> ContactAccountAssociation | None:
        all_active_associations_by_account_id = (
            await self.map_active_contact_account_association_by_account_id_for_contact(
                organization_id=organization_id,
                contact_id=contact_id,
            )
        )
        if not all_active_associations_by_account_id:
            return None
        # todo(xw): this need to be re-associated at in case of back-and-forth
        #  between primary and non-primary associations
        active_associations_by_created_at_desc = sorted(
            all_active_associations_by_account_id.values(),
            key=lambda x: x.created_at,
            reverse=True,
        )
        return next(
            (
                can_asso
                for can_asso in active_associations_by_created_at_desc
                if (not can_asso.archived_at)
                and (can_asso.account_id not in excluded_account_ids)
            ),
            None,
        )

    async def patch_contact(self, request: UpdateContactRequest) -> Contact:  # noqa: C901, PLR0912
        if not (
            existing_contact := await self.find_by_tenanted_primary_key(
                Contact,
                exclude_deleted_or_archived=False,
                organization_id=request.organization_id,
                id=request.contact_id,
            )
        ):
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.CONTACT_NOT_FOUND,
                    details="contact with organization_id and id not found.",
                )
            )
        try:
            async with self.engine.begin():
                contact_update = request.contact_update
                if request.address_request:
                    # Update address
                    if existing_contact.address_id:
                        address = await self.update_by_tenanted_primary_key(
                            Address,
                            exclude_deleted_or_archived=False,
                            organization_id=request.organization_id,
                            primary_key_to_value={"id": existing_contact.address_id},
                            column_to_update=request.address_request.to_db_update(
                                updated_at=contact_update.updated_at
                            ),
                        )
                    else:
                        address = await self.insert(
                            request.address_request.to_db_insert(
                                created_at=request.contact_update.updated_at
                            )
                        )
                    if not address:
                        raise IllegalStateError(
                            f"Address update failed for address id {existing_contact.address_id}"
                        )
                    contact_update = strict_model_copy(
                        contact_update,
                        address_id=address.id,
                    )

                updated_contact: Contact = not_none(
                    await self.update_by_tenanted_primary_key(
                        Contact,
                        exclude_deleted_or_archived=False,
                        organization_id=request.organization_id,
                        primary_key_to_value={"id": request.contact_id},
                        column_to_update=contact_update,
                    )
                )
                original_primary_email = cast(
                    EmailStrLower,
                    await self.get_primary_channel_by_contact_id(
                        contact_channel_type=ContactChannelType.EMAIL,
                        organization_id=request.organization_id,
                        contact_id=request.contact_id,
                    ),
                )

                refresh_contact = await self.try_refresh_display_name(
                    user_id=request.contact_update.updated_by_user_id,
                    contact=updated_contact,
                    primary_email=original_primary_email,
                )
                if refresh_contact:
                    updated_contact = refresh_contact

                    # if the primary email is changed, need to update the primary contact email accordingly
                # there is basically a workaround here, the primary_email field should be removed from patch_contact
                # after multi email migration is done
                primary_email = request.primary_email
                if primary_email or request.remove_current_primary_email:
                    existing_contact_email = cast(
                        ContactEmail,
                        await self.get_primary_contact_channel_info_by_contact_id(
                            contact_channel_type=ContactChannelType.EMAIL,
                            organization_id=request.organization_id,
                            contact_id=request.contact_id,
                        ),
                    )
                    # there will be 4 cases and 3 of them need to handle
                    if (
                        existing_contact_email
                        and primary_email
                        and existing_contact_email.email != primary_email
                    ):
                        # case 1: update the existing primary contact email to another
                        await self._update_unique_by_column_values(
                            ContactEmail,
                            column_value_to_query={
                                "organization_id": request.organization_id,
                                "contact_id": existing_contact.id,
                                "is_contact_primary": True,
                            },
                            column_to_update=ContactEmailUpdate(
                                email=primary_email,
                                updated_by_user_id=contact_update.updated_by_user_id,
                            ),
                        )
                    elif (
                        existing_contact_email
                        and not primary_email
                        and request.remove_current_primary_email
                    ):
                        # case 2: delete the existing primary contact email (set to null)
                        await self.delete_contact_email(
                            _in_nested_transaction=True,
                            organization_id=existing_contact.organization_id,
                            contact_id=existing_contact.id,
                            email=existing_contact_email.email,
                            user_id=contact_update.updated_by_user_id,
                        )
                    elif not existing_contact_email and primary_email:
                        # case 3: add a new primary contact email (null -> not null)
                        await self.upsert_contact_email(
                            _in_nested_transaction=True,
                            contact_email=ContactEmail(
                                organization_id=existing_contact.organization_id,
                                contact_id=existing_contact.id,
                                email=str(primary_email),
                                is_contact_primary=True,
                                created_by_user_id=contact_update.updated_by_user_id,
                                updated_by_user_id=contact_update.updated_by_user_id,
                            ),
                            contact_email_account_associations=[],
                        )
                await self._update_or_delete_contact_phone_number_for_contact(
                    organization_id=request.organization_id,
                    contact_id=request.contact_id,
                    existing_contact=existing_contact,
                    primary_phone_number=request.contact_update.primary_phone_number,
                    contact_update=contact_update,
                )
                return updated_contact

        except IntegrityError as e:
            if specified(request.primary_email) and (
                primary_email := request.primary_email
            ):
                contact_from_primary_email = await self.find_contact_by_primary_email(
                    organization_id=request.organization_id,
                    primary_email=primary_email,
                )
                if (
                    contact_from_primary_email
                    and contact_from_primary_email.id != request.contact_id
                ):
                    raise ConflictResourceError(
                        additional_error_details=ConflictErrorDetails(
                            code=ErrorCode.CONTACT_ALREADY_EXISTS_WITH_EMAIL,
                            details="contact with primary_email and organization_id"
                            " already exists.",
                            reference_id=str(contact_from_primary_email.id),
                            conflicted_existing_object=StdObjectIdentifiers.contact.identifier,
                            conflicted_existing_object_attrs={
                                ContactField.organization_id: contact_from_primary_email.organization_id,
                                ContactField.id: contact_from_primary_email.id,
                                ContactField.display_name: contact_from_primary_email.display_name,
                                ContactField.primary_email: primary_email,
                            },
                        )
                    )
            raise e

    async def try_refresh_display_name(
        self,
        user_id: UUID,
        contact_id: UUID | None = None,
        contact: Contact | None = None,
        primary_email: str | None = None,
    ) -> Contact | None:
        if not contact and contact_id:
            contact = await self.find_by_primary_key(Contact, id=contact_id)
        if not contact:
            return None
        updated_display_name = self._generate_display_name(
            contact=contact, primary_email=primary_email
        )
        if updated_display_name != contact.display_name:
            return not_none(
                await self.update_by_tenanted_primary_key(
                    Contact,
                    exclude_deleted_or_archived=False,
                    organization_id=contact.organization_id,
                    primary_key_to_value={"id": contact.id},
                    column_to_update=ContactUpdate(
                        display_name=updated_display_name,
                        updated_at=zoned_utc_now(),
                        updated_by_user_id=user_id,
                    ),
                )
            )
        return None

    def _generate_display_name(
        self, contact: Contact, primary_email: str | None = None
    ) -> str:
        proposed_display_name = contact.display_name
        if contact.first_name and contact.last_name:
            proposed_display_name = f"{contact.first_name} {contact.last_name}"
        elif contact.first_name:
            proposed_display_name = contact.first_name
        elif contact.last_name:
            proposed_display_name = contact.last_name
        elif contact.middle_name:
            proposed_display_name = contact.middle_name
        elif primary_email:
            proposed_display_name = primary_email
        elif contact.primary_email:
            proposed_display_name = contact.primary_email
        elif contact.primary_phone_number:
            proposed_display_name = contact.primary_phone_number

        return proposed_display_name

    async def _update_or_delete_contact_phone_number_for_contact(
        self,
        organization_id: UUID,
        contact_id: UUID,
        existing_contact: Contact,
        primary_phone_number: UnsetAware[PhoneNumberWithExtension | None],
        contact_update: ContactUpdate,
    ) -> None:
        # if the primary phone number is changed, need to update the primary contact phone number accordingly
        # there is basically a workaround here, the primary_email field should be removed from patch_contact
        # after multi phone number migration is done
        if specified(primary_phone_number):
            existing_contact_phone_number = cast(
                ContactPhoneNumber,
                await self.get_primary_contact_channel_info_by_contact_id(
                    contact_channel_type=ContactChannelType.PHONE_NUMBER,
                    organization_id=organization_id,
                    contact_id=contact_id,
                ),
            )
            if (
                existing_contact_phone_number
                and primary_phone_number
                and existing_contact_phone_number.phone_number != primary_phone_number
            ):
                # case 1: update the existing primary contact phone number to another
                await self._update_unique_by_column_values(
                    ContactPhoneNumber,
                    column_value_to_query={
                        "organization_id": organization_id,
                        "contact_id": existing_contact.id,
                        "is_contact_primary": True,
                    },
                    column_to_update=ContactPhoneNumberUpdate(
                        phone_number=primary_phone_number,
                        updated_by_user_id=contact_update.updated_by_user_id,
                    ),
                )
            elif (
                existing_contact_phone_number
                and specified(primary_phone_number)
                and not primary_phone_number
            ):
                # case 2: delete the existing primary contact email (set to null)
                await self.delete_contact_phone_number(
                    _in_nested_transaction=True,
                    organization_id=existing_contact.organization_id,
                    contact_id=existing_contact.id,
                    phone_number=existing_contact_phone_number.phone_number,
                    user_id=contact_update.updated_by_user_id,
                )
            elif not existing_contact_phone_number and primary_phone_number:
                # case 3: add a new primary contact email (null -> not null)
                await self.upsert_contact_phone_number(
                    _in_nested_transaction=True,
                    contact_phone_number=ContactPhoneNumber(
                        organization_id=existing_contact.organization_id,
                        contact_id=existing_contact.id,
                        phone_number=str(primary_phone_number),
                        is_contact_primary=True,
                        created_by_user_id=contact_update.updated_by_user_id,
                        updated_by_user_id=contact_update.updated_by_user_id,
                    ),
                    contact_phone_number_account_associations=[],
                )

    async def archive_contact(
        self,
        organization_id: UUID,
        contact_id: UUID,
        archived_by_user_id: UUID,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> Contact:
        if not (
            contact := await self.find_by_tenanted_primary_key(
                Contact,
                exclude_deleted_or_archived=False,
                exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                organization_id=organization_id,
                id=contact_id,
            )
        ):
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.CONTACT_NOT_FOUND,
                    details="contact with organization_id and id not found.",
                )
            )
        if contact.archived_at:
            logger.warning("contact already archived", contact=contact)
            return contact

        # check if the contact <> account association has an active pipeline association,
        # if so, raise an InvalidArgumentError to roll back the transaction.
        # this is to ensure we don't accidentally remove any contact <> account association
        # that is still being used in a pipeline due to a race condition.
        if (
            _active_pipeline_associations
            := await self.list_contact_pipeline_associations(
                organization_id=organization_id,
                contact_id=contact_id,
                exclude_deleted_or_archived=True,
            )
        ):
            _impacted_pipeline_ids = [
                association.pipeline_id for association in _active_pipeline_associations
            ]
            raise ReferentialViolationError(
                "contact has active pipeline associations",
                additional_error_details=ReferentialViolationErrorDetails(
                    violating_object=StdObjectIdentifiers.contact.identifier,
                    error_code=ErrorCode.CONTACT_ACCOUNT_ASSOCIATION_HAS_ACTIVE_PIPELINE_ASSOCIATION,
                    violating_relationship_id=ContactRelationship.contact__from__contact_pipeline_role,
                    dependent_object=StdObjectIdentifiers.pipeline.identifier,
                    dependent_object_record_ids=_impacted_pipeline_ids,
                ),
            )
        # todo(xw): Add post update validation on pipeline associations in transaction.

        async with self.engine.begin():
            if not (
                updated_contact := await self.update_by_tenanted_primary_key(
                    Contact,
                    organization_id=organization_id,
                    primary_key_to_value=contact.primary_key_to_value(),
                    exclude_deleted_or_archived=False,
                    exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                    column_to_update=ContactUpdate(
                        archived_at=zoned_utc_now(),
                        archived_by_user_id=archived_by_user_id,
                        updated_at=zoned_utc_now(),
                        updated_by_user_id=archived_by_user_id,
                    ),
                    column_condition=ContactUpdateCondition(
                        updated_at=contact.updated_at
                    ),
                )
            ):
                raise ConcurrentModificationError(
                    "The contact being archived is modified by another process. "
                    f"Please retry: contact (id={contact.id})"
                )

            await self._archive_contact_email(
                organization_id=organization_id,
                contact_id=contact_id,
                user_id=archived_by_user_id,
            )
        return updated_contact

    async def unarchive_contact(
        self, organization_id: UUID, contact_id: UUID, unarchived_by_user_id: UUID
    ) -> Contact:
        if not (
            contact := await self.find_by_tenanted_primary_key(
                Contact,
                exclude_deleted_or_archived=False,
                organization_id=organization_id,
                id=contact_id,
            )
        ):
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.CONTACT_NOT_FOUND,
                    details="contact with organization_id and id not found.",
                )
            )
        if not contact.archived_at:
            logger.warning("contact already un-archived", contact=contact)
            return contact

        async with self.engine.begin():
            if not (
                updated_contact := await self.update_by_tenanted_primary_key(
                    Contact,
                    organization_id=organization_id,
                    primary_key_to_value=contact.primary_key_to_value(),
                    exclude_deleted_or_archived=False,
                    column_to_update=ContactUpdate(
                        archived_at=None,
                        archived_by_user_id=None,
                        updated_at=zoned_utc_now(),
                        updated_by_user_id=unarchived_by_user_id,
                    ),
                    column_condition=ContactUpdateCondition(
                        updated_at=contact.updated_at
                    ),
                )
            ):
                raise ConcurrentModificationError(
                    "The contact being un-archived is modified by another process. "
                    f"Please retry: contact (id={contact.id})"
                )

            await self._unarchive_contact_email(
                organization_id=organization_id,
                contact_id=contact_id,
                user_id=unarchived_by_user_id,
            )
        return updated_contact

    async def find_contact_id_display_name_by_emails(
        self,
        *,
        organization_id: UUID,
        emails: set[EmailStrLower],
    ) -> list[ContactIdDisplayNameFromEmail]:
        if not emails:
            return []
        stmt = (
            text(
                """
                select ce.email as from_email, c.id, c.display_name, c.organization_id
                from contact_email ce
                left join contact c
                on ce.contact_id = c.id
                where ce.organization_id = :organization_id
                and ce.email in :emails
                and ce.archived_at is null and ce.deleted_at is null
                and c.archived_at is null;
                """
            )
            .bindparams(organization_id=organization_id)
            .bindparams(bindparam("emails", list(emails), expanding=True))
        )
        rows = await self.engine.all(stmt)
        return await ContactIdDisplayNameFromEmail.bulk_from_rows(rows=rows)

    async def find_contact_by_primary_email(
        self, organization_id: UUID, primary_email: EmailStrLower
    ) -> Contact | None:
        email_contact_map = await self.find_contacts_by_primary_contact_emails(
            organization_id=organization_id, emails=[primary_email]
        )
        return email_contact_map.get(primary_email)

    async def list_by_person_ids(
        self, person_ids: list[UUID], organization_id: UUID | None
    ) -> list[Contact]:
        if not person_ids:
            return []

        stmt = (
            text(
                """
            select * from contact where person_id in :person_ids and organization_id = :organization_id;
            """
            )
            .bindparams(bindparam("person_ids", person_ids, expanding=True))
            .bindparams(organization_id=organization_id)
        )

        rows = await self.engine.all(stmt)
        return await Contact.bulk_from_rows(rows=rows)

    @staticmethod
    def _get_remove_primary_account_association_statement(
        *,
        organization_id: UUID,
        contact_id: UUID,
        user_id: UUID,
        excluded_account_id: UUID | None = None,
    ) -> TextClause:
        excluded_account_id_clause = (
            " and account_id != :excluded_account_id" if excluded_account_id else ""
        )
        stmt = (
            text(
                f"""
            update contact_account_association set
                is_primary = false,
                updated_at = now(),
                updated_by_user_id = :updated_by_user_id
            where contact_id = :contact_id
              and organization_id = :organization_id
              and is_primary
              and archived_at is null
              {excluded_account_id_clause}
            returning *
            """  # noqa: S608
            )
            .bindparams(organization_id=organization_id)
            .bindparams(contact_id=contact_id)
            .bindparams(updated_by_user_id=user_id)
        )
        if excluded_account_id:
            stmt = stmt.bindparams(excluded_account_id=excluded_account_id)
        return stmt

    @staticmethod
    def _get_upsert_contact_account_association_statement(
        *,
        contact_account_association: ContactAccountAssociation,
        set_as_primary: bool = False,
    ) -> TextClause:
        return text(
            """
            insert into contact_account_association (id,
                                                     organization_id,
                                                     contact_id,
                                                     account_id,
                                                     department,
                                                     title,
                                                     created_at,
                                                     created_by_user_id,
                                                     updated_at,
                                                     updated_by_user_id,
                                                     is_primary
                                                     )
            values (:id,
                    :organization_id,
                    :contact_id,
                    :account_id,
                    :department,
                    :title,
                    :created_at,
                    :created_by_user_id,
                    :updated_at,
                    :updated_by_user_id,
                    :is_primary
                    )
            on conflict (organization_id, contact_id, account_id) where archived_at is null
            do update
                set department          = excluded.department,
                    title               = excluded.title,
                    updated_at          = excluded.updated_at,
                    updated_by_user_id  = excluded.updated_by_user_id,
                    is_primary          = excluded.is_primary
            returning *
            """
        ).bindparams(
            id=contact_account_association.id,
            organization_id=contact_account_association.organization_id,
            contact_id=contact_account_association.contact_id,
            account_id=contact_account_association.account_id,
            department=contact_account_association.department,
            title=contact_account_association.title,
            created_at=contact_account_association.created_at,
            created_by_user_id=contact_account_association.created_by_user_id,
            updated_at=contact_account_association.updated_at,
            updated_by_user_id=contact_account_association.updated_by_user_id,
            is_primary=contact_account_association.is_primary or set_as_primary,
        )

    async def map_contact_by_primary_account_ids(
        self,
        organization_id: UUID,
        primary_account_ids: set[UUID],
        include_archived: bool = False,
    ) -> Mapping[UUID, list[Contact]]:
        """
        List contacts grouped by primary_account_id.
        todo(xw): Properly support m2m contact <> account relationship and
            provide method to list by any account associations.
        """
        if not primary_account_ids:
            return frozendict[UUID, list[Contact]]()
        stmt = (
            text(
                f"""
                select a.account_id, c.*
                from contact c
                left join contact_account_association a
                on c.id = a.contact_id and a.organization_id = c.organization_id
                where c.organization_id = :organization_id
                  and a.account_id = any(:primary_account_ids)
                  and a.is_primary = true
                  and a.archived_at is null
                  and {"true" if include_archived else "c.archived_at is null and c.integrity_job_finished_at is null and c.integrity_job_started_at is null"}
                """  # noqa: S608
            )
            .bindparams(organization_id=organization_id)
            .bindparams(primary_account_ids=list(primary_account_ids))
        )

        rows = await self.engine.all(stmt)
        seen_contact_ids: set[UUID] = set()
        result: Mapping[UUID, list[Contact]] = defaultdict(list)
        for row in rows:
            account_id = row[0]
            contact = Contact.from_row(row)
            result[account_id].append(contact)
            seen_contact_ids.add(contact.id)
        return frozendict[UUID, list[Contact]](result)

    ######################################
    ######################################
    # Contact Repository V2 Methods Ends #
    ######################################

    async def list_all(
        self,
        organization_id: UUID,
        exclude_archived: bool = True,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> list[Contact]:
        return await self._find_by_column_values(
            Contact,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )

    async def list_by_ids(
        self,
        contact_ids: list[UUID],
        organization_id: UUID | None,
    ) -> list[Contact]:
        if not contact_ids:
            return []
        stmt = text(
            """
            select *
            from contact as c
            where c.id = any(:contact_ids)
            and c.organization_id = :organization_id
            """,
        ).bindparams(contact_ids=contact_ids, organization_id=organization_id)

        rows = await self.engine.all(stmt)
        return await Contact.bulk_from_rows(rows=rows)

    async def get_by_id(
        self,
        contact_id: UUID,
        organization_id: UUID,
    ) -> Contact | None:
        return await self._find_unique_by_column_values(
            Contact,
            id=contact_id,
            organization_id=organization_id,
        )

    async def list_by_emails(
        self,
        emails: list[str],
        organization_id: UUID | None,
    ) -> list[Contact]:
        if not emails:
            return []
        stmt = text(
            """
            select c.* from contact as c
            INNER JOIN contact_email c_e on c_e.contact_id = c.id
            where c_e.email in :emails
            and c.organization_id = :organization_id
            and c.archived_at is null
            and c.integrity_job_finished_at is null
            and c.integrity_job_started_at is null
            """,
        ).bindparams(
            bindparam("emails", emails, expanding=True), organization_id=organization_id
        )
        rows = await self.engine.all(stmt)
        duplicate_contacts = await Contact.bulk_from_rows(rows=rows)
        res: list[Contact] = []
        contact_id_set: set[UUID] = set()
        for contact in duplicate_contacts:
            if contact.id not in contact_id_set:
                contact_id_set.add(contact.id)
                res.append(contact)
        return res

    async def list_with_primary_account(
        self,
        organization_id: UUID,
    ) -> list[Contact]:
        stmt = text(
            """
            select c.* from contact c
            left join contact_account_association a
            on c.id = a.contact_id and a.organization_id = c.organization_id
            where
            and a.is_primary = true
            and c.organization_id = :organization_id
            and c.archived_at is null
            and a.archived_at is null
            and c.integrity_job_finished_at is null
            and c.integrity_job_started_at is null
            """,
        ).bindparams(organization_id=organization_id)
        rows = await self.engine.all(stmt)
        return await Contact.bulk_from_rows(rows=rows)

    async def map_contacts_by_primary_emails(
        self,
        *,
        emails: set[str],
        organization_id: UUID,
        exclude_archived: bool = True,
    ) -> frozendict[EmailStr, Contact]:
        if not emails:
            return frozendict[EmailStr, Contact]()
        stmt = (
            text(
                f"""
                select c_e.email as contact_email, c.* from contact as c
                left join contact_email c_e on c_e.contact_id = c.id
                where c_e.email in :emails
                and c_e.is_contact_primary = TRUE
                and c.organization_id = :organization_id
                and {"c.archived_at is null and c.integrity_job_finished_at is null and c.integrity_job_started_at is null" if exclude_archived else "true"}
                """  # noqa: S608
            )
            .bindparams(bindparam("emails", list(emails), expanding=True))
            .bindparams(organization_id=organization_id)
        )
        rows = await self.engine.all(stmt)

        res: dict[EmailStr, Contact] = {}
        for row in rows:
            email = row[0]
            res.update({not_none(email): Contact.from_row(row)})
        return frozendict[EmailStr, Contact](res)

    async def list_by_linkedin_urls(
        self,
        linkedin_urls: list[str],
        organization_id: UUID | None,
    ) -> list[Contact]:
        if not linkedin_urls:
            return []
        stmt = text(
            """
            select * from contact
            where linkedin_url in :linkedin_urls
            and organization_id = :organization_id
            and archived_at is null
            and integrity_job_finished_at is null
            and integrity_job_started_at is null
            """,
        ).bindparams(
            bindparam("linkedin_urls", linkedin_urls, expanding=True),
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        return await Contact.bulk_from_rows(rows=rows)

    async def list_by_pipeline_id(
        self,
        pipeline_id: UUID,
        organization_id: UUID,
        exclude_archived: bool = True,
    ) -> list[Contact]:
        where_archived = "and c.archived_at is null" if exclude_archived else ""
        stmt = text(f"""
        select c.* from contact c
            join contact_pipeline_association cpa
                on c.id = cpa.contact_id and c.organization_id = cpa.organization_id
            where c.organization_id = :organization_id
            and cpa.pipeline_id = :pipeline_id
            and cpa.archived_at is null
            {where_archived}
        """).bindparams(  # noqa: S608
            organization_id=organization_id,
            pipeline_id=pipeline_id,
        )
        rows = await self.engine.all(stmt)
        return await Contact.bulk_from_rows(rows=rows)

    async def find_contacts_by_ids(
        self,
        organization_id: UUID,
        contact_ids: list[UUID],
    ) -> list[Contact]:
        if not contact_ids:
            return []
        return await self._find_by_column_values(
            Contact,
            id=contact_ids,
            organization_id=organization_id,
        )

    async def find_contacts_by_primary_contact_emails(
        self,
        organization_id: UUID,
        emails: list[EmailStr],
    ) -> dict[EmailStr, Contact]:
        stmt = text("""
                        select c_e.email, c.* from contact as c
                        LEFT JOIN contact_email c_e on c_e.contact_id = c.id
                        where c_e.email in :emails
                        and c_e.is_contact_primary = True
                        and c.organization_id = :organization_id
                        and c.archived_at is null
                    """).bindparams(
            bindparam("emails", emails, expanding=True),
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        res: dict[EmailStr, Contact] = {}
        for row in rows:
            res.update({row[0]: Contact.from_row(row)})
        return res

    async def find_contacts_by_contact_emails(
        self, organization_id: UUID, emails: list[EmailStr]
    ) -> dict[EmailStr, Contact]:
        stmt = text("""
            select c_e.email, c.* from contact as c
            LEFT JOIN contact_email c_e on c_e.contact_id = c.id
            where c_e.email in :emails
            and c.organization_id = :organization_id
            and c.archived_at is null
        """).bindparams(
            bindparam("emails", emails, expanding=True),
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        res: dict[EmailStr, Contact] = {}
        for row in rows:
            res.update({row[0]: Contact.from_row(row)})
        return res

    async def find_contact_by_phone_number(
        self,
        organization_id: UUID,
        phone_number: str,
    ) -> Contact | None:
        stmt = text("""
            select c.* from contact c
            where c.organization_id = :organization_id
            and c.primary_phone_number = :phone_number
            and c.archived_at is null
        """).bindparams(
            organization_id=organization_id,
            phone_number=phone_number,
        )
        row = await self.engine.first_or_none(stmt)
        return Contact.from_row(row) if row else None

    async def list_contacts_by_phone_number(
        self,
        organization_id: UUID,
        phone_number: str,
    ) -> list[Contact]:
        # TODO: remove after mutli phone number migration

        # First try exact match
        stmt = text("""
            select c.* from contact c
            where c.organization_id = :organization_id
            and c.primary_phone_number = :phone_number
            and c.archived_at is null
        """).bindparams(
            organization_id=organization_id,
            phone_number=phone_number,
        )
        rows = await self.engine.all(stmt)
        contacts = [Contact.from_row(row) for row in rows]

        # Only try pattern match if no exact matches found
        if not contacts:
            stmt = text("""
                select c.* from contact c
                where c.organization_id = :organization_id
                and c.primary_phone_number LIKE :phone_number_pattern
                and c.archived_at is null
            """).bindparams(
                organization_id=organization_id,
                phone_number_pattern=f"%{phone_number}%",
            )
            rows = await self.engine.all(stmt)
            contacts = [Contact.from_row(row) for row in rows]

        return contacts

    async def list_primary_contact_account_id_pairs(
        self,
        organization_id: UUID,
        only_include_contact_ids: list[UUID] | None,
    ) -> list[tuple[UUID, UUID]]:
        stmt = text("""
            select c.id as contact_id, caa.account_id as account_id
            from contact c
            join contact_account_association caa on c.organization_id = caa.organization_id and c.id = caa.contact_id
            where c.archived_at is null
            and c.integrity_job_finished_at is null
            and c.integrity_job_started_at is null
            and caa.archived_at is null
            and caa.is_primary
            and c.organization_id = :organization_id
            and (:only_include_contact_ids :: uuid[] IS NULL OR  c.id = any(:only_include_contact_ids));
            """).bindparams(
            organization_id=organization_id,
            only_include_contact_ids=only_include_contact_ids,
        )
        rows = await self.engine.all(stmt)
        return [tuple(row) for row in rows]

    async def map_prefer_contact_email_by_contact_id_and_account_id(
        self,
        organization_id: UUID,
        contact_account_pairs: list[tuple[UUID, UUID | None]],
    ) -> dict[UUID, tuple[ContactEmail, ContactEmailAccountAssociation]]:
        contact_account_map = {
            contact_id: account_id
            for contact_id, account_id in contact_account_pairs
            if contact_id and account_id
        }
        stmt = text("""
                    select ce.*, cea.*
                    from contact_email ce
                    left join contact_email_account_association cea
                    on ce.contact_id = cea.contact_id and ce.id = cea.contact_email_id
                    where ce.archived_at is null
                    and cea.archived_at is null
                    and cea.is_contact_account_primary
                    and ce.organization_id = :organization_id
                    and ce.contact_id = any(:contact_ids);
                    """).bindparams(
            organization_id=organization_id,
            contact_ids=list(contact_account_map.keys()),
        )
        rows = await self.engine.all(stmt)
        res: dict[UUID, tuple[ContactEmail, ContactEmailAccountAssociation]] = (
            defaultdict()
        )
        for row in rows:
            contact_email = ContactEmail.from_row(row)
            if not contact_email:
                continue
            contact_email_account_association = ContactEmailAccountAssociation.from_row(
                row
            )
            if contact_email_account_association.account_id != contact_account_map.get(
                contact_email.contact_id
            ):
                continue
            res[contact_email.contact_id] = (
                contact_email,
                contact_email_account_association,
            )
        return res

    async def map_primary_account_prefer_contact_email_by_contact_id(
        self,
        organization_id: UUID,
        contact_ids: list[UUID],
    ) -> dict[UUID, tuple[ContactEmail, ContactEmailAccountAssociation]]:
        stmt = text("""
                    select ce.*, cea.*
                    from contact_email ce
                    left join contact_account_association caa
                    ON ce.contact_id = caa.contact_id AND ce.organization_id = caa.organization_id
                    left join contact_email_account_association cea
                    on ce.id = cea.contact_email_id and caa.account_id = cea.account_id
                    where ce.archived_at is null
                    and caa.archived_at is null
                    and cea.archived_at is null
                    and caa.is_primary
                    and cea.is_contact_account_primary
                    and ce.organization_id = :organization_id
                    and ce.contact_id = any(:contact_ids);
                    """).bindparams(
            organization_id=organization_id,
            contact_ids=contact_ids,
        )
        rows = await self.engine.all(stmt)
        res: dict[UUID, tuple[ContactEmail, ContactEmailAccountAssociation]] = (
            defaultdict()
        )
        for row in rows:
            contact_email = ContactEmail.from_row(row)
            if not contact_email:
                continue
            contact_email_account_association = ContactEmailAccountAssociation.from_row(
                row
            )
            res[contact_email.contact_id] = (
                contact_email,
                contact_email_account_association,
            )
        return res

    async def search_contact_by_email(
        self,
        *,
        organization_id: UUID,
        keyword: str,
        account_id: UUID | None = None,
        need_to_have_account_association: bool = False,
        limit: int | None = None,
    ) -> list[UUID]:
        if account_id:
            query = """
                SELECT DISTINCT ce.contact_id
                FROM contact_email ce
                JOIN contact c ON c.id = ce.contact_id
                JOIN contact_account_association caa ON caa.contact_id = ce.contact_id AND caa.organization_id = ce.organization_id
                WHERE ce.organization_id = :organization_id
                AND c.archived_at IS NULL
                AND ce.deleted_at IS NULL
                AND caa.archived_at IS NULL
                AND caa.account_id = :account_id
                AND LOWER(ce.email) LIKE LOWER(:keyword)
            """
        else:
            query = """
                SELECT DISTINCT ce.contact_id
                FROM contact_email ce
                JOIN contact c ON c.id = ce.contact_id
                LEFT JOIN contact_account_association caa ON caa.contact_id = c.id AND caa.organization_id = ce.organization_id
                WHERE ce.organization_id = :organization_id
                AND c.archived_at IS NULL
                AND ce.deleted_at IS NULL
                AND LOWER(ce.email) LIKE LOWER(:keyword)
            """

        if need_to_have_account_association:
            query += " AND caa.id IS NOT NULL"
        if limit:
            query += f" LIMIT {limit}"

        result = await self.engine.execute(
            text(query),
            {
                "organization_id": organization_id,
                "keyword": f"%{keyword}%",
                "account_id": account_id,
            },
        )

        return [row[0] for row in result]

    async def search_contact_by_display_name(
        self,
        *,
        organization_id: UUID,
        keyword: str,
        account_id: UUID | None = None,
        need_to_have_account_association: bool = False,
        limit: int | None = None,
    ) -> list[UUID]:
        if account_id:
            query = """
                SELECT DISTINCT c.id
                FROM contact c
                JOIN contact_account_association caa ON caa.contact_id = c.id AND caa.organization_id = c.organization_id
                WHERE c.organization_id = :organization_id
                AND c.archived_at IS NULL
                AND caa.archived_at IS NULL
                AND caa.account_id = :account_id
                AND LOWER(c.display_name) LIKE LOWER(:keyword)
            """
        else:
            query = """
                SELECT DISTINCT c.id
                FROM contact c
                LEFT JOIN contact_account_association caa ON caa.contact_id = c.id AND caa.organization_id = c.organization_id
                WHERE c.organization_id = :organization_id
                AND c.archived_at IS NULL
                AND LOWER(c.display_name) LIKE LOWER(:keyword)
            """

        if need_to_have_account_association:
            query += " AND caa.id IS NOT NULL"
        if limit:
            query += f" LIMIT {limit}"

        result = await self.engine.execute(
            text(query),
            {
                "organization_id": organization_id,
                "keyword": f"%{keyword}%",
                "account_id": account_id,
            },
        )

        return [row[0] for row in result]

    async def list_contacts_with_display_name_and_emails(
        self,
        *,
        organization_id: UUID,
        contact_ids: list[UUID],
    ) -> list[ContactSearchResult]:
        if not contact_ids:
            return []

        email_info_query = """
            SELECT c.id, c.display_name, ce.email, ce.is_contact_primary, ce.deleted_at
            FROM contact c
            LEFT JOIN contact_email ce ON ce.contact_id = c.id
            WHERE c.id = ANY(:contact_ids)
            AND c.organization_id = :organization_id
        """

        account_info_query = """
            SELECT caa.contact_id, caa.account_id, a.display_name, caa.is_primary
            FROM contact_account_association caa
            LEFT JOIN account a ON a.id = caa.account_id
            WHERE caa.contact_id = ANY(:contact_ids)
            AND caa.archived_at IS NULL
            AND caa.organization_id = :organization_id
        """

        async with TaskGroup() as tg:
            email_info_task = tg.create_task(
                self.engine.execute(
                    text(email_info_query),
                    {
                        "organization_id": organization_id,
                        "contact_ids": contact_ids,
                    },
                )
            )

            account_info_task = tg.create_task(
                self.engine.execute(
                    text(account_info_query),
                    {
                        "organization_id": organization_id,
                        "contact_ids": contact_ids,
                    },
                )
            )

        email_info_result = email_info_task.result()
        account_info_result = account_info_task.result()

        contact_id_to_result_map: dict[UUID, ContactSearchResult] = {}

        for row in email_info_result:
            contact_id = row[0]
            display_name = row[1]
            email = row[2]
            is_contact_primary = row[3]
            is_email_delete = row[4]

            if contact_id not in contact_id_to_result_map:
                contact_id_to_result_map[contact_id] = ContactSearchResult(
                    contact_id=contact_id,
                    display_name=display_name,
                    emails=[],
                    account_associations=[],
                )

            if email and not is_email_delete:
                contact_id_to_result_map[contact_id].emails.append(
                    ContactEmailInfo(
                        email=email,
                        is_contact_primary=is_contact_primary,
                    )
                )

        for row in account_info_result:
            contact_id = row[0]
            account_id = row[1]
            account_display_name = row[2]
            is_primary = row[3]

            if contact_id not in contact_id_to_result_map:
                continue

            contact_id_to_result_map[contact_id].account_associations.append(
                ContactAccountAssociationInfo(
                    account_id=account_id,
                    display_name=account_display_name,
                    is_primary=is_primary,
                )
            )

        res = list(contact_id_to_result_map.values())
        if res:
            res.sort(key=lambda info: info.display_name or "")
            for res_info in res:
                res_info.emails.sort(key=lambda email_info: email_info.email or "")
                res_info.account_associations.sort(
                    key=lambda account_info: account_info.display_name or ""
                )
        return res

    async def find_contacts_by_address(
        self,
        *,
        organization_id: UUID,
        address_ids: list[UUID],
        exclude_deleted_or_archived: bool = False,
    ) -> list[Contact]:
        if not address_ids:
            return []

        return await self._find_by_column_values(
            Contact,
            organization_id=organization_id,
            address_id=address_ids,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
        )
