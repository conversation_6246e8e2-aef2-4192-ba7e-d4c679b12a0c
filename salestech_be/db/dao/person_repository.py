import uuid
from uuid import UUID

from sqlalchemy import bindparam, text

from salestech_be.common.error_code import <PERSON>rrorCode
from salestech_be.common.exception import ErrorDetails, ServiceError
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.dto.person_dto import PersonDto
from salestech_be.db.models.company import Company
from salestech_be.db.models.pdl_company import PDLCompany
from salestech_be.db.models.pdl_person import PD<PERSON>erson
from salestech_be.db.models.person import (
    Person,
    PersonUpdate,
    PhoneNumberDetail,
    ProspectingEnrichStatus,
    ProspectingSource,
)
from salestech_be.integrations.pdl.model import (
    PeopleDataLabsPerson,
    PeopleDataLabsPreviewPerson,
)
from salestech_be.util.time import zoned_utc_now


class PersonRepository(GenericRepository):
    async def list_by_ids(self, organization_id: UUID, ids: list[UUID]) -> list[Person]:
        if not ids:
            return []
        stmt = (
            text(
                """
            select * from person where id in :ids and organization_id=:organization_id;
            """
            )
            .bindparams(bindparam("ids", ids, expanding=True))
            .bindparams(organization_id=organization_id)
        )

        rows = await self.engine.all(stmt)
        return await Person.bulk_from_rows(rows=rows)

    async def list_by_ids_and_enrich_state(self, ids: list[UUID]) -> list[Person]:
        if not ids:
            return []
        stmt = text(
            """
            select * from person where id in :ids and phone_number_enrich_status = 'pending';
            """
        ).bindparams(bindparam("ids", ids, expanding=True))

        rows = await self.engine.all(stmt)
        return await Person.bulk_from_rows(rows=rows)

    async def update_by_id(
        self, person_id: UUID, organization_id: UUID, person_update: PersonUpdate
    ) -> Person:
        if updated_person := await self._update_unique_by_column_values(
            Person,
            column_value_to_query={"id": person_id, "organization_id": organization_id},
            column_to_update=person_update,
        ):
            return updated_person
        raise ServiceError(
            additional_error_details=ErrorDetails(
                code=ErrorCode.DB_UPDATE_ERROR,
                details=f"Person {person_id} failed to update.",
            )
        )

    async def update_db_person_and_pdl_person(
        self,
        people_data_labs_person_list: list[PeopleDataLabsPerson],
        user_id: UUID,
        organization_id: UUID,
        enrich_phone_numbers: bool,
    ) -> list[PersonDto]:
        results = []
        async with self.engine.begin():
            for people_data_labs_person in people_data_labs_person_list:
                db_company = None
                if (
                    people_data_labs_person.current_company
                    and people_data_labs_person.current_company.id
                ):
                    pdl_company = await self.upsert_unique_target_columns(
                        PDLCompany.from_people_data_labs_company(
                            company_id=uuid.uuid4(),
                            organization_id=organization_id,
                            people_data_labs_company=people_data_labs_person.current_company,
                        ),
                        on_conflict_target_columns=["organization_id", "ext_id"],
                        exclude_columns_from_update=["id", "company_id", "created_at"],
                    )
                    db_company = await self.upsert_unique_target_columns(
                        Company.from_people_data_labs_company(
                            people_data_labs_company=people_data_labs_person.current_company,
                            user_id=user_id,
                            organization_id=organization_id,
                        ).model_copy(update={"id": pdl_company.company_id}),
                        on_conflict_target_columns=["id"],
                        exclude_columns_from_update=[
                            "user_id",
                            "created_at",
                            "organization_id",
                        ],
                    )

                updated_pdl_person = await self._update_unique_by_column_values(
                    PDLPerson,
                    column_value_to_query={
                        "ext_id": people_data_labs_person.id,
                        "organization_id": organization_id,
                    },
                    column_to_update={
                        "data": people_data_labs_person.raw_data,
                        "updated_at": zoned_utc_now(),
                    },
                )
                if not updated_pdl_person:
                    raise ServiceError(
                        additional_error_details=ErrorDetails(
                            code=ErrorCode.DB_UPDATE_ERROR,
                            details=f"PDL person with ext_id {people_data_labs_person.id} and org_id {organization_id} failed to update.",
                        )
                    )
                updated_db_person = await self.update_by_id(
                    person_id=updated_pdl_person.person_id,
                    organization_id=organization_id,
                    person_update=PersonUpdate(
                        work_email=people_data_labs_person.work_email,
                        emails=people_data_labs_person.personal_emails or None,
                        company_id=db_company.id if db_company else None,
                        phone_numbers=(
                            [
                                PhoneNumberDetail(number=_number)
                                for _number in people_data_labs_person.phone_numbers
                            ]
                            if people_data_labs_person.phone_numbers
                            else None
                        ),
                        enrich_provider=ProspectingSource.PDL,
                        phone_number_enrich_status=(
                            ProspectingEnrichStatus.ENRICHED
                            if enrich_phone_numbers
                            and people_data_labs_person.phone_numbers
                            else ProspectingEnrichStatus.NOT_REQUESTED
                        ),
                        work_email_enrich_status=(
                            ProspectingEnrichStatus.ENRICHED
                            if people_data_labs_person.work_email
                            else ProspectingEnrichStatus.NOT_REQUESTED
                        ),
                        last_enriched_at=zoned_utc_now(),
                        first_name=people_data_labs_person.first_name,
                        last_name=people_data_labs_person.last_name,
                        location=people_data_labs_person.location_name,
                    ),
                )
                results.append(
                    PersonDto(db_person=updated_db_person, db_company=db_company)
                )
        return results

    async def list_by_linkedin_urls(
        self, organization_id: UUID, linkedin_urls: list[str]
    ) -> list[Person]:
        if not linkedin_urls:
            return []
        stmt = (
            text(
                """
                SELECT DISTINCT ON (linkedin_url) *
                FROM person
                WHERE organization_id = :organization_id
                AND linkedin_url IN :linkedin_urls
                ORDER BY linkedin_url, created_at ASC;
            """
            )
            .bindparams(bindparam("linkedin_urls", linkedin_urls, expanding=True))
            .bindparams(organization_id=organization_id)
        )
        rows = await self.engine.all(stmt)
        return await Person.bulk_from_rows(rows=rows)

    async def upsert_person_with_pdl_preview_person(
        self,
        user_id: UUID,
        organization_id: UUID,
        pdl_preview_person: PeopleDataLabsPreviewPerson,
    ) -> PersonDto:
        async with self.engine.begin():
            # insert pdl_person
            db_pdl_person: PDLPerson = await self.upsert_unique_target_columns(
                PDLPerson.from_pdl_preview_person(
                    organization_id=organization_id,
                    pdl_preview_person=pdl_preview_person,
                ),
                on_conflict_target_columns=["organization_id", "ext_id"],
                exclude_columns_from_update=["person_id", "created_at", "data"],
            )
            # insert person
            # there are some fields that are bool in preview search, and they need be updated in the enrichment process
            db_person = await self.upsert_unique_target_columns(
                Person(
                    id=db_pdl_person.person_id,
                    user_id=user_id,
                    organization_id=organization_id,
                    first_name=None,
                    last_name=None,
                    full_name=pdl_preview_person.full_name.title(),
                    linkedin_url=pdl_preview_person.linkedin_url,
                    current_company=(
                        pdl_preview_person.job_company_name.title()
                        if pdl_preview_person.job_company_name
                        else None
                    ),
                    current_company_website_url=pdl_preview_person.job_company_website,
                    company_id=None,
                    job_title=(
                        pdl_preview_person.job_title.title()
                        if pdl_preview_person.job_title
                        else None
                    ),
                    industry=(
                        pdl_preview_person.industry.title()
                        if pdl_preview_person.industry
                        else None
                    ),
                    work_email=None,
                    work_email_enrich_status=ProspectingEnrichStatus.NOT_REQUESTED,
                    email_status=None,
                    emails=None,
                    state=None,
                    country=None,
                    location=pdl_preview_person.location_name,
                    phone_numbers=None,
                    phone_number_enrich_status=ProspectingEnrichStatus.NOT_REQUESTED,
                    enrich_provider=ProspectingSource.PDL,
                    last_enriched_at=None,
                    created_at=zoned_utc_now(),
                    updated_at=zoned_utc_now(),
                    has_email=pdl_preview_person.work_email,
                    has_phone_numbers=pdl_preview_person.phone_numbers,
                ),
                on_conflict_target_columns=["id"],
                exclude_columns_from_update=[
                    "user_id",
                    "organization_id",
                    "phone_numbers",
                    "work_email",
                    "company_id",
                    "work_email_enrich_status",
                    "phone_number_enrich_status",
                    "last_enriched_at",
                    "created_at",
                    "first_name",
                    "last_name",
                    "company_id",
                    "location",
                ],
            )
        return PersonDto(
            db_person=db_person,
            db_company=None,
        )

    async def upsert_person_with_pdl_enriched_person(
        self,
        people_data_labs_person: PeopleDataLabsPerson,
        user_id: UUID,
        organization_id: UUID,
        enrich_phone_numbers: bool,
    ) -> PersonDto:
        async with self.engine.begin():
            db_company = None
            if (
                people_data_labs_person.current_company
                and people_data_labs_person.current_company.id
            ):
                pdl_company = await self.upsert_unique_target_columns(
                    PDLCompany.from_people_data_labs_company(
                        company_id=uuid.uuid4(),
                        organization_id=organization_id,
                        people_data_labs_company=people_data_labs_person.current_company,
                    ),
                    on_conflict_target_columns=["organization_id", "ext_id"],
                    exclude_columns_from_update=["id", "company_id", "created_at"],
                )
                db_company = await self.upsert_unique_target_columns(
                    Company.from_people_data_labs_company(
                        people_data_labs_company=people_data_labs_person.current_company,
                        user_id=user_id,
                        organization_id=organization_id,
                    ).model_copy(update={"id": pdl_company.company_id}),
                    on_conflict_target_columns=["id"],
                    exclude_columns_from_update=["user_id", "organization_id"],
                )

            db_pdl_person = await self.upsert_unique_target_columns(
                PDLPerson.from_people_data_labs_person(
                    organization_id=organization_id,
                    people_data_labs_person=people_data_labs_person,
                ),
                on_conflict_target_columns=["organization_id", "ext_id"],
                exclude_columns_from_update=["person_id", "created_at"],
            )

            db_person = await self.upsert_unique_target_columns(
                Person(
                    id=db_pdl_person.person_id,
                    user_id=user_id,
                    organization_id=organization_id,
                    first_name=people_data_labs_person.first_name,
                    last_name=people_data_labs_person.last_name,
                    full_name=people_data_labs_person.full_name,
                    linkedin_url=people_data_labs_person.linkedin_url,
                    current_company=people_data_labs_person.job_company_name,
                    current_company_website_url=people_data_labs_person.job_company_website,
                    company_id=db_company.id if db_company else None,
                    job_title=people_data_labs_person.job_title,
                    industry=people_data_labs_person.industry,
                    work_email=people_data_labs_person.work_email,
                    work_email_enrich_status=(
                        ProspectingEnrichStatus.ENRICHED
                        if people_data_labs_person.work_email
                        else ProspectingEnrichStatus.NOT_REQUESTED
                    ),
                    emails=people_data_labs_person.personal_emails or None,
                    state=None,
                    country=None,
                    location=people_data_labs_person.location_name,
                    phone_numbers=(
                        [
                            PhoneNumberDetail(number=_number)
                            for _number in people_data_labs_person.phone_numbers
                        ]
                        if people_data_labs_person.phone_numbers
                        else None
                    ),
                    phone_number_enrich_status=(
                        ProspectingEnrichStatus.ENRICHED
                        if enrich_phone_numbers
                        and people_data_labs_person.phone_numbers
                        else ProspectingEnrichStatus.NOT_REQUESTED
                    ),
                    enrich_provider=ProspectingSource.PDL,
                    last_enriched_at=zoned_utc_now(),
                    created_at=zoned_utc_now(),
                    updated_at=zoned_utc_now(),
                    has_email=bool(people_data_labs_person.work_email),
                    has_phone_numbers=bool(people_data_labs_person.phone_numbers),
                ),
                on_conflict_target_columns=["id"],
                exclude_columns_from_update=[
                    "updated_at",
                    "user_id",
                    "organization_id",
                    "phone_numbers",
                    "work_email",
                    "company_id",
                    "work_email_enrich_status",
                    "phone_number_enrich_status",
                ],
            )

        return PersonDto(
            db_person=db_person,
            db_company=db_company,
        )
