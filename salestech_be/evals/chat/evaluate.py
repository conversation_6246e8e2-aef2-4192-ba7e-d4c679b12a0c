import asyncio
import datetime
import json
import time
from pathlib import Path
from uuid import UUID

import aiofiles
import pytz.reference
import uvloop
from pydantic_ai.agent import AgentRunResult
from pydantic_ai.messages import ModelMessage, ModelRequest, ModelResponse

from salestech_be.common.langfuse_otel import langfuse_otel_trace
from salestech_be.common.ree_llm import LLMTraceMetadata
from salestech_be.core.account.service.account_service import get_account_service
from salestech_be.core.chat.agent_deps import ChatAgentDeps
from salestech_be.core.chat.chat_agent import (
    chat_fallback_models,
    render_system_prompt,
    select_variant,
)
from salestech_be.core.contact.service.contact_service import get_contact_service
from salestech_be.core.meeting.service.meeting_transcript_agent_service import (
    meeting_transcript_agent_service_from_engine,
)
from salestech_be.core.meeting.utils import format_duration_display
from salestech_be.core.organization.service.organization_service import (
    get_organization_service_general,
)
from salestech_be.core.transcript.transcript_service import (
    transcript_service_from_engine,
)
from salestech_be.core.user.service.user_service import (
    get_user_service_general,
)
from salestech_be.db.dao.task_repository import TaskRepository
from salestech_be.evals.chat.dataset import eval_dataset
from salestech_be.evals.chat.types import ChatHistoryMessage, ChatRunResult, EvalRun
from salestech_be.ree_logging import get_logger
from salestech_be.search.es.search.client_utils import get_es_search_client
from salestech_be.search.search.search_service import get_search_service
from salestech_be.temporal.database import get_or_init_db_engine
from salestech_be.web.api.chat.schema import ViewContext

logger = get_logger(__name__)
db_engine = asyncio.run(get_or_init_db_engine())
search_service = get_search_service(search_client=get_es_search_client())
transcript_service = transcript_service_from_engine(engine=db_engine)
user_service = get_user_service_general(db_engine=db_engine)
organization_service = get_organization_service_general(db_engine=db_engine)
multi_meeting_service = meeting_transcript_agent_service_from_engine(engine=db_engine)
contact_service = get_contact_service(db_engine=db_engine)
account_service = get_account_service(db_engine=db_engine)
task_repository = TaskRepository(engine=db_engine)

VARIANT_NAME = "claude-sonnet-4"


async def main() -> None:
    chat_agent = select_variant(VARIANT_NAME)
    results = []
    run_start = time.perf_counter()
    for conversation in eval_dataset:
        conversation_usage = []
        result = None
        chat_history = []

        logger.info(f"Starting conversation: {conversation.queries}")
        for q in conversation.queries:
            try:
                deps = await create_deps(
                    conversation.user_id,
                    conversation.organization_id,
                    conversation.meeting_id,
                )
                logger.info(f"Running query: {q.query}")
                if result is not None:
                    history = await build_followup_history(result.all_messages())
                else:
                    history = [render_system_prompt(ctx=deps)]

                trace_metadata = LLMTraceMetadata(
                    trace_name="chat-agent-evals",
                    user_id=conversation.user_id,
                    custom_fields={
                        "organization_id": str(conversation.organization_id),
                        "organization_name": deps.organization.display_name,
                        "user_display_name": deps.user.display_name or deps.user.email,
                    },
                )
                with langfuse_otel_trace(trace_metadata=trace_metadata) as parent_span:
                    parent_span.set_attribute("input.value", q.query)
                    result = await chat_agent.run(
                        user_prompt=q.query,
                        model=chat_fallback_models(VARIANT_NAME),
                        deps=deps,
                        message_history=history,
                    )
                    parent_span.set_attribute("output.value", result.output)
                    conversation_usage.append(result.usage())
                    chat_history.extend(await to_user_chat_history(result))

            except Exception as e:
                logger.exception(f"Error processing query: {q.query}")
                chat_history.append({"role": "user", "content": q.query})
                chat_history.append(
                    {
                        "role": "assistant",
                        "content": f"An error occurred: {e!s}",
                    }
                )

        run_result = ChatRunResult(
            queries=conversation.queries,
            chat_history=chat_history,
            usages=conversation_usage,
        )
        results.append(run_result)

    output = EvalRun(
        variant=VARIANT_NAME,
        timestamp=datetime.datetime.now(tz=pytz.reference.Pacific),
        results=results,
    )

    logger.info(
        f"Finished eval simulation run over {len(results)} conversations in {format_duration_display(int(time.perf_counter() - run_start))}"
    )
    output_dir = result_output_dir()
    logger.info(f"Writing output to {output_dir}")
    output_dir.mkdir(parents=True, exist_ok=True)
    async with aiofiles.open(output_dir / "results.json", "w") as f:
        await f.write(json.dumps(output.model_dump(mode="json"), indent=2))


def get_output_path(variant: str | None = None) -> Path:
    if variant is None:
        variant = VARIANT_NAME
    output_dir = (
        Path(__file__).parent.parent.parent.parent
        / "local-data"
        / "chat"
        / str(datetime.date.today())  # noqa: DTZ011
        / variant
    )
    output_dir.mkdir(parents=True, exist_ok=True)
    return output_dir


def result_output_dir() -> Path:
    return (
        Path(__file__).parent.parent.parent.parent
        / "local-data"
        / "chat"
        / str(datetime.datetime.now(tz=pytz.reference.Pacific).date())
        / VARIANT_NAME
    )


async def build_followup_history(messages: list[ModelMessage]) -> list[ModelMessage]:
    followup_history: list[ModelMessage] = []
    for message in messages:
        if isinstance(message, ModelRequest):
            if all(
                p.part_kind not in {"tool-return", "retry-prompt"}
                for p in message.parts
            ):
                followup_history.append(message)
            else:
                logger.debug("Dropping tool-return message")
        elif isinstance(message, ModelResponse):
            if all(p.part_kind != "tool-call" for p in message.parts):
                followup_history.append(message)
            else:
                parts = [p for p in message.parts if p.part_kind != "tool-call"]
                if parts:
                    m = ModelResponse(
                        parts=[p for p in message.parts if p.part_kind != "tool-call"],
                        model_name=message.model_name,
                        timestamp=message.timestamp,
                    )
                    followup_history.append(m)
    return followup_history


async def to_user_chat_history(result: AgentRunResult) -> list[ChatHistoryMessage]:
    pai_messages = result.new_messages()
    chat_history: list[ChatHistoryMessage] = []
    for message in pai_messages:
        if isinstance(message, ModelRequest):
            for part in message.parts:
                if part.part_kind == "user-prompt" and isinstance(part.content, str):
                    chat_history.append({"role": "user", "content": part.content})
                else:
                    logger.debug(f"Skipping part: {part.part_kind}")
        elif isinstance(message, ModelResponse):
            for part in message.parts:  # type: ignore[assignment]
                if part.part_kind == "text":  # type: ignore[comparison-overlap]
                    chat_history.append({"role": "assistant", "content": part.content})
    return chat_history


async def create_deps(
    user_id: UUID, organization_id: UUID, meeting_id: UUID | None = None
) -> ChatAgentDeps:
    user = await user_service.get_user_v2(
        user_id=user_id, organization_id=organization_id
    )
    org = await organization_service.get_organization_by_id(
        organization_id=organization_id
    )
    if org is None:
        raise ValueError(f"Organization {organization_id} not found")

    org_users = await user_service.list_all_users_in_organization(
        organization_id=organization_id,
        active_users_only=True,
    )

    return ChatAgentDeps(
        user_id=user_id,
        organization_id=organization_id,
        search_service=search_service,
        task_repository=task_repository,
        transcript_service=transcript_service,
        multi_meeting_service=multi_meeting_service,
        contact_service=contact_service,
        account_service=account_service,
        view_contexts=[ViewContext(type="meeting", id=meeting_id)],
        user=user,
        organization=org,
        org_users=org_users,
    )


if __name__ == "__main__":
    uvloop.run(main())
