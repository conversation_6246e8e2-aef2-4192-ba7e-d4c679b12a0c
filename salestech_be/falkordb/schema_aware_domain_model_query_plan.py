"""
Schema-aware domain model query plan module.
Provides enhanced query plan support for domain model projections using schema information.
"""

import re

from salestech_be.common.type.metadata.schema import (
    FieldDescriptor,
    ObjectDescriptor,
    OrganizationSchemaDescriptor,
)
from salestech_be.falkordb.domain_model_query_plan import DomainModelQueryPlan
from salestech_be.falkordb.query_analyzer import QueryDependencyGraph
from salestech_be.falkordb.query_plan import (
    FilterExpressionBuilder,
    FilterOperation,
    MatchOperation,
    OrderByOperation,
    PaginationOperation,
    QueryNode,
    QueryRelationship,
    RelationshipDirection,
    RelationshipMatchOperation,
    ReturnOperation,
    WithOperation,
    process_composite_structure,
)
from salestech_be.falkordb.schema_registry import (
    FieldTypeInfo,
    SchemaRegistry,
    SchemaValidationError,
)
from salestech_be.ree_logging import get_logger


def sanitize_property_reference(prop_name: str) -> str:
    """Sanitizes a property name for use in Cypher property access (e.g., node.`prop-name`)."""
    # Check if sanitization is needed (starts with digit or contains special chars except _)
    if prop_name and (prop_name[0].isdigit() or re.search(r"[^a-zA-Z0-9_]", prop_name)):  # noqa: SIM102
        # Check if already sanitized (naive check)
        if not (prop_name.startswith("`") and prop_name.endswith("`")):
            return f"`{prop_name}`"
    return prop_name  # Return original if no sanitization needed


logger = get_logger(__name__)


class CircularReferenceError(Exception):
    """Exception raised when a circular reference is detected in the schema."""


class SchemaAwareDomainModelQueryPlan(DomainModelQueryPlan):
    """
    Enhanced query plan for domain model construction using schema information.

    This extends the base DomainModelQueryPlan to add schema validation
    and improved handling of complex domain models with nested structures.
    """

    def __init__(
        self,
        object_descriptor: ObjectDescriptor | None = None,
        schema: OrganizationSchemaDescriptor | None = None,
        registry: SchemaRegistry | None = None,
    ) -> None:
        """
        Initialize the schema-aware domain model query plan.

        Args:
            object_descriptor: The descriptor for the root object type
            schema: The organization schema
            registry: Optional pre-configured schema registry
        """
        super().__init__()
        self.object_descriptor = object_descriptor
        self.schema = schema

        # Use provided registry or create a new one with the schema
        self.registry = registry if registry else SchemaRegistry(schema)

        self.entity_projection_vars: dict[
            str, str
        ] = {}  # Maps node alias to projection variable
        self.processed_nodes: set[str] = set()  # Track processed nodes
        self.node_dependencies: dict[str, set[str]] = {}  # Track node dependencies

    def _build_field_projection(
        self,
        node_alias: str,
        field: FieldDescriptor,
        field_info: FieldTypeInfo,
    ) -> str:
        """
        Build projection syntax for a specific field based on its type.
        This method now primarily handles primitive fields and primitive collections
        stored directly as node properties. Relationships and complex nested
        structures are handled by the calling projection methods.

        Args:
            node_alias: The alias of the node in the query.
            field: The field descriptor for this field.
            field_info: Type information for the field.

        Returns:
            String containing the Cypher projection syntax for this field
            (e.g., "field_name: node_alias.field_name"), or an empty string
            if the field type is handled elsewhere (like relationships).
        """
        if not self.registry:
            logger.error("Schema registry not available in _build_field_projection")
            return ""  # Or raise error

        simple_field_name = self.registry.extract_field_name(field.field_identifier)
        sanitized_prop_access = sanitize_property_reference(simple_field_name)
        sanitized_field_name = sanitize_property_reference(simple_field_name)

        # Primitive fields (stored as direct properties)
        if field_info.is_primitive:
            return f"{sanitized_field_name}: {node_alias}.{sanitized_prop_access}"

        # Primitive collection fields (assuming stored as list properties on the node)
        if field_info.is_collection and not field_info.nested_descriptor:
            # The graph stores primitive lists directly as properties
            return f"{sanitized_field_name}: {node_alias}.{sanitized_prop_access}"

        # Nested objects and object collections are not handled here.
        # They are constructed based on relationships in _build_internal_node_projection.
        # Returning an empty string signifies that the calling method should handle it.
        if field_info.is_nested or (
            field_info.is_collection and field_info.nested_descriptor
        ):
            logger.debug(
                f"Field '{simple_field_name}' on node '{node_alias}' is complex; "
                "projection handled by relationship logic in calling method."
            )
            return ""  # Handled by relationship logic

        # Default case for potentially unhandled types
        logger.warning(
            f"Unhandled field type for '{simple_field_name}' on node '{node_alias}'. "
            "Returning NULL projection."
        )
        # Return a placeholder or skip, depending on desired strictness
        return f"{simple_field_name}: NULL"

    def _build_nested_projection(
        self,
        node_alias: str,
        descriptor: ObjectDescriptor,
        visited_objects: set[str] | None = None,
        object_path: list[str] | None = None,
    ) -> str:
        """
        Build a projection for a nested object structure.

        Args:
            node_alias: The alias of the node in the query
            descriptor: The object descriptor for this nested object
            visited_objects: Set of object identifier strings that have been visited in this path,
                             used for detecting circular references
            object_path: List of object identifiers in the current path, for tracking the chain
                         of references that led to a circular reference

        Returns:
            String containing the Cypher projection for this nested object

        Raises:
            SchemaValidationError: When schema is missing for nested projection
        """
        if not self.schema:
            raise SchemaValidationError("Schema is required for nested projection")

        if visited_objects is None:
            visited_objects = set()
        if object_path is None:
            object_path = []

        current_object_id_str = str(descriptor.object_identifier)
        if current_object_id_str in visited_objects:
            raise CircularReferenceError(
                f"Circular reference detected: {current_object_id_str} in {object_path}"
            )

        visited_objects.add(current_object_id_str)
        object_path.append(self.registry.get_object_name(descriptor))

        field_projections = []
        has_any_primitive_or_list_of_primitive = False

        for field in descriptor.fields:
            # Ensure schema is passed correctly if analyze_field_type needs it
            # However, the corrected analyze_field_type should take (parent_descriptor, field_descriptor)
            field_info = self.registry.analyze_field_type(descriptor, field)

            # Build the field projection based on its type
            proj = self._build_field_projection(node_alias, field, field_info)
            field_projections.append(proj)

            # Check if the field is primitive or a list of primitives
            if field_info.is_primitive or (
                field_info.is_collection and not field_info.nested_descriptor
            ):
                has_any_primitive_or_list_of_primitive = True

        # Combine all field projections into a single nested projection
        return (
            "{" + ", ".join(field_projections) + "}"
            if has_any_primitive_or_list_of_primitive
            else ""
        )

    def build_bottom_up_projection(self) -> None:
        """
        Build domain model using bottom-up projection with intermediate variables.

        This method analyzes node dependencies and builds the projection
        from leaf nodes up to the root node.
        """
        if not self.schema or not self.object_descriptor:
            raise SchemaValidationError("Schema and object descriptor are required")

        # Determine node processing order (bottom-up)
        processing_order = self._get_node_processing_order()

        # Initialize map of node aliases to projection variables
        for node_alias, node in self.nodes.items():
            entity_type = node.label
            # Sanitize label for use in variable name:
            # 1. Replace hyphens with underscores.
            # 2. Prepend an underscore to guarantee validity (handles labels starting with digits or other potential issues).
            sanitized_label_for_var = "_" + entity_type.lower().replace("-", "_")
            var_name = f"{sanitized_label_for_var}_entity_{node_alias[1:]}"
            self.entity_projection_vars[node_alias] = var_name

        # Track nodes that need to be carried forward in WITH clauses
        active_nodes = set(self.nodes.keys())

        # Start building projections from leaf nodes up
        for node_alias in processing_order:
            # Skip already processed nodes
            if node_alias in self.processed_nodes:
                continue

            # Find outgoing relationships from this node
            outgoing_rels = self._get_outgoing_relationships(node_alias)

            # Determine if this node represents a nested field or a regular entity
            self._is_nested_field_node(node_alias)

            # If this is a leaf node (no outgoing rels), create initial projection
            if not outgoing_rels:
                self._build_leaf_node_projection(node_alias, active_nodes)
            else:
                # Create projection that includes references to other entities
                self._build_internal_node_projection(
                    node_alias, outgoing_rels, active_nodes
                )

            # Mark as processed
            self.processed_nodes.add(node_alias)

            # For the root node, add the final simplifying WITH and RETURN operations
            if node_alias == "n0":
                # After the projection for n0 is built (which adds its own WITH clause),
                # add a final WITH that *only* carries forward the final projection variable.
                final_entity_var = self.entity_projection_vars[node_alias]
                self.add_operation(WithOperation([final_entity_var], {}))
                # Add the RETURN operation
                self.add_operation(ReturnOperation(final_entity_var))

    def _is_nested_field_node(self, node_alias: str) -> bool:
        """
        Determine if a node represents a nested field rather than a regular entity.

        This helps distinguish between entity relationships and nested field structures.

        Args:
            node_alias: The node alias to check

        Returns:
            True if the node represents a nested field, False otherwise
        """
        # If this node is the target of a relationship that represents a nested field,
        # then it's a nested field node
        for rel in self.relationships:
            if rel.target.var_name == node_alias:
                # Attempt to determine if this is a nested field relationship
                # by checking if the relationship ID matches a field name
                if not self.object_descriptor or not self.schema:
                    continue

                source_label = rel.source.label
                source_descriptor = self.registry.get_object_descriptor(source_label)
                if not source_descriptor:
                    continue

                # Convert source descriptor to its name for dictionary lookup
                self.registry.get_object_name(source_descriptor)

                # Check if relationship ID is a field name in the source descriptor
                # using the object name and relationship type as keys
                if self.registry.is_nested_structure(source_descriptor, rel.type_):
                    return True

                # Check if any field in the source descriptor matches the relationship ID
                for field in source_descriptor.fields:
                    if str(field.field_identifier) == rel.type_:
                        field_info = self.registry.analyze_field_type(
                            source_descriptor, field
                        )
                        return field_info.is_nested or field_info.is_collection

        return False

    def _build_node_dependency_graph(self) -> dict[str, set[str]]:
        """
        Build a graph of node dependencies.

        Returns:
            Dictionary mapping node aliases to their dependencies
        """
        dependencies: dict[str, set[str]] = {}

        # Initialize all nodes
        for node_alias in self.nodes:
            dependencies[node_alias] = set()

        # Add dependencies based on relationships
        for rel in self.relationships:
            source = rel.source.var_name
            target = rel.target.var_name

            # Target depends on source (for processing order)
            if target in dependencies:
                dependencies[target].add(source)

        self.node_dependencies = dependencies
        return dependencies

    def _get_node_processing_order(self) -> list[str]:
        """
        Get ordering of nodes for bottom-up processing.

        Returns:
            List of node aliases in bottom-up processing order
        """
        dependency_graph = self._build_node_dependency_graph()

        # Find leaf nodes (no nodes depend on them)
        leaf_nodes = []
        dependent_nodes = set()
        for deps in dependency_graph.values():
            dependent_nodes.update(deps)

        # Leaf nodes are those that no other nodes depend on
        for node in dependency_graph:
            if node not in dependent_nodes:
                leaf_nodes.append(node)

        # Build processing order starting from nodes with deepest dependencies
        processing_order = []
        visited = set()

        def visit(node: str) -> None:
            if node in visited:
                return

            # Visit dependencies first - SORTED for determinism
            for dep in sorted(dependency_graph[node]):
                visit(dep)

            visited.add(node)
            processing_order.append(node)

        # Visit all nodes - SORTED for determinism
        for node in sorted(dependency_graph.keys()):
            if node not in visited:
                visit(node)

        # We get a top-down order, but we want bottom-up, so reverse
        return list(reversed(processing_order))

    def _get_outgoing_relationships(self, node_alias: str) -> list[QueryRelationship]:
        """
        Get all outgoing relationships from a node.

        Args:
            node_alias: The node alias to check

        Returns:
            List of relationships where the node is the source
        """
        outgoing_rels = []
        for rel in self.relationships:
            if rel.source.var_name == node_alias:
                outgoing_rels.append(rel)
        return outgoing_rels

    def _build_leaf_node_projection(
        self, node_alias: str, active_nodes: set[str]
    ) -> None:
        """
        Build projection for a leaf node.

        Args:
            node_alias: The node alias to build projection for
            active_nodes: Set of node aliases that need to be carried forward
        """
        if not self.schema or not self.registry:
            raise SchemaValidationError(
                "Schema and registry are required for leaf node projection"
            )

        projection_var = self.entity_projection_vars[node_alias]

        # Get object descriptor for this node
        node_descriptor = self.registry.get_object_descriptor(
            self.nodes[node_alias].label
        )
        if not node_descriptor:
            raise SchemaValidationError(f"No descriptor found for node {node_alias}")

        # Build projections based on schema fields
        field_projections = []
        for field in node_descriptor.fields:
            field_info = self.registry.analyze_field_type(node_descriptor, field)
            # 1. Handle Primitive / Primitive Collection fields
            if field_info.is_primitive or (
                field_info.is_collection and not field_info.nested_descriptor
            ):
                proj = self._build_field_projection(node_alias, field, field_info)
                if proj:
                    field_projections.append(proj)
            # 2. Handle Complex schema fields when node is a query leaf:
            # If the schema defines a complex field (NestedObject/List), but this node
            # is a leaf in the *current query* (no outgoing relationships requested from it),
            # project an empty list `[]`. This ensures the output map has the key defined
            # by the schema, without performing an unrequested/expensive fetch for nested data.
            # This prioritizes query intent and performance over auto-fetching all schema possibilities.
            elif field_info.is_nested or (
                field_info.is_collection and field_info.nested_descriptor
            ):
                simple_field_name = self.registry.extract_field_name(
                    field.field_identifier
                )
                logger.debug(
                    f"Schema field '{simple_field_name}' is complex, but node '{node_alias}' is a leaf in this query. Projecting empty list."
                )
                # Project as empty list for collections or null for single objects (or always list for consistency)
                sanitized_field_key = sanitize_property_reference(simple_field_name)
                field_projections.append(
                    f"{sanitized_field_key}: []"
                )  # Consistent empty list

        # Sort field projections alphabetically by key
        field_projections.sort(key=lambda proj_str: proj_str.split(":", 1)[0].strip())

        # Combine projections, ensuring alphabetical order by key
        if not field_projections:
            logger.warning(
                f"No primitive fields found for leaf node {node_alias} projection based on schema. "
                f"Falling back to projecting all properties {node_alias}{{.*}}."
            )
            map_projection = f"{node_alias} {{.*}}"
        else:
            map_projection = "{\n"
            for i, proj in enumerate(field_projections):
                if i == len(field_projections) - 1:
                    map_projection += f"    {proj}\n"  # No comma for last item
                else:
                    map_projection += f"    {proj},\n"
            map_projection += "}"

        # Create final projection definition with NULL check
        projection_definition = f"CASE WHEN {node_alias} IS NULL THEN NULL ELSE {map_projection} END AS {projection_var}"

        # Determine all variables to carry forward:
        # 1. The current projection definition (CASE... AS new_var)
        # 2. All original node aliases (sorted)
        # 3. All projection variables from *previously* processed nodes (sorted)
        all_original_nodes = sorted(self.nodes.keys())
        # Get projection vars for nodes processed *before* this one
        previous_projection_vars = sorted(
            [
                self.entity_projection_vars[p]
                for p in self.processed_nodes  # processed_nodes contains nodes processed in previous steps
            ]
        )
        carry_forward_vars = [
            projection_definition,
            *all_original_nodes,
            *previous_projection_vars,
        ]

        # Remove duplicates (preserves order)
        carry_forward_vars = list(dict.fromkeys(carry_forward_vars))

        # Add WITH clause
        self.add_operation(WithOperation(carry_forward_vars, {}))

    def _build_internal_node_projection(  # noqa: C901, PLR0912, PLR0915
        self,
        node_alias: str,
        outgoing_rels: list[QueryRelationship],
        active_nodes: set[str],
    ) -> None:
        """
        Build projection for an internal node using its schema descriptor.
        Iterates through schema fields, projecting primitives directly and using
        query plan relationships for nested objects/collections.

        Args:
            node_alias: The node alias to build projection for
            outgoing_rels: List of outgoing relationships from this node in the query plan
            active_nodes: Set of node aliases that need to be carried forward
        """
        if not self.schema or not self.registry:
            raise SchemaValidationError(
                "Schema and registry are required for internal node projection"
            )

        projection_var = self.entity_projection_vars[node_alias]

        # Get object descriptor for this node
        node_descriptor = self.registry.get_object_descriptor(
            self.nodes[node_alias].label
        )
        if not node_descriptor:
            raise SchemaValidationError(f"No descriptor found for node {node_alias}")

        # Map relationship types in the query plan originating from this node
        query_rels_map: dict[str, tuple[str, str]] = {}
        for rel in self.relationships:
            if rel.source.var_name == node_alias:
                target_alias = rel.target.var_name
                if target_alias in self.entity_projection_vars:
                    target_var = self.entity_projection_vars[target_alias]
                    query_rels_map[rel.type_] = (target_alias, target_var)
                else:
                    logger.warning(
                        f"Projection variable not found for target '{target_alias}' of relationship '{rel.type_}' from '{node_alias}'"
                    )

        # Build projections based on schema fields
        combined_projections = []
        processed_rel_fields = set()

        for field in node_descriptor.fields:
            field_info = self.registry.analyze_field_type(node_descriptor, field)
            simple_field_name = self.registry.extract_field_name(field.field_identifier)
            sanitized_field_key = sanitize_property_reference(simple_field_name)

            # 1. Handle Primitive / Primitive Collection fields
            if field_info.is_primitive or (
                field_info.is_collection and not field_info.nested_descriptor
            ):
                proj = self._build_field_projection(node_alias, field, field_info)
                if proj:
                    combined_projections.append(proj)

            # 2. Handle Complex fields (Nested Object / Object Collection)
            elif field_info.is_nested or (
                field_info.is_collection and field_info.nested_descriptor
            ):
                expected_rel_type = simple_field_name
                processed_rel_fields.add(expected_rel_type)

                if expected_rel_type in query_rels_map:
                    target_alias, target_var = query_rels_map[expected_rel_type]
                    proj = f"{sanitized_field_key}: COLLECT(CASE WHEN {target_alias} IS NULL THEN [] ELSE {target_var} END)"
                    combined_projections.append(proj)
                else:
                    logger.debug(
                        f"Schema field '{simple_field_name}' (rel: '{expected_rel_type}') not found in query relationships for node '{node_alias}'. Projecting empty list."
                    )
                    combined_projections.append(f"{sanitized_field_key}: []")

        # 3. Handle explicit domain relationships from the query that *don't* correspond to a schema field
        for rel_type, (target_alias, target_var) in query_rels_map.items():
            if rel_type not in processed_rel_fields:
                sanitized_rel_key = sanitize_property_reference(rel_type)
                proj = f"{sanitized_rel_key}: COLLECT(CASE WHEN {target_alias} IS NULL THEN [] ELSE {target_var} END)"
                combined_projections.append(proj)

        # Ensure deterministic order of keys in the projection map by sorting based on the key name
        # Sort the combined_projections list alphabetically based on the key part of each string
        combined_projections.sort(
            key=lambda proj_str: proj_str.split(":", 1)[0].strip()
        )

        # Format the map projection with line breaks
        map_projection = "{\n"
        for i, proj in enumerate(combined_projections):
            if i == len(combined_projections) - 1:
                map_projection += f"    {proj}\n"  # No comma for last item
            else:
                map_projection += f"    {proj},\n"
        map_projection += "}"

        # Create final projection definition with NULL check
        projection_definition = f"CASE WHEN {node_alias} IS NULL THEN NULL ELSE {map_projection} END AS {projection_var}"

        # Determine all variables to carry forward:
        # 1. The current projection definition (CASE... AS new_var)
        # 2. All original node aliases (sorted)
        # 3. All projection variables from *previously* processed nodes (sorted)
        all_original_nodes = sorted(self.nodes.keys())
        # Get projection vars for nodes processed *before* this one
        previous_projection_vars = sorted(
            [
                self.entity_projection_vars[p]
                for p in self.processed_nodes  # processed_nodes contains nodes processed in previous steps
            ]
        )
        carry_forward_vars = [
            projection_definition,
            *all_original_nodes,
            *previous_projection_vars,
        ]

        # Remove duplicates (preserves order)
        carry_forward_vars = list(dict.fromkeys(carry_forward_vars))

        # Add WITH clause
        self.add_operation(WithOperation(carry_forward_vars, {}))


def create_schema_aware_domain_model_query_plan(
    graph: QueryDependencyGraph,
    entity_type: str,
    schema: OrganizationSchemaDescriptor,
    offset: int = 0,
    limit: int = 20,
) -> SchemaAwareDomainModelQueryPlan:
    """
    Create a SchemaAwareDomainModelQueryPlan from a QueryDependencyGraph.

    Args:
        graph: The dependency graph
        entity_type: The primary object type
        schema: The organization schema (required)
        limit: The maximum number of results to return
        offset: The number of results to skip

    Returns:
        A SchemaAwareDomainModelQueryPlan

    Raises:
        SchemaValidationError: If any required schema elements are not found
    """
    # Create and initialize schema registry
    registry = SchemaRegistry(schema)

    # Get object descriptor - required
    object_descriptor = registry.get_object_descriptor(entity_type)
    if not object_descriptor:
        raise SchemaValidationError(f"Object '{entity_type}' not found in schema")

    # Create plan
    plan = SchemaAwareDomainModelQueryPlan(object_descriptor, schema, registry)

    # Copy nodes from the dependency graph
    for alias, node_obj in graph.nodes.items():
        entity_type_name = node_obj.label
        node = QueryNode(entity_type_name, alias)
        plan.add_node(node)

    # Build relationships from the dependency graph
    for source_alias, rel_set in graph.entity_relationships.items():
        source_node = plan.nodes[source_alias]

        for rel_id, target_alias in rel_set:
            target_node = plan.nodes[target_alias]

            # Validate relationship against schema
            source_descriptor = registry.get_object_descriptor(source_node.label)
            if source_descriptor:
                registry.validate_relationship(source_descriptor, rel_id)

            relationship = QueryRelationship(
                source=source_node,
                type_=rel_id,
                target=target_node,
                direction=RelationshipDirection.OUTGOING,
            )
            plan.add_relationship(relationship)

    # Add operations for matching nodes and relationships
    _add_match_operations(plan, graph)

    # Build projections
    plan.build_bottom_up_projection()

    # Add pagination operation
    plan.add_operation(PaginationOperation(offset, limit))

    return plan


def _validate_relationships(
    plan: SchemaAwareDomainModelQueryPlan, registry: SchemaRegistry
) -> None:
    """
    Validate all relationships exist in schema.

    Args:
        plan: The query plan
        registry: The schema registry

    Raises:
        SchemaValidationError: If any relationship is not found in the schema
    """
    if not plan.schema:
        return

    for rel in plan.relationships:
        source_node = rel.source
        source_type = source_node.label
        rel_id = rel.type_

        # Get source object descriptor
        source_descriptor = registry.get_object_descriptor(source_type)
        if not source_descriptor:
            raise SchemaValidationError(
                f"Source object '{source_type}' not found in schema"
            )

        # Validate relationship exists
        registry.validate_relationship(source_descriptor, rel_id)


def _add_match_operations(  # noqa: C901, PLR0912, PLR0915 # Keep existing noqa
    plan: SchemaAwareDomainModelQueryPlan, graph: QueryDependencyGraph
) -> None:
    """
    Add MATCH operations for the root node and relationships, combining relationship
    and target node matching for efficiency. Also adds necessary WHERE clauses.

    Args:
        plan: The query plan
        graph: The dependency graph
    """
    # 1. Add MATCH for the root node (n0)
    root_node_alias = "n0"
    if root_node_alias in plan.nodes:
        match_op = MatchOperation(plan.nodes[root_node_alias], optional=False)
        plan.add_operation(match_op)
        matched_aliases = {root_node_alias}
    else:
        # This case should ideally not happen if the graph is well-formed
        logger.error("Root node 'n0' not found in plan nodes.")
        matched_aliases = set()
        # Assuming graph is valid and n0 exists based on typical usage.
        # If n0 could be missing, error handling might be needed.

    # 2. Add OPTIONAL MATCH for relationships, including target node labels directly
    # Sort relationships for deterministic query generation
    sorted_relationships = sorted(
        plan.relationships,
        key=lambda r: (r.type_, r.source.var_name, r.target.var_name),
    )
    for rel in sorted_relationships:
        # IMPORTANT ASSUMPTION: RelationshipMatchOperation's `to_cypher` method
        # needs to render the pattern `(source)-[:type]->(target:TargetLabel)`.
        # If it only renders `(source)-[:type]->(target)`, this change alone
        # will not produce the desired Cypher structure, and `query_plan.py`
        # would also need modification. The tests should verify the output.
        rel_match_op = RelationshipMatchOperation(rel, optional=True)
        plan.add_operation(rel_match_op)
        # Track all aliases introduced or referenced by the matches
        matched_aliases.add(rel.source.var_name)
        matched_aliases.add(rel.target.var_name)

    # --- Add the crucial first WITH clause ---
    # This carries forward all matched node aliases before any filtering.
    # Ensure consistent order
    carry_forward_aliases = sorted(matched_aliases)
    plan.add_with_clause(carry_forward_aliases)

    # Process filters
    filter_builder = FilterExpressionBuilder(plan)
    filters_added = False

    # Add standalone filter operations from graph
    # Ensure entity_alias exists in our matched_aliases before applying filter
    if hasattr(graph, "standalone_filters"):
        for entity_alias, filters in graph.standalone_filters.items():
            if (
                entity_alias not in carry_forward_aliases
            ):  # Check against aliases actually available
                logger.warning(
                    f"Filter specified for entity '{entity_alias}', but it's not matched in the base query. Skipping filter."
                )
                continue
            for filter_condition in filters:
                expr, params, nodes = filter_builder.process_value_filter(
                    filter_condition, entity_alias
                )
                # Ensure all referenced nodes in the filter are available
                if all(node in carry_forward_aliases for node in nodes):
                    filter_op = FilterOperation(
                        expression=expr, params=params, referenced_nodes=nodes
                    )
                    plan.add_operation(filter_op)
                    plan.params.update(params)
                    filters_added = True
                else:
                    logger.warning(
                        f"Skipping filter '{expr}' because it references unavailable nodes: {set(nodes) - set(carry_forward_aliases)}"
                    )

    # Process composite structures if present
    if hasattr(graph, "composite_structures") and graph.composite_structures:
        for structure in graph.composite_structures:
            expr, params, nodes = process_composite_structure(
                structure, plan, filter_builder
            )
            if expr:
                # Ensure all referenced nodes in the filter are available
                if all(node in carry_forward_aliases for node in nodes):
                    filter_op = FilterOperation(
                        expression=expr, params=params, referenced_nodes=nodes
                    )
                    plan.add_operation(filter_op)
                    plan.params.update(params)
                    filters_added = True
                else:
                    logger.warning(
                        f"Skipping composite filter '{expr}' because it references unavailable nodes: {set(nodes) - set(carry_forward_aliases)}"
                    )

    # Add WITH clause *after* filters *if* filters were added
    # to separate WHERE from subsequent clauses.
    if filters_added:
        plan.add_with_clause(carry_forward_aliases)  # Carry forward the same aliases

    # Add sorting operations
    sort_expressions = []
    # Use a more specific flag name to avoid potential scope issues if 'has_sorting' was used differently before
    is_sorting_present = False
    if hasattr(graph, "sort_conditions"):  # Check if attribute exists
        for entity_alias, sorters in graph.sort_conditions.items():
            if (
                entity_alias not in carry_forward_aliases
            ):  # Check against aliases actually available
                logger.warning(
                    f"Sorting specified for entity '{entity_alias}', but it's not matched in the base query. Skipping sort."
                )
                continue
            # If we reach here, at least one valid sort condition exists for this alias
            for field_name, ascending in sorters:
                is_sorting_present = True  # Set flag indicating sorting is present
                direction = "ASC" if ascending else "DESC"
                sanitized_prop_access = sanitize_property_reference(field_name)
                # Ensure the alias.property format is correct
                sort_expressions.append(
                    (f"{entity_alias}.{sanitized_prop_access}", direction)
                )

    # Add ORDER BY operation only if there are valid sort expressions
    if sort_expressions:
        plan.add_operation(OrderByOperation(sort_expressions))
        # Add WITH clause *after* sorting *if* sorting was added
        # to separate ORDER BY from subsequent clauses.
        plan.add_with_clause(carry_forward_aliases)  # Carry forward the same aliases
    elif is_sorting_present:
        # Log if sorting was requested but no valid expressions were generated
        logger.warning(
            "Sorting was requested, but no valid sort expressions could be generated based on available aliases."
        )

    # Do NOT add a final WITH here - the projection builder handles its own WITH clauses.
