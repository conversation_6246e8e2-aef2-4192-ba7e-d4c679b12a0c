from __future__ import annotations

from falkordb.asyncio import FalkorDB as AsyncFalkorDB
from redis.asyncio import BlockingConnectionPool, ConnectionPool

from salestech_be.common.singleton import Singleton
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

logger = get_logger(__name__)


def create_falkor_connection_pool(
    host: str,
    port: int,
    username: str | None,
    password: str | None,
    blocking_connection_pool: bool,
    max_connections: int,
) -> ConnectionPool:
    """Creates a Redis ConnectionPool for FalkorDB."""
    logger.info(f"Creating FalkorDB connection pool for host: {host}:{port}")

    if blocking_connection_pool:
        return BlockingConnectionPool(
            host=host,
            port=port,
            username=username,
            password=password,
            max_connections=max_connections,
            decode_responses=True,
            timeout=None,
        )
    else:
        return ConnectionPool(
            host=host,
            port=port,
            username=username,
            password=password,
            max_connections=max_connections,
            decode_responses=True,
        )


def create_async_falkordb_instance(
    pool: ConnectionPool, username: str | None, password: str | None
) -> AsyncFalkorDB:
    """Creates an AsyncFalkorDB client instance."""
    logger.info("Creating AsyncFalkorDB client instance.")
    return AsyncFalkorDB(
        connection_pool=pool,
        username=username,
        password=password,
    )


class FalkorDBConnectionManager(Singleton):
    """Manages a singleton instance of the AsyncFalkorDB client and its pool.

    Creates the client and pool upon first instantiation.
    """

    _client: AsyncFalkorDB
    _pool: ConnectionPool

    def __init__(self) -> None:
        """Initializes the manager, creating the client and pool."""
        # Prevent re-initialization by the Singleton pattern
        if hasattr(self, "_client"):
            return

        logger.info(
            "Initializing FalkorDBConnectionManager singleton and creating resources."
        )
        self._pool = create_falkor_connection_pool(
            host=settings.falkordb_host,
            port=settings.falkordb_port,
            username=settings.falkordb_user,
            password=settings.falkordb_password,
            blocking_connection_pool=settings.falkordb_blocking_connection_pool,
            max_connections=settings.falkordb_max_connections,
        )
        self._client = create_async_falkordb_instance(
            pool=self._pool,
            username=settings.falkordb_user,
            password=settings.falkordb_password,
        )

    @property
    def client(self) -> AsyncFalkorDB:
        """Returns the managed AsyncFalkorDB client."""
        # Initialization is guaranteed by the Singleton pattern's __init__ call
        return self._client

    @property
    def pool(self) -> ConnectionPool:
        """Returns the managed ConnectionPool."""
        # Initialization is guaranteed by the Singleton pattern's __init__ call
        return self._pool

    async def close(self) -> None:
        """Closes the managed connection pool and implicitly the client using it."""
        if not hasattr(self, "_pool") or self._pool is None:
            logger.warning(
                "Attempted to close FalkorDBConnectionManager but pool is not initialized."
            )
            return

        logger.info("Closing FalkorDB pool via ConnectionManager.")
        try:
            await self._pool.aclose()
            logger.info("FalkorDB pool closed successfully.")
        except Exception as e:
            logger.error(f"Error closing FalkorDB pool: {e}", exc_info=True)
        finally:
            # Clear references even if close fails
            # Type checking might complain, but we are resetting the instance state
            delattr(self, "_client")
            delattr(self, "_pool")
            logger.info("FalkorDBConnectionManager internal references cleared.")


def get_falkordb_connection_manager() -> FalkorDBConnectionManager:
    """Returns the singleton instance of FalkorDBConnectionManager.

    Pass settings only on the very first call if needed, otherwise retrieves them.
    """
    return FalkorDBConnectionManager()
