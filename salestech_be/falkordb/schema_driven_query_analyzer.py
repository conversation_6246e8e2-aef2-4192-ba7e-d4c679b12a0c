"""
Schema-driven query analyzer for FalkorDB queries.
Uses the SchemaRegistry to analyze field references and determine entity dependencies.
"""

from collections import deque
from collections.abc import Sequence
from typing import Any
from uuid import UUID

# Import original types directly
from salestech_be.common.query_util.filter_schema import (
    CompositeFilter,
    FilterSpec,
    ValueFilter,
)
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.query_util.sort_schema import (
    OrderEnum,
    SortingSpec,
)

# Ensure correct import of the common FieldType enum
from salestech_be.common.type.metadata.schema import (
    FieldReference,
    ObjectDescriptor,
    OrganizationSchemaDescriptor,
    QualifiedField,
)
from salestech_be.core.data.util import ObjectRecordFetchConditions
from salestech_be.falkordb.pre_processor import PreprocessingContext
from salestech_be.falkordb.query_analyzer import (
    CompositeFilterStructure,
    FineGrainedNodeRole,
    QueryD<PERSON><PERSON>encyGraph,
    QueryNode,
)
from salestech_be.falkordb.schema_registry import SchemaRegistry
from salestech_be.falkordb.types import ArrayElement

# --- Add logger import ---
from salestech_be.ree_logging import get_logger

# --- End Add ---

# --- Add logger instance ---
logger = get_logger(__name__)
# --- End Add ---

# Add class-level hint for the mapping dictionary
select_list_id_mapping: dict[UUID, set[UUID]] | None = None


class SchemaFieldReferenceAnalyzer:
    """
    Schema-driven analyzer for field references in queries.

    Uses SchemaRegistry to determine the entity types and relationships
    based on the actual schema definition rather than inference.
    """

    def __init__(
        self,
        root_entity_type: str,
        schema_registry: SchemaRegistry,
        root_entity_descriptor: ObjectDescriptor | None = None,
    ) -> None:
        """
        Initialize the analyzer with a root entity type and schema registry.

        Args:
            root_entity_type: The type of the root entity (e.g., "Account")
            schema_registry: The schema registry to use for schema information
            root_entity_descriptor: Optional descriptor for the root entity
        """
        self.root_entity_type = root_entity_type
        self.schema_registry = schema_registry

        # If not provided, try to get the root entity descriptor from the schema
        if root_entity_descriptor is None and schema_registry.schema:
            root_entity_descriptor = schema_registry.get_object_descriptor(
                root_entity_type
            )
        self.root_entity_descriptor = root_entity_descriptor

        # Entity tracking
        self.current_entity_type = root_entity_type
        self.current_entity_alias = "n0"
        self.next_entity_index = 1

        # Entity and relationship tracking
        self.entity_types: dict[str, str] = {"n0": root_entity_type}
        self.entity_type_aliases: dict[str, set[str]] = {root_entity_type: {"n0"}}
        self.relationships: set[tuple[str, str, str]] = (
            set()
        )  # (source_alias, rel_id, target_alias)

        # Field mapping - maps field path to (entity_alias, field_name)
        self.field_mapping: dict[str, tuple[str, str]] = {}

        # Context-aware mapping uses tuples (entity_alias, field_name) as key
        self.context_field_mapping: dict[tuple[str, str], tuple[str, str]] = {}

        # Cache for parsed field references to ensure consistency
        self.parsed_references_cache: dict[int, tuple[str, str]] = {}

        # Relationship to alias mapping for deduplication
        self.relationship_to_alias: dict[str, str] = {}

        # Track full traversal paths for field references
        self.field_paths: dict[str, list[Any]] = {}

        # Track array paths
        self.array_paths: dict[str, str] = {}

        # Add internal self.nodes to SchemaFieldReferenceAnalyzer if not present
        self.nodes: dict[str, QueryNode] = {}

        # --- Add root node explicitly ---
        self.current_entity_alias = "n0"
        self.current_entity_type = root_entity_type
        self.entity_types[self.current_entity_alias] = self.current_entity_type
        # Create and add the QueryNode for the root
        # Check if root node already exists before adding
        if self.current_entity_alias not in self.nodes:
            self.nodes[self.current_entity_alias] = QueryNode(
                label=self.current_entity_type, var_name=self.current_entity_alias
            )
        if self.current_entity_type not in self.entity_type_aliases:
            self.entity_type_aliases[self.current_entity_type] = set()
        self.entity_type_aliases[self.current_entity_type].add(
            self.current_entity_alias
        )
        # --- End add root node ---

        self.next_entity_index = 1  # Start subsequent aliases from n1

    def get_or_create_entity_alias(
        self,
        entity_type: str,
        relationship_id: str | None = None,
        full_path: str | None = None,
        purpose: FineGrainedNodeRole | None = None,
    ) -> str:
        """
        Get an existing alias for an entity type or create/update a new one, adding purpose.

        Args:
            entity_type: The type of entity
            relationship_id: Optional relationship ID for the immediate relationship
            full_path: Full reference path from root entity (for proper deduplication)
            purpose: The purpose of the node

        Returns:
            An entity alias
        """
        # Use full path for deduplication if provided
        dedup_key = full_path if full_path else relationship_id
        alias: str | None = None

        if dedup_key is not None and dedup_key in self.relationship_to_alias:
            alias = self.relationship_to_alias[dedup_key]
        elif (
            full_path is None
            and entity_type in self.entity_type_aliases
            and len(self.entity_type_aliases[entity_type]) > 0
        ):
            # Reuse existing alias only if NOT creating for a specific path
            # AND if type already exists (prefer reusing for simple cases)
            alias = next(iter(sorted(self.entity_type_aliases[entity_type])))
            if relationship_id is not None:
                self.relationship_to_alias[relationship_id] = alias

        # If no suitable existing alias found, create a new one
        if alias is None:
            alias = f"n{self.next_entity_index}"
            self.next_entity_index += 1
            self.entity_types[alias] = entity_type
            if entity_type not in self.entity_type_aliases:
                self.entity_type_aliases[entity_type] = set()
            self.entity_type_aliases[entity_type].add(alias)
            # Store mapping
            if dedup_key is not None:
                self.relationship_to_alias[dedup_key] = alias

        # --- Add Purpose ---
        # Find the QueryNode (it should exist either previously or just created)
        # This assumes plan.nodes is accessible or managed by the analyzer directly
        # Let's assume self.nodes holds the nodes being built for the graph
        if alias not in self.nodes:  # Need to manage self.nodes within analyzer
            # If we just created alias, add node object
            self.nodes[alias] = QueryNode(label=entity_type, var_name=alias)

        # Add the fine-grained role
        if purpose:
            self.nodes[alias].add_fine_grained_role(purpose)

        return alias

    def parse_field_reference(
        self,
        field_reference: QualifiedField | FieldReference,
        purpose: FineGrainedNodeRole | None = None,
    ) -> tuple[str, str]:
        """
        Parse a field reference to determine entity dependencies.

        Args:
            field_reference: The field reference to parse. Can be a QualifiedField or FieldReference.
            purpose: The purpose of the node

        Returns:
            A tuple containing the entity alias and field name.
        """
        # Check cache first - use the object's id as key
        cache_key = id(field_reference)
        if cache_key in self.parsed_references_cache:
            return self.parsed_references_cache[cache_key]

        result = self._parse_field_reference_internal(field_reference, purpose=purpose)

        # Cache the result
        self.parsed_references_cache[cache_key] = result

        return result

    def _parse_field_reference_internal(  # noqa: C901, PLR0912, PLR0915
        self,
        field_reference: QualifiedField | FieldReference,
        path_prefix: str = "",
        purpose: FineGrainedNodeRole | None = None,
    ) -> tuple[str, str]:
        """
        Internal implementation of parse_field_reference without caching.
        Uses SchemaRegistry to determine relationship types and target entity types.

        Args:
            field_reference: The field reference to parse
            path_prefix: Optional path prefix for nested references
            purpose: The purpose of the node

        Returns:
            A tuple containing the entity alias and field name
        """
        # Handle QualifiedField
        if isinstance(field_reference, QualifiedField):
            origin_alias_for_hint: str | None = None
            origin_descriptor_for_hint: ObjectDescriptor | None = None
            base_path_for_hint: str = ""
            result_alias: str
            result_field_name: str

            # Empty path, use root entity
            if not field_reference.path:
                result_alias = self.current_entity_alias
                result_field_name = ""
                origin_alias_for_hint = self.current_entity_alias
                origin_descriptor_for_hint = self.root_entity_descriptor
                base_path_for_hint = (
                    path_prefix  # Base path is just the prefix if path is empty
                )

            # For multi-segment paths representing nested objects
            elif len(field_reference.path) > 1:
                # Extract path segments
                path_segments = [str(p) for p in field_reference.path]

                # Handle each segment except the last one as relationship traversals
                current_alias = self.current_entity_alias
                current_type = self.current_entity_type
                current_descriptor = self.root_entity_descriptor

                # Build the full traversal path
                full_path = path_prefix
                path_steps = []

                for _i, segment in enumerate(path_segments[:-1]):
                    # For each segment except the last, treat it as a relationship
                    relationship_id = segment
                    path_steps.append(relationship_id)

                    # Build the full path for this relationship traversal
                    full_path = (
                        f"{full_path}.{relationship_id}"
                        if full_path
                        else relationship_id
                    )

                    # Get the target entity type from schema if available
                    if current_descriptor:
                        # Use SchemaRegistry to determine if this is a nested structure
                        # or a domain relationship
                        is_domain = self.schema_registry.is_domain_relationship(
                            current_descriptor, segment
                        )
                        is_nested = self.schema_registry.is_nested_structure(
                            current_descriptor, segment
                        )

                        if is_nested or is_domain:
                            try:
                                # Get related object name from SchemaRegistry
                                target_type = (
                                    self.schema_registry.get_related_object_name(
                                        current_descriptor, segment
                                    )
                                )
                            except Exception as e:
                                raise ValueError(
                                    f"Failed to find relationship '{segment}' in schema for entity {current_type}. "
                                    f"Error: {e!s}"
                                )
                        else:
                            raise ValueError(
                                f"Field '{segment}' is not a valid relationship in schema for entity {current_type}"
                            )

                        # Get the target descriptor for next iteration
                        if self.schema_registry.schema:
                            target_descriptor = (
                                self.schema_registry.get_object_descriptor(target_type)
                            )
                            if not target_descriptor:
                                raise ValueError(
                                    f"Failed to find descriptor for '{target_type}' in schema"
                                )
                        else:
                            raise ValueError(
                                "Schema is required for schema-driven query analysis"
                            )
                    else:
                        raise ValueError(
                            f"Missing descriptor for entity type '{current_type}'"
                        )

                    # Get or create alias for target entity, using the full path for deduplication
                    target_alias = self.get_or_create_entity_alias(
                        target_type, relationship_id, full_path, purpose=purpose
                    )

                    # Add the relationship to our graph
                    self.relationships.add(
                        (current_alias, relationship_id, target_alias)
                    )

                    # Update current for next iteration
                    current_alias = target_alias
                    current_type = target_type
                    current_descriptor = target_descriptor

                # Last segment is the field name
                field_name = path_segments[-1]

                # Construct the full path string with existing prefix
                full_path_with_field = (
                    f"{full_path}.{field_name}" if full_path else field_name
                )

                # Store the full path for future reference
                self.field_paths[full_path_with_field] = [*path_steps, field_name]

                # Store the full path in field_mapping
                self.field_mapping[full_path_with_field] = (current_alias, field_name)

                # Add to context mapping
                self.context_field_mapping[(current_alias, field_name)] = (
                    current_alias,
                    field_name,
                )

                # --- Set variables for hint processing ---
                origin_alias_for_hint = current_alias
                origin_descriptor_for_hint = current_descriptor
                base_path_for_hint = (
                    full_path  # Use path leading *up to* the final field
                )
                result_alias = current_alias
                result_field_name = field_name
                # --- End Set variables ---

            else:  # Single path segment
                # For QualifiedField with single path element
                field_name = ".".join(
                    str(component) for component in field_reference.path
                )

                # Build full path with prefix if available
                full_path = field_name
                if path_prefix:
                    full_path = f"{path_prefix}.{field_name}"

                # Store either simple field name or full path
                if not path_prefix:
                    # Simple field name - maps to root entity
                    self.field_mapping[field_name] = (
                        self.current_entity_alias,
                        field_name,
                    )
                else:
                    # Full path including prefix
                    self.field_mapping[full_path] = (
                        self.current_entity_alias,
                        field_name,
                    )

                # Always add to context mapping
                self.context_field_mapping[(self.current_entity_alias, field_name)] = (
                    self.current_entity_alias,
                    field_name,
                )

                # Track the path components
                if path_prefix:
                    self.field_paths[full_path] = [*path_prefix.split("."), field_name]
                else:
                    self.field_paths[field_name] = [field_name]

                # --- Set variables for hint processing ---
                origin_alias_for_hint = self.current_entity_alias
                # If called recursively, root_entity_descriptor might not be correct origin
                # Need descriptor corresponding to self.current_entity_alias
                if self.schema_registry.schema:
                    origin_descriptor_for_hint = (
                        self.schema_registry.get_object_descriptor(
                            self.current_entity_type
                        )
                    )
                else:
                    origin_descriptor_for_hint = None  # Or raise error

                base_path_for_hint = (
                    path_prefix  # Hint originates from the prefix path entity
                )
                result_alias = self.current_entity_alias
                result_field_name = field_name
                # --- End Set variables ---

            # --- START: Unified Handle fetch_relationship_ids --- (Moved after if/else)
            if (
                field_reference.fetch_relationship_ids
                and origin_descriptor_for_hint
                and origin_alias_for_hint
            ):
                for hint_rel_id_obj in field_reference.fetch_relationship_ids:
                    hint_rel_id = str(hint_rel_id_obj)
                    try:
                        hint_target_type = self.schema_registry.get_related_object_name(
                            origin_descriptor_for_hint, hint_rel_id
                        )
                        # Create a unique path key for the hint
                        hint_path_key = f"{base_path_for_hint}.hint.{hint_rel_id}"
                        hint_target_alias = self.get_or_create_entity_alias(
                            hint_target_type,
                            hint_rel_id,
                            full_path=hint_path_key,
                            purpose=purpose,
                        )
                        hint_tuple = (
                            origin_alias_for_hint,
                            hint_rel_id,
                            hint_target_alias,
                        )
                        if hint_tuple not in self.relationships:
                            self.relationships.add(hint_tuple)
                            # self.hinted_relationships.add(hint_tuple) # Optional: Mark as hinted if tracking separately
                    except Exception as e:
                        # Log or handle error if hinted relationship is invalid
                        logger.warning(
                            f"Error processing hinted relationship '{hint_rel_id}' from '{origin_alias_for_hint}': {e}"
                        )
            # --- END: Unified Handle fetch_relationship_ids ---

            # --- *** ADD THIS *** ---
            # Ensure the purpose is added to the resolved entity alias
            if purpose and result_alias in self.nodes:
                self.nodes[result_alias].add_fine_grained_role(purpose)
            # --- *** END ADD *** ---

            return result_alias, result_field_name

        # Handle FieldReference
        elif isinstance(field_reference, FieldReference):
            return self._parse_field_reference_object(
                field_reference, path_prefix, purpose=purpose
            )

        else:
            raise TypeError(
                f"Unsupported field reference type: {type(field_reference)}"
            )

    def _parse_field_reference_object(  # noqa: PLR0912, PLR0915, C901
        self,
        field_reference: FieldReference,
        path_prefix: str = "",
        purpose: FineGrainedNodeRole | None = None,
    ) -> tuple[str, str]:
        """
        Parse a FieldReference object using schema information.

        Args:
            field_reference: The FieldReference to parse
            path_prefix: Optional path prefix for nested references
            purpose: The purpose of the node

        Returns:
            A tuple containing the entity alias and field name
        """
        # Get relationship ID
        relationship_id = str(field_reference.relationship_id)

        # Update the path prefix to include this relationship
        new_path_prefix = relationship_id
        if path_prefix:
            new_path_prefix = f"{path_prefix}.{relationship_id}"

        # Get the current entity descriptor based on the current entity type
        current_descriptor = None
        if self.schema_registry.schema:
            current_descriptor = self.schema_registry.get_object_descriptor(
                self.current_entity_type
            )
            if not current_descriptor:
                raise ValueError(
                    f"Failed to find descriptor for '{self.current_entity_type}' in schema"
                )
        else:
            raise ValueError("Schema is required for schema-driven query analysis")

        # Use SchemaRegistry to get target entity type from current descriptor
        try:
            target_type = self.schema_registry.get_related_object_name(
                current_descriptor, relationship_id
            )
        except Exception as e:
            raise ValueError(
                f"Failed to find relationship '{relationship_id}' in schema for entity "
                f"{self.current_entity_type}. Error: {e!s}"
            )

        # Get or create alias for target entity, passing purpose=None for intermediate hop
        target_alias = self.get_or_create_entity_alias(
            target_type, relationship_id, new_path_prefix, purpose=None
        )

        # Add the relationship to our graph
        self.relationships.add(
            (self.current_entity_alias, relationship_id, target_alias)
        )

        # Get target descriptor if schema is available
        target_descriptor = None
        if self.schema_registry.schema:
            target_descriptor = self.schema_registry.get_object_descriptor(target_type)
            if not target_descriptor:
                raise ValueError(
                    f"Failed to find descriptor for '{target_type}' in schema"
                )
        else:
            raise ValueError("Schema is required for schema-driven query analysis")

        # Handle nested field references
        inner_field = field_reference.field

        # CASE 1: Inner field is also a FieldReference (recursive relationship path)
        if isinstance(inner_field, FieldReference):
            # Save current state (needed for correct recursion on self)
            saved_current_entity_alias = self.current_entity_alias
            saved_current_entity_type = self.current_entity_type
            saved_root_entity_descriptor = self.root_entity_descriptor

            # Set current state to target for recursive processing
            self.current_entity_alias = target_alias
            self.current_entity_type = target_type
            self.root_entity_descriptor = target_descriptor

            # Recursively process, passing the original PURPOSE down
            nested_alias, nested_field = self._parse_field_reference_internal(
                inner_field, new_path_prefix, purpose=purpose
            )

            # Restore state
            self.current_entity_alias = saved_current_entity_alias
            self.current_entity_type = saved_current_entity_type
            self.root_entity_descriptor = saved_root_entity_descriptor

            return nested_alias, nested_field

        # CASE 2: Inner field is a QualifiedField (path on the related object)
        elif isinstance(inner_field, QualifiedField) and inner_field.path:
            # CASE 2a: Path has multiple segments (nested traversal from related object)
            if len(inner_field.path) > 1:
                current_alias = target_alias
                current_descriptor = target_descriptor
                current_path_prefix = new_path_prefix

                for segment in inner_field.path[:-1]:
                    relationship_id_seg = str(segment)
                    if not current_descriptor:
                        raise ValueError(
                            f"Cannot traverse nested path segment '{relationship_id_seg}' from {current_alias}, missing descriptor."
                        )
                    current_path_prefix = f"{current_path_prefix}.{relationship_id_seg}"
                    try:
                        next_target_type = self.schema_registry.get_related_object_name(
                            current_descriptor, relationship_id_seg
                        )
                        if self.schema_registry.schema:
                            next_target_descriptor = (
                                self.schema_registry.get_object_descriptor(
                                    next_target_type
                                )
                            )
                            if not next_target_descriptor:
                                raise ValueError(
                                    f"Failed to find descriptor for '{next_target_type}'"
                                )
                        else:
                            raise ValueError("Schema required")
                    except Exception as e:
                        raise ValueError(
                            f"Failed to find relationship '{relationship_id_seg}' for {current_alias}. Error: {e!s}"
                        )
                    next_target_alias = self.get_or_create_entity_alias(
                        next_target_type,
                        relationship_id_seg,
                        current_path_prefix,
                        purpose=None,
                    )
                    rel_tuple = (current_alias, relationship_id_seg, next_target_alias)
                    if rel_tuple not in self.relationships:
                        self.relationships.add(rel_tuple)
                    current_alias = next_target_alias
                    current_descriptor = next_target_descriptor

                final_field_name = str(inner_field.path[-1])
                final_entity_alias = current_alias

                # Assign PURPOSE to the FINAL node alias (e.g., n2 for Department)
                if purpose and final_entity_alias in self.nodes:
                    self.nodes[final_entity_alias].add_fine_grained_role(purpose)

                # Update mappings for the full path
                full_path_str = (
                    f"{new_path_prefix}.{'.'.join(str(p) for p in inner_field.path)}"
                )
                self.field_mapping[full_path_str] = (
                    final_entity_alias,
                    final_field_name,
                )
                self.context_field_mapping[(final_entity_alias, final_field_name)] = (
                    final_entity_alias,
                    final_field_name,
                )
                if full_path_str not in self.field_paths:
                    self.field_paths[full_path_str] = [
                        *new_path_prefix.split("."),
                        *[str(p) for p in inner_field.path],
                    ]

                return final_entity_alias, final_field_name

            # CASE 2b: Path has single segment (direct field on related object)
            elif inner_field.path:  # Check if path is not empty
                final_field_name = str(inner_field.path[0])
                final_entity_alias = (
                    target_alias  # The field is on the direct relation target
                )

                # Assign PURPOSE to this direct target node alias (e.g., n1 for Contact)
                if purpose and final_entity_alias in self.nodes:
                    self.nodes[final_entity_alias].add_fine_grained_role(purpose)

                # Update mappings
                full_path_str = f"{new_path_prefix}.{final_field_name}"
                self.field_mapping[full_path_str] = (
                    final_entity_alias,
                    final_field_name,
                )
                self.context_field_mapping[(final_entity_alias, final_field_name)] = (
                    final_entity_alias,
                    final_field_name,
                )
                if full_path_str not in self.field_paths:
                    self.field_paths[full_path_str] = [
                        *new_path_prefix.split("."),
                        final_field_name,
                    ]

                return final_entity_alias, final_field_name
            else:
                # Path is empty - handle as an error or invalid case
                logger.warning(
                    f"Inner QualifiedField has empty path within FieldReference: {field_reference}"
                )
                # Apply purpose to target_alias as fallback? Maybe not.
                # if purpose and target_alias in self.nodes:
                #     self.nodes[target_alias].add_fine_grained_role(purpose)
                return target_alias, ""  # Return empty field name

        # CASE 3: Inner field is invalid or not a QualifiedField/FieldReference
        else:
            logger.warning(
                f"Inner field of FieldReference was not a valid QualifiedField or FieldReference: {field_reference.field}"
            )
            # Apply purpose to the relationship target as a fallback?
            # Let's avoid this for now as it caused the previous bug.
            # if purpose and target_alias in self.nodes:
            #     self.nodes[target_alias].add_fine_grained_role(purpose)
            # Refactored logic for QualifiedField inside FieldReference
            inner_field = field_reference.field
            if isinstance(inner_field, QualifiedField) and inner_field.path:
                # Start traversal from the target of the initial FieldReference
                current_alias = target_alias  # e.g., n1 (Contact)
                current_descriptor = target_descriptor  # Contact descriptor
                current_path_prefix = (
                    new_path_prefix  # e.g., "account__to__primary_contact"
                )

                # Iterate through nested path segments EXCEPT the last one
                for segment in inner_field.path[:-1]:
                    relationship_id = str(segment)
                    if not current_descriptor:
                        raise ValueError(
                            f"Cannot traverse nested path segment '{relationship_id}' from {current_alias}, missing descriptor."
                        )

                    # Update path prefix
                    current_path_prefix = f"{current_path_prefix}.{relationship_id}"

                    # Find target type and descriptor using registry
                    try:
                        next_target_type = self.schema_registry.get_related_object_name(
                            current_descriptor, relationship_id
                        )
                        if self.schema_registry.schema:
                            next_target_descriptor = (
                                self.schema_registry.get_object_descriptor(
                                    next_target_type
                                )
                            )
                            if not next_target_descriptor:
                                raise ValueError(
                                    f"Failed to find descriptor for '{next_target_type}'"
                                )
                        else:
                            raise ValueError("Schema required for analysis")
                    except Exception as e:
                        raise ValueError(
                            f"Failed to find relationship '{relationship_id}' in schema for entity {current_alias}. Error: {e!s}"
                        )

                    # Get or create alias for the intermediate node
                    # Pass purpose=None here, only apply purpose to the final node holding the field
                    next_target_alias = self.get_or_create_entity_alias(
                        next_target_type,
                        relationship_id,
                        current_path_prefix,
                        purpose=None,
                    )

                    # Add relationship if not already present
                    rel_tuple = (current_alias, relationship_id, next_target_alias)
                    if rel_tuple not in self.relationships:
                        self.relationships.add(rel_tuple)

                    # Update for next iteration
                    current_alias = next_target_alias
                    current_descriptor = next_target_descriptor

                # Last segment is the final field name
                final_field_name = str(inner_field.path[-1])
                final_entity_alias = (
                    current_alias  # Alias of the entity holding the field
                )

                # Update mappings using the FINAL alias and field name
                full_path_str = (
                    f"{new_path_prefix}.{'.'.join(str(p) for p in inner_field.path)}"
                )
                self.field_mapping[full_path_str] = (
                    final_entity_alias,
                    final_field_name,
                )
                self.context_field_mapping[(final_entity_alias, final_field_name)] = (
                    final_entity_alias,
                    final_field_name,
                )
                if full_path_str not in self.field_paths:
                    self.field_paths[full_path_str] = [
                        *new_path_prefix.split("."),
                        *[str(p) for p in inner_field.path],
                    ]

                return final_entity_alias, final_field_name

            # Fallback if inner_field has no path or is not QualifiedField
            # Remove the potentially incorrect role assignment here,
            # as the role should be assigned by the internal logic when parsing the final field.
            # if purpose and target_alias in self.nodes:
            #     self.nodes[target_alias].add_fine_grained_role(purpose)

            # Log a warning and return the target alias of the relationship, but an empty field name
            logger.warning(
                f"Inner field of FieldReference was not a valid QualifiedField with a path: {field_reference.field}"
            )
            return target_alias, ""

    def get_field_mapping(self, entity_alias: str, field_name: str) -> tuple[str, str]:
        """
        Get the field mapping using entity context.

        Args:
            entity_alias: The entity alias for context
            field_name: The field name to look up

        Returns:
            A tuple containing (entity_alias, field_name)

        Raises:
            KeyError: If the field mapping is not found
        """
        # Try context-aware lookup first
        lookup_key = (entity_alias, field_name)
        if lookup_key in self.context_field_mapping:
            return self.context_field_mapping[lookup_key]

        # If not found with entity context, search through field_paths
        # to find a full path that ends with this field name
        for path, components in self.field_paths.items():
            if (
                components
                and components[-1] == field_name
                and path in self.field_mapping
            ):
                mapped_entity, mapped_field = self.field_mapping[path]
                if mapped_entity == entity_alias:
                    return (mapped_entity, mapped_field)

        # Finally, check if this is a simple field name mapping to the root entity
        if field_name in self.field_mapping:
            mapped_entity, mapped_field = self.field_mapping[field_name]
            if mapped_entity == entity_alias:
                return (mapped_entity, mapped_field)

        # If we still can't find it, raise KeyError
        raise KeyError(f"No field mapping found for {entity_alias}.{field_name}")


class SchemaDrivenQueryAnalyzer:
    """
    Schema-driven query analyzer that builds a dependency graph for a query.

    Uses SchemaRegistry to determine entity types, relationships, and field types
    based on the actual schema rather than inference.
    """

    def __init__(
        self,
        root_entity_type: str,
        schema_registry: SchemaRegistry | None = None,
        schema: OrganizationSchemaDescriptor | None = None,
        expand_schema: bool = True,
        preprocessing_context: PreprocessingContext | None = None,
    ) -> None:
        """
        Initialize the analyzer with a root entity type and schema information.

        Args:
            root_entity_type: The type of the root entity (e.g., "Account")
            schema_registry: Optional existing schema registry to use
            schema: Optional schema to use if no registry is provided
            expand_schema: Whether to automatically expand the graph with all
                         nested schema fields (default True).
        """
        # Initialize schema registry if needed
        if schema_registry is None:
            if schema is None:
                raise ValueError("Either schema_registry or schema must be provided")
            schema_registry = SchemaRegistry(schema)
        self.schema_registry = schema_registry

        # Get the root entity descriptor
        self.root_entity_descriptor = None
        if schema_registry.schema:
            self.root_entity_descriptor = schema_registry.get_object_descriptor(
                root_entity_type
            )

        # Initialize the field reference analyzer
        self.field_reference_analyzer = SchemaFieldReferenceAnalyzer(
            root_entity_type=root_entity_type,
            schema_registry=schema_registry,
            root_entity_descriptor=self.root_entity_descriptor,
        )

        # Filter tracking
        self.standalone_filters: dict[str, list[ValueFilter]] = {}
        self.composite_structures: list[CompositeFilterStructure] = []
        self.array_element_filters: list[
            tuple[str, str, ValueFilter, ArrayElement]
        ] = []

        # Sort tracking
        self.entity_sorters: dict[str, list[tuple[str, bool]]] = {}
        self.array_element_sorters: list[tuple[str, str, str, bool]] = []

        # Requested output paths tracking
        self.requested_output_paths: set[str] | None = None

        self.expand_schema = expand_schema

        # Store the pre-processed context, providing a default if None
        self.preprocessing_context = preprocessing_context or PreprocessingContext()

        # --- Add collected_field_types attribute ---
        self.collected_field_types: dict[tuple[str, str], str] = {}
        # --- End collected_field_types attribute ---

    def _create_leaf_node_filter(
        self, entity_alias: str, value_filter: ValueFilter
    ) -> ValueFilter:
        """
        Create a new filter that uses just the leaf node field for nested paths.
        This ensures consistent handling of nested paths across the codebase.

        Args:
            entity_alias: The entity alias from field reference analysis
            value_filter: The original value filter

        Returns:
            A possibly modified value filter with the correct field path
        """
        # If we have a QualifiedField with multiple path segments, extract just the leaf node
        if (
            isinstance(value_filter.field, QualifiedField)
            and len(value_filter.field.path) > 1
        ):
            # Create a new filter with just the leaf node field
            leaf_field = QualifiedField(path=(value_filter.field.path[-1],))

            # Try to look up the mapping using our context-aware method if available
            try:
                # Get the field name
                field_name = str(value_filter.field.path[-1])
                # If we find this field in our context mapping for this entity, use it
                mapped_entity, mapped_field = (
                    self.field_reference_analyzer.get_field_mapping(
                        entity_alias, field_name
                    )
                )
                # If the mapping matches the expected entity, we're good to go
                if mapped_entity == entity_alias:
                    return ValueFilter(
                        field=leaf_field,
                        operator=value_filter.operator,
                        value=value_filter.value,
                    )
            except KeyError:
                # If lookup fails, just continue with the default behavior
                pass

            return ValueFilter(
                field=leaf_field,
                operator=value_filter.operator,
                value=value_filter.value,
            )

        return value_filter

    def analyze_value_filter(  # noqa: C901,PLR0912,PLR0915
        self, value_filter: ValueFilter, purpose: FineGrainedNodeRole | None = None
    ) -> tuple[str, ValueFilter, str]:
        """
        Analyze a single value filter and return entity alias, modified filter, and field name.
        Uses SchemaRegistry to determine the entity and field information.

        Args:
            value_filter: The value filter to analyze
            purpose: The purpose of the node

        Returns:
            Tuple of (entity_alias, modified_value_filter, field_name)
        """
        field_ref = value_filter.field
        entity_alias, field_name = self.field_reference_analyzer.parse_field_reference(
            field_ref, purpose=purpose
        )

        # Check if this field is a remappable select list ID field
        # Need the object type name for the check
        object_type_name = self.field_reference_analyzer.entity_types.get(entity_alias)
        is_remappable_field = False
        if object_type_name:
            is_remappable_field = (
                field_name
                in self.preprocessing_context.remappable_fields_by_object.get(
                    object_type_name, set()
                )
            )
        # If it's a remappable field and the operator is suitable, expand the IDs
        if is_remappable_field and hasattr(
            self.preprocessing_context, "select_list_id_mapping"
        ):
            logger.debug(
                f"Expanding remappable field '{field_name}' for {entity_alias} with operator {value_filter.operator}"
            )
            original_values: set[UUID] = set()
            if isinstance(value_filter.value, UUID):
                original_values.add(value_filter.value)
            elif isinstance(value_filter.value, (set, list)):
                for item in value_filter.value:
                    if isinstance(item, UUID):
                        original_values.add(item)
                    elif isinstance(item, str):
                        try:
                            original_values.add(UUID(item))
                        except ValueError:
                            logger.warning(
                                f"Could not convert value '{item}' to UUID for remappable field '{field_name}'. Skipping expansion for this value."
                            )

            if original_values:
                expanded_ids: set[UUID] = set()
                for original_id in original_values:
                    # Get the union set from the pre-fetched mapping,
                    # defaulting to the original ID itself if not found in the map.
                    equivalent_ids = (
                        self.preprocessing_context.select_list_id_mapping.get(
                            original_id, {original_id}
                        )
                    )
                    expanded_ids.update(equivalent_ids)

                # Create a new ValueFilter with IN operator and expanded IDs
                if value_filter.operator in (MatchOperator.EQ, MatchOperator.IN):
                    value_filter = ValueFilter(
                        field=value_filter.field,  # Use the original field reference
                        operator=MatchOperator.IN,
                        value=set(expanded_ids),
                    )
                elif value_filter.operator in (MatchOperator.NE, MatchOperator.NIN):
                    value_filter = ValueFilter(
                        field=value_filter.field,  # Use the original field reference
                        operator=MatchOperator.NIN,
                        value=set(expanded_ids),
                    )
            else:
                logger.warning(
                    f"Could not extract valid UUIDs from filter value for remappable field '{field_name}'. Proceeding without expansion."
                )

        # Store or update field mappings to ensure we can look up by context
        if isinstance(field_ref, FieldReference) and hasattr(
            field_ref, "relationship_id"
        ):
            relationship_id = str(field_ref.relationship_id)
            orig_field_name = ""
            if isinstance(field_ref.field, QualifiedField) and field_ref.field.path:
                orig_field_name = ".".join(str(p) for p in field_ref.field.path)

            # Create legacy-style relationship field name mapping
            relationship_field_name = f"{relationship_id}.{orig_field_name}"
            self.field_reference_analyzer.field_mapping[relationship_field_name] = (
                entity_alias,
                field_name,
            )

            # Add to context mapping
            self.field_reference_analyzer.context_field_mapping[
                (entity_alias, field_name)
            ] = (entity_alias, field_name)

            # Track in field paths if not already there
            if relationship_field_name not in self.field_reference_analyzer.field_paths:
                self.field_reference_analyzer.field_paths[relationship_field_name] = [
                    relationship_id,
                    field_name,
                ]

        # For QualifiedField with multi-segment path, ensure mapping exists for full path
        elif isinstance(field_ref, QualifiedField) and len(field_ref.path) > 1:
            full_path = ".".join(str(p) for p in field_ref.path)

            # Update both mappings
            self.field_reference_analyzer.field_mapping[full_path] = (
                entity_alias,
                field_name,
            )
            self.field_reference_analyzer.context_field_mapping[
                (entity_alias, field_name)
            ] = (entity_alias, field_name)

            # Track the path components if not already tracked
            if full_path not in self.field_reference_analyzer.field_paths:
                self.field_reference_analyzer.field_paths[full_path] = [
                    str(p) for p in field_ref.path
                ]

        # Create a modified filter with proper field path if needed
        modified_filter = self._create_leaf_node_filter(entity_alias, value_filter)

        # --- Store field type ---
        field_type_val = self._get_primitive_field_type(entity_alias, field_name)
        logger.debug(
            f"[analyze_value_filter] For {entity_alias=}, {field_name=}, got field_type_val={field_type_val}"
        )
        if field_type_val:
            self.collected_field_types[(entity_alias, field_name)] = field_type_val
            logger.debug(
                f"[analyze_value_filter] Stored in collected_field_types: key=({entity_alias}, {field_name}), value={field_type_val}"
            )

        return entity_alias, modified_filter, field_name

    def add_standalone_filter(
        self,
        entity_alias: str,
        filter_condition: ValueFilter,
    ) -> None:
        """
        Add a standalone filter condition to an entity.

        Args:
            entity_alias: The alias of the entity
            filter_condition: The filter condition to add
        """
        if entity_alias not in self.standalone_filters:
            self.standalone_filters[entity_alias] = []
        self.standalone_filters[entity_alias].append(filter_condition)

    def build_dependency_graph(self) -> QueryDependencyGraph:  # noqa: C901, PLR0912
        """
        Build a query dependency graph from the analyzed query using schema information.

        Returns:
            A SchemaDrivenQueryDependencyGraph representing the structure of the query
        """
        graph = QueryDependencyGraph()

        # Add entities by passing the QueryNode objects from the analyzer
        for alias, node_obj in self.field_reference_analyzer.nodes.items():
            # Pass the QueryNode object directly to the graph's add_entity method
            graph.add_entity(alias, node_obj)  # Modified to pass QueryNode

        # Add relationships (ensure source/target nodes were added first)
        for source, rel_id, target in self.field_reference_analyzer.relationships:
            # Defensive check: Ensure nodes exist in graph before adding relationship
            if source in graph.nodes and target in graph.nodes:
                graph.add_relationship(source, rel_id, target)
            else:
                # Log warning about inconsistent state if nodes are missing
                logger.warning(
                    f"Skipping relationship ({source}, {rel_id}, {target}) as source or target node not found in graph nodes."
                )

        # Add standalone filters - copy each list of filters
        for alias, filters in self.standalone_filters.items():
            if alias in graph.nodes:  # Check if alias exists in graph
                graph.standalone_filters[alias] = filters.copy()
            else:
                logger.warning(
                    f"Skipping standalone filters for alias '{alias}' as it's not in graph nodes."
                )

        # Add composite structures
        graph.composite_structures = self.composite_structures.copy()

        # Add array element filters
        for (
            array_path,
            entity_alias,
            filter_,
            array_element_type,
        ) in self.array_element_filters:
            # Check if alias exists before adding
            if entity_alias in graph.nodes:
                graph.add_array_element_filter(
                    array_path, entity_alias, filter_, array_element_type
                )
            else:
                logger.warning(
                    f"Skipping array element filter for alias '{entity_alias}' as it's not in graph nodes."
                )

        # Add sort conditions
        for alias, sorters in self.entity_sorters.items():
            if alias in graph.nodes:  # Check if alias exists
                for field_name, ascending in sorters:
                    graph.add_sort_condition(alias, field_name, ascending)
            else:
                logger.warning(
                    f"Skipping sort conditions for alias '{alias}' as it's not in graph nodes."
                )

        # Add array element sorts
        for (
            array_path,
            entity_alias,
            field_name,
            ascending,
        ) in self.array_element_sorters:
            # Check if alias exists
            if entity_alias in graph.nodes:
                graph.add_array_element_sort(
                    array_path, entity_alias, field_name, ascending
                )
            else:
                logger.warning(
                    f"Skipping array element sort for alias '{entity_alias}' as it's not in graph nodes."
                )

        # --- Expand the graph with nested schema fields --- #
        if self.expand_schema and self.schema_registry.schema:
            expand_graph_with_nested_schema(
                graph=graph,
                schema=self.schema_registry.schema,
                registry=self.schema_registry,
            )
        # -------------------------------------------------- #

        # --- Correctly populate graph.field_types from self.collected_field_types ---
        if hasattr(graph, "field_types"):
            graph.field_types.update(self.collected_field_types)
        else:
            logger.warning(
                "QueryDependencyGraph does not have 'field_types' attribute. Skipping population by collected_field_types."
            )
        # --- End populate ---

        return graph

    def get_entity_traversal_order(self) -> list[str]:
        """Get the order in which entities should be traversed (potentially based on graph)."""
        return self.build_dependency_graph().get_topological_order()

    def get_array_paths(self) -> dict[str, str]:
        """Get the array paths and their target types."""
        return self.field_reference_analyzer.array_paths

    def get_array_element_filters(
        self,
    ) -> list[tuple[str, str, ValueFilter, ArrayElement]]:
        """
        Get all array element filter conditions.

        Returns:
            A list of (array_path, entity_alias, filter, array_element_type) tuples
        """
        return self.array_element_filters

    def get_sort_conditions(self) -> list[tuple[str, str, bool]]:
        """
        Get sort conditions.

        Returns:
            A list of (entity_alias, field_name, ascending) tuples
        """
        # Convert from entity_sorters to appropriate return type
        result: list[tuple[str, str, bool]] = []
        for alias, sorters in self.entity_sorters.items():
            for field_name, ascending in sorters:
                result.append((alias, field_name, ascending))
        return result

    def get_array_element_sorts(self) -> list[tuple[str, str, str, bool]]:
        """
        Get all array element sort conditions.

        Returns:
            A list of (array_path, entity_alias, field_name, ascending) tuples
        """
        return self.array_element_sorters

    def analyze_filter_criteria(self, filter_spec: FilterSpec) -> None:  # noqa: C901, PLR0915
        """
        Analyze filter criteria and extract entity relationships using schema information.

        Args:
            filter_spec: The filter specification to analyze
        """
        if not filter_spec or not filter_spec.filter:
            return

        # Get the root entity alias for this filter
        root_entity_alias = "n0"  # Default alias for root entity

        def analyze_composite_filter(  # noqa: C901, PLR0915, PLR0912
            composite_filter: CompositeFilter,
        ) -> CompositeFilterStructure:
            """Analyze a composite filter and store its structure using schema information."""
            # Count non-empty filter groups to check if restructuring is needed
            non_empty_filter_groups = 0
            if composite_filter.all_of:
                non_empty_filter_groups += 1
            if composite_filter.any_of:
                non_empty_filter_groups += 1
            if composite_filter.none_of:
                non_empty_filter_groups += 1

            # If there are multiple non-empty filter groups, restructure
            if non_empty_filter_groups > 1:
                # Create a parent composite structure using all_of as the top level connector
                parent_composite = CompositeFilterStructure(root_entity_alias)

                # Create nested structures for each filter type
                if composite_filter.all_of:
                    all_of_structure = CompositeFilterStructure(root_entity_alias)
                    for filter_ in composite_filter.all_of:
                        if isinstance(filter_, ValueFilter):
                            entity_alias, modified_filter, _ = (
                                self.analyze_value_filter(filter_, purpose="FILTER")
                            )
                            all_of_structure.all_of_filters.append(
                                (entity_alias, modified_filter)
                            )
                        elif isinstance(filter_, CompositeFilter):
                            nested_composite = process_nested_composite(
                                filter_, purpose="FILTER"
                            )
                            all_of_structure.nested_all_of.append(nested_composite)
                    parent_composite.nested_all_of.append(all_of_structure)

                if composite_filter.any_of:
                    any_of_structure = CompositeFilterStructure(root_entity_alias)
                    for filter_ in composite_filter.any_of:
                        if isinstance(filter_, ValueFilter):
                            entity_alias, modified_filter, _ = (
                                self.analyze_value_filter(filter_, purpose="FILTER")
                            )
                            any_of_structure.any_of_filters.append(
                                (entity_alias, modified_filter)
                            )
                        elif isinstance(filter_, CompositeFilter):
                            nested_composite = process_nested_composite(
                                filter_, purpose="FILTER"
                            )
                            any_of_structure.nested_any_of.append(nested_composite)
                    parent_composite.nested_all_of.append(any_of_structure)

                if composite_filter.none_of:
                    none_of_structure = CompositeFilterStructure(root_entity_alias)
                    for filter_ in composite_filter.none_of:
                        if isinstance(filter_, ValueFilter):
                            entity_alias, modified_filter, _ = (
                                self.analyze_value_filter(filter_, purpose="FILTER")
                            )
                            none_of_structure.none_of_filters.append(
                                (entity_alias, modified_filter)
                            )
                        elif isinstance(filter_, CompositeFilter):
                            nested_composite = process_nested_composite(
                                filter_, purpose="FILTER"
                            )
                            none_of_structure.nested_none_of.append(nested_composite)
                    parent_composite.nested_all_of.append(none_of_structure)

                # Add the parent structure to composite_structures
                self.composite_structures.append(parent_composite)
                return parent_composite
            else:
                # Standard case with only one non-empty filter group
                composite = CompositeFilterStructure(root_entity_alias)

                # Process all direct ValueFilters in the composite
                for filter_ in composite_filter.all_of:
                    if isinstance(filter_, ValueFilter):
                        entity_alias, modified_filter, _ = self.analyze_value_filter(
                            filter_, purpose="FILTER"
                        )
                        composite.all_of_filters.append((entity_alias, modified_filter))
                    elif isinstance(filter_, CompositeFilter):
                        # Create a nested structure for composite filters
                        nested_composite = process_nested_composite(
                            filter_, purpose="FILTER"
                        )
                        composite.nested_all_of.append(nested_composite)

                for filter_ in composite_filter.any_of:
                    if isinstance(filter_, ValueFilter):
                        entity_alias, modified_filter, _ = self.analyze_value_filter(
                            filter_, purpose="FILTER"
                        )
                        composite.any_of_filters.append((entity_alias, modified_filter))
                    elif isinstance(filter_, CompositeFilter):
                        # Create a nested structure for composite filters
                        nested_composite = process_nested_composite(
                            filter_, purpose="FILTER"
                        )
                        composite.nested_any_of.append(nested_composite)

                for filter_ in composite_filter.none_of:
                    if isinstance(filter_, ValueFilter):
                        entity_alias, modified_filter, _ = self.analyze_value_filter(
                            filter_, purpose="FILTER"
                        )
                        composite.none_of_filters.append(
                            (entity_alias, modified_filter)
                        )
                    elif isinstance(filter_, CompositeFilter):
                        # Create a nested structure for composite filters
                        nested_composite = process_nested_composite(
                            filter_, purpose="FILTER"
                        )
                        composite.nested_none_of.append(nested_composite)

                self.composite_structures.append(composite)
                return composite

        def process_nested_composite(
            nested_filter: CompositeFilter, purpose: FineGrainedNodeRole | None = None
        ) -> CompositeFilterStructure:
            """Process a nested composite filter using schema information."""
            nested_composite = CompositeFilterStructure(root_entity_alias)

            # Process nested filters recursively
            for filter_ in nested_filter.all_of:
                if isinstance(filter_, ValueFilter):
                    entity_alias, modified_filter, _ = self.analyze_value_filter(
                        filter_, purpose=purpose
                    )
                    nested_composite.all_of_filters.append(
                        (entity_alias, modified_filter)
                    )
                elif isinstance(filter_, CompositeFilter):
                    sub_nested = process_nested_composite(filter_, purpose=purpose)
                    nested_composite.nested_all_of.append(sub_nested)

            for filter_ in nested_filter.any_of:
                if isinstance(filter_, ValueFilter):
                    entity_alias, modified_filter, _ = self.analyze_value_filter(
                        filter_, purpose=purpose
                    )
                    nested_composite.any_of_filters.append(
                        (entity_alias, modified_filter)
                    )
                elif isinstance(filter_, CompositeFilter):
                    sub_nested = process_nested_composite(filter_, purpose=purpose)
                    nested_composite.nested_any_of.append(sub_nested)

            for filter_ in nested_filter.none_of:
                if isinstance(filter_, ValueFilter):
                    entity_alias, modified_filter, _ = self.analyze_value_filter(
                        filter_, purpose=purpose
                    )
                    nested_composite.none_of_filters.append(
                        (entity_alias, modified_filter)
                    )
                elif isinstance(filter_, CompositeFilter):
                    sub_nested = process_nested_composite(filter_, purpose=purpose)
                    nested_composite.nested_none_of.append(sub_nested)

            return nested_composite

        # Handle filter based on its type
        filter_ = filter_spec.filter
        if isinstance(filter_, CompositeFilter):
            analyze_composite_filter(filter_)
        elif isinstance(filter_, ValueFilter):
            # Handle standalone value filter
            entity_alias, modified_filter, _ = self.analyze_value_filter(
                filter_, purpose="FILTER"
            )
            self.add_standalone_filter(entity_alias, modified_filter)

    def analyze_sort_criteria(self, sorting_spec: SortingSpec) -> None:
        """
        Analyze sort criteria to determine entity dependencies using schema information.

        Args:
            sorting_spec: The sorting specification to analyze
        """
        if not sorting_spec or not sorting_spec.ordered_sorters:
            return

        for sorter in sorting_spec.ordered_sorters:
            # Get entity alias and field name using schema information
            entity_alias, field_name = (
                self.field_reference_analyzer.parse_field_reference(
                    sorter.field, purpose="SORT"
                )
            )

            # --- Store field type ---
            field_type_val = self._get_primitive_field_type(entity_alias, field_name)
            if field_type_val:
                self.collected_field_types[(entity_alias, field_name)] = field_type_val
            # --- End store field type ---

            # Add sort condition
            if entity_alias not in self.entity_sorters:
                self.entity_sorters[entity_alias] = []
            self.entity_sorters[entity_alias].append(
                (field_name, sorter.order == OrderEnum.ASC)
            )

    def analyze_field_references(
        self, field_refs: Sequence[QualifiedField | FieldReference]
    ) -> None:
        """
        Analyze field references to determine entity dependencies using schema information.

        Args:
            field_refs: The field references to analyze
        """
        for field_ref in field_refs:
            # Use SchemaFieldReferenceAnalyzer to parse field references
            self.field_reference_analyzer.parse_field_reference(
                field_ref, purpose="PROJECTION"
            )

    def _get_field_ref_path_string(
        self, field_ref: QualifiedField | FieldReference
    ) -> str:
        """Convert a field reference object into its canonical string path."""
        if isinstance(field_ref, QualifiedField):
            return ".".join(str(p) for p in field_ref.path)
        elif isinstance(field_ref, FieldReference):
            # Recursively get the path of the nested field
            nested_path = self._get_field_ref_path_string(field_ref.field)
            # If the nested part is empty, just use the relationship ID
            if not nested_path:
                return str(field_ref.relationship_id)
            return f"{field_ref.relationship_id}.{nested_path}"
        else:
            # Should not happen with current types
            raise TypeError(
                f"Unsupported field reference type for path string: {type(field_ref)}"
            )

    def _add_nested_schema_dependencies(
        self,
        current_alias: str,
        current_descriptor: ObjectDescriptor,
        path_prefix: str,
        visited_paths: set[str],
    ) -> None:
        """
        Recursively analyze an object descriptor and add dependencies for all
        nested object fields and collections defined in the schema.

        Args:
            current_alias: The alias of the node being analyzed.
            current_descriptor: The descriptor of the object type for the current node.
            path_prefix: The dot-separated path from the root to reach this node.
            visited_paths: Set of str(ObjectIdentifier) visited in the current recursion path.
        """
        if not current_descriptor or not current_descriptor.fields:
            return

        # Iterate through fields defined in the schema for the current object type
        for field in current_descriptor.fields:
            field_info = self.schema_registry.analyze_field_type(
                current_descriptor, field
            )

            # Check if it's a nested structure (single object or collection of objects)
            is_nested_structure = (
                field_info.is_nested or field_info.is_collection
            ) and field_info.nested_descriptor is not None

            if is_nested_structure:
                nested_descriptor = field_info.nested_descriptor
                if not nested_descriptor:
                    continue

                relationship_id = self.schema_registry.extract_field_name(
                    field.field_identifier
                )
                target_type = self.schema_registry.get_object_name(nested_descriptor)

                # Construct the unique path for alias generation
                full_path = (
                    f"{path_prefix}.{relationship_id}"
                    if path_prefix
                    else relationship_id
                )

                # Get or create an alias for the target node, ensuring unique paths get unique aliases
                target_alias = self.field_reference_analyzer.get_or_create_entity_alias(
                    target_type, relationship_id, full_path, purpose="FILTER"
                )

                # Add the relationship to the analyzer's state
                self.field_reference_analyzer.relationships.add(
                    (current_alias, relationship_id, target_alias)
                )

                # --- Cycle Check Before Recursion ---
                # Check if the target node+type we are about to recurse into
                # has already been visited in the current path using its string identifier.
                target_visit_key = str(nested_descriptor.object_identifier)
                if target_visit_key in visited_paths:
                    continue  # Don't recurse into a cycle

                # Recurse on the nested structure
                self._add_nested_schema_dependencies(
                    target_alias, nested_descriptor, full_path, visited_paths.copy()
                )

    def analyze_query(self, fetch_conditions: ObjectRecordFetchConditions) -> None:
        """
        Analyze a query to determine entity dependencies using schema information,
        including automatic expansion of nested schema fields.

        Args:
            fetch_conditions: The query to analyze, as fetch conditions
        """
        # Analyze explicitly requested fields, filters, and sorts
        if fetch_conditions.fields:
            self.analyze_field_references(fetch_conditions.fields)
        if fetch_conditions.filter_spec:
            self.analyze_filter_criteria(fetch_conditions.filter_spec)
        if fetch_conditions.sorting_spec:
            self.analyze_sort_criteria(fetch_conditions.sorting_spec)

        # --- Process Requested Fields ---
        requested_paths_found: set[str] | None = None
        if fetch_conditions.fields:
            # Ensure graph elements for requested fields are parsed and added to analyzer state
            self.analyze_field_references(fetch_conditions.fields)
            # Store the canonical paths for the requested fields
            requested_paths_found = {
                self._get_field_ref_path_string(field_ref)
                for field_ref in fetch_conditions.fields
            }

        self.requested_output_paths = (
            requested_paths_found  # Store None if fields was empty/None
        )

        # --- Removed automatic expansion call --- #
        # # Automatically add dependencies for all nested schema fields starting from root
        # if self.root_entity_descriptor:
        #     self._add_nested_schema_dependencies(
        #         "n0",  # Root alias
        #         self.root_entity_descriptor,
        #         "",  # Initial path prefix
        #         set(),  # Initial visited set
        #     )

    def _get_primitive_field_type(  # noqa: PLR0911
        self, entity_alias: str, field_name: str
    ) -> str | None:
        logger.debug(
            f"[_get_primitive_field_type] Called for: {entity_alias=}, {field_name=}"
        )
        if not self.schema_registry.schema:
            logger.warning("_get_primitive_field_type: Schema is not available.")
            return None

        entity_type = self.field_reference_analyzer.entity_types.get(entity_alias)
        if not entity_type:
            logger.warning(
                f"_get_primitive_field_type: Entity type not found for alias {entity_alias}."
            )
            return None

        object_descriptor = self.schema_registry.get_object_descriptor(entity_type)
        if not object_descriptor:
            logger.warning(
                f"_get_primitive_field_type: Object descriptor not found for entity type {entity_type}."
            )
            return None

        target_field_descriptor = next(
            (
                f
                for f in object_descriptor.fields
                if self.schema_registry.extract_field_name(f.field_identifier)
                == field_name
            ),
            None,
        )

        if not target_field_descriptor:
            return None

        type_analysis_info = self.schema_registry.analyze_field_type(
            object_descriptor, target_field_descriptor
        )

        if type_analysis_info.is_collection:  # Use .is_collection
            # Use the new pre-calculated primitive type string
            primitive_cat = type_analysis_info.list_element_primitive_type
            if primitive_cat == "string":
                return "array_string"
            elif primitive_cat == "number":
                return "array_number"
            elif primitive_cat == "other_primitive":
                return "array_other_primitive"
            else:
                # List of something not fitting the simple categories (e.g., list of nested objects, list of lists)
                logger.debug(
                    f"_get_primitive_field_type: Field {entity_alias}.{field_name} is a list of non-categorized type: {primitive_cat}. Returning None."
                )
                return None
        elif type_analysis_info.is_primitive:
            result_type_str = type_analysis_info.element_primitive_type_str
        else:
            result_type_str = None  # Not a collection and not primitive

        logger.debug(
            f"[_get_primitive_field_type] For {entity_alias}.{field_name}, determined type: {result_type_str}"
        )
        return result_type_str


def create_query_analyzer(
    root_entity_type: str,
    schema_registry: SchemaRegistry | None = None,
    schema: OrganizationSchemaDescriptor | None = None,
) -> SchemaDrivenQueryAnalyzer:
    """
    Create a schema-driven query analyzer for the given entity type using schema information.

    This is a convenience function for creating a SchemaDrivenQueryAnalyzer with
    proper schema information, ensuring that schema-based decisions are made
    rather than relying on inference.

    Args:
        root_entity_type: The type of the root entity (e.g., "Account")
        schema_registry: Optional existing schema registry to use
        schema: Optional schema to use if no registry is provided

    Returns:
        A schema-driven query analyzer for the given entity type

    Raises:
        ValueError: If neither schema_registry nor schema is provided
    """
    return SchemaDrivenQueryAnalyzer(
        root_entity_type=root_entity_type,
        schema_registry=schema_registry,
        schema=schema,
    )


def expand_graph_with_nested_schema(  # noqa: C901, PLR0912, PLR0915
    graph: QueryDependencyGraph,
    schema: OrganizationSchemaDescriptor,
    registry: SchemaRegistry,
) -> None:
    """
    Expands a QueryDependencyGraph by adding nodes and relationships
    for all nested schema fields reachable from existing nodes, including cycle detection.

    Modifies the graph in place.

    Args:
        graph: The initial QueryDependencyGraph to expand.
        schema: The organization schema descriptor.
        registry: The schema registry.
    """
    if not schema:
        return  # Cannot expand without a schema

    # --- Initialization ---
    # Tracks (source_alias, relationship_id) to avoid reprocessing the same edge
    expanded_edges: set[tuple[str, str]] = set()
    # Maps (source_alias, relationship_id) -> target_alias for consistent alias use if an edge exists pre-expansion
    # NOTE: This map is only populated based on the *initial* graph state. New expansions always create new aliases.
    initial_edge_to_target_alias: dict[tuple[str, str], str] = {}

    # Pre-populate tracking based on relationships already in the graph
    for source_alias, rel_set in graph.entity_relationships.items():
        for rel_id, target_alias in rel_set:
            path_key = (source_alias, rel_id)
            expanded_edges.add(path_key)
            if path_key not in initial_edge_to_target_alias:
                initial_edge_to_target_alias[path_key] = target_alias

    # Initialize alias counter
    max_existing_alias_num = 0
    # Iterate through graph.nodes (which contains QueryNode objects)
    for alias in graph.nodes:
        if alias.startswith("n") and alias[1:].isdigit():
            max_existing_alias_num = max(max_existing_alias_num, int(alias[1:]))
    alias_counter = max_existing_alias_num + 1
    # --- End Initialization ---

    # Queue stores tuples: (alias_to_process, path_tuple_of_object_ids)
    queue: deque[tuple[str, tuple[str, ...]]] = deque()
    # Track aliases added to the queue during this expansion to prevent redundant queue adds
    queued_aliases_in_expansion = set()

    # Initialize queue with existing nodes and their initial paths
    # Iterate through graph.nodes
    for alias, node_obj in graph.nodes.items():
        entity_type = node_obj.label
        descriptor = registry.get_object_descriptor(entity_type)
        if descriptor:
            obj_id_str = str(descriptor.object_identifier)
            initial_path = (obj_id_str,)
            queue.append((alias, initial_path))
            queued_aliases_in_expansion.add(alias)
        else:
            # Log warning if descriptor not found for an existing node
            logger.warning(
                f"Could not find descriptor for existing node type '{entity_type}' (alias: '{alias}') during expansion."
            )

    while queue:
        current_alias, current_path = queue.popleft()

        # Use graph.get_entity_type to safely get type
        try:
            current_type = graph.get_entity_type(current_alias)
        except KeyError:
            logger.warning(
                f"Alias '{current_alias}' not found in graph during expansion queue processing."
            )
            continue

        current_descriptor = registry.get_object_descriptor(current_type)
        if not current_descriptor:
            logger.warning(
                f"Descriptor not found for type '{current_type}' (alias: '{current_alias}') during expansion field iteration."
            )
            continue

        # Iterate through schema fields for the current node type
        for field in current_descriptor.fields:
            field_info = registry.analyze_field_type(current_descriptor, field)
            is_nested_structure = (
                field_info.is_nested or field_info.is_collection
            ) and field_info.nested_descriptor is not None

            if is_nested_structure:
                nested_descriptor = field_info.nested_descriptor
                if not nested_descriptor:
                    continue

                relationship_id = registry.extract_field_name(field.field_identifier)
                target_type = registry.get_object_name(nested_descriptor)
                target_obj_id_str = str(nested_descriptor.object_identifier)

                # --- Cycle Check ---
                if target_obj_id_str in current_path:
                    continue  # Skip this expansion path

                # --- Edge Processed Check ---
                edge_key = (current_alias, relationship_id)
                if edge_key in expanded_edges:
                    # This edge was already present or added, ensure graph state is consistent
                    # but don't re-add or re-queue the target from this path.
                    # We might need to retrieve the existing target_alias if needed elsewhere,
                    # but for queueing purposes, we stop here for this path.
                    continue
                expanded_edges.add(edge_key)  # Mark this specific edge as processed

                # --- Alias Generation ---
                # Always generate a new alias for newly expanded paths
                target_alias = f"n{alias_counter}"
                alias_counter += 1

                # Add node and relationship to graph
                if target_alias not in graph.nodes:
                    # Create a default QueryNode when expanding
                    new_node = QueryNode(label=target_type, var_name=target_alias)
                    # Add default 'FILTER' role? Or leave roles empty for expanded nodes?
                    # Let's leave roles empty unless explicitly needed later.
                    # new_node.add_fine_grained_role(FineGrainedNodeRole.FILTER) # Example if default needed
                    graph.add_entity(target_alias, new_node)  # Pass the QueryNode

                # Add relationship (Graph add_relationship checks if nodes exist)
                try:
                    graph.add_relationship(current_alias, relationship_id, target_alias)
                except KeyError as e:
                    logger.error(f"Error adding relationship during expansion: {e}")
                    continue  # Skip this path if relationship add fails

                # --- Enqueue New Node ---
                if target_alias not in queued_aliases_in_expansion:
                    new_path = (*current_path, target_obj_id_str)
                    queue.append((target_alias, new_path))
                    queued_aliases_in_expansion.add(target_alias)
