import asyncio
from collections.abc import Awaitable, Callable
from enum import Str<PERSON>num
from typing import Any
from uuid import UUID

from pydantic import BaseModel

from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.custom_object_repository import CustomObjectRepository
from salestech_be.db.models.core.constants import TableName
from salestech_be.ree_logging import get_logger
from salestech_be.util.uuid import safe_parse_uuid

logger = get_logger(__name__)


class DomainNodeType(StrEnum):
    # indexing methods already exist for these
    USER = "user"
    ACCOUNT = "account"
    CONTACT = "contact"
    PIPELINE = "pipeline"
    CUSTOM_OBJECT = "custom_object"

    # TODO: indexing methods are in the works
    CUSTOM_ASSOCIATION = "custom_association"
    CONTACT_ACCOUNT_ROLE = "contact_account_role"
    CONTACT_PIPELINE_ROLE = "contact_pipeline_role"

    # do not need by 5/8
    MEETING = "meeting"
    TASK = "task"


DOMAIN_NODE_TYPE_DEPENDENCIES: dict[DomainNodeType, set[DomainNodeType]] = {
    DomainNodeType.CONTACT_ACCOUNT_ROLE: {
        DomainNodeType.ACCOUNT,
        DomainNodeType.CONTACT,
    },
    DomainNodeType.CONTACT_PIPELINE_ROLE: {
        DomainNodeType.CONTACT,
        DomainNodeType.PIPELINE,
    },
}


class DomainNodeRef(BaseModel, frozen=True):
    node_type: DomainNodeType
    node_id: UUID
    organization_id: UUID


MAPPER_FUNCTION = Callable[[dict[str, Any]], Awaitable[list[DomainNodeRef]]]


def single_column_mapper(
    node_type: DomainNodeType, identity_column: str
) -> MAPPER_FUNCTION:
    async def mapper(payload: dict[str, Any]) -> list[DomainNodeRef]:
        organization_id = safe_parse_uuid(payload.get("organization_id"), None)
        node_id = safe_parse_uuid(payload.get(identity_column), None)
        if not organization_id or not node_id:
            return []

        return [
            DomainNodeRef(
                node_type=node_type,
                organization_id=organization_id,
                node_id=node_id,
            )
        ]

    return mapper


def multi_column_mapper(*columns: tuple[DomainNodeType, str]) -> MAPPER_FUNCTION:
    mappers = [
        single_column_mapper(node_type, identity_column)
        for node_type, identity_column in columns
    ]

    async def mapper(payload: dict[str, Any]) -> list[DomainNodeRef]:
        tasks = [m(payload) for m in mappers]
        results = await asyncio.gather(*tasks)
        return [ref for refs in results for ref in refs]

    return mapper


SIMPLE_TABLE_NAME_MAPPER_FUNCTIONS: dict[TableName, MAPPER_FUNCTION] = {
    # simple user tables
    TableName.user: single_column_mapper(
        DomainNodeType.USER,
        "id",
    ),
    TableName.user_organization_association: single_column_mapper(
        DomainNodeType.USER,
        "user_id",
    ),
    TableName.user_organization_preference: single_column_mapper(
        DomainNodeType.USER,
        "user_id",
    ),
    # simple contact tables
    TableName.contact: single_column_mapper(
        DomainNodeType.CONTACT,
        "id",
    ),
    TableName.contact_account_association: multi_column_mapper(
        (DomainNodeType.CONTACT, "contact_id"),
        (DomainNodeType.ACCOUNT, "account_id"),
        (DomainNodeType.CONTACT_ACCOUNT_ROLE, "id"),
    ),
    TableName.contact_account_engagement_association: single_column_mapper(
        DomainNodeType.CONTACT,
        "contact_id",
    ),
    TableName.contact_deal: single_column_mapper(
        DomainNodeType.CONTACT,
        "contact_id",
    ),
    TableName.contact_email: single_column_mapper(
        DomainNodeType.CONTACT,
        "contact_id",
    ),
    TableName.contact_email_account_association: multi_column_mapper(
        (DomainNodeType.CONTACT, "contact_id"),
        (DomainNodeType.ACCOUNT, "account_id"),
    ),
    TableName.contact_pipeline_association: multi_column_mapper(
        (DomainNodeType.CONTACT, "contact_id"),
        (DomainNodeType.PIPELINE, "pipeline_id"),
        (DomainNodeType.CONTACT_PIPELINE_ROLE, "id"),
    ),
    TableName.contact_phone_number: single_column_mapper(
        DomainNodeType.CONTACT,
        "contact_id",
    ),
    TableName.contact_phone_number_account_association: multi_column_mapper(
        (DomainNodeType.CONTACT, "contact_id"),
        (DomainNodeType.ACCOUNT, "account_id"),
    ),
    TableName.contact_pipeline_association: multi_column_mapper(
        (DomainNodeType.CONTACT, "contact_id"),
        (DomainNodeType.PIPELINE, "pipeline_id"),
    ),
    # simple account tables
    TableName.account: single_column_mapper(
        DomainNodeType.ACCOUNT,
        "id",
    ),
    # simple pipeline tables
    TableName.pipeline: single_column_mapper(
        DomainNodeType.PIPELINE,
        "id",
    ),
    TableName.pipeline_qualification_property: single_column_mapper(
        DomainNodeType.PIPELINE,
        "pipeline_id",
    ),
    # simple custom object tables
    TableName.cobject_metadata: single_column_mapper(
        DomainNodeType.CUSTOM_OBJECT,
        "id",
    ),
    TableName.cobject_index: single_column_mapper(
        DomainNodeType.CUSTOM_OBJECT,
        "id",
    ),
}

PARENT_OBJECT_NAME_TO_DOMAIN_NODE_TYPE: dict[
    ExtendableStandardObject, DomainNodeType
] = {
    ExtendableStandardObject.account: DomainNodeType.ACCOUNT,
    ExtendableStandardObject.contact: DomainNodeType.CONTACT,
    ExtendableStandardObject.task: DomainNodeType.TASK,
    ExtendableStandardObject.meeting: DomainNodeType.MEETING,
    ExtendableStandardObject.pipeline: DomainNodeType.PIPELINE,
    # ExtendableStandardObject.comment: DomainNodeType.COMMENT,
}


class DomainMapper:
    def __init__(
        self,
        account_repository: AccountRepository,
        contact_repository: ContactRepository,
        custom_object_repository: CustomObjectRepository,
    ) -> None:
        self._account_repository = account_repository
        self._contact_repository = contact_repository
        self._custom_object_repository = custom_object_repository
        self._table_mappers: dict[TableName, MAPPER_FUNCTION] = {
            **SIMPLE_TABLE_NAME_MAPPER_FUNCTIONS,
            TableName.address: self.address_to_node_references_mapper,
            TableName.cobject_data: self.cobject_data_to_node_references_mapper,
            # NOTE: these tables don't have any in-use table relationships, so not enabling for now:
            # TableName.phone_number: self.phone_number_to_node_references_mapper,
            # NOTE: these have very wide fan-out when updated, so not enabling until we have a way to handle
            # TableName.cobject_metadata: self.cobject_metadata_to_node_references_mapper,
            # TableName.cobject_index: self.cobject_index_to_node_references_mapper,
        }

    async def address_to_node_references_mapper(
        self, payload: dict[str, Any]
    ) -> list[DomainNodeRef]:
        organization_id = safe_parse_uuid(payload.get("organization_id"), None)
        address_id = safe_parse_uuid(payload.get("id"), None)
        if not organization_id or not address_id:
            return []

        accounts = await self._account_repository.find_accounts_by_address(
            address_ids=[address_id],
            organization_id=organization_id,
            exclude_deleted_or_archived=True,
        )
        contacts = await self._contact_repository.find_contacts_by_address(
            address_ids=[address_id],
            organization_id=organization_id,
            exclude_deleted_or_archived=True,
        )

        accounts_refs = [
            DomainNodeRef(
                node_type=DomainNodeType.ACCOUNT,
                organization_id=a.organization_id,
                node_id=a.id,
            )
            for a in accounts
        ]
        contacts_refs = [
            DomainNodeRef(
                node_type=DomainNodeType.CONTACT,
                organization_id=c.organization_id,
                node_id=c.id,
            )
            for c in contacts
        ]
        return accounts_refs + contacts_refs

    async def cobject_data_to_node_references_mapper(
        self, payload: dict[str, Any]
    ) -> list[DomainNodeRef]:
        organization_id = safe_parse_uuid(payload.get("organization_id"), None)
        cobject_data_id = safe_parse_uuid(payload.get("id"), None)

        if not organization_id or not cobject_data_id:
            return []

        refs = await self._custom_object_repository.list_custom_object_reference_ids(
            organization_id=organization_id,
            cobject_data_ids=[cobject_data_id],
        )

        results: list[DomainNodeRef] = []
        for ref in refs:
            if ref.parent_object_name:
                node_id = ref.extension_id
                node_type = PARENT_OBJECT_NAME_TO_DOMAIN_NODE_TYPE.get(
                    ref.parent_object_name
                )
                if node_type and node_id:
                    results.append(
                        DomainNodeRef(
                            organization_id=organization_id,
                            node_type=node_type,
                            node_id=node_id,
                        )
                    )
            elif ref.cobject_data_id:
                results.append(
                    DomainNodeRef(
                        organization_id=organization_id,
                        node_type=DomainNodeType.CUSTOM_OBJECT,
                        node_id=ref.cobject_data_id,
                    )
                )

        return results

    async def get_node_references(
        self, table_name: str, payload: dict[str, Any]
    ) -> list[DomainNodeRef]:
        try:
            table_name_enum = TableName(table_name)
            if table_name_enum in self._table_mappers:
                return await self._table_mappers[table_name_enum](payload)
        except ValueError:
            logger.warning("Invalid table name", table_name=table_name)

        return []
