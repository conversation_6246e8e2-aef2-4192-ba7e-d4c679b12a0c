from enum import StrEnum

from pydantic_settings import BaseSettings, SettingsConfigDict


class TopicNames(StrEnum):
    CHANGE_EVENTS = "debezium.salestech_be.change_events"
    CHANGE_EVENTS_PARTITIONED = "debezium.salestech_be.change_events_partitioned"
    INDEX_EVENTS = "salestech_be.falkor.index_events"


class KafkaConsumerSettings(BaseSettings):
    topic: str
    group_id: str
    batch_size: int = 200
    batch_timeout: float = 0.1


class CDCEventSettings(BaseSettings):
    """Event settings.

    These parameters can be configured
    with environment variables.
    """

    model_config = SettingsConfigDict(
        extra="allow",
        env_file=".env",
        env_prefix="SALESTECH_DEBEZIUM_",
        env_file_encoding="utf-8",
    )

    partitioner_from_topic: str = TopicNames.CHANGE_EVENTS
    partitioner_to_topic: str = TopicNames.CHANGE_EVENTS_PARTITIONED
    partitioner_group_id: str = "cdc_partitioner"
    partitioner_batch_size: int = 1000
    partitioner_batch_timeout: float = 0.1

    fan_out_from_topic: str = TopicNames.CHANGE_EVENTS_PARTITIONED
    fan_out_to_topic: str = TopicNames.INDEX_EVENTS
    fan_out_group_id: str = "falkor_fan_out"
    fan_out_batch_size: int = 200
    fan_out_batch_timeout: float = 0.1

    indexer_from_topic: str = TopicNames.INDEX_EVENTS
    indexer_group_id: str = "falkor_indexer"
    indexer_batch_size: int = 1000
    indexer_concurrency_limit: int = 10


cdc_event_settings = CDCEventSettings()
