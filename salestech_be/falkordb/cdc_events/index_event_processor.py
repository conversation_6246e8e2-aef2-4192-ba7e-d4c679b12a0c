import asyncio
from enum import StrEnum
from uuid import UUID

from aiokafka import TopicPartition
from more_itertools import chunked

from salestech_be.common.stats.metric import custom_metric
from salestech_be.falkordb.cdc_events.domain_mapper import (
    DOMAIN_NODE_TYPE_DEPENDENCIES,
    DomainNodeRef,
    DomainNodeType,
)
from salestech_be.falkordb.indexing_lib import (
    FalkorDBIndexingLib,
)
from salestech_be.integrations.kafka.kafka_consumer import KafkaPartitionProcessor
from salestech_be.integrations.kafka.kafka_model_parser import KafkaModelParser
from salestech_be.integrations.kafka.types import TRecord
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


class MetricNames(StrEnum):
    FALKOR_CDC_INDEX_DELAY = "falkor.cdc.index_delay"


class MetricTags(StrEnum):
    DOMAIN_MODEL = "domain_model"


def group_by_org_id_and_node_type(
    domain_node_refs: list[DomainNodeRef],
) -> dict[UUID, dict[DomainNodeType, set[UUID]]]:
    org_id_and_node_type_to_refs: dict[UUID, dict[DomainNodeType, set[UUID]]] = {}
    for domain_node_ref in domain_node_refs:
        org_id_and_node_type_to_refs.setdefault(
            domain_node_ref.organization_id, {}
        ).setdefault(domain_node_ref.node_type, set()).add(domain_node_ref.node_id)
    return org_id_and_node_type_to_refs


class IndexEventProcessor(KafkaPartitionProcessor):
    def __init__(
        self,
        falkor_indexing_lib: FalkorDBIndexingLib,
        concurrency_limit: int = 10,
    ) -> None:
        super().__init__()
        self._indexing_lib = falkor_indexing_lib
        self._semaphore = asyncio.Semaphore(concurrency_limit)
        self._parser = KafkaModelParser(DomainNodeRef)

    async def index(  # noqa: C901, PLR0912
        self,
        organization_id: UUID,
        node_type: DomainNodeType,
        node_ids: list[UUID],
        node_type_futures: dict[DomainNodeType, asyncio.Future[None]],
    ) -> None:
        dependencies = DOMAIN_NODE_TYPE_DEPENDENCIES.get(node_type, set())
        for dependency in dependencies:
            if dependency in node_type_futures:
                await node_type_futures[dependency]

        try:
            async with (
                self._semaphore,
                custom_metric.timer(
                    metric_name=MetricNames.FALKOR_CDC_INDEX_DELAY,
                    tags=[f"{MetricTags.DOMAIN_MODEL}:{node_type}"],
                ),
            ):
                for chunk in chunked(node_ids, 100):
                    match node_type:
                        case DomainNodeType.USER:
                            await self._indexing_lib.index_users(
                                organization_id=organization_id,
                                user_ids=chunk,
                                update_edges=True,
                            )
                        case DomainNodeType.ACCOUNT:
                            await self._indexing_lib.index_accounts(
                                organization_id=organization_id,
                                account_ids=chunk,
                                update_edges=True,
                            )
                        case DomainNodeType.CONTACT:
                            await self._indexing_lib.index_contacts(
                                organization_id=organization_id,
                                contact_ids=chunk,
                                update_edges=True,
                            )
                        case DomainNodeType.PIPELINE:
                            await self._indexing_lib.index_pipelines(
                                organization_id=organization_id,
                                pipeline_ids=chunk,
                                update_edges=True,
                            )
                        case DomainNodeType.CUSTOM_OBJECT:
                            await self._indexing_lib.index_custom_objects(
                                organization_id=organization_id,
                                cobject_data_ids=chunk,
                                update_edges=True,
                            )
                        case DomainNodeType.CONTACT_ACCOUNT_ROLE:
                            await self._indexing_lib.index_contact_account_roles(
                                organization_id=organization_id,
                                association_ids=chunk,
                                update_edges=True,
                            )
                        case DomainNodeType.CONTACT_PIPELINE_ROLE:
                            await self._indexing_lib.index_contact_pipeline_roles(
                                organization_id=organization_id,
                                association_ids=chunk,
                                update_edges=True,
                            )
                        case _:
                            logger.warning(
                                "unsupported node type",
                                node_type=node_type,
                                organization_id=organization_id,
                                node_ids=chunk,
                            )
        except Exception as e:
            # intentionally skipping all errors from the indexing lib to prevent HOL blocking
            logger.error(
                "Error indexing domain nodes",
                exc_info=e,
                node_type=node_type,
                organization_id=organization_id,
                node_ids=node_ids,
            )
        finally:
            if node_type in node_type_futures:
                node_type_futures[node_type].set_result(None)

    async def index_organization(
        self,
        organization_id: UUID,
        node_type_to_node_ids: dict[DomainNodeType, set[UUID]],
    ) -> None:
        node_type_futures: dict[DomainNodeType, asyncio.Future[None]] = {}
        for node_type in node_type_to_node_ids:
            node_type_futures.setdefault(node_type, asyncio.Future[None]())

        async with asyncio.TaskGroup() as tg:
            for node_type, node_ids in node_type_to_node_ids.items():
                tg.create_task(
                    self.index(
                        organization_id=organization_id,
                        node_type=node_type,
                        node_ids=list(node_ids),
                        node_type_futures=node_type_futures,
                    )
                )

    async def process(self, partition: TopicPartition, records: list[TRecord]) -> None:
        domain_node_refs: list[DomainNodeRef] = [
            model
            for record in records
            if (model := self._parser.parse_record(record)) is not None
        ]

        grouped_refs = group_by_org_id_and_node_type(domain_node_refs)

        async with asyncio.TaskGroup() as tg:
            for organization_id, node_type_to_node_ids in grouped_refs.items():
                tg.create_task(
                    self.index_organization(
                        organization_id=organization_id,
                        node_type_to_node_ids=node_type_to_node_ids,
                    )
                )
