from enum import StrEnum
from time import time_ns

from aiokafka import TopicPartition

from salestech_be.common.stats.metric import custom_metric
from salestech_be.falkordb.cdc_events.domain_mapper import (
    DomainMapper,
    DomainNodeRef,
)
from salestech_be.falkordb.cdc_events.index_event_producer import IndexEventProducer
from salestech_be.falkordb.cdc_events.types import DebeziumCDCEvent
from salestech_be.integrations.kafka.kafka_consumer import KafkaPartitionProcessor
from salestech_be.integrations.kafka.kafka_model_parser import KafkaModelParser
from salestech_be.integrations.kafka.types import TRecord
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


class MetricNames(StrEnum):
    DEBEZIUM_PUBLISH_DELAY = "debezium.publish_delay"
    FALKOR_CDC_CONSUME_DELAY = "falkor.cdc.consume_delay"
    FALKOR_CDC_FAN_OUT_DELAY = "falkor.cdc.fan_out_delay"
    FALKOR_CDC_PUBLISH_DELAY = "falkor.cdc.publish_delay"


class MetricTags(StrEnum):
    TABLE_NAME = "table_name"
    DOMAIN_MODEL = "domain_model"


class CDCEventProcessor(KafkaPartitionProcessor):
    def __init__(
        self,
        domain_mapper: DomainMapper,
        index_producer: IndexEventProducer,
    ) -> None:
        super().__init__()
        self._domain_mapper = domain_mapper
        self._index_producer = index_producer
        self._parser = KafkaModelParser(DebeziumCDCEvent)

    async def start(self) -> None:
        await self._index_producer.start()

    async def stop(self) -> None:
        await self._index_producer.stop()

    async def fan_out_cdc_to_index_events(
        self, events: list[DebeziumCDCEvent]
    ) -> list[tuple[DebeziumCDCEvent, list[DomainNodeRef]]]:
        all_node_updates: list[tuple[DebeziumCDCEvent, list[DomainNodeRef]]] = []

        for cdc_event in events:
            logger.debug("Processing CDC event", cdc_event=cdc_event)

            table_name = cdc_event.source.table
            payload = cdc_event.after or cdc_event.before
            if not payload:
                logger.warning("No payload found for CDC event", cdc_event=cdc_event)
                continue

            async with custom_metric.timer(
                metric_name=MetricNames.FALKOR_CDC_FAN_OUT_DELAY,
                tags=[
                    f"{MetricTags.TABLE_NAME}:{table_name}",
                ],
            ):
                domain_node_refs = await self._domain_mapper.get_node_references(
                    table_name=table_name, payload=payload
                )
                all_node_updates.append((cdc_event, domain_node_refs))

        return all_node_updates

    async def process(self, partition: TopicPartition, records: list[TRecord]) -> None:
        cdc_events: list[DebeziumCDCEvent] = []
        for record in records:
            if record.value is None:
                continue

            cdc_event = self._parser.parse_record(record)
            if cdc_event is None:
                logger.warning("Invalid CDC event", record=record)
                continue

            cdc_events.append(cdc_event)

            if cdc_event.ts_ns > 0 and cdc_event.source.ts_ns > 0:
                custom_metric.timing(
                    metric_name=MetricNames.DEBEZIUM_PUBLISH_DELAY,
                    value=(cdc_event.ts_ns - cdc_event.source.ts_ns) / 1e6,
                    tags=[
                        f"{MetricTags.TABLE_NAME}:{cdc_event.source.table}",
                    ],
                )
                custom_metric.timing(
                    metric_name=MetricNames.FALKOR_CDC_CONSUME_DELAY,
                    value=(time_ns() - cdc_event.ts_ns) / 1e6,
                    tags=[
                        f"{MetricTags.TABLE_NAME}:{cdc_event.source.table}",
                    ],
                )

        domain_node_refs_per_event = await self.fan_out_cdc_to_index_events(cdc_events)

        all_domain_node_refs = [
            domain_node_ref
            for _, domain_node_refs in domain_node_refs_per_event
            for domain_node_ref in domain_node_refs
        ]
        await self._index_producer.send_events(all_domain_node_refs)

        for cdc_event, domain_node_refs in domain_node_refs_per_event:
            for domain_node_ref in domain_node_refs:
                custom_metric.timing(
                    metric_name=MetricNames.FALKOR_CDC_PUBLISH_DELAY,
                    value=(time_ns() - cdc_event.ts_ns) / 1e6,
                    tags=[
                        f"{MetricTags.TABLE_NAME}:{cdc_event.source.table}",
                        f"{MetricTags.DOMAIN_MODEL}:{domain_node_ref.node_type}",
                    ],
                )
