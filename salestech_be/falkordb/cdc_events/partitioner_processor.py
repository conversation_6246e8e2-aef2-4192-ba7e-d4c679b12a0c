from aiokafka import TopicPartition

from salestech_be.db.models.core.constants import TableName
from salestech_be.falkordb.cdc_events.types import DebeziumCDCEvent
from salestech_be.integrations.kafka.kafka_consumer import KafkaPartitionProcessor
from salestech_be.integrations.kafka.kafka_manager import MSKProducerFactory
from salestech_be.integrations.kafka.kafka_model_parser import KafkaModelParser
from salestech_be.integrations.kafka.types import TRecord
from salestech_be.ree_logging import get_logger
from salestech_be.util.uuid import safe_parse_uuid_or_none

logger = get_logger(__name__)


class PartitionProcessor(KafkaPartitionProcessor):
    def __init__(self, topic: str) -> None:
        self.topic = topic
        self.producer = MSKProducerFactory.create_raw()
        self.parser = KafkaModelParser(DebeziumCDCEvent)

    async def start(self) -> None:
        logger.info("starting org partition processor")
        await self.producer.start()

    async def stop(self) -> None:
        logger.info("stopping org partition processor")
        await self.producer.flush()
        await self.producer.stop()

    def get_record_partition_key(self, record: TRecord) -> bytes | None:
        cdc_event = self.parser.parse_record(record)
        if cdc_event:
            payload = cdc_event.after or cdc_event.before
            if payload:
                uuid_str = payload.get("organization_id", None)
                if cdc_event.source.table == TableName.organization:
                    uuid_str = payload.get("id", None)

                org_id = safe_parse_uuid_or_none(uuid_str)
                if org_id:
                    return str(org_id).encode("utf-8")
        return record.key  # type: ignore[no-any-return]

    async def process(self, partition: TopicPartition, records: list[TRecord]) -> None:
        for record in records:
            await self.producer.send(
                topic=self.topic,
                key=self.get_record_partition_key(record),
                value=record.value,
                headers=list(record.headers),
            )
        await self.producer.flush()
