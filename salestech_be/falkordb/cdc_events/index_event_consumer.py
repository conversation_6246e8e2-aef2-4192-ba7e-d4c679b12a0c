import asyncio
import signal
import sys

import sentry_sdk
from sentry_sdk.integrations.asyncio import AsyncioIntegration
from sentry_sdk.integrations.asyncpg import AsyncPGIntegration
from sentry_sdk.integrations.httpx import HttpxIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration

from salestech_be.event.database import get_db_engine_for_event_handling
from salestech_be.falkordb.cdc_events.index_event_processor import (
    IndexEventProcessor,
)
from salestech_be.falkordb.cdc_events.settings import cdc_event_settings
from salestech_be.falkordb.falkordb_client import get_falkordb_client
from salestech_be.falkordb.indexing_lib import get_falkordb_indexing_lib
from salestech_be.integrations.kafka.kafka_consumer import KafkaConsumer
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

logger = get_logger(__name__)


async def run_until_sigint() -> None:
    db_engine = await get_db_engine_for_event_handling()
    falkordb_client = get_falkordb_client()
    falkor_indexing_lib = get_falkordb_indexing_lib(db_engine, falkordb_client)

    processor = IndexEventProcessor(
        falkor_indexing_lib=falkor_indexing_lib,
        concurrency_limit=cdc_event_settings.indexer_concurrency_limit,
    )

    consumer = KafkaConsumer(
        topic=cdc_event_settings.indexer_from_topic,
        group_id=cdc_event_settings.indexer_group_id,
        batch_size=cdc_event_settings.indexer_batch_size,
        processor=processor,
    )

    loop = asyncio.get_running_loop()
    async with asyncio.TaskGroup() as tg:
        task = tg.create_task(consumer.run_until_stopped())

        async def stop_consumer() -> None:
            logger.info("received SIGINT stopping consumer")
            try:
                consumer.stop()
                await asyncio.wait_for(task, timeout=30.0)
            except TimeoutError:
                sys.exit(0)

        loop.add_signal_handler(signal.SIGINT, lambda: tg.create_task(stop_consumer()))


async def main() -> None:
    logger.info("Starting event processor", cdc_event_settings=cdc_event_settings)

    # Initialize Sentry
    sentry_sdk.init(
        dsn=settings.sentry_dsn,
        traces_sample_rate=settings.sentry_traces_sample_rate,
        profiles_sample_rate=settings.sentry_profiles_sample_rate,
        environment=settings.environment,
        integrations=[
            SqlalchemyIntegration(),
            AsyncioIntegration(),
            HttpxIntegration(),
            AsyncPGIntegration(),
        ],
    )

    await run_until_sigint()


if __name__ == "__main__":
    asyncio.run(main())
