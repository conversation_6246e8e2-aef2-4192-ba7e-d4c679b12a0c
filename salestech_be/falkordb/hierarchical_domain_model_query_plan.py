"""
Hierarchical Domain Model Query Plan.

Generates a query plan focused on first filtering/sorting/paginating
the core domain entity subgraph before enriching with nested fields.
"""

import re
from collections import deque
from typing import NamedTuple

from salestech_be.common.type.metadata.schema import (
    FieldDescriptor,
    ObjectDescriptor,
    OrganizationSchemaDescriptor,  # Added import
)
from salestech_be.falkordb.query_analyzer import QueryDependencyGraph
from salestech_be.falkordb.query_plan import (
    FilterExpressionBuilder,
    FilterOperation,
    MatchOperation,
    OrderByOperation,
    PaginationOperation,
    QueryNode,
    QueryPlan,
    QueryRelationship,
    RelationshipMatchOperation,
    ReturnOperation,
    WithOperation,
    process_composite_structure_with_null_checks,
)
from salestech_be.falkordb.schema_aware_domain_model_query_plan import (
    sanitize_property_reference,
)
from salestech_be.falkordb.schema_registry import (
    FieldTypeInfo,
    SchemaRegistry,
    SchemaValidationError,
)
from salestech_be.ree_logging import get_logger

logger = get_logger()


# Corrected NamedTuple definition using functional syntax
class ProjectionContext(NamedTuple):
    current_path_object_ids: set[str]
    depth: int


MAX_PROJECTION_DEPTH = 10


class HierarchicalDomainModelQueryPlan(QueryPlan):
    """
    Represents a query plan using a hierarchical, two-stage approach.
    Phase 1: Filter/Sort/Paginate core domain entities and required related nodes.
    Phase 2 (Future): Enrich results with nested fields.
    """

    def __init__(
        self,
        root_object_descriptor: ObjectDescriptor | None = None,
        schema: OrganizationSchemaDescriptor | None = None,
        registry: SchemaRegistry | None = None,
    ) -> None:
        super().__init__()
        self.root_object_descriptor = root_object_descriptor
        self.schema = schema
        # Ensure registry is created if not provided
        self.registry = registry if registry else SchemaRegistry(self.schema)
        if not self.registry.schema and schema:
            # Ensure registry has the schema if it was created without one
            self.registry.schema = schema

        # Add any specific state needed for this planner
        self.root_alias = "n0"  # Convention

        # State needed for bottom-up projection (Phase 2)
        self.entity_projection_vars: dict[str, str] = {}
        self.processed_nodes: set[str] = set()
        self.node_dependencies: dict[str, set[str]] = {}

    # Placeholder for future enrichment phase logic
    def build_enrichment_phase(self) -> None:
        # This would involve taking the results of Phase 1 (root node IDs/aliases)
        # and generating MATCH/COLLECT/RETURN clauses to fetch and structure nested data.
        logger.warning("Phase 2 enrichment is not yet implemented.")

    # --- Projection logic copied/adapted from SchemaAwareDomainModelQueryPlan ---

    def _build_field_projection(
        self,
        node_alias: str,
        field: FieldDescriptor,
        field_info: FieldTypeInfo,
    ) -> str:
        """Build projection for a specific field based on its type (primitive/primitive collection)."""
        if not self.registry:
            logger.error("Schema registry not available in _build_field_projection")
            return ""

        simple_field_name = self.registry.extract_field_name(field.field_identifier)
        sanitized_prop_access = sanitize_property_reference(simple_field_name)
        sanitized_field_key = sanitize_property_reference(simple_field_name)

        if field_info.is_primitive or field_info.is_custom_field:
            logger.debug(
                f"Primitive or Custom Field Projection Debug: Analyzing field {field.field_identifier} for node {node_alias} field_info: {field_info}"
            )
            return f"{sanitized_field_key}: {node_alias}.{sanitized_prop_access}"
        if field_info.is_collection and not field_info.nested_descriptor:
            return f"{sanitized_field_key}: {node_alias}.{sanitized_prop_access}"
        return ""

    def _build_leaf_node_projection(  # noqa: C901
        self, node_alias: str, all_aliases: set[str]
    ) -> None:
        """Build projection for a leaf node in the bottom-up process."""
        if not self.schema or not self.registry or not self.root_object_descriptor:
            raise SchemaValidationError(
                "Schema, registry, and root descriptor required for leaf node projection"
            )

        # Ensure entity_projection_vars is initialized for this node_alias
        if node_alias not in self.entity_projection_vars:
            entity_type = self.nodes[node_alias].label
            sanitized_label_for_var = (
                "_" + re.sub(r"[^a-zA-Z0-9_]", "_", entity_type).lower()
            )
            self.entity_projection_vars[node_alias] = (
                f"{sanitized_label_for_var}_entity_{node_alias[1:]}"
            )

        projection_var = self.entity_projection_vars[node_alias]
        node_info = self.nodes.get(node_alias)
        if not node_info:
            raise ValueError(f"Node alias {node_alias} not found in plan nodes.")
        node_descriptor = self.registry.get_object_descriptor(node_info.label)

        field_projections = []
        if not node_descriptor:
            logger.warning(
                f"No descriptor for leaf node {node_alias} ({node_info.label}), projecting all properties."
            )
            map_projection = f'{node_alias} {{.*}}"'
        else:
            for field in node_descriptor.fields:
                field_info = self.registry.analyze_field_type(node_descriptor, field)
                logger.debug(
                    f"Leaf Node Projection Debug: Analyzing field {field.field_identifier} for node {node_alias} field_info: {field_info}"
                )
                if (field_info.is_primitive or field_info.is_custom_field) or (
                    field_info.is_collection and not field_info.nested_descriptor
                ):
                    proj = self._build_field_projection(node_alias, field, field_info)
                    if proj:
                        field_projections.append(proj)
                elif field_info.is_nested or (
                    field_info.is_collection and field_info.nested_descriptor
                ):
                    simple_field_name = self.registry.extract_field_name(
                        field.field_identifier
                    )
                    sanitized_field_key = sanitize_property_reference(simple_field_name)
                    field_projections.append(f"{sanitized_field_key}: []")

            field_projections.sort(
                key=lambda proj_str: proj_str.split(":", 1)[0].strip()
            )

            if not field_projections:
                logger.warning(
                    f"No primitive fields for leaf {node_alias} ({node_info.label}), projecting all."
                )
                map_projection = f'{node_alias} {{.*}}"'
            else:
                map_lines = []
                for i, proj in enumerate(field_projections):
                    comma = "," if i < len(field_projections) - 1 else ""
                    map_lines.append(f"    {proj}{comma}")
                map_projection = "{" + "\n".join(map_lines) + "\n}"

        projection_definition = f"CASE WHEN {node_alias} IS NULL THEN NULL ELSE {map_projection} END AS {projection_var}"

        # Get projection vars from already processed nodes (sibling branches)
        processed_before = self.processed_nodes
        other_processed_vars = sorted(
            [
                self.entity_projection_vars[p]
                for p in processed_before
                if p != node_alias
                and p in self.entity_projection_vars  # Exclude self, ensure var exists
            ]
        )

        # Construct the WITH clause
        carry_forward_vars = [
            projection_definition,  # Defines projection_var
            node_alias,  # Needed for CASE WHEN check
            *other_processed_vars,  # Carry forward results from completed sibling branches
        ]
        carry_forward_vars = list(
            dict.fromkeys(carry_forward_vars)
        )  # Remove potential duplicates

        # Add WITH clause
        self.add_operation(WithOperation(carry_forward_vars, {}))

        # Mark as processed *after* the projection operation is added
        self.processed_nodes.add(node_alias)

    def _build_internal_node_projection(  # noqa: C901, PLR0912, PLR0915
        self,
        node_alias: str,
        outgoing_rels: list[QueryRelationship],
        all_aliases: set[str],
    ) -> None:
        """Build projection for an internal node in the bottom-up process."""
        if not self.schema or not self.registry or not self.root_object_descriptor:
            raise SchemaValidationError(
                "Schema, registry, and root descriptor required"
            )

        if node_alias not in self.entity_projection_vars:
            entity_type = self.nodes[node_alias].label
            sanitized_label_for_var = (
                "_" + re.sub(r"[^a-zA-Z0-9_]", "_", entity_type).lower()
            )
            self.entity_projection_vars[node_alias] = (
                f"{sanitized_label_for_var}_entity_{node_alias[1:]}"
            )

        projection_var = self.entity_projection_vars[node_alias]
        node_info = self.nodes.get(node_alias)
        if not node_info:
            raise ValueError(f"Node {node_alias} not found.")
        node_descriptor = self.registry.get_object_descriptor(node_info.label)

        processed_rel_fields = set()
        combined_projections = []

        if not node_descriptor:
            logger.warning(
                f"No descriptor for internal node {node_alias} ({node_info.label}), projecting only relationships."
            )
        else:
            for field in node_descriptor.fields:
                field_info = self.registry.analyze_field_type(node_descriptor, field)
                simple_field_name = self.registry.extract_field_name(
                    field.field_identifier
                )
                sanitized_field_key = sanitize_property_reference(simple_field_name)

                if (field_info.is_primitive or field_info.is_custom_field) or (
                    field_info.is_collection and not field_info.nested_descriptor
                ):
                    proj = self._build_field_projection(node_alias, field, field_info)
                    if proj:
                        combined_projections.append(proj)
                elif field_info.is_nested or (
                    field_info.is_collection and field_info.nested_descriptor
                ):
                    expected_rel_type = simple_field_name
                    processed_rel_fields.add(expected_rel_type)
                    matching_rel = next(
                        (
                            r
                            for r in self.relationships
                            if r.source.var_name == node_alias
                            and r.type_ == expected_rel_type
                        ),
                        None,
                    )

                    if (
                        matching_rel
                        and matching_rel.target.var_name in self.entity_projection_vars
                    ):
                        target_alias = matching_rel.target.var_name
                        target_var = self.entity_projection_vars[target_alias]
                        proj = f"{sanitized_field_key}: COLLECT(CASE WHEN {target_alias} IS NULL THEN [] ELSE {target_var} END)"
                        logger.debug(
                            f"Projection Debug: Added COLLECT projection: {proj}"
                        )  # Log success
                        combined_projections.append(proj)
                    else:
                        logger.warning(
                            f"Projection Debug: Defaulting to empty list for {sanitized_field_key} on {node_alias}."
                        )  # Log fallback
                        combined_projections.append(f"{sanitized_field_key}: []")

        # Add explicit domain relationships
        for rel in outgoing_rels:
            if (
                rel.type_ not in processed_rel_fields
                and rel.target.var_name in self.entity_projection_vars
            ):
                target_alias = rel.target.var_name
                target_var = self.entity_projection_vars[target_alias]
                sanitized_rel_key = sanitize_property_reference(rel.type_)
                proj = f"{sanitized_rel_key}: COLLECT(CASE WHEN {target_alias} IS NULL THEN [] ELSE {target_var} END)"
                combined_projections.append(proj)

        combined_projections.sort(
            key=lambda proj_str: proj_str.split(":", 1)[0].strip()
        )

        if not combined_projections:
            map_projection = f'{node_alias} {{.*}}"'
        else:
            map_lines = []
            for i, proj in enumerate(combined_projections):
                comma = "," if i < len(combined_projections) - 1 else ""
                map_lines.append(f"    {proj}{comma}")
            map_projection = "{" + "\n".join(map_lines) + "\n}"

        projection_definition = f"CASE WHEN {node_alias} IS NULL THEN NULL ELSE {map_projection} END AS {projection_var}"

        # Get projection vars for direct dependencies (needed inside COLLECT)
        dependency_aliases = self.node_dependencies.get(node_alias, set())
        dependency_projection_vars = sorted(
            [
                self.entity_projection_vars[dep]
                for dep in dependency_aliases
                if dep in self.entity_projection_vars  # Ensure dependency var exists
            ]
        )

        # Get projection vars from already processed nodes EXCLUDING dependencies (sibling branches)
        processed_before = self.processed_nodes
        other_processed_vars = sorted(
            [
                self.entity_projection_vars[p]
                for p in processed_before
                if p != node_alias
                and p not in dependency_aliases
                and p in self.entity_projection_vars
            ]
        )

        # Construct the WITH clause
        carry_forward_vars = [
            projection_definition,  # Defines projection_var
            node_alias,  # Needed for CASE WHEN check
            *dependency_projection_vars,  # Needed for COLLECT
            *other_processed_vars,  # Carry forward results from completed sibling branches
        ]
        carry_forward_vars = list(
            dict.fromkeys(carry_forward_vars)
        )  # Remove potential duplicates

        # Add WITH clause
        self.add_operation(WithOperation(carry_forward_vars, {}))

        # Mark as processed *after* the projection operation is added
        self.processed_nodes.add(node_alias)

    def _build_node_dependency_graph(self) -> dict[str, set[str]]:
        """Build node dependency graph for bottom-up projection order."""
        dependencies: dict[str, set[str]] = {alias: set() for alias in self.nodes}
        for rel in self.relationships:
            if (
                rel.target.var_name in dependencies
                and rel.source.var_name in self.nodes
            ):
                dependencies[rel.target.var_name].add(rel.source.var_name)
        self.node_dependencies = dependencies
        return dependencies

    def _get_node_processing_order(self) -> list[str]:
        """Get node processing order (bottom-up). Uses topological sort."""
        dependency_graph = self._build_node_dependency_graph()
        in_degree = dict.fromkeys(self.nodes, 0)
        adj: dict[str, list[str]] = {node: [] for node in self.nodes}
        for u in self.nodes:
            for v in dependency_graph.get(u, set()):
                if u in adj and v in self.nodes:  # Check both exist
                    adj[v].append(u)
                    in_degree[u] = in_degree.get(u, 0) + 1

        queue = deque([node for node in self.nodes if in_degree.get(node, 0) == 0])
        topological_order = []

        while queue:
            u = queue.popleft()
            topological_order.append(u)
            for v in adj.get(u, []):
                if v in in_degree:  # Ensure neighbor exists before decrementing
                    in_degree[v] -= 1
                    if in_degree[v] == 0:
                        queue.append(v)

        if len(topological_order) != len(self.nodes):
            logger.error(
                "Cycle detected in dependency graph, cannot determine processing order."
            )
            return list(self.nodes.keys())

        return list(reversed(topological_order))

    def _get_outgoing_relationships(self, node_alias: str) -> list[QueryRelationship]:
        """Get outgoing relationships for a node from the plan's relationships."""
        if node_alias not in self.nodes:
            return []
        return [rel for rel in self.relationships if rel.source.var_name == node_alias]

    def build_bottom_up_projection(self, all_aliases_in_graph: set[str]) -> None:  # noqa: C901
        """Build domain model projection using bottom-up strategy."""

        if not self.schema or not self.root_object_descriptor:
            raise SchemaValidationError(
                "Schema and root descriptor required for projection"
            )

        if not self.nodes:
            logger.warning("No nodes available for projection.")
            if not any(isinstance(op, ReturnOperation) for op in self.operations):
                self.add_operation(ReturnOperation("null"))
            return

        processing_order = self._get_node_processing_order()

        self.entity_projection_vars = {}
        for node_alias, node in self.nodes.items():
            entity_type = node.label
            sanitized_label_for_var = (
                "_" + re.sub(r"[^a-zA-Z0-9_]", "_", entity_type).lower()
            )
            var_name = f"{sanitized_label_for_var}_entity_{node_alias[1:]}"
            self.entity_projection_vars[node_alias] = var_name

        self.processed_nodes = set()

        for node_alias in processing_order:
            if node_alias not in self.nodes:
                continue
            if node_alias in self.processed_nodes:
                continue

            outgoing_rels = self._get_outgoing_relationships(node_alias)
            # Pass the full set of aliases for context during projection building
            self._build_internal_node_projection(
                node_alias, outgoing_rels, all_aliases_in_graph
            ) if outgoing_rels else self._build_leaf_node_projection(
                node_alias, all_aliases_in_graph
            )

            self.processed_nodes.add(node_alias)

        # Add final RETURN for the root alias's projection variable if it exists
        if self.root_alias in self.entity_projection_vars:
            final_entity_var = self.entity_projection_vars[self.root_alias]
            last_op = self.operations[-1] if self.operations else None
            if isinstance(last_op, WithOperation) and last_op.variables == [
                final_entity_var
            ]:
                pass
            if isinstance(last_op, ReturnOperation):
                pass  # Already handled by RETURN

            # For now, let's assume we might still need a separate simplifying WITH.
            # Potentially optimize later by modifying the last WITH?

        if not any(isinstance(op, ReturnOperation) for op in self.operations):
            self.add_operation(ReturnOperation(final_entity_var))
        elif not any(isinstance(op, ReturnOperation) for op in self.operations):
            logger.warning(
                f"Root node {self.root_alias} projection variable not found. Adding fallback RETURN."
            )
            self.add_operation(ReturnOperation(self.root_alias))

    # --- END of copied/adapted projection logic ---

    # --- START: New Core Projection Methods (Ensured definitions are before caller) --- #
    def _generate_projection_map_string(
        self,
        current_parent_alias: str,
        parent_descriptor: ObjectDescriptor,
        context: ProjectionContext,
    ) -> str:
        """
        Recursively generates a Cypher map projection string for a given node alias (current_parent_alias),
        using its descriptor. Uses ".*" for primitives and pattern expansion for nested relationships.
        """
        if not self.registry or not self.schema:
            logger.error(
                "Schema registry or schema not available in HierarchicalDomainModelQueryPlan for _generate_projection_map_string."
            )
            return f"{current_parent_alias} {{ .__error__: 'Schema registry not available' }}"

        node_label = self.registry.get_object_name(parent_descriptor)

        if context.depth > MAX_PROJECTION_DEPTH:
            logger.warning(
                f"Max projection depth {MAX_PROJECTION_DEPTH} reached for alias {current_parent_alias} (type {node_label}). Returning minimal projection."
            )
            return f"{current_parent_alias} {{ .__alias__: '{current_parent_alias}', .__type__: '{node_label}' }}"

        current_object_id_str = str(parent_descriptor.object_identifier)
        if current_object_id_str in context.current_path_object_ids:
            logger.warning(
                f"Cycle detected: {parent_descriptor.object_identifier} already in path {context.current_path_object_ids}. Projecting basic info for {current_parent_alias}."
            )
            return f"{current_parent_alias} {{ .__alias__: '{current_parent_alias}', .__type__: '{node_label}', .__cycle_ref_to__: '{current_object_id_str}' }}"

        projection_parts = [".*"]

        for field in parent_descriptor.fields:
            field_info: FieldTypeInfo = self.registry.analyze_field_type(
                parent_descriptor, field
            )

            if not field_info.nested_descriptor:
                continue

            output_field_name = sanitize_property_reference(
                self.registry.extract_field_name(field.field_identifier)
            )
            target_descriptor = field_info.nested_descriptor
            target_label = self.registry.get_object_name(target_descriptor)
            rel_type = sanitize_property_reference(
                self.registry.extract_field_name(field.field_identifier)
            )
            if not rel_type:
                logger.error(
                    f"Could not determine relationship type for nested field '{field.field_identifier}' on {current_parent_alias}. Skipping this field."
                )
                continue

            c_local_alias = (
                f"c_{output_field_name.replace('`', '').lower()[:4]}_{context.depth}"
            )

            # Create new context for recursive call - NamedTuples are immutable
            new_context = ProjectionContext(
                current_path_object_ids=context.current_path_object_ids
                | {current_object_id_str},
                depth=context.depth + 1,
            )
            child_projection_string = self._generate_projection_map_string(
                c_local_alias, target_descriptor, new_context
            )

            if field_info.is_collection:
                projection_parts.append(
                    f"{output_field_name}: [({current_parent_alias})-[:{rel_type}]->({c_local_alias}:{target_label}) | {child_projection_string}]"
                )
            else:
                projection_parts.append(
                    f"{output_field_name}: HEAD([({current_parent_alias})-[:{rel_type}]->({c_local_alias}:{target_label}) | {child_projection_string}])"
                )

        map_content = ", ".join(projection_parts)
        if map_content == ".*":
            return f"{current_parent_alias} {{ .* }}"
        return f"{current_parent_alias} {{ {map_content} }}"

    def _build_node_projection_string_with_pattern_expansion(
        self, node_alias: str, node_descriptor: ObjectDescriptor
    ) -> str:
        """
        Builds the Cypher map projection string for a single node using pattern expansion.
        """
        if not self.registry or not self.schema:
            logger.error(
                "Schema registry or schema not available for _build_node_projection_string_with_pattern_expansion."
            )
            return f"{node_alias} {{ .__error__: 'Schema registry not available' }}"

        initial_context = ProjectionContext(current_path_object_ids=set(), depth=0)

        return self._generate_projection_map_string(
            node_alias, node_descriptor, initial_context
        )

    # --- END: New Core Projection Methods --- #

    # --- START: New Orchestration Logic for Pattern Expansion Projection --- #
    def build_projection_with_pattern_expansion(  # noqa: C901, PLR0912
        self, phase1_aliases_to_project: list[str]
    ) -> None:
        """
        Builds the enrichment phase of the query using the new pattern expansion projection logic.
        """
        if not self.schema or not self.registry:
            logger.error(
                "Schema or registry not available for build_projection_with_pattern_expansion."
            )
            if not any(isinstance(op, ReturnOperation) for op in self.operations):
                self.add_operation(ReturnOperation("null"))
            return

        if not phase1_aliases_to_project:
            logger.warning(
                "No Phase 1 aliases provided for projection. Adding RETURN null."
            )
            if not any(isinstance(op, ReturnOperation) for op in self.operations):
                self.add_operation(ReturnOperation("null"))
            return

        current_run_projection_vars: dict[str, str] = {}
        aliases_to_carry_in_with = set(phase1_aliases_to_project)
        generated_projection_var_names = []

        for node_alias in phase1_aliases_to_project:
            if node_alias not in self.nodes:
                logger.warning(
                    f"Alias '{node_alias}' from phase1_aliases_to_project not in plan.nodes. Skipping its projection."
                )
                aliases_to_carry_in_with.discard(node_alias)
                continue

            node_descriptor = self.registry.get_object_descriptor(
                self.nodes[node_alias].label
            )
            if not node_descriptor:
                logger.warning(
                    f"No descriptor for alias '{node_alias}' (label '{self.nodes[node_alias].label}'). Projecting as null."
                )
                projection_var_name = f"_projection_var_{node_alias}"
                current_run_projection_vars[node_alias] = projection_var_name
                generated_projection_var_names.append(projection_var_name)

                with_elements = [f"null AS {projection_var_name}"]
                with_elements.extend(sorted(aliases_to_carry_in_with - {node_alias}))
                with_elements.extend(
                    sorted(
                        [
                            pv
                            for pv in generated_projection_var_names
                            if pv != projection_var_name
                        ]
                    )
                )
                self.add_operation(WithOperation(sorted(set(with_elements))))
                aliases_to_carry_in_with.discard(node_alias)
                continue

            map_projection_string = (
                self._build_node_projection_string_with_pattern_expansion(
                    node_alias, node_descriptor
                )
            )

            object_name_for_var = self.registry.get_object_name(node_descriptor).lower()
            projection_var_name = (
                f"_{object_name_for_var}_entity_{node_alias.removeprefix('n')}"
            )
            current_run_projection_vars[node_alias] = projection_var_name
            generated_projection_var_names.append(projection_var_name)

            projection_definition = f"CASE WHEN {node_alias} IS NULL THEN null ELSE {map_projection_string} END AS {projection_var_name}"

            with_elements_set = {projection_definition, node_alias}
            for p1_alias_to_carry in aliases_to_carry_in_with:
                if p1_alias_to_carry != node_alias:
                    with_elements_set.add(p1_alias_to_carry)

            for pv_name in generated_projection_var_names:
                if pv_name != projection_var_name:
                    with_elements_set.add(pv_name)

            self.add_operation(WithOperation(sorted(with_elements_set)))
            aliases_to_carry_in_with.discard(node_alias)

        final_return_vars = sorted(
            [
                pv_name
                for alias, pv_name in current_run_projection_vars.items()
                if alias in phase1_aliases_to_project
            ]
        )

        if final_return_vars:
            self.add_operation(
                ReturnOperation(", ".join(final_return_vars))
            )  # Join into a single string if multiple
        elif not any(isinstance(op, ReturnOperation) for op in self.operations):
            logger.warning(
                "No projection variables were generated by build_projection_with_pattern_expansion. Adding RETURN null."
            )
            self.add_operation(ReturnOperation("null"))

    # --- END: New Orchestration Logic --- #


def create_hierarchical_domain_model_query_plan(  # noqa: C901, PLR0912, PLR0915
    graph: QueryDependencyGraph,
    entity_type: str,
    schema: OrganizationSchemaDescriptor,
    offset: int = 0,
    limit: int = 20,
    enrich_results: bool = True,  # Option to control Phase 2
) -> HierarchicalDomainModelQueryPlan:
    """
    Factory method to create a hierarchical query plan.
    Phase 1: Handles domain graph matching, filtering, sorting, pagination.
    Phase 2 (optional): Enriches results using bottom-up projection based on schema.
    """
    logger.debug(
        f"[HierarchicalBuilder] Received graph.field_types: {getattr(graph, 'field_types', 'Attribute missing')}"
    )

    if not schema:
        raise ValueError("Schema is required for HierarchicalDomainModelQueryPlan")

    registry = SchemaRegistry(schema)
    root_object_descriptor = registry.get_object_descriptor(entity_type)
    if not root_object_descriptor:
        raise SchemaValidationError(
            f"Root object type '{entity_type}' not found in schema."
        )

    plan = HierarchicalDomainModelQueryPlan(root_object_descriptor, schema, registry)
    root_alias = plan.root_alias

    # --- Initial Setup: Copy full graph structure to plan ---
    all_aliases_in_graph = set()
    temp_nodes = {}
    # Iterate over nodes dictionary which now contains QueryNode objects
    for alias, node_obj in graph.nodes.items():
        entity_type_name = node_obj.label  # Get label from QueryNode
        node_descriptor = registry.get_object_descriptor(entity_type_name)
        allowed_non_object_types = {"select_list_value"}
        if not node_descriptor and entity_type_name not in allowed_non_object_types:
            logger.warning(
                f"Entity type '{entity_type_name}' for alias '{alias}' not in schema. Skipping."
            )
            continue
        # Create a new QueryNode for the plan, potentially copying roles if needed later
        node = QueryNode(
            entity_type_name, alias, fine_grained_roles=node_obj.fine_grained_roles
        )  # Copy roles
        temp_nodes[alias] = node
        all_aliases_in_graph.add(alias)

    if root_alias not in temp_nodes:
        raise ValueError(
            f"Root node '{root_alias}' for entity type '{entity_type}' could not be added."
        )

    # Add valid nodes to plan
    for node in temp_nodes.values():
        plan.add_node(node)

    # Build and store *all* relationships from the input graph
    plan.relationships = []  # Reset relationships
    for source_alias, rel_set in graph.entity_relationships.items():
        if source_alias not in plan.nodes:
            continue
        source_node = plan.nodes[source_alias]
        source_descriptor = registry.get_object_descriptor(source_node.label)
        for rel_id, target_alias in rel_set:
            if target_alias not in plan.nodes:
                continue
            target_node = plan.nodes[target_alias]
            direction = registry.get_relationship_direction(source_descriptor, rel_id)
            relationship = QueryRelationship(
                source_node, target_node, rel_id, direction
            )
            plan.add_relationship(relationship)

    # --- Phase 1: Match based on roles, Filter, Sort, Paginate ---

    # 1. Determine necessary aliases for Phase 1 (FILTER/SORT roles)
    phase1_required_aliases = {root_alias}  # Always include root
    phase1_rel_paths_to_match = (
        set()
    )  # Stores tuples (source_alias, rel_id, target_alias)

    # Find all nodes needed for filtering or sorting
    filter_sort_aliases = set()
    for alias, node in plan.nodes.items():
        if node.fine_grained_roles.intersection({"FILTER", "SORT"}):
            filter_sort_aliases.add(alias)

    # Find paths to reach filter/sort nodes using BFS/DFS from root
    if filter_sort_aliases:
        queue: deque[tuple[str, list[tuple[str, str, str]]]] = deque(
            [(root_alias, [])]
        )  # (current_alias, path_of_rels)
        visited_bfs = {root_alias}
        paths_found: dict[
            str, list[tuple[str, str, str]] | None
        ] = {}  # Linter fix: Initialize empty dict
        # Initialize keys for all filter/sort aliases
        for alias in filter_sort_aliases:
            paths_found[alias] = None

        while queue:
            current_alias, current_path = queue.popleft()

            if (
                current_alias in filter_sort_aliases
                and paths_found[current_alias] is None
            ):
                paths_found[current_alias] = current_path
                # Add aliases and rels from this path to required sets
                phase1_required_aliases.add(current_alias)
                for src, _rid, tgt in current_path:
                    phase1_required_aliases.add(src)
                    phase1_required_aliases.add(tgt)
                    phase1_rel_paths_to_match.add((src, _rid, tgt))

            # Explore neighbors
            for rel in plan.relationships:
                neighbor_alias = None
                if rel.source.var_name == current_alias:
                    neighbor_alias = rel.target.var_name
                # Add check for INCOMING relationships if needed by schema/query patterns
                # elif rel.target.var_name == current_alias and rel.direction == RelationshipDirection.INCOMING:
                #     neighbor_alias = rel.source.var_name

                if neighbor_alias and neighbor_alias not in visited_bfs:
                    visited_bfs.add(neighbor_alias)
                    new_path = [
                        *current_path,
                        (rel.source.var_name, rel.type_, rel.target.var_name),
                    ]
                    queue.append((neighbor_alias, new_path))

        # Add nodes and relationships found in necessary paths
        phase1_required_aliases.update(
            {alias for alias in filter_sort_aliases if paths_found[alias] is not None}
        )
        for src, _rid, tgt in phase1_rel_paths_to_match:
            phase1_required_aliases.add(src)
            phase1_required_aliases.add(tgt)

    # 2. Add MATCH operations ONLY for required aliases and relationships
    plan.add_operation(MatchOperation(plan.nodes[root_alias], optional=False))
    matched_aliases = {root_alias}

    # Add OPTIONAL MATCH for required relationships
    # Sort paths for deterministic query generation
    sorted_phase1_rels = sorted(phase1_rel_paths_to_match)
    for src_alias, _rid, tgt_alias in sorted_phase1_rels:
        # Find the corresponding QueryRelationship object
        rel_obj = next(
            (
                r
                for r in plan.relationships
                if r.source.var_name == src_alias
                and r.target.var_name == tgt_alias
                and r.type_ == _rid
            ),
            None,
        )
        if rel_obj:
            plan.add_operation(RelationshipMatchOperation(rel_obj, optional=True))
            matched_aliases.add(src_alias)
            matched_aliases.add(tgt_alias)
        else:
            logger.warning(
                f"Could not find QueryRelationship object for path: {src_alias}-{_rid}->{tgt_alias}"
            )

    # Ensure all explicitly required aliases (even if not reached by path match, e.g., isolated filter nodes) are included if possible.
    # This scenario might indicate an issue in graph building or analysis phase.
    for req_alias in phase1_required_aliases:
        if (
            req_alias not in matched_aliases
            and req_alias in plan.nodes
            and req_alias != root_alias
        ):
            # If a required node wasn't matched via relationship path, match it directly
            # This might be less efficient but ensures filter/sort can be applied
            # Check if it's the root node (already matched)
            plan.add_operation(MatchOperation(plan.nodes[req_alias], optional=True))
            matched_aliases.add(req_alias)
            logger.warning(
                f"Required node '{req_alias}' was not reached via relationship paths, adding direct OPTIONAL MATCH."
            )

    # 3. Initial WITH clause carrying ONLY the necessary aliases for Phase 1
    initial_carry_forward = sorted(phase1_required_aliases)
    plan.add_with_clause(initial_carry_forward)

    # 4. Filtering & Sorting (Applied only to aliases available in Phase 1)
    filters_added = False
    # Pass field_types from the graph to the FilterExpressionBuilder
    field_types_from_graph = graph.field_types if hasattr(graph, "field_types") else {}
    filter_builder = FilterExpressionBuilder(plan, field_types=field_types_from_graph)
    if hasattr(graph, "standalone_filters"):
        for entity_alias, filters in graph.standalone_filters.items():
            if entity_alias not in initial_carry_forward:
                # logger.debug(f"Skipping standalone filter on {entity_alias} - not in phase 1 carry forward")
                continue  # Skip filters on entities not available in Phase 1
            for filter_condition in filters:
                try:
                    # Apply null checks when processing standalone filters.
                    expr, params, nodes = filter_builder.process_value_filter(
                        filter_condition, entity_alias, apply_null_check=True
                    )
                    # Ensure *all* nodes needed for the filter expression are available
                    if all(node in initial_carry_forward for node in nodes):
                        filter_op = FilterOperation(
                            expression=expr, params=params, referenced_nodes=nodes
                        )
                        plan.add_operation(filter_op)
                        plan.params.update(params)
                        filters_added = True
                    else:
                        logger.warning(
                            f"Skipping filter '{expr}' due to unavailable nodes {set(nodes) - set(initial_carry_forward)} in Phase 1."
                        )
                except Exception as e:
                    logger.error(f"Filter processing error: {e}", exc_info=True)

    if hasattr(graph, "composite_structures") and graph.composite_structures:
        for structure in graph.composite_structures:
            # Check if the root alias of the structure is available
            if structure.root_alias not in initial_carry_forward:
                # logger.debug(f"Skipping composite filter rooted at {structure.root_alias} - not in phase 1 carry forward")
                continue
            try:
                expr, params, nodes = process_composite_structure_with_null_checks(
                    structure, plan, filter_builder
                )
                if expr:
                    # Ensure *all* nodes needed for the composite expression are available
                    if all(node in initial_carry_forward for node in nodes):
                        filter_op = FilterOperation(
                            expression=expr, params=params, referenced_nodes=nodes
                        )
                        plan.add_operation(filter_op)
                        plan.params.update(params)
                        filters_added = True
                    else:
                        logger.warning(
                            f"Skipping composite filter '{expr}' due to unavailable nodes {set(nodes) - set(initial_carry_forward)} in Phase 1."
                        )
            except Exception as e:
                logger.error(f"Composite filter processing error: {e}", exc_info=True)

    carry_forward_after_filter = initial_carry_forward
    if filters_added:
        plan.add_with_clause(initial_carry_forward)

    # Sorting
    sort_expressions: list[tuple[str, str]] = []
    if hasattr(graph, "sort_conditions"):
        for entity_alias, sorters in graph.sort_conditions.items():
            if entity_alias not in carry_forward_after_filter:
                # logger.debug(f"Skipping sort on {entity_alias} - not in phase 1 carry forward")
                continue  # Skip sorts on entities not available
            for field_name, ascending in sorters:
                if not isinstance(field_name, str):
                    continue
                sort_direction_str = "ASC" if ascending else "DESC"
                sanitized_prop = sanitize_property_reference(field_name)
                sort_expressions.append(
                    (f"{entity_alias}.{sanitized_prop}", sort_direction_str)
                )

    # --- Determine Aliases Carried Forward After Pagination ---
    # Find the last WITH operation BEFORE the PaginationOperation
    last_with_op_index = -1
    for i in range(
        len(plan.operations) - 2, -1, -1
    ):  # Search backwards from before pagination
        if isinstance(plan.operations[i], WithOperation):
            last_with_op_index = i
            break

    if last_with_op_index != -1:
        # Ensure the operation is indeed a WithOperation before accessing variables
        op_to_check = plan.operations[last_with_op_index]
        if isinstance(op_to_check, WithOperation):
            carry_forward_after_pagination = op_to_check.variables
        else:
            # Fallback if the found operation wasn't WITH (shouldn't happen)
            carry_forward_after_pagination = [root_alias]
            logger.warning(
                f"Operation at index {last_with_op_index} before pagination was not WithOperation, defaulting to: {[root_alias]}"
            )
    else:
        # Fallback if no WITH clause was found before pagination
        # This might happen in very simple cases (MATCH n0 SKIP LIMIT RETURN n0)
        # In such cases, the scope before pagination is just the matched root.
        # However, even MATCH (n0) WITH n0 ... should be caught.
        # If truly nothing before pagination, use matched_aliases from Phase 1.
        carry_forward_after_pagination = sorted(matched_aliases)
        logger.warning(
            f"Could not find suitable WITH clause before pagination, defaulting to Phase 1 matched aliases: {carry_forward_after_pagination}"
        )

    # --- Phase 2: Enrichment (Optional) ---
    if not enrich_results:
        # Phase 1 only: Handle distinct root nodes, potential sorting via aggregation,
        # pagination, and return.

        if sort_expressions:  # Check if any sorting is requested
            # --- Scenario: Sorting is requested ---
            aggregation_map = {}  # sort_key_alias -> aggregation expression string
            final_order_by_expressions = []
            valid_sort_found = (
                False  # Flag to track if any valid sorters were processed
            )
            has_related_node_sorters = False  # New flag
            count_sort_expressions = 0
            for expr, sort_direction in sort_expressions:
                count_sort_expressions += 1
                alias_part = expr.split(".")[0]
                prop_part = expr.split(".", 1)[1] if "." in expr else None

                if alias_part not in carry_forward_after_filter:
                    logger.warning(
                        f"Sort expression '{expr}' refers to alias '{alias_part}' which is not available after filtering. Skipping this sorter."
                    )
                    continue

                sanitized_prop_for_alias = f"sort_{count_sort_expressions}"
                sort_key_alias = f"sortKey_{alias_part}_{sanitized_prop_for_alias}"

                if alias_part != root_alias:
                    has_related_node_sorters = True  # Set the flag
                    agg_func = "min"
                    sanitized_prop_for_agg = (
                        sanitize_property_reference(prop_part) if prop_part else ""
                    )
                    if sanitized_prop_for_agg:
                        aggregation_map[sort_key_alias] = (
                            f"{agg_func}({alias_part}.{sanitized_prop_for_agg})"
                        )
                        final_order_by_expressions.append(
                            (sort_key_alias, sort_direction)
                        )
                        valid_sort_found = True
                else:  # alias_part == root_alias
                    sanitized_prop_for_agg = (
                        sanitize_property_reference(prop_part) if prop_part else ""
                    )
                    if sanitized_prop_for_agg:
                        # Store direct property access for root alias
                        aggregation_map[sort_key_alias] = (
                            f"{root_alias}.{sanitized_prop_for_agg}"
                        )
                        final_order_by_expressions.append(
                            (sort_key_alias, sort_direction)
                        )
                        valid_sort_found = True
                # --- End of loop --- #

            if valid_sort_found:
                # 1. Build the WITH clause that includes sort keys.
                # This clause will also handle distinctness of root_alias either implicitly via aggregation
                # or explicitly via options={"DISTINCT": True} if only sorting by root properties.
                with_vars = [root_alias] + [
                    f"{agg_expr} AS {key}"
                    for key, agg_expr in aggregation_map.items()
                    if key
                    in [
                        e[0] for e in final_order_by_expressions
                    ]  # Only include used keys
                ]

                with_options = {}
                if not has_related_node_sorters:
                    # If all valid sorts are on the root_alias, we need to explicitly make it distinct.
                    # If there are related node sorters, the aggregation (e.g., min()) will handle distinctness of root_alias.
                    with_options["DISTINCT"] = True

                plan.add_operation(
                    WithOperation(variables=with_vars, options=with_options)
                )

                # 2. Order by calculated sort keys
                plan.add_operation(OrderByOperation(final_order_by_expressions))

                # 3. Add WITH to carry only root_alias forward before pagination
                plan.add_with_clause([root_alias])
            else:
                # No valid sorters remained, fall back to just distinct root node
                logger.warning(
                    "Sorting requested, but no valid sort expressions remained after analysis. Proceeding without sorting."
                )
                # Establish distinct root nodes directly after the initial WITH.
                plan.add_operation(
                    WithOperation(variables=[root_alias], options={"DISTINCT": True})
                )

            # 4. Apply Pagination (common for sorted path and fallback-distinct path)
            plan.add_operation(PaginationOperation(offset, limit))

            # 5. Final Return (common for sorted path and fallback-distinct path)
            plan.add_operation(ReturnOperation(root_alias))

        else:
            # --- Scenario: No sorting requested ---
            # 1. Establish distinct root nodes directly after the initial WITH.
            plan.add_operation(
                WithOperation(variables=[root_alias], options={"DISTINCT": True})
            )

            # 2. Apply Pagination (No intermediate WITH n0 needed as no sorting)
            plan.add_operation(PaginationOperation(offset, limit))

            # 3. Final Return
            plan.add_operation(ReturnOperation(root_alias))

    else:
        # Start enrichment phase WITH only the aliases carried from Phase 1
        # Ensure carry_forward_after_pagination was calculated correctly before this block
        # Note: User edits commented out original pagination, which might affect carry_forward_after_pagination.
        # Assuming carry_forward_after_pagination holds the correct aliases *before* any enrichment/distinct logic.
        plan.add_with_clause(
            carry_forward_after_pagination
        )  # Use the variable calculated before the if/else block

        # --- Phase 2 Logic using Roles and Graph Traversal ---

        # 1. Identify ALL nodes needed for Phase 2 (Phase 1 results + Projection paths)
        required_aliases_for_phase2 = set(
            carry_forward_after_pagination
        )  # Start with P1 results
        # Find all nodes marked for projection or on path to them
        projection_targets = {
            alias
            for alias, node in graph.nodes.items()
            if "PROJECTION" in node.fine_grained_roles
        }
        logger.debug(f"Phase 2 - Projection Targets: {projection_targets}")

        # BFS from P1 results to find all reachable nodes needed to connect to projection targets
        nodes_on_path = set()
        if projection_targets:  # Only run BFS if there are specific targets
            # Explicitly type queue as deque[str]
            # projection_bfs_queue: deque[str] = deque(carry_forward_after_pagination)
            # Remove type hint, let inference work
            projection_bfs_queue = deque(carry_forward_after_pagination)
            visited_bfs = set(carry_forward_after_pagination)
            nodes_on_path = set(carry_forward_after_pagination)

            while projection_bfs_queue:
                current_alias = projection_bfs_queue.popleft()  # Pops a str
                nodes_on_path.add(current_alias)  # Adds a str

                # Explore neighbors based on the original graph structure
                # current_alias is correctly a str here
                for _rel_id, target_alias in graph.entity_relationships.get(
                    current_alias, set()
                ):
                    if target_alias not in visited_bfs:
                        visited_bfs.add(target_alias)
                        projection_bfs_queue.append(target_alias)  # Appends a str

        # The final set of required aliases includes Phase 1 outputs, projection targets, and path nodes
        required_aliases_for_phase2.update(projection_targets)
        required_aliases_for_phase2.update(nodes_on_path)
        logger.debug(
            f"Phase 2 - Required Aliases (P1 + Proj + Paths): {required_aliases_for_phase2}"
        )

        # 2. Add OPTIONAL MATCH for necessary relationships NOT matched in Phase 1
        ops_to_add = []
        edges_matched_phase1 = {
            (src, rel_id) for src, rel_id, _tgt in phase1_rel_paths_to_match
        }
        edges_added_in_rematch = set()

        for rel in sorted(
            plan.relationships, key=lambda x: x.source.var_name
        ):  # Iterate relationships copied from original graph
            src_alias = rel.source.var_name
            tgt_alias = rel.target.var_name
            rel_id = rel.type_
            edge_key = (src_alias, rel_id)

            # If both source and target are required for Phase 2 AND the edge wasn't matched in Phase 1
            if (
                src_alias in required_aliases_for_phase2
                and tgt_alias in required_aliases_for_phase2
                and edge_key not in edges_matched_phase1
                and edge_key not in edges_added_in_rematch
            ):
                logger.debug(
                    f"Phase 2 - Adding missing OPTIONAL MATCH: {src_alias}-[{rel_id}]->{tgt_alias}"
                )
                ops_to_add.append(RelationshipMatchOperation(rel, optional=True))
                edges_added_in_rematch.add(edge_key)
                # Ensure target node exists if it was potentially pruned but now needed
                if tgt_alias not in plan.nodes and tgt_alias in graph.nodes:
                    plan.add_node(graph.nodes[tgt_alias])

        if ops_to_add:
            logger.debug(
                f"Phase 2: Added {len(ops_to_add)} OPTIONAL MATCH ops for missing projection paths."
            )
            for op in ops_to_add:
                plan.add_operation(op)

        # 3. Enrichment for NESTED fields only, starting from all required aliases
        enrichment_match_ops = []
        enrichment_aliases_added = set()
        processed_schema_edges = (
            set()
        )  # Track edges added ONLY during this enrichment pass
        schema_queue: deque[tuple[str, ObjectDescriptor, tuple[str, ...]]] = deque()
        enrichment_alias_counter = 1  # Initialize counter here

        # Start enrichment traversal from all nodes now deemed required
        for alias in required_aliases_for_phase2:
            if alias in plan.nodes:
                node_label = plan.nodes[alias].label
                r_descriptor = registry.get_object_descriptor(node_label)
                if r_descriptor:
                    # Log the descriptor being added to the queue
                    logger.debug(
                        f"Phase 2 Enrichment - Adding to queue: Alias={alias}, Type={node_label}, Descriptor Fields={[f.field_identifier for f in r_descriptor.fields]}"
                    )
                    obj_id_str = str(r_descriptor.object_identifier)
                    if (alias, obj_id_str) not in [
                        (q[0], str(q[1].object_identifier)) for q in schema_queue
                    ]:
                        schema_queue.append(
                            (alias, r_descriptor, (obj_id_str,))
                        )  # Start traversal
        while schema_queue:
            current_src_alias, current_descriptor, current_path_ids = (
                schema_queue.popleft()
            )
            logger.debug(
                f"Phase 2 Enrichment - Processing node: {current_src_alias} ({current_descriptor.object_identifier})"
            )  # Log node being processed
            for field in current_descriptor.fields:
                field_info = registry.analyze_field_type(current_descriptor, field)
                # Log field analysis result
                logger.debug(
                    f"Phase 2 Enrichment - Analyzing field: {field.field_identifier}, TypeInfo: {field_info}"
                )
                if field_info.is_nested:
                    logger.debug(
                        f"Phase 2 Nested Field - Descriptor for nested field {field.field_identifier} is: {field_info.nested_descriptor}"
                    )
                is_nested_rel = (
                    field_info.is_nested or field_info.is_collection
                ) and field_info.nested_descriptor
                if is_nested_rel:
                    target_descriptor = field_info.nested_descriptor
                    if not target_descriptor:
                        continue  # Move continue to its own line
                    rel_id = registry.extract_field_name(field.field_identifier)
                    target_type = registry.get_object_name(target_descriptor)
                    target_obj_id_str = str(target_descriptor.object_identifier)
                    if target_obj_id_str in current_path_ids:
                        continue  # Move continue to its own line (Cycle check)

                    schema_edge_key = (current_src_alias, rel_id)
                    # IMPORTANT: Also check if this edge was matched in Phase 1 or Re-Matching
                    if (
                        schema_edge_key in edges_matched_phase1
                        or schema_edge_key in edges_added_in_rematch
                        or schema_edge_key in processed_schema_edges
                    ):
                        continue

                    processed_schema_edges.add(schema_edge_key)
                    target_alias = f"en{enrichment_alias_counter}"
                    enrichment_alias_counter += 1
                    enrichment_aliases_added.add(target_alias)

                    if current_src_alias not in plan.nodes:
                        continue  # Move continue to its own line
                    source_node_for_match = plan.nodes[current_src_alias]
                    target_node_for_match = QueryNode(target_type, target_alias)
                    if target_alias not in plan.nodes:
                        plan.add_node(target_node_for_match)

                    direction = registry.get_relationship_direction(
                        current_descriptor, rel_id
                    )
                    enrichment_rel = QueryRelationship(
                        source_node_for_match, target_node_for_match, rel_id, direction
                    )
                    plan.add_relationship(enrichment_rel)
                    enrichment_match_ops.append(
                        RelationshipMatchOperation(enrichment_rel, optional=True)
                    )
                    new_path_ids = (*current_path_ids, target_obj_id_str)
                    if target_descriptor:
                        schema_queue.append(
                            (target_alias, target_descriptor, new_path_ids)
                        )

        # Add enrichment OPTIONAL MATCH operations
        if enrichment_match_ops:
            logger.debug(
                f"Phase 2: Added {len(enrichment_match_ops)} OPTIONAL MATCH ops for nested field enrichment."
            )
            for op in enrichment_match_ops:
                plan.add_operation(op)

        # 4. Final WITH clause before projection, carrying all aliases involved
        final_aliases_for_projection = (
            required_aliases_for_phase2 | enrichment_aliases_added
        )
        final_carry_forward = sorted(final_aliases_for_projection)
        logger.debug(
            f"Phase 2 - Final aliases for projection WITH: {final_carry_forward}"
        )
        plan.add_with_clause(final_carry_forward)

        # --- Build Bottom-Up Projection using all collected aliases ---
        # Ensure the plan's internal state reflects all nodes needed for projection
        # (The add_node calls during schema traversal should handle this)
        plan.build_bottom_up_projection(all_aliases_in_graph=all_aliases_in_graph)
        # The build_bottom_up_projection method adds the final RETURN

    return plan
