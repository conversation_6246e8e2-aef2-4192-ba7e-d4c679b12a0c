"""
Module for preparing batch data for complex node structures with schema awareness.

This module adapts the logic from create_complex_node_structure to generate
lists of node and relationship data suitable for batch processing with UNWIND,
instead of executing individual Cypher queries.
"""

import datetime
import uuid
from enum import Enum
from typing import Any, TypedDict

from salestech_be.common.type.metadata.common import (
    CustomFieldIdentifier,
    CustomObjectIdentifier,
    StandardFieldIdentifier,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.schema import (
    ObjectDescriptor,
)
from salestech_be.falkordb.schema_registry import (
    SchemaRegistry,
)
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


# Define expected structure for node and relationship data used in batch updates
class NodeTypeData(TypedDict):
    label: str
    id: str
    properties: dict[str, Any]


class RelationshipTypeData(TypedDict):
    from_label: str
    from_id: str
    to_label: str
    to_id: str
    type: str
    from_id_field_name: str | None
    to_id_field_name: str | None
    properties: dict[str, Any]


# Helper function to convert values to FalkorDB compatible types
def _convert_param_value(value: Any) -> Any:  # noqa: PLR0911
    if isinstance(value, uuid.UUID):
        return str(value)
    if isinstance(value, datetime.datetime):
        # Convert datetime to epoch milliseconds (integer)
        return int(value.timestamp() * 1000)
    if isinstance(value, Enum):
        # Convert enums to their string values
        return str(value.value)
    if isinstance(value, list):
        # Recursively convert items in lists
        return [_convert_param_value(item) for item in value]
    if isinstance(value, dict):
        # Recursively convert values in dictionaries
        return {k: _convert_param_value(v) for k, v in value.items()}
    # Basic types (str, int, float, bool) are generally fine
    # Return other types as string as a fallback
    if not isinstance(value, (str, int, float, bool)) and value is not None:
        return str(value)
    return value


# Helper function to prepare node data dictionary
def _prepare_node_data(
    label: str,
    node_id: str,
    properties: dict[str, Any],
) -> NodeTypeData:
    # Ensure ID is in properties and is a string
    sanitized_props = properties.copy()  # Create a copy first
    sanitized_props["id"] = str(node_id)

    # Sanitize all property values
    for key, value in sanitized_props.items():
        sanitized_props[key] = _convert_param_value(value)

    logger.debug(
        f"Preparing node data for {label}[{node_id}] (props count: {len(sanitized_props)})"
    )

    return NodeTypeData(
        label=label,
        id=str(node_id),
        properties=sanitized_props,
    )


# Helper function to prepare relationship data dictionary
def _prepare_relationship_data(
    from_label: str,
    from_id: str,
    to_label: str,
    to_id: str,
    relationship_type: str,
    properties: dict[str, Any] | None = None,
    from_id_field_name: str = "id",
    to_id_field_name: str = "id",
) -> RelationshipTypeData:
    # Default to empty dict if properties are None
    rel_props = properties or {}

    # Sanitize all property values
    sanitized_props = {k: _convert_param_value(v) for k, v in rel_props.items()}

    logger.debug(
        f"Preparing relationship data: {from_label}[{from_id}] -[{relationship_type}]-> {to_label}[{to_id}] (props count: {len(sanitized_props)})"
    )

    return RelationshipTypeData(
        from_label=from_label,
        from_id=str(from_id),
        to_label=to_label,
        to_id=str(to_id),
        type=relationship_type,
        properties=sanitized_props,
        from_id_field_name=from_id_field_name,
        to_id_field_name=to_id_field_name,
    )


# Function to process fields based on schema
def _process_fields_by_schema_for_batch(  # noqa: C901, PLR0912, PLR0915
    node_type: str,
    node_id: str,
    data: dict[str, Any],
    obj_descriptor: ObjectDescriptor,
    registry: SchemaRegistry,
) -> tuple[dict[str, Any], list[NodeTypeData], list[RelationshipTypeData]]:
    """
    Processes fields for a given node based on its schema descriptor.
    Separates primitive properties, nested nodes, and relationships.

    Args:
        node_type: The label/type of the node.
        node_id: The ID of the node.
        data: The raw data dictionary for the node.
        schema: The overall organization schema.
        obj_descriptor: The specific ObjectDescriptor for this node_type.
        registry: The SchemaRegistry instance.

    Returns:
        A tuple containing:
        - primitive_props: Dictionary of processed primitive properties.
        - all_nested_nodes: List of NodeTypeData for nested nodes to be created.
        - all_nested_rels: List of RelationshipTypeData for relationships to be created.
    """
    primitive_props: dict[str, Any] = {}
    all_nested_nodes: list[NodeTypeData] = []
    all_nested_rels: list[RelationshipTypeData] = []

    # Log entry into this specific function
    logger.debug(
        f"Entering _process_fields_by_schema_for_batch for {node_type}[{node_id}]"
    )
    logger.debug(
        f"Descriptor identifier for {node_type}[{node_id}]: {str(obj_descriptor.object_identifier) if obj_descriptor and obj_descriptor.object_identifier else 'N/A'}"
    )
    logger.debug(
        f"Data keys for {node_type}[{node_id}]: {list(data.keys()) if data else 'N/A'}"
    )

    # Ensure descriptor.fields is iterable, default to empty list if None
    fields_to_process = (
        obj_descriptor.fields if obj_descriptor.fields is not None else []
    )

    # Create a set of field names defined in the schema
    schema_field_names = {
        registry.extract_field_name(field.field_identifier)
        for field in fields_to_process  # Use the safe list
    }

    logger.debug(
        f"Processing {len(schema_field_names)} schema fields for {node_type}[{node_id}]"
    )

    # Add logging right before the loop starts
    logger.debug(f"Starting field processing loop for {node_type}[{node_id}]")

    for field_name in sorted(schema_field_names):  # Sort for consistent log order
        logger.debug(f"Processing field: '{field_name}' for {node_type}[{node_id}]")

        # --- Get field descriptor from schema --- >
        field_descriptor = next(
            (
                f
                for f in fields_to_process
                if registry.extract_field_name(f.field_identifier) == field_name
            ),
            None,
        )
        if not field_descriptor:
            logger.warning(
                f"Schema inconsistency: Field '{field_name}' not found in descriptor fields for {node_type}[{node_id}]. Skipping."
            )
            continue
        # < --- End get field descriptor ---

        # --- Determine field type and get value using schema --- >
        field_value = None
        field_id_for_prop = field_name  # Default to name for standard fields
        field_info = registry.analyze_field_type(obj_descriptor, field_descriptor)

        if isinstance(field_descriptor.field_identifier, CustomFieldIdentifier):
            if isinstance(obj_descriptor.object_identifier, StandardObjectIdentifier):
                custom_field_map = data.get("custom_field_data")
            elif isinstance(obj_descriptor.object_identifier, CustomObjectIdentifier):
                custom_field_map = data.get("field_values")
            else:
                raise ValueError(f"Unknown object type: {obj_descriptor}")

            field_uuid = field_descriptor.field_identifier.field_id
            field_id_for_prop = (
                f"`{field_uuid!s}`"  # Use UUID string as prop key for custom fields
            )

            if isinstance(custom_field_map, dict):
                if isinstance(
                    obj_descriptor.object_identifier, StandardObjectIdentifier
                ):
                    field_value = custom_field_map.get(field_uuid)
                elif isinstance(
                    obj_descriptor.object_identifier, CustomObjectIdentifier
                ):
                    field_uuid_str = str(field_uuid)
                    field_value = custom_field_map.get(field_uuid_str)
                else:
                    raise ValueError(f"Unknown object type: {obj_descriptor}")

                logger.debug(
                    f"Field '{field_name}' ({field_uuid}) identified as CUSTOM. Value found in custom_field_map: {field_value is not None}"
                )
            else:
                logger.warning(
                    f"Expected dict for custom_field_data in {node_type}[{node_id}], got {type(custom_field_map)}."
                )
        elif isinstance(field_descriptor.field_identifier, StandardFieldIdentifier):
            # Standard field key is the field name string
            field_value = data.get(field_name)
            logger.debug(
                f"Field '{field_name}' identified as STANDARD. Value found in data: {field_value is not None}"
            )
        else:
            logger.warning(
                f"Unknown field identifier type for '{field_name}': {type(field_descriptor.field_identifier)}. Skipping."
            )
            continue
        # < --- End determine field type and get value ---

        if field_value is None:
            logger.debug(
                f"Field '{field_name}' for {node_type}[{node_id}] has None value or was not found. Skipping."
            )
            continue

        # --- Log before getting relationship type --- >
        try:
            logger.debug(
                f"Determining relationship type for field '{field_name}' in {node_type}[{node_id}]"
            )
            # Check relationships on descriptor just before the call
            outbound_rels_exist = (
                hasattr(obj_descriptor, "outbound_relationships")
                and obj_descriptor.outbound_relationships is not None
            )
            inbound_rels_exist = (
                hasattr(obj_descriptor, "inbound_relationships")
                and obj_descriptor.inbound_relationships is not None
            )
            logger.debug(
                f"Descriptor for '{field_name}': outbound_rels_exist={outbound_rels_exist}, inbound_rels_exist={inbound_rels_exist}"
            )
            relationship_type = registry.get_field_relationship_type(
                obj_descriptor, field_name
            )
        except TypeError as e:
            logger.error(
                f"TypeError occurred while getting relationship type for field '{field_name}' in {node_type}[{node_id}]. Error: {e}",
                exc_info=True,
            )
            continue  # Skip this field if type determination fails
        except Exception as e:
            logger.error(
                f"Unexpected error getting relationship type for field '{field_name}' in {node_type}[{node_id}]. Error: {e}",
                exc_info=True,
            )
            continue  # Skip this field on other errors
        # < --- End log before getting relationship type ---

        logger.debug(
            f"Field '{field_name}' for {node_type}[{node_id}]: Relationship type = {relationship_type}"
        )

        # Skip domain relationships as they are handled elsewhere
        if relationship_type == "domain_relationship":
            logger.debug(f"  Skipping field '{field_name}' (domain_relationship)")
            continue

        if relationship_type == "nested_structure":
            # --- START Try block for nested/collection processing --- >
            logger.debug(
                f"Entering nested structure processing for field '{field_name}' in {node_type}[{node_id}]"
            )
            try:
                # We already have field_descriptor from above
                logger.debug(
                    f"  Found field descriptor for '{field_name}': {field_descriptor is not None}"
                )
                # No need to check again, already handled
                # if not field_descriptor:
                #     logger.warning(f"Schema inconsistency: Field '{field_name}' not found in descriptor fields for {node_type}[{node_id}]")
                #     continue

                field_info = registry.analyze_field_type(
                    obj_descriptor, field_descriptor
                )

                if field_info.is_collection and not field_info.is_nested:
                    if field_value is None:
                        logger.warning(
                            f"Collection field '{field_name}' in {node_type}[{node_id}] has None value. Skipping."
                        )
                        continue
                    if not isinstance(field_value, list):
                        logger.warning(
                            f"Expected list for collection field '{field_name}' in {node_type}[{node_id}], got {type(field_value)}. Storing as primitive."
                        )
                        continue

                # Handle single nested object
                if (
                    field_info.is_nested
                    and not field_info.is_collection
                    and field_info.nested_descriptor
                ):
                    logger.debug(
                        f"Processing single nested object for field '{field_name}' in {node_type}[{node_id}]"
                    )
                    logger.debug(
                        f"  Value type for nested '{field_name}': {type(field_value)}"
                    )
                    if not isinstance(field_value, dict):
                        logger.warning(
                            f"Expected dict for nested field '{field_name}' in {node_type}[{node_id}], got {type(field_value)}. Storing as primitive."
                        )
                        # Use field_id_for_prop for consistent property key handling
                        primitive_props[field_id_for_prop] = field_value
                        logger.debug(
                            f"    Added '{field_id_for_prop}' to primitive_props (fallback)"
                        )
                        continue

                    target_type = registry.get_object_name(field_info.nested_descriptor)
                    sub_node_id = field_value.get("id", f"{node_id}_{field_name}")
                    logger.debug(
                        f"Preparing recursive call for nested object: target_type={target_type}, sub_node_id={sub_node_id}"
                    )
                    if not sub_node_id or not isinstance(
                        sub_node_id, (str, int, uuid.UUID)
                    ):
                        sub_node_id = f"{node_id}_{field_name}_{uuid.uuid4()}"

                    # Recursive call
                    nested_nodes, nested_rels = prepare_node_structure_batch(
                        node_type=target_type,
                        node_id=str(sub_node_id),
                        data=field_value,
                        schema_registry=registry,
                    )
                    all_nested_nodes.extend(nested_nodes)
                    all_nested_rels.extend(nested_rels)
                    all_nested_rels.append(
                        _prepare_relationship_data(
                            from_label=node_type,
                            from_id=node_id,
                            from_id_field_name="id",
                            to_label=target_type,
                            to_id=str(sub_node_id),
                            to_id_field_name="id",
                            # Use field_name for relationship type for consistency
                            relationship_type=field_name,
                        )
                    )

                # Handle collection of nested objects
                elif field_info.is_collection and field_info.nested_descriptor:
                    logger.debug(
                        f"Processing collection for field '{field_name}' in {node_type}[{node_id}]"
                    )
                    logger.debug(
                        f"  Value type for collection '{field_name}': {type(field_value)}"
                    )
                    if not isinstance(field_value, list):
                        logger.warning(
                            f"Expected list for collection field '{field_name}' in {node_type}[{node_id}], got {type(field_value)}. Storing as primitive."
                        )
                        # Use field_id_for_prop for consistent property key handling
                        primitive_props[field_id_for_prop] = field_value
                        logger.debug(
                            f"    Added '{field_id_for_prop}' to primitive_props (fallback)"
                        )
                        continue
                    target_type = registry.get_object_name(field_info.nested_descriptor)
                    for i, item in enumerate(field_value):
                        logger.debug(
                            f"Processing item {i} in collection '{field_name}' for {node_type}[{node_id}]"
                        )
                        if not isinstance(item, dict):
                            logger.warning(
                                f"Expected dict item in collection field '{field_name}'[{i}] for {node_type}[{node_id}], got {type(item)}. Skipping item."
                            )
                            continue

                        item_node_id = item.get("id", f"{node_id}_{field_name}_{i}")
                        logger.debug(
                            f"Preparing recursive call for collection item: target_type={target_type}, item_node_id={item_node_id}"
                        )
                        if not item_node_id or not isinstance(
                            item_node_id, (str, int, uuid.UUID)
                        ):
                            item_node_id = f"{node_id}_{field_name}_{i}_{uuid.uuid4()}"

                        # Recursive call
                        nested_nodes, nested_rels = prepare_node_structure_batch(
                            node_type=target_type,
                            node_id=str(item_node_id),
                            data=item,
                            schema_registry=registry,
                        )
                        all_nested_nodes.extend(nested_nodes)
                        all_nested_rels.extend(nested_rels)
                        all_nested_rels.append(
                            _prepare_relationship_data(
                                from_label=node_type,
                                from_id=node_id,
                                to_label=target_type,
                                to_id=str(item_node_id),
                                # Use field_name for relationship type
                                relationship_type=field_name,
                            )
                        )
                else:
                    # Field type doesn't match expected nested structure, store as primitive
                    logger.debug(
                        f"  Field '{field_name}' determined as nested_structure but doesn't fit criteria. Storing as primitive."
                    )
                    # Use field_id_for_prop for consistent property key handling
                    primitive_props[field_id_for_prop] = field_value
                    logger.debug(
                        f"    Added '{field_id_for_prop}' to primitive_props (fallback)"
                    )
            # --- END Try block --- <
            except TypeError as e:
                # Log specific TypeError with context
                logger.error(
                    f"TypeError during processing of nested field '{field_name}' for {node_type}[{node_id}]. "
                    f"Value type: {type(field_value)}. Error: {e}",
                    exc_info=True,  # Include stack trace
                )
                # Skip processing this field and continue with the next field
                continue
            except Exception as e:
                # Log other unexpected errors during nested processing
                logger.error(
                    f"Unexpected error processing nested field '{field_name}' for {node_type}[{node_id}]. Error: {e}",
                    exc_info=True,
                )
                # Skip processing this field and continue with the next field
                continue

        elif relationship_type == "primitive":
            # Use field_id_for_prop for consistent property key handling
            logger.debug(f"  Adding primitive field: '{field_id_for_prop}'")
            primitive_props[field_id_for_prop] = field_value
        # The case for custom fields is implicitly handled by using field_id_for_prop when relationship_type == 'primitive'
        else:  # Unknown type or standard field not categorized as primitive/nested
            logger.debug(
                f"  Relationship type '{relationship_type}' for field '{field_name}' is not explicitly handled as primitive/nested. Storing as primitive."
            )
            # Use field_id_for_prop for consistent property key handling
            primitive_props[field_id_for_prop] = field_value

    logger.debug(
        f"Finished processing fields for {node_type}[{node_id}]. Final primitive_props count: {len(primitive_props)}"
    )

    return primitive_props, all_nested_nodes, all_nested_rels


# --- Main Function ---


def prepare_node_structure_batch(
    node_type: str,
    node_id: str,
    data: dict[str, Any],
    schema_registry: SchemaRegistry,
) -> tuple[list[NodeTypeData], list[RelationshipTypeData]]:
    """
    Prepare batch data for a complex node structure based on a schema.

    Recursively processes nested structures defined in the schema, generating
    lists of node data and relationship data suitable for batch insertion/update
    using UNWIND. Domain relationships defined in the schema are ignored.

    Args:
        node_type: Type/label of the root node to process.
        node_id: ID of the root node.
        data: Data dictionary for the root node, potentially including nested data.
        schema: The OrganizationSchemaDescriptor to guide processing.

    Returns:
        A tuple containing two lists:
        1. List of node data dictionaries (`NodeTypeData`).
        2. List of relationship data dictionaries (`RelationshipTypeData`).
    """
    # Log entry into the main function
    logger.debug(f"Entering prepare_node_structure_batch for {node_type}[{node_id}]")

    if not data:
        return [], []

    source_descriptor = schema_registry.get_object_descriptor(node_type)

    all_nodes_data: list[NodeTypeData] = []
    all_relationships_data: list[RelationshipTypeData] = []
    primitive_props: dict[str, Any] = {}

    if source_descriptor:
        primitive_props, nested_nodes, nested_rels = (
            _process_fields_by_schema_for_batch(
                node_type=node_type,
                node_id=node_id,
                data=data,
                obj_descriptor=source_descriptor,
                registry=schema_registry,
            )
        )
        all_nodes_data.extend(nested_nodes)
        all_relationships_data.extend(nested_rels)
    else:
        logger.info(
            f"No schema descriptor found for type {node_type}. Node will be created with primitive properties only (if any)."
        )
        # Fallback: attempt to store all top-level non-dict/list items as primitives
        for key, value in data.items():
            if not isinstance(value, (dict, list, tuple, set)):
                primitive_props[key] = value

    # Prepare the data for the root node itself, including its primitive properties
    root_node_data = _prepare_node_data(
        label=node_type,
        node_id=node_id,
        properties=primitive_props,
    )
    all_nodes_data.insert(0, root_node_data)  # Add root node data at the beginning

    logger.debug(
        f"Prepared batch data for {node_type}[{node_id}]: {len(all_nodes_data)} nodes, {len(all_relationships_data)} relationships."
    )
    return all_nodes_data, all_relationships_data
