from typing import Any, TypeVar, cast
from uuid import UUID

from falkordb import Node

# Added import for TypeAdapter
from pydantic import BaseModel

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.common.type.metadata.common import (
    ObjectIdentifier,
    RelationshipId,
    StandardObjectIdentifier,
    object_id_or_name,
)
from salestech_be.common.type.metadata.schema import (
    ObjectDescriptor,
    OrganizationSchemaDescriptor,
)
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.common.types import (
    Address,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.data.types import (
    ModeledObjectRecord,
    StandardRecord,
)
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
from salestech_be.core.metadata.types import (
    ContactAccountAssociationLite,
    ContactEmailLite,
    ContactPhoneNumber,
    PipelineStageSelectListValueLite,
    SelectListValueLite,
)
from salestech_be.core.pipeline.types_v2 import <PERSON>pelineV2
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.falkordb.schema_registry import FieldTypeInfo, SchemaRegistry
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)

# Type variable for the Pydantic model within StandardRecord
ModelType = TypeVar("ModelType", bound=BaseModel)

# Mapping from standard object name to Pydantic model class
# Add other standard models as needed
STANDARD_MODEL_MAP: dict[str, type[Any]] = {
    str(object_id_or_name(AccountV2.object_id)): AccountV2,
    str(object_id_or_name(ContactV2.object_id)): ContactV2,
    str(object_id_or_name(PipelineV2.object_id)): PipelineV2,
    str(object_id_or_name(MeetingV2.object_id)): MeetingV2,
    str(object_id_or_name(TaskV2.object_id)): TaskV2,
    str(object_id_or_name(OrganizationUserV2.object_id)): OrganizationUserV2,
    # Add missing nested models (lowercase keys)
    "address": Address,
    "contact_account_association": ContactAccountAssociationLite,
    "contact_email": ContactEmailLite,
    "contact_phone_number": ContactPhoneNumber,
    "select_list_value": SelectListValueLite,
    "pipeline_stage_select_list_value": PipelineStageSelectListValueLite,  # Missing for 'Pipeline' object
    # Add AI suggested types if they have standalone models and are expected here
    # "ai_suggested_annual_revenue": AccountAISuggestedAnnualRevenue, # Example if needed
    # "ai_suggested_category_list": AccountAISuggestedCategoryList, # Example if needed
    # "ai_suggested_description": AccountAISuggestedDescription, # Example if needed
    # "ai_suggested_employee_count": AccountAISuggestedEmployeeCount, # Example if needed
}


# Helper function to recursively hydrate Pydantic models from nested dicts
def _hydrate_pydantic_model(  # noqa: C901, PLR0912
    result_dict: dict[str, Any],
    model_class: type[ModelType],
    object_descriptor: ObjectDescriptor,
    schema: OrganizationSchemaDescriptor,
    registry: SchemaRegistry,
) -> ModelType:
    primitive_data: dict[str, Any] = {}
    nested_field_data: dict[str, Any] = {}

    # Get domain relationship keys to ignore them during primitive/nested processing
    schema_domain_rel_keys = {
        str(rel.id) for rel in object_descriptor.outbound_relationships
    } | {
        rel.relationship_name
        for rel in object_descriptor.outbound_relationships
        if rel.relationship_name
    }

    schema_fields_info: dict[str, FieldTypeInfo] = {
        registry.extract_field_name(f.field_identifier): registry.analyze_field_type(
            object_descriptor, f
        )
        for f in object_descriptor.fields
    }

    for key, value in result_dict.items():
        if key in schema_domain_rel_keys:
            continue  # Domain relationships are handled by the main function

        field_info = schema_fields_info.get(key)
        is_nested = field_info and (
            field_info.is_nested
            or (field_info.is_collection and field_info.nested_descriptor)
        )

        if is_nested and isinstance(value, list):
            if not field_info or not field_info.nested_descriptor:
                logger.warning(
                    f"Schema inconsistency: Nested field '{key}' lacks nested descriptor."
                )
                continue

            target_nested_model_name = registry.get_object_name(
                field_info.nested_descriptor
            )
            target_nested_model_class = STANDARD_MODEL_MAP.get(
                target_nested_model_name.lower()
            )
            target_nested_object_descriptor = registry.get_object_descriptor(
                target_nested_model_name
            )

            if not target_nested_model_class or not target_nested_object_descriptor:
                logger.warning(
                    f"Could not find model/descriptor for nested type '{target_nested_model_name}' in field '{key}'."
                )
                continue

            # Check if the retrieved class is actually a Pydantic model
            if not issubclass(target_nested_model_class, BaseModel):
                logger.warning(
                    f"Retrieved class for '{target_nested_model_name}' is not a Pydantic BaseModel."
                )
                continue

            transformed_items = []
            for item_dict in value:
                if not isinstance(item_dict, dict) or not item_dict:
                    continue
                try:
                    # Recursive call to hydrate nested model
                    hydrated_item = _hydrate_pydantic_model(
                        item_dict,
                        target_nested_model_class,
                        target_nested_object_descriptor,
                        schema,
                        registry,
                    )
                    transformed_items.append(hydrated_item)
                except Exception as e:
                    logger.warning(
                        f"Failed to hydrate nested item for field '{key}': {e}"
                    )

            if field_info.is_collection:
                nested_field_data[key] = transformed_items
            elif transformed_items:
                nested_field_data[key] = transformed_items[0]
            else:
                nested_field_data[key] = None
        elif key in schema_fields_info:  # Primitive field
            primitive_data[key] = value

    # Combine primitive and hydrated nested data
    combined_data = {**primitive_data, **nested_field_data}
    # Filter for fields actually in the target model
    model_fields = model_class.model_fields.keys()
    filtered_data = {k: v for k, v in combined_data.items() if k in model_fields}

    try:
        return model_class(**filtered_data)
    except Exception as e:
        logger.error(
            "Final instantiation error for {model_class_name} with data {filtered_data}: {error}",
            model_class_name=model_class.__name__,
            filtered_data=filtered_data,
            error=e,
        )
        raise InvalidArgumentError(
            f"Failed final instantiation for {model_class.__name__}: {e}"
        ) from e


def transform_dict_to_record(  # noqa: C901, PLR0912, PLR0915
    result_dict: dict[str, Any],
    object_identifier: ObjectIdentifier,
    schema: OrganizationSchemaDescriptor,
    schema_registry: SchemaRegistry | None = None,
    log_prefix: str = "",
) -> ModeledObjectRecord:
    """
    Transforms a raw dictionary result (from Cypher projection) into a
    StandardRecord or CustomRecord instance.

    Args:
        result_dict: The raw dictionary containing object data and related objects.
        object_identifier: The identifier of the primary object this dict represents.
        schema: The organization schema descriptor.
        schema_registry: Optional pre-initialized schema registry for lookups.

    Returns:
        A StandardRecord or CustomRecord instance.

    Raises:
        NotImplementedError: If attempting to transform a custom object type (not yet supported).
        ValueError: If the object type is unknown or schema information is missing.
        TypeError: If the input is not a dictionary.
    """
    if isinstance(result_dict, Node):
        result_dict = result_dict.properties
    if not isinstance(result_dict, dict):
        logger.warning(
            log_prefix
            + f"Expected a dictionary input, but got {type(result_dict)}, converting to dict."
        )
        result_dict = dict(result_dict)

    if not isinstance(object_identifier, StandardObjectIdentifier):
        # TODO: Implement CustomRecord transformation logic
        raise NotImplementedError(
            f"Transformation for CustomObjectIdentifier {object_identifier} not yet implemented."
        )

    registry = schema_registry or SchemaRegistry(schema)
    object_name = object_identifier.object_name

    # 1. Get the Pydantic Model and Object Descriptor
    model_class = STANDARD_MODEL_MAP.get(object_name.lower())
    object_descriptor = registry.get_object_descriptor(object_name)

    if not model_class:
        raise ValueError(f"Unknown standard object model name: {object_name}")
    if not object_descriptor:
        raise ValueError(f"Could not find ObjectDescriptor for {object_name} in schema")

    # 2. Categorize input keys based on schema
    primitive_data: dict[str, Any] = {}
    nested_field_raw_data: dict[str, list[dict[str, Any]]] = {}
    domain_relationship_raw_data: dict[RelationshipId, list[dict[str, Any]]] = {}

    # Get field info and relationship info from descriptor
    schema_fields_info: dict[str, FieldTypeInfo] = {
        registry.extract_field_name(f.field_identifier): registry.analyze_field_type(
            object_descriptor, f
        )
        for f in object_descriptor.fields
    }

    # Map relationship key (str(id) or name) to the actual RelationshipId object and target ObjectIdentifier
    schema_domain_rels: dict[str, tuple[RelationshipId, ObjectIdentifier]] = {}
    for rel in object_descriptor.outbound_relationships:
        schema_domain_rels[str(rel.id)] = (rel.id, rel.related_object_identifier)
        if rel.relationship_name and rel.relationship_name != str(rel.id):
            schema_domain_rels[rel.relationship_name] = (
                rel.id,
                rel.related_object_identifier,
            )

    # Get keys representing nested fields
    nested_field_keys = {
        key
        for key, info in schema_fields_info.items()
        if info.is_nested or (info.is_collection and info.nested_descriptor)
    }

    # Get keys representing primitive fields (those in fields_info but not nested)
    primitive_field_keys = set(schema_fields_info.keys()) - nested_field_keys

    for key, value in result_dict.items():
        # Check if it's a known domain relationship key
        if key in schema_domain_rels and isinstance(value, list):
            rel_id_obj, _ = schema_domain_rels[key]
            domain_relationship_raw_data[rel_id_obj] = [
                item for item in value if isinstance(item, dict) and item
            ]
        # Check if it's a known nested field key
        elif key in nested_field_keys and isinstance(value, list):
            nested_field_raw_data[key] = [
                item for item in value if isinstance(item, dict) and item
            ]
        # Check if it's a known primitive field key (or id)
        elif key in primitive_field_keys or key == "id":
            primitive_data[key] = value
        else:
            # Log if the key doesn't match any known schema definition for this object type
            logger.warning(
                f"Skipping key '{key}' in object {object_name} during transformation - not found in schema fields or relationships, or unexpected type: {type(value)}"
            )

    # 3. Recursively Transform Nested Fields using _hydrate_pydantic_model
    hydrated_nested_data: dict[str, Any] = {}
    for field_name, nested_raw_list in nested_field_raw_data.items():
        field_info = schema_fields_info.get(field_name)
        if not field_info or not field_info.nested_descriptor:
            logger.warning(
                log_prefix
                + f"Schema inconsistency: Nested field '{field_name}' lacks info/descriptor during hydration."
            )
            continue

        target_nested_model_name = registry.get_object_name(
            field_info.nested_descriptor
        )
        target_nested_model_class = STANDARD_MODEL_MAP.get(
            target_nested_model_name.lower()
        )
        target_nested_object_descriptor = registry.get_object_descriptor(
            target_nested_model_name
        )

        if not target_nested_model_class or not target_nested_object_descriptor:
            logger.warning(
                log_prefix
                + f"Could not find model/descriptor for nested type '{target_nested_model_name}' in field '{field_name}', cannot hydrate."
            )
            continue

        # Check if the retrieved class is actually a Pydantic model
        if not issubclass(target_nested_model_class, BaseModel):
            logger.warning(
                log_prefix
                + f"Retrieved class for '{target_nested_model_name}' is not a Pydantic BaseModel."
            )
            continue

        transformed_items = []
        for item_dict in nested_raw_list:
            if not item_dict:
                continue
            try:
                hydrated_item = _hydrate_pydantic_model(
                    item_dict,
                    target_nested_model_class,
                    target_nested_object_descriptor,
                    schema,
                    registry,
                )
                transformed_items.append(hydrated_item)
            except Exception as e:
                logger.warning(
                    log_prefix
                    + f"Skipping hydration for nested item in field '{field_name}': {e}"
                )

        # Store single object or list based on schema field type
        if field_info.is_collection:
            hydrated_nested_data[field_name] = transformed_items
        elif transformed_items:  # Single nested object
            hydrated_nested_data[field_name] = transformed_items[0]
        else:  # Handle case where nested object was expected but not found/transformed
            hydrated_nested_data[field_name] = None

    # 4. Instantiate the Pydantic 'data' model with primitives AND hydrated nested objects
    # Combine primitive fields with the hydrated nested field data
    combined_primitive_data = {**primitive_data, **hydrated_nested_data}
    model_fields = model_class.model_fields.keys()
    # Filter combined data to only include fields defined in the model class and are not None
    filtered_data_for_model = {
        k: v
        for k, v in combined_primitive_data.items()
        if k in model_fields and v is not None
    }
    try:
        data_instance = model_class(**filtered_data_for_model)
    except Exception as e:
        logger.error(
            log_prefix + f"Error instantiating {model_class.__name__}",
            exc_info=e,
        )
        raise InvalidArgumentError(
            log_prefix + f"Failed to instantiate {model_class.__name__}: {e}"
        ) from e

    # 5. Process Domain Relationships Recursively for related_records
    related_records_map: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = {}
    for relationship_id, related_raw_list in domain_relationship_raw_data.items():
        if not related_raw_list:
            continue
        try:
            # Get the target object identifier for this relationship
            # Try finding by string ID first, then by name if ID didn't match
            target_id_tuple = schema_domain_rels.get(str(relationship_id))
            if not target_id_tuple:
                # Fallback attempt if key was relationship_name (which must be str)
                target_id_tuple = schema_domain_rels.get(
                    relationship_id
                    if isinstance(relationship_id, str)
                    else str(relationship_id)
                )

            if not target_id_tuple:
                logger.warning(
                    log_prefix
                    + f"Could not determine target object identifier for relationship '{relationship_id}' in {object_name}"
                )
                continue

            target_object_identifier = target_id_tuple[1]  # Get the ObjectIdentifier

            related_records_list: list[ModeledObjectRecord] = []
            for related_dict in related_raw_list:
                if not related_dict:
                    continue
                # Recursive call to the main transformer function
                related_record = transform_dict_to_record(
                    result_dict=related_dict,
                    object_identifier=target_object_identifier,
                    schema=schema,
                    schema_registry=registry,
                    log_prefix=log_prefix,
                )
                related_records_list.append(related_record)

            if related_records_list:
                related_records_map[relationship_id] = tuple(related_records_list)

        except Exception as e:
            logger.warning(
                log_prefix
                + f"Failed to process domain relationship '{relationship_id}' for {object_name}: {e}"
            )

    # 6. Instantiate StandardRecord
    standard_record = StandardRecord[model_class](  # type: ignore[valid-type]
        object_id=object_identifier,
        data=data_instance,
        related_records=related_records_map,
        requested_relationships=set(
            related_records_map.keys()
        ),  # Reflects only processed domain relationships
        is_editable=True,  # Placeholder
    )

    return cast(ModeledObjectRecord, standard_record)


def get_node_ids_from_result_set(
    result_set: list[list[dict[str, Any]]],
    object_identifier: ObjectIdentifier,
    log_prefix: str = "",
) -> list[UUID]:
    """
    Transforms a list of raw dictionary results (from Cypher projection) into a
    list of StandardRecord or CustomRecord instances.

    Args:
        result_set: A list of raw dictionaries, each representing an object.
        object_identifier: The identifier of the primary object type in the results.
        schema: The organization schema descriptor.
        schema_registry: Optional pre-initialized schema registry.

    Returns:
        A list of StandardRecord or CustomRecord instances.
    """
    node_ids: list[UUID] = []
    # this list of dicts is a single row in the query result.
    for result_dict_list in result_set:
        try:
            result_dict = result_dict_list[0]
            if isinstance(result_dict, Node):
                result_dict = result_dict.properties
            node_ids.append(UUID(result_dict["id"]))
        except Exception as e:
            logger.info(
                log_prefix
                + " An unexpected error occurred when getting node ids from result set for {object_identifier}",
                object_identifier=object_identifier,
                error=e,
                exc_info=True,
            )
    return node_ids


def transform_result_set_to_records(
    result_set: list[list[dict[str, Any]]],
    object_identifier: ObjectIdentifier,
    schema: OrganizationSchemaDescriptor,
    schema_registry: SchemaRegistry | None = None,
    log_prefix: str = "",
) -> list[ModeledObjectRecord]:
    """
    Transforms a list of raw dictionary results (from Cypher projection) into a
    list of StandardRecord or CustomRecord instances.

    Args:
        result_set: A list of raw dictionaries, each representing an object.
        object_identifier: The identifier of the primary object type in the results.
        schema: The organization schema descriptor.
        schema_registry: Optional pre-initialized schema registry.

    Returns:
        A list of StandardRecord or CustomRecord instances.
    """
    records: list[ModeledObjectRecord] = []
    registry = schema_registry or SchemaRegistry(schema)
    # this list of dicts is a single row in the query result.
    for result_dict_list in result_set:
        try:
            record = transform_dict_to_record(
                # Use only the first dict in the list as it represents the first column.
                # This is fine for now as we only plan to return one column that represents a single object.
                result_dict=result_dict_list[0],
                object_identifier=object_identifier,
                schema=schema,
                schema_registry=registry,  # Pass the registry down
                log_prefix=log_prefix,
            )
            records.append(record)
        except (NotImplementedError, ValueError, TypeError) as e:
            logger.error(
                log_prefix
                + f"Failed to transform dictionary to record for {object_identifier}: {e}",
                exc_info=True,
            )
        except Exception as e:
            logger.error(
                log_prefix
                + "An unexpected error occurred during transformation for {object_identifier}",
                object_identifier=object_identifier,
                error=e,
                exc_info=True,
            )
            # Decide if you want to continue processing other records or raise the exception

    return records
