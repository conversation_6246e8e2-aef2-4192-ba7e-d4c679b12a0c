"""
Schema registry module for FalkorDB.
Provides utilities for schema validation and relationship lookup.
"""

from __future__ import annotations  # Ensure this is at the top

from dataclasses import dataclass
from typing import TYPE_CHECKING
from uuid import UUID

from salestech_be.common.type.metadata.common import (
    CustomFieldIdentifier,
    CustomObjectIdentifier,
    FieldIdentifier,
    FieldKind,
    ObjectIdentifier,
    StandardFieldIdentifier,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.field.field_type import FieldType
from salestech_be.common.type.metadata.field.field_type_property import (
    BaseFieldTypeProperty,
    ListFieldProperty,
    NestedObjectFieldProperty,
    standard_field_property_types,
)
from salestech_be.common.type.metadata.schema import (
    FieldDescriptor,
    InboundRelationship,
    ObjectDescriptor,
    OrganizationSchemaDescriptor,
    OutboundRelationship,
)
from salestech_be.falkordb.relationship_registry import (
    RelationshipDirection as PlanDirection,
)
from salestech_be.ree_logging import get_logger

# Conditional import for type hinting to avoid circular dependencies at runtime
if TYPE_CHECKING:
    pass  # No specific conditional imports needed for this version based on current content

logger = get_logger()


@dataclass(frozen=True)
class FieldTypeInfo:
    """Information about a field's type derived from schema analysis."""

    is_primitive: bool = False
    is_nested: bool = False
    is_collection: bool = False
    is_custom_field: bool = False
    nested_descriptor: ObjectDescriptor | None = None
    element_type_property: BaseFieldTypeProperty | None = None
    list_element_primitive_type: str | None = None
    element_primitive_type_str: str | None = None
    primitive_category: str | None = None


class SchemaValidationError(Exception):
    """Exception raised for schema validation errors."""


class SchemaRegistry:
    """
    Registry for schema validation and lookup.

    Provides efficient access to schema information through pre-indexed data structures
    and explicit dictionary-based caching.
    """

    EXPECTED_KEY_PARTS_LENGTH = 2

    def __init__(self, schema: OrganizationSchemaDescriptor | None = None) -> None:
        """Initialize the schema registry with an optional schema."""
        self.schema: OrganizationSchemaDescriptor | None = None

        # Indexed data (populated by load_schema)
        self.object_descriptors_by_name: dict[str, ObjectDescriptor] = {}
        self.relationships_by_source_and_id: dict[tuple[str, str], str] = {}
        self.field_types_by_object_and_field_id: dict[
            tuple[str, str], FieldTypeInfo
        ] = {}
        self.nested_field_relationships_by_object_and_field_id: dict[
            tuple[str, str], str
        ] = {}
        self.domain_object_relationships_by_object_name: dict[
            str, list[OutboundRelationship]
        ] = {}
        self.custom_associations_by_object_name: dict[
            str, list[OutboundRelationship | InboundRelationship]
        ] = {}
        self.inbound_domain_object_relationships_by_object_name: dict[
            str, list[InboundRelationship]
        ] = {}

        # Explicit Caches
        self._object_descriptor_cache: dict[
            str, ObjectDescriptor | None
        ] = {}  # key: object_name
        self._object_name_from_id_key_cache: dict[
            str, str
        ] = {}  # key: object_identifier_key
        self._field_name_from_id_key_cache: dict[
            str, str
        ] = {}  # key: field_identifier_key
        self._field_type_info_cache: dict[
            str, FieldTypeInfo
        ] = {}  # key: parent_obj_id_key + ":" + field_id_key
        self._is_domain_relationship_cache: dict[
            str, bool
        ] = {}  # key: obj_id_key + ":" + field_name_or_rel_id
        self._relationship_direction_cache: dict[
            str, PlanDirection
        ] = {}  # key: obj_id_key + ":" + relationship_id_str

        if schema:
            self.load_schema(schema)

    # --- Key Generation Helper Methods ---
    def _make_object_identifier_key(self, identifier: ObjectIdentifier) -> str:
        if isinstance(identifier, StandardObjectIdentifier):
            return f"std_obj:{identifier.object_name}"
        elif isinstance(identifier, CustomObjectIdentifier):
            return f"custom_obj:{identifier.object_id!s}"
        return f"unknown_obj_id_type:{identifier!s}"

    def _make_field_identifier_key(self, identifier: FieldIdentifier) -> str:
        if isinstance(identifier, StandardFieldIdentifier):
            return f"std_field:{identifier.field_name}"
        elif isinstance(identifier, CustomFieldIdentifier):
            return f"custom_field:{identifier.field_id!s}"
        return f"unknown_field_id_type:{identifier!s}"

    def _clear_caches(self) -> None:
        """Clears all internal caches."""
        self._object_descriptor_cache.clear()
        self._object_name_from_id_key_cache.clear()
        self._field_name_from_id_key_cache.clear()
        self._field_type_info_cache.clear()
        self._is_domain_relationship_cache.clear()
        self._relationship_direction_cache.clear()
        logger.debug("SchemaRegistry caches cleared.")

    def load_schema(self, schema: OrganizationSchemaDescriptor) -> None:  # noqa: C901, PLR0912, PLR0915
        """
        Load and index a schema for efficient lookups. Clears existing caches.
        """
        self._clear_caches()
        self.schema = schema

        # Clear existing indexed data
        self.object_descriptors_by_name.clear()
        self.relationships_by_source_and_id.clear()
        self.field_types_by_object_and_field_id.clear()
        self.nested_field_relationships_by_object_and_field_id.clear()
        self.domain_object_relationships_by_object_name.clear()
        self.custom_associations_by_object_name.clear()
        self.inbound_domain_object_relationships_by_object_name.clear()

        if not schema:
            return

        # First pass: index all object descriptors by their actual name
        for obj_desc in schema.objects:
            # Use the direct (non-cached for this specific call) method to get name for indexing
            obj_name = self._get_object_name_from_identifier_uncached(
                obj_desc.object_identifier
            )
            self.object_descriptors_by_name[obj_name] = obj_desc
            # Initialize relationship lists for this object by its actual name
            self.domain_object_relationships_by_object_name[obj_name] = []
            self.custom_associations_by_object_name.setdefault(
                obj_name, []
            )  # Ensure list exists
            self.inbound_domain_object_relationships_by_object_name[obj_name] = []

        # Second pass: index relationships and field types
        for obj_desc in schema.objects:
            source_obj_id_key = self._make_object_identifier_key(
                obj_desc.object_identifier
            )
            source_obj_name = self.get_object_name_from_id_key(
                source_obj_id_key
            )  # Uses cache

            # Index outbound relationships
            for outbound_rel in obj_desc.outbound_relationships:
                target_obj_id_key = self._make_object_identifier_key(
                    outbound_rel.related_object_identifier
                )
                target_obj_name = self.get_object_name_from_id_key(target_obj_id_key)
                rel_id_str = str(outbound_rel.id)
                self.relationships_by_source_and_id[(source_obj_name, rel_id_str)] = (
                    target_obj_name
                )

                # is_domain_relationship uses its own cache
                if self.is_domain_relationship(
                    obj_desc, outbound_rel.relationship_name
                ):  # Use public method
                    if isinstance(
                        outbound_rel.id, str
                    ):  # Standard domain relationships
                        self.domain_object_relationships_by_object_name[
                            source_obj_name
                        ].append(outbound_rel)
                    elif isinstance(
                        outbound_rel.id, UUID
                    ):  # Custom associations treated as domain if name matches
                        self.custom_associations_by_object_name[source_obj_name].append(
                            outbound_rel
                        )
                elif isinstance(outbound_rel.id, UUID):  # Other custom associations
                    self.custom_associations_by_object_name[source_obj_name].append(
                        outbound_rel
                    )

            # Index inbound relationships
            for inbound_rel in obj_desc.inbound_relationships:
                # The 'related_object_identifier' on an InboundRelationship refers to the object
                # that has the outbound link *to us*.
                original_source_obj_id_key = self._make_object_identifier_key(
                    inbound_rel.related_object_identifier
                )
                original_source_obj_name = self.get_object_name_from_id_key(
                    original_source_obj_id_key
                )
                rel_id_str = str(inbound_rel.id)
                self.relationships_by_source_and_id[(source_obj_name, rel_id_str)] = (
                    original_source_obj_name
                )

                if self.is_domain_relationship(
                    obj_desc, inbound_rel.relationship_name
                ):  # Use public method
                    if isinstance(inbound_rel.id, str):
                        self.inbound_domain_object_relationships_by_object_name[
                            source_obj_name
                        ].append(inbound_rel)
                    elif isinstance(inbound_rel.id, UUID):
                        self.custom_associations_by_object_name[source_obj_name].append(
                            inbound_rel
                        )
                elif isinstance(inbound_rel.id, UUID):
                    self.custom_associations_by_object_name[source_obj_name].append(
                        inbound_rel
                    )

            # Index field types and implicit relationships
            for field_desc in obj_desc.fields:
                field_id_key = self._make_field_identifier_key(
                    field_desc.field_identifier
                )
                # analyze_field_type uses its own cache
                field_info = self.analyze_field_type(
                    obj_desc, field_desc
                )  # Use public method

                # Use the string representation of the identifier for direct indexing
                field_identifier_str = str(field_desc.field_identifier)
                self.field_types_by_object_and_field_id[
                    (source_obj_name, field_identifier_str)
                ] = field_info

                # Also index by simple field name if different
                simple_field_name = self.get_field_name_from_id_key(
                    field_id_key
                )  # Uses cache
                if simple_field_name and simple_field_name != field_identifier_str:
                    self.field_types_by_object_and_field_id[
                        (source_obj_name, simple_field_name)
                    ] = field_info

                if (field_info.is_nested and field_info.nested_descriptor) or (
                    field_info.is_collection and field_info.nested_descriptor
                ):
                    nested_obj_desc = (
                        field_info.nested_descriptor
                    )  # This is an ObjectDescriptor
                    nested_obj_id_key = self._make_object_identifier_key(
                        nested_obj_desc.object_identifier
                    )
                    nested_obj_name = self.get_object_name_from_id_key(
                        nested_obj_id_key
                    )  # Uses cache

                    self.nested_field_relationships_by_object_and_field_id[
                        (source_obj_name, field_identifier_str)
                    ] = nested_obj_name
                    if simple_field_name and simple_field_name != field_identifier_str:
                        self.nested_field_relationships_by_object_and_field_id[
                            (source_obj_name, simple_field_name)
                        ] = nested_obj_name
        logger.debug("Schema loaded and indexed.")

    # --- Cached Getters using explicit dictionaries ---

    def get_object_descriptor(self, object_name: str) -> ObjectDescriptor | None:
        """Cached: Get an object descriptor by its string name."""
        if object_name in self._object_descriptor_cache:
            return self._object_descriptor_cache[object_name]

        # Fallback to the indexed data if not in cache (should be populated by load_schema)
        descriptor = self.object_descriptors_by_name.get(object_name)
        self._object_descriptor_cache[object_name] = (
            descriptor  # Cache result, even if None
        )
        if descriptor is None:
            logger.debug(
                f"Object descriptor not found for name: {object_name} (after checking index)"
            )
        return descriptor

    def _get_object_name_from_identifier_uncached(
        self, identifier: ObjectIdentifier
    ) -> str:
        """Internal Uncached: Get object name directly from identifier. Used during load_schema priming."""
        if isinstance(identifier, StandardObjectIdentifier):
            return identifier.object_name
        elif isinstance(identifier, CustomObjectIdentifier):
            return str(identifier.object_id)
        logger.warning(
            f"Unknown ObjectIdentifier type in _get_object_name_from_identifier_uncached: {type(identifier)}"
        )
        return str(identifier)

    def get_object_name_from_id_key(self, object_identifier_key: str) -> str:
        """Cached: Get object name from its identifier key."""
        if object_identifier_key in self._object_name_from_id_key_cache:
            return self._object_name_from_id_key_cache[object_identifier_key]

        # Cache miss, compute by parsing key
        name: str
        key_parts = object_identifier_key.split(":", 1)
        if len(key_parts) == self.EXPECTED_KEY_PARTS_LENGTH:
            id_type, id_val = key_parts
            if id_type == "std_obj":
                name = id_val
            elif id_type == "custom_obj":
                name = id_val  # For custom objects, UUID string is the name
            else:
                name = object_identifier_key  # Fallback if key format is unexpected
                logger.warning(
                    f"Unexpected object_identifier_key format: {object_identifier_key}"
                )
        else:
            name = object_identifier_key  # Fallback
            logger.warning(f"Malformed object_identifier_key: {object_identifier_key}")

        self._object_name_from_id_key_cache[object_identifier_key] = name
        return name

    def get_field_name_from_id_key(self, field_identifier_key: str) -> str:
        """Cached: Get field name/ID string from its identifier key."""
        if field_identifier_key in self._field_name_from_id_key_cache:
            return self._field_name_from_id_key_cache[field_identifier_key]

        # Cache miss, compute by parsing key
        name: str
        key_parts = field_identifier_key.split(":", 1)
        if len(key_parts) == self.EXPECTED_KEY_PARTS_LENGTH:
            id_type, id_val = key_parts
            if id_type == "std_field":
                name = id_val
            elif id_type == "custom_field":
                name = id_val  # For custom fields, UUID string is the name/ID
            else:
                name = field_identifier_key  # Fallback
                logger.warning(
                    f"Unexpected field_identifier_key format: {field_identifier_key}"
                )
        else:
            name = field_identifier_key  # Fallback
            logger.warning(f"Malformed field_identifier_key: {field_identifier_key}")

        self._field_name_from_id_key_cache[field_identifier_key] = name
        return name

    def _get_field_descriptor_from_keys(
        self, parent_object_identifier_key: str, field_identifier_key: str
    ) -> FieldDescriptor | None:
        """Internal: Find FieldDescriptor using keys. Not directly cached itself, relies on cached getters."""
        parent_object_name = self.get_object_name_from_id_key(
            parent_object_identifier_key
        )
        parent_descriptor = self.get_object_descriptor(parent_object_name)  # Uses cache
        if not parent_descriptor:
            logger.debug(
                f"Parent descriptor not found for key {parent_object_identifier_key} (name: {parent_object_name})"
            )
            return None

        # Get the string field name/id that we are looking for in the parent_descriptor
        # This is the actual ID string (e.g. "my_field" or a UUID string)
        target_field_str_id_from_key = self.get_field_name_from_id_key(
            field_identifier_key
        )  # Uses cache

        for field_desc in parent_descriptor.fields:
            # Compare against the string representation of the field's *actual* identifier.
            # For standard fields, this is field_name. For custom, it's field_id (UUID as str).
            actual_field_identifier_str = self._get_field_name_from_identifier_uncached(
                field_desc.field_identifier
            )
            if actual_field_identifier_str == target_field_str_id_from_key:
                return field_desc

        logger.debug(
            f"Field not found for key {field_identifier_key} in parent {parent_object_name}"
        )
        return None

    def _get_field_name_from_identifier_uncached(
        self, identifier: FieldIdentifier
    ) -> str:
        """Internal Uncached: Get field name/ID string directly from identifier."""
        if isinstance(identifier, StandardFieldIdentifier):
            return identifier.field_name
        elif isinstance(identifier, CustomFieldIdentifier):
            return str(identifier.field_id)
        logger.warning(
            f"Unknown FieldIdentifier type in _get_field_name_from_identifier_uncached: {type(identifier)}"
        )
        return str(identifier)

    def _analyze_field_type_cached_with_keys(  # noqa: C901, PLR0911, PLR0912, PLR0915
        self, parent_object_identifier_key: str, field_identifier_key: str
    ) -> FieldTypeInfo:
        """Cached: Analyze field type using string keys for parent object and field."""
        cache_key = f"{parent_object_identifier_key}:{field_identifier_key}"
        if cache_key in self._field_type_info_cache:
            return self._field_type_info_cache[cache_key]

        if not self.schema:  # Should be loaded by load_schema
            logger.warning(
                "Schema not loaded in analyze_field_type, returning default."
            )
            default_info = FieldTypeInfo(
                is_primitive=True, primitive_category="schema_not_loaded"
            )
            self._field_type_info_cache[cache_key] = default_info
            return default_info

        field_descriptor = self._get_field_descriptor_from_keys(
            parent_object_identifier_key, field_identifier_key
        )

        if not field_descriptor:
            logger.warning(
                f"Field descriptor not found for keys {parent_object_identifier_key}, {field_identifier_key} in analyze_field_type"
            )
            default_info = FieldTypeInfo(
                is_primitive=True, primitive_category="field_not_found_in_parent"
            )
            self._field_type_info_cache[cache_key] = default_info
            return default_info

        # --- Actual analysis logic ---
        if field_descriptor.field_kind == FieldKind.CUSTOM:
            info = FieldTypeInfo(is_custom_field=True)
            self._field_type_info_cache[cache_key] = info
            return info

        if (
            not hasattr(field_descriptor, "field_type_property")
            or field_descriptor.field_type_property is None
        ):
            info = FieldTypeInfo(is_primitive=True, primitive_category="no_type_prop")
            self._field_type_info_cache[cache_key] = info
            return info

        field_type_prop = field_descriptor.field_type_property

        if isinstance(field_type_prop, NestedObjectFieldProperty):
            # Get the ObjectDescriptor for the nested type
            nested_obj_name = self._get_object_name_from_identifier_uncached(
                field_type_prop.object_identifier
            )
            nested_desc = self.get_object_descriptor(nested_obj_name)  # Uses cache
            info = FieldTypeInfo(is_nested=True, nested_descriptor=nested_desc)
            self._field_type_info_cache[cache_key] = info
            return info

        if isinstance(field_type_prop, ListFieldProperty):
            element_type_prop = field_type_prop.element_field_type_property
            nested_desc_for_list: ObjectDescriptor | None = None
            element_primitive_type_str: str | None = None

            if isinstance(element_type_prop, NestedObjectFieldProperty):
                nested_list_obj_name = self._get_object_name_from_identifier_uncached(
                    element_type_prop.object_identifier
                )
                nested_desc_for_list = self.get_object_descriptor(
                    nested_list_obj_name
                )  # Uses cache
            elif (
                hasattr(element_type_prop, "field_type")
                and element_type_prop.field_type
            ):  # Check if field_type is not None
                common_ft = element_type_prop.field_type
                if common_ft in (
                    FieldType.TEXT,
                    FieldType.TEXT_AREA,
                    FieldType.PHONE_NUMBER,
                    FieldType.EMAIL,
                    FieldType.URL,
                ):
                    element_primitive_type_str = "string"
                elif common_ft in (
                    FieldType.CURRENCY,
                    FieldType.PERCENT,
                    FieldType.TIMESTAMP,
                    FieldType.LOCAL_DATE,
                    FieldType.LOCAL_TIME_OF_DAY,
                    FieldType.TIME_OF_DAY,
                    FieldType.NUMERIC,
                ):
                    element_primitive_type_str = "number"
                elif common_ft in (
                    FieldType.UUID,
                    FieldType.SINGLE_SELECT,
                    FieldType.MULTI_SELECT,
                ):
                    element_primitive_type_str = "other_primitive"

                if element_primitive_type_str is None:  # Default if not categorized
                    logger.debug(
                        f"Uncategorized list element type: {common_ft}, defaulting to text primitive string."
                    )
                    element_primitive_type_str = (
                        "text"  # Default for other uncategorized FieldTypes
                    )
            else:
                logger.debug(
                    f"List element_type_prop has no field_type: {element_type_prop}, defaulting to unknown primitive string."
                )
                element_primitive_type_str = "unknown"

            info = FieldTypeInfo(
                is_collection=True,
                nested_descriptor=nested_desc_for_list,
                element_type_property=element_type_prop,
                list_element_primitive_type=element_primitive_type_str,
            )
            self._field_type_info_cache[cache_key] = info
            return info

        if isinstance(field_type_prop, standard_field_property_types):
            primitive_category_str: str
            if field_type_prop.field_type in (
                FieldType.TEXT,
                FieldType.TEXT_AREA,
                FieldType.PHONE_NUMBER,
                FieldType.EMAIL,
                FieldType.URL,
            ):
                primitive_category_str = "string"
            elif field_type_prop.field_type in {
                FieldType.NUMERIC,
                FieldType.CURRENCY,
                FieldType.PERCENT,
                FieldType.TIMESTAMP,
                FieldType.LOCAL_DATE,
                FieldType.LOCAL_TIME_OF_DAY,
                FieldType.TIME_OF_DAY,
            }:
                primitive_category_str = "number"
            else:  # Default for other standard primitive types (e.g. UUID, SINGLE_SELECT, MULTI_SELECT, BOOLEAN, etc.)
                primitive_category_str = "other_primitive"
            info = FieldTypeInfo(
                is_primitive=True,
                primitive_category=primitive_category_str,
                element_primitive_type_str=primitive_category_str,
            )
            self._field_type_info_cache[cache_key] = info
            return info

        logger.warning(f"Unknown field_type_prop encountered: {type(field_type_prop)}")
        info = FieldTypeInfo(
            is_primitive=True, primitive_category="unknown_type_prop_fallback"
        )
        self._field_type_info_cache[cache_key] = info
        return info

    def analyze_field_type(
        self, parent_descriptor: ObjectDescriptor, field_descriptor: FieldDescriptor
    ) -> FieldTypeInfo:
        """Analyze field type. Uses cached, key-based implementation internally."""
        parent_object_id_key = self._make_object_identifier_key(
            parent_descriptor.object_identifier
        )
        field_id_key = self._make_field_identifier_key(
            field_descriptor.field_identifier
        )
        return self._analyze_field_type_cached_with_keys(
            parent_object_id_key, field_id_key
        )

    def _is_domain_relationship_cached_with_key(
        self, source_object_identifier_key: str, field_name_or_rel_id: str
    ) -> bool:
        """Cached: Determine if a field/relationship ID on a source object is a domain relationship."""
        cache_key = f"{source_object_identifier_key}:{field_name_or_rel_id}"
        if cache_key in self._is_domain_relationship_cache:
            return self._is_domain_relationship_cache[cache_key]

        source_object_name = self.get_object_name_from_id_key(
            source_object_identifier_key
        )  # Uses cache
        source_descriptor = self.get_object_descriptor(source_object_name)  # Uses cache

        if not source_descriptor:
            self._is_domain_relationship_cache[cache_key] = False
            return False

        # Domain relationships are typically identified by having a string ID and being explicitly in outbound/inbound lists.
        # Custom associations usually have UUIDs as IDs.
        for rel in source_descriptor.outbound_relationships:
            if (
                rel.relationship_name == field_name_or_rel_id
                or str(rel.id) == field_name_or_rel_id
            ) and isinstance(rel.id, str):
                self._is_domain_relationship_cache[cache_key] = True
                return True
        for inbound_rel_check in (
            source_descriptor.inbound_relationships
        ):  # Changed rel to inbound_rel_check
            if (
                inbound_rel_check.relationship_name == field_name_or_rel_id
                or str(inbound_rel_check.id) == field_name_or_rel_id
            ) and isinstance(inbound_rel_check.id, str):
                self._is_domain_relationship_cache[cache_key] = True
                return True

        self._is_domain_relationship_cache[cache_key] = False
        return False

    def is_domain_relationship(
        self, source_descriptor: ObjectDescriptor, field_name_or_rel_id: str
    ) -> bool:
        """Determine if a field/relationship ID on a source object is a domain relationship. Uses cached, key-based implementation internally."""
        source_object_id_key = self._make_object_identifier_key(
            source_descriptor.object_identifier
        )
        return self._is_domain_relationship_cached_with_key(
            source_object_id_key, field_name_or_rel_id
        )

    def _get_relationship_direction_cached_with_key(  # noqa: C901, PLR0911, PLR0912
        self, source_object_identifier_key: str | None, relationship_id_str: str
    ) -> PlanDirection:
        """Cached: Determine relationship direction using source object key and relationship ID string."""
        cache_key = f"{source_object_identifier_key or 'None'}:{relationship_id_str}"
        if cache_key in self._relationship_direction_cache:
            return self._relationship_direction_cache[cache_key]

        if not source_object_identifier_key:
            logger.warning(
                f"Source object key not provided for relationship '{relationship_id_str}'. Defaulting to OUTGOING."
            )
            self._relationship_direction_cache[cache_key] = PlanDirection.OUTGOING
            return PlanDirection.OUTGOING

        source_object_name = self.get_object_name_from_id_key(
            source_object_identifier_key
        )  # Uses cache
        source_descriptor = self.get_object_descriptor(source_object_name)  # Uses cache

        if not source_descriptor:
            logger.warning(
                f"Source descriptor '{source_object_name}' not found for key '{source_object_identifier_key}'. Defaulting to OUTGOING for relationship '{relationship_id_str}'."
            )
            self._relationship_direction_cache[cache_key] = PlanDirection.OUTGOING
            return PlanDirection.OUTGOING

        # Check explicit outbound relationships
        for rel in source_descriptor.outbound_relationships:
            if (
                str(rel.id) == relationship_id_str
                or rel.relationship_name == relationship_id_str
            ):
                # rel.direction is Relationship.Direction enum (StrEnum)
                direction_value = rel.direction.value
                if direction_value == PlanDirection.OUTGOING.value:
                    result = PlanDirection.OUTGOING
                elif direction_value == PlanDirection.INCOMING.value:
                    result = PlanDirection.INCOMING
                elif direction_value == PlanDirection.BOTH.value:
                    result = PlanDirection.BOTH
                else:
                    logger.warning(
                        f"Unknown direction '{direction_value}' in outbound rel for '{relationship_id_str}'. Defaulting OUTGOING."
                    )
                    result = PlanDirection.OUTGOING
                self._relationship_direction_cache[cache_key] = result
                return result

        # Check explicit inbound relationships
        for inbound_rel_check in (
            source_descriptor.inbound_relationships
        ):  # Changed rel to inbound_rel_check
            if (
                str(inbound_rel_check.id) == relationship_id_str
                or inbound_rel_check.relationship_name == relationship_id_str
            ):
                # By definition, if it's in our inbound list, it's INCOMING to us.
                self._relationship_direction_cache[cache_key] = PlanDirection.INCOMING
                return PlanDirection.INCOMING

        # Check if relationship_id_str matches a field name that implies a nested structure (outgoing)
        for field_desc in source_descriptor.fields:
            field_id_key = self._make_field_identifier_key(field_desc.field_identifier)
            # Check against simple field name and full string identifier
            simple_field_name = self.get_field_name_from_id_key(field_id_key)
            full_field_id_str = self._get_field_name_from_identifier_uncached(
                field_desc.field_identifier
            )

            if relationship_id_str in (simple_field_name, full_field_id_str):
                if not self.schema:  # Should be loaded
                    logger.error(
                        "Schema not available in get_relationship_direction for field type analysis."
                    )
                    self._relationship_direction_cache[cache_key] = (
                        PlanDirection.OUTGOING
                    )  # Fallback
                    return PlanDirection.OUTGOING

                # Use cached analyze_field_type with keys
                field_info = self.analyze_field_type(
                    source_descriptor, field_desc
                )  # Use public method
                if field_info.is_nested or (
                    field_info.is_collection and field_info.nested_descriptor
                ):
                    self._relationship_direction_cache[cache_key] = (
                        PlanDirection.OUTGOING
                    )
                    return PlanDirection.OUTGOING
                # If it matches a field name but isn't nested/collection_of_nested, it might be a primitive.
                # This case might need more nuanced handling if primitive fields can be confused with relationship IDs.
                # For now, if it matched a field name and wasn't a structural link, we might assume it's not a navigable relationship here.
                # However, the problem asks for direction, so if it's not explicitly defined, OUTGOING is a common default.

        logger.warning(
            f"Relationship '{relationship_id_str}' direction not explicitly found or derived for source '{source_object_name}'. Defaulting to OUTGOING."
        )
        self._relationship_direction_cache[cache_key] = PlanDirection.OUTGOING
        return PlanDirection.OUTGOING

    def get_relationship_direction(
        self, source_descriptor: ObjectDescriptor | None, relationship_id_str: str
    ) -> PlanDirection:
        """Determine relationship direction. Uses cached, key-based implementation internally."""
        source_object_id_key: str | None = None
        if source_descriptor:
            source_object_id_key = self._make_object_identifier_key(
                source_descriptor.object_identifier
            )
        return self._get_relationship_direction_cached_with_key(
            source_object_id_key, relationship_id_str
        )

    # --- Public Methods (Orchestrators or Simple Getters, using cached methods internally) ---

    # Uncached, but uses cached get_object_descriptor
    def get_object_descriptors_by_names(
        self, object_names: list[str]
    ) -> dict[str, ObjectDescriptor]:
        result: dict[str, ObjectDescriptor] = {}
        for name in object_names:
            descriptor = self.get_object_descriptor(name)  # Uses cache
            if descriptor:
                result[name] = descriptor
        return result

    # Uncached, uses cached methods internally
    def get_object_name(self, descriptor: ObjectDescriptor) -> str:
        """Get object name from ObjectDescriptor (uses cached method internally via key)."""
        key = self._make_object_identifier_key(descriptor.object_identifier)
        return self.get_object_name_from_id_key(key)

    # Uncached, uses cached methods internally
    def _get_object_name_from_identifier(
        self, object_id: ObjectIdentifier
    ) -> str:  # Retaining for potential internal direct calls
        """Get object name from ObjectIdentifier (uses cached method internally via key)."""
        key = self._make_object_identifier_key(object_id)
        return self.get_object_name_from_id_key(key)

    # Uncached, uses cached methods internally
    def extract_field_name(self, field_identifier: FieldIdentifier) -> str:
        """Extract field name/ID string from FieldIdentifier (uses cached method internally via key)."""
        key = self._make_field_identifier_key(field_identifier)
        return self.get_field_name_from_id_key(key)

    def validate_relationship(
        self, source_descriptor: ObjectDescriptor, relationship_id_str: str
    ) -> bool:
        """Validate that a relationship exists on the source object."""
        # This method relies on data indexed during load_schema or direct checks on the descriptor.
        # Caching for this specific validation path might be overkill if source_descriptor is already available.
        source_obj_name = self.get_object_name(source_descriptor)

        # Check explicit relationships directly from the descriptor
        if any(
            str(rel.id) == relationship_id_str
            or rel.relationship_name == relationship_id_str
            for rel in source_descriptor.outbound_relationships
        ) or any(
            str(rel.id) == relationship_id_str
            or rel.relationship_name == relationship_id_str
            for rel in source_descriptor.inbound_relationships
        ):
            return True

        # Check implicit relationships from fields
        if self.schema:  # Ensure schema is loaded for field analysis
            self._make_object_identifier_key(source_descriptor.object_identifier)
            for field_desc in source_descriptor.fields:
                # Check if relationship_id_str matches field name or full field identifier string
                field_id_key = self._make_field_identifier_key(
                    field_desc.field_identifier
                )
                simple_field_name = self.get_field_name_from_id_key(field_id_key)
                full_field_id_str = self._get_field_name_from_identifier_uncached(
                    field_desc.field_identifier
                )

                if relationship_id_str in (simple_field_name, full_field_id_str):
                    field_info = self.analyze_field_type(
                        source_descriptor, field_desc
                    )  # Use public method
                    if field_info.is_nested or (
                        field_info.is_collection and field_info.nested_descriptor
                    ):
                        return True

        # Fallback check using the indexed relationship dictionaries (populated by load_schema)
        if (
            source_obj_name,
            relationship_id_str,
        ) in self.relationships_by_source_and_id or (
            source_obj_name,
            relationship_id_str,
        ) in self.nested_field_relationships_by_object_and_field_id:
            return True

        raise SchemaValidationError(
            f"Relationship or nested field '{relationship_id_str}' not found on object '{source_obj_name}'"
        )

    def get_related_object_name(
        self, source_descriptor: ObjectDescriptor, relationship_id_str: str
    ) -> str:
        """Get the name of the object on the other side of a relationship."""
        source_obj_name = self.get_object_name(source_descriptor)

        # Check indexed explicit relationships
        if (
            source_obj_name,
            relationship_id_str,
        ) in self.relationships_by_source_and_id:
            return self.relationships_by_source_and_id[
                (source_obj_name, relationship_id_str)
            ]
        # Check indexed nested field relationships
        if (
            source_obj_name,
            relationship_id_str,
        ) in self.nested_field_relationships_by_object_and_field_id:
            return self.nested_field_relationships_by_object_and_field_id[
                (source_obj_name, relationship_id_str)
            ]

        # Fallback to iterate through descriptor relationships (should be rare if load_schema is complete)
        for rel in source_descriptor.outbound_relationships:
            if (
                str(rel.id) == relationship_id_str
                or rel.relationship_name == relationship_id_str
            ):
                return self.get_object_name_from_id_key(
                    self._make_object_identifier_key(rel.related_object_identifier)
                )
        for inbound_rel_check in (
            source_descriptor.inbound_relationships
        ):  # Changed rel to inbound_rel_check
            if (
                str(inbound_rel_check.id) == relationship_id_str
                or inbound_rel_check.relationship_name == relationship_id_str
            ):
                return self.get_object_name_from_id_key(
                    self._make_object_identifier_key(
                        inbound_rel_check.related_object_identifier
                    )
                )

        # Fallback: Check nested fields by analyzing them
        self._make_object_identifier_key(source_descriptor.object_identifier)
        for field_desc in source_descriptor.fields:
            field_id_key = self._make_field_identifier_key(field_desc.field_identifier)
            simple_field_name = self.get_field_name_from_id_key(field_id_key)
            full_field_id_str = self._get_field_name_from_identifier_uncached(
                field_desc.field_identifier
            )

            if relationship_id_str in (simple_field_name, full_field_id_str):
                field_info = self.analyze_field_type(
                    source_descriptor, field_desc
                )  # Use public method
                if field_info.nested_descriptor:
                    # field_info.nested_descriptor is an ObjectDescriptor
                    return self.get_object_name(field_info.nested_descriptor)

        raise SchemaValidationError(
            f"Relationship '{relationship_id_str}' not found on object '{source_obj_name}'"
        )

    def get_field_type_info(
        self, object_name: str, field_identifier_str: str
    ) -> FieldTypeInfo | None:
        """Get field type information. Uses data indexed by load_schema which internally uses cached analyze_field_type."""
        return self.field_types_by_object_and_field_id.get(
            (object_name, field_identifier_str)
        )

    def is_nested_structure(
        self, source_descriptor: ObjectDescriptor, field_name: str
    ) -> bool:
        """Determine if a field represents a nested structure. Relies on cached methods."""
        if not self.schema:
            return False  # Schema must be loaded

        self._make_object_identifier_key(source_descriptor.object_identifier)

        # Iterate through fields to find the one matching field_name
        for field_desc in source_descriptor.fields:
            field_id_key = self._make_field_identifier_key(field_desc.field_identifier)
            # Check against simple name and full string ID
            simple_name = self.get_field_name_from_id_key(field_id_key)
            full_id_str = self._get_field_name_from_identifier_uncached(
                field_desc.field_identifier
            )

            if field_name in (simple_name, full_id_str):
                field_info = self.analyze_field_type(
                    source_descriptor, field_desc
                )  # Use public method

                is_structurally_nested = field_info.is_nested or (
                    field_info.is_collection
                    and field_info.nested_descriptor is not None
                )

                # A field is a nested structure if it's structurally nested AND NOT a domain relationship
                if is_structurally_nested and not self.is_domain_relationship(
                    source_descriptor, field_name
                ):
                    return True
                return False  # Found the field, but it's either not structurally nested or it's a domain relationship

        logger.debug(
            f"Field '{field_name}' not found in descriptor for is_nested_structure check on {self.get_object_name(source_descriptor)}."
        )
        return False  # Field not found

    def get_field_relationship_type(
        self, source_descriptor: ObjectDescriptor, field_name: str
    ) -> str:
        """Determine the relationship type of a field. Relies on cached methods."""
        self._make_object_identifier_key(
            source_descriptor.object_identifier
        )  # Keep for internal use if needed

        # is_domain_relationship uses its own cache
        if self.is_domain_relationship(
            source_descriptor, field_name
        ):  # Use public method
            return "domain_relationship"

        # is_nested_structure uses its own set of cached calls
        if self.is_nested_structure(source_descriptor, field_name):  # Use public method
            return "nested_structure"

        # If not domain or nested, find the field to analyze its basic type
        target_field_desc: FieldDescriptor | None = None
        for f_desc in source_descriptor.fields:
            # Check against simple name and full string ID
            f_id_key = self._make_field_identifier_key(f_desc.field_identifier)
            simple_f_name = self.get_field_name_from_id_key(f_id_key)  # Uses cache
            full_f_id_str = self._get_field_name_from_identifier_uncached(
                f_desc.field_identifier
            )
            if field_name in (simple_f_name, full_f_id_str):
                target_field_desc = f_desc
                break

        if not target_field_desc:
            logger.debug(
                f"Field '{field_name}' not found in get_field_relationship_type on {self.get_object_name(source_descriptor)}."
            )
            return "unknown"

        # analyze_field_type uses its own cache
        field_info = self.analyze_field_type(
            source_descriptor, target_field_desc
        )  # Use public method

        if field_info.is_custom_field:
            return "custom_field"
        if field_info.is_primitive:
            return "primitive"

        logger.debug(
            f"Field '{field_name}' on {self.get_object_name(source_descriptor)} did not resolve to a known relationship type."
        )
        return "unknown"

    # --- Direct accessors for indexed data (populated by load_schema) ---
    def get_domain_relationships(self, object_name: str) -> list[OutboundRelationship]:
        return self.domain_object_relationships_by_object_name.get(object_name, [])

    def get_custom_associations(
        self, object_name: str
    ) -> list[OutboundRelationship | InboundRelationship]:
        return self.custom_associations_by_object_name.get(object_name, [])

    def get_inbound_domain_relationships(
        self, object_name: str
    ) -> list[InboundRelationship]:
        return self.inbound_domain_object_relationships_by_object_name.get(
            object_name, []
        )
