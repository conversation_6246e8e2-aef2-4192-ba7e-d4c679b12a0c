from pydantic import BaseModel

from salestech_be.common.query_util.baseline_filter_extraction import (
    BaselineFilters,
)
from salestech_be.common.query_util.sort_schema import (
    NonRelationalSortingSpec,
)
from salestech_be.common.type.metadata.common import ObjectIdentifier


class DomainFetchHints(BaseModel):
    """
    DomainFetchContext is used to encapsulate the parameters needed for fetching domain objects in a paginated and sorted manner.

    The context contains:
    - non_relational_sorting_spec: Specifies how to sort the domain objects using only direct field values
      (no relationship fields). This ensures consistent sorting behavior when fetching from data sources.

    - offset: The number of records to skip before starting to return results. Used for pagination to
      determine which "page" of results to return.

    - limit: The maximum number of records to return in a single fetch. Used along with offset to
      implement pagination by limiting the result set size.

    - baseline_filters: A group of baseline filters that can be used to optimize the fetch query.

    This context object helps standardize how domain objects are fetched across different data sources
    while supporting common access patterns like pagination and sorting.
    """

    baseline_filters: BaselineFilters | None = None
    non_relational_sorting_spec: NonRelationalSortingSpec
    limit: int | None = None
    # whether to exclude invisible objects from the result set, e.g. archived or deleted
    exclude_invisible: bool = True
    # whether to exclude objects locked by running integrity jobs, only take effect for contact and account
    exclude_locked_by_integrity_jobs: bool = False
    offset: int | None = None

    @property
    def object_identifier(self) -> ObjectIdentifier:
        return self.non_relational_sorting_spec.primary_object_identifier
