from abc import ABC, abstractmethod

from salestech_be.common.query_util.filter_schema import CompositeFilter, ValueFilter


class FilterWalker[T: CompositeFilter | ValueFilter](ABC):
    @abstractmethod
    def __call__(self, filter_: T) -> CompositeFilter | ValueFilter:
        raise NotImplementedError


class CompositeFilterWalker(FilterWalker[CompositeFilter]):
    @abstractmethod
    def __call__(self, filter_: CompositeFilter) -> CompositeFilter | ValueFilter:
        raise NotImplementedError


class ValueFilterWalker(FilterWalker[ValueFilter]):
    @abstractmethod
    def __call__(self, filter_: ValueFilter) -> CompositeFilter | ValueFilter:
        raise NotImplementedError


class DefaultCompositeFilterWalker(CompositeFilterWalker):
    def __init__(self, value_filter_walker: ValueFilterWalker):
        self._value_filter_walker = value_filter_walker

    def _walk_filter(
        self, filter_: CompositeFilter | ValueFilter
    ) -> CompositeFilter | ValueFilter:
        if isinstance(filter_, CompositeFilter):
            return self(filter_)
        else:
            return self._value_filter_walker(filter_)

    def __call__(self, filter_: CompositeFilter) -> CompositeFilter | ValueFilter:
        all_of: list[CompositeFilter | ValueFilter] = []
        any_of: list[CompositeFilter | ValueFilter] = []
        none_of: list[CompositeFilter | ValueFilter] = []

        for _filter in filter_.all_of:
            _translated = self._walk_filter(_filter)
            if isinstance(_translated, ValueFilter):
                all_of.append(_translated)
            elif (not _translated.any_of) and (not _translated.none_of):
                # in this case, when and only when all the "all_of" filters in the translated filter are truthy,
                # the translated filter is truthy, so we can simply extend parent all_of
                # to include all contained filters in the translated filter's all_of
                all_of.extend(_translated.all_of)
            else:
                all_of.append(_translated)

        for _filter in filter_.any_of:
            _translated = self._walk_filter(_filter)
            if isinstance(_translated, ValueFilter):
                any_of.append(_translated)
            elif (not _translated.all_of) and (not _translated.none_of):
                # in this case, any truthy "any_of" in the translated filter will result
                # the translated filter to be truthy, so we can simply extend parent any_of
                # to include all contained filters in the translated filter's any_of
                any_of.extend(_translated.any_of)
            else:
                any_of.append(_translated)

        for _filter in filter_.none_of:
            _translated = self._walk_filter(_filter)
            if isinstance(_translated, ValueFilter):
                none_of.append(_translated)
            elif (not _translated.all_of) and (not _translated.none_of):
                # in this case, when and only when all the "any_of" filters in the translated filter are falsy,
                # the translated filter is falsy and therefore the parent filter will be evaluated as true,
                # so we can simply extend parent none_of to include all contained filters in the translated filter's any_of
                none_of.extend(_translated.any_of)
            else:
                none_of.append(_translated)

        return filter_.model_copy(
            update={"all_of": all_of, "any_of": any_of, "none_of": none_of}
        )
