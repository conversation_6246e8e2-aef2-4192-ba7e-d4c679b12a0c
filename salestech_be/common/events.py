import asyncio
from abc import ABC, abstractmethod
from collections.abc import Call<PERSON>, Coroutine
from typing import Generic, TypeVar

from pydantic import BaseModel

from salestech_be.core.common.types import (  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CustomDomainModel,
    DomainModel,
)
from salestech_be.db.models.core.base import (
    TableModel,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.integrations.kafka.types import (
    CDCObject,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CDCObjectState,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    EnrichedCDCEvent,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

TableModelT = TypeVar("TableModelT", bound=TableModel)
DomainModelT = TypeVar("DomainModelT", bound=DomainModel | CustomDomainModel)


class DomainEnrichedCDCEvent(BaseModel, Generic[TableModelT, DomainModelT]):
    modified_fields: set[str]
    before: TableModelT | None
    after: TableModelT
    before_domain_model: DomainModelT | None
    current_domain_model: DomainModelT


class EnrichedCDCEventProcessor(ABC, Generic[TableModelT, DomainModelT]):
    async def process(self, event: EnrichedCDCEvent) -> DomainModelT:
        domain_event = await self.create_domain_enriched_event(event)
        return await self.process_domain_enriched_event(domain_event)

    async def create_domain_enriched_event(
        self, event: EnrichedCDCEvent
    ) -> DomainEnrichedCDCEvent[TableModelT, DomainModelT]:
        """
        Enrich table model object (before and after) with domain model objects.

        The table model (before and after) will be enriched to domain model objects
        (before_domain_model, current_domain_model), respectively.

        FIXME:
        Currently, only account, contact and pipeline domain services have the ability
        to convert its table model object to domain model object.

        Therefore,

        For before_domain_model, if the table model object is either account, contact or pipeline,
        it will be converted to a AccountV2, ContactV2 or PipelineV2 domain object respectively.
        Otherwise, before_domain_model will be None.

        For current_domain_model, no matter which table model object it is, it will fetch the latest
        version of domain model object from db by its object id.

        NOTE:
        This is obviosuly a temp solution, we should come back and revist the cdc design.
        """
        return DomainEnrichedCDCEvent(
            modified_fields=event.modified_fields,
            before=self.get_table_model(event.before) if event.before else None,
            after=self.get_table_model(event.after),
            before_domain_model=await self.get_domain_model(
                event.before, CDCObjectState.BEFORE
            )
            if event.before
            and event.view_model
            in ["account_view", "contact_view", "contact_view_v2", "pipeline_view"]
            else None,
            current_domain_model=await self.get_domain_model(
                event.after, CDCObjectState.CURRENT
            ),
        )

    @abstractmethod
    def get_table_model(self, event: CDCObject) -> TableModelT: ...

    @abstractmethod
    async def get_domain_model(
        self, event: CDCObject, event_state: CDCObjectState
    ) -> DomainModelT: ...

    @abstractmethod
    async def process_domain_enriched_event(
        self,
        domain_event: DomainEnrichedCDCEvent[TableModelT, DomainModelT],
    ) -> DomainModelT: ...

    # TODO: Make abstract
    async def get_domain_enriched_event_handlers(
        self,
        domain_event: DomainEnrichedCDCEvent[TableModelT, DomainModelT],
    ) -> list[
        Callable[
            [DomainEnrichedCDCEvent[TableModelT, DomainModelT]],
            Coroutine[None, None, None],
        ]
    ]:
        raise NotImplementedError

    # TODO: Replace process_domain_enriched_event with process_domain_enriched_event_handlers
    async def process_domain_enriched_event_handlers(
        self,
        domain_event: DomainEnrichedCDCEvent[TableModelT, DomainModelT],
    ) -> DomainModelT:
        handlers = await self.get_domain_enriched_event_handlers(domain_event)
        async with asyncio.TaskGroup() as group:
            for handler in handlers:
                group.create_task(handler(domain_event))
        return domain_event.current_domain_model
