"""
Standard object and field identifiers for the CRM system.
This module defines the core data model and relationships between different entities.

Key Components:
1. Field Identifiers - Define available fields for each object type (e.g., AccountField, ContactField)
2. Object Identifiers - Define available object types (e.g., account, contact, deal)
3. Select List Identifiers - Define available select lists (e.g., pipeline_stage,)

Usage Examples:
    >>> # Object Identifiers
    >>> account_obj = StdObjectIdentifiers.account
    >>> account_obj.identifier  # Returns StandardObjectIdentifier(object_name="account")

    >>> # Field Access
    >>> AccountField.display_name.identifier  # Returns StandardFieldIdentifier(field_name="display_name")
    >>> ContactField.email.identifier  # Returns StandardFieldIdentifier(field_name="email")

    >>> # Select Lists
    >>> stage = StdSelectListIdentifier.pipeline_stage
    >>> is_direct = is_direct_std_select_list_identifier(stage)

Important Notes:
    1. All identifiers are immutable after definition
    2. Field names must be unique within their object type
    3. Object names must be unique across the system
    4. Select list identifiers are used for dropdown/enum values
    5. Direct select lists are implemented with GenericSelectList
    6. Special select lists may have additional metadata

Type Hierarchy:
    - StdFieldIdentifierEnum (base class)
        ├── AccountField
        ├── ContactField
        ├── PipelineField
        └── ... (other field classes)

    - StdObjectIdentifiers
        Contains all available object types

    - StdSelectListIdentifier
        Contains all available select list types
"""

from enum import StrEnum, unique
from typing import Literal, TypeGuard, cast, get_args

from salestech_be.common.type.metadata.common import (
    StandardFieldIdentifier,
    StandardObjectIdentifier,
)
from salestech_be.util.enum_util import NameValueStrEnum

__all__ = [
    "AISuggestedValueField",
    "AccountField",
    "AccountSummaryField",
    "AddressField",
    "ChampionField",
    "CommentField",
    "CompetitionField",
    "ContactAccountEmailField",
    "ContactAccountRoleField",
    "ContactEmailField",
    "ContactField",
    "ContactPipelineRoleField",
    "ContactSummaryField",
    "CoreCrmStdObjectIdentifiers",
    "DecisionCriteriaField",
    "DecisionProcessField",
    "DomainConfigurationField",
    "EmailAccountField",
    "GiantTaskField",
    "GlobalMessageField",
    "GlobalThreadField",
    "IdentifiedPainField",
    "IdentifiedPainItemField",
    "MetricField",
    "MetricItemField",
    "OrganizationField",
    "OrganizationUserField",
    "OutboundDomainField",
    "PaperProcessField",
    "PipelineField",
    "PipelineStageSelectListValueField",
    "SelectListValueField",
    "SequenceEnrollmentContactField",
    "SequenceEnrollmentRunField",
    "SequenceField",
    "SequenceParticipantField",
    "SequenceScheduleField",
    "SequenceScheduleTimeField",
    "SequenceStatsField",
    "StdFieldIdentifierEnum",
    "StdObjectIdentifiers",
    "StdSelectListIdentifier",
    "TaskField",
    "UserGoalField",
    "WhyAnyField",
    "WhyNowField",
    "WhyUsField",
]


@unique
class StdSelectListIdentifier(NameValueStrEnum):
    """Standard identifiers for select list types in the CRM system.

    This enum defines all available select list types, which can be categorized into:
    1. Special select lists - Have additional metadata in domain-specific models
    2. Direct select lists - Implemented directly with GenericSelectList without extra metadata

    Usage Examples:
        >>> # Access select list identifiers
        >>> stage = StdSelectListIdentifier.pipeline_stage
        >>> stage.value  # Returns "pipeline_stage"

        >>> # Check if a select list is direct (implemented with GenericSelectList)
        >>> is_direct = is_direct_std_select_list_identifier(
        ...     StdSelectListIdentifier.pipeline_source
        ... )
        >>> assert is_direct == True

        >>> # Get all direct select list identifiers
        >>> direct_lists = direct_std_select_list_identifiers()
        >>> assert StdSelectListIdentifier.pipeline_source in direct_lists

    Special Select Lists:
        - account_stage: Reserved for future account stage management

    Direct Select Lists:
        - pipeline_source: Source of pipeline entries
        - pipeline_type: Type classification for pipelines
        - contact_stage: Stage/status for contacts
        - pipeline_closed_won_reasons: Reasons for won deals
        - pipeline_closed_lost_reasons: Reasons for lost deals
    """

    account_stage = "account_stage"  # UNUSED UNTIL re-write

    # Direct select lists implemented with GenericSelectList
    pipeline_stage = "pipeline_stage"
    pipeline_source = "pipeline_source"
    pipeline_type = "pipeline_type"
    pipeline_closed_won_reasons = "pipeline_closed_won_reasons"
    pipeline_closed_lost_reasons = "pipeline_closed_lost_reasons"
    contact_stage = "contact_stage"


# These are standard select lists that implemented directly with GenericSelectList
# and do not have additional metadata in domain specific models/implementation.

# Whereas PipelineStageSelectList is a special case that is implemented with
# GenericSelectList but has additional metadata in domain specific models/implementation
# and is managed by PipelineStageSelectListService.
DirectStdSelectListIdentifier = Literal[
    StdSelectListIdentifier.pipeline_source,
    StdSelectListIdentifier.pipeline_type,
    StdSelectListIdentifier.contact_stage,
    StdSelectListIdentifier.pipeline_closed_won_reasons,
    StdSelectListIdentifier.pipeline_closed_lost_reasons,
]

BootstrapRequiredDirectStdSelectListIdentifier = Literal[
    StdSelectListIdentifier.contact_stage,
    StdSelectListIdentifier.pipeline_type,
    StdSelectListIdentifier.pipeline_source,
]


def direct_std_select_list_identifiers(
    bootstrap: bool = False,
) -> set[DirectStdSelectListIdentifier]:
    """Get all enum members included in PlainStdSelectListIdentifier literal definition.

    Returns:
        set[StdSelectListIdentifier]: Set of StdSelectListIdentifier enum members that are defined
            as plain select lists without additional metadata.
    """
    # Get the type hints for PlainStdSelectListIdentifier
    plain_type = get_args(
        BootstrapRequiredDirectStdSelectListIdentifier
        if bootstrap
        else DirectStdSelectListIdentifier
    )

    # Extract the enum members from the Literal type
    # The __args__ attribute contains the literal values defined in the Literal type
    return {
        cast(DirectStdSelectListIdentifier, member)
        for member in StdSelectListIdentifier
        if member in plain_type
    }


def is_direct_std_select_list_identifier(
    identifier: StdSelectListIdentifier,
) -> TypeGuard[DirectStdSelectListIdentifier]:
    return identifier in direct_std_select_list_identifiers()


class StdObjectIdentifiers(NameValueStrEnum):
    user = "user"
    address = "address"
    account = "account"
    activity = "activity"
    activity_sub_reference = "activity_sub_reference"
    chat = "chat"
    chat_message = "chat_message"
    contact = "contact"
    comment = "comment"
    conversation = "conversation"
    meeting = "meeting"
    meeting_participant = "meeting_participant"
    meeting_bot_status_event = "meeting_bot_status_event"
    bot_error = "bot_error"
    pipeline = "pipeline"
    competition = "competition"
    competitor = "competitor"
    decision_criteria = "decision_criteria"
    decision_criteria_item = "decision_criteria_item"
    decision_process = "decision_process"
    decision_process_item = "decision_process_item"
    identified_pain = "identified_pain"
    identified_pain_item = "identified_pain_item"
    metric = "metric"
    metric_item = "metric_item"
    paper_process = "paper_process"
    paper_process_item = "paper_process_item"
    why_any = "why_any"
    why_now = "why_now"
    why_us = "why_us"
    account_summary = "account_summary"
    contact_role_summary = "contact_role_summary"
    task = "task"
    giant_task = "giant_task"
    contact_for_giant_task = "contact_for_giant_task"
    email_thread_for_giant_task = "email_thread_for_giant_task"
    citation_for_giant_task = "citation_for_giant_task"
    user_goal = "user_goal"
    user_organization_profile = "user_organization_profile"
    organization = "organization"
    screen_share_ranges = "screen_share_ranges"
    pipeline_stage_select_list_value = "pipeline_stage_select_list_value"
    select_list_value = "select_list_value"
    global_thread = "global_thread"
    global_message = "global_message"
    global_message_error_info = "global_message_error_info"
    global_message_event_summary = "global_message_event_summary"
    email_participant = "email_participant"
    attachment_details = "attachment_details"
    event_schedule = "event_schedule"
    email_template = "email_template"
    email_account_pool = "email_account_pool"
    domain_object_list = "domain_object_list"
    domain_object_list_item = "domain_object_list_item"
    email_account = "email_account"
    email_account_warm_up_campaign = "email_account_warm_up_campaign"
    outbound_domain = "outbound_domain"
    domain_configuration = "domain_configuration"
    citation = "citation"
    sequence = "sequence"
    sequence_participant = "sequence_participant"
    sequence_stats = "sequence_stats"
    sequence_schedule = "sequence_schedule"
    sequence_schedule_time = "sequence_schedule_time"
    sequence_enrollment = "sequence_enrollment"
    sequence_enrollment_contact = "sequence_enrollment_contact"
    sequence_enrollment_run = "sequence_enrollment_run"
    sequence_step = "sequence_step"
    sequence_step_variant = "sequence_step_variant"
    sequence_step_execution = "sequence_step_execution"
    signature = "signature"
    contact_email = "contact_email"
    contact_phone_number = "contact_phone_number"
    contact_account_association = "contact_account_association"
    contact_account_role = "contact_account_role"
    contact_pipeline_role = "contact_pipeline_role"
    contact_account_email = "contact_account_email"
    person = "person"
    company = "company"
    phone_number_detail = "phone_number_detail"
    prospecting_filter_field_options_facet = "prospecting_filter_field_options_facet"
    prospecting_filter_field_options = "prospecting_filter_field_options"
    prospecting_run = "prospecting_run"
    prospecting_run_result = "prospecting_run_result"
    prospecting_credit_by_type = "prospecting_credit_by_type"
    prospecting_credit_usage = "prospecting_credit_usage"
    prospecting_list_enrollment_result = "prospecting_list_enrollment_result"
    prospecting_credit_usage_point = "prospecting_credit_usage_point"
    prospecting_saved_search_query = "prospecting_saved_search_query"
    prospecting_phone_number_detail = "prospecting_phone_number_detail"
    people_enrichment_summary = "people_enrichment_summary"

    # Placeholders for error code representations
    # Please review these usages and fix them as needed before using them in business logic
    # >>>>>>
    workspace = "workspace"
    campaign = "campaign"
    domain = "domain"

    # <<<<<<
    # Placeholders for error code representations
    # Please review these usages and fix them as needed before using them in business logic

    def __init__(self, object_name: str):
        super().__init__(object_name)
        self._object_identifier = StandardObjectIdentifier(object_name=object_name)
        # ensure subclass don't default duplicate object names
        self._ensure_no_duplicates(new_value=object_name, new_name=self.name)

    @property
    def identifier(self) -> StandardObjectIdentifier:
        return self._object_identifier

    @classmethod
    def _ensure_no_duplicates(cls, new_value: str, new_name: str) -> None:
        for existing_name, member in cls.__members__.items():
            if new_value.lower() == member.lower():
                raise ValueError(
                    f"case-insensitive duplicates found. value: '{new_value}'"
                    f"conflicting member names: '{new_name}' and '{existing_name}' "
                    f"in Enum: {cls.__name__}"
                )


# These are core crm standard object identifiers
CoreCrmStdObjectIdentifiers = Literal[
    StdObjectIdentifiers.account,
    StdObjectIdentifiers.contact,
    StdObjectIdentifiers.pipeline,
]


class StdFieldIdentifierEnum(StrEnum):
    def __init__(self, field_name: str):
        super().__init__()
        self._field_identifier = StandardFieldIdentifier(field_name=field_name)
        # ensure subclass don't default duplicate field names
        self._ensure_no_duplicates(new_value=field_name, new_name=self.name)

    @property
    def identifier(self) -> StandardFieldIdentifier:
        return self._field_identifier

    @classmethod
    def _ensure_no_duplicates(cls, new_value: str, new_name: str) -> None:
        for existing_name, member in cls.__members__.items():
            if new_value.lower() == member.lower():
                raise ValueError(
                    f"case-insensitive duplicates found. value: '{new_value}'"
                    f"conflicting member names: '{new_name}' and '{existing_name}' "
                    f"in Enum: {cls.__name__}"
                )


class AccountField(StdFieldIdentifierEnum):
    id = "id"
    display_name = "display_name"
    status = "status"
    official_website = "official_website"
    linkedin_url = "linkedin_url"
    x_url = "x_url"
    zoominfo_url = "zoominfo_url"
    facebook_url = "facebook_url"
    description = "description"
    ai_suggested_description = "ai_suggested_description"
    domain_name = "domain_name"
    keyword_list = "keyword_list"
    category_list = "category_list"
    ai_suggested_category_list = "ai_suggested_category_list"
    technology_list = "technology_list"
    estimated_annual_revenue = "estimated_annual_revenue"
    ai_suggested_annual_revenue = "ai_suggested_annual_revenue"
    estimated_employee_count = "estimated_employee_count"
    ai_suggested_employee_count = "ai_suggested_employee_count"
    street_one = "street_one"
    street_two = "street_two"
    zip_code = "zip_code"
    city = "city"
    state = "state"
    country = "country"
    created_at = "created_at"
    updated_at = "updated_at"
    archived_at = "archived_at"
    integrity_job_started_at = "integrity_job_started_at"
    integrity_job_finished_at = "integrity_job_finished_at"
    organization_id = "organization_id"
    owner_user_id = "owner_user_id"
    archived_by_user_id = "archived_by_user_id"
    created_by_user_id = "created_by_user_id"
    created_source = "created_source"
    updated_by_user_id = "updated_by_user_id"
    integrity_job_started_by_user_id = "integrity_job_started_by_user_id"
    integrity_job_started_by_job_ids = "integrity_job_started_by_job_ids"
    integrity_job_finished_by_user_id = "integrity_job_finished_by_user_id"
    research_tldr = "research_tldr"
    research_content = "research_content"
    research_reference_urls = "research_reference_urls"
    access_status = "access_status"
    merge_to_account_id = "merge_to_account_id"
    participant_user_id_list = "participant_user_id_list"
    company_id = "company_id"


class AccountSummaryField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    display_name = "display_name"
    description = "description"
    ai_suggested_description = "ai_suggested_description"
    category = "category"
    keywords = "keywords"
    category_list = "category_list"
    ai_suggested_category_list = "ai_suggested_category_list"
    keyword_list = "keyword_list"
    technology_list = "technology_list"
    estimated_employee_count = "estimated_employee_count"
    estimated_annual_revenue = "estimated_annual_revenue"
    ai_suggested_annual_revenue = "ai_suggested_annual_revenue"
    ai_suggested_employee_count = "ai_suggested_employee_count"
    official_website = "official_website"
    city = "city"
    state = "state"
    country = "country"
    employee_range = "employee_range"
    stage = "stage"
    created_at = "created_at"
    updated_at = "updated_at"


class CitationField(StdFieldIdentifierEnum):
    id = "id"
    for_object_id = "for_object_id"
    for_object_type = "for_object_type"
    source_type = "source_type"
    source_id = "source_id"
    metadata = "metadata"
    organization_id = "organization_id"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class CitationForGiantTaskField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    source_id = "source_id"
    source_type = "source_type"
    metadata = "metadata"


class ContactForGiantTaskField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    display_name = "display_name"


class ContactField(StdFieldIdentifierEnum):
    id = "id"
    display_name = "display_name"

    stage_id = "stage_id"
    stage = "stage"
    first_name = "first_name"
    last_name = "last_name"
    middle_name = "middle_name"
    primary_email = "primary_email"
    primary_phone_number = "primary_phone_number"
    linkedin_url = "linkedin_url"
    zoominfo_url = "zoominfo_url"
    x_url = "x_url"
    facebook_url = "facebook_url"
    address = "address"
    # have to use title_ instead of title as it is a built-in function in python str
    title_ = "title"
    department = "department"
    owner_user_id = "owner_user_id"
    created_by_user_id = "created_by_user_id"
    created_source = "created_source"
    updated_by_user_id = "updated_by_user_id"
    archived_by_user_id = "archived_by_user_id"
    integrity_job_started_by_user_id = "integrity_job_started_by_user_id"
    integrity_job_finished_by_user_id = "integrity_job_finished_by_user_id"
    integrity_job_started_by_job_ids = "integrity_job_started_by_job_ids"
    created_at = "created_at"
    updated_at = "updated_at"
    archived_at = "archived_at"
    integrity_job_started_at = "integrity_job_started_at"
    integrity_job_finished_at = "integrity_job_finished_at"
    organization_id = "organization_id"
    # created_source = "created_source"
    primary_account_id = "primary_account_id"
    contact_emails = "contact_emails"
    contact_phone_numbers = "contact_phone_numbers"
    contact_account_associations = "contact_account_associations"
    person_id = "person_id"
    access_status = "access_status"
    participant_user_id_list = "participant_user_id_list"
    merge_to_contact_id = "merge_to_contact_id"
    ai_suggested_linkedin_url = "ai_suggested_linkedin_url"
    ai_suggested_display_name = "ai_suggested_display_name"
    ai_suggested_title = "ai_suggested_title"
    email_enriched_at = "email_enriched_at"
    phone_enriched_at = "phone_enriched_at"


class ContactSummaryField(StdFieldIdentifierEnum):
    id = "id"
    display_name = "display_name"
    primary_email = "primary_email"
    title_ = "title"
    role = "role"
    note = "note"


class ConversationField(StdFieldIdentifierEnum):
    id = "id"
    reference_id = "reference_id"
    reference_type = "reference_type"

    created_at = "created_at"
    created_by_user_id = "created_by_user_id"

    organization_id = "organization_id"
    account_id = "account_id"
    pipeline_id = "pipeline_id"

    participant_user_id_list = "participant_user_id_list"
    participant_contact_id_list = "participant_contact_id_list"
    attendee_user_id_list = "attendee_user_id_list"
    attendee_contact_id_list = "attendee_contact_id_list"

    # TODO: decide what other DB fields that we want to expose
    # updated_at = "updated_at"
    # updated_by_user_id = "updated_by_user_id"
    # deleted_at = "deleted_at"
    # deleted_by_user_id = "deleted_by_user_id"
    # metadata = "metadata"


class MeetingParticipantField(StdFieldIdentifierEnum):
    name_ = "name"
    is_host = "is_host"
    platform = "platform"


class MeetingParticipantFieldV2(StdFieldIdentifierEnum):
    name_ = "name"
    is_host = "is_host"
    platform = "platform"
    rsvp_status = "rsvp_status"
    user_id = "user_id"
    contact_id = "contact_id"
    email = "email"


class MeetingBotStatusEventField(StdFieldIdentifierEnum):
    status = "status"
    status_at = "status_at"
    sub_code = "sub_code"


class MeetingShareScreenRangeField(StdFieldIdentifierEnum):
    start_offset = "start_offset"
    end_offset = "end_offset"


class BotErrorField(StdFieldIdentifierEnum):
    error_code = "error_code"
    error_desc = "error_desc"


class MeetingField(StdFieldIdentifierEnum):
    id = "id"
    meeting_url = "meeting_url"
    meeting_calendar_group_key = "meeting_calendar_group_key"
    meeting_platform = "meeting_platform"
    meeting_created_at = "meeting_created_at"
    starts_at = "starts_at"
    started_at = "started_at"
    title_ = "title"
    description = "description"
    meeting_bot_status_history = "meeting_bot_status_history"
    pre_signed_media_url = "pre_signed_media_url"
    pre_signed_vtt_url = "pre_signed_vtt_url"
    pre_signed_sprite_url = "pre_signed_sprite_url"
    pre_signed_preview_thumbnail_url = "pre_signed_preview_thumbnail_url"
    pre_signed_media_expire_time = "pre_signed_media_expire_time"
    meeting_bot_removed_at = "meeting_bot_removed_at"
    meeting_bot_status = "meeting_bot_status"
    meeting_status = "meeting_status"
    meeting_type = "meeting_type"
    ends_at = "ends_at"
    ended_at = "ended_at"
    event_schedule_id = "event_schedule_id"
    invitee_user_id_list = "invitee_user_id_list"
    invitee_contact_id_list = "invitee_contact_id_list"
    attendee_user_id_list = "attendee_user_id_list"
    attendee_contact_id_list = "attendee_contact_id_list"
    owner_user_calendar_event_id = "owner_user_calendar_event_id"
    current_user_calendar_event_id = "current_user_calendar_event_id"
    has_active_bot = "has_active_bot"
    is_sales_meeting = "is_sales_meeting"
    consent_declined_at = "consent_declined_at"
    is_recorded = "is_recorded"
    agenda = "agenda"
    key_talking_points = "key_talking_points"
    organizer_user_id = "organizer_user_id"
    created_by_user_id = "created_by_user_id"
    meeting_participants = "meeting_participants"
    is_no_show = "is_no_show"
    reference_id = "reference_id"
    reference_id_type = "reference_id_type"
    confirmation_state = "confirmation_state"
    screen_share_ranges = "screen_share_ranges"
    pipeline_id = "pipeline_id"
    pipeline_select_list_value_id = "pipeline_select_list_value_id"
    rescheduled_from_id = "rescheduled_from_id"
    is_rescheduled = "is_rescheduled"
    account_id = "account_id"
    is_external_meeting = "is_external_meeting"
    duration_seconds = "duration_seconds"
    owner_user_id = "owner_user_id"
    is_bot_enabled = "is_bot_enabled"
    bot_error = "bot_error"
    sequence_id = "sequence_id"
    sales_action_types = "sales_action_types"


class PipelineField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    account_id = "account_id"
    primary_contact_id = "primary_contact_id"
    additional_contact_ids = "additional_contact_ids"
    all_contact_ids = "all_contact_ids"
    stage_id = "stage_id"
    stage_pipeline_id = "stage_pipeline_id"
    stage_last_shifted_at = "stage_last_shifted_at"
    stage_value = "stage_value"
    stage = "stage"
    type_id = "type_id"
    type = "type"
    closed_reason_select_list_value_ids = "closed_reason_select_list_value_ids"
    closed_reason_select_list_values = "closed_reason_select_list_values"
    closed_reason_custom_detail = "closed_reason_custom_detail"
    amount = "amount"
    display_name = "display_name"
    owner_user_id = "owner_user_id"
    updated_at = "updated_at"
    created_at = "created_at"
    archived_at = "archived_at"
    archived_by_user_id = "archived_by_user_id"
    created_by_user_id = "created_by_user_id"
    created_source = "created_source"
    updated_by_user_id = "updated_by_user_id"
    next_step_details = "next_step_details"
    next_step_due_at = "next_step_due_at"
    anticipated_closing_at = "anticipated_closing_at"
    expires_at = "expires_at"
    closed_at = "closed_at"
    closed_by_user_id = "closed_by_user_id"
    status = "status"
    access_status = "access_status"
    participant_user_id_list = "participant_user_id_list"
    competition = "competition"
    decision_criteria = "decision_criteria"
    decision_process = "decision_process"
    identified_pain = "identified_pain"
    metric = "metric"
    paper_process = "paper_process"


class PipelineStageSelectListValueField(StdFieldIdentifierEnum):
    id = "id"
    select_list_id = "select_list_id"
    display_value = "display_value"
    description = "description"
    rank = "rank"
    is_default = "is_default"
    status = "status"

    # pipeline stage specific fields
    default_closing_probability = "default_closing_probability"
    outcome_state = "outcome_state"
    color_code = "color_code"
    expected_days_in_current_stage = "expected_days_in_current_stage"


class SelectListValueField(StdFieldIdentifierEnum):
    id = "id"
    select_list_id = "select_list_id"
    display_value = "display_value"
    description = "description"
    rank = "rank"
    is_default = "is_default"
    status = "status"


class AddressField(StdFieldIdentifierEnum):
    street_one = "street_one"
    street_two = "street_two"
    zip_code = "zip_code"
    city = "city"
    state = "state"
    country = "country"


class OrganizationField(StdFieldIdentifierEnum):
    id = "id"
    display_name = "display_name"


class OrganizationUserField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    first_name = "first_name"
    last_name = "last_name"
    display_name = "display_name"
    email = "email"
    phone_number = "phone_number"
    phone_number_rfc_3966 = "phone_number_rfc_3966"
    reevo_phone_number = "reevo_phone_number"
    reevo_phone_number_rfc_3966 = "reevo_phone_number_rfc_3966"
    linkedin_url = "linkedin_url"
    avatar_s3_key = "avatar_s3_key"
    avatar_url = "avatar_url"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deactivated_at = "deactivated_at"
    deactivated_by_user_id = "deactivated_by_user_id"
    organization_roles = "organization_roles"
    organization_association_status = "organization_association_status"
    organization_association_profile = "organization_association_profile"
    organization_association_updated_at = "organization_association_updated_at"
    organization_association_updated_by_user_id = (
        "organization_association_updated_by_user_id"
    )


class TaskField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    title_ = "title"
    status = "status"
    priority = "priority"
    type = "type"
    due_at = "due_at"
    note = "note"
    created_at = "created_at"
    updated_at = "updated_at"
    completed_at = "completed_at"
    completed_by_user_id = "completed_by_user_id"
    archived_at = "archived_at"
    owner_user_id = "owner_user_id"
    created_by_user_id = "created_by_user_id"
    updated_by_user_id = "updated_by_user_id"
    archived_by_user_id = "archived_by_user_id"
    pipeline_id = "pipeline_id"
    account_id = "account_id"
    sequence_id = "sequence_id"
    sequence_enrollment_id = "sequence_enrollment_id"
    sequence_step_id = "sequence_step_id"
    sequence_step_execution_id = "sequence_step_execution_id"
    sequence_step_variant_id = "sequence_step_variant_id"
    citation_id = "citation_id"
    email_ids = "email_ids"
    email_thread_ids = "email_thread_ids"
    meeting_id = "meeting_id"
    result_meeting_ids = "result_meeting_ids"
    contact_ids = "contact_ids"
    insight_id = "insight_id"
    task_comments = "task_comments"
    source_type = "source_type"
    participant_user_id_list = "participant_user_id_list"
    disposition = "disposition"


class EventScheduleField(StdFieldIdentifierEnum):
    id = "id"
    event_title = "event_title"
    scheduler_title = "scheduler_title"
    event_title_template = "event_title_template"
    event_description = "event_description"
    event_schedule_type = "event_schedule_type"
    hosts = "hosts"
    duration_minutes = "duration_minutes"
    conferencing_provider = "conferencing_provider"
    available_days_in_future = "available_days_in_future"
    availability = "availability"
    status = "status"
    timezone = "timezone"
    scheduling_page_url = "scheduling_page_url"
    enable_recording = "enable_recording"
    is_disabled = "is_disabled"
    created_by_user_id = "created_by_user_id"
    updated_by_user_id = "updated_by_user_id"
    deleted_by_user_id = "deleted_by_user_id"
    created_at = "created_at"
    updated_at = "updated_at"
    deleted_at = "deleted_at"
    event_title_slug = "event_title_slug"
    create_buffer_events = "create_buffer_events"
    visibility = "visibility"


class UserGoalField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    metric = "metric"
    display_name = "display_name"
    evaluation_time_unit = "evaluation_time_unit"
    parent_user_goal_id = "parent_user_goal_id"
    starts_at = "starts_at"
    ends_at = "ends_at"
    assigned_to_user_id = "assigned_to_user_id"
    initial_value = "initial_value"
    target_value = "target_value"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    is_primary = "is_primary"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"
    progress_value = "progress_value"
    progress_percent = "progress_percent"


class UserOrganizationProfileField(StdFieldIdentifierEnum):
    timezone = "timezone"
    location = "location"
    title_ = "title"
    keywords = "keywords"
    is_task_digest_enabled = "is_task_digest_enabled"
    meeting_bot_name = "meeting_bot_name"


class CommentField(StdFieldIdentifierEnum):
    id = "id"
    reference_id = "reference_id"
    reference_id_type = "reference_id_type"
    comment_html = "comment_html"

    organization_id = "organization_id"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"

    attachment_ids = "attachment_ids"
    mentioned_user_ids = "mentioned_user_ids"
    metadata = "metadata"
    parent_comment_id = "parent_comment_id"


class ActivitySubReferenceField(StdFieldIdentifierEnum):
    value_ = "value"
    type = "type"
    contact_id = "contact_id"
    user_id = "user_id"
    email_account_id = "email_account_id"
    display_name = "display_name"


class ActivityField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    type_ = "type"
    sub_type = "sub_type"
    priority = "priority"
    status = "status"
    owner_user_id = "owner_user_id"
    account_id = "account_id"
    pipeline_id = "pipeline_id"
    sequence_id = "sequence_id"
    reference_id = "reference_id"
    reference_id_type = "reference_id_type"
    display_name = "display_name"
    sub_references = "sub_references"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    # todo(xw): support metadata
    # metadata = "metadata"
    contact_ids = "contact_ids"
    keywords = "keywords"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"


class GlobalThreadField(StdFieldIdentifierEnum):
    id = "id"
    subject = "subject"
    snippet = "snippet"
    owner_user_ids = "owner_user_ids"
    latest_message_received_date = "latest_message_received_date"
    latest_message_date = "latest_message_date"
    messages = "messages"
    message_count = "message_count"
    participant_contact_id_list = "participant_contact_id_list"
    account_id = "account_id"
    account_ids = "account_ids"
    pipeline_id = "pipeline_id"
    potential_pipeline_ids = "potential_pipeline_ids"
    participant_emails = "participant_emails"
    sequence_id = "sequence_id"


class EmailThreadForGiantTaskField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    subject = "subject"


class GlobalMessageField(StdFieldIdentifierEnum):
    id = "id"
    global_thread_id = "global_thread_id"
    user_email_account_ids = "user_email_account_ids"
    message_owner_email_accounts_and_messages_map = (
        "message_owner_email_accounts_and_messages_map"
    )
    can_reply = "can_reply"
    subject = "subject"
    status = "status"
    send_from = "send_from"
    to = "to"
    cc = "cc"
    bcc = "bcc"
    reply_to = "reply_to"
    snippet = "snippet"
    body_text = "body_text"
    body_html = "body_html"
    main_body_text = "main_body_text"
    main_body_html = "main_body_html"
    send_at = "send_at"
    folders = "folders"
    attachment_details = "attachment_details"
    received_date = "received_date"
    reply_to_global_message_id = "reply_to_global_message_id"
    error_info = "error_info"
    email_events = "email_events"
    email_account_id = "email_account_id"
    sales_action_types = "sales_action_types"


class GlobalMessageErrorInfoField(StdFieldIdentifierEnum):
    error_code = "error_code"
    is_resolvable = "is_resolvable"
    description = "description"


class GlobalMessageEventSummaryField(StdFieldIdentifierEnum):
    event_type = "event_type"
    latest_event_time = "latest_event_time"


class EmailParticipantField(StdFieldIdentifierEnum):
    email_account_id = "email_account_id"
    contact_id = "contact_id"
    account_id = "account_id"
    email = "email"
    name = "name"


class AttachmentDetailsField(StdFieldIdentifierEnum):
    id = "id"
    content_type = "content_type"
    file_name = "file_name"
    content_id = "content_id"
    is_inline = "is_inline"
    size = "size"
    public_url = "public_url"
    s3_key = "s3_key"


class EmailTemplateField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    name = "name"
    type = "type"
    subject = "subject"
    body_html = "body_html"
    version = "version"
    attachment_ids = "attachment_ids"
    categories = "categories"
    include_email_signature = "include_email_signature"
    created_by_user_id = "created_by_user_id"
    updated_by_user_id = "updated_by_user_id"
    deleted_by_user_id = "deleted_by_user_id"
    created_at = "created_at"
    updated_at = "updated_at"
    deleted_at = "deleted_at"
    permissions = "permissions"


class DomainObjectListItemField(StdFieldIdentifierEnum):
    domain_object_list_id = "domain_object_list_id"
    reference_type = "reference_type"
    reference_id = "reference_id"
    organization_id = "organization_id"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"


class DomainObjectListField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    name = "name"
    description = "description"
    processing_status = "processing_status"
    item_type = "item_type"
    owner_user_id = "owner_user_id"
    origin = "origin"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class OutboundDomainField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    domain = "domain"
    status = "status"
    domain_health = "domain_health"
    total_mailbox_count = "total_mailbox_count"
    active_mailbox_count = "active_mailbox_count"
    created_by_user_id = "created_by_user_id"
    sequence_count = "sequence_count"
    purchased_at = "purchased_at"
    updated_at = "updated_at"
    deleted_at = "deleted_at"
    archived_at = "archived_at"
    domain_configuration = "domain_configuration"


class EmailAccountField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    email = "email"
    reply_to_email = "reply_to_email"
    signature_id = "signature_id"
    first_name = "first_name"
    last_name = "last_name"
    display_name = "display_name"
    status = "status"
    warmup_limit = "warmup_limit"
    email_account_health_score = "email_account_health_score"
    is_in_default_pool = "is_in_default_pool"
    default_pool_status = "default_pool_status"
    active = "active"
    seconds_delay_between_emails = "seconds_delay_between_emails"
    type = "type"
    owner_user_id = "owner_user_id"
    daily_quota = "daily_quota"
    current_day_usage = "current_day_usage"
    all_time_usage = "all_time_usage"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    archived_at = "archived_at"
    archived_by_user_id = "archived_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"
    outbound_domain_id = "outbound_domain_id"
    use_override = "use_override"


class EmailAccountWarmUpCampaignField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    email_account_id = "email_account_id"
    status = "status"


class EmailAccountPoolField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    name = "name"
    description = "description"
    is_default = "is_default"
    created_by_user_id = "created_by_user_id"
    updated_by_user_id = "updated_by_user_id"
    deleted_by_user_id = "deleted_by_user_id"
    created_at = "created_at"
    updated_at = "updated_at"
    deleted_at = "deleted_at"


class SequenceField(StdFieldIdentifierEnum):
    id = "id"
    name = "name"
    description = "description"
    owner_user_id = "owner_user_id"
    participants = "participants"
    visibility = "visibility"
    status = "status"
    cloned_from_sequence_id = "cloned_from_sequence_id"
    schedule = "schedule"
    organization_id = "organization_id"
    created_by_user_id = "created_by_user_id"
    created_at = "created_at"
    updated_by_user_id = "updated_by_user_id"
    updated_at = "updated_at"
    deleted_by_user_id = "deleted_by_user_id"
    deleted_at = "deleted_at"
    stats = "stats"
    participant_user_id_list = "participant_user_id_list"
    scheduled_count = "scheduled_count"
    delivered_count = "delivered_count"
    bounced_count = "bounced_count"
    reply_count = "reply_count"
    opt_out_count = "opt_out_count"
    opened_count = "opened_count"
    link_clicked_count = "link_clicked_count"
    last_activated_at = "last_activated_at"
    flow_control_config = "flow_control_config"


class SequenceParticipantField(StdFieldIdentifierEnum):
    user_id = "user_id"
    role = "role"


class SequenceEnrollmentField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    sequence_id = "sequence_id"
    email_account_id = "email_account_id"
    email_account_pool_id = "email_account_pool_id"
    contact_id = "contact_id"
    account_id = "account_id"
    domain_object_list_id = "domain_object_list_id"
    email = "email"
    status = "status"
    display_status = "display_status"
    current_step_id = "current_step_id"
    workflow_id = "workflow_id"
    enrolled_at = "enrolled_at"
    enrolled_by_user_id = "enrolled_by_user_id"
    exited_at = "exited_at"
    exited_by_user_id = "exited_by_user_id"
    exited_by_reference_id = "exited_by_reference_id"
    exited_by_reference_id_type = "exited_by_reference_id_type"
    exited_reason = "exited_reason"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class SequenceStepV2Field(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    sequence_id = "sequence_id"
    name = "name"
    is_first_step = "is_first_step"
    next_step_id = "next_step_id"
    delay_minutes = "delay_minutes"
    type = "type"
    manual_flow_control = "manual_flow_control"
    flow_control_config = "flow_control_config"
    manual_task_priority = "manual_task_priority"
    support_ab_test = "support_ab_test"
    delay_minutes_from_beginning = "delay_minutes_from_beginning"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"
    step_number = "step_number"
    variants = "variants"
    stats = "stats"


class SequenceStepExecutionField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    sequence_id = "sequence_id"
    sequence_step_id = "sequence_step_id"
    sequence_step_variant_id = "sequence_step_variant_id"
    sequence_enrollment_id = "sequence_enrollment_id"
    contact_id = "contact_id"
    contact_owner_user_id = "contact_owner_user_id"
    mailbox_id = "mailbox_id"
    global_thread_id = "global_thread_id"
    global_message_id = "global_message_id"
    status = "status"
    scheduled_at = "scheduled_at"
    executed_at = "executed_at"
    error_code = "error_code"
    error_detail = "error_detail"
    result_entity_id = "result_entity_id"
    result_entity_type = "result_entity_type"
    created_at = "created_at"
    updated_at = "updated_at"
    deleted_at = "deleted_at"


class SequenceStepVariantV2Field(StdFieldIdentifierEnum):
    id = "id"
    name = "name"
    sequence_step_id = "sequence_step_id"
    template_id = "template_id"
    content = "content"
    status = "status"
    reply_to_previous_thread = "reply_to_previous_thread"
    organization_id = "organization_id"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"
    stats = "stats"


class SequenceStatsField(StdFieldIdentifierEnum):
    scheduled_count = "scheduled_count"
    delivered_count = "delivered_count"
    bounced_count = "bounced_count"
    reply_count = "reply_count"
    opt_out_count = "opt_out_count"
    opened_count = "opened_count"
    link_clicked_count = "link_clicked_count"


class SequenceScheduleField(StdFieldIdentifierEnum):
    timezone = "timezone"
    skip_holidays = "skip_holidays"
    schedule_times = "schedule_times"


class SequenceScheduleTimeField(StdFieldIdentifierEnum):
    day_of_the_week = "day_of_the_week"
    start_time = "start_time"
    end_time = "end_time"
    is_active = "is_active"


class ContactEmailField(StdFieldIdentifierEnum):
    id = "id"
    labels = "labels"
    contact_id = "contact_id"
    email = "email"
    is_contact_primary = "is_contact_primary"
    contact_account_emails = "contact_account_emails"


class ContactPhoneNumberField(StdFieldIdentifierEnum):
    id = "id"
    is_contact_primary = "is_contact_primary"
    country_code = "country_code"
    number_e164 = "number_e164"
    extension = "extension"


class ContactAccountAssociationField(StdFieldIdentifierEnum):
    contact_id = "contact_id"
    account_id = "account_id"
    is_primary = "is_primary"
    title_ = "title"
    department = "department"


class ContactAccountRoleField(StdFieldIdentifierEnum):
    id = "id"
    contact_account_association_id = "contact_account_association_id"
    contact_id = "contact_id"
    account_id = "account_id"
    is_primary_account = "is_primary_account"
    title_ = "title"
    department = "department"
    contact_account_emails = "contact_account_emails"


class ContactAccountEmailField(StdFieldIdentifierEnum):
    id = "id"
    contact_email_account_association_id = "contact_email_account_association_id"
    contact_email_id = "contact_email_id"
    contact_id = "contact_id"
    account_id = "account_id"
    is_contact_account_primary = "is_contact_account_primary"
    email = "email"


class ProspectingPersonField(StdFieldIdentifierEnum):
    id = "id"
    first_name = "first_name"
    last_name = "last_name"
    full_name = "full_name"
    job_title = "job_title"
    current_company = "current_company"
    company_id = "company_id"
    company_website_url = "company_website_url"
    state = "state"
    country = "country"
    location = "location"
    linkedin_url = "linkedin_url"
    work_email = "work_email"
    phone_numbers = "phone_numbers"
    work_email_enrich_status = "work_email_enrich_status"
    phone_number_enrich_status = "phone_number_enrich_status"
    last_enriched_at = "last_enriched_at"
    has_email = "has_email"
    has_phone_numbers = "has_phone_numbers"
    contact_id = "contact_id"


class ProspectingPhoneNumberDetailField(StdFieldIdentifierEnum):
    number = "number"
    type = "type"
    status = "status"


class ProspectingCompanyField(StdFieldIdentifierEnum):
    id = "id"
    name = "name"
    domain = "domain"
    linkedin_url = "linkedin_url"
    logo_url = "logo_url"
    website = "website"


class ProspectingFilterFieldOptionsFacetField(StdFieldIdentifierEnum):
    filter_field_type = "filter_field_type"
    is_searchable = "is_searchable"
    prospecting_search_query_type = "prospecting_search_query_type"
    value_format_list = "value_format_list"
    data_type = "data_type"


class ProspectingFilterFieldOptionsField(StdFieldIdentifierEnum):
    id = "id"
    filter_field_type = "filter_field_type"
    display_name = "display_name"
    value = "value"
    category = "category"
    _count = "count"


class ProspectingRunField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    user_id = "user_id"
    run_type = "run_type"
    credit_usage = "credit_usage"
    sequence_id = "sequence_id"
    list_ids = "list_ids"
    person_count = "person_count"
    status = "status"
    run_results = "run_results"
    created_at = "created_at"
    updated_at = "updated_at"
    starts_at = "starts_at"
    ends_at = "ends_at"
    search_query_type = "search_query_type"
    people_enrichment_summary = "people_enrichment_summary"


class ProspectingRunResultField(StdFieldIdentifierEnum):
    id = "id"
    person_id = "person_id"
    contact_id = "contact_id"
    email_enrichment_status = "email_enrichment_status"
    phone_enrichment_status = "phone_enrichment_status"
    list_enrollment_results = "list_enrollment_results"
    sequence_enrollment_status = "sequence_enrollment_status"
    sequence_enrollment_rejection_reason = "sequence_enrollment_rejection_reason"
    actual_email_credits = "actual_email_credits"
    actual_phone_credits = "actual_phone_credits"
    company_id = "company_id"
    account_id = "account_id"


class ProspectingCreditByTypeField(StdFieldIdentifierEnum):
    email_enrichment = "email_enrichment"
    phone_enrichment = "phone_enrichment"


class ProspectingCreditUsageField(StdFieldIdentifierEnum):
    total = "total"
    by_type = "by_type"


class ProspectingListEnrollmentResultField(StdFieldIdentifierEnum):
    list_id = "list_id"
    status = "status"
    rejection_reason = "rejection_reason"


class PeopleEnrichmentSummaryField(StdFieldIdentifierEnum):
    total_enrichment_requests_count = "total_enrichment_requests_count"
    successful_email_enrichments_count = "successful_email_enrichments_count"
    successful_phone_enrichments_count = "successful_phone_enrichments_count"


class ProspectingCreditUsagePointField(StdFieldIdentifierEnum):
    date = "date"
    credits_used = "credits_used"
    usage_type = "usage_type"
    user_id = "user_id"
    user_name = "user_name"


class DomainConfigurationField(StdFieldIdentifierEnum):
    contains_mx_records = "contains_mx_records"
    contains_spf_records = "contains_spf_records"
    contains_dkim_record = "contains_dkim_record"
    contains_dmarc_record = "contains_dmarc_record"
    domain_forwarding = "domain_forwarding"


class ProspectingSavedSearchQueryField(StdFieldIdentifierEnum):
    id = "id"
    name = "name"
    type = "type"
    permission = "permission"
    created_at = "created_at"
    updated_at = "updated_at"
    deleted_at = "deleted_at"


class AISuggestedValueField(StdFieldIdentifierEnum):
    value = "value"
    updated_at = "updated_at"


class ContactPipelineRoleField(StdFieldIdentifierEnum):
    id = "id"
    contact_id = "contact_id"
    pipeline_id = "pipeline_id"
    is_primary_contact = "is_primary_contact"
    role_types = "role_types"
    note = "note"
    creation_ai_rec_id = "creation_ai_rec_id"
    creation_citation_ids = "creation_citation_ids"


class SignatureField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    name = "name"
    signature_html = "signature_html"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"
    owner_user_id = "owner_user_id"


class PaperProcessField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    pipeline_id = "pipeline_id"
    ordered_paper_processes = "ordered_paper_processes"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class MetricField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    pipeline_id = "pipeline_id"
    metrics = "metrics"
    business_negative_consequences = "business_negative_consequences"
    business_positive_outcomes = "business_positive_outcomes"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class IdentifiedPainField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    pipeline_id = "pipeline_id"
    ordered_pain_items = "ordered_pain_items"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class DecisionCriteriaItemField(StdFieldIdentifierEnum):
    id = "id"
    name_ = "name"
    note = "note"
    creation_ai_rec_id = "creation_ai_rec_id"
    creation_citation_ids = "creation_citation_ids"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class DecisionCriteriaField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    pipeline_id = "pipeline_id"
    decision_criteria_items = "decision_criteria_items"
    product_solution_fit_note = "product_solution_fit_note"
    budget_amount = "budget_amount"
    budget_note = "budget_note"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class DecisionProcessField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    solution_launches_by = "solution_launches_by"
    implementation_starts_by = "implementation_starts_by"
    decision_by = "decision_by"
    testing_starts_by = "testing_starts_by"
    testing_ends_by = "testing_ends_by"
    pipeline_id = "pipeline_id"
    ordered_decision_processes = "ordered_decision_processes"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class ChampionField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    pipeline_id = "pipeline_id"
    champions = "champions"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class CompetitionField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    pipeline_id = "pipeline_id"
    competitors = "competitors"
    solution_differentiation = "solution_differentiation"
    note = "note"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class CompetitorField(StdFieldIdentifierEnum):
    id = "id"
    competitor_name = "competitor_name"
    competitor_advantage = "competitor_advantage"
    competitor_disadvantage = "competitor_disadvantage"
    note = "note"
    creation_ai_rec_id = "creation_ai_rec_id"
    creation_citation_ids = "creation_citation_ids"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class WhyAnyField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    pipeline_id = "pipeline_id"
    details = "details"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class WhyNowField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    pipeline_id = "pipeline_id"
    details = "details"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class WhyUsField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    pipeline_id = "pipeline_id"
    details = "details"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class MetricItemField(StdFieldIdentifierEnum):
    id = "id"
    name_ = "name"
    note = "note"
    creation_ai_rec_id = "creation_ai_rec_id"
    creation_citation_ids = "creation_citation_ids"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class IdentifiedPainItemField(StdFieldIdentifierEnum):
    id = "id"
    name_ = "name"
    note = "note"
    creation_ai_rec_id = "creation_ai_rec_id"
    creation_citation_ids = "creation_citation_ids"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class DecisionProcessItemField(StdFieldIdentifierEnum):
    id = "id"
    name_ = "name"
    note = "note"
    creation_ai_rec_id = "creation_ai_rec_id"
    creation_citation_ids = "creation_citation_ids"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class PaperProcessItemField(StdFieldIdentifierEnum):
    id = "id"
    name_ = "name"
    note = "note"
    creation_ai_rec_id = "creation_ai_rec_id"
    creation_citation_ids = "creation_citation_ids"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class ChatField(StdFieldIdentifierEnum):
    id = "id"
    title_ = "title"
    organization_id = "organization_id"
    owner_user_id = "owner_user_id"
    created_at = "created_at"
    updated_at = "updated_at"
    deleted_at = "deleted_at"


class ChatMessageField(StdFieldIdentifierEnum):
    id = "id"
    chat_id = "chat_id"
    role = "role"
    content = "content"
    created_at = "created_at"
    feedback = "feedback"


class GiantTaskField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    title_ = "title"
    note = "note"
    status = "status"
    type = "type"
    due_at = "due_at"
    priority = "priority"
    source_type = "source_type"
    created_at = "created_at"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    completed_at = "completed_at"
    completed_by_user_id = "completed_by_user_id"
    archived_at = "archived_at"
    archived_by_user_id = "archived_by_user_id"
    is_time_specified = "is_time_specified"
    participant_user_id_list = "participant_user_id_list"
    contacts_for_giant_task = "contacts_for_giant_task"

    account_id = "account_id"
    account_organization_id = "account_organization_id"
    account_display_name = "account_display_name"
    account_official_website = "account_official_website"

    pipeline_id = "pipeline_id"
    pipeline_organization_id = "pipeline_organization_id"
    pipeline_display_name = "pipeline_display_name"
    pipeline_stage_id = "pipeline_stage_id"

    citations_for_giant_task = "citations_for_giant_task"

    meeting_id = "meeting_id"
    meeting_title = "meeting_title"

    email_threads_for_giant_task = "email_threads_for_giant_task"

    sequence_id = "sequence_id"
    sequence_name = "sequence_name"
    sequence_step_id = "sequence_step_id"
    sequence_step_name = "sequence_step_name"
    sequence_step_variant_id = "sequence_step_variant_id"
    sequence_step_variant_name = "sequence_step_variant_name"

    owner_user_id = "owner_user_id"
    owner_user_display_name = "owner_user_display_name"
    owner_user_first_name = "owner_user_first_name"
    owner_user_last_name = "owner_user_last_name"
    owner_user_avatar_url = "owner_user_avatar_url"

    created_by_user_id = "created_by_user_id"
    created_by_user_display_name = "created_by_user_display_name"
    created_by_user_first_name = "created_by_user_first_name"
    created_by_user_last_name = "created_by_user_last_name"
    created_by_user_avatar_url = "created_by_user_avatar_url"


class SequenceEnrollmentContactField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    enrollment_run_id = "enrollment_run_id"
    contact_id = "contact_id"
    account_id = "account_id"
    email = "email"
    warning_reasons = "warning_reasons"
    fail_reasons = "fail_reasons"
    status = "status"
    description = "description"
    display_name = "display_name"


class SequenceEnrollmentRunField(StdFieldIdentifierEnum):
    id = "id"
    organization_id = "organization_id"
    sequence_id = "sequence_id"
    mode = "mode"
    workflow_id = "workflow_id"
    status = "status"
    created_by_user_id = "created_by_user_id"
    created_at = "created_at"
    num_successes_contacts = "num_successes_contacts"
    num_warnings_contacts = "num_warnings_contacts"
    num_failures_contacts = "num_failures_contacts"
