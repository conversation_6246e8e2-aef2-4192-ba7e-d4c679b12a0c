from typing import Any, Self

from salestech_be.util.enum_util import NameValueStrEnum


class ErrorType(NameValueStrEnum):
    SERVER_ERROR = "SERVER_ERROR"
    CLIENT_ERROR = "CLIENT_ERROR"


class ErrorCode(NameValueStrEnum):
    def __new__(cls, *values: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if len(values) != 3:  # noqa: PLR2004
            raise ValueError(f"ErrorCode must have 3 values, but found {len(values)}")
        string_value = values[0]
        if not isinstance(string_value, str):
            raise TypeError(f"{string_value!r} is not a string")
        error_type_value = values[1]
        if not isinstance(error_type_value, ErrorType):
            raise TypeError(f"{error_type_value!r} is not a ErrorType")
        error_description_value = values[2]
        if not isinstance(error_description_value, str):
            raise TypeError(f"{error_description_value!r} is not a string")
        _string_value = string_value
        member = str.__new__(cls, string_value)
        member._value_ = _string_value
        member._error_type = error_type_value  # type: ignore[attr-defined]  # noqa: SLF001
        member._error_description = error_description_value  # type: ignore[attr-defined]  # noqa: SLF001
        return member

    def __init__(self, *value: Any):  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().__init__(value[0])

    @property
    def error_type(self) -> ErrorType:
        return ErrorType(self._error_type)  # type: ignore[attr-defined]

    @property
    def error_description(self) -> str:
        return str(self._error_description)  # type: ignore[attr-defined]

    # Generic System Errors
    GENERIC_ERROR = "GENERIC_ERROR", ErrorType.SERVER_ERROR, "generic error"
    SERVER_ERROR = "SERVER_ERROR", ErrorType.SERVER_ERROR, "system error"
    SERVICE_ERROR = (
        "SERVICE_ERROR",
        ErrorType.SERVER_ERROR,
        "external service error",
    )

    # Authentication & Authorization
    FORBIDDEN = "FORBIDDEN", ErrorType.CLIENT_ERROR, "forbidden"
    ORG_NOT_AUTHORIZED = (
        "ORG_NOT_AUTHORIZED",
        ErrorType.CLIENT_ERROR,
        "organization not authorized",
    )

    # Request Validation
    REQUEST_ENTITY_SIZE_TOO_LARGE = (
        "REQUEST_ENTITY_SIZE_TOO_LARGE",
        ErrorType.CLIENT_ERROR,
        "request entity size too large",
    )
    INVALID_UPLOADED_FILE = (
        "INVALID_UPLOADED_FILE",
        ErrorType.CLIENT_ERROR,
        "invalid uploaded file",
    )
    INVALID_PATCH_REQUEST = (
        "INVALID_PATCH_REQUEST",
        ErrorType.CLIENT_ERROR,
        "invalid patch request",
    )
    INVALID_REQUEST = "INVALID_REQUEST", ErrorType.CLIENT_ERROR, "invalid request"
    ILLEGAL_STATE = "ILLEGAL_STATE", ErrorType.CLIENT_ERROR, "illegal state"
    CONFLICT = "CONFLICT", ErrorType.CLIENT_ERROR, "conflict"
    VALUE_TOO_LOW = "VALUE_TOO_LOW", ErrorType.CLIENT_ERROR, "value too low"

    # Database & Resource Access
    RESOURCE_NOT_FOUND = (
        "RESOURCE_NOT_FOUND",
        ErrorType.CLIENT_ERROR,
        "resource not found",
    )
    REF_VIOLATION = "REF_VIOLATION", ErrorType.CLIENT_ERROR, "referential violation"
    DB_UPDATE_ERROR = "DB_UPDATE_ERROR", ErrorType.CLIENT_ERROR, "database update error"
    DATABASE_ERROR = "DATABASE_ERROR", ErrorType.CLIENT_ERROR, "database error"
    MULTIPLE_RESOURCE_FOUND = (
        "MULTIPLE_RESOURCE_FOUND",
        ErrorType.CLIENT_ERROR,
        "multiple resource found",
    )

    # Rate Limiting
    RATE_LIMIT_EXCEEDED = (
        "RATE_LIMIT_EXCEEDED",
        ErrorType.CLIENT_ERROR,
        "rate limit exceeded",
    )

    # List Operations (Filtering & Sorting)
    FILTER_REQUIRED_FIELD_MISSING = (
        "FILTER_REQUIRED_FIELD_MISSING",
        ErrorType.CLIENT_ERROR,
        "filter required field missing",
    )
    FILTER_FIELD_NOT_SUPPORTED = (
        "FILTER_FIELD_NOT_SUPPORTED",
        ErrorType.CLIENT_ERROR,
        "filter field not supported",
    )
    FILTER_PREDICATE_NOT_SUPPORTED = (
        "FILTER_PREDICATE_NOT_SUPPORTED",
        ErrorType.CLIENT_ERROR,
        "filter predicate not supported",
    )
    FILTER_OPERATOR_NOT_SUPPORTED = (
        "FILTER_OPERATOR_NOT_SUPPORTED",
        ErrorType.CLIENT_ERROR,
        "filter operator not supported",
    )
    SORTER_FIELD_NOT_SUPPORTED = (
        "SORTER_FIELD_NOT_SUPPORTED",
        ErrorType.CLIENT_ERROR,
        "sorter field not supported",
    )

    # Provider Related
    MISSING_PROVIDER_ID = (
        "MISSING_PROVIDER_ID",
        ErrorType.CLIENT_ERROR,
        "missing provider id",
    )
    MISSING_PROVIDER_ENTITY = (
        "MISSING_PROVIDER_ENTITY",
        ErrorType.CLIENT_ERROR,
        "missing provider entity",
    )

    # External Service Integration Errors
    NYLAS_CALL_ERROR = "NYLAS_CALL_ERROR", ErrorType.CLIENT_ERROR, "nylas call error"
    CRUSTDATA_CALL_ERROR = (
        "CRUSTDATA_CALL_ERROR",
        ErrorType.CLIENT_ERROR,
        "crustdata call error",
    )
    LINKEDIN_CALL_ERROR = (
        "LINKEDIN_CALL_ERROR",
        ErrorType.CLIENT_ERROR,
        "linkedin call error",
    )
    HUBSPOT_CALL_ERROR = (
        "HUBSPOT_CALL_ERROR",
        ErrorType.CLIENT_ERROR,
        "hubspot call error",
    )
    KNOCK_CALL_ERROR = "KNOCK_CALL_ERROR", ErrorType.CLIENT_ERROR, "knock call error"

    CRUSTDATA_INVALID_ACCESS_TOKEN = (
        "CRUSTDATA_INVALID_ACCESS_TOKEN",
        ErrorType.CLIENT_ERROR,
        "crustdata invalid access token",
    )
    PDL_CALL_ERROR = (
        "PDL_CALL_ERROR",
        ErrorType.CLIENT_ERROR,
        "pdl call error",
    )
    PDL_INVALID_ACCESS_TOKEN = (
        "PDL_INVALID_ACCESS_TOKEN",
        ErrorType.CLIENT_ERROR,
        "pdl invalid access token",
    )

    # Messaging Related
    INCORRECT_MESSAGE_NUMBER_TO_SEND = (
        "INCORRECT_MESSAGE_NUMBER_TO_SEND",
        ErrorType.CLIENT_ERROR,
        "incorrect message number to send",
    )
    MESSAGE_MISSING_THREAD = (
        "MESSAGE_MISSING_THREAD",
        ErrorType.CLIENT_ERROR,
        "message missing thread",
    )
    MESSAGE_SEND_FAILURE = (
        "MESSAGE_SEND_FAILURE",
        ErrorType.CLIENT_ERROR,
        "message send failure",
    )
    EMAIL_ACCOUNT_CONFIGURATION_ERROR = (
        "EMAIL_ACCOUNT_CONFIGURATION_ERROR",
        ErrorType.CLIENT_ERROR,
        "email account configuration error",
    )

    # Email Account Related
    EMAIL_ACCOUNT_ALREADY_EXISTS_WITH_DOMAIN_NAME = (
        "EMAIL_ACCOUNT_ALREADY_EXISTS_WITH_DOMAIN_NAME",
        ErrorType.CLIENT_ERROR,
        "email account already exists with domain name",
    )

    # Email Account Related
    EMAIL_ACCOUNT_FAILED_TO_PURCHASE_MAILBOX = (
        "EMAIL_ACCOUNT_FAILED_TO_PURCHASE_MAILBOX",
        ErrorType.CLIENT_ERROR,
        "email account failed to purchase mailbox",
    )

    # Email Account Related
    EMAIL_ACCOUNT_FAILED_TO_GET_MAILBOX_CREDENTIALS = (
        "EMAIL_ACCOUNT_FAILED_TO_GET_MAILBOX_CREDENTIALS",
        ErrorType.CLIENT_ERROR,
        "email account failed to get mailbox credentials",
    )

    EMAIL_ACCOUNT_QUOTA_EXCEEDED = (
        "EMAIL_ACCOUNT_QUOTA_EXCEEDED",
        ErrorType.CLIENT_ERROR,
        "email account quota exceeded",
    )

    # Custom Object Related
    CUSTOM_OBJECT_SLOT_MAX_OUT = (
        "CUSTOM_OBJECT_SLOT_MAX_OUT",
        ErrorType.CLIENT_ERROR,
        "custom object slot max out",
    )
    CUSTOM_FIELD_ALREADY_REMOVED = (
        "CUSTOM_FIELD_ALREADY_REMOVED",
        ErrorType.CLIENT_ERROR,
        "custom field already removed",
    )
    CUSTOM_FIELD_NOT_FOUND = (
        "CUSTOM_FIELD_NOT_FOUND",
        ErrorType.CLIENT_ERROR,
        "custom field not found",
    )
    CUSTOM_FIELD_ENUM_PROPERTY_NOT_FOUND = (
        "CUSTOM_FIELD_ENUM_PROPERTY_NOT_FOUND",
        ErrorType.CLIENT_ERROR,
        "custom field enum property not found",
    )
    CUSTOM_FIELD_ENUM_PROPERTY_IS_INACTIVE = (
        "CUSTOM_FIELD_ENUM_PROPERTY_IS_INACTIVE",
        ErrorType.CLIENT_ERROR,
        "custom field enum property is inactive",
    )
    CUSTOM_OBJECT_IS_READ_ONLY = (
        "CUSTOM_OBJECT_IS_READ_ONLY",
        ErrorType.CLIENT_ERROR,
        "custom object is read only",
    )
    CUSTOM_OBJECT_DATA_VALIDATION_ERROR = (
        "CUSTOM_OBJECT_DATA_VALIDATION_ERROR",
        ErrorType.CLIENT_ERROR,
        "custom object data validation error",
    )
    CUSTOM_OBJECT_DATA_INTEGRITY_ERROR = (
        "CUSTOM_OBJECT_DATA_INTEGRITY_ERROR",
        ErrorType.CLIENT_ERROR,
        "custom object data integrity error",
    )
    CUSTOM_OBJECT_WITH_SAME_DISPLAY_NAME_ALREADY_EXISTS = (
        "CUSTOM_OBJECT_WITH_SAME_DISPLAY_NAME_ALREADY_EXISTS",
        ErrorType.CLIENT_ERROR,
        "custom object with same display name already exists",
    )
    CUSTOM_OBJECT_DATA_RECORD_WITH_SAME_DISPLAY_NAME_ALREADY_EXISTS = (
        "CUSTOM_OBJECT_DATA_RECORD_WITH_SAME_DISPLAY_NAME_ALREADY_EXISTS",
        ErrorType.CLIENT_ERROR,
        "custom object data record with same display name already exists",
    )

    # Account Related
    ACCOUNT_ALREADY_EXISTS_WITH_DOMAIN_NAME = (
        "ACCOUNT_ALREADY_EXISTS_WITH_DOMAIN_NAME",
        ErrorType.CLIENT_ERROR,
        "account already exists with domain name",
    )
    ACCOUNT_NOT_FOUND = "ACCOUNT_NOT_FOUND", ErrorType.CLIENT_ERROR, "account not found"
    ACCOUNT_HAS_ACTIVE_PIPELINE_ASSOCIATIONS = (
        "ACCOUNT_HAS_ACTIVE_PIPELINE_ASSOCIATIONS",
        ErrorType.CLIENT_ERROR,
        "account has active pipeline associations",
    )

    # Campaign Related
    CAMPAIGN_ALREADY_EXISTS = (
        "CAMPAIGN_ALREADY_EXISTS",
        ErrorType.CLIENT_ERROR,
        "campaign already exists",
    )

    # Contact Related
    CONTACT_ACCOUNT_ASSOCIATION_NOT_FOUND = (
        "CONTACT_ACCOUNT_ASSOCIATION_NOT_FOUND",
        ErrorType.CLIENT_ERROR,
        "contact account association not found",
    )
    CONTACT_ALREADY_EXISTS_WITH_EMAIL = (
        "CONTACT_ALREADY_EXISTS_WITH_EMAIL",
        ErrorType.CLIENT_ERROR,
        "contact already exists with email",
    )
    CONTACT_NOT_FOUND = "CONTACT_NOT_FOUND", ErrorType.CLIENT_ERROR, "contact not found"
    CONTACT_ACCOUNT_ASSOCIATION_HAS_ACTIVE_PIPELINE_ASSOCIATION = (
        "CONTACT_ACCOUNT_ASSOCIATION_HAS_ACTIVE_PIPELINE_ASSOCIATION",
        ErrorType.CLIENT_ERROR,
        "contact account association has active pipeline association",
    )
    CONTACT_NOT_ASSOCIATED_WITH_EMAIL = (
        "CONTACT_NOT_ASSOCIATED_WITH_EMAIL",
        ErrorType.CLIENT_ERROR,
        "contact not associated with email",
    )

    # Pipeline Related
    PIPELINE_ALREADY_EXISTS_WITH_NAME_AND_ACCOUNT = (
        "PIPELINE_ALREADY_EXISTS_WITH_NAME_AND_ACCOUNT",
        ErrorType.CLIENT_ERROR,
        "pipeline already exists with name and account",
    )

    # Domain Related
    DOMAIN_ALREADY_EXISTS = (
        "DOMAIN_ALREADY_EXISTS",
        ErrorType.CLIENT_ERROR,
        "domain already exists",
    )
    DOMAIN_NOT_ACTIVE = "DOMAIN_NOT_ACTIVE", ErrorType.CLIENT_ERROR, "domain not active"
    DOMAIN_NOT_INACTIVE = (
        "DOMAIN_NOT_INACTIVE",
        ErrorType.CLIENT_ERROR,
        "domain not inactive",
    )
    DOMAIN_NOT_AVAILABLE = (
        "DOMAIN_NOT_AVAILABLE",
        ErrorType.CLIENT_ERROR,
        "domain not available",
    )
    DOMAIN_NOT_FOUND = "DOMAIN_NOT_FOUND", ErrorType.CLIENT_ERROR, "domain not found"
    DOMAIN_HEALTH_NOT_FOUND = (
        "DOMAIN_HEALTH_NOT_FOUND",
        ErrorType.CLIENT_ERROR,
        "domain health not found",
    )
    DOMAIN_QUOTA_EXCEEDED = (
        "DOMAIN_QUOTA_EXCEEDED",
        ErrorType.CLIENT_ERROR,
        "domain quota exceeded",
    )
    # Extraction Related
    EXTRACTION_CONFIG_CREATION_FAILED = (
        "EXTRACTION_CONFIG_CREATION_FAILED",
        ErrorType.CLIENT_ERROR,
        "extraction config creation failed",
    )
    EXTRACTION_CONFIG_FEATURE_LINK_NOT_FOUND = (
        "EXTRACTION_CONFIG_FEATURE_LINK_NOT_FOUND",
        ErrorType.CLIENT_ERROR,
        "extraction config feature link not found",
    )
    EXTRACTION_CONFIG_MISSING_FEATURE_LINK = (
        "EXTRACTION_CONFIG_MISSING_FEATURE_LINK",
        ErrorType.CLIENT_ERROR,
        "extraction config missing feature link",
    )
    EXTRACTION_CONFIG_NOT_FOUND = (
        "EXTRACTION_CONFIG_NOT_FOUND",
        ErrorType.CLIENT_ERROR,
        "extraction config not found",
    )
    EXTRACTION_FIELD_CREATION_FAILED = (
        "EXTRACTION_FIELD_CREATION_FAILED",
        ErrorType.CLIENT_ERROR,
        "extraction field creation failed",
    )

    # Mailbox Related
    MAILBOXES_ALREADY_EXIST = (
        "MAILBOXES_ALREADY_EXIST",
        ErrorType.CLIENT_ERROR,
        "mailboxes already exist",
    )

    # Meeting Related
    MEETING_NOT_FOUND = "MEETING_NOT_FOUND", ErrorType.CLIENT_ERROR, "meeting not found"

    # User Related
    USER_NOT_FOUND = "USER_NOT_FOUND", ErrorType.CLIENT_ERROR, "user not found"

    # Workspace Related
    WORKSPACE_ALREADY_EXISTS = (
        "WORKSPACE_ALREADY_EXISTS",
        ErrorType.CLIENT_ERROR,
        "workspace already exists",
    )
    MOCK_WORKSPACE_CANNOT_BUY_REAL_DOMAIN = (
        "MOCK_WORKSPACE_CANNOT_BUY_REAL_DOMAIN",
        ErrorType.CLIENT_ERROR,
        "mock workspace cannot buy real domain",
    )
    WORKSPACE_CREATION_FAILED = (
        "WORKSPACE_CREATION_FAILED",
        ErrorType.CLIENT_ERROR,
        "workspace creation failed",
    )
    WORKSPACE_NOT_FOUND = (
        "WORKSPACE_NOT_FOUND",
        ErrorType.CLIENT_ERROR,
        "workspace not found",
    )

    # Service Related
    RETRIES_EXHAUSTED = (
        "RETRIES_EXHAUSTED",
        ErrorType.CLIENT_ERROR,
        "retries exhausted",
    )

    # CRM Integrity
    INTEGRITY_JOB_VALIDATION_ERROR = (
        "INTEGRITY_JOB_VALIDATION_ERROR",
        ErrorType.CLIENT_ERROR,
        "integrity job validation error",
    )

    # Quota Related
    QUOTA_LIMIT_EXCEEDED = (
        "QUOTA_LIMIT_EXCEEDED",
        ErrorType.CLIENT_ERROR,
        "quota limit exceeded",
    )
    NO_QUOTA_POLICY = (
        "NO_QUOTA_POLICY",
        ErrorType.CLIENT_ERROR,
        "no quota policy",
    )
    MULTIPLE_QUOTA_POLICIES = (
        "MULTIPLE_QUOTA_POLICIES",
        ErrorType.CLIENT_ERROR,
        "multiple quota policies",
    )
