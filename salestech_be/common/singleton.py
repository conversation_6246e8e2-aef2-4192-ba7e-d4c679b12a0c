from typing import Any, Self, cast


class Singleton:
    _instance: Any | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
    _initialized: bool = False

    def __new__(cls, *args: object, **kwargs: object) -> Self:
        if cls._instance is None:
            instance = super().__new__(cls)
            cls._instance = instance
        return cast(Self, cls._instance)

    def __init__(self, *args: object, **kwargs: object) -> None:
        if not self._initialized:  # Prevent reinitialization
            self._initialized = True
            super().__init__(*args, **kwargs)

    @classmethod
    def get_singleton_instance(cls) -> Self:
        """
        Get the singleton instance. Creates a new instance if one doesn't exist.

        Returns:
            The singleton instance
        """
        if cls._instance is None:
            instance = cls()
            cls._instance = instance
        return cast(Self, cls._instance)

    @classmethod
    def has_instance(cls) -> bool:
        """
        Check if a singleton instance exists.

        Returns:
            bool: True if an instance exists, False otherwise
        """
        return cls._instance is not None
