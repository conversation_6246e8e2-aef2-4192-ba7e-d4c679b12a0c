import asyncio
import re
from collections.abc import Coroutine
from datetime import UTC, datetime
from typing import Any, TypeVar
from uuid import UUID

from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.validation import not_none

logger = get_logger(__name__)

T = TypeVar("T")


def is_dev_env() -> bool:
    return settings.environment == "dev"


def is_prod_env() -> bool:
    return settings.environment == "prod"


def is_local_env() -> bool:
    return settings.environment == "local"


def is_reevo_org(org_id: UUID) -> bool:
    return str(org_id) in settings.reevo_org_ids


def exactly_one_not_none(a: Any, b: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    return (a is None) != (b is None)


def sanitize_linkedin_url(url: str) -> str | None:
    url = re.sub(r"\?.*$", "", url)
    pattern = r"linkedin\.com/(in|company|school)/[a-zA-Z0-9\-'&%+_\..]+/?$"

    # Trim the URL and ensure it starts with 'https'
    sanitized_url = url.strip().replace("http://", "https://", 1)

    # Check if the URL matches the expected pattern
    if not re.match(r"^(https?://)?(www\.)?" + pattern, sanitized_url):
        return None

    # Ensure the URL starts with 'https://www.'
    if not re.match(r"https://www\.", sanitized_url):
        # Extract the LinkedIn path and append it to 'https://www.linkedin.com/'
        path = not_none(
            re.search(
                pattern,
                sanitized_url,
            )
        ).group()
        sanitized_url = f"https://www.{path}"

    # Remove any trailing slash
    return sanitized_url.rstrip("/")


def convert_to_compact_amount(amount: int | None) -> str | None:
    if amount is None:
        return None

    suffixes = ["", "K", "M", "B", "T"]
    for i, suffix in enumerate(suffixes):
        unit = 1_000**i
        if amount < unit * 1_000:
            return f"{amount / unit:.1f}{suffix}" if suffix else f"{amount}"

    # Default return if none of the conditions above match (e.g., extraordinarily large amounts)
    return f"{amount / (1_000 ** (len(suffixes) - 1)):.1f}{suffixes[-1]}"


def get_caller_function_name() -> str | None:
    import traceback

    stack = traceback.extract_stack()
    caller_position = 3
    if len(stack) < caller_position:
        return None
    caller = stack[-caller_position]
    caller_name = caller.name
    caller_filename = caller.filename
    caller_lineno = caller.lineno
    return f"{caller_filename}:{caller_lineno}:{caller_name}"


def get_meeting_share_url(meeting_share_id: UUID) -> str:
    base_url = "app.reevo.ai" if is_prod_env() else "app-ng-dev.reevo.ai"
    return f"https://{base_url}/public/meeting/shares/{meeting_share_id}"


def validate_datetime_to_epoch_or_none(value: Any) -> int | None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Convert various datetime formats to epoch timestamp, or return None if value is empty or None.

    Args:
        value: The value to convert, can be int, str, or datetime

    Returns:
        int | None: Unix epoch timestamp, or None if conversion fails
    """
    if value is None:
        return None
    if isinstance(value, str):
        stripped_lower = value.strip().lower()
        if stripped_lower == "":
            return None
        if stripped_lower in ("null", "none"):
            return None
    return validate_datetime_to_epoch(value)


EPOCH_S_MIN = -2e10
EPOCH_S_MAX = 2e10

EPOCH_S_NEWS_REASONABLE_MIN = (
    1000000000  # 2001-09-09, any news before is considered irrelevant
)


def validate_datetime_to_epoch(value: Any) -> int:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Convert various datetime formats to epoch timestamp.

    Args:
        value: The value to convert, can be int, str, or datetime

    Returns:
        int: Unix epoch timestamp, raising error if conversion fails
    """

    # https://docs.pydantic.dev/2.0/usage/types/datetime/
    # assumed as Unix time, i.e. seconds (if >= -2e10 and <= 2e10) or milliseconds (if < -2e10 or > 2e10)
    if isinstance(value, int):
        return value if (EPOCH_S_MIN <= value <= EPOCH_S_MAX) else int(value / 1000)
    if isinstance(value, float):
        return (
            int(value) if (EPOCH_S_MIN <= value <= EPOCH_S_MAX) else int(value / 1000)
        )
    if isinstance(value, str):
        # if string represents a numeric value, assume it's a Unix time
        try:
            numeric_value = float(value)
            return (
                int(numeric_value)
                if (EPOCH_S_MIN <= numeric_value <= EPOCH_S_MAX)
                else int(numeric_value / 1000)
            )
        except ValueError:
            pass
        # Try common date formats, assuming UTC timezone
        for fmt in [
            "%Y-%m-%d",
            "%m/%d/%Y",
            "%d/%m/%Y",
            "%b %d %Y",
            "%b %d, %Y",
            "%B %d %Y",
            "%B %d, %Y",
        ]:
            try:
                # For date-only formats, explicitly set time to midnight UTC
                naive_dt = datetime.strptime(value, fmt)  # noqa: DTZ007
                # Create UTC datetime at midnight
                dt = datetime(
                    year=naive_dt.year,
                    month=naive_dt.month,
                    day=naive_dt.day,
                    hour=0,
                    minute=0,
                    second=0,
                    tzinfo=UTC,
                )
                return int(dt.timestamp())
            except ValueError:
                continue
        try:
            # Try ISO format first
            dt = datetime.fromisoformat(value.replace("Z", "+00:00"))
            return int(dt.timestamp())
        except ValueError as e:
            logger.warning(f"cannot parse datetime: {value}")
            raise ValueError(
                f"cannot parse datetime of type {type(value)}: {value}"
            ) from e
    if isinstance(value, datetime):
        # If datetime is naive, assume UTC
        if value.tzinfo is None:
            value = value.replace(tzinfo=UTC)
        return int(value.timestamp())
    # if type not expected, raise
    logger.warning(f"cannot parse datetime of type {type(value)}: {value}")
    raise ValueError(f"cannot parse datetime of type {type(value)}: {value}")


# Reference: https://discuss.python.org/t/boundedtaskgroup-to-control-parallelism/27171
class BoundedTaskGroup(asyncio.TaskGroup):
    """A TaskGroup that limits the number of concurrent tasks.

    This class extends asyncio.TaskGroup to add a concurrency limit through a semaphore.
    Tasks created through this group will be bounded by the max_parallelism parameter.

    Args:
        max_parallelism: Maximum number of tasks that can run concurrently.
            If 0 or negative, no limit is applied.
        *args: Additional arguments passed to asyncio.TaskGroup.

    Example:
        async with BoundedTaskGroup(max_parallelism=5) as tg:
            for item in large_collection:
                tg.create_task(method_call(item))
    """

    def __init__(self, *args: Any, max_parallelism: int = 0) -> None:  # type: ignore[explicit-any]
        super().__init__(*args)
        self._sem = asyncio.Semaphore(max_parallelism) if max_parallelism > 0 else None

    def create_task(  # type: ignore[explicit-any]
        self,
        coro: Coroutine[Any, Any, T],
        *args: Any,
        **kwargs: Any,
    ) -> asyncio.Task[T]:
        """Create a new task within the group, respecting the concurrency limit.

        Args:
            coro: The coroutine to schedule as a task.
            *args: Additional arguments passed to asyncio.TaskGroup.create_task.
            **kwargs: Additional keyword arguments passed to asyncio.TaskGroup.create_task.

        Returns:
            The created task object.
        """
        if self._sem:

            async def _wrapped_coro(  # type: ignore[explicit-any]
                sem: asyncio.Semaphore, coro: Coroutine[Any, Any, T]
            ) -> T:
                async with sem:
                    return await coro

            coro = _wrapped_coro(self._sem, coro)

        return super().create_task(coro, *args, **kwargs)
