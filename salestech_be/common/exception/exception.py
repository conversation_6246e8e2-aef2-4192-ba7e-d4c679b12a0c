from abc import ABC, abstractmethod
from http import HTTPStatus
from typing import Annotated, Any, Generic, Literal, Self
from uuid import UUID

from pydantic import BaseModel, Discriminator, Field, computed_field, model_validator
from typing_extensions import TypeVar

from salestech_be.common.error_code import ErrorCode, ErrorType
from salestech_be.common.type.metadata.common import (
    CustomObjectIdentifier,
    ObjectIdentifier,
    RelationshipId,
    StandardObjectIdentifier,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.enum_util import NameValueStrEnum

logger = get_logger()


# ############################################################################
# Base Error Details Definitions <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
# ############################################################################
class ErrorDetails(BaseModel):
    code: str = ""  # to be deprecated, user should define error_code enum instead
    details: str | None = None
    reference_id: str | None = None
    error_code: ErrorCode = Field(
        default=ErrorCode.GENERIC_ERROR,
        json_schema_extra={
            "x-enumDescriptions": {
                code: ErrorCode(code).error_description for code in ErrorCode
            }
        },
        description="programmatic error code",
    )

    @model_validator(mode="after")
    def coerce_error_code(self) -> Self:
        if (
            self.code
            and self.code in ErrorCode
            and ("error_code" not in self.model_fields_set)
        ):
            self.error_code = ErrorCode(self.code)
        return self

    @computed_field(  # type: ignore[prop-decorator]
        title="Error Code Description",
        return_type=str,
        description="Detailed meaning of the error",
    )
    @property
    def error_description(self) -> str:
        return ErrorCode(self.error_code).error_description

    @computed_field(  # type: ignore[prop-decorator]
        title="Error Type",
        return_type=ErrorType,
        description="Type of the error",
    )
    @property
    def error_type(self) -> ErrorType:
        return ErrorCode(self.error_code).error_type


ErrorDetailsT = TypeVar("ErrorDetailsT", bound=ErrorDetails)


# ############################################################################
# Base Error Details Definitions <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
# ############################################################################


# ############################################################################
# Base Error Response Definitions >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
# ############################################################################


class ErrorResponseType(NameValueStrEnum):
    GENERIC = "GENERIC"
    CONFLICT = "CONFLICT"
    REF_VIOLATION = "REF_VIOLATION"


class BaseErrorResponse(BaseModel, Generic[ErrorDetailsT]):
    error_response_type: ErrorResponseType
    error: str
    message: str
    details: ErrorDetailsT | None = None


class GenericErrorResponse(BaseErrorResponse[ErrorDetails]):
    error_response_type: Literal[ErrorResponseType.GENERIC] = ErrorResponseType.GENERIC
    error: str
    message: str
    details: ErrorDetails | None = None


# ############################################################################
# Base Error Response Definitions <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
# ############################################################################


# ############################################################################
# Concrete Error Details / Response Definitions <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
# ############################################################################


class ConflictErrorDetails(ErrorDetails):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    reference_id: str | None = None
    error_code: ErrorCode = ErrorCode.CONFLICT
    conflicted_existing_object: ObjectIdentifier = Field(
        description=f"""The name of the existing object:\n
        e.g.:
       {StandardObjectIdentifier(object_name="account").model_dump_json()}
       {CustomObjectIdentifier(object_id=UUID("d3b590a9-6eb8-4c8f-ad1d-e34811c6d7ec"), organization_id=UUID("f58c1a2a-a371-45a3-a16c-a29a82a86eb3")).model_dump_json()}
"""
    )
    conflicted_existing_object_attrs: dict[str | UUID, Any] = Field(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        default_factory=dict,
        description="""Attributes of the existing object that caused conflict:\n e.g. {"primary_email": "<EMAIL>"}""",
    )


class ReevoConflictErrorResponse(BaseErrorResponse[ConflictErrorDetails]):
    error_response_type: Literal[ErrorResponseType.CONFLICT] = (
        ErrorResponseType.CONFLICT
    )
    details: ConflictErrorDetails | None = None


class ReferentialViolationErrorDetails(ErrorDetails):
    error_code: ErrorCode = ErrorCode.REF_VIOLATION
    reference_id: str | None = None
    violating_object: ObjectIdentifier = Field(
        description="The object that violates the referential integrity"
    )
    violating_relationship_id: RelationshipId = Field(
        description="The relationship that is violated"
    )
    dependent_object: ObjectIdentifier = Field(
        description="The object that depends on the violating object"
    )
    dependent_object_record_ids: list[UUID] = Field(
        description="The ids of the dependent objects"
    )


class ReferentialViolationErrorResponse(
    BaseErrorResponse[ReferentialViolationErrorDetails]
):
    error_response_type: Literal[ErrorResponseType.REF_VIOLATION] = (
        ErrorResponseType.REF_VIOLATION
    )
    details: ReferentialViolationErrorDetails | None = None


# ############################################################################
# Concrete Error Details / Response Definitions <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
# ############################################################################

# ############################################################################
# Error Response Discriminated Union <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
# ############################################################################

DefaultReevoErrorResponse = Annotated[
    GenericErrorResponse | ReferentialViolationErrorResponse,
    Discriminator("error_response_type"),
]


class BaseApplicationError(Exception, Generic[ErrorDetailsT], ABC):
    def __init__(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        *args: Any,
        additional_error_details: ErrorDetailsT | None = None,
    ):
        super().__init__(*args)
        self.additional_error_details = additional_error_details

    @abstractmethod
    def http_code(self) -> int:
        return HTTPStatus.INTERNAL_SERVER_ERROR

    @abstractmethod
    def to_reevo_error_response(self) -> BaseErrorResponse[ErrorDetailsT]:
        raise NotImplementedError


class ApplicationError(BaseApplicationError[ErrorDetails]):
    def __init__(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        *args: Any,
        additional_error_details: ErrorDetailsT | None = None,
    ):
        super().__init__(*args)
        self.additional_error_details = additional_error_details

    @abstractmethod
    def http_code(self) -> int:
        return HTTPStatus.INTERNAL_SERVER_ERROR

    def to_reevo_error_response(self) -> GenericErrorResponse:
        return GenericErrorResponse(
            error_response_type=ErrorResponseType.GENERIC,
            error=self.__class__.__name__,
            message=str(self),
            details=self.additional_error_details,
        )


class DatabaseError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.INTERNAL_SERVER_ERROR


class ConcurrentModificationError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.CONFLICT


class ResourceNotFoundError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.NOT_FOUND


class InvalidRecordCountError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.BAD_REQUEST


class FailedDependencyError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.FAILED_DEPENDENCY


class ImportEntityDependencyNotFoundError(ResourceNotFoundError):
    def __init__(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        *args: Any,
        entity_type: str,
        entity_value: str | None = None,
        message: str | None = None,
    ):
        super().__init__(*args)
        self.entity_value = (entity_value,)
        self.entity_type = (entity_type,)
        self.message = message


class InvalidArgumentError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.BAD_REQUEST


class ReferentialViolationError(BaseApplicationError[ReferentialViolationErrorDetails]):
    def http_code(self) -> int:
        return HTTPStatus.BAD_REQUEST

    def to_reevo_error_response(self) -> ReferentialViolationErrorResponse:
        return ReferentialViolationErrorResponse(
            error=self.__class__.__name__,
            message=str(self),
            details=self.additional_error_details,
        )


class ConflictResourceError(BaseApplicationError[ConflictErrorDetails]):
    def http_code(self) -> int:
        return HTTPStatus.CONFLICT

    def to_reevo_error_response(self) -> ReevoConflictErrorResponse:
        return ReevoConflictErrorResponse(
            error=self.__class__.__name__,
            message=str(self),
            details=self.additional_error_details,
        )


class ImportEntityConflictError(ConflictResourceError):
    def __init__(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        *args: Any,
        conflict_entity_id: UUID,
        message: str | None = None,
        additional_error_details: ConflictErrorDetails | None = None,
    ):
        super().__init__(*args, additional_error_details=additional_error_details)
        self.conflict_entity_id = conflict_entity_id
        self.message = message


class CRMSyncConflictError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.CONFLICT


class CRMSyncStatemachineDriftError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.CONFLICT


class IllegalStateError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.INTERNAL_SERVER_ERROR


class ServiceError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.INTERNAL_SERVER_ERROR


class UnauthorizedError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.UNAUTHORIZED


class ForbiddenError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.FORBIDDEN


class PaymentError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.PAYMENT_REQUIRED


class ClientError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.BAD_GATEWAY


class ExternalServiceError(ApplicationError):
    # NOTE: add some special handling for ExternalServiceError
    #       to prevent leaking out our vendor information
    #       and to provide generic user friendly error message
    def __init__(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        *args: Any,
        return_http_code: int = HTTPStatus.INTERNAL_SERVER_ERROR,
        additional_error_details: ErrorDetails | None = None,
        response_json: list[Any] | dict[str, Any] | None = None,
    ):
        # NOTE: We want to preserve/translate the original http code in some cases
        #   but if the callsite does not specify a http code, the default is 500
        self.return_http_code = return_http_code
        self.original_error_code = (
            additional_error_details.code if additional_error_details else None
        )
        self.response_json = response_json
        # With our sentry loguru config, this should be sent to Sentry as well
        if return_http_code < HTTPStatus.INTERNAL_SERVER_ERROR:
            logger.bind(
                return_http_code=return_http_code,
                **(
                    additional_error_details.model_dump(mode="python")
                    if additional_error_details
                    else {}
                ),
            ).warning("External service bad request")
        else:
            logger.bind(
                return_http_code=return_http_code,
                **(
                    additional_error_details.model_dump(mode="python")
                    if additional_error_details
                    else {}
                ),
            ).error("External service error")

        self.hidden_error_details: ErrorDetails | None = (
            None  # Do not expose details beyond the service layer.
        )
        user_friendly_error_details = None
        if additional_error_details:
            user_friendly_error_details = ErrorDetails(
                code=ErrorCode.SERVICE_ERROR,
                details="An error occurred. Please try again later.",
            )
            self.hidden_error_details = additional_error_details

        super().__init__(*args, additional_error_details=user_friendly_error_details)

    def get_original_http_code(self) -> int:
        return self.return_http_code

    def http_code(self) -> int:
        return self.return_http_code

    def get_original_error_code(self) -> str | None:
        return self.original_error_code


class RequestEntityTooLargeError(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.REQUEST_ENTITY_TOO_LARGE


class UnprocessableEntity(ApplicationError):
    def http_code(self) -> int:
        return HTTPStatus.UNPROCESSABLE_ENTITY


class FalkorDBUnavailableError(ApplicationError):
    """Raised when FalkorDB connection could not be established during startup."""

    def http_code(self) -> int:
        # Service unavailable is appropriate here
        return HTTPStatus.SERVICE_UNAVAILABLE
