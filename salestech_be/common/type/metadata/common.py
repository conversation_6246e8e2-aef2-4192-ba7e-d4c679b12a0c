from enum import StrEnum
from typing import Annotated, Literal
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from salestech_be.util.enum_util import NameValueStrEnum


class FieldKind(NameValueStrEnum):
    STANDARD = "STANDARD"
    CUSTOM = "CUSTOM"


class ObjectKind(NameValueStrEnum):
    STANDARD = "STANDARD"
    CUSTOM = "CUSTOM"


class ObjectAccessStatus(NameValueStrEnum):
    ACTIVE = "ACTIVE"
    DELETED = "DELETED"
    ARCHIVED = "ARCHIVED"
    INTEGRITY_JOB_RUNNING = "INTEGRITY_JOB_RUNNING"


class RelationshipType(StrEnum):
    LOOKUP = "LOOKUP"
    PARENT_CHILD = "PARENT_CHILD"


class RelationshipCardinality(StrEnum):
    ONE_TO_ONE = "ONE_TO_ONE"
    ONE_TO_MANY = "ONE_TO_MANY"
    MANY_TO_MANY = "MANY_TO_MANY"


class AssociationState(StrEnum):
    ACTIVE = "ACTIVE"
    DISABLED = "DISABLED"
    ACTIVATING = "ACTIVATING"


class ValidationAction(StrEnum):
    """Enum for validation action types."""

    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"


class BaseObjectIdentifier(BaseModel):
    model_config = ConfigDict(frozen=True)
    object_kind: ObjectKind


class StandardObjectIdentifier(BaseObjectIdentifier):
    object_kind: Literal[ObjectKind.STANDARD] = ObjectKind.STANDARD
    object_name: str


class CustomObjectIdentifier(BaseObjectIdentifier):
    object_kind: Literal[ObjectKind.CUSTOM] = ObjectKind.CUSTOM
    organization_id: UUID
    object_id: UUID


class BaseFieldIdentifier(BaseModel):
    model_config = ConfigDict(frozen=True)
    field_kind: FieldKind


class StandardFieldIdentifier(BaseFieldIdentifier):
    field_kind: Literal[FieldKind.STANDARD] = FieldKind.STANDARD
    field_name: str


class CustomFieldIdentifier(BaseFieldIdentifier):
    field_kind: Literal[FieldKind.CUSTOM] = FieldKind.CUSTOM
    field_id: UUID


ObjectIdentifier = Annotated[
    StandardObjectIdentifier | CustomObjectIdentifier,
    Field(description="The identifier of the object", discriminator="object_kind"),
]


def object_id_or_name(object_identifier: ObjectIdentifier) -> str | UUID:
    if isinstance(object_identifier, StandardObjectIdentifier):
        return object_identifier.object_name
    return str(object_identifier.object_id)


FieldIdentifier = Annotated[
    StandardFieldIdentifier | CustomFieldIdentifier,
    Field(description="The identifier of the field", discriminator="field_kind"),
]


def field_identifier(
    field_name_or_id: str | UUID,
) -> FieldIdentifier:
    if isinstance(field_name_or_id, UUID):
        return CustomFieldIdentifier(field_id=field_name_or_id)
    return StandardFieldIdentifier(field_name=field_name_or_id)


StandardObjectRelationShipId = str
CustomObjectRelationShipId = UUID
RelationshipId = Annotated[
    CustomObjectRelationShipId
    | StandardObjectRelationShipId,  # Try UUID first, then str
    Field(description="The identifier of the relationship", union_mode="left_to_right"),
]
