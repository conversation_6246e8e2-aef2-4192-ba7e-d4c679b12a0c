import typing
from collections.abc import Mapping
from typing import Literal, assert_never

from frozendict import frozendict
from pydantic import BaseModel, ConfigDict

from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.util.enum_util import NameValueStrEnum


class NativeValueType(NameValueStrEnum):
    UUID = "UUID"
    DECIMAL = "DECIMAL"
    STRING = "STRING"
    BOOLEAN = "BOOLEAN"
    ZONED_TIME_OF_DAY = "ZONED_TIME_OF_DAY"
    LOCAL_TIME_OF_DAY = "LOCAL_TIME_OF_DAY"
    ZONED_TIMESTAMP = "ZONED_TIMESTAMP"
    LOCAL_DATE = "LOCAL_DATE"
    OBJECT = "OBJECT"
    LIST = "LIST"
    DICT = "DICT"

    def is_temporal(self) -> bool:
        return self in {
            NativeValueType.ZONED_TIME_OF_DAY,
            NativeValueType.LOCAL_TIME_OF_DAY,
            NativeValueType.ZONED_TIMESTAMP,
            NativeValueType.LOCAL_DATE,
        }

    def is_numeric(self) -> bool:
        return self in {
            NativeValueType.DECIMAL,
        }


class MatchOperatorName(BaseModel):
    model_config = ConfigDict(frozen=True)

    operator: MatchOperator
    display_name: str


def _match_operators_by_native_value_type(  # noqa: PLR0911, C901
    *,
    native_type: NativeValueType,
) -> tuple[MatchOperator, ...]:
    match native_type:
        case NativeValueType.UUID:
            return (
                MatchOperator.EQ,
                MatchOperator.NE,
                MatchOperator.IN,
                MatchOperator.NIN,
                MatchOperator.BLANK,
                MatchOperator.NBLANK,
            )
        case NativeValueType.DECIMAL:
            return (
                MatchOperator.EQ,
                MatchOperator.NE,
                MatchOperator.LT,
                MatchOperator.GT,
                MatchOperator.LTE,
                MatchOperator.GTE,
                MatchOperator.IN,
                MatchOperator.NIN,
                MatchOperator.BLANK,
                MatchOperator.NBLANK,
            )
        case NativeValueType.STRING:
            return (
                MatchOperator.EQ,
                MatchOperator.NE,
                MatchOperator.CONTAINS,
                MatchOperator.NCONTAINS,
                MatchOperator.STARTS_WITH,
                MatchOperator.ENDS_WITH,
                MatchOperator.BLANK,
                MatchOperator.NBLANK,
                MatchOperator.IN,
                MatchOperator.NIN,
            )
        case NativeValueType.BOOLEAN:
            return (
                MatchOperator.EQ,
                MatchOperator.NE,
                MatchOperator.BLANK,
                MatchOperator.NBLANK,
            )
        case NativeValueType.ZONED_TIME_OF_DAY:
            return (
                MatchOperator.EQ,
                MatchOperator.NE,
                MatchOperator.LT,
                MatchOperator.GT,
                MatchOperator.LTE,
                MatchOperator.GTE,
                MatchOperator.BLANK,
                MatchOperator.NBLANK,
            )
        case NativeValueType.LOCAL_TIME_OF_DAY:
            return (
                MatchOperator.EQ,
                MatchOperator.NE,
                MatchOperator.LT,
                MatchOperator.GT,
                MatchOperator.LTE,
                MatchOperator.GTE,
                MatchOperator.BLANK,
                MatchOperator.NBLANK,
            )
        case NativeValueType.ZONED_TIMESTAMP:
            return (
                MatchOperator.EQ,
                MatchOperator.NE,
                MatchOperator.LT,
                MatchOperator.GT,
                MatchOperator.LTE,
                MatchOperator.GTE,
                MatchOperator.BLANK,
                MatchOperator.NBLANK,
            )
        case NativeValueType.LOCAL_DATE:
            return (
                MatchOperator.EQ,
                MatchOperator.NE,
                MatchOperator.LT,
                MatchOperator.GT,
                MatchOperator.LTE,
                MatchOperator.GTE,
                MatchOperator.IN,
                MatchOperator.NIN,
                MatchOperator.BLANK,
                MatchOperator.NBLANK,
            )
        case NativeValueType.OBJECT:
            return (
                MatchOperator.BLANK,
                MatchOperator.NBLANK,
            )
        case NativeValueType.LIST:
            return (
                MatchOperator.BLANK,
                MatchOperator.NBLANK,
                MatchOperator.CONTAINS,
                MatchOperator.NCONTAINS,
            )
        case NativeValueType.DICT:
            return (
                MatchOperator.BLANK,
                MatchOperator.NBLANK,
                MatchOperator.CONTAINS,
                MatchOperator.NCONTAINS,
            )
        case _ as never:
            assert_never(never)


def _match_operator_native_value_display_name(  # noqa: C901, PLR0911, PLR0912
    *,
    match_operator: MatchOperator,
    native_value_type: NativeValueType,
) -> str:
    match match_operator:
        case MatchOperator.EQ:
            return "is"
        case MatchOperator.NE:
            return "is not"
        case MatchOperator.LT:
            return "before" if native_value_type.is_temporal() else "less than"
        case MatchOperator.GT:
            return "after" if native_value_type.is_temporal() else "greater than"
        case MatchOperator.LTE:
            return (
                "before or on"
                if native_value_type.is_temporal()
                else "less than or equals"
            )
        case MatchOperator.GTE:
            return (
                "after or on"
                if native_value_type.is_temporal()
                else "greater than or equals"
            )
        case MatchOperator.IN:
            return "is any of"
        case MatchOperator.NIN:
            return "is none of"
        case MatchOperator.CONTAINS:
            return "contains"
        case MatchOperator.NCONTAINS:
            return "not contains"
        case MatchOperator.STARTS_WITH:
            return "starts with"
        case MatchOperator.ENDS_WITH:
            return "ends with"
        case MatchOperator.BLANK:
            return "empty"
        case MatchOperator.NBLANK:
            return "not empty"
        case _ as never:
            assert_never(never)


match_operator_names_by_native_type: typing.Final[
    Mapping[NativeValueType, tuple[MatchOperatorName, ...]]
] = frozendict(
    {
        nt: tuple(
            MatchOperatorName(
                operator=op,
                display_name=_match_operator_native_value_display_name(
                    match_operator=op, native_value_type=NativeValueType(nt)
                ),
            )
            for op in _match_operators_by_native_value_type(
                native_type=NativeValueType(nt)
            )
        )
        for nt in NativeValueType
    }
)

sortable_native_field_types: typing.Final[frozenset[NativeValueType]] = frozenset(
    {
        NativeValueType.DECIMAL,
        NativeValueType.STRING,
        NativeValueType.BOOLEAN,
        NativeValueType.ZONED_TIME_OF_DAY,
        NativeValueType.LOCAL_TIME_OF_DAY,
        NativeValueType.ZONED_TIMESTAMP,
        NativeValueType.LOCAL_DATE,
    }
)

SelectListValueType = Literal[NativeValueType.STRING,]
