from collections.abc import Sequence
from enum import StrEnum, unique
from functools import cached_property
from typing import Annotated, Any, Literal, Self
from uuid import UUID

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    field_validator,
    model_validator,
)
from typing_extensions import TypeIs

from salestech_be.common.type.metadata.common import (
    BaseFieldIdentifier,
    BaseObjectIdentifier,
    CustomFieldIdentifier,
    CustomObjectIdentifier,
    FieldIdentifier,
    FieldKind,
    ObjectIdentifier,
    ObjectKind,
    RelationshipId,
    StandardFieldIdentifier,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    CustomFieldTypeProperty,
    FieldTypeProperty,
    StandardFieldTypeProperty,
)
from salestech_be.core.custom_object.type.object_status import (
    CustomObjectStatus,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.db.models.organization_external_sync import (  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CrmProvider,
    SyncMode,
)
from salestech_be.integrations.field_origin.field_origin_types import (
    HubspotObjEnum,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class SkipDescriptor:
    """
    A marker object to annotate on field to indicate not to generate a descriptor
    """

    def __init__(self, reason: str = "descriptor is not needed") -> None:
        self.reason = reason

    def __str__(self) -> str:
        return f"SkipDescriptor({self.reason})"

    def __repr__(self) -> str:
        return str(self)


class Relationship(BaseModel):
    model_config = ConfigDict(frozen=True)

    class Direction(StrEnum):
        """
        The direction of the relationship.
        - OUTBOUND: The object has a field that references to another object.
        - INBOUND: The object is referenced by another object on one of its fields.
        """

        OUTBOUND = "OUTBOUND"
        INBOUND = "INBOUND"

    class Cardinality(StrEnum):
        """
        The cardinality of the relationship.
        - ONE: The described object is on the "one" side of a
            "one-to-many" or "one-to-one" relationship.
        - MANY: The described object is on the "many" side of a
            "one-to-many" or "many-to-many" relationship.
        """

        ONE = "ONE"
        MANY = "MANY"

    class RelationType(StrEnum):
        """
        The type of the relationship.
        - MASTER_DETAIL: (SFDC) https://help.salesforce.com/s/articleView?id=sf.overview_of_custom_object_relationships.htm
        - LOOKUP: (SFDC) https://help.salesforce.com/s/articleView?id=sf.overview_of_custom_object_relationships.htm
        """

        # todo(xw): write our own version of this description.

        MASTER_DETAIL = "MASTER_DETAIL"
        LOOKUP = "LOOKUP"

    direction: Annotated[
        Direction, Field(description="The direction of the relationship")
    ]
    relation_type: Annotated[
        RelationType, Field(description="Type of this relationship")
    ]
    id: RelationshipId
    relationship_name: Annotated[
        str,
        Field(
            description="The name of the relationship, auto-generated or "
            "specified by the creator"
        ),
    ]
    self_object_identifier: ObjectIdentifier
    related_object_identifier: ObjectIdentifier
    self_cardinality: Cardinality
    related_object_cardinality: Cardinality
    ordered_self_field_identifiers: tuple[FieldIdentifier, ...]
    ordered_related_field_identifiers: tuple[FieldIdentifier, ...]

    @model_validator(mode="after")
    def validate_relationship(self) -> Self:
        if len(self.ordered_self_field_identifiers) != len(
            self.ordered_related_field_identifiers
        ):
            raise ValueError(
                "The number of self and related field identifiers must be the same"
                f"but found self: {self.ordered_self_field_identifiers}"
                f"and related: {self.ordered_related_field_identifiers}"
            )
        return self


class InboundRelationship(Relationship):
    direction: Literal[Relationship.Direction.INBOUND] = Relationship.Direction.INBOUND
    is_related_object_junction: Annotated[
        bool,
        Field(
            description="Whether the related object is a junction object. "
            "This should be true if the related object is a junction object "
            "and will have a limited set of allowed outbound relationships."
        ),
    ] = False


class OutboundRelationship(Relationship):
    direction: Literal[Relationship.Direction.OUTBOUND] = (
        Relationship.Direction.OUTBOUND
    )


@unique
class ArrayElement(StrEnum):
    ANY = "."
    ALL = "*"


"""
Represent the path of the field from an owning object. Assuming we have an object with following structure:
    {
        "field_a": 123,
        "field_b": [
            "a", "b", "c"
        ],
        "field_c": {
            "c__field_a": "123e4567-e89b-12d3-a456-************",
            "c__field_b": "John Doe",
            "c__field_c": [
                {
                    "c__c__field_a": "123e4567-e89b-12d3-a456-************",
                },
                {
                    "c__c__field_a": "10dk4567-e89b-12d3-a456-************",
                },
            ]
        },
        "custom_object_data": {
            "custom_field_data_by_id": {
                "123e4567-e89b-12d3-a456-************": {
                    "custom_field_id": "123e4567-e89b-12d3-a456-************",
                    "field_display_name": "Some custom field",
                    "field_type": "TEXT",
                    "field_value_detail": {
                        "field_type": "TEXT",
                        "text_value": "Some custom field value"
                    }
                },
                "987e6543-e21b-12d3-a456-************": {
                    "custom_field_id": "987e6543-e21b-12d3-a456-************",
                    "field_display_name": "Some custom field",
                    "field_type": "NUMBER",
                    "field_value_detail": {
                        "field_type": "NUMBER",
                        "number_value": 42
                    }
                }
            }
        }
    }

Standard Fields:
1. Reference on a top level singular field:
    ["field_a"] --> 123
2. Reference on a ANY value of an array field:
    ["field_b"] --> any of ["a", "b", "c"]
3. Reference on a nested object field:
    ["field_c", "c__field_a"] --> "123e4567-e89b-12d3-a456-************"
4. Reference on a nested object's ANY value of an array nested object field:
    ["field_c", "c__field_c", "c__c__field_a"] --> any of ["123e4567-e89b-12d3-a456-************", "10dk4567-e89b-12d3-a456-************"]

Custom Object Fields (or Custom Fields):

1. Reference on a top level singular field:
    ["123e4567-e89b-12d3-a456-************"] -->
    {
        "custom_field_id": "123e4567-e89b-12d3-a456-************",
        "field_display_name": "Some custom field",
        "field_type": "TEXT",
        "field_value_detail": {
            "field_type": "TEXT",
            "text_value": "Some custom field value"
        }
    }
"""

type FieldPathType = tuple[UUID | str, ...]

FieldPath = Annotated[
    FieldPathType,
    Field(
        description="Represent the path of the field from an owning object",
        default_factory=tuple,
    ),
]


class QualifiedField(BaseModel):
    model_config = ConfigDict(frozen=True)
    path: FieldPath
    fetch_relationship_ids: tuple[RelationshipId, ...] = Field(
        default_factory=tuple,
        description="The relationship ids to fetch",
    )

    @field_validator("path", mode="before")
    @classmethod
    def validate_path(cls, v: list[UUID | str]) -> FieldPath:
        translated_path: list[UUID | str] = []
        for item in v:
            if isinstance(item, UUID):
                translated_path.append(item)
            elif isinstance(item, str):
                try:
                    translated_path.append(UUID(item))
                except ValueError:
                    translated_path.append(item)
            else:
                raise ValueError(f"Invalid field path item, not UUID or str: {item}")
        return tuple(translated_path)

    @field_validator("fetch_relationship_ids", mode="before")
    @classmethod
    def validate_fetch_relationship_ids(
        cls, v: list[RelationshipId]
    ) -> tuple[RelationshipId, ...]:
        # ensure list order is always consistent, for set / hashing operations.
        sorted_v = sorted(set(v))
        return tuple(sorted_v)

    def has_custom_field(self) -> bool:
        return any(isinstance(fp, UUID) for fp in self.path)


class FieldReference(BaseModel):
    """
    A field reference that is used to identify a field on a related object.

    The referenced the field on the related object by either providing the field path
    or by providing the yet another field reference to its downstream related object.

    Examples:
    - Referencing a field on the related object:
        {
            "relationship_id": "123e4567-e89b-12d3-a456-************",
            "field_path": ["field_a"],
        }
    - Referencing a field on the downstream related object:
        {
            "relationship_id": "123e4567-e89b-12d3-a456-************",
            "field_reference": {
                "relationship_id": "123e4567-e89b-12d3-a456-************",
                "field_path": ["field_b", ".", "field_c", ".", "field_d"],
            }
        }
    - Referencing a field on the 2nd layer downstream related object:
        {
            "relationship_id": "123e4567-e89b-12d3-a456-************",
            "field_reference": {
                "relationship_id": "123e4567-e89b-12d3-a456-426614174111",
                "field_path": ["field_a"],
            }
        }
    """

    model_config = ConfigDict(frozen=True)
    relationship_id: RelationshipId
    field: "QualifiedField | FieldReference"
    is_relationship_display_field: Annotated[
        bool,
        Field(
            description="Whether the field reference is used as a representation "
            "display of a relationship."
        ),
    ] = False

    @cached_property
    def relationship_chain(self) -> tuple[RelationshipId, ...]:
        chain: list[RelationshipId] = []
        field_ref: FieldReference | None = self
        while field_ref:
            chain.append(field_ref.relationship_id)
            field_ref = (
                field_ref.field if isinstance(field_ref.field, FieldReference) else None
            )
        return tuple(chain)

    @cached_property
    def leaf_field_path(self) -> FieldPath:
        field_ref: FieldReference | None = self
        while field_ref:
            if isinstance(field_ref.field, QualifiedField):
                return field_ref.field.path
            field_ref = field_ref.field
        return ()

    def has_custom_field(self) -> bool:
        return self.field.has_custom_field()

    @classmethod
    def of_visited_relationships(
        cls,
        visited_relationships: tuple[RelationshipId, ...],
        leaf_path: tuple[str | UUID, ...],
    ) -> Self:
        """
        Reconstruct a field reference from the visited relationships and the leaf path.
        """
        if not visited_relationships:
            raise ValueError("Visited relationships cannot be empty")
        leaf_ref = cls(
            relationship_id=visited_relationships[-1],
            field=QualifiedField(path=leaf_path),
        )
        for relationship_id in visited_relationships[-2::-1]:
            leaf_ref = cls(
                relationship_id=relationship_id,
                field=leaf_ref,
            )
        return leaf_ref


FieldReference.model_rebuild()


class RelatedFieldIdentifier(BaseModel):
    """
    A field identifier that is used to identify a field on a related object.
    """

    model_config = ConfigDict(frozen=True)

    relationship_id: RelationshipId
    related_field_identifier: FieldIdentifier


class BaseFieldDescriptor(BaseModel):
    model_config = ConfigDict(frozen=True)
    field_kind: FieldKind
    field_identifier: BaseFieldIdentifier
    field_type_property: FieldTypeProperty


class StandardFieldDescriptor(BaseFieldDescriptor):
    field_kind: Literal[FieldKind.STANDARD] = FieldKind.STANDARD
    field_identifier: StandardFieldIdentifier
    field_type_property: StandardFieldTypeProperty


class CustomFieldDescriptor(BaseFieldDescriptor):
    field_kind: Literal[FieldKind.CUSTOM] = FieldKind.CUSTOM
    field_identifier: CustomFieldIdentifier
    field_type_property: CustomFieldTypeProperty
    created_at: ZoneRequiredDateTime
    updated_at: ZoneRequiredDateTime | None = None


FieldDescriptor = Annotated[
    StandardFieldDescriptor | CustomFieldDescriptor,
    Field(description="The properties of the field type", discriminator="field_kind"),
]


def is_field_descriptor(val: Any) -> TypeIs[FieldDescriptor]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    return isinstance(val, StandardFieldDescriptor | CustomFieldDescriptor)


class BaseObjectDescriptor(BaseModel):
    model_config = ConfigDict(frozen=True, validate_assignment=True)
    object_identifier: BaseObjectIdentifier
    object_display_name: str
    is_junction_object: Annotated[
        bool,
        Field(
            description="""
                Whether the object is a junction object.

                A junction object is an object that is used to connect multiple objects together.
                It is a special object that is used to represent the relationship between 2
                objects.

                When an object is a junction object, it can only have 2 outbound relationships with 0 inbound relationships.
                """
        ),
    ] = False
    fields: Sequence[FieldDescriptor]
    inbound_relationships: Annotated[
        Sequence[InboundRelationship],
        Field(
            description="All inbound relationship, where the object is referenced "
            "by others"
        ),
    ]
    outbound_relationships: Annotated[
        Sequence[OutboundRelationship],
        Field(
            description="All outbound relationship, where this object references "
            "other objects"
        ),
    ]

    def field_descriptor(
        self, field_identifier: FieldIdentifier
    ) -> FieldDescriptor | None:
        for field_descriptor in self.fields:
            if field_descriptor.field_identifier == field_identifier:
                return field_descriptor
        return None

    def relationship(
        self, *, relationship_id: RelationshipId
    ) -> InboundRelationship | OutboundRelationship | None:
        for inbound_relationship in self.inbound_relationships:
            if inbound_relationship.id == relationship_id:
                return inbound_relationship
        for outbound_relationship in self.outbound_relationships:
            if outbound_relationship.id == relationship_id:
                return outbound_relationship
        return None

    @model_validator(mode="after")
    def validate_junction_object(self) -> Self:
        if self.is_junction_object:
            if len(self.outbound_relationships) != 2:  # noqa: PLR2004
                raise ValueError("Junction object must have 2 outbound relationships")
            if len(self.inbound_relationships) != 0:
                raise ValueError("Junction object must have 0 inbound relationships")
        return self


class ObjectExternalProvider(BaseModel):
    """
    External provider description for the object. This is analogous to the
    finer granularity of FieldExternalProvider.
    """

    provider: CrmProvider
    provider_obj: HubspotObjEnum | None
    sync_mode: SyncMode | None = None


class ObjectTypeProperty(BaseModel):
    """Properties that describe the object type"""

    external_provider: ObjectExternalProvider | None = None
    is_ui_creatable: bool = True
    is_ui_editable: bool = True
    is_ui_archivable: bool = True


class StandardObjectDescriptor(BaseObjectDescriptor):
    object_kind: Literal[ObjectKind.STANDARD] = ObjectKind.STANDARD
    object_identifier: StandardObjectIdentifier
    fields: Sequence[FieldDescriptor]
    object_type_property: ObjectTypeProperty


class CustomObjectDescriptor(BaseObjectDescriptor):
    object_kind: Literal[ObjectKind.CUSTOM] = ObjectKind.CUSTOM
    object_identifier: CustomObjectIdentifier
    fields: Sequence[FieldDescriptor]
    icon: str | None = None
    visible_on_navigation: bool = True
    display_name: str = ""
    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    updated_at: ZoneRequiredDateTime | None = None
    updated_by_user_id: UUID | None = None
    status: CustomObjectStatus = CustomObjectStatus.ACTIVE
    deleted_at: ZoneRequiredDateTime | None = None
    deleted_by_user_id: UUID | None = None


ObjectDescriptor = Annotated[
    StandardObjectDescriptor | CustomObjectDescriptor,
    Field(description="The metadata of the object", discriminator="object_kind"),
]


def is_object_descriptor(val: Any) -> TypeIs[ObjectDescriptor]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    return isinstance(val, StandardObjectDescriptor | CustomFieldDescriptor)


class OrganizationSchemaDescriptor(BaseModel):
    organization_id: UUID
    objects: Sequence[ObjectDescriptor]
