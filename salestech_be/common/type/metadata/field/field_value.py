import typing
from abc import ABC, abstractmethod
from datetime import date
from decimal import Decimal
from typing import (
    Annotated,
    Any,
    Generic,
    Literal,
    Protocol,
    Self,
    TypeVar,
    assert_never,
    runtime_checkable,
)
from uuid import UUID

from litellm import ConfigDict
from pydantic import AnyUrl, BaseModel, Field, UrlConstraints
from typing_extensions import TypeIs

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdSelectListIdentifier,
)
from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.common.type.metadata.field.field_indexable_config import (
    IndexableConfigMixin,
)
from salestech_be.common.type.metadata.field.field_type import (
    CustomFieldType,
    FieldType,
    url_constraint,
)
from salestech_be.db.models.select_list import (
    SelectListValueStatus,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.pydantic_types.str import PhoneNumber
from salestech_be.util.pydantic_types.time import (
    LocalTime,
    ZoneRequiredDateTime,
    ZoneRequiredTime,
)

AnyValueT = TypeVar("AnyValueT")


@runtime_checkable
class TextIndexable(Protocol):
    text_value: str | None
    unique_text_value: str | None


@runtime_checkable
class NumberIndexable(Protocol):
    number_value: float | Decimal | None
    unique_number_value: float | Decimal | None


@runtime_checkable
class UUIDIndexable(Protocol):
    uuid_value: UUID | None
    unique_uuid_value: UUID | None


@runtime_checkable
class ZonedTimeStampIndexable(Protocol):
    timestamp_value: ZoneRequiredDateTime | None


@runtime_checkable
class ZonedTimeIndexable(Protocol):
    zoned_time_value: ZoneRequiredTime | None


@runtime_checkable
class LocalTimeIndexable(Protocol):
    local_time_value: LocalTime | None


@runtime_checkable
class LocalDateIndexable(Protocol):
    local_date_value: date | None


@runtime_checkable
class BooleanIndexable(Protocol):
    boolean_value: bool | None


IndexableT = TypeVar(
    "IndexableT",
    TextIndexable,
    NumberIndexable,
    ZonedTimeStampIndexable,
    ZonedTimeIndexable,
    LocalTimeIndexable,
    LocalDateIndexable,
    BooleanIndexable,
    UUIDIndexable,
)


class BaseFieldValue(BaseModel, Generic[AnyValueT], ABC):
    field_type: FieldType

    @abstractmethod
    def is_present(self) -> bool:
        raise NotImplementedError

    @abstractmethod
    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        raise NotImplementedError


class BaseSingularFieldValue(BaseFieldValue[AnyValueT], Generic[AnyValueT], ABC):
    @classmethod
    @abstractmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        raise NotImplementedError


class BaseCollectionFieldValue(BaseFieldValue[object], ABC):
    @classmethod
    @abstractmethod
    def from_generic_value(cls, value: Any, value_field_type: FieldType) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        raise NotImplementedError


class NonIndexableFieldValue(BaseSingularFieldValue[Any], ABC):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    pass


class IndexableFieldValue(BaseSingularFieldValue[IndexableT], Generic[IndexableT], ABC):
    @abstractmethod
    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, indexable: IndexableT, indexable_property: IndexableConfigMixin[Any]
    ) -> None:
        raise NotImplementedError


class UUIDFieldValue(IndexableFieldValue[UUIDIndexable]):
    field_type: Literal[FieldType.UUID] = FieldType.UUID
    uuid: UUID | None

    def is_present(self) -> bool:
        return self.uuid is not None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.uuid

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(uuid=value)

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, indexable: UUIDIndexable, indexable_property: IndexableConfigMixin[Any]
    ) -> None:
        if not indexable_property.is_indexed:
            return
        if indexable_property.is_unique:
            indexable.unique_uuid_value = self.uuid
            indexable.uuid_value = None
        else:
            indexable.uuid_value = self.uuid
            indexable.unique_uuid_value = None


class NumericFieldValue(IndexableFieldValue[NumberIndexable]):
    field_type: Literal[FieldType.NUMERIC] = FieldType.NUMERIC
    number: Decimal | None

    def is_present(self) -> bool:
        return self.number is not None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.number

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(number=value)

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, indexable: NumberIndexable, indexable_property: IndexableConfigMixin[Any]
    ) -> None:
        if not indexable_property.is_indexed:
            return
        if indexable_property.is_unique:
            indexable.unique_number_value = self.number
            indexable.number_value = None
        else:
            indexable.number_value = self.number
            indexable.unique_number_value = None


class CurrencyFieldValue(IndexableFieldValue[NumberIndexable]):
    field_type: Literal[FieldType.CURRENCY] = FieldType.CURRENCY
    currency: Decimal | None

    def is_present(self) -> bool:
        return self.currency is not None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.currency

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(currency=value)

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, indexable: NumberIndexable, indexable_property: IndexableConfigMixin[Any]
    ) -> None:
        if not indexable_property.is_indexed:
            return
        indexable.number_value = self.currency
        indexable.unique_number_value = None


class TextFieldValue(IndexableFieldValue[TextIndexable]):
    field_type: Literal[FieldType.TEXT] = FieldType.TEXT
    text: str | None

    def is_present(self) -> bool:
        return self.text is not None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.text

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(text=value)

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, indexable: TextIndexable, indexable_property: IndexableConfigMixin[Any]
    ) -> None:
        if not indexable_property.is_indexed:
            return
        value_to_use = (
            self.text.lower()
            if self.text and (not indexable_property.is_index_case_sensitive)
            else self.text
        )
        if indexable_property.is_unique:
            indexable.unique_text_value = value_to_use
            indexable.text_value = None
        else:
            indexable.text_value = value_to_use
            indexable.unique_text_value = None


class TextAreaFieldValue(NonIndexableFieldValue):
    field_type: Literal[FieldType.TEXT_AREA] = FieldType.TEXT_AREA
    text_area: str | None

    def is_present(self) -> bool:
        return self.text_area is not None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.text_area

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(text_area=value)


class LongTextAreaFieldValue(NonIndexableFieldValue):
    field_type: Literal[FieldType.LONG_TEXT_AREA] = FieldType.LONG_TEXT_AREA
    long_text_area: str | None

    def is_present(self) -> bool:
        return self.long_text_area is not None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.long_text_area

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(long_text_area=value)


class RichTextAreaFieldValue(NonIndexableFieldValue):
    field_type: Literal[FieldType.RICH_TEXT_AREA] = FieldType.RICH_TEXT_AREA
    rich_text_area: str | None

    def is_present(self) -> bool:
        return self.rich_text_area is not None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.rich_text_area

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(rich_text_area=value)


class TimestampFieldValue(IndexableFieldValue[ZonedTimeStampIndexable]):
    field_type: Literal[FieldType.TIMESTAMP] = FieldType.TIMESTAMP
    timestamp: ZoneRequiredDateTime | None

    def is_present(self) -> bool:
        return self.timestamp is not None

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        indexable: ZonedTimeStampIndexable,
        indexable_property: IndexableConfigMixin[Any],
    ) -> None:
        if not indexable_property.is_indexed:
            return
        indexable.timestamp_value = self.timestamp

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.timestamp

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(timestamp=value)


class LocalTimeOfDayFieldValue(IndexableFieldValue[LocalTimeIndexable]):
    field_type: Literal[FieldType.LOCAL_TIME_OF_DAY] = FieldType.LOCAL_TIME_OF_DAY
    local_time_of_day: LocalTime | None

    def is_present(self) -> bool:
        return self.local_time_of_day is not None

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        indexable: LocalTimeIndexable,
        indexable_property: IndexableConfigMixin[Any],
    ) -> None:
        if not indexable_property.is_indexed:
            return
        indexable.local_time_value = self.local_time_of_day

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.local_time_of_day

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(local_time_of_day=value)


class TimeOfDayFieldValue(IndexableFieldValue[ZonedTimeIndexable]):
    field_type: Literal[FieldType.TIME_OF_DAY] = FieldType.TIME_OF_DAY
    time_of_day: ZoneRequiredTime | None

    def is_present(self) -> bool:
        return self.time_of_day is not None

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        indexable: ZonedTimeIndexable,
        indexable_property: IndexableConfigMixin[Any],
    ) -> None:
        if not indexable_property.is_indexed:
            return
        indexable.zoned_time_value = self.time_of_day

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.time_of_day

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(time_of_day=value)


class LocalDateFieldValue(IndexableFieldValue[LocalDateIndexable]):
    field_type: Literal[FieldType.LOCAL_DATE] = FieldType.LOCAL_DATE
    local_date: date | None

    def is_present(self) -> bool:
        return self.local_date is not None

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        indexable: LocalDateIndexable,
        indexable_property: IndexableConfigMixin[Any],
    ) -> None:
        if not indexable_property.is_indexed:
            return
        indexable.local_date_value = self.local_date

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.local_date

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(local_date=value)


class BooleanCheckboxFieldValue(IndexableFieldValue[BooleanIndexable]):
    field_type: Literal[FieldType.BOOLEAN_CHECKBOX] = FieldType.BOOLEAN_CHECKBOX
    checked: bool

    def is_present(self) -> bool:
        return True

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, indexable: BooleanIndexable, indexable_property: IndexableConfigMixin[Any]
    ) -> None:
        if not indexable_property.is_indexed:
            return
        indexable.boolean_value = self.checked

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.checked

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(checked=value)


class EmailFieldValue(IndexableFieldValue[TextIndexable]):
    field_type: Literal[FieldType.EMAIL] = FieldType.EMAIL
    email: EmailStrLower | None

    def is_present(self) -> bool:
        return self.email is not None

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, indexable: TextIndexable, indexable_property: IndexableConfigMixin[Any]
    ) -> None:
        if not indexable_property.is_indexed:
            return
        indexable.text_value = self.email.lower() if self.email else None
        indexable.unique_text_value = None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.email

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(email=value)


class PercentFieldValue(IndexableFieldValue[NumberIndexable]):
    field_type: Literal[FieldType.PERCENT] = FieldType.PERCENT
    percent: Decimal | None

    def is_present(self) -> bool:
        return self.percent is not None

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, indexable: NumberIndexable, indexable_property: IndexableConfigMixin[Any]
    ) -> None:
        if not indexable_property.is_indexed:
            return
        indexable.number_value = self.percent
        indexable.unique_number_value = None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.percent

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(percent=value)


class PhoneNumberFieldValue(IndexableFieldValue[TextIndexable]):
    field_type: Literal[FieldType.PHONE_NUMBER] = FieldType.PHONE_NUMBER
    phone_number: PhoneNumber | None

    def is_present(self) -> bool:
        return self.phone_number is not None

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, indexable: TextIndexable, indexable_property: IndexableConfigMixin[Any]
    ) -> None:
        if not indexable_property.is_indexed:
            return
        indexable.text_value = str(self.phone_number)
        indexable.unique_text_value = None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.phone_number

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(phone_number=value)


class SingleSelectFieldValue(IndexableFieldValue[UUIDIndexable]):
    field_type: Literal[FieldType.SINGLE_SELECT] = FieldType.SINGLE_SELECT
    value_id: UUID | None

    def is_present(self) -> bool:
        return self.value_id is not None

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, indexable: UUIDIndexable, indexable_property: IndexableConfigMixin[Any]
    ) -> None:
        if not indexable_property.is_indexed:
            return
        indexable.uuid_value = self.value_id
        indexable.unique_uuid_value = None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.value_id

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(value_id=value)


class PresentableSelectListValue(BaseModel):
    model_config = ConfigDict(frozen=True)

    id: UUID
    select_list_id: UUID
    display_value: str
    description: str | None
    rank: Decimal
    is_default: bool
    status: SelectListValueStatus
    application_code_name: StdSelectListIdentifier | None


class PresentableSingleSelectFieldValue(SingleSelectFieldValue):
    value: PresentableSelectListValue

    def __lt__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(other, PresentableSingleSelectFieldValue):
            raise ValueError(
                f"Cannot compare PresentableSingleSelectFieldValue with {type(other)}"
            )
        if self.value.select_list_id != other.value.select_list_id:
            raise ValueError(
                f"Cannot compare PresentableSingleSelectFieldValue with different select_list_id: \
                    {self.value.select_list_id} and {other.value.select_list_id}"
            )
        return self.value.rank < other.value.rank

    def __le__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(other, PresentableSingleSelectFieldValue):
            raise ValueError(
                f"Cannot compare PresentableSingleSelectFieldValue with {type(other)}"
            )
        if self.value.select_list_id != other.value.select_list_id:
            raise ValueError(
                f"Cannot compare PresentableSingleSelectFieldValue with different select_list_id: \
                    {self.value.select_list_id} and {other.value.select_list_id}"
            )
        return self.value.rank <= other.value.rank

    def __gt__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(other, PresentableSingleSelectFieldValue):
            raise ValueError(
                f"Cannot compare PresentableSingleSelectFieldValue with {type(other)}"
            )
        if self.value.select_list_id != other.value.select_list_id:
            raise ValueError(
                f"Cannot compare PresentableSingleSelectFieldValue with different select_list_id: \
                    {self.value.select_list_id} and {other.value.select_list_id}"
            )
        return self.value.rank > other.value.rank

    def __ge__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(other, PresentableSingleSelectFieldValue):
            raise ValueError(
                f"Cannot compare PresentableSingleSelectFieldValue with {type(other)}"
            )
        if self.value.select_list_id != other.value.select_list_id:
            raise ValueError(
                f"Cannot compare PresentableSingleSelectFieldValue with different select_list_id: \
                    {self.value.select_list_id} and {other.value.select_list_id}"
            )
        return self.value.rank >= other.value.rank

    def __eq__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(other, PresentableSingleSelectFieldValue):
            raise ValueError(
                f"Cannot compare PresentableSingleSelectFieldValue with {type(other)}"
            )
        if self.value.select_list_id != other.value.select_list_id:
            raise ValueError(
                f"Cannot compare PresentableSingleSelectFieldValue with different select_list_id: \
                    {self.value.select_list_id} and {other.value.select_list_id}"
            )
        return self.value_id == other.value_id and self.value.rank == other.value.rank

    def __ne__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(other, PresentableSingleSelectFieldValue):
            raise ValueError(
                f"Cannot compare PresentableSingleSelectFieldValue with {type(other)}"
            )

        return not self.__eq__(other)


class MultiSelectFieldValue(NonIndexableFieldValue):
    field_type: Literal[FieldType.MULTI_SELECT] = FieldType.MULTI_SELECT
    value_ids: set[UUID]

    def is_present(self) -> bool:
        return len(self.value_ids) > 0

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.value_ids

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(value_ids=set(value))


class PresentableMultiSelectFieldValue(MultiSelectFieldValue):
    values: list[PresentableSelectListValue]


class UrlFieldValue(IndexableFieldValue[TextIndexable]):
    field_type: Literal[FieldType.URL] = FieldType.URL
    value: (
        Annotated[AnyUrl, UrlConstraints(max_length=url_constraint.max_url_length)]
        | None
    )

    def is_present(self) -> bool:
        return self.value is not None

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, indexable: TextIndexable, indexable_property: IndexableConfigMixin[Any]
    ) -> None:
        if not indexable_property.is_indexed:
            return
        indexable.text_value = str(self.value)
        indexable.unique_text_value = None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.value

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(value=value)


class GeoLocationFieldValue(NonIndexableFieldValue):
    field_type: Literal[FieldType.GEO_LOCATION] = FieldType.GEO_LOCATION
    latitude: Annotated[float | None, Field(ge=-90, le=90)] = None
    longitude: Annotated[float | None, Field(ge=-180, le=180)] = None

    def model_post_init(self, __context: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().model_post_init(__context)
        if (self.latitude is None) != (self.longitude is None):
            raise ValueError(
                "Latitude and longitude must both be present or both be absent"
                f"But got latitude={self.latitude} and longitude={self.longitude}"
            )

    def is_present(self) -> bool:
        return self.latitude is not None and self.longitude is not None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return {
            "latitude": self.latitude,
            "longitude": self.longitude,
        }

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(latitude=value.get("latitude"), longitude=value.get("longitude"))


class NestedObjectFieldValue(NonIndexableFieldValue):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    field_type: Literal[FieldType.NESTED_OBJECT] = FieldType.NESTED_OBJECT
    nested_object: dict[str, Any]  # type: ignore[explicit-any] # TODO: fix-any-annotation

    def is_present(self) -> bool:
        return self.nested_object is not None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.nested_object

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(nested_object=value)


class DefaultEnumFieldValue(IndexableFieldValue[TextIndexable]):
    field_type: Literal[FieldType.DEFAULT_ENUM] = FieldType.DEFAULT_ENUM
    enum_value: str | None

    def is_present(self) -> bool:
        return self.enum_value is not None

    def set_indexable(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, indexable: TextIndexable, indexable_property: IndexableConfigMixin[Any]
    ) -> None:
        if not indexable_property.is_indexed:
            return
        indexable.text_value = self.enum_value
        indexable.unique_text_value = None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self.enum_value

    @classmethod
    def from_generic_value(cls, value: Any) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls(enum_value=value)


SingularFieldValue = Annotated[
    UUIDFieldValue
    | NumericFieldValue
    | CurrencyFieldValue
    | TextFieldValue
    | TextAreaFieldValue
    | LongTextAreaFieldValue
    | RichTextAreaFieldValue
    | TimestampFieldValue
    | LocalTimeOfDayFieldValue
    | TimeOfDayFieldValue
    | LocalDateFieldValue
    | BooleanCheckboxFieldValue
    | EmailFieldValue
    | PercentFieldValue
    | PhoneNumberFieldValue
    | SingleSelectFieldValue
    | MultiSelectFieldValue
    | UrlFieldValue
    | GeoLocationFieldValue
    | NestedObjectFieldValue
    | DefaultEnumFieldValue,
    Field(discriminator="field_type"),
]


class ListFieldValue(BaseCollectionFieldValue):
    field_type: Literal[FieldType.LIST] = FieldType.LIST
    values: list[SingularFieldValue] | None = None

    def is_present(self) -> bool:
        return self.values is not None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return (
            [value.to_generic_value() for value in self.values] if self.values else None
        )

    @classmethod
    def from_generic_value(cls, value: Any, value_field_type: FieldType) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(value, list | tuple):
            return cls(values=[])
        self_value_type = field_value_type_from_field_type(value_field_type)
        return cls(values=[self_value_type.from_generic_value(v) for v in value])


class DictFieldValue(BaseCollectionFieldValue):
    field_type: Literal[FieldType.DICT] = FieldType.DICT
    values: dict[str, SingularFieldValue] | None = None

    def is_present(self) -> bool:
        return self.values is not None

    def to_generic_value(self) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return (
            {key: value.to_generic_value() for key, value in self.values.items()}
            if self.values
            else None
        )

    @classmethod
    def from_generic_value(cls, value: Any, value_field_type: FieldType) -> Self:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(value, dict):
            return cls(values={})
        self_value_type = field_value_type_from_field_type(value_field_type)
        return cls(
            values={
                key: self_value_type.from_generic_value(v) for key, v in value.items()
            }
        )


FieldValue = Annotated[
    UUIDFieldValue
    | NumericFieldValue
    | CurrencyFieldValue
    | TextFieldValue
    | TextAreaFieldValue
    | LongTextAreaFieldValue
    | RichTextAreaFieldValue
    | TimestampFieldValue
    | LocalTimeOfDayFieldValue
    | TimeOfDayFieldValue
    | LocalDateFieldValue
    | BooleanCheckboxFieldValue
    | EmailFieldValue
    | PercentFieldValue
    | PhoneNumberFieldValue
    | SingleSelectFieldValue
    | MultiSelectFieldValue
    | UrlFieldValue
    | GeoLocationFieldValue
    | NestedObjectFieldValue
    | DefaultEnumFieldValue
    | ListFieldValue
    | DictFieldValue,
    Field(discriminator="field_type"),
]

PresentableFieldValue = Annotated[
    UUIDFieldValue
    | NumericFieldValue
    | CurrencyFieldValue
    | TextFieldValue
    | TextAreaFieldValue
    | LongTextAreaFieldValue
    | RichTextAreaFieldValue
    | TimestampFieldValue
    | LocalTimeOfDayFieldValue
    | TimeOfDayFieldValue
    | LocalDateFieldValue
    | BooleanCheckboxFieldValue
    | EmailFieldValue
    | PercentFieldValue
    | PhoneNumberFieldValue
    | PresentableSingleSelectFieldValue
    | PresentableMultiSelectFieldValue
    | UrlFieldValue
    | GeoLocationFieldValue
    | NestedObjectFieldValue
    | DefaultEnumFieldValue
    | ListFieldValue
    | DictFieldValue,
    Field(discriminator="field_type"),
]


_field_value_types: tuple[type, ...] = tuple(
    arg
    for arg in typing.get_args(typing.get_args(FieldValue)[0])
    if type(arg) is type(BaseFieldValue) and issubclass(arg, BaseFieldValue)
)


def is_field_value(value: Any) -> TypeIs[FieldValue]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    return isinstance(value, _field_value_types)


def field_value_type_from_field_type(field_type: FieldType) -> type[SingularFieldValue]:  # noqa: C901, PLR0911, PLR0912
    match field_type:
        case FieldType.UUID:
            return UUIDFieldValue
        case FieldType.NUMERIC:
            return NumericFieldValue
        case FieldType.CURRENCY:
            return CurrencyFieldValue
        case FieldType.TEXT:
            return TextFieldValue
        case FieldType.TEXT_AREA:
            return TextAreaFieldValue
        case FieldType.LONG_TEXT_AREA:
            return LongTextAreaFieldValue
        case FieldType.RICH_TEXT_AREA:
            return RichTextAreaFieldValue
        case FieldType.TIMESTAMP:
            return TimestampFieldValue
        case FieldType.LOCAL_TIME_OF_DAY:
            return LocalTimeOfDayFieldValue
        case FieldType.TIME_OF_DAY:
            return TimeOfDayFieldValue
        case FieldType.LOCAL_DATE:
            return LocalDateFieldValue
        case FieldType.BOOLEAN_CHECKBOX:
            return BooleanCheckboxFieldValue
        case FieldType.EMAIL:
            return EmailFieldValue
        case FieldType.PERCENT:
            return PercentFieldValue
        case FieldType.PHONE_NUMBER:
            return PhoneNumberFieldValue
        case FieldType.SINGLE_SELECT:
            return SingleSelectFieldValue
        case FieldType.MULTI_SELECT:
            return MultiSelectFieldValue
        case FieldType.URL:
            return UrlFieldValue
        case FieldType.GEO_LOCATION:
            return GeoLocationFieldValue
        case FieldType.NESTED_OBJECT:
            return NestedObjectFieldValue
        case FieldType.DEFAULT_ENUM:
            return DefaultEnumFieldValue
        case FieldType.LIST | FieldType.DICT:
            raise ValueError(f"{field_type} is not singular field types")
        case _ as unreachable:
            assert_never(unreachable)


FieldValueOrAny = Annotated[FieldValue | Any, Field(union_mode="left_to_right")]  # type: ignore[explicit-any] # TODO: fix-any-annotation


def custom_field_value_from_generic_value(  # type: ignore[explicit-any] # TODO: fix-any-annotation # noqa: C901, PLR0911, PLR0912
    field_type: CustomFieldType, value: Any
) -> FieldValue:
    match field_type:
        case FieldType.UUID:
            return UUIDFieldValue.from_generic_value(value)
        case FieldType.NUMERIC:
            return NumericFieldValue.from_generic_value(value)
        case FieldType.CURRENCY:
            return CurrencyFieldValue.from_generic_value(value)
        case FieldType.TEXT:
            return TextFieldValue.from_generic_value(value)
        case FieldType.TEXT_AREA:
            return TextAreaFieldValue.from_generic_value(value)
        case FieldType.LONG_TEXT_AREA:
            return LongTextAreaFieldValue.from_generic_value(value)
        case FieldType.RICH_TEXT_AREA:
            return RichTextAreaFieldValue.from_generic_value(value)
        case FieldType.TIMESTAMP:
            return TimestampFieldValue.from_generic_value(value)
        case FieldType.LOCAL_TIME_OF_DAY:
            return LocalTimeOfDayFieldValue.from_generic_value(value)
        case FieldType.TIME_OF_DAY:
            return TimeOfDayFieldValue.from_generic_value(value)
        case FieldType.LOCAL_DATE:
            return LocalDateFieldValue.from_generic_value(value)
        case FieldType.BOOLEAN_CHECKBOX:
            return BooleanCheckboxFieldValue.from_generic_value(value)
        case FieldType.EMAIL:
            return EmailFieldValue.from_generic_value(value)
        case FieldType.PERCENT:
            return PercentFieldValue.from_generic_value(value)
        case FieldType.PHONE_NUMBER:
            return PhoneNumberFieldValue.from_generic_value(value)
        case FieldType.SINGLE_SELECT:
            return SingleSelectFieldValue.from_generic_value(value)
        case FieldType.MULTI_SELECT:
            return MultiSelectFieldValue.from_generic_value(value)
        case FieldType.URL:
            return UrlFieldValue.from_generic_value(value)
        case FieldType.GEO_LOCATION:
            return GeoLocationFieldValue.from_generic_value(value)
        case _ as unreachable:
            assert_never(unreachable)
