import inspect
import uuid
from collections.abc import Callable
from typing import (
    Annotated,
    Any,
    ClassVar,
    Final,
    Generic,
    TypeVar,
    Unpack,
    final,
)

from pydantic import (
    BaseModel,
    BeforeValidator,
    ConfigDict,
    Field,
    WrapSerializer,
    model_serializer,
)
from pydantic.json_schema import SkipJsonSchema
from typing_extensions import TypeIs

__all__ = [
    "UNSET",
    "AbstractUnsetAwareModel",
    "AbstractUnsetAwareT",
    "BaseBulkPatchRequest",
    "BasePatchRequest",
    "BulkPatchEntityResponse",
    "UnsetAware",
    "is_unset",
    "is_unset_type",
    "new_or_unset_if_same",
    "specified",
    "specified_all",
    "specified_any",
    "specified_or_default",
]

from pydantic_core import PydanticUndefined
from pydantic_core.core_schema import SerializationInfo, ValidationInfo

T = TypeVar("T")


@final
class _Unset(BaseModel):
    model_config = ConfigDict(
        frozen=True,
        # make sure use "forbid" mode here
        # this will force pydantic only deserialize an empty map
        # into an unset in a smart "union" mode
        extra="forbid",
    )

    def __init_subclass__(cls, **kwargs: Unpack[ConfigDict]):
        super().__init_subclass__(**kwargs)
        raise TypeError("You cannot subclass _Unset")


UNSET: Final[_Unset] = _Unset()


def ensure_is_patch_request_subclass(v: Any, info: ValidationInfo) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """
    This is probably the only hack in the patch request module.
    There isn't a way to enforce a pydantic field to be used in subclasses of a
    specific class without using inspect (reflection) module. So, we use a hack here
    to enforce the field to be used in subclasses of BasePatchRequest during any
    instance creation or assignment.
    """
    class_schema_title: str = info.config.get("title", "") if info.config else ""
    if BasePatchRequest._title_element not in class_schema_title:
        raise ValueError(
            "the field defined with UnsetAware must be used in a "
            "subclass of BasePatchRequest"
        )
    return v


def serialize_unset(v: Any, handler: Callable[[Any], Any], info: Any) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    if isinstance(v, _Unset):
        return None
    return handler(v)


UnsetAware = Annotated[
    T | SkipJsonSchema[_Unset],
    Field(
        # use default_factory, such that pydantic will render
        # this field as non-required without setting a static default
        # value in the schema
        default_factory=lambda: UNSET,
        union_mode="smart",
    ),
    BeforeValidator(ensure_is_patch_request_subclass),
    WrapSerializer(serialize_unset),
]


def specified(val: UnsetAware[T]) -> TypeIs[T]:
    return not isinstance(val, _Unset)


def specified_any(*vals: UnsetAware[T]) -> bool:
    return any(specified(val) for val in vals)


def specified_all(*vals: UnsetAware[T]) -> bool:
    return all(specified(val) for val in vals)


def specified_or_default(val: UnsetAware[T], default: T) -> T:
    return val if specified(val) else default


def is_unset(val: Any) -> TypeIs[_Unset]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    return isinstance(val, _Unset)


def is_unset_type(val: Any) -> TypeIs[type[_Unset]]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    return val == _Unset  # type: ignore[no-any-return]


def new_or_unset_if_same(*, old: T, new: T | _Unset) -> T | _Unset:
    return new if (not is_unset(new)) and (new != old) else UNSET


AbstractUnsetAwareT = TypeVar("AbstractUnsetAwareT")


class AbstractUnsetAwareModel(BaseModel, Generic[AbstractUnsetAwareT]):
    _title_element: ClassVar[str] = "UnsetTypeAware"

    model_config = ConfigDict(
        frozen=True,
        # any assignment on a patch request instance need to go through type validation
        validate_assignment=True,
        title=_title_element,
    )

    def __init_subclass__(cls, **kwargs: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        """
        Hack, inject a title suffix to subclass schema title,
        such that we can use Unset BeforeValidator to ensure
        the Unset field is only used in subclasses of BasePatchRequest.
        """
        this_cls_title = cls.model_config.get("title", cls.__name__) or ""
        cls.model_config["title"] = (
            f"{this_cls_title} ({AbstractUnsetAwareModel._title_element})"
            if AbstractUnsetAwareModel._title_element not in this_cls_title
            else this_cls_title
        )

    @classmethod
    def __pydantic_init_subclass__(cls, **kwargs: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().__pydantic_init_subclass__(**kwargs)
        fields = cls.model_fields

        for field_name, field_info in fields.items():
            # We use default_factory to avoid generate "default" value in Json schema
            # for "UnsetAware". And this default_factory always take precedence over
            # default value. Hence, any manually set default value with presence of
            # default_factory doesn't make sense, and potentially cause confusion
            if callable(field_info.default_factory) and (
                field_info.default is not PydanticUndefined
            ):
                # check whether field_info.default_factory is a function that takes no arguments
                default_factory_params = inspect.signature(
                    field_info.default_factory
                ).parameters.values()
                if len(default_factory_params) != 0:
                    raise TypeError(
                        f"In a PatchRequest, default_factory for {field_name} must be a function that takes no arguments"
                    )
                default_factory_value = field_info.default_factory()  # type: ignore[call-arg]
                if default_factory_value != field_info.default:
                    raise TypeError(
                        f"there is default-factory defined for {field_name}, the factory"
                        f" vends values with type ({type(default_factory_value)}) "
                        "user shouldn't set default value vending different type "
                        f"({type(field_info.default)}) at the same time."
                    )

    def _find_unset_fields(self) -> set[str]:
        return {
            field_name
            for field_name in self.model_fields
            if is_unset(getattr(self, field_name, None))
        }

    @model_serializer(mode="wrap")
    def _custom_serialize_unset(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, handler: Callable[[Any], Any], info: SerializationInfo
    ) -> Any:
        """
        Ensure unset value is never ever serialized into dict or json
        no matter it's on top level or a nested model fields.
        """
        interim = handler(self)
        unspecified_fields = self._find_unset_fields()
        for field_name in unspecified_fields:
            interim.pop(field_name, None)
        return interim


class BasePatchRequest(AbstractUnsetAwareModel[Any]):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """
    See patch_request unit tests for usage examples and usage restrictions!

    Sample usage:
    class MyPatchRequest(BasePatchRequest):
        val1: UnsetAware[int | None]  # allow set val1 to "int" or null value
        val2: UnsetAware[dict[str, Any]]  # allow set val2 to "dict[str, Any]"

    After checking out the test cases,
    You will realize that this base class and UnsetAware are very intentionally designed
    such that an "unset" value can only come from a json/dict deserialized python object
    but never from a code constructed object. Since unset means "it was not set"
    """

    require_at_least_one_specified_field: ClassVar[bool] = False

    def model_post_init(self, __context: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().model_post_init(__context)
        if self.require_at_least_one_specified_field and not any(
            specified(getattr(self, field_name)) for field_name in self.model_fields
        ):
            raise ValueError("need at least one field to be specified")

    def model_dump(self, **kwargs: Any) -> dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        """
        Intentionally set model dump to filter out unset fields for patching request.
        It doesn't make sense for unset values (even for regular types) to bleed into
        downstream consumer.
        """
        kwargs["exclude_unset"] = True
        return super().model_dump(**kwargs)

    def model_dump_json(self, **kwargs: Any) -> str:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        """
        Intentionally set model dump to filter out unset fields for patching request.
        It doesn't make sense for unset values (even for regular types) to bleed into
        downstream consumer.
        """
        kwargs["exclude_unset"] = True
        return super().model_dump_json(**kwargs)


class BaseBulkPatchRequest(BaseModel):
    entity_ids: list[uuid.UUID]
    patch_request: BasePatchRequest


class BulkPatchEntityResponse(BaseModel):
    total_requested_count: int
    patch_success_count: int
    patch_failed_count: int
    failure_details: dict[uuid.UUID, str] = {}
