import inspect
import types
import typing
from typing import Any, <PERSON>V<PERSON>, <PERSON>ric, TypeVar

from pydantic import BaseModel, ConfigDict, Field
from pydantic.fields import FieldInfo

from salestech_be.common.type.patch_request import (
    AbstractUnsetAwareModel,
    AbstractUnsetAwareT,
    is_unset_type,
    specified,
)


class FieldParity(BaseModel):
    model_config = ConfigDict(frozen=True)
    excluded_fields: frozenset[str] = Field(default_factory=frozenset)


class ShapeConstrainedModel(
    AbstractUnsetAwareModel[AbstractUnsetAwareT], Generic[AbstractUnsetAwareT]
):
    __constraint__: type[AbstractUnsetAwareT] = BaseModel  # type: ignore[assignment]

    # If field_parity is set, then
    # 1. subclass must declare all the fields excluding the ones in
    #   'excluded_fields' from constraint model.
    # 2. subclass must not declare any fields that are in the 'excluded_fields'.
    field_parity: ClassVar[FieldParity | None] = None

    @classmethod
    def constraint(cls) -> type[AbstractUnsetAwareT]:
        return cls.__constraint__

    def __init_subclass__(cls, **kwargs: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().__init_subclass__(**kwargs)

    @classmethod
    def __pydantic_init_subclass__(cls, **kwargs: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().__pydantic_init_subclass__(**kwargs)
        cls._populate_constraint()
        cls._validate_model_fields_against_constraint()

    @classmethod
    def _populate_constraint(cls) -> None:
        generic_origin = cls.__pydantic_generic_metadata__.get("origin", None)
        if not generic_origin:
            return
        if not issubclass(cls, generic_origin):
            raise TypeError(f"{cls.__name__} must be a subclass of {generic_origin})")
        generic_args = cls.__pydantic_generic_metadata__.get("args", ())
        if not generic_args:
            raise TypeError(f"{cls.__name__} must have generic args")
        first_generic_arg = generic_args[0]
        if inspect.isclass(first_generic_arg):
            if not issubclass(first_generic_arg, BaseModel):
                raise TypeError(
                    f"{cls.__name__} must have a generic arg that is a subclass of pydantic.BaseModel",
                )
        elif not isinstance(first_generic_arg, TypeVar):
            raise TypeError(
                f"{cls.__name__} the generic arg can only be a TypeVar or a subclass of pydantic.BaseModel"
            )
        cls.__constraint__ = first_generic_arg

    @classmethod
    def _validate_model_fields_against_constraint(cls) -> None:  # noqa: C901
        if isinstance(cls.__constraint__, TypeVar):
            return
        if cls.__constraint__ is BaseModel:
            raise TypeError(
                f"constraint of class {cls.__name__} must be overridden to a subclass of "
                f"pydantic.BaseModel, but found {cls.__constraint__}",
            )
        if not issubclass(cls.__constraint__, BaseModel):
            raise TypeError(
                f"constraint of class {cls.__name__} must be a subclass of DBModel",
            )
        constraint_fields: dict[str, FieldInfo] = cls.__constraint__.model_fields

        # we don't need to validate excluded fields against constraint
        if cls.field_parity and (
            missing_fields := constraint_fields.keys()
            - cls.model_fields.keys()
            - cls.field_parity.excluded_fields
        ):
            raise TypeError(
                f"{missing_fields} are not declared to be in parity with constraint"
                f" model {cls.__constraint__.__name__}"
            )
        if cls.field_parity and (
            extra_fields := {
                extra_field
                for extra_field in cls.model_fields
                if extra_field in cls.field_parity.excluded_fields
            }
        ):
            raise TypeError(
                f"constraint model {cls.__constraint__.__name__} has extra fields "
                f"that are declared in cls.field_parity.excluded_fields: {extra_fields}"
            )

        constraint_type_hints = typing.get_type_hints(cls.__constraint__)
        my_class_type_hints = typing.get_type_hints(cls)
        for f_name in cls.model_fields:
            # step 1, validate field name
            if f_name not in constraint_fields:
                raise TypeError(
                    f"field {f_name} in {cls.__name__} is not found in "
                    f"{cls.__constraint__.__name__}",
                )
            # step 2, validate field type
            my_field_type_hints = my_class_type_hints.get(f_name)
            if (
                typing.get_origin(my_field_type_hints) == typing.Union
                or type(my_field_type_hints) is types.UnionType
            ):
                my_field_type_hints = typing.get_args(my_field_type_hints) or (
                    my_field_type_hints,
                )
            else:
                my_field_type_hints = (my_field_type_hints,)
            my_field_type_hints = tuple(
                th for th in my_field_type_hints if not is_unset_type(th)
            )
            constraint_field_type_hints = constraint_type_hints.get(f_name)
            if (
                typing.get_origin(constraint_field_type_hints) == typing.Union
                or type(constraint_field_type_hints) is types.UnionType
            ):
                constraint_field_type_hints = typing.get_args(
                    constraint_field_type_hints
                ) or (constraint_field_type_hints,)
            else:
                constraint_field_type_hints = (constraint_field_type_hints,)
            if set(my_field_type_hints) - set(constraint_field_type_hints):
                raise TypeError(
                    f"field {f_name} in {cls.__name__} has different type "
                    f"from {cls.__constraint__.__name__}",
                    f"expected within {constraint_field_type_hints}, got {my_field_type_hints}",
                )

    def flatten_specified_values(self) -> dict[str, object]:
        """
        Flatten the specified values of the model into a dictionary.
        !!!NOTE!!! This is different from model_dump. This function WILL NOT dump all fields
        recursively into dictionary. By design, it will:
        1. return fields <> value pairs as long as they are not UNSET
        2. this means, if a field has a default value set, then it will be included as well.
        """
        result: dict[str, object] = {}
        for f_name in self.model_fields:
            if specified(f_value := getattr(self, f_name)):
                result[f_name] = f_value
        return result

    def specified_fields(self) -> list[str]:
        return [
            f_name for f_name in self.model_fields if specified(getattr(self, f_name))
        ]


def shaped_model_patch[T: BaseModel](
    *, patch: ShapeConstrainedModel[T], target: T
) -> T:
    return target.model_copy(update=patch.model_dump(), deep=True)
