import asyncio
import time
from collections.abc import Awaitable, Callable
from functools import wraps
from typing import ParamSpec, TypeVar

from salestech_be.ree_logging import Logger, get_logger

# Default logger for this file
default_logger = get_logger(__name__)

P = ParamSpec("P")
R = TypeVar("R")


def log_timing_sync(
    name: str | None = None,
    logger: Logger | None = None,
) -> Callable[[Callable[P, R]], Callable[P, R]]:
    """
    A decorator that logs the execution time of a synchronous function.

    Args:
        name: Optional name to use in the log message. If not provided, uses the function name.
        logger: Optional logger instance to use. If not provided, uses this file's default logger.

    Example:
        # Using default logger
        @log_timing_sync()
        def my_function():
            ...

        # Using custom logger and name
        logger = get_logger(__name__)
        @log_timing_sync("custom_name", logger=logger)
        def my_function():
            ...
    """

    def decorator(func: Callable[P, R]) -> Callable[P, R]:
        if asyncio.iscoroutinefunction(func):
            raise ValueError("log_timing_sync cannot be used with async functions")

        # Get the function name for logging
        func_name = name or func.__name__
        # Use provided logger or default to this file's logger
        log = logger or default_logger

        @wraps(func)
        def sync_wrapper(*args: P.args, **kwargs: P.kwargs) -> R:
            start_time = time.perf_counter()
            try:
                log.info(
                    "Function execution started",
                    func_name=func_name,
                )
                return func(*args, **kwargs)
            finally:
                end_time = time.perf_counter()
                duration_ms = (end_time - start_time) * 1000
                log.info(
                    "Function execution finished",
                    func_name=func_name,
                    duration_ms=duration_ms,
                )

        return sync_wrapper

    return decorator


def log_timing_async(
    name: str | None = None,
    logger: Logger | None = None,
) -> Callable[[Callable[P, Awaitable[R]]], Callable[P, Awaitable[R]]]:
    """
    A decorator that logs the execution time of an async function.

    Args:
        name: Optional name to use in the log message. If not provided, uses the function name.
        logger: Optional logger instance to use. If not provided, uses this file's default logger.

    Example:
        # Using default logger
        @log_timing_async()
        async def my_async_function():
            ...

        # Using custom logger and name
        logger = get_logger(__name__)
        @log_timing_async("custom_name", logger=logger)
        async def my_async_function():
            ...
    """

    def decorator(func: Callable[P, Awaitable[R]]) -> Callable[P, Awaitable[R]]:
        if not asyncio.iscoroutinefunction(func):
            raise ValueError("log_timing_async can only be used with async functions")

        # Get the function name for logging
        func_name = name or func.__name__
        # Use provided logger or default to this file's logger
        log = logger or default_logger

        @wraps(func)
        async def async_wrapper(*args: P.args, **kwargs: P.kwargs) -> R:
            start_time = time.perf_counter()
            try:
                log.info(
                    "Function execution started",
                    func_name=func_name,
                )
                return await func(*args, **kwargs)
            finally:
                end_time = time.perf_counter()
                duration_ms = (end_time - start_time) * 1000
                log.info(
                    "Function execution finished",
                    func_name=func_name,
                    duration_ms=duration_ms,
                )

        return async_wrapper

    return decorator


def log_timing(
    name: str | None = None,
    logger: Logger | None = None,
) -> Callable[[Callable[P, R]], Callable[P, R]]:
    """
    A legacy decorator that logs the execution time of a function.
    Works with both async and sync functions.

    For new code, prefer using log_timing_sync or log_timing_async.

    Args:
        name: Optional name to use in the log message. If not provided, uses the function name.
        logger: Optional logger instance to use. If not provided, uses this file's default logger.

    Example:
        # Using default logger
        @log_timing()
        def my_function():
            ...

        # Using custom logger and name
        logger = get_logger(__name__)
        @log_timing("custom_name", logger=logger)
        async def my_async_function():
            ...
    """

    def decorator(func: Callable[P, R]) -> Callable[P, R]:
        if asyncio.iscoroutinefunction(func):
            return log_timing_async(name, logger)(func)  # type: ignore
        return log_timing_sync(name, logger)(func)

    return decorator
