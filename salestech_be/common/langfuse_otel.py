import base64
import json
import os
from collections.abc import Iterator
from contextlib import contextmanager

from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import DEPLOYMENT_ENVIRONMENT, SERVICE_NAME, Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.trace import Span

from salestech_be.common.ree_llm import LLMTraceMetadata
from salestech_be.settings import settings

auth_str = f"{settings.langfuse_public_key.get_secret_value()}:{settings.langfuse_secret_key.get_secret_value()}"
LANGFUSE_AUTH = base64.b64encode(auth_str.encode()).decode()
os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"] = (
    "https://us.cloud.langfuse.com/api/public/otel"  # US data region
)
os.environ["OTEL_EXPORTER_OTLP_HEADERS"] = f"Authorization=Basic {LANGFUSE_AUTH}"

# Set service name and env
resource = Resource.create(
    {SERVICE_NAME: "salestech-be", DEPLOYMENT_ENVIRONMENT: settings.environment}
)

exporter = OTLPSpanExporter(timeout=30)
span_processor = BatchSpanProcessor(exporter)
tracer_provider = TracerProvider(resource=resource)
tracer_provider.add_span_processor(span_processor)


def create_tracer(name: str) -> trace.Tracer:
    """
    Create and configure a tracer for the specified name.

    Args:
        name: The name of the service or component to trace
        provider: Optional TracerProvider to use. If None, a new one is created

    Returns:
        A configured tracer
    """
    return tracer_provider.get_tracer(name)


@contextmanager
def langfuse_otel_trace(
    trace_metadata: LLMTraceMetadata,
) -> Iterator[Span]:
    """
    Context manager for creating a trace span.

    Args:
        trace_metadata: Optional LLMTraceMetadata to use for trace context

    Yields:
        The created span

    Example::

        with langfuse_otel_trace(LLMTraceMetadata(trace_name="pai-agent")) as span:
            # Perform operations to be traced
            ...
            span.set_attribute("additional.info", "some value")
    """
    tracer = create_tracer(trace_metadata.trace_name)

    with tracer.start_as_current_span(trace_metadata.trace_name) as span:
        span.set_attribute(
            "langfuse.tags", [settings.environment, *(trace_metadata.tags or [])]
        )
        if trace_metadata.user_id:
            span.set_attribute("langfuse.user.id", str(trace_metadata.user_id))
        if trace_metadata.session_id:
            span.set_attribute("langfuse.session.id", str(trace_metadata.session_id))
        if trace_metadata.prompt:
            span.set_attribute("langfuse.prompt.name", trace_metadata.prompt.name)
            span.set_attribute(
                "langfuse.prompt.version", str(trace_metadata.prompt.version)
            )
        if trace_metadata.custom_fields:
            span.set_attribute(
                "langfuse.metadata", json.dumps(trace_metadata.custom_fields)
            )

        yield span
