from typing import Literal

from pydantic import BaseModel, Field

from salestech_be.util.enum_util import NameValueStrEnum


class StandardSalesActionType(NameValueStrEnum):
    INTRO = "INTRO"
    FOLLOWUP = "FOLLOWUP"
    GENERIC_DEMO_SHARING = "GENERIC_DEMO_SHARING"
    PRICING_SHARING = "PRICING_SHARING"
    MEETING_DECK_SHARING = "MEETING_DECK_SHARING"
    BUYER_SPECIFIC_DEMO_SHARING = "BUYER_SPECIFIC_DEMO_SHARING"
    PRODUCT_ONE_PAGER_SHARING = "PRODUCT_ONE_PAGER_SHARING"
    PROBING_NEEDS = "PROBING_NEEDS"
    PROBING_PAIN = "PROBING_PAIN"
    PROBING_PRODUCT_SOLUTION_FIT = "PROBING_PRODUCT_SOLUTION_FIT"
    PROBING_TIMING = "PROBING_TIMING"
    PROBING_BUDGET = "PROBING_BUDGET"


class SalesActionActivityType(NameValueStrEnum):
    MEETING = "MEETING"
    EMAIL = "EMAIL"


class SalesActionActivityState(NameValueStrEnum):
    COMPLETED = "COMPLETED"
    SCHEDULE_CONFIRMED = "SCHEDULE_CONFIRMED"


class BaseSalesActionRequirement(BaseModel):
    required_activity_types: list[SalesActionActivityType]
    required_activity_states: list[SalesActionActivityState] = Field(
        default_factory=lambda: [SalesActionActivityState.COMPLETED]
    )
    name: str
    description: str


class StandardSalesActionRequirement(BaseSalesActionRequirement):
    requirement_type: Literal["standard"] = "standard"
    sales_action: StandardSalesActionType
