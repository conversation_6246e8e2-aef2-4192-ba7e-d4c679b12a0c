from typing import Annotated

from fastapi import Depends

from salestech_be.core.data.types import StandardRecord
from salestech_be.core.task.giant_task_types import GiantTask
from salestech_be.core.task.service.giant_task_query_service import (
    GiantTaskQueryService,
    get_giant_task_query_service,
)
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import (
    require_read_task_access,
)
from salestech_be.web.api.common.container import (
    PaginatedListResponse,
)
from salestech_be.web.api.filter.logical_filter import (
    paginate_object_records,
)
from salestech_be.web.api.task.schema import (
    ListTaskRequest,
)
from salestech_be.web.api_router_ext import Ree<PERSON><PERSON>outer
from salestech_be.web.middleware.timeout import timeout_task
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserAuthContext,
    AnnotatedReevoUserId,
)

router = ReeAPIRouter()
logger = get_logger(__name__)


@router.post(
    "/_list",
    response_model=PaginatedListResponse[StandardRecord[GiantTask]],
    dependencies=[Depends(require_read_task_access)],
)
@timeout_task(timeout_seconds=20)
async def list_giant_tasks(
    list_task_request: ListTaskRequest,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    user_auth_context: AnnotatedReevoUserAuthContext,
    giant_task_query_service: Annotated[
        GiantTaskQueryService, Depends(get_giant_task_query_service)
    ],
) -> PaginatedListResponse[StandardRecord[GiantTask]]:
    cursor = list_task_request.cursor

    # Handle task ID filtering logic
    only_include_task_ids = None
    if list_task_request.record_id:
        # If single record_id is specified, use that
        only_include_task_ids = {list_task_request.record_id}
    elif list_task_request.task_ids:
        # Otherwise use task_ids list if provided
        only_include_task_ids = set(list_task_request.task_ids)

    db_giant_tasks, new_cursor = await giant_task_query_service.list_giant_tasks(
        organization_id=organization_id,
        only_include_task_ids=only_include_task_ids,
        owner_user_id=list_task_request.owner_user_id,
        owner_user_ids_in=list_task_request.owner_user_ids_in,
        status_ne=list_task_request.status_ne,
        due_at_gt=list_task_request.due_at_gt,
        due_at_lt=list_task_request.due_at_lt,
        due_at_blank=list_task_request.due_at_blank,
        sorting_field=list_task_request.sorting_field,
        sorting_direction=list_task_request.sorting_direction,
        null_first=list_task_request.null_first,
        cursor=cursor,
        account_id=list_task_request.account_id,
        account_ids_in=list_task_request.account_ids_in,
        contact_ids=list_task_request.contact_ids,
        pipeline_id=list_task_request.pipeline_id,
        pipeline_ids_in=list_task_request.pipeline_ids_in,
        require_pipeline_id=list_task_request.require_pipeline_id,
        pipeline_stage_select_list_value_ids_in=list_task_request.pipeline_stage_select_list_value_ids_in,
        meeting_id=list_task_request.meeting_id,
        email_thread_ids=list_task_request.email_thread_ids,
        sequence_id=list_task_request.sequence_id,
        sequence_step_id_in=list_task_request.sequence_step_id_in,
        priority_in=list_task_request.priority_in,
        type_in=list_task_request.type_in,
        status_in=list_task_request.status_in,
    )

    if new_cursor:
        return PaginatedListResponse(
            list_data=[
                StandardRecord(
                    object_id=GiantTask.object_id,
                    data=giant_task,
                    related_records={},
                    requested_relationships=set(),
                )
                for giant_task in db_giant_tasks
            ],
            cursor=new_cursor,
        )

    return paginate_object_records(
        object_records=[
            StandardRecord(
                object_id=GiantTask.object_id,
                data=giant_task,
                related_records={},
                requested_relationships=set(),
            )
            for giant_task in db_giant_tasks
        ],
        cursor=list_task_request.cursor,
    )
