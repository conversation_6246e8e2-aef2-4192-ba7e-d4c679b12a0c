from __future__ import annotations

from datetime import datetime, timedelta
from enum import StrEnum, auto
from typing import Annotated, Any, Literal, Self
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field, model_validator
from pydantic.alias_generators import to_camel

from salestech_be.common.core_crm.sales_action import StandardSalesActionType
from salestech_be.common.type.metadata.field.field_value import FieldValueOrAny
from salestech_be.common.type.patch_request import UNSET, BasePatchRequest, UnsetAware
from salestech_be.core.extraction_prompt.type.shared_type import ExtractionType
from salestech_be.core.meeting.dto.meeting_dto import MeetingDto
from salestech_be.core.meeting.dto.meeting_insight_dto import InsightDTO
from salestech_be.db.dto.research_dto import MeetingResearchDto
from salestech_be.db.models.insight import (
    Insight as DbInsight,
)
from salestech_be.db.models.insight import (
    InsightAuthorType,
    InsightSourceType,
    InsightUserFeedback,
)
from salestech_be.db.models.meeting import (
    BotStatusEvent as DbBotStatusEvent,
)
from salestech_be.db.models.meeting import (
    LiveTranscriptSession as DbLiveTranscriptSession,
)
from salestech_be.db.models.meeting import (
    MeetingAnnotation as DbMeetingAnnotation,
)
from salestech_be.db.models.meeting import (
    MeetingAnnotationType,
    MeetingAttendeeMonologue,
    MeetingAttendeeTalkRatio,
    MeetingBotStatus,
    MeetingClip,
    MeetingProvider,
    MeetingReferenceIdType,
    MeetingShare,
    MeetingShareAccessType,
    MeetingStats,
    MeetingStatus,
    RecallBotParticipant,
)
from salestech_be.db.models.transcript import Transcript as DbTranscript
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.account.schema import AccountResearchResponse
from salestech_be.web.api.common.container import (
    ListCustomizableEntityRequest,
    ListEntityRequest,
)
from salestech_be.web.api.contact.schema import ContactResearchResponse

logger = get_logger()


class TranscriptSentenceWord(BaseModel):
    text: str
    start_timestamp: int
    end_timestamp: int
    confidence: float


class TranscriptSentence(BaseModel):
    speaker: str | None
    text: str
    average_confidence: float
    start_timestamp: int
    end_timestamp: int
    language: str
    words: list[TranscriptSentenceWord] | None = None

    def compact(self, line: int) -> str:
        return f"{self.speaker} [{line} - {self.start_timestamp}]: {self.text}"

    @property
    def duration(self) -> int:
        if self.start_timestamp > self.end_timestamp:
            raise ValueError("Transcript with invalid start and end time.")
        return self.end_timestamp - self.start_timestamp


class TranscriptSentenceWithLineNum(TranscriptSentence):
    line_number: int


class Transcript(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    id: UUID
    provider: str
    analysis_input: dict[str, Any] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
    s3_raw_key: str | None = None
    s3_processed_key: str | None = None
    transcript_sentences: list[TranscriptSentence] | None = None
    external_job_id: str | None = None
    created_at: datetime
    updated_at: datetime

    @staticmethod
    def map_from_db(db_transcript: DbTranscript) -> Transcript:
        return Transcript(
            id=db_transcript.id,
            provider=db_transcript.provider,
            analysis_input=db_transcript.analysis_input,
            s3_raw_key=db_transcript.s3_raw_key,
            s3_processed_key=db_transcript.s3_processed_key,
            transcript_sentences=None,
            external_job_id=db_transcript.external_job_id,
            created_at=db_transcript.created_at,
            updated_at=db_transcript.updated_at,
        )


class RecallBotEventTypes(StrEnum):
    BOT_OUTPUT_LOG = "bot.output_log"
    BOT_STATUS_CHANGE = "bot.status_change"
    BOT_REALTIME_TRANSCRIPTION = "bot.transcription"
    BOT_REALTIME_PARTICIPANT_JOIN = "bot.participant_join"
    BOT_REALTIME_PARTICIPANT_LEAVE = "bot.participant_leave"
    BOT_REALTIME_ACTIVE_SPEAKER_NOTIFY = "bot.active_speaker_notify"
    BOT_REALTIME_PARTICIPANT_IN_WAITING_ROOM = "bot.participant_in_waiting_room"
    BOT_REALTIME_PARTICIPANT_SCREEN_OFF = "bot.participant_screen_off"


class ParticipantMetadata(BaseModel):
    email: str | None = None
    contact_id: UUID | None = None
    user_id: UUID | None = None
    display_name: str = ""
    account_id: UUID | None = None


class Invitee(ParticipantMetadata):
    is_organizer: bool = False
    status: str | None = None


class Attendee(ParticipantMetadata):
    transcript_speaker_name: str | None = None
    # persona: Literal["Champion", "Saboteur", "Decision Maker"] | None = None


class Target(BaseModel):
    contact_id: str | None = None


class ListMeetingsRequest(ListEntityRequest):
    include_canceled: bool = False


class ListMeetingHistoryRequest(ListEntityRequest):
    meeting_id: UUID


class EndMeetingRequest(BaseModel):
    is_no_show: bool


class PatchMeetingRequest(BasePatchRequest):
    require_at_least_one_specified_field = True

    status: UnsetAware[MeetingStatus] = UNSET
    verbal_consent_at: UnsetAware[ZoneRequiredDateTime] = UNSET
    title: UnsetAware[str] = UNSET
    started_at: UnsetAware[ZoneRequiredDateTime] = UNSET
    description: UnsetAware[str | None] = UNSET
    starts_at: UnsetAware[ZoneRequiredDateTime] = UNSET
    ends_at: UnsetAware[ZoneRequiredDateTime] = UNSET
    platform: UnsetAware[MeetingProvider | None] = UNSET
    event_conferencing: UnsetAware[MeetingEventConferencing] = UNSET
    user_calendar_event_id: UnsetAware[UUID | None] = UNSET
    invitees: UnsetAware[list[Invitee]] = UNSET
    custom_field_data: UnsetAware[dict[UUID, FieldValueOrAny] | None] = UNSET
    agenda: UnsetAware[str | None] = UNSET
    key_talking_points: UnsetAware[str | None] = UNSET
    is_no_show: UnsetAware[bool] = UNSET
    is_sales_meeting: UnsetAware[bool] = UNSET
    is_external_meeting: UnsetAware[bool] = UNSET
    is_bot_enabled: UnsetAware[bool | None] = UNSET
    reference_id: UnsetAware[MeetingReferenceId] = UNSET
    sales_action_types: UnsetAware[list[StandardSalesActionType]] = UNSET
    pipeline_id: UnsetAware[UUID | None] = UNSET


class MeetingEventConferencing(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    event_schedule_id: UUID | None
    conferencing_details: dict[str, Any]  # type: ignore[explicit-any] # TODO: fix-any-annotation
    meeting_url: str | None
    consent_id: UUID | None
    location: str | None
    use_bot: bool


MeetingReferenceId = str | UUID


class CreateMeetingRequest(BaseModel):
    platform: MeetingProvider | None
    reference_id: MeetingReferenceId
    reference_id_type: MeetingReferenceIdType
    starts_at: ZoneRequiredDateTime
    ends_at: ZoneRequiredDateTime | None = None
    title: str
    description: str | None = None
    event_conferencing: MeetingEventConferencing | None = None
    invitees: list[Invitee]
    pipeline_id: UUID | None = None
    pipeline_select_list_value_id: UUID | None = None
    account_id: UUID | None = None
    sequence_id: UUID | None = None
    user_calendar_event_id: UUID | None = None

    # for meeting that started immediately (like voice call)
    started_at: ZoneRequiredDateTime | None = None


# This is used as input to import_meeting_from_external_recording
class ImportMeetingFromExternalRecordingRequest(BaseModel):
    user_names: list[str]
    contact_emails: list[EmailStr]
    user_id: UUID
    account_id: UUID | None = None
    title: str
    pipeline_id: UUID | None = None
    pipeline_display_name: str | None = None
    description: str | None = None
    reference_id: MeetingReferenceId
    date_of_meeting: ZoneRequiredDateTime = zoned_utc_now()
    presigned_media_url: str
    duration_seconds: int


class Participant(BaseModel):
    name: str
    is_host: bool
    platform: str

    @classmethod
    def from_recall_participant(
        cls, recall_participant: RecallBotParticipant
    ) -> Participant:
        return cls(
            name=recall_participant.name,
            is_host=recall_participant.is_host,
            platform=recall_participant.platform,
        )

    @classmethod
    def from_meeting_attendee(cls, meeting_dto: MeetingDto) -> list[Participant]:
        return (
            [
                cls(
                    name=attendee.transcript_speaker_name or "Unknown",
                    is_host=attendee.is_organizer,
                    platform=meeting_dto.meeting.meeting_platform,
                )
                for attendee in meeting_dto.meeting.attendees
            ]
            if meeting_dto.meeting.meeting_platform and meeting_dto.meeting.attendees
            else []
        )


class BotStatusEvent(BaseModel):
    status: str
    status_at: datetime

    @classmethod
    def from_db_object(cls, bot_status: DbBotStatusEvent) -> BotStatusEvent:
        return cls(status=bot_status.status, status_at=bot_status.status_at)


# Recall Bot Webhook
class RecallBotEventStatus(BaseModel):
    code: str
    created_at: str
    sub_code: str | None = None
    message: str | None = None
    recording_id: str | None = None


class RecallBotEventData(BaseModel):
    bot_id: str
    status: RecallBotEventStatus

    def get_db_status_event(self) -> DbBotStatusEvent:
        return DbBotStatusEvent(
            status=self.status.code,
            sub_code=self.status.sub_code,
            status_at=datetime.fromisoformat(self.status.created_at),
        )


class RecallBotStatusChangeEvent(BaseModel):
    # Following recall AI https://docs.recall.ai/docs/bot-status-change-events spec
    event: Literal[RecallBotEventTypes.BOT_STATUS_CHANGE]
    data: RecallBotEventData


class RecallBotOutputLog(BaseModel):
    created_at: datetime | None = None
    level: str
    message: str
    output_id: str | None = None


class RecallBotOutputLogData(BaseModel):
    job_id: str | None = None
    log: RecallBotOutputLog


class RecallBotOutputLogEvent(BaseModel):
    # Following recall AI https://docs.recall.ai/docs/bot-status-change-events spec
    event: Literal[RecallBotEventTypes.BOT_OUTPUT_LOG]
    data: RecallBotOutputLogData


class RecallRealtimeTranscriptWord(BaseModel):
    text: str
    start_time: float
    end_time: float


class RecallRealtimeTranscript(BaseModel):
    speaker: str | None
    speaker_id: int | None
    language: str | None
    original_transcript_id: int
    words: list[RecallRealtimeTranscriptWord]
    is_final: bool


class RecallRealtimeSearchHit(BaseModel):
    text: str
    start_time: float
    end_time: float
    confidence: float


class RecallRealtimeSearch(BaseModel):
    speaker: str | None
    original_transcript_id: str
    hits: list[RecallRealtimeSearchHit]


class RecallRealtimeTranscriptionData(BaseModel):
    bot_id: str
    recording_id: str
    transcript: RecallRealtimeTranscript | None
    # search is documented but seems to not be used
    search: RecallRealtimeSearch | None = None
    # undocumented field that isn't populated
    source: str | None = None


class RecallRealtimeTranscriptionEvent(BaseModel):
    event: Literal[RecallBotEventTypes.BOT_REALTIME_TRANSCRIPTION]
    data: RecallRealtimeTranscriptionData


class RecallRealtimeCallData(BaseModel):
    bot_id: str
    participant_id: int
    created_at: str


class RecallRealtimeParticipantJoinEvent(BaseModel):
    event: Literal[RecallBotEventTypes.BOT_REALTIME_PARTICIPANT_JOIN]
    data: RecallRealtimeCallData


class RecallRealtimeParticipantLeaveEvent(BaseModel):
    event: Literal[RecallBotEventTypes.BOT_REALTIME_PARTICIPANT_LEAVE]
    data: RecallRealtimeCallData


class RecallRealtimeActiveSpeakerNotifyEvent(BaseModel):
    event: Literal[RecallBotEventTypes.BOT_REALTIME_ACTIVE_SPEAKER_NOTIFY]
    data: RecallRealtimeCallData


class RecallRealtimeParticipantInWaitingRoomEvent(BaseModel):
    event: Literal[RecallBotEventTypes.BOT_REALTIME_PARTICIPANT_IN_WAITING_ROOM]
    data: RecallRealtimeCallData


class RecallRealtimeParticipantScreenOffEvent(BaseModel):
    event: Literal[RecallBotEventTypes.BOT_REALTIME_PARTICIPANT_SCREEN_OFF]
    data: RecallRealtimeCallData


RecallBotEvent = Annotated[
    RecallBotStatusChangeEvent
    | RecallBotOutputLogEvent
    | RecallRealtimeTranscriptionEvent
    | RecallRealtimeParticipantJoinEvent
    | RecallRealtimeParticipantLeaveEvent
    | RecallRealtimeActiveSpeakerNotifyEvent
    | RecallRealtimeParticipantInWaitingRoomEvent
    | RecallRealtimeParticipantScreenOffEvent,
    Field(discriminator="event"),
]


class AttachBotRequest(BaseModel):
    meeting_id: UUID
    is_verbal_consent_granted: bool
    starts_at: ZoneRequiredDateTime | None = None


class ImportBotRequest(BaseModel):
    meeting_id: UUID
    external_meeting_bot_id: str


class MeetingConsentRequest(BaseModel):
    consent_id: UUID
    is_recording_consent_given: bool
    evict_bot: bool = False


class MeetingConsentResponse(BaseModel):
    meeting_url: str | None


class ListInsightsRequest(BasePatchRequest):
    meeting_id: UUID | None = None
    upcoming_meeting_id: UUID | None = None


class ReRankInsightRequest(BaseModel):
    predecessor_id: UUID | None = None
    successor_id: UUID | None = None


class PatchInsightRequest(BasePatchRequest):
    require_at_least_one_specified_field = True

    contact_id: UnsetAware[UUID] = UNSET
    field_values: UnsetAware[list[str]] = UNSET
    detailed_explanation: UnsetAware[str] = UNSET
    tags: UnsetAware[list[str]] = UNSET
    user_feedback: UnsetAware[InsightUserFeedback | None] = UNSET
    transcript_offset_locations: UnsetAware[list[int]] = UNSET
    approved_at: UnsetAware[ZoneRequiredDateTime] = UNSET


class CreateInsightsFieldRequest(BasePatchRequest):
    meeting_insight_id: UUID
    name: str
    rank: float | None = None
    description: str | None = None
    field_values: list[str]
    contact_id: UUID | None = None


class CreateLiveTranscriptSessionRequest(BaseModel):
    meeting_id: UUID


class LiveRecordingsAction(StrEnum):
    PAUSE = auto()
    RESUME = auto()
    STOP = auto()


class LiveRecordingsRequest(BaseModel):
    meeting_id: UUID
    action: LiveRecordingsAction
    # Set to true for initial testing
    is_verbal_consent_given: bool = True


class LiveTranscriptSession(BaseModel):
    id: UUID
    organization_id: UUID
    user_id: UUID
    meeting_id: UUID
    created_at: datetime
    updated_at: datetime
    last_connected_at: datetime

    @classmethod
    def from_db_insight(
        cls, live_transcript_session: DbLiveTranscriptSession
    ) -> LiveTranscriptSession:
        return LiveTranscriptSession(**live_transcript_session.dict())


class LiveTranscriptionSegmentType(StrEnum):
    FINALIZED = auto()
    PARTIAL = auto()


class LiveTranscriptSegment(BaseModel):
    meeting_id: UUID
    organization_id: UUID
    speaker: str | None
    words: str
    segment_type: LiveTranscriptionSegmentType
    start_offset: int
    end_offset: int

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    @classmethod
    def from_recall_data(
        cls,
        meeting_id: UUID,
        organization_id: UUID,
        transcript_data: RecallRealtimeTranscript,
    ) -> LiveTranscriptSegment:
        start_offset = min(
            word_entry.start_time for word_entry in transcript_data.words
        )
        end_offset = max(word_entry.start_time for word_entry in transcript_data.words)
        text = " ".join(word_entry.text for word_entry in transcript_data.words)

        return LiveTranscriptSegment(
            meeting_id=meeting_id,
            speaker=transcript_data.speaker,
            words=text,
            segment_type=LiveTranscriptionSegmentType.FINALIZED
            if transcript_data.is_final
            else LiveTranscriptionSegmentType.PARTIAL,
            start_offset=int(start_offset),
            end_offset=int(end_offset),
            organization_id=organization_id,
        )


class MeetingInsight(BaseModel):
    id: UUID
    insight_type: ExtractionType
    source_type: InsightSourceType
    organization_id: UUID
    name: str
    extraction_section_id: UUID | None
    extraction_section_version: int | None
    insight_fields: list[MeetingInsightField]
    meeting_id: UUID
    created_at: datetime
    created_by_user_id: UUID | None
    updated_at: datetime
    updated_by_user_id: UUID | None
    deleted_at: datetime | None
    deleted_by_user_id: UUID | None
    user_feedback: InsightUserFeedback | None

    @classmethod
    def from_dto(cls, insight_dto: InsightDTO) -> MeetingInsight:
        insight_section = insight_dto.insight_section
        insight_fields = [
            MeetingInsightField.from_db_object(field) for field in insight_dto.insights
        ]

        # Custom sorting for different insight types
        if insight_section.name == "BANT":
            order_map = {"Budget": 0, "Authority": 1, "Need": 2, "Timeline": 3}
        elif insight_section.name == "MEDDIC":
            order_map = {
                "Metrics": 0,
                "Economic Buyer": 1,
                "Decision Criteria": 2,
                "Decision Process": 3,
                "Identify Pain": 4,
                "Champion": 5,
            }
        elif insight_section.name == "SPICED":
            order_map = {
                "Situation": 0,
                "Pain": 1,
                "Impact": 2,
                "Critical Event": 3,
                "Decision": 4,
            }
        else:
            order_map = {}

        if order_map:
            insight_fields.sort(key=lambda x: order_map.get(x.name, len(order_map)))

        return cls(
            id=insight_section.id,
            insight_type=insight_section.insight_type,
            source_type=insight_section.source_type,
            organization_id=insight_section.organization_id,
            name=insight_section.name,
            extraction_section_id=insight_section.extraction_section_id,
            extraction_section_version=insight_section.extraction_section_version,
            meeting_id=not_none(insight_section.reference_id),
            created_at=insight_section.created_at,
            created_by_user_id=insight_section.created_by_user_id,
            updated_at=insight_section.updated_at,
            updated_by_user_id=insight_section.updated_by_user_id,
            deleted_at=insight_section.deleted_at,
            deleted_by_user_id=insight_section.deleted_by_user_id,
            user_feedback=insight_section.user_feedback,
            insight_fields=insight_fields,
        )


class ConfiguredSection(BaseModel, frozen=True):
    type: ExtractionType
    name: str
    insight_section_id: UUID


class MeetingInsights(BaseModel):
    configured_sections: list[ConfiguredSection]
    map_data: dict[ExtractionType, list[MeetingInsight]]

    @staticmethod
    def build_map_from_list(
        meeting_insights: list[MeetingInsight],
    ) -> dict[ExtractionType, list[MeetingInsight]]:
        map_data: dict[ExtractionType, list[MeetingInsight]] = {}

        for extraction_type in ExtractionType.__members__.values():
            map_data[extraction_type] = []

        for insight in meeting_insights:
            if insight.insight_type not in map_data:
                map_data[insight.insight_type] = []
            map_data[insight.insight_type].append(insight)
        return map_data

    @staticmethod
    def reduce_list_to_configured_sections(
        meeting_insights: list[MeetingInsight],
    ) -> list[ConfiguredSection]:
        sections: list[ConfiguredSection] = []

        for insight in meeting_insights:
            section = ConfiguredSection(
                type=insight.insight_type,
                name=insight.name,
                insight_section_id=insight.id,
            )
            sections.append(section)

        return sections


class TranscriptRange(BaseModel):
    start: int
    end: int


class MeetingInsightField(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    id: UUID
    name: str
    description: str
    contact_id: UUID | None
    field_values: list[str]
    detailed_explanation: str
    tags: list[str]
    transcript_offset_locations: list[int]
    transcript_range_locations: list[TranscriptRange]
    author_type: InsightAuthorType
    created_at: datetime
    metadata: dict[str, Any]  # type: ignore[explicit-any] # TODO: fix-any-annotation
    created_by_user_id: UUID | None
    updated_at: datetime
    updated_by_user_id: UUID | None
    deleted_at: datetime | None
    deleted_by_user_id: UUID | None
    user_feedback: InsightUserFeedback | None
    approved_at: ZoneRequiredDateTime | None
    approved_by_user_id: UUID | None

    @classmethod
    def from_db_object(cls, insight: DbInsight) -> MeetingInsightField:
        return cls(
            id=insight.id,
            name=insight.insight_name,
            description=insight.insight_description
            if insight.insight_description
            else "",
            contact_id=insight.contact_id,
            tags=insight.tags if insight.tags else [],
            field_values=insight.brief_values if insight.brief_values else [],
            detailed_explanation=insight.detailed_explanation
            if insight.detailed_explanation
            else "",
            transcript_offset_locations=insight.transcript_locations
            if insight.transcript_locations
            else [],
            transcript_range_locations=(
                MeetingInsightField.transcript_ranges(insight.transcript_locations, 2)
                if insight.transcript_locations
                else []
            ),
            author_type=insight.author_type,
            created_at=insight.created_at,
            metadata=insight.metadata,
            created_by_user_id=insight.created_by_user_id,
            updated_at=insight.updated_at,
            updated_by_user_id=insight.updated_by_user_id,
            deleted_at=insight.deleted_at,
            deleted_by_user_id=insight.deleted_by_user_id,
            user_feedback=insight.user_feedback,
            approved_at=insight.approved_at,
            approved_by_user_id=insight.approved_by_user_id,
        )

    @staticmethod
    def transcript_ranges(locations: list[int], max_gap: int) -> list[TranscriptRange]:
        # Sort the locations to ensure they are in ascending order
        locations = sorted(locations)

        # Initialize the ranges list and the first range
        ranges = []
        start = locations[0]
        end = start

        for location in locations[1:]:
            if location - end <= max_gap:
                # Extend the current range
                end = location
            else:
                # Close the current range and start a new one
                ranges.append(TranscriptRange(start=start, end=end))
                start = location
                end = start

        # Append the last range
        ranges.append(TranscriptRange(start=start, end=end))
        return ranges


class MeetingAnnotation(BaseModel):
    id: UUID
    organization_id: UUID
    annotation_type: str
    start_offset: int
    contact_id: UUID | None
    transcript_text: str
    description: str | None
    created_by_user_id: UUID
    updated_by_user_id: UUID | None
    deleted_by_user_id: UUID | None
    created_at: ZoneRequiredDateTime
    updated_at: ZoneRequiredDateTime
    deleted_at: ZoneRequiredDateTime | None

    @classmethod
    def from_db_object(
        cls, meeting_annotation: DbMeetingAnnotation
    ) -> MeetingAnnotation:
        return MeetingAnnotation(**meeting_annotation.dict())


class CreateMeetingAnnotationRequest(BaseModel):
    meeting_id: UUID
    annotation_type: MeetingAnnotationType
    start_offset: int
    transcript_text: str
    description: str | None = None
    contact_id: UUID | None = None


class ListMeetingAnnotationRequest(BaseModel):
    meeting_id: UUID


class PatchMeetingAnnotationRequest(BasePatchRequest):
    contact_id: UnsetAware[UUID]
    description: UnsetAware[str]


class AnalyzeMeetingRequest(BaseModel):
    force_reanalyze: bool = False
    run_async: bool = False


class MeetingStatsResponse(BaseModel):
    talk_ratios: list[MeetingAttendeeTalkRatio]
    longest_user_monologue: MeetingAttendeeMonologue | None
    longest_customer_story: MeetingAttendeeMonologue | None
    interactivity: float | None
    patience: float | None
    organization_id: UUID

    @classmethod
    def from_db_meeting_stats(
        cls, db_meeting_stats: MeetingStats
    ) -> MeetingStatsResponse:
        return cls(
            talk_ratios=db_meeting_stats.talk_ratios,
            longest_user_monologue=db_meeting_stats.longest_user_monologue,
            longest_customer_story=db_meeting_stats.longest_customer_story,
            interactivity=db_meeting_stats.interactivity,
            patience=db_meeting_stats.patience,
            organization_id=db_meeting_stats.organization_id,
        )


class MeetingResearchResponse(BaseModel):
    contact_researches: list[ContactResearchResponse]
    account_researches: list[AccountResearchResponse]

    @classmethod
    def from_meeting_research_dto(
        cls, meeting_research_dto: MeetingResearchDto
    ) -> MeetingResearchResponse:
        return MeetingResearchResponse(
            contact_researches=[
                ContactResearchResponse.from_contact_research_dto(contact_research_dto)
                for contact_research_dto in meeting_research_dto.contact_researches
            ],
            account_researches=[
                AccountResearchResponse.from_account_research_dto(account_research_dto)
                for account_research_dto in meeting_research_dto.account_researches
            ],
        )


class CreateMeetingClipRequest(BaseModel):
    name: str
    start_offset: int
    end_offset: int

    @model_validator(mode="after")
    def validate_clip(self) -> Self:
        if self.end_offset <= self.start_offset:
            raise ValueError(
                "The end offset must be greater than the start offset, "
                f"but found start: {self.start_offset} and end: {self.end_offset}"
            )
        return self


class ListMeetingClipRequest(ListEntityRequest):
    include_archived: bool | None = None


class PatchMeetingClipRequest(BasePatchRequest):
    require_at_least_one_specified_field = True

    name: UnsetAware[str]
    start_offset: UnsetAware[int]
    end_offset: UnsetAware[int]


class MeetingClipResponse(BaseModel):
    id: UUID
    name: str
    meeting_id: UUID
    organization_id: UUID
    start_offset: int
    end_offset: int
    created_by_user_id: UUID
    updated_by_user_id: UUID | None
    deleted_by_user_id: UUID | None
    created_at: ZoneRequiredDateTime
    updated_at: ZoneRequiredDateTime
    deleted_at: ZoneRequiredDateTime | None

    @classmethod
    def from_object(cls, db_meeting_clip: MeetingClip) -> MeetingClipResponse:
        return cls(
            id=db_meeting_clip.id,
            name=db_meeting_clip.name,
            meeting_id=db_meeting_clip.meeting_id,
            organization_id=db_meeting_clip.organization_id,
            start_offset=db_meeting_clip.start_offset,
            end_offset=db_meeting_clip.end_offset,
            created_by_user_id=db_meeting_clip.created_by_user_id,
            updated_by_user_id=db_meeting_clip.updated_by_user_id,
            deleted_by_user_id=db_meeting_clip.deleted_by_user_id,
            created_at=db_meeting_clip.created_at,
            updated_at=db_meeting_clip.updated_at,
            deleted_at=db_meeting_clip.deleted_at,
        )


class CreateMeetingShareRequest(BaseModel):
    meeting_id: UUID
    access_type: MeetingShareAccessType = MeetingShareAccessType.PRIVATE_WITH_VERIFY
    start_offset: int | None = Field(default=None, description="Unit is MS")
    end_offset: int | None = Field(default=None, description="Unit is MS")
    authorized_emails: list[EmailStr] | None
    include_ai_content: bool
    expires_at: ZoneRequiredDateTime
    notify_recipient: bool = False

    @model_validator(mode="after")
    def validate_request(self) -> Self:
        # Only validate offsets if both are provided
        if self.start_offset is not None or self.end_offset is not None:
            # If one is provided, both must be provided
            if self.start_offset is None or self.end_offset is None:
                raise ValueError(
                    "Both start_offset and end_offset must be provided together"
                )

            # Only validate the values if both are provided
            if self.start_offset is not None and self.end_offset is not None:
                if self.start_offset > self.end_offset:
                    raise ValueError("Start offset must be less than end offset")
                if self.start_offset < 0 or self.end_offset < 0:
                    raise ValueError("Start and end offsets must be non-negative")

        if self.expires_at and self.expires_at < zoned_utc_now():
            raise ValueError("Expires at must be in the future")

        # validate max expiration time (180 days)
        if self.expires_at and self.expires_at > zoned_utc_now() + timedelta(days=180):
            raise ValueError("Expires at must be within 180 days")

        if (
            self.access_type == MeetingShareAccessType.PRIVATE_WITH_VERIFY
            and not self.authorized_emails
        ):
            raise ValueError(
                "Authorized emails are required for private shares with verification"
            )
        # lower all authorized emails
        self.authorized_emails = [
            email.lower() for email in self.authorized_emails or []
        ]

        return self


class MeetingShareResponse(BaseModel):
    id: UUID
    meeting_id: UUID
    organization_id: UUID
    access_type: MeetingShareAccessType
    authorized_emails: list[str] | None
    start_offset: int | None
    end_offset: int | None
    include_ai_content: bool
    expires_at: ZoneRequiredDateTime
    created_at: ZoneRequiredDateTime
    updated_at: ZoneRequiredDateTime

    @classmethod
    def from_object(cls, obj: MeetingShare) -> MeetingShareResponse:
        return cls(
            id=obj.id,
            meeting_id=obj.meeting_id,
            organization_id=obj.organization_id,
            access_type=obj.access_type,
            authorized_emails=obj.authorized_emails,
            start_offset=obj.start_offset,
            end_offset=obj.end_offset,
            include_ai_content=obj.include_ai_content,
            expires_at=obj.expires_at,
            created_at=obj.created_at,
            updated_at=obj.updated_at,
        )


class PatchMeetingShareRequest(BasePatchRequest):
    require_at_least_one_specified_field = True
    start_offset: UnsetAware[int | None] = UNSET
    end_offset: UnsetAware[int | None] = UNSET
    authorized_emails: UnsetAware[list[EmailStr]] = UNSET
    include_ai_content: UnsetAware[bool] = UNSET
    expires_at: UnsetAware[ZoneRequiredDateTime] = UNSET

    def _validate_offset_fields(self) -> None:
        if self.start_offset is UNSET and self.end_offset is UNSET:
            return  # Both unset, no validation needed
        if self.start_offset is UNSET or self.end_offset is UNSET:
            raise ValueError(
                "Both start_offset and end_offset must be provided together"
            )
        if self.start_offset is not None and not isinstance(self.start_offset, int):
            raise ValueError("Start offset must be an integer or None")
        if self.end_offset is not None and not isinstance(self.end_offset, int):
            raise ValueError("End offset must be an integer or None")
        if self.start_offset is not None and self.end_offset is not None:
            if self.start_offset > self.end_offset:
                raise ValueError("Start offset must be less than end offset")
            if self.start_offset < 0 or self.end_offset < 0:
                raise ValueError("Start and end offsets must be non-negative or None")

    def _validate_expiration_time(self) -> None:
        if self.expires_at != UNSET:
            expires = self.expires_at
            if isinstance(expires, datetime):
                current_time = zoned_utc_now()
                if expires < current_time:
                    raise ValueError("Expires at must be in the future")
                if expires > current_time + timedelta(days=180):
                    raise ValueError("Expires at must be within 180 days")

    @model_validator(mode="after")
    def validate_request(self) -> Self:
        self._validate_offset_fields()
        self._validate_expiration_time()
        return self


class MeetingInfo(BaseModel):
    id: UUID
    organization_id: UUID
    title: str
    description: str | None
    starts_at: datetime
    ends_at: datetime
    started_at: datetime | None
    ended_at: datetime | None
    meeting_status: MeetingStatus
    pre_signed_media_url: str | None
    pre_signed_vtt_url: str | None
    pre_signed_sprite_url: str | None
    pre_signed_preview_thumbnail_url: str | None
    start_offset: int | None
    end_offset: int | None
    expires_at: ZoneRequiredDateTime | None
    meeting_platform: MeetingProvider | None


class MeetingShareMeetingInfoResponse(BaseModel):
    meeting: MeetingInfo
    transcript: Transcript
    insights: MeetingInsights | None


class ListMeetingShareRequest(ListEntityRequest):
    meeting_id: UUID


class ListMeetingChatMessageRequest(ListEntityRequest):
    pass


class MeetingChatMessageResponse(BaseModel):
    question: str
    answer: str | None
    created_at: ZoneRequiredDateTime
    updated_at: ZoneRequiredDateTime | None = None


class ListMeetingTrackerStatsRequest(ListEntityRequest):
    pass


class ListMeetingTrackerStatsByTrackerIdAndPhraseRequest(ListEntityRequest):
    tracker_id: UUID
    matched_phrase: str | None


class ListMeetingRequest(ListCustomizableEntityRequest):
    include_property_metadata: bool = False


class BotErrorDto(BaseModel):
    error_code: str
    error_desc: str


class QueryMeetingBotStatusResponse(BaseModel):
    meeting_id: UUID
    meeting_bot_status: MeetingBotStatus | None
    bot_error: BotErrorDto | None
    ends_at: ZoneRequiredDateTime | None
    has_active_bot: bool | None
