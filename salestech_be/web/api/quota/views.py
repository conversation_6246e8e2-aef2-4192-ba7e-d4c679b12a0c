from typing import Annotated
from uuid import UUID

from fastapi import Depends

from salestech_be.core.quota.service.quota_policy_service import (
    QuotaPolicyService,
    get_quota_policy_service,
)
from salestech_be.core.quota.service.quota_service import (
    QuotaService,
    get_quota_service,
)
from salestech_be.core.quota.type.quota_policy_type import (
    QuotaCheckResponse,
    QuotaConsumingResource,
    QuotaPeriod,
    QuotaPolicy,
    QuotaSummaryItem,
    UpdateQuotaPolicyRequest,
)
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import require_read_placeholder_access
from salestech_be.web.api.common.container import (
    ListEntityRequest,
    ListResponse,
    PaginatedListResponse,
)
from salestech_be.web.api.filter.logical_filter import (
    paginate_entities,
    sort_entities,
    standard_filter_entities,
)
from salestech_be.web.api_router_ext import <PERSON>e<PERSON><PERSON>outer
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserId,
)

logger = get_logger(__name__)

router = ReeAPIRouter()


@router.get(
    "/policies/{policy_id}",
    response_model=QuotaPolicy,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def get_quota_policies(
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    policy_id: UUID,
    quota_policy_service: Annotated[
        QuotaPolicyService,
        Depends(get_quota_policy_service),
    ],
) -> QuotaPolicy:
    return await quota_policy_service.get_quota_policy(
        policy_id=policy_id,
        organization_id=organization_id,
    )


@router.post(
    "/policies/_list",
    response_model=PaginatedListResponse[QuotaPolicy],
    dependencies=[Depends(require_read_placeholder_access)],
)
async def list_quota_policies(
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    list_quota_policy_request: ListEntityRequest,
    quota_policy_service: Annotated[
        QuotaPolicyService,
        Depends(get_quota_policy_service),
    ],
) -> PaginatedListResponse[QuotaPolicy]:
    all_template_response = await quota_policy_service.list_quota_policies(
        organization_id=organization_id,
    )

    filtered_template_response = standard_filter_entities(
        all_template_response, list_quota_policy_request.filters
    )

    sorted_template_response = sort_entities(
        filtered_template_response, list_quota_policy_request.sorters
    )
    paginated_template_response, response_cursor = paginate_entities(
        sorted_template_response, list_quota_policy_request.cursor
    )

    return PaginatedListResponse[QuotaPolicy](
        list_data=paginated_template_response, cursor=response_cursor
    )


@router.patch(
    "/policies/{policy_id}",
    response_model=QuotaPolicy,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def update_quota_policy(
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    policy_id: UUID,
    api_request: UpdateQuotaPolicyRequest,
    quota_policy_service: Annotated[
        QuotaPolicyService,
        Depends(get_quota_policy_service),
    ],
) -> QuotaPolicy:
    return await quota_policy_service.update_quota_policy(
        user_id=user_id,
        policy_id=policy_id,
        organization_id=organization_id,
        api_request=api_request,
    )


@router.post(
    "/policies/{policy_id}/_reset",
    response_model=QuotaPolicy,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def rest_quota_policy(
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    policy_id: UUID,
    quota_policy_service: Annotated[
        QuotaPolicyService,
        Depends(get_quota_policy_service),
    ],
) -> QuotaPolicy:
    return await quota_policy_service.rest_quota_policy_to_default(
        user_id=user_id,
        policy_id=policy_id,
        organization_id=organization_id,
    )


@router.get(
    "/usages/summary",
    response_model=ListResponse[QuotaSummaryItem],
    dependencies=[Depends(require_read_placeholder_access)],
)
async def get_org_quota_summary(
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    quota_service: Annotated[
        QuotaService,
        Depends(get_quota_service),
    ],
) -> ListResponse[QuotaSummaryItem]:
    quota_summary_list = await quota_service.get_org_quota_summary(
        organization_id=organization_id,
    )
    return ListResponse[QuotaSummaryItem](
        list_data=quota_summary_list,
    )


@router.get(
    "/check_quota",
    response_model=QuotaCheckResponse | list[QuotaCheckResponse],
    dependencies=[Depends(require_read_placeholder_access)],
)
async def check_quota_reached(
    organization_id: AnnotatedReevoOrganizationId,
    quota_service: Annotated[
        QuotaService,
        Depends(get_quota_service),
    ],
    resource: QuotaConsumingResource,
    period: QuotaPeriod = QuotaPeriod.MONTHLY,
    user_id: UUID | None = None,
) -> QuotaCheckResponse | list[QuotaCheckResponse]:
    """Check if a specific quota type has been reached for an organization."""
    """if user_id is not provided, it will check the organization-level quota"""
    # Get quota summary for single user (default behavior)
    usage_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        user_id=user_id,
        resource=resource,
        period=period,
    )
    logger.bind(
        organization_id=organization_id,
        user_id=user_id,
        resource=resource,
        period=period,
        usage_summary=usage_summary,
    ).info("Check quota reached")

    return QuotaCheckResponse(
        organization_id=organization_id,
        user_id=user_id,
        resource=resource,
        period=period,
        reached=usage_summary.total_remaining <= 0,
        total_usage=usage_summary.total_used,
        total_limit=usage_summary.total_limit,
    )
