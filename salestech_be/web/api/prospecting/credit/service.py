from uuid import UUID

from fastapi import Request

from salestech_be.common.lifespan import get_db_engine
from salestech_be.db.dao.quota_repository import (
    QuotaPolicyRepository,
    QuotaUsageRepository,
)
from salestech_be.db.dto.prospecting_dto import ProspectingCreditType
from salestech_be.db.models.prospecting_run import ResourceCredit
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
)
from salestech_be.ree_logging import get_logger
from salestech_be.web.api.prospecting.common.prospecting_common_service import (
    ProspectingCommonService,
    get_prospecting_common_service,
)
from salestech_be.web.api.prospecting.credit.schema import (
    EstimateType,
    GetEstimateCreditRequest,
    ProspectingCreditTypeBalance,
)

logger = get_logger(__name__)


class ProspectingCreditService:
    def __init__(
        self,
        quota_usage_repository: QuotaUsageRepository,
        quota_policy_repository: QuotaPolicyRepository,
        prospecting_common_service: ProspectingCommonService,
    ):
        self.quota_usage_repository = quota_usage_repository
        self.quota_policy_repository = quota_policy_repository
        self.prospecting_common_service = prospecting_common_service

    async def get_credit_type_balance(
        self, organization_id: UUID
    ) -> tuple[int, list[ProspectingCreditTypeBalance]]:
        credit_type_balances: list[ProspectingCreditTypeBalance] = []

        # Filter out the total balance for the organization in current period
        total_balance = await self.quota_policy_repository.get_organization_total_credits_by_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.PROSPECTING_ENRICHMENT,
            entity_id=organization_id,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
        )

        for (
            credit_type,
            resource,
        ) in ProspectingCreditType.to_quota_consuming_resource().items():
            used_credits = await self.quota_usage_repository.get_organization_used_credits_by_resource_and_entity(
                organization_id=organization_id,
                resource=QuotaConsumingResource.PROSPECTING_ENRICHMENT,
                entity_id=organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                applied_sub_resource=resource,
            )

            total_balance -= used_credits
            credit_type_balances.append(
                ProspectingCreditTypeBalance(
                    credit_type=credit_type,
                    current_balance=0,
                    total_credits=0,
                    used_credits=used_credits,
                )
            )

        return total_balance, credit_type_balances

    async def estimate_credit_usage(
        self,
        estimate_request: GetEstimateCreditRequest,
        user_id: UUID,
        organization_id: UUID,
    ) -> list[ResourceCredit]:
        match estimate_request.estimate_type:
            case EstimateType.BULK_IMPORT_PEOPLE:
                # Get properly validated request
                import_people_request = estimate_request.people_bulk_import_request

                if (
                    import_people_request.person_ids
                    and not import_people_request.filter_spec
                ):
                    # enrich people
                    return await self.prospecting_common_service.enrich_people_by_ids_estimate(
                        user_id=user_id,
                        organization_id=organization_id,
                        person_ids=import_people_request.person_ids,
                        enrich_phone_numbers=import_people_request.enrich_phone_numbers
                        or False,
                        sequence_id=import_people_request.add_to_sequence_id,
                        domain_list_ids=import_people_request.add_to_list_ids,
                    )
                else:
                    # bulk import people
                    return await self.prospecting_common_service.bulk_import_people_estimate(
                        user_id=user_id,
                        organization_id=organization_id,
                        bulk_import_people_request=import_people_request,
                    )


def get_prospecting_credit_service(request: Request) -> ProspectingCreditService:
    db_engine = get_db_engine(request=request)
    quota_usage_repository = QuotaUsageRepository(engine=db_engine)
    quota_policy_repository = QuotaPolicyRepository(engine=db_engine)
    prospecting_common_service = get_prospecting_common_service(db_engine=db_engine)
    return ProspectingCreditService(
        quota_usage_repository=quota_usage_repository,
        quota_policy_repository=quota_policy_repository,
        prospecting_common_service=prospecting_common_service,
    )
