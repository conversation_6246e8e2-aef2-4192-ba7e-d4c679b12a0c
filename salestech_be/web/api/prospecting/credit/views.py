from typing import Annotated

from fastapi import Depends

from salestech_be.core.data.service.query_service import DomainObjectQueryService
from salestech_be.core.data.util import ObjectRecordFetchConditions
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import (
    require_admin_access,
    require_create_prospecting_access,
)
from salestech_be.web.api.filter.logical_filter import (
    paginate_object_records,
)
from salestech_be.web.api.prospecting.credit.schema import (
    EstimateResponse,
    GetEstimateCreditRequest,
    ListCreditUsageRequest,
    ListCreditUsageResponse,
    ProspectingCreditBalanceResponse,
)
from salestech_be.web.api.prospecting.credit.service import (
    ProspectingCreditService,
    get_prospecting_credit_service,
)
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.lifespan_service import domain_object_query_service_from_lifespan
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserId,
)

logger = get_logger()

router = ReeAPIRouter()


@router.get(
    "",
    response_model=ProspectingCreditBalanceResponse,
    dependencies=[Depends(require_admin_access)],
)
async def get_prospecting_credit_balance(
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    prospecting_credit_service: Annotated[
        ProspectingCreditService, Depends(get_prospecting_credit_service)
    ],
) -> ProspectingCreditBalanceResponse:
    # TODO: current response structure is not correct!
    (
        total_balance,
        credit_type_balances,
    ) = await prospecting_credit_service.get_credit_type_balance(
        organization_id=organization_id
    )
    return ProspectingCreditBalanceResponse(
        credits=credit_type_balances,
        total_balance=total_balance,
    )


@router.post(
    "/usage/_list",
    response_model=ListCreditUsageResponse,
    dependencies=[Depends(require_admin_access)],
)
async def list_prospecting_credit_usages(
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    list_credit_usage_request: ListCreditUsageRequest,
    domain_object_query_service: Annotated[
        DomainObjectQueryService, Depends(domain_object_query_service_from_lifespan)
    ],
) -> ListCreditUsageResponse:
    (
        credit_usages,
        credit_usage_summary,
    ) = await domain_object_query_service.list_prospecting_credit_usage_records(
        organization_id=organization_id,
        aggregation=list_credit_usage_request.aggregation,
        include_custom_object=False,
        fetch_conditions=ObjectRecordFetchConditions(
            filter_spec=list_credit_usage_request.filter_spec,
            sorting_spec=list_credit_usage_request.sorting_spec,
            fields=list_credit_usage_request.ordered_object_fields,
        ),
    )
    paginated_credit_usages = paginate_object_records(
        object_records=credit_usages,
        cursor=list_credit_usage_request.cursor,
    )
    return ListCreditUsageResponse(
        list_data=paginated_credit_usages.list_data,
        cursor=paginated_credit_usages.cursor,
        summary=credit_usage_summary,
    )


@router.post(
    "/estimate",
    response_model=EstimateResponse,
    dependencies=[Depends(require_create_prospecting_access)],
)
async def estimate_credit_usage(
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    estimate_request: GetEstimateCreditRequest,
    prospecting_credit_service: Annotated[
        ProspectingCreditService, Depends(get_prospecting_credit_service)
    ],
) -> EstimateResponse:
    estimated_credits = await prospecting_credit_service.estimate_credit_usage(
        estimate_request=estimate_request,
        user_id=user_id,
        organization_id=organization_id,
    )
    return EstimateResponse(
        estimated_credits=estimated_credits,
    )
