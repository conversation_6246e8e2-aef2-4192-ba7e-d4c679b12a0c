from enum import StrEnum

from pydantic import BaseModel

from salestech_be.core.data.types import StandardRecord
from salestech_be.core.prospecting.prospecting_credit_query_service import (
    ProspectingCreditUsageSummary,
)
from salestech_be.core.prospecting.type.credit import (
    ProspectingCreditUsagePointV2,
)
from salestech_be.db.dao.quota_repository import ProspectingCreditUsageAggregation
from salestech_be.db.dto.prospecting_dto import ProspectingCreditType
from salestech_be.db.models.prospecting_run import ResourceCredit
from salestech_be.web.api.common.container import (
    ListEntityRequestV2,
    PaginatedListResponse,
)
from salestech_be.web.api.prospecting.people.schema import (
    BulkImportPeopleRequest,
)


class ProspectingCreditTypeBalance(BaseModel):
    credit_type: ProspectingCreditType
    current_balance: int
    total_credits: int
    used_credits: int


class ProspectingCreditBalanceResponse(BaseModel):
    credits: list[ProspectingCreditTypeBalance]
    total_balance: int


class ListCreditUsageRequest(ListEntityRequestV2):
    aggregation: ProspectingCreditUsageAggregation


class ListCreditUsageResponse(
    PaginatedListResponse[StandardRecord[ProspectingCreditUsagePointV2]]
):
    summary: ProspectingCreditUsageSummary


class EstimateType(StrEnum):
    BULK_IMPORT_PEOPLE = "BULK_IMPORT_PEOPLE"


class GetEstimateCreditRequest(BaseModel):
    estimate_type: EstimateType
    people_bulk_import_request: BulkImportPeopleRequest


class EstimateResponse(BaseModel):
    estimated_credits: list[ResourceCredit]
