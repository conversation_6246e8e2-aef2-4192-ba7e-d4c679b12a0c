import json
import math
from typing import Any, cast
from uuid import UUID

from asyncpg import UniqueViolationError
from fastapi import Request
from sqlalchemy.exc import IntegrityError

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ConflictResourceError,
    ErrorDetails,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.query_util.filter_schema import (
    CompositeFilter,
    FilterSpec,
    ValueFilter,
)
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.query_util.sort_schema import _b64_decode, _b64_encode
from salestech_be.common.results import Cursor
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
    StdSelectListIdentifier,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.metadata.schema import <PERSON><PERSON><PERSON>
from salestech_be.core.account.service.account_service import (
    AccountService,
    get_account_service,
)
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.contact.service.contact_service import (
    ContactService,
    get_contact_service,
)
from salestech_be.core.contact.service_api_schema import PatchContactRequest
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.domain_object_list.service.domain_object_list_service import (
    DomainObjectListService,
    get_domain_object_list_service,
)
from salestech_be.core.imports.service.crm_sync_service import (
    CrmSyncService,
    get_crm_sync_service_from_db_engine,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
    get_select_list_service,
)
from salestech_be.core.prospecting.prospecting_quota_service import (
    ProspectingQuotaService,
    get_prospecting_quota_service_by_db_engine,
)
from salestech_be.core.prospecting.type.filter_field import (
    FilterFieldOptionsFacetV2,
    ProspectingFilterFieldType,
)
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    SequenceEnrollmentService,
    get_sequence_enrollment_service_by_db_engine,
)
from salestech_be.core.sequence.type.sequence_enrollment_contact_type import (
    SequenceEnrollmentContact,
)
from salestech_be.db.dao.company_repository import CompanyRepository
from salestech_be.db.dao.pdl_company_repository import PDLCompanyRepository
from salestech_be.db.dao.pdl_person_repository import PDLPersonRepository
from salestech_be.db.dao.person_repository import PersonRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.company_dto import CompanyDto
from salestech_be.db.dto.person_dto import PersonDto
from salestech_be.db.dto.prospecting_dto import (
    EMAIL_ENRICH_CREDITS_PER_ENRICHMENT,
    MAX_NUMBER_OF_ACCOUNT_IN_PEOPLE_SEARCH,
    MOBILE_ENRICH_CREDITS_PER_ENRICHMENT,
    PDL_SEARCH_CREDITS_PER_RECORD,
    AccountSimpleInfo,
    ContactSimpleInfo,
)
from salestech_be.db.models.account import Account
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.domain_object_list import DomainObjectListItemType
from salestech_be.db.models.person import PersonUpdate, ProspectingEnrichStatus
from salestech_be.db.models.prospecting_run import (
    ListEnrollmentResult,
    ProspectingCompanyRunRequest,
    ProspectingEnrichStatusInfo,
    ProspectingEnrollmentStatus,
    ProspectingRun,
    ProspectingRunRequest,
    ProspectingRunStatus,
    ProspectingRunType,
    ProspectingRunUpdate,
    ResourceCredit,
)
from salestech_be.db.models.quota import (
    QuotaConsumingResource,
)
from salestech_be.db.models.search_query import ProspectingSearchQueryType
from salestech_be.db.models.sequence import SequenceEnrollmentContactStatus
from salestech_be.integrations.pdl.model import (
    PDL_SEARCH_MAX_PAGE_SIZE,
    PeopleDataLabsBulkEnrichItem,
    PeopleDataLabsBulkEnrichParams,
    PeopleDataLabsBulkEnrichPersonRequest,
    PeopleDataLabsCompanySearchField,
    PeopleDataLabsEnrichPersonRequest,
    PeopleDataLabsPeopleSearchField,
    PeopleDataLabsPerson,
    PeopleDataLabsPersonResponseStatus,
    PeopleDataLabsSearchCommonRequest,
    PeopleDataLabsSearchCompanyRequest,
    PeopleDataLabsSearchPeopleRequest,
)
from salestech_be.integrations.pdl.pdl_client import PdlClient
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.common.container import (
    ListEntityRequestV2,
)
from salestech_be.web.api.domain_object_list.schema import AddAndRemoveItemsRequest
from salestech_be.web.api.prospecting.common.util import (
    build_range_query,
)
from salestech_be.web.api.prospecting.company.schema import BulkImportCompanyRequest
from salestech_be.web.api.prospecting.people.schema import BulkImportPeopleRequest
from salestech_be.web.api.prospecting.run.service import (
    ProspectingRunService,
    get_prospecting_run_service_from_engine,
)
from salestech_be.web.api.sequence.enrollment.schema import (
    ContactForSequenceEnrollment,
)

logger = get_logger(__name__)


class ProspectingCommonService:
    def __init__(
        self,
        prospecting_quota_service: ProspectingQuotaService,
        prospecting_run_service: ProspectingRunService,
        pdl_client: PdlClient,
        person_repository: PersonRepository,
        company_repository: CompanyRepository,
        pdl_person_repository: PDLPersonRepository,
        pdl_company_repository: PDLCompanyRepository,
        contact_service: ContactService,
        contact_query_service: ContactQueryService,
        account_service: AccountService,
        select_list_service: InternalSelectListService,
        crm_sync_service: CrmSyncService,
        sequence_enrollment_service: SequenceEnrollmentService,
        domain_object_list_service: DomainObjectListService,
    ):
        self.prospecting_quota_service = prospecting_quota_service
        self.prospecting_run_service = prospecting_run_service
        self.pdl_client = pdl_client
        self.person_repository = person_repository
        self.company_repository = company_repository
        self.pdl_person_repository = pdl_person_repository
        self.pdl_company_repository = pdl_company_repository
        self.contact_service = contact_service
        self.contact_query_service = contact_query_service
        self.account_service = account_service
        self.select_list_service = select_list_service
        self.crm_sync_service = crm_sync_service
        self.sequence_enrollment_service = sequence_enrollment_service
        self.domain_object_list_service = domain_object_list_service
        self._MAX_NUMBER_OF_FILTER_IN_PEOPLE_PREVIEW_SEARCH = 1000

    async def bulk_import_people_estimate(
        self,
        user_id: UUID,
        organization_id: UUID,
        bulk_import_people_request: BulkImportPeopleRequest,
    ) -> list[ResourceCredit]:
        # 1. Get existing people from db
        db_person_dto_list: list[PersonDto] = []
        if bulk_import_people_request.person_ids:
            db_person_dto_list = await self.find_person_dto_list_by_person_ids(
                organization_id=organization_id,
                person_ids=bulk_import_people_request.person_ids,
                raise_exception_if_missing=True,
            )

        # 2. Calculate how many more people we need to search for
        need_more = 0
        if (
            bulk_import_people_request.filter_spec
            and len(db_person_dto_list) < bulk_import_people_request.total_number
        ):
            need_more = bulk_import_people_request.total_number - len(
                db_person_dto_list
            )

        # 3. Calculate total estimated credits for both existing and potential new people
        estimated_credits, _ = await self._calculate_estimated_credits(
            person_dto_list=db_person_dto_list,
            enrich_phone_numbers=bool(bulk_import_people_request.enrich_phone_numbers),
            need_more_count=need_more,
        )
        return estimated_credits

    async def bulk_import_people(
        self,
        user_id: UUID,
        organization_id: UUID,
        bulk_import_people_request: BulkImportPeopleRequest,
    ) -> ProspectingRun:
        # 1: get estimated credits
        estimated_credits = await self.bulk_import_people_estimate(
            user_id=user_id,
            organization_id=organization_id,
            bulk_import_people_request=bulk_import_people_request,
        )
        enrichment_estimated_credits = [
            credit
            for credit in estimated_credits
            if credit.resource != QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH
        ]
        search_estimated_credits = [
            credit
            for credit in estimated_credits
            if credit.resource == QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH
        ]
        # 2. Check quota for all estimated credits
        for resource, resource_credits in [
            (
                QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH,
                search_estimated_credits,
            ),
            (
                QuotaConsumingResource.PROSPECTING_ENRICHMENT,
                enrichment_estimated_credits,
            ),
        ]:
            if not resource_credits:
                continue
            await self._check_quota(
                organization_id=organization_id,
                user_id=user_id,
                resource=resource,
                estimated_usage=sum(
                    resource_credit.credit for resource_credit in resource_credits
                ),
            )

        # 3. Create prospecting run - only send enrichment credits, not search credits
        return await self.prospecting_run_service.create_people_run(
            user_id=user_id,
            organization_id=organization_id,
            run_request=ProspectingRunRequest(
                person_ids=bulk_import_people_request.person_ids,
                filter_spec=bulk_import_people_request.filter_spec,
                exclude_person_ids=bulk_import_people_request.exclude_person_ids,
                total_number=bulk_import_people_request.total_number,
                enrich_work_email=True,
                enrich_phone_numbers=bool(
                    bulk_import_people_request.enrich_phone_numbers
                ),
                sequence_id=bulk_import_people_request.add_to_sequence_id,
                list_ids=bulk_import_people_request.add_to_list_ids,
            ),
            run_type=ProspectingRunType.AUTOMATED,
            estimated_credits=enrichment_estimated_credits,
            run_results=None,
        )

    async def _calculate_estimated_credits(
        self,
        person_dto_list: list[PersonDto],
        enrich_phone_numbers: bool,
        need_more_count: int = 0,
    ) -> tuple[list[ResourceCredit], dict[UUID, ProspectingEnrichStatusInfo]]:
        """
        Calculate all estimated credits needed for a list of existing people and potential new people.

        Args:
            person_dto_list: List of existing people to calculate credits for
            enrich_phone_numbers: Whether to enrich phone numbers
            need_more_count: Number of additional people that will be searched

        Returns:
            tuple containing:
            - List of ResourceCredit objects for search, email and phone enrichment
            - Dictionary mapping person IDs to enrichment status info for existing people
        """
        email_enrich_credits = 0
        phone_enrich_credits = 0
        search_credits = 0
        run_results: dict[UUID, ProspectingEnrichStatusInfo] = {}
        estimated_credits: list[ResourceCredit] = []

        # 1. Calculate credits for existing people
        for person_dto in person_dto_list:
            person = person_dto.db_person

            # Track email enrichment credits
            if person.work_email_enrich_status == ProspectingEnrichStatus.NOT_REQUESTED:
                email_enrich_credits += EMAIL_ENRICH_CREDITS_PER_ENRICHMENT

            # Track phone enrichment credits if requested
            if (
                enrich_phone_numbers
                and person.phone_number_enrich_status
                == ProspectingEnrichStatus.NOT_REQUESTED
            ):
                phone_enrich_credits += MOBILE_ENRICH_CREDITS_PER_ENRICHMENT

            # Initialize run results for this person
            run_results[person.id] = ProspectingEnrichStatusInfo(
                email_enrich_status=person.work_email_enrich_status
                or ProspectingEnrichStatus.NOT_REQUESTED,
                phone_enrich_status=person.phone_number_enrich_status
                or ProspectingEnrichStatus.NOT_REQUESTED,
            )

        # 2. Calculate credits for search and potential new people
        if need_more_count > 0:
            # Search credits
            search_credits = PDL_SEARCH_CREDITS_PER_RECORD * need_more_count

            # All new people will need email enrichment
            email_enrich_credits += (
                EMAIL_ENRICH_CREDITS_PER_ENRICHMENT * need_more_count
            )

            # Add phone enrichment credits if requested
            if enrich_phone_numbers:
                phone_enrich_credits += (
                    MOBILE_ENRICH_CREDITS_PER_ENRICHMENT * need_more_count
                )

        # 3. Build credit requirement list
        if search_credits > 0:
            estimated_credits.append(
                ResourceCredit(
                    resource=QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH,
                    credit=search_credits,
                )
            )

        if email_enrich_credits > 0:
            estimated_credits.append(
                ResourceCredit(
                    resource=QuotaConsumingResource.PROSPECTING_EMAIL_ENRICHMENT,
                    credit=email_enrich_credits,
                )
            )

        if phone_enrich_credits > 0:
            estimated_credits.append(
                ResourceCredit(
                    resource=QuotaConsumingResource.PROSPECTING_PHONE_NUMBER_ENRICHMENT,
                    credit=phone_enrich_credits,
                )
            )

        return estimated_credits, run_results

    async def enrich_people_by_ids_estimate(
        self,
        person_ids: list[UUID],
        user_id: UUID,
        organization_id: UUID,
        enrich_phone_numbers: bool,
        sequence_id: UUID | None = None,
        domain_list_ids: list[UUID] | None = None,
    ) -> list[ResourceCredit]:
        # 1. Find people by IDs
        person_dto_list = await self.find_person_dto_list_by_person_ids(
            person_ids=person_ids,
            organization_id=organization_id,
            raise_exception_if_missing=True,
        )

        # 2. Calculate estimated enrichment credits using shared method
        estimated_credits, _ = await self._calculate_estimated_credits(
            person_dto_list=person_dto_list,
            enrich_phone_numbers=enrich_phone_numbers,
            need_more_count=0,  # No search needed for enrich_people_by_ids
        )

        return estimated_credits

    async def enrich_people_by_ids(
        self,
        person_ids: list[UUID],
        user_id: UUID,
        organization_id: UUID,
        enrich_phone_numbers: bool,
        sequence_id: UUID | None = None,
        domain_list_ids: list[UUID] | None = None,
    ) -> list[PersonDto]:
        # 1. Find people by IDs
        person_dto_list = await self.find_person_dto_list_by_person_ids(
            person_ids=person_ids,
            organization_id=organization_id,
            raise_exception_if_missing=True,
        )

        # 2. Calculate estimated enrichment credits using shared method
        estimated_credits, run_results = await self._calculate_estimated_credits(
            person_dto_list=person_dto_list,
            enrich_phone_numbers=enrich_phone_numbers,
            need_more_count=0,  # No search needed for enrich_people_by_ids
        )

        # 3. Check quota for all estimated credits
        if estimated_credits:
            await self._check_quota(
                organization_id=organization_id,
                user_id=user_id,
                resource=QuotaConsumingResource.PROSPECTING_ENRICHMENT,
                estimated_usage=sum(credit.credit for credit in estimated_credits),
            )

        # 4. Create prospecting run with enrichment credits
        prospecting_run = await self.prospecting_run_service.create_people_run(
            user_id=user_id,
            organization_id=organization_id,
            run_request=ProspectingRunRequest(
                person_ids=person_ids,
                total_number=len(person_ids),
                enrich_work_email=True,
                enrich_phone_numbers=enrich_phone_numbers,
                list_ids=domain_list_ids,
                sequence_id=sequence_id,
                filter_spec=None,
            ),
            run_type=ProspectingRunType.MANUAL,
            estimated_credits=estimated_credits,
            run_results=run_results,
        )

        enriched_people: list[PersonDto] = []
        try:
            # 5. Enrich people
            enriched_people = await self.enrich_people_with_run(
                person_dto_list=person_dto_list,
                prospecting_run=prospecting_run,
                enrich_phone_numbers=enrich_phone_numbers,
                organization_id=organization_id,
                user_id=user_id,
            )

            # 6. Process people workflow
            await self._process_people_workflow(
                enriched_people=enriched_people,
                user_id=user_id,
                organization_id=organization_id,
                prospecting_run=prospecting_run,
                sequence_id=sequence_id,
                domain_list_ids=domain_list_ids,
            )
        except Exception as e:
            logger.bind(
                organization_id=organization_id,
                user_id=user_id,
                person_ids=person_ids,
                enrich_phone_numbers=enrich_phone_numbers,
                exc_info=e,
            ).error("Failed to enrich people")
            await self.prospecting_run_service.update_run(
                prospecting_run_id=prospecting_run.id,
                organization_id=organization_id,
                prospecting_run_update=ProspectingRunUpdate(
                    status=ProspectingRunStatus.FAILED,
                    ends_at=zoned_utc_now(),
                    error_info=str(e),
                ),
            )
        return enriched_people

    async def _process_people_workflow(
        self,
        enriched_people: list[PersonDto],
        user_id: UUID,
        organization_id: UUID,
        prospecting_run: ProspectingRun,
        sequence_id: UUID | None = None,
        domain_list_ids: list[UUID] | None = None,
    ) -> None:
        # 1. Convert to contacts and update run
        contact_list = await self.convert_people_to_contacts(
            person_dto_list=enriched_people,
            prospecting_run_id=prospecting_run.id,
            organization_id=organization_id,
            user_id=user_id,
        )

        # 2. Add to sequences if needed
        if contact_list and sequence_id:
            await self.add_contacts_to_sequence(
                contact_simple_list=not_none(contact_list),
                sequence_id=not_none(sequence_id),
                prospecting_run_id=prospecting_run.id,
                organization_id=organization_id,
                user_id=user_id,
            )

        # 3. Add to domain lists if needed
        if contact_list and domain_list_ids:
            await self.add_contacts_to_domain_lists(
                contact_simple_list=not_none(contact_list),
                domain_list_ids=not_none(domain_list_ids),
                prospecting_run_id=prospecting_run.id,
                organization_id=organization_id,
                user_id=user_id,
            )

        # 4. Mark run as completed
        await self.mark_run_as_completed(
            prospecting_run_id=prospecting_run.id,
            organization_id=organization_id,
        )

    async def search_additional_people(
        self,
        user_id: UUID,
        organization_id: UUID,
        filter_spec: FilterSpec,
        count_needed: int,
        exclude_person_ids: list[UUID] | None = None,
    ) -> list[PersonDto]:
        result_people: list[PersonDto] = []
        cursor = Cursor()

        while len(result_people) < count_needed:
            # Calculate how many to request in this batch
            batch_size = min(
                PDL_SEARCH_MAX_PAGE_SIZE, count_needed - len(result_people)
            )
            requested_page_token = (
                list(cursor.page_tokens.values())[-1] if cursor.page_tokens else None
            )
            page_index = (
                list(cursor.page_tokens.keys())[-1] if cursor.page_tokens else 1
            )
            logger.bind(
                organization_id=organization_id,
                user_id=user_id,
                filter_spec=filter_spec,
                requested_page_token=requested_page_token,
                page_index=page_index,
            ).info(f"Searching for people in page {page_index}")
            # Search for people
            searched_person_dto_list, next_cursor = await self.search_people(
                list_people_request=ListEntityRequestV2(
                    cursor=cursor.model_copy(
                        update={
                            "page_size": batch_size,
                            "page_index": page_index,
                            "requested_page_token": requested_page_token,
                        }
                    ),
                    filter_spec=filter_spec,
                ),
                user_id=user_id,
                organization_id=organization_id,
                skip_quota_check=True,
                exclude_person_ids=exclude_person_ids,
            )
            logger.bind(
                organization_id=organization_id,
                user_id=user_id,
                filter_spec=filter_spec,
                requested_page_token=requested_page_token,
            ).info(
                f"Found {len(searched_person_dto_list)} additional people for page {page_index}"
            )
            # Add found people to results
            result_people += searched_person_dto_list

            # Prepare for next iteration or exit
            if not next_cursor.has_more:
                break
            cursor = next_cursor

        return result_people

    async def enrich_people_with_run(
        self,
        person_dto_list: list[PersonDto],
        prospecting_run: ProspectingRun,
        enrich_phone_numbers: bool,
        organization_id: UUID,
        user_id: UUID,
    ) -> list[PersonDto]:
        logger.bind(
            organization_id=organization_id,
            prospecting_run_id=prospecting_run.id,
            person_count=len(person_dto_list),
            enrich_phone_numbers=enrich_phone_numbers,
        ).info("Enriching people with run")
        # Initialize credit tracker
        credit_tracker = {
            QuotaConsumingResource.PROSPECTING_EMAIL_ENRICHMENT: 0,
            QuotaConsumingResource.PROSPECTING_PHONE_NUMBER_ENRICHMENT: 0,
        }

        # Separate people by enrichment status
        person_dto_enriched = [
            p for p in person_dto_list if p.db_person.last_enriched_at
        ]
        person_dto_not_enriched = [
            p for p in person_dto_list if not p.db_person.last_enriched_at
        ]

        # Process already enriched people
        results_for_already_enriched_people = (
            await self._process_already_enriched_people(
                person_dto_enriched=person_dto_enriched,
                enrich_phone_numbers=enrich_phone_numbers,
                organization_id=organization_id,
                prospecting_run=prospecting_run,
                credit_tracker=credit_tracker,
            )
        )

        # Process non-enriched people
        results_for_non_enriched_people = await self._process_non_enriched_people(
            person_dto_not_enriched=person_dto_not_enriched,
            enrich_phone_numbers=enrich_phone_numbers,
            organization_id=organization_id,
            user_id=user_id,
            prospecting_run=prospecting_run,
            credit_tracker=credit_tracker,
        )

        # Add actual credits used to credit tracker
        # Calculate actual credits used
        actual_credits = [
            ResourceCredit(resource=resource, credit=credit)
            for resource, credit in credit_tracker.items()
            if credit > 0
        ]
        # insert new usage
        for credit in actual_credits:
            await self.prospecting_quota_service.insert_quota_usage(
                organization_id=organization_id,
                user_id=user_id,
                resource=QuotaConsumingResource.PROSPECTING_ENRICHMENT,
                usage=credit.credit,
                applied_sub_resource=credit.resource,
            )
        # update prospecting run
        await self.prospecting_run_service.update_run(
            prospecting_run_id=prospecting_run.id,
            organization_id=organization_id,
            prospecting_run_update=ProspectingRunUpdate(
                actual_credits=actual_credits,
                ends_at=zoned_utc_now(),
            ),
        )
        return results_for_already_enriched_people + results_for_non_enriched_people

    async def _process_already_enriched_people(
        self,
        person_dto_enriched: list[PersonDto],
        enrich_phone_numbers: bool,
        organization_id: UUID,
        prospecting_run: ProspectingRun,
        credit_tracker: dict[QuotaConsumingResource, int],
    ) -> list[PersonDto]:
        """Process people that are already enriched."""
        # Early return if no people to process
        if not person_dto_enriched:
            return []

        # If not enriching phone numbers, return as-is
        if not enrich_phone_numbers:
            return person_dto_enriched

        results: list[PersonDto] = []
        to_update_person_ids: list[UUID] = []

        # Separate already enriched or no phone numbers from those needing update
        for person_dto in person_dto_enriched:
            if (
                person_dto.db_person.phone_number_enrich_status
                == ProspectingEnrichStatus.ENRICHED
            ) or (not person_dto.db_person.phone_numbers):
                # Already enriched or no phone numbers - no update needed
                results.append(
                    PersonDto(
                        db_person=person_dto.db_person, db_company=person_dto.db_company
                    )
                )
            else:
                # Needs update
                credit_tracker[
                    QuotaConsumingResource.PROSPECTING_PHONE_NUMBER_ENRICHMENT
                ] += MOBILE_ENRICH_CREDITS_PER_ENRICHMENT

                updated_db_person = await self.person_repository.update_by_id(
                    person_id=person_dto.db_person.id,
                    organization_id=organization_id,
                    person_update=PersonUpdate(
                        phone_number_enrich_status=ProspectingEnrichStatus.ENRICHED
                    ),
                )
                results.append(
                    PersonDto(
                        db_person=updated_db_person, db_company=person_dto.db_company
                    )
                )
                to_update_person_ids.append(person_dto.db_person.id)

        # Update run results for all processed people
        if to_update_person_ids:
            await self.prospecting_run_service.bulk_update_run_results(
                prospecting_run_id=prospecting_run.id,
                person_ids=to_update_person_ids,
                organization_id=organization_id,
                phone_enrichment_status=ProspectingEnrichStatus.ENRICHED,
            )

        return results

    async def _process_non_enriched_people(
        self,
        person_dto_not_enriched: list[PersonDto],
        enrich_phone_numbers: bool,
        organization_id: UUID,
        user_id: UUID,
        prospecting_run: ProspectingRun,
        credit_tracker: dict[QuotaConsumingResource, int],
    ) -> list[PersonDto]:
        """Process people that are not yet enriched."""
        if not person_dto_not_enriched:
            return []

        # Find PDL person for those not enriched
        pdl_person_list = await self.pdl_person_repository.list_by_person_ids(
            person_ids=[
                person_dto.db_person.id for person_dto in person_dto_not_enriched
            ],
            organization_id=organization_id,
        )
        logger.bind(
            person_dto_count=len(person_dto_not_enriched),
            pdl_person_count=len(pdl_person_list),
        ).info("process_non_enriched_people")
        # Build the SQL query with proper parameter handling
        pdl_ext_ids = [pdl_person.ext_id for pdl_person in pdl_person_list]
        if not pdl_ext_ids:
            logger.bind(
                organization_id=organization_id,
                user_id=user_id,
            ).warning("No PDL persons found for enrichment")
            return []

        people_data_labs_person_list = await self._enrich_people_with_pdl(
            pdl_ext_ids=pdl_ext_ids
        )
        if len(people_data_labs_person_list) != len(pdl_ext_ids):
            logger.bind(
                organization_id=organization_id,
                user_id=user_id,
                prospecting_run_id=prospecting_run.id,
                pdl_ext_ids=pdl_ext_ids,
            ).warning(
                f"No enough enrichment results found. Expected {len(pdl_ext_ids)} results, but only got {len(people_data_labs_person_list)}"
            )

        # Update person and pdl_person
        updated_person_dto_list = (
            await self.person_repository.update_db_person_and_pdl_person(
                people_data_labs_person_list=people_data_labs_person_list,
                enrich_phone_numbers=enrich_phone_numbers,
                user_id=user_id,
                organization_id=organization_id,
            )
        )
        for person_dto in updated_person_dto_list:
            email_enrichment_status = ProspectingEnrichStatus.NOT_REQUESTED
            phone_enrichment_status = ProspectingEnrichStatus.NOT_REQUESTED
            if person_dto.db_person.work_email:
                credit_tracker[QuotaConsumingResource.PROSPECTING_EMAIL_ENRICHMENT] += (
                    EMAIL_ENRICH_CREDITS_PER_ENRICHMENT
                )
                email_enrichment_status = ProspectingEnrichStatus.ENRICHED
            if person_dto.db_person.phone_numbers and enrich_phone_numbers:
                credit_tracker[
                    QuotaConsumingResource.PROSPECTING_PHONE_NUMBER_ENRICHMENT
                ] += MOBILE_ENRICH_CREDITS_PER_ENRICHMENT
                phone_enrichment_status = ProspectingEnrichStatus.ENRICHED

            # Update run results
            await self.prospecting_run_service.bulk_update_run_results(
                prospecting_run_id=prospecting_run.id,
                person_ids=[person_dto.db_person.id],
                organization_id=organization_id,
                email_enrichment_status=email_enrichment_status,
                phone_enrichment_status=phone_enrichment_status,
            )

        return updated_person_dto_list

    async def convert_people_to_contacts(
        self,
        person_dto_list: list[PersonDto],
        organization_id: UUID,
        user_id: UUID,
        prospecting_run_id: UUID | None = None,
    ) -> list[ContactSimpleInfo]:
        # Create contacts
        contact_simple_list: list[ContactSimpleInfo] = []
        create_account_request_list: list[CreateAccountRequest] = [
            person_dto.to_create_account_request(owner_user_id=user_id)
            for person_dto in person_dto_list
            if person_dto.db_company
        ]
        account_v2_result_map = await self.convert_to_accounts(
            create_account_request_list=create_account_request_list,
            organization_id=organization_id,
            user_id=user_id,
        )
        existing_contacts = await self.contact_service.list_by_person_ids(
            person_ids=[person_dto.db_person.id for person_dto in person_dto_list],
            organization_id=organization_id,
        )
        existing_contact_map = {
            contact.person_id: contact for contact in existing_contacts
        }
        for person_dto in person_dto_list:
            # skip if contact already exists
            if person_dto.db_person.id in existing_contact_map:
                contact = existing_contact_map[person_dto.db_person.id]
            else:
                created_contact = await self._get_or_create_contact(
                    person_dto=person_dto,
                    organization_id=organization_id,
                    user_id=user_id,
                    primary_account_id=(
                        account_v2_result_map[person_dto.db_company.id]
                        if (
                            person_dto.db_company
                            and person_dto.db_company.id in account_v2_result_map
                        )
                        else None
                    ),
                )
                if not created_contact:
                    continue
                contact = created_contact
            # Extract only the essential information
            contact_simple = ContactSimpleInfo(
                id=contact.id,
                person_id=not_none(contact.person_id),
                primary_account_id=contact.primary_account_id,
                primary_email=contact.primary_email,
            )
            contact_simple_list.append(contact_simple)

        # Update prospecting run with contact IDs if provided
        if prospecting_run_id:
            await self.update_run_with_contact_ids(
                prospecting_run_id=prospecting_run_id,
                organization_id=organization_id,
                contact_simple_list=contact_simple_list,
            )

        return contact_simple_list

    async def _get_or_create_contact(
        self,
        person_dto: PersonDto,
        organization_id: UUID,
        user_id: UUID,
        primary_account_id: UUID | None = None,
    ) -> ContactV2 | None:
        default_stage_list = await self.select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        domain_contact: ContactV2 | None = None
        try:
            if (
                not default_stage_list
                or not default_stage_list.default_or_initial_active_value
            ):
                raise InvalidArgumentError(
                    "Stage is required for creating a contact, however "
                    "the organization does not have a default stage set."
                )
            default_value = default_stage_list.default_or_initial_active_value
            domain_contact = await self.contact_service.create_contact_with_contact_channels(
                organization_id=organization_id,
                user_id=user_id,
                create_contact_with_contact_channel_request=person_dto.to_create_contact_with_contact_channel_request(
                    owner_user_id=user_id,
                    stage_id=default_value.id,
                    primary_account_id=primary_account_id,
                ),
            )
        except (ConflictResourceError, UniqueViolationError, IntegrityError):
            domain_contact_map = (
                await self.contact_service.find_contacts_by_primary_emails(
                    organization_id=organization_id,
                    primary_emails={not_none(person_dto.db_person.work_email)},
                )
            )
            domain_contact = domain_contact_map.get(
                not_none(person_dto.db_person.work_email)
            )
            if not domain_contact:
                logger.bind(
                    organization_id=organization_id,
                    email=person_dto.db_person.work_email,
                ).warning(
                    f"[get_or_create_contact] Contact not found for email {person_dto.db_person.work_email}"
                )
            # update contact without person_id
            if domain_contact and not domain_contact.person_id:
                domain_contact = await self.contact_service.patch_by_id(
                    organization_id=organization_id,
                    contact_id=domain_contact.id,
                    user_id=user_id,
                    request=PatchContactRequest(
                        person_id=person_dto.db_person.id,
                    ),
                )
            logger.bind(
                organization_id=organization_id,
                email=person_dto.db_person.work_email,
            ).info(
                "[get_or_create_account_and_contact] Contact with email already exists"
            )
        except Exception as e:
            logger.bind(
                organization_id=organization_id,
                email=person_dto.db_person.work_email,
                exc_info=e,
            ).error(
                f"[get_or_create_contact] Error creating contact for email {person_dto.db_person.work_email}"
            )
        return domain_contact

    async def update_run_with_contact_ids(
        self,
        prospecting_run_id: UUID,
        organization_id: UUID,
        contact_simple_list: list[ContactSimpleInfo],
    ) -> None:
        await self.prospecting_run_service.bulk_set_contact_ids_for_run_results(
            prospecting_run_id=prospecting_run_id,
            organization_id=organization_id,
            person_contact_id_map={
                contact.person_id: contact.id
                for contact in contact_simple_list
                if contact.person_id
            },
        )

    async def add_contacts_to_sequence(  # noqa: C901
        self,
        contact_simple_list: list[ContactSimpleInfo],
        sequence_id: UUID,
        prospecting_run_id: UUID,
        organization_id: UUID,
        user_id: UUID,
    ) -> None:
        logger.bind(
            organization_id=organization_id,
            prospecting_run_id=prospecting_run_id,
            sequence_id=sequence_id,
            contact_count=len(contact_simple_list),
        ).info("Adding contacts to sequence")

        # 1. Categorize contacts
        contacts_with_account: list[ContactSimpleInfo] = []
        contacts_without_account: list[ContactSimpleInfo] = []

        for contact in contact_simple_list:
            if contact.primary_account_id:
                contacts_with_account.append(contact)
            else:
                contacts_without_account.append(contact)

        # 2. create a mapping from contact_id to person_id
        contact_id_to_person_id = {
            contact.id: contact.person_id for contact in contact_simple_list
        }

        # 3.1 Process contacts without accounts - mark all sequences as rejected
        for contact in contacts_without_account:
            await self.prospecting_run_service.bulk_update_run_results(
                prospecting_run_id=prospecting_run_id,
                organization_id=organization_id,
                person_ids=[contact.person_id],
                sequence_enrollment_rejection_reason="Contact does not have a primary account",
                sequence_enrollment_status=ProspectingEnrollmentStatus.rejected,
            )

        # 3.2 Process contacts with accounts
        if not contacts_with_account:
            logger.bind(
                organization_id=organization_id,
                prospecting_run_id=prospecting_run_id,
            ).info("No contacts with accounts to enroll in sequences")

            return

        create_enrollment_run_response = await self.sequence_enrollment_service.create_sequence_enrollment_run_and_enrollments_from_contacts(
            user_id=user_id,
            organization_id=organization_id,
            contacts=[
                ContactForSequenceEnrollment(
                    contact_id=contact.id,
                    account_id=not_none(contact.primary_account_id),
                    email=contact.primary_email,
                )
                for contact in contacts_with_account
            ],
            sequence_id=sequence_id,
            bypass_warnings=False,
        )

        # Gather enrollments by status
        failed_enrollments: list[SequenceEnrollmentContact] = []
        warning_enrollments: list[SequenceEnrollmentContact] = []
        successful_enrollments: list[SequenceEnrollmentContact] = []

        for enrollment_contact in create_enrollment_run_response.enrollment_contacts:
            if enrollment_contact.status == SequenceEnrollmentContactStatus.FAILED:
                failed_enrollments.append(enrollment_contact)
            elif enrollment_contact.status == SequenceEnrollmentContactStatus.WARNING:
                warning_enrollments.append(enrollment_contact)
            else:
                successful_enrollments.append(enrollment_contact)

        # Process enrollments
        if successful_enrollments:
            await self.prospecting_run_service.bulk_update_run_results(
                prospecting_run_id=prospecting_run_id,
                organization_id=organization_id,
                person_ids=[
                    contact_id_to_person_id[contact.contact_id]
                    for contact in successful_enrollments
                ],
                sequence_enrollment_status=ProspectingEnrollmentStatus.enrolled,
            )

        for warning_enrollment in warning_enrollments:
            await self.prospecting_run_service.bulk_update_run_results(
                prospecting_run_id=prospecting_run_id,
                organization_id=organization_id,
                person_ids=[contact_id_to_person_id[warning_enrollment.contact_id]],
                sequence_enrollment_status=ProspectingEnrollmentStatus.rejected,
                sequence_enrollment_rejection_reason=",".join(
                    warning_enrollment.warning_reasons
                )
                if warning_enrollment.warning_reasons
                else "",
            )

        for failed_enrollment in failed_enrollments:
            await self.prospecting_run_service.bulk_update_run_results(
                prospecting_run_id=prospecting_run_id,
                organization_id=organization_id,
                person_ids=[contact_id_to_person_id[failed_enrollment.contact_id]],
                sequence_enrollment_status=ProspectingEnrollmentStatus.rejected,
                sequence_enrollment_rejection_reason=",".join(
                    failed_enrollment.fail_reasons
                )
                if failed_enrollment.fail_reasons
                else "",
            )

        logger.bind(
            organization_id=organization_id,
            prospecting_run_id=prospecting_run_id,
        ).info(
            "Completed adding contacts to sequences",
        )

    async def add_contacts_to_domain_lists(
        self,
        contact_simple_list: list[ContactSimpleInfo],
        domain_list_ids: list[UUID],
        prospecting_run_id: UUID,
        organization_id: UUID,
        user_id: UUID,
    ) -> None:
        contact_ids = [contact.id for contact in contact_simple_list]
        logger.bind(
            organization_id=organization_id,
            prospecting_run_id=prospecting_run_id,
            domain_list_ids=domain_list_ids,
            contact_count=len(contact_ids),
        ).info(
            "Adding contacts to domain lists",
        )

        # Create a mapping from contact_id to person_id
        contact_id_to_person_id = {
            contact.id: contact.person_id for contact in contact_simple_list
        }

        # Track enrollment results for each contact
        enrollment_results: dict[UUID, list[ListEnrollmentResult]] = {
            contact.id: [] for contact in contact_simple_list
        }

        # 1. Add contacts to each domain list
        for domain_list_id in domain_list_ids:
            results = await self.crm_sync_service.domain_object_list_service.add_and_remove_items(
                request=AddAndRemoveItemsRequest(
                    item_ids_to_add=set(contact_ids),
                    item_ids_to_remove=None,
                ),
                user_id=user_id,
                organization_id=organization_id,
                list_id=domain_list_id,
            )

            # Skip if results is None
            if results is None:
                logger.warning(
                    f"Failed to add contacts to domain list {domain_list_id}",
                    domain_list_id=domain_list_id,
                )
                continue

            # Get set of failed contact IDs (if any)
            failed_item_set = set()
            if results.failed_items is not None:
                failed_item_set = {item.id for item in results.failed_items}

            # Process each contact
            for contact_id in contact_ids:
                if contact_id not in failed_item_set:
                    # Contact was successfully added
                    enrollment_results[contact_id].append(
                        ListEnrollmentResult(
                            list_id=domain_list_id,
                            status=ProspectingEnrollmentStatus.enrolled,
                            rejection_reason=None,
                        )
                    )
                else:
                    # Contact failed to be added
                    error_message = "Unknown error"
                    if results.failed_items is not None:
                        error_message = next(
                            (
                                item.reason
                                for item in results.failed_items
                                if item.id == contact_id
                            ),
                            error_message,
                        )

                    enrollment_results[contact_id].append(
                        ListEnrollmentResult(
                            list_id=domain_list_id,
                            status=ProspectingEnrollmentStatus.rejected,
                            rejection_reason=error_message,
                        )
                    )

        # 2. Update prospecting_run_result for each contact
        for contact_id, list_results in enrollment_results.items():
            if list_results:
                await self.prospecting_run_service.bulk_update_run_results(
                    prospecting_run_id=prospecting_run_id,
                    organization_id=organization_id,
                    person_ids=[contact_id_to_person_id[contact_id]],
                    list_enrollment_results=list_results,
                )

        logger.bind(
            organization_id=organization_id,
            prospecting_run_id=prospecting_run_id,
        ).info(
            "Completed adding contacts to domain lists",
        )

    def _extract_value_filters(  # noqa: C901
        self, filter_obj: CompositeFilter | ValueFilter
    ) -> list[tuple[ValueFilter, str]]:
        """
        Recursively extracts all ValueFilter objects from a CompositeFilter structure
        with their logical relationship context.

        Args:
            filter_obj: CompositeFilter or ValueFilter object

        Returns:
            List of tuples containing ValueFilter objects and their logical context
            ("all_of", "any_of", or "none_of")
        """
        result: list[tuple[ValueFilter, str]] = []

        # If filter_obj is a ValueFilter, directly return it with default "all_of" logic
        if isinstance(filter_obj, ValueFilter):
            return [(filter_obj, "all_of")]

        # Process all_of filters (AND logic)
        for sub_filter in filter_obj.all_of:
            if isinstance(sub_filter, ValueFilter):
                result.append((sub_filter, "all_of"))
            elif isinstance(sub_filter, CompositeFilter):
                # When recursing into nested CompositeFilters, preserve the top-level logic
                nested_filters = self._extract_value_filters(sub_filter)
                result.extend(
                    [(filter_val, "all_of") for filter_val, _ in nested_filters]
                )

        # Process any_of filters (OR logic) - mark as any_of but will be skipped in search params
        for sub_filter in filter_obj.any_of:
            if isinstance(sub_filter, ValueFilter):
                result.append((sub_filter, "any_of"))
            elif isinstance(sub_filter, CompositeFilter):
                nested_filters = self._extract_value_filters(sub_filter)
                result.extend(
                    [(filter_val, "any_of") for filter_val, _ in nested_filters]
                )

        # Process none_of filters (NOT logic) - mark as none_of but will be skipped in search params
        for sub_filter in filter_obj.none_of:
            if isinstance(sub_filter, ValueFilter):
                result.append((sub_filter, "none_of"))
            elif isinstance(sub_filter, CompositeFilter):
                nested_filters = self._extract_value_filters(sub_filter)
                result.extend(
                    [(filter_val, "none_of") for filter_val, _ in nested_filters]
                )

        return result

    async def build_pdl_common_search_request(  # noqa: C901, PLR0912, PLR0915
        self,
        user_id: UUID,
        organization_id: UUID,
        list_request: ListEntityRequestV2,
        search_query_type: ProspectingSearchQueryType,
        exclude_person_ids: list[UUID] | None = None,
        exclude_company_ids: list[UUID] | None = None,
    ) -> tuple[int, PeopleDataLabsSearchCommonRequest]:
        if not list_request.filter_spec or not list_request.filter_spec.filter:
            raise ValueError("Filter specification is required.")

        query_params: list[dict[str, Any]] = []  # type: ignore[explicit-any]
        composite_filter = cast(CompositeFilter, list_request.filter_spec.filter)

        # Extract all value filters with their logical context
        value_filters_with_logic = self._extract_value_filters(composite_filter)

        # Process only the all_of filters, skip any_of and none_of with logging
        non_all_of_fields = set()

        # Group filters by field type to combine company_ids and account_ids filters
        company_ids_values: list[  # type: ignore[explicit-any]
            tuple[ProspectingFilterFieldType, list[Any], MatchOperator]
        ] = []
        other_filters: list[  # type: ignore[explicit-any]
            tuple[ProspectingFilterFieldType, list[Any], MatchOperator]
        ] = []
        contact_ids_values: list[  # type: ignore[explicit-any]
            tuple[ProspectingFilterFieldType, list[Any], MatchOperator]
        ] = []
        # First pass: group filters by field type
        for value_filter, logic_type in value_filters_with_logic:
            if (
                not isinstance(value_filter.field, QualifiedField)
                or not value_filter.field.path
                or not isinstance(value_filter.value, set)
            ):
                continue

            field_name = str(value_filter.field.path[0])
            # Skip filters that aren't all_of logic
            if logic_type != "all_of":
                non_all_of_fields.add(field_name)
                continue

            field_type = ProspectingFilterFieldType(field_name)
            filter_value = list(value_filter.value)

            # Group by filter field type - both company_ids and account_ids are handled together
            if field_type in (
                ProspectingFilterFieldType.company_ids,
                ProspectingFilterFieldType.account_ids,
                ProspectingFilterFieldType.domain_object_list_account_ids,
            ):
                company_ids_values.append(
                    (field_type, filter_value, value_filter.operator)
                )
            elif (
                field_type
                in (
                    ProspectingFilterFieldType.domain_object_list_contact_ids,
                    ProspectingFilterFieldType.sequence_ids,
                )
                and search_query_type == ProspectingSearchQueryType.PEOPLE
            ):
                # Only add contact_ids to the query if the operator is NIN and search_query_type is PEOPLE
                if value_filter.operator == MatchOperator.NIN:
                    contact_ids_values.append(
                        (field_type, filter_value, value_filter.operator)
                    )
            else:
                # Keep track of other field types for normal processing
                other_filters.append((field_type, filter_value, value_filter.operator))

        if (
            search_query_type == ProspectingSearchQueryType.PEOPLE
            and exclude_person_ids
        ):
            ext_ids = await self._convert_person_ids_to_pdl_ext_ids(
                person_ids=exclude_person_ids,
                organization_id=organization_id,
            )
            other_filters.append(
                (ProspectingFilterFieldType.person_id, ext_ids, MatchOperator.NIN)
            )

        if (
            search_query_type == ProspectingSearchQueryType.COMPANY
            and exclude_company_ids
        ):
            ext_ids = await self._convert_company_ids_to_pdl_ext_ids(
                field_type=ProspectingFilterFieldType.company_id,
                filter_value=exclude_company_ids,
                user_id=user_id,
                organization_id=organization_id,
            )
            other_filters.append(
                (ProspectingFilterFieldType.company_id, ext_ids, MatchOperator.NIN)
            )

        # Process company_ids and account_ids filters - convert and combine into queries
        if company_ids_values:
            # Separate filters by operator (IN vs NOT IN)
            mapping: dict[MatchOperator, list[str]] = {
                MatchOperator.IN: [],
                MatchOperator.NIN: [],
            }

            # Process all filters and collect converted IDs by operator
            for field_type, filter_value, operator in company_ids_values:
                # Convert to PDL external IDs - with proper validation
                valid_uuids = [v for v in filter_value if isinstance(v, UUID)]
                if not valid_uuids:
                    continue

                converted_ids = await self._convert_company_ids_to_pdl_ext_ids(
                    field_type=field_type,
                    filter_value=valid_uuids,
                    user_id=user_id,
                    organization_id=organization_id,
                )

                # Add to the appropriate operator collection
                if converted_ids:
                    mapping[operator].extend(converted_ids)

            # Add the aggregated filters to the query_params directly
            for operator, converted_ids in mapping.items():
                other_filters.append(
                    (ProspectingFilterFieldType.company_ids, converted_ids, operator)
                )

        # Process contact_list_ids and sequence_ids filters
        must_not_filters: list[dict[str, Any]] = []  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if contact_ids_values:
            for field_type, filter_value, _operator in contact_ids_values:
                # Convert to PDL external IDs - with proper validation
                valid_uuids = [v for v in filter_value if isinstance(v, UUID)]
                if not valid_uuids:
                    continue

                await self._convert_contact_ids_to_must_not_filters(
                    field_type=field_type,
                    filter_value=valid_uuids,
                    organization_id=organization_id,
                    must_not_filters=must_not_filters,
                )
        # Process all other filters normally
        for field_type, filter_value, operator in other_filters:
            # Skip if filter value is empty
            if not filter_value:
                continue

            # Get the PDL search field
            if not isinstance(field_type, ProspectingFilterFieldType):
                raise ValueError(f"Invalid field type: {field_type}")

            search_field = FilterFieldOptionsFacetV2.to_pdl_search_field(
                filter_field_type=field_type,
                search_query_type=search_query_type,
            )

            # Handle normal filter cases
            if operator == MatchOperator.IN:
                # Handle range fields
                if search_field in (
                    PeopleDataLabsCompanySearchField.employee_growth_rate_12_month,
                    PeopleDataLabsCompanySearchField.last_funding_date,
                ):
                    # For range field, we need special ES range query
                    if not filter_value:
                        continue

                    # Use the enhanced build_range_query function
                    range_query = build_range_query(search_field, filter_value)

                    # Skip if no valid query could be built
                    if range_query is None:
                        continue

                    query_params.append(range_query)  # type: ignore # TODO: fix-any-annotation
                # use match query for fuzzy matching
                elif search_field in (
                    PeopleDataLabsPeopleSearchField.summary,
                    PeopleDataLabsCompanySearchField.summary,
                ):
                    for value in filter_value:
                        query_params.append({"match": {search_field: value}})
                # use match query for fuzzy matching for keyword fields with text field
                elif search_field == PeopleDataLabsPeopleSearchField.job_title:
                    query_params.append(
                        {
                            "bool": {
                                "should": [
                                    {"match": {f"{search_field}.text": value}}
                                    for value in filter_value
                                ]
                            }
                        }
                    )
                else:
                    query_params.append({"terms": {search_field: filter_value}})
            # NIN filters
            elif operator == MatchOperator.NIN:
                if search_field == PeopleDataLabsPeopleSearchField.job_title:
                    query_params.append(
                        {
                            "bool": {
                                "must_not": [
                                    {"match": {f"{search_field}.text": value}}
                                    for value in filter_value
                                ]
                            }
                        }
                    )
                else:
                    query_params.append(
                        {"bool": {"must_not": {"terms": {search_field: filter_value}}}}
                    )

        # Log skipped non-all_of fields
        if non_all_of_fields:
            logger.warning(
                f"Skipped non-all_of filters for fields: {', '.join(non_all_of_fields)}. "
                "Currently only 'all_of' (AND) logic is supported."
            )

        # Construct Elasticsearch query
        must_filters = [
            {"bool": {"must": query_params}},
        ]
        # add job_company_id must exist condition if search_query_type is PEOPLE
        if search_query_type == ProspectingSearchQueryType.PEOPLE:
            must_filters.append({"exists": {"field": "job_company_id"}})  # type: ignore

        es_filters = {"must": must_filters}
        if search_query_type == ProspectingSearchQueryType.PEOPLE:
            if must_not_filters:
                es_filters["must_not"] = must_not_filters
            # Ensure at least one of work_email or phone_numbers exists for people search
            es_filters["must"].append(
                {
                    "bool": {
                        "should": [
                            {"exists": {"field": "work_email"}},
                            {"exists": {"field": "phone_numbers"}},
                        ]
                    }
                }
            )

        es_query = json.dumps({"bool": es_filters})
        page_index, page_size, scroll_token = self.parse_requested_page_token(
            cursor=list_request.cursor
        )
        return page_index, PeopleDataLabsSearchCommonRequest(
            query=es_query,
            size=page_size,
            scroll_token=scroll_token,
        )

    async def _convert_person_ids_to_pdl_ext_ids(
        self,
        person_ids: list[UUID],
        organization_id: UUID,
    ) -> list[str]:
        pdl_persons = await self.pdl_person_repository.list_by_person_ids(
            person_ids=person_ids,
            organization_id=organization_id,
        )
        return [pdl_person.ext_id for pdl_person in pdl_persons]

    async def _convert_company_ids_to_pdl_ext_ids(
        self,
        field_type: ProspectingFilterFieldType,
        filter_value: list[UUID],
        user_id: UUID,
        organization_id: UUID,
    ) -> list[str]:
        # CASE 1: Handle direct company ID conversion
        match field_type:
            case (
                ProspectingFilterFieldType.company_ids
                | ProspectingFilterFieldType.company_id
            ):
                pdl_companies = await self.pdl_company_repository.list_by_company_ids(
                    company_ids=filter_value,
                    organization_id=organization_id,
                )
                return [pdl_company.ext_id for pdl_company in pdl_companies]
            case (
                ProspectingFilterFieldType.domain_object_list_account_ids
                | ProspectingFilterFieldType.account_ids
            ):
                if (
                    field_type
                    == ProspectingFilterFieldType.domain_object_list_account_ids
                ):
                    domain_object_list_items = await self.domain_object_list_service.get_domain_object_list_items_by_domain_object_list_ids(
                        organization_id=organization_id,
                        domain_object_list_ids=filter_value,
                        reference_type=DomainObjectListItemType.ACCOUNT,
                    )
                    account_ids = [
                        item.reference_id for item in domain_object_list_items
                    ]
                else:
                    account_ids = filter_value

                # Check if the number of account ids exceeds the maximum limit
                if len(account_ids) > MAX_NUMBER_OF_ACCOUNT_IN_PEOPLE_SEARCH:
                    raise ValueError(
                        f"Number of account ids exceeds the maximum limit of {MAX_NUMBER_OF_ACCOUNT_IN_PEOPLE_SEARCH}"
                    )

                # Get all account information
                db_account_list = (
                    await self.account_service.list_accounts_by_ids_untenanted(
                        account_ids=account_ids,
                        exclude_deleted_or_archived=False,
                    )
                )

                # Collect accounts with company IDs
                company_ids = [
                    db_account.company_id
                    for db_account in db_account_list
                    if db_account.company_id
                ]

                # Collect accounts with domains but no company IDs
                domains = [
                    db_account.domain_name
                    for db_account in db_account_list
                    if db_account.domain_name and not db_account.company_id
                ]

                # If we have domains, search for associated companies
                if domains:
                    # Search companies by domain
                    company_dto_list, _ = await self.search_company(
                        list_company_request=ListEntityRequestV2(
                            filter_spec=FilterSpec(
                                primary_object_identifier=StdObjectIdentifiers.company.identifier,
                                filter=CompositeFilter(
                                    all_of=[
                                        ValueFilter(
                                            field=QualifiedField(
                                                path=(
                                                    ProspectingFilterFieldType.website.value,
                                                )
                                            ),
                                            value=set(domains),
                                            operator=MatchOperator.IN,
                                        )
                                    ]
                                ),
                            ),
                            cursor=Cursor(
                                page_size=len(domains),
                            ),
                        ),
                        user_id=user_id,
                        # This parameter is not important here but needed
                        organization_id=organization_id,
                    )

                    # Add found company IDs
                    company_ids.extend(
                        [company_dto.db_company.id for company_dto in company_dto_list]
                    )
                # If no company IDs found, return empty list
                if not company_ids:
                    return []

                # Convert company IDs to PDL external IDs
                pdl_companies = await self.pdl_company_repository.list_by_company_ids(
                    company_ids=company_ids,
                    organization_id=organization_id,
                )
                return [pdl_company.ext_id for pdl_company in pdl_companies]
            case _:
                # Default case, return empty list
                return []

    async def _process_contacts_with_person_id_to_should_filters(
        self,
        contacts_with_person_id: list[Contact],
        organization_id: UUID,
        should_filters: list[dict[str, dict[str, list[str]]]],
    ) -> None:
        """Process contacts that have person_id"""
        if not contacts_with_person_id:
            return

        person_ids = [
            not_none(contact.person_id) for contact in contacts_with_person_id
        ]
        pdl_persons = await self.pdl_person_repository.list_by_person_ids(
            person_ids=person_ids,
            organization_id=organization_id,
        )

        pdl_person_ids = [pdl_person.ext_id for pdl_person in pdl_persons]
        if pdl_person_ids:
            if (
                len(pdl_person_ids)
                > self._MAX_NUMBER_OF_FILTER_IN_PEOPLE_PREVIEW_SEARCH
            ):
                logger.warning(
                    f"Number of person ids exceeds the maximum limit of {self._MAX_NUMBER_OF_FILTER_IN_PEOPLE_PREVIEW_SEARCH}"
                )
                pdl_person_ids = pdl_person_ids[
                    : self._MAX_NUMBER_OF_FILTER_IN_PEOPLE_PREVIEW_SEARCH
                ]

            should_filters.append({"terms": {"id": pdl_person_ids}})

    async def _process_contacts_without_person_id_to_should_filters(
        self,
        contacts_without_person_id: list[Contact],
        should_filters: list[dict[str, dict[str, list[str]]]],
    ) -> None:
        """Process contacts that don't have person_id"""
        if not contacts_without_person_id:
            return

        # Build filters for each attribute type
        filter_attrs = {
            "work_email": {
                c.primary_email for c in contacts_without_person_id if c.primary_email
            },
            "linkedin_url": {
                c.linkedin_url for c in contacts_without_person_id if c.linkedin_url
            },
        }

        # Add each non-empty attribute filter
        for field_name, values in filter_attrs.items():
            if values:
                _values = list(values)
                if len(_values) > self._MAX_NUMBER_OF_FILTER_IN_PEOPLE_PREVIEW_SEARCH:
                    logger.warning(
                        f"Number of {field_name} exceeds the maximum limit of {self._MAX_NUMBER_OF_FILTER_IN_PEOPLE_PREVIEW_SEARCH}"
                    )
                    _values = _values[
                        : self._MAX_NUMBER_OF_FILTER_IN_PEOPLE_PREVIEW_SEARCH
                    ]

                should_filters.append({"terms": {field_name: _values}})

    async def _convert_contact_ids_to_must_not_filters(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        field_type: ProspectingFilterFieldType,
        filter_value: list[UUID],
        organization_id: UUID,
        must_not_filters: list[dict[str, Any]],
    ) -> None:
        """Convert contact IDs to PDL filters for excluding existing contacts"""
        # Collect all contact IDs based on field type
        contact_ids = []
        if field_type == ProspectingFilterFieldType.domain_object_list_contact_ids:
            domain_object_list_items = await self.domain_object_list_service.get_domain_object_list_items_by_domain_object_list_ids(
                organization_id=organization_id,
                domain_object_list_ids=filter_value,
                reference_type=DomainObjectListItemType.CONTACT,
            )
            contact_ids += [item.reference_id for item in domain_object_list_items]

        # Add sequence-based contact IDs if applicable
        if field_type == ProspectingFilterFieldType.sequence_ids:
            contact_ids += await self._fetch_contact_ids_by_sequence_ids(
                sequence_ids=filter_value,
                organization_id=organization_id,
            )

        if not contact_ids:
            return

        # Fetch all contacts at once
        contacts = await self.contact_query_service.list_by_ids(
            contact_ids=list(set(contact_ids)),
            organization_id=organization_id,
        )

        # Split contacts into those with and without person_ids
        contacts_with_person_id = []
        contacts_without_person_id = []

        for contact in contacts:
            if contact.person_id:
                contacts_with_person_id.append(contact)
            else:
                contacts_without_person_id.append(contact)
        should_filters: list[dict[str, dict[str, list[str]]]] = []
        # Process contacts with person_ids
        await self._process_contacts_with_person_id_to_should_filters(
            contacts_with_person_id=contacts_with_person_id,
            organization_id=organization_id,
            should_filters=should_filters,
        )

        # Process contacts without person_ids
        await self._process_contacts_without_person_id_to_should_filters(
            contacts_without_person_id=contacts_without_person_id,
            should_filters=should_filters,
        )

        if should_filters:
            must_not_filters.append({"bool": {"should": should_filters}})

    async def _fetch_contact_ids_by_sequence_ids(
        self,
        sequence_ids: list[UUID],
        organization_id: UUID,
    ) -> list[UUID]:
        """
        fetch contact_ids from sequence_enrollment by sequence_ids and organization_id
        """
        contact_ids: list[UUID] = []
        for sequence_id in sequence_ids:
            sequence_enrollments = await self.sequence_enrollment_service.sequence_enrollment_query_service.list_sequence_enrollments(
                organization_id=organization_id,
                sequence_id=sequence_id,
            )
            contact_ids += [
                enrollment.contact_id for enrollment in sequence_enrollments
            ]
        return contact_ids

    def parse_requested_page_token(
        self, cursor: Cursor | None
    ) -> tuple[int, int, str | None]:
        # Handle None cursor case
        if not cursor:
            return 1, PDL_SEARCH_MAX_PAGE_SIZE, None

        # Extract the requested page token
        requested_page_token = cursor.requested_page_token
        if not requested_page_token:
            # No token provided, use defaults
            return 1, cursor.page_size or PDL_SEARCH_MAX_PAGE_SIZE, None

        # Decode the token
        try:
            parsed_page_token = json.loads(_b64_decode(requested_page_token))
            # Extract values with proper defaults
            page_index = int(parsed_page_token.get("page_index", 1))
            page_size = cursor.page_size or PDL_SEARCH_MAX_PAGE_SIZE
            scroll_token = parsed_page_token.get("scroll_token")
            return page_index, page_size, scroll_token
        except Exception as e:
            # Fallback for unexpected errors
            raise ValueError(f"Unexpected error parsing page token: {e!s}")

    def generate_next_cursor(
        self,
        page_index: int,
        page_size: int,
        scroll_token: str | None,
        total_number: int,
    ) -> Cursor:
        # Calculate next page index
        next_page_index = page_index + 1

        # Prepare token data
        token_data = {
            "page_index": next_page_index,
            "page_size": page_size,
            "scroll_token": scroll_token,
        }

        # Create and return cursor
        has_more = bool(scroll_token) and total_number > page_index * page_size
        page_tokens = (
            {
                next_page_index: _b64_encode(json.dumps(token_data)),
            }
            if has_more
            else None
        )

        return Cursor(
            page_index=page_index,  # current page index
            page_size=page_size,
            page_tokens=page_tokens,  # next page token
            has_more=has_more,
            total_number=total_number,
        )

    async def search_people(
        self,
        list_people_request: ListEntityRequestV2,
        user_id: UUID,
        organization_id: UUID,
        skip_quota_check: bool = False,
        exclude_person_ids: list[UUID] | None = None,
    ) -> tuple[list[PersonDto], Cursor]:
        # Check quota before performing search
        if not skip_quota_check:
            await self._check_quota(
                organization_id=organization_id,
                user_id=user_id,
                resource=QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH,
                estimated_usage=PDL_SEARCH_CREDITS_PER_RECORD
                * (
                    list_people_request.cursor.page_size
                    if list_people_request.cursor
                    else PDL_SEARCH_MAX_PAGE_SIZE
                ),
            )

        # Proceed with search
        (
            page_index,
            pdl_search_common_request,
        ) = await self.build_pdl_common_search_request(
            user_id=user_id,
            organization_id=organization_id,
            list_request=list_people_request,
            search_query_type=ProspectingSearchQueryType.PEOPLE,
            exclude_person_ids=exclude_person_ids,
        )

        pdl_search_people_response = await self.pdl_client.preview_search_people(
            pdl_search_people_request=PeopleDataLabsSearchPeopleRequest.from_common_request(
                pdl_search_common_request
            )
        )

        person_dto_list = []
        for pdl_preview_person in pdl_search_people_response.data:
            person_dto = (
                await self.person_repository.upsert_person_with_pdl_preview_person(
                    user_id=user_id,
                    organization_id=organization_id,
                    pdl_preview_person=pdl_preview_person,
                )
            )
            person_dto_list.append(person_dto)

        cursor = self.generate_next_cursor(
            page_index=page_index,
            page_size=pdl_search_common_request.size,
            scroll_token=pdl_search_people_response.scroll_token,
            total_number=pdl_search_people_response.total,
        )

        # Update quota usage after successful search
        await self.prospecting_quota_service.insert_quota_usage(
            organization_id=organization_id,
            user_id=user_id,
            resource=QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH,
            usage=PDL_SEARCH_CREDITS_PER_RECORD * len(person_dto_list),
        )
        return person_dto_list, cursor

    async def _enrich_people_with_pdl(
        self,
        pdl_ext_ids: list[str],
    ) -> list[PeopleDataLabsPerson]:
        if not pdl_ext_ids:
            return []

        people_data_labs_person_list: list[PeopleDataLabsPerson] = []
        total_batches = math.ceil(len(pdl_ext_ids) / PDL_SEARCH_MAX_PAGE_SIZE)

        for i in range(total_batches):
            pdl_ext_ids_batch = pdl_ext_ids[
                i * PDL_SEARCH_MAX_PAGE_SIZE : (i + 1) * PDL_SEARCH_MAX_PAGE_SIZE
            ]

            if len(pdl_ext_ids_batch) == 1:
                person_response = await self.pdl_client.enrich_person(
                    pdl_enrich_person_request=PeopleDataLabsEnrichPersonRequest(
                        pdl_id=pdl_ext_ids_batch[0]
                    )
                )
                if (
                    person_response.status
                    == PeopleDataLabsPersonResponseStatus.SUCCESS.value
                ):
                    people_data_labs_person_list.append(not_none(person_response.data))
            else:
                response = await self.pdl_client.bulk_enrich_person(
                    pdl_bulk_enrich_person_request=PeopleDataLabsBulkEnrichPersonRequest(
                        requests=[
                            PeopleDataLabsBulkEnrichItem(
                                params=PeopleDataLabsBulkEnrichParams(pdl_id=pdl_ext_id)
                            )
                            for pdl_ext_id in pdl_ext_ids_batch
                        ]
                    )
                )
                people_data_labs_person_list += [
                    not_none(person_response.data)
                    for person_response in response.list_data
                    if person_response.status
                    == PeopleDataLabsPersonResponseStatus.SUCCESS.value
                ]

        return people_data_labs_person_list

    async def find_person_dto_list_by_person_ids(
        self,
        organization_id: UUID,
        person_ids: list[UUID],
        raise_exception_if_missing: bool = False,
    ) -> list[PersonDto]:
        db_person_list = await self.person_repository.list_by_ids(
            ids=person_ids,
            organization_id=organization_id,
        )

        # Check for missing person IDs
        missing_person_ids: list[str] = [
            str(person_id)
            for person_id in person_ids
            if person_id not in [db_person.id for db_person in db_person_list]
        ]

        # Raise error if missing any and raise_exception_if_missing is True
        if missing_person_ids and raise_exception_if_missing:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"People {', '.join(missing_person_ids)} not found.",
                )
            )

        # Get company information for each person
        db_company_list = await self.company_repository.list_by_ids(
            organization_id=organization_id,
            ids=[
                db_person.company_id
                for db_person in db_person_list
                if db_person.company_id
            ],
        )

        # Build and return person DTOs with company information
        return [
            PersonDto(
                db_person=db_person,
                db_company=next(
                    (
                        db_company
                        for db_company in db_company_list
                        if db_person.company_id
                        and db_company.id == db_person.company_id
                    ),
                    None,
                ),
            )
            for db_person in db_person_list
        ]

    async def _check_quota(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        resource: QuotaConsumingResource,
        estimated_usage: int,
    ) -> None:
        await self.prospecting_quota_service.check_quota_limit_exceeded(
            organization_id=organization_id,
            user_id=user_id,
            resource=resource,
            estimated_usage=estimated_usage,
        )

    async def mark_run_as_completed(
        self,
        prospecting_run_id: UUID,
        organization_id: UUID,
    ) -> None:
        await self.prospecting_run_service.update_run(
            prospecting_run_id=prospecting_run_id,
            organization_id=organization_id,
            prospecting_run_update=ProspectingRunUpdate(
                status=ProspectingRunStatus.COMPLETED,
                ends_at=zoned_utc_now(),
            ),
        )

    async def search_company(
        self,
        list_company_request: ListEntityRequestV2,
        user_id: UUID,
        organization_id: UUID,
        exclude_company_ids: list[UUID] | None = None,
    ) -> tuple[list[CompanyDto], Cursor]:
        # Check quota before performing search
        await self._check_quota(
            organization_id=organization_id,
            user_id=user_id,
            resource=QuotaConsumingResource.PROSPECTING_COMPANY_SEARCH,
            estimated_usage=PDL_SEARCH_CREDITS_PER_RECORD
            * (
                list_company_request.cursor.page_size
                if list_company_request.cursor
                else PDL_SEARCH_MAX_PAGE_SIZE
            ),
        )

        # Proceed with search
        (
            page_index,
            pdl_search_common_request,
        ) = await self.build_pdl_common_search_request(
            user_id=user_id,
            organization_id=organization_id,
            list_request=list_company_request,
            search_query_type=ProspectingSearchQueryType.COMPANY,
            exclude_company_ids=exclude_company_ids,
        )

        pdl_search_company_response = await self.pdl_client.search_company(
            pdl_search_company_request=PeopleDataLabsSearchCompanyRequest.from_common_request(
                pdl_search_common_request
            )
        )

        company_dto_list = (
            await self.company_repository.update_db_company_and_pdl_company(
                user_id=user_id,
                organization_id=organization_id,
                people_data_labs_company_list=pdl_search_company_response.data,
            )
        )

        cursor = self.generate_next_cursor(
            page_index=page_index,
            page_size=pdl_search_common_request.size,
            scroll_token=pdl_search_company_response.scroll_token,
            total_number=pdl_search_company_response.total,
        )

        # Update quota usage after successful search
        await self.prospecting_quota_service.insert_quota_usage(
            organization_id=organization_id,
            user_id=user_id,
            resource=QuotaConsumingResource.PROSPECTING_COMPANY_SEARCH,
            usage=PDL_SEARCH_CREDITS_PER_RECORD * len(company_dto_list),
        )

        return company_dto_list, cursor

    async def find_company_dto_list_by_company_ids(
        self,
        organization_id: UUID,
        company_ids: list[UUID],
        raise_exception_if_missing: bool = True,
    ) -> list[CompanyDto]:
        db_company_list = await self.company_repository.list_by_ids(
            ids=company_ids,
            organization_id=organization_id,
        )

        # Check for missing company IDs
        missing_company_ids: list[str] = [
            str(company_id)
            for company_id in company_ids
            if company_id not in [db_company.id for db_company in db_company_list]
        ]

        # Raise error if missing any and raise_exception_if_missing is True
        if missing_company_ids and raise_exception_if_missing:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"Company {', '.join(missing_company_ids)} not found.",
                )
            )

        # Build and return company DTOs
        return [
            CompanyDto(
                db_company=db_company,
            )
            for db_company in db_company_list
        ]

    async def _search_additional_company(
        self,
        user_id: UUID,
        organization_id: UUID,
        filter_spec: FilterSpec,
        count_needed: int,
        exclude_company_ids: list[UUID] | None = None,
    ) -> list[CompanyDto]:
        result_company: list[CompanyDto] = []
        cursor = Cursor()

        while len(result_company) < count_needed:
            # Calculate how many to request in this batch
            batch_size = min(
                PDL_SEARCH_MAX_PAGE_SIZE, count_needed - len(result_company)
            )
            requested_page_token = (
                list(cursor.page_tokens.values())[-1] if cursor.page_tokens else None
            )
            page_index = (
                list(cursor.page_tokens.keys())[-1] if cursor.page_tokens else 1
            )

            # Search for company
            searched_company_dto_list, next_cursor = await self.search_company(
                list_company_request=ListEntityRequestV2(
                    cursor=cursor.model_copy(
                        update={
                            "page_size": batch_size,
                            "page_index": page_index,
                            "requested_page_token": requested_page_token,
                        }
                    ),
                    filter_spec=filter_spec,
                ),
                user_id=user_id,
                organization_id=organization_id,
                exclude_company_ids=exclude_company_ids,
            )
            logger.bind(
                organization_id=organization_id,
                user_id=user_id,
                filter_spec=filter_spec,
                requested_page_token=requested_page_token,
            ).info(
                f"Found {len(searched_company_dto_list)} companies for page {page_index}"
            )
            # Add found company to results
            result_company += searched_company_dto_list

            # Prepare for next iteration or exit
            if not next_cursor.has_more:
                break

        return result_company

    async def bulk_import_company(
        self,
        user_id: UUID,
        organization_id: UUID,
        bulk_import_company_request: BulkImportCompanyRequest,
    ) -> tuple[list[UUID], ProspectingRun]:
        # 1. Get existing companies from db
        db_company_dto_list: list[CompanyDto] = []
        if bulk_import_company_request.company_ids:
            db_company_dto_list = await self.find_company_dto_list_by_company_ids(
                organization_id=organization_id,
                company_ids=bulk_import_company_request.company_ids,
                raise_exception_if_missing=True,
            )

        # 2. Calculate how many more companies we need to search for
        need_more = 0
        if (
            bulk_import_company_request.filter_spec
            and len(db_company_dto_list) < bulk_import_company_request.total_number
        ):
            need_more = bulk_import_company_request.total_number - len(
                db_company_dto_list
            )

        # 3. Calculate total estimated search credits
        search_estimated_credits: list[ResourceCredit] = []
        if need_more > 0:
            # Search credits
            search_credits = PDL_SEARCH_CREDITS_PER_RECORD * need_more
            search_estimated_credits.append(
                ResourceCredit(
                    resource=QuotaConsumingResource.PROSPECTING_COMPANY_SEARCH,
                    credit=search_credits,
                )
            )

        # 4. Check quota for search estimated credits
        if search_estimated_credits:
            await self._check_quota(
                organization_id=organization_id,
                user_id=user_id,
                resource=QuotaConsumingResource.PROSPECTING_COMPANY_SEARCH,
                estimated_usage=sum(
                    resource_credit.credit
                    for resource_credit in search_estimated_credits
                ),
            )

        # 5. Find/search companies
        all_company_dto_list = db_company_dto_list

        # Search for more companies if needed
        if need_more > 0:
            additional_companies = await self._search_additional_company(
                user_id=user_id,
                organization_id=organization_id,
                filter_spec=not_none(bulk_import_company_request.filter_spec),
                count_needed=need_more,
                exclude_company_ids=bulk_import_company_request.exclude_company_ids,
            )

            # Add newly found companies to the list
            all_company_dto_list += additional_companies

        # 6. Verify we found enough companies
        if len(all_company_dto_list) < bulk_import_company_request.total_number:
            logger.bind(
                organization_id=organization_id,
                user_id=user_id,
                bulk_import_company_request=bulk_import_company_request,
            ).warning(
                f"Not enough companies found. Requested {bulk_import_company_request.total_number}, found {len(all_company_dto_list)}"
            )

        # 7. Create prospecting run
        prospecting_run = await self.create_prospecting_run_for_company(
            user_id=user_id,
            organization_id=organization_id,
            company_dto_list=all_company_dto_list,
            prospecting_run_request=ProspectingCompanyRunRequest(
                company_ids=bulk_import_company_request.company_ids,
                filter_spec=bulk_import_company_request.filter_spec,
                total_number=bulk_import_company_request.total_number,
                list_ids=bulk_import_company_request.add_to_list_ids,
                exclude_company_ids=bulk_import_company_request.exclude_company_ids,
            ),
            estimated_credits=search_estimated_credits,
        )
        return [
            company_dto.db_company.id for company_dto in all_company_dto_list
        ], prospecting_run

    async def create_prospecting_run_for_company(
        self,
        *,
        user_id: UUID,
        organization_id: UUID,
        company_dto_list: list[CompanyDto],
        prospecting_run_request: ProspectingCompanyRunRequest,
        estimated_credits: list[ResourceCredit],
    ) -> ProspectingRun:
        run_results: dict[UUID, ProspectingEnrichStatusInfo] = {
            company_dto.db_company.id: ProspectingEnrichStatusInfo(
                email_enrich_status=ProspectingEnrichStatus.NOT_REQUESTED,
                phone_enrich_status=ProspectingEnrichStatus.NOT_REQUESTED,
            )
            for company_dto in company_dto_list
        }
        # Create the run
        return await self.prospecting_run_service.create_company_run(
            user_id=user_id,
            organization_id=organization_id,
            run_request=prospecting_run_request,
            run_type=ProspectingRunType.MANUAL,
            estimated_credits=estimated_credits,
            run_results=run_results,
        )

    async def convert_company_to_accounts(
        self,
        company_dto_list: list[CompanyDto],
        organization_id: UUID,
        user_id: UUID,
        prospecting_run_id: UUID | None = None,
    ) -> list[AccountSimpleInfo]:
        account_v2_result_map = await self.convert_to_accounts(
            create_account_request_list=[
                company_dto.to_create_account_request(owner_user_id=user_id)
                for company_dto in company_dto_list
            ],
            organization_id=organization_id,
            user_id=user_id,
        )
        # account_simple_list, construct from account_v2_result_map
        account_simple_list: list[AccountSimpleInfo] = []
        for company_id, account_v2_id in account_v2_result_map.items():
            account_simple = AccountSimpleInfo(
                id=account_v2_id,
                company_id=company_id,
            )
            account_simple_list.append(account_simple)
        # Update prospecting run with contact IDs if provided
        if prospecting_run_id:
            await self.update_run_with_account_ids(
                prospecting_run_id=prospecting_run_id,
                organization_id=organization_id,
                account_simple_list=account_simple_list,
            )

        return account_simple_list

    async def update_run_with_account_ids(
        self,
        prospecting_run_id: UUID,
        organization_id: UUID,
        account_simple_list: list[AccountSimpleInfo],
    ) -> None:
        await self.prospecting_run_service.bulk_set_account_ids_for_run_results(
            prospecting_run_id=prospecting_run_id,
            organization_id=organization_id,
            company_account_id_map={
                account_simple.company_id: account_simple.id
                for account_simple in account_simple_list
            },
        )

    async def convert_to_accounts(
        self,
        create_account_request_list: list[CreateAccountRequest],
        organization_id: UUID,
        user_id: UUID,
    ) -> dict[UUID, UUID]:
        """
        Convert create account request list to account v2 result map of company id to account v2 id
        """
        # get or create account by official website if provided
        create_account_request_by_official_website: list[CreateAccountRequest] = [
            create_account_request
            for create_account_request in create_account_request_list
            if create_account_request.company_id is not None
            and create_account_request.has_official_website()
        ]
        create_account_request_by_company_id: list[CreateAccountRequest] = [
            create_account_request
            for create_account_request in create_account_request_list
            if create_account_request.company_id is not None
            and not create_account_request.has_official_website()
        ]
        account_v2_result_map: dict[UUID, UUID] = {}
        if create_account_request_by_official_website:
            for create_account_request in create_account_request_by_official_website:
                try:
                    account_v2_result_map[
                        not_none(create_account_request.company_id)
                    ] = (
                        await self.account_service.get_or_create_account(
                            create_account_request=create_account_request,
                            organization_id=organization_id,
                            user_id=user_id,
                        )
                    ).id
                except ConflictResourceError as e:
                    logger.bind(
                        organization_id=organization_id,
                        user_id=user_id,
                        create_account_request=create_account_request,
                        e=e.additional_error_details,
                    ).warning(
                        "Conflict resource error when getting or creating account",
                    )
                    continue
        if create_account_request_by_company_id:
            account_list = await self.account_service.list_account_by_company_ids(
                organization_id=organization_id,
                company_ids=[
                    not_none(create_account_request.company_id)
                    for create_account_request in create_account_request_by_company_id
                ],
            )
            accounts_from_company_ids_map: dict[UUID, list[Account]] = {
                not_none(account.company_id): [] for account in account_list
            }
            for account in account_list:
                accounts_from_company_ids_map[not_none(account.company_id)].append(
                    account
                )
            # for company id found in account list, return account v2
            # for company id not found in account list, create account
            for create_account_request in create_account_request_by_company_id:
                accounts = accounts_from_company_ids_map.get(
                    not_none(create_account_request.company_id)
                )
                if not accounts:
                    try:
                        account_v2_result_map[
                            not_none(create_account_request.company_id)
                        ] = (
                            await self.account_service.create_account_v2(
                                create_account_request=create_account_request,
                                organization_id=organization_id,
                                user_id=user_id,
                            )
                        ).id
                    except ConflictResourceError as e:
                        logger.bind(
                            organization_id=organization_id,
                            user_id=user_id,
                            create_account_request=create_account_request,
                            e=e.additional_error_details,
                        ).warning(
                            "Conflict resource error when creating account",
                        )
                        continue
                elif len(accounts) > 1:
                    logger.warning(
                        "More than one account found for company",
                        company_id=create_account_request.company_id,
                    )
                else:
                    account_v2_result_map[
                        not_none(create_account_request.company_id)
                    ] = (
                        await self.account_service.get_account_v2(
                            account_id=accounts[0].id,
                            organization_id=organization_id,
                        )
                    ).id
        return account_v2_result_map

    async def add_accounts_to_domain_lists(
        self,
        account_simple_list: list[AccountSimpleInfo],
        domain_list_ids: list[UUID],
        prospecting_run_id: UUID,
        organization_id: UUID,
        user_id: UUID,
    ) -> None:
        account_ids = [account.id for account in account_simple_list]
        logger.bind(
            organization_id=organization_id,
            prospecting_run_id=prospecting_run_id,
            domain_list_ids=domain_list_ids,
            account_ids=account_ids,
        ).info(
            "Adding accounts to domain lists",
        )

        # Create a mapping from account_id to company_id
        account_id_to_company_id = {
            account.id: account.company_id for account in account_simple_list
        }

        # Track enrollment results for each account
        enrollment_results: dict[UUID, list[ListEnrollmentResult]] = {
            account.id: [] for account in account_simple_list
        }

        # 1. Add contacts to each domain list
        for domain_list_id in domain_list_ids:
            results = await self.crm_sync_service.domain_object_list_service.add_and_remove_items(
                request=AddAndRemoveItemsRequest(
                    item_ids_to_add=set(account_ids),
                    item_ids_to_remove=None,
                ),
                user_id=user_id,
                organization_id=organization_id,
                list_id=domain_list_id,
            )

            # Skip if results is None
            if results is None:
                logger.warning(
                    f"Failed to add accounts to domain list {domain_list_id}",
                    domain_list_id=domain_list_id,
                )
                continue

            # Get set of failed account IDs (if any)
            failed_item_set = set()
            if results.failed_items is not None:
                failed_item_set = {item.id for item in results.failed_items}

            # Process each account
            for account_id in account_ids:
                if account_id not in failed_item_set:
                    # Account was successfully added
                    enrollment_results[account_id].append(
                        ListEnrollmentResult(
                            list_id=domain_list_id,
                            status=ProspectingEnrollmentStatus.enrolled,
                            rejection_reason=None,
                        )
                    )
                else:
                    # Account failed to be added
                    error_message = "Unknown error"
                    if results.failed_items is not None:
                        error_message = next(
                            (
                                item.reason
                                for item in results.failed_items
                                if item.id == account_id
                            ),
                            error_message,
                        )

                    enrollment_results[account_id].append(
                        ListEnrollmentResult(
                            list_id=domain_list_id,
                            status=ProspectingEnrollmentStatus.rejected,
                            rejection_reason=error_message,
                        )
                    )

        # 2. Update prospecting_run_result for each account
        for account_id, list_results in enrollment_results.items():
            if list_results:
                await self.prospecting_run_service.bulk_update_run_results_for_company_ids(
                    prospecting_run_id=prospecting_run_id,
                    organization_id=organization_id,
                    company_ids=[account_id_to_company_id[account_id]],
                    list_enrollment_results=list_results,
                )

        logger.bind(
            organization_id=organization_id,
            prospecting_run_id=prospecting_run_id,
        ).info(
            "Completed adding accounts to domain lists",
        )

    async def find_contacts_by_person_ids(
        self,
        person_ids: list[UUID],
        organization_id: UUID,
    ) -> list[ContactV2]:
        return await self.contact_query_service.list_by_person_ids(
            person_ids=person_ids,
            organization_id=organization_id,
        )


class SingletonProspectingCommonService(Singleton, ProspectingCommonService):
    """Singleton implementation of ProspectingCommonService for efficient reuse."""


def get_prospecting_common_service(
    db_engine: DatabaseEngine,
) -> ProspectingCommonService:
    return SingletonProspectingCommonService(
        prospecting_quota_service=get_prospecting_quota_service_by_db_engine(
            db_engine=db_engine
        ),
        prospecting_run_service=get_prospecting_run_service_from_engine(
            db_engine=db_engine
        ),
        pdl_client=PdlClient(),
        person_repository=PersonRepository(engine=db_engine),
        company_repository=CompanyRepository(engine=db_engine),
        pdl_person_repository=PDLPersonRepository(engine=db_engine),
        pdl_company_repository=PDLCompanyRepository(engine=db_engine),
        contact_service=get_contact_service(db_engine=db_engine),
        contact_query_service=get_contact_query_service(db_engine=db_engine),
        account_service=get_account_service(db_engine=db_engine),
        select_list_service=get_select_list_service(engine=db_engine),
        crm_sync_service=get_crm_sync_service_from_db_engine(db_engine),
        sequence_enrollment_service=get_sequence_enrollment_service_by_db_engine(
            db_engine=db_engine
        ),
        domain_object_list_service=get_domain_object_list_service(db_engine=db_engine),
    )


def get_prospecting_common_service_by_request(
    request: Request,
) -> ProspectingCommonService:
    db_engine = get_db_engine(request=request)
    return get_prospecting_common_service(db_engine=db_engine)
