import hashlib
import json
import uuid
from datetime import timed<PERSON>ta
from typing import cast

from fastapi import Request

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.db.dao.pdl_cache_repository import PDLCacheRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.pdl_cache import PDLCache
from salestech_be.integrations.pdl.model import (
    PeopleDataLabsSearchCommonRequest,
    PeopleDataLabsSearchCompanyRequest,
    PeopleDataLabsSearchCompanyResponse,
)
from salestech_be.integrations.pdl.pdl_client import PdlClient
from salestech_be.ree_logging import get_logger
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)

# Type alias for JSON-like structures using | operator (Python 3.10+)
JsonPrimitive = str | int | float | bool | None
JsonPayload = JsonPrimitive | list["JsonPayload"] | dict[str, "JsonPayload"]


def _normalize_bool_query_value(
    bool_dict_value: dict[str, JsonPayload],
) -> dict[str, JsonPayload]:
    """Normalizes the value part of a 'bool' query (the dictionary of clauses)."""
    bool_clauses_normalized: dict[str, JsonPayload] = {}
    for bool_key, bool_value_untyped in sorted(bool_dict_value.items()):
        bool_value: JsonPayload = bool_value_untyped
        if bool_key in ["must", "should", "filter", "must_not"] and isinstance(
            bool_value, list
        ):
            try:
                normalized_clauses_as_strings = sorted(
                    [
                        json.dumps(
                            _normalize_es_query_recursive(clause),
                            sort_keys=True,
                            separators=(",", ":"),
                        )
                        for clause in bool_value
                    ]
                )
                bool_clauses_normalized[bool_key] = [
                    json.loads(s) for s in normalized_clauses_as_strings
                ]
            except TypeError:  # pragma: no cover
                bool_clauses_normalized[bool_key] = _normalize_es_query_recursive(
                    bool_value
                )
        else:
            bool_clauses_normalized[bool_key] = _normalize_es_query_recursive(
                bool_value
            )
    return bool_clauses_normalized


def _normalize_terms_query_value(
    terms_dict_value: dict[str, JsonPayload],
) -> dict[str, JsonPayload]:
    """Normalizes the value part of a 'terms' query (the dictionary of field -> list_of_values)."""
    normalized_terms_dict: dict[str, JsonPayload] = {}
    for field_name, terms_values_untyped in sorted(terms_dict_value.items()):
        terms_values: JsonPayload = terms_values_untyped
        if isinstance(terms_values, list):
            string_list = sorted([str(tv) for tv in terms_values])
            normalized_terms_dict[field_name] = cast(JsonPayload, string_list)
        else:
            normalized_terms_dict[field_name] = _normalize_es_query_recursive(
                terms_values
            )
    return normalized_terms_dict


def _normalize_es_query_recursive(item: JsonPayload) -> JsonPayload:
    """
    Recursively normalizes an Elasticsearch query part.
    Sorts dictionary keys and specific lists (e.g., in bool or terms queries).
    """
    if isinstance(item, dict):
        new_dict: dict[str, JsonPayload] = {}
        for key, value_untyped in sorted(item.items()):
            value: JsonPayload = (
                value_untyped  # Ensure value is treated as JsonPayload for recursion
            )
            if key == "bool" and isinstance(value, dict):
                new_dict[key] = _normalize_bool_query_value(value)
            elif key == "terms" and isinstance(value, dict):
                new_dict[key] = _normalize_terms_query_value(value)
            else:
                new_dict[key] = _normalize_es_query_recursive(value)
        return new_dict
    elif isinstance(item, list):
        return [_normalize_es_query_recursive(elem) for elem in item]
    else:  # JsonPrimitive
        return item


def _get_canonical_es_query_string_from_obj(
    query_obj: dict[str, JsonPayload] | None,
) -> str:
    """
    Converts a Python dictionary representing an ES query into a canonical JSON string.
    If query_obj is None, it's treated as an empty query.
    """
    query_obj_to_normalize: dict[str, JsonPayload] = (
        {} if query_obj is None else query_obj
    )
    normalized_query_obj = _normalize_es_query_recursive(query_obj_to_normalize)
    return json.dumps(normalized_query_obj, sort_keys=True, separators=(",", ":"))


class PDLOperationName(NameValueStrEnum):
    search_company = "search_company"
    search_people = "search_people"


PDL_CACHE_EXPIRE_SECONDS = 1000 * 60 * 60 * 24 * 1  # 1 days


class PDLCacheService:
    def __init__(
        self,
        pdl_client: PdlClient,
        pdl_cache_repository: PDLCacheRepository,
    ):
        self.pdl_client = pdl_client
        self.pdl_cache_repository = pdl_cache_repository

    def get_hash_key(
        self,
        operation_name: PDLOperationName,
        pdl_search_common_request: PeopleDataLabsSearchCommonRequest,
    ) -> str:
        """
        Get hash key for pdl search common request.
        The hash is based on operation_name, normalized ES query, size, and scroll_token.
        """
        es_query_str = pdl_search_common_request.query
        es_query_obj: dict[str, JsonPayload] | None = None
        if es_query_str is not None:
            try:
                # Attempt to load the JSON string
                loaded_query_untyped = json.loads(es_query_str)
                if not isinstance(loaded_query_untyped, dict):
                    logger.error(
                        "PDL request query string did not parse to a dictionary.",
                        query_string=es_query_str,
                    )
                else:
                    # Now that we know it's a dict, we can assert its type more strongly if needed,
                    # or trust it conforms to Dict[str, JsonPayload] for a valid ES query.
                    es_query_obj = loaded_query_untyped
            except json.JSONDecodeError:
                logger.error(
                    "PDL request query string is invalid JSON.",
                    query_string=es_query_str,
                )

        canonical_query_str = _get_canonical_es_query_string_from_obj(es_query_obj)

        size = pdl_search_common_request.size
        scroll_token = pdl_search_common_request.scroll_token

        parts_to_hash = [
            f"operation_name:{operation_name.value}",
            f"query:{canonical_query_str}",
            f"size:{size!s}",
            f"scroll_token:{scroll_token!s}",
        ]

        combined_string = "|".join(parts_to_hash)
        return hashlib.sha256(combined_string.encode("utf-8")).hexdigest()

    async def search_company(
        self,
        pdl_search_company_request: PeopleDataLabsSearchCompanyRequest,
    ) -> PeopleDataLabsSearchCompanyResponse:
        query_hash = None
        cache_hit = False
        cached_response: PeopleDataLabsSearchCompanyResponse | None = None

        try:
            query_hash = self.get_hash_key(
                PDLOperationName.search_company, pdl_search_company_request
            )
            cache_result_model = await self.pdl_cache_repository.find_by_query_hash(
                PDLOperationName.search_company, query_hash
            )
            if cache_result_model and cache_result_model.expires_at > zoned_utc_now():
                logger.bind(
                    api_name=PDLOperationName.search_company,
                    query_hash=query_hash,
                ).info("hit cache")
                cached_response = cache_result_model.response
                cache_hit = True
        except Exception as e:
            logger.error(
                "Error during cache lookup, proceeding without cache.",
                exc_info=e,
                query_hash=query_hash,
            )

        if cache_hit and cached_response is not None:
            return cached_response

        result = await self.pdl_client.search_company(pdl_search_company_request)

        if query_hash is not None:
            try:
                await self.pdl_cache_repository.upsert_cache(
                    PDLCache(
                        id=uuid.uuid4(),
                        created_at=zoned_utc_now(),
                        deleted_at=None,
                        operation_name=PDLOperationName.search_company,
                        query_hash=query_hash,
                        request=pdl_search_company_request,
                        response=result,
                        expires_at=zoned_utc_now()
                        + timedelta(seconds=PDL_CACHE_EXPIRE_SECONDS),
                    )
                )
            except Exception as e:
                logger.error(
                    "Error during cache upsert, API call was successful but could not cache.",
                    exc_info=e,
                    query_hash=query_hash,
                )
        return result


class SingletonPDLCacheService(Singleton, PDLCacheService):
    """Singleton implementation of PDLCacheService for efficient reuse."""


def get_pdl_cache_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> PDLCacheService:
    return SingletonPDLCacheService(
        pdl_client=PdlClient(),
        pdl_cache_repository=PDLCacheRepository(engine=db_engine),
    )


def get_pdl_cache_service_by_request(
    request: Request,
) -> PDLCacheService:
    db_engine = get_db_engine(request=request)
    return get_pdl_cache_service_by_db_engine(db_engine=db_engine)
