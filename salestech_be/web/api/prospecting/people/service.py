from uuid import UUID

from salestech_be.common.results import <PERSON>ursor
from salestech_be.common.schema_manager.std_object_relationship import (
    ProspectingPersonRelationship,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.metadata.common import RelationshipId
from salestech_be.common.type.metadata.schema import FieldReference
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.data.types import ModeledObjectRecord, StandardRecord
from salestech_be.core.data.util import (
    ObjectRecordFetchConditions,
    extract_field,
    extract_field_references_from_field_set,
)
from salestech_be.core.prospecting.type.person_type_v2 import ProspectingPerson
from salestech_be.db.dao.person_repository import PersonRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.person_dto import PersonDto
from salestech_be.db.models.prospecting_run import ProspectingRun
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import TemporalTaskQueue
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.workflows.prospecting.people_bulk_import_workflow import (
    PeopleBulkImportWorkflowInput,
)
from salestech_be.util.validation import not_none
from salestech_be.web.api.common.container import (
    ListEntityRequestV2,
)
from salestech_be.web.api.prospecting.common.prospecting_common_service import (
    ProspectingCommonService,
    get_prospecting_common_service,
)
from salestech_be.web.api.prospecting.people.schema import (
    BulkImportPeopleRequest,
)

logger = get_logger()


class PeopleService:
    def __init__(
        self,
        person_repository: PersonRepository,
        prospecting_common_service: ProspectingCommonService,
    ):
        self.person_repository = person_repository
        self.prospecting_common_service = prospecting_common_service

    async def search_people(
        self,
        list_people_request: ListEntityRequestV2,
        user_id: UUID,
        organization_id: UUID,
    ) -> tuple[list[StandardRecord[ProspectingPerson]], Cursor]:
        person_dto_list, cursor = await self.prospecting_common_service.search_people(
            list_people_request=list_people_request,
            user_id=user_id,
            organization_id=organization_id,
        )
        prospecting_person_list = await self.convert_to_prospecting_person(
            list_request=list_people_request,
            fetched_person_dto_list=person_dto_list,
            organization_id=organization_id,
        )
        return prospecting_person_list, cursor

    async def enrich_people_by_ids(
        self,
        person_ids: list[UUID],
        user_id: UUID,
        organization_id: UUID,
        enrich_phone_numbers: bool,
        sequence_id: UUID | None = None,
        domain_list_ids: list[UUID] | None = None,
    ) -> list[StandardRecord[ProspectingPerson]]:
        person_dto_list = await self.prospecting_common_service.enrich_people_by_ids(
            person_ids=person_ids,
            user_id=user_id,
            organization_id=organization_id,
            enrich_phone_numbers=enrich_phone_numbers,
            sequence_id=sequence_id,
            domain_list_ids=domain_list_ids,
        )

        return await self.convert_to_prospecting_person(
            list_request=None,
            fetched_person_dto_list=person_dto_list,
            organization_id=organization_id,
        )

    async def convert_to_prospecting_person(  # noqa: C901, PLR0912
        self,
        organization_id: UUID,
        fetched_person_dto_list: list[PersonDto],
        list_request: ListEntityRequestV2 | None = None,
    ) -> list[StandardRecord[ProspectingPerson]]:
        """
        Convert a list of PersonDto objects to ProspectingPerson records with related data.
        """
        # Early return if empty list
        if not fetched_person_dto_list:
            return []

        # Get all person ids for efficient querying
        person_ids = [person_dto.db_person.id for person_dto in fetched_person_dto_list]

        # Fetch all contacts in a single query
        all_contacts = (
            await self.prospecting_common_service.find_contacts_by_person_ids(
                person_ids=person_ids,
                organization_id=organization_id,
            )
        )

        # Build person_id to contact mapping
        person_id_to_contact: dict[UUID, ContactV2 | None] = {}

        # Group contacts by person_id first to avoid multiple iterations
        contacts_by_person_id: dict[UUID, list[ContactV2]] = {}
        for contact_item in all_contacts:
            person_id = not_none(contact_item.person_id)
            if person_id not in contacts_by_person_id:
                contacts_by_person_id[person_id] = []
            contacts_by_person_id[person_id].append(contact_item)

        # Process each person once
        for person_id in person_ids:
            contacts = contacts_by_person_id.get(person_id, [])
            if len(contacts) > 1:
                logger.bind(person_id=person_id, contacts=contacts).warning(
                    f"Multiple contacts found for person {person_id}"
                )
            person_id_to_contact[person_id] = contacts[0] if contacts else None

        # Extract field references and relationship info
        field_references_by_relationship_id: dict[
            RelationshipId, list[FieldReference]
        ] = {}

        if list_request:
            fetch_conditions = ObjectRecordFetchConditions(
                filter_spec=list_request.filter_spec,
                sorting_spec=list_request.sorting_spec,
                fields=list_request.ordered_object_fields,
            )

            field_set = extract_field(fetch_conditions=fetch_conditions)
            for ref in extract_field_references_from_field_set(field_set=field_set):
                if ref.relationship_id:
                    if ref.relationship_id not in field_references_by_relationship_id:
                        field_references_by_relationship_id[ref.relationship_id] = []
                    field_references_by_relationship_id[ref.relationship_id].append(ref)

        # Prepare related records structure
        related_records_mapping: dict[
            UUID, dict[RelationshipId, tuple[ModeledObjectRecord, ...]]
        ] = {}

        # Process relationships using match/case
        for relationship_id in field_references_by_relationship_id:
            if relationship_id in ProspectingPersonRelationship:
                typed_relationship = ProspectingPersonRelationship(str(relationship_id))

                # Match different relationship types
                match typed_relationship:
                    case ProspectingPersonRelationship.prospecting_person__to__contact:
                        # Process contact relationship
                        for (
                            entry_person_id,
                            entry_contact,
                        ) in person_id_to_contact.items():
                            if entry_contact is not None:
                                # Type checking requires this assignment
                                typed_contact: ContactV2 = entry_contact

                                if entry_person_id not in related_records_mapping:
                                    related_records_mapping[entry_person_id] = {}

                                # Create contact record and store as tuple
                                contact_record = StandardRecord[ContactV2](
                                    object_id=ContactV2.object_id,
                                    data=typed_contact,
                                )
                                related_records_mapping[entry_person_id][
                                    typed_relationship
                                ] = (contact_record,)
                    case _:
                        # Handle unrecognized relationship types
                        logger.warning(
                            f"Unhandled prospecting person relationship: {typed_relationship}"
                        )

        # Build final result
        result: list[StandardRecord[ProspectingPerson]] = []
        for person_dto in fetched_person_dto_list:
            current_person_id = person_dto.db_person.id
            current_contact = person_id_to_contact.get(current_person_id)

            # Create ProspectingPerson with explicit handling of potential None
            prospecting_person = ProspectingPerson.map_from_person_dto(
                person_dto=person_dto,
                contact_id=current_contact.id if current_contact is not None else None,
            )

            # Get related records or empty dict with proper typing
            related_records: dict[RelationshipId, tuple[ModeledObjectRecord, ...]] = (
                related_records_mapping.get(current_person_id, {})
            )

            # Create standard record
            result.append(
                StandardRecord[ProspectingPerson](
                    object_id=ProspectingPerson.object_id,
                    requested_relationships=set(
                        field_references_by_relationship_id.keys()
                    ),
                    related_records=related_records,
                    data=prospecting_person,
                )
            )

        return result

    async def bulk_import_people(
        self,
        *,
        user_id: UUID,
        organization_id: UUID,
        bulk_import_people_request: BulkImportPeopleRequest,
    ) -> ProspectingRun:
        prospecting_run = await self.prospecting_common_service.bulk_import_people(
            user_id=user_id,
            organization_id=organization_id,
            bulk_import_people_request=bulk_import_people_request,
        )
        # Trigger workflow
        try:
            client = await get_temporal_client()
            await client.start_workflow(
                "PeopleBulkImportWorkflow",
                args=[
                    PeopleBulkImportWorkflowInput(
                        user_id=user_id,
                        organization_id=organization_id,
                        prospecting_run_id=prospecting_run.id,
                        sequence_id=bulk_import_people_request.add_to_sequence_id,
                        domain_list_ids=bulk_import_people_request.add_to_list_ids,
                    )
                ],
                id=f"bulk_import_people:{prospecting_run.id}",
                task_queue=TemporalTaskQueue.PROSPECTING_TASK_QUEUE,
            )
            logger.bind(
                organization_id=organization_id,
                user_id=user_id,
                prospecting_run_id=prospecting_run.id,
            ).info("Triggered workflow for bulk importing people")
        except Exception as e:
            logger.bind(
                organization_id=organization_id,
                user_id=user_id,
                prospecting_run_id=prospecting_run.id,
                exc_info=e,
            ).error("Error bulk importing people")
        return prospecting_run


class SingletonPeopleService(Singleton, PeopleService):
    pass


def get_people_service_with_db_engine(
    db_engine: DatabaseEngine,
) -> PeopleService:
    return SingletonPeopleService(
        person_repository=PersonRepository(engine=db_engine),
        prospecting_common_service=get_prospecting_common_service(db_engine=db_engine),
    )
