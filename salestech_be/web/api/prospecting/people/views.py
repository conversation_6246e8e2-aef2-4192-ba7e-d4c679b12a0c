from typing import Annotated

from fastapi import Depends

from salestech_be.core.data.types import StandardRecord
from salestech_be.core.prospecting.type.person_type_v2 import ProspectingPerson
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import (
    require_create_prospecting_access,
    require_read_prospecting_access,
    require_update_prospecting_access,
)
from salestech_be.web.api.common.container import (
    ListEntityRequestV2,
    ListResponse,
    PaginatedListResponse,
)
from salestech_be.web.api.prospecting.common.util import validate_search_request
from salestech_be.web.api.prospecting.people.schema import (
    BulkImportPeopleRequest,
    BulkImportPeopleResponse,
    EnrichPeopleRequest,
)
from salestech_be.web.api.prospecting.people.service import PeopleService
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.lifespan_service import people_service_from_lifespan
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserId,
)

logger = get_logger()

router = ReeAPIRouter()


@router.post(
    "/_list",
    response_model=PaginatedListResponse[StandardRecord[ProspectingPerson]],
    dependencies=[Depends(require_read_prospecting_access)],
)
async def search_people(
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    list_people_request: ListEntityRequestV2,
    people_service: Annotated[PeopleService, Depends(people_service_from_lifespan)],
) -> PaginatedListResponse[StandardRecord[ProspectingPerson]]:
    validate_search_request(list_people_request)
    prospecting_person_list, cursor = await people_service.search_people(
        user_id=user_id,
        organization_id=organization_id,
        list_people_request=list_people_request,
    )
    return PaginatedListResponse(
        list_data=prospecting_person_list,
        cursor=cursor,
    )


@router.post(
    "/_enrich",
    response_model=ListResponse[StandardRecord[ProspectingPerson]],
    dependencies=[Depends(require_update_prospecting_access)],
)
async def enrich_people(
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    enrich_people_request: EnrichPeopleRequest,
    people_service: Annotated[PeopleService, Depends(people_service_from_lifespan)],
) -> ListResponse[StandardRecord[ProspectingPerson]]:
    prospecting_person_list = await people_service.enrich_people_by_ids(
        user_id=user_id,
        organization_id=organization_id,
        person_ids=enrich_people_request.person_ids,
        enrich_phone_numbers=bool(enrich_people_request.enrich_phone_numbers),
        sequence_id=enrich_people_request.add_to_sequence_id,
        domain_list_ids=enrich_people_request.add_to_list_ids,
    )

    return ListResponse[StandardRecord[ProspectingPerson]](
        list_data=prospecting_person_list,
    )


@router.post(
    "/_bulk_import",
    response_model=BulkImportPeopleResponse,
    dependencies=[Depends(require_create_prospecting_access)],
)
async def bulk_import_people(
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    bulk_import_people_request: BulkImportPeopleRequest,
    people_service: Annotated[PeopleService, Depends(people_service_from_lifespan)],
) -> BulkImportPeopleResponse:
    prospecting_run = await people_service.bulk_import_people(
        user_id=user_id,
        organization_id=organization_id,
        bulk_import_people_request=bulk_import_people_request,
    )

    return BulkImportPeopleResponse(
        prospecting_run_id=prospecting_run.id,
    )
