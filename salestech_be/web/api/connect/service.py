from datetime import <PERSON><PERSON><PERSON>
from uuid import UUID, uuid4

from fastapi import HTTPException, Request, status
from sqlalchemy.exc import IntegrityError

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ConflictResourceError,
    ErrorDetails,
    IllegalStateError,
    InvalidArgumentError,
    ResourceNotFoundError,
    ServiceError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.core.calendar.account.calendar_account_service import (
    CalendarAccountService,
)
from salestech_be.core.calendar.user_calendar_sync_service import (
    UserCalendarSyncService,
    get_user_calendar_sync_service_by_db_engine,
)
from salestech_be.core.crm_sync.hubspot.hubspot_service import (
    HubSpotService,
    get_hubspot_service_by_db_engine,
)
from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.meeting.conference_service import ConferenceService
from salestech_be.db.dao.oauth_repository import OauthRepository
from salestech_be.db.dao.user_calendar_repository import UserCalendarRepository
from salestech_be.db.dao.user_integration_repository import (
    UserIntegrationRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.user_integration_dto import UserIntegrationDto
from salestech_be.db.models.calendar_account import CalendarAccount
from salestech_be.db.models.oauth import OAuthStatus, OAuthUserAuth
from salestech_be.db.models.user import User
from salestech_be.db.models.user_integration import (
    IntegrationProvider,
    IntegrationStatus,
    IntegrationType,
    UserIntegration,
    UserIntegrationUpdate,
)
from salestech_be.db.models.user_integration_connector import (
    IntegrationConnectorProvider,
    IntegrationConnectorStatus,
    NylasConnectorMetadata,
    UserIntegrationConnector,
)
from salestech_be.db.models.user_organization_association import (
    UserOrganizationAssociation,
    UserOrganizationAssociationStatus,
)
from salestech_be.integrations.google.client import GoogleClient
from salestech_be.integrations.hubspot.model import HubSpotToken
from salestech_be.integrations.nylas.client import NylasClient
from salestech_be.integrations.nylas.model import NylasGrantStatus
from salestech_be.integrations.nylas.type import CreateGrantRequest
from salestech_be.integrations.recallai.client import (
    RecallAIClientV2,
    get_zoom_recall_oauth_callback_url,
)
from salestech_be.integrations.recallai.type import CreateZoomOauthCredentialRequest
from salestech_be.integrations.zoom.client import ZoomClient
from salestech_be.integrations.zoom.type import Token
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.clients import HUBSPOT_SCOPES
from salestech_be.services.auth.encryptions import (
    EncryptionManager,
    fernet_encryption_manager,
)
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_from_timestamp, zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.connect.schema import (
    CreateUserAuthRequest,
    CreateUserIntegrationRequest,
    GoogleOAuthIntegrationRequest,
    ListUserIntegrationResponse,
    MicrosoftOAuthIntegrationRequest,
    UserIntegrationResponse,
    ZoomOAuthIntegrationRequest,
)
from salestech_be.web.api.webhook.service.utils import (
    get_connector_status_from_nylas_status,
)

logger = get_logger("service.connect")


class ProviderClients:
    def __init__(
        self,
        nylas: NylasClient,
        zoom: ZoomClient,
        google: GoogleClient,
        recall: RecallAIClientV2,
    ):
        self.nylas = nylas
        self.zoom = zoom
        self.google = google
        self.recall = recall


class UserIntegrationService:
    def __init__(  # -- too many arguments
        self,
        provider_clients: ProviderClients,
        user_integration_repository: UserIntegrationRepository,
        oauth_repository: OauthRepository,
        encryption_manager: EncryptionManager,
        conference_service: ConferenceService,
        email_account_service_ext: EmailAccountServiceExt,
        calendar_account_service: CalendarAccountService,
        user_calendar_repository: UserCalendarRepository,
        hubspot_service: HubSpotService,
        user_calendar_sync_service: UserCalendarSyncService,
    ):
        self.nylas = provider_clients.nylas
        self.zoom = provider_clients.zoom
        self.google = provider_clients.google
        self.user_integration_repo = user_integration_repository
        self.oauth_repository = oauth_repository
        self.encryption_manager = encryption_manager
        self.recall = provider_clients.recall
        self.conference_service = conference_service
        self.email_account_service_ext = email_account_service_ext
        self.calendar_account_service = calendar_account_service
        self.user_calendar_repository = user_calendar_repository
        self.hubspot_service = hubspot_service
        self.user_calendar_sync_service = user_calendar_sync_service

    async def list_integration_by_user_id(
        self,
        user_id: UUID,
        organization_id: UUID,
    ) -> ListUserIntegrationResponse:
        integrations = (
            await self.user_integration_repo.find_all_user_integrations_by_user_id(
                user_id=user_id,
                organization_id=organization_id,
            )
        )
        return ListUserIntegrationResponse(
            data=[
                UserIntegrationResponse(
                    user_integration_id=it.integration_id,
                    user_id=it.user_id,
                    email_account_id=it.user_integration.email_account_id,
                    organization_id=it.user_organization_id,
                    integration_status=it.integration_status,
                    integration_type=it.integration_type,
                    integration_provider=it.integration_provider,
                    integration_calendar_account_id=it.user_integration.calendar_account_id,
                )
                for it in integrations
            ],
        )

    async def list_organization_integrations(
        self,
        organization_id: UUID,
        integration_type: IntegrationType,
    ) -> ListUserIntegrationResponse:
        integrations = await self.user_integration_repo.find_organization_integrations(
            organization_id=organization_id,
            integration_type=integration_type,
        )
        user_integration_response_list = []
        for integration in integrations:
            integration_calendar_email = None
            user_display_name = None

            if (
                integration_type == IntegrationType.CALENDAR
                and integration.user_integration.calendar_account_id is not None
            ):
                integration_calendar_account = (
                    await self.user_integration_repo.find_by_tenanted_primary_key(
                        CalendarAccount,
                        id=not_none(integration.user_integration.calendar_account_id),
                        organization_id=organization_id,
                    )
                )
                integration_calendar_email = (
                    integration_calendar_account.email
                    if integration_calendar_account
                    else None
                )
                user = await self.user_integration_repo.find_by_primary_key(
                    User, id=integration.user_id
                )
                if not user:
                    continue
                user_display_name = user.display_name
            user_integration_response_list.append(
                UserIntegrationResponse(
                    user_integration_id=integration.integration_id,
                    user_id=integration.user_id,
                    email_account_id=integration.user_integration.email_account_id,
                    organization_id=integration.user_organization_id,
                    integration_status=integration.integration_status,
                    integration_type=integration.integration_type,
                    integration_provider=integration.integration_provider,
                    integration_calendar_account_id=integration.user_integration.calendar_account_id,
                    integration_calendar_email=integration_calendar_email,
                    user_display_name=user_display_name,
                )
            )
        return ListUserIntegrationResponse(
            data=user_integration_response_list,
        )

    async def create_hubspot_auth_and_integration(
        self,
        user_id: UUID,
        organization_id: UUID,
        hubspot_token: HubSpotToken,
    ) -> UserIntegration:
        """Create HubSpot Integration.

        HubSpot integration is organization level. We only allow one integration per org
        for HubSpot.
        """
        # Step 1: Find existing HubSpot user integration
        existing_integration_dto = await self.hubspot_service.get_hubspot_integration(
            organization_id=organization_id
        )
        existing_integration = (
            existing_integration_dto.user_integration
            if existing_integration_dto
            else None
        )
        # Step 2: Update user auth if both OAuthUserAuth and UserIntegration exist
        if existing_integration and existing_integration.user_auth_id:
            await self.hubspot_service.update_hubspot_user_auth(
                user_auth_id=existing_integration.user_auth_id,
                organization_id=organization_id,
                hubspot_token=hubspot_token,
            )
            return existing_integration

        create_user_auth_request = CreateUserAuthRequest(
            user_id=user_id,
            organization_id=organization_id,
            access_token=hubspot_token.access_token,
            refresh_token=hubspot_token.refresh_token,
            id_token=hubspot_token.id_token,
            provider_id=uuid4(),
            expires_at=zoned_utc_from_timestamp(
                int(zoned_utc_now().timestamp()) + hubspot_token.expires_in
            ),
            scopes=HUBSPOT_SCOPES,
            status=OAuthStatus.ACTIVE,
            token_type=hubspot_token.token_type,
        )

        # Step 2: create OAuthUserAuth and update UserIntegration if only UserIntegration exist
        if existing_integration and not existing_integration.user_auth_id:
            user_auth = await self.create_user_auth(request=create_user_auth_request)
            if not (
                updated_user_integration
                := await self.user_integration_repo.update_by_tenanted_primary_key(
                    UserIntegration,
                    organization_id=organization_id,
                    primary_key_to_value={"id": existing_integration.id},
                    column_to_update=UserIntegrationUpdate(user_auth_id=user_auth.id),
                )
            ):
                raise ServiceError(
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.DB_UPDATE_ERROR,
                        details=f"UserIntegration {existing_integration.id} failed to update.",
                    )
                )
            return updated_user_integration

        # Step 3: create OAuthUserAuth and UserIntegration if UserIntegration not exist
        user_auth = await self.create_user_auth(request=create_user_auth_request)
        return await self.user_integration_repo.insert(
            UserIntegration(
                id=uuid4(),
                user_id=user_id,
                organization_id=organization_id,
                user_auth_id=user_auth.id,
                integration_provider=IntegrationProvider.HUBSPOT,
                integration_type=IntegrationType.CRM,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )

    async def create_integration(
        self,
        request: CreateUserIntegrationRequest,
    ) -> UserIntegrationResponse:
        existing_integration = (
            await self.user_integration_repo.find_user_integration_by_user_id(
                user_id=request.user_id,
                integration_type=request.integration_type,
                organization_id=request.organization_id,
            )
        )

        non_persisted_user_integration = (
            await self.create_or_update_external_integration(
                request=request,
                existing_integration=existing_integration,
            )
        )

        if request.integration_type == IntegrationType.EMAIL:
            email_account = (
                await self.email_account_service_ext.get_or_create_email_account(
                    owner_user_id=request.user_id,
                    email=not_none(
                        non_persisted_user_integration.user_integration.connected_email
                    ),
                    organization_id=request.organization_id,
                    user_id=request.user_id,
                )
            )
            non_persisted_user_integration.user_integration = (
                non_persisted_user_integration.user_integration.model_copy(
                    update={
                        "email_account_id": email_account.id,
                    }
                )
            )
            email_account_id = email_account.id
        else:
            email_account_id = None

        if request.integration_type == IntegrationType.CALENDAR:
            calendar_account = (
                await self.calendar_account_service.update_or_create_calendar_account(
                    owner_user_id=request.user_id,
                    email=not_none(
                        non_persisted_user_integration.user_integration.connected_email
                    ),
                    organization_id=request.organization_id,
                    user_id=request.user_id,
                    is_default=True,
                    active=True,
                )
            )
            non_persisted_user_integration.user_integration = (
                non_persisted_user_integration.user_integration.model_copy(
                    update={
                        "calendar_account_id": calendar_account.id,
                    }
                )
            )
        created_or_updated: UserIntegrationDto
        try:
            if existing_integration:
                created_or_updated = (
                    await self.user_integration_repo.update_user_integration(
                        user_integration_dto=non_persisted_user_integration,
                    )
                )
            else:
                created_or_updated = (
                    await self.user_integration_repo.create_user_integration(
                        user_integration_dto=non_persisted_user_integration,
                    )
                )
            # create user calendar if not exist for calendar connection
            if created_or_updated.user_integration.calendar_account_id:
                try:
                    await self.user_calendar_sync_service.sync_user_calendar(
                        user_integration_dto=created_or_updated,
                    )
                except Exception as e:
                    logger.error(
                        "failed to create user calendar",
                        user_id=created_or_updated.user_id,
                        organization_id=created_or_updated.user_organization_id,
                        integration_id=created_or_updated.integration_id,
                        error=e,
                    )

        except IntegrityError:
            if non_persisted_user_integration.user_integration.connected_email:
                integration = await self.user_integration_repo.find_user_integration_by_connected_email(
                    integration_type=non_persisted_user_integration.user_integration.integration_type,
                    connected_email=non_persisted_user_integration.user_integration.connected_email,
                    organization_id=non_persisted_user_integration.user_organization_id,
                )
                if integration:
                    logger.error(
                        "same email has already been connected",
                        email=integration.connected_email,
                        organization_id=integration.organization_id,
                        integration_id=integration.id,
                    )
                    raise ConflictResourceError(
                        f"same email {integration.connected_email} has already been "
                        f"connected to the organization by someone"
                    )
            raise ConflictResourceError(
                f"duplicated user integration {non_persisted_user_integration.user_integration}"
            )

        return UserIntegrationResponse(
            user_id=created_or_updated.user_id,
            email_account_id=email_account_id,
            organization_id=created_or_updated.user_organization_id,
            user_integration_id=created_or_updated.integration_id,
            integration_type=created_or_updated.integration_type,
            integration_provider=created_or_updated.integration_provider,
            integration_status=created_or_updated.integration_status,
            integration_calendar_account_id=created_or_updated.user_integration.calendar_account_id,
        )

    async def delete_calendar_and_email_integrations(  # noqa: C901, PLR0912
        self,
        user_id: UUID,
        organization_id: UUID,
    ) -> list[IntegrationType]:
        integrations: list[
            UserIntegrationDto
        ] = await self.user_integration_repo.find_all_user_integrations_by_user_id(
            user_id=user_id,
            organization_id=organization_id,
        )
        # sub-list of all CAL integrations from integrations list
        cal_integrations = [
            it
            for it in integrations
            if it.integration_type == IntegrationType.CALENDAR
            and not_none(it.user_integration_connector).status
            == IntegrationConnectorStatus.CONNECTED
        ]

        # sub-list of all EMAIL integration from integrations list
        email_integrations = [
            it
            for it in integrations
            if it.integration_type == IntegrationType.EMAIL
            and not_none(it.user_integration_connector).status
            == IntegrationConnectorStatus.CONNECTED
        ]

        # HTTPException if cal_integrations and email_integrations are empty
        if not cal_integrations and not email_integrations:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No calendar and email integrations found",
            )

        # Log Error if there are more than one CAL or EMAIL integrations
        if len(cal_integrations) > 1:
            logger.error(
                f"More than one calendar integration found for user: {user_id}"
            )
        if len(email_integrations) > 1:
            logger.error(f"More than one email integration found for user: {user_id}")

        access_token_to_revoke: dict[str, list[OAuthUserAuth]] = {}
        all_integrations = cal_integrations + email_integrations
        # Create a dictionary that maps user_id to nylas_grant_id
        user_to_nylas_grant: dict[UUID, str] = {}
        integrations_removed: list[IntegrationType] = []

        for integration in all_integrations:
            if (
                integration.user_auth
                and integration.user_auth.access_token
                and integration.user_auth.status == OAuthStatus.ACTIVE
            ):
                unencrypted_access_token = self.encryption_manager.decrypt(
                    integration.user_auth.access_token
                )
                # add integration.user_auth to access_token_to_revoke keyed by unencrypted_access_token
                if unencrypted_access_token not in access_token_to_revoke:
                    access_token_to_revoke[unencrypted_access_token] = []
                access_token_to_revoke[unencrypted_access_token].append(
                    integration.user_auth
                )
            if (
                integration.user_integration_connector
                and integration.user_integration_connector.connector_provider
                == IntegrationConnectorProvider.NYLAS
            ):
                # Delete user integration + user_integration_connector
                await self.user_integration_repo.update_user_integration(
                    user_integration_dto=UserIntegrationDto(
                        user_integration=integration.user_integration.model_copy(
                            update={
                                "deleted_at": zoned_utc_now(),
                            }
                        ),
                        user_integration_connector=integration.user_integration_connector.model_copy(
                            update={
                                "deleted_at": zoned_utc_now(),
                                "status": IntegrationConnectorStatus.DISCONNECTED,
                            }
                        ),
                    )
                )

                # Store the user_id to nylas_grant_id mapping, keeping user_id as UUID
                user_to_nylas_grant[integration.user_id] = (
                    integration.user_integration_connector.external_id
                )
                integrations_removed.append(integration.integration_type)

        # Check each user and delete their grant if they have no active organization associations
        for grant_user_id, nylas_grant_id in user_to_nylas_grant.items():
            # Check if this user has any active organization associations
            user_org_associations = (
                await self.user_integration_repo._find_by_column_values(
                    UserOrganizationAssociation,
                    user_id=grant_user_id,
                    status=UserOrganizationAssociationStatus.ACTIVE,
                )
            )

            # If the user has active organization associations, skip deleting the grant
            if user_org_associations:
                logger.info(
                    f"Not deleting nylas grant {nylas_grant_id} as user {grant_user_id} still has active organization associations"
                )
                continue

            # User has no active associations, delete the grant
            delete_response = await self.nylas.destroy_grant(grant_id=nylas_grant_id)

            # Check response
            if not delete_response.request_id:
                logger.error(f"Failed to delete nylas grant id: {nylas_grant_id}")

        for k, v in access_token_to_revoke.items():
            try:
                # delete user_auth
                for u in v:
                    await self.oauth_repository.update_instance(
                        u.model_copy(
                            update={
                                "status": OAuthStatus.INACTIVE,
                                "expires_at": zoned_utc_now(),
                            }
                        )
                    )

                await self.google.revoke_token(access_token=k)
            except Exception as e:
                logger.error(
                    f"Error revoking token for user: {user_id}, access_token: {k}. Error {e}"
                )
        return integrations_removed

    async def delete_all_user_integrations(
        self,
        user_id: UUID,
        organization_id: UUID,
    ) -> list[IntegrationType]:
        integrations_removed: list[IntegrationType] = []

        # First try to delete all calendar and email integrations
        try:
            cal_email_removed = await self.delete_calendar_and_email_integrations(
                user_id=user_id,
                organization_id=organization_id,
            )
            integrations_removed.extend(cal_email_removed)
        except HTTPException as e:
            # Only catch 400 errors (when no calendar/email integrations exist)
            if e.status_code != status.HTTP_400_BAD_REQUEST:
                raise

        # Now find and delete all Zoom integrations
        integrations = (
            await self.user_integration_repo.find_all_user_integrations_by_user_id(
                user_id=user_id,
                organization_id=organization_id,
            )
        )

        zoom_integrations = [
            it
            for it in integrations
            if (
                it.integration_type == IntegrationType.CONFERENCING
                and it.integration_provider == IntegrationProvider.ZOOM
            )
        ]

        # Process each Zoom integration
        for integration in zoom_integrations:
            await self._delete_integration(
                existing_user_integration=integration.user_integration
            )
            integrations_removed.append(integration.integration_type)

        return integrations_removed

    async def delete_integration_by_id(
        self, user_integration_id: UUID, organization_id: UUID
    ) -> UserIntegrationResponse:
        existing_integration = (
            await self.user_integration_repo.find_by_tenanted_primary_key(
                UserIntegration,
                id=user_integration_id,
                organization_id=organization_id,
            )
        )
        if not existing_integration:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"user integration: {user_integration_id} is not exist",
            )
        if (
            existing_integration.integration_provider != IntegrationProvider.ZOOM
            or existing_integration.integration_type != IntegrationType.CONFERENCING
        ):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Delete integration is only available for Zoom",
            )
        return await self._delete_integration(
            existing_user_integration=existing_integration
        )

    async def delete_integration_by_zoom_webhook(
        self,
        zoom_user_id: str,
    ) -> None:
        logger.info(f"zoom_user_id: {zoom_user_id}")
        existing_integration_list = (
            await self.user_integration_repo.list_user_integration_by_external_id(
                external_id=zoom_user_id,
                integration_type=IntegrationType.CONFERENCING,
                integration_provider=IntegrationProvider.ZOOM,
            )
        )
        if not existing_integration_list:
            logger.warning(f"no existing integration for zoom user: {zoom_user_id}")
            return
        for user_integration in existing_integration_list:
            await self._delete_integration(existing_user_integration=user_integration)

    async def create_user_auth(
        self,
        request: CreateUserAuthRequest,
    ) -> OAuthUserAuth:
        encrypted_access_token = self.encryption_manager.encrypt(request.access_token)
        encrypted_refresh_token = (
            self.encryption_manager.encrypt(request.refresh_token)
            if request.refresh_token
            else None
        )
        oauth_user_auth = OAuthUserAuth(
            id=uuid4(),
            user_id=request.user_id,
            organization_id=request.organization_id,
            access_token=encrypted_access_token,
            scopes=request.scopes,
            token_type=request.token_type,
            refresh_token=encrypted_refresh_token,
            id_token=request.id_token,
            provider_id=request.provider_id,  # dummy value for now
            expires_at=request.expires_at,
            status=OAuthStatus.ACTIVE,
            created_at=zoned_utc_now(),
            additional_auth_info=request.additional_auth_info,
        )
        response = await self.oauth_repository.insert_oauth_user_auth(
            oauth_user_auth,
        )
        if not response:
            raise ValueError("insert user_auth failed")
        return response

    async def create_or_update_external_integration(
        self,
        request: CreateUserIntegrationRequest,
        existing_integration: UserIntegrationDto | None = None,
    ) -> UserIntegrationDto:
        # NOTE: this function does not have other side-effects than Nylas grant external
        # API call (from created_or_updated_by_nylas_with_google). No internal data objects
        # will be updated or created. Only the DTO obj is instantiated.

        if request.integration_type == IntegrationType.CONFERENCING:
            integration_provider_request = request.integration_provider_request
            if not isinstance(
                integration_provider_request,
                ZoomOAuthIntegrationRequest,
            ):
                raise InvalidArgumentError(
                    "expected ZoomOAuthIntegrationRequest but "
                    f"found {type(request.integration_provider_request)}",
                )

            # Register zoom access token to recall ai
            if settings.enable_recall_register_map_zoom_user_credential:
                await self.recall.register_map_zoom_user_credential(
                    user_id=str(request.user_id),
                    request=CreateZoomOauthCredentialRequest(
                        access_token_callback_url=get_zoom_recall_oauth_callback_url(
                            user_id=request.user_id,
                            organization_id=request.organization_id,
                        ),
                        oauth_app=settings.recall_zoom_oauth_app_id,
                    ),
                )

            # TODO: copy from existing_integration
            now = zoned_utc_now()
            return UserIntegrationDto(
                user_integration=UserIntegration(
                    id=uuid4(),
                    user_id=request.user_id,
                    organization_id=request.organization_id,
                    user_auth_id=integration_provider_request.user_auth_id,
                    integration_type=IntegrationType.CONFERENCING,
                    integration_provider=IntegrationProvider.ZOOM,
                    external_id=integration_provider_request.external_id,
                    created_at=now,
                    updated_at=now,
                ),
            )

        return await self.create_or_update_grant_by_nylas(
            request=request,
            existing_integration=existing_integration,
        )

    async def create_or_update_grant_by_nylas(
        self,
        request: CreateUserIntegrationRequest,
        existing_integration: UserIntegrationDto | None = None,
    ) -> UserIntegrationDto:
        # NOTE: this function does not have other side-effects than Nylas grant external
        # API call. No internal data objects will be updated or created. Only the DTO
        # obj is instantiated.
        match request.integration_provider_request:
            case GoogleOAuthIntegrationRequest():
                google_oauth: GoogleOAuthIntegrationRequest = (
                    request.integration_provider_request
                )

                nylas_grant = await self.nylas.create_grant(
                    request=CreateGrantRequest(
                        provider="google",
                        google_refresh_token=google_oauth.refresh_token,
                    ),
                )
            case MicrosoftOAuthIntegrationRequest():
                ms_oauth: MicrosoftOAuthIntegrationRequest = (
                    request.integration_provider_request
                )
                nylas_grant = await self.nylas.create_grant(
                    request=CreateGrantRequest(
                        provider="microsoft",
                        microsoft_refresh_token=ms_oauth.refresh_token,
                    ),
                )
            case _:
                raise IllegalStateError(
                    "only google or microsoft oauth is supported, "
                    f"but got {request.integration_provider_request}",
                )

        existing_connectors_with_the_grant = await self.user_integration_repo.find_user_integration_connectors_by_external_id(
            connector_external_id=nylas_grant.id,
            connector_provider=IntegrationConnectorProvider.NYLAS,
        )

        if (
            existing_connectors_with_the_grant
            and len({coon.user_id for coon in existing_connectors_with_the_grant}) > 1
        ):
            # if the same grant is associated with multiple users (not supported
            # use-case yet), raise an error
            raise ConflictResourceError(
                "another user is associated with the connection"
            )

        new_external_created_at = zoned_utc_from_timestamp(nylas_grant.created_at)
        new_external_updated_at = (
            zoned_utc_from_timestamp(nylas_grant.updated_at)
            if nylas_grant.updated_at
            else None
        )
        new_metadata = NylasConnectorMetadata(
            status=nylas_grant.grant_status,
            state=nylas_grant.state,
            scope=nylas_grant.scope,
            email=nylas_grant.email,
        )
        new_external_id = nylas_grant.id
        now = zoned_utc_now()

        existing_db_integration = (
            existing_integration.user_integration if existing_integration else None
        )
        existing_db_integration_connector = (
            existing_integration.user_integration_connector
            if existing_integration
            else None
        )

        dirty_user_integration: UserIntegration
        dirty_user_integration_connector: UserIntegrationConnector

        if existing_db_integration:
            dirty_user_integration = existing_db_integration.model_copy(
                update={
                    "updated_at": now,
                    "connected_email": nylas_grant.email,
                    "deleted_at": None,
                    "user_auth_id": request.integration_provider_request.user_auth_id,
                },
            )
            if not existing_db_integration_connector:
                raise ValueError("expected existing db integration connector")
            dirty_user_integration_connector = (
                existing_db_integration_connector.model_copy(
                    update={
                        "external_id": new_external_id,
                        "external_created_at": new_external_created_at,
                        "external_updated_at": new_external_updated_at
                        or existing_db_integration_connector.external_updated_at,
                        # TODO: (hao) refactor this func to nylas integration
                        "status": get_connector_status_from_nylas_status(
                            NylasGrantStatus(nylas_grant.grant_status)
                        ),
                        "metadata": new_metadata,
                        "updated_at": now,
                        "deleted_at": None,
                    },
                )
            )
        else:
            dirty_user_integration_connector = UserIntegrationConnector(
                id=uuid4(),
                user_id=request.user_id,
                organization_id=request.organization_id,
                connector_provider=IntegrationConnectorProvider.NYLAS,
                external_id=nylas_grant.id,
                status=IntegrationConnectorStatus.CONNECTED,
                metadata=new_metadata,
                external_created_at=new_external_created_at,
                external_updated_at=new_external_updated_at or new_external_created_at,
                created_at=now,
                updated_at=now,
            )
            dirty_user_integration = UserIntegration(
                id=uuid4(),
                connected_email=nylas_grant.email,
                user_id=request.user_id,
                organization_id=request.organization_id,
                user_auth_id=request.integration_provider_request.user_auth_id,
                integration_type=request.integration_type,
                integration_provider=request.integration_provider_request.integration_provider,
                user_integration_connector_id=dirty_user_integration_connector.id,
                created_at=now,
                updated_at=now,
            )
        return UserIntegrationDto(
            user_integration=dirty_user_integration,
            user_integration_connector=dirty_user_integration_connector,
        )

    async def retrieve_zoom_access_token_from_db(
        self, user_id: UUID, organization_id: UUID
    ) -> str:
        (
            _db_user_integration,
            db_user_auth,
        ) = await self.user_integration_repo.get_user_integration_and_user_auth_by_user_id(
            user_id=user_id,
            organization_id=organization_id,
            integration_type=IntegrationType.CONFERENCING,
        )
        if db_user_auth is None:
            logger.error(
                f"user: {user_id} is not connected with zoom. Recall ai can't get access token"
            )
            return ""

        logger.info(f"zoom access token will be expired at: {db_user_auth.expires_at}")
        if db_user_auth.expires_at and (
            zoned_utc_now()
            > zoned_utc_from_timestamp(
                int(db_user_auth.expires_at.timestamp()) - (20 * 60)
            )
        ):
            logger.info(
                f"Zoom access token is expired for user: {user_id}, refreshing now"
            )
            return await self.conference_service.refresh_zoom_expired_token(
                user_id=user_id, organization_id=organization_id
            )

        return (
            self.encryption_manager.decrypt(db_user_auth.access_token)
            if db_user_auth.access_token
            else ""
        )

    async def zoom_get_token(self, code: str) -> Token:
        return await self.zoom.get_token(code=code)

    async def zoom_get_user_id(self, token: str) -> str:
        return await self.zoom.get_user_id(token=token)

    async def zoom_refresh_access_token(self, refresh_token: str) -> Token:
        return await self.zoom.refresh_access_token(refresh_token=refresh_token)

    async def _revoke_integration_access_token(
        self, user_integration: UserIntegration, access_token: str
    ) -> None:
        if not access_token:
            logger.error(
                "access token is empty",
                user_integration=user_integration,
            )
            return

        match user_integration.integration_provider:
            case IntegrationProvider.ZOOM:
                await self.zoom.revoke_access_token(access_token=access_token)
            case IntegrationProvider.GOOGLE:
                await self.google.revoke_token(access_token=access_token)
            case _:
                raise ValueError(
                    f"Unsupported integration provider for revoking token: {user_integration.integration_provider}"
                )

    async def _delete_integration(
        self, existing_user_integration: UserIntegration
    ) -> UserIntegrationResponse:
        utc_now = zoned_utc_now()
        deleted = not_none(
            await self.user_integration_repo.update_instance(
                existing_user_integration.model_copy(
                    update={
                        "updated_at": utc_now,
                        "deleted_at": utc_now,
                    }
                )
            )
        )
        logger.info(f"delete: {deleted}")
        auth_id = existing_user_integration.user_auth_id
        user_auth = await self.oauth_repository.find_by_tenanted_primary_key(
            OAuthUserAuth,
            id=auth_id,
            organization_id=existing_user_integration.organization_id,
        )
        deleted_auth_status = IntegrationStatus.BROKEN
        if not user_auth:
            logger.error(
                f"not auth available for deletion for user_integration {existing_user_integration}"
            )
        else:
            access_token = (
                self.encryption_manager.decrypt(user_auth.access_token)
                if user_auth.access_token
                else ""
            )
            await self._revoke_integration_access_token(
                user_integration=existing_user_integration,
                access_token=access_token,
            )

            deleted_auth = not_none(
                await self.oauth_repository.update_instance(
                    user_auth.model_copy(
                        update={"status": OAuthStatus.INACTIVE, "expires_at": utc_now}
                    )
                )
            )
            deleted_auth_status = IntegrationStatus.from_oauth_status(
                deleted_auth.status
            )
        return UserIntegrationResponse(
            user_id=deleted.user_id,
            email_account_id=deleted.email_account_id,
            organization_id=deleted.organization_id,
            user_integration_id=deleted.id,
            integration_type=deleted.integration_type,
            integration_provider=deleted.integration_provider,
            integration_status=deleted_auth_status,
            integration_calendar_account_id=deleted.calendar_account_id,
        )

    async def get_or_refresh_google_access_token(
        self,
        organization_id: UUID,
        user_id: UUID,
        integration_type: IntegrationType,
    ) -> str:
        (
            _,
            user_auth,
        ) = await self.user_integration_repo.get_user_integration_and_user_auth_by_user_id(  # TODO: change to email account id for multiple emails
            user_id=user_id,
            organization_id=organization_id,
            integration_type=integration_type,
        )
        if not user_auth:
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
                integration_type=integration_type,
            ).info("User auth not found.")
            raise ResourceNotFoundError("User auth not found.")

        if not user_auth.access_token or not user_auth.refresh_token:
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
                integration_type=integration_type,
            ).info("User auth does not have refresh token or access token.")
            raise ServiceError("User auth missing access or refresh token.")

        # directly return if access token not expired (add 2 mins buffer)
        if user_auth.expires_at and user_auth.expires_at > zoned_utc_now() + timedelta(
            minutes=2
        ):
            return self.encryption_manager.decrypt(
                not_none(user_auth.access_token)
            )  # access token is encrypted in the db

        google_token = await self.google.refresh_access_token(
            encrypted_refresh_token=(not_none(user_auth.refresh_token))
        )
        # Update OAuth user auth directly
        await self.oauth_repository.update_oauth_user_auth(
            oauth_user_auth_id=user_auth.id,
            organization_id=organization_id,
            access_token=self.encryption_manager.encrypt(
                google_token.access_token
            ),  # access token is not normally encrypted in GoogleToken
            expires_at=zoned_utc_from_timestamp(
                int(zoned_utc_now().timestamp()) + google_token.expires_in,
            ),
            refresh_token=not_none(user_auth.refresh_token),
        )

        return google_token.access_token


def user_integration_service(request: Request) -> UserIntegrationService:
    db_engine = get_db_engine(request=request)
    user_integration_repository = UserIntegrationRepository(engine=db_engine)
    oauth_repository = OauthRepository(engine=db_engine)
    nylas = NylasClient(
        v3_api_key=settings.nylas_v3_api_key.get_secret_value(),
        v3_base_url=settings.nylas_v3_api_url,
        v3_google_override_cred_id=settings.nylas_v3_google_app_override_cred_id,
    )
    encryption_manager = fernet_encryption_manager
    zoom = ZoomClient()
    recall = RecallAIClientV2(
        recall_v2_api_key=settings.recall_v1_api_key,
        recall_v2_base_url=settings.recall_v2_base_url,
    )
    google = GoogleClient(encryption_manager)
    conference_service = ConferenceService(
        user_integration_repository=user_integration_repository,
        oauth_repository=oauth_repository,
        zoom=zoom,
        google=google,
        encryption_manager=fernet_encryption_manager,
    )
    provider_clients = ProviderClients(
        nylas=nylas, google=google, zoom=zoom, recall=recall
    )
    email_account_service_ext = EmailAccountServiceExt(engine=db_engine)
    calendar_account_service = CalendarAccountService(engine=db_engine)
    user_calendar_repository = UserCalendarRepository(engine=db_engine)
    user_calendar_sync_service = get_user_calendar_sync_service_by_db_engine(db_engine)
    return UserIntegrationService(
        user_integration_repository=user_integration_repository,
        oauth_repository=oauth_repository,
        provider_clients=provider_clients,
        encryption_manager=encryption_manager,
        conference_service=conference_service,
        email_account_service_ext=email_account_service_ext,
        calendar_account_service=calendar_account_service,
        user_calendar_repository=user_calendar_repository,
        hubspot_service=get_hubspot_service_by_db_engine(db_engine=db_engine),
        user_calendar_sync_service=user_calendar_sync_service,
    )


def get_user_integration_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> UserIntegrationService:
    user_integration_repository = UserIntegrationRepository(engine=db_engine)
    oauth_repository = OauthRepository(engine=db_engine)
    nylas = NylasClient(
        v3_api_key=settings.nylas_v3_api_key.get_secret_value(),
        v3_base_url=settings.nylas_v3_api_url,
        v3_google_override_cred_id=settings.nylas_v3_google_app_override_cred_id,
    )
    encryption_manager = fernet_encryption_manager
    zoom = ZoomClient()
    recall = RecallAIClientV2(
        recall_v2_api_key=settings.recall_v1_api_key,
        recall_v2_base_url=settings.recall_v2_base_url,
    )
    google = GoogleClient(encryption_manager)
    conference_service = ConferenceService(
        user_integration_repository=user_integration_repository,
        oauth_repository=oauth_repository,
        zoom=zoom,
        google=google,
        encryption_manager=fernet_encryption_manager,
    )
    provider_clients = ProviderClients(
        nylas=nylas, google=google, zoom=zoom, recall=recall
    )
    email_account_service_ext = EmailAccountServiceExt(engine=db_engine)
    calendar_account_service = CalendarAccountService(engine=db_engine)
    user_calendar_repository = UserCalendarRepository(engine=db_engine)
    user_calendar_sync_service = get_user_calendar_sync_service_by_db_engine(db_engine)
    return UserIntegrationService(
        user_integration_repository=user_integration_repository,
        oauth_repository=oauth_repository,
        provider_clients=provider_clients,
        encryption_manager=encryption_manager,
        conference_service=conference_service,
        email_account_service_ext=email_account_service_ext,
        calendar_account_service=calendar_account_service,
        user_calendar_repository=user_calendar_repository,
        hubspot_service=get_hubspot_service_by_db_engine(db_engine=db_engine),
        user_calendar_sync_service=user_calendar_sync_service,
    )


def get_user_integration_service(request: Request) -> UserIntegrationService:
    return get_user_integration_service_by_db_engine(db_engine=get_db_engine(request))
