from datetime import timedelta
from functools import reduce
from http import HTTPStatus
from operator import add
from typing import cast
from uuid import UUI<PERSON>, uuid4

from fastapi import HTTP<PERSON>x<PERSON>, status
from pydantic import BaseModel, EmailStr
from slugify import slugify

from salestech_be.common.exception import (
    ConflictResourceError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.singleton import Singleton
from salestech_be.core.account.service.account_service import (
    AccountService,
    get_account_service,
)
from salestech_be.core.calendar.user_calendar_common_service import (
    UserCalendarCommonService,
    get_user_calendar_common_service_by_db_engine,
)
from salestech_be.core.contact.service.contact_resolve_service import (
    ContactResolveService,
    get_contact_resolve_service,
)
from salestech_be.core.contact.service.contact_service import (
    ContactService,
    get_contact_service,
)
from salestech_be.core.contact.service_api_schema import (
    UpsertContactAccountRoleRequest,
)
from salestech_be.core.email.event.email_event_service import EmailEventService
from salestech_be.core.imports.models.import_csv_fields import AccountImportCsvFields
from salestech_be.core.imports.service.crm_sync_service import (
    CrmSyncService,
    get_crm_sync_service_from_db_engine,
)
from salestech_be.core.meeting.conference_service import (
    ConferenceService,
    init_conference_service_by_db_engine,
)
from salestech_be.core.meeting.meeting_service import (
    MeetingService,
    meeting_service_factory_general,
)
from salestech_be.core.variable.variable_service import (
    VariableService,
    get_variable_service,
)
from salestech_be.db.dao.calendar_account_repository import CalendarAccountRepository
from salestech_be.db.dao.event_schedule_repository import EventScheduleRepository
from salestech_be.db.dao.user_calendar_repository import UserCalendarRepository
from salestech_be.db.dao.user_integration_repository import UserIntegrationRepository
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.event_schedule_dto import EventScheduleDto
from salestech_be.db.models.calendar_account import CalendarAccount
from salestech_be.db.models.contact import (
    CreateContactAccountRoleRequest,
    CreateContactRequest,
    CreateDbContactEmailRequest,
    CreateDbContactRequest,
)
from salestech_be.db.models.event_schedule import (
    AvailabilityMethod,
    EventSchedule,
    EventScheduleBooking,
    EventScheduleBookingHost,
    EventScheduleHost,
    EventScheduleOpenHour,
    EventScheduleStatus,
    EventScheduleType,
    HostAvailabilityType,
)
from salestech_be.db.models.organization import Organization
from salestech_be.db.models.user import User
from salestech_be.db.models.user_calendar_event import (
    BufferInfo,
    UserCalendarEvent,
    build_group_key,
)
from salestech_be.db.models.user_integration import (
    IntegrationProvider,
    IntegrationStatus,
)
from salestech_be.integrations.nylas.async_nylas_client import AsyncNylasClient
from salestech_be.integrations.nylas.model import (
    AvailabilityBuffer,
    ConfigurationAvailability,
    ConfigurationAvailabilityRules,
    ConfigurationEventBooking,
    ConfigurationParticipant,
    ConfigurationParticipantAvailability,
    ConfigurationParticipantBooking,
    ConfigurationScheduler,
    DefaultOpenHour,
    NylasConferencingAutocreate,
    NylasConferencingDetails,
    NylasConferencingProvider,
    NylasCreateConfigurationRequest,
    NylasCreateEventRequest,
    NylasCreateScheduleBookingRequest,
    NylasCreateTimespan,
    NylasDeleteScheduleBookingRequest,
    NylasEvent,
    NylasScheduleBookingGuest,
    NylasScheduleBookingParticipant,
    NylasUpdateConfigurationRequest,
    NylasUpdateEventRequest,
    NylasUpdateScheduleBookingRequest,
    Visibility,
)
from salestech_be.integrations.nylas.model import (
    AvailabilityMethod as NylasAvailabilityMethod,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import TemporalTaskQueue
from salestech_be.integrations.zoom.type import UpdateMeetingRequest
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.workflows.event_schedule import EventScheduleWorkflow
from salestech_be.temporal.workflows.user_calendar.user_calendar_event_meeting_sync_workflow import (
    UserCalendarEventMeetingCreateWorkflow,
    UserCalendarEventMeetingCreateWorkflowInput,
)
from salestech_be.util.email_utils import (
    format_email_username_as_display_name,
    generate_first_and_last_name,
)
from salestech_be.util.time import (
    zoned_utc_from_timestamp,
    zoned_utc_now,
)
from salestech_be.util.validation import not_none
from salestech_be.web.api.email.common.utils import PUBLIC_EMAIL_DOMAIN
from salestech_be.web.api.schedule.schema import (
    BookingStatus,
    CreateEventScheduleHost,
    CreateEventScheduleRequest,
    CreateScheduleBooking,
    DeleteScheduleBooking,
    GetEventScheduleAvailabilityRequest,
    Organizer,
    SchedulingType,
    TimeSlot,
    UpdateEventScheduleRequest,
    UpdateScheduleBooking,
)

logger = get_logger("service.calendar")


class HostInfo(BaseModel):
    user_id: UUID
    calendar_account_id: UUID
    name: str
    availability_type: HostAvailabilityType


class EventScheduleService:
    def __init__(
        self,
        async_nylas_client: AsyncNylasClient,
        user_integration_repo: UserIntegrationRepository,
        user_cal_repo: UserCalendarRepository,
        user_repo: UserRepository,
        event_schedule_repo: EventScheduleRepository,
        user_calendar_common_service: UserCalendarCommonService,
        meeting_service: MeetingService,
        conference_service: ConferenceService,
        email_event_service: EmailEventService,
        calendar_account_repo: CalendarAccountRepository,
        crm_sync_service: CrmSyncService,
        account_service: AccountService,
        contact_service: ContactService,
        variable_service: VariableService,
        contact_resolve_service: ContactResolveService,
    ):
        self.async_nylas_client = async_nylas_client
        self.user_integration_repo = user_integration_repo
        self.user_cal_repo = user_cal_repo
        self.user_repo = user_repo
        self.event_schedule_repo = event_schedule_repo
        self.user_calendar_common_service = user_calendar_common_service
        self.meeting_service = meeting_service
        self.conference_service = conference_service
        self.email_event_service = email_event_service
        self.calendar_account_repo = calendar_account_repo
        self.crm_sync_service = crm_sync_service
        self.account_service = account_service
        self.contact_service = contact_service
        self.variable_service = variable_service
        self.contact_resolve_service = contact_resolve_service

    async def create_event_schedule(
        self,
        user: User,
        organization_id: UUID,
        request: CreateEventScheduleRequest,
    ) -> EventScheduleDto:
        logger.bind(
            user_id=user.id,
            organization_id=organization_id,
            request=request,
        ).info("Create event schedule request")
        # 1. generate Nylas request
        if not request.calendar_account_id:
            calendar_account = (
                await self.calendar_account_repo.find_default_calendar_account_on_error(
                    user_id=user.id,
                    organization_id=organization_id,
                )
            )
        else:
            calendar_account = (
                await self.calendar_account_repo.find_by_tenanted_primary_key_or_fail(
                    CalendarAccount,
                    id=request.calendar_account_id,
                    organization_id=organization_id,
                )
            )
        # get calendar account email
        calendar_account_id = calendar_account.id
        calendar_account_email = calendar_account.email
        user_integration_dto = await self.user_integration_repo.find_user_integration_by_calendar_account_or_error(
            calendar_account_id=calendar_account_id,
            organization_id=organization_id,
        )
        primary_cal = await self.user_cal_repo.find_user_primary_calendar_by_calendar_account_or_error(
            calendar_account_id=calendar_account_id,
            organization_id=organization_id,
        )

        default_open_hours: list[DefaultOpenHour] = []
        if request.availability and request.availability.default_open_hours:
            for open_hour in request.availability.default_open_hours:
                oh = DefaultOpenHour(
                    days=open_hour.days,
                    start=open_hour.start,
                    end=open_hour.end,
                    exdates=open_hour.exdates,
                )
                default_open_hours.append(oh)
        else:
            # TODO: get default from user availability config
            default_open_hours = [
                DefaultOpenHour(days=[1, 2, 3, 4, 5], start="9:00", end="17:00")
            ]

        participants: list[ConfigurationParticipant] = []
        host_user_ids: list[UUID] = []
        host_email_info_map: dict[str, HostInfo] = {}
        if request.hosts:
            (
                participants,
                host_user_ids,
                host_email_info_map,
            ) = await self._process_hosts(
                hosts=request.hosts,
                organization_id=organization_id,
                default_open_hours=default_open_hours,
            )
        else:
            participants.append(
                ConfigurationParticipant(
                    name=user.display_name,
                    email=calendar_account_email,
                    is_organizer=True,
                    availability=ConfigurationParticipantAvailability(
                        calendar_ids=[primary_cal.external_id]
                    ),
                    booking=ConfigurationParticipantBooking(
                        calendar_id=primary_cal.external_id
                    ),
                )
            )
        availability_method: NylasAvailabilityMethod = "collective"
        if (
            request.event_schedule_type == EventScheduleType.ROUND_ROBIN
            and request.availability
        ):
            availability_method = (
                "max-fairness"
                if request.availability.availability_method
                == AvailabilityMethod.MAX_FAIRNESS
                else "max-availability"
            )

        nylas_request = NylasCreateConfigurationRequest(
            version="1.0.0",
            participants=participants,
            availability=ConfigurationAvailability(
                duration_minutes=request.duration_minutes,
                round_to_30_minutes=True,
                availability_rules=ConfigurationAvailabilityRules(
                    default_open_hours=default_open_hours,
                    availability_method=availability_method,
                    buffer=AvailabilityBuffer(
                        before=request.availability.buffer.before,
                        after=request.availability.buffer.after,
                    )
                    if request.availability and request.availability.buffer
                    else None,
                ),
            ),
            event_booking=ConfigurationEventBooking(
                title=request.scheduler_title,
                description=request.event_description,
                timezone=request.timezone,
                disable_emails=True,
                conferencing=NylasConferencingAutocreate(
                    provider=cast(
                        NylasConferencingProvider,
                        request.conferencing_provider,
                    ),
                    autocreate={},
                )
                if request.conferencing_provider in ["Google Meet", "Microsoft Teams"]
                else None,
            ),
            scheduler=ConfigurationScheduler(
                available_days_in_future=request.available_days_in_future
            ),
        )
        # 2. call nylas request
        nylas_configuration = (
            await self.async_nylas_client.create_scheduler_configuration(
                grant_id=not_none(
                    user_integration_dto.user_integration_connector
                ).external_id,
                request=nylas_request,
            )
        )
        # 3. persist object from nylas
        event_schedule = EventSchedule.from_nylas(
            user_id=user.id,
            organization_id=organization_id,
            calendar_account_id=calendar_account_id,
            nylas_configuration=nylas_configuration,
            conferencing_provider=request.conferencing_provider,
            enable_recording=request.enable_recording,
            event_schedule_type=request.event_schedule_type,
            host_user_ids=host_user_ids,
            status=EventScheduleStatus.ACTIVE,
            create_buffer_events=request.create_buffer_events,
            visibility=request.visibility,
            event_title_template=request.event_title_template,
        )

        event_schedule = await self._enrich_event_schedule(
            user=user,
            organization_id=organization_id,
            event_schedule=event_schedule,
        )

        event_schedule_hosts = (
            [
                EventScheduleHost(
                    id=uuid4(),
                    event_schedule_id=event_schedule.id,
                    user_id=host_email_info_map[participant.email].user_id,
                    organization_id=organization_id,
                    calendar_account_id=host_email_info_map[
                        participant.email
                    ].calendar_account_id,
                    email=participant.email,
                    is_organizer=participant.is_organizer
                    if participant.is_organizer is not None
                    else False,
                    availability_type=host_email_info_map[
                        participant.email
                    ].availability_type,
                    open_hours=[
                        EventScheduleOpenHour(
                            days=oh.days,
                            start=oh.start,
                            end=oh.end,
                            exdates=oh.exdates if oh.exdates else [],
                        )
                        for oh in participant.availability.open_hours
                    ]
                    if participant.availability is not None
                    and participant.availability.open_hours is not None
                    else [],
                    created_at=event_schedule.created_at,
                    created_by_user_id=user.id,
                    updated_at=event_schedule.updated_at,
                    updated_by_user_id=user.id,
                )
                for participant in nylas_configuration.participants
                if host_email_info_map.get(participant.email) is not None
            ]
            if nylas_configuration.participants
            else None
        )

        return await self.event_schedule_repo.create_event_schedule_dto(
            EventScheduleDto(
                event_schedule=event_schedule, event_schedule_hosts=event_schedule_hosts
            )
        )

    async def is_event_title_template_valid(
        self, event_title_template: str | None
    ) -> bool:
        if not event_title_template:
            return True

        phs = self.variable_service.get_variable_placeholders_without_space(
            event_title_template
        )
        event_title_variables = await self.variable_service.list_scheduler_variables()
        supported_phs: set[str] = set(
            reduce(
                add,
                [
                    [field.display_value for field in var.fields]
                    for var in event_title_variables
                ],
                [],
            )
        )
        if len(set(phs) - supported_phs) > 0:
            return False
        return True

    async def _enrich_event_schedule(
        self,
        *,
        user: User,
        organization_id: UUID,
        event_schedule: EventSchedule,
    ) -> EventSchedule:
        active_user_org_association_dto = not_none(
            await self.user_repo.find_active_user_organization_association(
                user_id=user.id,
                organization_id=organization_id,
            )
        )

        # (colin): This is the identifier used for the calendar account integration?
        user_org_identifier = not_none(
            active_user_org_association_dto.user_organization_association.user_org_identifier
        )
        event_title_slug = await self.generate_title_slug(
            user_id=user.id,
            organization_id=organization_id,
            scheduler_title=event_schedule.scheduler_title,
            duration_minutes=event_schedule.duration_minutes,
        )

        logger.bind(
            user_id=user.id,
            organization_id=organization_id,
            event_schedule_id=event_schedule.id,
            event_title_slug=event_title_slug,
        ).info("Enriched event schedule with generated event title slug")

        return event_schedule.model_copy(
            update={
                "event_title_slug": event_title_slug,
                "scheduling_page_url": f"{user_org_identifier}/{event_title_slug}",
            }
        )

    async def generate_title_slug(
        self,
        user_id: UUID,
        organization_id: UUID,
        scheduler_title: str,
        duration_minutes: int,
    ) -> str:
        """
        Generate a unique event title slug.

        First tries duration-based slug (e.g. "30min").
        If that exists, tries event title based slug.
        Falls back to adding incrementing numbers until a unique slug is found.

        Args:
            user_id: ID of the user
            organization_id: ID of the organization
            scheduler_title: Title of the event
            duration_minutes: Duration of event in minutes

        Returns:
            A unique event title slug
        """
        # Try duration-based slug first
        duration_slug = f"{duration_minutes}min"

        # Check if duration slug is already used
        existing_duration_schedule = (
            await self.event_schedule_repo.get_event_schedule_by_event_title_slug(
                event_title_slug=duration_slug,
                user_id=user_id,
                organization_id=organization_id,
            )
        )

        if not existing_duration_schedule:
            return duration_slug

        # Get all existing slugs for this user/org/title
        # Case insensitive since base slug will be lowercased below
        existing_slugs = await self.event_schedule_repo.get_event_title_slugs_by_user_id_and_scheduler_title(
            user_id=user_id,
            organization_id=organization_id,
            scheduler_title=scheduler_title,
        )

        # Generate unique slug by incrementing number suffix
        base_slug = slugify(scheduler_title)
        index = len(existing_slugs)

        candidate_slug = f"{base_slug}-{index}" if index else base_slug
        # Keep incrementing index until we find an unused slug
        while candidate_slug in existing_slugs:
            index += 1
            candidate_slug = f"{base_slug}-{index}"

        return candidate_slug

    async def switch_timezone_for_user_event_schedules(
        self,
        user_id: UUID,
        organization_id: UUID,
        timezone: str,
    ) -> list[EventScheduleDto]:
        logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            timezone=timezone,
        ).info("Switching timezone for user event schedules")

        updated_event_schedule_dtos: list[EventScheduleDto] = []
        calendar_accounts = await self.calendar_account_repo.find_all_calendar_events_by_owner_user_id_active(
            owner_user_id=user_id,
            organization_id=organization_id,
        )
        for calendar_account in calendar_accounts:
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
                calendar_account_id=calendar_account.id,
            ).info("Switching timezone for user event schedules for calendar account")
            event_schedule_dtos = await self.event_schedule_repo.find_event_schedule_by_user_id_and_calendar_account_id(
                user_id=user_id,
                organization_id=organization_id,
                calendar_account_id=calendar_account.id,
            )
            for event_schedule_dto in event_schedule_dtos:
                event_schedule = event_schedule_dto.event_schedule
                if event_schedule.timezone != timezone:
                    updated_event_schedule_dtos.append(
                        await self.update_event_schedule(
                            user_id=user_id,
                            organization_id=organization_id,
                            event_schedule_id=event_schedule.id,
                            request=UpdateEventScheduleRequest(timezone=timezone),
                        )
                    )
                else:
                    logger.bind(
                        user_id=user_id,
                        organization_id=organization_id,
                        calendar_account_id=calendar_account.id,
                        event_schedule_id=event_schedule.id,
                        event_schedule_title=event_schedule.scheduler_title,
                        timezone=timezone,
                    ).info("Event schedule already has the correct timezone")
        return updated_event_schedule_dtos

    async def update_event_schedule(  # noqa: C901
        self,
        user_id: UUID,
        organization_id: UUID,
        event_schedule_id: UUID,
        request: UpdateEventScheduleRequest,
    ) -> EventScheduleDto:
        logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            event_schedule_id=event_schedule_id,
            request=request,
        ).info("Updating event schedule")
        # 1. Get existing event schedule
        existing_event_schedule = (
            await self.event_schedule_repo.find_event_schedule_by_tenanted_id_or_error(
                event_schedule_id=event_schedule_id,
                organization_id=organization_id,
            )
        )
        user_integration_dto = await self.user_integration_repo.find_user_integration_by_calendar_account_or_error(
            calendar_account_id=not_none(existing_event_schedule.calendar_account_id),
            organization_id=organization_id,
        )

        # 2. Generate Nylas request
        availability: ConfigurationAvailability | None = None
        event_booking: ConfigurationEventBooking | None = None
        scheduler: ConfigurationScheduler | None = None
        default_open_hours: list[DefaultOpenHour] = []
        if request.availability and request.availability.default_open_hours:
            for open_hour in request.availability.default_open_hours:
                oh = DefaultOpenHour(
                    days=open_hour.days,
                    start=open_hour.start,
                    end=open_hour.end,
                    exdates=open_hour.exdates,
                )
                default_open_hours.append(oh)
        elif existing_event_schedule.open_hours:
            for o in existing_event_schedule.open_hours:
                oh = DefaultOpenHour(
                    days=o.days,
                    start=o.start,
                    end=o.end,
                    exdates=o.exdates,
                )
                default_open_hours.append(oh)

        availability_rules = (
            ConfigurationAvailabilityRules(
                default_open_hours=default_open_hours,
                availability_method=cast(
                    NylasAvailabilityMethod,
                    request.availability.availability_method.value,
                )
                if request.availability and request.availability.availability_method
                else cast(
                    NylasAvailabilityMethod,
                    existing_event_schedule.availability_method,
                ),
                buffer=AvailabilityBuffer(
                    before=request.availability.buffer.before,
                    after=request.availability.buffer.after,
                )
                if request.availability and request.availability.buffer
                else None,
            )
            if request.availability
            and (
                request.availability.default_open_hours
                or request.availability.availability_method
                or request.availability.buffer
            )
            else None
        )
        if request.duration_minutes or availability_rules:
            availability = ConfigurationAvailability(
                duration_minutes=request.duration_minutes,
                availability_rules=availability_rules,
            )
        if (
            request.conferencing_provider
            or request.scheduler_title
            or request.event_description
            or request.timezone
        ):
            event_booking = ConfigurationEventBooking(
                title=request.scheduler_title
                if request.scheduler_title
                else existing_event_schedule.scheduler_title,
                description=request.event_description,
                timezone=request.timezone,
                conferencing=NylasConferencingAutocreate(
                    provider=cast(
                        NylasConferencingProvider,
                        request.conferencing_provider,
                    ),
                    autocreate={},
                )
                if request.conferencing_provider in ["Google Meet", "Microsoft Teams"]
                else None,
            )

        if request.available_days_in_future:
            scheduler = ConfigurationScheduler(
                available_days_in_future=request.available_days_in_future
            )

        # Update participants if hosts are provided
        participants: list[ConfigurationParticipant] = []
        host_user_ids: list[UUID] = []
        host_email_info_map: dict[str, HostInfo] = {}
        if request.hosts:
            (
                participants,
                host_user_ids,
                host_email_info_map,
            ) = await self._process_hosts(
                hosts=request.hosts,
                organization_id=organization_id,
                default_open_hours=default_open_hours,
            )
        else:
            user = await self.user_repo.find_user_by_id_and_organization_id(
                user_id=user_id, organization_id=organization_id
            )
            if not user:
                raise ResourceNotFoundError("User not found")
            calendar_account = (
                await self.calendar_account_repo.find_default_calendar_account_on_error(
                    user_id=user_id,
                    organization_id=organization_id,
                )
            )
            primary_cal = await self.user_cal_repo.find_user_primary_calendar_by_calendar_account_or_error(
                calendar_account_id=calendar_account.id,
                organization_id=organization_id,
            )
            participants.append(
                ConfigurationParticipant(
                    name=user.display_name,
                    email=calendar_account.email,
                    is_organizer=True,
                    availability=ConfigurationParticipantAvailability(
                        calendar_ids=[primary_cal.external_id]
                    ),
                    booking=ConfigurationParticipantBooking(
                        calendar_id=primary_cal.external_id
                    ),
                )
            )
        # 3. Update nylas configuration
        updated_nylas_configuration = (
            await self.async_nylas_client.update_scheduler_configuration(
                grant_id=not_none(
                    user_integration_dto.user_integration_connector
                ).external_id,
                configuration_id=existing_event_schedule.external_id,
                request=NylasUpdateConfigurationRequest(
                    availability=availability,
                    event_booking=event_booking,
                    scheduler=scheduler,
                    participants=participants,
                ),
            )
        )
        if not updated_nylas_configuration:
            raise InvalidArgumentError(
                "Nylas Scheduler configuration is already deleted",
            )

        # 4. Update event schedule
        updated_event_schedule = EventSchedule.from_nylas(
            user_id=user_id,
            organization_id=organization_id,
            calendar_account_id=not_none(existing_event_schedule.calendar_account_id),
            nylas_configuration=updated_nylas_configuration,
            conferencing_provider=request.conferencing_provider
            if request.conferencing_provider
            else existing_event_schedule.conferencing_provider,
            enable_recording=request.enable_recording
            if request.enable_recording is not None
            else existing_event_schedule.enable_recording,
            event_schedule_type=existing_event_schedule.event_schedule_type,
            host_user_ids=host_user_ids,
            status=existing_event_schedule.status,
            create_buffer_events=request.create_buffer_events
            if request.create_buffer_events is not None
            else existing_event_schedule.create_buffer_events,
            visibility=request.visibility
            if request.visibility is not None
            else not_none(existing_event_schedule.visibility),
            event_title_template=request.event_title_template
            if request.event_title_template is not None
            else existing_event_schedule.event_title_template,
        ).model_copy(
            update={
                "id": existing_event_schedule.id,
                "is_disabled": request.is_disabled,
                "external_id": existing_event_schedule.external_id,
                "created_at": existing_event_schedule.created_at,
                "scheduling_page_url": existing_event_schedule.scheduling_page_url,
                "event_title_slug": existing_event_schedule.event_title_slug,
            }
        )
        updated_event_schedule_hosts = (
            [
                EventScheduleHost(
                    id=uuid4(),
                    event_schedule_id=existing_event_schedule.id,
                    user_id=host_email_info_map[participant.email].user_id,
                    organization_id=organization_id,
                    calendar_account_id=host_email_info_map[
                        participant.email
                    ].calendar_account_id,
                    email=participant.email,
                    is_organizer=participant.is_organizer
                    if participant.is_organizer is not None
                    else False,
                    availability_type=host_email_info_map[
                        participant.email
                    ].availability_type,
                    open_hours=[
                        EventScheduleOpenHour(
                            days=oh.days,
                            start=oh.start,
                            end=oh.end,
                            exdates=oh.exdates if oh.exdates else [],
                        )
                        for oh in participant.availability.open_hours
                    ]
                    if participant.availability is not None
                    and participant.availability.open_hours is not None
                    else [],
                    created_at=updated_event_schedule.created_at,
                    created_by_user_id=user_id,
                    updated_at=updated_event_schedule.updated_at,
                    updated_by_user_id=user_id,
                )
                for participant in updated_nylas_configuration.participants
                if host_email_info_map.get(participant.email) is not None
            ]
            if updated_nylas_configuration.participants
            else None
        )

        # 6. Return EventScheduleDto
        return await self.event_schedule_repo.update_event_schedule_dto(
            EventScheduleDto(
                event_schedule=updated_event_schedule,
                event_schedule_hosts=updated_event_schedule_hosts,
            )
        )

    async def list_event_schedules(
        self,
        user_id: UUID,
        organization_id: UUID,
        include_shared: bool = False,
    ) -> list[EventScheduleDto]:
        logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            include_shared=include_shared,
        ).info("List event schedules request")
        default_calendar_account = (
            await self.calendar_account_repo.find_default_calendar_account_on_error(
                user_id=user_id,
                organization_id=organization_id,
            )
        )
        if include_shared:
            hosts = await self.event_schedule_repo.find_event_schedule_hosts_by_user_id_and_calendar_account_id(
                user_id=user_id,
                organization_id=organization_id,
                calendar_account_id=default_calendar_account.id,
            )
            event_schedule_ids = [host.event_schedule_id for host in hosts]
            return await self.event_schedule_repo.list_event_schedule_dto_by_ids(
                ids=event_schedule_ids,
                organization_id=organization_id,
            )
        else:
            return await self.event_schedule_repo.find_event_schedule_by_user_id_and_calendar_account_id(
                calendar_account_id=default_calendar_account.id,
                user_id=user_id,
                organization_id=organization_id,
            )

    async def get_event_schedule_by_id(
        self, event_schedule_id: UUID, organization_id: UUID, user_id: UUID
    ) -> EventScheduleDto:
        logger.bind(
            event_schedule_id=event_schedule_id,
            organization_id=organization_id,
            user_id=user_id,
        ).info("Get event schedule by id request")
        return await self.event_schedule_repo.get_event_schedule_dto(
            event_schedule_id=event_schedule_id,
            organization_id=organization_id,
        )

    async def delete_event_schedule(
        self, user_id: UUID, organization_id: UUID, event_schedule_id: UUID
    ) -> EventScheduleDto:
        logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            event_schedule_id=event_schedule_id,
        ).info("Delete event schedule request")
        # 1. Get existing event schedules
        existing_event_schedule = (
            await self.event_schedule_repo.find_event_schedule_by_tenanted_id_or_error(
                event_schedule_id=event_schedule_id,
                organization_id=organization_id,
            )
        )
        user_integration_dto = await self.user_integration_repo.find_user_integration_by_calendar_account_or_error(
            calendar_account_id=not_none(existing_event_schedule.calendar_account_id),
            organization_id=organization_id,
        )

        # 2. Delete from Nylas
        await self.async_nylas_client.delete_scheduler_configuration(
            grant_id=not_none(
                user_integration_dto.user_integration_connector
            ).external_id,
            configuration_id=existing_event_schedule.external_id,
        )

        # 3. update db
        utc_now = zoned_utc_now()
        delete_event_schedule = (
            await self.event_schedule_repo.update_by_tenanted_primary_key(
                table_model=EventSchedule,
                primary_key_to_value={"id": existing_event_schedule.id},
                organization_id=organization_id,
                column_to_update={
                    "updated_at": utc_now,
                    "deleted_at": utc_now,
                    "deleted_by_user_id": user_id,
                },
            )
        )

        if delete_event_schedule is None:
            raise ResourceNotFoundError("Event schedule not found or already deleted")

        deleted_hosts = await self.event_schedule_repo._update_by_column_values(
            table_model=EventScheduleHost,
            column_value_to_query={
                "event_schedule_id": delete_event_schedule.id,
                "organization_id": organization_id,
            },
            column_to_update={
                "updated_at": utc_now,
                "deleted_at": utc_now,
                "deleted_by_user_id": user_id,
            },
        )
        return EventScheduleDto(
            event_schedule=delete_event_schedule,
            event_schedule_hosts=deleted_hosts,
        )

    async def get_schedule_availability(
        self,
        request: GetEventScheduleAvailabilityRequest,
        event_schedule_id: UUID,
        booking_id: UUID | None = None,
    ) -> tuple[EventSchedule, list[User], list[TimeSlot]]:
        logger.bind(
            event_schedule_id=event_schedule_id,
            booking_id=booking_id,
            request=request,
        ).info("Get schedule availability request")
        # 1. Get Event Schedule
        event_schedule = (
            await self.event_schedule_repo.find_event_schedule_by_id_or_error(
                event_schedule_id=event_schedule_id,
            )
        )

        return await self._get_schedule_availability_by_event_schedule(
            request=request,
            event_schedule=event_schedule,
            booking_id=booking_id,
        )

    async def _get_schedule_availability_by_event_schedule(
        self,
        *,
        request: GetEventScheduleAvailabilityRequest,
        event_schedule: EventSchedule,
        booking_id: UUID | None = None,
    ) -> tuple[EventSchedule, list[User], list[TimeSlot]]:
        starts_at = request.starts_at
        ends_at = request.ends_at

        booking = None
        if booking_id:
            booking = (
                await self.event_schedule_repo.find_event_schedule_booking_or_error(
                    event_schedule_booking_id=booking_id
                )
            )
        # 2. Get organizer user
        hosts = []
        if event_schedule.host_user_ids:
            for host_user_id in event_schedule.host_user_ids:
                organizer = await self.user_repo.find_user_by_id_or_error(
                    user_id=host_user_id
                )
                hosts.append(organizer)
        else:
            organizer = await self.user_repo.find_user_by_id_or_error(
                user_id=event_schedule.user_id
            )
            hosts.append(organizer)
        # 3. Verify user calendar integration is valid
        user_integration_dto = await self.user_integration_repo.find_user_integration_by_calendar_account_id(
            calendar_account_id=not_none(event_schedule.calendar_account_id),
            organization_id=event_schedule.organization_id,
        )
        if (
            not user_integration_dto
            or user_integration_dto.integration_status != IntegrationStatus.CONNECTED
        ):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User Calendar Integration is invalid.",
            )

        # 4. Get Availability from Nylas
        nylas_availability = await self.async_nylas_client.get_schedule_availability(
            nylas_configuration_id=event_schedule.external_id,
            nylas_booking_id=booking.external_id
            if booking
            and event_schedule.event_schedule_type == EventScheduleType.ROUND_ROBIN
            else None,
            starts_at=starts_at,
            ends_at=ends_at,
        )

        max_available_date = zoned_utc_now() + timedelta(
            days=event_schedule.available_days_in_future
        )
        timeslots: list[TimeSlot] = [
            TimeSlot(
                starts_at=zoned_utc_from_timestamp(slot.start_time),
                ends_at=zoned_utc_from_timestamp(slot.end_time),
            )
            for slot in nylas_availability.time_slots
            if zoned_utc_from_timestamp(slot.end_time) <= max_available_date
        ]

        return event_schedule, hosts, timeslots

    async def get_schedule_availability_by_user_org_identifier_and_event_title_slug(
        self,
        *,
        user_org_identifier: str,
        event_title_slug: str,
        request: GetEventScheduleAvailabilityRequest,
        booking_id: UUID | None = None,
    ) -> tuple[EventSchedule, list[User], list[TimeSlot]]:
        logger.bind(
            user_org_identifier=user_org_identifier,
            event_title_slug=event_title_slug,
            request=request,
        ).info("Get schedule availability by identifier and slug request")
        user_org_association = await self.user_repo.find_user_organization_association_by_user_org_identifier(
            user_org_identifier=user_org_identifier,
        )
        event_schedule = not_none(
            await self.event_schedule_repo._find_unique_by_column_values(
                EventSchedule,
                user_id=user_org_association.user_id,
                organization_id=user_org_association.organization_id,
                event_title_slug=event_title_slug,
            )
        )
        return await self._get_schedule_availability_by_event_schedule(
            request=request,
            event_schedule=event_schedule,
            booking_id=booking_id,
        )

    async def get_event_schedule_id_by_user_org_identifier_and_event_title_slug(
        self,
        *,
        user_org_identifier: str,
        event_title_slug: str,
    ) -> UUID:
        """
        Get the event schedule ID by user organization identifier and event title slug.

        Args:
            user_org_identifier: User organization identifier
            event_title_slug: Event title slug

        Returns:
            Event schedule ID
        """
        logger.bind(
            user_org_identifier=user_org_identifier,
            event_title_slug=event_title_slug,
        ).info("Get event schedule ID by identifier and slug request")
        user_org_association = await self.user_repo.find_user_organization_association_by_user_org_identifier(
            user_org_identifier=user_org_identifier,
        )
        event_schedule = not_none(
            await self.event_schedule_repo._find_unique_by_column_values(
                EventSchedule,
                user_id=user_org_association.user_id,
                organization_id=user_org_association.organization_id,
                event_title_slug=event_title_slug,
            )
        )
        return event_schedule.id

    async def create_event_schedule_booking(  # noqa: PLR0912, PLR0915, C901
        self, event_schedule_id: UUID, request: CreateScheduleBooking
    ) -> tuple[EventScheduleBooking, EventSchedule, Organizer]:
        logging_params = {
            "event_schedule_id": event_schedule_id,
        }
        task_logger = logger.bind(**logging_params)
        task_logger.bind(request=request).info("Create event schedule booking request")

        # 1. Get Event Schedule and Organizer data
        event_schedule = (
            await self.event_schedule_repo.find_event_schedule_by_id_or_error(
                event_schedule_id=event_schedule_id
            )
        )
        organization_id = event_schedule.organization_id

        # Check schedule availability for round-robin
        nylas_participants = None
        if event_schedule.event_schedule_type == EventScheduleType.ROUND_ROBIN:
            nylas_availability = (
                await self.async_nylas_client.get_schedule_availability(
                    nylas_configuration_id=event_schedule.external_id,
                    starts_at=request.starts_at - timedelta(seconds=1),
                    ends_at=request.ends_at + timedelta(seconds=1),
                )
            )
            if not nylas_availability.time_slots or not nylas_availability.order:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No availability for this time slot.",
                )
            participant_calendar_id = next(
                (
                    calendar_id
                    for calendar_id in nylas_availability.order
                    if calendar_id in nylas_availability.time_slots[0].emails
                ),
                None,
            )
            if not participant_calendar_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No available calendar found for this time slot.",
                )
            nylas_participants = [
                NylasScheduleBookingParticipant(email=participant_calendar_id)
            ]

            event_schedule_host = (
                await self.event_schedule_repo._find_unique_by_column_values(
                    EventScheduleHost,
                    email=participant_calendar_id,
                    organization_id=organization_id,
                    event_schedule_id=event_schedule_id,
                )
            )
            if not event_schedule_host:
                # TODO handle the scenario where the host is removed from our side but still available in Nylas
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="The host is no longer available.",
                )
            user_id = event_schedule_host.user_id
            calendar_account_id = event_schedule_host.calendar_account_id
        else:
            user_id = event_schedule.user_id
            calendar_account_id = not_none(event_schedule.calendar_account_id)

        user = await self.user_repo.find_user_by_id_or_error(user_id=user_id)
        user_integration_dto = await self.user_integration_repo.find_user_integration_by_calendar_account_or_error(
            calendar_account_id=calendar_account_id,
            organization_id=organization_id,
        )
        user_primary_calendar = await self.user_cal_repo.find_user_primary_calendar_by_calendar_account_or_error(
            calendar_account_id=calendar_account_id,
            organization_id=organization_id,
        )
        calendar_id = user_primary_calendar.external_id
        # Add guest to contact if not existing contact
        guest_email_name_map: dict[EmailStr, str] = {
            request.guest_email: request.guest_name
        }
        for additional_guest_email in request.additional_guest_emails:
            guest_email_name_map[additional_guest_email] = ""
        (
            created_contact_ids,
            guest_company_name,
            internal_main_guest_name,
        ) = await self._import_guests_as_contacts(
            organization_id=organization_id,
            user_id=user_id,
            main_guest_email=request.guest_email,
            main_guest_name=request.guest_name,
            guest_email_name_map=guest_email_name_map,
        )
        task_logger.info(
            f"created contacts while guest booking through scheduling page: {created_contact_ids}",
            user_id=user_id,
            organization_id=organization_id,
        )

        # 2. Book through Nylas
        nylas_booking = await self.async_nylas_client.create_schedule_booking(
            nylas_configuration_id=event_schedule.external_id,
            request=NylasCreateScheduleBookingRequest(
                start_time=int(request.starts_at.timestamp()),
                end_time=int(request.ends_at.timestamp()),
                guest=NylasScheduleBookingGuest(
                    name=request.guest_name, email=request.guest_email
                ),
                additional_guests=[
                    NylasScheduleBookingGuest(email=guest_email)
                    for guest_email in request.additional_guest_emails
                ]
                if request.additional_guest_emails
                else None,
                participants=nylas_participants,
            ),
        )
        # Get existing event to preserve conferencing details
        existing_event = await self.async_nylas_client.get_event(
            grant_id=not_none(
                user_integration_dto.user_integration_connector
            ).external_id,
            calendar_id=calendar_id,
            event_id=nylas_booking.event_id,
        )
        nylas_conferencing_detail = None
        if existing_event and existing_event.conferencing:
            nylas_conferencing_detail = NylasConferencingDetails(
                provider=existing_event.conferencing["provider"],
                details=existing_event.conferencing["details"],
            )
        # update event visibility
        if event_schedule.visibility:
            logger.bind(
                event_schedule_id=event_schedule_id,
                event_id=nylas_booking.event_id,
                visibility=event_schedule.visibility,
            ).info("Updating event visibility after booking")
            await self.async_nylas_client.update_event(
                grant_id=not_none(
                    user_integration_dto.user_integration_connector
                ).external_id,
                calendar_id=calendar_id,
                event_id=nylas_booking.event_id,
                notify_all_participants=False,
                request=NylasUpdateEventRequest(
                    visibility=Visibility(event_schedule.visibility),
                    conferencing=nylas_conferencing_detail,
                ),
            )

        rendered_event_title = self.variable_service.render_event_title(
            event_schedule.event_title_template,
            meeting_duration=event_schedule.duration_minutes,
            scheduler_full_name=request.guest_name,
        )
        pre_meeting_buffer_event = None
        post_meeting_buffer_event = None
        if event_schedule.create_buffer_events and (
            event_schedule.buffer_before or event_schedule.buffer_after
        ):
            # Create buffer event before the meeting
            if event_schedule.buffer_before:
                buffer_before_start = request.starts_at - timedelta(
                    minutes=event_schedule.buffer_before
                )
                buffer_before_end = request.starts_at

                pre_meeting_buffer_event = await self.async_nylas_client.create_event(
                    grant_id=not_none(
                        user_integration_dto.user_integration_connector
                    ).external_id,
                    calendar_id=user_primary_calendar.external_id,
                    request=NylasCreateEventRequest(
                        title=f"[Pre-Meeting Buffer] {rendered_event_title or event_schedule.scheduler_title}",
                        description="Buffer time block before scheduled meeting",
                        when=NylasCreateTimespan(
                            start_time=int(buffer_before_start.timestamp()),
                            end_time=int(buffer_before_end.timestamp()),
                        ),
                        busy=True,
                        visibility=Visibility.private,  # TODO: change to request.visibility
                    ),
                    notify_all_participants=False,
                )

            # Create buffer event after the meeting
            if event_schedule.buffer_after:
                buffer_after_start = request.ends_at
                buffer_after_end = request.ends_at + timedelta(
                    minutes=event_schedule.buffer_after
                )

                post_meeting_buffer_event = await self.async_nylas_client.create_event(
                    grant_id=not_none(
                        user_integration_dto.user_integration_connector
                    ).external_id,
                    calendar_id=user_primary_calendar.external_id,
                    request=NylasCreateEventRequest(
                        title=f"[Post-Meeting Buffer] {rendered_event_title or event_schedule.scheduler_title}",
                        description="Buffer time block after scheduled meeting",
                        when=NylasCreateTimespan(
                            start_time=int(buffer_after_start.timestamp()),
                            end_time=int(buffer_after_end.timestamp()),
                        ),
                        busy=True,
                        visibility=Visibility.private,  # TODO: change to request.visibility
                    ),
                    notify_all_participants=False,
                )

        task_logger.info(
            "Booked scheduler event through Nylas.",
            user_id=user_id,
            organization_id=organization_id,
            nylas_booking=nylas_booking,
        )
        organizer = Organizer(
            name=nylas_booking.organizer.name,
            email=nylas_booking.organizer.email,
        )
        try:
            # 3. Get Nylas event
            nylas_event = await self.async_nylas_client.get_event(
                grant_id=not_none(
                    user_integration_dto.user_integration_connector
                ).external_id,
                calendar_id=calendar_id,
                event_id=nylas_booking.event_id,
            )
            if not nylas_event:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Nylas user calendar event is not successfully filtered",
                )

            # 4. Update(backfill) nylas calendar event with conferencing & additional guests
            if event_schedule.conferencing_provider == "Zoom Meeting":
                conferencing_provider: NylasConferencingProvider = "Zoom Meeting"
            elif event_schedule.conferencing_provider == "Google Meet":
                conferencing_provider = "Google Meet"
            else:
                conferencing_provider = "Microsoft Teams"

            if conferencing_provider == "Zoom Meeting":
                (
                    conferencing,
                    location,
                ) = await self.user_calendar_common_service.create_conferencing(
                    user=user,
                    organization_id=organization_id,
                    conference_provider=conferencing_provider,
                    title=rendered_event_title or event_schedule.scheduler_title,
                    description=event_schedule.event_description,
                    starts_at=request.starts_at,
                    ends_at=request.ends_at,
                )
                nylas_update_event = await self.async_nylas_client.update_event(
                    grant_id=not_none(
                        user_integration_dto.user_integration_connector
                    ).external_id,
                    calendar_id=calendar_id,
                    event_id=nylas_booking.event_id,
                    notify_all_participants=user_integration_dto.integration_provider
                    == IntegrationProvider.MICROSOFT,
                    request=NylasUpdateEventRequest(
                        location=location, conferencing=conferencing
                    ),
                )
                nylas_event = nylas_update_event if nylas_update_event else nylas_event

            # 5. Persist meeting and user calendar event
            cal_event_from_nylas = UserCalendarEvent.from_async_nylas(
                user_calendar=user_primary_calendar,
                organizer_user_id=user_id,
                user_id=user_id,
                organization_id=organization_id,
                evt=nylas_event,
            )
            temp_user_calendar_event = cal_event_from_nylas.copy(
                update={
                    "participants": await self.user_calendar_common_service.map_calendar_event_participants(
                        cal_event=cal_event_from_nylas,
                        organization_id=organization_id,
                        user_integration=user_integration_dto.user_integration,
                    ),
                }
            )

            organization = not_none(
                await self.event_schedule_repo.find_by_primary_key(
                    Organization, id=organization_id
                )
            )
            meeting_title = rendered_event_title or (
                f"{guest_company_name} <> {organization.display_name}"
                if guest_company_name
                else f"{internal_main_guest_name} <> {organization.display_name}"
            )
            meeting_id = None
            if settings.enable_async_scheduler_meeting_create:
                logger.bind(
                    user_calendar_event_id=temp_user_calendar_event.id,
                    event_schedule_id=event_schedule_id,
                ).info("Triggering workflow for meeting creation")
                temporal_client = await get_temporal_client()
                await temporal_client.start_workflow(
                    UserCalendarEventMeetingCreateWorkflow.run,
                    args=[
                        UserCalendarEventMeetingCreateWorkflowInput(
                            user_id=user_id,
                            organization_id=organization_id,
                            calendar_event_id=temp_user_calendar_event.id,
                            schedule_bot=bool(event_schedule.enable_recording),
                            event_schedule_id=event_schedule.id,
                            meeting_title=meeting_title,
                        ),
                    ],
                    id=f"meeting_create_{temp_user_calendar_event.id}",
                    task_queue=TemporalTaskQueue.CALENDAR_TASK_QUEUE,
                )
                dirty_event = temp_user_calendar_event.copy(
                    update={
                        "event_schedule_id": event_schedule.id,
                        "buffer_info": BufferInfo(
                            pre_buffer_external_id=pre_meeting_buffer_event.id
                            if pre_meeting_buffer_event
                            else None,
                            post_buffer_external_id=post_meeting_buffer_event.id
                            if post_meeting_buffer_event
                            else None,
                        )
                        if event_schedule.create_buffer_events
                        else None,
                    }
                )
            else:
                meeting_dto = (
                    await self.user_calendar_common_service.create_meeting_for_event(
                        user_id=user_id,
                        organization_id=organization_id,
                        new_calendar_event=temp_user_calendar_event,
                        consent_id=None,
                        participants=temp_user_calendar_event.participants,
                        schedule_bot=bool(event_schedule.enable_recording),
                        event_schedule_id=event_schedule.id,
                        meeting_title=meeting_title,
                    )
                )
                if not meeting_dto:
                    # Unexpected case where meeting is missing
                    # Log error but allow request to go through so user does not experience
                    # any issues and we can resolve after.
                    logger.bind(event_schedule_id=event_schedule_id).error(
                        "Meeting not defined for booking event"
                    )
                else:
                    meeting_id = meeting_dto.meeting_id

                dirty_event = temp_user_calendar_event.copy(
                    update={
                        "meeting_id": meeting_id,
                        "event_schedule_id": event_schedule.id,
                        "buffer_info": BufferInfo(
                            pre_buffer_external_id=pre_meeting_buffer_event.id
                            if pre_meeting_buffer_event
                            else None,
                            post_buffer_external_id=post_meeting_buffer_event.id
                            if post_meeting_buffer_event
                            else None,
                        )
                        if event_schedule.create_buffer_events
                        else None,
                    }
                )

            # Atomically insert or update calendar event
            cal_event = await self.user_cal_repo.upsert_unique_target_columns(
                dirty_event,
                on_conflict_target_columns=["user_calendar_id", "external_id"],
                exclude_columns_from_update=["id"],
            )
            # 6, Persist Booking data
            meeting_url = (
                cal_event.conferencing_details["url"]
                if cal_event.conferencing_details
                else None
            )
            hosts_info: list[EventScheduleBookingHost] = []
            if (
                event_schedule.event_schedule_type
                and event_schedule.event_schedule_type == EventScheduleType.MULTI_HOST
                and event_schedule.host_user_ids
            ):
                for host in event_schedule.host_user_ids:
                    event_schedule_host = await self.event_schedule_repo.find_event_schedule_host_by_user_id_and_event_schedule_id(
                        event_schedule_id=event_schedule_id,
                        user_id=host,
                        organization_id=organization_id,
                    )
                    if event_schedule_host:
                        host_user = await self.user_repo.find_user_by_id_or_error(
                            user_id=event_schedule_host.user_id
                        )
                        hosts_info.append(
                            EventScheduleBookingHost(
                                email=event_schedule_host.email,
                                name=host_user.display_name,
                            )
                        )
            else:
                hosts_info.append(
                    EventScheduleBookingHost(
                        email=organizer.email,
                        name=organizer.name,
                    )
                )

            utc_now = zoned_utc_now()
            booking = await self.event_schedule_repo.insert(
                instance=EventScheduleBooking(
                    id=uuid4(),
                    external_id=nylas_booking.booking_id,
                    group_key=build_group_key(
                        ical_uid=nylas_event.ical_uid, external_id=nylas_event.id
                    ),
                    user_calendar_event_id=cal_event.id,
                    event_schedule_id=event_schedule.id,
                    meeting_id=meeting_id,
                    guest_name=request.guest_name,
                    guest_email=request.guest_email,
                    guest_notes=request.guest_notes,
                    additional_guest_emails=request.additional_guest_emails,
                    hosts_info=hosts_info,
                    status=nylas_booking.status,
                    meeting_url=meeting_url,
                    pre_buffer_external_id=pre_meeting_buffer_event.id
                    if pre_meeting_buffer_event
                    else None,
                    post_buffer_external_id=post_meeting_buffer_event.id
                    if post_meeting_buffer_event
                    else None,
                    user_id=user_id,
                    organization_id=organization_id,
                    starts_at=cal_event.starts_at,
                    ends_at=cal_event.ends_at,
                    created_at=utc_now,
                    updated_at=utc_now,
                )
            )
            # 7. Async task: [Google]auto accept meeting invite, [Google]update guest notes and send emails
            temporal_client = await get_temporal_client()
            await temporal_client.start_workflow(
                EventScheduleWorkflow.run,
                args=[SchedulingType.BOOK.value, booking.id, organization_id],
                id=f"event_schedule_create_booking_{booking.id}",
                task_queue=TemporalTaskQueue.CALENDAR_TASK_QUEUE,
            )
        except Exception as e:
            # Delete the main booking
            await self.async_nylas_client.delete_booking(
                booking_id=nylas_booking.booking_id,
                configuration_id=event_schedule.external_id,
                request=NylasDeleteScheduleBookingRequest(
                    cancellation_reason="system error"
                ),
            )

            # Delete buffer events if they were created
            if pre_meeting_buffer_event:
                try:
                    await self.async_nylas_client.delete_event(
                        grant_id=not_none(
                            user_integration_dto.user_integration_connector
                        ).external_id,
                        calendar_id=user_primary_calendar.external_id,
                        event_id=pre_meeting_buffer_event.id,
                        notify_all_participants=False,
                    )
                except Exception as ex:
                    logger.info("Error deleting pre-meeting buffer event", e=ex)

            if post_meeting_buffer_event:
                try:
                    await self.async_nylas_client.delete_event(
                        grant_id=not_none(
                            user_integration_dto.user_integration_connector
                        ).external_id,
                        calendar_id=user_primary_calendar.external_id,
                        event_id=post_meeting_buffer_event.id,
                        notify_all_participants=False,
                    )
                except Exception as ex:
                    logger.info("Error deleting post-meeting buffer event", e=ex)

            logger.error(
                "Error after created booking through Nylas, deleting created booking and buffer events to avoid confusion.",
                external_booking_id=nylas_booking.booking_id,
            )
            raise e

        return (
            booking,
            event_schedule,
            organizer,
        )

    async def update_event_schedule_booking(
        self,
        event_schedule_booking_id: UUID,
        request: UpdateScheduleBooking,
    ) -> tuple[EventScheduleBooking, EventSchedule, Organizer]:
        logger.bind(
            event_schedule_booking_id=event_schedule_booking_id,
            request=request,
        ).info("Update event schedule booking request")
        # 1. Get Event Schedule and existing booking
        existing_booking = (
            await self.event_schedule_repo.find_event_schedule_booking_or_error(
                event_schedule_booking_id=event_schedule_booking_id
            )
        )
        event_schedule_id = existing_booking.event_schedule_id
        user_id = existing_booking.user_id
        organization_id = existing_booking.organization_id

        event_schedule = (
            await self.event_schedule_repo.find_event_schedule_by_id_or_error(
                event_schedule_id=event_schedule_id
            )
        )

        organizer = await self.user_repo.find_user_by_id_or_error(user_id=user_id)
        user_integration_dto = await self.user_integration_repo.find_user_integration_by_calendar_account_or_error(
            calendar_account_id=not_none(event_schedule.calendar_account_id),
            organization_id=organization_id,
        )

        existing_cal_event = await self.user_cal_repo.find_by_tenanted_primary_key(
            UserCalendarEvent,
            id=existing_booking.user_calendar_event_id,
            organization_id=organization_id,
        )
        if not existing_cal_event:
            raise ResourceNotFoundError(
                "the calendar event is neither not exist or already deleted.",
            )
        # 2. reschedule from nylas side with new timeslot
        await self.async_nylas_client.reschedule_booking(
            booking_id=existing_booking.external_id,
            configuration_id=event_schedule.external_id,
            request=NylasUpdateScheduleBookingRequest(
                start_time=int(request.starts_at.timestamp()),
                end_time=int(request.ends_at.timestamp()),
            ),
        )
        # 3. update zoom with new time
        if (
            event_schedule.conferencing_provider == "Zoom Meeting"
            and existing_cal_event.conferencing_details
        ):
            zoom_meeting_id = existing_cal_event.conferencing_details["meeting_code"]
            await self.conference_service.update_zoom_meeting(
                user_id=user_id,
                zoom_meeting_id=zoom_meeting_id,
                organization_id=organization_id,
                request=UpdateMeetingRequest(
                    start_time=request.starts_at,
                ),
            )

        # 4, persist to db
        cal_event = not_none(
            await self.user_cal_repo.update_instance(
                existing_cal_event.copy(
                    update={
                        "updated_at": zoned_utc_now(),
                        "starts_at": request.starts_at,
                        "ends_at": request.ends_at,
                    }
                )
            )
        )
        # Update buffer events after updated user calendar event
        if event_schedule.buffer_before or event_schedule.buffer_after:
            # Update buffer events for the organizer
            user_primary_calendar = await self.user_cal_repo.find_user_primary_calendar_by_calendar_account_or_error(
                calendar_account_id=not_none(event_schedule.calendar_account_id),
                organization_id=organization_id,
            )

            # Update buffer event before/after the meeting
            if cal_event.buffer_info:
                await self.user_calendar_common_service.update_buffer_event(
                    user_calendar_event=cal_event,
                    calendar_external_id=user_primary_calendar.external_id,
                    grant_id=not_none(
                        user_integration_dto.user_integration_connector
                    ).external_id,
                    async_nylas_client=self.async_nylas_client,
                )

        updated_booking = not_none(
            await self.event_schedule_repo.update_instance(
                existing_booking.copy(
                    update={
                        "starts_at": cal_event.starts_at,
                        "ends_at": cal_event.ends_at,
                    }
                )
            )
        )

        temporal_client = await get_temporal_client()
        await temporal_client.start_workflow(
            EventScheduleWorkflow.run,
            args=[
                SchedulingType.RESCHEDULE.value,
                updated_booking.id,
                organization_id,
            ],
            id=f"event_schedule_update_booking_{updated_booking.id}",
            task_queue=TemporalTaskQueue.CALENDAR_TASK_QUEUE,
        )

        return (
            updated_booking,
            event_schedule,
            Organizer(
                name=organizer.display_name,
                email=user_integration_dto.user_integration.connected_email
                if user_integration_dto.user_integration.connected_email
                else organizer.email,
            ),
        )

    async def delete_event_schedule_booking(
        self,
        event_schedule_booking_id: UUID,
        request: DeleteScheduleBooking,
    ) -> tuple[EventScheduleBooking, EventSchedule, Organizer]:
        logger.bind(
            event_schedule_booking_id=event_schedule_booking_id,
            request=request,
        ).info("Delete event schedule booking request")
        # 1. Get Event Schedule and existing booking
        existing_booking = (
            await self.event_schedule_repo.find_event_schedule_booking_or_error(
                event_schedule_booking_id=event_schedule_booking_id
            )
        )
        event_schedule_id = existing_booking.event_schedule_id
        user_id = existing_booking.user_id
        organization_id = existing_booking.organization_id
        event_schedule = (
            await self.event_schedule_repo.find_event_schedule_by_id_or_error(
                event_schedule_id=event_schedule_id
            )
        )

        organizer = await self.user_repo.find_user_by_id_or_error(user_id=user_id)
        user_integration_dto = await self.user_integration_repo.find_user_integration_by_calendar_account_or_error(
            calendar_account_id=not_none(event_schedule.calendar_account_id),
            organization_id=organization_id,
        )
        primary_cal = (
            await self.user_cal_repo.find_user_primary_calendar_by_calendar_account(
                calendar_account_id=not_none(event_schedule.calendar_account_id),
                organization_id=organization_id,
            )
        )
        if primary_cal is None:
            raise HTTPException(
                HTTPStatus.BAD_REQUEST,
                detail="user has no calendar connected",
            )
        existing_cal_event = await self.user_cal_repo.find_by_tenanted_primary_key(
            UserCalendarEvent,
            id=existing_booking.user_calendar_event_id,
            organization_id=organization_id,
        )
        if not existing_cal_event:
            raise ResourceNotFoundError(
                "the calendar event is neither not exist or already deleted.",
            )
        # 2. Delete from Nylas
        await self.async_nylas_client.delete_booking(
            booking_id=existing_booking.external_id,
            configuration_id=event_schedule.external_id,
            request=NylasDeleteScheduleBookingRequest(
                cancellation_reason=request.cancellation_reason
            ),
        )
        # 3. Delete from Zoom if any
        if (
            event_schedule.conferencing_provider == "Zoom Meeting"
            and existing_cal_event.conferencing_details
        ):
            zoom_meeting_id = existing_cal_event.conferencing_details["meeting_code"]
            await self.conference_service.delete_zoom_meeting(
                user_id=user_id,
                zoom_meeting_id=zoom_meeting_id,
                organization_id=organization_id,
            )

        # Delete buffer events if they exist
        if (
            existing_booking.pre_buffer_external_id
            or existing_booking.post_buffer_external_id
        ):
            user_primary_calendar = await self.user_cal_repo.find_user_primary_calendar_by_calendar_account_or_error(
                calendar_account_id=not_none(event_schedule.calendar_account_id),
                organization_id=organization_id,
            )

            if existing_booking.pre_buffer_external_id:
                await self.async_nylas_client.delete_event(
                    grant_id=not_none(
                        user_integration_dto.user_integration_connector
                    ).external_id,
                    calendar_id=user_primary_calendar.external_id,
                    event_id=existing_booking.pre_buffer_external_id,
                    notify_all_participants=False,
                )

            if existing_booking.post_buffer_external_id:
                await self.async_nylas_client.delete_event(
                    grant_id=not_none(
                        user_integration_dto.user_integration_connector
                    ).external_id,
                    calendar_id=user_primary_calendar.external_id,
                    event_id=existing_booking.post_buffer_external_id,
                    notify_all_participants=False,
                )

        # 4. persist db
        utc_now = zoned_utc_now()
        updated_event = await self.user_cal_repo.update_instance(
            existing_cal_event.model_copy(
                update={"updated_at": utc_now, "deleted_at": utc_now},
            ),
        )
        if not updated_event:
            # In case it was deleted already via webhook - don't block whole thing if this happens
            logger.bind(
                event_id=existing_cal_event.id, booking_id=existing_booking.id
            ).error(
                "Did not update event for canceled booking, event already deleted.",
            )

        deleted_booking = not_none(
            await self.event_schedule_repo.update_instance(
                existing_booking.copy(
                    update={
                        "updated_at": utc_now,
                        "deleted_at": utc_now,
                        "status": BookingStatus.CANCELED,
                        "cancellation_reason": request.cancellation_reason,
                    }
                )
            )
        )

        temporal_client = await get_temporal_client()
        await temporal_client.start_workflow(
            EventScheduleWorkflow.run,
            args=[
                SchedulingType.CANCELLATION.value,
                deleted_booking.id,
                organization_id,
            ],
            id=f"event_schedule_cancel_booking_{deleted_booking.id}",
            task_queue=TemporalTaskQueue.CALENDAR_TASK_QUEUE,
        )

        return (
            deleted_booking,
            event_schedule,
            Organizer(
                name=organizer.display_name,
                email=user_integration_dto.user_integration.connected_email
                if user_integration_dto.user_integration.connected_email
                else organizer.email,
            ),
        )

    async def get_event_schedule_booking_by_id(
        self,
        event_schedule_booking_id: UUID,
    ) -> tuple[EventScheduleBooking, EventSchedule, Organizer]:
        logger.bind(
            event_schedule_booking_id=event_schedule_booking_id,
        ).info("Get event schedule booking by id request")
        # 1. Get Event Schedule and existing booking
        existing_booking = (
            await self.event_schedule_repo.find_event_schedule_booking_or_error(
                event_schedule_booking_id=event_schedule_booking_id
            )
        )
        event_schedule_id = existing_booking.event_schedule_id
        user_id = existing_booking.user_id
        organization_id = existing_booking.organization_id
        event_schedule = (
            await self.event_schedule_repo.find_event_schedule_by_id_or_error(
                event_schedule_id=event_schedule_id
            )
        )
        organizer = await self.user_repo.find_user_by_id_or_error(user_id=user_id)
        user_integration_dto = await self.user_integration_repo.find_user_integration_by_calendar_account_id(
            calendar_account_id=not_none(event_schedule.calendar_account_id),
            organization_id=organization_id,
        )
        if (
            not user_integration_dto
            or user_integration_dto.integration_status != IntegrationStatus.CONNECTED
        ):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User Calendar Integration is invalid.",
            )

        return (
            existing_booking,
            event_schedule,
            Organizer(
                name=organizer.display_name,
                email=user_integration_dto.user_integration.connected_email
                if user_integration_dto.user_integration.connected_email
                else organizer.email,
            ),
        )

    async def _import_guests_as_contacts(  # noqa: C901
        self,
        guest_email_name_map: dict[EmailStr, str],
        user_id: UUID,
        organization_id: UUID,
        main_guest_email: EmailStr,
        main_guest_name: str,
    ) -> tuple[list[UUID], str | None, str]:
        # todo: https://linear.app/reevo/issue/REEVO-1858/wire-up-pipeline-id-from-account-object-for-meeting-creation
        """
        Import guests as contacts in the system, handling three scenarios:
        1. For non-existing contacts: Create/get account and create contact with that account, return deal and lead if it's the main guest.
        2. For existing contacts without associated account: Create account, associate it with the contact,
           and return deal and lead if it's the main guest.
        3. For existing contacts with associated account: return deal and lead if it's the main guest.

        Args:
            guest_email_name_map: A dictionary mapping guest emails to their names.
            user_id: The ID of the user performing the import.
            organization_id: The ID of the organization.
            main_guest_email: The email of the main guest.

        Returns:
            A tuple containing:
            - A list of UUIDs for newly created contacts.
            - The UUID of the lead associated with the main guest's account (if any).
            - The UUID of the deal associated with the main guest's account (if any).
        """
        created_contact_ids: list[UUID] = []
        guest_company_name = None
        internal_main_guest_name = main_guest_name

        async def process_guest(guest_email: EmailStr, is_main_guest: bool) -> None:
            nonlocal guest_company_name, internal_main_guest_name
            existing_contact_list = await self.contact_service.find_by_primary_emails(
                organization_id=organization_id,
                primary_emails=[guest_email],
            )
            existing_contact = (
                existing_contact_list[0] if existing_contact_list else None
            )
            display_name = (
                (
                    guest_email_name_map.get(guest_email)
                    or format_email_username_as_display_name(guest_email.split("@")[0])
                )
                if not is_main_guest
                else main_guest_name
            )
            domain_name = guest_email.split("@")[1]
            is_public_domain = domain_name in PUBLIC_EMAIL_DOMAIN
            company_name = (
                guest_email.split("@")[0] if is_public_domain else domain_name
            )
            account_id: UUID | None = None
            if existing_contact and (
                account_id
                := await self.contact_resolve_service.resolve_relevant_account_by_contact_and_email(
                    organization_id=organization_id,
                    contact_id=existing_contact.id,
                    email=guest_email,
                )
            ):
                account = await self.account_service.get_account_v2(
                    account_id=account_id,
                    organization_id=organization_id,
                )
            else:
                account_import_csv_fields = AccountImportCsvFields(
                    company_display_name=company_name,
                    company_official_website=company_name.replace(".", "-")
                    if is_public_domain
                    else domain_name,
                    company_domain_name=None,
                    company_description=None,
                    company_industry=None,
                    company_technologies=None,
                    company_estimated_annual_revenue=None,
                    company_estimated_employee_count=None,
                    company_linkedin_url=None,
                    company_facebook_url=None,
                    company_x_url=None,
                    company_owner_email=None,
                    company_created_at=None,
                    company_status=None,
                )
                logger.bind(
                    account_import_csv_fields=account_import_csv_fields,
                    organization_id=organization_id,
                    user_id=user_id,
                ).info("Find or create account for guest contact")
                (
                    account,
                    _is_newly_created,
                ) = await self.crm_sync_service.find_or_create_account(
                    account_import_csv_fields=account_import_csv_fields,
                    user_id=user_id,
                    organization_id=organization_id,
                    local_timezone=None,
                    owner_user_id=None,
                )

            if not existing_contact:
                first_name, last_name = generate_first_and_last_name(display_name)
                try:
                    default_stage_id = (
                        await self.contact_service.get_default_stage_id_for_contact(
                            organization_id
                        )
                    )
                    contact = await self.contact_service.create_contact_with_contact_channels(
                        organization_id=organization_id,
                        user_id=user_id,
                        create_contact_with_contact_channel_request=CreateContactRequest(
                            contact=CreateDbContactRequest(
                                display_name=display_name,
                                first_name=first_name,
                                last_name=last_name,
                                created_by_user_id=user_id,
                                owner_user_id=user_id,
                                stage_id=default_stage_id,
                            ),
                            contact_emails=[
                                CreateDbContactEmailRequest(
                                    email=guest_email,
                                    is_contact_primary=True,
                                )
                            ],
                            contact_account_roles=[
                                CreateContactAccountRoleRequest(
                                    account_id=account.id, is_primary_account=True
                                )
                            ],
                        ),
                    )
                    created_contact_ids.append(contact.id)
                    if is_main_guest:
                        internal_main_guest_name = contact.display_name
                except ConflictResourceError:
                    logger.info(
                        f"Contact with email {guest_email} already exists in organization {organization_id}"
                    )
            elif not account_id:
                upsert_asso_result = (
                    await self.contact_service.upsert_contact_account_association(
                        organization_id=organization_id,
                        user_id=user_id,
                        contact_id=existing_contact.id,
                        req=UpsertContactAccountRoleRequest(
                            account_id=account.id, is_primary=True
                        ),
                    )
                )
                contact_v2 = (
                    upsert_asso_result.updated_contact
                    if upsert_asso_result.updated_contact
                    else await self.contact_service.get_contact_v2(
                        organization_id=organization_id,
                        contact_id=existing_contact.id,
                    )
                )
                if is_main_guest:
                    internal_main_guest_name = contact_v2.display_name
            elif is_main_guest:
                internal_main_guest_name = existing_contact.display_name

            if is_main_guest:
                # todo: https://linear.app/reevo/issue/REEVO-1858/wire-up-pipeline-id-from-account-object-for-meeting-creation
                guest_company_name = (
                    account.display_name
                    if account.display_name != guest_email.split("@")[0]
                    else None
                )

        for guest_email in guest_email_name_map:
            await process_guest(guest_email, guest_email == main_guest_email)

        return (
            created_contact_ids,
            guest_company_name,
            internal_main_guest_name,
        )

    @staticmethod
    def _get_nylas_event_for_booking(
        expected_participants: list[str], nylas_event_list: list[NylasEvent]
    ) -> NylasEvent | None:
        for e in nylas_event_list:
            attendee_list_in_event = []
            for p in e.participants:
                attendee_list_in_event.append(p.email)
            if set(attendee_list_in_event) == set(expected_participants):
                logger.info("Found nylas event for booking", nylas_event=e)
                return e
        if nylas_event_list:
            return nylas_event_list[0]
        return None

    @staticmethod
    def _get_booking_description(
        event_schedule: EventSchedule, guest_notes: str | None
    ) -> str | None:
        return (
            f"""{event_schedule.event_description}<br><br>"""
            f"""<strong>Guest Notes:</strong> {guest_notes}"""
            if guest_notes
            else event_schedule.event_description
        )

    async def _process_hosts(
        self,
        hosts: list[CreateEventScheduleHost],
        organization_id: UUID,
        default_open_hours: list[DefaultOpenHour],
    ) -> tuple[list[ConfigurationParticipant], list[UUID], dict[str, HostInfo]]:
        participants = []
        host_user_ids = []
        host_email_info_map = {}
        for host in hosts:
            host_user = await self.user_repo.find_user_by_id_and_organization_id(
                user_id=host.user_id, organization_id=organization_id
            )
            if host_user is None:
                logger.warning(
                    f"User not found. user_id: {host.user_id}, organization_id: {organization_id}"
                )
                continue
            host_calendar_account = (
                await self.calendar_account_repo.find_default_calendar_account_on_error(
                    user_id=host_user.id,
                    organization_id=organization_id,
                )
            )
            host_integration_dto = await self.user_integration_repo.find_user_integration_by_calendar_account_or_error(
                calendar_account_id=host_calendar_account.id,
                organization_id=organization_id,
            )
            if host_integration_dto.integration_status != IntegrationStatus.CONNECTED:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"User Calendar Integration is invalid. user_id: {host_user.id}",
                )
            host_primary_cal = await self.user_cal_repo.find_user_primary_calendar_by_calendar_account_or_error(
                calendar_account_id=host_calendar_account.id,
                organization_id=host_integration_dto.user_organization_id,
            )
            open_hours = default_open_hours
            if (
                host.availability
                and host.availability.availability_type == HostAvailabilityType.CUSTOM
                and host.availability.open_hours is not None
            ):
                open_hours = [
                    DefaultOpenHour(
                        days=open_hour.days,
                        start=open_hour.start,
                        end=open_hour.end,
                        exdates=open_hour.exdates,
                    )
                    for open_hour in host.availability.open_hours
                ]

            participants.append(
                ConfigurationParticipant(
                    name=host_user.display_name,
                    email=host_calendar_account.email,
                    is_organizer=host.is_organizer,
                    availability=ConfigurationParticipantAvailability(
                        calendar_ids=[host_primary_cal.external_id],
                        open_hours=open_hours,
                    ),
                    booking=ConfigurationParticipantBooking(
                        calendar_id=host_primary_cal.external_id
                    ),
                )
            )
            host_user_ids.append(host_user.id)
            host_email_info_map[host_calendar_account.email] = HostInfo(
                user_id=host_user.id,
                calendar_account_id=host_calendar_account.id,
                name=host_user.display_name,
                availability_type=HostAvailabilityType.CUSTOM
                if host.availability
                and host.availability.availability_type == HostAvailabilityType.CUSTOM
                else HostAvailabilityType.DEFAULT,
            )

        return participants, host_user_ids, host_email_info_map


class SingletonEventScheduleService(EventScheduleService, Singleton):
    pass


def get_event_schedule_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> EventScheduleService:
    event_schedule_repo = EventScheduleRepository(engine=db_engine)
    user_integration_repo = UserIntegrationRepository(engine=db_engine)
    user_cal_repo = UserCalendarRepository(engine=db_engine)
    user_repo = UserRepository(engine=db_engine)
    meeting_service = meeting_service_factory_general(db_engine=db_engine)
    user_calendar_common_service = get_user_calendar_common_service_by_db_engine(
        db_engine
    )
    email_event_service = EmailEventService(db_engine=db_engine)
    calendar_account_repo = CalendarAccountRepository(engine=db_engine)
    crm_sync_service = get_crm_sync_service_from_db_engine(engine=db_engine)
    conference_service = init_conference_service_by_db_engine(db_engine=db_engine)
    account_service = get_account_service(db_engine=db_engine)
    contact_service = get_contact_service(db_engine=db_engine)
    contact_resolve_service = get_contact_resolve_service(db_engine=db_engine)
    return SingletonEventScheduleService(
        async_nylas_client=AsyncNylasClient(),
        user_integration_repo=user_integration_repo,
        user_cal_repo=user_cal_repo,
        user_repo=user_repo,
        event_schedule_repo=event_schedule_repo,
        user_calendar_common_service=user_calendar_common_service,
        meeting_service=meeting_service,
        conference_service=conference_service,
        email_event_service=email_event_service,
        calendar_account_repo=calendar_account_repo,
        crm_sync_service=crm_sync_service,
        account_service=account_service,
        contact_service=contact_service,
        variable_service=get_variable_service(db_engine=db_engine),
        contact_resolve_service=contact_resolve_service,
    )
