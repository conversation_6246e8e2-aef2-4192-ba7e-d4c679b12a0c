from __future__ import annotations

from collections.abc import Mapping
from typing import Literal
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.type.metadata.field.field_value import FieldValueOrAny
from salestech_be.common.type.patch_request import UNSET, BasePatchRequest, UnsetAware
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.db.models.task import (
    TaskPriority,
    TaskReferenceRelationshipType,
    TaskSourceType,
    TaskStatus,
    TaskTemplate,
    TaskType,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.web.api.common.container import (
    ListCustomizableEntityRequest,
    ListEntityRequest,
)


class TaskTemplateResponse(TaskTemplate):
    def __init__(self, task_template: TaskTemplate):
        super().__init__(**task_template.dict())


class CreateTaskRequest(BaseModel):
    title: str
    status: TaskStatus
    priority: TaskPriority
    type: TaskType
    pipeline_id: UUID | None = None
    contact_ids: list[UUID] | None = None
    account_id: UUID | None = None
    email_thread_ids: list[UUID] | None = None
    email_ids: list[UUID] | None = None
    meeting_id: UUID | None = None
    voice_call_id: UUID | None = None
    insight_id: UUID | None = None
    owner_user_id: UUID
    due_at: ZoneRequiredDateTime | None = None
    note: str | None = None
    is_time_specified: bool | None = None
    custom_field_data: Mapping[UUID, FieldValueOrAny] | None = None
    source_type: TaskSourceType = TaskSourceType.USER
    participant_user_id_list: list[UUID] | None = None
    created_source: CreatedSource | None = None
    sequence_id: UUID | None = None
    sequence_enrollment_id: UUID | None = None
    sequence_step_id: UUID | None = None
    sequence_step_execution_id: UUID | None = None
    sequence_step_variant_id: UUID | None = None
    disposition: str | None = None


class PatchTaskRequest(BasePatchRequest):
    require_at_least_one_specified_field = True

    title: UnsetAware[str] = UNSET
    status: UnsetAware[TaskStatus] = UNSET
    priority: UnsetAware[TaskPriority] = UNSET
    contact_ids: UnsetAware[list[UUID] | None] = UNSET
    pipeline_id: UnsetAware[UUID | None] = UNSET
    account_id: UnsetAware[UUID | None] = UNSET
    owner_user_id: UnsetAware[UUID] = UNSET
    due_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    note: UnsetAware[str | None] = UNSET
    custom_field_data: UnsetAware[dict[UUID, FieldValueOrAny] | None] = UNSET
    participant_user_id_list: UnsetAware[list[UUID] | None] = UNSET
    created_source: CreatedSource | None = None
    meeting_id: UnsetAware[UUID | None] = UNSET
    relationship_type: UnsetAware[TaskReferenceRelationshipType | None] = UNSET
    disposition: UnsetAware[str | None] = UNSET


class CreateTaskTemplateRequest(BaseModel):
    title: str
    priority: TaskPriority
    type: TaskType
    note: str | None = None


class PatchTaskTemplateRequest(BasePatchRequest):
    title: UnsetAware[str]
    priority: UnsetAware[TaskPriority]
    type: UnsetAware[TaskType]
    note: UnsetAware[str | None]


class ListTaskRequest(ListCustomizableEntityRequest):
    record_id: UUID | None = None
    owner_user_id: UUID | None = Field(
        None, description="Filter tasks by owner user id"
    )
    owner_user_ids_in: list[UUID] | None = Field(  # this is only used by giant_tasks.
        None, description="Filter tasks by owner user ids (IN operation)"
    )
    status_ne: str | None = Field(
        None, description="Filter tasks whose status is not equal to this value"
    )
    due_at_gt: ZoneRequiredDateTime | None = Field(
        None, description="Filter tasks with due date greater than this"
    )
    due_at_lt: ZoneRequiredDateTime | None = Field(
        None, description="Filter tasks with due date less than this"
    )
    due_at_blank: bool | None = Field(
        None, description="Filter tasks with no due date when set to true"
    )
    sorting_field: Literal["due_at", "created_at", "updated_at"] | None = Field(
        None, description="Field to sort tasks by (due_at, created_at, updated_at)"
    )
    sorting_direction: Literal["asc", "desc"] | None = Field(
        None, description="Sort direction, asc or desc"
    )
    null_first: bool = Field(
        False, description="Whether to show null values first in sorting"
    )
    account_id: UUID | None = Field(None, description="Filter tasks by account id")
    account_ids_in: list[UUID] | None = Field(  # this is only used by giant_tasks.
        None, description="Filter tasks by account ids (IN operation)"
    )
    contact_ids: list[UUID] | None = Field(
        None, description="Filter tasks by contact ids"
    )
    pipeline_id: UUID | None = Field(None, description="Filter tasks by pipeline id")
    pipeline_ids_in: list[UUID] | None = Field(  # this is only used by giant_tasks.
        None, description="Filter tasks by pipeline ids (IN operation)"
    )
    require_pipeline_id: bool = Field(
        False, description="Require tasks to have a pipeline reference"
    )
    pipeline_stage_select_list_value_ids_in: list[UUID] | None = (
        Field(  # this is only used by giant_tasks.
            None,
            description="Filter tasks by pipeline stage select list value ids (IN operation)",
        )
    )
    meeting_id: UUID | None = Field(None, description="Filter tasks by meeting id")
    email_thread_ids: list[UUID] | None = Field(
        None, description="Filter tasks by email thread ids"
    )
    sequence_id: UUID | None = Field(None, description="Filter tasks by sequence id")
    sequence_step_id_in: list[UUID] | None = Field(  # this is only used by giant_tasks.
        None, description="Filter tasks by sequence step ids (IN operation)"
    )
    task_ids: list[UUID] | None = Field(
        None, description="Filter tasks by specific task IDs (IN operation)"
    )
    priority_in: list[TaskPriority] | None = Field(
        None, description="Filter tasks by priority (IN operation)"
    )
    type_in: list[TaskType] | None = Field(
        None, description="Filter tasks by type (IN operation)"
    )
    status_in: list[TaskStatus] | None = Field(
        None, description="Filter tasks by status (IN operation)"
    )


class GetTaskSummaryRequest(ListEntityRequest): ...


class GetTaskSummaryRequestV2(ListCustomizableEntityRequest): ...


class GetTaskSummaryResponse(BaseModel):
    total_count: int
    type_counts: dict[TaskType, int]
    status_counts: dict[TaskStatus, int]


class GetTaskSummaryResponseV2(BaseModel):
    total_count: int
    type_counts: dict[TaskType, int]
    status_counts: dict[TaskStatus, int]
    priority_counts: dict[TaskPriority, int]
    owner_counts: dict[UUID, int]
    created_count: int
    completed_on_time_count: int
    delayed_count: int
    open_count: int


class CreateTaskCommentRequest(BaseModel):
    comment_html: str
    attachment_ids: list[UUID] | None = None


class PatchTaskCommentRequest(BasePatchRequest):
    require_at_least_one_specified_field = True

    comment_html: UnsetAware[str]
    attachment_ids: UnsetAware[list[UUID] | None]
