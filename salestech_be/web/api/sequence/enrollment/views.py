import uuid
from typing import Annotated

from fastapi import Depends

from salestech_be.common.exception.exception import ForbiddenError
from salestech_be.core.data.service.query_service import DomainObjectQueryService
from salestech_be.core.data.types import StandardRecord
from salestech_be.core.data.util import ObjectRecordFetchConditions
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    SequenceEnrollmentService,
)
from salestech_be.core.sequence.type.sequence_enrollment_contact_type import (
    SequenceEnrollmentContact,
)
from salestech_be.core.sequence.type.sequence_enrollment_run_type import (
    SequenceEnrollmentRun,
)
from salestech_be.core.sequence.type.sequence_enrollment_types import (
    SequenceEnrollment,
)
from salestech_be.services.auth.perm_predicates import (
    require_read_sequence_access,
    require_update_sequence_access,
)
from salestech_be.web.api.common.container import (
    ListEntityRequestV2,
    PaginatedListResponse,
)
from salestech_be.web.api.filter.logical_filter import (
    paginate_object_records,
    single_paginate_object_record,
)
from salestech_be.web.api.sequence.enrollment.schema import (
    BulkChangeSequenceEnrollmentRequest,
    BulkChangeSequenceEnrollmentResponse,
    BulkReenrollContactsRequest,
    CreateSequenceEnrollmentRequest,
    CreateSequenceEnrollmentResponse,
    CreateSequenceEnrollmentResponseV2,
    DeleteSequenceEnrollmentResponse,
    PatchEnrollmentEmailRequest,
    PatchEnrollmentEmailResponse,
    PatchSequenceEnrollmentRequest,
    PatchSequenceEnrollmentResponse,
    PreviewSequenceEnrollmentRequest,
    PreviewSequenceEnrollmentResponse,
    ProceedEnrollment,
    ReenrollContactsRequest,
)
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.lifespan_service import (
    domain_object_query_service_from_lifespan,
    sequence_enrollment_service_from_lifespan,
)
from salestech_be.web.middleware.types import (
    AnnotatedReevoUserAuthContext,
)

router = ReeAPIRouter()


@router.post(
    "/_list",
    response_model=PaginatedListResponse[StandardRecord[SequenceEnrollment]],
    dependencies=[Depends(require_read_sequence_access)],
)
async def list_sequence_enrollments(
    request: ListEntityRequestV2,
    user_auth_context: AnnotatedReevoUserAuthContext,
    domain_object_query_service: Annotated[
        DomainObjectQueryService, Depends(domain_object_query_service_from_lifespan)
    ],
) -> PaginatedListResponse[StandardRecord[SequenceEnrollment]]:
    if request.record_id:
        sequence_enrollment = (
            await domain_object_query_service.list_sequence_enrollment_records(
                user_auth_context=user_auth_context,
                only_include_sequence_enrollment_ids={request.record_id},
            )
        )
        return single_paginate_object_record(
            object_records=sequence_enrollment,
        )

    sequence_enrollments = (
        await domain_object_query_service.list_sequence_enrollment_records(
            user_auth_context=user_auth_context,
            fetch_conditions=ObjectRecordFetchConditions(
                filter_spec=request.filter_spec,
                sorting_spec=request.sorting_spec,
                fields=request.ordered_object_fields,
            ),
        )
    )

    return paginate_object_records(
        object_records=sequence_enrollments,
        cursor=request.cursor,
    )


@router.post(
    "/_preview",
    response_model=PreviewSequenceEnrollmentResponse,
    dependencies=[Depends(require_read_sequence_access)],
)
async def preview_sequence_enrollments(
    request: PreviewSequenceEnrollmentRequest,
    user_auth_context: AnnotatedReevoUserAuthContext,
    sequence_enrollment_service: Annotated[
        SequenceEnrollmentService, Depends(sequence_enrollment_service_from_lifespan)
    ],
) -> PreviewSequenceEnrollmentResponse:
    return await sequence_enrollment_service.preview_sequence_enrollments(
        request=request,
        user_auth_context=user_auth_context,
    )


@router.post(
    "",
    response_model=CreateSequenceEnrollmentResponse,
    dependencies=[Depends(require_update_sequence_access)],
)
async def create_sequence_enrollments(
    request: CreateSequenceEnrollmentRequest,
    user_auth_context: AnnotatedReevoUserAuthContext,
    sequence_enrollment_service: Annotated[
        SequenceEnrollmentService, Depends(sequence_enrollment_service_from_lifespan)
    ],
) -> CreateSequenceEnrollmentResponse:
    return await sequence_enrollment_service.create_sequence_enrollment(
        request=request,
        user_auth_context=user_auth_context,
    )


@router.delete(
    "/{enrollment_id}",
    response_model=DeleteSequenceEnrollmentResponse,
    dependencies=[Depends(require_update_sequence_access)],
)
async def delete_sequence_enrollment(
    enrollment_id: uuid.UUID,
    user_auth_context: AnnotatedReevoUserAuthContext,
    sequence_enrollment_service: Annotated[
        SequenceEnrollmentService, Depends(sequence_enrollment_service_from_lifespan)
    ],
) -> DeleteSequenceEnrollmentResponse:
    return await sequence_enrollment_service.delete_sequence_enrollment_by_id(
        sequence_enrollment_id=enrollment_id,
        user_auth_context=user_auth_context,
    )


@router.patch(
    "/{enrollment_id}",
    response_model=PatchSequenceEnrollmentResponse,
    dependencies=[Depends(require_update_sequence_access)],
)
async def patch_sequence_enrollment(
    enrollment_id: uuid.UUID,
    request: PatchSequenceEnrollmentRequest,
    user_auth_context: AnnotatedReevoUserAuthContext,
    sequence_enrollment_service: Annotated[
        SequenceEnrollmentService, Depends(sequence_enrollment_service_from_lifespan)
    ],
) -> PatchSequenceEnrollmentResponse:
    return await sequence_enrollment_service.patch_sequence_enrollment_by_id(
        user_auth_context=user_auth_context,
        request=request,
        sequence_enrollment_id=enrollment_id,
    )


@router.post(
    "/_bulk_change",
    response_model=BulkChangeSequenceEnrollmentResponse,
    dependencies=[Depends(require_update_sequence_access)],
)
async def bulk_change_sequence_enrollments(
    request: BulkChangeSequenceEnrollmentRequest,
    user_auth_context: AnnotatedReevoUserAuthContext,
    sequence_enrollment_service: Annotated[
        SequenceEnrollmentService, Depends(sequence_enrollment_service_from_lifespan)
    ],
) -> BulkChangeSequenceEnrollmentResponse:
    return await sequence_enrollment_service.bulk_change_sequence_enrollments(
        request=request,
        user_auth_context=user_auth_context,
    )


@router.post(
    "/{enrollment_id}/_proceed",
    response_model=ProceedEnrollment,
    dependencies=[Depends(require_update_sequence_access)],
)
async def proceed_sequence_enrollment(
    enrollment_id: uuid.UUID,
    user_auth_context: AnnotatedReevoUserAuthContext,
    sequence_enrollment_service: Annotated[
        SequenceEnrollmentService, Depends(sequence_enrollment_service_from_lifespan)
    ],
) -> ProceedEnrollment:
    return await sequence_enrollment_service.proceed_sequence_enrollment(
        enrollment_id=enrollment_id,
        user_auth_context=user_auth_context,
    )


@router.post(
    "/sequence_enrollment_runs/_list",
    dependencies=[Depends(require_read_sequence_access)],
    response_model=PaginatedListResponse[StandardRecord[SequenceEnrollmentRun]],
)
async def list_sequence_enrollment_runs(
    request: ListEntityRequestV2,
    user_auth_context: AnnotatedReevoUserAuthContext,
    domain_object_query_service: Annotated[
        DomainObjectQueryService, Depends(domain_object_query_service_from_lifespan)
    ],
) -> PaginatedListResponse[StandardRecord[SequenceEnrollmentRun]]:
    enrollment_runs = (
        await domain_object_query_service.list_sequence_enrollment_run_records(
            user_auth_context=user_auth_context,
            organization_id=user_auth_context.organization_id,
            fetch_conditions=ObjectRecordFetchConditions(
                filter_spec=request.filter_spec,
                sorting_spec=request.sorting_spec,
                fields=request.ordered_object_fields,
            ),
        )
    )

    return paginate_object_records(
        object_records=enrollment_runs,
        cursor=request.cursor,
    )


@router.post(
    "/sequence_enrollment_runs/{run_id}/contacts/_list",
    response_model=PaginatedListResponse[StandardRecord[SequenceEnrollmentContact]],
    dependencies=[Depends(require_read_sequence_access)],
)
async def list_sequence_enrollment_contacts(
    run_id: uuid.UUID,
    request: ListEntityRequestV2,
    user_auth_context: AnnotatedReevoUserAuthContext,
    domain_object_query_service: Annotated[
        DomainObjectQueryService, Depends(domain_object_query_service_from_lifespan)
    ],
) -> PaginatedListResponse[StandardRecord[SequenceEnrollmentContact]]:
    """
    List all contact enrollment results for a specific sequence enrollment run.
    """
    if request.record_id:
        enrollment_contact = (
            await domain_object_query_service.list_sequence_enrollment_contact_records(
                user_auth_context=user_auth_context,
                organization_id=user_auth_context.organization_id,
                enrollment_run_id=run_id,
                only_include_contact_ids={request.record_id},
            )
        )
        return single_paginate_object_record(
            object_records=enrollment_contact,
        )

    enrollment_contacts = (
        await domain_object_query_service.list_sequence_enrollment_contact_records(
            user_auth_context=user_auth_context,
            organization_id=user_auth_context.organization_id,
            enrollment_run_id=run_id,
            fetch_conditions=ObjectRecordFetchConditions(
                filter_spec=request.filter_spec,
                sorting_spec=request.sorting_spec,
                fields=request.ordered_object_fields,
            ),
        )
    )

    return paginate_object_records(
        object_records=enrollment_contacts,
        cursor=request.cursor,
    )


@router.post(
    "/async",
    response_model=CreateSequenceEnrollmentResponseV2,
    dependencies=[Depends(require_update_sequence_access)],
)
async def create_sequence_enrollments_and_run_async(
    request: CreateSequenceEnrollmentRequest,
    user_auth_context: AnnotatedReevoUserAuthContext,
    sequence_enrollment_service: Annotated[
        SequenceEnrollmentService, Depends(sequence_enrollment_service_from_lifespan)
    ],
) -> CreateSequenceEnrollmentResponseV2:
    return await sequence_enrollment_service.create_enrollments_and_run_async(
        user_auth_context=user_auth_context,
        request=request,
    )


@router.post(
    "/sync",
    response_model=CreateSequenceEnrollmentResponseV2,
    dependencies=[Depends(require_update_sequence_access)],
)
async def create_sequence_enrollments_and_run(
    request: CreateSequenceEnrollmentRequest,
    user_auth_context: AnnotatedReevoUserAuthContext,
    sequence_enrollment_service: Annotated[
        SequenceEnrollmentService, Depends(sequence_enrollment_service_from_lifespan)
    ],
) -> CreateSequenceEnrollmentResponseV2:
    can_access_sequence = (
        await sequence_enrollment_service.can_access_sequence_by_id_for_update(
            user_auth_context=user_auth_context,
            sequence_id=request.sequence_id,
        )
    )
    if not can_access_sequence:
        raise ForbiddenError("You do not have permission to access this sequence")

    return await sequence_enrollment_service.create_sequence_enrollment_run_and_enrollments(
        contacts=request.contacts,
        domain_object_list_id=request.domain_object_list_id,
        sequence_id=request.sequence_id,
        bypass_warnings=request.bypass_warnings,
        user_id=user_auth_context.user_id,
        organization_id=user_auth_context.organization_id,
        enrollment_run_id=None,
    )


@router.post(
    "/reenroll",
    response_model=CreateSequenceEnrollmentResponseV2,
    dependencies=[Depends(require_update_sequence_access)],
)
async def reenroll_sequence_enrollment(
    request: ReenrollContactsRequest,
    user_auth_context: AnnotatedReevoUserAuthContext,
    sequence_enrollment_service: Annotated[
        SequenceEnrollmentService, Depends(sequence_enrollment_service_from_lifespan)
    ],
) -> CreateSequenceEnrollmentResponseV2:
    return await sequence_enrollment_service.bulk_reenroll_contacts_by_run_id(
        user_auth_context=user_auth_context,
        sequence_id=request.sequence_id,
        enrollment_run_id=request.enrollment_run_id,
        include_prev_warnings=request.include_prev_warnings,
        include_prev_failures=request.include_prev_failures,
        bypass_warnings=request.bypass_warnings,
        mode=request.mode,
    )


@router.post(
    "/_reenroll_contacts",
    response_model=CreateSequenceEnrollmentResponseV2,
    dependencies=[Depends(require_update_sequence_access)],
)
async def bulk_reenroll_contacts(
    request: BulkReenrollContactsRequest,
    user_auth_context: AnnotatedReevoUserAuthContext,
    sequence_enrollment_service: Annotated[
        SequenceEnrollmentService, Depends(sequence_enrollment_service_from_lifespan)
    ],
) -> CreateSequenceEnrollmentResponseV2:
    return await sequence_enrollment_service.bulk_reenroll_enrollments(
        user_auth_context=user_auth_context,
        enrollment_ids=request.enrollment_ids,
        include_non_completed=request.include_non_completed,
    )


@router.patch(
    "/{enrollment_id}/email",
    response_model=PatchEnrollmentEmailResponse,
    dependencies=[Depends(require_update_sequence_access)],
)
async def patch_enrollment_email(
    enrollment_id: uuid.UUID,
    request: PatchEnrollmentEmailRequest,
    user_auth_context: AnnotatedReevoUserAuthContext,
    sequence_enrollment_service: Annotated[
        SequenceEnrollmentService, Depends(sequence_enrollment_service_from_lifespan)
    ],
) -> PatchEnrollmentEmailResponse:
    return await sequence_enrollment_service.patch_enrollment_email(
        enrollment_id=enrollment_id,
        email=request.email,
        organization_id=user_auth_context.organization_id,
        user_id=user_auth_context.user_id,
    )
