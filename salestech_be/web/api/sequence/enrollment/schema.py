from __future__ import annotations

from datetime import datetime
from enum import StrEnum
from uuid import UUID

from pydantic import BaseModel

from salestech_be.common.type.patch_request import BasePatchRequest
from salestech_be.core.sequence.type.sequence_enrollment_contact_type import (
    SequenceEnrollmentContact,
)
from salestech_be.core.sequence.type.sequence_enrollment_types import (
    SequenceEnrollmentEligibility,
    SequenceEnrollmentStatus,
)
from salestech_be.db.models.sequence import (
    SequenceEnrollmentRun as DBSequenceEnrollmentRun,
)
from salestech_be.db.models.sequence import (
    SequenceEnrollmentRunMode,
    SequenceEnrollmentRunStatus,
    SequenceFailureReason,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.web.api.common.container import (
    ListEntityRequestV2,
)

logger = get_logger(__name__)


class ListSequenceEnrollmentRequest(ListEntityRequestV2):
    sequence_id: UUID


class EnrollmentPreviewItem(BaseModel):
    contact_id: UUID
    email: str | None = None
    eligibility: SequenceEnrollmentEligibility
    can_enroll: bool
    display_name: str
    reason: str


class PreviewSequenceEnrollmentRequest(BaseModel):
    sequence_id: UUID
    contacts: list[ContactForSequenceEnrollment]
    domain_object_list_id: UUID | None = None


class PreviewSequenceEnrollmentResponse(BaseModel):
    sequence_id: UUID
    preview: list[EnrollmentPreviewItem]


class CreateSequenceEnrollmentRequest(BaseModel):
    sequence_id: UUID
    contacts: list[ContactForSequenceEnrollment]
    domain_object_list_id: UUID | None = None
    bypass_warnings: bool = False


class ReenrollContactsRequest(BaseModel):
    sequence_id: UUID
    enrollment_run_id: UUID
    include_prev_warnings: bool = False
    include_prev_failures: bool = False
    bypass_warnings: bool = False
    mode: SequenceEnrollmentRunMode = SequenceEnrollmentRunMode.SYNC


class BulkReenrollContactsRequest(BaseModel):
    enrollment_ids: list[UUID]
    include_non_completed: bool = False


class ContactForSequenceEnrollment(BaseModel):
    contact_id: UUID
    account_id: UUID | None = None
    email: str | None = None


class EnrolledContact(BaseModel):
    id: UUID
    contact_id: UUID
    status: SequenceEnrollmentStatus
    enrolled_at: ZoneRequiredDateTime


class FailedEnrollment(BaseModel):
    contact_id: UUID
    failure_reasons: list[SequenceFailureReason]


class CreateSequenceEnrollmentResponse(BaseModel):
    message: str
    enrolled_contacts: list[EnrolledContact]
    failed_enrollments: list[FailedEnrollment]


class CreateSequenceEnrollmentResponseV2(BaseModel):
    message: str
    enrollment_run_id: UUID | None
    enrollment_contacts: list[SequenceEnrollmentContact]


class EligibleWithWarningsContact(BaseModel):
    contact_id: UUID
    email: str | None = None
    account_id: UUID | None = None
    reasons: list[EnrollmentWarningReason]
    description: str | None = None


class IneligibleContact(BaseModel):
    contact_id: UUID
    email: str | None = None
    account_id: UUID | None = None
    reasons: list[EnrollmentIneligibilityReason]


class DeleteSequenceEnrollmentResponse(BaseModel):
    message: str
    id: UUID
    exited_at: ZoneRequiredDateTime
    deleted_at: ZoneRequiredDateTime
    deleted_by_user_id: UUID


class PatchSequenceEnrollmentRequest(BasePatchRequest):
    status: SequenceEnrollmentStatus


class PatchSequenceEnrollmentResponse(BaseModel):
    id: UUID
    status: SequenceEnrollmentStatus
    updated_at: ZoneRequiredDateTime


class BulkChangeSequenceEnrollmentRequest(BaseModel):
    enrollment_ids: list[UUID]
    status: SequenceEnrollmentStatus


class UpdatedEnrollment(BaseModel):
    id: UUID
    status: SequenceEnrollmentStatus


class BulkChangeSequenceEnrollmentResponse(BaseModel):
    message: str
    updated_enrollments: list[UpdatedEnrollment]


class ProceedEnrollment(BaseModel):
    id: UUID
    status: SequenceEnrollmentStatus


class SequenceEnrollmentRun(BaseModel):
    id: UUID
    organization_id: UUID
    sequence_id: UUID
    mode: SequenceEnrollmentRunMode
    workflow_id: str | None = None
    status: SequenceEnrollmentRunStatus
    created_by_user_id: UUID
    created_at: datetime

    @classmethod
    def from_db(cls, db_run: DBSequenceEnrollmentRun) -> SequenceEnrollmentRun:
        return cls(
            id=db_run.id,
            organization_id=db_run.organization_id,
            sequence_id=db_run.sequence_id,
            mode=db_run.mode,
            workflow_id=db_run.workflow_id,
            status=db_run.status,
            created_by_user_id=db_run.created_by_user_id,
            created_at=db_run.created_at,
        )


class ListSequenceEnrollmentRunsResponse(BaseModel):
    runs: list[SequenceEnrollmentRun]


class ListSequenceEnrollmentRunsRequest(BaseModel):
    sequence_id: UUID


class PatchEnrollmentEmailRequest(BaseModel):
    email: str


class PatchEnrollmentEmailResponse(BaseModel):
    message: str
    enrollment_id: UUID
    new_email: str


class EnrollmentWarningReason(StrEnum):
    MAILBOXES_ARE_NOT_WARMED_UP = "Mailboxes are not warmed up"
    CONTACT_IS_UNSUBSCRIBED = "Contact is unsubscribed"
    CONTACT_IS_ENROLLED_IN_OTHER_SEQUENCES = "Contact is enrolled in other sequences"


class EnrollmentIneligibilityReason(StrEnum):
    CONTACT_DOES_NOT_HAVE_ACCOUNT = "Contact does not have account"
    CONTACT_DOES_NOT_HAVE_EMAIL = "Contact does not have email"
    CONTACT_ALREADY_ENROLLED_IN_THIS_SEQUENCE = (
        "Contact is already enrolled in this sequence"
    )
    CONTACT_ALREADY_FAILED_THIS_SEQUENCE_PREVIOUSLY = (
        "Contact already failed this sequence previously"
    )
    CONTACT_ALREADY_EXITED_THIS_SEQUENCE_PREVIOUSLY = (
        "Contact already exited this sequence previously"
    )
    CONTACT_DOES_NOT_HAVE_A_EMAIL_POOL_AVAILABLE = (
        "Contact does not have a email pool available"
    )
    ALL_MAILBOXES_IN_POOL_ARE_AT_OVER_CAPACITY = (
        "All mailboxes in pool are at/over capacity"
    )
