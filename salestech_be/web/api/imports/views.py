from typing import Annotated
from uuid import UUID

from fastapi import Depends, HTTPException
from fastapi.params import Query

from salestech_be.core.imports.models.import_job import (
    ImportCsvJobStatus,
    ImportMetadata,
)
from salestech_be.core.imports.service.crm_sync_service import (
    CrmSyncService,
    get_crm_sync_service,
)
from salestech_be.core.imports.service.import_job_service import (
    ImportJobService,
    get_import_job_service,
)
from salestech_be.core.imports.types import (
    ImportCsvJobDetail,
    ImportCsvJobList,
    ImportCsvSampleRequest,
    ImportCsvSampleResponse,
    ImportJobFilter,
    ImportMeetingRequest,
    ImportMeetingResponse,
    SignalResponse,
    WorkflowSignalRequest,
)
from salestech_be.services.auth.perm_predicates import require_read_placeholder_access
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserId,
)

router = ReeAPIRouter()

REMOVED_ENDPOINT_MSG: str = (
    "This endpoint is deprecated with implementation removed, currently"
    " unused, and will be completely removed in a future version."
)


@router.post(
    "/csv/samples",
    response_model=ImportCsvSampleResponse,
    dependencies=[Depends(require_read_placeholder_access)],
    deprecated=True,
)
async def get_csv_samples(
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    import_csv_sample_request: ImportCsvSampleRequest,
    import_job_service: Annotated[ImportJobService, Depends(get_import_job_service)],
) -> ImportCsvSampleResponse:
    """(DEPRECATED TO UNIMPLEMENTED) Get CSV samples."""
    raise NotImplementedError(REMOVED_ENDPOINT_MSG)


@router.post(
    "/csv/jobs",
    response_model=ImportCsvJobDetail,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def create_import_csv_job(
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    service: Annotated[ImportJobService, Depends(get_import_job_service)],
) -> ImportCsvJobDetail:
    """Create a new import CSV job and start its workflow"""
    job = await service.create_and_start_import_csv_job(
        organization_id, user_id=user_id
    )
    return ImportCsvJobDetail.model_validate(job.model_dump())


@router.post(
    "/",
    response_model=ImportCsvJobDetail,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def create_import_job(
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    import_metadata: ImportMetadata,
    service: Annotated[ImportJobService, Depends(get_import_job_service)],
) -> ImportCsvJobDetail:
    job = await service.process_file_import(
        organization_id,
        user_id=user_id,
        import_metadata=import_metadata,
    )
    return ImportCsvJobDetail.model_validate(job.model_dump())


@router.get(
    "/csv/jobs/{job_id}",
    response_model=ImportCsvJobDetail,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def get_import_csv_job(
    job_id: UUID,
    organization_id: AnnotatedReevoOrganizationId,
    service: Annotated[ImportJobService, Depends(get_import_job_service)],
) -> ImportCsvJobDetail:
    """Get import CSV job"""
    job = await service.get_job(job_id, organization_id)
    if not job:
        raise HTTPException(status_code=404, detail="Import job not found")
    return ImportCsvJobDetail.model_validate(job.model_dump())


@router.get(
    "/csv/jobs/{job_id}/details",
    response_model=ImportCsvJobDetail,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def get_import_job_details(
    job_id: UUID,
    organization_id: AnnotatedReevoOrganizationId,
    service: Annotated[ImportJobService, Depends(get_import_job_service)],
) -> ImportCsvJobDetail:
    """Get details of an import CSV job"""
    return await service.get_job_details(job_id, organization_id)


@router.get(
    "/csv/jobs/{job_id}/summary",
    response_model=ImportCsvJobDetail,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def get_import_job_summary(
    job_id: UUID,
    organization_id: AnnotatedReevoOrganizationId,
    service: Annotated[ImportJobService, Depends(get_import_job_service)],
) -> ImportCsvJobDetail:
    """Get details of an import CSV job"""
    return await service.get_job_details(job_id, organization_id, lightweight_mode=True)


@router.post(
    "/csv/jobs/{job_id}/signal",
    response_model=SignalResponse,
    dependencies=[Depends(require_read_placeholder_access)],
    deprecated=True,
)
async def send_workflow_signal(
    job_id: UUID,
    signal_request: WorkflowSignalRequest,
    organization_id: AnnotatedReevoOrganizationId,
    service: Annotated[ImportJobService, Depends(get_import_job_service)],
) -> SignalResponse:
    """(DEPRECATED TO UNIMPLEMENTED) Send a signal to the import CSV workflow."""
    raise NotImplementedError(REMOVED_ENDPOINT_MSG)


@router.get(
    "/csv/jobs",
    response_model=ImportCsvJobList,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def list_import_jobs(
    crm_sync_service: Annotated[ImportJobService, Depends(get_import_job_service)],
    organization_id: AnnotatedReevoOrganizationId,
    status: Annotated[ImportCsvJobStatus | None, Query()] = None,
) -> ImportCsvJobList:
    jobs = await crm_sync_service.list_jobs(organization_id)
    return ImportCsvJobList(
        jobs=[job for job in jobs if (not status or status == job.status)]
    )


@router.post(
    "/jobs",
    response_model=ImportCsvJobList,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def list_jobs(
    crm_sync_service: Annotated[ImportJobService, Depends(get_import_job_service)],
    organization_id: AnnotatedReevoOrganizationId,
    job_filter: ImportJobFilter,
) -> ImportCsvJobList:
    jobs = await crm_sync_service.filter_jobs(organization_id, job_filter)
    return ImportCsvJobList(jobs=jobs)


@router.post(
    "/import_meeting",
    response_model=ImportMeetingResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def import_meeting(
    import_meeting_request: ImportMeetingRequest,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    crm_sync_service: Annotated[CrmSyncService, Depends(get_crm_sync_service)],
) -> ImportMeetingResponse:
    field_values_map = import_meeting_request.model_dump()

    import_record = await crm_sync_service.process_meeting_import_row(
        user_id=user_id,
        organization_id=organization_id,
        field_values_map=field_values_map,
    )

    return ImportMeetingResponse(import_record=import_record)
