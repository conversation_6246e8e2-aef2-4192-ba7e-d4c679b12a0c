from typing import Any
from uuid import UUID

from temporalio import activity, workflow

from salestech_be.integrations.slack.slack_client import SlackChannels

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.imports.service.crm_sync_service import (
        get_crm_sync_service_from_db_engine,
    )
    from salestech_be.core.imports.types import ImportCsvJobDetail, ImportResultStats
    from salestech_be.integrations.temporal.client import get_temporal_client
    from salestech_be.ree_logging import get_logger
    from salestech_be.settings import settings
    from salestech_be.temporal.database import get_or_init_db_engine

logger = get_logger()


async def _generate_import_result_slack_message(
    job_result_lite: ImportCsvJobDetail,
) -> str:
    """Generate a formatted slack message for import job results.

    Uses Slack's mrkdwn formatting to create a multi-line message with job details,
    summary counts, and relevant links.
    """
    # Calculate duration in minutes and seconds
    duration_seconds = 0
    if job_result_lite.completed_at and job_result_lite.created_at:
        duration_seconds = int(
            (job_result_lite.completed_at - job_result_lite.created_at).total_seconds()
        )

    # Create the review page URL
    review_url = f"{settings.public_app_base_url}/settings/imports/{job_result_lite.id}"

    # Get aggregate results for summary counts
    total_count = 0
    success_count = 0
    conflict_count = 0
    failed_count = 0

    if job_result_lite.aggregate_import_results:
        total_count = job_result_lite.aggregate_import_results.get(
            ImportResultStats.TOTAL_RECORDS_ATTEMPTED, 0
        )
        success_count = job_result_lite.aggregate_import_results.get(
            ImportResultStats.TOTAL_SUCCESS_RECORDS, 0
        )
        conflict_count = job_result_lite.aggregate_import_results.get(
            ImportResultStats.TOTAL_CONFLICT_RECORDS, 0
        )
        failed_count = job_result_lite.aggregate_import_results.get(
            ImportResultStats.TOTAL_FAILED_RECORDS, 0
        )

    # Format message using Slack's mrkdwn syntax
    message = (
        f"{settings.environment} - Import Job process completed: {job_result_lite.id}\n"
        f"Duration: {duration_seconds // 60} minutes and {duration_seconds % 60} seconds\n"
        f"Review page: <{review_url}|View import details>\n\n"
        f"*Summary:*\n"
        f"• Total: {total_count}\n"
        f"• Success: {success_count}\n"
        f"• Conflict: {conflict_count}\n"
        f"• Failed: {failed_count}"
    )

    if hasattr(job_result_lite, "output_file") and job_result_lite.output_file:
        message += f"\n*Output:* <{job_result_lite.output_file.direct_link}|Download Output File>"

    return message


@activity.defn(name="process_csv_import_v2")
async def process_csv_import_v2(  # type: ignore[explicit-any] # TODO: fix-any-annotation
    job_id: UUID,
    organization_id: UUID,
) -> dict[str, Any]:
    db_engine = await get_or_init_db_engine()
    crm_sync_service = get_crm_sync_service_from_db_engine(engine=db_engine)
    # Import here to avoid circular dependency
    from salestech_be.core.imports.service.import_job_service import (
        get_import_job_service,
    )

    import_job_service = get_import_job_service(
        temporal_client=await get_temporal_client(), db_engine=db_engine
    )
    logger.bind(job_id=job_id, organization_id=organization_id).info(
        "process_csv_import_v2 started."
    )
    try:
        process_result = await crm_sync_service.process_import_job_v2(
            job_id=job_id,
            organization_id=organization_id,
        )

        logger.bind(process_result=process_result, job_id=job_id).info(
            "process_csv_import_v2 job completed."
        )

        job_result_lite = await import_job_service.get_job_details(
            job_id=job_id, organization_id=organization_id, lightweight_mode=True
        )

        await crm_sync_service.slack_client.send_message(
            env=settings.environment,
            channel=SlackChannels.CRM_IMPORT_STATUS,
            text=await _generate_import_result_slack_message(
                job_result_lite=job_result_lite
            ),
        )

        # Let's not return process_result, it could be large for workflow to track.  However,
        #   let's also not change the function signature, which is a workflow versioning hassle.
        return {}
    except Exception as e:
        logger.opt(exception=e).exception("process_csv_import_v2 got general exception")
        return {"error": str(e)}
