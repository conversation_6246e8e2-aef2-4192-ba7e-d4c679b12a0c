from collections.abc import Sequence
from typing import Annotated
from uuid import UUID

from fastapi import Depends, HTTPException, status

from salestech_be.common.exception.exception import (
    ForbiddenError,
    ResourceNotFoundError,
)
from salestech_be.core.imports.service.import_csv_job_review_service import (
    ImportCsvJobReviewService,
)
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import (
    require_admin_access,
    require_super_admin_access,
)
from salestech_be.web.api.imports.csv.job_review.schema import (
    ImportCsvJobReviewApprove,
    ImportCsvJobReviewArchive,
    ImportCsvJobReviewCreate,
    ImportCsvJobReviewDetailResponse,
    ImportCsvJobReviewExtraInfoResponse,
    ImportCsvJobReviewListRequest,
    ImportCsvJobReviewPending,
    ImportCsvJobReviewReject,
    ImportCsvJobReviewResponse,
)
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.lifespan_service import (
    import_csv_job_review_service_from_lifespan,
)
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserAuthContext,
    AnnotatedReevoUserId,
)

logger = get_logger()
router = ReeAPIRouter()


def _restrict_organization_admin_access(
    user_auth_context: AnnotatedReevoUserAuthContext,
    job_organization_id: UUID | None,
) -> None:
    """
    Enforcement is only for regular admins.  Super admins are cross organization.
    """
    if not user_auth_context.is_super_admin and (
        job_organization_id is None
        or user_auth_context.organization_id != job_organization_id
    ):
        raise ForbiddenError(
            f"Not allowed operation on organization({job_organization_id})"
            f" from organization({user_auth_context.organization_id})"
        )


@router.post(
    "",
    response_model=ImportCsvJobReviewResponse,
    status_code=status.HTTP_201_CREATED,
    description="Create a new import CSV job_review",
    dependencies=[Depends(require_admin_access)],
)
async def create_job_review(
    user_auth_context: AnnotatedReevoUserAuthContext,
    request_body: ImportCsvJobReviewCreate,
    import_csv_job_review_service: Annotated[
        ImportCsvJobReviewService,
        Depends(import_csv_job_review_service_from_lifespan),
    ],
) -> ImportCsvJobReviewResponse:
    logger.bind(
        job_organization_id=request_body.job_organization_id,
        user_id=user_auth_context.user_id,
        organization_id=user_auth_context.organization_id,
    ).info("Create job review")
    _restrict_organization_admin_access(
        user_auth_context, request_body.job_organization_id
    )
    to_return = await import_csv_job_review_service.create_job_review(
        organization_id=request_body.job_organization_id,
        submitter_user_id=user_auth_context.user_id,
        import_metadata=request_body.import_metadata,
    )
    return ImportCsvJobReviewResponse.from_db(to_return)


@router.post(
    "/_list",
    response_model=list[ImportCsvJobReviewExtraInfoResponse],
    status_code=status.HTTP_201_CREATED,
    description="List CSV import job reviews for the current organization",
    dependencies=[Depends(require_admin_access)],
)
async def list_submissions(
    user_auth_context: AnnotatedReevoUserAuthContext,
    request_body: ImportCsvJobReviewListRequest,
    import_csv_job_review_service: Annotated[
        ImportCsvJobReviewService,
        Depends(import_csv_job_review_service_from_lifespan),
    ],
) -> Sequence[ImportCsvJobReviewExtraInfoResponse]:
    _restrict_organization_admin_access(
        user_auth_context, request_body.job_organization_id
    )
    to_return = await import_csv_job_review_service.list_job_reviews(
        organization_id=request_body.job_organization_id,
        statuses=request_body.statuses,
        limit=request_body.limit,
        offset=request_body.offset,
    )
    return [
        ImportCsvJobReviewExtraInfoResponse.from_db_extra_info(job_review)
        for job_review in to_return
    ]


@router.get(
    "/{job_review_id}",
    response_model=ImportCsvJobReviewDetailResponse,
    status_code=status.HTTP_200_OK,
    description="Get a CSV import job review along with the import job",
    dependencies=[Depends(require_admin_access)],
)
async def get_job_review(
    user_auth_context: AnnotatedReevoUserAuthContext,
    job_review_id: UUID,
    import_csv_job_review_service: Annotated[
        ImportCsvJobReviewService,
        Depends(import_csv_job_review_service_from_lifespan),
    ],
) -> ImportCsvJobReviewDetailResponse:
    organization_id_to_enforce: UUID | None = (
        None  # We don't get organization until DB lookup
    )
    if not user_auth_context.is_super_admin:
        organization_id_to_enforce = user_auth_context.organization_id
    (
        job_review,
        import_job,
        organization,
    ) = await import_csv_job_review_service.get_job_review_detail(
        job_review_id,
        organization_id_to_enforce=organization_id_to_enforce,
    )
    return ImportCsvJobReviewDetailResponse.from_db_detail(
        job_review,
        import_job,
        organization.display_name if organization else None,
    )


@router.post(
    "/{job_review_id}/_approve",
    response_model=ImportCsvJobReviewResponse,
    status_code=status.HTTP_201_CREATED,
    description="Approve a CSV import job review",
    dependencies=[Depends(require_super_admin_access)],
)
async def approve_job_review(
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    job_review_id: UUID,
    request_body: ImportCsvJobReviewApprove,
    import_csv_job_review_service: Annotated[
        ImportCsvJobReviewService,
        Depends(import_csv_job_review_service_from_lifespan),
    ],
) -> ImportCsvJobReviewResponse:
    logger.bind(
        job_review_id=job_review_id,
        job_organization_id=request_body.job_organization_id,
        user_id=user_id,
        organization_id=organization_id,
    ).info("Approving job review")
    to_return = await import_csv_job_review_service.set_approve_job_review(
        organization_id=request_body.job_organization_id,
        job_review_id=job_review_id,
        reviewer_user_id=user_id,
    )
    return ImportCsvJobReviewResponse.from_db(to_return)


@router.post(
    "/{job_review_id}/_reject",
    response_model=ImportCsvJobReviewResponse,
    status_code=status.HTTP_201_CREATED,
    description="Reject a CSV import job review",
    dependencies=[Depends(require_super_admin_access)],
)
async def reject_job_review(
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    job_review_id: UUID,
    request_body: ImportCsvJobReviewReject,
    import_csv_job_review_service: Annotated[
        ImportCsvJobReviewService,
        Depends(import_csv_job_review_service_from_lifespan),
    ],
) -> ImportCsvJobReviewResponse:
    logger.bind(
        job_review_id=job_review_id,
        job_organization_id=request_body.job_organization_id,
        user_id=user_id,
        organization_id=organization_id,
    ).info("Rejecting job review")
    to_return = await import_csv_job_review_service.set_reject_job_review(
        organization_id=request_body.job_organization_id,
        job_review_id=job_review_id,
        reviewer_user_id=user_id,
        reviewer_notes=request_body.reviewer_notes,
    )
    return ImportCsvJobReviewResponse.from_db(to_return)


@router.post(
    "/{job_review_id}/_pending",
    response_model=ImportCsvJobReviewResponse,
    status_code=status.HTTP_201_CREATED,
    description="Mark a CSV import job review as ready for review, likely after a rejection",
    dependencies=[Depends(require_admin_access)],
)
async def pending_job_review(
    user_auth_context: AnnotatedReevoUserAuthContext,
    job_review_id: UUID,
    request_body: ImportCsvJobReviewPending,
    import_csv_job_review_service: Annotated[
        ImportCsvJobReviewService,
        Depends(import_csv_job_review_service_from_lifespan),
    ],
) -> ImportCsvJobReviewResponse:
    logger.bind(
        job_review_id=job_review_id,
        job_organization_id=request_body.job_organization_id,
        user_id=user_auth_context.user_id,
        organization_id=user_auth_context.organization_id,
    ).info("Set PENDING job review")
    _restrict_organization_admin_access(
        user_auth_context, request_body.job_organization_id
    )
    to_return = await import_csv_job_review_service.set_pending_review(
        organization_id=request_body.job_organization_id,
        job_review_id=job_review_id,
        submitter_user_id=user_auth_context.user_id,
        import_metadata=request_body.import_metadata,
    )
    return ImportCsvJobReviewResponse.from_db(to_return)


@router.post(
    "/{job_review_id}/_archive",
    status_code=status.HTTP_201_CREATED,
    description="Delete a CSV import job review",
    dependencies=[Depends(require_super_admin_access)],
)
async def archive_job_review(
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    job_review_id: UUID,
    request_body: ImportCsvJobReviewArchive,
    import_csv_job_review_service: Annotated[
        ImportCsvJobReviewService,
        Depends(import_csv_job_review_service_from_lifespan),
    ],
) -> None:
    logger.bind(
        job_review_id=job_review_id,
        job_organization_id=request_body.job_organization_id,
        user_id=user_id,
        organization_id=organization_id,
    ).info("Archiving job review")
    try:
        await import_csv_job_review_service.delete_job_review(
            organization_id=request_body.job_organization_id,
            job_review_id=job_review_id,
        )
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
