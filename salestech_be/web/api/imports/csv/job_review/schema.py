from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.core.imports.models.import_job import ImportJob, ImportMetadata
from salestech_be.db.models.import_csv_job_review_db import (
    ImportCsvJobReviewDb,
    ImportCsvJobReviewExtraInfoDb,
    ImportCsvJobReviewStatusDb,
)


class ImportCsvJobReviewBase(BaseModel):
    pass


class ImportCsvJobReviewCreate(ImportCsvJobReviewBase):
    job_organization_id: UUID = Field(..., description="Organization ID of the job")
    import_metadata: ImportMetadata = Field(
        ..., description="Metadata for the import job"
    )


class ImportCsvJobReviewPending(ImportCsvJobReviewBase):
    job_organization_id: UUID = Field(..., description="Organization ID of the job")
    import_metadata: ImportMetadata = Field(
        ..., description="Metadata for the import job"
    )


class ImportCsvJobReviewListRequest(BaseModel):
    job_organization_id: UUID | None = Field(
        None, description="Filter by organization ID of the job"
    )
    statuses: list[ImportCsvJobReviewStatusDb] | None = Field(
        None, description="Filter by submission statuses"
    )
    limit: int = Field(
        100, ge=1, le=2000, description="Maximum number of submissions to return"
    )
    offset: int = Field(0, ge=0, description="Number of submissions to skip")


class ImportCsvJobReviewResponse(BaseModel):
    id: UUID = Field(..., description="Unique identifier for the submission")
    organization_id: UUID = Field(..., description="Organization ID")
    display_name: str | None = Field(
        None, description="Display name for the submission"
    )
    submitter_user_id: UUID = Field(..., description="User ID who submitted the file")
    import_csv_job_id: UUID | None = Field(
        None,
        description="ID of the associated import CSV job. Can use GET /imports/csv/jobs/{import_csv_job_id} to retrieve the job details.",
    )
    reviewer_user_id: UUID | None = Field(
        None, description="User ID who reviewed the submission"
    )
    reviewer_notes: str | None = Field(None, description="Notes from the reviewer")
    status: ImportCsvJobReviewStatusDb = Field(
        ..., description="Current status of the submission"
    )
    status_changed_at: datetime | None = Field(
        None, description="Timestamp when status last changed"
    )
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    archived_at: datetime | None = Field(None, description="Archive timestamp")

    class Config:
        from_attributes = True

    @staticmethod
    def from_db(job_review: ImportCsvJobReviewDb) -> "ImportCsvJobReviewResponse":
        return ImportCsvJobReviewResponse(
            id=job_review.id,
            organization_id=job_review.organization_id,
            display_name=job_review.display_name,
            submitter_user_id=job_review.submitter_user_id,
            import_csv_job_id=job_review.import_csv_job_id,
            reviewer_user_id=job_review.reviewer_user_id,
            reviewer_notes=job_review.reviewer_notes,
            status=ImportCsvJobReviewStatusDb(job_review.status),
            status_changed_at=job_review.status_changed_at,
            created_at=job_review.created_at,
            updated_at=job_review.updated_at,
            archived_at=job_review.archived_at,
        )


class ImportCsvJobReviewDetailResponse(ImportCsvJobReviewResponse):
    import_metadata: ImportMetadata | None = Field(
        ..., description="Import job details"
    )
    info_organization_display_name: str | None = Field(
        None, description="Display name of the organization"
    )

    @staticmethod
    def from_db_detail(
        job_review: ImportCsvJobReviewDb,
        import_job: ImportJob | None,
        organization_display_name: str | None,
    ) -> "ImportCsvJobReviewDetailResponse":
        import_metadata = None
        if import_job and import_job.configuration and import_job.metadata:
            import_metadata = ImportMetadata(
                display_name=import_job.display_name if import_job.display_name else "",
                file_ids=[],
                import_configuration=import_job.configuration,
                file_metadata=[import_job.metadata],
            )
        return ImportCsvJobReviewDetailResponse(
            id=job_review.id,
            organization_id=job_review.organization_id,
            display_name=job_review.display_name,
            submitter_user_id=job_review.submitter_user_id,
            import_csv_job_id=job_review.import_csv_job_id,
            reviewer_user_id=job_review.reviewer_user_id,
            reviewer_notes=job_review.reviewer_notes,
            status=ImportCsvJobReviewStatusDb(job_review.status),
            status_changed_at=job_review.status_changed_at,
            created_at=job_review.created_at,
            updated_at=job_review.updated_at,
            archived_at=job_review.archived_at,
            import_metadata=import_metadata,
            info_organization_display_name=organization_display_name,
        )


class ImportCsvJobReviewExtraInfoResponse(ImportCsvJobReviewResponse):
    info_organization_display_name: str | None = Field(
        None, description="Display name of the organization"
    )

    @staticmethod
    def from_db_extra_info(
        job_review_extra_info: ImportCsvJobReviewExtraInfoDb,
    ) -> "ImportCsvJobReviewExtraInfoResponse":
        return ImportCsvJobReviewExtraInfoResponse(
            id=job_review_extra_info.id,
            organization_id=job_review_extra_info.organization_id,
            display_name=job_review_extra_info.display_name,
            submitter_user_id=job_review_extra_info.submitter_user_id,
            import_csv_job_id=job_review_extra_info.import_csv_job_id,
            reviewer_user_id=job_review_extra_info.reviewer_user_id,
            reviewer_notes=job_review_extra_info.reviewer_notes,
            status=ImportCsvJobReviewStatusDb(job_review_extra_info.status),
            status_changed_at=job_review_extra_info.status_changed_at,
            created_at=job_review_extra_info.created_at,
            updated_at=job_review_extra_info.updated_at,
            archived_at=job_review_extra_info.archived_at,
            info_organization_display_name=job_review_extra_info.info_organization_display_name,
        )


class ImportCsvJobReviewApprove(BaseModel):
    job_organization_id: UUID = Field(..., description="Organization ID of the job")


class ImportCsvJobReviewReject(BaseModel):
    job_organization_id: UUID = Field(..., description="Organization ID of the job")
    reviewer_notes: str | None = Field(None, description="Reason for rejection")


class ImportCsvJobReviewArchive(BaseModel):
    job_organization_id: UUID = Field(..., description="Organization ID of the job")
