from datetime import datetime
from typing import Any, Literal
from uuid import UUID

from pydantic import BaseModel

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception.exception import ErrorDetails, InvalidArgumentError
from salestech_be.common.type.patch_request import (
    BasePatchRequest,
    UnsetAware,
)
from salestech_be.core.email.template.types import EmailTemplate
from salestech_be.core.email.type.email import (
    EmailHydratedParticipant,
    PreviewTemplateBodyParticipant,
)
from salestech_be.db.models.email_template import (
    EmailTemplateCategory,
    EmailTemplatePermissions,
    EmailTemplateType,
)
from salestech_be.web.api.common.container import ListEntityRequestV2


class CreateEmailTemplateRequest(BaseModel):
    name: str
    type: EmailTemplateType
    subject: str | None = None
    body_html: str
    categories: list[str] | None = None
    attachment_ids: list[UUID] | None = None
    permissions: Literal[
        EmailTemplatePermissions.PRIVATE,
        EmailTemplatePermissions.TEAM_VIEWABLE,
        EmailTemplatePermissions.TEAM_EDITABLE,
    ]
    include_email_signature: bool = False


class PatchEmailTemplateRequest(BasePatchRequest):
    name: UnsetAware[str]
    type: UnsetAware[EmailTemplateType]
    subject: UnsetAware[str | None]
    body_html: UnsetAware[str]
    attachment_ids: UnsetAware[list[UUID] | None]
    categories: UnsetAware[list[str] | None]
    permissions: UnsetAware[
        Literal[
            EmailTemplatePermissions.PRIVATE,
            EmailTemplatePermissions.TEAM_VIEWABLE,
            EmailTemplatePermissions.TEAM_EDITABLE,
        ]
    ]
    include_email_signature: UnsetAware[bool]


class PreviewEmailTemplateRequest(BaseModel):
    send_from: EmailHydratedParticipant
    send_to: EmailHydratedParticipant | None = None

    def model_post_init(self, __context: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().model_post_init(__context)
        if self.send_to and self.send_to.email:
            self.send_to.validate_request()

        if not self.send_from.email_account_id:
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_REQUEST,
                    details="Email account id is required.",
                )
            )


class PreviewEmailTemplateResponse(BaseModel):
    subject: str | None
    body_html: str
    unresolved_variables: list[str] | None

    @classmethod
    def from_email_template(
        cls,
        email_template: EmailTemplate,
        unresolved_variables: set[str] | None = None,
    ) -> "PreviewEmailTemplateResponse":
        return PreviewEmailTemplateResponse(
            subject=email_template.subject,
            body_html=email_template.body_html,
            unresolved_variables=list(unresolved_variables)
            if unresolved_variables
            else None,
        )


class PreviewEmailTemplateBodyRequest(BaseModel):
    send_from: PreviewTemplateBodyParticipant | None = None
    send_to: PreviewTemplateBodyParticipant | None = None
    body_html: str
    subject: str
    include_email_signature: bool = False
    use_default: bool = True

    def model_post_init(self, __context: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().model_post_init(__context)

        if self.send_to:
            self.send_to.validate_request()

        if self.send_from:
            self.send_from.validate_request()


class PreviewEmailTemplateBodyResponse(BaseModel):
    subject: str | None
    body_html: str
    unresolved_variables: list[str] | None


class ListEmailTemplateHistoryRequest(ListEntityRequestV2):
    email_template_id: UUID


class EmailTemplateCategoryResponse(BaseModel):
    id: UUID
    name: str
    color: str | None
    created_at: datetime
    updated_at: datetime
    deleted_at: datetime | None
    created_by_user_id: UUID | None
    updated_by_user_id: UUID | None
    deleted_by_user_id: UUID | None

    @classmethod
    def from_db_category(
        cls, db_category: EmailTemplateCategory
    ) -> "EmailTemplateCategoryResponse":
        return EmailTemplateCategoryResponse(
            id=db_category.id,
            name=db_category.name,
            color=db_category.color,
            created_at=db_category.created_at,
            updated_at=db_category.updated_at,
            deleted_at=db_category.deleted_at,
            created_by_user_id=db_category.created_by_user_id,
            updated_by_user_id=db_category.updated_by_user_id,
            deleted_by_user_id=db_category.deleted_by_user_id,
        )


class CreateEmailTemplateCategoryRequest(BaseModel):
    name: str
    color: str | None = None


class PatchEmailTemplateCategoryRequest(BasePatchRequest):
    require_at_least_one_specified_field = True
    color: UnsetAware[str | None]
