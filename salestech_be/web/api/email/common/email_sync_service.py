import base64
import uuid
from datetime import datetime
from itertools import chain
from uuid import UUID

from fastapi import Request
from pydantic import EmailStr

from salestech_be.common.exception import ResourceNotFoundError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.ai.email.activities.classify_message_and_update_metadata import (
    classify_message_and_update_metadata,
)
from salestech_be.core.ai.email.activities.parse_main_body_text_and_persist import (
    parse_main_body_text_and_persist,
)
from salestech_be.core.common.email_calendar_mixin import EmailCalendarMixin
from salestech_be.core.contact.service.contact_resolve_service import (
    ContactResolveService,
    get_contact_resolve_service,
)
from salestech_be.core.contact.service.contact_service import (
    ContactService,
    get_contact_service,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.email.account.email_account_ext import (
    EmailAccountServiceExt,
    get_email_account_service_ext_by_db_engine,
)
from salestech_be.core.email.attachment.email_attachment_service import (
    EmailAttachmentService,
    get_email_attachment_service_by_db_engine,
)
from salestech_be.core.email.participant.email_participant_service import (
    EmailParticipantService,
    get_email_participant_service,
)
from salestech_be.core.email.utils import sanitize_content
from salestech_be.core.files.service.file_service import (
    FileService,
    file_service_from_engine,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
    get_select_list_service,
)
from salestech_be.core.organization.service.organization_preference_service import (
    OrganizationPreferenceService,
    organization_preference_service_from_engine,
)
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.email_attchment_dto import AttachmentDto
from salestech_be.db.dto.email_dto import EmailDto, MessageDto
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.email_account import EmailAccount, EmailProvider
from salestech_be.db.models.message import Message
from salestech_be.db.models.thread import Thread
from salestech_be.db.models.user_integration import IntegrationProvider, IntegrationType
from salestech_be.integrations.google.client import GoogleClient
from salestech_be.integrations.nylas.async_nylas_client import AsyncNylasClient
from salestech_be.integrations.nylas.model import (
    NylasListMessageQuery,
    NylasListThreadQuery,
    NylasMessage,
    NylasThread,
)
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.encryptions import (
    EncryptionManager,
    fernet_encryption_manager,
)
from salestech_be.settings import settings
from salestech_be.util.validation import not_none
from salestech_be.web.api.connect.service import (
    UserIntegrationService,
    get_user_integration_service_by_db_engine,
)

logger = get_logger()


class EmailSyncService(EmailCalendarMixin):
    def __init__(
        self,
        async_nylas_client: AsyncNylasClient,
        message_repository: MessageRepository,
        thread_repository: ThreadRepository,
        file_service: FileService,
        email_attachment_service: EmailAttachmentService,
        email_participant_service: EmailParticipantService,
        email_account_service: EmailAccountServiceExt,
        contact_service: ContactService,
        contact_resolve_service: ContactResolveService,
        user_service: UserService,
        select_list_service: InternalSelectListService,
        google_client: GoogleClient,
        encryption_manager: EncryptionManager,
        user_integration_service: UserIntegrationService,
        organization_preference_service: OrganizationPreferenceService,
    ):
        super().__init__(
            contact_service=contact_service,
            user_service=user_service,
            select_list_service=select_list_service,
            organization_preference_service=organization_preference_service,
        )
        self.user_integration_service = user_integration_service
        self.async_nylas_client = async_nylas_client
        self.message_repository = message_repository
        self.thread_repository = thread_repository
        self.file_service = file_service
        self.email_attachment_service = email_attachment_service
        self.email_participant_service = email_participant_service
        self.email_account_service = email_account_service
        self.google_client = google_client
        self.encryption_manager = encryption_manager
        self.contact_resolve_service = contact_resolve_service

    async def sync_threads(
        self,
        email_account_id: UUID,
        organization_id: UUID,
        sync_to: datetime,
        total: int,
    ) -> list[EmailDto]:
        if not (
            email_account := await self.email_account_service.get_email_account_by_id(
                email_account_id=email_account_id, organization_id=organization_id
            )
        ):
            raise ResourceNotFoundError(
                f"Could not find email account for email_account : {email_account_id},"
                f"organization: {organization_id}"
            )

        (
            grant_id,
            integration_provider,
        ) = await self.email_account_service.get_nylas_grant_id_by_email_account_id(
            email_account_id=email_account.id,
            organization_id=organization_id,
        )

        # Define folders to fetch
        folders = ["INBOX", "SENT"]
        nylas_thread_list = []

        # Fetch threads from each folder
        for folder in folders:
            response = await self.async_nylas_client.list_threads(
                grant_id=grant_id,
                nylas_list_thread_query=NylasListThreadQuery(
                    latest_message_before=int(sync_to.timestamp()),
                    in_=folder,
                ),
                total=total,
            )
            nylas_thread_list += response.data

        # Deduplicate by thread ID
        nylas_thread_list = list(
            {thread.id: thread for thread in nylas_thread_list}.values()
        )

        return await self.insert_thread_and_sync_messages(
            email_account_id=email_account.id,
            organization_id=organization_id,
            grant_id=grant_id,
            integration_provider=integration_provider,
            nylas_thread_list=nylas_thread_list,
        )

    async def _get_first_message_in_thread_from_nylas(
        self,
        *,
        nylas_thread: NylasThread,
        grant_id: str,
    ) -> NylasMessage | None:
        """
        Get the first message in a thread, if it exists.

        This invovles external Nylas API call.

        Args:
            nylas_thread: Nylas thread
            grant_id: Nylas grant id
        """

        # Get the first message in the thread, assuming Nylas API behavior is consistent
        # TODO: (hao) confirm with Nylas
        first_message_id_in_thread = (
            nylas_thread.message_ids[0] if nylas_thread.message_ids else None
        )
        if first_message_id_in_thread:
            return await self.async_nylas_client.get_message_by_id(
                grant_id=grant_id,
                nylas_message_id=first_message_id_in_thread,
            )
        return None

    async def _create_records_from_thread_sync(
        self,
        *,
        nylas_thread: NylasThread,
        grant_id: str,
        integration_provider: IntegrationProvider,
        email_account_id: UUID,
        organization_id: UUID,
        emails_in_threads: set[EmailStr],
    ) -> None:
        """
        Create core CRM records from thread sync.

        Args:
            nylas_thread: Nylas thread
            grant_id: Nylas grant id
            email_account_id: Email account id
            organization_id: Organization id
            emails_in_threads: Set of emails in threads
        """
        if not settings.enable_email_activity_capture:
            return

        logger.info(
            "try create records from thread sync",
            provider_id=nylas_thread.id,
            grant_id=grant_id,
            email_account_id=email_account_id,
        )
        #
        # Get the original sender emails from DB if available or use external Nylas API
        #
        original_sender_emails = None
        if integration_provider == IntegrationProvider.GOOGLE:
            # With Google, the first message has an id that matches the thread id.
            # Therefore, we can do an early return if that's the case, without doing
            # external nalys API calls
            first_message_from_db = (
                await self.message_repository.get_by_provider_id_and_email_account_id(
                    provider_id=nylas_thread.id,
                    email_provider=EmailProvider.NYLAS,
                    email_account_id=email_account_id,
                )
            )
            if first_message_from_db and len(first_message_from_db) == 1:
                original_sender_emails = [
                    a.email for a in first_message_from_db[0].send_from
                ]
            logger.info(
                "original_sender_emails from DB",
                emails=original_sender_emails,
                provider_id=nylas_thread.id,
                grant_id=grant_id,
                email_account_id=email_account_id,
            )

        if not original_sender_emails:
            # this is a fallback to external Nylas API call to get the first message for the thread
            first_message_in_thread = (
                await self._get_first_message_in_thread_from_nylas(
                    nylas_thread=nylas_thread,
                    grant_id=grant_id,
                )
            )
            if not first_message_in_thread:
                logger.bind(
                    nylas_thread_id=nylas_thread.id,
                    grant_id=grant_id,
                ).error("No first message found in nylas thread, invalid thread?")
                return

            original_sender_emails = [a.email for a in first_message_in_thread.from_]
            logger.info(
                "original_sender_emails from nylas",
                emails=original_sender_emails,
                provider_id=nylas_thread.id,
                grant_id=grant_id,
                email_account_id=email_account_id,
            )

        #
        # Get the user_id for the email_account_id, and create contacts/accounts if applicable
        #
        email_account = await self.email_account_service.get_email_account_by_id(
            email_account_id=email_account_id,
            organization_id=organization_id,
        )
        if email_account:
            # if any email address in the orignal sender list is the email account's email
            # then it's originated by the user
            is_originated_by_user = any(
                email == email_account.email for email in original_sender_emails
            )

            # Convert set to list
            emails_list = list(emails_in_threads)

            await self.try_create_account_and_contact_for_emails(
                emails=emails_list,
                user_id=email_account.owner_user_id,
                organization_id=organization_id,
                is_event_sent_by_user=is_originated_by_user,
            )

    async def insert_thread_and_sync_messages(
        self,
        *,
        nylas_thread_list: list[NylasThread],
        grant_id: str,
        integration_provider: IntegrationProvider,
        email_account_id: UUID,
        organization_id: UUID,
        should_create_records: bool = False,
    ) -> list[EmailDto]:
        """
        Upsert thread and sync messages from Nylas. Return those with new messages.
        Optionally, create core CRM records from thread sync if enabled

        Args:
            nylas_thread_list: List of Nylas threads
            grant_id: Nylas grant id
            integration_provider: Integration provider
            email_account_id: Email account id
            organization_id: Organization id
            should_create_records: If True, create core CRM records from thread sync
        """
        results: list[EmailDto] = []
        emails_in_threads = {
            email.email
            for nylas_thread in nylas_thread_list
            if nylas_thread.participants
            for email in nylas_thread.participants
        }

        if should_create_records:
            # Create core CRM records from thread sync, before syncing messages
            # This is to make the legacy logic work (chicken-and-egg problem), which will skip syncing if no contacts available
            # Potentially read-after-write, if that's an issue, we can change to merge the results from this and the find_by_emails below to build the email_person_map
            for nylas_thread in nylas_thread_list:
                await self._create_records_from_thread_sync(
                    nylas_thread=nylas_thread,
                    grant_id=grant_id,
                    integration_provider=integration_provider,
                    email_account_id=email_account_id,
                    organization_id=organization_id,
                    emails_in_threads=emails_in_threads,
                )

        email_person_map: dict[
            EmailStr, EmailAccount | ContactV2 | None
        ] = await self.email_participant_service.find_contact_or_email_account_by_emails(
            organization_id=organization_id, emails=list(emails_in_threads)
        )
        for nylas_thread in nylas_thread_list:
            if not self._has_valid_contact_participant(nylas_thread, email_person_map):
                logger.bind(
                    email_account_id=email_account_id, nylas_thread_id=nylas_thread.id
                ).info("No existing contact found in nylas thread, skip syncing.")
                continue
            contact_email_pairs: list[tuple[UUID | None, str | None]] = []
            for participant in nylas_thread.participants or []:
                person = email_person_map.get(participant.email)
                if isinstance(person, Contact | ContactV2):
                    contact_email_pairs.append((person.id, participant.email))
            contact_account_map = await self.contact_resolve_service.resolve_relevant_account_by_contact_and_email_pairs(
                organization_id=organization_id,
                contact_email_pairs=contact_email_pairs,
            )
            inserted_thread = (
                await self.thread_repository.upsert_threads(
                    [
                        Thread.from_nylas_thread(
                            email_account_id=email_account_id,
                            organization_id=organization_id,
                            nylas_thread=nylas_thread,
                            email_person_map=email_person_map,
                            contact_account_map=contact_account_map,
                        )
                    ]
                )
            )[0]
            email_dto, has_new_message = await self.sync_messages_and_attachments(
                organization_id=organization_id,
                grant_id=grant_id,
                thread=inserted_thread,
                email_account_id=email_account_id,
            )
            if has_new_message:
                results.append(email_dto)

        return results

    async def sync_messages_and_attachments(
        self,
        organization_id: UUID,
        grant_id: str,
        thread: Thread,
        email_account_id: UUID,
    ) -> tuple[EmailDto, bool]:
        existing_email_dto = await self.get_email_dto_with_thread(
            db_thread=thread,
            organization_id=organization_id,
        )

        # Skip those messages already in db
        latest_message_received_date = existing_email_dto.latest_message_received_date
        if (
            latest_message_received_date
            and thread.latest_message_received_date
            and thread.latest_message_received_date <= latest_message_received_date
        ):
            logger.bind(thread_id=thread.id).info(
                "[sync_messages_and_attachments] skip syncing messages and attachments since it's up to date."
            )
            return existing_email_dto, False

        # Get messages from Nylas
        nylas_message_list = await self.async_nylas_client.list_messages(
            grant_id=grant_id,
            nylas_list_message_query=NylasListMessageQuery(
                received_after=int(
                    existing_email_dto.latest_message_received_date.timestamp()
                )
                if existing_email_dto.latest_message_received_date
                else None,
                thread_id=not_none(thread.provider_id),
                fields="include_headers",
            ),
        )

        message_dtos: list[MessageDto] = []
        for nylas_message in nylas_message_list.data:
            # Nylas message filter with latest_message_received_date is not accurate,
            # For example, when specifying latest_message_received_date to **********,
            # it can also return message of **********. Even if you add 3s, the message
            # with timestamp ********** can still be returned.
            # So, here we do some optimization here to filter out those already exists in db
            if nylas_message.id in existing_email_dto.provider_id_list:
                continue

            # Get plaintext content from Google
            plaintext = None
            try:
                plaintext = await self._get_message_plaintext(
                    provider_message_id=nylas_message.id,
                    email_account_id=email_account_id,
                    organization_id=organization_id,
                )
            except Exception as e:
                logger.bind(
                    nylas_message_id=nylas_message.id,
                    email_account_id=email_account_id,
                ).error("Failed to get message plaintext", exc_info=e)
            # hydrate email participants
            email_person_map = await self.email_participant_service.find_contact_or_email_account_by_emails(
                organization_id=organization_id,
                emails=nylas_message.all_email_addresses,
            )

            message_id = uuid.uuid4()
            attachment_dtos: list[AttachmentDto] = []
            if nylas_message.attachments:
                attachment_dtos = (
                    await self.email_attachment_service.upload_nylas_attachments(
                        organization_id=organization_id,
                        email_account_id=thread.email_account_id,
                        message_id=message_id,
                        nylas_message_id=nylas_message.id,
                        grant_id=grant_id,
                        nylas_attachments=nylas_message.attachments,
                    )
                )
            attachment_ids = [
                attachment.attachment.id for attachment in attachment_dtos
            ]

            contact_email_pairs: list[tuple[UUID | None, str | None]] = []
            for from_ in nylas_message.from_:
                person = email_person_map.get(from_.email)
                if isinstance(person, Contact | ContactV2):
                    contact_email_pairs.append((person.id, from_.email))
            contact_account_map = await self.contact_resolve_service.resolve_relevant_account_by_contact_and_email_pairs(
                organization_id=organization_id,
                contact_email_pairs=contact_email_pairs,
            )
            message_to_insert = Message.from_nylas_message(
                email_account_id=thread.email_account_id,
                organization_id=organization_id,
                nylas_message=nylas_message,
                email_person_map=email_person_map,
                body_text=plaintext,  # plaintext is already sanitized in _get_message_plaintext
                attachment_ids=attachment_ids,
                contact_account_map=contact_account_map,
            ).model_copy(
                update={
                    "id": message_id,
                    "thread_id": thread.id,
                }
            )
            # Add to total attachment list
            # Message created location
            message = await self.thread_repository.upsert_message(message_to_insert)
            await parse_main_body_text_and_persist(
                message=message,
            )
            await classify_message_and_update_metadata(
                message=message,
            )
            message_dtos.append(
                MessageDto(
                    message=message,
                    attachments=attachment_dtos,
                )
            )

        # Early return if there are no message dtos
        if not message_dtos:
            return existing_email_dto, False

        email_dto = EmailDto(thread=thread, message_dtos=message_dtos)

        logger.bind(thread_id=thread.id, count=len(email_dto.message_dtos)).info(
            "[sync_messages_and_attachments] synced finished."
        )

        return EmailDto(
            thread=thread,
            message_dtos=existing_email_dto.message_dtos + email_dto.message_dtos,
        ), True

    async def _get_message_plaintext(
        self,
        provider_message_id: str,
        email_account_id: UUID,
        organization_id: UUID,
    ) -> str:
        if not (
            email_account := await self.email_account_service.get_email_account_by_id(
                email_account_id=email_account_id,
                organization_id=organization_id,
            )
        ):
            logger.bind(email_account_id=email_account_id).error(
                "[_get_message_plaintext] Email account not found"
            )
            return ""

        google_access_token = (
            await self.user_integration_service.get_or_refresh_google_access_token(
                organization_id=organization_id,
                user_id=email_account.owner_user_id,
                integration_type=IntegrationType.EMAIL,
            )
        )

        google_message = await self.google_client.get_message(
            message_id=provider_message_id,
            email_address=email_account.email,
            access_token=google_access_token,
        )
        if google_message.payload.body and google_message.payload.body.data:
            content = base64.urlsafe_b64decode(
                google_message.payload.body.data
            ).decode()
            return sanitize_content(content) or ""

        # makes sure that if we get a simple message, it's going to return plaintext straight from data.
        for part in google_message.payload.parts or []:
            if part.mime_type == "text/plain":
                content = base64.urlsafe_b64decode(part.body.data or "").decode()
                return sanitize_content(content) or ""
            # if the part is a multipart, we need to check the parts of the part
            if part.parts:
                for sub_part in part.parts:
                    if sub_part.mime_type == "text/plain":
                        content = base64.urlsafe_b64decode(
                            sub_part.body.data or ""
                        ).decode()
                        return sanitize_content(content) or ""
        return ""

    async def list_email_dtos_by_thread_ids(
        self,
        thread_ids: list[UUID],
        organization_id: UUID,
        include_attachment: bool = False,
    ) -> list[EmailDto]:
        if not thread_ids:
            return []
        results: list[EmailDto] = []
        db_thread_list = await self.thread_repository.list_by_ids(
            thread_ids=thread_ids,
            organization_id=organization_id,
        )
        for db_thread in db_thread_list:
            email_dto = await self.get_email_dto_with_thread(
                db_thread=db_thread,
                organization_id=organization_id,
                include_attachments=include_attachment,
            )
            results.append(email_dto)

        return results

    async def get_email_dto_with_thread(
        self,
        db_thread: Thread,
        organization_id: UUID,
        include_attachments: bool = False,
    ) -> EmailDto:
        db_messages = await self.message_repository.list_by_thread_id(
            thread_id=db_thread.id, organization_id=organization_id
        )

        db_attachments = []
        if include_attachments:
            existing_attachment_ids = list(
                chain(
                    *[
                        db_message.attachment_ids
                        for db_message in db_messages
                        if db_message.attachment_ids
                    ]
                )
            )
            db_attachments = await self.email_attachment_service.find_attachment_by_ids(
                organization_id=organization_id,
                attachment_ids=existing_attachment_ids,
            )

        message_dtos = [
            MessageDto(
                message=db_message,
                attachments=[
                    db_attachment
                    for db_attachment in db_attachments
                    if db_message.attachment_ids
                    and db_attachment.attachment.id in db_message.attachment_ids
                ],
            )
            for db_message in db_messages
        ]
        return EmailDto(thread=db_thread, message_dtos=message_dtos)

    @staticmethod
    def _has_valid_contact_participant(
        nylas_thread: NylasThread,
        email_person_map: dict[EmailStr, EmailAccount | ContactV2 | None],
    ) -> bool:
        if not nylas_thread.participants:
            return False
        for participant in nylas_thread.participants:
            if isinstance(email_person_map.get(participant.email), ContactV2):
                return True
        return False


class SingletonEmailSyncService(Singleton, EmailSyncService):
    pass


def get_email_sync_service(request: Request) -> EmailSyncService:
    db_engine = get_db_engine(request=request)
    return SingletonEmailSyncService(
        google_client=GoogleClient(encryption_manager=fernet_encryption_manager),
        async_nylas_client=AsyncNylasClient(),
        message_repository=MessageRepository(engine=db_engine),
        thread_repository=ThreadRepository(engine=db_engine),
        file_service=file_service_from_engine(engine=db_engine),
        email_attachment_service=get_email_attachment_service_by_db_engine(
            db_engine=db_engine
        ),
        email_participant_service=get_email_participant_service(db_engine),
        email_account_service=get_email_account_service_ext_by_db_engine(
            engine=db_engine
        ),
        user_service=get_user_service_general(db_engine=db_engine),
        contact_service=get_contact_service(db_engine=db_engine),
        select_list_service=get_select_list_service(engine=db_engine),
        encryption_manager=fernet_encryption_manager,
        user_integration_service=get_user_integration_service_by_db_engine(
            db_engine=db_engine
        ),
        organization_preference_service=organization_preference_service_from_engine(
            db_engine=db_engine
        ),
    )


def get_email_sync_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> EmailSyncService:
    return SingletonEmailSyncService(
        user_integration_service=get_user_integration_service_by_db_engine(
            db_engine=db_engine
        ),
        google_client=GoogleClient(encryption_manager=fernet_encryption_manager),
        async_nylas_client=AsyncNylasClient(),
        message_repository=MessageRepository(engine=db_engine),
        thread_repository=ThreadRepository(engine=db_engine),
        file_service=file_service_from_engine(engine=db_engine),
        email_attachment_service=get_email_attachment_service_by_db_engine(
            db_engine=db_engine
        ),
        email_participant_service=get_email_participant_service(db_engine),
        email_account_service=get_email_account_service_ext_by_db_engine(
            engine=db_engine
        ),
        user_service=get_user_service_general(db_engine=db_engine),
        contact_service=get_contact_service(db_engine=db_engine),
        contact_resolve_service=get_contact_resolve_service(db_engine=db_engine),
        select_list_service=get_select_list_service(engine=db_engine),
        encryption_manager=fernet_encryption_manager,
        organization_preference_service=organization_preference_service_from_engine(
            db_engine=db_engine
        ),
    )
