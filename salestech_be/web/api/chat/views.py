from typing import Annotated
from uuid import UUID

from fastapi import Depends, Path
from starlette.responses import StreamingResponse

from salestech_be.common.ai_stream import create_streaming_response
from salestech_be.common.query_util.legacy.sort_schema import OrderEnum
from salestech_be.common.query_util.sort_schema import Sorter, SortingSpec
from salestech_be.common.schema_manager.std_object_field_identifier import (
    ChatMessageField,
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.schema import QualifiedField
from salestech_be.common.type.patch_request import specified
from salestech_be.core.chat.service.chat_history_query_service import (
    ChatHistoryQueryService,
)
from salestech_be.core.chat.service.chat_service import ChatService
from salestech_be.core.chat.types import Chat, ChatMessage
from salestech_be.core.data.types import StandardRecord
from salestech_be.core.data.util import (
    ObjectRecordFetchConditions,
)
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import require_read_placeholder_access
from salestech_be.web.api.chat.schema import (
    ChatRequest,
    PatchChatRequest,
)
from salestech_be.web.api.common.container import (
    ListEntityRequestV2,
    PaginatedListResponse,
)
from salestech_be.web.api.filter.logical_filter import paginate_entities
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.lifespan_service import (
    chat_history_query_service_from_lifespan,
    chat_service_from_lifespan,
)
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserId,
)

router = ReeAPIRouter()
logger = get_logger(__name__)


@router.post("", dependencies=[Depends(require_read_placeholder_access)])
async def chat_stream(
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    request: ChatRequest,
    chat_service: Annotated[ChatService, Depends(chat_service_from_lifespan)],
) -> StreamingResponse:
    """
    Process a chat request and stream the response.
    """
    return create_streaming_response(
        chat_service.process_user_message(
            user_id=user_id,
            organization_id=organization_id,
            chat_request=request,
        )
    )


@router.post("/_list", dependencies=[Depends(require_read_placeholder_access)])
async def list_chats(
    request: ListEntityRequestV2,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    chat_history_query_service: Annotated[
        ChatHistoryQueryService, Depends(chat_history_query_service_from_lifespan)
    ],
) -> PaginatedListResponse[StandardRecord[Chat]]:
    fetch_conditions = ObjectRecordFetchConditions(
        fields=request.ordered_object_fields,
        filter_spec=request.filter_spec,
        sorting_spec=request.sorting_spec,
    )

    chats = await chat_history_query_service.list_chats(
        user_id=user_id,
        organization_id=organization_id,
        fetch_conditions=fetch_conditions,
    )

    paginated_chats, response_cursor = paginate_entities(
        chats,
        cursor=request.cursor,
    )

    return PaginatedListResponse(list_data=paginated_chats, cursor=response_cursor)


@router.patch("/{chat_id}", dependencies=[Depends(require_read_placeholder_access)])
async def update_chat(
    request: PatchChatRequest,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    chat_id: Annotated[UUID, Path()],
    chat_service: Annotated[ChatService, Depends(chat_service_from_lifespan)],
) -> Chat:
    return await chat_service.update_chat(
        user_id=user_id,
        organization_id=organization_id,
        chat_id=chat_id,
        title=request.title if specified(request.title) else None,
    )


@router.delete("/{chat_id}", dependencies=[Depends(require_read_placeholder_access)])
async def delete_chat(
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    chat_id: Annotated[UUID, Path()],
    chat_service: Annotated[ChatService, Depends(chat_service_from_lifespan)],
) -> Chat:
    return await chat_service.delete_chat(
        user_id=user_id,
        organization_id=organization_id,
        chat_id=chat_id,
    )


@router.post(
    "/{chat_id}/messages/_list", dependencies=[Depends(require_read_placeholder_access)]
)
async def list_chat_messages(
    request: ListEntityRequestV2,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    chat_id: Annotated[UUID, Path()],
    chat_history_query_service: Annotated[
        ChatHistoryQueryService, Depends(chat_history_query_service_from_lifespan)
    ],
) -> PaginatedListResponse[StandardRecord[ChatMessage]]:
    sorting_spec = request.sorting_spec or SortingSpec(
        primary_object_identifier=StdObjectIdentifiers.chat_message.identifier,
        ordered_sorters=(
            Sorter(
                field=QualifiedField(path=(ChatMessageField.created_at,)),
                order=OrderEnum.ASC,
            ),
        ),
    )
    fetch_conditions = ObjectRecordFetchConditions(
        fields=request.ordered_object_fields,
        filter_spec=request.filter_spec,
        sorting_spec=sorting_spec,
    )

    chat_messages = await chat_history_query_service.list_chat_messages(
        organization_id=organization_id,
        chat_id=chat_id,
        user_id=user_id,
        fetch_conditions=fetch_conditions,
    )

    paginated_messages, response_cursor = paginate_entities(
        chat_messages,
        cursor=request.cursor,
    )

    return PaginatedListResponse(list_data=paginated_messages, cursor=response_cursor)
