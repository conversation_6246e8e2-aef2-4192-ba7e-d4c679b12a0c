from uuid import UUID

from fastapi import HTTPException

from salestech_be.common.exception.exception import (
    IllegalStateError,
    ResourceNotFoundError,
)
from salestech_be.common.stats.metric import api_metric
from salestech_be.common.type.patch_request import UNSET
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.contact.service_api_schema import PatchContactRequest
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.user.service.user_service import UserService
from salestech_be.db.models.account import Account as DbAccount
from salestech_be.db.models.contact import (
    CreateContactAccountRoleRequest,
    CreateContactRequest,
    CreateDbContactEmailRequest,
    CreateDbContactRequest,
)
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.db.models.user_organization_association import (
    UserOrganizationAssociationStatus,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.str import (
    validate_domain_name,
)
from salestech_be.util.validation import one_row_or_none
from salestech_be.web.api.email.common.utils import PUBLIC_EMAIL_DOMAIN
from salestech_be.web.api.webhook.schema import AccountContactCreationWebhookRequest

logger = get_logger(__name__)


class AccountContactCreationWebhookError(Exception):
    pass


class AccountContactCreationWebhookService:
    def __init__(
        self,
        account_service: AccountService,
        contact_service: ContactService,
        user_service: UserService,
    ):
        self.account_service = account_service
        self.contact_service = contact_service
        self.user_service = user_service

    def _get_unique_identifier(
        self, request: AccountContactCreationWebhookRequest, domain_name: str
    ) -> str:
        is_public_domain = domain_name in PUBLIC_EMAIL_DOMAIN
        company_name = (
            request.email.split("@")[0].lower() if is_public_domain else domain_name
        )
        if domain_name not in PUBLIC_EMAIL_DOMAIN:
            return domain_name
        if request.account_website and (
            validated_domain_name := validate_domain_name(request.account_website)
        ):
            return validated_domain_name
        return company_name.replace(".", "-")

    async def _find_or_create_account(
        self, request: AccountContactCreationWebhookRequest, org_id: UUID, user_id: UUID
    ) -> AccountV2:
        domain_name = request.email.split("@")[1].lower()
        unique_identifier = self._get_unique_identifier(request, domain_name)
        existing_account = one_row_or_none(
            await self.account_service.account_repository._find_by_column_values(  # noqa: SLF001
                DbAccount,
                organization_id=org_id,
                domain_name=unique_identifier,
                exclude_deleted_or_archived=False,
            )
        )

        if existing_account:
            if existing_account.archived_at:
                await self.account_service.reactivate_by_id(
                    user_id=user_id,
                    organization_id=org_id,
                    account_id=existing_account.id,
                )
                logger.bind(account_id=existing_account.id).info(
                    "[AccountContactCreationWebhookService] Reactivated archived account."
                )
            return await self.account_service.get_account_v2(
                account_id=existing_account.id, organization_id=org_id
            )

        owner_user_id = await self._get_owner_user_id(
            org_id, request.account_owner_email, user_id
        )
        self.increment_account_contact_creation_webhook_metric(
            metric_name="account_created", org_id=org_id
        )
        return await self.account_service.create_account_v2(
            organization_id=org_id,
            user_id=user_id,
            create_account_request=CreateAccountRequest(
                display_name=request.company or "UNKNOWN",
                owner_user_id=owner_user_id or user_id,
                official_website=request.account_website
                or validate_domain_name(domain_name)
                if domain_name not in PUBLIC_EMAIL_DOMAIN
                else None,
                domain_name=unique_identifier,
                linkedin_url=request.account_linkedin_url,
                facebook_url=request.account_facebook_url,
                zoominfo_url=request.account_zoominfo_url,
                x_url=request.account_x_url,
                created_source=CreatedSource.ACCOUNT_CONTACT_CREATION_WEBHOOK,
            ),
        )

    async def _get_owner_user_id(
        self, org_id: UUID, user_email: str | None, user_id: UUID
    ) -> UUID | None:
        if not user_email:
            return None
        try:
            org_user = await self.user_service.get_user_by_email_v2(
                email=user_email, organization_id=org_id
            )
            if (
                org_user.organization_association_status
                == UserOrganizationAssociationStatus.ACTIVE
            ):
                return org_user.id
            else:
                logger.bind(organization_id=org_id, user_email=user_email).info(
                    f"[AccountContactCreationWebhookService] User with email {user_email} is not active in organization, going to use the zap creator as owner"
                )
        except ResourceNotFoundError:
            logger.bind(organization_id=org_id, user_email=user_email).info(
                f"[AccountContactCreationWebhookService] Could not find user for organization with email {user_email}, going to use the zap creator as owner"
            )
        except HTTPException:
            logger.bind(organization_id=org_id, user_email=user_email).info(
                f"[AccountContactCreationWebhookService] Could not find user for organization with email {user_email}, going to use the zap creator as owner"
            )
        except IllegalStateError:
            logger.bind(organization_id=org_id, user_email=user_email).info(
                f"[AccountContactCreationWebhookService] Found multiple users for organization with email {user_email}, going to use the zap creator as owner"
            )
        except Exception as e:
            logger.bind(organization_id=org_id, user_email=user_email).info(
                f"[AccountContactCreationWebhookService] Unknown error when getting owner user id for organization with email {user_email}, {e}, going to use the zap creator as owner"
            )

        return None

    async def handle_account_contact_creation_webhook(
        self, request: AccountContactCreationWebhookRequest, org_id: UUID, user_id: UUID
    ) -> ContactV2:
        """
        Handle the Account and Contact creation webhook request
        """
        email_address = request.email.lower()
        logger.bind(organization_id=org_id, user_id=user_id).info(
            f"[AccountContactCreationWebhookService] Handling Account and Contact creation webhook request for user {email_address}"
        )
        owner_user_id = await self._get_owner_user_id(
            org_id, request.contact_owner_email, user_id
        )
        stage_id = await self.contact_service.get_default_stage_id_for_contact(org_id)
        display_name = request.first_name
        if display_name and request.last_name:
            display_name += " " + request.last_name
        elif request.last_name:
            display_name = request.last_name
        else:
            display_name = email_address.split("@")[0]

        existing_contact_list = await self.contact_service.list_by_emails(
            organization_id=org_id,
            emails=[email_address],
        )
        if existing_contact_list:
            # Email is unique per organization and contact, so we can fetch the first one
            logger.bind(organization_id=org_id, email=email_address).info(
                f"[AccountContactCreationWebhookService] Found existing contact with email {email_address}, updating it with new data"
            )
            existing_contact = existing_contact_list[0]
            # Only update the fields that are set in the request
            self.increment_account_contact_creation_webhook_metric(
                metric_name="contact_updated", org_id=org_id
            )
            return await self.contact_service.patch_by_id(
                organization_id=org_id,
                user_id=user_id,
                contact_id=existing_contact.id,
                request=PatchContactRequest(
                    display_name=display_name if request.first_name else UNSET,
                    owner_user_id=owner_user_id if owner_user_id else UNSET,
                    first_name=request.first_name if request.first_name else UNSET,
                    last_name=request.last_name if request.last_name else UNSET,
                    middle_name=request.middle_name if request.middle_name else UNSET,
                    primary_phone_number=request.phone_number
                    if request.phone_number
                    else UNSET,
                    title=request.title if request.title else UNSET,
                    department=request.department if request.department else UNSET,
                    linkedin_url=request.contact_linkedin_url
                    if request.contact_linkedin_url
                    else UNSET,
                    facebook_url=request.contact_facebook_url
                    if request.contact_facebook_url
                    else UNSET,
                    zoominfo_url=request.contact_zoominfo_url
                    if request.contact_zoominfo_url
                    else UNSET,
                ),
            )

        account = await self._find_or_create_account(request, org_id, user_id)

        contact_request = CreateContactRequest(
            contact=CreateDbContactRequest(
                display_name=display_name,
                created_by_user_id=user_id,
                owner_user_id=owner_user_id or user_id,
                first_name=request.first_name,
                last_name=request.last_name,
                middle_name=request.middle_name,
                primary_phone_number=request.phone_number,
                stage_id=stage_id,
                title=request.title,
                department=request.department,
                linkedin_url=request.contact_linkedin_url,
                facebook_url=request.contact_facebook_url,
                zoominfo_url=request.contact_zoominfo_url,
                x_url=request.contact_x_url,
                created_source=CreatedSource.ACCOUNT_CONTACT_CREATION_WEBHOOK,
            ),
            contact_emails=[
                CreateDbContactEmailRequest(
                    email=email_address,
                    is_contact_primary=True,
                )
            ],
            contact_account_roles=[
                CreateContactAccountRoleRequest(
                    account_id=account.id,
                    is_primary_account=True,
                )
            ],
        )
        self.increment_account_contact_creation_webhook_metric(
            metric_name="contact_created", org_id=org_id
        )
        logger.bind(organization_id=org_id, email=email_address).info(
            f"[AccountContactCreationWebhookService] Creating new contact with email {email_address}"
        )
        return await self.contact_service.create_contact_with_contact_channels(
            user_id=user_id,
            organization_id=org_id,
            create_contact_with_contact_channel_request=contact_request,
        )

    @staticmethod
    def increment_account_contact_creation_webhook_metric(
        metric_name: str, org_id: UUID | None = None
    ) -> None:
        tags = []
        if org_id:
            tags.append(f"organization_id:{org_id}")
        api_metric.increment(
            metric_name=f"account_contact_creation_webhook_{metric_name}", tags=tags
        )
