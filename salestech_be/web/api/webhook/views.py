import json
import uuid
from typing import Annotated, Any
from urllib.parse import parse_qs
from uuid import UUID

from fastapi import Depends, Form, Header, HTTPException, Query, Request, status
from starlette.responses import PlainTextResponse, Response

from salestech_be.common.exception import UnauthorizedError
from salestech_be.core.account.service.account_service import get_account_service
from salestech_be.core.calendar.user_calendar_webhook_service import (
    UserCalendarWebhookService,
)
from salestech_be.core.common.api_key_service import (
    ApiKeyService,
    api_key_service_factory,
)
from salestech_be.core.contact.service.contact_service import get_contact_service
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.meeting.meeting_webhook_service import (
    MeetingWebhookService,
    meeting_webhook_service_api_factory,
)
from salestech_be.core.transcript.constants import TRANSCRIPT_WEBHOOK_PATH
from salestech_be.core.transcript.transcript_service import (
    TranscriptService,
    transcript_service_factory,
)
from salestech_be.core.user.service.user_service import get_user_service_general
from salestech_be.core.voice.service_type import (
    RecipientEntityType,
    TwilioCallEvent,
    TwilioRecordingStatus,
)
from salestech_be.core.voice.voice_service import VoiceService, voice_service_factory
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.api_key import ApiKey, ApiKeyScope
from salestech_be.db.models.meeting import MeetingReferenceIdType
from salestech_be.db.models.transcript import TranscriptReferenceIdType
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import MEETING_TASK_QUEUE
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.workflows.meeting.meeting_completion import (
    MeetingCompletionWorkflow,
    MeetingCompletionWorkflowData,
)
from salestech_be.util.pydantic_types.str import PhoneNumber, TwilioSID
from salestech_be.util.pydantic_types.time import RFC2822Timestamp
from salestech_be.web.api.connect.service import (
    UserIntegrationService,
)
from salestech_be.web.api.meeting.schema import RecallBotEvent
from salestech_be.web.api.webhook.schema import (
    AccountContactCreationWebhookRequest,
    AssemblyAITranscriptWebhookRequest,
    BrightDataScraperNotifyWebhookRequest,
    NylasWebhookEventData,
    NylasWebhookRequest,
    NylasWebhookType,
    TwilioAnsweredBy,
    TwilioCallStatus,
    TwilioLogLevel,
    TwilioVerificationStatus,
    ZoomWebhookEvent,
)
from salestech_be.web.api.webhook.service.account_contact_creation_webhook_service import (
    AccountContactCreationWebhookService,
)
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.lifespan_service import (
    get_lifespan_service,
    user_integration_service_from_lifespan,
)

router = ReeAPIRouter()
deprecated_router = ReeAPIRouter()
logger = get_logger("webhook.view")


@deprecated_router.post(
    "/twilio/voice", user_organization_tenanted=False, deprecated=True
)
async def handle_twilio_voice(
    AccountSid: Annotated[TwilioSID, Form()],  # noqa: N803
    CallSid: Annotated[TwilioSID, Form()],  # noqa: N803
    From: Annotated[str, Form(min_length=43, max_length=43)],  # noqa: N803
    To: Annotated[PhoneNumber, Form()],  # noqa: N803
    voice_service: Annotated[VoiceService, Depends(voice_service_factory)],
    from_number: Annotated[PhoneNumber, Form()],
    recipient_entity_id: Annotated[UUID, Form()],
    recipient_entity_type: Annotated[RecipientEntityType, Form()],
    record: Annotated[bool, Form()] = False,
) -> Response:
    response = await voice_service.build_twilio_voice_response(
        account_sid=AccountSid,
        call_sid=CallSid,
        caller=From,
        from_number=from_number,
        recipient_number=To,
        recipient_entity_id=recipient_entity_id,
        recipient_entity_type=recipient_entity_type,
        record=record,
    )
    return Response(content=str(response), media_type="text/xml")


@deprecated_router.post(
    "/twilio/log", user_organization_tenanted=False, deprecated=True
)
async def handle_twilio_log(
    AccountSid: Annotated[TwilioSID, Form()],  # noqa: N803
    Sid: Annotated[TwilioSID, Form()],  # noqa: N803
    Level: Annotated[TwilioLogLevel, Form()],  # noqa: N803
    Payload: Annotated[str, Form()],  # noqa: N803
) -> None:
    with logger.contextualize(
        account_sid=AccountSid, sid=Sid, payload=json.loads(Payload)
    ):
        match Level:
            case TwilioLogLevel.WARNING:
                logger.warning("Twilio log")
            case TwilioLogLevel.ERROR:
                logger.error("Twilio log")


@deprecated_router.post(
    VoiceService.AMD_PATH, user_organization_tenanted=False, deprecated=True
)
async def handle_twilio_answering_machine_detection(
    voice_service: Annotated[VoiceService, Depends(voice_service_factory)],
    CallSid: Annotated[TwilioSID, Form()],  # noqa: N803
    AccountSid: Annotated[TwilioSID, Form()],  # noqa: N803
    AnsweredBy: Annotated[TwilioAnsweredBy, Form()],  # noqa: N803
) -> None:
    if settings.enable_answering_machine_detection:
        await voice_service.answering_machine(
            call_sid=CallSid,
            account_sid=AccountSid,
            answered_by=AnsweredBy,
        )


@deprecated_router.post(
    VoiceService.CALL_STATUS_PATH, user_organization_tenanted=False, deprecated=True
)
async def handle_twilio_call_status(
    voice_service: Annotated[VoiceService, Depends(voice_service_factory)],
    CallSid: Annotated[TwilioSID, Form()],  # noqa: N803
    ParentCallSid: Annotated[TwilioSID, Form()],  # noqa: N803
    CallStatus: Annotated[TwilioCallStatus, Form()],  # noqa: N803
    SequenceNumber: Annotated[int, Form()],  # noqa: N803
    Timestamp: Annotated[RFC2822Timestamp, Form()],  # noqa: N803
    CallDuration: Annotated[int | None, Form()] = None,  # noqa: N803
) -> None:
    await voice_service.handle_twilio_call_event(
        TwilioCallEvent(
            call_sid=CallSid,
            parent_call_sid=ParentCallSid,
            call_status=CallStatus,
            timestamp=Timestamp,
            sequence_number=SequenceNumber,
            call_duration_seconds=CallDuration,
        )
    )


@deprecated_router.post(
    "/twilio/onboarding-verification-callback",
    user_organization_tenanted=False,
    deprecated=True,
)
async def handle_twilio_onboarding_verification_callback(
    voice_service: Annotated[VoiceService, Depends(voice_service_factory)],
    Called: Annotated[PhoneNumber, Form()],  # noqa: N803
    VerificationStatus: Annotated[TwilioVerificationStatus, Form()],  # noqa: N803
    AccountSid: Annotated[TwilioSID, Form()],  # noqa: N803
    OutgoingCallerIdSid: Annotated[TwilioSID | None, Form()] = None,  # noqa: N803
) -> None:
    await voice_service.handle_twilio_onboarding_verification_callback(
        phone_number=Called,
        verification_status=VerificationStatus,
        account_sid=AccountSid,
        outgoing_caller_id_sid=OutgoingCallerIdSid,
    )


@deprecated_router.post(
    VoiceService.RECORDING_STATUS_PATH,
    user_organization_tenanted=False,
    deprecated=True,
)
async def handle_twilio_recording_status_callback(
    voice_service: Annotated[VoiceService, Depends(voice_service_factory)],
    AccountSid: Annotated[TwilioSID, Form()],  # noqa: N803
    CallSid: Annotated[TwilioSID, Form()],  # noqa: N803
    RecordingSid: Annotated[TwilioSID, Form()],  # noqa: N803
    RecordingStatus: Annotated[TwilioRecordingStatus, Form()],  # noqa: N803
) -> None:
    await voice_service.handle_twilio_recording_status(
        account_sid=AccountSid,
        call_sid=CallSid,
        recording_sid=RecordingSid,
        recording_status=RecordingStatus,
    )


@router.post(TRANSCRIPT_WEBHOOK_PATH, user_organization_tenanted=False)
async def handle_assembly_ai_transcript_webhook(
    transcript_service: Annotated[
        TranscriptService, Depends(transcript_service_factory)
    ],
    request: AssemblyAITranscriptWebhookRequest,
) -> None:
    transcript = await transcript_service.handle_transcript_webhook(request)

    client = await get_temporal_client()
    await client.start_workflow(
        MeetingCompletionWorkflow.run,
        args=[
            MeetingCompletionWorkflowData(
                meeting_reference_id=transcript.reference_id,
                meeting_reference_id_type=MeetingReferenceIdType.VOICE_V2
                if transcript.reference_id_type == TranscriptReferenceIdType.VOICE_V2
                else MeetingReferenceIdType.EXTERNAL_RECORDING,
                organization_id=transcript.organization_id,
            ),
        ],
        id=f"meeting_completion_workflow_{transcript.reference_id}",
        task_queue=MEETING_TASK_QUEUE,
    )


@router.get("/nylas/v3/grant", user_organization_tenanted=False)
async def handle_nylas_grant_verification(
    challenge: str,
    request: Request,
) -> PlainTextResponse:
    logger.info(f"received challenge: {challenge}, {request.query_params}")
    return PlainTextResponse(challenge)


@router.post("/nylas/v3/grant", user_organization_tenanted=False)
async def handle_nylas_grant_webhook(  # type: ignore[explicit-any] # TODO: fix-any-annotation
    request: Request,
    event: dict[str, Any],
) -> None:
    webhook_request = NylasWebhookRequest.model_validate(event)
    event_type = webhook_request.type
    logger.bind(event_type=event_type).info(
        f"received nylas webhook grant.{event_type}: {webhook_request.model_dump_json()}"
    )

    nylas_webhook_service = get_lifespan_service(request).nylas_webhook_service

    if event_type in [
        NylasWebhookType.GRANT_CREATED,
        NylasWebhookType.GRANT_UPDATED,
        NylasWebhookType.GRANT_DELETED,
        NylasWebhookType.GRANT_EXPIRED,
    ]:
        return await nylas_webhook_service.handle_nylas_event(payload=webhook_request)

    raise HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail=f"Unsupported event type: {event_type}",
    )


@router.get("/nylas/v3/calendar", user_organization_tenanted=False)
async def handle_nylas_calendar_verification(
    challenge: str,
    request: Request,
) -> PlainTextResponse:
    logger.info(f"received challenge: {challenge}, {request.query_params}")
    return PlainTextResponse(challenge)


@router.post("/nylas/v3/calendar", user_organization_tenanted=False)
async def handle_nylas_calendar_webhook(  # type: ignore[explicit-any] # TODO: fix-any-annotation
    request: Request,
    event: dict[str, Any],
) -> None:
    webhook_request = NylasWebhookRequest.model_validate(event)
    event_type = webhook_request.type
    logger.bind(webhook_request_id=webhook_request.id, event_type=event_type).info(
        f"received nylas webhook calendar.{event_type}: {webhook_request.model_dump_json()}"
    )
    if event_type in [
        NylasWebhookType.EVENT_CREATED,
        NylasWebhookType.EVENT_UPDATED,
        NylasWebhookType.EVENT_DELETED,
    ]:
        user_calendar_webhook_service = get_lifespan_service(
            request
        ).user_calendar_webhook_service

        if settings.enable_nylas_user_calendar_webhook_handle_in_temporal:
            return await user_calendar_webhook_service.handle_nylas_event(
                webhook_request=webhook_request
            )
        else:
            return await handle_nylas_event(
                user_calendar_webhook_service=user_calendar_webhook_service,
                webhook_request=webhook_request,
            )

    raise HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail=f"Unsupported event type: {event_type}",
    )


@router.post("/nylas/v3", user_organization_tenanted=False)
async def handle_nylas_webhook(  # type: ignore[explicit-any] # TODO: fix-any-annotation
    request: Request,
    event: dict[str, Any],
) -> None:
    lifespan_service = get_lifespan_service(request)

    email_webhook_service = lifespan_service.email_webhook_service
    nylas_webhook_service = lifespan_service.nylas_webhook_service

    webhook_request = NylasWebhookRequest.model_validate(event)
    event_type = webhook_request.type
    logger.bind(event_type=event_type, request=webhook_request).info(
        f"received nylas webhook email.{event_type}"
    )
    # We don't need to handle message updated events
    if event_type == NylasWebhookType.MESSAGE_UPDATED:
        return None

    if event_type in [
        NylasWebhookType.MESSAGE_OPENED,
        NylasWebhookType.MESSAGE_LINK_CLICKED,
        NylasWebhookType.THREAD_REPLIED,
        NylasWebhookType.MESSAGE_BOUNCE_DETECTED,
    ]:
        return await email_webhook_service.handle_nylas_tracking_event_workflow(
            nylas_webhook_type=NylasWebhookType(event_type),
            webhook_request=webhook_request,
        )

    if event_type in [
        NylasWebhookType.MESSAGE_CREATED,
        NylasWebhookType.MESSAGE_CREATED_TRUNCATED,
    ]:
        return await email_webhook_service.handle_nylas_message_created_event(
            data=webhook_request.data
        )

    if event_type in [
        NylasWebhookType.GRANT_CREATED,
        NylasWebhookType.GRANT_UPDATED,
        NylasWebhookType.GRANT_DELETED,
        NylasWebhookType.GRANT_EXPIRED,
    ]:
        return await nylas_webhook_service.handle_nylas_event(payload=webhook_request)

    logger.warning(f"unknown type webhook: {json.dumps(event)}")
    return None


async def handle_nylas_event(
    *,
    user_calendar_webhook_service: UserCalendarWebhookService,
    webhook_request: NylasWebhookRequest,
) -> None:
    obj = NylasWebhookEventData.model_validate(webhook_request.data).object
    connection_external_id: str = obj.grant_id
    calendar_external_id: str = obj.calendar_id
    event_external_id: str = obj.id

    with logger.contextualize(
        connection_external_id=connection_external_id,
        calendar_external_id=calendar_external_id,
        event_external_id=event_external_id,
    ):
        if (
            webhook_request.type == NylasWebhookType.EVENT_CREATED
            and obj.metadata is not None
            and obj.metadata["source"] == "scheduler"
        ):
            logger.info(
                "Skip syncing scheduler created event to avoid conflicts",
            )
            return

        if webhook_request.type == NylasWebhookType.EVENT_DELETED:
            deleted = (
                await user_calendar_webhook_service.delete_calendar_event_from_external(
                    event_external_id=event_external_id,
                    calendar_external_id=calendar_external_id,
                    connection_external_id=connection_external_id,
                )
            )
            logger.info(f"deleted calendar events from external: {deleted}")
            return

        # Only process non-recurrence events in webhook
        if not obj.recurrence:
            updated_events = (
                await user_calendar_webhook_service.upsert_calendar_event_from_external(
                    event_external_id=event_external_id,
                    calendar_external_id=calendar_external_id,
                    connection_external_id=connection_external_id,
                )
            )
            logger.info(
                "updated calendar events from external.", updated_events=updated_events
            )


@router.get("/nylas/v3", user_organization_tenanted=False)
async def handle_nylas_verification(
    challenge: str,
    request: Request,
) -> PlainTextResponse:
    logger.info(f"received challenge: {challenge}, {request.query_params}")
    return PlainTextResponse(challenge)


@router.post("/recallai/bot/event", user_organization_tenanted=False)
async def handle_recallai_event(
    request: Request,
    event: RecallBotEvent,
    meeting_webhook_service: Annotated[
        MeetingWebhookService, Depends(meeting_webhook_service_api_factory)
    ],
) -> None:
    logger.info(f"received recallai event: {event}")
    logger.debug(
        f"received recallai event: {event.model_dump_json()} and "
        f"headers: {request.headers}",
    )

    if not await meeting_webhook_service.is_signature_valid(event, request):
        logger.bind(request=request).warning("Invalid recall event")
        raise UnauthorizedError("Unauthorized recall event")

    # For some webhooks, like realtime transcript, recall may specify query parameters
    # (even though this is a post).  Pass along to service layer to make use of.
    query_params: dict[str, list[str]] = {}
    if request.url.query:
        try:
            query_params = parse_qs(request.url.query)
        except Exception:
            logger.info(
                f"Unable to extract parameters from query string {request.url.query}"
            )
    await meeting_webhook_service.handle_event(event.event, event, query_params)


@router.post("/zoom/deauth", user_organization_tenanted=False)
async def handle_zoom_deauth_event(
    request: Request,
    event: ZoomWebhookEvent,
    user_int_service: Annotated[
        UserIntegrationService,
        Depends(user_integration_service_from_lifespan),
    ],
) -> PlainTextResponse:
    logger.info(
        f"received zoom de-authorization event: {event} header: {request.headers}"
    )

    if (not event.event) or (event.event != "app_deauthorized"):
        logger.error("Wrong webhook event")

    elif (not event.payload) or ("user_id" not in event.payload):
        logger.error(
            f"can't deauthorize since zoom user_id is not valid: {event.model_dump_json()}"
        )
    else:
        await user_int_service.delete_integration_by_zoom_webhook(
            zoom_user_id=event.payload["user_id"]
        )

    # Always return 200 OK with empty content
    return PlainTextResponse(status_code=status.HTTP_200_OK, content="")


@router.post("/brightdata/scraper/notify", user_organization_tenanted=False)
async def handle_brightdata_scraper_notify(
    request: BrightDataScraperNotifyWebhookRequest,
) -> None:
    logger.info(f"received brightdata scraper notify: {request.model_dump_json()}")
    # TODO(REEVO-1435) signal scraping workflow


@router.get("/recall_zoom", user_organization_tenanted=False)
async def zoom_recall_connect_callback(
    # this is from recall webhook
    user_id: Annotated[uuid.UUID, Query()],
    organization_id: Annotated[uuid.UUID, Query()],
    recall_key: Annotated[str, Query()],
    request: Request,
) -> PlainTextResponse:
    integration_service = get_lifespan_service(request).user_integration_service

    # 1. validate this request is coming from Recall
    if recall_key != settings.recall_zoom_oauth_callback_key:
        logger.info(f"Received recall_key: {recall_key}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="This is not a Recall ai request",
        )
    # 2. get access token of the zoom user
    logger.info(f"Retrieving access token for user: {user_id}")
    access_token = await integration_service.retrieve_zoom_access_token_from_db(
        user_id=user_id,
        organization_id=organization_id,
    )
    return PlainTextResponse(access_token)


@router.post("/account_and_contact_creation", user_organization_tenanted=False)
async def handle_account_and_contact_creation(
    request: AccountContactCreationWebhookRequest,
    x_api_key: Annotated[
        str, Header(description="Account and contact creation webhook API key")
    ],
    api_key_service: Annotated[ApiKeyService, Depends(api_key_service_factory)],
) -> ContactV2:
    async def validate_with_deprecated_support(
        api_key_service: ApiKeyService,
        secret_key: str,
        current_scope: ApiKeyScope,
        deprecated_scope: ApiKeyScope,
    ) -> ApiKey:
        """Try to validate an API key against current scope, falling back to deprecated scope if needed."""
        try:
            return await api_key_service.validate_api_key(secret_key, current_scope)
        except UnauthorizedError:
            return await api_key_service.validate_api_key(secret_key, deprecated_scope)

    try:
        db_engine = DatabaseEngine(url=str(settings.db_url))

        # Try validating with current scope, falling back to deprecated if needed
        api_key = await validate_with_deprecated_support(
            api_key_service=api_key_service,
            secret_key=x_api_key,
            current_scope=ApiKeyScope.ACCOUNT_CONTACT_CREATION_WEBHOOK,
            deprecated_scope=ApiKeyScope.ZAPIER_WEBHOOK,
        )

        AccountContactCreationWebhookService.increment_account_contact_creation_webhook_metric(
            metric_name="request_made", org_id=api_key.organization_id
        )
        contact_service = get_contact_service(db_engine=db_engine)
        account_service = get_account_service(db_engine=db_engine)
        user_service = get_user_service_general(db_engine=db_engine)
        account_contact_creation_webhook_service = AccountContactCreationWebhookService(
            contact_service=contact_service,
            account_service=account_service,
            user_service=user_service,
        )
        return await account_contact_creation_webhook_service.handle_account_contact_creation_webhook(
            request, api_key.organization_id, api_key.user_id
        )
    except UnauthorizedError:
        AccountContactCreationWebhookService.increment_account_contact_creation_webhook_metric(
            metric_name="unauthorized_user_attempt"
        )
        logger.error("Invalid API key")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid API key"
        )
    except Exception as e:
        AccountContactCreationWebhookService.increment_account_contact_creation_webhook_metric(
            metric_name="error",
            org_id=api_key.organization_id if api_key else None,
        )
        logger.bind(organization_id=api_key.organization_id if api_key else None).error(
            f"Error handling account and contact creation webhook: {e}"
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
