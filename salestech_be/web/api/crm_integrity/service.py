from collections import defaultdict
from uuid import UUID, uuid4

from fastapi import Request
from temporalio.common import WorkflowIDReusePolicy
from temporalio.exceptions import WorkflowAlreadyStartedError

from salestech_be.common.exception.exception import (
    ErrorCode,
    ErrorDetails,
    InvalidArgumentError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.type.patch_request import specified
from salestech_be.core.account.service.account_service import get_account_service
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.service.contact_service import get_contact_service
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.crm_integrity.data_operation_tracker.integrity_job_service import (
    IntegrityJobService,
)
from salestech_be.core.crm_integrity.integrity_operation_preview.integrity_job_preview import (
    IntegrityJobPreviewService,
)
from salestech_be.core.crm_integrity.integrity_operation_validation.integrity_job_validation import (
    IntegrityJobValidationService,
)
from salestech_be.core.crm_integrity.integrity_operation_workflows.integrity_job_dispatch_workflow import (
    IntegrityJobDispatchWorkflow,
    get_integrity_job_dispatch_workflow_id,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    BaseIntegrityJobParam,
    UpdateIntegrityJobParam,
)
from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
    get_affected_sub_domains_by_integrity_job_name,
    get_default_user_option_by_integrity_job_name,
    get_user_manual_update_sub_domains_by_integrity_job_name,
    get_user_option_names_by_integrity_job_name,
    get_user_options_by_integrity_job_name,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    CreateCRMIntegrityJobRequest,
    CreateCRMIntegrityJobResponse,
    IntegrityJobPreviewRequest,
    IntegrityJobPreviewResponse,
    IntegrityJobUserOptionsResponse,
    IntegrityJobValidationRequest,
    PatchCRMIntegrityJobRequest,
    StartCRMIntegrityJobResponse,
)
from salestech_be.core.metadata.converter import (
    contact_account_email_from_db,
    contact_email_lite_from_db,
)
from salestech_be.core.metadata.types import ContactEmailLite
from salestech_be.core.pipeline.service.pipeline_service import get_pipeline_service
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.user.service.user_service import (
    get_user_service_general,
)
from salestech_be.db.dao.crm_integrity_repository import (
    CRMIntegrityAssociatedEntityOperationRepository,
    CRMIntegrityJobRepository,
    CRMIntegrityOperationRepository,
    CRMIntegritySubDomainJobRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.contact import ContactEmailAccountAssociation
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityJobContextualParam,
    CRMIntegrityJobUserOption,
    CRMIntegritySubDomainJob,
    CRMSubDomain,
    EntityType,
    IntegrityJobName,
    IntegrityJobType,
    JobStatus,
    MoveContactEmailToAccountContextualParam,
    MoveContactToAccountContextualParam,
    SubDomainJobStatus,
    get_integrity_job_name,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import INTEGRITY_JOB_TASK_QUEUE
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.copy_util import strict_model_copy
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import (
    cast_or_error,
    not_none,
    one_row_only,
    one_row_or_none,
)
from salestech_be.web.api.crm_integrity.schema import (
    IntegrityAssociatedDataOperation,
    IntegrityJobDetails,
    IntegrityOperation,
    IntegritySubDomainJob,
)

logger = get_logger(__name__)


class CRMIntegrityService:
    def __init__(
        self,
        engine: DatabaseEngine,
    ):
        self._integrity_job_preview_service = IntegrityJobPreviewService(engine)
        self._integrity_job_validation_service = IntegrityJobValidationService(engine)
        self._integrity_job_service = IntegrityJobService(db_engine=engine)
        self._integrity_job_repository = CRMIntegrityJobRepository(engine=engine)
        self._integrity_operation_repository = CRMIntegrityOperationRepository(
            engine=engine
        )
        self._integrity_sub_domain_job_repository = CRMIntegritySubDomainJobRepository(
            engine=engine
        )
        self._integrity_associated_entity_operation_repository = (
            CRMIntegrityAssociatedEntityOperationRepository(engine=engine)
        )
        self._user_service = get_user_service_general(db_engine=engine)
        self._contact_service = get_contact_service(db_engine=engine)
        self._account_service = get_account_service(db_engine=engine)
        self._pipeline_service = get_pipeline_service(db_engine=engine)

    async def _validate_job_request(
        self,
        *,
        job_type: IntegrityJobType,
        src_entity_type: EntityType,
        dest_entity_type: EntityType | None,
        src_entity_id: UUID,
        dest_entity_id: UUID | None,
        contextual_param: CRMIntegrityJobContextualParam | None = None,
        organization_id: UUID,
    ) -> None:
        validations_results = (
            await self._integrity_job_validation_service.validate_crm_integrity_job(
                integrity_job_validation_request=IntegrityJobValidationRequest(
                    job_type=job_type,
                    src_entity_type=src_entity_type,
                    dest_entity_type=dest_entity_type,
                    src_entity_id=src_entity_id,
                    dest_entity_id=dest_entity_id,
                    contextual_param=contextual_param,
                ),
                organization_id=organization_id,
            )
        )

        if validations_results.is_invalid:
            logger.info(
                f"validate job request failed, validations: {[v.violation.value for v in validations_results.validations]}"
            )

            raise InvalidArgumentError(
                "Job related operations validation failed",
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INTEGRITY_JOB_VALIDATION_ERROR,
                    details=validations_results.model_dump_json(),
                ),
            )

    async def _validate_user_choices_param(
        self,
        *,
        affected_sub_domains: list[CRMSubDomain],
        user_choices: dict[CRMSubDomain, CRMIntegrityJobUserOption | None] | None,
        available_user_option_names: list[CRMIntegrityJobUserOption],
    ) -> None:
        """
        Validate the user choices param.

        Note, we will allow incomplete user choices param for a given integrity job. The missing
        part will be enriched with default values later.

        But following cases are not allowed and will raise an InvalidArgumentError:
        1. Provide a user choice with null value
        2. Provide a user choice for a subdomain that is not in the affected subdomains
        list of the given integrity job.

        While following cases are allowed:
        1. User choices param is not provided for integrity job that need user options provided,
        this case, all subdomain will use the default user option.
        """

        if not user_choices:
            return

        if not available_user_option_names and user_choices:
            raise InvalidArgumentError(
                "User choices param is provided for integrity job that has no available user options"
            )

        for subdomain in user_choices:
            if subdomain not in affected_sub_domains:
                raise InvalidArgumentError(
                    f"Subdomain {subdomain} is not in the affected subdomains list of the given integrity job"
                )
            if user_choices[subdomain] is None:
                raise InvalidArgumentError(
                    f"User choice for subdomain {subdomain} cannot be None once provided"
                )
            elif user_choices[subdomain] not in available_user_option_names:
                raise InvalidArgumentError(
                    f"User choice for subdomain {subdomain} is not in the available user options: {available_user_option_names}"
                )

    async def _validate_and_enriched_user_choices_param(
        self,
        *,
        integrity_job_name: IntegrityJobName,
        user_choices: dict[CRMSubDomain, CRMIntegrityJobUserOption | None] | None,
    ) -> dict[CRMSubDomain, CRMIntegrityJobUserOption | None]:
        """
        Validate and Enrich the user choices param with default values for the missing subdomains
        that are not in the user choices param.
        """
        affected_sub_domains = get_affected_sub_domains_by_integrity_job_name(
            integrity_job_name=integrity_job_name
        )
        available_user_option_names = get_user_option_names_by_integrity_job_name(
            integrity_job_name=integrity_job_name
        )

        await self._validate_user_choices_param(
            affected_sub_domains=affected_sub_domains,
            user_choices=user_choices,
            available_user_option_names=available_user_option_names,
        )

        if available_user_option_names:
            default_user_option = get_default_user_option_by_integrity_job_name(
                integrity_job_name=integrity_job_name
            )
        else:
            default_user_option = None

        return {
            sub_domain: user_choices[sub_domain]
            if user_choices and user_choices.get(sub_domain)
            else default_user_option
            for sub_domain in affected_sub_domains
        }

    async def create_integrity_job(
        self,
        user_id: UUID,
        organization_id: UUID,
        integrity_operation_request: CreateCRMIntegrityJobRequest,
    ) -> CreateCRMIntegrityJobResponse:
        integrity_job_name = get_integrity_job_name(
            integrity_job_type=integrity_operation_request.job_type,
            src_entity_type=integrity_operation_request.src_entity_type,
            dest_entity_type=integrity_operation_request.dest_entity_type,
        )

        enriched_user_choices_with_default_values = (
            await self._validate_and_enriched_user_choices_param(
                integrity_job_name=integrity_job_name,
                user_choices=integrity_operation_request.user_choices,
            )
        )

        await self._validate_job_request(
            job_type=integrity_operation_request.job_type,
            src_entity_type=integrity_operation_request.src_entity_type,
            dest_entity_type=integrity_operation_request.dest_entity_type,
            src_entity_id=integrity_operation_request.src_entity_id,
            dest_entity_id=integrity_operation_request.dest_entity_id,
            contextual_param=integrity_operation_request.contextual_param,
            organization_id=organization_id,
        )

        (
            integrity_job,
            subdomain_jobs,
        ) = await self._integrity_job_service.create_integrity_job(
            user_id=user_id,
            organization_id=organization_id,
            job_type=integrity_operation_request.job_type,
            src_entity_type=integrity_operation_request.src_entity_type,
            src_entity_id=integrity_operation_request.src_entity_id,
            dest_entity_type=integrity_operation_request.dest_entity_type,
            dest_entity_id=integrity_operation_request.dest_entity_id,
            contextual_param=integrity_operation_request.contextual_param,
            user_choices=enriched_user_choices_with_default_values,
        )

        return CreateCRMIntegrityJobResponse(
            job_id=integrity_job.id,
            subdomain_job_ids=[subdomain_job.id for subdomain_job in subdomain_jobs],
        )

    async def start_integrity_job(
        self,
        *,
        user_id: UUID,
        organization_id: UUID,
        integrity_job_id: UUID,
        skip_job_validation: bool = False,
    ) -> StartCRMIntegrityJobResponse:
        integrity_job = await self._integrity_job_repository.get_integrity_job(
            organization_id=organization_id,
            job_id=integrity_job_id,
        )

        if integrity_job.status != JobStatus.NEW:
            raise InvalidArgumentError("Only new job can be started")

        if not skip_job_validation:
            await self._validate_job_request(
                job_type=integrity_job.type,
                src_entity_type=integrity_job.src_entity_type,
                dest_entity_type=integrity_job.dest_entity_type,
                src_entity_id=integrity_job.src_entity_id,
                dest_entity_id=integrity_job.dest_entity_id,
                contextual_param=integrity_job.contextual_param,
                organization_id=organization_id,
            )

        job_user_choices = (
            await self._integrity_job_repository.find_user_choices_by_integrity_job_id(
                integrity_job_id=integrity_job_id,
                organization_id=organization_id,
            )
        )

        integrity_job_name = get_integrity_job_name(
            integrity_job_type=integrity_job.type,
            src_entity_type=integrity_job.src_entity_type,
            dest_entity_type=integrity_job.dest_entity_type,
        )

        await self._integrity_job_service.update_integrity_job(
            param=UpdateIntegrityJobParam(
                job_id=integrity_job.id,
                status=JobStatus.QUEUED,
                user_id=user_id,
                organization_id=organization_id,
            )
        )

        job_param = BaseIntegrityJobParam(
            integrity_job_name=integrity_job_name,
            job_id=integrity_job.id,
            retry_count=integrity_job.retry_count,
            src_entity_id=integrity_job.src_entity_id,
            dest_entity_id=integrity_job.dest_entity_id,
            user_id=user_id,
            organization_id=organization_id,
            user_choices=job_user_choices,
            contextual_param=integrity_job.contextual_param,
        )

        client = await get_temporal_client()
        await client.start_workflow(
            IntegrityJobDispatchWorkflow.run,
            id=get_integrity_job_dispatch_workflow_id(organization_id=organization_id),
            task_queue=INTEGRITY_JOB_TASK_QUEUE,
            start_signal="enqueue_job_param",
            start_signal_args=[job_param],
            id_reuse_policy=WorkflowIDReusePolicy.ALLOW_DUPLICATE,
        )

        return StartCRMIntegrityJobResponse(job_id=integrity_job.id)

    async def patch_integrity_job(
        self,
        job_id: UUID,
        user_id: UUID,
        organization_id: UUID,
        intergrity_job_patch_request: PatchCRMIntegrityJobRequest,
    ) -> None:
        if not specified(intergrity_job_patch_request.user_choices):
            return

        integrity_job = await self._integrity_job_repository.get_integrity_job(
            organization_id=organization_id,
            job_id=job_id,
        )

        if integrity_job.status != JobStatus.NEW:
            raise InvalidArgumentError("Only new job can be patched")

        integrity_job_name = get_integrity_job_name(
            integrity_job_type=integrity_job.type,
            src_entity_type=integrity_job.src_entity_type,
            dest_entity_type=integrity_job.dest_entity_type,
        )

        enriched_user_choices_with_default_values = (
            await self._validate_and_enriched_user_choices_param(
                integrity_job_name=integrity_job_name,
                user_choices=intergrity_job_patch_request.user_choices,
            )
        )

        for subdomain, user_choice in enriched_user_choices_with_default_values.items():
            db_subdomain_job = await self._integrity_sub_domain_job_repository.find_sub_domain_job_by_integrity_job_id_and_subdomain(
                integrity_job_id=job_id,
                subdomain=subdomain,
                organization_id=organization_id,
            )

            if not db_subdomain_job:
                await self._integrity_sub_domain_job_repository.insert(
                    CRMIntegritySubDomainJob(
                        id=uuid4(),
                        integrity_job_id=job_id,
                        subdomain=subdomain,
                        user_choice=user_choice,
                        status=SubDomainJobStatus.NEW,
                        created_at=zoned_utc_now(),
                        created_by_user_id=user_id,
                        organization_id=organization_id,
                    )
                )
            else:
                await self._integrity_sub_domain_job_repository.update_by_primary_key(
                    CRMIntegritySubDomainJob,
                    primary_key_to_value={"id": db_subdomain_job.id},
                    column_to_update={"user_choice": user_choice},
                )

    async def delete_integrity_job(
        self, job_id: UUID, user_id: UUID, organization_id: UUID
    ) -> None:
        await self._integrity_job_repository.delete_integrity_job(
            organization_id=organization_id,
            user_id=user_id,
            job_id=job_id,
        )

    async def retry_integrity_job(
        self, job_id: UUID, user_id: UUID, organization_id: UUID
    ) -> None:
        integrity_job = await self._integrity_job_repository.get_integrity_job(
            organization_id=organization_id,
            job_id=job_id,
        )

        if integrity_job.status != JobStatus.FAIL:
            raise InvalidArgumentError("Only failed job can be retried")

        await self._validate_job_request(
            job_type=integrity_job.type,
            src_entity_type=integrity_job.src_entity_type,
            dest_entity_type=integrity_job.dest_entity_type,
            src_entity_id=integrity_job.src_entity_id,
            dest_entity_id=integrity_job.dest_entity_id,
            contextual_param=integrity_job.contextual_param,
            organization_id=organization_id,
        )

        integrity_job_name = get_integrity_job_name(
            integrity_job_type=integrity_job.type,
            src_entity_type=integrity_job.src_entity_type,
            dest_entity_type=integrity_job.dest_entity_type,
        )

        user_choices = (
            await self._integrity_job_repository.find_user_choices_by_integrity_job_id(
                integrity_job_id=job_id,
                organization_id=organization_id,
            )
        )

        updated_retry_count = integrity_job.retry_count + 1

        await self._integrity_job_service.update_integrity_job(
            param=UpdateIntegrityJobParam(
                job_id=integrity_job.id,
                status=JobStatus.QUEUED,
                retry_count=updated_retry_count,
                user_id=user_id,
                organization_id=organization_id,
            )
        )

        job_param = BaseIntegrityJobParam(
            integrity_job_name=integrity_job_name,
            job_id=integrity_job.id,
            retry_count=updated_retry_count,
            src_entity_id=integrity_job.src_entity_id,
            dest_entity_id=integrity_job.dest_entity_id,
            user_id=user_id,
            organization_id=organization_id,
            user_choices=user_choices,
            contextual_param=integrity_job.contextual_param,
        )

        try:
            client = await get_temporal_client()
            await client.start_workflow(
                IntegrityJobDispatchWorkflow.run,
                id=get_integrity_job_dispatch_workflow_id(
                    organization_id=organization_id
                ),
                task_queue=INTEGRITY_JOB_TASK_QUEUE,
                start_signal="enqueue_job_param",
                start_signal_args=[job_param],
                id_reuse_policy=WorkflowIDReusePolicy.ALLOW_DUPLICATE,
            )
        except WorkflowAlreadyStartedError:
            raise InvalidArgumentError("Job already started")

    async def get_integrity_job_details(
        self,
        organization_id: UUID,
        job_id: UUID,
    ) -> IntegrityJobDetails:
        db_integrity_job = await self._integrity_job_repository.get_integrity_job(
            organization_id=organization_id,
            job_id=job_id,
        )

        db_user_v2 = one_row_or_none(
            await self._user_service.list_users_v2(
                only_include_user_ids={db_integrity_job.created_by_user_id},
                organization_id=organization_id,
            )
        )

        db_contact_emails = []

        # for now, only src_entity_id could be contact email id
        if db_integrity_job.src_entity_type == EntityType.CONTACT_EMAIL:
            db_contact_emails.append(
                not_none(
                    await self._contact_service.get_contact_email_by_id(
                        contact_email_id=db_integrity_job.src_entity_id,
                        organization_id=organization_id,
                        exclude_deleted_or_archived=False,
                    )
                )
            )

        db_integrity_operations = await self._integrity_operation_repository.find_integrity_operations_by_job_id(
            integrity_job_id=job_id,
            organization_id=organization_id,
        )

        db_integrity_sub_domain_jobs = await self._integrity_sub_domain_job_repository.find_sub_domain_jobs_by_integrity_job_id(
            integrity_job_id=job_id,
            organization_id=organization_id,
        )

        db_integrity_associated_entity_operations = await self._integrity_associated_entity_operation_repository.find_associated_entity_operations_by_integrity_job_id(
            integrity_job_id=job_id,
            organization_id=organization_id,
        )

        sub_domain_job_to_associated_data_operations_map: dict[
            UUID, list[IntegrityAssociatedDataOperation]
        ] = {}
        for aeo in db_integrity_associated_entity_operations:
            sub_domain_job_to_associated_data_operations_map.setdefault(
                not_none(aeo.integrity_subdomain_job_id), []
            ).append(
                IntegrityAssociatedDataOperation.from_db(
                    db_integrity_associated_entity_operation=aeo
                )
            )

        incomplete_job_detail = IntegrityJobDetails.from_db(
            db_integrity_job,
            operations=[
                IntegrityOperation.from_db(db_integrity_operation=op)
                for op in db_integrity_operations
            ],
            sub_domain_jobs=[
                IntegritySubDomainJob.from_db(
                    db_integrity_sub_domain_job=sub_domain_job,
                    associated_data_operations=sub_domain_job_to_associated_data_operations_map.get(
                        sub_domain_job.id, []
                    ),
                )
                for sub_domain_job in db_integrity_sub_domain_jobs
            ],
            created_by_user_v2=db_user_v2,
            contact_emails_lite=[],
            accounts=[],
            contacts=[],
            pipelines=[],
        )

        return one_row_only(
            await self.enrich_entities_for_job_details(
                incomplete_job_details=[incomplete_job_detail],
                organization_id=organization_id,
            )
        )

    async def preview_crm_integrity_job(
        self,
        user_id: UUID,
        organization_id: UUID,
        integrity_job_preview_request: IntegrityJobPreviewRequest,
    ) -> IntegrityJobPreviewResponse:
        integrity_job_name = get_integrity_job_name(
            integrity_job_type=integrity_job_preview_request.job_type,
            src_entity_type=integrity_job_preview_request.src_entity_type,
            dest_entity_type=integrity_job_preview_request.dest_entity_type,
        )

        enriched_user_choices_with_default_values = (
            await self._validate_and_enriched_user_choices_param(
                integrity_job_name=integrity_job_name,
                user_choices=integrity_job_preview_request.user_choices,
            )
        )

        await self._validate_job_request(
            job_type=integrity_job_preview_request.job_type,
            src_entity_type=integrity_job_preview_request.src_entity_type,
            dest_entity_type=integrity_job_preview_request.dest_entity_type,
            src_entity_id=integrity_job_preview_request.src_entity_id,
            dest_entity_id=integrity_job_preview_request.dest_entity_id,
            contextual_param=integrity_job_preview_request.contextual_param,
            organization_id=organization_id,
        )

        return await self._integrity_job_preview_service.preview_crm_integrity_job(
            integrity_job_preview_request=strict_model_copy(
                integrity_job_preview_request,
                user_choices=enriched_user_choices_with_default_values,
            ),
            user_id=user_id,
            organization_id=organization_id,
        )

    async def list_crm_integrity_jobs(
        self,
        organization_id: UUID,
    ) -> list[IntegrityJobDetails]:
        db_integrity_jobs = (
            await self._integrity_job_repository.list_crm_integrity_jobs(
                organization_id=organization_id,
            )
        )

        # enrich created user info
        created_by_user_ids_set = {
            db_integrity_job.created_by_user_id
            for db_integrity_job in db_integrity_jobs
        }

        db_users_v2 = await self._user_service.list_users_v2(
            only_include_user_ids=created_by_user_ids_set,
            organization_id=organization_id,
        )

        db_user_id_to_user_v2_map = {
            db_user_v2.id: db_user_v2 for db_user_v2 in db_users_v2
        }

        # enrich subdomain jobs
        db_subdomain_jobs = (
            await self._integrity_sub_domain_job_repository.list_sub_domain_jobs(
                organization_id=organization_id,
            )
        )

        integrity_job_to_subdomain_jobs_map: dict[
            UUID, list[IntegritySubDomainJob]
        ] = {}
        for db_subdomain_job in db_subdomain_jobs:
            integrity_job_to_subdomain_jobs_map.setdefault(
                db_subdomain_job.integrity_job_id, []
            ).append(
                IntegritySubDomainJob.from_db(
                    db_integrity_sub_domain_job=db_subdomain_job,
                    associated_data_operations=[],
                )
            )

        return [
            IntegrityJobDetails.from_db(
                db_integrity_job=db_integrity_job,
                operations=[],
                sub_domain_jobs=integrity_job_to_subdomain_jobs_map.get(
                    db_integrity_job.id, []
                ),
                created_by_user_v2=db_user_id_to_user_v2_map.get(
                    db_integrity_job.created_by_user_id
                ),
                contact_emails_lite=[],
                accounts=[],
                contacts=[],
                pipelines=[],
            )
            for db_integrity_job in db_integrity_jobs
        ]

    async def list_crm_integrity_job_user_options(
        self,
    ) -> list[IntegrityJobUserOptionsResponse]:
        return [
            IntegrityJobUserOptionsResponse(
                integrity_job_name=integrity_job_name,
                user_options=get_user_options_by_integrity_job_name(
                    integrity_job_name=integrity_job_name
                ),
                affected_sub_domains=get_affected_sub_domains_by_integrity_job_name(
                    integrity_job_name=integrity_job_name
                ),
                manual_update_subdomains=get_user_manual_update_sub_domains_by_integrity_job_name(
                    integrity_job_name=integrity_job_name
                ),
            )
            for integrity_job_name in IntegrityJobName
        ]

    def _collect_entity_ids_for_job_details(
        self, job_details: list[IntegrityJobDetails]
    ) -> dict[EntityType, set[UUID]]:
        """
        Collect all entity IDs that need to be enriched, grouped by entity type.

        Currently, collect ids from
        - src_entity_id
        - dest_entity_id if present
        - contact_email.contact_id if present, but this contact_id can only be collected
        after fetching contact_emails, which is delayed to _fetch_entities method.

        These IDs should be enough for FE to generate Job Title.

        """
        entity_type_to_ids_map: dict[EntityType, set[UUID]] = {
            EntityType.CONTACT: set(),
            EntityType.ACCOUNT: set(),
            EntityType.CONTACT_EMAIL: set(),
            EntityType.PIPELINE: set(),
        }

        for job in job_details:
            if job.src_entity_type in entity_type_to_ids_map:
                entity_type_to_ids_map[job.src_entity_type].add(job.src_entity_id)

            if (
                job.dest_entity_type
                and job.dest_entity_id
                and job.dest_entity_type in entity_type_to_ids_map
            ):
                entity_type_to_ids_map[job.dest_entity_type].add(job.dest_entity_id)

            if (
                isinstance(
                    job.contextual_param,
                    MoveContactToAccountContextualParam
                    | MoveContactEmailToAccountContextualParam,
                )
                and job.contextual_param.src_account_id
            ):
                entity_type_to_ids_map[EntityType.ACCOUNT].add(
                    job.contextual_param.src_account_id
                )

        return entity_type_to_ids_map

    async def _fetch_entities_for_job_details(
        self,
        *,
        entity_type_to_ids_map: dict[EntityType, set[UUID]],
        organization_id: UUID,
    ) -> dict[UUID, AccountV2 | ContactV2 | PipelineV2 | ContactEmailLite]:
        id_to_entity_map: dict[
            UUID, AccountV2 | ContactV2 | PipelineV2 | ContactEmailLite
        ] = {}

        db_contact_emails = await self._contact_service.list_contact_emails_by_ids(
            organization_id=organization_id,
            contact_email_ids=entity_type_to_ids_map[EntityType.CONTACT_EMAIL],
            exclude_deleted_or_archived=False,
        )

        db_contact_email_account_associations = await self._contact_service.list_contact_email_account_associations_by_contact_email_ids(
            organization_id=organization_id,
            contact_email_ids=entity_type_to_ids_map[EntityType.CONTACT_EMAIL],
            exclude_deleted_or_archived=False,
        )

        email_account_association_to_contact_email_id_map: dict[
            UUID, list[ContactEmailAccountAssociation]
        ] = defaultdict(list)
        for (
            db_contact_email_account_association
        ) in db_contact_email_account_associations:
            email_account_association_to_contact_email_id_map[
                db_contact_email_account_association.contact_email_id
            ].append(db_contact_email_account_association)

        for db_contact_email in db_contact_emails:
            # append contact_id from contact_email to contact id list as well
            entity_type_to_ids_map[EntityType.CONTACT].add(db_contact_email.contact_id)

            # construct contact email lite
            contact_email_account_association_list = (
                email_account_association_to_contact_email_id_map.get(
                    db_contact_email.id, []
                )
            )
            contact_account_emails = [
                contact_account_email_from_db(
                    email=db_contact_email.email,
                    contact_email_account_association=eaa,
                )
                for eaa in contact_email_account_association_list
            ]

            id_to_entity_map[db_contact_email.id] = contact_email_lite_from_db(
                contact_email=db_contact_email,
                contact_account_emails=contact_account_emails,
            )

        contacts_v2 = await self._contact_service.list_contacts_v2(
            organization_id=organization_id,
            only_include_contact_ids=entity_type_to_ids_map[EntityType.CONTACT],
        )

        for contact_v2 in contacts_v2:
            id_to_entity_map[contact_v2.id] = contact_v2

        accounts_v2 = await self._account_service.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids=entity_type_to_ids_map[EntityType.ACCOUNT],
        )

        for account_v2 in accounts_v2:
            id_to_entity_map[account_v2.id] = account_v2

        pipelines_v2 = await self._pipeline_service.list_pipelines(
            organization_id=organization_id,
            only_include_pipeline_ids=entity_type_to_ids_map[EntityType.PIPELINE],
        )

        for pipeline_v2 in pipelines_v2:
            id_to_entity_map[pipeline_v2.id] = pipeline_v2

        return id_to_entity_map

    async def enrich_entities_for_job_details(  # noqa: C901
        self,
        *,
        incomplete_job_details: list[IntegrityJobDetails],
        organization_id: UUID,
    ) -> list[IntegrityJobDetails]:
        """Enrich job details with entity information from contacts, accounts, and contact emails."""
        entity_type_to_ids_map = self._collect_entity_ids_for_job_details(
            incomplete_job_details
        )

        id_to_entity_map = await self._fetch_entities_for_job_details(
            entity_type_to_ids_map=entity_type_to_ids_map,
            organization_id=organization_id,
        )

        enriched_job_details: list[IntegrityJobDetails] = []

        def _add_entity_to_list(
            *,
            entity_type: EntityType,
            entity: AccountV2 | ContactV2 | PipelineV2 | ContactEmailLite,
            contact_emails_lite_entity_list: dict[UUID, ContactEmailLite],
            accounts_entity_list: dict[UUID, AccountV2],
            contacts_entity_list: dict[UUID, ContactV2],
            pipelines_entity_list: dict[UUID, PipelineV2],
        ) -> None:
            if entity_type == EntityType.CONTACT_EMAIL:
                contact_email = cast_or_error(entity, ContactEmailLite)
                contact_emails_lite_entity_list[entity.id] = contact_email

                # fetch contact in contact_email to list as well for job title generation
                if id_to_entity_map.get(contact_email.contact_id):
                    contacts_entity_list[contact_email.contact_id] = cast_or_error(
                        id_to_entity_map[contact_email.contact_id], ContactV2
                    )
            elif entity_type == EntityType.ACCOUNT:
                accounts_entity_list[entity.id] = cast_or_error(entity, AccountV2)
            elif entity_type == EntityType.CONTACT:
                contacts_entity_list[entity.id] = cast_or_error(entity, ContactV2)
            elif entity_type == EntityType.PIPELINE:
                pipelines_entity_list[entity.id] = cast_or_error(entity, PipelineV2)

        for job_detail in incomplete_job_details:
            contact_emails_lite_entity_list: dict[UUID, ContactEmailLite] = {}
            accounts_entity_list: dict[UUID, AccountV2] = {}
            contacts_entity_list: dict[UUID, ContactV2] = {}
            pipelines_entity_list: dict[UUID, PipelineV2] = {}

            if id_to_entity_map.get(job_detail.src_entity_id):
                _add_entity_to_list(
                    entity_type=job_detail.src_entity_type,
                    entity=id_to_entity_map[job_detail.src_entity_id],
                    contact_emails_lite_entity_list=contact_emails_lite_entity_list,
                    accounts_entity_list=accounts_entity_list,
                    contacts_entity_list=contacts_entity_list,
                    pipelines_entity_list=pipelines_entity_list,
                )

            if (
                job_detail.dest_entity_type
                and job_detail.dest_entity_id
                and id_to_entity_map.get(job_detail.dest_entity_id)
            ):
                _add_entity_to_list(
                    entity_type=job_detail.dest_entity_type,
                    entity=id_to_entity_map[job_detail.dest_entity_id],
                    contact_emails_lite_entity_list=contact_emails_lite_entity_list,
                    accounts_entity_list=accounts_entity_list,
                    contacts_entity_list=contacts_entity_list,
                    pipelines_entity_list=pipelines_entity_list,
                )

            if (
                isinstance(
                    job_detail.contextual_param,
                    MoveContactToAccountContextualParam
                    | MoveContactEmailToAccountContextualParam,
                )
                and job_detail.contextual_param.src_account_id
            ):
                _add_entity_to_list(
                    entity_type=EntityType.ACCOUNT,
                    entity=id_to_entity_map[job_detail.contextual_param.src_account_id],
                    contact_emails_lite_entity_list=contact_emails_lite_entity_list,
                    accounts_entity_list=accounts_entity_list,
                    contacts_entity_list=contacts_entity_list,
                    pipelines_entity_list=pipelines_entity_list,
                )

            enriched_job_details.append(
                strict_model_copy(
                    job_detail,
                    contact_emails_lite=list(contact_emails_lite_entity_list.values()),
                    accounts=list(accounts_entity_list.values()),
                    contacts=list(contacts_entity_list.values()),
                    pipelines=list(pipelines_entity_list.values()),
                )
            )

        return enriched_job_details


def get_crm_integrity_service(
    request: Request,
) -> CRMIntegrityService:
    db_engine = get_db_engine(request=request)
    return CRMIntegrityService(
        engine=db_engine,
    )


def get_crm_integrity_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> CRMIntegrityService:
    return CRMIntegrityService(
        engine=db_engine,
    )
