from __future__ import annotations

from uuid import UUID

from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationBase,
    IntegrityJobBase,
    IntegrityOperationBase,
    IntegritySubDomainJobBase,
)
from salestech_be.core.metadata.types import ContactEmailLite
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityAssociatedEntityOperation,
    CRMIntegrityJob,
    CRMIntegrityJobUserOption,
    CRMIntegrityOperation,
    CRMIntegritySubDomainJob,
    JobErrorDetails,
)
from salestech_be.db.models.crm_integrity import (
    JobStatus as IntegrityJobStatus,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class IntegrityAssociatedDataOperation(IntegrityAssociatedDataOperationBase):
    id: UUID

    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    updated_at: ZoneRequiredDateTime | None
    updated_by_user_id: UUID | None
    deleted_at: ZoneRequiredDateTime | None
    deleted_by_user_id: UUID | None

    organization_id: UUID

    @staticmethod
    def from_db(
        db_integrity_associated_entity_operation: CRMIntegrityAssociatedEntityOperation,
    ) -> IntegrityAssociatedDataOperation:
        return IntegrityAssociatedDataOperation(
            id=db_integrity_associated_entity_operation.id,
            type=db_integrity_associated_entity_operation.type,
            entity_type=db_integrity_associated_entity_operation.entity_type,
            entity_id=db_integrity_associated_entity_operation.entity_id,
            entity_field=db_integrity_associated_entity_operation.entity_field,
            entity_field_type=db_integrity_associated_entity_operation.entity_field_type,
            before_value=db_integrity_associated_entity_operation.before_value,
            after_value=db_integrity_associated_entity_operation.after_value,
            created_at=db_integrity_associated_entity_operation.created_at,
            created_by_user_id=db_integrity_associated_entity_operation.created_by_user_id,
            updated_at=db_integrity_associated_entity_operation.updated_at,
            updated_by_user_id=db_integrity_associated_entity_operation.updated_by_user_id,
            deleted_at=db_integrity_associated_entity_operation.deleted_at,
            deleted_by_user_id=db_integrity_associated_entity_operation.deleted_by_user_id,
            organization_id=db_integrity_associated_entity_operation.organization_id,
        )


class IntegritySubDomainJob(IntegritySubDomainJobBase):
    id: UUID
    associated_data_operations: list[IntegrityAssociatedDataOperation]

    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    updated_at: ZoneRequiredDateTime | None
    updated_by_user_id: UUID | None
    deleted_at: ZoneRequiredDateTime | None
    deleted_by_user_id: UUID | None
    organization_id: UUID

    @staticmethod
    def from_db(
        db_integrity_sub_domain_job: CRMIntegritySubDomainJob,
        associated_data_operations: list[IntegrityAssociatedDataOperation],
    ) -> IntegritySubDomainJob:
        # Explicitly type the user_choice to match the base class
        user_choice: CRMIntegrityJobUserOption | None = (
            db_integrity_sub_domain_job.user_choice
        )
        return IntegritySubDomainJob(
            id=db_integrity_sub_domain_job.id,
            associated_data_operations=associated_data_operations,
            subdomain=db_integrity_sub_domain_job.subdomain,
            user_choice=user_choice,
            status=db_integrity_sub_domain_job.status,
            created_at=db_integrity_sub_domain_job.created_at,
            created_by_user_id=db_integrity_sub_domain_job.created_by_user_id,
            updated_at=db_integrity_sub_domain_job.updated_at,
            updated_by_user_id=db_integrity_sub_domain_job.updated_by_user_id,
            deleted_at=db_integrity_sub_domain_job.deleted_at,
            deleted_by_user_id=db_integrity_sub_domain_job.deleted_by_user_id,
            organization_id=db_integrity_sub_domain_job.organization_id,
        )


class IntegrityOperation(IntegrityOperationBase):
    id: UUID

    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    updated_at: ZoneRequiredDateTime | None
    updated_by_user_id: UUID | None
    deleted_at: ZoneRequiredDateTime | None
    deleted_by_user_id: UUID | None
    organization_id: UUID

    @staticmethod
    def from_db(
        db_integrity_operation: CRMIntegrityOperation,
    ) -> IntegrityOperation:
        return IntegrityOperation(
            id=db_integrity_operation.id,
            type=db_integrity_operation.type,
            src_entity_type=db_integrity_operation.src_entity_type,
            src_entity_id=db_integrity_operation.src_entity_id,
            dest_entity_type=db_integrity_operation.dest_entity_type,
            dest_entity_id=db_integrity_operation.dest_entity_id,
            created_at=db_integrity_operation.created_at,
            created_by_user_id=db_integrity_operation.created_by_user_id,
            updated_at=db_integrity_operation.updated_at,
            updated_by_user_id=db_integrity_operation.updated_by_user_id,
            deleted_at=db_integrity_operation.deleted_at,
            deleted_by_user_id=db_integrity_operation.deleted_by_user_id,
            organization_id=db_integrity_operation.organization_id,
        )


class IntegrityJobDetails(IntegrityJobBase):
    job_id: UUID
    job_status: IntegrityJobStatus
    operations: list[IntegrityOperation]
    sub_domain_jobs: list[IntegritySubDomainJob]
    started_at: ZoneRequiredDateTime | None
    ended_at: ZoneRequiredDateTime | None
    retry_count: int
    error_details: JobErrorDetails | None

    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    created_by_user_display_name: str | None
    created_by_user_avatar_url: str | None
    updated_at: ZoneRequiredDateTime | None
    updated_by_user_id: UUID | None
    deleted_at: ZoneRequiredDateTime | None
    deleted_by_user_id: UUID | None
    organization_id: UUID

    contact_emails_lite: list[ContactEmailLite]
    contacts: list[ContactV2]
    accounts: list[AccountV2]
    pipelines: list[PipelineV2]

    @staticmethod
    def from_db(
        db_integrity_job: CRMIntegrityJob,
        operations: list[IntegrityOperation],
        sub_domain_jobs: list[IntegritySubDomainJob],
        created_by_user_v2: OrganizationUserV2 | None,
        contact_emails_lite: list[ContactEmailLite],
        accounts: list[AccountV2],
        contacts: list[ContactV2],
        pipelines: list[PipelineV2],
    ) -> IntegrityJobDetails:
        user_choices = {
            sub_domain_job.subdomain: sub_domain_job.user_choice
            for sub_domain_job in sub_domain_jobs
        }

        return IntegrityJobDetails(
            job_id=db_integrity_job.id,
            job_status=db_integrity_job.status,
            src_entity_type=db_integrity_job.src_entity_type,
            src_entity_id=db_integrity_job.src_entity_id,
            dest_entity_type=db_integrity_job.dest_entity_type,
            dest_entity_id=db_integrity_job.dest_entity_id,
            contextual_param=db_integrity_job.contextual_param,
            user_choices=user_choices,
            job_type=db_integrity_job.type,
            started_at=db_integrity_job.started_at,
            ended_at=db_integrity_job.ended_at,
            retry_count=db_integrity_job.retry_count,
            error_details=db_integrity_job.error_details,
            created_at=db_integrity_job.created_at,
            created_by_user_id=db_integrity_job.created_by_user_id,
            created_by_user_display_name=created_by_user_v2.display_name
            if created_by_user_v2
            else None,
            created_by_user_avatar_url=created_by_user_v2.avatar_url
            if created_by_user_v2
            else None,
            updated_at=db_integrity_job.updated_at,
            updated_by_user_id=db_integrity_job.updated_by_user_id,
            deleted_at=db_integrity_job.deleted_at,
            deleted_by_user_id=db_integrity_job.deleted_by_user_id,
            organization_id=db_integrity_job.organization_id,
            operations=operations,
            sub_domain_jobs=sub_domain_jobs,
            contact_emails_lite=contact_emails_lite,
            accounts=accounts,
            contacts=contacts,
            pipelines=pipelines,
        )
