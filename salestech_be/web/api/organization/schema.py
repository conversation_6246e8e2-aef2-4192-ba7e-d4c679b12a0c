from __future__ import annotations

import re
from datetime import time
from enum import StrEnum
from typing import Self, cast
from uuid import UUID

from pydantic import BaseModel, field_validator, model_validator

from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.common.type.patch_request import UNSET, BasePatchRequest, UnsetAware
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.db.models.organization import Organization
from salestech_be.db.models.organization_info import (
    CustomWord,
    MeetingBuffer,
    MeetingRecordingLevel,
    MeetingSyncLevel,
    OrganizationInfo,
)
from salestech_be.db.models.organization_info import (
    OrganizationProfile as DbOrganizationProfile,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.web.api.common.container import ListEntityRequest


class OrganizationProfile(BaseModel):
    is_task_digest_enabled: bool | None = None
    task_digest_delivery_time: time | None = None
    custom_words: list[CustomWord] | None = None
    meeting_recording_level: MeetingRecordingLevel | None = (
        MeetingRecordingLevel.EXTERNAL_MEETINGS
    )
    meeting_sync_level: MeetingSyncLevel | None = None
    meeting_buffer: MeetingBuffer | None = None
    alternate_domains: list[str] | None = None
    bot_assistant: str | None = None

    @classmethod
    def from_db_obj(
        cls, db_organization_profile: DbOrganizationProfile
    ) -> OrganizationProfile:
        return OrganizationProfile(
            is_task_digest_enabled=db_organization_profile.is_task_digest_enabled,
            task_digest_delivery_time=db_organization_profile.task_digest_delivery_time,
            custom_words=db_organization_profile.custom_words,
            meeting_recording_level=(
                db_organization_profile.meeting_recording_level
                if db_organization_profile.meeting_recording_level
                else MeetingRecordingLevel.EXTERNAL_MEETINGS
            ),
            meeting_sync_level=db_organization_profile.meeting_sync_level,
            meeting_buffer=db_organization_profile.meeting_buffer,
            alternate_domains=db_organization_profile.alternate_domains,
            bot_assistant=db_organization_profile.bot_assistant,
        )


class OrganizationResponse(BaseModel):
    id: UUID
    display_name: str
    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    updated_at: ZoneRequiredDateTime | None
    updated_by_user_id: UUID | None
    deactivated_at: ZoneRequiredDateTime | None
    deactivated_by_user_id: UUID | None
    organization_info: dict[str, str] | None
    organization_profile: OrganizationProfile | None

    @staticmethod
    def from_db_obj(
        organization_db_obj: Organization,
        organization_info_db_obj: OrganizationInfo | None,
    ) -> OrganizationResponse:
        return OrganizationResponse(
            id=organization_db_obj.id,
            display_name=organization_db_obj.display_name,
            created_at=organization_db_obj.created_at,
            created_by_user_id=organization_db_obj.created_by_user_id,
            updated_at=organization_db_obj.updated_at,
            updated_by_user_id=organization_db_obj.updated_by_user_id,
            deactivated_at=organization_db_obj.deactivated_at,
            deactivated_by_user_id=organization_db_obj.deactivated_by_user_id,
            organization_info=(
                organization_info_db_obj.info if organization_info_db_obj else None
            ),
            organization_profile=(
                OrganizationProfile.from_db_obj(organization_info_db_obj.profile)
                if organization_info_db_obj and organization_info_db_obj.profile
                else OrganizationProfile()
            ),
        )


class OrganizationUpdateRequest(BaseModel):
    display_name: str | None
    organization_info: dict[str, str] | None


class PatchOrganizationRequest(BasePatchRequest):
    require_at_least_one_specified_field = True

    is_task_digest_enabled: UnsetAware[bool | None] = UNSET
    task_digest_delivery_time: UnsetAware[time | None] = UNSET
    custom_words: UnsetAware[list[CustomWord] | None] = UNSET
    meeting_recording_level: UnsetAware[MeetingRecordingLevel | None] = UNSET
    meeting_sync_level: UnsetAware[MeetingSyncLevel | None] = UNSET
    meeting_buffer: UnsetAware[MeetingBuffer | None] = UNSET
    alternate_domains: UnsetAware[list[str] | None] = UNSET


class ListOrganizationRequest(ListEntityRequest):
    pass


class PatchPublicDomainAccountsRequest(BaseModel):
    domain: str
    dry_run: bool = False


OrganizationPreferenceSingleValueType = str | bool | int | float | UUID

OrganizationPreferenceValue = (
    OrganizationPreferenceSingleValueType
    | list[OrganizationPreferenceSingleValueType]
    | None
)

SettingsType = (
    dict[str, dict[str, OrganizationPreferenceValue]]
    | dict[str, OrganizationPreferenceValue]
)


class FollowupEmailPreferences(BaseModel):
    enabled: bool | None = None
    default_template_id: UUID | None = None


class OrganizationPreferenceDomainVoice(BaseModel):
    followup_email: FollowupEmailPreferences | None = None


class ActivityCaptureMode(StrEnum):
    ALL = "all"
    SENT = "sent"
    NONE = "none"
    DOMAIN = "domain"


class MeetingRecordingPreference(BaseModel):
    sales_meetings: bool = True
    internal_meetings: bool = False

    # on top of the sales_meetings and internal_meetings
    # this field can additionally control recording behavior for recurring meetings
    recurring_meetings: bool = True

    @field_validator("recurring_meetings")
    @classmethod
    def recurring_meetings_must_be_true(cls, v: bool) -> bool:
        if v is not True:
            raise ValueError("Invalid value for recurring_meetings")
        return v


class OrganizationActivityCapturePreference(BaseModel):
    capture_mode: ActivityCaptureMode = ActivityCaptureMode.SENT
    blocked_domains: list[str] = []
    blocked_emails: list[EmailStrLower] = []


class OrganizationSequenceSettings(BaseModel):
    append_opt_out_after_signature: bool | None = None
    opt_out_message_template: str | None = None


class OrganizationPreferenceResponse(BaseModel):
    voice: OrganizationPreferenceDomainVoice | None = None
    activity_capture: OrganizationActivityCapturePreference | None = None
    sequence_settings: OrganizationSequenceSettings | None = None
    meeting_recording: MeetingRecordingPreference | None = None


class FollowupEmailPreferencesRequest(ShapeConstrainedModel[FollowupEmailPreferences]):
    enabled: UnsetAware[bool | None]
    default_template_id: UnsetAware[UUID | None]


class OrganizationPreferenceDomainVoiceRequest(BasePatchRequest):
    followup_email: UnsetAware[FollowupEmailPreferencesRequest]


class OrganizationActivityCapturePreferenceRequest(BasePatchRequest):
    capture_mode: UnsetAware[ActivityCaptureMode | None] = UNSET
    blocked_domains: UnsetAware[list[str] | None] = UNSET
    blocked_emails: UnsetAware[list[EmailStrLower] | None] = UNSET


class MeetingRecordingPreferenceRequest(BasePatchRequest):
    sales_meetings: UnsetAware[bool | None] = UNSET
    internal_meetings: UnsetAware[bool | None] = UNSET
    recurring_meetings: UnsetAware[bool | None] = UNSET

    @field_validator("recurring_meetings")
    @classmethod
    def recurring_meetings_must_be_true(
        cls, v: UnsetAware[bool | None]
    ) -> UnsetAware[bool | None]:
        if v is not UNSET and v is not None and v is not True:
            raise ValueError("recurring_meetings is set, must be True")
        return v


class OrganizationSequenceSettingsRequest(BasePatchRequest):
    append_opt_out_after_signature: UnsetAware[bool | None] = UNSET
    opt_out_message_template: UnsetAware[str | None] = UNSET

    @field_validator("opt_out_message_template", mode="before")
    @classmethod
    def validate_opt_out_message_template(
        cls, v: UnsetAware[str | None]
    ) -> UnsetAware[str | None]:
        # ensure the template contains only one pair of <% and %>
        if isinstance(v, str) and len(re.findall(r"<%(.*?)%>", v)) != 1:
            raise ValueError("Template must contain exactly one pair of '<%' and '%>'")
        return v


class OrganizationPreferenceRequest(BasePatchRequest):
    voice: UnsetAware[OrganizationPreferenceDomainVoiceRequest] = UNSET
    activity_capture: UnsetAware[OrganizationActivityCapturePreferenceRequest] = UNSET
    sequence_settings: UnsetAware[OrganizationSequenceSettingsRequest] = UNSET
    meeting_recording: UnsetAware[MeetingRecordingPreferenceRequest] = UNSET

    @model_validator(mode="after")
    def validate_request(self) -> Self:
        self._validate_exactly_one_field_set()
        return self

    def _validate_exactly_one_field_set(self) -> None:
        """Ensures exactly one preference type is set in the request."""
        set_fields = [
            field for field in self.model_fields if getattr(self, field) is not UNSET
        ]

        if len(set_fields) != 1:
            raise ValueError(
                f"Exactly one preference type must be set. Found: {set_fields}"
            )

    @property
    def key(self) -> str:
        """Returns the name of the key that is set"""
        for field_name in self.model_fields:
            if getattr(self, field_name) is not UNSET:
                return field_name
        # This should never happen due to the validator, but keep as fallback
        return "voice"  # Voice is the current default key

    @property
    def settings(self) -> BasePatchRequest:
        """Returns the settings of the key that is set"""
        key = self.key
        return cast(BasePatchRequest, getattr(self, key))
