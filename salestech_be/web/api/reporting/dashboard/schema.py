from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.core.reporting.type.layout import DashboardLayoutConfig, DashboardReportLayoutConfig


class DashboardResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str] = None
    created_at: str
    updated_at: Optional[str] = None
    layout_config: Optional[DashboardLayoutConfig] = None


class DashboardDetailResponse(DashboardResponse):
    pass


class DashboardListResponse(BaseModel):
    dashboards: List[DashboardResponse]
    total: int
    page: int
    page_size: int


class CreateDashboardRequest(BaseModel):
    name: str
    description: Optional[str] = None
    layout_config: Optional[DashboardLayoutConfig] = None


class UpdateDashboardRequest(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    layout_config: Optional[DashboardLayoutConfig] = None


class DashboardReportAssociationResponse(BaseModel):
    id: UUID
    dashboard_id: UUID
    report_id: UUID
    layout_config: Optional[DashboardReportLayoutConfig] = None


class CreateDashboardReportAssociationRequest(BaseModel):
    dashboard_id: UUID
    report_id: UUID
    layout_config: Optional[DashboardReportLayoutConfig] = None


class UpdateDashboardReportAssociationRequest(BaseModel):
    layout_config: Optional[DashboardReportLayoutConfig] = None
