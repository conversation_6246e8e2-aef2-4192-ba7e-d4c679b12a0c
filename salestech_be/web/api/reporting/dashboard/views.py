from typing import Annotated
from uuid import UUID

from fastapi import Depends, HTTPException, Query

from salestech_be.common.lifespan import get_db_engine
from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.reporting import ReportingDashboard, ReportingDashboardReportAssociation
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import require_read_placeholder_access
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.reporting.dashboard.schema import (
    CreateDashboardReportAssociationRequest,
    CreateDashboardRequest,
    DashboardDetailResponse,
    DashboardListResponse,
    DashboardReportAssociationResponse,
    DashboardResponse,
    UpdateDashboardReportAssociationRequest,
    UpdateDashboardRequest,
)
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserId,
)

router = ReeAPIRouter()
logger = get_logger("reporting.dashboard")


async def get_reporting_repository(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> ReportingRepository:
    return ReportingRepository(engine=db_engine)


@router.get(
    "",
    response_model=DashboardListResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def list_dashboards(
    organization_id: AnnotatedReevoOrganizationId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
) -> DashboardListResponse:
    """List all dashboards for an organization."""
    dashboards = await reporting_repository.get_dashboards()

    # Apply pagination
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    paginated_dashboards = dashboards[start_idx:end_idx]

    return DashboardListResponse(
        dashboards=[
            DashboardResponse(
                id=dashboard.id,
                name=dashboard.name,
                description=dashboard.description,
                created_at=dashboard.created_at.isoformat(),
                updated_at=dashboard.updated_at.isoformat() if dashboard.updated_at else None,
                layout_config=dashboard.layout_config,
            )
            for dashboard in paginated_dashboards
        ],
        total=len(dashboards),
        page=page,
        page_size=page_size,
    )


@router.get(
    "/{dashboard_id}",
    response_model=DashboardDetailResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def get_dashboard(
    dashboard_id: UUID,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
) -> DashboardDetailResponse:
    """Get a dashboard by ID."""
    # TODO: Add organization_id filter to find_dashboard_by_id
    dashboard = await reporting_repository._find_unique_by_column_values(
        table_model=ReportingDashboard,
        id=dashboard_id,
    )
    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    return DashboardDetailResponse(
        id=dashboard.id,
        name=dashboard.name,
        description=dashboard.description,
        created_at=dashboard.created_at.isoformat(),
        updated_at=dashboard.updated_at.isoformat() if dashboard.updated_at else None,
        layout_config=dashboard.layout_config,
    )


@router.post(
    "",
    response_model=DashboardDetailResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def create_dashboard(
    request: CreateDashboardRequest,
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
) -> DashboardDetailResponse:
    """Create a new dashboard."""
    # Create the dashboard
    now = zoned_utc_now()
    dashboard = ReportingDashboard(
        id=UUID(int=0),  # Will be replaced by the database
        name=request.name,
        description=request.description,
        layout_config=request.layout_config,
        organization_id=organization_id,
        created_at=now,
        created_by_user_id=user_id,
    )

    created_dashboard = await reporting_repository.insert(dashboard)

    return DashboardDetailResponse(
        id=created_dashboard.id,
        name=created_dashboard.name,
        description=created_dashboard.description,
        created_at=created_dashboard.created_at.isoformat(),
        updated_at=created_dashboard.updated_at.isoformat() if created_dashboard.updated_at else None,
        layout_config=created_dashboard.layout_config,
    )


@router.put(
    "/{dashboard_id}",
    response_model=DashboardDetailResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def update_dashboard(
    dashboard_id: UUID,
    request: UpdateDashboardRequest,
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
) -> DashboardDetailResponse:
    """Update an existing dashboard."""
    # Get the existing dashboard
    # TODO: Add organization_id filter to find_dashboard_by_id
    dashboard = await reporting_repository._find_unique_by_column_values(
        table_model=ReportingDashboard,
        id=dashboard_id,
    )
    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Update the dashboard fields
    if request.name is not None:
        dashboard.name = request.name
    if request.description is not None:
        dashboard.description = request.description
    if request.layout_config is not None:
        dashboard.layout_config = request.layout_config

    # Update the audit fields
    dashboard.updated_at = zoned_utc_now()
    dashboard.updated_by_user_id = user_id

    # Save the updated dashboard
    updated_dashboard = await reporting_repository.update(dashboard)

    return DashboardDetailResponse(
        id=updated_dashboard.id,
        name=updated_dashboard.name,
        description=updated_dashboard.description,
        created_at=updated_dashboard.created_at.isoformat(),
        updated_at=updated_dashboard.updated_at.isoformat() if updated_dashboard.updated_at else None,
        layout_config=updated_dashboard.layout_config,
    )


@router.post(
    "/{dashboard_id}/reports",
    response_model=DashboardReportAssociationResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def add_report_to_dashboard(
    dashboard_id: UUID,
    request: CreateDashboardReportAssociationRequest,
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
) -> DashboardReportAssociationResponse:
    """Add a report to a dashboard."""
    # Validate that the dashboard exists
    # TODO: Add organization_id filter to find_dashboard_by_id
    dashboard = await reporting_repository._find_unique_by_column_values(
        table_model=ReportingDashboard,
        id=dashboard_id,
    )
    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Validate that the report exists
    report = await reporting_repository.find_report_by_id(request.report_id, organization_id)
    if not report:
        raise HTTPException(status_code=404, detail="Report not found")

    # Create the association
    now = zoned_utc_now()
    association = ReportingDashboardReportAssociation(
        id=UUID(int=0),  # Will be replaced by the database
        dashboard_id=dashboard_id,
        report_id=request.report_id,
        layout_config=request.layout_config,
        organization_id=organization_id,
        created_at=now,
        created_by_user_id=user_id,
    )

    created_association = await reporting_repository.insert(association)

    return DashboardReportAssociationResponse(
        id=created_association.id,
        dashboard_id=created_association.dashboard_id,
        report_id=created_association.report_id,
        layout_config=created_association.layout_config,
    )


@router.put(
    "/{dashboard_id}/reports/{report_id}",
    response_model=DashboardReportAssociationResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def update_dashboard_report_association(
    dashboard_id: UUID,
    report_id: UUID,
    request: UpdateDashboardReportAssociationRequest,
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
) -> DashboardReportAssociationResponse:
    """Update a dashboard-report association."""
    # Get the existing association
    association = await reporting_repository._find_unique_by_column_values(
        table_model=ReportingDashboardReportAssociation,
        dashboard_id=dashboard_id,
        report_id=report_id,
    )
    if not association:
        raise HTTPException(status_code=404, detail="Dashboard-report association not found")

    # Update the association fields
    if request.layout_config is not None:
        association.layout_config = request.layout_config

    # Update the audit fields
    association.updated_at = zoned_utc_now()
    association.updated_by_user_id = user_id

    # Save the updated association
    updated_association = await reporting_repository.update(association)

    return DashboardReportAssociationResponse(
        id=updated_association.id,
        dashboard_id=updated_association.dashboard_id,
        report_id=updated_association.report_id,
        layout_config=updated_association.layout_config,
    )
