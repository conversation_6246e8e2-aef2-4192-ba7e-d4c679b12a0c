"""
This module re-exports the schemas from the reporting modules.
All schemas have been refactored into separate modules:
- dataset
- report
- dashboard
- engine

These re-exports are provided for backward compatibility.
"""

__all__ = [
    # Dataset schemas
    "CreateDatasetRequest",
    "DatasetDetailResponse",
    "DatasetFieldResponse",
    "DatasetListResponse",
    "DatasetQueryRequest",
    "DatasetResponse",
    "UpdateDatasetRequest",
    # Report schemas
    "CreateReportRequest",
    "ReportDetailResponse",
    "ReportListResponse",
    "ReportQueryRequest",
    "ReportResponse",
    "UpdateReportRequest",
    # Dashboard schemas
    "CreateDashboardReportAssociationRequest",
    "CreateDashboardRequest",
    "DashboardDetailResponse",
    "DashboardListResponse",
    "DashboardReportAssociationResponse",
    "DashboardResponse",
    "UpdateDashboardReportAssociationRequest",
    "UpdateDashboardRequest",
    # Engine schemas
    "EngineQueryRequest",
    "FunctionDetailResponse",
    "FunctionListResponse",
    "FunctionParameterResponse",
    "FunctionResponse",
    "QueryPreviewRequest",
    "QueryResponse",
]

# Dataset schemas
# Dashboard schemas
from salestech_be.web.api.reporting.dashboard.schema import (
    CreateDashboardReportAssociationRequest,
    CreateDashboardRequest,
    DashboardDetailResponse,
    DashboardListResponse,
    DashboardReportAssociationResponse,
    DashboardResponse,
    UpdateDashboardReportAssociationRequest,
    UpdateDashboardRequest,
)
from salestech_be.web.api.reporting.dataset.schema import (
    CreateDatasetRequest,
    DatasetDetailResponse,
    DatasetFieldResponse,
    DatasetListResponse,
    DatasetQueryRequest,
    DatasetResponse,
    UpdateDatasetRequest,
)

# Engine schemas
from salestech_be.web.api.reporting.engine.schema import (
    EngineQueryRequest,
    FunctionDetailResponse,
    FunctionListResponse,
    FunctionParameterResponse,
    FunctionResponse,
    QueryPreviewRequest,
    QueryResponse,
)

# Report schemas
from salestech_be.web.api.reporting.report.schema import (
    CreateReportRequest,
    ReportDetailResponse,
    ReportListResponse,
    ReportQueryRequest,
    ReportResponse,
    UpdateReportRequest,
)
