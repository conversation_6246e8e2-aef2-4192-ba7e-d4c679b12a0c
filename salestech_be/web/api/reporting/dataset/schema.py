from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.core.reporting.type.query_config import QueryConfig


class DatasetResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str] = None
    source: str
    type: str
    owner_user_id: UUID
    created_at: str
    updated_at: Optional[str] = None


class DatasetFieldResponse(BaseModel):
    id: UUID
    dataset_id: UUID
    name: str
    display_name: Optional[str] = None
    data_type: str
    is_derived: bool = False
    derivation_expression: Optional[str] = None


class DatasetDetailResponse(DatasetResponse):
    fields: List[DatasetFieldResponse] = Field(default_factory=list)
    query_config: Optional[QueryConfig] = None
    sql_statement: Optional[str] = None
    table_reference: Optional[str] = None


class DatasetListResponse(BaseModel):
    datasets: List[DatasetResponse]
    total: int
    page: int
    page_size: int


class CreateDatasetRequest(BaseModel):
    name: str
    description: Optional[str] = None
    source: str
    type: str
    table_reference: Optional[str] = None
    query_config: Optional[QueryConfig] = None
    sql_statement: Optional[str] = None


class UpdateDatasetRequest(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    table_reference: Optional[str] = None
    query_config: Optional[QueryConfig] = None
    sql_statement: Optional[str] = None


class DatasetQueryRequest(BaseModel):
    filters: Optional[dict] = None
