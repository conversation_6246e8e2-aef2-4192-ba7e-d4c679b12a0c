from typing import Annotated

from fastapi import Depends, HTTPException

from salestech_be.common.lifespan import get_db_engine
from salestech_be.core.reporting.connection.materialize_connection import (
    AsyncMaterializeConnection,
)
from salestech_be.core.reporting.service.query_execution_service import (
    QueryExecutionService,
)
from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import require_read_placeholder_access
from salestech_be.web.api.reporting.engine.schema import (
    EngineQueryRequest,
    QueryPreviewRequest,
    QueryResponse,
)
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
)

router = ReeAPIRouter()
logger = get_logger("reporting.engine")


async def get_reporting_repository(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> ReportingRepository:
    return ReportingRepository(engine=db_engine)


async def get_materialize_connection() -> AsyncMaterializeConnection:
    """Get a connection to the Materialize database using settings."""
    from salestech_be.settings import settings

    connection = AsyncMaterializeConnection(
        host=settings.materialize_host,
        port=settings.materialize_port,
        user=settings.materialize_user,
        password=settings.materialize_pass,
        database=settings.materialize_base,
        ssl=True,
    )
    await connection.connect()
    return connection


async def get_query_execution_service(
    materialize_connection: Annotated[
        AsyncMaterializeConnection, Depends(get_materialize_connection)
    ],
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> QueryExecutionService:
    return QueryExecutionService(
        db_engine=db_engine,
        dwh_connection=materialize_connection,
    )


@router.post(
    "/_query",
    response_model=QueryResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def query_engine(
    request: EngineQueryRequest,
    organization_id: AnnotatedReevoOrganizationId,
    query_execution_service: Annotated[
        QueryExecutionService, Depends(get_query_execution_service)
    ],
) -> QueryResponse:
    """
    Query a dataset or report with optional filters.

    This endpoint can be used in the following ways:
    1. dataset_id only: Load dataset and execute query
    2. dataset_id + filters: Load dataset, merge filters, and execute query
    3. report_id only: Load report, load dataset, and execute query
    4. report_id + filters: Load report, load dataset, merge filters, and execute query

    Either dataset_id or report_id must be provided, but not both.
    """
    logger.info(f"Query engine request: {request}")
    # Execute query based on request type
    if request.dataset_id is not None:
        # Dataset query
        results = await query_execution_service.execute_dataset(
            dataset_id=request.dataset_id,
            filters=request.filters,
            organization_id=organization_id,
        )
    else:
        # Report query
        results = await query_execution_service.execute_report(
            report_id=request.report_id,
            filters=request.filters,
            organization_id=organization_id,
        )

    # Extract column names from the first result
    columns = list(results[0].keys()) if results else []
    return QueryResponse(
        data=results,
        columns=columns,
    )


@router.post(
    "/_preview",
    response_model=QueryResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def preview_query(
    request: QueryPreviewRequest,
    organization_id: AnnotatedReevoOrganizationId,
    query_execution_service: Annotated[
        QueryExecutionService, Depends(get_query_execution_service)
    ],
) -> QueryResponse:
    """
    Preview a query from a raw query config.

    This endpoint is used when creating a new report to preview the results
    before saving. It takes a QueryConfig directly and executes it.
    """
    results = await query_execution_service.execute_query(
        query_config=request.query_config,
        organization_id=organization_id,
    )

    # Extract column names from the first result
    columns = list(results[0].keys()) if results else []
    return QueryResponse(
        data=results,
        columns=columns,
    )
