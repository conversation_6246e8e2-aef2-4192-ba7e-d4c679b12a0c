from typing import Any, Self
from uuid import UUID

from pydantic import BaseModel, model_validator

from salestech_be.common.exception.exception import InvalidArgumentError
from salestech_be.core.reporting.type.filter import Filter
from salestech_be.core.reporting.type.query_config import QueryConfig


class QueryResponse(BaseModel):
    data: list[dict[str, Any]]
    columns: list[str]


class QueryPreviewRequest(BaseModel):
    query_config: QueryConfig


class EngineQueryRequest(BaseModel):
    """Request for querying a dataset or report with optional filters"""

    dataset_id: UUID | None = None
    report_id: UUID | None = None
    filters: list[Filter] | None = None

    class Config:
        json_schema_extra = {
            "example": {
                "dataset_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                "filters": [
                    {"col": "status", "op": "=", "val": "active"},
                    {"col": "created_at", "op": ">", "val": "2023-01-01T00:00:00Z"},
                ],
            }
        }

    @model_validator(mode="after")
    def validate_request(self) -> Self:
        if self.dataset_id is None and self.report_id is None:
            raise InvalidArgumentError("Either dataset_id or report_id must be provided")
        if self.dataset_id is not None and self.report_id is not None:
            raise InvalidArgumentError(
                "Only one of dataset_id or report_id should be provided, not both"
            )
        return self
