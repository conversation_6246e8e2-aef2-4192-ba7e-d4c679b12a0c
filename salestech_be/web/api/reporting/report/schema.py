from typing import List, Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.core.reporting.type.layout import ReportLayoutConfig


class ReportResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str] = None
    dashboard_id: UUID
    dataset_id: UUID
    created_at: str
    updated_at: Optional[str] = None
    layout_config: Optional[ReportLayoutConfig] = None


class ReportDetailResponse(ReportResponse):
    pass


class ReportListResponse(BaseModel):
    reports: List[ReportResponse]
    total: int
    page: int
    page_size: int


class CreateReportRequest(BaseModel):
    name: str
    description: Optional[str] = None
    dashboard_id: UUID
    dataset_id: UUID
    layout_config: Optional[ReportLayoutConfig] = None


class UpdateReportRequest(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    layout_config: Optional[ReportLayoutConfig] = None


class ReportQueryRequest(BaseModel):
    filters: Optional[Dict[str, Any]] = None
