from typing import Annotated
from uuid import UUID

from fastapi import Depends, HTTPException, Query

from salestech_be.common.lifespan import get_db_engine
from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.reporting import ReportingReport
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import require_read_placeholder_access
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.reporting.report.schema import (
    CreateReportRequest,
    ReportDetailResponse,
    ReportListResponse,
    ReportResponse,
    UpdateReportRequest,
)
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserId,
)

router = ReeAPIRouter()
logger = get_logger("reporting.report")


async def get_reporting_repository(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> ReportingRepository:
    return ReportingRepository(engine=db_engine)


@router.get(
    "",
    response_model=ReportListResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def list_reports(
    organization_id: AnnotatedReevoOrganizationId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
) -> ReportListResponse:
    """List all reports for an organization."""
    reports = await reporting_repository.get_reports()

    # Apply pagination
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    paginated_reports = reports[start_idx:end_idx]

    return ReportListResponse(
        reports=[
            ReportResponse(
                id=report.id,
                name=report.name,
                description=report.description,
                dashboard_id=report.dashboard_id,
                dataset_id=report.dataset_id,
                created_at=report.created_at.isoformat(),
                updated_at=report.updated_at.isoformat() if report.updated_at else None,
                layout_config=report.layout_config,
            )
            for report in paginated_reports
        ],
        total=len(reports),
        page=page,
        page_size=page_size,
    )


@router.get(
    "/{report_id}",
    response_model=ReportDetailResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def get_report(
    report_id: UUID,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
) -> ReportDetailResponse:
    """Get a report by ID."""
    report = await reporting_repository.find_report_by_id(report_id, organization_id)
    if not report:
        raise HTTPException(status_code=404, detail="Report not found")

    return ReportDetailResponse(
        id=report.id,
        name=report.name,
        description=report.description,
        dashboard_id=report.dashboard_id,
        dataset_id=report.dataset_id,
        created_at=report.created_at.isoformat(),
        updated_at=report.updated_at.isoformat() if report.updated_at else None,
        layout_config=report.layout_config,
    )


@router.post(
    "",
    response_model=ReportDetailResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def create_report(
    request: CreateReportRequest,
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
) -> ReportDetailResponse:
    """Create a new report."""
    # Validate that the dashboard exists
    # TODO: Add validation for dashboard_id

    # Validate that the dataset exists
    dataset = await reporting_repository.find_dataset_by_id(request.dataset_id, organization_id)
    if not dataset:
        raise HTTPException(status_code=400, detail="Dataset not found")

    # Create the report
    now = zoned_utc_now()
    report = ReportingReport(
        id=UUID(int=0),  # Will be replaced by the database
        name=request.name,
        description=request.description,
        dashboard_id=request.dashboard_id,
        dataset_id=request.dataset_id,
        layout_config=request.layout_config,
        organization_id=organization_id,
        created_at=now,
        created_by_user_id=user_id,
    )

    created_report = await reporting_repository.insert(report)

    return ReportDetailResponse(
        id=created_report.id,
        name=created_report.name,
        description=created_report.description,
        dashboard_id=created_report.dashboard_id,
        dataset_id=created_report.dataset_id,
        created_at=created_report.created_at.isoformat(),
        updated_at=created_report.updated_at.isoformat() if created_report.updated_at else None,
        layout_config=created_report.layout_config,
    )


@router.put(
    "/{report_id}",
    response_model=ReportDetailResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def update_report(
    report_id: UUID,
    request: UpdateReportRequest,
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
) -> ReportDetailResponse:
    """Update an existing report."""
    # Get the existing report
    report = await reporting_repository.find_report_by_id(report_id, organization_id)
    if not report:
        raise HTTPException(status_code=404, detail="Report not found")

    # Update the report fields
    if request.name is not None:
        report.name = request.name
    if request.description is not None:
        report.description = request.description
    if request.layout_config is not None:
        report.layout_config = request.layout_config

    # Update the audit fields
    report.updated_at = zoned_utc_now()
    report.updated_by_user_id = user_id

    # Save the updated report
    updated_report = await reporting_repository.update(report)

    return ReportDetailResponse(
        id=updated_report.id,
        name=updated_report.name,
        description=updated_report.description,
        dashboard_id=updated_report.dashboard_id,
        dataset_id=updated_report.dataset_id,
        created_at=updated_report.created_at.isoformat(),
        updated_at=updated_report.updated_at.isoformat() if updated_report.updated_at else None,
        layout_config=updated_report.layout_config,
    )
