from typing import Any
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.core.reporting.type.filter import Filter
from salestech_be.core.reporting.type.query_config import QueryConfig


class FunctionResponse(BaseModel):
    id: UUID
    func_name: str
    display_name: str
    version: int
    return_type: str
    example: str | None = None
    description: str | None = None
    is_active: bool = True


class FunctionParameterResponse(BaseModel):
    param_order: int
    param_type: str
    is_required: bool = False
    options: dict[str, Any] | None = None


class FunctionDetailResponse(FunctionResponse):
    parameters: list[FunctionParameterResponse] = Field(default_factory=list)


class FunctionListResponse(BaseModel):
    functions: list[FunctionResponse]
    total: int
    page: int
    page_size: int
