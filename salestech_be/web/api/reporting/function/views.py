from typing import Annotated
from uuid import UUID

from fastapi import Depends, HTTPException, Query

from salestech_be.common.lifespan import get_db_engine
from salestech_be.core.reporting.connection.materialize_connection import (
    AsyncMaterializeConnection,
)
from salestech_be.core.reporting.service.query_execution_service import (
    QueryExecutionService,
)
from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import require_read_placeholder_access
from salestech_be.web.api.reporting.engine.schema import (
    EngineQueryRequest,
    FunctionDetailResponse,
    FunctionListResponse,
    FunctionParameterResponse,
    FunctionResponse,
    QueryPreviewRequest,
    QueryResponse,
)
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
)

router = ReeAPIRouter()
logger = get_logger("reporting.engine")


async def get_reporting_repository(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> ReportingRepository:
    return ReportingRepository(engine=db_engine)


async def get_materialize_connection() -> AsyncMaterializeConnection:
    """Get a connection to the Materialize database using settings."""
    from salestech_be.settings import settings

    connection = AsyncMaterializeConnection(
        host=settings.materialize_host,
        port=settings.materialize_port,
        user=settings.materialize_user,
        password=settings.materialize_pass,
        database=settings.materialize_base,
        ssl=True,
    )
    await connection.connect()
    return connection


async def get_query_execution_service(
    materialize_connection: Annotated[
        AsyncMaterializeConnection, Depends(get_materialize_connection)
    ],
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> QueryExecutionService:
    return QueryExecutionService(
        db_engine=db_engine,
        dwh_connection=materialize_connection,
    )


@router.post(
    "/query",
    response_model=QueryResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def query_engine(
    request: EngineQueryRequest,
    organization_id: AnnotatedReevoOrganizationId,
    query_execution_service: Annotated[
        QueryExecutionService, Depends(get_query_execution_service)
    ],
) -> QueryResponse:
    """
    Query a dataset or report with optional filters.

    This endpoint can be used in the following ways:
    1. dataset_id only: Load dataset and execute query
    2. dataset_id + filters: Load dataset, merge filters, and execute query
    3. report_id only: Load report, load dataset, and execute query
    4. report_id + filters: Load report, load dataset, merge filters, and execute query

    Either dataset_id or report_id must be provided, but not both.
    """
    try:
        logger.info(f"Query engine request: {request}")

        # Validate request
        if request.dataset_id is None and request.report_id is None:
            raise HTTPException(
                status_code=400,
                detail="Either dataset_id or report_id must be provided",
            )

        if request.dataset_id is not None and request.report_id is not None:
            raise HTTPException(
                status_code=400,
                detail="Only one of dataset_id or report_id should be provided, not both",
            )

        # Execute query based on request type
        if request.dataset_id is not None:
            # Dataset query
            results = await query_execution_service.execute_dataset(
                dataset_id=request.dataset_id, filters=request.filters
            )
        else:
            # Report query
            results = await query_execution_service.execute_report(
                report_id=request.report_id, filters=request.filters
            )

        # Extract column names from the first result
        columns = list(results[0].keys()) if results else []

        return QueryResponse(
            data=results,
            columns=columns,
        )
    except Exception as e:
        logger.error(f"Error in query engine: {e!s}")
        raise HTTPException(status_code=500, detail=f"Error executing query: {e!s}")


@router.post(
    "/preview",
    response_model=QueryResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def preview_query(
    request: QueryPreviewRequest,
    organization_id: AnnotatedReevoOrganizationId,
    query_execution_service: Annotated[
        QueryExecutionService, Depends(get_query_execution_service)
    ],
) -> QueryResponse:
    """
    Preview a query from a raw query config.

    This endpoint is used when creating a new report to preview the results
    before saving. It takes a QueryConfig directly and executes it.
    """
    try:
        logger.info(f"Previewing query for organization {organization_id}")
        results = await query_execution_service.execute_query(request.query_config)

        # Extract column names from the first result
        columns = list(results[0].keys()) if results else []

        return QueryResponse(
            data=results,
            columns=columns,
        )
    except Exception as e:
        logger.error(f"Error previewing query: {e!s}")
        raise HTTPException(status_code=500, detail=f"Error previewing query: {e!s}")


@router.get(
    "/functions",
    response_model=FunctionListResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def list_functions(
    reporting_repository: Annotated[
        ReportingRepository, Depends(get_reporting_repository)
    ],
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    is_active: bool = True,
) -> FunctionListResponse:
    """List all available functions."""
    functions = await reporting_repository.get_functions(is_active)

    # Apply pagination
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    paginated_functions = functions[start_idx:end_idx]

    return FunctionListResponse(
        functions=[
            FunctionResponse(
                id=function.id,
                func_name=function.func_name,
                display_name=function.display_name,
                version=function.version,
                return_type=function.return_type,
                example=function.example,
                description=function.description,
                is_active=function.is_active,
            )
            for function in paginated_functions
        ],
        total=len(functions),
        page=page,
        page_size=page_size,
    )


@router.get(
    "/functions/{function_id}",
    response_model=FunctionDetailResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def get_function(
    function_id: UUID,
    reporting_repository: Annotated[
        ReportingRepository, Depends(get_reporting_repository)
    ],
) -> FunctionDetailResponse:
    """Get a function by ID with its parameters."""
    function = await reporting_repository.get_function_by_id(function_id)
    if not function:
        raise HTTPException(status_code=404, detail="Function not found")

    parameters = await reporting_repository.get_function_parameters(function_id)

    return FunctionDetailResponse(
        id=function.id,
        func_name=function.func_name,
        display_name=function.display_name,
        version=function.version,
        return_type=function.return_type,
        example=function.example,
        description=function.description,
        is_active=function.is_active,
        parameters=[
            FunctionParameterResponse(
                param_order=param.param_order,
                param_type=param.param_type,
                is_required=param.is_required,
                options=param.options,
            )
            for param in parameters
        ],
    )
