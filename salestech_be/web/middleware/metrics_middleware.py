import contextlib
import json
import time
from http import HTTPStatus
from typing import Any, Final
from uuid import uuid4

from starlette.datastructures import Headers
from starlette.types import ASGIApp, Message, Receive, Scope, Send

from salestech_be.common.stats.metric import ReeMetrics
from salestech_be.ree_logging import get_logger

logger = get_logger()

SLOW_RESPONSE_THRESHOLD_MS: Final[int] = 5000


class MetricsMiddleware:
    def __init__(self, app: ASGIApp, metrics: ReeMetrics) -> None:
        self.app = app
        self.metrics = metrics

    async def _emit_metrics_and_log(  # type: ignore[explicit-any]
        self,
        request_method: str | None,
        request_path: str | None,
        request_body: Any,
        referer: str | None,
        start_time: float,
        status_code: int | None,
        authorization_bearer_partial: str,
        exception: Exception | None = None,
    ) -> None:
        try:
            method_tag = f"method:{request_method}"
            status_tag = f"status:{status_code}"
            path_tag = f"path:{request_path}"
            referer_tag = f"referer:{referer}"
            tags = [method_tag, status_tag, path_tag, referer_tag]
            process_time = (time.perf_counter() - start_time) * 1000

            # Log bad request
            if status_code is not None and status_code >= HTTPStatus.BAD_REQUEST:
                logger.warning(
                    "API request failed",
                    status_code=status_code,
                    process_time=process_time,
                    request_body=request_body,
                    authorization_bearer_partial=authorization_bearer_partial,
                    exception=exception,
                )

            # Log slow request
            if process_time > SLOW_RESPONSE_THRESHOLD_MS:
                logger.warning(
                    "API request slow",
                    status_code=status_code,
                    process_time=process_time,
                    request_body=request_body,
                )

            # Record the processing time metric
            timing_metric_name = "api_timing"
            self.metrics.timing(timing_metric_name, process_time, tags=tags)

            # Increment the count of responses
            status_metric_name = "api_status"
            self.metrics.increment(status_metric_name, tags=tags)

        except Exception:
            # survive all exceptions to preserve response
            logger.exception("Exception while emitting metrics")

    def get_request_body_json(  # type: ignore[explicit-any]
        self, request_body_bytearray: bytearray
    ) -> Any:
        with contextlib.suppress(Exception):
            return json.loads(bytes(request_body_bytearray).decode())

    async def __call__(self, scope: Scope, receive: Receive, send: Send) -> None:
        # only metrics http/https requests
        # skip other scopes type e.g websocket/lifespan/other-user-defined
        scope_type = scope.get("type")
        if scope_type != "http":
            await self.app(scope, receive, send)
            return

        start_time = time.perf_counter()
        headers = Headers(scope=scope)
        method = scope.get("method", "")
        path = scope.get("path")
        user_id = headers.get("x-reevo-user-id")
        organization_id = headers.get("x-reevo-org-id")
        content_type = headers.get("content-type")
        referer = headers.get("referer")
        # use natural_start_time_ms to pass down for logging
        # as logging time is async populated, we can't use perf_counter in real time.
        natural_start_time_ms = time.time() * 1000
        # "Authorization: Bearer <token>"
        # split(" ")[-1] -> <token>
        # split(".", 3)[0:2] -> [<header>, <payload>] - the truncated part is the signature (DO NOT LOG THIS).
        # ".".join() -> "<header>.<payload>"
        authorization_bearer_partial = ".".join(
            headers.get("authorization", "").split(" ")[-1].split(".", 3)[0:2]
        )

        _receive: Receive = receive
        request_body_bytearray = bytearray()

        if content_type and "application/json" in content_type:

            async def _receive() -> Message:
                nonlocal receive, request_body_bytearray
                message = await receive()
                if message["type"] == "http.request":
                    request_body_bytearray.extend(message.get("body", b""))
                return message

        response_status: int | None = None

        async def _send(message: Message) -> None:
            nonlocal response_status, send

            if message["type"] == "http.response.start":
                response_status = message["status"]
            await send(message)

        logger.debug(
            "API request received",
            method=method,
            path=path,
            user_id=user_id,
            organization_id=organization_id,
        )

        with logger.contextualize(
            trace_id=str(uuid4()),
            span_id=str(uuid4())[:16],
            custom_trace=uuid4(),
            method=method,
            path=path,
            user_id=user_id,
            organization_id=organization_id,
            request_start_time_ms=natural_start_time_ms,
        ):
            try:
                logger.debug("Request starts")
                await self.app(scope, _receive, _send)
                await self._emit_metrics_and_log(
                    request_method=method,
                    request_path=path,
                    request_body=self.get_request_body_json(request_body_bytearray),
                    referer=referer,
                    start_time=start_time,
                    status_code=response_status,
                    authorization_bearer_partial=authorization_bearer_partial,
                )
            except Exception as e:
                # Don't have to log uncaught exception here
                # uvicorn.error log will capture it.
                await self._emit_metrics_and_log(
                    request_method=method,
                    request_path=path,
                    request_body=self.get_request_body_json(request_body_bytearray),
                    referer=referer,
                    start_time=start_time,
                    status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
                    authorization_bearer_partial=authorization_bearer_partial,
                    exception=e,
                )
                raise
            finally:
                logger.debug("Request ends")
