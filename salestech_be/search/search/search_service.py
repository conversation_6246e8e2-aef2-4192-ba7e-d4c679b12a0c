from collections.abc import Sequence
from typing import Any, TypeVar
from uuid import UUID

from elastic_transport import ObjectApiResponse
from elasticsearch_dsl.types import InnerHits

from salestech_be.common.ree_voyage import get_sync_voyage_client
from salestech_be.common.singleton import Singleton
from salestech_be.ree_logging import get_logger
from salestech_be.search.common.type import DocumentType
from salestech_be.search.es.indexing.mapping.common import (
    Indexable,
    get_instrumented_es_nested_fields,
)
from salestech_be.search.es.search.client import AsyncElasticsearchSearchClient
from salestech_be.search.indexing.converter import (
    doc_type_to_indexable_cls,
    doc_type_to_read_alias,
)
from salestech_be.search.search.query_models import (
    BaseQueryModel,
    BoolQueryModel,
    ExistsQueryModel,
    KnnQueryModel,
    MatchQueryModel,
    MultiMatchQueryModel,
    NQQueryModel,
)
from salestech_be.search.search.service_api_schema import (
    EmbeddingEnabledNestedQueryField,
    FilterInput,
    FilterOperator,
    FullTextSearchResponse,
    SearchRequest,
)
from salestech_be.search.search.service_api_schema_legacy import (
    InnerHitsResult,
    SearchHitLegacy,
    SearchResponseLegacy,
    SentencesEmbeddingInnerHit,
    TranscriptionQueryOpts,
)
from salestech_be.settings import settings

logger = get_logger(__name__)

DEFAULT_MUST_NOT_FIELDS = {"archived_at", "deleted_at", "merged_at"}

EMBEDDING_ENABLED_NESTED_QUERY_FIELDS = {
    field.value for field in EmbeddingEnabledNestedQueryField
}

ALL_INDEXABLE_CLASSES: list[type[Indexable]] = [
    doc_type_to_indexable_cls(doc_type) for doc_type in DocumentType
]


_DEFAULT_WILDCARD_QUERY_FIELDS = ["*"]

T = TypeVar("T", bound=SearchHitLegacy)


def build_transcription_nested_query(
    query: str,
    query_embedding: Sequence[float],
    transcription_query_opts: TranscriptionQueryOpts,
) -> NQQueryModel:
    return NQQueryModel(
        path="sentences_embedding",
        query=BoolQueryModel(
            should=[
                *(
                    [
                        MatchQueryModel(
                            field="sentences_embedding.text",
                            query=query,
                        )
                    ]
                    if transcription_query_opts.use_match_query
                    else []
                ),
                *(
                    [
                        KnnQueryModel(
                            field="sentences_embedding.vector",
                            vector=list(query_embedding),
                            k=transcription_query_opts.knn_top_k,
                            num_candidates=transcription_query_opts.knn_num_candidates,
                        )
                    ]
                    if transcription_query_opts.use_knn_query
                    else []
                ),
            ]
        ),
        inner_hits=InnerHits(
            _source={"includes": ["sentences_embedding.text"]},
            size=transcription_query_opts.inner_hits_size,
        ),
        # such that we don't get an error if the nested field is not present in some of the indices
        ignore_unmapped=True,
    )


EMBEDDING_ENABLED_NESTED_QUERY_MODEL_FACTORIES = {
    EmbeddingEnabledNestedQueryField.SENTENCES_EMBEDDING: build_transcription_nested_query,
}


class SearchService:
    def __init__(self, search_client: AsyncElasticsearchSearchClient) -> None:
        self.search_client = search_client

    async def validate_search_request(self, search_request: SearchRequest) -> None:
        if search_request.search_after:
            if search_request.sort is None:
                raise ValueError("search_after requires a sort")
            if not len(search_request.search_after) == len(search_request.sort):
                raise ValueError("search_after and sort must have the same length")

    async def search_v0(  # noqa: C901, PLR0912, PLR0915
        self,
        organization_id: UUID,
        search_request: SearchRequest,
        transcription_query_options: TranscriptionQueryOpts | None = None,
    ) -> SearchResponseLegacy[SearchHitLegacy]:
        """
        Execute a search request against Elasticsearch.

        Args:
            organization_id: The organization ID to restrict results to
            search_request: The search request containing query and filters
            transcription_query_options: Options for how transcription inner document queries should be executed

        Returns:
            SearchResponse containing the search hits and total count
        """
        filters = search_request.filters
        indexes = search_request.indexes
        included_fields = search_request.included_fields
        query = search_request.query
        search_request.sort = search_request.sort or [{"_score": "desc"}, {"id": "asc"}]

        await self.validate_search_request(search_request)

        # Add org_id filter to restrict results to organization
        filters.append(
            FilterInput(
                field="organization_id",
                value=str(organization_id),
                operator=FilterOperator.EQUALS,
            )
        )

        # Map index enums to concrete names or use wildcard
        index_names = (
            [doc_type_to_read_alias(idx) for idx in indexes] if indexes else ["*"]
        )
        indexable_clses: list[type[Indexable]] = (
            [doc_type_to_indexable_cls(idx) for idx in indexes]
            if indexes
            else ALL_INDEXABLE_CLASSES
        )
        # sort here just so that the order of the indexable classes is deterministic when we start to append queries
        indexable_clses.sort(key=lambda cls: cls.__name__)

        nested_queries: list[NQQueryModel] = []

        if query is not None and included_fields:
            embedding_enabled_nested_query_fields = [
                field
                for field in included_fields
                if field in EMBEDDING_ENABLED_NESTED_QUERY_FIELDS
            ]
            # only do embedding if needed, most of time `included_fields` will be empty
            # We'll search all nested fields if no included_fields are specified
            # If included_fields are specified, we'll only build nested queries for matching nested fields
            if embedding_enabled_nested_query_fields and query is not None:
                voyage = get_sync_voyage_client()
                query_embedding = voyage.embed(
                    texts=[query],
                    model=settings.voyage_text_model,
                    input_type="query",
                ).embeddings[0]
                for field in embedding_enabled_nested_query_fields:
                    match field:
                        case EmbeddingEnabledNestedQueryField.SENTENCES_EMBEDDING:
                            factory = EMBEDDING_ENABLED_NESTED_QUERY_MODEL_FACTORIES[
                                field
                            ]
                            nested_queries.append(
                                factory(
                                    query,
                                    query_embedding,
                                    transcription_query_options
                                    or TranscriptionQueryOpts(),
                                )
                            )
                            included_fields.append("sentences_embedding.text")
                        case _:
                            raise ValueError(f"Unsupported nested query field: {field}")

        logger.bind(
            nested_queries=[
                nq.model_dump(exclude={"__all__": {"vector": True}})
                for nq in nested_queries
            ]
        ).info("nested_queries")

        # Remove nested query fields we expanded above from included_fields
        cleaned_fields = [*_DEFAULT_WILDCARD_QUERY_FIELDS]
        if included_fields:
            cleaned_fields = [
                field
                for field in included_fields
                if field not in EMBEDDING_ENABLED_NESTED_QUERY_FIELDS
            ]

        logger.info("cleaned_fields", cleaned_fields=cleaned_fields)

        # Add must_not fields that are not in included_fields
        must_not_fields = list(DEFAULT_MUST_NOT_FIELDS - set(included_fields or []))

        # create a superset of all nested fields that are not embedding enabled
        fulltext_search_nested_fields: set[str] = {
            field_name
            for indexable_cls in indexable_clses
            for field_name in get_instrumented_es_nested_fields(indexable_cls)
            if (
                (field_name not in EMBEDDING_ENABLED_NESTED_QUERY_FIELDS)
                and (
                    cleaned_fields == _DEFAULT_WILDCARD_QUERY_FIELDS
                    or field_name in cleaned_fields
                )
            )
        }

        # build a map of nested path to nested filters
        nested_path_to_nested_filters: dict[str, list[FilterInput]] = {}
        for fulltext_search_nested_field in fulltext_search_nested_fields:
            nested_path_to_nested_filters[fulltext_search_nested_field] = [
                f
                for f in filters
                if f.field.split(".")[0] in fulltext_search_nested_field
            ]

        # Translate all the nested filters since both cases need them
        nested_filters: list[NQQueryModel] = []
        for nested_path, filter_list in nested_path_to_nested_filters.items():
            for filter_ in filter_list:
                nested_filter = NQQueryModel(
                    path=nested_path,
                    query=filter_.to_es_model(),
                    ignore_unmapped=True,
                )
                nested_filters.append(nested_filter)

        # Grab all the filters that are not nested
        all_filters: list[BaseQueryModel] = [
            filter_.to_es_model()
            for filter_ in filters
            if filter_.field.split(".")[0] not in [f.path for f in nested_filters]
        ]
        all_filters.extend(nested_filters)

        # If query is None, only use filters
        if query is None:
            bool_query = BoolQueryModel(
                filter=all_filters,
                must_not=[ExistsQueryModel(field=field) for field in must_not_fields],
            )
            logger.info("bool_query (filters only)", bool_query=bool_query)
        else:
            # Existing logic for when query is not None
            match_query = MultiMatchQueryModel(
                query=query,
                fields=cleaned_fields,
                type="best_fields",
                lenient=True,
            )
            logger.info("match_query", match_query=match_query)

            for fulltext_search_nested_field in fulltext_search_nested_fields:
                nested_queries.append(
                    NQQueryModel(
                        path=fulltext_search_nested_field,
                        query=MultiMatchQueryModel(
                            query=query,
                            fields=[f"{fulltext_search_nested_field}.*"],
                            type="best_fields",
                            lenient=True,
                        ),
                        ignore_unmapped=True,
                    )
                )

            try:
                bool_query = BoolQueryModel(
                    filter=all_filters,
                    should=[match_query, *nested_queries],
                    must_not=[
                        ExistsQueryModel(field=field) for field in must_not_fields
                    ],
                    minimum_should_match=1,
                )
                logger.info("bool_query", bool_query=bool_query)
            except Exception:
                logger.exception("Error building bool_query")
                raise

        # Execute search
        _error: Exception | None = None
        _query_body: dict[str, Any] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
        _results: ObjectApiResponse[Any] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
        try:
            _query_body = {
                "query": bool_query.to_query().to_dict(),
                "size": search_request.size or settings.es_max_search_results_size,
                # For cursor pagination, we need a consistent sort order
                "sort": search_request.sort,
                "_source": {"excludes": settings.es_search_results_exclude_fields},
                **(
                    {"search_after": search_request.search_after}
                    if search_request.search_after
                    else {}
                ),
            }
            _results = await self.search_client.options(
                request_timeout=search_request.timeout_secs
            ).search(
                index=index_names,
                body=_query_body,
            )
        except Exception as e:
            _error = e
            logger.exception("Error executing search")
            raise
        finally:
            logger.info(
                "executed search request",
                index_names=index_names,
                query_body=_query_body,
                succeeded=_error is None,
            )
            logger.bind(_results=_results).debug("Search results")

        if _results.get("error"):
            logger.warning(
                "Elasticsearch error",
                error=_results.get("error", {}).get("reason"),
            )

        return SearchResponseLegacy(
            hits=[
                SearchHitLegacy(
                    id=UUID(hit["_id"]),
                    score=hit["_score"],
                    source=hit["_source"],
                    inner_hits=None
                    if hit.get("inner_hits") is None
                    else self._to_inner_hits_result(hit["inner_hits"]),
                    sort=hit.get("sort"),
                )
                for hit in _results["hits"]["hits"]
            ],
            total=_results["hits"]["total"]["value"],
            last_sort=_results["hits"]["hits"][-1]["sort"]
            if _results["hits"]["hits"]
            else None,
        )

    def _to_inner_hits_result(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, inner_hits_dict: dict[str, Any]
    ) -> InnerHitsResult | None:
        if "sentences_embedding" in inner_hits_dict:
            return InnerHitsResult(
                total=inner_hits_dict["sentences_embedding"]["hits"]["total"]["value"],
                hits=[
                    SentencesEmbeddingInnerHit(
                        score=hit["_score"],
                        source_text=hit["_source"]["text"],
                    )
                    for hit in inner_hits_dict["sentences_embedding"]["hits"]["hits"]
                ],
            )

        logger.bind(inner_hits=inner_hits_dict).info(
            "Encountered unsupported inner_hits type"
        )
        return None

    async def fulltext_search(
        self,
        organization_id: UUID,
        search_request: SearchRequest,
    ) -> FullTextSearchResponse:
        """
        Execute a fulltext search request against Elasticsearch.

        Under the hood, this will execute a search request for each document type,
        and then merge the results into a single response grouped by document type.

        In terms of execution sequence, following steps are performed:
        1. Based on requested fields and indices (document_type) in the search request,
           build a group of search queries that focus on specific fields and indices.
        2. For each built search query, wrap it with a bool query that includes
           the search query and a filter that restricts the results to the
           organization_id, also with a filter that excludes deleted / archived / merged documents.
        3. Execute all built search queries in parallel via Elasticsearch's multi search API.
            see: https://www.elastic.co/guide/en/elasticsearch/reference/current/search-multi-search.html
        4. Merge the results into a single response grouped by document type then return.
        Args:
            organization_id: The organization ID to restrict results to
            search_request: The search request containing query and filters
        """
        raise NotImplementedError


class SingletonSearchService(Singleton, SearchService):
    pass


def get_search_service(search_client: AsyncElasticsearchSearchClient) -> SearchService:
    return SingletonSearchService(search_client=search_client)
