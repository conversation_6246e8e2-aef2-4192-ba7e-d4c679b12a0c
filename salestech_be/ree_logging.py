import asyncio
import dataclasses
import logging  # noqa: TID251
import os
import sys
import types
from collections.abc import Callable, Mapping
from enum import StrEnum
from functools import wraps
from typing import (
    Any,
    ParamSpec,
    TypeVar,
    cast,
)
from uuid import UUID

import orj<PERSON>
from loguru import logger  # noqa: TID251
from loguru._logger import Logger  # noqa: TID251
from loguru._recattrs import RecordException
from opentelemetry.sdk.trace import RandomIdGenerator
from opentelemetry.trace import INVALID_SPAN, INVALID_SPAN_CONTEXT, get_current_span
from pydantic import BaseModel

from salestech_be.logging_utils.traceback_utils import ExceptionDictTransformer
from salestech_be.settings import settings

LogRecord = dict[str, Any]  # type: ignore[explicit-any] # TODO: fix-any-annotation

__all__ = [
    "Level",
    "Logger",
    "get_logger",
    "get_system_log_level",
    "get_system_log_level_name",
    "log_context",
    "patch_gunicorn_logger",
    "patch_litellm_logger",
    "patch_uvicorn_logger",
]

exception_transformer = ExceptionDictTransformer(show_locals=False)


# Only log failed health checks
class HealthCheckFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        return '"GET /api/v1/monitoring/health HTTP/1.1" 200' not in record.getMessage()


def is_in_k8s() -> bool:
    return os.getenv("KUBERNETES_SERVICE_HOST") is not None


def is_singular_safe_serializable(v: object) -> bool:
    if isinstance(v, StrEnum):
        return True
    return isinstance(v, str | int | float | bool | types.NoneType | UUID)


def to_safe_serializable(v: object) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation  # noqa: PLR0911
    if isinstance(v, StrEnum):
        return v.value
    if isinstance(v, BaseModel):
        try:
            return v.model_dump(mode="json")
        except Exception:
            return str(v)
    if dataclasses.is_dataclass(v) and not isinstance(v, type):
        try:
            return dataclasses.asdict(v)
        except Exception:
            # pass down to orjson to serialize dataclass with
            # slightly less readable format
            return v
    if is_singular_safe_serializable(v):
        return v
    if isinstance(v, list | tuple | set):
        return [to_safe_serializable(i) for i in v]
    if isinstance(v, dict):
        return {str(k): to_safe_serializable(v) for k, v in v.items()}
    return str(v)


# "vcr": any log message containing `{}` plus extra params will cause loguru to fail
# see https://github.com/Delgan/loguru/issues/318
# "temporalio": worker taking tasks with pydantic models will cause loguru to enter an infinite stack
EXCLUDED_PACKAGES = {"aiokafka", "vcr", "temporalio"}


class InterceptHandler(logging.Handler):
    """Default handler from examples in loguru documentation.

    This handler intercepts all log requests and
    passes them to loguru.

    For more info see:
    https://loguru.readthedocs.io/en/stable/overview.html#entirely-compatible-with-standard-logging
    """

    def __init__(self, level: int = logging.NOTSET, _logger: Logger | None = None):
        super().__init__(level)
        self._logger = _logger or logger

    def emit(self, record: logging.LogRecord) -> None:  # pragma: no cover
        """Propagates logs to loguru.

        :param record: record to log.
        """
        if record.name.split(".")[0] in EXCLUDED_PACKAGES:
            return

        try:
            level: str | int = self._logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back  # type: ignore
            depth += 1

        self._logger.opt(depth=depth, exception=record.exc_info).log(
            level,
            record.getMessage(),
            name=record.name,
            _lineno=record.lineno,
            _funcname=record.funcName,
        )


def record_dict_enricher(record: dict[str, Any]) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    # Use OpenTelemetry span if available, otherwise generate IDs
    span = get_current_span()
    id_generator = RandomIdGenerator()

    # Set span_id and trace_id only if not already present in context
    record["extra"]["span_id"] = str(
        record["extra"].get("span_id", id_generator.generate_span_id())
    )
    record["extra"]["trace_id"] = str(
        record["extra"].get("trace_id", id_generator.generate_trace_id())
    )

    # OpenTelemetry spans take precedence over context and generated IDs
    if span != INVALID_SPAN:
        span_context = span.get_span_context()
        if span_context != INVALID_SPAN_CONTEXT:
            record["extra"]["span_id"] = str(format(span_context.span_id, "016x"))
            record["extra"]["trace_id"] = str(format(span_context.trace_id, "032x"))

    record["extra"] = {k: to_safe_serializable(v) for k, v in record["extra"].items()}
    record["extra"]["serialized"] = serialize_record_dict(record)


def serialize_any(val: Any) -> str | dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    try:
        if isinstance(val, BaseModel):
            return val.model_dump(mode="json")
        else:
            return orjson.dumps(val, default=str).decode()
    except Exception:
        return orjson.dumps(val, default=str).decode()


def serialize_record_dict(record: dict[str, Any]) -> str:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    record_extra = {**record["extra"]} if isinstance(record["extra"], dict) else {}
    name = record_extra.pop("name", record["name"])
    line = record_extra.pop("_lineno", record["line"])
    function_name = record_extra.pop("_funcname", record["function"])

    time_stamp = record["time"].timestamp() * 1000

    default_map_to_log = {
        "message": record["message"],
        "name": name,
        "lineno": line,
        "function_name": function_name,
        "level": record["level"].name,
        "timestamp": time_stamp,
        "time": record["time"].isoformat(),
        "thread_id": record["thread"].id,
        "thread_name": record["thread"].name,
        "process_id": record["process"].id,
    }

    if (
        request_start_time_ms := record_extra.get("request_start_time_ms")
    ) and isinstance(request_start_time_ms, float | int):
        # contextualized in https://github.com/ReevoAI/salestech-be/blob/main/salestech_be/web/middleware/metrics_middleware.py#L103-L108
        record_extra["request_elapsed_time_ms"] = time_stamp - request_start_time_ms

    map_to_log = {
        **default_map_to_log,
        "extra": record_extra,
    }
    try:
        if (exception := record.get("exception")) and isinstance(
            exception, RecordException
        ):
            transformed = exception_transformer(
                (
                    exception.type,
                    exception.value,
                    exception.traceback,
                )
            )
            map_to_log["extra"]["exc_stack"] = transformed
        return orjson.dumps(map_to_log, default=serialize_any).decode()
    except Exception:
        return orjson.dumps(default_map_to_log, default=serialize_any).decode()


def get_system_log_level() -> int:
    """Returns the log level threshold for system libraries and dependencies.

    System libraries use the maximum of INFO and configured log level
    to prevent excessive logging from dependencies.
    """
    return max(logging.INFO, logger.level(settings.log_level.value).no)


def get_system_log_level_name() -> str:
    """Returns the log level name for system libraries as a lowercase string.

    This converts the system log level threshold to a string name.
    """
    level_no = get_system_log_level()
    return logging.getLevelName(level_no).lower()


def record_formatter(  # type: ignore[explicit-any] # TODO: fix-any-annotation  # pragma: no cover
    record: dict[str, Any],
) -> str:
    """Formats the record.

    This function formats message
    by adding extra trace information to the record.

    :param record: record information.
    :return: format string.
    """
    name = "{name}" if "name" not in record["extra"] else "{extra[name]}"
    line = "{line}" if "_lineno" not in record["extra"] else "{extra[_lineno]}"
    function = (
        "{function}" if "_funcname" not in record["extra"] else "{extra[_funcname]}"
    )
    if "serialized" not in record["extra"]:
        record["extra"]["serialized"] = ""

    if is_in_k8s():
        log_format = "{extra[serialized]}\n"
    else:
        log_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> "
            "| <level>{level: <8}</level> "
            r"| <level>{message}</level> | <yellow>serialized: {extra[serialized]}</yellow>"
            f"| <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> "
            "| <magenta>trace_id={extra[trace_id]}</magenta> "
            "| <blue>span_id={extra[span_id]}</blue>\n"
        )
        if record["exception"]:
            log_format = f"{log_format}\n{{exception}}"

    return log_format


def configure_loguru() -> Logger:  # pragma: no cover
    """Configures logging."""
    from loguru import logger  # noqa: TID251

    # set logs output, level and format
    logger.remove()

    # Use a simple filter to control log levels by package
    def log_filter(record: LogRecord) -> bool:
        # Allow salestech_be logs at the configured level
        record_name = record["name"]
        if record_name is not None and record_name.startswith("salestech_be"):
            return True

        # For system libraries, use a higher threshold to reduce noise
        system_threshold = max(
            logger.level("INFO").no, logger.level(settings.log_level.value).no
        )
        level_comparison = record["level"].no >= system_threshold
        return cast(bool, level_comparison)  # Ensure bool return type

    logger.add(
        sys.stdout,
        level=settings.log_level.value,
        format=record_formatter,  # type: ignore
        enqueue=settings.enable_loguru_queue_mode,
        catch=True,
        filter=log_filter,  # type: ignore[arg-type]
    )

    return logger.patch(record_dict_enricher)  # type: ignore[return-value,arg-type]


ree_logger = configure_loguru()
intercept_handler = InterceptHandler(_logger=ree_logger)


def patch_logging() -> None:
    """Initialize global logging configuration."""
    # Use the system log level threshold for the root logger
    log_level = get_system_log_level()

    logging.captureWarnings(True)
    logging.basicConfig(handlers=[intercept_handler], level=logging.NOTSET, force=True)

    # Set root logger level
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)


def patch_uvicorn_logger() -> None:
    """Configure uvicorn loggers with our interceptor."""
    # Use the system log level threshold for uvicorn loggers
    log_level = get_system_log_level()

    uvicorn_logger = logging.getLogger("uvicorn")
    uvicorn_logger.handlers = [intercept_handler]
    uvicorn_logger.propagate = False
    uvicorn_logger.setLevel(log_level)

    uvicorn_access_logger = logging.getLogger("uvicorn.access")
    uvicorn_access_logger.handlers = [intercept_handler]
    uvicorn_access_logger.propagate = False
    uvicorn_access_logger.setLevel(log_level)

    uvicorn_error_logger = logging.getLogger("uvicorn.error")
    uvicorn_error_logger.handlers = [intercept_handler]
    uvicorn_error_logger.propagate = False
    uvicorn_error_logger.setLevel(log_level)


def patch_gunicorn_logger() -> None:
    """Configure gunicorn loggers with our interceptor."""
    # Use the system log level threshold for gunicorn loggers
    log_level = get_system_log_level()

    gunicorn_logger = logging.getLogger("gunicorn")
    gunicorn_logger.handlers = [intercept_handler]
    gunicorn_logger.propagate = False
    gunicorn_logger.setLevel(log_level)

    gunicorn_access_logger = logging.getLogger("gunicorn.access")
    gunicorn_access_logger.handlers = [intercept_handler]
    gunicorn_access_logger.propagate = False
    gunicorn_access_logger.setLevel(log_level)

    gunicorn_error_logger = logging.getLogger("gunicorn.error")
    gunicorn_error_logger.handlers = [intercept_handler]
    gunicorn_error_logger.propagate = False
    gunicorn_error_logger.setLevel(log_level)


def patch_litellm_logger() -> None:
    """Configure LiteLLM logger with our interceptor."""
    # Use the system log level threshold for litellm logger
    log_level = get_system_log_level()

    litellm_logger = logging.getLogger("LiteLLM")
    litellm_logger.handlers = [intercept_handler]
    litellm_logger.propagate = False
    litellm_logger.setLevel(log_level)
    os.environ["LITELLM_LOG"] = "CRITICAL"  # Suppress duplicate logs to stdout


patch_logging()
patch_uvicorn_logger()
patch_gunicorn_logger()
patch_litellm_logger()
# Suppress health check logging from uvicorn.access
logging.getLogger("uvicorn.access").addFilter(HealthCheckFilter())


def get_logger(name: str | None = None) -> Logger:
    return ree_logger.bind(name=name) if name else ree_logger


class Level(StrEnum):
    TRACE = ree_logger.level("TRACE").name
    DEBUG = ree_logger.level("DEBUG").name
    INFO = ree_logger.level("INFO").name
    WARN = ree_logger.level("WARNING").name  # alias for WARNING
    WARNING = ree_logger.level("WARNING").name
    ERROR = ree_logger.level("ERROR").name
    CRITICAL = ree_logger.level("CRITICAL").name


R = TypeVar("R")
P = ParamSpec("P")


def _log_context_dict(  # type: ignore[explicit-any] # TODO: fix-any-annotation
    *, extracts: list[str] | None = None, keyw_args: Mapping[str, Any]
) -> dict[str, Any]:
    return {arg: keyw_args.get(arg) for arg in extracts or []}


def log_context(
    *,
    from_kwargs: list[str] | None = None,
) -> Callable[[Callable[P, R]], Callable[P, R]]:
    def log_context_decorator(func: Callable[P, R]) -> Callable[P, R]:
        if not from_kwargs:
            return func

        if asyncio.iscoroutinefunction(func):

            @wraps(func)
            def async_wrapper(*args: P.args, **kwargs: P.kwargs) -> R:
                async def async_run(*args: P.args, **kwargs: P.kwargs) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
                    with ree_logger.contextualize(
                        **_log_context_dict(extracts=from_kwargs, keyw_args=kwargs)
                    ):
                        return await func(*args, **kwargs)

                return cast(R, async_run(*args, **kwargs))

            return async_wrapper
        else:

            @wraps(func)
            def sync_wrapper(*args: P.args, **kwargs: P.kwargs) -> R:
                with ree_logger.contextualize(
                    **_log_context_dict(extracts=from_kwargs, keyw_args=kwargs)
                ):
                    return func(*args, **kwargs)

            return sync_wrapper

    return log_context_decorator
