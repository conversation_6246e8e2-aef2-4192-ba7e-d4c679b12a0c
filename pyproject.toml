[project]
name = "salestech_be"
version = "0.1.0"
description = ""
authors = []
requires-python = ">=3.12,<3.13"
readme = "README.md"
maintainers = []
dependencies = [
    "aiofiles~=24.1.0",
    "aiokafka[lz4]~=0.10.0",
    "alembic~=1.11",
    "anthropic[vertex]~=0.52",
    "anyio~=4.3",
    "APScheduler~=3.10",
    "asgi-lifespan~=2.1",
    "async-lru~=2.0",
    "asyncpg~=0.29.0",
    "auth0-python~=4.7",
    "authlib~=1.3",
    "beautifulsoup4~=4.12.3",
    "boto3~=1.34",
    "botocore~=1.34",
    "cron-validator~=1.0.8",
    "croniter~=2.0",
    "datadog~=0.49.1",
    "dramatiq[redis,watch]~=1.16",
    "elasticsearch[orjson,async]~=8.17.1",
    "elasticsearch-dsl[async]~=8.17.1",
    "email-reply-parser>=0.5.12",
    "email-validator~=2.1.0.post1",
    "en-core-web-sm @ https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.7.0/en_core_web_sm-3.7.0-py3-none-any.whl",
    "falkordb~=1.1.1",
    "fastapi~=0.115",
    "fastapi-users[oauth,sqlalchemy]~=12.1",
    "freezegun (>=1.5.1,<2.0.0)",
    "frozendict~=2.4",
    "google-api-python-client~=2.127",
    "google-apps-meet~=0.1.6",
    "google-genai~=1.15",
    "gunicorn~=22.0",
    "httptools~=0.6.0",
    "httpx~=0.28.1",
    "icalendar~=5.0",
    "Jinja2~=3.1",
    "json-repair<1.0.0",
    "lancedb (>=0.19.0,<0.20.0)",
    "langgraph~=0.2.3",
    "langchain (>=0.3.17,<0.4.0)",
    "langchain-anthropic (>=0.3.5,<0.4.0)",
    "langchain-aws (>=0.2.11,<0.3.0)",
    "langchain-community (>=0.3.16,<0.4.0)",
    "langchain-google-community (>=2.0.4,<3.0.0)",
    "langchain-openai (>=0.3.3,<0.4.0)",
    "langfuse~=2.59",
    "langsmith~=0.1.117",
    "litellm~=1.67",
    "loguru~=0.7.0",
    "markdownify~=0.14.1",
    "memory-profiler (>=0.61.0,<0.62.0)",
    "monday~=1.3.3",
    "nameparser (>=1.1.3,<2.0.0)",
    "networkx~=3.3",
    "numpy==1.26.4",
    "nylas~=6.0",
    "opentelemetry-api~=1.33",
    "opentelemetry-exporter-otlp~=1.33",
    "opentelemetry-instrumentation>=0.54b0,<0.55",
    "opentelemetry-instrumentation-fastapi>=0.54b0,<0.55",
    "opentelemetry-instrumentation-redis>=0.54b0,<0.55",
    "opentelemetry-instrumentation-sqlalchemy>=0.54b0,<0.55",
    "opentelemetry-sdk~=1.33",
    "orjson~=3.9",
    "phonenumbers~=8.13",
    "pillow~=11.1",
    "playwright~=1.46",
    "posthog~=3.8",
    "prometheus-client~=0.17.0",
    "prometheus-fastapi-instrumentator==6.0",
    "pydantic==2.11.4",
    "pydantic-ai~=0.2.6",
    "pydantic-extra-types (>=2.10.2,<3.0.0)",
    "pydantic-settings~=2.0",
    "pydantic-string-url~=1.0",
    "pyinstrument~=4.7",
    "python-dateutil~=2.9.0.post0",
    "python-jose[pycryptodome]~=3.3.0",
    "python-slugify~=8.0.4",
    "pytz~=2024.1",
    "py-spy (>=0.4.0,<0.5.0)",
    "redis[hiredis]>=5.0.1,<6.0.0",
    "requests~=2.31",
    "sendgrid~=6.11",
    "sentry-dramatiq~=0.3.3",
    "sentry-sdk~=2.9",
    "spacy==3.7.5", # spacy 3.8.x causes build issues on Apple Silicon
    "SQLAlchemy[asyncio]~=2.0",
    "starlette~=0.40",
    "svix~=1.24",
    "temporalio (>=1.10.0,<2.0.0)",
    "tldextract~=5.1.2",
    "transformers~=4.41", # Missing dependency used by langchain-core
    "twilio~=9.0",
    "types-aiofiles~=24.1.0.20250326",
    "types-python-dateutil~=2.9.0.20240316",
    "types-pytz~=2024.1",
    "types-requests~=2.31",
    "typing-extensions~=4.11",
    "ujson~=5.8",
    "uvicorn[standard]~=0.26",
    "uvloop~=0.19.0",
    "validators~=0.33.0",
    "voyageai~=0.3.1",
    "w3lib~=2.2.1",
    "whoisit~=3.0.4",
    "yarl~=1.9",
    "zeep~=4.2",
]

[project.scripts]
reevo = "scripts.cli.reevo:main"
touch_latest_alembic_revision = "salestech_be.db.migrations.hook:touch_latest_revision"
askreevo = "scripts.cli.askreevo:main"

[dependency-groups]
dev = [
    "argcomplete>=3.6.0",
    "autoevals>=0.0.127",
    "debugpy>=1.8.5,<2",
    "deepeval>=2.6.6",
    "deepteam>=0.1.0",
    "faker-enum>=0.0.2,<0.0.3",
    "fakeredis>=2.5.0,<3",
    "fastapi-cli>=0.0.3,<0.0.4",
    "icecream>=2.1.3,<3",
    "ipython>=8.20.0,<9",
    "locust>=2.32.5,<3",
    "mypy>=1.15.0",
    "polyfactory>=2.18.0,<3",
    "pre-commit>=3.0.1,<4",
    "prompt-toolkit>=3.0.50",
    "pytest>=7.2.1,<8",
    "pytest-asyncio>=0.23.7,<0.24",
    "pytest-cov>=4.0.0,<5",
    "pytest-env>=0.8.1,<0.9",
    "pytest-mock>=3.14.0,<4",
    "pytest-recording>=0.13.2,<0.14",
    "pytest-split>=0.8.2,<0.9",
    "rich>=13.9.4",
    "ruff~=0.11",
    "tach>=0.26.1",
    "testcontainers~=4.7",
    "types-croniter~=2.0",
    "wiremock>=2.6.1,<3",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.metadata]
allow-direct-references = true

[tool.mypy]
strict = true
ignore_missing_imports = true

# Dynamic typing constraints
disallow_subclassing_any = true
disallow_untyped_calls = true
disallow_untyped_decorators = true
disallow_untyped_defs = true
check_untyped_defs = true
untyped_calls_exclude = ["loguru._logger", "dramatiq", "zeep"]
disallow_any_generics = true
# disallow_any_decorated = true
# disallow_any_expr = true
disallow_any_explicit = true

# Local partial types
# Keep this as true for more rigit type checking, it's by default enabled in mypy 2.0
local_partial_types = true

# None and Optional handling
strict_optional = true

# Warnings
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_return_any = true
warn_unused_configs = true

pretty = true
show_error_codes = true
show_error_context = true
implicit_reexport = true
namespace_packages = true

plugins = ["pydantic.mypy"]
python_version = "3.12"

[[tool.mypy.overrides]]
module = ["redis.asyncio"]
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = ["tests.*"]
disallow_any_explicit = false
disallow_any_decorated = false

[[tool.mypy.overrides]]
module = ["salestech_be.falkordb.*"]
disallow_any_explicit = false

[tool.pydantic-mypy]
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = true

[tool.pytest.ini_options]
filterwarnings = [
    "error",
    "ignore::DeprecationWarning",
    "ignore:.*unclosed.*:ResourceWarning",
    "ignore:.*MemcachedBackend.*:ImportWarning",
    "ignore:.*Valid config keys have changed in V2.*:UserWarning",
    "ignore:.*pandas.*:ImportWarning",
    "ignore:.*scipy.*:ImportWarning",
    "ignore:Module already imported:pytest.PytestAssertRewriteWarning",
]
env = [
    "SALESTECH_BE_ENVIRONMENT=pytest",
    "SALESTECH_BE_DB_BASE=salestech_be",
    "SALESTECH_BE_SENTRY_DSN=",
    "SALESTECH_BE_POSTHOG_DISABLED=True",
    "SALESTECH_BE_SENTRY_TRACES_SAMPLE_RATE=1",
    "USE_STUB_BROKER=1",
]
