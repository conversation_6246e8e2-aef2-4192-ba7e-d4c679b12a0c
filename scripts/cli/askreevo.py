from __future__ import annotations as _annotations

import argparse
import asyncio
import sys
from collections.abc import Sequence
from importlib.metadata import version
from pathlib import Path
from typing import cast
from uuid import UUID

from pydantic_graph.nodes import End

from salestech_be.common.langfuse_otel import langfuse_otel_trace
from salestech_be.common.ree_llm import LLMTraceMetadata
from salestech_be.core.account.service.account_service import get_account_service
from salestech_be.core.chat.agent_deps import ChatAgentDeps
from salestech_be.core.contact.service.contact_service import get_contact_service
from salestech_be.core.meeting.service.meeting_transcript_agent_service import (
    meeting_transcript_agent_service_from_engine,
)
from salestech_be.core.organization.service.organization_service import (
    get_organization_service_general,
)
from salestech_be.core.transcript.transcript_service import (
    transcript_service_from_engine,
)
from salestech_be.core.user.service.user_service import (
    get_user_service_general,
)
from salestech_be.db.dao.task_repository import TaskRepository
from salestech_be.ree_logging import get_logger
from salestech_be.search.es.search.client_utils import get_es_search_client
from salestech_be.search.search.search_service import get_search_service
from salestech_be.temporal.database import get_or_init_db_engine

try:
    import argcomplete
    from prompt_toolkit import PromptSession
    from prompt_toolkit.auto_suggest import AutoSuggestFromHistory, Suggestion
    from prompt_toolkit.buffer import Buffer
    from prompt_toolkit.document import Document
    from prompt_toolkit.history import FileHistory
    from rich.console import Console, ConsoleOptions, RenderResult
    from rich.live import Live
    from rich.markdown import CodeBlock, Markdown
    from rich.status import Status
    from rich.syntax import Syntax
    from rich.text import Text
except ImportError as _import_error:
    raise ImportError(
        "Please install `rich`, `prompt-toolkit` and `argcomplete` to use the PydanticAI CLI, "
        'you can use the `cli` optional group — `pip install "pydantic-ai-slim[cli]"`'
    ) from _import_error

import contextlib

from pydantic_ai.agent import Agent
from pydantic_ai.messages import (
    ModelMessage,
    PartDeltaEvent,
    PartStartEvent,
    TextPartDelta,
)

from salestech_be.core.chat.chat_agent import (
    chat_fallback_models,
    render_system_prompt,
    select_variant,
)

VARIANT_NAME = "claude-sonnet-4"
USER_ID = UUID("3b2f46a7-e4b3-42f1-a7f4-a232eb5dcfaa")
ORG_ID = UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5")

__version__ = version("pydantic-ai")
logger = get_logger(__name__)


class SimpleCodeBlock(CodeBlock):
    def __rich_console__(
        self, console: Console, options: ConsoleOptions
    ) -> RenderResult:  # pragma: no cover
        code = str(self.text).rstrip()
        yield Text(self.lexer_name, style="dim")
        yield Syntax(
            code,
            self.lexer_name,
            theme=self.theme,
            background_color="default",
            word_wrap=True,
        )
        yield Text(f"/{self.lexer_name}", style="dim")


Markdown.elements["fence"] = SimpleCodeBlock


async def init_dependencies() -> ChatAgentDeps:
    db_engine = await get_or_init_db_engine()
    search_service = get_search_service(search_client=get_es_search_client())
    transcript_service = transcript_service_from_engine(engine=db_engine)
    user_service = get_user_service_general(db_engine=db_engine)
    organization_service = get_organization_service_general(db_engine=db_engine)
    multi_meeting_service = meeting_transcript_agent_service_from_engine(db_engine)
    task_repo = TaskRepository(engine=db_engine)
    contact_service = get_contact_service(db_engine=db_engine)
    account_service = get_account_service(db_engine=db_engine)

    user = await user_service.get_user_v2(organization_id=ORG_ID, user_id=USER_ID)
    org = await organization_service.get_organization_by_id(organization_id=ORG_ID)
    if org is None:
        raise ValueError(f"Organization with ID {ORG_ID} not found")

    org_users = await user_service.list_all_users_in_organization(
        organization_id=ORG_ID,
        active_users_only=True,
    )

    logger.bind(user_id=USER_ID, org_id=ORG_ID).info(
        f"Using user {user.display_name} and organization {org.display_name}"
    )

    return ChatAgentDeps(
        organization_id=ORG_ID,
        user_id=USER_ID,
        user=user,
        search_service=search_service,
        transcript_service=transcript_service,
        task_repository=task_repo,
        organization=org,
        org_users=org_users,
        multi_meeting_service=multi_meeting_service,
        contact_service=contact_service,
        account_service=account_service,
        view_contexts=[],
    )


async def async_cli(args_list: Sequence[str] | None = None) -> int:  # noqa: C901, PLR0912, PLR0915
    parser = argparse.ArgumentParser(
        prog="askreevo",
        description=f"""\
Askreevo CLI v{__version__}\n\n

Special prompt:
* `/exit` - exit the interactive mode
* `/markdown` - show the last markdown output of the last question
* `/multiline` - toggle multiline mode
* `/new` - start a new conversation
""",
        formatter_class=argparse.RawTextHelpFormatter,
    )
    parser.add_argument(
        "prompt", nargs="?", help="AI Prompt, if omitted fall into interactive mode"
    )
    parser.add_argument(
        "--no-stream",
        action="store_true",
        help="Whether to stream responses from OpenAI",
    )
    parser.add_argument("--version", action="store_true", help="Show version and exit")

    argcomplete.autocomplete(parser)
    args = parser.parse_args(args_list)

    console = Console()
    console.print(
        f"askreevo - Askreevo CLI v{__version__}", style="green bold", highlight=False
    )
    if args.version:
        return 0

    agent: Agent[ChatAgentDeps, str] = select_variant(VARIANT_NAME)

    stream = not args.no_stream
    deps = await init_dependencies()

    messages: list[ModelMessage] = [render_system_prompt(ctx=deps)]
    if prompt := cast(str, args.prompt):
        with contextlib.suppress(KeyboardInterrupt):
            await ask_agent(agent, prompt, stream, console, deps, messages=messages)
        return 0

    history = Path.home() / ".askreevo-prompt-history.txt"
    session = PromptSession(history=FileHistory(str(history)))  # type: ignore
    multiline = False

    while True:
        try:
            auto_suggest = CustomAutoSuggest(
                ["/markdown", "/multiline", "/exit", "/new"]
            )
            text = cast(
                str,
                await session.prompt_async(
                    "askreevo ➤ ", auto_suggest=auto_suggest, multiline=multiline
                ),
            )
        except (KeyboardInterrupt, EOFError):
            return 0

        if not text.strip():
            continue

        ident_prompt = text.lower().strip(" ").replace(" ", "-").lstrip(" ")
        if ident_prompt == "/new":
            messages = [render_system_prompt(ctx=deps)]
            console.print("[dim]Starting a new conversation…[/dim]")
            continue
        if ident_prompt == "/markdown":
            try:
                parts = messages[-1].parts
            except IndexError:
                console.print("[dim]No markdown output available.[/dim]")
                continue
            for part in parts:
                if part.part_kind == "text":
                    last_content = part.content
                    console.print("[dim]Last markdown output of last question:[/dim]\n")
                    console.print(
                        Syntax(
                            last_content, lexer="markdown", background_color="default"
                        )
                    )

            continue
        if ident_prompt == "/multiline":
            multiline = not multiline
            if multiline:
                console.print(
                    "Enabling multiline mode. "
                    "[dim]Press [Meta+Enter] or [Esc] followed by [Enter] to accept input.[/dim]"
                )
            else:
                console.print("Disabling multiline mode.")
            continue
        if ident_prompt == "/exit":
            console.print("[dim]Exiting…[/dim]")
            return 0

        try:
            messages = await ask_agent(
                agent, text, stream, console, deps, messages=messages
            )
        except KeyboardInterrupt:
            return 0


def cli(args_list: Sequence[str] | None = None) -> int:
    return asyncio.run(async_cli(args_list))


async def ask_agent(  # noqa: C901, PLR0912
    agent: Agent[ChatAgentDeps, str],
    prompt: str,
    stream: bool,
    console: Console,
    deps: ChatAgentDeps,
    messages: list[ModelMessage] | None = None,
) -> list[ModelMessage]:
    status: None | Status = Status("[dim]Working on it…[/dim]", console=console)
    live = Live("", refresh_per_second=15, console=console)
    if not status:
        return []

    status.start()

    deps.num_tool_calls = 0
    trace_metadata = LLMTraceMetadata(
        trace_name="chat-agent-cli",
        user_id=deps.user_id,
        custom_fields={
            "organization_id": str(deps.organization_id),
        },
    )
    with langfuse_otel_trace(trace_metadata=trace_metadata) as parent_span:
        parent_span.set_attribute("input.value", prompt)

        async with agent.iter(
            prompt,
            model=chat_fallback_models(VARIANT_NAME),
            message_history=messages,
            deps=deps,
        ) as agent_run:
            console.print("\nResponse:", style="green")

            content: str = ""
            interrupted = False
            # Count words and letters in message history
            word_count = sum(
                len(str(msg).split()) for msg in agent_run.ctx.state.message_history
            )
            letter_count = sum(
                len(str(msg)) for msg in agent_run.ctx.state.message_history
            )
            console.print(
                f"[dim]Message history stats: {word_count} words, {letter_count} characters[/dim]"
            )
            try:
                node = agent_run.next_node
                while not isinstance(node, End):
                    node = await agent_run.next(node)
                    if Agent.is_model_request_node(node):
                        async with node.stream(agent_run.ctx) as handle_stream:
                            # NOTE(Marcelo): It took me a lot of time to figure out how to stop `status` and start `live`
                            # in a context manager, so I had to do it manually with `stop` and `start` methods.
                            # PR welcome to simplify this code.
                            if status is not None:
                                status.stop()
                                status = None
                            if not live.is_started:
                                live.start()
                            async for event in handle_stream:
                                if (
                                    isinstance(event, PartDeltaEvent)
                                    and isinstance(event.delta, TextPartDelta)
                                    and stream
                                ):
                                    content += event.delta.content_delta
                                    live.update(Markdown(content))
                                elif (
                                    isinstance(event, PartStartEvent)
                                    and event.part.part_kind == "text"
                                    and stream
                                ):
                                    content += event.part.content
                                    live.update(Markdown(content))
                    elif Agent.is_call_tools_node(node) and stream is True:
                        content += "\n\n"
                        live.update(Markdown(content))
            except KeyboardInterrupt:
                interrupted = True
            finally:
                live.stop()

            if interrupted:
                console.print("[dim]Interrupted[/dim]")

            assert agent_run.result  # noqa: S101
            if not stream:
                content = agent_run.result.output
                console.print(Markdown(content))
            parent_span.set_attribute("output.value", agent_run.result.output)
            return agent_run.result.all_messages()


class CustomAutoSuggest(AutoSuggestFromHistory):
    def __init__(
        self, special_suggestions: list[str] | None = None
    ):  # pragma: no cover
        super().__init__()
        self.special_suggestions = special_suggestions or []

    def get_suggestion(
        self, buffer: Buffer, document: Document
    ) -> Suggestion | None:  # pragma: no cover
        # Get the suggestion from history
        suggestion = super().get_suggestion(buffer, document)

        # Check for custom suggestions
        text = document.text_before_cursor.strip()
        for special in self.special_suggestions:
            if special.startswith(text):
                return Suggestion(special[len(text) :])
        return suggestion


def app() -> None:  # pragma: no cover
    sys.exit(cli())


def main() -> None:
    app()


if __name__ == "__main__":
    main()
