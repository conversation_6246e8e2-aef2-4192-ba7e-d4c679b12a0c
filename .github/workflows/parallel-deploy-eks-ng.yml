name: <PERSON>llel Deploy Sales Tech BE - EKS

on:
  workflow_dispatch:
    inputs:
      semver:
        description: "Which version you want to increment? Use major, minor or patch"
        required: true
        default: "patch"
        type: choice
        options:
        - "patch"
        - "minor"
        - "major"
      aws_account_id:
        description: "ZV AWS account ID"
        required: true
        default: "************"
      env:
        description: "Environment to deploy to"
        required: true
        default: "dev"
        type: choice
        options:
        - "dev"
        - "prod"
        - "staging"

env:
  AWS_REGION: us-west-2
  ECR_REPO: reevo-ecr/be
  CLUSTER_NAME: reevo-${{ github.event.inputs.env }}-eks
  K8S_NAMESPACE: reevo-be
  KUBE_CONFIG_DATA: ""

concurrency: parallel-${{ github.event.inputs.env }}-deploy
run-name: ${{ github.event.inputs.env || 'unknown' }} parallel deployment - Commit ${{ github.sha }}

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      image_eks_tag: ${{ steps.image-config.outputs.image_eks_tag }}
    steps:
    - name: "Checkout Code"
      uses: actions/checkout@v4
      with:
        fetch-depth: "0"

      # Get current latest version
    - name: Get current latest version
      uses: actions-ecosystem/action-get-latest-tag@v1
      id: get-latest-tag

      # Generate potentially new versions
    - name: Generate new version
      id: semverGenerate
      uses: anothrNick/github-tag-action@1.69.0
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        WITH_V: true
        DRY_RUN: true
        VERBOSE: true
        DEFAULT_BUMP: ${{ github.event.inputs.semver }}
        BRANCH_HISTORY: last

    - name: Check if new tag is same as current tag
      id: tagCheck
      run: |
        if [ "${{ steps.semverGenerate.outputs.new_tag }}" == "${{ steps.get-latest-tag.outputs.tag }}" ]; then
          echo "skip=true" >> $GITHUB_OUTPUT
        else
          echo "skip=false" >> $GITHUB_OUTPUT
        fi

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
      with:
        registries: ${{ inputs.aws_account_id }}
        mask-password: "true"

    - name: Build image config
      id: image-config
      shell: bash
      env:
        ECR_REPOSITORY: ${{ env.ECR_REPO }}
        IMAGE_EKS_TAG: be-eks-${{ steps.semverGenerate.outputs.new_tag }}
      run: |
        echo "image_eks_tag=$IMAGE_EKS_TAG" >> "$GITHUB_OUTPUT"

    - name: Check if image exists in ECR
      id: check-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: ${{ env.ECR_REPO }}
        IMAGE_EKS_TAG: be-eks-${{ steps.semverGenerate.outputs.new_tag }}
      run: |
        if aws ecr describe-images --repository-name $ECR_REPOSITORY --image-ids imageTag=$IMAGE_EKS_TAG 2>/dev/null; then
          echo "Image tag already exists in ECR"
          echo "exists=true" >> $GITHUB_OUTPUT
        else
          echo "Image tag does not exist in ECR"
          echo "exists=false" >> $GITHUB_OUTPUT
        fi

    - name: Debug image check
      run: |
        echo "Tag check skip: ${{ steps.tagCheck.outputs.skip }}"
        echo "Image exists: ${{ steps.check-image.outputs.exists }}"
        echo "Image tag: ${{ steps.image-config.outputs.image_eks_tag }}"
        echo "Full image: ${{ steps.image-config.outputs.full_eks_image }}"
        echo "Kube config data length: $(echo '${{ steps.kube-config.outputs.kube_config_data }}' | wc -c) characters"

    - name: Build and push image
      if: ${{ steps.tagCheck.outputs.skip != 'true' && steps.check-image.outputs.exists != 'true' }}
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: ${{ env.ECR_REPO }}
        IMAGE_EKS_TAG: be-eks-${{ steps.semverGenerate.outputs.new_tag }}
      run: |
        docker build . -f "deploy/eks.Dockerfile" -t "$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_EKS_TAG"
        docker push "$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_EKS_TAG"

      # Add version bump step
    - name: Bump version and push tag
      if: steps.tagCheck.outputs.skip == 'false'
      uses: anothrNick/github-tag-action@1.69.0
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        WITH_V: true
        DEFAULT_BUMP: ${{ github.event.inputs.semver }}

  db-migration:
    needs: setup
    runs-on: ubuntu-latest
    steps:
    - name: Debug setup outputs - Before
      run: |
        echo "Setup job outputs:"
        echo "Image tag: ${{ needs.setup.outputs.image_eks_tag }}"

    - name: "👷‍♂️ Checkout Code"
      uses: actions/checkout@v4
      with:
        fetch-depth: "0"

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
      with:
        registries: ${{ inputs.aws_account_id }}
        mask-password: "true"

    - name: Debug - Before restore
      run: |
        echo "GITHUB_STATE contents:"
        cat $GITHUB_STATE || echo "State file empty or doesn't exist"
        echo "GITHUB_ENV contents:"
        cat $GITHUB_ENV || echo "Env file empty or doesn't exist"
        echo "Current KUBE_CONFIG_DATA env value:"
        echo "$KUBE_CONFIG_DATA"

    - name: Restore KUBE_CONFIG_DATA
      run: |
        echo "KUBE_CONFIG_DATA=$(cat $GITHUB_STATE | grep KUBE_CONFIG_DATA | cut -d= -f2-)" >> "$GITHUB_ENV"

    - name: Debug - After restore
      run: |
        echo "GITHUB_ENV contents:"
        cat $GITHUB_ENV || echo "Env file empty or doesn't exist"
        echo "Current KUBE_CONFIG_DATA env value:"
        echo "$KUBE_CONFIG_DATA"

    - name: Debug setup outputs - After AWS Login
      run: |
        echo "Setup job outputs after AWS login:"
        echo "Image tag: ${{ needs.setup.outputs.image_eks_tag }}"
        echo "ECR Registry: ${{ steps.login-ecr.outputs.registry }}"
        echo "Full image: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPO }}:${{ needs.setup.outputs.image_eks_tag }}"
        echo "Kube config data length: $(echo '${{ needs.setup.outputs.kube_config_data }}' | wc -c) characters"

    - name: Set up kubeconfig
      run: |
        aws eks update-kubeconfig --name ${{ env.CLUSTER_NAME }} --region ${{ env.AWS_REGION }}

    - name: Debug setup outputs - After AWS Login
      run: |
        echo "Setup job outputs after AWS login:"
        echo "Image tag: ${{ needs.setup.outputs.image_eks_tag }}"
        echo "ECR Registry: ${{ steps.login-ecr.outputs.registry }}"
        echo "Full image: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPO }}:${{ needs.setup.outputs.image_eks_tag }}"

    - name: Run DB Migration Job
      run: |
        export JOB_MANIFEST_NAME=deploy/kubernetes/db.migration.${{ github.event.inputs.env }}-ng.job.yaml
        export JOB_RUN_NAME=database-migration-${{ needs.setup.outputs.image_eks_tag }}
        export FULL_IMAGE="${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPO }}:${{ needs.setup.outputs.image_eks_tag }}"

        echo "Using image: $FULL_IMAGE"
        echo "Current manifest before changes:"
        cat $JOB_MANIFEST_NAME

        # interpolate k8s job fields
        yq e -i '.metadata.name = env(JOB_RUN_NAME)' $JOB_MANIFEST_NAME
        yq e -i '.spec.template.spec.containers[0].image = strenv(FULL_IMAGE)' $JOB_MANIFEST_NAME

        # run the migration job
        kubectl apply -f $JOB_MANIFEST_NAME --validate=false

        # check exit status, wait for job until timeout
        # kubectl wait will return exit code 1 after timeout, use `|| true` so github action is not exiting rightaway
        kubectl wait --for=condition=complete job/$JOB_RUN_NAME -n ${{ env.K8S_NAMESPACE }} --timeout=10m || true

        # Fetch the job status
        JOB_STATUS=$(kubectl get job/$JOB_RUN_NAME -n ${{ env.K8S_NAMESPACE }} -o jsonpath='{.status.succeeded}')

        # If the job succeeded, it will have succeeded count == 1
        if [ "$JOB_STATUS" -eq 1 ]; then
          echo "Database migration completed successfully."
        else
          echo "Database migration failed or timed out. Printing logs..."
        # Print the logs to help diagnose the issue
          kubectl logs job/$JOB_RUN_NAME -n ${{ env.K8S_NAMESPACE }}
          exit 1
        fi

  parallel-deploy:
    needs: [setup, db-migration]
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          # Core Services
        - service: api
          namespace: reevo-be
          timeout: 40m
        - service: webhook
          namespace: reevo-be
          timeout: 40m
        - service: tracking
          namespace: reevo-be
          timeout: 40m
          # Temporal Services
        - service: temporal-default-wf-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-meeting-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-ai-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-research-act-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-research-wf-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-research-act-lowpri-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-research-wf-lowpri-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-research-act-throttled-lowpri-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-research-act-throttled-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-calendar-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-email-workflow-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-email-activity-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-email-imap-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-prospecting-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-sequence-activity-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-sequence-workflow-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-voice-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-integrity-job-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-crm-sync-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-domain-crm-association-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-falkor-workflow-worker
          namespace: temporal
          timeout: 10m
        - service: temporal-falkor-activity-worker
          namespace: temporal
          timeout: 10m
          # Event Processor
        - service: event-processor-a
          namespace: reevo-be
          timeout: 10m
          extra_args: --set envVars.SALESTECH_EVENT_GROUP_ID=event_group_a
        - service: event-consumer-dialer
          namespace: reevo-be
          timeout: 10m
        - service: event-consumer-tracking
          namespace: reevo-be
          timeout: 10m
          # Falkor CDC
        - service: falkor-cdc
          namespace: reevo-be
          timeout: 10m
        - service: falkor-cdc-index
          namespace: reevo-be
          timeout: 10m
        - service: falkor-cdc-partitioner
          namespace: reevo-be
          timeout: 10m
      fail-fast: false
    steps:
    - name: Debug setup outputs - Before
      run: |
        echo "Setup job outputs for ${{ matrix.service }}:"
        echo "Image tag: ${{ needs.setup.outputs.image_eks_tag }}"

    - name: "👷‍♂️ Checkout Code"
      uses: actions/checkout@v4
      with:
        fetch-depth: "0"

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
      with:
        registries: ${{ inputs.aws_account_id }}
        mask-password: "true"

    - name: Debug setup outputs - After AWS Login
      run: |
        echo "Setup job outputs after AWS login for ${{ matrix.service }}:"
        echo "Image tag: ${{ needs.setup.outputs.image_eks_tag }}"
        echo "ECR Registry: ${{ steps.login-ecr.outputs.registry }}"
        echo "Full image: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPO }}:${{ needs.setup.outputs.image_eks_tag }}"

    - name: Set up kubeconfig
      run: |
        aws eks update-kubeconfig --name ${{ env.CLUSTER_NAME }} --region ${{ env.AWS_REGION }}

    - name: Debug setup outputs - After AWS Login
      run: |
        echo "Setup job outputs after AWS login for ${{ matrix.service }}:"
        echo "Image tag: ${{ needs.setup.outputs.image_eks_tag }}"
        echo "ECR Registry: ${{ steps.login-ecr.outputs.registry }}"
        echo "Full image: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPO }}:${{ needs.setup.outputs.image_eks_tag }}"

    - name: Helm deploy - ${{ matrix.service }}
      run: |
        helm repo add gruntwork-io https://helmcharts.gruntwork.io/
        helm upgrade --install ${{ matrix.service }} gruntwork-io/k8s-service \
          -f deploy/helm/${{ github.event.inputs.env }}-ng/${{ matrix.service}}.${{ github.event.inputs.env }}.values.yaml \
          --set containerImage.tag=${{ needs.setup.outputs.image_eks_tag }} \
          --set containerImage.repository=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPO }} \
          ${{ matrix.extra_args }} \
          -n ${{ matrix.namespace }} --create-namespace --wait --timeout ${{ matrix.timeout }}

  slack-notification:
    needs: [setup, db-migration, parallel-deploy]
    if: always()
    uses: ./.github/workflows/slack.yml
    secrets: inherit
    with:
      name: Parallel Deploy
      status: ${{ needs.setup.result && needs.db-migration.result && needs.parallel-deploy.result }}
